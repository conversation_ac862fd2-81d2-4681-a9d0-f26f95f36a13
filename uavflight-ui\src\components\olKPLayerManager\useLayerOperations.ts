import { reactive } from 'vue';
import { useMapLayerManagerStore } from '/@/stores/olMapLayer/mapLayerManager';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getCenter } from 'ol/extent';

// 右键菜单状态
export const contextMenu = reactive({
  visible: false,
  left: 0,
  top: 0,
  currentLayer: null as any
});

// 获取图层类型显示文本
export function getLayerTypeDisplay(layer: any): string {
  if (!layer) return '未知';
  
  if (layer.type === 'raster') {
    return '栅格';
  } else if (layer.type === 'vector') {
    // 确保 originalLayer 存在
    if (!layer.originalLayer) {
      return '矢量';
    }
    
    // 根据几何类型返回点、线、面
    const geometryType = layer.originalLayer.geometryType || '';
    if (geometryType.includes('Point')) {
      return '点';
    } else if (geometryType.includes('Line')) {
      return '线';
    } else if (geometryType.includes('Polygon')) {
      return '面';
    } else {
      return '矢量';
    }
  }
  return layer.type || '未知';
}

// 加载图层
export async function loadLayer(layerId: string): Promise<boolean> {
  const mapLayerStore = useMapLayerManagerStore();
  try {
    const success = await mapLayerStore.addLayer(layerId);
    return success;
  } catch (error) {
    console.error('加载图层出错:', error);
    return false;
  }
}

// 移除图层
export function unloadLayer(layerId: string): boolean {
  const mapLayerStore = useMapLayerManagerStore();
  return mapLayerStore.removeLayer(layerId);
}

// 显示右键菜单
export const showContextMenu = (event: MouseEvent, layer: any) => {
  event.preventDefault();
  
  // 设置菜单位置和当前选中图层
  contextMenu.left = event.clientX;
  contextMenu.top = event.clientY;
  contextMenu.currentLayer = layer;
  contextMenu.visible = true;
};

// 缩放至图层
export async function zoomToLayer() {
  if (!contextMenu.currentLayer) {
    return;
  }
  
  try {
    const mapLayerStore = useMapLayerManagerStore();
    const layerId = contextMenu.currentLayer.id;
    
    // 如果图层未加载，先加载图层
    if (!mapLayerStore.isLayerLoaded(layerId)) {
      ElMessage({
        type: 'info',
        message: '正在加载图层...',
        duration: 1000
      });
      
      const success = await loadLayer(layerId);
      if (!success) {
        ElMessage.error('无法加载图层');
        return;
      }
      
      // 更新复选框状态
      contextMenu.currentLayer.loaded = true;
    }
    
    const layer = mapLayerStore.getLayerById(layerId);
    
    if (!layer || !layer.layerInstance) {
      ElMessage.warning('无法获取图层实例');
      return;
    }
    
    // 检查地图实例是否存在
    if (!mapLayerStore.olMap) {
      ElMessage.warning('地图实例不存在');
      return;
    }
    
    // 显示加载提示
    ElMessage({
      type: 'info',
      message: '正在获取图层范围...',
      duration: 1000
    });
    
    // 添加调试信息，输出图层详细信息
    console.log('缩放至图层，图层信息:', {
      id: layer.id,
      name: layer.name,
      type: layer.type,
      protocol: layer.protocol,
      workspace: layer.workspace,
      layerName: layer.layerName,
      extent: layer.extent
    });
    
    // 首先尝试使用API获取精确的图层范围（对于栅格图层）
    let extent = null;
    
    // 如果是栅格图层并有workspace和layerName，则尝试从API获取
    if (layer.type === 'raster' && layer.workspace && layer.layerName) {
      try {
        console.log(`尝试从API获取图层 ${layerId} (${layer.workspace}:${layer.layerName}) 的精确边界框...`);
        // 使用forceRefresh=true参数，强制刷新缓存的边界框数据
        extent = await mapLayerStore.fetchLayerExtent(layerId, true);
        
        // 检查获取到的边界框是否是全球范围
        if (extent && extent.length === 4) {
          if (extent[0] === -180 && extent[1] === -90 && extent[2] === 180 && extent[3] === 90) {
            console.warn(`获取到全球范围的边界框，这可能不是图层的真实边界`);
            extent = null; // 清空，以便使用备用方法
          } else if (!extent.some((val: number) => !isFinite(val))) {
            console.log(`从API成功获取到图层 ${layerId} 的边界框:`, extent);
          }
        }
      } catch (err) {
        console.warn(`从API获取图层 ${layerId} 边界框失败:`, err);
      }
    }
    
    // 如果API获取失败，使用同步方法获取图层范围
    if (!extent || extent.some((val: number) => !isFinite(val))) {
      console.log(`尝试使用同步方法获取图层 ${layerId} 的边界框...`);
      extent = mapLayerStore.getLayerExtent(layerId);
    }
    
    if (!extent || extent.some((val: number) => !isFinite(val))) {
      console.warn('获取图层范围失败:', extent);
      ElMessage.warning('无法获取图层范围，使用默认视图');
      
      // 使用默认的中国大致范围作为视图中心
      const defaultCenter = [11808925, 4100000]; 
      const defaultZoom = 5;
      
      // 使用默认中心点和缩放级别
      const view = mapLayerStore.olMap.getView();
      view.animate({
        center: defaultCenter,
        duration: 1000,
        zoom: defaultZoom
      });
    } else {
      // 范围有效，缩放到图层范围
      const view = mapLayerStore.olMap.getView();
      
      // 添加调试信息
      console.log('使用以下范围进行缩放:', extent);
      
      view.fit(extent, {
        padding: [50, 50, 50, 50],
        duration: 1000,
        maxZoom: 18 // 添加最大缩放级别限制，防止过度缩放
      });
      
      ElMessage.success(`已缩放至图层: ${contextMenu.currentLayer.name}`);
    }
  } catch (error) {
    console.error('缩放图层出错:', error);
    ElMessage.error(`缩放图层出错: ${error instanceof Error ? error.message : String(error)}`);
  } finally {
    // 关闭右键菜单
    contextMenu.visible = false;
  }
} 

// 批量加载图层
export async function loadLayers(layerIds: string[]): Promise<number> {
  let successCount = 0;
  
  for (const id of layerIds) {
    const success = await loadLayer(id);
    if (success) {
      successCount++;
    }
  }
  
  return successCount;
}

// 批量移除图层
export function unloadLayers(layerIds: string[]): number {
  let successCount = 0;
  
  for (const id of layerIds) {
    const success = unloadLayer(id);
    if (success) {
      successCount++;
    }
  }
  
  return successCount;
}

// 获取图层主题
export function getLayerTheme(layerId: string): string | null {
  const mapLayerStore = useMapLayerManagerStore();
  const layer = mapLayerStore.getLayerById(layerId);
  return layer?.theme || null;
}

// 删除同主题图层
export async function removeThemeLayers(layer: any) {
  // 获取图层主题
  const theme = layer && layer.originalLayer ? (layer.originalLayer as any).theme : null;
  
  // 如果没有主题，只删除当前图层
  if (!theme) {
    ElMessage.warning('该图层没有主题信息，将只删除当前图层');
    unloadLayer(layer.id); // 使用unloadLayer替代removeLayer
    return;
  }
  
  try {
    // 显示确认对话框
    await ElMessageBox.confirm(
      `确认删除主题"${theme}"的所有已加载图层吗？`,
      '删除同主题图层',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    const mapLayerStore = useMapLayerManagerStore();
    
    // 获取同主题的所有已加载图层
    const themeLayerIds = mapLayerStore.mapConfig?.layers
      .filter(l => (l as any).theme === theme && mapLayerStore.isLayerLoaded(l.id))
      .map(l => l.id) || [];
      
    if (themeLayerIds.length === 0) {
      ElMessage.info(`主题"${theme}"下没有已加载的图层`);
      return;
    }
    
    // 删除所有同主题图层
    let successCount = 0;
    let failCount = 0;
    
    for (const id of themeLayerIds) {
      const success = unloadLayer(id); // 使用unloadLayer替代removeLayer
      if (success) {
        successCount++;
      } else {
        failCount++;
      }
    }
    
    if (failCount === 0) {
      ElMessage.success(`成功删除主题"${theme}"的全部 ${successCount} 个图层`);
    } else {
      ElMessage.warning(`删除主题"${theme}"的图层：${successCount} 个成功，${failCount} 个失败`);
    }
    
  } catch (error) {
    // 用户取消或发生错误
    if (error === 'cancel' || error === 'close') {
      console.log('用户取消删除同主题图层');
    } else {
      console.error('删除同主题图层出错:', error);
      ElMessage.error(`删除同主题图层出错: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
} 