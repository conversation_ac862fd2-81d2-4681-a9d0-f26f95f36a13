/**
 * @file styleUtils.ts
 * @description 地图样式工具函数集合，提供OpenLayers样式创建和处理功能
 * 
 * 该文件提供了一系列用于处理OpenLayers地图样式的工具函数，包括：
 * - 颜色解析和处理（parseColor）
 * - 点、线、面、图标等基础样式创建
 * - 根据几何类型自动创建样式
 * - 样式过滤条件评估
 * 
 * 这些工具函数简化了OpenLayers样式的创建和管理过程，
 * 为应用提供统一且易用的样式处理接口。
 * 
 * <AUTHOR>
 * @date 2025-05-19
 */
/*
 * @Description: 地图样式工具函数
 */
import { Fill, Stroke, Style, Circle, Icon } from 'ol/style';
import { Feature } from 'ol';

/**
 * 颜色类型定义
 */
interface Color {
	r: number;
	g: number;
	b: number;
	a: number;
}

/**
 * 生成随机颜色
 * @returns 随机颜色对象
 */
export function generateRandomColor(): Color {
	return {
		r: Math.floor(Math.random() * 200) + 55, // 55-255 范围，避免太暗的颜色
		g: Math.floor(Math.random() * 200) + 55,
		b: Math.floor(Math.random() * 200) + 55,
		a: 1
	};
}

/**
 * 生成深色版本
 * @param color 原始颜色
 * @param darkFactor 深色因子(0-1)，越小越深
 * @returns 深色版本的颜色
 */
export function getDarkerColor(color: Color, darkFactor: number = 0.7): Color {
	return {
		r: Math.floor(color.r * darkFactor),
		g: Math.floor(color.g * darkFactor),
		b: Math.floor(color.b * darkFactor),
		a: color.a
	};
}

/**
 * 将Color对象转换为十六进制颜色字符串
 * @param color 颜色对象
 * @returns 十六进制颜色字符串，如 #FF0000
 */
export function colorToHex(color: Color): string {
	const r = color.r.toString(16).padStart(2, '0');
	const g = color.g.toString(16).padStart(2, '0');
	const b = color.b.toString(16).padStart(2, '0');
	return `#${r}${g}${b}`;
}

/**
 * 将Color对象转换为带透明度的rgba字符串
 * @param color 颜色对象
 * @returns rgba颜色字符串，如 rgba(255,0,0,0.5)
 */
export function colorToRgba(color: Color): string {
	return `rgba(${color.r},${color.g},${color.b},${color.a})`;
}

/**
 * 解析颜色字符串
 * @param colorStr 颜色字符串(十六进制或rgba)
 * @returns 解析后的颜色对象
 */
export function parseColor(colorStr: string): Color {
	// 默认颜色
	const defaultColor = { r: 51, g: 136, b: 255, a: 1 };
	
	if (!colorStr) return defaultColor;
	
	// 处理rgba格式
	if (colorStr.startsWith('rgba')) {
		const rgba = colorStr.match(/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/);
		if (rgba) {
			return {
				r: parseInt(rgba[1]),
				g: parseInt(rgba[2]),
				b: parseInt(rgba[3]),
				a: parseFloat(rgba[4])
			};
		}
	}
	
	// 处理rgb格式
	if (colorStr.startsWith('rgb')) {
		const rgb = colorStr.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/);
		if (rgb) {
			return {
				r: parseInt(rgb[1]),
				g: parseInt(rgb[2]),
				b: parseInt(rgb[3]),
				a: 1
			};
		}
	}
	
	// 处理十六进制格式
	if (colorStr.startsWith('#')) {
		let hex = colorStr.substring(1);
		
		// 转换短格式 (#rgb) 为长格式 (#rrggbb)
		if (hex.length === 3) {
			hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
		}
		
		const r = parseInt(hex.substring(0, 2), 16);
		const g = parseInt(hex.substring(2, 4), 16);
		const b = parseInt(hex.substring(4, 6), 16);
		
		return { r, g, b, a: 1 };
	}
	
	return defaultColor;
}

/**
 * 创建点样式
 * @param color 颜色
 * @param radius 半径
 * @returns OpenLayers样式
 */
export function createPointStyle(color: string, radius: number = 6): Style {
	return new Style({
		image: new Circle({
			radius,
			fill: new Fill({
				color
			}),
			stroke: new Stroke({
				color: 'white',
				width: 2
			})
		})
	});
}

/**
 * 创建线样式
 * @param color 颜色
 * @param width 线宽
 * @returns OpenLayers样式
 */
export function createLineStyle(color: string, width: number = 2): Style {
	return new Style({
		stroke: new Stroke({
			color,
			width,
			lineCap: 'round',
			lineJoin: 'round'
		})
	});
}

/**
 * 创建面样式
 * @param fillColor 填充颜色
 * @param strokeColor 边框颜色
 * @param strokeWidth 边框宽度
 * @returns OpenLayers样式
 */
export function createPolygonStyle(fillColor: string, strokeColor: string = '#3388ff', strokeWidth: number = 2): Style {
	return new Style({
		fill: new Fill({
			color: fillColor
		}),
		stroke: new Stroke({
			color: strokeColor,
			width: strokeWidth
		})
	});
}

/**
 * 创建图标样式
 * @param src 图标URL
 * @param scale 缩放比例
 * @returns OpenLayers样式
 */
export function createIconStyle(src: string, scale: number = 1): Style {
	return new Style({
		image: new Icon({
			src,
			scale
		})
	});
}

/**
 * 评估过滤条件
 * @param feature 要素
 * @param property 属性名
 * @param operator 操作符
 * @param value 比较值
 * @returns 是否满足条件
 */
export function evaluateFilter(
	feature: Feature,
	property: string,
	operator: '=' | '!=' | '>' | '<' | '>=' | '<=' | 'in',
	value: any
): boolean {
	const properties = feature.getProperties();
	const propValue = properties[property];
	
	switch (operator) {
		case '=': return propValue === value;
		case '!=': return propValue !== value;
		case '>': return propValue > value;
		case '<': return propValue < value;
		case '>=': return propValue >= value;
		case '<=': return propValue <= value;
		case 'in': return Array.isArray(value) && value.includes(propValue);
		default: return false;
	}
}

/**
 * 根据要素类型创建样式
 * @param feature 要素
 * @param color 颜色
 * @returns OpenLayers样式
 */
export function createStyleByGeometryType(feature: Feature, color: string = '#3388ff'): Style {
	const geometry = feature.getGeometry();
	
	if (!geometry) {
		return createPointStyle(color);
	}
	
	const type = geometry.getType();
	
	if (type === 'Point' || type === 'MultiPoint') {
		return createPointStyle(color);
	} else if (type === 'LineString' || type === 'MultiLineString') {
		return createLineStyle(color);
	} else if (type === 'Polygon' || type === 'MultiPolygon') {
		return createPolygonStyle(color + '80', color);
	}
	
	return createPointStyle(color);
}

/**
 * 将静态WFS URL转换为带有bbox参数的URL
 * @deprecated 不再需要手动添加bbox参数，OpenLayers会自动处理
 * @param url 原始WFS URL
 * @param extent 范围数组 [minX, minY, maxX, maxY]
 * @returns 原始WFS URL
 */
export function convertToWfsBboxUrl(url: string, extent: number[]): string {
	console.log('convertToWfsBboxUrl函数已废弃，OpenLayers会自动处理bbox参数');
	return url;
}