const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.Bchm_fAu.js","assets/index.C0-0gsfl.js","assets/vue.CnN__PXn.js","assets/index.CB8Sa4Ps.css","assets/search.DLZ6w4n3.js","assets/search.xPEshroP.css","assets/personal.BgNtSobm.js","assets/user.DtNqwFgs.js","assets/personal.CFJVuEYH.css"])))=>i.map(i=>d[i]);
import{v as oe,m as ce,u as ue,L as F,a as S,e as R,a8 as ie,ah as me,S as de,ac as fe,q as be,__tla as ge}from"./index.C0-0gsfl.js";import{L as he,d as P,z as _e,s as D,k as p,A as we,c as $,o as pe,B as i,m as ke,a as V,b as x,f as h,q as ve,t as l,e as Ee,D as ye,v as a,E as k,u as d,G as _,j as O,n as Ce}from"./vue.CnN__PXn.js";let j,Fe=Promise.all([(()=>{try{return ge}catch{}})()]).then(async()=>{const L=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],c=(()=>{if(typeof document>"u")return!1;const n=L[0],s={};for(const t of L)if((t==null?void 0:t[1])in document){for(const[g,o]of t.entries())s[n[g]]=o;return s}return!1})(),z={change:c.fullscreenchange,error:c.fullscreenerror};let r={request:(n=document.documentElement,s)=>new Promise((t,f)=>{const g=()=>{r.off("change",g),t()};r.on("change",g);const o=n[c.requestFullscreen](s);o instanceof Promise&&o.then(g).catch(f)}),exit:()=>new Promise((n,s)=>{if(!r.isFullscreen)return void n();const t=()=>{r.off("change",t),n()};r.on("change",t);const f=document[c.exitFullscreen]();f instanceof Promise&&f.then(t).catch(s)}),toggle:(n,s)=>r.isFullscreen?r.exit():r.request(n,s),onchange(n){r.on("change",n)},onerror(n){r.on("error",n)},on(n,s){const t=z[n];t&&document.addEventListener(t,s,!1)},off(n,s){const t=z[n];t&&document.removeEventListener(t,s,!1)},raw:c};Object.defineProperties(r,{isFullscreen:{get:()=>!!document[c.fullscreenElement]},element:{enumerable:!0,get:()=>document[c.fullscreenElement]??void 0},isEnabled:{enumerable:!0,get:()=>!!document[c.fullscreenEnabled]}}),c||(r={isEnabled:!1});let v,A,T,q;v=he("msg",{state:()=>({msgArray:[]}),actions:{getAllMsg(){return this.msgArray},setMsg(n){this.msgArray.push(n)},removeAll(){this.msgArray=[]}}}),A={class:"layout-navbars-breadcrumb-user-link"},T=["src"],q=P({name:"layoutBreadcrumbUser"}),j=be(P({...q,setup(n){const s=O(()=>S(()=>import("./index.Bchm_fAu.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([0,1,2,3]))),t=O(()=>S(()=>import("./search.DLZ6w4n3.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([4,1,2,3,5]))),f=O(()=>S(()=>import("./personal.BgNtSobm.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([6,1,2,3,7,8]))),{locale:g,t:o}=oe.useI18n(),E=_e(),U=ce(),G=ue(),{userInfos:y}=D(U),{themeConfig:w}=D(G),B=p();p();const I=p(),N=we({isScreenfull:!1,disabledI18n:"zh-cn",disabledSize:"large"}),H=p(!1),J=$(()=>{let e="";const{layout:u,isClassicSplitMenu:b}=w.value;return e=["defaults","columns"].includes(u)||u==="classic"&&!b?"1":"",e}),K=()=>{R.emit("openSetingsDrawer")},Q=e=>{e==="logOut"?ie({closeOnClickModal:!1,closeOnPressEscape:!1,title:o("user.logOutTitle"),message:o("user.logOutMessage"),showCancelButton:!0,confirmButtonText:o("user.logOutConfirm"),cancelButtonText:o("user.logOutCancel"),buttonSize:"default",beforeClose:(u,b,m)=>{u==="confirm"?(b.confirmButtonLoading=!0,b.confirmButtonText=o("user.logOutExit"),setTimeout(()=>{m(),setTimeout(()=>{b.confirmButtonLoading=!1},300)},700)):m()}}).then(async()=>{R.emit("onCurrentContextmenuClick",Object.assign({},{contextMenuClickId:3,...E})),await me(),de.clear(),window.location.reload()}).catch(()=>{}):e==="personal"?I.value.open():E.push(e)},W=()=>{B.value.openSearch()},X=()=>{w.value.isLockScreen=!0,w.value.lockScreenTime=0,F.set("themeConfig",w.value)},Y=()=>{const{href:e}=E.resolve({path:"/bigScreen"});window.open(e,"_blank")},M=(e,u)=>{N[u]=F.get("themeConfig")[e]},Z=e=>{v().setMsg({label:"websocket\u6D88\u606F",value:e,time:fe(new Date)})};return $(()=>v().getAllMsg().length>0),pe(()=>{F.get("themeConfig")&&(M("globalComponentSize","disabledSize"),M("globalI18n","disabledI18n"))}),(e,u)=>{const b=i("ele-Monitor"),m=i("el-icon"),ee=i("ele-Lock"),ne=i("ele-Search"),te=i("ele-Setting"),le=i("ele-ArrowDown"),C=i("el-dropdown-item"),se=i("el-dropdown-menu"),re=i("el-dropdown"),ae=ke("auth");return x(),V("div",{class:"layout-navbars-breadcrumb-user pr15",style:Ce({flex:d(J)})},[h("div",{class:"layout-navbars-breadcrumb-user-icon screen-stats",onClick:Y},[l(m,null,{default:a(()=>[l(b)]),_:1}),u[0]||(u[0]=h("span",{class:"screen-text"},"\u5927\u5C4F\u7EDF\u8BA1",-1))]),h("div",{class:"layout-navbars-breadcrumb-user-icon",onClick:X},[l(m,{title:e.$t("layout.threeLockScreenTime")},{default:a(()=>[l(ee)]),_:1},8,["title"])]),h("div",{class:"layout-navbars-breadcrumb-user-icon",onClick:W},[l(m,{title:e.$t("user.title2")},{default:a(()=>[l(ne)]),_:1},8,["title"])]),ve((x(),V("div",{class:"layout-navbars-breadcrumb-user-icon",onClick:K},[l(m,{title:e.$t("user.title3")},{default:a(()=>[l(te)]),_:1},8,["title"])])),[[ae,"admin.per"]]),l(re,{"show-timeout":70,"hide-timeout":50,onCommand:Q},{dropdown:a(()=>[l(se,null,{default:a(()=>[l(C,{command:"/home"},{default:a(()=>[k(_(e.$t("user.dropdown1")),1)]),_:1}),l(C,{command:"personal"},{default:a(()=>[k(_(e.$t("user.dropdown2")),1)]),_:1}),l(C,{divided:"",command:"logOut"},{default:a(()=>[k(_(e.$t("user.dropdown5")),1)]),_:1})]),_:1})]),default:a(()=>[h("span",A,[h("img",{src:e.baseURL+d(y).user.avatar,class:"layout-navbars-breadcrumb-user-link-photo mr5"},null,8,T),k(" "+_(d(y).deptName)+"-"+_(d(y).user.name)+" ",1),l(m,{class:"el-icon--right"},{default:a(()=>[l(le)]),_:1})])]),_:1}),l(d(t),{ref_key:"searchRef",ref:B},null,512),d(H)?(x(),Ee(d(s),{key:0,uri:"/admin/ws/info",onRollback:Z})):ye("",!0),l(d(f),{ref_key:"personalDrawerRef",ref:I},null,512)],4)}}}),[["__scopeId","data-v-df5b1edb"]])});export{Fe as __tla,j as default};
