/* 引入样式变量 */
@import "../../theme/mixins/index.scss";

/* 右侧快拼图层面板样式 */
.hd-layer-manager-container {
  position: relative !important; /* 改为相对定位，填充父容器 */
  width: 100% !important; /* 填充父容器宽度 */
  height: 100% !important; /* 填充父容器高度 */
  display: flex !important;
  flex-direction: column !important;
  background-color: rgba(15, 21, 32, 0.5) !important; /* 50%透明度，强制应用 */
  backdrop-filter: none !important; /* 移除毛玻璃效果 */
  color: #fff !important;
  border-radius: 4px !important; /* 四周都有圆角 */
  overflow: hidden !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4) !important; /* 与图层管理阴影效果一致 */
  z-index: 100 !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important; /* 与图层管理边框颜色一致 */
}

/* 快拼图层管理器内容 */
.hd-layer-manager-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 标题栏 */
.hd-layer-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  // background-color: rgba(64, 158, 255, 0.2);
  height: 42px;
}

.hd-layer-manager-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

/* 面板通用样式 */
.panel-content {
  padding: 10px;
  height: 100%;
  overflow-y: auto;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar,
.panel-content-area::-webkit-scrollbar,
.batch-list::-webkit-scrollbar {
  width: 4px;
}

.panel-content::-webkit-scrollbar-track,
.panel-content-area::-webkit-scrollbar-track,
.batch-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.panel-content::-webkit-scrollbar-thumb,
.panel-content-area::-webkit-scrollbar-thumb,
.batch-list::-webkit-scrollbar-thumb {
  background: rgba(64, 158, 255, 0.5);
  border-radius: 2px;
}

/* Element Plus 组件样式覆盖 */
.hd-layer-manager-container {
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #409EFF;
    border-color: #409EFF;
  }
  
  .el-slider__button {
    border-color: #409EFF;
  }
  
  .el-slider__bar {
    background-color: #409EFF;
  }
  
  /* 保持复选框文字颜色为白色，即使勾选后也是 */
  .el-checkbox__input.is-checked + .el-checkbox__label,
  .el-checkbox__input.is-indeterminate + .el-checkbox__label {
    color: #fff !important;
  }
  
  /* 确保表格中的复选框也保持白色文字 */
  .el-table .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #fff !important;
  }
} 