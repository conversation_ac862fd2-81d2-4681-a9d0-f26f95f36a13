<!--
 * @Author: AI Assistant 
 * @Date: 2025-07-15 
 * @Description: 无人机视频图层面板组件
 *  
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved. 
-->
<template>
  <div class="drone-video-panel">
    <div class="panel-title">
      <span>无人机影像</span>
    </div>
    <div class="panel-content">
      <!-- 飞行批次选择器 -->
      <div class="flight-batch-selector">
        <div class="section-title">飞行批次</div>
        <div class="batch-list">
          <div 
            class="batch-item" 
            v-for="batch in flightBatches" 
            :key="batch.id"
            :class="{ 'active': batch.checked }"
          >
            <el-checkbox 
              v-model="batch.checked" 
              @change="handleBatchToggle(batch)"
            >{{ batch.name }}</el-checkbox>
            <el-icon v-if="batch.checked" class="expand-icon"><CaretRight /></el-icon>
          </div>
        </div>
      </div>

      <!-- 视频播放控制区 -->
      <div class="video-controls" v-if="selectedBatch">
        <div class="section-title">视频控制</div>
        <div class="control-buttons">
          <el-button size="small" type="primary" @click="playVideo">
            <el-icon><CaretRight /></el-icon>
            播放
          </el-button>
          <el-button size="small" type="info" @click="pauseVideo">
            <el-icon><VideoPause /></el-icon>
            暂停
          </el-button>
          <el-button size="small" type="warning" @click="stopVideo">
            <el-icon><CircleClose /></el-icon>
            停止
          </el-button>
        </div>
        
        <!-- 视频进度条 -->
        <div class="video-progress">
          <el-slider 
            v-model="videoProgress" 
            :min="0" 
            :max="100" 
            @change="handleProgressChange"
          ></el-slider>
          <div class="progress-time">
            <span>{{ formatTime(currentTime) }}</span>
            <span>{{ formatTime(totalTime) }}</span>
          </div>
        </div>

        <!-- 视频播放速率 -->
        <div class="video-speed">
          <span>播放速率：{{ playbackSpeed }}x</span>
          <div class="speed-buttons">
            <el-button 
              v-for="speed in speedOptions" 
              :key="speed" 
              size="small" 
              :type="playbackSpeed === speed ? 'primary' : ''" 
              @click="setPlaybackSpeed(speed)"
            >{{ speed }}x</el-button>
          </div>
        </div>
      </div>

      <!-- 视频图层设置 -->
      <div class="video-layer-settings" v-if="selectedBatch">
        <div class="section-title">图层设置</div>
        <div class="setting-item">
          <span>透明度</span>
          <el-slider 
            v-model="layerOpacity" 
            :min="0" 
            :max="100" 
            :step="10"
            @change="handleOpacityChange"
          ></el-slider>
        </div>
        <div class="setting-item">
          <span>亮度</span>
          <el-slider 
            v-model="brightness" 
            :min="0" 
            :max="200" 
            :step="10"
            :format-tooltip="formatPercentage"
            @change="handleBrightnessChange"
          ></el-slider>
        </div>
        <div class="setting-item">
          <span>对比度</span>
          <el-slider 
            v-model="contrast" 
            :min="0" 
            :max="200" 
            :step="10"
            :format-tooltip="formatPercentage"
            @change="handleContrastChange"
          ></el-slider>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { CaretRight, VideoPause, CircleClose } from '@element-plus/icons-vue';

// 飞行批次数据
const flightBatches = reactive([
  { id: '1', name: '2025-06-10飞行批次', checked: false },
  { id: '2', name: '2025-05-28飞行批次', checked: true },
  { id: '3', name: '2025-05-14飞行批次', checked: true },
  { id: '4', name: '2025-04-24飞行批次', checked: true },
  { id: '5', name: '2025-04-15飞行批次', checked: true },
  { id: '6', name: '2025-04-06飞行批次', checked: true },
  { id: '7', name: '2025-03-28飞行批次', checked: false },
  { id: '8', name: '2025-03-24飞行批次', checked: false },
  { id: '9', name: '2025-03-19飞行批次', checked: false },
  { id: '10', name: '2025-03-17飞行批次', checked: false },
]);

// 选中的批次
const selectedBatch = computed(() => {
  return flightBatches.find(batch => batch.checked);
});

// 视频控制相关状态
const videoProgress = ref(0);
const currentTime = ref(0);
const totalTime = ref(600); // 10分钟视频
const playbackSpeed = ref(1);
const speedOptions = [0.5, 1, 2, 5, 10];
const isPlaying = ref(false);

// 图层设置
const layerOpacity = ref(100);
const brightness = ref(100);
const contrast = ref(100);

// 处理批次选择
const handleBatchToggle = (batch: any) => {
  if (batch.checked) {
    // 取消选中其他批次
    flightBatches.forEach(item => {
      if (item.id !== batch.id) {
        item.checked = false;
      }
    });
    console.log(`已选择飞行批次: ${batch.name}`);
  } else {
    console.log(`已取消飞行批次: ${batch.name}`);
  }
};

// 视频控制函数
const playVideo = () => {
  isPlaying.value = true;
  console.log('开始播放视频');
};

const pauseVideo = () => {
  isPlaying.value = false;
  console.log('暂停播放视频');
};

const stopVideo = () => {
  isPlaying.value = false;
  videoProgress.value = 0;
  currentTime.value = 0;
  console.log('停止播放视频');
};

const handleProgressChange = (val: number) => {
  currentTime.value = (totalTime.value * val) / 100;
  console.log(`视频进度已更改为: ${val}%`);
};

const setPlaybackSpeed = (speed: number) => {
  playbackSpeed.value = speed;
  console.log(`播放速度已设置为: ${speed}x`);
};

// 图层设置函数
const handleOpacityChange = (val: number) => {
  console.log(`图层透明度已更改为: ${val}%`);
};

const handleBrightnessChange = (val: number) => {
  console.log(`亮度已更改为: ${val}%`);
};

const handleContrastChange = (val: number) => {
  console.log(`对比度已更改为: ${val}%`);
};

// 格式化时间显示 (秒 -> MM:SS)
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainSeconds = Math.floor(seconds % 60);
  return `${minutes.toString().padStart(2, '0')}:${remainSeconds.toString().padStart(2, '0')}`;
};

// 格式化百分比显示
const formatPercentage = (val: number) => {
  return `${val}%`;
};

onMounted(() => {
  console.log('无人机视频图层面板已加载');
});
</script>

<style lang="scss" scoped>
.drone-video-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
  padding: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #e6e6e6;
  display: flex;
  align-items: center;
}

.flight-batch-selector {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  padding: 10px;
}

.batch-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
  max-height: 200px;
  overflow-y: auto;
}

.batch-item {
  padding: 5px 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 4px;
  transition: all 0.3s;
}

.batch-item.active {
  background-color: rgba(64, 158, 255, 0.2);
}

.expand-icon {
  color: #409eff;
}

.video-controls {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  padding: 10px;
}

.control-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 10px;
}

.video-progress {
  margin: 15px 0;
}

.progress-time {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 12px;
  color: #aaa;
}

.video-speed {
  margin-top: 10px;
  
  > span {
    display: block;
    margin-bottom: 8px;
  }
}

.speed-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.video-layer-settings {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  padding: 10px;
}

.setting-item {
  margin-bottom: 15px;
  
  > span {
    display: block;
    margin-bottom: 5px;
  }
}

:deep(.el-checkbox__label) {
  color: #fff;
}

:deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.2);
}

:deep(.el-slider__bar) {
  background-color: #409eff;
}

:deep(.el-slider__button) {
  border: 2px solid #409eff;
}

:deep(.el-button--small) {
  padding: 5px 10px;
}
</style> 