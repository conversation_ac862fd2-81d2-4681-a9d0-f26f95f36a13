{"version": 3, "sources": ["../../ol/interaction/Draw.js"], "sourcesContent": ["/**\n * @module ol/interaction/Draw\n */\nimport Circle from '../geom/Circle.js';\nimport Event from '../events/Event.js';\nimport EventType from '../events/EventType.js';\nimport Feature from '../Feature.js';\nimport GeometryCollection from '../geom/GeometryCollection.js';\nimport InteractionProperty from './Property.js';\nimport LineString from '../geom/LineString.js';\nimport MapBrowserEvent from '../MapBrowserEvent.js';\nimport MapBrowserEventType from '../MapBrowserEventType.js';\nimport MultiLineString from '../geom/MultiLineString.js';\nimport MultiPoint from '../geom/MultiPoint.js';\nimport MultiPolygon from '../geom/MultiPolygon.js';\nimport Point from '../geom/Point.js';\nimport PointerInteraction from './Pointer.js';\nimport Polygon, {fromCircle, makeRegular} from '../geom/Polygon.js';\nimport VectorLayer from '../layer/Vector.js';\nimport VectorSource from '../source/Vector.js';\nimport {FALSE, TRUE} from '../functions.js';\nimport {\n  always,\n  never,\n  noModifierKeys,\n  shiftKeyOnly,\n} from '../events/condition.js';\nimport {\n  boundingExtent,\n  getBottomLeft,\n  getBottomRight,\n  getTopLeft,\n  getTopRight,\n} from '../extent.js';\nimport {clamp, squaredDistance, toFixed} from '../math.js';\nimport {createEditingStyle} from '../style/Style.js';\nimport {\n  distance,\n  squaredDistance as squaredCoordinateDistance,\n} from '../coordinate.js';\nimport {fromUserCoordinate, getUserProjection} from '../proj.js';\nimport {getStrideForLayout} from '../geom/SimpleGeometry.js';\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../geom/Geometry.js\").Type} type Geometry type of\n * the geometries being drawn with this instance.\n * @property {number} [clickTolerance=6] The maximum distance in pixels between\n * \"down\" and \"up\" for a \"up\" event to be considered a \"click\" event and\n * actually add a point/vertex to the geometry being drawn.  The default of `6`\n * was chosen for the draw interaction to behave correctly on mouse as well as\n * on touch devices.\n * @property {import(\"../Collection.js\").default<Feature>} [features]\n * Destination collection for the drawn features.\n * @property {VectorSource} [source] Destination source for\n * the drawn features.\n * @property {number} [dragVertexDelay=500] Delay in milliseconds after pointerdown\n * before the current vertex can be dragged to its exact position.\n * @property {number} [snapTolerance=12] Pixel distance for snapping to the\n * drawing finish. Must be greater than `0`.\n * @property {boolean} [stopClick=false] Stop click, singleclick, and\n * doubleclick events from firing during drawing.\n * @property {number} [maxPoints] The number of points that can be drawn before\n * a polygon ring or line string is finished. By default there is no\n * restriction.\n * @property {number} [minPoints] The number of points that must be drawn\n * before a polygon ring or line string can be finished. Default is `3` for\n * polygon rings and `2` for line strings.\n * @property {import(\"../events/condition.js\").Condition} [finishCondition] A function\n * that takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * boolean to indicate whether the drawing can be finished. Not used when drawing\n * POINT or MULTI_POINT geometries.\n * @property {import(\"../style/Style.js\").StyleLike|import(\"../style/flat.js\").FlatStyleLike} [style]\n * Style for sketch features.\n * @property {GeometryFunction} [geometryFunction]\n * Function that is called when a geometry's coordinates are updated.\n * @property {string} [geometryName] Geometry name to use for features created\n * by the draw interaction.\n * @property {import(\"../events/condition.js\").Condition} [condition] A function that\n * takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * boolean to indicate whether that event should be handled.\n * By default {@link module:ol/events/condition.noModifierKeys}, i.e. a click,\n * adds a vertex or deactivates freehand drawing.\n * @property {boolean} [freehand=false] Operate in freehand mode for lines,\n * polygons, and circles.  This makes the interaction always operate in freehand\n * mode and takes precedence over any `freehandCondition` option.\n * @property {import(\"../events/condition.js\").Condition} [freehandCondition]\n * Condition that activates freehand drawing for lines and polygons. This\n * function takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and\n * returns a boolean to indicate whether that event should be handled. The\n * default is {@link module:ol/events/condition.shiftKeyOnly}, meaning that the\n * Shift key activates freehand drawing.\n * @property {boolean|import(\"../events/condition.js\").Condition} [trace=false] Trace a portion of another geometry.\n * Ignored when in freehand mode.\n * @property {VectorSource} [traceSource] Source for features to trace.  If tracing is active and a `traceSource` is\n * not provided, the interaction's `source` will be used.  Tracing requires that the interaction is configured with\n * either a `traceSource` or a `source`.\n * @property {boolean} [wrapX=false] Wrap the world horizontally on the sketch\n * overlay.\n * @property {import(\"../geom/Geometry.js\").GeometryLayout} [geometryLayout='XY'] Layout of the\n * feature geometries created by the draw interaction.\n */\n\n/**\n * Coordinate type when drawing points.\n * @typedef {import(\"../coordinate.js\").Coordinate} PointCoordType\n */\n\n/**\n * Coordinate type when drawing lines.\n * @typedef {Array<import(\"../coordinate.js\").Coordinate>} LineCoordType\n */\n\n/**\n * Coordinate type when drawing polygons.\n * @typedef {Array<Array<import(\"../coordinate.js\").Coordinate>>} PolyCoordType\n */\n\n/**\n * Types used for drawing coordinates.\n * @typedef {PointCoordType|LineCoordType|PolyCoordType} SketchCoordType\n */\n\n/**\n * @typedef {Object} TraceState\n * @property {boolean} active Tracing active.\n * @property {import(\"../pixel.js\").Pixel} [startPx] The initially clicked pixel location.\n * @property {Array<TraceTarget>} [targets] Targets available for tracing.\n * @property {number} [targetIndex] The index of the currently traced target.  A value of -1 indicates\n * that no trace target is active.\n */\n\n/**\n * @typedef {Object} TraceTarget\n * @property {Array<import(\"../coordinate.js\").Coordinate>} coordinates Target coordinates.\n * @property {boolean} ring The target coordinates are a linear ring.\n * @property {number} startIndex The index of first traced coordinate.  A fractional index represents an\n * edge intersection.  Index values for rings will wrap (may be negative or larger than coordinates length).\n * @property {number} endIndex The index of last traced coordinate.  Details from startIndex also apply here.\n */\n\n/**\n * Function that takes an array of coordinates and an optional existing geometry\n * and a projection as arguments, and returns a geometry. The optional existing\n * geometry is the geometry that is returned when the function is called without\n * a second argument.\n * @typedef {function(!SketchCoordType, import(\"../geom/SimpleGeometry.js\").default,\n *     import(\"../proj/Projection.js\").default):\n *     import(\"../geom/SimpleGeometry.js\").default} GeometryFunction\n */\n\n/**\n * @typedef {'Point' | 'LineString' | 'Polygon' | 'Circle'} Mode\n * Draw mode.  This collapses multi-part geometry types with their single-part\n * cousins.\n */\n\n/**\n * @enum {string}\n */\nconst DrawEventType = {\n  /**\n   * Triggered upon feature draw start\n   * @event DrawEvent#drawstart\n   * @api\n   */\n  DRAWSTART: 'drawstart',\n  /**\n   * Triggered upon feature draw end\n   * @event DrawEvent#drawend\n   * @api\n   */\n  DRAWEND: 'drawend',\n  /**\n   * Triggered upon feature draw abortion\n   * @event DrawEvent#drawabort\n   * @api\n   */\n  DRAWABORT: 'drawabort',\n};\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/interaction/Draw~Draw} instances are\n * instances of this type.\n */\nexport class DrawEvent extends Event {\n  /**\n   * @param {DrawEventType} type Type.\n   * @param {Feature} feature The feature drawn.\n   */\n  constructor(type, feature) {\n    super(type);\n\n    /**\n     * The feature being drawn.\n     * @type {Feature}\n     * @api\n     */\n    this.feature = feature;\n  }\n}\n\n/**\n * @param {import(\"../coordinate.js\").Coordinate} coordinate The coordinate.\n * @param {Array<Feature>} features The candidate features.\n * @return {Array<TraceTarget>} The trace targets.\n */\nfunction getTraceTargets(coordinate, features) {\n  /**\n   * @type {Array<TraceTarget>}\n   */\n  const targets = [];\n\n  for (let i = 0; i < features.length; ++i) {\n    const feature = features[i];\n    const geometry = feature.getGeometry();\n    appendGeometryTraceTargets(coordinate, geometry, targets);\n  }\n\n  return targets;\n}\n\n/**\n * @param {import(\"../coordinate.js\").Coordinate} a One coordinate.\n * @param {import(\"../coordinate.js\").Coordinate} b Another coordinate.\n * @return {number} The squared distance between the two coordinates.\n */\nfunction getSquaredDistance(a, b) {\n  return squaredDistance(a[0], a[1], b[0], b[1]);\n}\n\n/**\n * @param {LineCoordType} coordinates The ring coordinates.\n * @param {number} index The index.  May be wrapped.\n * @return {import(\"../coordinate.js\").Coordinate} The coordinate.\n */\nfunction getCoordinate(coordinates, index) {\n  const count = coordinates.length;\n  if (index < 0) {\n    return coordinates[index + count];\n  }\n  if (index >= count) {\n    return coordinates[index - count];\n  }\n  return coordinates[index];\n}\n\n/**\n * Get the cumulative squared distance along a ring path.  The end index index may be \"wrapped\" and it may\n * be less than the start index to indicate the direction of travel.  The start and end index may have\n * a fractional part to indicate a point between two coordinates.\n * @param {LineCoordType} coordinates Ring coordinates.\n * @param {number} startIndex The start index.\n * @param {number} endIndex The end index.\n * @return {number} The cumulative squared distance along the ring path.\n */\nfunction getCumulativeSquaredDistance(coordinates, startIndex, endIndex) {\n  let lowIndex, highIndex;\n  if (startIndex < endIndex) {\n    lowIndex = startIndex;\n    highIndex = endIndex;\n  } else {\n    lowIndex = endIndex;\n    highIndex = startIndex;\n  }\n  const lowWholeIndex = Math.ceil(lowIndex);\n  const highWholeIndex = Math.floor(highIndex);\n\n  if (lowWholeIndex > highWholeIndex) {\n    // both start and end are on the same segment\n    const start = interpolateCoordinate(coordinates, lowIndex);\n    const end = interpolateCoordinate(coordinates, highIndex);\n    return getSquaredDistance(start, end);\n  }\n\n  let sd = 0;\n\n  if (lowIndex < lowWholeIndex) {\n    const start = interpolateCoordinate(coordinates, lowIndex);\n    const end = getCoordinate(coordinates, lowWholeIndex);\n    sd += getSquaredDistance(start, end);\n  }\n\n  if (highWholeIndex < highIndex) {\n    const start = getCoordinate(coordinates, highWholeIndex);\n    const end = interpolateCoordinate(coordinates, highIndex);\n    sd += getSquaredDistance(start, end);\n  }\n\n  for (let i = lowWholeIndex; i < highWholeIndex - 1; ++i) {\n    const start = getCoordinate(coordinates, i);\n    const end = getCoordinate(coordinates, i + 1);\n    sd += getSquaredDistance(start, end);\n  }\n\n  return sd;\n}\n\n/**\n * @param {import(\"../coordinate.js\").Coordinate} coordinate The coordinate.\n * @param {import(\"../geom/Geometry.js\").default} geometry The candidate geometry.\n * @param {Array<TraceTarget>} targets The trace targets.\n */\nfunction appendGeometryTraceTargets(coordinate, geometry, targets) {\n  if (geometry instanceof LineString) {\n    appendTraceTarget(coordinate, geometry.getCoordinates(), false, targets);\n    return;\n  }\n  if (geometry instanceof MultiLineString) {\n    const coordinates = geometry.getCoordinates();\n    for (let i = 0, ii = coordinates.length; i < ii; ++i) {\n      appendTraceTarget(coordinate, coordinates[i], false, targets);\n    }\n    return;\n  }\n  if (geometry instanceof Polygon) {\n    const coordinates = geometry.getCoordinates();\n    for (let i = 0, ii = coordinates.length; i < ii; ++i) {\n      appendTraceTarget(coordinate, coordinates[i], true, targets);\n    }\n    return;\n  }\n  if (geometry instanceof MultiPolygon) {\n    const polys = geometry.getCoordinates();\n    for (let i = 0, ii = polys.length; i < ii; ++i) {\n      const coordinates = polys[i];\n      for (let j = 0, jj = coordinates.length; j < jj; ++j) {\n        appendTraceTarget(coordinate, coordinates[j], true, targets);\n      }\n    }\n    return;\n  }\n  if (geometry instanceof GeometryCollection) {\n    const geometries = geometry.getGeometries();\n    for (let i = 0; i < geometries.length; ++i) {\n      appendGeometryTraceTargets(coordinate, geometries[i], targets);\n    }\n    return;\n  }\n  // other types cannot be traced\n}\n\n/**\n * @typedef {Object} TraceTargetUpdateInfo\n * @property {number} index The new target index.\n * @property {number} endIndex The new segment end index.\n */\n\n/**\n * @type {TraceTargetUpdateInfo}\n */\nconst sharedUpdateInfo = {index: -1, endIndex: NaN};\n\n/**\n * @param {import(\"../coordinate.js\").Coordinate} coordinate The coordinate.\n * @param {TraceState} traceState The trace state.\n * @param {import(\"../Map.js\").default} map The map.\n * @param {number} snapTolerance The snap tolerance.\n * @return {TraceTargetUpdateInfo} Information about the new trace target.  The returned\n * object is reused between calls and must not be modified by the caller.\n */\nfunction getTraceTargetUpdate(coordinate, traceState, map, snapTolerance) {\n  const x = coordinate[0];\n  const y = coordinate[1];\n\n  let closestTargetDistance = Infinity;\n\n  let newTargetIndex = -1;\n  let newEndIndex = NaN;\n\n  for (\n    let targetIndex = 0;\n    targetIndex < traceState.targets.length;\n    ++targetIndex\n  ) {\n    const target = traceState.targets[targetIndex];\n    const coordinates = target.coordinates;\n\n    let minSegmentDistance = Infinity;\n    let endIndex;\n    for (\n      let coordinateIndex = 0;\n      coordinateIndex < coordinates.length - 1;\n      ++coordinateIndex\n    ) {\n      const start = coordinates[coordinateIndex];\n      const end = coordinates[coordinateIndex + 1];\n      const rel = getPointSegmentRelationship(x, y, start, end);\n      if (rel.squaredDistance < minSegmentDistance) {\n        minSegmentDistance = rel.squaredDistance;\n        endIndex = coordinateIndex + rel.along;\n      }\n    }\n\n    if (minSegmentDistance < closestTargetDistance) {\n      closestTargetDistance = minSegmentDistance;\n      if (target.ring && traceState.targetIndex === targetIndex) {\n        // same target, maintain the same trace direction\n        if (target.endIndex > target.startIndex) {\n          // forward trace\n          if (endIndex < target.startIndex) {\n            endIndex += coordinates.length;\n          }\n        } else if (target.endIndex < target.startIndex) {\n          // reverse trace\n          if (endIndex > target.startIndex) {\n            endIndex -= coordinates.length;\n          }\n        }\n      }\n      newEndIndex = endIndex;\n      newTargetIndex = targetIndex;\n    }\n  }\n\n  const newTarget = traceState.targets[newTargetIndex];\n  let considerBothDirections = newTarget.ring;\n  if (traceState.targetIndex === newTargetIndex && considerBothDirections) {\n    // only consider switching trace direction if close to the start\n    const newCoordinate = interpolateCoordinate(\n      newTarget.coordinates,\n      newEndIndex\n    );\n    const pixel = map.getPixelFromCoordinate(newCoordinate);\n    if (distance(pixel, traceState.startPx) > snapTolerance) {\n      considerBothDirections = false;\n    }\n  }\n\n  if (considerBothDirections) {\n    const coordinates = newTarget.coordinates;\n    const count = coordinates.length;\n    const startIndex = newTarget.startIndex;\n    const endIndex = newEndIndex;\n    if (startIndex < endIndex) {\n      const forwardDistance = getCumulativeSquaredDistance(\n        coordinates,\n        startIndex,\n        endIndex\n      );\n      const reverseDistance = getCumulativeSquaredDistance(\n        coordinates,\n        startIndex,\n        endIndex - count\n      );\n      if (reverseDistance < forwardDistance) {\n        newEndIndex -= count;\n      }\n    } else {\n      const reverseDistance = getCumulativeSquaredDistance(\n        coordinates,\n        startIndex,\n        endIndex\n      );\n      const forwardDistance = getCumulativeSquaredDistance(\n        coordinates,\n        startIndex,\n        endIndex + count\n      );\n      if (forwardDistance < reverseDistance) {\n        newEndIndex += count;\n      }\n    }\n  }\n\n  sharedUpdateInfo.index = newTargetIndex;\n  sharedUpdateInfo.endIndex = newEndIndex;\n  return sharedUpdateInfo;\n}\n\n/**\n * @param {import(\"../coordinate.js\").Coordinate} coordinate The clicked coordinate.\n * @param {Array<import(\"../coordinate.js\").Coordinate>} coordinates The geometry component coordinates.\n * @param {boolean} ring The coordinates represent a linear ring.\n * @param {Array<TraceTarget>} targets The trace targets.\n */\nfunction appendTraceTarget(coordinate, coordinates, ring, targets) {\n  const x = coordinate[0];\n  const y = coordinate[1];\n  for (let i = 0, ii = coordinates.length - 1; i < ii; ++i) {\n    const start = coordinates[i];\n    const end = coordinates[i + 1];\n    const rel = getPointSegmentRelationship(x, y, start, end);\n    if (rel.squaredDistance === 0) {\n      const index = i + rel.along;\n      targets.push({\n        coordinates: coordinates,\n        ring: ring,\n        startIndex: index,\n        endIndex: index,\n      });\n      return;\n    }\n  }\n}\n\n/**\n * @typedef {Object} PointSegmentRelationship\n * @property {number} along The closest point expressed as a fraction along the segment length.\n * @property {number} squaredDistance The squared distance of the point to the segment.\n */\n\n/**\n * @type {PointSegmentRelationship}\n */\nconst sharedRel = {along: 0, squaredDistance: 0};\n\n/**\n * @param {number} x The point x.\n * @param {number} y The point y.\n * @param {import(\"../coordinate.js\").Coordinate} start The segment start.\n * @param {import(\"../coordinate.js\").Coordinate} end The segment end.\n * @return {PointSegmentRelationship} The point segment relationship.  The returned object is\n * shared between calls and must not be modified by the caller.\n */\nfunction getPointSegmentRelationship(x, y, start, end) {\n  const x1 = start[0];\n  const y1 = start[1];\n  const x2 = end[0];\n  const y2 = end[1];\n  const dx = x2 - x1;\n  const dy = y2 - y1;\n  let along = 0;\n  let px = x1;\n  let py = y1;\n  if (dx !== 0 || dy !== 0) {\n    along = clamp(((x - x1) * dx + (y - y1) * dy) / (dx * dx + dy * dy), 0, 1);\n    px += dx * along;\n    py += dy * along;\n  }\n\n  sharedRel.along = along;\n  sharedRel.squaredDistance = toFixed(squaredDistance(x, y, px, py), 10);\n  return sharedRel;\n}\n\n/**\n * @param {LineCoordType} coordinates The coordinates.\n * @param {number} index The index.  May be fractional and may wrap.\n * @return {import(\"../coordinate.js\").Coordinate} The interpolated coordinate.\n */\nfunction interpolateCoordinate(coordinates, index) {\n  const count = coordinates.length;\n\n  let startIndex = Math.floor(index);\n  const along = index - startIndex;\n  if (startIndex >= count) {\n    startIndex -= count;\n  } else if (startIndex < 0) {\n    startIndex += count;\n  }\n\n  let endIndex = startIndex + 1;\n  if (endIndex >= count) {\n    endIndex -= count;\n  }\n\n  const start = coordinates[startIndex];\n  const x0 = start[0];\n  const y0 = start[1];\n  const end = coordinates[endIndex];\n  const dx = end[0] - x0;\n  const dy = end[1] - y0;\n\n  return [x0 + dx * along, y0 + dy * along];\n}\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types|\n *     'change:active', import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<'drawabort'|'drawend'|'drawstart', DrawEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types|\n *     'change:active'|'drawabort'|'drawend'|'drawstart', Return>} DrawOnSignature\n */\n\n/**\n * @classdesc\n * Interaction for drawing feature geometries.\n *\n * @fires DrawEvent\n * @api\n */\nclass Draw extends PointerInteraction {\n  /**\n   * @param {Options} options Options.\n   */\n  constructor(options) {\n    const pointerOptions = /** @type {import(\"./Pointer.js\").Options} */ (\n      options\n    );\n    if (!pointerOptions.stopDown) {\n      pointerOptions.stopDown = FALSE;\n    }\n\n    super(pointerOptions);\n\n    /***\n     * @type {DrawOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {DrawOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {DrawOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.shouldHandle_ = false;\n\n    /**\n     * @type {import(\"../pixel.js\").Pixel}\n     * @private\n     */\n    this.downPx_ = null;\n\n    /**\n     * @type {ReturnType<typeof setTimeout>}\n     * @private\n     */\n    this.downTimeout_;\n\n    /**\n     * @type {number|undefined}\n     * @private\n     */\n    this.lastDragTime_;\n\n    /**\n     * Pointer type of the last pointermove event\n     * @type {string}\n     * @private\n     */\n    this.pointerType_;\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.freehand_ = false;\n\n    /**\n     * Target source for drawn features.\n     * @type {VectorSource|null}\n     * @private\n     */\n    this.source_ = options.source ? options.source : null;\n\n    /**\n     * Target collection for drawn features.\n     * @type {import(\"../Collection.js\").default<Feature>|null}\n     * @private\n     */\n    this.features_ = options.features ? options.features : null;\n\n    /**\n     * Pixel distance for snapping.\n     * @type {number}\n     * @private\n     */\n    this.snapTolerance_ = options.snapTolerance ? options.snapTolerance : 12;\n\n    /**\n     * Geometry type.\n     * @type {import(\"../geom/Geometry.js\").Type}\n     * @private\n     */\n    this.type_ = /** @type {import(\"../geom/Geometry.js\").Type} */ (\n      options.type\n    );\n\n    /**\n     * Drawing mode (derived from geometry type.\n     * @type {Mode}\n     * @private\n     */\n    this.mode_ = getMode(this.type_);\n\n    /**\n     * Stop click, singleclick, and doubleclick events from firing during drawing.\n     * Default is `false`.\n     * @type {boolean}\n     * @private\n     */\n    this.stopClick_ = !!options.stopClick;\n\n    /**\n     * The number of points that must be drawn before a polygon ring or line\n     * string can be finished.  The default is 3 for polygon rings and 2 for\n     * line strings.\n     * @type {number}\n     * @private\n     */\n    this.minPoints_ = options.minPoints\n      ? options.minPoints\n      : this.mode_ === 'Polygon'\n      ? 3\n      : 2;\n\n    /**\n     * The number of points that can be drawn before a polygon ring or line string\n     * is finished. The default is no restriction.\n     * @type {number}\n     * @private\n     */\n    this.maxPoints_ =\n      this.mode_ === 'Circle'\n        ? 2\n        : options.maxPoints\n        ? options.maxPoints\n        : Infinity;\n\n    /**\n     * A function to decide if a potential finish coordinate is permissible\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.finishCondition_ = options.finishCondition\n      ? options.finishCondition\n      : TRUE;\n\n    /**\n     * @private\n     * @type {import(\"../geom/Geometry.js\").GeometryLayout}\n     */\n    this.geometryLayout_ = options.geometryLayout\n      ? options.geometryLayout\n      : 'XY';\n\n    let geometryFunction = options.geometryFunction;\n    if (!geometryFunction) {\n      const mode = this.mode_;\n      if (mode === 'Circle') {\n        /**\n         * @param {!LineCoordType} coordinates The coordinates.\n         * @param {import(\"../geom/SimpleGeometry.js\").default|undefined} geometry Optional geometry.\n         * @param {import(\"../proj/Projection.js\").default} projection The view projection.\n         * @return {import(\"../geom/SimpleGeometry.js\").default} A geometry.\n         */\n        geometryFunction = function (coordinates, geometry, projection) {\n          const circle = geometry\n            ? /** @type {Circle} */ (geometry)\n            : new Circle([NaN, NaN]);\n          const center = fromUserCoordinate(coordinates[0], projection);\n          const squaredLength = squaredCoordinateDistance(\n            center,\n            fromUserCoordinate(coordinates[coordinates.length - 1], projection)\n          );\n          circle.setCenterAndRadius(\n            center,\n            Math.sqrt(squaredLength),\n            this.geometryLayout_\n          );\n          const userProjection = getUserProjection();\n          if (userProjection) {\n            circle.transform(projection, userProjection);\n          }\n          return circle;\n        };\n      } else {\n        let Constructor;\n        if (mode === 'Point') {\n          Constructor = Point;\n        } else if (mode === 'LineString') {\n          Constructor = LineString;\n        } else if (mode === 'Polygon') {\n          Constructor = Polygon;\n        }\n        /**\n         * @param {!LineCoordType} coordinates The coordinates.\n         * @param {import(\"../geom/SimpleGeometry.js\").default|undefined} geometry Optional geometry.\n         * @param {import(\"../proj/Projection.js\").default} projection The view projection.\n         * @return {import(\"../geom/SimpleGeometry.js\").default} A geometry.\n         */\n        geometryFunction = function (coordinates, geometry, projection) {\n          if (geometry) {\n            if (mode === 'Polygon') {\n              if (coordinates[0].length) {\n                // Add a closing coordinate to match the first\n                geometry.setCoordinates(\n                  [coordinates[0].concat([coordinates[0][0]])],\n                  this.geometryLayout_\n                );\n              } else {\n                geometry.setCoordinates([], this.geometryLayout_);\n              }\n            } else {\n              geometry.setCoordinates(coordinates, this.geometryLayout_);\n            }\n          } else {\n            geometry = new Constructor(coordinates, this.geometryLayout_);\n          }\n          return geometry;\n        };\n      }\n    }\n\n    /**\n     * @type {GeometryFunction}\n     * @private\n     */\n    this.geometryFunction_ = geometryFunction;\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.dragVertexDelay_ =\n      options.dragVertexDelay !== undefined ? options.dragVertexDelay : 500;\n\n    /**\n     * Finish coordinate for the feature (first point for polygons, last point for\n     * linestrings).\n     * @type {import(\"../coordinate.js\").Coordinate}\n     * @private\n     */\n    this.finishCoordinate_ = null;\n\n    /**\n     * Sketch feature.\n     * @type {Feature<import('../geom/SimpleGeometry.js').default>}\n     * @private\n     */\n    this.sketchFeature_ = null;\n\n    /**\n     * Sketch point.\n     * @type {Feature<Point>}\n     * @private\n     */\n    this.sketchPoint_ = null;\n\n    /**\n     * Sketch coordinates. Used when drawing a line or polygon.\n     * @type {SketchCoordType}\n     * @private\n     */\n    this.sketchCoords_ = null;\n\n    /**\n     * Sketch line. Used when drawing polygon.\n     * @type {Feature<LineString>}\n     * @private\n     */\n    this.sketchLine_ = null;\n\n    /**\n     * Sketch line coordinates. Used when drawing a polygon or circle.\n     * @type {LineCoordType}\n     * @private\n     */\n    this.sketchLineCoords_ = null;\n\n    /**\n     * Squared tolerance for handling up events.  If the squared distance\n     * between a down and up event is greater than this tolerance, up events\n     * will not be handled.\n     * @type {number}\n     * @private\n     */\n    this.squaredClickTolerance_ = options.clickTolerance\n      ? options.clickTolerance * options.clickTolerance\n      : 36;\n\n    /**\n     * Draw overlay where our sketch features are drawn.\n     * @type {VectorLayer}\n     * @private\n     */\n    this.overlay_ = new VectorLayer({\n      source: new VectorSource({\n        useSpatialIndex: false,\n        wrapX: options.wrapX ? options.wrapX : false,\n      }),\n      style: options.style ? options.style : getDefaultStyleFunction(),\n      updateWhileInteracting: true,\n    });\n\n    /**\n     * Name of the geometry attribute for newly created features.\n     * @type {string|undefined}\n     * @private\n     */\n    this.geometryName_ = options.geometryName;\n\n    /**\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.condition_ = options.condition ? options.condition : noModifierKeys;\n\n    /**\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.freehandCondition_;\n    if (options.freehand) {\n      this.freehandCondition_ = always;\n    } else {\n      this.freehandCondition_ = options.freehandCondition\n        ? options.freehandCondition\n        : shiftKeyOnly;\n    }\n\n    /**\n     * @type {import(\"../events/condition.js\").Condition}\n     * @private\n     */\n    this.traceCondition_;\n    this.setTrace(options.trace || false);\n\n    /**\n     * @type {TraceState}\n     * @private\n     */\n    this.traceState_ = {active: false};\n\n    /**\n     * @type {VectorSource|null}\n     * @private\n     */\n    this.traceSource_ = options.traceSource || options.source || null;\n\n    this.addChangeListener(InteractionProperty.ACTIVE, this.updateState_);\n  }\n\n  /**\n   * Toggle tracing mode or set a tracing condition.\n   *\n   * @param {boolean|import(\"../events/condition.js\").Condition} trace A boolean to toggle tracing mode or an event\n   *     condition that will be checked when a feature is clicked to determine if tracing should be active.\n   */\n  setTrace(trace) {\n    let condition;\n    if (!trace) {\n      condition = never;\n    } else if (trace === true) {\n      condition = always;\n    } else {\n      condition = trace;\n    }\n    this.traceCondition_ = condition;\n  }\n\n  /**\n   * Remove the interaction from its current map and attach it to the new map.\n   * Subclasses may set up event handlers to get notified about changes to\n   * the map here.\n   * @param {import(\"../Map.js\").default} map Map.\n   */\n  setMap(map) {\n    super.setMap(map);\n    this.updateState_();\n  }\n\n  /**\n   * Get the overlay layer that this interaction renders sketch features to.\n   * @return {VectorLayer} Overlay layer.\n   * @api\n   */\n  getOverlay() {\n    return this.overlay_;\n  }\n\n  /**\n   * Handles the {@link module:ol/MapBrowserEvent~MapBrowserEvent map browser event} and may actually draw or finish the drawing.\n   * @param {import(\"../MapBrowserEvent.js\").default} event Map browser event.\n   * @return {boolean} `false` to stop event propagation.\n   * @api\n   */\n  handleEvent(event) {\n    if (event.originalEvent.type === EventType.CONTEXTMENU) {\n      // Avoid context menu for long taps when drawing on mobile\n      event.originalEvent.preventDefault();\n    }\n    this.freehand_ = this.mode_ !== 'Point' && this.freehandCondition_(event);\n    let move = event.type === MapBrowserEventType.POINTERMOVE;\n    let pass = true;\n    if (\n      !this.freehand_ &&\n      this.lastDragTime_ &&\n      event.type === MapBrowserEventType.POINTERDRAG\n    ) {\n      const now = Date.now();\n      if (now - this.lastDragTime_ >= this.dragVertexDelay_) {\n        this.downPx_ = event.pixel;\n        this.shouldHandle_ = !this.freehand_;\n        move = true;\n      } else {\n        this.lastDragTime_ = undefined;\n      }\n      if (this.shouldHandle_ && this.downTimeout_ !== undefined) {\n        clearTimeout(this.downTimeout_);\n        this.downTimeout_ = undefined;\n      }\n    }\n    if (\n      this.freehand_ &&\n      event.type === MapBrowserEventType.POINTERDRAG &&\n      this.sketchFeature_ !== null\n    ) {\n      this.addToDrawing_(event.coordinate);\n      pass = false;\n    } else if (\n      this.freehand_ &&\n      event.type === MapBrowserEventType.POINTERDOWN\n    ) {\n      pass = false;\n    } else if (move && this.getPointerCount() < 2) {\n      pass = event.type === MapBrowserEventType.POINTERMOVE;\n      if (pass && this.freehand_) {\n        this.handlePointerMove_(event);\n        if (this.shouldHandle_) {\n          // Avoid page scrolling when freehand drawing on mobile\n          event.originalEvent.preventDefault();\n        }\n      } else if (\n        event.originalEvent.pointerType === 'mouse' ||\n        (event.type === MapBrowserEventType.POINTERDRAG &&\n          this.downTimeout_ === undefined)\n      ) {\n        this.handlePointerMove_(event);\n      }\n    } else if (event.type === MapBrowserEventType.DBLCLICK) {\n      pass = false;\n    }\n\n    return super.handleEvent(event) && pass;\n  }\n\n  /**\n   * Handle pointer down events.\n   * @param {import(\"../MapBrowserEvent.js\").default} event Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleDownEvent(event) {\n    this.shouldHandle_ = !this.freehand_;\n\n    if (this.freehand_) {\n      this.downPx_ = event.pixel;\n      if (!this.finishCoordinate_) {\n        this.startDrawing_(event.coordinate);\n      }\n      return true;\n    }\n\n    if (!this.condition_(event)) {\n      this.lastDragTime_ = undefined;\n      return false;\n    }\n\n    this.lastDragTime_ = Date.now();\n    this.downTimeout_ = setTimeout(() => {\n      this.handlePointerMove_(\n        new MapBrowserEvent(\n          MapBrowserEventType.POINTERMOVE,\n          event.map,\n          event.originalEvent,\n          false,\n          event.frameState\n        )\n      );\n    }, this.dragVertexDelay_);\n    this.downPx_ = event.pixel;\n    return true;\n  }\n\n  /**\n   * @private\n   */\n  deactivateTrace_() {\n    this.traceState_ = {active: false};\n  }\n\n  /**\n   * Activate or deactivate trace state based on a browser event.\n   * @param {import(\"../MapBrowserEvent.js\").default} event Event.\n   * @private\n   */\n  toggleTraceState_(event) {\n    if (!this.traceSource_ || !this.traceCondition_(event)) {\n      return;\n    }\n\n    if (this.traceState_.active) {\n      this.deactivateTrace_();\n      return;\n    }\n\n    const map = this.getMap();\n    const lowerLeft = map.getCoordinateFromPixel([\n      event.pixel[0] - this.snapTolerance_,\n      event.pixel[1] + this.snapTolerance_,\n    ]);\n    const upperRight = map.getCoordinateFromPixel([\n      event.pixel[0] + this.snapTolerance_,\n      event.pixel[1] - this.snapTolerance_,\n    ]);\n    const extent = boundingExtent([lowerLeft, upperRight]);\n    const features = this.traceSource_.getFeaturesInExtent(extent);\n    if (features.length === 0) {\n      return;\n    }\n\n    const targets = getTraceTargets(event.coordinate, features);\n    if (targets.length) {\n      this.traceState_ = {\n        active: true,\n        startPx: event.pixel.slice(),\n        targets: targets,\n        targetIndex: -1,\n      };\n    }\n  }\n\n  /**\n   * @param {TraceTarget} target The trace target.\n   * @param {number} endIndex The new end index of the trace.\n   * @private\n   */\n  addOrRemoveTracedCoordinates_(target, endIndex) {\n    // three cases to handle:\n    //  1. traced in the same direction and points need adding\n    //  2. traced in the same direction and points need removing\n    //  3. traced in a new direction\n    const previouslyForward = target.startIndex <= target.endIndex;\n    const currentlyForward = target.startIndex <= endIndex;\n    if (previouslyForward === currentlyForward) {\n      // same direction\n      if (\n        (previouslyForward && endIndex > target.endIndex) ||\n        (!previouslyForward && endIndex < target.endIndex)\n      ) {\n        // case 1 - add new points\n        this.addTracedCoordinates_(target, target.endIndex, endIndex);\n      } else if (\n        (previouslyForward && endIndex < target.endIndex) ||\n        (!previouslyForward && endIndex > target.endIndex)\n      ) {\n        // case 2 - remove old points\n        this.removeTracedCoordinates_(endIndex, target.endIndex);\n      }\n    } else {\n      // case 3 - remove old points, add new points\n      this.removeTracedCoordinates_(target.startIndex, target.endIndex);\n      this.addTracedCoordinates_(target, target.startIndex, endIndex);\n    }\n  }\n\n  /**\n   * @param {number} fromIndex The start index.\n   * @param {number} toIndex The end index.\n   * @private\n   */\n  removeTracedCoordinates_(fromIndex, toIndex) {\n    if (fromIndex === toIndex) {\n      return;\n    }\n\n    let remove = 0;\n    if (fromIndex < toIndex) {\n      const start = Math.ceil(fromIndex);\n      let end = Math.floor(toIndex);\n      if (end === toIndex) {\n        end -= 1;\n      }\n      remove = end - start + 1;\n    } else {\n      const start = Math.floor(fromIndex);\n      let end = Math.ceil(toIndex);\n      if (end === toIndex) {\n        end += 1;\n      }\n      remove = start - end + 1;\n    }\n\n    if (remove > 0) {\n      this.removeLastPoints_(remove);\n    }\n  }\n\n  /**\n   * @param {TraceTarget} target The trace target.\n   * @param {number} fromIndex The start index.\n   * @param {number} toIndex The end index.\n   * @private\n   */\n  addTracedCoordinates_(target, fromIndex, toIndex) {\n    if (fromIndex === toIndex) {\n      return;\n    }\n\n    const coordinates = [];\n    if (fromIndex < toIndex) {\n      // forward trace\n      const start = Math.ceil(fromIndex);\n      let end = Math.floor(toIndex);\n      if (end === toIndex) {\n        // if end is snapped to a vertex, it will be added later\n        end -= 1;\n      }\n      for (let i = start; i <= end; ++i) {\n        coordinates.push(getCoordinate(target.coordinates, i));\n      }\n    } else {\n      // reverse trace\n      const start = Math.floor(fromIndex);\n      let end = Math.ceil(toIndex);\n      if (end === toIndex) {\n        end += 1;\n      }\n      for (let i = start; i >= end; --i) {\n        coordinates.push(getCoordinate(target.coordinates, i));\n      }\n    }\n    if (coordinates.length) {\n      this.appendCoordinates(coordinates);\n    }\n  }\n\n  /**\n   * Update the trace.\n   * @param {import(\"../MapBrowserEvent.js\").default} event Event.\n   * @private\n   */\n  updateTrace_(event) {\n    const traceState = this.traceState_;\n    if (!traceState.active) {\n      return;\n    }\n\n    if (traceState.targetIndex === -1) {\n      // check if we are ready to pick a target\n      if (distance(traceState.startPx, event.pixel) < this.snapTolerance_) {\n        return;\n      }\n    }\n\n    const updatedTraceTarget = getTraceTargetUpdate(\n      event.coordinate,\n      traceState,\n      this.getMap(),\n      this.snapTolerance_\n    );\n\n    if (traceState.targetIndex !== updatedTraceTarget.index) {\n      // target changed\n      if (traceState.targetIndex !== -1) {\n        // remove points added during previous trace\n        const oldTarget = traceState.targets[traceState.targetIndex];\n        this.removeTracedCoordinates_(oldTarget.startIndex, oldTarget.endIndex);\n      }\n      // add points for the new target\n      const newTarget = traceState.targets[updatedTraceTarget.index];\n      this.addTracedCoordinates_(\n        newTarget,\n        newTarget.startIndex,\n        updatedTraceTarget.endIndex\n      );\n    } else {\n      // target stayed the same\n      const target = traceState.targets[traceState.targetIndex];\n      this.addOrRemoveTracedCoordinates_(target, updatedTraceTarget.endIndex);\n    }\n\n    // modify the state with updated info\n    traceState.targetIndex = updatedTraceTarget.index;\n    const target = traceState.targets[traceState.targetIndex];\n    target.endIndex = updatedTraceTarget.endIndex;\n\n    // update event coordinate and pixel to match end point of final segment\n    const coordinate = interpolateCoordinate(\n      target.coordinates,\n      target.endIndex\n    );\n    const pixel = this.getMap().getPixelFromCoordinate(coordinate);\n    event.coordinate = coordinate;\n    event.pixel = [Math.round(pixel[0]), Math.round(pixel[1])];\n  }\n\n  /**\n   * Handle pointer up events.\n   * @param {import(\"../MapBrowserEvent.js\").default} event Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleUpEvent(event) {\n    let pass = true;\n\n    if (this.getPointerCount() === 0) {\n      if (this.downTimeout_) {\n        clearTimeout(this.downTimeout_);\n        this.downTimeout_ = undefined;\n      }\n\n      this.handlePointerMove_(event);\n      const tracing = this.traceState_.active;\n      this.toggleTraceState_(event);\n\n      if (this.shouldHandle_) {\n        const startingToDraw = !this.finishCoordinate_;\n        if (startingToDraw) {\n          this.startDrawing_(event.coordinate);\n        }\n        if (!startingToDraw && this.freehand_) {\n          this.finishDrawing();\n        } else if (\n          !this.freehand_ &&\n          (!startingToDraw || this.mode_ === 'Point')\n        ) {\n          if (this.atFinish_(event.pixel, tracing)) {\n            if (this.finishCondition_(event)) {\n              this.finishDrawing();\n            }\n          } else {\n            this.addToDrawing_(event.coordinate);\n          }\n        }\n        pass = false;\n      } else if (this.freehand_) {\n        this.abortDrawing();\n      }\n    }\n\n    if (!pass && this.stopClick_) {\n      event.preventDefault();\n    }\n    return pass;\n  }\n\n  /**\n   * Handle move events.\n   * @param {import(\"../MapBrowserEvent.js\").default} event A move event.\n   * @private\n   */\n  handlePointerMove_(event) {\n    this.pointerType_ = event.originalEvent.pointerType;\n    if (\n      this.downPx_ &&\n      ((!this.freehand_ && this.shouldHandle_) ||\n        (this.freehand_ && !this.shouldHandle_))\n    ) {\n      const downPx = this.downPx_;\n      const clickPx = event.pixel;\n      const dx = downPx[0] - clickPx[0];\n      const dy = downPx[1] - clickPx[1];\n      const squaredDistance = dx * dx + dy * dy;\n      this.shouldHandle_ = this.freehand_\n        ? squaredDistance > this.squaredClickTolerance_\n        : squaredDistance <= this.squaredClickTolerance_;\n      if (!this.shouldHandle_) {\n        return;\n      }\n    }\n\n    if (!this.finishCoordinate_) {\n      this.createOrUpdateSketchPoint_(event.coordinate.slice());\n      return;\n    }\n\n    this.updateTrace_(event);\n    this.modifyDrawing_(event.coordinate);\n  }\n\n  /**\n   * Determine if an event is within the snapping tolerance of the start coord.\n   * @param {import(\"../pixel.js\").Pixel} pixel Pixel.\n   * @param {boolean} [tracing] Drawing in trace mode (only stop if at the starting point).\n   * @return {boolean} The event is within the snapping tolerance of the start.\n   * @private\n   */\n  atFinish_(pixel, tracing) {\n    let at = false;\n    if (this.sketchFeature_) {\n      let potentiallyDone = false;\n      let potentiallyFinishCoordinates = [this.finishCoordinate_];\n      const mode = this.mode_;\n      if (mode === 'Point') {\n        at = true;\n      } else if (mode === 'Circle') {\n        at = this.sketchCoords_.length === 2;\n      } else if (mode === 'LineString') {\n        potentiallyDone =\n          !tracing && this.sketchCoords_.length > this.minPoints_;\n      } else if (mode === 'Polygon') {\n        const sketchCoords = /** @type {PolyCoordType} */ (this.sketchCoords_);\n        potentiallyDone = sketchCoords[0].length > this.minPoints_;\n        potentiallyFinishCoordinates = [\n          sketchCoords[0][0],\n          sketchCoords[0][sketchCoords[0].length - 2],\n        ];\n        if (tracing) {\n          potentiallyFinishCoordinates = [sketchCoords[0][0]];\n        } else {\n          potentiallyFinishCoordinates = [\n            sketchCoords[0][0],\n            sketchCoords[0][sketchCoords[0].length - 2],\n          ];\n        }\n      }\n      if (potentiallyDone) {\n        const map = this.getMap();\n        for (let i = 0, ii = potentiallyFinishCoordinates.length; i < ii; i++) {\n          const finishCoordinate = potentiallyFinishCoordinates[i];\n          const finishPixel = map.getPixelFromCoordinate(finishCoordinate);\n          const dx = pixel[0] - finishPixel[0];\n          const dy = pixel[1] - finishPixel[1];\n          const snapTolerance = this.freehand_ ? 1 : this.snapTolerance_;\n          at = Math.sqrt(dx * dx + dy * dy) <= snapTolerance;\n          if (at) {\n            this.finishCoordinate_ = finishCoordinate;\n            break;\n          }\n        }\n      }\n    }\n    return at;\n  }\n\n  /**\n   * @param {import(\"../coordinate\").Coordinate} coordinates Coordinate.\n   * @private\n   */\n  createOrUpdateSketchPoint_(coordinates) {\n    if (!this.sketchPoint_) {\n      this.sketchPoint_ = new Feature(new Point(coordinates));\n      this.updateSketchFeatures_();\n    } else {\n      const sketchPointGeom = this.sketchPoint_.getGeometry();\n      sketchPointGeom.setCoordinates(coordinates);\n    }\n  }\n\n  /**\n   * @param {import(\"../geom/Polygon.js\").default} geometry Polygon geometry.\n   * @private\n   */\n  createOrUpdateCustomSketchLine_(geometry) {\n    if (!this.sketchLine_) {\n      this.sketchLine_ = new Feature();\n    }\n    const ring = geometry.getLinearRing(0);\n    let sketchLineGeom = this.sketchLine_.getGeometry();\n    if (!sketchLineGeom) {\n      sketchLineGeom = new LineString(\n        ring.getFlatCoordinates(),\n        ring.getLayout()\n      );\n      this.sketchLine_.setGeometry(sketchLineGeom);\n    } else {\n      sketchLineGeom.setFlatCoordinates(\n        ring.getLayout(),\n        ring.getFlatCoordinates()\n      );\n      sketchLineGeom.changed();\n    }\n  }\n\n  /**\n   * Start the drawing.\n   * @param {import(\"../coordinate.js\").Coordinate} start Start coordinate.\n   * @private\n   */\n  startDrawing_(start) {\n    const projection = this.getMap().getView().getProjection();\n    const stride = getStrideForLayout(this.geometryLayout_);\n    while (start.length < stride) {\n      start.push(0);\n    }\n    this.finishCoordinate_ = start;\n    if (this.mode_ === 'Point') {\n      this.sketchCoords_ = start.slice();\n    } else if (this.mode_ === 'Polygon') {\n      this.sketchCoords_ = [[start.slice(), start.slice()]];\n      this.sketchLineCoords_ = this.sketchCoords_[0];\n    } else {\n      this.sketchCoords_ = [start.slice(), start.slice()];\n    }\n    if (this.sketchLineCoords_) {\n      this.sketchLine_ = new Feature(new LineString(this.sketchLineCoords_));\n    }\n    const geometry = this.geometryFunction_(\n      this.sketchCoords_,\n      undefined,\n      projection\n    );\n    this.sketchFeature_ = new Feature();\n    if (this.geometryName_) {\n      this.sketchFeature_.setGeometryName(this.geometryName_);\n    }\n    this.sketchFeature_.setGeometry(geometry);\n    this.updateSketchFeatures_();\n    this.dispatchEvent(\n      new DrawEvent(DrawEventType.DRAWSTART, this.sketchFeature_)\n    );\n  }\n\n  /**\n   * Modify the drawing.\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @private\n   */\n  modifyDrawing_(coordinate) {\n    const map = this.getMap();\n    const geometry = this.sketchFeature_.getGeometry();\n    const projection = map.getView().getProjection();\n    const stride = getStrideForLayout(this.geometryLayout_);\n    let coordinates, last;\n    while (coordinate.length < stride) {\n      coordinate.push(0);\n    }\n    if (this.mode_ === 'Point') {\n      last = this.sketchCoords_;\n    } else if (this.mode_ === 'Polygon') {\n      coordinates = /** @type {PolyCoordType} */ (this.sketchCoords_)[0];\n      last = coordinates[coordinates.length - 1];\n      if (this.atFinish_(map.getPixelFromCoordinate(coordinate))) {\n        // snap to finish\n        coordinate = this.finishCoordinate_.slice();\n      }\n    } else {\n      coordinates = this.sketchCoords_;\n      last = coordinates[coordinates.length - 1];\n    }\n    last[0] = coordinate[0];\n    last[1] = coordinate[1];\n    this.geometryFunction_(\n      /** @type {!LineCoordType} */ (this.sketchCoords_),\n      geometry,\n      projection\n    );\n    if (this.sketchPoint_) {\n      const sketchPointGeom = this.sketchPoint_.getGeometry();\n      sketchPointGeom.setCoordinates(coordinate);\n    }\n    if (geometry.getType() === 'Polygon' && this.mode_ !== 'Polygon') {\n      this.createOrUpdateCustomSketchLine_(/** @type {Polygon} */ (geometry));\n    } else if (this.sketchLineCoords_) {\n      const sketchLineGeom = this.sketchLine_.getGeometry();\n      sketchLineGeom.setCoordinates(this.sketchLineCoords_);\n    }\n    this.updateSketchFeatures_();\n  }\n\n  /**\n   * Add a new coordinate to the drawing.\n   * @param {!PointCoordType} coordinate Coordinate\n   * @private\n   */\n  addToDrawing_(coordinate) {\n    const geometry = this.sketchFeature_.getGeometry();\n    const projection = this.getMap().getView().getProjection();\n    let done;\n    let coordinates;\n    const mode = this.mode_;\n    if (mode === 'LineString' || mode === 'Circle') {\n      this.finishCoordinate_ = coordinate.slice();\n      coordinates = /** @type {LineCoordType} */ (this.sketchCoords_);\n      if (coordinates.length >= this.maxPoints_) {\n        if (this.freehand_) {\n          coordinates.pop();\n        } else {\n          done = true;\n        }\n      }\n      coordinates.push(coordinate.slice());\n      this.geometryFunction_(coordinates, geometry, projection);\n    } else if (mode === 'Polygon') {\n      coordinates = /** @type {PolyCoordType} */ (this.sketchCoords_)[0];\n      if (coordinates.length >= this.maxPoints_) {\n        if (this.freehand_) {\n          coordinates.pop();\n        } else {\n          done = true;\n        }\n      }\n      coordinates.push(coordinate.slice());\n      if (done) {\n        this.finishCoordinate_ = coordinates[0];\n      }\n      this.geometryFunction_(this.sketchCoords_, geometry, projection);\n    }\n    this.createOrUpdateSketchPoint_(coordinate.slice());\n    this.updateSketchFeatures_();\n    if (done) {\n      this.finishDrawing();\n    }\n  }\n\n  /**\n   * @param {number} n The number of points to remove.\n   */\n  removeLastPoints_(n) {\n    if (!this.sketchFeature_) {\n      return;\n    }\n    const geometry = this.sketchFeature_.getGeometry();\n    const projection = this.getMap().getView().getProjection();\n    const mode = this.mode_;\n    for (let i = 0; i < n; ++i) {\n      let coordinates;\n      if (mode === 'LineString' || mode === 'Circle') {\n        coordinates = /** @type {LineCoordType} */ (this.sketchCoords_);\n        coordinates.splice(-2, 1);\n        if (coordinates.length >= 2) {\n          this.finishCoordinate_ = coordinates[coordinates.length - 2].slice();\n          const finishCoordinate = this.finishCoordinate_.slice();\n          coordinates[coordinates.length - 1] = finishCoordinate;\n          this.createOrUpdateSketchPoint_(finishCoordinate);\n        }\n        this.geometryFunction_(coordinates, geometry, projection);\n        if (geometry.getType() === 'Polygon' && this.sketchLine_) {\n          this.createOrUpdateCustomSketchLine_(\n            /** @type {Polygon} */ (geometry)\n          );\n        }\n      } else if (mode === 'Polygon') {\n        coordinates = /** @type {PolyCoordType} */ (this.sketchCoords_)[0];\n        coordinates.splice(-2, 1);\n        const sketchLineGeom = this.sketchLine_.getGeometry();\n        if (coordinates.length >= 2) {\n          const finishCoordinate = coordinates[coordinates.length - 2].slice();\n          coordinates[coordinates.length - 1] = finishCoordinate;\n          this.createOrUpdateSketchPoint_(finishCoordinate);\n        }\n        sketchLineGeom.setCoordinates(coordinates);\n        this.geometryFunction_(this.sketchCoords_, geometry, projection);\n      }\n\n      if (coordinates.length === 1) {\n        this.abortDrawing();\n        break;\n      }\n    }\n\n    this.updateSketchFeatures_();\n  }\n\n  /**\n   * Remove last point of the feature currently being drawn. Does not do anything when\n   * drawing POINT or MULTI_POINT geometries.\n   * @api\n   */\n  removeLastPoint() {\n    this.removeLastPoints_(1);\n  }\n\n  /**\n   * Stop drawing and add the sketch feature to the target layer.\n   * The {@link module:ol/interaction/Draw~DrawEventType.DRAWEND} event is\n   * dispatched before inserting the feature.\n   * @api\n   */\n  finishDrawing() {\n    const sketchFeature = this.abortDrawing_();\n    if (!sketchFeature) {\n      return;\n    }\n    let coordinates = this.sketchCoords_;\n    const geometry = sketchFeature.getGeometry();\n    const projection = this.getMap().getView().getProjection();\n    if (this.mode_ === 'LineString') {\n      // remove the redundant last point\n      coordinates.pop();\n      this.geometryFunction_(coordinates, geometry, projection);\n    } else if (this.mode_ === 'Polygon') {\n      // remove the redundant last point in ring\n      /** @type {PolyCoordType} */ (coordinates)[0].pop();\n      this.geometryFunction_(coordinates, geometry, projection);\n      coordinates = geometry.getCoordinates();\n    }\n\n    // cast multi-part geometries\n    if (this.type_ === 'MultiPoint') {\n      sketchFeature.setGeometry(\n        new MultiPoint([/** @type {PointCoordType} */ (coordinates)])\n      );\n    } else if (this.type_ === 'MultiLineString') {\n      sketchFeature.setGeometry(\n        new MultiLineString([/** @type {LineCoordType} */ (coordinates)])\n      );\n    } else if (this.type_ === 'MultiPolygon') {\n      sketchFeature.setGeometry(\n        new MultiPolygon([/** @type {PolyCoordType} */ (coordinates)])\n      );\n    }\n\n    // First dispatch event to allow full set up of feature\n    this.dispatchEvent(new DrawEvent(DrawEventType.DRAWEND, sketchFeature));\n\n    // Then insert feature\n    if (this.features_) {\n      this.features_.push(sketchFeature);\n    }\n    if (this.source_) {\n      this.source_.addFeature(sketchFeature);\n    }\n  }\n\n  /**\n   * Stop drawing without adding the sketch feature to the target layer.\n   * @return {Feature<import(\"../geom/SimpleGeometry.js\").default>|null} The sketch feature (or null if none).\n   * @private\n   */\n  abortDrawing_() {\n    this.finishCoordinate_ = null;\n    const sketchFeature = this.sketchFeature_;\n    this.sketchFeature_ = null;\n    this.sketchPoint_ = null;\n    this.sketchLine_ = null;\n    this.overlay_.getSource().clear(true);\n    this.deactivateTrace_();\n    return sketchFeature;\n  }\n\n  /**\n   * Stop drawing without adding the sketch feature to the target layer.\n   * @api\n   */\n  abortDrawing() {\n    const sketchFeature = this.abortDrawing_();\n    if (sketchFeature) {\n      this.dispatchEvent(new DrawEvent(DrawEventType.DRAWABORT, sketchFeature));\n    }\n  }\n\n  /**\n   * Append coordinates to the end of the geometry that is currently being drawn.\n   * This can be used when drawing LineStrings or Polygons. Coordinates will\n   * either be appended to the current LineString or the outer ring of the current\n   * Polygon. If no geometry is being drawn, a new one will be created.\n   * @param {!LineCoordType} coordinates Linear coordinates to be appended to\n   * the coordinate array.\n   * @api\n   */\n  appendCoordinates(coordinates) {\n    const mode = this.mode_;\n    const newDrawing = !this.sketchFeature_;\n    if (newDrawing) {\n      this.startDrawing_(coordinates[0]);\n    }\n    /** @type {LineCoordType} */\n    let sketchCoords;\n    if (mode === 'LineString' || mode === 'Circle') {\n      sketchCoords = /** @type {LineCoordType} */ (this.sketchCoords_);\n    } else if (mode === 'Polygon') {\n      sketchCoords =\n        this.sketchCoords_ && this.sketchCoords_.length\n          ? /** @type {PolyCoordType} */ (this.sketchCoords_)[0]\n          : [];\n    } else {\n      return;\n    }\n\n    if (newDrawing) {\n      sketchCoords.shift();\n    }\n\n    // Remove last coordinate from sketch drawing (this coordinate follows cursor position)\n    sketchCoords.pop();\n\n    // Append coordinate list\n    for (let i = 0; i < coordinates.length; i++) {\n      this.addToDrawing_(coordinates[i]);\n    }\n\n    const ending = coordinates[coordinates.length - 1];\n    // Duplicate last coordinate for sketch drawing (cursor position)\n    this.addToDrawing_(ending);\n    this.modifyDrawing_(ending);\n  }\n\n  /**\n   * Initiate draw mode by starting from an existing geometry which will\n   * receive new additional points. This only works on features with\n   * `LineString` geometries, where the interaction will extend lines by adding\n   * points to the end of the coordinates array.\n   * This will change the original feature, instead of drawing a copy.\n   *\n   * The function will dispatch a `drawstart` event.\n   *\n   * @param {!Feature<LineString>} feature Feature to be extended.\n   * @api\n   */\n  extend(feature) {\n    const geometry = feature.getGeometry();\n    const lineString = geometry;\n    this.sketchFeature_ = feature;\n    this.sketchCoords_ = lineString.getCoordinates();\n    const last = this.sketchCoords_[this.sketchCoords_.length - 1];\n    this.finishCoordinate_ = last.slice();\n    this.sketchCoords_.push(last.slice());\n    this.sketchPoint_ = new Feature(new Point(last));\n    this.updateSketchFeatures_();\n    this.dispatchEvent(\n      new DrawEvent(DrawEventType.DRAWSTART, this.sketchFeature_)\n    );\n  }\n\n  /**\n   * Redraw the sketch features.\n   * @private\n   */\n  updateSketchFeatures_() {\n    const sketchFeatures = [];\n    if (this.sketchFeature_) {\n      sketchFeatures.push(this.sketchFeature_);\n    }\n    if (this.sketchLine_) {\n      sketchFeatures.push(this.sketchLine_);\n    }\n    if (this.sketchPoint_) {\n      sketchFeatures.push(this.sketchPoint_);\n    }\n    const overlaySource = this.overlay_.getSource();\n    overlaySource.clear(true);\n    overlaySource.addFeatures(sketchFeatures);\n  }\n\n  /**\n   * @private\n   */\n  updateState_() {\n    const map = this.getMap();\n    const active = this.getActive();\n    if (!map || !active) {\n      this.abortDrawing();\n    }\n    this.overlay_.setMap(active ? map : null);\n  }\n}\n\n/**\n * @return {import(\"../style/Style.js\").StyleFunction} Styles.\n */\nfunction getDefaultStyleFunction() {\n  const styles = createEditingStyle();\n  return function (feature, resolution) {\n    return styles[feature.getGeometry().getType()];\n  };\n}\n\n/**\n * Create a `geometryFunction` for `type: 'Circle'` that will create a regular\n * polygon with a user specified number of sides and start angle instead of a\n * {@link import(\"../geom/Circle.js\").Circle} geometry.\n * @param {number} [sides] Number of sides of the regular polygon.\n *     Default is 32.\n * @param {number} [angle] Angle of the first point in counter-clockwise\n *     radians. 0 means East.\n *     Default is the angle defined by the heading from the center of the\n *     regular polygon to the current pointer position.\n * @return {GeometryFunction} Function that draws a polygon.\n * @api\n */\nexport function createRegularPolygon(sides, angle) {\n  return function (coordinates, geometry, projection) {\n    const center = fromUserCoordinate(\n      /** @type {LineCoordType} */ (coordinates)[0],\n      projection\n    );\n    const end = fromUserCoordinate(\n      /** @type {LineCoordType} */ (coordinates)[coordinates.length - 1],\n      projection\n    );\n    const radius = Math.sqrt(squaredCoordinateDistance(center, end));\n    geometry = geometry || fromCircle(new Circle(center), sides);\n\n    let internalAngle = angle;\n    if (!angle && angle !== 0) {\n      const x = end[0] - center[0];\n      const y = end[1] - center[1];\n      internalAngle = Math.atan2(y, x);\n    }\n    makeRegular(\n      /** @type {Polygon} */ (geometry),\n      center,\n      radius,\n      internalAngle\n    );\n\n    const userProjection = getUserProjection();\n    if (userProjection) {\n      geometry.transform(projection, userProjection);\n    }\n    return geometry;\n  };\n}\n\n/**\n * Create a `geometryFunction` that will create a box-shaped polygon (aligned\n * with the coordinate system axes).  Use this with the draw interaction and\n * `type: 'Circle'` to return a box instead of a circle geometry.\n * @return {GeometryFunction} Function that draws a box-shaped polygon.\n * @api\n */\nexport function createBox() {\n  return function (coordinates, geometry, projection) {\n    const extent = boundingExtent(\n      /** @type {LineCoordType} */ ([\n        coordinates[0],\n        coordinates[coordinates.length - 1],\n      ]).map(function (coordinate) {\n        return fromUserCoordinate(coordinate, projection);\n      })\n    );\n    const boxCoordinates = [\n      [\n        getBottomLeft(extent),\n        getBottomRight(extent),\n        getTopRight(extent),\n        getTopLeft(extent),\n        getBottomLeft(extent),\n      ],\n    ];\n    if (geometry) {\n      geometry.setCoordinates(boxCoordinates);\n    } else {\n      geometry = new Polygon(boxCoordinates);\n    }\n    const userProjection = getUserProjection();\n    if (userProjection) {\n      geometry.transform(projection, userProjection);\n    }\n    return geometry;\n  };\n}\n\n/**\n * Get the drawing mode.  The mode for multi-part geometries is the same as for\n * their single-part cousins.\n * @param {import(\"../geom/Geometry.js\").Type} type Geometry type.\n * @return {Mode} Drawing mode.\n */\nfunction getMode(type) {\n  switch (type) {\n    case 'Point':\n    case 'MultiPoint':\n      return 'Point';\n    case 'LineString':\n    case 'MultiLineString':\n      return 'LineString';\n    case 'Polygon':\n    case 'MultiPolygon':\n      return 'Polygon';\n    case 'Circle':\n      return 'Circle';\n    default:\n      throw new Error('Invalid type: ' + type);\n  }\n}\n\nexport default Draw;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgKA,IAAM,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMX,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMT,WAAW;AACb;AAOO,IAAM,YAAN,cAAwB,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnC,YAAY,MAAM,SAAS;AACzB,UAAM,IAAI;AAOV,SAAK,UAAU;AAAA,EACjB;AACF;AAOA,SAAS,gBAAgB,YAAY,UAAU;AAI7C,QAAM,UAAU,CAAC;AAEjB,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACxC,UAAM,UAAU,SAAS,CAAC;AAC1B,UAAM,WAAW,QAAQ,YAAY;AACrC,+BAA2B,YAAY,UAAU,OAAO;AAAA,EAC1D;AAEA,SAAO;AACT;AAOA,SAAS,mBAAmB,GAAG,GAAG;AAChC,SAAO,gBAAgB,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC/C;AAOA,SAAS,cAAc,aAAa,OAAO;AACzC,QAAM,QAAQ,YAAY;AAC1B,MAAI,QAAQ,GAAG;AACb,WAAO,YAAY,QAAQ,KAAK;AAAA,EAClC;AACA,MAAI,SAAS,OAAO;AAClB,WAAO,YAAY,QAAQ,KAAK;AAAA,EAClC;AACA,SAAO,YAAY,KAAK;AAC1B;AAWA,SAAS,6BAA6B,aAAa,YAAY,UAAU;AACvE,MAAI,UAAU;AACd,MAAI,aAAa,UAAU;AACzB,eAAW;AACX,gBAAY;AAAA,EACd,OAAO;AACL,eAAW;AACX,gBAAY;AAAA,EACd;AACA,QAAM,gBAAgB,KAAK,KAAK,QAAQ;AACxC,QAAM,iBAAiB,KAAK,MAAM,SAAS;AAE3C,MAAI,gBAAgB,gBAAgB;AAElC,UAAM,QAAQ,sBAAsB,aAAa,QAAQ;AACzD,UAAM,MAAM,sBAAsB,aAAa,SAAS;AACxD,WAAO,mBAAmB,OAAO,GAAG;AAAA,EACtC;AAEA,MAAI,KAAK;AAET,MAAI,WAAW,eAAe;AAC5B,UAAM,QAAQ,sBAAsB,aAAa,QAAQ;AACzD,UAAM,MAAM,cAAc,aAAa,aAAa;AACpD,UAAM,mBAAmB,OAAO,GAAG;AAAA,EACrC;AAEA,MAAI,iBAAiB,WAAW;AAC9B,UAAM,QAAQ,cAAc,aAAa,cAAc;AACvD,UAAM,MAAM,sBAAsB,aAAa,SAAS;AACxD,UAAM,mBAAmB,OAAO,GAAG;AAAA,EACrC;AAEA,WAAS,IAAI,eAAe,IAAI,iBAAiB,GAAG,EAAE,GAAG;AACvD,UAAM,QAAQ,cAAc,aAAa,CAAC;AAC1C,UAAM,MAAM,cAAc,aAAa,IAAI,CAAC;AAC5C,UAAM,mBAAmB,OAAO,GAAG;AAAA,EACrC;AAEA,SAAO;AACT;AAOA,SAAS,2BAA2B,YAAY,UAAU,SAAS;AACjE,MAAI,oBAAoB,oBAAY;AAClC,sBAAkB,YAAY,SAAS,eAAe,GAAG,OAAO,OAAO;AACvE;AAAA,EACF;AACA,MAAI,oBAAoB,yBAAiB;AACvC,UAAM,cAAc,SAAS,eAAe;AAC5C,aAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,wBAAkB,YAAY,YAAY,CAAC,GAAG,OAAO,OAAO;AAAA,IAC9D;AACA;AAAA,EACF;AACA,MAAI,oBAAoB,iBAAS;AAC/B,UAAM,cAAc,SAAS,eAAe;AAC5C,aAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,wBAAkB,YAAY,YAAY,CAAC,GAAG,MAAM,OAAO;AAAA,IAC7D;AACA;AAAA,EACF;AACA,MAAI,oBAAoB,sBAAc;AACpC,UAAM,QAAQ,SAAS,eAAe;AACtC,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,YAAM,cAAc,MAAM,CAAC;AAC3B,eAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,0BAAkB,YAAY,YAAY,CAAC,GAAG,MAAM,OAAO;AAAA,MAC7D;AAAA,IACF;AACA;AAAA,EACF;AACA,MAAI,oBAAoB,4BAAoB;AAC1C,UAAM,aAAa,SAAS,cAAc;AAC1C,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AAC1C,iCAA2B,YAAY,WAAW,CAAC,GAAG,OAAO;AAAA,IAC/D;AACA;AAAA,EACF;AAEF;AAWA,IAAM,mBAAmB,EAAC,OAAO,IAAI,UAAU,IAAG;AAUlD,SAAS,qBAAqB,YAAY,YAAY,KAAK,eAAe;AACxE,QAAM,IAAI,WAAW,CAAC;AACtB,QAAM,IAAI,WAAW,CAAC;AAEtB,MAAI,wBAAwB;AAE5B,MAAI,iBAAiB;AACrB,MAAI,cAAc;AAElB,WACM,cAAc,GAClB,cAAc,WAAW,QAAQ,QACjC,EAAE,aACF;AACA,UAAM,SAAS,WAAW,QAAQ,WAAW;AAC7C,UAAM,cAAc,OAAO;AAE3B,QAAI,qBAAqB;AACzB,QAAI;AACJ,aACM,kBAAkB,GACtB,kBAAkB,YAAY,SAAS,GACvC,EAAE,iBACF;AACA,YAAM,QAAQ,YAAY,eAAe;AACzC,YAAM,MAAM,YAAY,kBAAkB,CAAC;AAC3C,YAAM,MAAM,4BAA4B,GAAG,GAAG,OAAO,GAAG;AACxD,UAAI,IAAI,kBAAkB,oBAAoB;AAC5C,6BAAqB,IAAI;AACzB,mBAAW,kBAAkB,IAAI;AAAA,MACnC;AAAA,IACF;AAEA,QAAI,qBAAqB,uBAAuB;AAC9C,8BAAwB;AACxB,UAAI,OAAO,QAAQ,WAAW,gBAAgB,aAAa;AAEzD,YAAI,OAAO,WAAW,OAAO,YAAY;AAEvC,cAAI,WAAW,OAAO,YAAY;AAChC,wBAAY,YAAY;AAAA,UAC1B;AAAA,QACF,WAAW,OAAO,WAAW,OAAO,YAAY;AAE9C,cAAI,WAAW,OAAO,YAAY;AAChC,wBAAY,YAAY;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AACA,oBAAc;AACd,uBAAiB;AAAA,IACnB;AAAA,EACF;AAEA,QAAM,YAAY,WAAW,QAAQ,cAAc;AACnD,MAAI,yBAAyB,UAAU;AACvC,MAAI,WAAW,gBAAgB,kBAAkB,wBAAwB;AAEvE,UAAM,gBAAgB;AAAA,MACpB,UAAU;AAAA,MACV;AAAA,IACF;AACA,UAAM,QAAQ,IAAI,uBAAuB,aAAa;AACtD,QAAI,SAAS,OAAO,WAAW,OAAO,IAAI,eAAe;AACvD,+BAAyB;AAAA,IAC3B;AAAA,EACF;AAEA,MAAI,wBAAwB;AAC1B,UAAM,cAAc,UAAU;AAC9B,UAAM,QAAQ,YAAY;AAC1B,UAAM,aAAa,UAAU;AAC7B,UAAM,WAAW;AACjB,QAAI,aAAa,UAAU;AACzB,YAAM,kBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,kBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACb;AACA,UAAI,kBAAkB,iBAAiB;AACrC,uBAAe;AAAA,MACjB;AAAA,IACF,OAAO;AACL,YAAM,kBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,kBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACb;AACA,UAAI,kBAAkB,iBAAiB;AACrC,uBAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAEA,mBAAiB,QAAQ;AACzB,mBAAiB,WAAW;AAC5B,SAAO;AACT;AAQA,SAAS,kBAAkB,YAAY,aAAa,MAAM,SAAS;AACjE,QAAM,IAAI,WAAW,CAAC;AACtB,QAAM,IAAI,WAAW,CAAC;AACtB,WAAS,IAAI,GAAG,KAAK,YAAY,SAAS,GAAG,IAAI,IAAI,EAAE,GAAG;AACxD,UAAM,QAAQ,YAAY,CAAC;AAC3B,UAAM,MAAM,YAAY,IAAI,CAAC;AAC7B,UAAM,MAAM,4BAA4B,GAAG,GAAG,OAAO,GAAG;AACxD,QAAI,IAAI,oBAAoB,GAAG;AAC7B,YAAM,QAAQ,IAAI,IAAI;AACtB,cAAQ,KAAK;AAAA,QACX;AAAA,QACA;AAAA,QACA,YAAY;AAAA,QACZ,UAAU;AAAA,MACZ,CAAC;AACD;AAAA,IACF;AAAA,EACF;AACF;AAWA,IAAM,YAAY,EAAC,OAAO,GAAG,iBAAiB,EAAC;AAU/C,SAAS,4BAA4B,GAAG,GAAG,OAAO,KAAK;AACrD,QAAM,KAAK,MAAM,CAAC;AAClB,QAAM,KAAK,MAAM,CAAC;AAClB,QAAM,KAAK,IAAI,CAAC;AAChB,QAAM,KAAK,IAAI,CAAC;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,MAAI,QAAQ;AACZ,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,OAAO,KAAK,OAAO,GAAG;AACxB,YAAQ,QAAQ,IAAI,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AACzE,UAAM,KAAK;AACX,UAAM,KAAK;AAAA,EACb;AAEA,YAAU,QAAQ;AAClB,YAAU,kBAAkB,QAAQ,gBAAgB,GAAG,GAAG,IAAI,EAAE,GAAG,EAAE;AACrE,SAAO;AACT;AAOA,SAAS,sBAAsB,aAAa,OAAO;AACjD,QAAM,QAAQ,YAAY;AAE1B,MAAI,aAAa,KAAK,MAAM,KAAK;AACjC,QAAM,QAAQ,QAAQ;AACtB,MAAI,cAAc,OAAO;AACvB,kBAAc;AAAA,EAChB,WAAW,aAAa,GAAG;AACzB,kBAAc;AAAA,EAChB;AAEA,MAAI,WAAW,aAAa;AAC5B,MAAI,YAAY,OAAO;AACrB,gBAAY;AAAA,EACd;AAEA,QAAM,QAAQ,YAAY,UAAU;AACpC,QAAM,KAAK,MAAM,CAAC;AAClB,QAAM,KAAK,MAAM,CAAC;AAClB,QAAM,MAAM,YAAY,QAAQ;AAChC,QAAM,KAAK,IAAI,CAAC,IAAI;AACpB,QAAM,KAAK,IAAI,CAAC,IAAI;AAEpB,SAAO,CAAC,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK;AAC1C;AAmBA,IAAM,OAAN,cAAmB,gBAAmB;AAAA;AAAA;AAAA;AAAA,EAIpC,YAAY,SAAS;AACnB,UAAM;AAAA;AAAA,MACJ;AAAA;AAEF,QAAI,CAAC,eAAe,UAAU;AAC5B,qBAAe,WAAW;AAAA,IAC5B;AAEA,UAAM,cAAc;AAKpB,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,SAAK,gBAAgB;AAMrB,SAAK,UAAU;AAMf,SAAK;AAML,SAAK;AAOL,SAAK;AAML,SAAK,YAAY;AAOjB,SAAK,UAAU,QAAQ,SAAS,QAAQ,SAAS;AAOjD,SAAK,YAAY,QAAQ,WAAW,QAAQ,WAAW;AAOvD,SAAK,iBAAiB,QAAQ,gBAAgB,QAAQ,gBAAgB;AAOtE,SAAK;AAAA,IACH,QAAQ;AAQV,SAAK,QAAQ,QAAQ,KAAK,KAAK;AAQ/B,SAAK,aAAa,CAAC,CAAC,QAAQ;AAS5B,SAAK,aAAa,QAAQ,YACtB,QAAQ,YACR,KAAK,UAAU,YACf,IACA;AAQJ,SAAK,aACH,KAAK,UAAU,WACX,IACA,QAAQ,YACR,QAAQ,YACR;AAON,SAAK,mBAAmB,QAAQ,kBAC5B,QAAQ,kBACR;AAMJ,SAAK,kBAAkB,QAAQ,iBAC3B,QAAQ,iBACR;AAEJ,QAAI,mBAAmB,QAAQ;AAC/B,QAAI,CAAC,kBAAkB;AACrB,YAAM,OAAO,KAAK;AAClB,UAAI,SAAS,UAAU;AAOrB,2BAAmB,SAAU,aAAa,UAAU,YAAY;AAC9D,gBAAM,SAAS;AAAA;AAAA,YACY;AAAA,cACvB,IAAI,eAAO,CAAC,KAAK,GAAG,CAAC;AACzB,gBAAM,SAAS,mBAAmB,YAAY,CAAC,GAAG,UAAU;AAC5D,gBAAM,gBAAgBA;AAAA,YACpB;AAAA,YACA,mBAAmB,YAAY,YAAY,SAAS,CAAC,GAAG,UAAU;AAAA,UACpE;AACA,iBAAO;AAAA,YACL;AAAA,YACA,KAAK,KAAK,aAAa;AAAA,YACvB,KAAK;AAAA,UACP;AACA,gBAAM,iBAAiB,kBAAkB;AACzC,cAAI,gBAAgB;AAClB,mBAAO,UAAU,YAAY,cAAc;AAAA,UAC7C;AACA,iBAAO;AAAA,QACT;AAAA,MACF,OAAO;AACL,YAAI;AACJ,YAAI,SAAS,SAAS;AACpB,wBAAc;AAAA,QAChB,WAAW,SAAS,cAAc;AAChC,wBAAc;AAAA,QAChB,WAAW,SAAS,WAAW;AAC7B,wBAAc;AAAA,QAChB;AAOA,2BAAmB,SAAU,aAAa,UAAU,YAAY;AAC9D,cAAI,UAAU;AACZ,gBAAI,SAAS,WAAW;AACtB,kBAAI,YAAY,CAAC,EAAE,QAAQ;AAEzB,yBAAS;AAAA,kBACP,CAAC,YAAY,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAAA,kBAC3C,KAAK;AAAA,gBACP;AAAA,cACF,OAAO;AACL,yBAAS,eAAe,CAAC,GAAG,KAAK,eAAe;AAAA,cAClD;AAAA,YACF,OAAO;AACL,uBAAS,eAAe,aAAa,KAAK,eAAe;AAAA,YAC3D;AAAA,UACF,OAAO;AACL,uBAAW,IAAI,YAAY,aAAa,KAAK,eAAe;AAAA,UAC9D;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAMA,SAAK,oBAAoB;AAMzB,SAAK,mBACH,QAAQ,oBAAoB,SAAY,QAAQ,kBAAkB;AAQpE,SAAK,oBAAoB;AAOzB,SAAK,iBAAiB;AAOtB,SAAK,eAAe;AAOpB,SAAK,gBAAgB;AAOrB,SAAK,cAAc;AAOnB,SAAK,oBAAoB;AASzB,SAAK,yBAAyB,QAAQ,iBAClC,QAAQ,iBAAiB,QAAQ,iBACjC;AAOJ,SAAK,WAAW,IAAIC,gBAAY;AAAA,MAC9B,QAAQ,IAAI,eAAa;AAAA,QACvB,iBAAiB;AAAA,QACjB,OAAO,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,MACzC,CAAC;AAAA,MACD,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,wBAAwB;AAAA,MAC/D,wBAAwB;AAAA,IAC1B,CAAC;AAOD,SAAK,gBAAgB,QAAQ;AAM7B,SAAK,aAAa,QAAQ,YAAY,QAAQ,YAAY;AAM1D,SAAK;AACL,QAAI,QAAQ,UAAU;AACpB,WAAK,qBAAqB;AAAA,IAC5B,OAAO;AACL,WAAK,qBAAqB,QAAQ,oBAC9B,QAAQ,oBACR;AAAA,IACN;AAMA,SAAK;AACL,SAAK,SAAS,QAAQ,SAAS,KAAK;AAMpC,SAAK,cAAc,EAAC,QAAQ,MAAK;AAMjC,SAAK,eAAe,QAAQ,eAAe,QAAQ,UAAU;AAE7D,SAAK,kBAAkB,iBAAoB,QAAQ,KAAK,YAAY;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO;AACd,QAAI;AACJ,QAAI,CAAC,OAAO;AACV,kBAAY;AAAA,IACd,WAAW,UAAU,MAAM;AACzB,kBAAY;AAAA,IACd,OAAO;AACL,kBAAY;AAAA,IACd;AACA,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KAAK;AACV,UAAM,OAAO,GAAG;AAChB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,OAAO;AACjB,QAAI,MAAM,cAAc,SAAS,kBAAU,aAAa;AAEtD,YAAM,cAAc,eAAe;AAAA,IACrC;AACA,SAAK,YAAY,KAAK,UAAU,WAAW,KAAK,mBAAmB,KAAK;AACxE,QAAI,OAAO,MAAM,SAAS,4BAAoB;AAC9C,QAAI,OAAO;AACX,QACE,CAAC,KAAK,aACN,KAAK,iBACL,MAAM,SAAS,4BAAoB,aACnC;AACA,YAAM,MAAM,KAAK,IAAI;AACrB,UAAI,MAAM,KAAK,iBAAiB,KAAK,kBAAkB;AACrD,aAAK,UAAU,MAAM;AACrB,aAAK,gBAAgB,CAAC,KAAK;AAC3B,eAAO;AAAA,MACT,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AACA,UAAI,KAAK,iBAAiB,KAAK,iBAAiB,QAAW;AACzD,qBAAa,KAAK,YAAY;AAC9B,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AACA,QACE,KAAK,aACL,MAAM,SAAS,4BAAoB,eACnC,KAAK,mBAAmB,MACxB;AACA,WAAK,cAAc,MAAM,UAAU;AACnC,aAAO;AAAA,IACT,WACE,KAAK,aACL,MAAM,SAAS,4BAAoB,aACnC;AACA,aAAO;AAAA,IACT,WAAW,QAAQ,KAAK,gBAAgB,IAAI,GAAG;AAC7C,aAAO,MAAM,SAAS,4BAAoB;AAC1C,UAAI,QAAQ,KAAK,WAAW;AAC1B,aAAK,mBAAmB,KAAK;AAC7B,YAAI,KAAK,eAAe;AAEtB,gBAAM,cAAc,eAAe;AAAA,QACrC;AAAA,MACF,WACE,MAAM,cAAc,gBAAgB,WACnC,MAAM,SAAS,4BAAoB,eAClC,KAAK,iBAAiB,QACxB;AACA,aAAK,mBAAmB,KAAK;AAAA,MAC/B;AAAA,IACF,WAAW,MAAM,SAAS,4BAAoB,UAAU;AACtD,aAAO;AAAA,IACT;AAEA,WAAO,MAAM,YAAY,KAAK,KAAK;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,OAAO;AACrB,SAAK,gBAAgB,CAAC,KAAK;AAE3B,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,MAAM;AACrB,UAAI,CAAC,KAAK,mBAAmB;AAC3B,aAAK,cAAc,MAAM,UAAU;AAAA,MACrC;AACA,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,KAAK,WAAW,KAAK,GAAG;AAC3B,WAAK,gBAAgB;AACrB,aAAO;AAAA,IACT;AAEA,SAAK,gBAAgB,KAAK,IAAI;AAC9B,SAAK,eAAe,WAAW,MAAM;AACnC,WAAK;AAAA,QACH,IAAI;AAAA,UACF,4BAAoB;AAAA,UACpB,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF,GAAG,KAAK,gBAAgB;AACxB,SAAK,UAAU,MAAM;AACrB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,SAAK,cAAc,EAAC,QAAQ,MAAK;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,OAAO;AACvB,QAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,gBAAgB,KAAK,GAAG;AACtD;AAAA,IACF;AAEA,QAAI,KAAK,YAAY,QAAQ;AAC3B,WAAK,iBAAiB;AACtB;AAAA,IACF;AAEA,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,YAAY,IAAI,uBAAuB;AAAA,MAC3C,MAAM,MAAM,CAAC,IAAI,KAAK;AAAA,MACtB,MAAM,MAAM,CAAC,IAAI,KAAK;AAAA,IACxB,CAAC;AACD,UAAM,aAAa,IAAI,uBAAuB;AAAA,MAC5C,MAAM,MAAM,CAAC,IAAI,KAAK;AAAA,MACtB,MAAM,MAAM,CAAC,IAAI,KAAK;AAAA,IACxB,CAAC;AACD,UAAM,SAAS,eAAe,CAAC,WAAW,UAAU,CAAC;AACrD,UAAM,WAAW,KAAK,aAAa,oBAAoB,MAAM;AAC7D,QAAI,SAAS,WAAW,GAAG;AACzB;AAAA,IACF;AAEA,UAAM,UAAU,gBAAgB,MAAM,YAAY,QAAQ;AAC1D,QAAI,QAAQ,QAAQ;AAClB,WAAK,cAAc;AAAA,QACjB,QAAQ;AAAA,QACR,SAAS,MAAM,MAAM,MAAM;AAAA,QAC3B;AAAA,QACA,aAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,8BAA8B,QAAQ,UAAU;AAK9C,UAAM,oBAAoB,OAAO,cAAc,OAAO;AACtD,UAAM,mBAAmB,OAAO,cAAc;AAC9C,QAAI,sBAAsB,kBAAkB;AAE1C,UACG,qBAAqB,WAAW,OAAO,YACvC,CAAC,qBAAqB,WAAW,OAAO,UACzC;AAEA,aAAK,sBAAsB,QAAQ,OAAO,UAAU,QAAQ;AAAA,MAC9D,WACG,qBAAqB,WAAW,OAAO,YACvC,CAAC,qBAAqB,WAAW,OAAO,UACzC;AAEA,aAAK,yBAAyB,UAAU,OAAO,QAAQ;AAAA,MACzD;AAAA,IACF,OAAO;AAEL,WAAK,yBAAyB,OAAO,YAAY,OAAO,QAAQ;AAChE,WAAK,sBAAsB,QAAQ,OAAO,YAAY,QAAQ;AAAA,IAChE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,yBAAyB,WAAW,SAAS;AAC3C,QAAI,cAAc,SAAS;AACzB;AAAA,IACF;AAEA,QAAI,SAAS;AACb,QAAI,YAAY,SAAS;AACvB,YAAM,QAAQ,KAAK,KAAK,SAAS;AACjC,UAAI,MAAM,KAAK,MAAM,OAAO;AAC5B,UAAI,QAAQ,SAAS;AACnB,eAAO;AAAA,MACT;AACA,eAAS,MAAM,QAAQ;AAAA,IACzB,OAAO;AACL,YAAM,QAAQ,KAAK,MAAM,SAAS;AAClC,UAAI,MAAM,KAAK,KAAK,OAAO;AAC3B,UAAI,QAAQ,SAAS;AACnB,eAAO;AAAA,MACT;AACA,eAAS,QAAQ,MAAM;AAAA,IACzB;AAEA,QAAI,SAAS,GAAG;AACd,WAAK,kBAAkB,MAAM;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,QAAQ,WAAW,SAAS;AAChD,QAAI,cAAc,SAAS;AACzB;AAAA,IACF;AAEA,UAAM,cAAc,CAAC;AACrB,QAAI,YAAY,SAAS;AAEvB,YAAM,QAAQ,KAAK,KAAK,SAAS;AACjC,UAAI,MAAM,KAAK,MAAM,OAAO;AAC5B,UAAI,QAAQ,SAAS;AAEnB,eAAO;AAAA,MACT;AACA,eAAS,IAAI,OAAO,KAAK,KAAK,EAAE,GAAG;AACjC,oBAAY,KAAK,cAAc,OAAO,aAAa,CAAC,CAAC;AAAA,MACvD;AAAA,IACF,OAAO;AAEL,YAAM,QAAQ,KAAK,MAAM,SAAS;AAClC,UAAI,MAAM,KAAK,KAAK,OAAO;AAC3B,UAAI,QAAQ,SAAS;AACnB,eAAO;AAAA,MACT;AACA,eAAS,IAAI,OAAO,KAAK,KAAK,EAAE,GAAG;AACjC,oBAAY,KAAK,cAAc,OAAO,aAAa,CAAC,CAAC;AAAA,MACvD;AAAA,IACF;AACA,QAAI,YAAY,QAAQ;AACtB,WAAK,kBAAkB,WAAW;AAAA,IACpC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,OAAO;AAClB,UAAM,aAAa,KAAK;AACxB,QAAI,CAAC,WAAW,QAAQ;AACtB;AAAA,IACF;AAEA,QAAI,WAAW,gBAAgB,IAAI;AAEjC,UAAI,SAAS,WAAW,SAAS,MAAM,KAAK,IAAI,KAAK,gBAAgB;AACnE;AAAA,MACF;AAAA,IACF;AAEA,UAAM,qBAAqB;AAAA,MACzB,MAAM;AAAA,MACN;AAAA,MACA,KAAK,OAAO;AAAA,MACZ,KAAK;AAAA,IACP;AAEA,QAAI,WAAW,gBAAgB,mBAAmB,OAAO;AAEvD,UAAI,WAAW,gBAAgB,IAAI;AAEjC,cAAM,YAAY,WAAW,QAAQ,WAAW,WAAW;AAC3D,aAAK,yBAAyB,UAAU,YAAY,UAAU,QAAQ;AAAA,MACxE;AAEA,YAAM,YAAY,WAAW,QAAQ,mBAAmB,KAAK;AAC7D,WAAK;AAAA,QACH;AAAA,QACA,UAAU;AAAA,QACV,mBAAmB;AAAA,MACrB;AAAA,IACF,OAAO;AAEL,YAAMC,UAAS,WAAW,QAAQ,WAAW,WAAW;AACxD,WAAK,8BAA8BA,SAAQ,mBAAmB,QAAQ;AAAA,IACxE;AAGA,eAAW,cAAc,mBAAmB;AAC5C,UAAM,SAAS,WAAW,QAAQ,WAAW,WAAW;AACxD,WAAO,WAAW,mBAAmB;AAGrC,UAAM,aAAa;AAAA,MACjB,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AACA,UAAM,QAAQ,KAAK,OAAO,EAAE,uBAAuB,UAAU;AAC7D,UAAM,aAAa;AACnB,UAAM,QAAQ,CAAC,KAAK,MAAM,MAAM,CAAC,CAAC,GAAG,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,OAAO;AACnB,QAAI,OAAO;AAEX,QAAI,KAAK,gBAAgB,MAAM,GAAG;AAChC,UAAI,KAAK,cAAc;AACrB,qBAAa,KAAK,YAAY;AAC9B,aAAK,eAAe;AAAA,MACtB;AAEA,WAAK,mBAAmB,KAAK;AAC7B,YAAM,UAAU,KAAK,YAAY;AACjC,WAAK,kBAAkB,KAAK;AAE5B,UAAI,KAAK,eAAe;AACtB,cAAM,iBAAiB,CAAC,KAAK;AAC7B,YAAI,gBAAgB;AAClB,eAAK,cAAc,MAAM,UAAU;AAAA,QACrC;AACA,YAAI,CAAC,kBAAkB,KAAK,WAAW;AACrC,eAAK,cAAc;AAAA,QACrB,WACE,CAAC,KAAK,cACL,CAAC,kBAAkB,KAAK,UAAU,UACnC;AACA,cAAI,KAAK,UAAU,MAAM,OAAO,OAAO,GAAG;AACxC,gBAAI,KAAK,iBAAiB,KAAK,GAAG;AAChC,mBAAK,cAAc;AAAA,YACrB;AAAA,UACF,OAAO;AACL,iBAAK,cAAc,MAAM,UAAU;AAAA,UACrC;AAAA,QACF;AACA,eAAO;AAAA,MACT,WAAW,KAAK,WAAW;AACzB,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AAEA,QAAI,CAAC,QAAQ,KAAK,YAAY;AAC5B,YAAM,eAAe;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,OAAO;AACxB,SAAK,eAAe,MAAM,cAAc;AACxC,QACE,KAAK,YACH,CAAC,KAAK,aAAa,KAAK,iBACvB,KAAK,aAAa,CAAC,KAAK,gBAC3B;AACA,YAAM,SAAS,KAAK;AACpB,YAAM,UAAU,MAAM;AACtB,YAAM,KAAK,OAAO,CAAC,IAAI,QAAQ,CAAC;AAChC,YAAM,KAAK,OAAO,CAAC,IAAI,QAAQ,CAAC;AAChC,YAAMF,mBAAkB,KAAK,KAAK,KAAK;AACvC,WAAK,gBAAgB,KAAK,YACtBA,mBAAkB,KAAK,yBACvBA,oBAAmB,KAAK;AAC5B,UAAI,CAAC,KAAK,eAAe;AACvB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,CAAC,KAAK,mBAAmB;AAC3B,WAAK,2BAA2B,MAAM,WAAW,MAAM,CAAC;AACxD;AAAA,IACF;AAEA,SAAK,aAAa,KAAK;AACvB,SAAK,eAAe,MAAM,UAAU;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,OAAO,SAAS;AACxB,QAAI,KAAK;AACT,QAAI,KAAK,gBAAgB;AACvB,UAAI,kBAAkB;AACtB,UAAI,+BAA+B,CAAC,KAAK,iBAAiB;AAC1D,YAAM,OAAO,KAAK;AAClB,UAAI,SAAS,SAAS;AACpB,aAAK;AAAA,MACP,WAAW,SAAS,UAAU;AAC5B,aAAK,KAAK,cAAc,WAAW;AAAA,MACrC,WAAW,SAAS,cAAc;AAChC,0BACE,CAAC,WAAW,KAAK,cAAc,SAAS,KAAK;AAAA,MACjD,WAAW,SAAS,WAAW;AAC7B,cAAM;AAAA;AAAA,UAA6C,KAAK;AAAA;AACxD,0BAAkB,aAAa,CAAC,EAAE,SAAS,KAAK;AAChD,uCAA+B;AAAA,UAC7B,aAAa,CAAC,EAAE,CAAC;AAAA,UACjB,aAAa,CAAC,EAAE,aAAa,CAAC,EAAE,SAAS,CAAC;AAAA,QAC5C;AACA,YAAI,SAAS;AACX,yCAA+B,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;AAAA,QACpD,OAAO;AACL,yCAA+B;AAAA,YAC7B,aAAa,CAAC,EAAE,CAAC;AAAA,YACjB,aAAa,CAAC,EAAE,aAAa,CAAC,EAAE,SAAS,CAAC;AAAA,UAC5C;AAAA,QACF;AAAA,MACF;AACA,UAAI,iBAAiB;AACnB,cAAM,MAAM,KAAK,OAAO;AACxB,iBAAS,IAAI,GAAG,KAAK,6BAA6B,QAAQ,IAAI,IAAI,KAAK;AACrE,gBAAM,mBAAmB,6BAA6B,CAAC;AACvD,gBAAM,cAAc,IAAI,uBAAuB,gBAAgB;AAC/D,gBAAM,KAAK,MAAM,CAAC,IAAI,YAAY,CAAC;AACnC,gBAAM,KAAK,MAAM,CAAC,IAAI,YAAY,CAAC;AACnC,gBAAM,gBAAgB,KAAK,YAAY,IAAI,KAAK;AAChD,eAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,KAAK;AACrC,cAAI,IAAI;AACN,iBAAK,oBAAoB;AACzB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,2BAA2B,aAAa;AACtC,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,eAAe,IAAI,gBAAQ,IAAI,cAAM,WAAW,CAAC;AACtD,WAAK,sBAAsB;AAAA,IAC7B,OAAO;AACL,YAAM,kBAAkB,KAAK,aAAa,YAAY;AACtD,sBAAgB,eAAe,WAAW;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gCAAgC,UAAU;AACxC,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc,IAAI,gBAAQ;AAAA,IACjC;AACA,UAAM,OAAO,SAAS,cAAc,CAAC;AACrC,QAAI,iBAAiB,KAAK,YAAY,YAAY;AAClD,QAAI,CAAC,gBAAgB;AACnB,uBAAiB,IAAI;AAAA,QACnB,KAAK,mBAAmB;AAAA,QACxB,KAAK,UAAU;AAAA,MACjB;AACA,WAAK,YAAY,YAAY,cAAc;AAAA,IAC7C,OAAO;AACL,qBAAe;AAAA,QACb,KAAK,UAAU;AAAA,QACf,KAAK,mBAAmB;AAAA,MAC1B;AACA,qBAAe,QAAQ;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,OAAO;AACnB,UAAM,aAAa,KAAK,OAAO,EAAE,QAAQ,EAAE,cAAc;AACzD,UAAM,SAAS,mBAAmB,KAAK,eAAe;AACtD,WAAO,MAAM,SAAS,QAAQ;AAC5B,YAAM,KAAK,CAAC;AAAA,IACd;AACA,SAAK,oBAAoB;AACzB,QAAI,KAAK,UAAU,SAAS;AAC1B,WAAK,gBAAgB,MAAM,MAAM;AAAA,IACnC,WAAW,KAAK,UAAU,WAAW;AACnC,WAAK,gBAAgB,CAAC,CAAC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,CAAC;AACpD,WAAK,oBAAoB,KAAK,cAAc,CAAC;AAAA,IAC/C,OAAO;AACL,WAAK,gBAAgB,CAAC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC;AAAA,IACpD;AACA,QAAI,KAAK,mBAAmB;AAC1B,WAAK,cAAc,IAAI,gBAAQ,IAAI,mBAAW,KAAK,iBAAiB,CAAC;AAAA,IACvE;AACA,UAAM,WAAW,KAAK;AAAA,MACpB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AACA,SAAK,iBAAiB,IAAI,gBAAQ;AAClC,QAAI,KAAK,eAAe;AACtB,WAAK,eAAe,gBAAgB,KAAK,aAAa;AAAA,IACxD;AACA,SAAK,eAAe,YAAY,QAAQ;AACxC,SAAK,sBAAsB;AAC3B,SAAK;AAAA,MACH,IAAI,UAAU,cAAc,WAAW,KAAK,cAAc;AAAA,IAC5D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,YAAY;AACzB,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,WAAW,KAAK,eAAe,YAAY;AACjD,UAAM,aAAa,IAAI,QAAQ,EAAE,cAAc;AAC/C,UAAM,SAAS,mBAAmB,KAAK,eAAe;AACtD,QAAI,aAAa;AACjB,WAAO,WAAW,SAAS,QAAQ;AACjC,iBAAW,KAAK,CAAC;AAAA,IACnB;AACA,QAAI,KAAK,UAAU,SAAS;AAC1B,aAAO,KAAK;AAAA,IACd,WAAW,KAAK,UAAU,WAAW;AACnC;AAAA,MAA4C,KAAK,cAAe,CAAC;AACjE,aAAO,YAAY,YAAY,SAAS,CAAC;AACzC,UAAI,KAAK,UAAU,IAAI,uBAAuB,UAAU,CAAC,GAAG;AAE1D,qBAAa,KAAK,kBAAkB,MAAM;AAAA,MAC5C;AAAA,IACF,OAAO;AACL,oBAAc,KAAK;AACnB,aAAO,YAAY,YAAY,SAAS,CAAC;AAAA,IAC3C;AACA,SAAK,CAAC,IAAI,WAAW,CAAC;AACtB,SAAK,CAAC,IAAI,WAAW,CAAC;AACtB,SAAK;AAAA;AAAA,MAC4B,KAAK;AAAA,MACpC;AAAA,MACA;AAAA,IACF;AACA,QAAI,KAAK,cAAc;AACrB,YAAM,kBAAkB,KAAK,aAAa,YAAY;AACtD,sBAAgB,eAAe,UAAU;AAAA,IAC3C;AACA,QAAI,SAAS,QAAQ,MAAM,aAAa,KAAK,UAAU,WAAW;AAChE,WAAK;AAAA;AAAA,QAAwD;AAAA,MAAS;AAAA,IACxE,WAAW,KAAK,mBAAmB;AACjC,YAAM,iBAAiB,KAAK,YAAY,YAAY;AACpD,qBAAe,eAAe,KAAK,iBAAiB;AAAA,IACtD;AACA,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,YAAY;AACxB,UAAM,WAAW,KAAK,eAAe,YAAY;AACjD,UAAM,aAAa,KAAK,OAAO,EAAE,QAAQ,EAAE,cAAc;AACzD,QAAI;AACJ,QAAI;AACJ,UAAM,OAAO,KAAK;AAClB,QAAI,SAAS,gBAAgB,SAAS,UAAU;AAC9C,WAAK,oBAAoB,WAAW,MAAM;AAC1C;AAAA,MAA4C,KAAK;AACjD,UAAI,YAAY,UAAU,KAAK,YAAY;AACzC,YAAI,KAAK,WAAW;AAClB,sBAAY,IAAI;AAAA,QAClB,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,kBAAY,KAAK,WAAW,MAAM,CAAC;AACnC,WAAK,kBAAkB,aAAa,UAAU,UAAU;AAAA,IAC1D,WAAW,SAAS,WAAW;AAC7B;AAAA,MAA4C,KAAK,cAAe,CAAC;AACjE,UAAI,YAAY,UAAU,KAAK,YAAY;AACzC,YAAI,KAAK,WAAW;AAClB,sBAAY,IAAI;AAAA,QAClB,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AACA,kBAAY,KAAK,WAAW,MAAM,CAAC;AACnC,UAAI,MAAM;AACR,aAAK,oBAAoB,YAAY,CAAC;AAAA,MACxC;AACA,WAAK,kBAAkB,KAAK,eAAe,UAAU,UAAU;AAAA,IACjE;AACA,SAAK,2BAA2B,WAAW,MAAM,CAAC;AAClD,SAAK,sBAAsB;AAC3B,QAAI,MAAM;AACR,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,GAAG;AACnB,QAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,IACF;AACA,UAAM,WAAW,KAAK,eAAe,YAAY;AACjD,UAAM,aAAa,KAAK,OAAO,EAAE,QAAQ,EAAE,cAAc;AACzD,UAAM,OAAO,KAAK;AAClB,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAI;AACJ,UAAI,SAAS,gBAAgB,SAAS,UAAU;AAC9C;AAAA,QAA4C,KAAK;AACjD,oBAAY,OAAO,IAAI,CAAC;AACxB,YAAI,YAAY,UAAU,GAAG;AAC3B,eAAK,oBAAoB,YAAY,YAAY,SAAS,CAAC,EAAE,MAAM;AACnE,gBAAM,mBAAmB,KAAK,kBAAkB,MAAM;AACtD,sBAAY,YAAY,SAAS,CAAC,IAAI;AACtC,eAAK,2BAA2B,gBAAgB;AAAA,QAClD;AACA,aAAK,kBAAkB,aAAa,UAAU,UAAU;AACxD,YAAI,SAAS,QAAQ,MAAM,aAAa,KAAK,aAAa;AACxD,eAAK;AAAA;AAAA,YACqB;AAAA,UAC1B;AAAA,QACF;AAAA,MACF,WAAW,SAAS,WAAW;AAC7B;AAAA,QAA4C,KAAK,cAAe,CAAC;AACjE,oBAAY,OAAO,IAAI,CAAC;AACxB,cAAM,iBAAiB,KAAK,YAAY,YAAY;AACpD,YAAI,YAAY,UAAU,GAAG;AAC3B,gBAAM,mBAAmB,YAAY,YAAY,SAAS,CAAC,EAAE,MAAM;AACnE,sBAAY,YAAY,SAAS,CAAC,IAAI;AACtC,eAAK,2BAA2B,gBAAgB;AAAA,QAClD;AACA,uBAAe,eAAe,WAAW;AACzC,aAAK,kBAAkB,KAAK,eAAe,UAAU,UAAU;AAAA,MACjE;AAEA,UAAI,YAAY,WAAW,GAAG;AAC5B,aAAK,aAAa;AAClB;AAAA,MACF;AAAA,IACF;AAEA,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB;AAChB,SAAK,kBAAkB,CAAC;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB;AACd,UAAM,gBAAgB,KAAK,cAAc;AACzC,QAAI,CAAC,eAAe;AAClB;AAAA,IACF;AACA,QAAI,cAAc,KAAK;AACvB,UAAM,WAAW,cAAc,YAAY;AAC3C,UAAM,aAAa,KAAK,OAAO,EAAE,QAAQ,EAAE,cAAc;AACzD,QAAI,KAAK,UAAU,cAAc;AAE/B,kBAAY,IAAI;AAChB,WAAK,kBAAkB,aAAa,UAAU,UAAU;AAAA,IAC1D,WAAW,KAAK,UAAU,WAAW;AAEN,MAAC,YAAa,CAAC,EAAE,IAAI;AAClD,WAAK,kBAAkB,aAAa,UAAU,UAAU;AACxD,oBAAc,SAAS,eAAe;AAAA,IACxC;AAGA,QAAI,KAAK,UAAU,cAAc;AAC/B,oBAAc;AAAA,QACZ,IAAI,mBAAW;AAAA;AAAA,UAAgC;AAAA,QAAY,CAAC;AAAA,MAC9D;AAAA,IACF,WAAW,KAAK,UAAU,mBAAmB;AAC3C,oBAAc;AAAA,QACZ,IAAI,wBAAgB;AAAA;AAAA,UAA+B;AAAA,QAAY,CAAC;AAAA,MAClE;AAAA,IACF,WAAW,KAAK,UAAU,gBAAgB;AACxC,oBAAc;AAAA,QACZ,IAAI,qBAAa;AAAA;AAAA,UAA+B;AAAA,QAAY,CAAC;AAAA,MAC/D;AAAA,IACF;AAGA,SAAK,cAAc,IAAI,UAAU,cAAc,SAAS,aAAa,CAAC;AAGtE,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,KAAK,aAAa;AAAA,IACnC;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,WAAW,aAAa;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACd,SAAK,oBAAoB;AACzB,UAAM,gBAAgB,KAAK;AAC3B,SAAK,iBAAiB;AACtB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,SAAS,UAAU,EAAE,MAAM,IAAI;AACpC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AACb,UAAM,gBAAgB,KAAK,cAAc;AACzC,QAAI,eAAe;AACjB,WAAK,cAAc,IAAI,UAAU,cAAc,WAAW,aAAa,CAAC;AAAA,IAC1E;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,kBAAkB,aAAa;AAC7B,UAAM,OAAO,KAAK;AAClB,UAAM,aAAa,CAAC,KAAK;AACzB,QAAI,YAAY;AACd,WAAK,cAAc,YAAY,CAAC,CAAC;AAAA,IACnC;AAEA,QAAI;AACJ,QAAI,SAAS,gBAAgB,SAAS,UAAU;AAC9C;AAAA,MAA6C,KAAK;AAAA,IACpD,WAAW,SAAS,WAAW;AAC7B,qBACE,KAAK,iBAAiB,KAAK,cAAc;AAAA;AAAA,QACP,KAAK,cAAe,CAAC;AAAA,UACnD,CAAC;AAAA,IACT,OAAO;AACL;AAAA,IACF;AAEA,QAAI,YAAY;AACd,mBAAa,MAAM;AAAA,IACrB;AAGA,iBAAa,IAAI;AAGjB,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,WAAK,cAAc,YAAY,CAAC,CAAC;AAAA,IACnC;AAEA,UAAM,SAAS,YAAY,YAAY,SAAS,CAAC;AAEjD,SAAK,cAAc,MAAM;AACzB,SAAK,eAAe,MAAM;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,OAAO,SAAS;AACd,UAAM,WAAW,QAAQ,YAAY;AACrC,UAAM,aAAa;AACnB,SAAK,iBAAiB;AACtB,SAAK,gBAAgB,WAAW,eAAe;AAC/C,UAAM,OAAO,KAAK,cAAc,KAAK,cAAc,SAAS,CAAC;AAC7D,SAAK,oBAAoB,KAAK,MAAM;AACpC,SAAK,cAAc,KAAK,KAAK,MAAM,CAAC;AACpC,SAAK,eAAe,IAAI,gBAAQ,IAAI,cAAM,IAAI,CAAC;AAC/C,SAAK,sBAAsB;AAC3B,SAAK;AAAA,MACH,IAAI,UAAU,cAAc,WAAW,KAAK,cAAc;AAAA,IAC5D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB;AACtB,UAAM,iBAAiB,CAAC;AACxB,QAAI,KAAK,gBAAgB;AACvB,qBAAe,KAAK,KAAK,cAAc;AAAA,IACzC;AACA,QAAI,KAAK,aAAa;AACpB,qBAAe,KAAK,KAAK,WAAW;AAAA,IACtC;AACA,QAAI,KAAK,cAAc;AACrB,qBAAe,KAAK,KAAK,YAAY;AAAA,IACvC;AACA,UAAM,gBAAgB,KAAK,SAAS,UAAU;AAC9C,kBAAc,MAAM,IAAI;AACxB,kBAAc,YAAY,cAAc;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,OAAO,CAAC,QAAQ;AACnB,WAAK,aAAa;AAAA,IACpB;AACA,SAAK,SAAS,OAAO,SAAS,MAAM,IAAI;AAAA,EAC1C;AACF;AAKA,SAAS,0BAA0B;AACjC,QAAM,SAAS,mBAAmB;AAClC,SAAO,SAAU,SAAS,YAAY;AACpC,WAAO,OAAO,QAAQ,YAAY,EAAE,QAAQ,CAAC;AAAA,EAC/C;AACF;AAeO,SAAS,qBAAqB,OAAO,OAAO;AACjD,SAAO,SAAU,aAAa,UAAU,YAAY;AAClD,UAAM,SAAS;AAAA;AAAA,MACiB,YAAa,CAAC;AAAA,MAC5C;AAAA,IACF;AACA,UAAM,MAAM;AAAA;AAAA,MACoB,YAAa,YAAY,SAAS,CAAC;AAAA,MACjE;AAAA,IACF;AACA,UAAM,SAAS,KAAK,KAAKA,iBAA0B,QAAQ,GAAG,CAAC;AAC/D,eAAW,YAAY,WAAW,IAAI,eAAO,MAAM,GAAG,KAAK;AAE3D,QAAI,gBAAgB;AACpB,QAAI,CAAC,SAAS,UAAU,GAAG;AACzB,YAAM,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC;AAC3B,YAAM,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC;AAC3B,sBAAgB,KAAK,MAAM,GAAG,CAAC;AAAA,IACjC;AACA;AAAA;AAAA,MAC0B;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,UAAM,iBAAiB,kBAAkB;AACzC,QAAI,gBAAgB;AAClB,eAAS,UAAU,YAAY,cAAc;AAAA,IAC/C;AACA,WAAO;AAAA,EACT;AACF;AASO,SAAS,YAAY;AAC1B,SAAO,SAAU,aAAa,UAAU,YAAY;AAClD,UAAM,SAAS;AAAA;AAAA,MACiB;AAAA,QAC5B,YAAY,CAAC;AAAA,QACb,YAAY,YAAY,SAAS,CAAC;AAAA,MACpC,EAAG,IAAI,SAAU,YAAY;AAC3B,eAAO,mBAAmB,YAAY,UAAU;AAAA,MAClD,CAAC;AAAA,IACH;AACA,UAAM,iBAAiB;AAAA,MACrB;AAAA,QACE,cAAc,MAAM;AAAA,QACpB,eAAe,MAAM;AAAA,QACrB,YAAY,MAAM;AAAA,QAClB,WAAW,MAAM;AAAA,QACjB,cAAc,MAAM;AAAA,MACtB;AAAA,IACF;AACA,QAAI,UAAU;AACZ,eAAS,eAAe,cAAc;AAAA,IACxC,OAAO;AACL,iBAAW,IAAI,gBAAQ,cAAc;AAAA,IACvC;AACA,UAAM,iBAAiB,kBAAkB;AACzC,QAAI,gBAAgB;AAClB,eAAS,UAAU,YAAY,cAAc;AAAA,IAC/C;AACA,WAAO;AAAA,EACT;AACF;AAQA,SAAS,QAAQ,MAAM;AACrB,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,YAAM,IAAI,MAAM,mBAAmB,IAAI;AAAA,EAC3C;AACF;AAEA,IAAO,eAAQ;", "names": ["squaredDistance", "Vector_default", "target"]}