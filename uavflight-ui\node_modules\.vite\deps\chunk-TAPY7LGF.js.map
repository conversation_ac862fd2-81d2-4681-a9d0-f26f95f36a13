{"version": 3, "sources": ["../../ol/Tile.js"], "sourcesContent": ["/**\n * @module ol/Tile\n */\nimport EventTarget from './events/Target.js';\nimport EventType from './events/EventType.js';\nimport TileState from './TileState.js';\nimport {abstract} from './util.js';\nimport {easeIn} from './easing.js';\n\n/**\n * A function that takes an {@link module:ol/Tile~Tile} for the tile and a\n * `{string}` for the url as arguments. The default is\n * ```js\n * source.setTileLoadFunction(function(tile, src) {\n *   tile.getImage().src = src;\n * });\n * ```\n * For more fine grained control, the load function can use fetch or XMLHttpRequest and involve\n * error handling:\n *\n * ```js\n * import TileState from 'ol/TileState.js';\n *\n * source.setTileLoadFunction(function(tile, src) {\n *   const xhr = new XMLHttpRequest();\n *   xhr.responseType = 'blob';\n *   xhr.addEventListener('loadend', function (evt) {\n *     const data = this.response;\n *     if (data !== undefined) {\n *       tile.getImage().src = URL.createObjectURL(data);\n *     } else {\n *       tile.setState(TileState.ERROR);\n *     }\n *   });\n *   xhr.addEventListener('error', function () {\n *     tile.setState(TileState.ERROR);\n *   });\n *   xhr.open('GET', src);\n *   xhr.send();\n * });\n * ```\n *\n * @typedef {function(Tile, string): void} LoadFunction\n * @api\n */\n\n/**\n * {@link module:ol/source/Tile~TileSource} sources use a function of this type to get\n * the url that provides a tile for a given tile coordinate.\n *\n * This function takes an {@link module:ol/tilecoord~TileCoord} for the tile\n * coordinate, a `{number}` representing the pixel ratio and a\n * {@link module:ol/proj/Projection~Projection} for the projection  as arguments\n * and returns a `{string}` representing the tile URL, or undefined if no tile\n * should be requested for the passed tile coordinate.\n *\n * @typedef {function(import(\"./tilecoord.js\").TileCoord, number,\n *           import(\"./proj/Projection.js\").default): (string|undefined)} UrlFunction\n * @api\n */\n\n/**\n * @typedef {Object} Options\n * @property {number} [transition=250] A duration for tile opacity\n * transitions in milliseconds. A duration of 0 disables the opacity transition.\n * @property {boolean} [interpolate=false] Use interpolated values when resampling.  By default,\n * the nearest neighbor is used when resampling.\n * @api\n */\n\n/**\n * @classdesc\n * Base class for tiles.\n *\n * @abstract\n */\nclass Tile extends EventTarget {\n  /**\n   * @param {import(\"./tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n   * @param {import(\"./TileState.js\").default} state State.\n   * @param {Options} [options] Tile options.\n   */\n  constructor(tileCoord, state, options) {\n    super();\n\n    options = options ? options : {};\n\n    /**\n     * @type {import(\"./tilecoord.js\").TileCoord}\n     */\n    this.tileCoord = tileCoord;\n\n    /**\n     * @protected\n     * @type {import(\"./TileState.js\").default}\n     */\n    this.state = state;\n\n    /**\n     * An \"interim\" tile for this tile. The interim tile may be used while this\n     * one is loading, for \"smooth\" transitions when changing params/dimensions\n     * on the source.\n     * @type {Tile}\n     */\n    this.interimTile = null;\n\n    /**\n     * A key assigned to the tile. This is used by the tile source to determine\n     * if this tile can effectively be used, or if a new tile should be created\n     * and this one be used as an interim tile for this new tile.\n     * @type {string}\n     */\n    this.key = '';\n\n    /**\n     * The duration for the opacity transition.\n     * @type {number}\n     */\n    this.transition_ =\n      options.transition === undefined ? 250 : options.transition;\n\n    /**\n     * Lookup of start times for rendering transitions.  If the start time is\n     * equal to -1, the transition is complete.\n     * @type {Object<string, number>}\n     */\n    this.transitionStarts_ = {};\n\n    /**\n     * @type {boolean}\n     */\n    this.interpolate = !!options.interpolate;\n  }\n\n  /**\n   * @protected\n   */\n  changed() {\n    this.dispatchEvent(EventType.CHANGE);\n  }\n\n  /**\n   * Called by the tile cache when the tile is removed from the cache due to expiry\n   */\n  release() {\n    if (this.state === TileState.ERROR) {\n      // to remove the `change` listener on this tile in `ol/TileQueue#handleTileChange`\n      this.setState(TileState.EMPTY);\n    }\n  }\n\n  /**\n   * @return {string} Key.\n   */\n  getKey() {\n    return this.key + '/' + this.tileCoord;\n  }\n\n  /**\n   * Get the interim tile most suitable for rendering using the chain of interim\n   * tiles. This corresponds to the  most recent tile that has been loaded, if no\n   * such tile exists, the original tile is returned.\n   * @return {!Tile} Best tile for rendering.\n   */\n  getInterimTile() {\n    if (!this.interimTile) {\n      //empty chain\n      return this;\n    }\n    let tile = this.interimTile;\n\n    // find the first loaded tile and return it. Since the chain is sorted in\n    // decreasing order of creation time, there is no need to search the remainder\n    // of the list (all those tiles correspond to older requests and will be\n    // cleaned up by refreshInterimChain)\n    do {\n      if (tile.getState() == TileState.LOADED) {\n        // Show tile immediately instead of fading it in after loading, because\n        // the interim tile is in place already\n        this.transition_ = 0;\n        return tile;\n      }\n      tile = tile.interimTile;\n    } while (tile);\n\n    // we can not find a better tile\n    return this;\n  }\n\n  /**\n   * Goes through the chain of interim tiles and discards sections of the chain\n   * that are no longer relevant.\n   */\n  refreshInterimChain() {\n    if (!this.interimTile) {\n      return;\n    }\n\n    let tile = this.interimTile;\n\n    /**\n     * @type {Tile}\n     */\n    let prev = this;\n\n    do {\n      if (tile.getState() == TileState.LOADED) {\n        //we have a loaded tile, we can discard the rest of the list\n        //we would could abort any LOADING tile request\n        //older than this tile (i.e. any LOADING tile following this entry in the chain)\n        tile.interimTile = null;\n        break;\n      } else if (tile.getState() == TileState.LOADING) {\n        //keep this LOADING tile any loaded tiles later in the chain are\n        //older than this tile, so we're still interested in the request\n        prev = tile;\n      } else if (tile.getState() == TileState.IDLE) {\n        //the head of the list is the most current tile, we don't need\n        //to start any other requests for this chain\n        prev.interimTile = tile.interimTile;\n      } else {\n        prev = tile;\n      }\n      tile = prev.interimTile;\n    } while (tile);\n  }\n\n  /**\n   * Get the tile coordinate for this tile.\n   * @return {import(\"./tilecoord.js\").TileCoord} The tile coordinate.\n   * @api\n   */\n  getTileCoord() {\n    return this.tileCoord;\n  }\n\n  /**\n   * @return {import(\"./TileState.js\").default} State.\n   */\n  getState() {\n    return this.state;\n  }\n\n  /**\n   * Sets the state of this tile. If you write your own {@link module:ol/Tile~LoadFunction tileLoadFunction} ,\n   * it is important to set the state correctly to {@link module:ol/TileState~ERROR}\n   * when the tile cannot be loaded. Otherwise the tile cannot be removed from\n   * the tile queue and will block other requests.\n   * @param {import(\"./TileState.js\").default} state State.\n   * @api\n   */\n  setState(state) {\n    if (this.state !== TileState.ERROR && this.state > state) {\n      throw new Error('Tile load sequence violation');\n    }\n    this.state = state;\n    this.changed();\n  }\n\n  /**\n   * Load the image or retry if loading previously failed.\n   * Loading is taken care of by the tile queue, and calling this method is\n   * only needed for preloading or for reloading in case of an error.\n   * @abstract\n   * @api\n   */\n  load() {\n    abstract();\n  }\n\n  /**\n   * Get the alpha value for rendering.\n   * @param {string} id An id for the renderer.\n   * @param {number} time The render frame time.\n   * @return {number} A number between 0 and 1.\n   */\n  getAlpha(id, time) {\n    if (!this.transition_) {\n      return 1;\n    }\n\n    let start = this.transitionStarts_[id];\n    if (!start) {\n      start = time;\n      this.transitionStarts_[id] = start;\n    } else if (start === -1) {\n      return 1;\n    }\n\n    const delta = time - start + 1000 / 60; // avoid rendering at 0\n    if (delta >= this.transition_) {\n      return 1;\n    }\n    return easeIn(delta / this.transition_);\n  }\n\n  /**\n   * Determine if a tile is in an alpha transition.  A tile is considered in\n   * transition if tile.getAlpha() has not yet been called or has been called\n   * and returned 1.\n   * @param {string} id An id for the renderer.\n   * @return {boolean} The tile is in transition.\n   */\n  inTransition(id) {\n    if (!this.transition_) {\n      return false;\n    }\n    return this.transitionStarts_[id] !== -1;\n  }\n\n  /**\n   * Mark a transition as complete.\n   * @param {string} id An id for the renderer.\n   */\n  endTransition(id) {\n    if (this.transition_) {\n      this.transitionStarts_[id] = -1;\n    }\n  }\n}\n\nexport default Tile;\n"], "mappings": ";;;;;;;;;;;;;;;AA4EA,IAAM,OAAN,cAAmB,eAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7B,YAAY,WAAW,OAAO,SAAS;AACrC,UAAM;AAEN,cAAU,UAAU,UAAU,CAAC;AAK/B,SAAK,YAAY;AAMjB,SAAK,QAAQ;AAQb,SAAK,cAAc;AAQnB,SAAK,MAAM;AAMX,SAAK,cACH,QAAQ,eAAe,SAAY,MAAM,QAAQ;AAOnD,SAAK,oBAAoB,CAAC;AAK1B,SAAK,cAAc,CAAC,CAAC,QAAQ;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,cAAc,kBAAU,MAAM;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,QAAI,KAAK,UAAU,kBAAU,OAAO;AAElC,WAAK,SAAS,kBAAU,KAAK;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,WAAO,KAAK,MAAM,MAAM,KAAK;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB;AACf,QAAI,CAAC,KAAK,aAAa;AAErB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,KAAK;AAMhB,OAAG;AACD,UAAI,KAAK,SAAS,KAAK,kBAAU,QAAQ;AAGvC,aAAK,cAAc;AACnB,eAAO;AAAA,MACT;AACA,aAAO,KAAK;AAAA,IACd,SAAS;AAGT,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB;AACpB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AAEA,QAAI,OAAO,KAAK;AAKhB,QAAI,OAAO;AAEX,OAAG;AACD,UAAI,KAAK,SAAS,KAAK,kBAAU,QAAQ;AAIvC,aAAK,cAAc;AACnB;AAAA,MACF,WAAW,KAAK,SAAS,KAAK,kBAAU,SAAS;AAG/C,eAAO;AAAA,MACT,WAAW,KAAK,SAAS,KAAK,kBAAU,MAAM;AAG5C,aAAK,cAAc,KAAK;AAAA,MAC1B,OAAO;AACL,eAAO;AAAA,MACT;AACA,aAAO,KAAK;AAAA,IACd,SAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,SAAS,OAAO;AACd,QAAI,KAAK,UAAU,kBAAU,SAAS,KAAK,QAAQ,OAAO;AACxD,YAAM,IAAI,MAAM,8BAA8B;AAAA,IAChD;AACA,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO;AACL,aAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,IAAI,MAAM;AACjB,QAAI,CAAC,KAAK,aAAa;AACrB,aAAO;AAAA,IACT;AAEA,QAAI,QAAQ,KAAK,kBAAkB,EAAE;AACrC,QAAI,CAAC,OAAO;AACV,cAAQ;AACR,WAAK,kBAAkB,EAAE,IAAI;AAAA,IAC/B,WAAW,UAAU,IAAI;AACvB,aAAO;AAAA,IACT;AAEA,UAAM,QAAQ,OAAO,QAAQ,MAAO;AACpC,QAAI,SAAS,KAAK,aAAa;AAC7B,aAAO;AAAA,IACT;AACA,WAAO,OAAO,QAAQ,KAAK,WAAW;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,IAAI;AACf,QAAI,CAAC,KAAK,aAAa;AACrB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,kBAAkB,EAAE,MAAM;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,IAAI;AAChB,QAAI,KAAK,aAAa;AACpB,WAAK,kBAAkB,EAAE,IAAI;AAAA,IAC/B;AAAA,EACF;AACF;AAEA,IAAO,eAAQ;", "names": []}