<template>
  <div class="chart-wrapper">
    <div v-if="loading" class="loading-mask">数据加载中...</div>
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts/core'
import { BarChart, type BarSeriesOption } from 'echarts/charts'
import {
  TitleComponent,
  type TitleComponentOption,
  GridComponent,
  type GridComponentOption,
  TooltipComponent,
  type TooltipComponentOption,
  LegendComponent,
  type LegendComponentOption,
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import type { PropType } from 'vue'

// ECharts 模块注册
echarts.use([BarChart, TitleComponent, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer])

type ECOption = echarts.ComposeOption<
  | BarSeriesOption
  | TitleComponentOption
  | GridComponentOption
  | TooltipComponentOption
  | LegendComponentOption
>

interface SeriesItem {
  name: string
  data: number[]
  stack?: string
  type?: 'bar'
}

const props = defineProps({
  title: {
    type: String,
    default: '层叠柱状图'
  },
  xAxisData: {
    type: Array as PropType<string[]>,
    required: true,
    validator: (value: unknown) => Array.isArray(value) && value.length > 0
  },
  seriesData: {
    type: Array as PropType<SeriesItem[]>,
    required: true,
    validator: (value: unknown) => 
      Array.isArray(value) && 
      value.every(item => 
        typeof item === 'object' && 
        'name' in item && 
        'data' in item
      )
  },
  colors: {
    type: Array as PropType<string[]>,
    default: () => [
      '#5470c6', '#91cc75', '#fac858', '#ee6666', 
      '#73c0de', '#3ba272', '#fc8452', '#9a60b4', 
      '#ea7ccc'
    ]
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null
const chartReady = ref(false)

// 防御式生成配置项
const generateOption = (): ECOption => {
  const safeSeriesData = props.seriesData ?? []
  const safeXAxisData = props.xAxisData ?? []

  return {
    title: {
      text: props.title,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    legend: {
      top: 30,
      data: safeSeriesData.map(item => item.name)
    },
    grid: {
      top: 80,
      left: '3%',
      right: '3%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: safeXAxisData
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => 
          value >= 1000 ? `${value / 1000}K` : String(value)
      }
    },
    series: safeSeriesData.map(item => ({
      ...item,
      type: 'bar' as const,
      stack: item.stack ?? 'total',
      emphasis: { focus: 'series' },
      label: {
        show: true,
        position: 'inside',
        formatter: ({ value }: { value: number }) => 
          value > 0 ? value : ''
      }
    })),
    color: props.colors
  }
}

// 图表初始化
const initChart = () => {
  if (!chartRef.value) {
    console.warn('图表容器未找到')
    return
  }

  try {
    chartInstance?.dispose()
    chartInstance = echarts.init(chartRef.value)
    chartInstance.setOption(generateOption())
    chartReady.value = true
  } catch (error) {
    console.error('图表初始化失败:', error)
    chartReady.value = false
  }
}

// 更新图表
const updateChart = () => {
  if (!chartInstance || !chartReady.value) return
  
  try {
    chartInstance.setOption(generateOption(), {
      notMerge: true,
      lazyUpdate: false
    })
  } catch (error) {
    console.error('图表更新失败:', error)
  }
}

// 自适应调整
const handleResize = () => {
  if (chartInstance && chartReady.value) {
    try {
      chartInstance.resize()
    } catch (error) {
      console.error('图表调整大小失败:', error)
    }
  }
}

// 生命周期管理
onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

// 响应式更新
watch(
  () => [
    props.title,
    props.colors,
    props.seriesData?.length,
    props.xAxisData?.length
  ],
  () => {
    nextTick(() => {
      if (props.seriesData?.length && props.xAxisData?.length) {
        updateChart()
        handleResize()
      } else {
        chartInstance?.clear()
      }
    })
  },
  { deep: true }
)

// 处理加载状态变化
watch(() => props.loading, (newVal) => {
  if (newVal) {
    chartInstance?.showLoading()
  } else {
    chartInstance?.hideLoading()
  }
})
</script>

<style scoped>
.chart-wrapper {
  position: relative;
  width: 100%;
  height: 500px;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
</style>
