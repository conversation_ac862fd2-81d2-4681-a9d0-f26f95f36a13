const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/subItem.DuIlENf5.js","assets/vue.CnN__PXn.js","assets/index.C0-0gsfl.js","assets/index.CB8Sa4Ps.css"])))=>i.map(i=>d[i]);
import{u as E,o as m,a as V,__tla as W}from"./index.C0-0gsfl.js";import{d as C,s as B,l as F,A as G,c as A,o as H,O as Q,w as R,B as p,e as d,b as i,v as s,a as g,F as I,p as S,u as t,t as h,j as T,f,G as _,P as U,Q as z}from"./vue.CnN__PXn.js";let L,J=Promise.all([(()=>{try{return W}catch{}})()]).then(async()=>{let y,v;y=["onClick"],v=C({name:"navMenuVertical"}),L=C({...v,props:{menuList:{type:Array,default:()=>[]}},setup(b){const x=T(()=>V(()=>import("./subItem.DuIlENf5.js").then(async n=>(await n.__tla,n)),__vite__mapDeps([0,1,2,3]))),D=b,P=E(),{themeConfig:u}=B(P),c=F(),l=G({defaultActive:c.meta.isDynamic?c.meta.isDynamicPath:c.path,isCollapse:!1}),M=A(()=>D.menuList),O=A(()=>u.value),k=n=>{const{path:r,meta:a}=n,o=a!=null&&a.isDynamic?a.isDynamicPath.split("/"):r.split("/");return o.length>=4&&(a!=null&&a.isHide)?o.splice(0,3).join("/"):r};return H(()=>{l.defaultActive=k(c)}),Q(n=>{l.defaultActive=k(n),document.body.clientWidth<1e3&&(u.value.isCollapse=!1)}),R(u.value,()=>{document.body.clientWidth<=1e3?l.isCollapse=!1:l.isCollapse=u.value.isCollapse},{immediate:!0}),(n,r)=>{const a=p("SvgIcon"),o=p("el-sub-menu"),j=p("el-menu-item"),q=p("el-menu");return i(),d(q,{router:"","default-active":t(l).defaultActive,"background-color":"transparent",collapse:t(l).isCollapse,"unique-opened":t(O).isUniqueOpened,"collapse-transition":!1},{default:s(()=>[(i(!0),g(I,null,S(t(M),e=>(i(),g(I,null,[e.children&&e.children.length>0?(i(),d(o,{index:e.path,key:e.path},{title:s(()=>[h(a,{name:e.meta.icon},null,8,["name"]),f("span",null,_(t(m).setMenuI18n(e)),1)]),default:s(()=>[h(t(x),{chil:e.children},null,8,["chil"])]),_:2},1032,["index"])):(i(),d(j,{index:e.path,key:e.path},U({default:s(()=>[h(a,{name:e.meta.icon},null,8,["name"])]),_:2},[!e.meta.isLink||e.meta.isLink&&e.meta.isIframe?{name:"title",fn:s(()=>[f("span",null,_(t(m).setMenuI18n(e)),1)]),key:"0"}:{name:"title",fn:s(()=>[f("a",{class:"w100",onClick:z(K=>(w=>{m.handleOpenLink(w)})(e),["prevent"])},_(t(m).setMenuI18n(e)),9,y)]),key:"1"}]),1032,["index"]))],64))),256))]),_:1},8,["default-active","collapse","unique-opened"])}}})});export{J as __tla,L as default};
