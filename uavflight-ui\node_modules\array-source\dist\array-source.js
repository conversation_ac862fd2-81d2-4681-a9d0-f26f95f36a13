// https://github.com/mbostock/array-source Version 0.0.4. Copyright 2017 <PERSON>.
(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
	typeof define === 'function' && define.amd ? define(factory) :
	(global.sources = global.sources || {}, global.sources.array = factory());
}(this, (function () { 'use strict';

var array_cancel = function() {
  this._array = null;
  return Promise.resolve();
};

var array_read = function() {
  var array = this._array;
  this._array = null;
  return Promise.resolve(array ? {done: false, value: array} : {done: true, value: undefined});
};

function array(array) {
  return new ArraySource(array instanceof Uint8Array ? array : new Uint8Array(array));
}

function ArraySource(array) {
  this._array = array;
}

ArraySource.prototype.read = array_read;
ArraySource.prototype.cancel = array_cancel;

return array;

})));
