import threeImg from "@/assets/img/common/three.png";
import {
    useToolsStore
} from "@/store/tools";

import {
    getLayerdirectory
} from "@/api/home/<USER>";
import {
    storeToRefs
} from "pinia";

import {
    defineComponent,
    nextTick
} from "vue";


class LayerManager {
    constructor() {
        this.init();

    }
    //图层树列表
    treeData = ref([]);

    init() {
        getLayerdirectory().then((res) => res.data).then((res) => {
            this.loadLabels = [];
            this.cloneData(res);

            this.loadModels();

            this.initCheckedKeys();
        });
    }

    cloneData(nodes, index = -1) {
        for (var i = 0; i < nodes.length; i++) {
            var node = nodes[i];
            if (
                node.hasOwnProperty("layerDirectoryRespVOList") &&
                node.layerDirectoryRespVOList.length > 0
            ) {
                this.treeData.value.push({
                    id: node.id,
                    label: node.typeName,
                    icon: threeImg,
                    layer: node.layer,
                    type: node.typeCode,
                    legend: [{
                        icon: threeImg,
                        title: node.description,
                    }, ],
                    children: [],
                });
                this.cloneData(node.layerDirectoryRespVOList, i);
            } else if (
                node.hasOwnProperty("layerDirectoryRespVOList") &&
                node.layerDirectoryRespVOList.length == 0
            ) {
                this.treeData.value.push({
                    id: node.id,
                    label: node.typeName,
                    icon: threeImg,
                    layer: node.layer,
                    type: node.typeCode,
                    legend: [{
                        icon: threeImg,
                        title: node.description,
                    }, ],
                });
            } else {
                if (this.treeData.value.hasOwnProperty("children")) {
                    this.treeData.value[index].children.push({
                        id: node.id,
                        label: node.typeName,
                        icon: threeImg,
                        layer: node.layer,
                        type: node.typeCode,
                        legend: [{
                            icon: threeImg,
                            title: node.description,
                        }, ],
                    });
                } else {
                    this.treeData.value.push({
                        id: node.id,
                        label: node.typeName,
                        icon: threeImg,
                        layer: node.layer,
                        type: node.typeCode,
                        legend: [{
                            icon: threeImg,
                            title: node.description,
                        }, ],
                    });
                }

            }
        }

    }


    loadModels() {
        const loadModel = (nodes) => {
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];

                if (node.hasOwnProperty("children")) {
                    loadModel(node.children);
                }
                if (node.layer != null && node.layer != '' && node.type == "tileset") {

                    earth.loadLayer({
                        id: node.id,
                        url: node.layer,
                        type: "tileset_url",
                        label: node.label,
                    });
                    this.loadLabels.push(node.label);


                }
            }
        };
     
        loadModel(this.treeData.value);
       

    }



    initCheckedKeys() {

        //设置图层管理默认勾选
        const toolsStore = useToolsStore();
        toolsStore.$patch((state) => {
            var dataTree = state.dataTree;
            var indexList = this.getIdsByLabels(this.loadLabels);
            //延迟1S等el-tree加载完成      
            setTimeout(() => {
                dataTree.setCheckedKeys(indexList);
            }, 1000);

        });





    }


    //图层的点击事件
    nodeClick() {
        return (data, checked, node) => {
            console.log(data, checked, node)
        };
    }

    //图层的勾选事件
    checkChange() {
        return (data, checked, node) => {
            const toolsStore = useToolsStore();
            if (!data.children) {
                //没有子节点    ----用于添加右侧图例
                if (checked) {
                    toolsStore.$patch((state) => {
                        state.layerArr.push(data);
                    });
                } else {
                    toolsStore.$patch((state) => {
                        let acData = state.layerArr;
                        let _acData = acData.findIndex((item) => item.id == data.id);
                        if (_acData > -1) {
                            acData.splice(_acData, 1);
                        }
                        state.layerArr = acData;
                    });
                }
            }

            var _node = this.getNodeByLabel(data.label);
            switch (true) {
                case _node.type == "tileset":
                    var id = this.getIdByLabel(data.label);

                    if (earth.layerManager.hasOwnProperty(id)) {
                        earth.layerManager[id].layer.show = checked;
                    }
                    break;
                case _node.type == "wmts":
                    break;
                case _node.type == "dir":
                    var id = this.getIdByLabel(data.label);
                    if (earth.layerManager.hasOwnProperty(id)) {
                        earth.layerManager[id].layer.show = checked;
                    }
                    break;
            }

            viewer.scene.forceRender();
        };
    }
    //通过extdata属性来确定是否显隐
    showFeatureByExtdata(featureList, data, checked) {
        //图层列表的label与模型的属性可能会不一致，需要映射
        var label = this.labelMap.hasOwnProperty(data.label) ?
            this.labelMap[data.label] :
            data.label;
        for (var i = 0; i < featureList.length; i++) {
            if (featureList[i].getProperty("extdata") == label) {
                featureList[i].show = checked;
            }
        }
    }

    getNodesByLabels(labels) {
        var nodesList = [];

        function getNodes(nodes) {
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];
                if (node.hasOwnProperty("children")) {
                    getNodes(node.children);
                }
                if (labels.indexOf(node.label) > -1) {
                    nodesList.push(node);
                }
            }
        }
        getNodes(this.treeData.value);
        return nodesList;
    }

    getIdsByLabels(labels) {
        var idsList = [];

        function getIds(nodes) {
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];
                if (node.hasOwnProperty("children")) {
                    getIds(node.children);
                }
                if (labels.indexOf(node.label) > -1) {
                    idsList.push(node.id);
                }
            }
        }
        getIds(this.treeData.value);
        return idsList;
    }

    getIdByLabel(label) {
        var id = -1;

        function getId(nodes) {
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];
                if (node.hasOwnProperty("children")) {
                    getId(node.children);
                }
                if (label == node.label) {
                    id = node.id;
                    break;
                }
            }
        }
        getId(this.treeData.value);
        return id;
    }
    getNodeByLabel(label) {
        var _node = -1;

        function getNode(nodes) {
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];
                if (node.hasOwnProperty("children")) {
                    getNode(node.children);
                }
                if (label == node.label) {
                    _node = node;
                    break;
                }
            }
        }
        getNode(this.treeData.value);
        return _node;
    }

    getLayersByLabels(labels) {
        var layers = [];

        function getLayers(nodes) {
            for (var i = 0; i < nodes.length; i++) {
                var node = nodes[i];
                if (node.hasOwnProperty("children")) {
                    getLayers(node.children);
                }
                if (labels.indexOf(node.label) > -1) {
                    layers.push(earth.layerManager[node.id].layer);
                }
            }
        }
        getLayers(this.treeData.value);
        return layers;
    }



}

export default LayerManager;