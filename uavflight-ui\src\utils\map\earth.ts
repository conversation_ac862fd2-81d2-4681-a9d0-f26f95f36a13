import * as Cesium from 'cesium';

class Earth {
    layerManager = {}
    entities = []
    planeList = []
    viewer=null

    constructor(_viewer:any) {
        this.initEarth(_viewer);
    }

    /**
     * 初始化加载地球
     * <AUTHOR>
     */
    initEarth(_viewer:any) {
        this.viewer = _viewer
        // window.viewer = _viewer
        this.initEarthConfig();


    }

    getViewer() {
        return this.viewer;
    }

    getEntities() {
        return this.entities;
    }
    /**
     * 初始化球配置
     * <AUTHOR>
     * hencai
     */
    initEarthConfig() {
        // 启用地形深度测试，确保3D对象正确渲染
        this.viewer.scene.globe.depthTestAgainstTerrain = true;
      
        //抗锯齿
        this.viewer.scene.fxaa = true;
        this.viewer.scene.postProcessStages.fxaa.enabled = true;
        //清除月亮太阳
        // viewer.scene.moon.show = false
        // viewer.scene.sun.show = false
        // viewer.scene.globe.show = false;


        // viewer.scene.globe.enableLighting = false; //关闭光照
        // viewer.shadows = false; //关闭阴影
        // this.viewer.cesiumWidget.creditContainer.style.display = "none";
        // this.viewer.scene.camera.changed.addEventListener(function () {
        //     this.viewer.scene.light = new Cesium.DirectionalLight({
        //         direction: this.viewer.scene.camera.directionWC,
        //     });
        //     this.viewer.scene.camera.changed.addEventListener(function (scene, time) {
        //         this.viewer.scene.light.direction = Cesium.Cartesian3.clone(
        //             this.viewer.scene.camera.directionWC,
        //             this.viewer.scene.light.direction
        //         );
        //     });
        //     this.viewer.scene.requestRender();
        // })

        //设置鼠标进去地下
        // viewer.scene.screenSpaceCameraController.enableCollisionDetection = false;


    }

    /**
     * 添加图层，支持3dtiles,wmts url,gltf等，后续所有图层类型均通过该方法进行扩展
     * tileset:3dtileset配置别名
     * tileset_url:3dtileset的服务地址
     * geowintms:二维瓦片配置别名
     * geowintms_url:二维瓦片的url服务地址
     * heatmap:热力图
     * @params {*}
     * {
     *  id:layerid,
     *  type:geowindem/tileset/geowintms,heatmap,
     *  position:
     *  options:
     *  bounds:
     *  tileName:
     * }
     *
     * <AUTHOR>
     */
    loadLayer(treeNode) {
        var id = treeNode.id;
        var layer = null;
        this.layerManager[id] = {
            treeNode: treeNode,
            layer: layer,
            features: [],
            isload: false
        };
        
        

        if (treeNode.type == "tileset_url" || treeNode.type == 'tileset') {
            var tileset = this.viewer.scene.primitives.add(
                new Cesium.Cesium3DTileset({
                    url: treeNode.url, //数据路径
                    
                }));
    
                
            
            tileset.readyPromise.then((tiles) => {
                this.layerManager[id].layer=tiles
                viewer.flyTo(tiles, {
                    duration: 1,
                });
            })              
        }

        // if (treeNode.setStyle != null)
        //     window[treeNode.setStyle].call(this, layer, treeNode);


    }

    /**
     * 移除图层
     * @param {*} treeNode
     * @returns
     */
    removeLayer(treeNode) {
        var id = treeNode.id;
        if (this.layerManager[id] == null) return;
        if (treeNode.type == "tileset" || treeNode.type == "geowintms") {
            this.geoapp.hide(this.layerManager[id].layer);
            return;
        }
        if (treeNode.type == "geowindem") {
            this.geoapp.removeTerrain();
            return;
        }
        if (this.layerManager[id].layer)
            this.geoapp.removeEntity(this.layerManager[id].layer);
        delete this.layerManager[id];
    }





}
export default Earth;