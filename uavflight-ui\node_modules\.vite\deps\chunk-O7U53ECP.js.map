{"version": 3, "sources": ["../../ol/MapBrowserEventType.js", "../../ol/events/condition.js"], "sourcesContent": ["/**\n * @module ol/MapBrowserEventType\n */\nimport EventType from './events/EventType.js';\n\n/**\n * Constants for event names.\n * @enum {string}\n */\nexport default {\n  /**\n   * A true single click with no dragging and no double click. Note that this\n   * event is delayed by 250 ms to ensure that it is not a double click.\n   * @event module:ol/MapBrowserEvent~MapBrowserEvent#singleclick\n   * @api\n   */\n  SINGLECLICK: 'singleclick',\n\n  /**\n   * A click with no dragging. A double click will fire two of this.\n   * @event module:ol/MapBrowserEvent~MapBrowserEvent#click\n   * @api\n   */\n  CLICK: EventType.CLICK,\n\n  /**\n   * A true double click, with no dragging.\n   * @event module:ol/MapBrowserEvent~MapBrowserEvent#dblclick\n   * @api\n   */\n  DBLCLICK: EventType.DBLCLICK,\n\n  /**\n   * Triggered when a pointer is dragged.\n   * @event module:ol/MapBrowserEvent~MapBrowserEvent#pointerdrag\n   * @api\n   */\n  POINTERDRAG: 'pointerdrag',\n\n  /**\n   * Triggered when a pointer is moved. Note that on touch devices this is\n   * triggered when the map is panned, so is not the same as mousemove.\n   * @event module:ol/MapBrowserEvent~MapBrowserEvent#pointermove\n   * @api\n   */\n  POINTERMOVE: 'pointermove',\n\n  POINTERDOWN: 'pointerdown',\n  POINTERUP: 'pointerup',\n  POINTEROVER: 'pointerover',\n  POINTEROUT: 'pointerout',\n  POINTERENTER: 'pointerenter',\n  POINTERLEAVE: 'pointerleave',\n  POINTERCANCEL: 'pointercancel',\n};\n\n/***\n * @typedef {'singleclick'|'click'|'dblclick'|'pointerdrag'|'pointermove'} Types\n */\n", "/**\n * @module ol/events/condition\n */\nimport MapBrowserEventType from '../MapBrowserEventType.js';\nimport {FALSE, TRUE} from '../functions.js';\nimport {MAC, WEBKIT} from '../has.js';\nimport {assert} from '../asserts.js';\n\n/**\n * A function that takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * `{boolean}`. If the condition is met, true should be returned.\n *\n * @typedef {function(this: ?, import(\"../MapBrowserEvent.js\").default): boolean} Condition\n */\n\n/**\n * Creates a condition function that passes when all provided conditions pass.\n * @param {...Condition} var_args Conditions to check.\n * @return {Condition} Condition function.\n */\nexport function all(var_args) {\n  const conditions = arguments;\n  /**\n   * @param {import(\"../MapBrowserEvent.js\").default} event Event.\n   * @return {boolean} All conditions passed.\n   */\n  return function (event) {\n    let pass = true;\n    for (let i = 0, ii = conditions.length; i < ii; ++i) {\n      pass = pass && conditions[i](event);\n      if (!pass) {\n        break;\n      }\n    }\n    return pass;\n  };\n}\n\n/**\n * Return `true` if only the alt-key is pressed, `false` otherwise (e.g. when\n * additionally the shift-key is pressed).\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True if only the alt key is pressed.\n * @api\n */\nexport const altKeyOnly = function (mapBrowserEvent) {\n  const originalEvent = /** @type {KeyboardEvent|MouseEvent|TouchEvent} */ (\n    mapBrowserEvent.originalEvent\n  );\n  return (\n    originalEvent.altKey &&\n    !(originalEvent.metaKey || originalEvent.ctrlKey) &&\n    !originalEvent.shiftKey\n  );\n};\n\n/**\n * Return `true` if only the alt-key and shift-key is pressed, `false` otherwise\n * (e.g. when additionally the platform-modifier-key is pressed).\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True if only the alt and shift keys are pressed.\n * @api\n */\nexport const altShiftKeysOnly = function (mapBrowserEvent) {\n  const originalEvent = /** @type {KeyboardEvent|MouseEvent|TouchEvent} */ (\n    mapBrowserEvent.originalEvent\n  );\n  return (\n    originalEvent.altKey &&\n    !(originalEvent.metaKey || originalEvent.ctrlKey) &&\n    originalEvent.shiftKey\n  );\n};\n\n/**\n * Return `true` if the map has the focus. This condition requires a map target\n * element with a `tabindex` attribute, e.g. `<div id=\"map\" tabindex=\"1\">`.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} event Map browser event.\n * @return {boolean} The map has the focus.\n * @api\n */\nexport const focus = function (event) {\n  const targetElement = event.map.getTargetElement();\n  const activeElement = event.map.getOwnerDocument().activeElement;\n  return targetElement.contains(activeElement);\n};\n\n/**\n * Return `true` if the map has the focus or no 'tabindex' attribute set.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} event Map browser event.\n * @return {boolean} The map container has the focus or no 'tabindex' attribute.\n */\nexport const focusWithTabindex = function (event) {\n  return event.map.getTargetElement().hasAttribute('tabindex')\n    ? focus(event)\n    : true;\n};\n\n/**\n * Return always true.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True.\n * @api\n */\nexport const always = TRUE;\n\n/**\n * Return `true` if the event is a `click` event, `false` otherwise.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True if the event is a map `click` event.\n * @api\n */\nexport const click = function (mapBrowserEvent) {\n  return mapBrowserEvent.type == MapBrowserEventType.CLICK;\n};\n\n/**\n * Return `true` if the event has an \"action\"-producing mouse button.\n *\n * By definition, this includes left-click on windows/linux, and left-click\n * without the ctrl key on Macs.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} The result.\n */\nexport const mouseActionButton = function (mapBrowserEvent) {\n  const originalEvent = /** @type {MouseEvent} */ (\n    mapBrowserEvent.originalEvent\n  );\n  return originalEvent.button == 0 && !(WEBKIT && MAC && originalEvent.ctrlKey);\n};\n\n/**\n * Return always false.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} False.\n * @api\n */\nexport const never = FALSE;\n\n/**\n * Return `true` if the browser event is a `pointermove` event, `false`\n * otherwise.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True if the browser event is a `pointermove` event.\n * @api\n */\nexport const pointerMove = function (mapBrowserEvent) {\n  return mapBrowserEvent.type == 'pointermove';\n};\n\n/**\n * Return `true` if the event is a map `singleclick` event, `false` otherwise.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True if the event is a map `singleclick` event.\n * @api\n */\nexport const singleClick = function (mapBrowserEvent) {\n  return mapBrowserEvent.type == MapBrowserEventType.SINGLECLICK;\n};\n\n/**\n * Return `true` if the event is a map `dblclick` event, `false` otherwise.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True if the event is a map `dblclick` event.\n * @api\n */\nexport const doubleClick = function (mapBrowserEvent) {\n  return mapBrowserEvent.type == MapBrowserEventType.DBLCLICK;\n};\n\n/**\n * Return `true` if no modifier key (alt-, shift- or platform-modifier-key) is\n * pressed.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True only if there no modifier keys are pressed.\n * @api\n */\nexport const noModifierKeys = function (mapBrowserEvent) {\n  const originalEvent = /** @type {KeyboardEvent|MouseEvent|TouchEvent} */ (\n    mapBrowserEvent.originalEvent\n  );\n  return (\n    !originalEvent.altKey &&\n    !(originalEvent.metaKey || originalEvent.ctrlKey) &&\n    !originalEvent.shiftKey\n  );\n};\n\n/**\n * Return `true` if only the platform-modifier-key (the meta-key on Mac,\n * ctrl-key otherwise) is pressed, `false` otherwise (e.g. when additionally\n * the shift-key is pressed).\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True if only the platform modifier key is pressed.\n * @api\n */\nexport const platformModifierKeyOnly = function (mapBrowserEvent) {\n  const originalEvent = /** @type {KeyboardEvent|MouseEvent|TouchEvent} */ (\n    mapBrowserEvent.originalEvent\n  );\n  return (\n    !originalEvent.altKey &&\n    (MAC ? originalEvent.metaKey : originalEvent.ctrlKey) &&\n    !originalEvent.shiftKey\n  );\n};\n\n/**\n * Return `true` if the platform-modifier-key (the meta-key on Mac,\n * ctrl-key otherwise) is pressed.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True if the platform modifier key is pressed.\n * @api\n */\nexport const platformModifierKey = function (mapBrowserEvent) {\n  const originalEvent = /** @type {KeyboardEvent|MouseEvent|TouchEvent} */ (\n    mapBrowserEvent.originalEvent\n  );\n  return MAC ? originalEvent.metaKey : originalEvent.ctrlKey;\n};\n\n/**\n * Return `true` if only the shift-key is pressed, `false` otherwise (e.g. when\n * additionally the alt-key is pressed).\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True if only the shift key is pressed.\n * @api\n */\nexport const shiftKeyOnly = function (mapBrowserEvent) {\n  const originalEvent = /** @type {KeyboardEvent|MouseEvent|TouchEvent} */ (\n    mapBrowserEvent.originalEvent\n  );\n  return (\n    !originalEvent.altKey &&\n    !(originalEvent.metaKey || originalEvent.ctrlKey) &&\n    originalEvent.shiftKey\n  );\n};\n\n/**\n * Return `true` if the target element is not editable, i.e. not an `input`,\n * `select`, or `textarea` element and no `contenteditable` attribute is\n * set or inherited, `false` otherwise.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True only if the target element is not editable.\n * @api\n */\nexport const targetNotEditable = function (mapBrowserEvent) {\n  const originalEvent = /** @type {KeyboardEvent|MouseEvent|TouchEvent} */ (\n    mapBrowserEvent.originalEvent\n  );\n  const tagName = /** @type {Element} */ (originalEvent.target).tagName;\n  return (\n    tagName !== 'INPUT' &&\n    tagName !== 'SELECT' &&\n    tagName !== 'TEXTAREA' &&\n    // `isContentEditable` is only available on `HTMLElement`, but it may also be a\n    // different type like `SVGElement`.\n    // @ts-ignore\n    !originalEvent.target.isContentEditable\n  );\n};\n\n/**\n * Return `true` if the event originates from a mouse device.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True if the event originates from a mouse device.\n * @api\n */\nexport const mouseOnly = function (mapBrowserEvent) {\n  const pointerEvent = /** @type {import(\"../MapBrowserEvent\").default} */ (\n    mapBrowserEvent\n  ).originalEvent;\n  assert(pointerEvent !== undefined, 56); // mapBrowserEvent must originate from a pointer event\n  // see https://www.w3.org/TR/pointerevents/#widl-PointerEvent-pointerType\n  return pointerEvent.pointerType == 'mouse';\n};\n\n/**\n * Return `true` if the event originates from a touchable device.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True if the event originates from a touchable device.\n * @api\n */\nexport const touchOnly = function (mapBrowserEvent) {\n  const pointerEvt = /** @type {import(\"../MapBrowserEvent\").default} */ (\n    mapBrowserEvent\n  ).originalEvent;\n  assert(pointerEvt !== undefined, 56); // mapBrowserEvent must originate from a pointer event\n  // see https://www.w3.org/TR/pointerevents/#widl-PointerEvent-pointerType\n  return pointerEvt.pointerType === 'touch';\n};\n\n/**\n * Return `true` if the event originates from a digital pen.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True if the event originates from a digital pen.\n * @api\n */\nexport const penOnly = function (mapBrowserEvent) {\n  const pointerEvt = /** @type {import(\"../MapBrowserEvent\").default} */ (\n    mapBrowserEvent\n  ).originalEvent;\n  assert(pointerEvt !== undefined, 56); // mapBrowserEvent must originate from a pointer event\n  // see https://www.w3.org/TR/pointerevents/#widl-PointerEvent-pointerType\n  return pointerEvt.pointerType === 'pen';\n};\n\n/**\n * Return `true` if the event originates from a primary pointer in\n * contact with the surface or if the left mouse button is pressed.\n * See https://www.w3.org/TR/pointerevents/#button-states.\n *\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n * @return {boolean} True if the event originates from a primary pointer.\n * @api\n */\nexport const primaryAction = function (mapBrowserEvent) {\n  const pointerEvent = /** @type {import(\"../MapBrowserEvent\").default} */ (\n    mapBrowserEvent\n  ).originalEvent;\n  assert(pointerEvent !== undefined, 56); // mapBrowserEvent must originate from a pointer event\n  return pointerEvent.isPrimary && pointerEvent.button === 0;\n};\n"], "mappings": ";;;;;;;;;;;;;;AASA,IAAO,8BAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOb,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOb,OAAO,kBAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB,UAAU,kBAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQb,aAAa;AAAA,EAEb,aAAa;AAAA,EACb,WAAW;AAAA,EACX,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,eAAe;AACjB;;;AClCO,SAAS,IAAI,UAAU;AAC5B,QAAM,aAAa;AAKnB,SAAO,SAAU,OAAO;AACtB,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,aAAO,QAAQ,WAAW,CAAC,EAAE,KAAK;AAClC,UAAI,CAAC,MAAM;AACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAUO,IAAM,aAAa,SAAU,iBAAiB;AACnD,QAAM;AAAA;AAAA,IACJ,gBAAgB;AAAA;AAElB,SACE,cAAc,UACd,EAAE,cAAc,WAAW,cAAc,YACzC,CAAC,cAAc;AAEnB;AAUO,IAAM,mBAAmB,SAAU,iBAAiB;AACzD,QAAM;AAAA;AAAA,IACJ,gBAAgB;AAAA;AAElB,SACE,cAAc,UACd,EAAE,cAAc,WAAW,cAAc,YACzC,cAAc;AAElB;AAUO,IAAM,QAAQ,SAAU,OAAO;AACpC,QAAM,gBAAgB,MAAM,IAAI,iBAAiB;AACjD,QAAM,gBAAgB,MAAM,IAAI,iBAAiB,EAAE;AACnD,SAAO,cAAc,SAAS,aAAa;AAC7C;AAQO,IAAM,oBAAoB,SAAU,OAAO;AAChD,SAAO,MAAM,IAAI,iBAAiB,EAAE,aAAa,UAAU,IACvD,MAAM,KAAK,IACX;AACN;AASO,IAAM,SAAS;AASf,IAAM,QAAQ,SAAU,iBAAiB;AAC9C,SAAO,gBAAgB,QAAQ,4BAAoB;AACrD;AAWO,IAAM,oBAAoB,SAAU,iBAAiB;AAC1D,QAAM;AAAA;AAAA,IACJ,gBAAgB;AAAA;AAElB,SAAO,cAAc,UAAU,KAAK,EAAE,UAAU,OAAO,cAAc;AACvE;AASO,IAAM,QAAQ;AAUd,IAAM,cAAc,SAAU,iBAAiB;AACpD,SAAO,gBAAgB,QAAQ;AACjC;AASO,IAAM,cAAc,SAAU,iBAAiB;AACpD,SAAO,gBAAgB,QAAQ,4BAAoB;AACrD;AASO,IAAM,cAAc,SAAU,iBAAiB;AACpD,SAAO,gBAAgB,QAAQ,4BAAoB;AACrD;AAUO,IAAM,iBAAiB,SAAU,iBAAiB;AACvD,QAAM;AAAA;AAAA,IACJ,gBAAgB;AAAA;AAElB,SACE,CAAC,cAAc,UACf,EAAE,cAAc,WAAW,cAAc,YACzC,CAAC,cAAc;AAEnB;AAWO,IAAM,0BAA0B,SAAU,iBAAiB;AAChE,QAAM;AAAA;AAAA,IACJ,gBAAgB;AAAA;AAElB,SACE,CAAC,cAAc,WACd,MAAM,cAAc,UAAU,cAAc,YAC7C,CAAC,cAAc;AAEnB;AAUO,IAAM,sBAAsB,SAAU,iBAAiB;AAC5D,QAAM;AAAA;AAAA,IACJ,gBAAgB;AAAA;AAElB,SAAO,MAAM,cAAc,UAAU,cAAc;AACrD;AAUO,IAAM,eAAe,SAAU,iBAAiB;AACrD,QAAM;AAAA;AAAA,IACJ,gBAAgB;AAAA;AAElB,SACE,CAAC,cAAc,UACf,EAAE,cAAc,WAAW,cAAc,YACzC,cAAc;AAElB;AAWO,IAAM,oBAAoB,SAAU,iBAAiB;AAC1D,QAAM;AAAA;AAAA,IACJ,gBAAgB;AAAA;AAElB,QAAM;AAAA;AAAA,IAAkC,cAAc,OAAQ;AAAA;AAC9D,SACE,YAAY,WACZ,YAAY,YACZ,YAAY;AAAA;AAAA;AAAA,EAIZ,CAAC,cAAc,OAAO;AAE1B;AASO,IAAM,YAAY,SAAU,iBAAiB;AAClD,QAAM;AAAA;AAAA,IACJ,gBACA;AAAA;AACF,SAAO,iBAAiB,QAAW,EAAE;AAErC,SAAO,aAAa,eAAe;AACrC;AASO,IAAM,YAAY,SAAU,iBAAiB;AAClD,QAAM;AAAA;AAAA,IACJ,gBACA;AAAA;AACF,SAAO,eAAe,QAAW,EAAE;AAEnC,SAAO,WAAW,gBAAgB;AACpC;AASO,IAAM,UAAU,SAAU,iBAAiB;AAChD,QAAM;AAAA;AAAA,IACJ,gBACA;AAAA;AACF,SAAO,eAAe,QAAW,EAAE;AAEnC,SAAO,WAAW,gBAAgB;AACpC;AAWO,IAAM,gBAAgB,SAAU,iBAAiB;AACtD,QAAM;AAAA;AAAA,IACJ,gBACA;AAAA;AACF,SAAO,iBAAiB,QAAW,EAAE;AACrC,SAAO,aAAa,aAAa,aAAa,WAAW;AAC3D;", "names": []}