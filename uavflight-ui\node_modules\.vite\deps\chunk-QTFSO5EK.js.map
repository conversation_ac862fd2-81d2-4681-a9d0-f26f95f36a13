{"version": 3, "sources": ["../../ol/interaction/DoubleClickZoom.js", "../../ol/interaction/DragPan.js", "../../ol/interaction/DragRotate.js", "../../ol/render/Box.js", "../../ol/interaction/DragBox.js", "../../ol/interaction/DragZoom.js", "../../ol/events/Key.js", "../../ol/interaction/KeyboardPan.js", "../../ol/interaction/KeyboardZoom.js", "../../ol/interaction/MouseWheelZoom.js", "../../ol/interaction/PinchRotate.js", "../../ol/interaction/PinchZoom.js", "../../ol/Kinetic.js", "../../ol/interaction/defaults.js"], "sourcesContent": ["/**\n * @module ol/interaction/DoubleClickZoom\n */\nimport Interaction, {zoomByDel<PERSON>} from './Interaction.js';\nimport MapBrowserEventType from '../MapBrowserEventType.js';\n\n/**\n * @typedef {Object} Options\n * @property {number} [duration=250] Animation duration in milliseconds.\n * @property {number} [delta=1] The zoom delta applied on each double click.\n */\n\n/**\n * @classdesc\n * Allows the user to zoom by double-clicking on the map.\n * @api\n */\nclass DoubleClickZoom extends Interaction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    super();\n\n    options = options ? options : {};\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.delta_ = options.delta ? options.delta : 1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.duration_ = options.duration !== undefined ? options.duration : 250;\n  }\n\n  /**\n   * Handles the {@link module:ol/MapBrowserEvent~MapBrowserEvent map browser event} (if it was a\n   * doubleclick) and eventually zooms the map.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n   * @return {boolean} `false` to stop event propagation.\n   */\n  handleEvent(mapBrowserEvent) {\n    let stopEvent = false;\n    if (mapBrowserEvent.type == MapBrowserEventType.DBLCLICK) {\n      const browserEvent = /** @type {MouseEvent} */ (\n        mapBrowserEvent.originalEvent\n      );\n      const map = mapBrowserEvent.map;\n      const anchor = mapBrowserEvent.coordinate;\n      const delta = browserEvent.shiftKey ? -this.delta_ : this.delta_;\n      const view = map.getView();\n      zoomByDelta(view, delta, anchor, this.duration_);\n      browserEvent.preventDefault();\n      stopEvent = true;\n    }\n    return !stopEvent;\n  }\n}\n\nexport default DoubleClickZoom;\n", "/**\n * @module ol/interaction/DragPan\n */\nimport PointerInteraction, {\n  centroid as centroidFromPointers,\n} from './Pointer.js';\nimport {FALSE} from '../functions.js';\nimport {\n  all,\n  focusWithTabindex,\n  noModifierKeys,\n  primaryAction,\n} from '../events/condition.js';\nimport {easeOut} from '../easing.js';\nimport {\n  rotate as rotateCoordinate,\n  scale as scaleCoordinate,\n} from '../coordinate.js';\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../events/condition.js\").Condition} [condition] A function that takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a boolean\n * to indicate whether that event should be handled.\n * Default is {@link module:ol/events/condition.noModifierKeys} and {@link module:ol/events/condition.primaryAction}.\n * @property {boolean} [onFocusOnly=false] When the map's target has a `tabindex` attribute set,\n * the interaction will only handle events when the map has the focus.\n * @property {import(\"../Kinetic.js\").default} [kinetic] Kinetic inertia to apply to the pan.\n */\n\n/**\n * @classdesc\n * Allows the user to pan the map by dragging the map.\n * @api\n */\nclass DragPan extends PointerInteraction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    super({\n      stopDown: FALSE,\n    });\n\n    options = options ? options : {};\n\n    /**\n     * @private\n     * @type {import(\"../Kinetic.js\").default|undefined}\n     */\n    this.kinetic_ = options.kinetic;\n\n    /**\n     * @type {import(\"../pixel.js\").Pixel}\n     */\n    this.lastCentroid = null;\n\n    /**\n     * @type {number}\n     */\n    this.lastPointersCount_;\n\n    /**\n     * @type {boolean}\n     */\n    this.panning_ = false;\n\n    const condition = options.condition\n      ? options.condition\n      : all(noModifierKeys, primaryAction);\n\n    /**\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.condition_ = options.onFocusOnly\n      ? all(focusWithTabindex, condition)\n      : condition;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.noKinetic_ = false;\n  }\n\n  /**\n   * Handle pointer drag events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   */\n  handleDragEvent(mapBrowserEvent) {\n    const map = mapBrowserEvent.map;\n    if (!this.panning_) {\n      this.panning_ = true;\n      map.getView().beginInteraction();\n    }\n    const targetPointers = this.targetPointers;\n    const centroid = map.getEventPixel(centroidFromPointers(targetPointers));\n    if (targetPointers.length == this.lastPointersCount_) {\n      if (this.kinetic_) {\n        this.kinetic_.update(centroid[0], centroid[1]);\n      }\n      if (this.lastCentroid) {\n        const delta = [\n          this.lastCentroid[0] - centroid[0],\n          centroid[1] - this.lastCentroid[1],\n        ];\n        const map = mapBrowserEvent.map;\n        const view = map.getView();\n        scaleCoordinate(delta, view.getResolution());\n        rotateCoordinate(delta, view.getRotation());\n        view.adjustCenterInternal(delta);\n      }\n    } else if (this.kinetic_) {\n      // reset so we don't overestimate the kinetic energy after\n      // after one finger down, tiny drag, second finger down\n      this.kinetic_.begin();\n    }\n    this.lastCentroid = centroid;\n    this.lastPointersCount_ = targetPointers.length;\n    mapBrowserEvent.originalEvent.preventDefault();\n  }\n\n  /**\n   * Handle pointer up events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleUpEvent(mapBrowserEvent) {\n    const map = mapBrowserEvent.map;\n    const view = map.getView();\n    if (this.targetPointers.length === 0) {\n      if (!this.noKinetic_ && this.kinetic_ && this.kinetic_.end()) {\n        const distance = this.kinetic_.getDistance();\n        const angle = this.kinetic_.getAngle();\n        const center = view.getCenterInternal();\n        const centerpx = map.getPixelFromCoordinateInternal(center);\n        const dest = map.getCoordinateFromPixelInternal([\n          centerpx[0] - distance * Math.cos(angle),\n          centerpx[1] - distance * Math.sin(angle),\n        ]);\n        view.animateInternal({\n          center: view.getConstrainedCenter(dest),\n          duration: 500,\n          easing: easeOut,\n        });\n      }\n      if (this.panning_) {\n        this.panning_ = false;\n        view.endInteraction();\n      }\n      return false;\n    }\n    if (this.kinetic_) {\n      // reset so we don't overestimate the kinetic energy after\n      // after one finger up, tiny drag, second finger up\n      this.kinetic_.begin();\n    }\n    this.lastCentroid = null;\n    return true;\n  }\n\n  /**\n   * Handle pointer down events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleDownEvent(mapBrowserEvent) {\n    if (this.targetPointers.length > 0 && this.condition_(mapBrowserEvent)) {\n      const map = mapBrowserEvent.map;\n      const view = map.getView();\n      this.lastCentroid = null;\n      // stop any current animation\n      if (view.getAnimating()) {\n        view.cancelAnimations();\n      }\n      if (this.kinetic_) {\n        this.kinetic_.begin();\n      }\n      // No kinetic as soon as more than one pointer on the screen is\n      // detected. This is to prevent nasty pans after pinch.\n      this.noKinetic_ = this.targetPointers.length > 1;\n      return true;\n    }\n    return false;\n  }\n}\n\nexport default DragPan;\n", "/**\n * @module ol/interaction/DragRotate\n */\nimport PointerInteraction from './Pointer.js';\nimport {FALSE} from '../functions.js';\nimport {\n  altShiftKeysOnly,\n  mouseActionButton,\n  mouseOnly,\n} from '../events/condition.js';\nimport {disable} from '../rotationconstraint.js';\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../events/condition.js\").Condition} [condition] A function that takes an\n * {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a boolean\n * to indicate whether that event should be handled.\n * Default is {@link module:ol/events/condition.altShiftKeysOnly}.\n * @property {number} [duration=250] Animation duration in milliseconds.\n */\n\n/**\n * @classdesc\n * Allows the user to rotate the map by clicking and dragging on the map,\n * normally combined with an {@link module:ol/events/condition} that limits\n * it to when the alt and shift keys are held down.\n *\n * This interaction is only supported for mouse devices.\n * @api\n */\nclass DragRotate extends PointerInteraction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    super({\n      stopDown: FALSE,\n    });\n\n    /**\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.condition_ = options.condition ? options.condition : altShiftKeysOnly;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.lastAngle_ = undefined;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.duration_ = options.duration !== undefined ? options.duration : 250;\n  }\n\n  /**\n   * Handle pointer drag events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   */\n  handleDragEvent(mapBrowserEvent) {\n    if (!mouseOnly(mapBrowserEvent)) {\n      return;\n    }\n\n    const map = mapBrowserEvent.map;\n    const view = map.getView();\n    if (view.getConstraints().rotation === disable) {\n      return;\n    }\n    const size = map.getSize();\n    const offset = mapBrowserEvent.pixel;\n    const theta = Math.atan2(size[1] / 2 - offset[1], offset[0] - size[0] / 2);\n    if (this.lastAngle_ !== undefined) {\n      const delta = theta - this.lastAngle_;\n      view.adjustRotationInternal(-delta);\n    }\n    this.lastAngle_ = theta;\n  }\n\n  /**\n   * Handle pointer up events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleUpEvent(mapBrowserEvent) {\n    if (!mouseOnly(mapBrowserEvent)) {\n      return true;\n    }\n\n    const map = mapBrowserEvent.map;\n    const view = map.getView();\n    view.endInteraction(this.duration_);\n    return false;\n  }\n\n  /**\n   * Handle pointer down events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleDownEvent(mapBrowserEvent) {\n    if (!mouseOnly(mapBrowserEvent)) {\n      return false;\n    }\n\n    if (\n      mouseActionButton(mapBrowserEvent) &&\n      this.condition_(mapBrowserEvent)\n    ) {\n      const map = mapBrowserEvent.map;\n      map.getView().beginInteraction();\n      this.lastAngle_ = undefined;\n      return true;\n    }\n    return false;\n  }\n}\n\nexport default DragRotate;\n", "/**\n * @module ol/render/Box\n */\n\nimport Disposable from '../Disposable.js';\nimport Polygon from '../geom/Polygon.js';\n\nclass RenderBox extends Disposable {\n  /**\n   * @param {string} className CSS class name.\n   */\n  constructor(className) {\n    super();\n\n    /**\n     * @type {import(\"../geom/Polygon.js\").default}\n     * @private\n     */\n    this.geometry_ = null;\n\n    /**\n     * @type {HTMLDivElement}\n     * @private\n     */\n    this.element_ = document.createElement('div');\n    this.element_.style.position = 'absolute';\n    this.element_.style.pointerEvents = 'auto';\n    this.element_.className = 'ol-box ' + className;\n\n    /**\n     * @private\n     * @type {import(\"../Map.js\").default|null}\n     */\n    this.map_ = null;\n\n    /**\n     * @private\n     * @type {import(\"../pixel.js\").Pixel}\n     */\n    this.startPixel_ = null;\n\n    /**\n     * @private\n     * @type {import(\"../pixel.js\").Pixel}\n     */\n    this.endPixel_ = null;\n  }\n\n  /**\n   * Clean up.\n   */\n  disposeInternal() {\n    this.setMap(null);\n  }\n\n  /**\n   * @private\n   */\n  render_() {\n    const startPixel = this.startPixel_;\n    const endPixel = this.endPixel_;\n    const px = 'px';\n    const style = this.element_.style;\n    style.left = Math.min(startPixel[0], endPixel[0]) + px;\n    style.top = Math.min(startPixel[1], endPixel[1]) + px;\n    style.width = Math.abs(endPixel[0] - startPixel[0]) + px;\n    style.height = Math.abs(endPixel[1] - startPixel[1]) + px;\n  }\n\n  /**\n   * @param {import(\"../Map.js\").default|null} map Map.\n   */\n  setMap(map) {\n    if (this.map_) {\n      this.map_.getOverlayContainer().removeChild(this.element_);\n      const style = this.element_.style;\n      style.left = 'inherit';\n      style.top = 'inherit';\n      style.width = 'inherit';\n      style.height = 'inherit';\n    }\n    this.map_ = map;\n    if (this.map_) {\n      this.map_.getOverlayContainer().appendChild(this.element_);\n    }\n  }\n\n  /**\n   * @param {import(\"../pixel.js\").Pixel} startPixel Start pixel.\n   * @param {import(\"../pixel.js\").Pixel} endPixel End pixel.\n   */\n  setPixels(startPixel, endPixel) {\n    this.startPixel_ = startPixel;\n    this.endPixel_ = endPixel;\n    this.createOrUpdateGeometry();\n    this.render_();\n  }\n\n  /**\n   * Creates or updates the cached geometry.\n   */\n  createOrUpdateGeometry() {\n    const startPixel = this.startPixel_;\n    const endPixel = this.endPixel_;\n    const pixels = [\n      startPixel,\n      [startPixel[0], endPixel[1]],\n      endPixel,\n      [endPixel[0], startPixel[1]],\n    ];\n    const coordinates = pixels.map(\n      this.map_.getCoordinateFromPixelInternal,\n      this.map_\n    );\n    // close the polygon\n    coordinates[4] = coordinates[0].slice();\n    if (!this.geometry_) {\n      this.geometry_ = new Polygon([coordinates]);\n    } else {\n      this.geometry_.setCoordinates([coordinates]);\n    }\n  }\n\n  /**\n   * @return {import(\"../geom/Polygon.js\").default} Geometry.\n   */\n  getGeometry() {\n    return this.geometry_;\n  }\n}\n\nexport default RenderBox;\n", "/**\n * @module ol/interaction/DragBox\n */\n// FIXME draw drag box\nimport Event from '../events/Event.js';\nimport PointerInteraction from './Pointer.js';\nimport RenderBox from '../render/Box.js';\nimport {mouseActionButton} from '../events/condition.js';\n\n/**\n * A function that takes a {@link module:ol/MapBrowserEvent~MapBrowserEvent} and two\n * {@link module:ol/pixel~Pixel}s and returns a `{boolean}`. If the condition is met,\n * true should be returned.\n * @typedef {function(this: ?, import(\"../MapBrowserEvent.js\").default, import(\"../pixel.js\").Pixel, import(\"../pixel.js\").Pixel):boolean} EndCondition\n */\n\n/**\n * @typedef {Object} Options\n * @property {string} [className='ol-dragbox'] CSS class name for styling the box.\n * @property {import(\"../events/condition.js\").Condition} [condition] A function that takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a boolean\n * to indicate whether that event should be handled.\n * Default is {@link ol/events/condition~mouseActionButton}.\n * @property {number} [minArea=64] The minimum area of the box in pixel, this value is used by the default\n * `boxEndCondition` function.\n * @property {EndCondition} [boxEndCondition] A function that takes a {@link module:ol/MapBrowserEvent~MapBrowserEvent} and two\n * {@link module:ol/pixel~Pixel}s to indicate whether a `boxend` event should be fired.\n * Default is `true` if the area of the box is bigger than the `minArea` option.\n * @property {function(this:DragBox, import(\"../MapBrowserEvent.js\").default):void} [onBoxEnd] Code to execute just\n * before `boxend` is fired.\n */\n\n/**\n * @enum {string}\n */\nconst DragBoxEventType = {\n  /**\n   * Triggered upon drag box start.\n   * @event DragBoxEvent#boxstart\n   * @api\n   */\n  BOXSTART: 'boxstart',\n\n  /**\n   * Triggered on drag when box is active.\n   * @event DragBoxEvent#boxdrag\n   * @api\n   */\n  BOXDRAG: 'boxdrag',\n\n  /**\n   * Triggered upon drag box end.\n   * @event DragBoxEvent#boxend\n   * @api\n   */\n  BOXEND: 'boxend',\n\n  /**\n   * Triggered upon drag box canceled.\n   * @event DragBoxEvent#boxcancel\n   * @api\n   */\n  BOXCANCEL: 'boxcancel',\n};\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/interaction/DragBox~DragBox} instances are instances of\n * this type.\n */\nexport class DragBoxEvent extends Event {\n  /**\n   * @param {string} type The event type.\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate The event coordinate.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Originating event.\n   */\n  constructor(type, coordinate, mapBrowserEvent) {\n    super(type);\n\n    /**\n     * The coordinate of the drag event.\n     * @const\n     * @type {import(\"../coordinate.js\").Coordinate}\n     * @api\n     */\n    this.coordinate = coordinate;\n\n    /**\n     * @const\n     * @type {import(\"../MapBrowserEvent.js\").default}\n     * @api\n     */\n    this.mapBrowserEvent = mapBrowserEvent;\n  }\n}\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types|\n *     'change:active', import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<'boxcancel'|'boxdrag'|'boxend'|'boxstart', DragBoxEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types|\n *     'change:active'|'boxcancel'|'boxdrag'|'boxend', Return>} DragBoxOnSignature\n */\n\n/**\n * @classdesc\n * Allows the user to draw a vector box by clicking and dragging on the map,\n * normally combined with an {@link module:ol/events/condition} that limits\n * it to when the shift or other key is held down. This is used, for example,\n * for zooming to a specific area of the map\n * (see {@link module:ol/interaction/DragZoom~DragZoom} and\n * {@link module:ol/interaction/DragRotateAndZoom~DragRotateAndZoom}).\n *\n * @fires DragBoxEvent\n * @api\n */\nclass DragBox extends PointerInteraction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    super();\n\n    /***\n     * @type {DragBoxOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {DragBoxOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {DragBoxOnSignature<void>}\n     */\n    this.un;\n\n    options = options ? options : {};\n\n    /**\n     * @type {import(\"../render/Box.js\").default}\n     * @private\n     */\n    this.box_ = new RenderBox(options.className || 'ol-dragbox');\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.minArea_ = options.minArea !== undefined ? options.minArea : 64;\n\n    if (options.onBoxEnd) {\n      this.onBoxEnd = options.onBoxEnd;\n    }\n\n    /**\n     * @type {import(\"../pixel.js\").Pixel}\n     * @private\n     */\n    this.startPixel_ = null;\n\n    /**\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.condition_ = options.condition ? options.condition : mouseActionButton;\n\n    /**\n     * @private\n     * @type {EndCondition}\n     */\n    this.boxEndCondition_ = options.boxEndCondition\n      ? options.boxEndCondition\n      : this.defaultBoxEndCondition;\n  }\n\n  /**\n   * The default condition for determining whether the boxend event\n   * should fire.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent The originating MapBrowserEvent\n   *     leading to the box end.\n   * @param {import(\"../pixel.js\").Pixel} startPixel The starting pixel of the box.\n   * @param {import(\"../pixel.js\").Pixel} endPixel The end pixel of the box.\n   * @return {boolean} Whether or not the boxend condition should be fired.\n   */\n  defaultBoxEndCondition(mapBrowserEvent, startPixel, endPixel) {\n    const width = endPixel[0] - startPixel[0];\n    const height = endPixel[1] - startPixel[1];\n    return width * width + height * height >= this.minArea_;\n  }\n\n  /**\n   * Returns geometry of last drawn box.\n   * @return {import(\"../geom/Polygon.js\").default} Geometry.\n   * @api\n   */\n  getGeometry() {\n    return this.box_.getGeometry();\n  }\n\n  /**\n   * Handle pointer drag events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   */\n  handleDragEvent(mapBrowserEvent) {\n    this.box_.setPixels(this.startPixel_, mapBrowserEvent.pixel);\n\n    this.dispatchEvent(\n      new DragBoxEvent(\n        DragBoxEventType.BOXDRAG,\n        mapBrowserEvent.coordinate,\n        mapBrowserEvent\n      )\n    );\n  }\n\n  /**\n   * Handle pointer up events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleUpEvent(mapBrowserEvent) {\n    this.box_.setMap(null);\n\n    const completeBox = this.boxEndCondition_(\n      mapBrowserEvent,\n      this.startPixel_,\n      mapBrowserEvent.pixel\n    );\n    if (completeBox) {\n      this.onBoxEnd(mapBrowserEvent);\n    }\n    this.dispatchEvent(\n      new DragBoxEvent(\n        completeBox ? DragBoxEventType.BOXEND : DragBoxEventType.BOXCANCEL,\n        mapBrowserEvent.coordinate,\n        mapBrowserEvent\n      )\n    );\n    return false;\n  }\n\n  /**\n   * Handle pointer down events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleDownEvent(mapBrowserEvent) {\n    if (this.condition_(mapBrowserEvent)) {\n      this.startPixel_ = mapBrowserEvent.pixel;\n      this.box_.setMap(mapBrowserEvent.map);\n      this.box_.setPixels(this.startPixel_, this.startPixel_);\n      this.dispatchEvent(\n        new DragBoxEvent(\n          DragBoxEventType.BOXSTART,\n          mapBrowserEvent.coordinate,\n          mapBrowserEvent\n        )\n      );\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Function to execute just before `onboxend` is fired\n   * @param {import(\"../MapBrowserEvent.js\").default} event Event.\n   */\n  onBoxEnd(event) {}\n}\n\nexport default DragBox;\n", "/**\n * @module ol/interaction/DragZoom\n */\nimport Drag<PERSON><PERSON> from './DragBox.js';\nimport {easeOut} from '../easing.js';\nimport {shiftKeyOnly} from '../events/condition.js';\n\n/**\n * @typedef {Object} Options\n * @property {string} [className='ol-dragzoom'] CSS class name for styling the\n * box.\n * @property {import(\"../events/condition.js\").Condition} [condition] A function that\n * takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * boolean to indicate whether that event should be handled.\n * Default is {@link module:ol/events/condition.shiftKeyOnly}.\n * @property {number} [duration=200] Animation duration in milliseconds.\n * @property {boolean} [out=false] Use interaction for zooming out.\n * @property {number} [minArea=64] The minimum area of the box in pixel, this value is used by the parent default\n * `boxEndCondition` function.\n */\n\n/**\n * @classdesc\n * Allows the user to zoom the map by clicking and dragging on the map,\n * normally combined with an {@link module:ol/events/condition} that limits\n * it to when a key, shift by default, is held down.\n *\n * To change the style of the box, use CSS and the `.ol-dragzoom` selector, or\n * your custom one configured with `className`.\n * @api\n */\nclass DragZoom extends DragBox {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    const condition = options.condition ? options.condition : shiftKeyOnly;\n\n    super({\n      condition: condition,\n      className: options.className || 'ol-dragzoom',\n      minArea: options.minArea,\n    });\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.duration_ = options.duration !== undefined ? options.duration : 200;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.out_ = options.out !== undefined ? options.out : false;\n  }\n\n  /**\n   * Function to execute just before `onboxend` is fired\n   * @param {import(\"../MapBrowserEvent.js\").default} event Event.\n   */\n  onBoxEnd(event) {\n    const map = this.getMap();\n    const view = /** @type {!import(\"../View.js\").default} */ (map.getView());\n    let geometry = this.getGeometry();\n\n    if (this.out_) {\n      const rotatedExtent = view.rotatedExtentForGeometry(geometry);\n      const resolution = view.getResolutionForExtentInternal(rotatedExtent);\n      const factor = view.getResolution() / resolution;\n      geometry = geometry.clone();\n      geometry.scale(factor * factor);\n    }\n\n    view.fitInternal(geometry, {\n      duration: this.duration_,\n      easing: easeOut,\n    });\n  }\n}\n\nexport default DragZoom;\n", "/**\n * @module ol/events/Key\n */\n\n/**\n * @enum {string}\n * @const\n */\nexport default {\n  LEFT: 'ArrowLeft',\n  UP: 'ArrowUp',\n  RIGHT: 'ArrowRight',\n  DOWN: 'ArrowDown',\n};\n", "/**\n * @module ol/interaction/KeyboardPan\n */\nimport EventType from '../events/EventType.js';\nimport Interaction, {pan} from './Interaction.js';\nimport Key from '../events/Key.js';\nimport {noModifierKeys, targetNotEditable} from '../events/condition.js';\nimport {rotate as rotateCoordinate} from '../coordinate.js';\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../events/condition.js\").Condition} [condition] A function that\n * takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * boolean to indicate whether that event should be handled. Default is\n * {@link module:ol/events/condition.noModifierKeys} and\n * {@link module:ol/events/condition.targetNotEditable}.\n * @property {number} [duration=100] Animation duration in milliseconds.\n * @property {number} [pixelDelta=128] The amount of pixels to pan on each key\n * press.\n */\n\n/**\n * @classdesc\n * Allows the user to pan the map using keyboard arrows.\n * Note that, although this interaction is by default included in maps,\n * the keys can only be used when browser focus is on the element to which\n * the keyboard events are attached. By default, this is the map div,\n * though you can change this with the `keyboardEventTarget` in\n * {@link module:ol/Map~Map}. `document` never loses focus but, for any other\n * element, focus will have to be on, and returned to, this element if the keys\n * are to function.\n * See also {@link module:ol/interaction/KeyboardZoom~KeyboardZoom}.\n * @api\n */\nclass KeyboardPan extends Interaction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    super();\n\n    options = options || {};\n\n    /**\n     * @private\n     * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Browser event.\n     * @return {boolean} Combined condition result.\n     */\n    this.defaultCondition_ = function (mapBrowserEvent) {\n      return (\n        noModifierKeys(mapBrowserEvent) && targetNotEditable(mapBrowserEvent)\n      );\n    };\n\n    /**\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.condition_ =\n      options.condition !== undefined\n        ? options.condition\n        : this.defaultCondition_;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.duration_ = options.duration !== undefined ? options.duration : 100;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.pixelDelta_ =\n      options.pixelDelta !== undefined ? options.pixelDelta : 128;\n  }\n\n  /**\n   * Handles the {@link module:ol/MapBrowserEvent~MapBrowserEvent map browser event} if it was a\n   * `KeyEvent`, and decides the direction to pan to (if an arrow key was\n   * pressed).\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n   * @return {boolean} `false` to stop event propagation.\n   */\n  handleEvent(mapBrowserEvent) {\n    let stopEvent = false;\n    if (mapBrowserEvent.type == EventType.KEYDOWN) {\n      const keyEvent = /** @type {KeyboardEvent} */ (\n        mapBrowserEvent.originalEvent\n      );\n      const key = keyEvent.key;\n      if (\n        this.condition_(mapBrowserEvent) &&\n        (key == Key.DOWN ||\n          key == Key.LEFT ||\n          key == Key.RIGHT ||\n          key == Key.UP)\n      ) {\n        const map = mapBrowserEvent.map;\n        const view = map.getView();\n        const mapUnitsDelta = view.getResolution() * this.pixelDelta_;\n        let deltaX = 0,\n          deltaY = 0;\n        if (key == Key.DOWN) {\n          deltaY = -mapUnitsDelta;\n        } else if (key == Key.LEFT) {\n          deltaX = -mapUnitsDelta;\n        } else if (key == Key.RIGHT) {\n          deltaX = mapUnitsDelta;\n        } else {\n          deltaY = mapUnitsDelta;\n        }\n        const delta = [deltaX, deltaY];\n        rotateCoordinate(delta, view.getRotation());\n        pan(view, delta, this.duration_);\n        keyEvent.preventDefault();\n        stopEvent = true;\n      }\n    }\n    return !stopEvent;\n  }\n}\n\nexport default KeyboardPan;\n", "/**\n * @module ol/interaction/KeyboardZoom\n */\nimport EventType from '../events/EventType.js';\nimport Interaction, {zoomByDelta} from './Interaction.js';\nimport {platformModifierKey, targetNotEditable} from '../events/condition.js';\n\n/**\n * @typedef {Object} Options\n * @property {number} [duration=100] Animation duration in milliseconds.\n * @property {import(\"../events/condition.js\").Condition} [condition] A function that\n * takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * boolean to indicate whether that event should be handled. The default condition is\n * that {@link module:ol/events/condition.targetNotEditable} is fulfilled and that\n * the platform modifier key isn't pressed\n * (!{@link module:ol/events/condition.platformModifierKey}).\n * @property {number} [delta=1] The zoom level delta on each key press.\n */\n\n/**\n * @classdesc\n * Allows the user to zoom the map using keyboard + and -.\n * Note that, although this interaction is by default included in maps,\n * the keys can only be used when browser focus is on the element to which\n * the keyboard events are attached. By default, this is the map div,\n * though you can change this with the `keyboardEventTarget` in\n * {@link module:ol/Map~Map}. `document` never loses focus but, for any other\n * element, focus will have to be on, and returned to, this element if the keys\n * are to function.\n * See also {@link module:ol/interaction/KeyboardPan~KeyboardPan}.\n * @api\n */\nclass KeyboardZoom extends Interaction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    super();\n\n    options = options ? options : {};\n\n    /**\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.condition_ = options.condition\n      ? options.condition\n      : function (mapBrowserEvent) {\n          return (\n            !platformModifierKey(mapBrowserEvent) &&\n            targetNotEditable(mapBrowserEvent)\n          );\n        };\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.delta_ = options.delta ? options.delta : 1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.duration_ = options.duration !== undefined ? options.duration : 100;\n  }\n\n  /**\n   * Handles the {@link module:ol/MapBrowserEvent~MapBrowserEvent map browser event} if it was a\n   * `KeyEvent`, and decides whether to zoom in or out (depending on whether the\n   * key pressed was '+' or '-').\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n   * @return {boolean} `false` to stop event propagation.\n   */\n  handleEvent(mapBrowserEvent) {\n    let stopEvent = false;\n    if (\n      mapBrowserEvent.type == EventType.KEYDOWN ||\n      mapBrowserEvent.type == EventType.KEYPRESS\n    ) {\n      const keyEvent = /** @type {KeyboardEvent} */ (\n        mapBrowserEvent.originalEvent\n      );\n      const key = keyEvent.key;\n      if (this.condition_(mapBrowserEvent) && (key === '+' || key === '-')) {\n        const map = mapBrowserEvent.map;\n        const delta = key === '+' ? this.delta_ : -this.delta_;\n        const view = map.getView();\n        zoomByDelta(view, delta, undefined, this.duration_);\n        keyEvent.preventDefault();\n        stopEvent = true;\n      }\n    }\n    return !stopEvent;\n  }\n}\n\nexport default KeyboardZoom;\n", "/**\n * @module ol/interaction/MouseWheelZoom\n */\nimport EventType from '../events/EventType.js';\nimport Interaction, {zoomByDelta} from './Interaction.js';\nimport {DEVICE_PIXEL_RATIO, FIREFOX} from '../has.js';\nimport {all, always, focusWithTabindex} from '../events/condition.js';\nimport {clamp} from '../math.js';\n\n/**\n * @typedef {'trackpad' | 'wheel'} Mode\n */\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../events/condition.js\").Condition} [condition] A function that\n * takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * boolean to indicate whether that event should be handled. Default is\n * {@link module:ol/events/condition.always}.\n * @property {boolean} [onFocusOnly=false] When the map's target has a `tabindex` attribute set,\n * the interaction will only handle events when the map has the focus.\n * @property {number} [maxDelta=1] Maximum mouse wheel delta.\n * @property {number} [duration=250] Animation duration in milliseconds.\n * @property {number} [timeout=80] Mouse wheel timeout duration in milliseconds.\n * @property {boolean} [useAnchor=true] Enable zooming using the mouse's\n * location as the anchor. When set to `false`, zooming in and out will zoom to\n * the center of the screen instead of zooming on the mouse's location.\n * @property {boolean} [constrainResolution=false] If true, the mouse wheel zoom\n * event will always animate to the closest zoom level after an interaction;\n * false means intermediary zoom levels are allowed.\n */\n\n/**\n * @classdesc\n * Allows the user to zoom the map by scrolling the mouse wheel.\n * @api\n */\nclass MouseWheelZoom extends Interaction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    super(\n      /** @type {import(\"./Interaction.js\").InteractionOptions} */ (options)\n    );\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.totalDelta_ = 0;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.lastDelta_ = 0;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDelta_ = options.maxDelta !== undefined ? options.maxDelta : 1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.duration_ = options.duration !== undefined ? options.duration : 250;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.timeout_ = options.timeout !== undefined ? options.timeout : 80;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.useAnchor_ =\n      options.useAnchor !== undefined ? options.useAnchor : true;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.constrainResolution_ =\n      options.constrainResolution !== undefined\n        ? options.constrainResolution\n        : false;\n\n    const condition = options.condition ? options.condition : always;\n\n    /**\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.condition_ = options.onFocusOnly\n      ? all(focusWithTabindex, condition)\n      : condition;\n\n    /**\n     * @private\n     * @type {?import(\"../coordinate.js\").Coordinate}\n     */\n    this.lastAnchor_ = null;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.startTime_ = undefined;\n\n    /**\n     * @private\n     * @type {ReturnType<typeof setTimeout>}\n     */\n    this.timeoutId_;\n\n    /**\n     * @private\n     * @type {Mode|undefined}\n     */\n    this.mode_ = undefined;\n\n    /**\n     * Trackpad events separated by this delay will be considered separate\n     * interactions.\n     * @private\n     * @type {number}\n     */\n    this.trackpadEventGap_ = 400;\n\n    /**\n     * @private\n     * @type {ReturnType<typeof setTimeout>}\n     */\n    this.trackpadTimeoutId_;\n\n    /**\n     * The number of delta values per zoom level\n     * @private\n     * @type {number}\n     */\n    this.deltaPerZoom_ = 300;\n  }\n\n  /**\n   * @private\n   */\n  endInteraction_() {\n    this.trackpadTimeoutId_ = undefined;\n    const map = this.getMap();\n    if (!map) {\n      return;\n    }\n    const view = map.getView();\n    view.endInteraction(\n      undefined,\n      this.lastDelta_ ? (this.lastDelta_ > 0 ? 1 : -1) : 0,\n      this.lastAnchor_\n    );\n  }\n\n  /**\n   * Handles the {@link module:ol/MapBrowserEvent~MapBrowserEvent map browser event} (if it was a mousewheel-event) and eventually\n   * zooms the map.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n   * @return {boolean} `false` to stop event propagation.\n   */\n  handleEvent(mapBrowserEvent) {\n    if (!this.condition_(mapBrowserEvent)) {\n      return true;\n    }\n    const type = mapBrowserEvent.type;\n    if (type !== EventType.WHEEL) {\n      return true;\n    }\n\n    const map = mapBrowserEvent.map;\n    const wheelEvent = /** @type {WheelEvent} */ (\n      mapBrowserEvent.originalEvent\n    );\n    wheelEvent.preventDefault();\n\n    if (this.useAnchor_) {\n      this.lastAnchor_ = mapBrowserEvent.coordinate;\n    }\n\n    // Delta normalisation inspired by\n    // https://github.com/mapbox/mapbox-gl-js/blob/001c7b9/js/ui/handler/scroll_zoom.js\n    let delta;\n    if (mapBrowserEvent.type == EventType.WHEEL) {\n      delta = wheelEvent.deltaY;\n      if (FIREFOX && wheelEvent.deltaMode === WheelEvent.DOM_DELTA_PIXEL) {\n        delta /= DEVICE_PIXEL_RATIO;\n      }\n      if (wheelEvent.deltaMode === WheelEvent.DOM_DELTA_LINE) {\n        delta *= 40;\n      }\n    }\n\n    if (delta === 0) {\n      return false;\n    }\n    this.lastDelta_ = delta;\n\n    const now = Date.now();\n\n    if (this.startTime_ === undefined) {\n      this.startTime_ = now;\n    }\n\n    if (!this.mode_ || now - this.startTime_ > this.trackpadEventGap_) {\n      this.mode_ = Math.abs(delta) < 4 ? 'trackpad' : 'wheel';\n    }\n\n    const view = map.getView();\n    if (\n      this.mode_ === 'trackpad' &&\n      !(view.getConstrainResolution() || this.constrainResolution_)\n    ) {\n      if (this.trackpadTimeoutId_) {\n        clearTimeout(this.trackpadTimeoutId_);\n      } else {\n        if (view.getAnimating()) {\n          view.cancelAnimations();\n        }\n        view.beginInteraction();\n      }\n      this.trackpadTimeoutId_ = setTimeout(\n        this.endInteraction_.bind(this),\n        this.timeout_\n      );\n      view.adjustZoom(-delta / this.deltaPerZoom_, this.lastAnchor_);\n      this.startTime_ = now;\n      return false;\n    }\n\n    this.totalDelta_ += delta;\n\n    const timeLeft = Math.max(this.timeout_ - (now - this.startTime_), 0);\n\n    clearTimeout(this.timeoutId_);\n    this.timeoutId_ = setTimeout(\n      this.handleWheelZoom_.bind(this, map),\n      timeLeft\n    );\n\n    return false;\n  }\n\n  /**\n   * @private\n   * @param {import(\"../Map.js\").default} map Map.\n   */\n  handleWheelZoom_(map) {\n    const view = map.getView();\n    if (view.getAnimating()) {\n      view.cancelAnimations();\n    }\n    let delta =\n      -clamp(\n        this.totalDelta_,\n        -this.maxDelta_ * this.deltaPerZoom_,\n        this.maxDelta_ * this.deltaPerZoom_\n      ) / this.deltaPerZoom_;\n    if (view.getConstrainResolution() || this.constrainResolution_) {\n      // view has a zoom constraint, zoom by 1\n      delta = delta ? (delta > 0 ? 1 : -1) : 0;\n    }\n    zoomByDelta(view, delta, this.lastAnchor_, this.duration_);\n\n    this.mode_ = undefined;\n    this.totalDelta_ = 0;\n    this.lastAnchor_ = null;\n    this.startTime_ = undefined;\n    this.timeoutId_ = undefined;\n  }\n\n  /**\n   * Enable or disable using the mouse's location as an anchor when zooming\n   * @param {boolean} useAnchor true to zoom to the mouse's location, false\n   * to zoom to the center of the map\n   * @api\n   */\n  setMouseAnchor(useAnchor) {\n    this.useAnchor_ = useAnchor;\n    if (!useAnchor) {\n      this.lastAnchor_ = null;\n    }\n  }\n}\n\nexport default MouseWheelZoom;\n", "/**\n * @module ol/interaction/PinchRotate\n */\nimport PointerInteraction, {\n  centroid as centroidFromPointers,\n} from './Pointer.js';\nimport {FALSE} from '../functions.js';\nimport {disable} from '../rotationconstraint.js';\n\n/**\n * @typedef {Object} Options\n * @property {number} [duration=250] The duration of the animation in\n * milliseconds.\n * @property {number} [threshold=0.3] Minimal angle in radians to start a rotation.\n */\n\n/**\n * @classdesc\n * Allows the user to rotate the map by twisting with two fingers\n * on a touch screen.\n * @api\n */\nclass PinchRotate extends PointerInteraction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    const pointerOptions = /** @type {import(\"./Pointer.js\").Options} */ (\n      options\n    );\n\n    if (!pointerOptions.stopDown) {\n      pointerOptions.stopDown = FALSE;\n    }\n\n    super(pointerOptions);\n\n    /**\n     * @private\n     * @type {import(\"../coordinate.js\").Coordinate}\n     */\n    this.anchor_ = null;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.lastAngle_ = undefined;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.rotating_ = false;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.rotationDelta_ = 0.0;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.threshold_ = options.threshold !== undefined ? options.threshold : 0.3;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.duration_ = options.duration !== undefined ? options.duration : 250;\n  }\n\n  /**\n   * Handle pointer drag events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   */\n  handleDragEvent(mapBrowserEvent) {\n    let rotationDelta = 0.0;\n\n    const touch0 = this.targetPointers[0];\n    const touch1 = this.targetPointers[1];\n\n    // angle between touches\n    const angle = Math.atan2(\n      touch1.clientY - touch0.clientY,\n      touch1.clientX - touch0.clientX\n    );\n\n    if (this.lastAngle_ !== undefined) {\n      const delta = angle - this.lastAngle_;\n      this.rotationDelta_ += delta;\n      if (!this.rotating_ && Math.abs(this.rotationDelta_) > this.threshold_) {\n        this.rotating_ = true;\n      }\n      rotationDelta = delta;\n    }\n    this.lastAngle_ = angle;\n\n    const map = mapBrowserEvent.map;\n    const view = map.getView();\n    if (view.getConstraints().rotation === disable) {\n      return;\n    }\n\n    // rotate anchor point.\n    // FIXME: should be the intersection point between the lines:\n    //     touch0,touch1 and previousTouch0,previousTouch1\n    this.anchor_ = map.getCoordinateFromPixelInternal(\n      map.getEventPixel(centroidFromPointers(this.targetPointers))\n    );\n\n    // rotate\n    if (this.rotating_) {\n      map.render();\n      view.adjustRotationInternal(rotationDelta, this.anchor_);\n    }\n  }\n\n  /**\n   * Handle pointer up events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleUpEvent(mapBrowserEvent) {\n    if (this.targetPointers.length < 2) {\n      const map = mapBrowserEvent.map;\n      const view = map.getView();\n      view.endInteraction(this.duration_);\n      return false;\n    }\n    return true;\n  }\n\n  /**\n   * Handle pointer down events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleDownEvent(mapBrowserEvent) {\n    if (this.targetPointers.length >= 2) {\n      const map = mapBrowserEvent.map;\n      this.anchor_ = null;\n      this.lastAngle_ = undefined;\n      this.rotating_ = false;\n      this.rotationDelta_ = 0.0;\n      if (!this.handlingDownUpSequence) {\n        map.getView().beginInteraction();\n      }\n      return true;\n    }\n    return false;\n  }\n}\n\nexport default PinchRotate;\n", "/**\n * @module ol/interaction/PinchZoom\n */\nimport PointerInteraction, {\n  centroid as centroidFromPointers,\n} from './Pointer.js';\nimport {FALSE} from '../functions.js';\n\n/**\n * @typedef {Object} Options\n * @property {number} [duration=400] Animation duration in milliseconds.\n */\n\n/**\n * @classdesc\n * Allows the user to zoom the map by pinching with two fingers\n * on a touch screen.\n * @api\n */\nclass PinchZoom extends PointerInteraction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    const pointerOptions = /** @type {import(\"./Pointer.js\").Options} */ (\n      options\n    );\n\n    if (!pointerOptions.stopDown) {\n      pointerOptions.stopDown = FALSE;\n    }\n\n    super(pointerOptions);\n\n    /**\n     * @private\n     * @type {import(\"../coordinate.js\").Coordinate}\n     */\n    this.anchor_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.duration_ = options.duration !== undefined ? options.duration : 400;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.lastDistance_ = undefined;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.lastScaleDelta_ = 1;\n  }\n\n  /**\n   * Handle pointer drag events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   */\n  handleDragEvent(mapBrowserEvent) {\n    let scaleDelta = 1.0;\n\n    const touch0 = this.targetPointers[0];\n    const touch1 = this.targetPointers[1];\n    const dx = touch0.clientX - touch1.clientX;\n    const dy = touch0.clientY - touch1.clientY;\n\n    // distance between touches\n    const distance = Math.sqrt(dx * dx + dy * dy);\n\n    if (this.lastDistance_ !== undefined) {\n      scaleDelta = this.lastDistance_ / distance;\n    }\n    this.lastDistance_ = distance;\n\n    const map = mapBrowserEvent.map;\n    const view = map.getView();\n\n    if (scaleDelta != 1.0) {\n      this.lastScaleDelta_ = scaleDelta;\n    }\n\n    // scale anchor point.\n    this.anchor_ = map.getCoordinateFromPixelInternal(\n      map.getEventPixel(centroidFromPointers(this.targetPointers))\n    );\n\n    // scale, bypass the resolution constraint\n    map.render();\n    view.adjustResolutionInternal(scaleDelta, this.anchor_);\n  }\n\n  /**\n   * Handle pointer up events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleUpEvent(mapBrowserEvent) {\n    if (this.targetPointers.length < 2) {\n      const map = mapBrowserEvent.map;\n      const view = map.getView();\n      const direction = this.lastScaleDelta_ > 1 ? 1 : -1;\n      view.endInteraction(this.duration_, direction);\n      return false;\n    }\n    return true;\n  }\n\n  /**\n   * Handle pointer down events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleDownEvent(mapBrowserEvent) {\n    if (this.targetPointers.length >= 2) {\n      const map = mapBrowserEvent.map;\n      this.anchor_ = null;\n      this.lastDistance_ = undefined;\n      this.lastScaleDelta_ = 1;\n      if (!this.handlingDownUpSequence) {\n        map.getView().beginInteraction();\n      }\n      return true;\n    }\n    return false;\n  }\n}\n\nexport default PinchZoom;\n", "/**\n * @module ol/Kinetic\n */\n\n/**\n * @classdesc\n * Implementation of inertial deceleration for map movement.\n *\n * @api\n */\nclass Kinetic {\n  /**\n   * @param {number} decay Rate of decay (must be negative).\n   * @param {number} minVelocity Minimum velocity (pixels/millisecond).\n   * @param {number} delay Delay to consider to calculate the kinetic\n   *     initial values (milliseconds).\n   */\n  constructor(decay, minVelocity, delay) {\n    /**\n     * @private\n     * @type {number}\n     */\n    this.decay_ = decay;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.minVelocity_ = minVelocity;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.delay_ = delay;\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.points_ = [];\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.angle_ = 0;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.initialVelocity_ = 0;\n  }\n\n  /**\n   * FIXME empty description for jsdoc\n   */\n  begin() {\n    this.points_.length = 0;\n    this.angle_ = 0;\n    this.initialVelocity_ = 0;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   */\n  update(x, y) {\n    this.points_.push(x, y, Date.now());\n  }\n\n  /**\n   * @return {boolean} Whether we should do kinetic animation.\n   */\n  end() {\n    if (this.points_.length < 6) {\n      // at least 2 points are required (i.e. there must be at least 6 elements\n      // in the array)\n      return false;\n    }\n    const delay = Date.now() - this.delay_;\n    const lastIndex = this.points_.length - 3;\n    if (this.points_[lastIndex + 2] < delay) {\n      // the last tracked point is too old, which means that the user stopped\n      // panning before releasing the map\n      return false;\n    }\n\n    // get the first point which still falls into the delay time\n    let firstIndex = lastIndex - 3;\n    while (firstIndex > 0 && this.points_[firstIndex + 2] > delay) {\n      firstIndex -= 3;\n    }\n\n    const duration = this.points_[lastIndex + 2] - this.points_[firstIndex + 2];\n    // we don't want a duration of 0 (divide by zero)\n    // we also make sure the user panned for a duration of at least one frame\n    // (1/60s) to compute sane displacement values\n    if (duration < 1000 / 60) {\n      return false;\n    }\n\n    const dx = this.points_[lastIndex] - this.points_[firstIndex];\n    const dy = this.points_[lastIndex + 1] - this.points_[firstIndex + 1];\n    this.angle_ = Math.atan2(dy, dx);\n    this.initialVelocity_ = Math.sqrt(dx * dx + dy * dy) / duration;\n    return this.initialVelocity_ > this.minVelocity_;\n  }\n\n  /**\n   * @return {number} Total distance travelled (pixels).\n   */\n  getDistance() {\n    return (this.minVelocity_ - this.initialVelocity_) / this.decay_;\n  }\n\n  /**\n   * @return {number} Angle of the kinetic panning animation (radians).\n   */\n  getAngle() {\n    return this.angle_;\n  }\n}\n\nexport default Kinetic;\n", "/**\n * @module ol/interaction/defaults\n */\nimport Collection from '../Collection.js';\nimport DoubleC<PERSON><PERSON>oom from './DoubleClickZoom.js';\nimport DragPan from './DragPan.js';\nimport DragRotate from './DragRotate.js';\nimport DragZoom from './DragZoom.js';\nimport KeyboardPan from './KeyboardPan.js';\nimport KeyboardZoom from './KeyboardZoom.js';\nimport Kinetic from '../Kinetic.js';\nimport MouseWheelZoom from './MouseWheelZoom.js';\nimport PinchRotate from './PinchRotate.js';\nimport PinchZoom from './PinchZoom.js';\n\n/**\n * @typedef {Object} DefaultsOptions\n * @property {boolean} [altShiftDragRotate=true] Whether Alt-Shift-drag rotate is\n * desired.\n * @property {boolean} [onFocusOnly=false] Interact only when the map has the\n * focus. This affects the `MouseWheelZoom` and `DragPan` interactions and is\n * useful when page scroll is desired for maps that do not have the browser's\n * focus.\n * @property {boolean} [doubleClickZoom=true] Whether double click zoom is\n * desired.\n * @property {boolean} [keyboard=true] Whether keyboard interaction is desired.\n * @property {boolean} [mouseWheelZoom=true] Whether mousewheel zoom is desired.\n * @property {boolean} [shiftDragZoom=true] Whether Shift-drag zoom is desired.\n * @property {boolean} [dragPan=true] Whether drag pan is desired.\n * @property {boolean} [pinchRotate=true] Whether pinch rotate is desired.\n * @property {boolean} [pinchZoom=true] Whether pinch zoom is desired.\n * @property {number} [zoomDelta] Zoom level delta when using keyboard or double click zoom.\n * @property {number} [zoomDuration] Duration of the zoom animation in\n * milliseconds.\n */\n\n/**\n * Set of interactions included in maps by default. Specific interactions can be\n * excluded by setting the appropriate option to false in the constructor\n * options, but the order of the interactions is fixed.  If you want to specify\n * a different order for interactions, you will need to create your own\n * {@link module:ol/interaction/Interaction~Interaction} instances and insert\n * them into a {@link module:ol/Collection~Collection} in the order you want\n * before creating your {@link module:ol/Map~Map} instance. Changing the order can\n * be of interest if the event propagation needs to be stopped at a point.\n * The default set of interactions, in sequence, is:\n * * {@link module:ol/interaction/DragRotate~DragRotate}\n * * {@link module:ol/interaction/DoubleClickZoom~DoubleClickZoom}\n * * {@link module:ol/interaction/DragPan~DragPan}\n * * {@link module:ol/interaction/PinchRotate~PinchRotate}\n * * {@link module:ol/interaction/PinchZoom~PinchZoom}\n * * {@link module:ol/interaction/KeyboardPan~KeyboardPan}\n * * {@link module:ol/interaction/KeyboardZoom~KeyboardZoom}\n * * {@link module:ol/interaction/MouseWheelZoom~MouseWheelZoom}\n * * {@link module:ol/interaction/DragZoom~DragZoom}\n *\n * @param {DefaultsOptions} [options] Defaults options.\n * @return {Collection<import(\"./Interaction.js\").default>}\n * A collection of interactions to be used with the {@link module:ol/Map~Map}\n * constructor's `interactions` option.\n * @api\n */\nexport function defaults(options) {\n  options = options ? options : {};\n\n  /** @type {Collection<import(\"./Interaction.js\").default>} */\n  const interactions = new Collection();\n\n  const kinetic = new Kinetic(-0.005, 0.05, 100);\n\n  const altShiftDragRotate =\n    options.altShiftDragRotate !== undefined\n      ? options.altShiftDragRotate\n      : true;\n  if (altShiftDragRotate) {\n    interactions.push(new DragRotate());\n  }\n\n  const doubleClickZoom =\n    options.doubleClickZoom !== undefined ? options.doubleClickZoom : true;\n  if (doubleClickZoom) {\n    interactions.push(\n      new DoubleClickZoom({\n        delta: options.zoomDelta,\n        duration: options.zoomDuration,\n      })\n    );\n  }\n\n  const dragPan = options.dragPan !== undefined ? options.dragPan : true;\n  if (dragPan) {\n    interactions.push(\n      new DragPan({\n        onFocusOnly: options.onFocusOnly,\n        kinetic: kinetic,\n      })\n    );\n  }\n\n  const pinchRotate =\n    options.pinchRotate !== undefined ? options.pinchRotate : true;\n  if (pinchRotate) {\n    interactions.push(new PinchRotate());\n  }\n\n  const pinchZoom = options.pinchZoom !== undefined ? options.pinchZoom : true;\n  if (pinchZoom) {\n    interactions.push(\n      new PinchZoom({\n        duration: options.zoomDuration,\n      })\n    );\n  }\n\n  const keyboard = options.keyboard !== undefined ? options.keyboard : true;\n  if (keyboard) {\n    interactions.push(new KeyboardPan());\n    interactions.push(\n      new KeyboardZoom({\n        delta: options.zoomDelta,\n        duration: options.zoomDuration,\n      })\n    );\n  }\n\n  const mouseWheelZoom =\n    options.mouseWheelZoom !== undefined ? options.mouseWheelZoom : true;\n  if (mouseWheelZoom) {\n    interactions.push(\n      new MouseWheelZoom({\n        onFocusOnly: options.onFocusOnly,\n        duration: options.zoomDuration,\n      })\n    );\n  }\n\n  const shiftDragZoom =\n    options.shiftDragZoom !== undefined ? options.shiftDragZoom : true;\n  if (shiftDragZoom) {\n    interactions.push(\n      new DragZoom({\n        duration: options.zoomDuration,\n      })\n    );\n  }\n\n  return interactions;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,IAAM,kBAAN,cAA8B,oBAAY;AAAA;AAAA;AAAA;AAAA,EAIxC,YAAY,SAAS;AACnB,UAAM;AAEN,cAAU,UAAU,UAAU,CAAC;AAM/B,SAAK,SAAS,QAAQ,QAAQ,QAAQ,QAAQ;AAM9C,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,iBAAiB;AAC3B,QAAI,YAAY;AAChB,QAAI,gBAAgB,QAAQ,4BAAoB,UAAU;AACxD,YAAM;AAAA;AAAA,QACJ,gBAAgB;AAAA;AAElB,YAAM,MAAM,gBAAgB;AAC5B,YAAM,SAAS,gBAAgB;AAC/B,YAAM,QAAQ,aAAa,WAAW,CAAC,KAAK,SAAS,KAAK;AAC1D,YAAM,OAAO,IAAI,QAAQ;AACzB,kBAAY,MAAM,OAAO,QAAQ,KAAK,SAAS;AAC/C,mBAAa,eAAe;AAC5B,kBAAY;AAAA,IACd;AACA,WAAO,CAAC;AAAA,EACV;AACF;AAEA,IAAO,0BAAQ;;;AC7Bf,IAAM,UAAN,cAAsB,gBAAmB;AAAA;AAAA;AAAA;AAAA,EAIvC,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ,UAAU;AAAA,IACZ,CAAC;AAED,cAAU,UAAU,UAAU,CAAC;AAM/B,SAAK,WAAW,QAAQ;AAKxB,SAAK,eAAe;AAKpB,SAAK;AAKL,SAAK,WAAW;AAEhB,UAAM,YAAY,QAAQ,YACtB,QAAQ,YACR,IAAI,gBAAgB,aAAa;AAMrC,SAAK,aAAa,QAAQ,cACtB,IAAI,mBAAmB,SAAS,IAChC;AAMJ,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,iBAAiB;AAC/B,UAAM,MAAM,gBAAgB;AAC5B,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW;AAChB,UAAI,QAAQ,EAAE,iBAAiB;AAAA,IACjC;AACA,UAAM,iBAAiB,KAAK;AAC5B,UAAMA,YAAW,IAAI,cAAc,SAAqB,cAAc,CAAC;AACvE,QAAI,eAAe,UAAU,KAAK,oBAAoB;AACpD,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,OAAOA,UAAS,CAAC,GAAGA,UAAS,CAAC,CAAC;AAAA,MAC/C;AACA,UAAI,KAAK,cAAc;AACrB,cAAM,QAAQ;AAAA,UACZ,KAAK,aAAa,CAAC,IAAIA,UAAS,CAAC;AAAA,UACjCA,UAAS,CAAC,IAAI,KAAK,aAAa,CAAC;AAAA,QACnC;AACA,cAAMC,OAAM,gBAAgB;AAC5B,cAAM,OAAOA,KAAI,QAAQ;AACzB,cAAgB,OAAO,KAAK,cAAc,CAAC;AAC3C,eAAiB,OAAO,KAAK,YAAY,CAAC;AAC1C,aAAK,qBAAqB,KAAK;AAAA,MACjC;AAAA,IACF,WAAW,KAAK,UAAU;AAGxB,WAAK,SAAS,MAAM;AAAA,IACtB;AACA,SAAK,eAAeD;AACpB,SAAK,qBAAqB,eAAe;AACzC,oBAAgB,cAAc,eAAe;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,iBAAiB;AAC7B,UAAM,MAAM,gBAAgB;AAC5B,UAAM,OAAO,IAAI,QAAQ;AACzB,QAAI,KAAK,eAAe,WAAW,GAAG;AACpC,UAAI,CAAC,KAAK,cAAc,KAAK,YAAY,KAAK,SAAS,IAAI,GAAG;AAC5D,cAAM,WAAW,KAAK,SAAS,YAAY;AAC3C,cAAM,QAAQ,KAAK,SAAS,SAAS;AACrC,cAAM,SAAS,KAAK,kBAAkB;AACtC,cAAM,WAAW,IAAI,+BAA+B,MAAM;AAC1D,cAAM,OAAO,IAAI,+BAA+B;AAAA,UAC9C,SAAS,CAAC,IAAI,WAAW,KAAK,IAAI,KAAK;AAAA,UACvC,SAAS,CAAC,IAAI,WAAW,KAAK,IAAI,KAAK;AAAA,QACzC,CAAC;AACD,aAAK,gBAAgB;AAAA,UACnB,QAAQ,KAAK,qBAAqB,IAAI;AAAA,UACtC,UAAU;AAAA,UACV,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AACA,UAAI,KAAK,UAAU;AACjB,aAAK,WAAW;AAChB,aAAK,eAAe;AAAA,MACtB;AACA,aAAO;AAAA,IACT;AACA,QAAI,KAAK,UAAU;AAGjB,WAAK,SAAS,MAAM;AAAA,IACtB;AACA,SAAK,eAAe;AACpB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,iBAAiB;AAC/B,QAAI,KAAK,eAAe,SAAS,KAAK,KAAK,WAAW,eAAe,GAAG;AACtE,YAAM,MAAM,gBAAgB;AAC5B,YAAM,OAAO,IAAI,QAAQ;AACzB,WAAK,eAAe;AAEpB,UAAI,KAAK,aAAa,GAAG;AACvB,aAAK,iBAAiB;AAAA,MACxB;AACA,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,MAAM;AAAA,MACtB;AAGA,WAAK,aAAa,KAAK,eAAe,SAAS;AAC/C,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAO,kBAAQ;;;AC7Jf,IAAM,aAAN,cAAyB,gBAAmB;AAAA;AAAA;AAAA;AAAA,EAI1C,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM;AAAA,MACJ,UAAU;AAAA,IACZ,CAAC;AAMD,SAAK,aAAa,QAAQ,YAAY,QAAQ,YAAY;AAM1D,SAAK,aAAa;AAMlB,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,iBAAiB;AAC/B,QAAI,CAAC,UAAU,eAAe,GAAG;AAC/B;AAAA,IACF;AAEA,UAAM,MAAM,gBAAgB;AAC5B,UAAM,OAAO,IAAI,QAAQ;AACzB,QAAI,KAAK,eAAe,EAAE,aAAa,SAAS;AAC9C;AAAA,IACF;AACA,UAAM,OAAO,IAAI,QAAQ;AACzB,UAAM,SAAS,gBAAgB;AAC/B,UAAM,QAAQ,KAAK,MAAM,KAAK,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC;AACzE,QAAI,KAAK,eAAe,QAAW;AACjC,YAAM,QAAQ,QAAQ,KAAK;AAC3B,WAAK,uBAAuB,CAAC,KAAK;AAAA,IACpC;AACA,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,iBAAiB;AAC7B,QAAI,CAAC,UAAU,eAAe,GAAG;AAC/B,aAAO;AAAA,IACT;AAEA,UAAM,MAAM,gBAAgB;AAC5B,UAAM,OAAO,IAAI,QAAQ;AACzB,SAAK,eAAe,KAAK,SAAS;AAClC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,iBAAiB;AAC/B,QAAI,CAAC,UAAU,eAAe,GAAG;AAC/B,aAAO;AAAA,IACT;AAEA,QACE,kBAAkB,eAAe,KACjC,KAAK,WAAW,eAAe,GAC/B;AACA,YAAM,MAAM,gBAAgB;AAC5B,UAAI,QAAQ,EAAE,iBAAiB;AAC/B,WAAK,aAAa;AAClB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAO,qBAAQ;;;ACpHf,IAAM,YAAN,cAAwB,mBAAW;AAAA;AAAA;AAAA;AAAA,EAIjC,YAAY,WAAW;AACrB,UAAM;AAMN,SAAK,YAAY;AAMjB,SAAK,WAAW,SAAS,cAAc,KAAK;AAC5C,SAAK,SAAS,MAAM,WAAW;AAC/B,SAAK,SAAS,MAAM,gBAAgB;AACpC,SAAK,SAAS,YAAY,YAAY;AAMtC,SAAK,OAAO;AAMZ,SAAK,cAAc;AAMnB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,SAAK,OAAO,IAAI;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,UAAM,aAAa,KAAK;AACxB,UAAM,WAAW,KAAK;AACtB,UAAM,KAAK;AACX,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,OAAO,KAAK,IAAI,WAAW,CAAC,GAAG,SAAS,CAAC,CAAC,IAAI;AACpD,UAAM,MAAM,KAAK,IAAI,WAAW,CAAC,GAAG,SAAS,CAAC,CAAC,IAAI;AACnD,UAAM,QAAQ,KAAK,IAAI,SAAS,CAAC,IAAI,WAAW,CAAC,CAAC,IAAI;AACtD,UAAM,SAAS,KAAK,IAAI,SAAS,CAAC,IAAI,WAAW,CAAC,CAAC,IAAI;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,KAAK;AACV,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,oBAAoB,EAAE,YAAY,KAAK,QAAQ;AACzD,YAAM,QAAQ,KAAK,SAAS;AAC5B,YAAM,OAAO;AACb,YAAM,MAAM;AACZ,YAAM,QAAQ;AACd,YAAM,SAAS;AAAA,IACjB;AACA,SAAK,OAAO;AACZ,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,oBAAoB,EAAE,YAAY,KAAK,QAAQ;AAAA,IAC3D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,YAAY,UAAU;AAC9B,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,uBAAuB;AAC5B,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACvB,UAAM,aAAa,KAAK;AACxB,UAAM,WAAW,KAAK;AACtB,UAAM,SAAS;AAAA,MACb;AAAA,MACA,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,MAC3B;AAAA,MACA,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,IAC7B;AACA,UAAM,cAAc,OAAO;AAAA,MACzB,KAAK,KAAK;AAAA,MACV,KAAK;AAAA,IACP;AAEA,gBAAY,CAAC,IAAI,YAAY,CAAC,EAAE,MAAM;AACtC,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY,IAAI,gBAAQ,CAAC,WAAW,CAAC;AAAA,IAC5C,OAAO;AACL,WAAK,UAAU,eAAe,CAAC,WAAW,CAAC;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAO,cAAQ;;;ACjGf,IAAM,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOV,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOR,WAAW;AACb;AAOO,IAAM,eAAN,cAA2B,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtC,YAAY,MAAM,YAAY,iBAAiB;AAC7C,UAAM,IAAI;AAQV,SAAK,aAAa;AAOlB,SAAK,kBAAkB;AAAA,EACzB;AACF;AAwBA,IAAM,UAAN,cAAsB,gBAAmB;AAAA;AAAA;AAAA;AAAA,EAIvC,YAAY,SAAS;AACnB,UAAM;AAKN,SAAK;AAKL,SAAK;AAKL,SAAK;AAEL,cAAU,UAAU,UAAU,CAAC;AAM/B,SAAK,OAAO,IAAI,YAAU,QAAQ,aAAa,YAAY;AAM3D,SAAK,WAAW,QAAQ,YAAY,SAAY,QAAQ,UAAU;AAElE,QAAI,QAAQ,UAAU;AACpB,WAAK,WAAW,QAAQ;AAAA,IAC1B;AAMA,SAAK,cAAc;AAMnB,SAAK,aAAa,QAAQ,YAAY,QAAQ,YAAY;AAM1D,SAAK,mBAAmB,QAAQ,kBAC5B,QAAQ,kBACR,KAAK;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,uBAAuB,iBAAiB,YAAY,UAAU;AAC5D,UAAM,QAAQ,SAAS,CAAC,IAAI,WAAW,CAAC;AACxC,UAAM,SAAS,SAAS,CAAC,IAAI,WAAW,CAAC;AACzC,WAAO,QAAQ,QAAQ,SAAS,UAAU,KAAK;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,WAAO,KAAK,KAAK,YAAY;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,iBAAiB;AAC/B,SAAK,KAAK,UAAU,KAAK,aAAa,gBAAgB,KAAK;AAE3D,SAAK;AAAA,MACH,IAAI;AAAA,QACF,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,iBAAiB;AAC7B,SAAK,KAAK,OAAO,IAAI;AAErB,UAAM,cAAc,KAAK;AAAA,MACvB;AAAA,MACA,KAAK;AAAA,MACL,gBAAgB;AAAA,IAClB;AACA,QAAI,aAAa;AACf,WAAK,SAAS,eAAe;AAAA,IAC/B;AACA,SAAK;AAAA,MACH,IAAI;AAAA,QACF,cAAc,iBAAiB,SAAS,iBAAiB;AAAA,QACzD,gBAAgB;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,iBAAiB;AAC/B,QAAI,KAAK,WAAW,eAAe,GAAG;AACpC,WAAK,cAAc,gBAAgB;AACnC,WAAK,KAAK,OAAO,gBAAgB,GAAG;AACpC,WAAK,KAAK,UAAU,KAAK,aAAa,KAAK,WAAW;AACtD,WAAK;AAAA,QACH,IAAI;AAAA,UACF,iBAAiB;AAAA,UACjB,gBAAgB;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO;AAAA,EAAC;AACnB;AAEA,IAAO,kBAAQ;;;AClPf,IAAM,WAAN,cAAuB,gBAAQ;AAAA;AAAA;AAAA;AAAA,EAI7B,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM,YAAY,QAAQ,YAAY,QAAQ,YAAY;AAE1D,UAAM;AAAA,MACJ;AAAA,MACA,WAAW,QAAQ,aAAa;AAAA,MAChC,SAAS,QAAQ;AAAA,IACnB,CAAC;AAMD,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAMrE,SAAK,OAAO,QAAQ,QAAQ,SAAY,QAAQ,MAAM;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO;AACd,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM;AAAA;AAAA,MAAqD,IAAI,QAAQ;AAAA;AACvE,QAAI,WAAW,KAAK,YAAY;AAEhC,QAAI,KAAK,MAAM;AACb,YAAM,gBAAgB,KAAK,yBAAyB,QAAQ;AAC5D,YAAM,aAAa,KAAK,+BAA+B,aAAa;AACpE,YAAM,SAAS,KAAK,cAAc,IAAI;AACtC,iBAAW,SAAS,MAAM;AAC1B,eAAS,MAAM,SAAS,MAAM;AAAA,IAChC;AAEA,SAAK,YAAY,UAAU;AAAA,MACzB,UAAU,KAAK;AAAA,MACf,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACF;AAEA,IAAO,mBAAQ;;;AC3Ef,IAAO,cAAQ;AAAA,EACb,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,MAAM;AACR;;;ACqBA,IAAM,cAAN,cAA0B,oBAAY;AAAA;AAAA;AAAA;AAAA,EAIpC,YAAY,SAAS;AACnB,UAAM;AAEN,cAAU,WAAW,CAAC;AAOtB,SAAK,oBAAoB,SAAU,iBAAiB;AAClD,aACE,eAAe,eAAe,KAAK,kBAAkB,eAAe;AAAA,IAExE;AAMA,SAAK,aACH,QAAQ,cAAc,SAClB,QAAQ,YACR,KAAK;AAMX,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAMrE,SAAK,cACH,QAAQ,eAAe,SAAY,QAAQ,aAAa;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,iBAAiB;AAC3B,QAAI,YAAY;AAChB,QAAI,gBAAgB,QAAQ,kBAAU,SAAS;AAC7C,YAAM;AAAA;AAAA,QACJ,gBAAgB;AAAA;AAElB,YAAM,MAAM,SAAS;AACrB,UACE,KAAK,WAAW,eAAe,MAC9B,OAAO,YAAI,QACV,OAAO,YAAI,QACX,OAAO,YAAI,SACX,OAAO,YAAI,KACb;AACA,cAAM,MAAM,gBAAgB;AAC5B,cAAM,OAAO,IAAI,QAAQ;AACzB,cAAM,gBAAgB,KAAK,cAAc,IAAI,KAAK;AAClD,YAAI,SAAS,GACX,SAAS;AACX,YAAI,OAAO,YAAI,MAAM;AACnB,mBAAS,CAAC;AAAA,QACZ,WAAW,OAAO,YAAI,MAAM;AAC1B,mBAAS,CAAC;AAAA,QACZ,WAAW,OAAO,YAAI,OAAO;AAC3B,mBAAS;AAAA,QACX,OAAO;AACL,mBAAS;AAAA,QACX;AACA,cAAM,QAAQ,CAAC,QAAQ,MAAM;AAC7B,eAAiB,OAAO,KAAK,YAAY,CAAC;AAC1C,YAAI,MAAM,OAAO,KAAK,SAAS;AAC/B,iBAAS,eAAe;AACxB,oBAAY;AAAA,MACd;AAAA,IACF;AACA,WAAO,CAAC;AAAA,EACV;AACF;AAEA,IAAO,sBAAQ;;;AC3Ff,IAAM,eAAN,cAA2B,oBAAY;AAAA;AAAA;AAAA;AAAA,EAIrC,YAAY,SAAS;AACnB,UAAM;AAEN,cAAU,UAAU,UAAU,CAAC;AAM/B,SAAK,aAAa,QAAQ,YACtB,QAAQ,YACR,SAAU,iBAAiB;AACzB,aACE,CAAC,oBAAoB,eAAe,KACpC,kBAAkB,eAAe;AAAA,IAErC;AAMJ,SAAK,SAAS,QAAQ,QAAQ,QAAQ,QAAQ;AAM9C,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,iBAAiB;AAC3B,QAAI,YAAY;AAChB,QACE,gBAAgB,QAAQ,kBAAU,WAClC,gBAAgB,QAAQ,kBAAU,UAClC;AACA,YAAM;AAAA;AAAA,QACJ,gBAAgB;AAAA;AAElB,YAAM,MAAM,SAAS;AACrB,UAAI,KAAK,WAAW,eAAe,MAAM,QAAQ,OAAO,QAAQ,MAAM;AACpE,cAAM,MAAM,gBAAgB;AAC5B,cAAM,QAAQ,QAAQ,MAAM,KAAK,SAAS,CAAC,KAAK;AAChD,cAAM,OAAO,IAAI,QAAQ;AACzB,oBAAY,MAAM,OAAO,QAAW,KAAK,SAAS;AAClD,iBAAS,eAAe;AACxB,oBAAY;AAAA,MACd;AAAA,IACF;AACA,WAAO,CAAC;AAAA,EACV;AACF;AAEA,IAAO,uBAAQ;;;AC5Df,IAAM,iBAAN,cAA6B,oBAAY;AAAA;AAAA;AAAA;AAAA,EAIvC,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B;AAAA;AAAA,MACgE;AAAA,IAChE;AAMA,SAAK,cAAc;AAMnB,SAAK,aAAa;AAMlB,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAMrE,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAMrE,SAAK,WAAW,QAAQ,YAAY,SAAY,QAAQ,UAAU;AAMlE,SAAK,aACH,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAMxD,SAAK,uBACH,QAAQ,wBAAwB,SAC5B,QAAQ,sBACR;AAEN,UAAM,YAAY,QAAQ,YAAY,QAAQ,YAAY;AAM1D,SAAK,aAAa,QAAQ,cACtB,IAAI,mBAAmB,SAAS,IAChC;AAMJ,SAAK,cAAc;AAMnB,SAAK,aAAa;AAMlB,SAAK;AAML,SAAK,QAAQ;AAQb,SAAK,oBAAoB;AAMzB,SAAK;AAOL,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,SAAK,qBAAqB;AAC1B,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,OAAO,IAAI,QAAQ;AACzB,SAAK;AAAA,MACH;AAAA,MACA,KAAK,aAAc,KAAK,aAAa,IAAI,IAAI,KAAM;AAAA,MACnD,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,iBAAiB;AAC3B,QAAI,CAAC,KAAK,WAAW,eAAe,GAAG;AACrC,aAAO;AAAA,IACT;AACA,UAAM,OAAO,gBAAgB;AAC7B,QAAI,SAAS,kBAAU,OAAO;AAC5B,aAAO;AAAA,IACT;AAEA,UAAM,MAAM,gBAAgB;AAC5B,UAAM;AAAA;AAAA,MACJ,gBAAgB;AAAA;AAElB,eAAW,eAAe;AAE1B,QAAI,KAAK,YAAY;AACnB,WAAK,cAAc,gBAAgB;AAAA,IACrC;AAIA,QAAI;AACJ,QAAI,gBAAgB,QAAQ,kBAAU,OAAO;AAC3C,cAAQ,WAAW;AACnB,UAAI,WAAW,WAAW,cAAc,WAAW,iBAAiB;AAClE,iBAAS;AAAA,MACX;AACA,UAAI,WAAW,cAAc,WAAW,gBAAgB;AACtD,iBAAS;AAAA,MACX;AAAA,IACF;AAEA,QAAI,UAAU,GAAG;AACf,aAAO;AAAA,IACT;AACA,SAAK,aAAa;AAElB,UAAM,MAAM,KAAK,IAAI;AAErB,QAAI,KAAK,eAAe,QAAW;AACjC,WAAK,aAAa;AAAA,IACpB;AAEA,QAAI,CAAC,KAAK,SAAS,MAAM,KAAK,aAAa,KAAK,mBAAmB;AACjE,WAAK,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,aAAa;AAAA,IAClD;AAEA,UAAM,OAAO,IAAI,QAAQ;AACzB,QACE,KAAK,UAAU,cACf,EAAE,KAAK,uBAAuB,KAAK,KAAK,uBACxC;AACA,UAAI,KAAK,oBAAoB;AAC3B,qBAAa,KAAK,kBAAkB;AAAA,MACtC,OAAO;AACL,YAAI,KAAK,aAAa,GAAG;AACvB,eAAK,iBAAiB;AAAA,QACxB;AACA,aAAK,iBAAiB;AAAA,MACxB;AACA,WAAK,qBAAqB;AAAA,QACxB,KAAK,gBAAgB,KAAK,IAAI;AAAA,QAC9B,KAAK;AAAA,MACP;AACA,WAAK,WAAW,CAAC,QAAQ,KAAK,eAAe,KAAK,WAAW;AAC7D,WAAK,aAAa;AAClB,aAAO;AAAA,IACT;AAEA,SAAK,eAAe;AAEpB,UAAM,WAAW,KAAK,IAAI,KAAK,YAAY,MAAM,KAAK,aAAa,CAAC;AAEpE,iBAAa,KAAK,UAAU;AAC5B,SAAK,aAAa;AAAA,MAChB,KAAK,iBAAiB,KAAK,MAAM,GAAG;AAAA,MACpC;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,KAAK;AACpB,UAAM,OAAO,IAAI,QAAQ;AACzB,QAAI,KAAK,aAAa,GAAG;AACvB,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,QACF,CAAC;AAAA,MACC,KAAK;AAAA,MACL,CAAC,KAAK,YAAY,KAAK;AAAA,MACvB,KAAK,YAAY,KAAK;AAAA,IACxB,IAAI,KAAK;AACX,QAAI,KAAK,uBAAuB,KAAK,KAAK,sBAAsB;AAE9D,cAAQ,QAAS,QAAQ,IAAI,IAAI,KAAM;AAAA,IACzC;AACA,gBAAY,MAAM,OAAO,KAAK,aAAa,KAAK,SAAS;AAEzD,SAAK,QAAQ;AACb,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,WAAW;AACxB,SAAK,aAAa;AAClB,QAAI,CAAC,WAAW;AACd,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AACF;AAEA,IAAO,yBAAQ;;;ACnRf,IAAM,cAAN,cAA0B,gBAAmB;AAAA;AAAA;AAAA;AAAA,EAI3C,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM;AAAA;AAAA,MACJ;AAAA;AAGF,QAAI,CAAC,eAAe,UAAU;AAC5B,qBAAe,WAAW;AAAA,IAC5B;AAEA,UAAM,cAAc;AAMpB,SAAK,UAAU;AAMf,SAAK,aAAa;AAMlB,SAAK,YAAY;AAMjB,SAAK,iBAAiB;AAMtB,SAAK,aAAa,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAMxE,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,iBAAiB;AAC/B,QAAI,gBAAgB;AAEpB,UAAM,SAAS,KAAK,eAAe,CAAC;AACpC,UAAM,SAAS,KAAK,eAAe,CAAC;AAGpC,UAAM,QAAQ,KAAK;AAAA,MACjB,OAAO,UAAU,OAAO;AAAA,MACxB,OAAO,UAAU,OAAO;AAAA,IAC1B;AAEA,QAAI,KAAK,eAAe,QAAW;AACjC,YAAM,QAAQ,QAAQ,KAAK;AAC3B,WAAK,kBAAkB;AACvB,UAAI,CAAC,KAAK,aAAa,KAAK,IAAI,KAAK,cAAc,IAAI,KAAK,YAAY;AACtE,aAAK,YAAY;AAAA,MACnB;AACA,sBAAgB;AAAA,IAClB;AACA,SAAK,aAAa;AAElB,UAAM,MAAM,gBAAgB;AAC5B,UAAM,OAAO,IAAI,QAAQ;AACzB,QAAI,KAAK,eAAe,EAAE,aAAa,SAAS;AAC9C;AAAA,IACF;AAKA,SAAK,UAAU,IAAI;AAAA,MACjB,IAAI,cAAc,SAAqB,KAAK,cAAc,CAAC;AAAA,IAC7D;AAGA,QAAI,KAAK,WAAW;AAClB,UAAI,OAAO;AACX,WAAK,uBAAuB,eAAe,KAAK,OAAO;AAAA,IACzD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,iBAAiB;AAC7B,QAAI,KAAK,eAAe,SAAS,GAAG;AAClC,YAAM,MAAM,gBAAgB;AAC5B,YAAM,OAAO,IAAI,QAAQ;AACzB,WAAK,eAAe,KAAK,SAAS;AAClC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,iBAAiB;AAC/B,QAAI,KAAK,eAAe,UAAU,GAAG;AACnC,YAAM,MAAM,gBAAgB;AAC5B,WAAK,UAAU;AACf,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB,WAAK,iBAAiB;AACtB,UAAI,CAAC,KAAK,wBAAwB;AAChC,YAAI,QAAQ,EAAE,iBAAiB;AAAA,MACjC;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAO,sBAAQ;;;AC3If,IAAM,YAAN,cAAwB,gBAAmB;AAAA;AAAA;AAAA;AAAA,EAIzC,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM;AAAA;AAAA,MACJ;AAAA;AAGF,QAAI,CAAC,eAAe,UAAU;AAC5B,qBAAe,WAAW;AAAA,IAC5B;AAEA,UAAM,cAAc;AAMpB,SAAK,UAAU;AAMf,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAMrE,SAAK,gBAAgB;AAMrB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,iBAAiB;AAC/B,QAAI,aAAa;AAEjB,UAAM,SAAS,KAAK,eAAe,CAAC;AACpC,UAAM,SAAS,KAAK,eAAe,CAAC;AACpC,UAAM,KAAK,OAAO,UAAU,OAAO;AACnC,UAAM,KAAK,OAAO,UAAU,OAAO;AAGnC,UAAM,WAAW,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAE5C,QAAI,KAAK,kBAAkB,QAAW;AACpC,mBAAa,KAAK,gBAAgB;AAAA,IACpC;AACA,SAAK,gBAAgB;AAErB,UAAM,MAAM,gBAAgB;AAC5B,UAAM,OAAO,IAAI,QAAQ;AAEzB,QAAI,cAAc,GAAK;AACrB,WAAK,kBAAkB;AAAA,IACzB;AAGA,SAAK,UAAU,IAAI;AAAA,MACjB,IAAI,cAAc,SAAqB,KAAK,cAAc,CAAC;AAAA,IAC7D;AAGA,QAAI,OAAO;AACX,SAAK,yBAAyB,YAAY,KAAK,OAAO;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,iBAAiB;AAC7B,QAAI,KAAK,eAAe,SAAS,GAAG;AAClC,YAAM,MAAM,gBAAgB;AAC5B,YAAM,OAAO,IAAI,QAAQ;AACzB,YAAM,YAAY,KAAK,kBAAkB,IAAI,IAAI;AACjD,WAAK,eAAe,KAAK,WAAW,SAAS;AAC7C,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,iBAAiB;AAC/B,QAAI,KAAK,eAAe,UAAU,GAAG;AACnC,YAAM,MAAM,gBAAgB;AAC5B,WAAK,UAAU;AACf,WAAK,gBAAgB;AACrB,WAAK,kBAAkB;AACvB,UAAI,CAAC,KAAK,wBAAwB;AAChC,YAAI,QAAQ,EAAE,iBAAiB;AAAA,MACjC;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAO,oBAAQ;;;AC5Hf,IAAM,UAAN,MAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOZ,YAAY,OAAO,aAAa,OAAO;AAKrC,SAAK,SAAS;AAMd,SAAK,eAAe;AAMpB,SAAK,SAAS;AAMd,SAAK,UAAU,CAAC;AAMhB,SAAK,SAAS;AAMd,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,SAAK,QAAQ,SAAS;AACtB,SAAK,SAAS;AACd,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,GAAG,GAAG;AACX,SAAK,QAAQ,KAAK,GAAG,GAAG,KAAK,IAAI,CAAC;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM;AACJ,QAAI,KAAK,QAAQ,SAAS,GAAG;AAG3B,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,KAAK,IAAI,IAAI,KAAK;AAChC,UAAM,YAAY,KAAK,QAAQ,SAAS;AACxC,QAAI,KAAK,QAAQ,YAAY,CAAC,IAAI,OAAO;AAGvC,aAAO;AAAA,IACT;AAGA,QAAI,aAAa,YAAY;AAC7B,WAAO,aAAa,KAAK,KAAK,QAAQ,aAAa,CAAC,IAAI,OAAO;AAC7D,oBAAc;AAAA,IAChB;AAEA,UAAM,WAAW,KAAK,QAAQ,YAAY,CAAC,IAAI,KAAK,QAAQ,aAAa,CAAC;AAI1E,QAAI,WAAW,MAAO,IAAI;AACxB,aAAO;AAAA,IACT;AAEA,UAAM,KAAK,KAAK,QAAQ,SAAS,IAAI,KAAK,QAAQ,UAAU;AAC5D,UAAM,KAAK,KAAK,QAAQ,YAAY,CAAC,IAAI,KAAK,QAAQ,aAAa,CAAC;AACpE,SAAK,SAAS,KAAK,MAAM,IAAI,EAAE;AAC/B,SAAK,mBAAmB,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,IAAI;AACvD,WAAO,KAAK,mBAAmB,KAAK;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,YAAQ,KAAK,eAAe,KAAK,oBAAoB,KAAK;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAO,kBAAQ;;;AC/DR,SAAS,SAAS,SAAS;AAChC,YAAU,UAAU,UAAU,CAAC;AAG/B,QAAM,eAAe,IAAI,mBAAW;AAEpC,QAAM,UAAU,IAAI,gBAAQ,OAAQ,MAAM,GAAG;AAE7C,QAAM,qBACJ,QAAQ,uBAAuB,SAC3B,QAAQ,qBACR;AACN,MAAI,oBAAoB;AACtB,iBAAa,KAAK,IAAI,mBAAW,CAAC;AAAA,EACpC;AAEA,QAAM,kBACJ,QAAQ,oBAAoB,SAAY,QAAQ,kBAAkB;AACpE,MAAI,iBAAiB;AACnB,iBAAa;AAAA,MACX,IAAI,wBAAgB;AAAA,QAClB,OAAO,QAAQ;AAAA,QACf,UAAU,QAAQ;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,UAAU,QAAQ,YAAY,SAAY,QAAQ,UAAU;AAClE,MAAI,SAAS;AACX,iBAAa;AAAA,MACX,IAAI,gBAAQ;AAAA,QACV,aAAa,QAAQ;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,cACJ,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAC5D,MAAI,aAAa;AACf,iBAAa,KAAK,IAAI,oBAAY,CAAC;AAAA,EACrC;AAEA,QAAM,YAAY,QAAQ,cAAc,SAAY,QAAQ,YAAY;AACxE,MAAI,WAAW;AACb,iBAAa;AAAA,MACX,IAAI,kBAAU;AAAA,QACZ,UAAU,QAAQ;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,WAAW,QAAQ,aAAa,SAAY,QAAQ,WAAW;AACrE,MAAI,UAAU;AACZ,iBAAa,KAAK,IAAI,oBAAY,CAAC;AACnC,iBAAa;AAAA,MACX,IAAI,qBAAa;AAAA,QACf,OAAO,QAAQ;AAAA,QACf,UAAU,QAAQ;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,iBACJ,QAAQ,mBAAmB,SAAY,QAAQ,iBAAiB;AAClE,MAAI,gBAAgB;AAClB,iBAAa;AAAA,MACX,IAAI,uBAAe;AAAA,QACjB,aAAa,QAAQ;AAAA,QACrB,UAAU,QAAQ;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,gBACJ,QAAQ,kBAAkB,SAAY,QAAQ,gBAAgB;AAChE,MAAI,eAAe;AACjB,iBAAa;AAAA,MACX,IAAI,iBAAS;AAAA,QACX,UAAU,QAAQ;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AACT;", "names": ["centroid", "map"]}