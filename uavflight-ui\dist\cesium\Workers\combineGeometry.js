define(["./PrimitivePipeline-75e51f37","./createTaskProcessorWorker","./Transforms-01e95659","./Matrix3-a348023f","./defaultValue-0a909f67","./Math-e97915da","./Matrix2-7146c9ca","./RuntimeError-06c93819","./combine-ca22a614","./ComponentDatatype-77274976","./WebGLConstants-a8cc3e8c","./GeometryAttribute-f5d71750","./GeometryAttributes-f06a2792","./GeometryPipeline-049a5b67","./AttributeCompression-50c9aeba","./EncodedCartesian3-0fb84db0","./IndexDatatype-2149f06c","./IntersectionTests-0bb04fde","./Plane-8575e17c","./WebMercatorProjection-f4deae14"],(function(e,t,i,r,a,n,o,c,s,m,b,f,u,P,p,d,l,y,G,C){"use strict";return t((function(t,i){const r=e.PrimitivePipeline.unpackCombineGeometryParameters(t),a=e.PrimitivePipeline.combineGeometry(r);return e.PrimitivePipeline.packCombineGeometryResults(a,i)}))}));
