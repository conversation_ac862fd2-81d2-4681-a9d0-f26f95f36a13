/**
 * @file styleUtils.ts
 * @description 地图样式工具函数集合，提供OpenLayers样式创建和处理功能
 * 
 * 该文件提供了一系列用于处理OpenLayers地图样式的工具函数，包括：
 * - 颜色解析和处理（parseColor）
 * - 点、线、面、图标等基础样式创建
 * - 根据几何类型自动创建样式
 * - 样式过滤条件评估
 * 
 * 这些工具函数简化了OpenLayers样式的创建和管理过程，
 * 为应用提供统一且易用的样式处理接口。
 * 
 * <AUTHOR>
 * @date 2025-05-19
 */
/*
 * @Description: 地图样式工具函数
 */
import { Fill, Stroke, Style, Circle, Icon } from 'ol/style';
import { Feature } from 'ol';

/**
 * 颜色类型定义
 */
interface Color {
	r: number;
	g: number;
	b: number;
	a: number;
}

/**
 * 解析颜色字符串
 * @param colorStr 颜色字符串(十六进制或rgba)
 * @returns 解析后的颜色对象
 */
export function parseColor(colorStr: string): Color {
	// 默认颜色
	const defaultColor = { r: 51, g: 136, b: 255, a: 1 };
	
	if (!colorStr) return defaultColor;
	
	// 处理rgba格式
	if (colorStr.startsWith('rgba')) {
		const rgba = colorStr.match(/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/);
		if (rgba) {
			return {
				r: parseInt(rgba[1]),
				g: parseInt(rgba[2]),
				b: parseInt(rgba[3]),
				a: parseFloat(rgba[4])
			};
		}
	}
	
	// 处理rgb格式
	if (colorStr.startsWith('rgb')) {
		const rgb = colorStr.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/);
		if (rgb) {
			return {
				r: parseInt(rgb[1]),
				g: parseInt(rgb[2]),
				b: parseInt(rgb[3]),
				a: 1
			};
		}
	}
	
	// 处理十六进制格式
	if (colorStr.startsWith('#')) {
		let hex = colorStr.substring(1);
		
		// 转换短格式 (#rgb) 为长格式 (#rrggbb)
		if (hex.length === 3) {
			hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
		}
		
		const r = parseInt(hex.substring(0, 2), 16);
		const g = parseInt(hex.substring(2, 4), 16);
		const b = parseInt(hex.substring(4, 6), 16);
		
		return { r, g, b, a: 1 };
	}
	
	return defaultColor;
}

/**
 * 创建点样式
 * @param color 颜色
 * @param radius 半径
 * @returns OpenLayers样式
 */
export function createPointStyle(color: string, radius: number = 6): Style {
	return new Style({
		image: new Circle({
			radius,
			fill: new Fill({
				color
			}),
			stroke: new Stroke({
				color: 'white',
				width: 2
			})
		})
	});
}

/**
 * 创建线样式
 * @param color 颜色
 * @param width 线宽
 * @returns OpenLayers样式
 */
export function createLineStyle(color: string, width: number = 2): Style {
	return new Style({
		stroke: new Stroke({
			color,
			width,
			lineCap: 'round',
			lineJoin: 'round'
		})
	});
}

/**
 * 创建面样式
 * @param fillColor 填充颜色
 * @param strokeColor 边框颜色
 * @param strokeWidth 边框宽度
 * @returns OpenLayers样式
 */
export function createPolygonStyle(fillColor: string, strokeColor: string = '#3388ff', strokeWidth: number = 2): Style {
	return new Style({
		fill: new Fill({
			color: fillColor
		}),
		stroke: new Stroke({
			color: strokeColor,
			width: strokeWidth
		})
	});
}

/**
 * 创建图标样式
 * @param src 图标URL
 * @param scale 缩放比例
 * @returns OpenLayers样式
 */
export function createIconStyle(src: string, scale: number = 1): Style {
	return new Style({
		image: new Icon({
			src,
			scale
		})
	});
}

/**
 * 评估过滤条件
 * @param feature 要素
 * @param property 属性名
 * @param operator 操作符
 * @param value 比较值
 * @returns 是否满足条件
 */
export function evaluateFilter(
	feature: Feature,
	property: string,
	operator: '=' | '!=' | '>' | '<' | '>=' | '<=' | 'in',
	value: any
): boolean {
	const properties = feature.getProperties();
	const propValue = properties[property];
	
	switch (operator) {
		case '=': return propValue === value;
		case '!=': return propValue !== value;
		case '>': return propValue > value;
		case '<': return propValue < value;
		case '>=': return propValue >= value;
		case '<=': return propValue <= value;
		case 'in': return Array.isArray(value) && value.includes(propValue);
		default: return false;
	}
}

/**
 * 根据要素类型创建样式
 * @param feature 要素
 * @param color 颜色
 * @returns OpenLayers样式
 */
export function createStyleByGeometryType(feature: Feature, color: string = '#3388ff'): Style {
	const geometry = feature.getGeometry();
	
	if (!geometry) {
		return createPointStyle(color);
	}
	
	const type = geometry.getType();
	
	if (type === 'Point' || type === 'MultiPoint') {
		return createPointStyle(color);
	} else if (type === 'LineString' || type === 'MultiLineString') {
		return createLineStyle(color);
	} else if (type === 'Polygon' || type === 'MultiPolygon') {
		return createPolygonStyle(color + '80', color);
	}
	
	return createPointStyle(color);
}

/**
 * 将静态WFS URL转换为使用bbox策略的函数式URL
 * @param url 原始WFS URL
 * @returns 返回一个接收extent参数的函数，用于动态生成带有bbox参数的WFS URL
 */
export function convertToWfsBboxUrl(url: string): (extent: number[]) => string {
	// 解析原始URL
	try {
		const parsedUrl = new URL(url);
		const params = parsedUrl.searchParams;
		
		// 提取关键参数
		const service = params.get('service') || 'WFS';
		const version = params.get('version') || '1.1.0';
		const request = params.get('request') || 'GetFeature';
		const outputFormat = params.get('outputFormat') || 'application/json';
		
		// 从URL中提取typename（图层名称）- 注意处理不同的大小写情况
		let typeName = params.get('typeName'); // 常见形式
		if (!typeName) typeName = params.get('TYPENAME') || params.get('typename') || params.get('TypeName');
		
		// 尝试从其他常见参数中获取
		if (!typeName) {
			typeName = params.get('layers') || params.get('LAYERS') || params.get('layer') || params.get('LAYER');
		}
		
		if (!typeName) {
			console.error('无法从URL中提取typeName/layers参数:', url);
			// 如果无法提取，则返回原始URL
			return () => url;
		}
		
		console.log(`成功提取到图层名称: ${typeName}`);
		
		// 构建基础URL（不包含bbox参数）
		const baseUrl = `${parsedUrl.origin}${parsedUrl.pathname}?service=${service}&version=${version}&request=${request}&typename=${typeName}&outputFormat=${outputFormat}&srsname=EPSG:3857`;
		
		// 返回接收extent参数的函数
		return function(extent: number[]): string {
			const bboxUrl = `${baseUrl}&bbox=${extent.join(',')},EPSG:4326`;
			console.log(`生成的WFS请求URL: ${bboxUrl}`);
			return bboxUrl;
		};
	} catch (error) {
		console.error('解析WFS URL失败:', error);
		// 发生错误时返回原始URL
		return () => url;
	}
} 