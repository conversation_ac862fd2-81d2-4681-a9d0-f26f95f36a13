/*
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-06-05 09:17:32
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-06-05 10:16:39
 * @FilePath: \uavflight-ui\src\env.d.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_PORT: number;
  readonly VITE_OPEN: boolean;
  readonly ENV: string;
  readonly VITE_ADMIN_PROXY_PATH: string;
  
  readonly VITE_GEN_PROXY_PATH: string;
  // 地图服务相关环境变量
  readonly VITE_MAP_SERVER_IP: string;
  readonly VITE_MAP_SERVER_PORT_NGINX: number;
  readonly VITE_MAP_SERVER_PORT_GEOSERVER: number;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

declare module '*.vue' {
  import type { DefineComponent } from 'vue';
  const component: DefineComponent<{}, {}, any>;
  export default component;
} 