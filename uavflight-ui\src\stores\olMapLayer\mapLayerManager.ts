/**
 * @file mapLayerManager.ts
 * @description 地图图层管理器Store，负责管理OpenLayers地图的图层加载、显示和样式
 * 
 * 该文件提供了地图图层管理的核心功能，包括：
 * - 加载和解析地图配置文件
 * - 创建和管理不同类型的图层（WMS、XYZ、GeoJSON、MVT等）
 * - 控制图层的显示/隐藏、透明度和样式
 * - 处理图层事件和交互
 * - 提供图层排序和重新排序功能
 * 
 * 该Store使用Pinia状态管理，与OpenLayers地图实例紧密集成，
 * 为应用提供统一的图层管理接口。
 * 
 * <AUTHOR>
 * @date 2025-05-19
 */
/*
 * @Description: 地图图层管理 Store (OpenLayers版本)
 */
import { defineStore } from 'pinia';
import type Map from 'ol/Map';
import TileLayer from 'ol/layer/Tile';
import VectorLayer from 'ol/layer/Vector';
import XYZ from 'ol/source/XYZ';
import TileWMS from 'ol/source/TileWMS';
import VectorSource from 'ol/source/Vector';
import GeoJSON from 'ol/format/GeoJSON';
import MVT from 'ol/format/MVT';
import VectorTileLayer from 'ol/layer/VectorTile';
import VectorTileSource from 'ol/source/VectorTile';
import WMTS from 'ol/source/WMTS';
import WMTSTileGrid from 'ol/tilegrid/WMTS';
import { get as getProjection } from 'ol/proj';
import { getTopLeft } from 'ol/extent';
import TileState from 'ol/TileState';
import { Style, Fill, Stroke, Icon, Circle, Text } from 'ol/style';
import Feature from 'ol/Feature';
import { fromLonLat } from 'ol/proj';
import type OLMap from '/@/utils/map/olMap';
import { 
	convertToWfsBboxUrl, 
	generateRandomColor, 
	getDarkerColor, 
	colorToHex, 
	colorToRgba 
} from './styleUtils';
import { bbox as bboxStrategy } from 'ol/loadingstrategy';
import { 
	getGeoserverWfsUrl, 
	getGeoserverWmsUrl, 
	buildGeoserverWfsUrl, 
	buildGeoserverWmsUrl,
	getGeoserverBaseUrl,
	buildGeoserverWmtsUrl
} from '/@/utils/geoserver';
import { GeoserverLayer } from '/@/utils/map/layers';
import { loadMapConfigFile, loadMapAndStyleConfig } from '/@/utils/mapConfig';

// ======= 优先级瓦片加载配置 =======

// 瓦片加载超时时间（毫秒）
// 超过这个时间没加载完就认为"卡住"，主动释放
const tileTimeoutMs = 3000;

// 待重试队列（高优先级瓦片的 key 会存到这里）
const retryQueue: string[] = [];

// 正在加载的瓦片集合： key → 定时器ID
// 用来在 onload/onerror 时清理对应的超时定时器
const loadingTiles = new globalThis.Map<string, number>();

/**
 * 根据 tile 对象生成唯一 key
 * @param tile - 当前加载的瓦片对象
 * @returns 唯一标识，例如 "z/x/y"
 */
function getTileKey(tile: any): string {
  const coord = tile.getTileCoord(); // [z, x, y]
  return coord.join('/');
}

/**
 * 自定义 tileLoadFunction
 * 核心逻辑：
 * 1. 开始加载瓦片时，启动一个超时计时器
 * 2. 如果超时仍未加载完 → 释放并行请求槽位（tile.setState(ERROR)）
 * 3. 释放的瓦片加入 retryQueue，等待优先重试
 */
function priorityTileLoadFunction(tile: any, src: string): void {
  const img = tile.getImage();   // 获取 HTMLImageElement
  const key = getTileKey(tile);  // 唯一标识
  let loaded = false;            // 标记瓦片是否已完成（成功/失败）

  // ======== 瓦片加载成功事件 ========
  img.onload = () => {
    loaded = true;
    const timeoutId = loadingTiles.get(key);
    if (timeoutId) {
      clearTimeout(timeoutId); // 清理超时定时器
    }
    loadingTiles.delete(key);            // 从正在加载集合中移除
    tile.setState(TileState.LOADED);     // 标记为已加载
  };

  // ======== 瓦片加载失败事件 ========
  img.onerror = () => {
    loaded = true;
    const timeoutId = loadingTiles.get(key);
    if (timeoutId) {
      clearTimeout(timeoutId); // 清理超时定时器
    }
    loadingTiles.delete(key);
    tile.setState(TileState.ERROR);      // 标记为加载失败
  };

  // ======== 超时释放机制 ========
  const timeoutId = setTimeout(() => {
    if (!loaded && tile.getState() === TileState.LOADING) {
      console.warn(`Tile ${key} timeout → releasing & queueing retry`);

      // 将该瓦片加入高优先级重试队列
      retryQueue.push(key);

      // 强制标记为 ERROR → 立即释放浏览器并行请求槽位
      tile.setState(TileState.ERROR);
    }
  }, tileTimeoutMs);

  // 把该瓦片加入"正在加载集合"
  loadingTiles.set(key, timeoutId);

  // ======== 正式触发加载 ========
  img.src = src;
}

// 定义参数类型
interface LayerParameters {
	service?: string;
	format?: string;
	transparent?: boolean;
	[key: string]: any;
}

// 定义图层样式类型
interface LayerStyle {
	color?: string;
	weight?: number;
	opacity?: number;
	fillColor?: string;
	fillOpacity?: number;
	image?: {
		src: string;
		scale?: number;
		anchor?: [number, number];
		rotation?: number;
	};
	label?: {
		text: string;
		font?: string;
		fill?: {
			color: string;
		};
		stroke?: {
			color: string;
			width: number;
		};
	};
}

// 定义样式规则
interface StyleRule {
	filter: {
		property: string;
		operator: '=' | '!=' | '>' | '<' | '>=' | '<=' | 'in';
		value: any;
	};
	style: LayerStyle | string;
}

// 定义地图图层接口
export interface MapLayer {
	id: string;
	name: string;
	type: 'raster' | 'vector';
	protocol?: 'XYZ' | 'WMS' | 'GeoJSON' | 'MVT' | 'WFS' | 'WMTS';
	url: string;
	initialLoad: boolean;
	// 矢量图层的几何类型
	geometryType?: 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon';
	// WMS 特定属性
	layers?: string;
	parameters?: LayerParameters;
	// 其他属性
	defaultStyle?: LayerStyle | string;
	styleRules?: StyleRule[];
	active?: boolean;
	layerInstance?: any;
	// 事件标识
	event?: string;
	showLabels?: boolean;
	labelField?: string;
	labelStyle?: {
		color: string;
		fontSize: number;
	};
	workspace?: string;
	layerName?: string;
	extent?: number[];
	opacity?: number;
	zIndex?: number;
	labelMaxResolution?: number;
	labelMinResolution?: number;
	theme?: string;
}

// 定义图层管理器状态
interface MapLayerManagerState {
	mapConfig: {
		layers: MapLayer[];
		styles?: Record<string, LayerStyle>;
	} | null;
	activeMap: OLMap | null;
	olMap: Map | null;  // 添加一个存储OpenLayers Map实例的属性
	loadedLayers: string[];
	styleLibrary: Record<string, LayerStyle>;
}

/**
 * 图层管理器Store
 */
export const useMapLayerManagerStore = defineStore({
	id: 'mapLayerManager',
	state: (): MapLayerManagerState => ({
		mapConfig: null,
		activeMap: null,
		olMap: null,
		loadedLayers: [],
		styleLibrary: {},
	}),
	getters: {
		/**
		 * 获取已加载图层数量
		 */
		loadedLayerCount: (state) => state.loadedLayers.length,
		
		/**
		 * 获取图层配置
		 */
		getLayerById: (state) => (layerId: string) => {
			return state.mapConfig?.layers.find(layer => layer.id === layerId);
		},
		
		/**
		 * 判断图层是否已加载
		 */
		isLayerLoaded: (state) => (layerId: string) => {
			return state.loadedLayers.includes(layerId);
		},
	},
	actions: {
		/**
		 * 加载地图配置
		 * 从服务器获取地图图层配置信息
		 * 
		 * 该方法执行以下操作：
		 * 1. 尝试从多个可能的URL路径加载配置文件
		 * 2. 验证加载的配置数据格式是否正确
		 * 3. 加载样式库
		 * 
		 * 配置文件应包含图层数组和可选的样式定义，格式如下：
		 * ```
		 * {
		 *   "layers": [
		 *     {
		 *       "id": "layer1",
		 *       "name": "底图",
		 *       "type": "raster",
		 *       "protocol": "XYZ",
		 *       "url": "https://example.com/tiles/{z}/{x}/{y}.png",
		 *       "initialLoad": true
		 *     },
		 *     ...
		 *   ],
		 *   "styles": {
		 *     "defaultStyle": { ... }
		 *   }
		 * }
		 * ```
		 * 
		 * @returns 是否加载成功的Promise
		 */
		async loadConfig(): Promise<boolean> {
			try {
				console.log('正在加载地图配置...');
				
				// 使用新的工具函数加载配置
				const { mapConfig, styleConfig } = await loadMapAndStyleConfig('baseMap2', 'baseStyle2');
				
				// 保存配置
				this.mapConfig = mapConfig;
				this.styleLibrary = styleConfig.styles;
				
				console.log('地图配置已加载:', this.mapConfig);
				console.log('样式库已加载:', this.styleLibrary);
				
				// 处理图层协议和URL替换
				if (this.mapConfig && this.mapConfig.layers) {
					this.mapConfig.layers.forEach(layer => {
						// 根据工作区和图层名构建URL
						this.buildLayerUrl(layer);
						
						// 替换Geoserver URL
						this.replaceGeoserverUrl(layer);
						
						// 自动检测和处理WFS协议
						if (layer.type === 'vector') {
							this.detectAndSetWfsProtocol(layer);
						}
					});
				}
				
				return true;
			} catch (error) {
				console.error('加载地图配置失败:', error);
				return false;
			}
		},
		
		/**
		 * 为图层构建URL
		 * 根据图层的workspace和layerName属性，自动构建对应协议的URL
		 * 
		 * @param layer 图层配置
		 */
		buildLayerUrl(layer: MapLayer): void {
			// 如果已有URL或没有工作区和图层名，则不处理
			if (layer.url || (!layer.workspace && !layer.layerName)) {
				return;
			}
			
			// 确保工作区和图层名都存在
			if (!layer.workspace || !layer.layerName) {
				console.warn(`图层 ${layer.id} 缺少工作区或图层名，无法自动构建URL`);
				return;
			}
			
			// 根据协议构建不同的URL
			switch (layer.protocol) {
				case 'WFS':
					layer.url = buildGeoserverWfsUrl(layer.workspace, layer.layerName);
					break;
				case 'WMS':
					layer.url = buildGeoserverWmsUrl(layer.workspace, layer.layerName);
					break;
				case 'WMTS':
					layer.url = buildGeoserverWmtsUrl(layer.workspace, layer.layerName);
					break;
				default:
					console.warn(`图层 ${layer.id} 使用不支持的协议 ${layer.protocol}，无法自动构建URL`);
			}
			
			if (layer.url) {
				console.log(`为图层 ${layer.id} 自动构建URL: ${layer.url}`);
			}
		},
		
		/**
		 * 替换图层URL中的Geoserver地址为环境配置的地址
		 * @param layer 图层配置
		 */
		replaceGeoserverUrl(layer: MapLayer): void {
			if (!layer.url) return;
			
			try {
				const url = new URL(layer.url);
				const currentHost = url.hostname;
				const currentPort = url.port;
				
				// 检查是否是本地Geoserver地址
				if (currentHost === '127.0.0.1' || currentHost === 'localhost') {
					// 获取环境配置的Geoserver地址
					const envHost = import.meta.env.VITE_GEOSERVER_HOST || '127.0.0.1';
					const envPort = import.meta.env.VITE_GEOSERVER_PORT || '8083';
					
					// 总是使用完整URL，不再判断是否为本地环境
					url.hostname = envHost;
					url.port = envPort;
					layer.url = url.toString();
					console.log(`替换图层 ${layer.id} 的URL: ${layer.url}`);
				}
			} catch (error) {
				console.warn(`替换图层 ${layer.id} URL失败:`, error);
			}
		},
		
		/**
		 * 检测并设置WFS协议
		 * 分析URL判断是否是WFS服务，如果是则将协议设置为WFS
		 * 
		 * @param layer 图层配置
		 */
		detectAndSetWfsProtocol(layer: MapLayer): void {
			// 仅处理矢量图层且未明确指定为GeoJSON或MVT协议的情况
			if (layer.type !== 'vector' || 
				(layer.protocol && ['GeoJSON', 'MVT'].includes(layer.protocol as string))) {
				return;
			}
			
			try {
				// 尝试解析URL
				const url = new URL(layer.url);
				const params = url.searchParams;
				
				// 检查是否包含WFS服务的典型参数
				const service = params.get('service');
				const request = params.get('request');
				const typeName = params.get('typeName');
				const outputFormat = params.get('outputFormat');
				
				// 判断是否是WFS请求
				const isWfs = service?.toUpperCase() === 'WFS' || 
					request?.toUpperCase() === 'GETFEATURE' ||
					(typeName && outputFormat && outputFormat.includes('json'));
				
				if (isWfs) {
					console.log(`检测到WFS服务: ${layer.id}, 将协议设置为WFS`);
					layer.protocol = 'WFS';
				}
			} catch (error) {
				console.warn(`解析图层 ${layer.id} URL失败，将使用默认协议:`, error);
			}
		},
		
		/**
		 * 初始化地图
		 * 将OLMap实例与图层管理器关联，并加载初始图层
		 * 
		 * 该方法是图层管理器的核心初始化方法，它执行以下操作：
		 * 1. 验证传入的OLMap实例是否有效
		 * 2. 存储OLMap实例和其内部的OpenLayers Map对象
		 * 3. 检查Map对象的关键方法是否可用
		 * 4. 如果地图配置已加载，则调用loadInitialLayers加载初始图层
		 * 
		 * @param map OLMap实例，包含OpenLayers的Map对象
		 */
		initializeMap(map: OLMap) {
			if (!map) {
				console.error('初始化地图失败：传入的OLMap实例为空');
				return;
			}
			
			if (!map.map) {
				console.error('初始化地图失败：OLMap实例中的map对象为空');
				return;
			}
			
			console.log('初始化地图管理器...');
			
			this.activeMap = map;
			this.olMap = map.map; // 存储OpenLayers的Map实例
			
			console.log('OpenLayers Map对象状态检查:', {
				map实例: !!this.olMap,
				map类型: typeof this.olMap,
				addLayer方法: typeof this.olMap.addLayer,
				getView方法: typeof this.olMap.getView
			});
			
			if (this.mapConfig) {
				this.loadInitialLayers();
			} else {
				console.warn('地图配置尚未加载，暂不加载初始图层');
			}
		},
		
		/**
		 * 加载配置并初始化地图
		 * @param map OLMap实例
		 * @returns 是否成功
		 */
		async loadMapConfigAndInitialize(map: OLMap): Promise<boolean> {
			const success = await this.loadConfig();
			if (success) {
				this.initializeMap(map);
				// 输出图层信息帮助调试
				this.printMapLayersInfo();
				return true;
			}
			return false;
		},
		
		/**
		 * 打印地图图层信息，帮助调试
		 */
		printMapLayersInfo() {
			if (!this.mapConfig || !this.mapConfig.layers) {
				console.warn('没有加载地图配置或配置中没有图层');
				return;
			}
			
			console.log('%c当前地图配置信息', 'color: blue; font-weight: bold');
			console.log(`总共 ${this.mapConfig.layers.length} 个图层配置：`);
			
			this.mapConfig.layers.forEach((layer, index) => {
				console.log(`%c图层 ${index + 1}: ${layer.name} (${layer.id})`, 'color: green; font-weight: bold');
				console.log(`- 类型: ${layer.type}`);
				console.log(`- 协议: ${layer.protocol}`);
				console.log(`- URL: ${layer.url}`);
				console.log(`- 初始加载: ${layer.initialLoad}`);
				if (layer.layers) {
					console.log(`- 图层名称: ${layer.layers}`);
				}
			});
			
			console.log(`%c已加载的图层: ${this.loadedLayers.join(', ')}`, 'color: blue');
		},
		
		/**
		 * 加载初始图层
		 * 根据配置文件中的initialLoad属性，加载标记为初始加载的图层
		 * 
		 * 该方法执行以下操作：
		 * 1. 检查地图配置和OpenLayers Map实例是否已初始化
		 * 2. 筛选出配置中标记为initialLoad=true的图层
		 * 3. 使用Promise.all并行加载所有初始图层
		 * 4. 统计加载成功的图层数量并输出日志
		 * 
		 * 初始图层通常是基础底图或必须在应用启动时显示的图层
		 */
		loadInitialLayers() {
			if (!this.mapConfig) {
				console.warn('图层配置未加载，无法加载初始图层');
				return;
			}
			
			if (!this.olMap) {
				console.error('OpenLayers地图未初始化，无法加载初始图层');
				return;
			}
			
			console.log('加载初始图层...');
			console.log(`OpenLayers Map实例状态:`, {
				已初始化: !!this.olMap,
				类型: this.olMap ? typeof this.olMap : 'null',
				有无addLayer方法: this.olMap && typeof this.olMap.addLayer === 'function'
			});
			
			// 使用Promise.all等待所有图层加载完成
			const promises = this.mapConfig.layers
				.filter(layer => layer.initialLoad)
				.map(layer => this.addLayer(layer.id));
			
			Promise.all(promises)
				.then(results => {
					const successCount = results.filter(Boolean).length;
					console.log(`初始图层加载完成，共${results.length}个图层，成功加载${successCount}个`);
				})
				.catch(error => {
					console.error('加载初始图层过程中发生错误:', error);
				});
		},
		
		/**
		 * 为WMS图层设置样式
		 * @param layerConfig 图层配置
		 * @param options WMS图层选项
		 */
		async setWmsLayerStyle(layerConfig: MapLayer, options: any): Promise<void> {
			try {
				// 检查是否有defaultStyle配置
				if (layerConfig.defaultStyle) {
					// 如果defaultStyle为"Random"，使用随机样式API
					if (layerConfig.defaultStyle === 'Random') {
						const randomStyle = await this.getRandomStyleFromApi();
						if (randomStyle) {
							options.params.STYLES = randomStyle;
							console.log(`为图层 ${layerConfig.id} 设置API随机样式: ${randomStyle}`);
						} else {
							// API调用失败，使用本地随机样式
							const localRandomStyle = this.getLocalRandomStyle();
							options.params.STYLES = localRandomStyle;
							console.log(`API调用失败，为图层 ${layerConfig.id} 设置本地随机样式: ${localRandomStyle}`);
						}
					} else {
						// 使用配置文件中指定的样式
						options.params.STYLES = layerConfig.defaultStyle;
						console.log(`为图层 ${layerConfig.id} 设置配置样式: ${layerConfig.defaultStyle}`);
					}
				} else {
					// 没有defaultStyle配置，使用随机样式API
					const randomStyle = await this.getRandomStyleFromApi();
					if (randomStyle) {
						options.params.STYLES = randomStyle;
						console.log(`为图层 ${layerConfig.id} 设置API随机样式: ${randomStyle}`);
					} else {
						// API调用失败，使用本地随机样式
						const localRandomStyle = this.getLocalRandomStyle();
						options.params.STYLES = localRandomStyle;
						console.log(`API调用失败，为图层 ${layerConfig.id} 设置本地随机样式: ${localRandomStyle}`);
					}
				}
			} catch (error) {
				console.error(`为图层 ${layerConfig.id} 设置样式失败:`, error);
				// 出错时使用本地随机样式作为后备
				const localRandomStyle = this.getLocalRandomStyle();
				options.params.STYLES = localRandomStyle;
				console.log(`出错后为图层 ${layerConfig.id} 设置本地随机样式: ${localRandomStyle}`);
			}
		},

		/**
		 * 从API获取随机样式
		 * @returns 随机样式名称
		 */
		async getRandomStyleFromApi(): Promise<string | null> {
			try {
				const response = await fetch('/api/map/random-style');
				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}
				const data = await response.json();

				if (data.status === 'success' && data.data && data.data.randomStyle && data.data.randomStyle.styleName) {
					return data.data.randomStyle.styleName;
				} else {
					console.warn('API返回的数据格式不正确:', data);
					return null;
				}
			} catch (error) {
				console.error('调用随机样式API失败:', error);
				return null;
			}
		},

		/**
		 * 获取本地随机样式（作为后备方案）
		 * @returns 本地随机样式名称
		 */
		getLocalRandomStyle(): string {
			const styleNames = ["Black", "Blue", "Brown", "Chartreuse", "Coral", "Cyan", "Gold", "Gray", "Green", "Lime", "Magenta", "Navy", "Olive", "Orange", "Pink", "Purple", "Red", "Teal", "Tomato", "White", "Yellow"];
			return styleNames[Math.floor(Math.random() * styleNames.length)];
		},

		/**
		 * 创建WMS图层
		 * @param layerConfig 图层配置
		 * @returns 创建的图层
		 */
		async createWmsLayer(layerConfig: MapLayer): Promise<any> {
			if (!this.olMap) {
				console.error('无法创建WMS图层，地图实例不存在');
				return null;
			}
			
			try {
				// 使用GeoserverLayer工具类创建WMS图层
				if (layerConfig.workspace && layerConfig.layerName) {
					// 如果有工作区和图层名，使用新方法创建
					const options = {
						params: layerConfig.parameters || {},
						visible: layerConfig.active !== false,
						opacity: layerConfig.opacity !== undefined ? layerConfig.opacity : 1,
						zIndex: layerConfig.zIndex || 1
					};

					// 如果图层配置中有extent属性，添加到params.bbox中
					if (layerConfig.extent) {
						options.params.bbox = layerConfig.extent;
						console.log(`为图层 ${layerConfig.id} 设置bbox:`, options.params.bbox);
					}

					// 为WMS图层设置样式
					await this.setWmsLayerStyle(layerConfig, options);

					// 创建WMS图层
					const wmsLayer = GeoserverLayer.createWmsLayer(
						layerConfig.workspace,
						layerConfig.layerName,
						options
					);
					
					// 将图层实例的extent属性复制到图层配置对象中
					if (wmsLayer && (wmsLayer as any).extent && !layerConfig.extent) {
						layerConfig.extent = (wmsLayer as any).extent;
						console.log(`从WMS图层实例获取并保存范围信息到图层配置:`, layerConfig.extent);
					}
					
					return wmsLayer;
				} else {
					// 传统方式创建
					const wmsParams: any = {
						'LAYERS': layerConfig.layers || '',
						'TILED': true,
						...layerConfig.parameters
					};

					// 为传统方式也设置样式
					if (layerConfig.defaultStyle) {
						if (layerConfig.defaultStyle === 'Ramdom' || layerConfig.defaultStyle === 'Random') {
							const randomStyle = await this.getRandomStyleFromApi();
							if (randomStyle) {
								wmsParams.STYLES = randomStyle;
								console.log(`为传统WMS图层 ${layerConfig.id} 设置API随机样式: ${randomStyle}`);
							} else {
								const localRandomStyle = this.getLocalRandomStyle();
								wmsParams.STYLES = localRandomStyle;
								console.log(`API调用失败，为传统WMS图层 ${layerConfig.id} 设置本地随机样式: ${localRandomStyle}`);
							}
						} else {
							wmsParams.STYLES = layerConfig.defaultStyle;
							console.log(`为传统WMS图层 ${layerConfig.id} 设置配置样式: ${layerConfig.defaultStyle}`);
						}
					} else {
						const randomStyle = await this.getRandomStyleFromApi();
						if (randomStyle) {
							wmsParams.STYLES = randomStyle;
							console.log(`为传统WMS图层 ${layerConfig.id} 设置API随机样式: ${randomStyle}`);
						} else {
							const localRandomStyle = this.getLocalRandomStyle();
							wmsParams.STYLES = localRandomStyle;
							console.log(`API调用失败，为传统WMS图层 ${layerConfig.id} 设置本地随机样式: ${localRandomStyle}`);
						}
					}

					const wmsSource = new TileWMS({
						url: layerConfig.url || '',
						params: wmsParams,
						serverType: 'geoserver',
						crossOrigin: 'anonymous',
						transition: 0
					});

					const layer = new TileLayer({
						source: wmsSource,
						visible: layerConfig.active !== false,
						opacity: layerConfig.opacity !== undefined ? layerConfig.opacity : 1,
						zIndex: layerConfig.zIndex || 1
					});
					
					// 尝试保存范围信息
					if (layerConfig.extent) {
						(layer as any).extent = layerConfig.extent;
					} else {
						// 设置一个默认的中国范围作为初始值
						const defaultExtent = [7000000, 2000000, 14000000, 6000000]; // 中国大致范围的Web墨卡托坐标
						(layer as any).extent = defaultExtent;
						layerConfig.extent = defaultExtent;
					}
					
					return layer;
				}
			} catch (error) {
				console.error(`创建WMS图层 ${layerConfig.id} 失败:`, error);
				return null;
			}
		},
		
		/**
		 * 创建XYZ图层
		 * @param layerConfig 图层配置
		 * @returns 创建的图层
		 */
		createXyzLayer(layerConfig: MapLayer): any {
			return new TileLayer({
				source: new XYZ({ url: layerConfig.url }),
				visible: true,
				zIndex: 0
			});
		},
		
		/**
		 * 创建矢量图层
		 * @param layerConfig 图层配置
		 * @returns 图层实例的Promise
		 */
		async createVectorLayer(layerConfig: MapLayer): Promise<any> {
			if (!this.olMap) {
				console.error('无法创建矢量图层，地图实例不存在');
				return null;
			}
			
			try {
				// 根据协议创建不同类型的矢量图层
				switch (layerConfig.protocol) {
					case 'WFS':
						// 使用GeoserverLayer工具类创建WFS图层
						if (layerConfig.workspace && layerConfig.layerName) {
							// 创建图层样式
							const style = this.createFeatureStyle(layerConfig);
							
							// 如果有工作区和图层名，使用新方法创建
							const options = {
								params: layerConfig.parameters || {},
								visible: layerConfig.active !== false,
								opacity: (layerConfig as any).opacity !== undefined ? (layerConfig as any).opacity : 1,
								zIndex: (layerConfig as any).zIndex || 2,
								style: style
							};
							
							return GeoserverLayer.createWfsLayer(
								layerConfig.workspace, 
								layerConfig.layerName, 
								options
							);
						} else {
					return this.createWfsVectorLayer(layerConfig);
				}
					case 'GeoJSON':
						// 创建GeoJSON矢量图层
				const vectorSource = new VectorSource({
							url: layerConfig.url || '',
							format: new GeoJSON()
						});
						
						// 创建图层样式
						const style = this.createFeatureStyle(layerConfig);
						
						return new VectorLayer({
					source: vectorSource,
							style: style,
							visible: layerConfig.active !== false,
							opacity: (layerConfig as any).opacity !== undefined ? (layerConfig as any).opacity : 1,
							zIndex: (layerConfig as any).zIndex || 2
						});
					case 'MVT':
						return this.createMvtLayer(layerConfig);
					default:
						console.warn(`不支持的矢量图层协议: ${layerConfig.protocol}`);
						return null;
				}
			} catch (error) {
				console.error(`创建矢量图层 ${layerConfig.id} 失败:`, error);
				return null;
			}
		},
		
		/**
		 * 创建WFS矢量图层
		 * @param layerConfig 图层配置
		 * @returns 图层实例的Promise
		 */
		async createWfsVectorLayer(layerConfig: MapLayer): Promise<any> {
			if (!this.olMap) {
				console.error('无法创建WFS矢量图层，地图实例不存在');
				return null;
			}
			
			try {
				// 使用GeoserverLayer工具类创建WFS图层
				if (layerConfig.workspace && layerConfig.layerName) {
					// 创建图层样式
					const style = this.createFeatureStyle(layerConfig);
					
					// 如果有工作区和图层名，使用新方法创建
					const options = {
						params: {
							...layerConfig.parameters,
							outputFormat: 'application/json' // 确保使用JSON格式
						},
						visible: layerConfig.active !== false,
						opacity: (layerConfig as any).opacity !== undefined ? (layerConfig as any).opacity : 1,
						zIndex: (layerConfig as any).zIndex !== undefined ? (layerConfig as any).zIndex : 2,
						style: style
					};
					
					console.log(`使用GeoserverLayer创建WFS图层: ${layerConfig.workspace}:${layerConfig.layerName}`);
					return GeoserverLayer.createWfsLayer(
						layerConfig.workspace, 
						layerConfig.layerName, 
						options
					);
				} else {
					// 传统方式创建
					console.log(`使用传统方式创建WFS图层: ${layerConfig.id}, URL: ${layerConfig.url || ''}`);
					
					// 确保URL存在
					if (!layerConfig.url) {
						console.error(`图层 ${layerConfig.id} 没有提供URL`);
						return null;
					}
					
					// 创建向量源
				const vectorSource = new VectorSource({
						format: new GeoJSON({
							// 添加数据投影定义，指定从服务器返回的数据使用EPSG:4326坐标系
							dataProjection: 'EPSG:4326',
							// 指定要素在地图上显示时使用的投影
							featureProjection: 'EPSG:3857'
						}),
						url: layerConfig.url,
					strategy: bboxStrategy
				});
				
					// 添加错误处理事件
					vectorSource.on('featuresloaderror', function(event) {
						console.error(`图层 ${layerConfig.id} WFS数据加载失败:`, event);
					});
					
					// 创建图层样式
					const style = this.createFeatureStyle(layerConfig);
					
					return new VectorLayer({
						source: vectorSource,
						style: style,
						visible: layerConfig.active !== false,
						opacity: (layerConfig as any).opacity !== undefined ? (layerConfig as any).opacity : 1,
						zIndex: (layerConfig as any).zIndex !== undefined ? (layerConfig as any).zIndex : 2
					});
				}
			} catch (error) {
				console.error(`创建WFS矢量图层 ${layerConfig.id} 失败:`, error);
				return null;
			}
		},
		
		/**
		 * 创建MVT图层
		 * @param layerConfig 图层配置
		 * @returns 创建的图层
		 */
		createMvtLayer(layerConfig: MapLayer): any {
			const source = new VectorTileSource({
				format: new MVT(),
				url: layerConfig.url || ''
			});
			
			return new VectorTileLayer({
				source,
				style: this.createFeatureStyle(layerConfig),
				visible: layerConfig.active !== false,
				opacity: (layerConfig as any).opacity !== undefined ? (layerConfig as any).opacity : 1,
				zIndex: (layerConfig as any).zIndex || 3
			});
		},
		
		/**
		 * 检测特征的几何类型
		 * @param feature 要素
		 * @returns 几何类型
		 */
		detectGeometryType(feature: Feature): string {
			const geometry = feature.getGeometry();
			if (!geometry) return 'Unknown';
			
			// 获取几何类型
			const geometryType = geometry.getType();
			return geometryType;
		},
		
		/**
		 * 创建要素样式
		 * @param layerConfig 图层配置
		 * @returns 样式函数
		 */
		createFeatureStyle(layerConfig: MapLayer): any {
			// 获取默认样式
			let defaultStyle: LayerStyle = {
				color: '#3388ff',
				weight: 2,
				opacity: 1,
				fillColor: '#3388ff',
				fillOpacity: 0.2
			};
			
			// 处理默认样式
			if (layerConfig.defaultStyle) {
				if (typeof layerConfig.defaultStyle === 'string' && this.styleLibrary[layerConfig.defaultStyle]) {
					defaultStyle = this.styleLibrary[layerConfig.defaultStyle];
				} else if (typeof layerConfig.defaultStyle !== 'string') {
					defaultStyle = layerConfig.defaultStyle;
				}
			}
			
			// 处理随机颜色样式
			if ((typeof layerConfig.defaultStyle === 'string' && layerConfig.defaultStyle === 'random_mvt') || 
				(typeof defaultStyle === 'object' && 
				 (defaultStyle.color === '#random' || defaultStyle.fillColor === '#random' || 
				  defaultStyle.fillColor === '#random88'))) {
				
				// 生成随机颜色
				const randomColor = generateRandomColor();
				const darkerColor = getDarkerColor(randomColor);
				
				// 应用随机颜色
				defaultStyle = {
					color: colorToHex(darkerColor),
					weight: 2,
					opacity: 1.0,
					fillColor: colorToRgba({...randomColor, a: 0.5})
				};
				
				console.log(`为图层 ${layerConfig.id} 生成随机颜色样式:`, defaultStyle);
			}
			
			// 获取图层的几何类型
			const geometryType = layerConfig.geometryType;
			console.log(`图层 ${layerConfig.id} 的几何类型是: ${geometryType || '未指定'}`);
			
			// 创建样式函数
			return (feature: Feature, resolution: number) => {
				let styleToUse = defaultStyle;
				
				// 首先使用配置中的几何类型，如果没有则自动检测
				const detectedGeometryType = this.detectGeometryType(feature);
				const effectiveGeometryType = geometryType || detectedGeometryType;
				console.log(`特征的实际几何类型: ${detectedGeometryType}, 使用类型: ${effectiveGeometryType}`);
				
				// 如果存在样式规则，优先应用样式规则
				if (layerConfig.styleRules && layerConfig.styleRules.length > 0) {
					// 寻找匹配的样式规则
					for (const rule of layerConfig.styleRules) {
						if (this.evaluateFilter(feature, rule.filter)) {
							if (typeof rule.style === 'string' && this.styleLibrary[rule.style]) {
								styleToUse = this.styleLibrary[rule.style];
								console.log(`应用样式库规则样式: ${rule.style}`, styleToUse);
							} else if (typeof rule.style !== 'string') {
								styleToUse = rule.style;
								console.log(`应用内联规则样式`, styleToUse);
							}
							break; // 找到第一个匹配的规则后停止
						}
					}
				}
				
				// 处理填充颜色 - 可能是带透明度的十六进制色值
				let fillColor = styleToUse.fillColor || 'rgba(51, 136, 255, 0.2)';
				let fillOpacity = styleToUse.fillOpacity;
				
				if (fillColor.startsWith('#') && fillColor.length === 9) {
					// 处理带透明度的十六进制色值 #RRGGBBAA
					fillOpacity = parseInt(fillColor.slice(7, 9), 16) / 255;
					fillColor = fillColor.slice(0, 7);
				}
				
				// 处理线条颜色
				let strokeColor = styleToUse.color || '#3388ff';
				let strokeOpacity = styleToUse.opacity || 1;
				
				if (strokeColor.startsWith('#') && strokeColor.length === 9) {
					// 处理带透明度的十六进制色值 #RRGGBBAA
					strokeOpacity = parseInt(strokeColor.slice(7, 9), 16) / 255;
					strokeColor = strokeColor.slice(0, 7);
				}
				
				// 准备描边样式
				const stroke = new Stroke({
					color: strokeOpacity < 1 ? 
						`rgba(${parseInt(strokeColor.slice(1, 3), 16)}, ${parseInt(strokeColor.slice(3, 5), 16)}, ${parseInt(strokeColor.slice(5, 7), 16)}, ${strokeOpacity})` : 
						strokeColor,
					width: styleToUse.weight || 2,
					lineCap: 'round',
					lineJoin: 'round'
				});

				// 准备填充样式
				const fill = new Fill({
					color: fillOpacity !== undefined ? 
						`rgba(${parseInt(fillColor.slice(1, 3), 16)}, ${parseInt(fillColor.slice(3, 5), 16)}, ${parseInt(fillColor.slice(5, 7), 16)}, ${fillOpacity})` : 
						fillColor
				});
				
				// 处理标签文本
				let labelText = '';
				let labelStyle = null;
				
				// 获取标签显示的分辨率范围
				// 默认最大分辨率(放大到什么程度才显示，数值越小需要放大程度越高)
				const maxLabelResolution = layerConfig.labelMaxResolution !== undefined ? 
					layerConfig.labelMaxResolution : 20;
				
				// 默认最小分辨率(缩小到什么程度就不显示，数值越大视野越广阔)
				const minLabelResolution = layerConfig.labelMinResolution !== undefined ?
					layerConfig.labelMinResolution : 0;
				
				console.log("maxLabelResolution",maxLabelResolution)
				console.log("minLabelResolution",minLabelResolution)
				console.log("resolution",resolution)
				// 只有当分辨率在指定范围内时才处理标签
				// 即：minLabelResolution < resolution <= maxLabelResolution
				if (resolution <= maxLabelResolution && resolution > minLabelResolution) {
					// 检查是否配置了显示标签以及标签字段
					if (layerConfig.showLabels && layerConfig.labelField) {
						// 从要素属性中获取标签文本
						const properties = feature.getProperties();
						if (properties[layerConfig.labelField]) {
							labelText = String(properties[layerConfig.labelField]);
							
							// 创建标签样式
							if (labelText) {
								// 使用配置的标签样式或默认样式
								const labelStyleConfig = layerConfig.labelStyle || {
									color: '#000000',
									fontSize: 12
								};
								
								labelStyle = new Style({
									text: new Text({
										text: labelText,
										font: `${labelStyleConfig.fontSize || 12}px sans-serif`,
										fill: new Fill({
											color: labelStyleConfig.color || '#000000'
										}),
										stroke: new Stroke({
											color: '#FFFFFF',
											width: 3
										}),
										offsetY: 15 // 将文本向下偏移
									})
								});
							}
						}
					}
					// 如果没有启用标签，尝试使用样式中的label配置
					else if (styleToUse.label) {
						// 如果标签是字符串，直接使用
						if (typeof styleToUse.label === 'string') {
							labelText = styleToUse.label;
						} 
						// 如果标签是对象，按类型处理
						else if (typeof styleToUse.label === 'object' && styleToUse.label.text) {
							// 可以是属性名，例如 ${name}
							if (styleToUse.label.text.includes('${')) {
								const propRegex = /\${([^}]+)}/g;
								labelText = styleToUse.label.text.replace(propRegex, (_, propName) => {
									return feature.get(propName) || '';
								});
							} else {
								labelText = styleToUse.label.text;
							}
							
							// 创建标签样式
							if (labelText) {
								labelStyle = new Style({
									text: new Text({
										text: labelText,
										font: styleToUse.label.font || '12px sans-serif',
										fill: new Fill({
											color: styleToUse.label.fill?.color || '#000000'
										}),
										stroke: styleToUse.label.stroke ? new Stroke({
											color: styleToUse.label.stroke.color || '#FFFFFF',
											width: styleToUse.label.stroke.width || 3
										}) : undefined,
										offsetY: 15 // 将文本向下偏移
									})
								});
							}
						}
					} else {
						// 尝试使用点的name属性作为标签
						const properties = feature.getProperties();
						if (effectiveGeometryType.includes('Point') && (properties.name || properties.NAME)) {
							labelText = properties.name || properties.NAME;
							
							labelStyle = new Style({
								text: new Text({
									text: labelText,
									font: '12px sans-serif',
									fill: new Fill({
										color: '#000000'
									}),
									stroke: new Stroke({
										color: '#FFFFFF',
										width: 3
									}),
									offsetY: 15
								})
							});
						}
					}
				}
				
				// 定义基本样式
				let mainStyle;
				
				if (effectiveGeometryType === 'Point' || effectiveGeometryType === 'MultiPoint') {
					// 点样式
					const pointRadius = styleToUse.weight ? styleToUse.weight * 2 : 6;
					
					if (styleToUse.image && styleToUse.image.src) {
						// 图标样式
						mainStyle = new Style({
							image: new Icon({
								src: styleToUse.image.src,
								scale: styleToUse.image.scale || 1,
								anchor: styleToUse.image.anchor || [0.5, 0.5],
								rotation: styleToUse.image.rotation || 0
							})
						});
					} else {
						// 圆圈样式
						mainStyle = new Style({
							image: new Circle({
								radius: pointRadius,
								fill: fill,
								stroke: stroke
							})
						});
					}
				} else if (effectiveGeometryType === 'LineString' || effectiveGeometryType === 'MultiLineString') {
					// 线样式
					mainStyle = new Style({
						stroke: stroke
					});
				} else {
					// 面或默认样式
					mainStyle = new Style({
						stroke: stroke,
						fill: fill
					});
				}
				
				// 如果有标签，返回样式数组
				if (labelStyle) {
					return [mainStyle, labelStyle];
				}
				
				// 否则返回单个样式
				return mainStyle;
			};
		},
		
		/**
		 * 评估过滤条件
		 * @param feature 要素
		 * @param filter 过滤条件
		 * @returns 是否满足条件
		 */
		evaluateFilter(feature: Feature, filter: any): boolean {
			const properties = feature.getProperties();
			const value = properties[filter.property];
			
			// 在Debug模式下记录属性和比较过程
			console.log(`评估过滤条件: 属性=${filter.property}, 操作符=${filter.operator}, 值=${filter.value}, 特征值=${value}`);
			
			// 处理值类型不匹配的情况
			let filterValue = filter.value;
			let propValue = value;
			
			// 尝试转换数字
			if (!isNaN(Number(filterValue)) && typeof propValue === 'string') {
				propValue = Number(propValue);
			} else if (!isNaN(Number(propValue)) && typeof filterValue === 'string') {
				filterValue = Number(filterValue);
			}
			
			let result = false;
			
			switch (filter.operator) {
				case '=': 
					result = propValue === filterValue;
					break;
				case '!=': 
					result = propValue !== filterValue;
					break;
				case '>': 
					result = propValue > filterValue;
					break;
				case '<': 
					result = propValue < filterValue;
					break;
				case '>=': 
					result = propValue >= filterValue;
					break;
				case '<=': 
					result = propValue <= filterValue;
					break;
				case 'in': 
					result = Array.isArray(filterValue) && filterValue.includes(propValue);
					break;
				default: 
					result = false;
			}
			
			console.log(`过滤结果: ${result}`);
			return result;
		},
		
		/**
		 * 添加图层
		 * @param layerId 图层ID
		 * @returns 是否成功添加的Promise
		 */
		async addLayer(layerId: string): Promise<boolean> {
			if (!this.olMap) {
				console.error('地图未初始化，无法添加图层');
				return false;
			}
			
			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig) {
				console.error(`图层 ${layerId} 不存在`);
				return false;
			}
			
			// 检查图层是否已加载
			if (this.isLayerLoaded(layerId)) {
				console.warn(`图层 ${layerId} 已经加载，跳过重复加载`);
				return true;
			}
			
			// 检查图层实例是否存在但状态不一致
			if (layerConfig.layerInstance) {
				console.warn(`图层 ${layerId} 存在实例但未被标记为已加载，清理现有实例...`);
				try {
					// 尝试从地图中移除可能存在的实例
					if (this.olMap) {
						this.olMap.getLayers().forEach((layer) => {
							// 尝试通过比较可能存在的自定义属性或实例引用来识别图层
							if (layer === layerConfig.layerInstance && this.olMap) {
								this.olMap.removeLayer(layer);
								console.log(`已从地图中移除图层 ${layerId} 的残留实例`);
							}
						});
					}
				} catch (e) {
					console.error(`清理图层 ${layerId} 的残留实例失败:`, e);
				}
				
				// 重置图层实例
				layerConfig.layerInstance = null;
			}
			
			try {
				let olLayer = null;
				
				if (layerConfig.type === 'raster') {
					if (layerConfig.protocol === 'WMS') {
						olLayer = await this.createWmsLayer(layerConfig);
					} else if (layerConfig.protocol === 'XYZ') {
						olLayer = this.createXyzLayer(layerConfig);
					} else if (layerConfig.protocol === 'WMTS') {
						olLayer = this.createWmtsLayer(layerConfig);
					}
				} else if (layerConfig.type === 'vector') {
					if (layerConfig.protocol === 'GeoJSON') {
						olLayer = await this.createVectorLayer(layerConfig);
					} else if (layerConfig.protocol === 'MVT') {
						olLayer = this.createMvtLayer(layerConfig);
					} else if (layerConfig.protocol === 'WFS') {
						// WFS协议也使用createVectorLayer方法处理，因为WFS返回的是GeoJSON格式的数据
						console.log(`处理WFS协议图层 ${layerConfig.id}，URL: ${layerConfig.url}`);
						olLayer = await this.createVectorLayer(layerConfig);
					}
				}
				
				if (olLayer) {
					// 设置图层的自定义属性，用于标识
					olLayer.set('layerId', layerId);
					
					// 添加到地图
					this.olMap.addLayer(olLayer);
					layerConfig.layerInstance = olLayer;
					layerConfig.active = true;
					
					// 确保图层是可见的
					this.setLayerVisible(layerId, true);
					
					// 如果图层实例有extent属性但配置对象没有，则同步
					if ((olLayer as any).extent && !layerConfig.extent) {
						layerConfig.extent = (olLayer as any).extent;
						console.log(`从图层实例同步范围信息到图层配置 ${layerId}:`, layerConfig.extent);
					}
					
					// 对于栅格图层，尝试通过API获取边界框
					if (layerConfig.type === 'raster' && layerConfig.workspace && layerConfig.layerName) {
						// 异步获取图层边界框，但不等待结果
						this.fetchLayerExtent(layerId).then(extent => {
							if (extent) {
								console.log(`已异步获取图层 ${layerId} 的边界框:`, extent);
								// 如果有范围，更新图层配置
								layerConfig.extent = extent;
							}
						}).catch(error => {
							console.warn(`异步获取图层 ${layerId} 边界框失败:`, error);
						});
					}
					
					// 如果不在已加载图层列表中，添加到列表
					if (!this.loadedLayers.includes(layerId)) {
						this.loadedLayers.push(layerId);
					}
					
					// 如果图层配置了显示标签，则自动启用标签显示
					if (layerConfig.showLabels && layerConfig.labelField) {
						console.log(`图层 ${layerId} 配置了自动显示标签，字段: ${layerConfig.labelField}`);
						// 这里不需要调用toggleLabel，因为createFeatureStyle已经处理了showLabels
					}
					
					console.log(`图层 ${layerId} 已成功添加到地图`);
					return true;
				}
				
				console.error(`创建图层 ${layerId} 失败`);
				return false;
			} catch (error) {
				console.error(`添加图层 ${layerId} 失败:`, error);
				return false;
			}
		},
		
		/**
		 * 设置图层可见性
		 * @param layerId 图层ID
		 * @param visible 是否可见
		 * @returns 是否设置成功
		 */
		setLayerVisible(layerId: string, visible: boolean): boolean {
			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig || !layerConfig.layerInstance) {
				console.error(`图层 ${layerId} 不存在或未加载`);
				return false;
			}
			
			try {
				// 设置图层可见性
				layerConfig.layerInstance.setVisible(visible);
				layerConfig.active = visible;
				
				// 对于WMTS图层，可能需要特殊处理
				if (layerConfig.protocol === 'WMTS') {
					// 强制刷新WMTS图层
					const source = layerConfig.layerInstance.getSource();
					if (source && typeof source.refresh === 'function') {
						source.refresh();
						console.log(`刷新WMTS图层 ${layerId} 的数据源`);
					}
				}
				
				console.log(`图层 ${layerId} 可见性已设置为 ${visible ? '显示' : '隐藏'}`);
				return true;
			} catch (error) {
				console.error(`设置图层 ${layerId} 可见性失败:`, error);
				return false;
			}
		},
		
		/**
		 * 重新加载图层
		 * @param layerId 图层ID
		 * @returns 是否成功重新加载
		 */
		async reloadLayer(layerId: string): Promise<boolean> {
			try {
				// 先移除图层
				const removed = this.removeLayer(layerId);
				if (!removed) {
					console.warn(`图层 ${layerId} 移除失败，无法重新加载`);
					return false;
				}
				
				// 重新添加图层
				const added = await this.addLayer(layerId);
				if (!added) {
					console.error(`图层 ${layerId} 重新添加失败`);
					return false;
				}
				
				// 确保图层是可见的
				this.setLayerVisible(layerId, true);
				
				console.log(`图层 ${layerId} 已成功重新加载`);
				return true;
			} catch (error) {
				console.error(`重新加载图层 ${layerId} 失败:`, error);
				return false;
			}
		},
		
		/**
		 * 切换图层可见性
		 * 控制地图图层的显示或隐藏状态
		 * 
		 * 该方法执行以下操作：
		 * 1. 检查图层配置和图层实例是否存在
		 * 2. 获取当前图层的可见状态
		 * 3. 将可见状态切换为相反值
		 * 4. 更新图层的active属性以反映新状态
		 * 5. 记录日志并返回新的可见状态
		 * 
		 * @param layerId 图层ID
		 * @returns 切换后的可见状态（true表示可见，false表示隐藏），如果图层不存在则返回null
		 */
		toggleLayerVisibility(layerId: string): boolean | null {
			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig || !layerConfig.layerInstance) {
				return null;
			}
			
			try {
				const isVisible = layerConfig.layerInstance.getVisible();
				return this.setLayerVisible(layerId, !isVisible);
			} catch (error) {
				console.error(`切换图层 ${layerId} 可见性失败:`, error);
				return null;
			}
		},
		
		/**
		 * 移除图层
		 * @param layerId 图层ID
		 * @returns 是否成功移除
		 */
		removeLayer(layerId: string): boolean {
			if (!this.olMap) {
				console.error('地图未初始化，无法移除图层');
				return false;
			}
			
			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig) {
				console.error(`图层 ${layerId} 不存在`);
				return false;
			}
			
			if (!this.isLayerLoaded(layerId)) {
				return true;
			}
			
			try {
				if (layerConfig.layerInstance) {
					this.olMap.removeLayer(layerConfig.layerInstance);
					layerConfig.active = false;
					
					// 重要：重置图层实例引用，允许该图层再次被加载
					layerConfig.layerInstance = null;
					
					// 清理所有与该图层相关的状态
					if (layerConfig.type === 'vector' && layerConfig.protocol === 'WFS') {
						// 对于WFS图层，可能需要特别清理
						console.log(`清理WFS图层 ${layerId} 的资源`);
					}
					
					const index = this.loadedLayers.indexOf(layerId);
					if (index > -1) {
						this.loadedLayers.splice(index, 1);
					}
					
					console.log(`图层 ${layerId} 已移除并重置，可以再次加载`);
					return true;
				}
				
				return false;
			} catch (error) {
				console.error(`移除图层 ${layerId} 失败:`, error);
				return false;
			}
		},
		
		/**
		 * 设置图层透明度
		 * 调整地图图层的透明度级别
		 * 
		 * 该方法执行以下操作：
		 * 1. 检查图层配置和图层实例是否存在
		 * 2. 将传入的透明度值限制在0-1范围内
		 * 3. 应用透明度值到图层实例
		 * 4. 记录日志并返回操作结果
		 * 
		 * 透明度值范围从0（完全透明）到1（完全不透明）
		 * 
		 * @param layerId 图层ID
		 * @param opacity 透明度值(0-1)
		 * @returns 是否设置成功
		 */
		setLayerOpacity(layerId: string, opacity: number): boolean {
			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig || !layerConfig.layerInstance) {
				return false;
			}
			
			try {
				layerConfig.layerInstance.setOpacity(Math.max(0, Math.min(1, opacity)));
				console.log(`图层 ${layerId} 的透明度已设置为 ${opacity}`);
				return true;
			} catch (error) {
				console.error(`设置图层 ${layerId} 透明度失败:`, error);
				return false;
			}
		},
		
		/**
		 * 重新排序图层
		 * 根据传入的图层ID数组调整图层的叠加顺序
		 * 
		 * 该方法执行以下操作：
		 * 1. 验证图层ID数组的有效性
		 * 2. 计算每个图层的新zIndex值
		 * 3. 应用新的zIndex到图层实例
		 * 4. 返回成功更新的图层数量
		 * 
		 * 数组中的第一个图层ID将被置于最顶层（最高zIndex）
		 * 
		 * @param layerIds 图层ID数组，按显示顺序排列（第一个为最上层）
		 * @returns 成功更新的图层数量
		 */
		reorderLayers(layerIds: string[]): number {
			if (!this.olMap || !layerIds || layerIds.length === 0) {
				return 0;
			}
			
			let successCount = 0;
			const baseZIndex = 10; // 基础zIndex值
			
			// 从高到低分配zIndex（数组第一个元素获取最高的zIndex）
			layerIds.forEach((layerId, index) => {
				const layerConfig = this.getLayerById(layerId);
				if (layerConfig && layerConfig.layerInstance) {
					try {
						// 计算新的zIndex：基础值 + 反向索引值（确保第一个元素zIndex最大）
						const newZIndex = baseZIndex + (layerIds.length - index);
						layerConfig.layerInstance.setZIndex(newZIndex);
						successCount++;
						console.log(`图层 ${layerId} 的zIndex已设置为 ${newZIndex}`);
					} catch (error) {
						console.error(`设置图层 ${layerId} 的zIndex失败:`, error);
					}
				}
			});
			
			return successCount;
		},
		
		/**
		 * 控制图层标注的显示与隐藏
		 * @param layerId 图层ID
		 * @param fieldName 可选，标注字段名，如果不提供则使用现有配置
		 * @param show 可选，是否显示标注，不提供则切换当前状态
		 * @param labelMinResolution 可选，标签显示的最小分辨率（数值越大，缩小级别越高，越早消失）
		 * @param labelMaxResolution 可选，标签显示的最大分辨率（数值越小，需要放大级别越高才显示）
		 * @returns 标注显示状态或null（操作失败）
		 */
		toggleLabel(layerId: string, fieldName?: string, show?: boolean, labelMinResolution?: number, labelMaxResolution?: number): boolean | null {
			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig || !layerConfig.layerInstance) {
				console.error(`图层 ${layerId} 不存在或未加载`);
				return null;
			}
			
			try {
				// 如果指定了字段名，更新labelField
				if (fieldName) {
					layerConfig.labelField = fieldName;
				}
				
				// 确保labelField存在
				if (!layerConfig.labelField) {
					console.warn(`图层 ${layerId} 未指定标注字段`);
					return false;
				}
				
				// 确定是显示还是隐藏
				const newShowLabels = show !== undefined ? show : !layerConfig.showLabels;
				layerConfig.showLabels = newShowLabels;
				
				// 设置默认标签样式（如果没有）
				if (!layerConfig.labelStyle) {
					layerConfig.labelStyle = {
						color: '#000000',
						fontSize: 12
					};
				}
				
				// 设置标签分辨率范围（如果提供）
				if (labelMinResolution !== undefined) {
					layerConfig.labelMinResolution = labelMinResolution;
				}
				
				if (labelMaxResolution !== undefined) {
					layerConfig.labelMaxResolution = labelMaxResolution;
				}
				
				// 使用统一的createFeatureStyle方法创建样式函数
				const styleFunc = this.createFeatureStyle(layerConfig);
				
				// 应用到图层
				layerConfig.layerInstance.setStyle(styleFunc);
				
				console.log(`图层 ${layerId} 的标注已${newShowLabels ? '显示' : '隐藏'}，字段：${layerConfig.labelField}，分辨率范围：${layerConfig.labelMinResolution || 0} - ${layerConfig.labelMaxResolution || 20}`);
				return newShowLabels;
			} catch (error) {
				console.error(`切换图层 ${layerId} 标注显示失败:`, error);
				return null;
			}
		},
		
		// 获取图层最大的zIndex
		getMaxZIndex(): number {
			if (!this.olMap) {
				return 0;
			}
			
			let maxZIndex = 0;
			
			// 遍历所有已加载的图层
			this.loadedLayers.forEach(layerId => {
				const layer = this.getLayerById(layerId);
				if (layer && layer.layerInstance) {
					const zIndex = layer.layerInstance.getZIndex() || 0;
					maxZIndex = Math.max(maxZIndex, zIndex);
				}
			});
			
			return maxZIndex;
		},
		
		/**
		 * 获取图层的实际范围
		 * @param layerId 图层ID
		 * @returns 图层范围数组 [minx, miny, maxx, maxy] 或 null
		 */
		getLayerExtent(layerId: string): number[] | null {
			const layer = this.getLayerById(layerId);
			if (!layer || !layer.layerInstance) {
				return null;
			}
			
			// 1. 首先检查图层配置中是否有extent属性
			if (layer.extent && layer.extent.length === 4) {
				console.log(`从图层配置中获取范围 ${layerId}:`, layer.extent);
				return layer.extent;
			}
			
			// 2. 检查图层实例是否有extent属性
			if ((layer.layerInstance as any).extent) {
				console.log(`从图层实例中获取范围 ${layerId}:`, (layer.layerInstance as any).extent);
				return (layer.layerInstance as any).extent;
			}
			
			// 3. 尝试从图层源获取范围
			const source = layer.layerInstance.getSource();
			if (source) {
				// 对于矢量图层，尝试从要素获取范围
				if (layer.type === 'vector' && typeof source.getFeatures === 'function') {
					const features = source.getFeatures();
					if (features && features.length > 0) {
						const extent = source.getExtent();
						if (extent && !extent.some((val: number) => !isFinite(val))) {
							console.log(`从矢量图层要素获取范围 ${layerId}:`, extent);
							return extent;
						}
					}
				}
				
				// 尝试从源的getExtent方法获取
				if (typeof source.getExtent === 'function') {
					try {
						const extent = source.getExtent();
						if (extent && !extent.some((val: number) => !isFinite(val))) {
							console.log(`从图层源getExtent获取范围 ${layerId}:`, extent);
							return extent;
						}
					} catch (e) {
						console.warn(`从源getExtent获取范围失败 ${layerId}:`, e);
					}
				}
				
				// 对于WMS图层，尝试从参数获取BBOX
				if (layer.protocol === 'WMS' && typeof source.getParams === 'function') {
					const params = source.getParams();
					if (params && params.BBOX) {
						if (typeof params.BBOX === 'string') {
							const extent = params.BBOX.split(',').map(Number);
							if (extent.length === 4 && !extent.some((val: number) => !isFinite(val))) {
								console.log(`从WMS参数BBOX获取范围 ${layerId}:`, extent);
								return extent;
							}
						}
					}
				}
				
				// 尝试从瓦片网格获取
				if (typeof source.getTileGrid === 'function') {
					try {
						const tileGrid = source.getTileGrid();
						if (tileGrid && typeof tileGrid.getExtent === 'function') {
							const extent = tileGrid.getExtent();
							if (extent && !extent.some((val: number) => !isFinite(val))) {
								console.log(`从瓦片网格获取范围 ${layerId}:`, extent);
								return extent;
							}
						}
					} catch (e) {
						console.warn(`从瓦片网格获取范围失败 ${layerId}:`, e);
					}
				}
			}
			
			// 4. 如果是WMS/栅格图层且有workspace和layerName，尝试从API获取bbox信息
			if ((layer.type === 'raster' || layer.protocol === 'WMS') && layer.workspace && layer.layerName) {
				// 这里不直接发起API请求，因为这是同步函数
				// 但是告诉用户可以使用异步的fetchLayerExtent方法
				console.log(`图层 ${layerId} 可使用 fetchLayerExtent 方法从API获取边界框`);
			}
			
			// 5. 根据图层名称提供更精确的默认范围
			if (layer.name.includes('广西') || layer.name.includes('南宁')) {
				// 广西/南宁大致范围
				const extent = [11800000, 2500000, 12500000, 3000000];
				console.log(`使用广西/南宁区域的默认范围 ${layerId}:`, extent);
				return extent;
			}
			
			// 6. 使用较小的默认范围
			const smallerExtent = [11000000, 3000000, 12000000, 4000000]; // 中国某个区域的Web墨卡托坐标
			console.log(`使用较小的默认范围 ${layerId}:`, smallerExtent);
			return smallerExtent;
		},
		
		/**
		 * 从API获取图层范围
		 * 这是一个异步方法，可以从服务器获取图层的精确范围
		 * @param layerId 图层ID
		 * @returns Promise<number[]> 图层范围数组，或者null
		 */
		async fetchLayerExtent(layerId: string, forceRefresh: boolean = false): Promise<number[] | null> {
			const layer = this.getLayerById(layerId);
			if (!layer) {
				console.error(`图层 ${layerId} 不存在`);
				return null;
			}
			
			// 如果已有缓存的extent并且不是强制刷新，直接返回
			if (!forceRefresh && layer.extent && layer.extent.length === 4 && 
				!(layer.extent[0] === -180 && layer.extent[1] === -90 && layer.extent[2] === 180 && layer.extent[3] === 90)) {
				// 如果缓存的不是全球范围，可以使用
				console.log(`使用缓存的图层范围 ${layerId}:`, layer.extent);
				return layer.extent;
			}
			
			// 如果没有workspace或layerName，无法获取
			if (!layer.workspace || !layer.layerName) {
				console.warn(`图层 ${layerId} 缺少workspace或layerName，无法从API获取范围`);
				return this.getLayerExtent(layerId); // 回退到同步方法
			}
			
			try {
				// 导入getLayerBbox函数和坐标转换函数
				const { getLayerBbox } = await import('/@/utils/geoserver');
				const { fromLonLat } = await import('ol/proj');
				
				// 从API获取边界框
				console.log(`正在从API获取图层 ${layerId} (${layer.workspace}:${layer.layerName}) 的边界框...`);
				const bboxData = await getLayerBbox(layer.workspace, layer.layerName);
				
				// 检查返回数据格式
				if (bboxData && bboxData.status === 'success' && bboxData.bbox && bboxData.bbox.latLon) {
					const { minx, miny, maxx, maxy } = bboxData.bbox.latLon;
					
					// 检查获取的边界框是否有效
					if (minx === -180 && miny === -90 && maxx === 180 && maxy === 90) {
						console.warn(`API返回全球范围作为图层 ${layerId} 的边界框，尝试使用备用方法`);
						
						// 如果是全球范围，尝试使用native属性
						if (bboxData.bbox.native && 
							!(bboxData.bbox.native.minx === -180 && 
							  bboxData.bbox.native.miny === -90 && 
							  bboxData.bbox.native.maxx === 180 && 
							  bboxData.bbox.native.maxy === 90)) {
							// 使用native属性
							const nativeBox = bboxData.bbox.native;
							console.log(`使用native边界框：`, nativeBox);
							
							// 转换为OpenLayers可用的范围
							const bottomLeft = fromLonLat([nativeBox.minx, nativeBox.miny]);
							const topRight = fromLonLat([nativeBox.maxx, nativeBox.maxy]);
							
							// 构建extent数组
							const extent = [bottomLeft[0], bottomLeft[1], topRight[0], topRight[1]];
							
							// 存储到图层配置中
							layer.extent = extent;
							console.log(`成功从API native属性获取并转换图层 ${layerId} 边界框:`, extent);
							return extent;
						} else {
							// 如果都是全球范围，则尝试其他方法
							return this.getLayerExtent(layerId);
						}
					}
					
					// 如果不是全球范围，则进行正常转换
					console.log(`API返回的有效边界框: minx=${minx}, miny=${miny}, maxx=${maxx}, maxy=${maxy}`);
					
					// 转换为OpenLayers可用的范围
					const bottomLeft = fromLonLat([minx, miny]);
					const topRight = fromLonLat([maxx, maxy]);
					
					// 构建extent数组
					const extent = [bottomLeft[0], bottomLeft[1], topRight[0], topRight[1]];
					
					// 检查转换后的坐标是否有效
					if (extent.some(val => !isFinite(val))) {
						console.warn(`转换后的坐标包含无效值:`, extent);
						return this.getLayerExtent(layerId);
					}
					
					// 存储到图层配置中
					layer.extent = extent;
					console.log(`成功从API获取并转换图层 ${layerId} 边界框:`, extent);
					return extent;
				} else {
					console.warn(`API返回的边界框数据格式不正确 ${layerId}:`, bboxData);
				}
			} catch (error) {
				console.error(`获取图层 ${layerId} 边界框失败:`, error);
			}
			
			// 如果API获取失败，回退到同步方法
			return this.getLayerExtent(layerId);
		},
		
		/**
		 * 创建WMTS图层
		 * @param layerConfig 图层配置
		 * @returns WMTS图层实例
		 */
		createWmtsLayer(layerConfig: MapLayer): any {
			if (!layerConfig.workspace || !layerConfig.layerName) {
				console.error(`WMTS图层 ${layerConfig.id} 缺少workspace或layerName配置`);
				return null;
			}
			
			try {
				console.log(`创建WMTS图层: ${layerConfig.id}, 工作区: ${layerConfig.workspace}, 图层名: ${layerConfig.layerName}`, layerConfig);
				
				// 设置默认值，不再需要从配置文件中指定
				const tileMatrixSet = layerConfig.parameters?.tileMatrixSet || 'EPSG:4326';
				const format = layerConfig.parameters?.format || 'image/png';
				const style = layerConfig.parameters?.style || '';
				
				const projection = getProjection(tileMatrixSet);
				
				if (!projection) {
					console.error(`无法获取投影系统: ${tileMatrixSet}`);
					return null;
				}
				
				console.log(`WMTS投影系统:`, projection);
				
				// 获取完整的WMTS服务URL (KVP格式)
				const baseUrl = getGeoserverBaseUrl();
				const wmtsEndpoint = `${baseUrl}/gwc/service/wmts`;
				console.log(`WMTS服务端点: ${wmtsEndpoint}`);
				
				// 创建WMTS源，使用KVP编码方式和优先级瓦片加载
				const source = new WMTS({
					url: wmtsEndpoint,
					layer: `${layerConfig.workspace}:${layerConfig.layerName}`,
					matrixSet: tileMatrixSet,
					format: format,
					projection: projection,
					style: style,
					// 关键参数：使用KVP编码而非REST，这样能正确处理参数
					requestEncoding: 'KVP',
					// 使用自定义TileGrid，与指定的坐标系兼容
					tileGrid: this.createWmtsTileGrid(projection, tileMatrixSet),
					// 其他设置
					wrapX: true,
					transition: 0,
					// 使用优先级瓦片加载函数
					tileLoadFunction: priorityTileLoadFunction
				});

				// 添加事件处理
				source.on('tileloaderror', (event: any) => {
					const tile = event.tile;
					const url = tile.src_ || '';
					console.warn(`WMTS图层 ${layerConfig.id} 加载失败:`, url);
				});

				// 监听瓦片加载完成事件
				// 当有瓦片完成时，如果有待重试的瓦片 → 优先重试它们
				source.on('tileloadend', () => {
					console.log(`WMTS图层 ${layerConfig.id} 部分加载成功`);

					if (retryQueue.length > 0) {
						const nextRetry = retryQueue.shift(); // 取一个高优先级瓦片
						console.log(`Retrying high-priority tile: ${nextRetry}`);

						// 刷新数据源 → 会重新加载当前视图可见的所有瓦片
						// 由于该瓦片是可见的，它会马上进入加载队列（优先级较高）
						source.refresh();
					}
				});
				
				// 创建图层
				const layer = new TileLayer({
					preload: Infinity,  // 开启所有级别预加载
					source: source,
					visible: layerConfig.active !== false,
					opacity: layerConfig.opacity !== undefined ? layerConfig.opacity : 1,
					zIndex: layerConfig.zIndex || 0
				});
				
				// 保存元数据
				(layer as any).layerId = layerConfig.id;
				(layer as any).layerType = 'WMTS';
				(layer as any).workspace = layerConfig.workspace;
				(layer as any).layerName = layerConfig.layerName;
				
				// 尝试获取范围信息
				if (layerConfig.extent) {
					(layer as any).extent = layerConfig.extent;
				} else if (projection.getExtent()) {
					(layer as any).extent = projection.getExtent();
					layerConfig.extent = projection.getExtent();
				}
				
				return layer;
			} catch (error) {
				console.error(`创建WMTS图层 ${layerConfig.id} 失败:`, error);
				return null;
			}
		},
		
		/**
		 * 创建WMTS图层的瓦片网格
		 * @param projection 投影
		 * @param gridSetId 网格集ID
		 * @returns 瓦片网格
		 */
		createWmtsTileGrid(projection: any, gridSetId: string): WMTSTileGrid {
			const projectionExtent = projection.getExtent();
			
			// 根据不同的坐标系创建合适的参数
			let origin, resolutions, matrixIds;
			
			if (gridSetId === 'EPSG:4326') {
				// EPSG:4326 特殊处理
				origin = [-180, 90]; // 正确的原点
				
				// 标准的EPSG:4326分辨率
				resolutions = [
					0.703125,              // 层级 0
					0.3515625,             // 层级 1
					0.17578125,            // 层级 2
					0.087890625,           // 层级 3
					0.0439453125,          // 层级 4
					0.02197265625,         // 层级 5
					0.010986328125,        // 层级 6
					0.0054931640625,       // 层级 7
					0.00274658203125,      // 层级 8
					0.001373291015625,     // 层级 9
					0.0006866455078125,    // 层级 10
					0.0003433227539062,    // 层级 11
					0.0001716613769531,    // 层级 12
					0.0000858306884766,    // 层级 13
					0.0000429153442383,    // 层级 14
					0.0000214576721191,    // 层级 15
					0.0000107288360596,    // 层级 16
					0.0000053644180298,    // 层级 17
					0.0000026822090149,    // 层级 18
					0.0000013411045074,    // 层级 19
					0.0000006705522537,    // 层级 20
					0.0000003352761269,    // 层级 21
					0.00000016763806345,   // 层级 22
					0.00000008381903173,   // 层级 23
					0.00000004190951586,   // 层级 24
					0.00000002095475793    // 层级 25
				];
				
				
				// 标准的EPSG:4326 GeoServer矩阵ID
				matrixIds = [];
				for (let i = 0; i < resolutions.length; i++) {
					matrixIds.push(`${gridSetId}:${i}`);
				}
			} else {
				// 默认情况下，使用自动计算的值
				origin = getTopLeft(projectionExtent);
				const size = Math.max(
					projectionExtent[2] - projectionExtent[0],
					projectionExtent[3] - projectionExtent[1]
				);
				const maxResolution = size / 256;
				
				resolutions = [];
				matrixIds = [];
				for (let i = 0; i < 20; i++) {
					resolutions.push(maxResolution / Math.pow(2, i));
					// 使用标准GeoServer格式矩阵ID
					matrixIds.push(`${gridSetId}:${i}`);
				}
			}
			
			console.log(`创建WMTS瓦片网格:`, {
				网格集: gridSetId,
				原点: origin,
				分辨率数: resolutions.length
			});
			
			return new WMTSTileGrid({
				origin: origin,
				resolutions: resolutions,
				matrixIds: matrixIds
			});
		},

		/**
		 * 清理优先级瓦片加载资源
		 * 在图层管理器销毁时调用
		 */
		dispose(): void {
			// 清理所有正在加载的瓦片的超时定时器
			loadingTiles.forEach((timeoutId) => {
				clearTimeout(timeoutId);
			});
			loadingTiles.clear();

			// 清空重试队列
			retryQueue.length = 0;

			console.log('olMapLayer 优先级瓦片加载资源已清理');
		},
	}
});

// 导出工具函数，使其可以在其他地方使用
export { buildGeoserverWfsUrl, buildGeoserverWmsUrl } from '/@/utils/geoserver';