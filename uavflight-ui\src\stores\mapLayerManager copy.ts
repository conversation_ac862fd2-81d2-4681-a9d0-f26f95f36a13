/*
 * @Description: 地图图层管理 Store
 */
import { defineStore } from 'pinia';
import http from '/@/utils/request'; // 使用项目现有的请求模块
import * as Cesium from 'cesium';
import { UrlParser, WfsParams } from '/@/utils/urlParser'; // 导入URL解析工具类和WfsParams接口

// 定义图层类型
interface LayerParameters {
	service?: string;
	format?: string;
	transparent?: boolean;
	[key: string]: any;
}

interface LayerStyle {
	// Cesium点样式
	point?: {
		color?: string; // CSS颜色
		pixelSize?: number;
		outlineColor?: string; // CSS颜色
		outlineWidth?: number;
		show?: boolean;
	};
	// Cesium线样式
	polyline?: {
		material?: {
			color?: string; // CSS颜色
			dashLength?: number; // 虚线长度
			dashPattern?: number; // 虚线模式
		} | string; // 颜色字符串快捷方式
		width?: number;
		clampToGround?: boolean;
		show?: boolean;
	};
	// Cesium面样式
	polygon?: {
		material?: {
			color?: string; // CSS颜色
		} | string; // 颜色字符串快捷方式
		outline?: boolean;
		outlineColor?: string; // CSS颜色
		outlineWidth?: number;
		show?: boolean;
	};
	// Cesium标签样式
	label?: {
		text?: string;
		font?: string;
		style?: string; // 'FILL' | 'OUTLINE' | 'FILL_AND_OUTLINE'
		fillColor?: string; // CSS颜色
		outlineColor?: string; // CSS颜色
		outlineWidth?: number;
		show?: boolean;
		pixelOffset?: [number, number];
		eyeOffset?: [number, number, number];
		horizontalOrigin?: string; // 'LEFT' | 'CENTER' | 'RIGHT'
		verticalOrigin?: string; // 'TOP' | 'CENTER' | 'BASELINE' | 'BOTTOM'
		scale?: number;
	};
	// Cesium广告牌样式
	billboard?: {
		image?: string; // 图片URL
		width?: number;
		height?: number;
		color?: string; // CSS颜色
		scale?: number;
		rotation?: number;
		horizontalOrigin?: string; // 'LEFT' | 'CENTER' | 'RIGHT' 
		verticalOrigin?: string; // 'TOP' | 'CENTER' | 'BASELINE' | 'BOTTOM'
		pixelOffset?: [number, number];
		eyeOffset?: [number, number, number];
		show?: boolean;
	};
}

// 定义过滤条件类型
interface FilterCondition {
	property: string;
	operator: '=' | '!=' | '>' | '<' | '>=' | '<=' | 'between' | 'inRange' | 'in' | 'notIn';
	value: string | number | boolean | string[] | number[] | [number, number];
	min?: number;
	max?: number;
}

// 定义样式规则类型
interface StyleRule {
	filter: FilterCondition;
	style: LayerStyle;
}

interface MapLayer {
	id: string;
	name: string;
	type: 'raster' | 'vector';
	protocol?: 'XYZ' | 'WMS' | 'WMTS' | 'WFS' | 'GeoJSON';
	url: string;
	initialLoad: boolean;
	// 矢量图层的几何类型
	geometryType?: 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon';
	// WMS 特定属性
	layers?: string;
	parameters?: LayerParameters;
	// 其他属性
	defaultStyle?: LayerStyle;
	styleRules?: StyleRule[];
	wfsParams?: WfsParams;
	active?: boolean;
	layerInstance?: any;
	// 事件标识
	event?: string;
}

interface MapConfig {
	layers: MapLayer[];
}

// 导出 Store 的类型定义，以便 earth.ts 可以导入类型
export type MapLayerManagerStore = ReturnType<typeof useMapLayerManagerStore>;

export const useMapLayerManagerStore = defineStore({
	id: 'mapLayerManager',

	state: () => ({
		mapConfig: null as MapConfig | null,
		loadedLayers: [] as string[],
		isLoading: false,
		error: null as string | null,
		activeMap: null as Cesium.Viewer | null,
		// 事件到图层ID的映射
		eventLayers: new Map<string, string[]>(),
	}),

	getters: {
		getLayerById: (state) => (id: string) => {
			return state.mapConfig?.layers.find((layer) => layer.id === id);
		},

		getInitialLayers: (state) => {
			return state.mapConfig?.layers.filter((layer) => layer.initialLoad) || [];
		},

		getLayersByEvent: (state) => (eventName: string) => {
			return state.mapConfig?.layers.filter((layer) => layer.event === eventName) || [];
		},

		isLayerLoaded: (state) => (layerId: string) => {
			return state.loadedLayers.includes(layerId);
		},
	},

	actions: {
		async loadMapConfig() {
			this.isLoading = true;
			this.error = null;

			try {
				// 直接从指定 URL 获取配置
				const response = await fetch('http://127.0.0.1:81/baseMap.json');
				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const data = await response.json();
				this.mapConfig = data;
				console.log('地图配置加载成功:', this.mapConfig);
				
				// 初始化事件图层映射
				this.initEventLayersMap();
				
				return this.mapConfig;
			} catch (err: any) {
				this.error = err.message || '加载地图配置失败';
				console.error('加载地图配置失败:', err);
				return null;
			} finally {
				this.isLoading = false;
			}
		},

		/**
		 * 初始化事件图层映射
		 */
		initEventLayersMap() {
			this.eventLayers.clear();
			
			if (this.mapConfig) {
				// 遍历所有图层，构建事件到图层ID的映射
				this.mapConfig.layers.forEach(layer => {
					if (layer.event) {
						if (!this.eventLayers.has(layer.event)) {
							this.eventLayers.set(layer.event, []);
						}
						this.eventLayers.get(layer.event)?.push(layer.id);
					}
				});
			}
			
			console.log('事件图层映射初始化完成:', this.eventLayers);
		},

		/**
		 * 加载地图配置并初始化地图
		 * @param viewer Cesium.Viewer实例
		 * @returns 是否成功加载并初始化
		 */
		async loadMapConfigAndInitialize(viewer: Cesium.Viewer): Promise<boolean> {
			const config = await this.loadMapConfig();
			if (config) {
				this.initializeMap(viewer);
				return true;
			}
			return false;
		},

		/**
		 * 初始化地图并加载标记为initialLoad的图层
		 * @param viewer Cesium.Viewer实例
		 */
		initializeMap(viewer: Cesium.Viewer) {
			this.activeMap = viewer;

			if (this.mapConfig) {
				// 加载所有initialLoad为true的图层
				const initialLayers = this.getInitialLayers;
				initialLayers.forEach((layer) => {
					this.addLayer(layer.id);
				});
			}
		},

		/**
		 * 添加图层到地图
		 * @param layerId 图层ID
		 * @returns 是否成功添加图层
		 */
		addLayer(layerId: string): boolean {
			if (!this.activeMap) {
				console.error('地图未初始化，无法添加图层');
				return false;
			}

			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig) {
				console.error(`图层 ${layerId} 不存在`);
				return false;
			}

			// 如果图层已加载则跳过
			if (this.isLayerLoaded(layerId)) {
				console.log(`图层 ${layerId} 已加载`);
				return true;
			}

			try {
				// 根据图层类型和协议创建对应的图层
				let cesiumLayer: any = null;

				if (layerConfig.type === 'raster') {
					if (layerConfig.protocol === 'WMS') {
						// 创建WMS图层
						console.log(`创建WMS图层: ${layerConfig.name}`);

						// 解析OpenLayers3格式的WMS URL
						const wmsInfo = UrlParser.parseOLWmsUrl(layerConfig.url);
						console.log(wmsInfo);
						// 在Cesium中添加WMS图层
						const wmsLayer = new Cesium.WebMapServiceImageryProvider({
							url: wmsInfo.baseUrl,
							layers: wmsInfo.parameters.layers,
							parameters: {
								format: 'image/png',
								transparent: true,
								service: 'WMS',
							},
						});

						// 添加到Cesium的图层集合中
						cesiumLayer = this.activeMap.imageryLayers.addImageryProvider(wmsLayer);
					} else if (layerConfig.protocol === 'WMTS') {
						// 创建WMTS图层
						console.log(`创建WMTS图层: ${layerConfig.name}`);

						// 解析WMTS URL
						const wmtsInfo = UrlParser.parseWMTSUrl(layerConfig.url);
						console.log('WMTS解析结果:', wmtsInfo);

						// 创建适当的TilingScheme
						let tilingScheme: Cesium.TilingScheme;

						if (wmtsInfo.tileMatrixSetID === 'EPSG:4326') {
							tilingScheme = new Cesium.GeographicTilingScheme();
						} else {
							// 默认使用WebMercator (EPSG:3857)
							tilingScheme = new Cesium.WebMercatorTilingScheme();
						}

						// 在Cesium中添加WMTS图层
						const wmtsLayer = new Cesium.WebMapTileServiceImageryProvider({
							url: wmtsInfo.serviceUrl,
							layer: wmtsInfo.layer,
							style: wmtsInfo.style,
							format: wmtsInfo.format,
							tileMatrixSetID: wmtsInfo.tileMatrixSetID,
							tilingScheme: tilingScheme,
						});
						console.log('WMTS图层添加到Cesium的图层集合中');
						// 添加到Cesium的图层集合中
						cesiumLayer = this.activeMap.imageryLayers.addImageryProvider(wmtsLayer);
					} else if (layerConfig.protocol === 'XYZ') {
						// 创建XYZ图层
						console.log(`创建XYZ图层: ${layerConfig.name}`);

						// 在Cesium中添加XYZ图层
						const xyzLayer = new Cesium.UrlTemplateImageryProvider({
							url: layerConfig.url,
						});

						cesiumLayer = this.activeMap.imageryLayers.addImageryProvider(xyzLayer);
					}
				} else if (layerConfig.type === 'vector') {
					if (layerConfig.protocol === 'GeoJSON') {
						// 创建GeoJSON图层
						console.log(`创建GeoJSON图层: ${layerConfig.name}, 几何类型: ${layerConfig.geometryType}`);

						// 获取默认样式
						const defaultStyle = layerConfig.defaultStyle || {
							point: {
								color: '#FF0000',
								pixelSize: 10,
								outlineColor: '#FFFFFF',
								outlineWidth: 2
							},
							polyline: {
								material: '#FF0000',
								width: 3
							},
							polygon: {
								material: '#FF000088',
								outline: true,
								outlineColor: '#FF0000',
								outlineWidth: 2
							}
						};

						// 创建加载选项
						const geoJsonOptions: any = {
							stroke: Cesium.Color.fromCssColorString(
								defaultStyle.polyline?.material && typeof defaultStyle.polyline.material === 'string' 
									? defaultStyle.polyline.material 
									: defaultStyle.polygon?.outlineColor || '#FF0000'
							),
							strokeWidth: defaultStyle.polyline?.width || 3,
							fill: Cesium.Color.fromCssColorString(
								defaultStyle.polygon?.material && typeof defaultStyle.polygon.material === 'string' 
									? defaultStyle.polygon.material 
									: '#FF000088'
							),
						};

						// 在Cesium中加载GeoJSON数据
						const dataSourcePromise = Cesium.GeoJsonDataSource.load(layerConfig.url, geoJsonOptions);

						// 根据几何类型处理特殊样式
						dataSourcePromise.then((dataSource: Cesium.GeoJsonDataSource) => {
							const entities = dataSource.entities.values;
							
							// 判断是否有样式规则
							if (layerConfig.styleRules && layerConfig.styleRules.length > 0) {
								console.log(`图层 ${layerConfig.name} 有 ${layerConfig.styleRules.length} 条样式规则，应用规则样式`);
								console.log(`规则列表:`, JSON.stringify(layerConfig.styleRules));
								
								// 有样式规则，只应用规则样式
								for (const entity of entities) {
									// 获取实体的属性
									const properties = entity.properties;
									if (!properties) {
										console.warn('实体没有properties属性');
										continue;
									}
									
									// 输出实体属性，便于调试
									console.log(`===== 实体属性 =====`);
									for (const key in properties._propertyNames) {
										const propName = properties._propertyNames[key];
										try {
											// Cesium.PropertyBag正确的调用方式
											let propValue;
											if (properties.hasProperty(propName)) {
												propValue = properties[propName].getValue(Cesium.JulianDate.now());
											}
											console.log(`属性: ${propName}, 值: ${propValue}, 类型: ${typeof propValue}`);
										} catch (error) {
											console.error(`获取属性 ${propName} 值时出错:`, error);
										}
									}
									console.log(`===================`);
									
									let matchedRule = false;
									// 检查每个样式规则
									for (const rule of layerConfig.styleRules || []) {
										console.log(`检查规则: ${JSON.stringify(rule.filter)}`);
										
										// 应用过滤器
										const isMatch = this.evaluateFilter(properties, rule.filter);
										console.log(`规则匹配结果: ${isMatch}`);
										
										if (isMatch) {
											// 如果匹配，应用样式
											console.log(`应用样式: ${JSON.stringify(rule.style)}`);
											this.applyStyle(entity, rule.style, layerConfig.geometryType);
											matchedRule = true;
											break; // 应用第一个匹配的规则后停止
										}
									}
									
									// 如果没有匹配的规则，才应用默认样式
									if (!matchedRule && layerConfig.defaultStyle) {
										console.log(`没有匹配的规则，应用默认样式`);
										this.applyStyle(entity, layerConfig.defaultStyle, layerConfig.geometryType);
									}
								}
							} else {
								console.log(`图层 ${layerConfig.name} 没有样式规则，应用默认样式`);
								// 没有样式规则，应用默认样式到所有实体
								if (layerConfig.defaultStyle) {
									for (const entity of entities) {
										this.applyStyle(entity, layerConfig.defaultStyle, layerConfig.geometryType);
									}
								}
							}
						});

						// 添加数据源到Cesium
						this.activeMap.dataSources.add(dataSourcePromise);

						// 将Promise保存到图层配置
						cesiumLayer = dataSourcePromise;
					} else if (layerConfig.protocol === 'WFS') {
						// 处理WFS图层
						let wfsUrl = layerConfig.url;

						if (layerConfig.wfsParams) {
							console.log(`创建WFS图层: ${layerConfig.name} 使用默认参数`);
							wfsUrl = UrlParser.buildWfsUrl(layerConfig.url, layerConfig.wfsParams);
						}

						// 获取默认样式
						const defaultStyle = layerConfig.defaultStyle || {
							point: {
								color: '#0066ff',
								pixelSize: 10,
								outlineColor: '#FFFFFF',
								outlineWidth: 2
							},
							polyline: {
								material: '#0066ff',
								width: 3
							},
							polygon: {
								material: '#0066ff88',
								outline: true,
								outlineColor: '#0066ff',
								outlineWidth: 2
							}
						};

						// 创建加载选项
						const geoJsonOptions: any = {
							stroke: Cesium.Color.fromCssColorString(
								defaultStyle.polyline?.material && typeof defaultStyle.polyline.material === 'string' 
									? defaultStyle.polyline.material 
									: defaultStyle.polygon?.outlineColor || '#0066ff'
							),
							strokeWidth: defaultStyle.polyline?.width || 3,
							fill: Cesium.Color.fromCssColorString(
								defaultStyle.polygon?.material && typeof defaultStyle.polygon.material === 'string' 
									? defaultStyle.polygon.material 
									: '#0066ff88'
							),
						};

						// 加载WFS数据为GeoJSON
						const dataSourcePromise = Cesium.GeoJsonDataSource.load(wfsUrl, geoJsonOptions);

						// 根据几何类型处理特殊样式
						dataSourcePromise.then((dataSource: Cesium.GeoJsonDataSource) => {
							const entities = dataSource.entities.values;
							
							// 判断是否有样式规则
							if (layerConfig.styleRules && layerConfig.styleRules.length > 0) {
								console.log(`图层 ${layerConfig.name} 有 ${layerConfig.styleRules.length} 条样式规则，应用规则样式`);
								console.log(`规则列表:`, JSON.stringify(layerConfig.styleRules));
								
								// 有样式规则，只应用规则样式
								for (const entity of entities) {
									// 获取实体的属性
									const properties = entity.properties;
									if (!properties) {
										console.warn('实体没有properties属性');
										continue;
									}
									
									// 输出实体属性，便于调试
									console.log(`===== 实体属性 =====`);
									for (const key in properties._propertyNames) {
										const propName = properties._propertyNames[key];
										try {
											// Cesium.PropertyBag正确的调用方式
											let propValue;
											if (properties.hasProperty(propName)) {
												propValue = properties[propName].getValue(Cesium.JulianDate.now());
											}
											console.log(`属性: ${propName}, 值: ${propValue}, 类型: ${typeof propValue}`);
										} catch (error) {
											console.error(`获取属性 ${propName} 值时出错:`, error);
										}
									}
									console.log(`===================`);
									
									let matchedRule = false;
									// 检查每个样式规则
									for (const rule of layerConfig.styleRules || []) {
										console.log(`检查规则: ${JSON.stringify(rule.filter)}`);
										
										// 应用过滤器
										const isMatch = this.evaluateFilter(properties, rule.filter);
										console.log(`规则匹配结果: ${isMatch}`);
										
										if (isMatch) {
											// 如果匹配，应用样式
											console.log(`应用样式: ${JSON.stringify(rule.style)}`);
											this.applyStyle(entity, rule.style, layerConfig.geometryType);
											matchedRule = true;
											break; // 应用第一个匹配的规则后停止
										}
									}
									
									// 如果没有匹配的规则，才应用默认样式
									if (!matchedRule && layerConfig.defaultStyle) {
										console.log(`没有匹配的规则，应用默认样式`);
										this.applyStyle(entity, layerConfig.defaultStyle, layerConfig.geometryType);
									}
								}
							} else {
								console.log(`图层 ${layerConfig.name} 没有样式规则，应用默认样式`);
								// 没有样式规则，应用默认样式到所有实体
								if (layerConfig.defaultStyle) {
									for (const entity of entities) {
										this.applyStyle(entity, layerConfig.defaultStyle, layerConfig.geometryType);
									}
								}
							}
						});

						// 添加数据源到Cesium
						this.activeMap.dataSources.add(dataSourcePromise);

						// 将Promise保存到图层配置
						cesiumLayer = dataSourcePromise;
					}
				}

				if (cesiumLayer) {
					// 保存图层实例到配置中
					layerConfig.layerInstance = cesiumLayer;
					layerConfig.active = true;

					// 标记图层已加载
					this.markLayerAsLoaded(layerId);

					console.log(`图层 ${layerConfig.name} 成功添加到地图`);
					return true;
				} else {
					console.error(`无法为 ${layerConfig.name} 创建图层`);
					return false;
				}
			} catch (error) {
				console.error(`添加图层 ${layerId} 时发生错误:`, error);
				return false;
			}
		},

		/**
		 * 切换指定事件名称的所有图层
		 * @param eventName 事件名称
		 * @param show 是否显示，true为显示，false为隐藏
		 * @returns 操作是否成功
		 */
		toggleEventLayers(eventName: string, show: boolean): boolean {
			try {
				const layers = this.getLayersByEvent(eventName);
				
				if (layers.length === 0) {
					console.warn(`没有找到与事件 ${eventName} 关联的图层`);
					return false;
				}
				
				console.log(`${show ? '显示' : '隐藏'}与事件 ${eventName} 关联的图层，共 ${layers.length} 个图层`);
				
				if (show) {
					// 添加所有该事件相关的图层
					console.log(`添加与事件 ${eventName} 关联的图层`);
					let successCount = 0;
					for (const layer of layers) {
						if (this.addLayer(layer.id)) {
							successCount++;
						}
					}
					console.log(`成功添加 ${successCount}/${layers.length} 个图层`);
					return successCount > 0;
				} else {
					// 移除所有该事件相关的图层
					console.log(`移除与事件 ${eventName} 关联的图层`);
					let successCount = 0;
					for (const layer of layers) {
						if (this.removeLayerFromMap(layer.id)) {
							successCount++;
						}
					}
					console.log(`成功移除 ${successCount}/${layers.length} 个图层`);
					return successCount > 0;
				}
			} catch (error) {
				console.error(`切换事件 ${eventName} 图层时发生错误:`, error);
				return false;
			}
		},

		/**
		 * 从地图中移除图层
		 * @param layerId 图层ID
		 * @returns 是否成功移除图层
		 */
		removeLayerFromMap(layerId: string): boolean {
			if (!this.activeMap) {
				console.error('地图未初始化，无法移除图层');
				return false;
			}

			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig) {
				console.error(`图层 ${layerId} 不存在`);
				return false;
			}

			// 检查图层是否已加载
			if (!this.isLayerLoaded(layerId)) {
				console.log(`图层 ${layerId} 未加载，无需移除`);
				return true;
			}

			try {
				// 从地图中移除图层
				if (layerConfig.layerInstance) {
					if (layerConfig.type === 'raster') {
						// 移除栅格图层
						this.activeMap.imageryLayers.remove(layerConfig.layerInstance);
					} else if (layerConfig.type === 'vector') {
						// 对于Promise实例，需要等待其解析后再移除
						if (layerConfig.layerInstance instanceof Promise) {
							layerConfig.layerInstance.then((dataSource: any) => {
								if (this.activeMap) {
									this.activeMap.dataSources.remove(dataSource);
								}
							});
						} else {
							// 直接移除矢量图层
							this.activeMap.dataSources.remove(layerConfig.layerInstance);
						}
					}

					// 更新图层状态
					layerConfig.active = false;
					layerConfig.layerInstance = null;

					// 从加载列表中移除
					this.removeLayer(layerId);

					console.log(`图层 ${layerConfig.name} 已从地图移除`);
					return true;
				} else {
					console.warn(`图层 ${layerConfig.name} 没有实例，可能未正确加载`);
					// 从加载列表中移除
					this.removeLayer(layerId);
					return true;
				}
			} catch (error) {
				console.error(`移除图层 ${layerId} 时发生错误:`, error);
				return false;
			}
		},

		/**
		 * 标记图层为已加载
		 * @param layerId 图层ID
		 */
		markLayerAsLoaded(layerId: string) {
			if (!this.loadedLayers.includes(layerId)) {
				this.loadedLayers.push(layerId);
			}
		},

		/**
		 * 移除图层
		 * @param layerId 图层ID
		 */
		removeLayer(layerId: string) {
			const index = this.loadedLayers.indexOf(layerId);
			if (index > -1) {
				this.loadedLayers.splice(index, 1);
			}
		},

		/**
		 * 重置所有图层
		 */
		resetLayers() {
			// 移除所有已加载的图层
			[...this.loadedLayers].forEach((layerId) => {
				this.removeLayerFromMap(layerId);
			});

			this.loadedLayers = [];
		},

		/**
		 * 评估过滤条件是否匹配
		 * @param properties 实体属性
		 * @param filter 过滤条件
		 * @returns 是否匹配
		 */
		evaluateFilter(properties: Cesium.PropertyBag, filter: FilterCondition): boolean {
			try {
				// 检查属性是否存在
				if (!properties || !filter || !filter.property) {
					console.log('属性对象或过滤条件无效');
					return false;
				}
				
				if (!properties.hasProperty(filter.property)) {
					console.log(`属性 ${filter.property} 不存在`);
					return false;
				}
				
				// 安全获取属性值 - 正确的调用方式
				let propValue;
				try {
					propValue = properties[filter.property].getValue(Cesium.JulianDate.now());
				} catch (error) {
					console.error(`获取属性 ${filter.property} 值时出错:`, error);
					return false;
				}
				
				if (propValue === undefined || propValue === null) {
					console.log(`属性 ${filter.property} 值为${propValue === undefined ? 'undefined' : 'null'}`);
					return false;
				}
				
				// 简化比较逻辑，避免过多的类型转换和特殊情况处理
				switch (filter.operator) {
					case '=':
						// 尝试数值比较（如果两者都可以转为数字）
						if (!isNaN(Number(propValue)) && !isNaN(Number(filter.value))) {
							return Number(propValue) === Number(filter.value);
						}
						// 否则字符串比较
						return String(propValue) === String(filter.value);
					
					case '!=':
						if (!isNaN(Number(propValue)) && !isNaN(Number(filter.value))) {
							return Number(propValue) !== Number(filter.value);
						}
						return String(propValue) !== String(filter.value);
					
					case '>':
						if (!isNaN(Number(propValue)) && !isNaN(Number(filter.value))) {
							return Number(propValue) > Number(filter.value);
						}
						return String(propValue) > String(filter.value);
					
					case '<':
						if (!isNaN(Number(propValue)) && !isNaN(Number(filter.value))) {
							return Number(propValue) < Number(filter.value);
						}
						return String(propValue) < String(filter.value);
					
					case '>=':
						if (!isNaN(Number(propValue)) && !isNaN(Number(filter.value))) {
							return Number(propValue) >= Number(filter.value);
						}
						return String(propValue) >= String(filter.value);
					
					case '<=':
						if (!isNaN(Number(propValue)) && !isNaN(Number(filter.value))) {
							return Number(propValue) <= Number(filter.value);
						}
						return String(propValue) <= String(filter.value);
					
					case 'between':
						const range = filter.value as [number, number];
						const numValue = Number(propValue);
						return !isNaN(numValue) && numValue >= range[0] && numValue <= range[1];
					
					case 'inRange':
						if (filter.min === undefined || filter.max === undefined) {
							return false;
						}
						const rangeValue = Number(propValue);
						return !isNaN(rangeValue) && rangeValue >= filter.min && rangeValue <= filter.max;
					
					case 'in':
						const valueList = filter.value as (string | number)[];
						// 检查原始值或转换后的值是否在列表中
						return valueList.some(v => 
							v === propValue || 
							(!isNaN(Number(propValue)) && Number(propValue) === Number(v))
						);
					
					case 'notIn':
						const excludeList = filter.value as (string | number)[];
						// 检查原始值或转换后的值是否不在列表中
						return !excludeList.some(v => 
							v === propValue || 
							(!isNaN(Number(propValue)) && Number(propValue) === Number(v))
						);
					
					default:
						console.log(`不支持的操作符: ${filter.operator}`);
						return false;
				}
			} catch (error) {
				console.error(`过滤条件评估出错:`, error);
				return false;
			}
		},

		/**
		 * 应用样式到实体
		 * @param entity Cesium实体
		 * @param style 样式
		 * @param geometryType 几何类型
		 */
		applyStyle(entity: Cesium.Entity, style: LayerStyle, geometryType?: string): void {
			// 应用点样式
			if (style.point && (geometryType?.includes('Point') || entity.point)) {
				if (!entity.point) {
					entity.point = new Cesium.PointGraphics();
				}
				
				if (style.point.color) {
					entity.point.color = new Cesium.ConstantProperty(
						Cesium.Color.fromCssColorString(style.point.color)
					);
				}
				
				if (style.point.pixelSize !== undefined) {
					entity.point.pixelSize = new Cesium.ConstantProperty(style.point.pixelSize);
				}
				
				if (style.point.outlineColor) {
					entity.point.outlineColor = new Cesium.ConstantProperty(
						Cesium.Color.fromCssColorString(style.point.outlineColor)
					);
				}
				
				if (style.point.outlineWidth !== undefined) {
					entity.point.outlineWidth = new Cesium.ConstantProperty(style.point.outlineWidth);
				}
				
				if (style.point.show !== undefined) {
					entity.point.show = new Cesium.ConstantProperty(style.point.show);
				}
			}
			
			// 应用线样式
			if (style.polyline && (geometryType?.includes('LineString') || entity.polyline)) {
				if (!entity.polyline) {
					entity.polyline = new Cesium.PolylineGraphics();
				}
				
				// 处理材质
				if (style.polyline.material) {
					if (typeof style.polyline.material === 'string') {
						// 使用颜色字符串
						entity.polyline.material = new Cesium.ColorMaterialProperty(
							Cesium.Color.fromCssColorString(style.polyline.material)
						);
					} else {
						// 使用材质对象
						if (style.polyline.material.dashLength !== undefined) {
							// 创建虚线材质
							entity.polyline.material = new Cesium.PolylineDashMaterialProperty({
								color: style.polyline.material.color 
									? Cesium.Color.fromCssColorString(style.polyline.material.color)
									: Cesium.Color.WHITE,
								dashLength: style.polyline.material.dashLength,
								dashPattern: style.polyline.material.dashPattern !== undefined 
									? style.polyline.material.dashPattern 
									: parseInt('1111000000000000', 2)
							});
						} else if (style.polyline.material.color) {
							// 创建普通颜色材质
							entity.polyline.material = new Cesium.ColorMaterialProperty(
								Cesium.Color.fromCssColorString(style.polyline.material.color)
							);
						}
					}
				}
				
				if (style.polyline.width !== undefined) {
					entity.polyline.width = new Cesium.ConstantProperty(style.polyline.width);
				}
				
				if (style.polyline.clampToGround !== undefined) {
					entity.polyline.clampToGround = new Cesium.ConstantProperty(style.polyline.clampToGround);
				}
				
				if (style.polyline.show !== undefined) {
					entity.polyline.show = new Cesium.ConstantProperty(style.polyline.show);
				}
			}
			
			// 应用面样式
			if (style.polygon && (geometryType?.includes('Polygon') || entity.polygon)) {
				if (!entity.polygon) {
					entity.polygon = new Cesium.PolygonGraphics();
				}
				
				// 处理材质
				if (style.polygon.material) {
					if (typeof style.polygon.material === 'string') {
						// 使用颜色字符串
						entity.polygon.material = new Cesium.ColorMaterialProperty(
							Cesium.Color.fromCssColorString(style.polygon.material)
						);
					} else if (style.polygon.material.color) {
						// 使用颜色对象
						entity.polygon.material = new Cesium.ColorMaterialProperty(
							Cesium.Color.fromCssColorString(style.polygon.material.color)
						);
					}
				}
				
				if (style.polygon.outline !== undefined) {
					entity.polygon.outline = new Cesium.ConstantProperty(style.polygon.outline);
				}
				
				if (style.polygon.outlineColor) {
					entity.polygon.outlineColor = new Cesium.ConstantProperty(
						Cesium.Color.fromCssColorString(style.polygon.outlineColor)
					);
				}
				
				if (style.polygon.outlineWidth !== undefined) {
					entity.polygon.outlineWidth = new Cesium.ConstantProperty(style.polygon.outlineWidth);
				}
				
				if (style.polygon.show !== undefined) {
					entity.polygon.show = new Cesium.ConstantProperty(style.polygon.show);
				}
			}
			
			// 应用广告牌样式
			if (style.billboard && (geometryType?.includes('Point') || entity.position)) {
				if (!entity.billboard) {
					entity.billboard = new Cesium.BillboardGraphics();
				}
				
				if (style.billboard.image) {
					entity.billboard.image = new Cesium.ConstantProperty(style.billboard.image);
				}
				
				if (style.billboard.width !== undefined) {
					entity.billboard.width = new Cesium.ConstantProperty(style.billboard.width);
				}
				
				if (style.billboard.height !== undefined) {
					entity.billboard.height = new Cesium.ConstantProperty(style.billboard.height);
				}
				
				if (style.billboard.scale !== undefined) {
					entity.billboard.scale = new Cesium.ConstantProperty(style.billboard.scale);
				}
				
				if (style.billboard.rotation !== undefined) {
					entity.billboard.rotation = new Cesium.ConstantProperty(style.billboard.rotation);
				}
				
				if (style.billboard.horizontalOrigin) {
					let horizontalOrigin;
					switch (style.billboard.horizontalOrigin) {
						case 'LEFT':
							horizontalOrigin = Cesium.HorizontalOrigin.LEFT;
							break;
						case 'RIGHT':
							horizontalOrigin = Cesium.HorizontalOrigin.RIGHT;
							break;
						default:
							horizontalOrigin = Cesium.HorizontalOrigin.CENTER;
					}
					entity.billboard.horizontalOrigin = new Cesium.ConstantProperty(horizontalOrigin);
				}
				
				if (style.billboard.verticalOrigin) {
					let verticalOrigin;
					switch (style.billboard.verticalOrigin) {
						case 'TOP':
							verticalOrigin = Cesium.VerticalOrigin.TOP;
							break;
						case 'CENTER':
							verticalOrigin = Cesium.VerticalOrigin.CENTER;
							break;
						case 'BASELINE':
							verticalOrigin = Cesium.VerticalOrigin.BASELINE;
							break;
						default:
							verticalOrigin = Cesium.VerticalOrigin.BOTTOM;
					}
					entity.billboard.verticalOrigin = new Cesium.ConstantProperty(verticalOrigin);
				}
				
				if (style.billboard.pixelOffset) {
					entity.billboard.pixelOffset = new Cesium.ConstantProperty(
						new Cesium.Cartesian2(style.billboard.pixelOffset[0], style.billboard.pixelOffset[1])
					);
				}
				
				if (style.billboard.eyeOffset) {
					entity.billboard.eyeOffset = new Cesium.ConstantProperty(
						new Cesium.Cartesian3(
							style.billboard.eyeOffset[0], 
							style.billboard.eyeOffset[1], 
							style.billboard.eyeOffset[2]
						)
					);
				}
				
				if (style.billboard.color) {
					entity.billboard.color = new Cesium.ConstantProperty(
						Cesium.Color.fromCssColorString(style.billboard.color)
					);
				}
				
				if (style.billboard.show !== undefined) {
					entity.billboard.show = new Cesium.ConstantProperty(style.billboard.show);
				}
				
				// 如果添加了billboard，通常需要隐藏点
				if (entity.point && style.billboard.image) {
					entity.point.show = new Cesium.ConstantProperty(false);
				}
			}
			
			// 应用标签样式
			if (style.label && (geometryType?.includes('Point') || entity.position)) {
				if (!entity.label) {
					entity.label = new Cesium.LabelGraphics();
				}
				
				if (style.label.text) {
					entity.label.text = new Cesium.ConstantProperty(style.label.text);
				}
				
				if (style.label.font) {
					entity.label.font = new Cesium.ConstantProperty(style.label.font);
				}
				
				if (style.label.style) {
					let labelStyle;
					switch (style.label.style) {
						case 'FILL':
							labelStyle = Cesium.LabelStyle.FILL;
							break;
						case 'OUTLINE':
							labelStyle = Cesium.LabelStyle.OUTLINE;
							break;
						case 'FILL_AND_OUTLINE':
						default:
							labelStyle = Cesium.LabelStyle.FILL_AND_OUTLINE;
					}
					entity.label.style = new Cesium.ConstantProperty(labelStyle);
				}
				
				if (style.label.fillColor) {
					entity.label.fillColor = new Cesium.ConstantProperty(
						Cesium.Color.fromCssColorString(style.label.fillColor)
					);
				}
				
				if (style.label.outlineColor) {
					entity.label.outlineColor = new Cesium.ConstantProperty(
						Cesium.Color.fromCssColorString(style.label.outlineColor)
					);
				}
				
				if (style.label.outlineWidth !== undefined) {
					entity.label.outlineWidth = new Cesium.ConstantProperty(style.label.outlineWidth);
				}
				
				if (style.label.pixelOffset) {
					entity.label.pixelOffset = new Cesium.ConstantProperty(
						new Cesium.Cartesian2(style.label.pixelOffset[0], style.label.pixelOffset[1])
					);
				}
				
				if (style.label.eyeOffset) {
					entity.label.eyeOffset = new Cesium.ConstantProperty(
						new Cesium.Cartesian3(
							style.label.eyeOffset[0], 
							style.label.eyeOffset[1], 
							style.label.eyeOffset[2]
						)
					);
				}
				
				if (style.label.horizontalOrigin) {
					let horizontalOrigin;
					switch (style.label.horizontalOrigin) {
						case 'LEFT':
							horizontalOrigin = Cesium.HorizontalOrigin.LEFT;
							break;
						case 'RIGHT':
							horizontalOrigin = Cesium.HorizontalOrigin.RIGHT;
							break;
						default:
							horizontalOrigin = Cesium.HorizontalOrigin.CENTER;
					}
					entity.label.horizontalOrigin = new Cesium.ConstantProperty(horizontalOrigin);
				}
				
				if (style.label.verticalOrigin) {
					let verticalOrigin;
					switch (style.label.verticalOrigin) {
						case 'TOP':
							verticalOrigin = Cesium.VerticalOrigin.TOP;
							break;
						case 'CENTER':
							verticalOrigin = Cesium.VerticalOrigin.CENTER;
							break;
						case 'BASELINE':
							verticalOrigin = Cesium.VerticalOrigin.BASELINE;
							break;
						default:
							verticalOrigin = Cesium.VerticalOrigin.BOTTOM;
					}
					entity.label.verticalOrigin = new Cesium.ConstantProperty(verticalOrigin);
				}
				
				if (style.label.scale !== undefined) {
					entity.label.scale = new Cesium.ConstantProperty(style.label.scale);
				}
				
				if (style.label.show !== undefined) {
					entity.label.show = new Cesium.ConstantProperty(style.label.show);
				}
			}
		},
	},
});
