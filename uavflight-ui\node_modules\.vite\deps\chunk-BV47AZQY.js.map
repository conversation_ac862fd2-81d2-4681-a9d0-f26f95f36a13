{"version": 3, "sources": ["../../ol/loadingstrategy.js"], "sourcesContent": ["/**\n * @module ol/loadingstrategy\n */\n\nimport {fromUserExtent, fromUserResolution, toUserExtent} from './proj.js';\n\n/**\n * Strategy function for loading all features with a single request.\n * @param {import(\"./extent.js\").Extent} extent Extent.\n * @param {number} resolution Resolution.\n * @return {Array<import(\"./extent.js\").Extent>} Extents.\n * @api\n */\nexport function all(extent, resolution) {\n  return [[-Infinity, -Infinity, Infinity, Infinity]];\n}\n\n/**\n * Strategy function for loading features based on the view's extent and\n * resolution.\n * @param {import(\"./extent.js\").Extent} extent Extent.\n * @param {number} resolution Resolution.\n * @return {Array<import(\"./extent.js\").Extent>} Extents.\n * @api\n */\nexport function bbox(extent, resolution) {\n  return [extent];\n}\n\n/**\n * Creates a strategy function for loading features based on a tile grid.\n * @param {import(\"./tilegrid/TileGrid.js\").default} tileGrid Tile grid.\n * @return {function(import(\"./extent.js\").Extent, number, import(\"./proj.js\").Projection): Array<import(\"./extent.js\").Extent>} Loading strategy.\n * @api\n */\nexport function tile(tileGrid) {\n  return (\n    /**\n     * @param {import(\"./extent.js\").Extent} extent Extent.\n     * @param {number} resolution Resolution.\n     * @param {import(\"./proj.js\").Projection} projection Projection.\n     * @return {Array<import(\"./extent.js\").Extent>} Extents.\n     */\n    function (extent, resolution, projection) {\n      const z = tileGrid.getZForResolution(\n        fromUserResolution(resolution, projection)\n      );\n      const tileRange = tileGrid.getTileRangeForExtentAndZ(\n        fromUserExtent(extent, projection),\n        z\n      );\n      /** @type {Array<import(\"./extent.js\").Extent>} */\n      const extents = [];\n      /** @type {import(\"./tilecoord.js\").TileCoord} */\n      const tileCoord = [z, 0, 0];\n      for (\n        tileCoord[1] = tileRange.minX;\n        tileCoord[1] <= tileRange.maxX;\n        ++tileCoord[1]\n      ) {\n        for (\n          tileCoord[2] = tileRange.minY;\n          tileCoord[2] <= tileRange.maxY;\n          ++tileCoord[2]\n        ) {\n          extents.push(\n            toUserExtent(tileGrid.getTileCoordExtent(tileCoord), projection)\n          );\n        }\n      }\n      return extents;\n    }\n  );\n}\n"], "mappings": ";;;;;;;AAaO,SAAS,IAAI,QAAQ,YAAY;AACtC,SAAO,CAAC,CAAC,WAAW,WAAW,UAAU,QAAQ,CAAC;AACpD;AAUO,SAAS,KAAK,QAAQ,YAAY;AACvC,SAAO,CAAC,MAAM;AAChB;AAQO,SAAS,KAAK,UAAU;AAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOE,SAAU,QAAQ,YAAY,YAAY;AACxC,YAAM,IAAI,SAAS;AAAA,QACjB,mBAAmB,YAAY,UAAU;AAAA,MAC3C;AACA,YAAM,YAAY,SAAS;AAAA,QACzB,eAAe,QAAQ,UAAU;AAAA,QACjC;AAAA,MACF;AAEA,YAAM,UAAU,CAAC;AAEjB,YAAM,YAAY,CAAC,GAAG,GAAG,CAAC;AAC1B,WACE,UAAU,CAAC,IAAI,UAAU,MACzB,UAAU,CAAC,KAAK,UAAU,MAC1B,EAAE,UAAU,CAAC,GACb;AACA,aACE,UAAU,CAAC,IAAI,UAAU,MACzB,UAAU,CAAC,KAAK,UAAU,MAC1B,EAAE,UAAU,CAAC,GACb;AACA,kBAAQ;AAAA,YACN,aAAa,SAAS,mBAAmB,SAAS,GAAG,UAAU;AAAA,UACjE;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAEJ;", "names": []}