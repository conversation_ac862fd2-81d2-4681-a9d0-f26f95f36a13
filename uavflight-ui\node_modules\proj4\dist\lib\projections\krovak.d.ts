export function init(): void;
export class init {
    a: number;
    es: number;
    e: number;
    lat0: number;
    long0: number;
    k0: number;
    s45: number;
    s90: number;
    fi0: number;
    e2: number;
    alfa: number;
    uq: number;
    u0: number;
    g: number;
    k: number;
    k1: number;
    n0: number;
    s0: number;
    n: number;
    ro0: number;
    ad: number;
}
export function forward(p: any): any;
export function inverse(p: any): any;
export const names: string[];
declare namespace _default {
    export { init };
    export { forward };
    export { inverse };
    export { names };
}
export default _default;
