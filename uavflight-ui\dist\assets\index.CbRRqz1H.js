const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/breadcrumb.DQVR5t9j.js","assets/vue.CnN__PXn.js","assets/index.C0-0gsfl.js","assets/index.CB8Sa4Ps.css","assets/breadcrumb.CVyL3RuH.css","assets/user.BfNLZf12.js","assets/user.DHbSQbGZ.css","assets/index.BlEf8vZS.js","assets/pigx-app.DmHLWGl6.js","assets/index.q5pfyk-F.css","assets/horizontal.5jYOHB7s.js","assets/horizontal.DfB5jVg5.css"])))=>i.map(i=>d[i]);
import{t as j,u as k,e as d,a as u,q as F,__tla as M}from"./index.C0-0gsfl.js";import{d as v,s as L,l as q,A as H,c as E,o as z,i as G,a as J,b as h,e as I,D as b,t as R,u as s,j as c}from"./vue.CnN__PXn.js";let S,K=Promise.all([(()=>{try{return M}catch{}})()]).then(async()=>{let m,p;m={class:"layout-navbars-breadcrumb-index"},p=v({name:"layoutBreadcrumbIndex"}),S=F(v({...p,setup(N){const x=c(()=>u(()=>import("./breadcrumb.DQVR5t9j.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([0,1,2,3,4]))),A=c(()=>u(()=>import("./user.BfNLZf12.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([5,2,1,3,6]))),C=c(()=>u(()=>import("./index.BlEf8vZS.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([7,8,2,1,3,9]))),D=c(()=>u(()=>import("./horizontal.5jYOHB7s.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([10,2,1,3,11]))),P=j(),g=k(),{themeConfig:_}=L(g),{routesList:i}=L(P),w=q(),o=H({menuList:[]}),O=E(()=>{let{isShowLogo:e,layout:t}=_.value;return e&&t==="classic"||e&&t==="transverse"}),T=E(()=>{let{layout:e,isClassicSplitMenu:t}=_.value;return e==="transverse"||t&&e==="classic"}),f=()=>{let{layout:e,isClassicSplitMenu:t}=_.value;if(e==="classic"&&t){o.menuList=V(l(i.value));const a=B(w.path);d.emit("setSendClassicChildren",a)}else o.menuList=l(i.value)},V=e=>(e.map(t=>{t.children&&delete t.children}),e),l=e=>e.reduce((t,a)=>{var r;if(!((r=a.meta)!=null&&r.isHide)){const n={...a};n.children&&(n.children=l(n.children)),t.push(n)}return t},[]),B=e=>{let t={children:[]};const a=y(i.value,e);if(a){const r=l(i.value).find(n=>n.path===a.path);r&&(t.item={...r},t.children=r.children||[])}return t},y=(e,t)=>{for(const a of e)if(a.path===t||a.children&&y(a.children,t))return a};return z(()=>{f(),d.on("getBreadcrumbIndexSetFilterRoutes",()=>{f()})}),G(()=>{d.off("getBreadcrumbIndexSetFilterRoutes",()=>{})}),(e,t)=>(h(),J("div",m,[s(O)?(h(),I(s(C),{key:0})):b("",!0),R(s(x)),s(T)?(h(),I(s(D),{key:1,menuList:s(o).menuList},null,8,["menuList"])):b("",!0),R(s(A))]))}}),[["__scopeId","data-v-87d57d83"]])});export{K as __tla,S as default};
