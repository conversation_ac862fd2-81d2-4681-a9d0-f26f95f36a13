export default {
	param: {
		index: '#',
		importsysPublicParamTip: 'import SysPublicParam',
		publicId: 'publicId',
		publicName: 'publicName',
		publicKey: 'publicKey',
		publicValue: 'publicValue',
		status: 'status',
		validateCode: 'validateCode',
		createBy: 'createBy',
		updateBy: 'updateBy',
		createTime: 'createTime',
		updateTime: 'updateTime',
		publicType: 'publicType',
		systemFlag: 'systemFlag',
		delFlag: 'delFlag',
		tenantId: 'tenantId',
		inputpublicIdTip: 'input publicId',
		inputpublicNameTip: 'input publicName',
		inputpublicKeyTip: 'input publicKey',
		inputpublicValueTip: 'input publicValue',
		inputstatusTip: 'input status',
		inputvalidateCodeTip: 'input validateCode',
		inputcreateByTip: 'input createBy',
		inputupdateByTip: 'input updateBy',
		inputcreateTimeTip: 'input createTime',
		inputupdateTimeTip: 'input updateTime',
		inputpublicTypeTip: 'input publicType',
		inputsystemFlagTip: 'input systemFlag',
		inputdelFlagTip: 'input delFlag',
		inputtenantIdTip: 'input tenantId',
	},
};
