{"version": 3, "sources": ["../../ol/structs/PriorityQueue.js", "../../ol/TileQueue.js"], "sourcesContent": ["/**\n * @module ol/structs/PriorityQueue\n */\nimport {assert} from '../asserts.js';\nimport {clear} from '../obj.js';\n\n/**\n * @type {number}\n */\nexport const DROP = Infinity;\n\n/**\n * @classdesc\n * Priority queue.\n *\n * The implementation is inspired from the Closure Library's Heap class and\n * Python's heapq module.\n *\n * See https://github.com/google/closure-library/blob/master/closure/goog/structs/heap.js\n * and https://hg.python.org/cpython/file/2.7/Lib/heapq.py.\n *\n * @template T\n */\nclass PriorityQueue {\n  /**\n   * @param {function(T): number} priorityFunction Priority function.\n   * @param {function(T): string} keyFunction Key function.\n   */\n  constructor(priorityFunction, keyFunction) {\n    /**\n     * @type {function(T): number}\n     * @private\n     */\n    this.priorityFunction_ = priorityFunction;\n\n    /**\n     * @type {function(T): string}\n     * @private\n     */\n    this.keyFunction_ = keyFunction;\n\n    /**\n     * @type {Array<T>}\n     * @private\n     */\n    this.elements_ = [];\n\n    /**\n     * @type {Array<number>}\n     * @private\n     */\n    this.priorities_ = [];\n\n    /**\n     * @type {!Object<string, boolean>}\n     * @private\n     */\n    this.queuedElements_ = {};\n  }\n\n  /**\n   * FIXME empty description for jsdoc\n   */\n  clear() {\n    this.elements_.length = 0;\n    this.priorities_.length = 0;\n    clear(this.queuedElements_);\n  }\n\n  /**\n   * Remove and return the highest-priority element. O(log N).\n   * @return {T} Element.\n   */\n  dequeue() {\n    const elements = this.elements_;\n    const priorities = this.priorities_;\n    const element = elements[0];\n    if (elements.length == 1) {\n      elements.length = 0;\n      priorities.length = 0;\n    } else {\n      elements[0] = elements.pop();\n      priorities[0] = priorities.pop();\n      this.siftUp_(0);\n    }\n    const elementKey = this.keyFunction_(element);\n    delete this.queuedElements_[elementKey];\n    return element;\n  }\n\n  /**\n   * Enqueue an element. O(log N).\n   * @param {T} element Element.\n   * @return {boolean} The element was added to the queue.\n   */\n  enqueue(element) {\n    assert(!(this.keyFunction_(element) in this.queuedElements_), 31); // Tried to enqueue an `element` that was already added to the queue\n    const priority = this.priorityFunction_(element);\n    if (priority != DROP) {\n      this.elements_.push(element);\n      this.priorities_.push(priority);\n      this.queuedElements_[this.keyFunction_(element)] = true;\n      this.siftDown_(0, this.elements_.length - 1);\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * @return {number} Count.\n   */\n  getCount() {\n    return this.elements_.length;\n  }\n\n  /**\n   * Gets the index of the left child of the node at the given index.\n   * @param {number} index The index of the node to get the left child for.\n   * @return {number} The index of the left child.\n   * @private\n   */\n  getLeftChildIndex_(index) {\n    return index * 2 + 1;\n  }\n\n  /**\n   * Gets the index of the right child of the node at the given index.\n   * @param {number} index The index of the node to get the right child for.\n   * @return {number} The index of the right child.\n   * @private\n   */\n  getRightChildIndex_(index) {\n    return index * 2 + 2;\n  }\n\n  /**\n   * Gets the index of the parent of the node at the given index.\n   * @param {number} index The index of the node to get the parent for.\n   * @return {number} The index of the parent.\n   * @private\n   */\n  getParentIndex_(index) {\n    return (index - 1) >> 1;\n  }\n\n  /**\n   * Make this a heap. O(N).\n   * @private\n   */\n  heapify_() {\n    let i;\n    for (i = (this.elements_.length >> 1) - 1; i >= 0; i--) {\n      this.siftUp_(i);\n    }\n  }\n\n  /**\n   * @return {boolean} Is empty.\n   */\n  isEmpty() {\n    return this.elements_.length === 0;\n  }\n\n  /**\n   * @param {string} key Key.\n   * @return {boolean} Is key queued.\n   */\n  isKeyQueued(key) {\n    return key in this.queuedElements_;\n  }\n\n  /**\n   * @param {T} element Element.\n   * @return {boolean} Is queued.\n   */\n  isQueued(element) {\n    return this.isKeyQueued(this.keyFunction_(element));\n  }\n\n  /**\n   * @param {number} index The index of the node to move down.\n   * @private\n   */\n  siftUp_(index) {\n    const elements = this.elements_;\n    const priorities = this.priorities_;\n    const count = elements.length;\n    const element = elements[index];\n    const priority = priorities[index];\n    const startIndex = index;\n\n    while (index < count >> 1) {\n      const lIndex = this.getLeftChildIndex_(index);\n      const rIndex = this.getRightChildIndex_(index);\n\n      const smallerChildIndex =\n        rIndex < count && priorities[rIndex] < priorities[lIndex]\n          ? rIndex\n          : lIndex;\n\n      elements[index] = elements[smallerChildIndex];\n      priorities[index] = priorities[smallerChildIndex];\n      index = smallerChildIndex;\n    }\n\n    elements[index] = element;\n    priorities[index] = priority;\n    this.siftDown_(startIndex, index);\n  }\n\n  /**\n   * @param {number} startIndex The index of the root.\n   * @param {number} index The index of the node to move up.\n   * @private\n   */\n  siftDown_(startIndex, index) {\n    const elements = this.elements_;\n    const priorities = this.priorities_;\n    const element = elements[index];\n    const priority = priorities[index];\n\n    while (index > startIndex) {\n      const parentIndex = this.getParentIndex_(index);\n      if (priorities[parentIndex] > priority) {\n        elements[index] = elements[parentIndex];\n        priorities[index] = priorities[parentIndex];\n        index = parentIndex;\n      } else {\n        break;\n      }\n    }\n    elements[index] = element;\n    priorities[index] = priority;\n  }\n\n  /**\n   * FIXME empty description for jsdoc\n   */\n  reprioritize() {\n    const priorityFunction = this.priorityFunction_;\n    const elements = this.elements_;\n    const priorities = this.priorities_;\n    let index = 0;\n    const n = elements.length;\n    let element, i, priority;\n    for (i = 0; i < n; ++i) {\n      element = elements[i];\n      priority = priorityFunction(element);\n      if (priority == DROP) {\n        delete this.queuedElements_[this.keyFunction_(element)];\n      } else {\n        priorities[index] = priority;\n        elements[index++] = element;\n      }\n    }\n    elements.length = index;\n    priorities.length = index;\n    this.heapify_();\n  }\n}\n\nexport default PriorityQueue;\n", "/**\n * @module ol/TileQueue\n */\nimport EventType from './events/EventType.js';\nimport PriorityQueue, {DROP} from './structs/PriorityQueue.js';\nimport TileState from './TileState.js';\n\n/**\n * @typedef {function(import(\"./Tile.js\").default, string, import(\"./coordinate.js\").Coordinate, number): number} PriorityFunction\n */\n\nclass TileQueue extends PriorityQueue {\n  /**\n   * @param {PriorityFunction} tilePriorityFunction Tile priority function.\n   * @param {function(): ?} tileChangeCallback Function called on each tile change event.\n   */\n  constructor(tilePriorityFunction, tileChangeCallback) {\n    super(\n      /**\n       * @param {Array} element Element.\n       * @return {number} Priority.\n       */\n      function (element) {\n        return tilePriorityFunction.apply(null, element);\n      },\n      /**\n       * @param {Array} element Element.\n       * @return {string} Key.\n       */\n      function (element) {\n        return /** @type {import(\"./Tile.js\").default} */ (element[0]).getKey();\n      }\n    );\n\n    /** @private */\n    this.boundHandleTileChange_ = this.handleTileChange.bind(this);\n\n    /**\n     * @private\n     * @type {function(): ?}\n     */\n    this.tileChangeCallback_ = tileChangeCallback;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.tilesLoading_ = 0;\n\n    /**\n     * @private\n     * @type {!Object<string,boolean>}\n     */\n    this.tilesLoadingKeys_ = {};\n  }\n\n  /**\n   * @param {Array} element Element.\n   * @return {boolean} The element was added to the queue.\n   */\n  enqueue(element) {\n    const added = super.enqueue(element);\n    if (added) {\n      const tile = element[0];\n      tile.addEventListener(EventType.CHANGE, this.boundHandleTileChange_);\n    }\n    return added;\n  }\n\n  /**\n   * @return {number} Number of tiles loading.\n   */\n  getTilesLoading() {\n    return this.tilesLoading_;\n  }\n\n  /**\n   * @param {import(\"./events/Event.js\").default} event Event.\n   * @protected\n   */\n  handleTileChange(event) {\n    const tile = /** @type {import(\"./Tile.js\").default} */ (event.target);\n    const state = tile.getState();\n    if (\n      state === TileState.LOADED ||\n      state === TileState.ERROR ||\n      state === TileState.EMPTY\n    ) {\n      if (state !== TileState.ERROR) {\n        tile.removeEventListener(EventType.CHANGE, this.boundHandleTileChange_);\n      }\n      const tileKey = tile.getKey();\n      if (tileKey in this.tilesLoadingKeys_) {\n        delete this.tilesLoadingKeys_[tileKey];\n        --this.tilesLoading_;\n      }\n      this.tileChangeCallback_();\n    }\n  }\n\n  /**\n   * @param {number} maxTotalLoading Maximum number tiles to load simultaneously.\n   * @param {number} maxNewLoads Maximum number of new tiles to load.\n   */\n  loadMoreTiles(maxTotalLoading, maxNewLoads) {\n    let newLoads = 0;\n    let state, tile, tileKey;\n    while (\n      this.tilesLoading_ < maxTotalLoading &&\n      newLoads < maxNewLoads &&\n      this.getCount() > 0\n    ) {\n      tile = /** @type {import(\"./Tile.js\").default} */ (this.dequeue()[0]);\n      tileKey = tile.getKey();\n      state = tile.getState();\n      if (state === TileState.IDLE && !(tileKey in this.tilesLoadingKeys_)) {\n        this.tilesLoadingKeys_[tileKey] = true;\n        ++this.tilesLoading_;\n        ++newLoads;\n        tile.load();\n      }\n    }\n  }\n}\n\nexport default TileQueue;\n\n/**\n * @param {import('./Map.js').FrameState} frameState Frame state.\n * @param {import(\"./Tile.js\").default} tile Tile.\n * @param {string} tileSourceKey Tile source key.\n * @param {import(\"./coordinate.js\").Coordinate} tileCenter Tile center.\n * @param {number} tileResolution Tile resolution.\n * @return {number} Tile priority.\n */\nexport function getTilePriority(\n  frameState,\n  tile,\n  tileSourceKey,\n  tileCenter,\n  tileResolution\n) {\n  // Filter out tiles at higher zoom levels than the current zoom level, or that\n  // are outside the visible extent.\n  if (!frameState || !(tileSourceKey in frameState.wantedTiles)) {\n    return DROP;\n  }\n  if (!frameState.wantedTiles[tileSourceKey][tile.getKey()]) {\n    return DROP;\n  }\n  // Prioritize the highest zoom level tiles closest to the focus.\n  // Tiles at higher zoom levels are prioritized using Math.log(tileResolution).\n  // Within a zoom level, tiles are prioritized by the distance in pixels between\n  // the center of the tile and the center of the viewport.  The factor of 65536\n  // means that the prioritization should behave as desired for tiles up to\n  // 65536 * Math.log(2) = 45426 pixels from the focus.\n  const center = frameState.viewState.center;\n  const deltaX = tileCenter[0] - center[0];\n  const deltaY = tileCenter[1] - center[1];\n  return (\n    65536 * Math.log(tileResolution) +\n    Math.sqrt(deltaX * deltaX + deltaY * deltaY) / tileResolution\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;AASO,IAAM,OAAO;AAcpB,IAAM,gBAAN,MAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlB,YAAY,kBAAkB,aAAa;AAKzC,SAAK,oBAAoB;AAMzB,SAAK,eAAe;AAMpB,SAAK,YAAY,CAAC;AAMlB,SAAK,cAAc,CAAC;AAMpB,SAAK,kBAAkB,CAAC;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,SAAK,UAAU,SAAS;AACxB,SAAK,YAAY,SAAS;AAC1B,UAAM,KAAK,eAAe;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,UAAM,WAAW,KAAK;AACtB,UAAM,aAAa,KAAK;AACxB,UAAM,UAAU,SAAS,CAAC;AAC1B,QAAI,SAAS,UAAU,GAAG;AACxB,eAAS,SAAS;AAClB,iBAAW,SAAS;AAAA,IACtB,OAAO;AACL,eAAS,CAAC,IAAI,SAAS,IAAI;AAC3B,iBAAW,CAAC,IAAI,WAAW,IAAI;AAC/B,WAAK,QAAQ,CAAC;AAAA,IAChB;AACA,UAAM,aAAa,KAAK,aAAa,OAAO;AAC5C,WAAO,KAAK,gBAAgB,UAAU;AACtC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,SAAS;AACf,WAAO,EAAE,KAAK,aAAa,OAAO,KAAK,KAAK,kBAAkB,EAAE;AAChE,UAAM,WAAW,KAAK,kBAAkB,OAAO;AAC/C,QAAI,YAAY,MAAM;AACpB,WAAK,UAAU,KAAK,OAAO;AAC3B,WAAK,YAAY,KAAK,QAAQ;AAC9B,WAAK,gBAAgB,KAAK,aAAa,OAAO,CAAC,IAAI;AACnD,WAAK,UAAU,GAAG,KAAK,UAAU,SAAS,CAAC;AAC3C,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB,OAAO;AACxB,WAAO,QAAQ,IAAI;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB,OAAO;AACzB,WAAO,QAAQ,IAAI;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,OAAO;AACrB,WAAQ,QAAQ,KAAM;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,QAAI;AACJ,SAAK,KAAK,KAAK,UAAU,UAAU,KAAK,GAAG,KAAK,GAAG,KAAK;AACtD,WAAK,QAAQ,CAAC;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO,KAAK,UAAU,WAAW;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,KAAK;AACf,WAAO,OAAO,KAAK;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,SAAS;AAChB,WAAO,KAAK,YAAY,KAAK,aAAa,OAAO,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,OAAO;AACb,UAAM,WAAW,KAAK;AACtB,UAAM,aAAa,KAAK;AACxB,UAAM,QAAQ,SAAS;AACvB,UAAM,UAAU,SAAS,KAAK;AAC9B,UAAM,WAAW,WAAW,KAAK;AACjC,UAAM,aAAa;AAEnB,WAAO,QAAQ,SAAS,GAAG;AACzB,YAAM,SAAS,KAAK,mBAAmB,KAAK;AAC5C,YAAM,SAAS,KAAK,oBAAoB,KAAK;AAE7C,YAAM,oBACJ,SAAS,SAAS,WAAW,MAAM,IAAI,WAAW,MAAM,IACpD,SACA;AAEN,eAAS,KAAK,IAAI,SAAS,iBAAiB;AAC5C,iBAAW,KAAK,IAAI,WAAW,iBAAiB;AAChD,cAAQ;AAAA,IACV;AAEA,aAAS,KAAK,IAAI;AAClB,eAAW,KAAK,IAAI;AACpB,SAAK,UAAU,YAAY,KAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,YAAY,OAAO;AAC3B,UAAM,WAAW,KAAK;AACtB,UAAM,aAAa,KAAK;AACxB,UAAM,UAAU,SAAS,KAAK;AAC9B,UAAM,WAAW,WAAW,KAAK;AAEjC,WAAO,QAAQ,YAAY;AACzB,YAAM,cAAc,KAAK,gBAAgB,KAAK;AAC9C,UAAI,WAAW,WAAW,IAAI,UAAU;AACtC,iBAAS,KAAK,IAAI,SAAS,WAAW;AACtC,mBAAW,KAAK,IAAI,WAAW,WAAW;AAC1C,gBAAQ;AAAA,MACV,OAAO;AACL;AAAA,MACF;AAAA,IACF;AACA,aAAS,KAAK,IAAI;AAClB,eAAW,KAAK,IAAI;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,UAAM,mBAAmB,KAAK;AAC9B,UAAM,WAAW,KAAK;AACtB,UAAM,aAAa,KAAK;AACxB,QAAI,QAAQ;AACZ,UAAM,IAAI,SAAS;AACnB,QAAI,SAAS,GAAG;AAChB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,gBAAU,SAAS,CAAC;AACpB,iBAAW,iBAAiB,OAAO;AACnC,UAAI,YAAY,MAAM;AACpB,eAAO,KAAK,gBAAgB,KAAK,aAAa,OAAO,CAAC;AAAA,MACxD,OAAO;AACL,mBAAW,KAAK,IAAI;AACpB,iBAAS,OAAO,IAAI;AAAA,MACtB;AAAA,IACF;AACA,aAAS,SAAS;AAClB,eAAW,SAAS;AACpB,SAAK,SAAS;AAAA,EAChB;AACF;AAEA,IAAO,wBAAQ;;;AC1Pf,IAAM,YAAN,cAAwB,sBAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpC,YAAY,sBAAsB,oBAAoB;AACpD;AAAA;AAAA;AAAA;AAAA;AAAA,MAKE,SAAU,SAAS;AACjB,eAAO,qBAAqB,MAAM,MAAM,OAAO;AAAA,MACjD;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,SAAU,SAAS;AACjB;AAAA;AAAA,UAAmD,QAAQ,CAAC,EAAG,OAAO;AAAA;AAAA,MACxE;AAAA,IACF;AAGA,SAAK,yBAAyB,KAAK,iBAAiB,KAAK,IAAI;AAM7D,SAAK,sBAAsB;AAM3B,SAAK,gBAAgB;AAMrB,SAAK,oBAAoB,CAAC;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,SAAS;AACf,UAAM,QAAQ,MAAM,QAAQ,OAAO;AACnC,QAAI,OAAO;AACT,YAAM,OAAO,QAAQ,CAAC;AACtB,WAAK,iBAAiB,kBAAU,QAAQ,KAAK,sBAAsB;AAAA,IACrE;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,OAAO;AACtB,UAAM;AAAA;AAAA,MAAmD,MAAM;AAAA;AAC/D,UAAM,QAAQ,KAAK,SAAS;AAC5B,QACE,UAAU,kBAAU,UACpB,UAAU,kBAAU,SACpB,UAAU,kBAAU,OACpB;AACA,UAAI,UAAU,kBAAU,OAAO;AAC7B,aAAK,oBAAoB,kBAAU,QAAQ,KAAK,sBAAsB;AAAA,MACxE;AACA,YAAM,UAAU,KAAK,OAAO;AAC5B,UAAI,WAAW,KAAK,mBAAmB;AACrC,eAAO,KAAK,kBAAkB,OAAO;AACrC,UAAE,KAAK;AAAA,MACT;AACA,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,iBAAiB,aAAa;AAC1C,QAAI,WAAW;AACf,QAAI,OAAO,MAAM;AACjB,WACE,KAAK,gBAAgB,mBACrB,WAAW,eACX,KAAK,SAAS,IAAI,GAClB;AACA;AAAA,MAAmD,KAAK,QAAQ,EAAE,CAAC;AACnE,gBAAU,KAAK,OAAO;AACtB,cAAQ,KAAK,SAAS;AACtB,UAAI,UAAU,kBAAU,QAAQ,EAAE,WAAW,KAAK,oBAAoB;AACpE,aAAK,kBAAkB,OAAO,IAAI;AAClC,UAAE,KAAK;AACP,UAAE;AACF,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,oBAAQ;AAUR,SAAS,gBACd,YACA,MACA,eACA,YACA,gBACA;AAGA,MAAI,CAAC,cAAc,EAAE,iBAAiB,WAAW,cAAc;AAC7D,WAAO;AAAA,EACT;AACA,MAAI,CAAC,WAAW,YAAY,aAAa,EAAE,KAAK,OAAO,CAAC,GAAG;AACzD,WAAO;AAAA,EACT;AAOA,QAAM,SAAS,WAAW,UAAU;AACpC,QAAM,SAAS,WAAW,CAAC,IAAI,OAAO,CAAC;AACvC,QAAM,SAAS,WAAW,CAAC,IAAI,OAAO,CAAC;AACvC,SACE,QAAQ,KAAK,IAAI,cAAc,IAC/B,KAAK,KAAK,SAAS,SAAS,SAAS,MAAM,IAAI;AAEnD;", "names": []}