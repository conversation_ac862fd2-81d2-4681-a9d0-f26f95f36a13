/*
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-04-24 09:17:42
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-04-24 10:18:38
 * @FilePath: \uavflight-ui\src\api\map\config.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
/*
 * @Description: 地图配置相关 API
 */
import http from '/@/utils/request';
import { getApiBaseUrl } from '/@/utils/mapConfig';

// 获取基础地图配置
export const getBaseMapConfig = () => {
  // 使用API接口
  const baseUrl = getApiBaseUrl();
  const apiUrl = `${baseUrl}/api/map/base-map`;
  
  return fetch(apiUrl)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      return { data };
    });
};

// 模拟地图配置数据，用于开发测试
export const mockMapConfig = () => {
  return Promise.resolve({
    data: {
      layers: [
        {
          id: "basemap_xyz",
          name: "南宁地图",
          type: "raster",
          initialLoad: true,
          url: "http://127.0.0.1:8083/geoserver/drone/wms",
          layers: "drone:geotiff_coverage",
          parameters: {
            service: "WMS",
            format: "image/png",
            transparent: true
          }
        },
        {
          id: "wmts_layer",
          name: "WMTS底图",
          type: "raster",
          protocol: "WMTS",
          initialLoad: true,
          workspace: "drone",
          layerName: "nanning",
          extent: [107.5, 21.5, 113.5, 26.5]
        },
        {
          id: "roads_vector",
          name: "地块网络",
          type: "vector",
          protocol: "GeoJSON",
          url: "http://127.0.0.1:8083/geoserver/drone/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=drone%3Aarea2&maxFeatures=50&outputFormat=application%2Fjson",
          initialLoad: true
        },
        {
          id: "tasks_polygons",
          name: "任务区域",
          type: "vector",
          protocol: "WFS",
          url: "https://your-geoserver.com/geoserver/your_ws/ows",
          initialLoad: false,
          trigger: {
            event: "taskSelected",
            params: ["taskId"]
          },
          wfsParams: {
            service: "WFS",
            version: "1.1.0",
            request: "GetFeature",
            typeName: "your_ws:task_areas",
            outputFormat: "application/json",
            CQL_FILTER: "task_id='{taskId}'"
          },
          style: {
            stroke: {
              color: "#0066ff",
              width: 3
            },
            fill: {
              color: "#0066ff88"
            }
          }
        }
      ]
    }
  });
}; 