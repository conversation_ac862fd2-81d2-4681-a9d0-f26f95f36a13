{"version": 3, "sources": ["../../ol/VectorTile.js", "../../ol/VectorRenderTile.js", "../../ol/source/VectorTile.js"], "sourcesContent": ["/**\n * @module ol/VectorTile\n */\nimport Tile from './Tile.js';\nimport TileState from './TileState.js';\n\nclass VectorTile extends Tile {\n  /**\n   * @param {import(\"./tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n   * @param {import(\"./TileState.js\").default} state State.\n   * @param {string} src Data source url.\n   * @param {import(\"./format/Feature.js\").default} format Feature format.\n   * @param {import(\"./Tile.js\").LoadFunction} tileLoadFunction Tile load function.\n   * @param {import(\"./Tile.js\").Options} [options] Tile options.\n   */\n  constructor(tileCoord, state, src, format, tileLoadFunction, options) {\n    super(tileCoord, state, options);\n\n    /**\n     * Extent of this tile; set by the source.\n     * @type {import(\"./extent.js\").Extent}\n     */\n    this.extent = null;\n\n    /**\n     * @private\n     * @type {import(\"./format/Feature.js\").default}\n     */\n    this.format_ = format;\n\n    /**\n     * @private\n     * @type {Array<import(\"./Feature.js\").default>}\n     */\n    this.features_ = null;\n\n    /**\n     * @private\n     * @type {import(\"./featureloader.js\").FeatureLoader}\n     */\n    this.loader_;\n\n    /**\n     * Feature projection of this tile; set by the source.\n     * @type {import(\"./proj/Projection.js\").default}\n     */\n    this.projection = null;\n\n    /**\n     * Resolution of this tile; set by the source.\n     * @type {number}\n     */\n    this.resolution;\n\n    /**\n     * @private\n     * @type {import(\"./Tile.js\").LoadFunction}\n     */\n    this.tileLoadFunction_ = tileLoadFunction;\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.url_ = src;\n\n    this.key = src;\n  }\n\n  /**\n   * Get the feature format assigned for reading this tile's features.\n   * @return {import(\"./format/Feature.js\").default} Feature format.\n   * @api\n   */\n  getFormat() {\n    return this.format_;\n  }\n\n  /**\n   * Get the features for this tile. Geometries will be in the view projection.\n   * @return {Array<import(\"./Feature.js\").FeatureLike>} Features.\n   * @api\n   */\n  getFeatures() {\n    return this.features_;\n  }\n\n  /**\n   * Load not yet loaded URI.\n   */\n  load() {\n    if (this.state == TileState.IDLE) {\n      this.setState(TileState.LOADING);\n      this.tileLoadFunction_(this, this.url_);\n      if (this.loader_) {\n        this.loader_(this.extent, this.resolution, this.projection);\n      }\n    }\n  }\n\n  /**\n   * Handler for successful tile load.\n   * @param {Array<import(\"./Feature.js\").default>} features The loaded features.\n   * @param {import(\"./proj/Projection.js\").default} dataProjection Data projection.\n   */\n  onLoad(features, dataProjection) {\n    this.setFeatures(features);\n  }\n\n  /**\n   * Handler for tile load errors.\n   */\n  onError() {\n    this.setState(TileState.ERROR);\n  }\n\n  /**\n   * Function for use in an {@link module:ol/source/VectorTile~VectorTile}'s `tileLoadFunction`.\n   * Sets the features for the tile.\n   * @param {Array<import(\"./Feature.js\").default>} features Features.\n   * @api\n   */\n  setFeatures(features) {\n    this.features_ = features;\n    this.setState(TileState.LOADED);\n  }\n\n  /**\n   * Set the feature loader for reading this tile's features.\n   * @param {import(\"./featureloader.js\").FeatureLoader} loader Feature loader.\n   * @api\n   */\n  setLoader(loader) {\n    this.loader_ = loader;\n  }\n}\n\nexport default VectorTile;\n", "/**\n * @module ol/VectorRenderTile\n */\nimport Tile from './Tile.js';\nimport {createCanvasContext2D, releaseCanvas} from './dom.js';\nimport {getUid} from './util.js';\n\n/**\n * @typedef {Object} ReplayState\n * @property {boolean} dirty Dirty.\n * @property {null|import(\"./render.js\").OrderFunction} renderedRenderOrder RenderedRenderOrder.\n * @property {number} renderedTileRevision RenderedTileRevision.\n * @property {number} renderedResolution RenderedResolution.\n * @property {number} renderedRevision RenderedRevision.\n * @property {number} renderedTileResolution RenderedTileResolution.\n * @property {number} renderedTileZ RenderedTileZ.\n */\n\n/**\n * @type {Array<HTMLCanvasElement>}\n */\nconst canvasPool = [];\n\nclass VectorRenderTile extends Tile {\n  /**\n   * @param {import(\"./tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n   * @param {import(\"./TileState.js\").default} state State.\n   * @param {import(\"./tilecoord.js\").TileCoord} urlTileCoord Wrapped tile coordinate for source urls.\n   * @param {function(VectorRenderTile):Array<import(\"./VectorTile\").default>} getSourceTiles Function\n   * to get source tiles for this tile.\n   */\n  constructor(tileCoord, state, urlTileCoord, getSourceTiles) {\n    super(tileCoord, state, {transition: 0});\n\n    /**\n     * @private\n     * @type {!Object<string, CanvasRenderingContext2D>}\n     */\n    this.context_ = {};\n\n    /**\n     * Executor groups by layer uid. Entries are read/written by the renderer.\n     * @type {Object<string, Array<import(\"./render/canvas/ExecutorGroup.js\").default>>}\n     */\n    this.executorGroups = {};\n\n    /**\n     * Executor groups for decluttering, by layer uid. Entries are read/written by the renderer.\n     * @type {Object<string, Array<import(\"./render/canvas/ExecutorGroup.js\").default>>}\n     */\n    this.declutterExecutorGroups = {};\n\n    /**\n     * Number of loading source tiles. Read/written by the source.\n     * @type {number}\n     */\n    this.loadingSourceTiles = 0;\n\n    /**\n     * @type {Object<number, ImageData>}\n     */\n    this.hitDetectionImageData = {};\n\n    /**\n     * @private\n     * @type {!Object<string, ReplayState>}\n     */\n    this.replayState_ = {};\n\n    /**\n     * @type {Array<import(\"./VectorTile.js\").default>}\n     */\n    this.sourceTiles = [];\n\n    /**\n     * @type {Object<string, boolean>}\n     */\n    this.errorTileKeys = {};\n\n    /**\n     * @type {number}\n     */\n    this.wantedResolution;\n\n    /**\n     * @type {!function():Array<import(\"./VectorTile.js\").default>}\n     */\n    this.getSourceTiles = getSourceTiles.bind(undefined, this);\n\n    /**\n     * @type {import(\"./tilecoord.js\").TileCoord}\n     */\n    this.wrappedTileCoord = urlTileCoord;\n  }\n\n  /**\n   * @param {import(\"./layer/Layer.js\").default} layer Layer.\n   * @return {CanvasRenderingContext2D} The rendering context.\n   */\n  getContext(layer) {\n    const key = getUid(layer);\n    if (!(key in this.context_)) {\n      this.context_[key] = createCanvasContext2D(1, 1, canvasPool);\n    }\n    return this.context_[key];\n  }\n\n  /**\n   * @param {import(\"./layer/Layer.js\").default} layer Layer.\n   * @return {boolean} Tile has a rendering context for the given layer.\n   */\n  hasContext(layer) {\n    return getUid(layer) in this.context_;\n  }\n\n  /**\n   * Get the Canvas for this tile.\n   * @param {import(\"./layer/Layer.js\").default} layer Layer.\n   * @return {HTMLCanvasElement} Canvas.\n   */\n  getImage(layer) {\n    return this.hasContext(layer) ? this.getContext(layer).canvas : null;\n  }\n\n  /**\n   * @param {import(\"./layer/Layer.js\").default} layer Layer.\n   * @return {ReplayState} The replay state.\n   */\n  getReplayState(layer) {\n    const key = getUid(layer);\n    if (!(key in this.replayState_)) {\n      this.replayState_[key] = {\n        dirty: false,\n        renderedRenderOrder: null,\n        renderedResolution: NaN,\n        renderedRevision: -1,\n        renderedTileResolution: NaN,\n        renderedTileRevision: -1,\n        renderedTileZ: -1,\n      };\n    }\n    return this.replayState_[key];\n  }\n\n  /**\n   * Load the tile.\n   */\n  load() {\n    this.getSourceTiles();\n  }\n\n  /**\n   * Remove from the cache due to expiry\n   */\n  release() {\n    for (const key in this.context_) {\n      const context = this.context_[key];\n      releaseCanvas(context);\n      canvasPool.push(context.canvas);\n      delete this.context_[key];\n    }\n    super.release();\n  }\n}\n\nexport default VectorRenderTile;\n", "/**\n * @module ol/source/VectorTile\n */\n\nimport EventType from '../events/EventType.js';\nimport Tile from '../VectorTile.js';\nimport TileCache from '../TileCache.js';\nimport TileGrid from '../tilegrid/TileGrid.js';\nimport TileState from '../TileState.js';\nimport UrlTile from './UrlTile.js';\nimport VectorRenderTile from '../VectorRenderTile.js';\nimport {DEFAULT_MAX_ZOOM} from '../tilegrid/common.js';\nimport {\n  buffer as bufferExtent,\n  getIntersection,\n  intersects,\n} from '../extent.js';\nimport {createXYZ, extentFromProjection} from '../tilegrid.js';\nimport {fromKey, getCacheKeyForTileKey, getKeyZXY} from '../tilecoord.js';\nimport {isEmpty} from '../obj.js';\nimport {loadFeaturesXhr} from '../featureloader.js';\nimport {toSize} from '../size.js';\n\n/**\n * @typedef {Object} Options\n * @property {import(\"./Source.js\").AttributionLike} [attributions] Attributions.\n * @property {boolean} [attributionsCollapsible=true] Attributions are collapsible.\n * @property {number} [cacheSize] Initial tile cache size. Will auto-grow to hold at least twice the number of tiles in the viewport.\n * @property {import(\"../extent.js\").Extent} [extent] Extent.\n * @property {import(\"../format/Feature.js\").default} [format] Feature format for tiles. Used and required by the default.\n * @property {boolean} [overlaps=true] This source may have overlapping geometries. Setting this\n * to `false` (e.g. for sources with polygons that represent administrative\n * boundaries or TopoJSON sources) allows the renderer to optimise fill and\n * stroke operations.\n * @property {import(\"../proj.js\").ProjectionLike} [projection='EPSG:3857'] Projection of the tile grid.\n * @property {import(\"./Source.js\").State} [state] Source state.\n * @property {typeof import(\"../VectorTile.js\").default} [tileClass] Class used to instantiate image tiles.\n * Default is {@link module:ol/VectorTile~VectorTile}.\n * @property {number} [maxZoom=22] Optional max zoom level. Not used if `tileGrid` is provided.\n * @property {number} [minZoom] Optional min zoom level. Not used if `tileGrid` is provided.\n * @property {number|import(\"../size.js\").Size} [tileSize=512] Optional tile size. Not used if `tileGrid` is provided.\n * @property {number} [maxResolution] Optional tile grid resolution at level zero. Not used if `tileGrid` is provided.\n * @property {import(\"../tilegrid/TileGrid.js\").default} [tileGrid] Tile grid.\n * @property {import(\"../Tile.js\").LoadFunction} [tileLoadFunction]\n * Optional function to load a tile given a URL. Could look like this for pbf tiles:\n * ```js\n * function(tile, url) {\n *   tile.setLoader(function(extent, resolution, projection) {\n *     fetch(url).then(function(response) {\n *       response.arrayBuffer().then(function(data) {\n *         const format = tile.getFormat() // ol/format/MVT configured as source format\n *         const features = format.readFeatures(data, {\n *           extent: extent,\n *           featureProjection: projection\n *         });\n *         tile.setFeatures(features);\n *       });\n *     });\n *   });\n * }\n * ```\n * If you do not need extent, resolution and projection to get the features for a tile (e.g.\n * for GeoJSON tiles), your `tileLoadFunction` does not need a `setLoader()` call. Only make sure\n * to call `setFeatures()` on the tile:\n * ```js\n * const format = new GeoJSON({featureProjection: map.getView().getProjection()});\n * async function tileLoadFunction(tile, url) {\n *   const response = await fetch(url);\n *   const data = await response.json();\n *   tile.setFeatures(format.readFeatures(data));\n * }\n * ```\n * @property {import(\"../Tile.js\").UrlFunction} [tileUrlFunction] Optional function to get tile URL given a tile coordinate and the projection.\n * @property {string} [url] URL template. Must include `{x}`, `{y}` or `{-y}`, and `{z}` placeholders.\n * A `{?-?}` template pattern, for example `subdomain{a-f}.domain.com`, may be\n * used instead of defining each one separately in the `urls` option.\n * @property {number} [transition] A duration for tile opacity\n * transitions in milliseconds. A duration of 0 disables the opacity transition.\n * @property {Array<string>} [urls] An array of URL templates.\n * @property {boolean} [wrapX=true] Whether to wrap the world horizontally.\n * When set to `false`, only one world\n * will be rendered. When set to `true`, tiles will be wrapped horizontally to\n * render multiple worlds.\n * @property {number|import(\"../array.js\").NearestDirectionFunction} [zDirection=1]\n * Choose whether to use tiles with a higher or lower zoom level when between integer\n * zoom levels. See {@link module:ol/tilegrid/TileGrid~TileGrid#getZForResolution}.\n */\n\n/**\n * @classdesc\n * Class for layer sources providing vector data divided into a tile grid, to be\n * used with {@link module:ol/layer/VectorTile~VectorTileLayer}. Although this source receives tiles\n * with vector features from the server, it is not meant for feature editing.\n * Features are optimized for rendering, their geometries are clipped at or near\n * tile boundaries and simplified for a view resolution. See\n * {@link module:ol/source/Vector~VectorSource} for vector sources that are suitable for feature\n * editing.\n *\n * @fires import(\"./Tile.js\").TileSourceEvent\n * @api\n */\nclass VectorTile extends UrlTile {\n  /**\n   * @param {!Options} options Vector tile options.\n   */\n  constructor(options) {\n    const projection = options.projection || 'EPSG:3857';\n\n    const extent = options.extent || extentFromProjection(projection);\n\n    const tileGrid =\n      options.tileGrid ||\n      createXYZ({\n        extent: extent,\n        maxResolution: options.maxResolution,\n        maxZoom: options.maxZoom !== undefined ? options.maxZoom : 22,\n        minZoom: options.minZoom,\n        tileSize: options.tileSize || 512,\n      });\n\n    super({\n      attributions: options.attributions,\n      attributionsCollapsible: options.attributionsCollapsible,\n      cacheSize: options.cacheSize,\n      interpolate: true,\n      opaque: false,\n      projection: projection,\n      state: options.state,\n      tileGrid: tileGrid,\n      tileLoadFunction: options.tileLoadFunction\n        ? options.tileLoadFunction\n        : defaultLoadFunction,\n      tileUrlFunction: options.tileUrlFunction,\n      url: options.url,\n      urls: options.urls,\n      wrapX: options.wrapX === undefined ? true : options.wrapX,\n      transition: options.transition,\n      zDirection: options.zDirection === undefined ? 1 : options.zDirection,\n    });\n\n    /**\n     * @private\n     * @type {import(\"../format/Feature.js\").default|null}\n     */\n    this.format_ = options.format ? options.format : null;\n\n    /**\n     * @private\n     * @type {TileCache}\n     */\n    this.sourceTileCache = new TileCache(this.tileCache.highWaterMark);\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.overlaps_ = options.overlaps == undefined ? true : options.overlaps;\n\n    /**\n     * @protected\n     * @type {typeof import(\"../VectorTile.js\").default}\n     */\n    this.tileClass = options.tileClass ? options.tileClass : Tile;\n\n    /**\n     * @private\n     * @type {Object<string, import(\"../tilegrid/TileGrid.js\").default>}\n     */\n    this.tileGrids_ = {};\n  }\n\n  /**\n   * Get features whose bounding box intersects the provided extent. Only features for cached\n   * tiles for the last rendered zoom level are available in the source. So this method is only\n   * suitable for requesting tiles for extents that are currently rendered.\n   *\n   * Features are returned in random tile order and as they are included in the tiles. This means\n   * they can be clipped, duplicated across tiles, and simplified to the render resolution.\n   *\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {Array<import(\"../Feature.js\").FeatureLike>} Features.\n   * @api\n   */\n  getFeaturesInExtent(extent) {\n    const features = [];\n    const tileCache = this.tileCache;\n    if (tileCache.getCount() === 0) {\n      return features;\n    }\n    const z = fromKey(tileCache.peekFirstKey())[0];\n    const tileGrid = this.tileGrid;\n    tileCache.forEach(function (tile) {\n      if (tile.tileCoord[0] !== z || tile.getState() !== TileState.LOADED) {\n        return;\n      }\n      const sourceTiles = tile.getSourceTiles();\n      for (let i = 0, ii = sourceTiles.length; i < ii; ++i) {\n        const sourceTile = sourceTiles[i];\n        const tileCoord = sourceTile.tileCoord;\n        if (intersects(extent, tileGrid.getTileCoordExtent(tileCoord))) {\n          const tileFeatures = sourceTile.getFeatures();\n          if (tileFeatures) {\n            for (let j = 0, jj = tileFeatures.length; j < jj; ++j) {\n              const candidate = tileFeatures[j];\n              const geometry = candidate.getGeometry();\n              if (intersects(extent, geometry.getExtent())) {\n                features.push(candidate);\n              }\n            }\n          }\n        }\n      }\n    });\n    return features;\n  }\n\n  /**\n   * @return {boolean} The source can have overlapping geometries.\n   */\n  getOverlaps() {\n    return this.overlaps_;\n  }\n\n  /**\n   * clear {@link module:ol/TileCache~TileCache} and delete all source tiles\n   * @api\n   */\n  clear() {\n    this.tileCache.clear();\n    this.sourceTileCache.clear();\n  }\n\n  /**\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @param {!Object<string, boolean>} usedTiles Used tiles.\n   */\n  expireCache(projection, usedTiles) {\n    const tileCache = this.getTileCacheForProjection(projection);\n    const usedSourceTiles = Object.keys(usedTiles).reduce((acc, key) => {\n      const cacheKey = getCacheKeyForTileKey(key);\n      const tile = tileCache.peek(cacheKey);\n      if (tile) {\n        const sourceTiles = tile.sourceTiles;\n        for (let i = 0, ii = sourceTiles.length; i < ii; ++i) {\n          acc[sourceTiles[i].getKey()] = true;\n        }\n      }\n      return acc;\n    }, {});\n    super.expireCache(projection, usedTiles);\n    this.sourceTileCache.expireCache(usedSourceTiles);\n  }\n\n  /**\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../proj/Projection\").default} projection Projection.\n   * @param {VectorRenderTile} tile Vector image tile.\n   * @return {Array<import(\"../VectorTile\").default>} Tile keys.\n   */\n  getSourceTiles(pixelRatio, projection, tile) {\n    if (tile.getState() === TileState.IDLE) {\n      tile.setState(TileState.LOADING);\n      const urlTileCoord = tile.wrappedTileCoord;\n      const tileGrid = this.getTileGridForProjection(projection);\n      const extent = tileGrid.getTileCoordExtent(urlTileCoord);\n      const z = urlTileCoord[0];\n      const resolution = tileGrid.getResolution(z);\n      // make extent 1 pixel smaller so we don't load tiles for < 0.5 pixel render space\n      bufferExtent(extent, -resolution, extent);\n      const sourceTileGrid = this.tileGrid;\n      const sourceExtent = sourceTileGrid.getExtent();\n      if (sourceExtent) {\n        getIntersection(extent, sourceExtent, extent);\n      }\n      const sourceZ = sourceTileGrid.getZForResolution(\n        resolution,\n        this.zDirection\n      );\n\n      sourceTileGrid.forEachTileCoord(extent, sourceZ, (sourceTileCoord) => {\n        const tileUrl = this.tileUrlFunction(\n          sourceTileCoord,\n          pixelRatio,\n          projection\n        );\n        const sourceTile = this.sourceTileCache.containsKey(tileUrl)\n          ? this.sourceTileCache.get(tileUrl)\n          : new this.tileClass(\n              sourceTileCoord,\n              tileUrl ? TileState.IDLE : TileState.EMPTY,\n              tileUrl,\n              this.format_,\n              this.tileLoadFunction\n            );\n        tile.sourceTiles.push(sourceTile);\n        const sourceTileState = sourceTile.getState();\n        if (sourceTileState < TileState.LOADED) {\n          const listenChange = (event) => {\n            this.handleTileChange(event);\n            const state = sourceTile.getState();\n            if (state === TileState.LOADED || state === TileState.ERROR) {\n              const sourceTileKey = sourceTile.getKey();\n              if (sourceTileKey in tile.errorTileKeys) {\n                if (sourceTile.getState() === TileState.LOADED) {\n                  delete tile.errorTileKeys[sourceTileKey];\n                }\n              } else {\n                tile.loadingSourceTiles--;\n              }\n              if (state === TileState.ERROR) {\n                tile.errorTileKeys[sourceTileKey] = true;\n              } else {\n                sourceTile.removeEventListener(EventType.CHANGE, listenChange);\n              }\n              if (tile.loadingSourceTiles === 0) {\n                tile.setState(\n                  isEmpty(tile.errorTileKeys)\n                    ? TileState.LOADED\n                    : TileState.ERROR\n                );\n              }\n            }\n          };\n          sourceTile.addEventListener(EventType.CHANGE, listenChange);\n          tile.loadingSourceTiles++;\n        }\n        if (sourceTileState === TileState.IDLE) {\n          sourceTile.extent =\n            sourceTileGrid.getTileCoordExtent(sourceTileCoord);\n          sourceTile.projection = projection;\n          sourceTile.resolution = sourceTileGrid.getResolution(\n            sourceTileCoord[0]\n          );\n          this.sourceTileCache.set(tileUrl, sourceTile);\n          sourceTile.load();\n        }\n      });\n      if (!tile.loadingSourceTiles) {\n        tile.setState(\n          tile.sourceTiles.some(\n            (sourceTile) => sourceTile.getState() === TileState.ERROR\n          )\n            ? TileState.ERROR\n            : TileState.LOADED\n        );\n      }\n    }\n\n    return tile.sourceTiles;\n  }\n\n  /**\n   * @param {number} z Tile coordinate z.\n   * @param {number} x Tile coordinate x.\n   * @param {number} y Tile coordinate y.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {!VectorRenderTile} Tile.\n   */\n  getTile(z, x, y, pixelRatio, projection) {\n    const coordKey = getKeyZXY(z, x, y);\n    const key = this.getKey();\n    let tile;\n    if (this.tileCache.containsKey(coordKey)) {\n      tile = this.tileCache.get(coordKey);\n      if (tile.key === key) {\n        return tile;\n      }\n    }\n    const tileCoord = [z, x, y];\n    let urlTileCoord = this.getTileCoordForTileUrlFunction(\n      tileCoord,\n      projection\n    );\n    const sourceExtent = this.getTileGrid().getExtent();\n    const tileGrid = this.getTileGridForProjection(projection);\n    if (urlTileCoord && sourceExtent) {\n      const tileExtent = tileGrid.getTileCoordExtent(urlTileCoord);\n      // make extent 1 pixel smaller so we don't load tiles for < 0.5 pixel render space\n      bufferExtent(tileExtent, -tileGrid.getResolution(z), tileExtent);\n      if (!intersects(sourceExtent, tileExtent)) {\n        urlTileCoord = null;\n      }\n    }\n    let empty = true;\n    if (urlTileCoord !== null) {\n      const sourceTileGrid = this.tileGrid;\n      const resolution = tileGrid.getResolution(z);\n      const sourceZ = sourceTileGrid.getZForResolution(resolution, 1);\n      // make extent 1 pixel smaller so we don't load tiles for < 0.5 pixel render space\n      const extent = tileGrid.getTileCoordExtent(urlTileCoord);\n      bufferExtent(extent, -resolution, extent);\n      sourceTileGrid.forEachTileCoord(extent, sourceZ, (sourceTileCoord) => {\n        empty =\n          empty &&\n          !this.tileUrlFunction(sourceTileCoord, pixelRatio, projection);\n      });\n    }\n    const newTile = new VectorRenderTile(\n      tileCoord,\n      empty ? TileState.EMPTY : TileState.IDLE,\n      urlTileCoord,\n      this.getSourceTiles.bind(this, pixelRatio, projection)\n    );\n\n    newTile.key = key;\n    if (tile) {\n      newTile.interimTile = tile;\n      newTile.refreshInterimChain();\n      this.tileCache.replace(coordKey, newTile);\n    } else {\n      this.tileCache.set(coordKey, newTile);\n    }\n    return newTile;\n  }\n\n  /**\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {!import(\"../tilegrid/TileGrid.js\").default} Tile grid.\n   */\n  getTileGridForProjection(projection) {\n    const code = projection.getCode();\n    let tileGrid = this.tileGrids_[code];\n    if (!tileGrid) {\n      // A tile grid that matches the tile size of the source tile grid is more\n      // likely to have 1:1 relationships between source tiles and rendered tiles.\n      const sourceTileGrid = this.tileGrid;\n      const resolutions = sourceTileGrid.getResolutions().slice();\n      const origins = resolutions.map(function (resolution, z) {\n        return sourceTileGrid.getOrigin(z);\n      });\n      const tileSizes = resolutions.map(function (resolution, z) {\n        return sourceTileGrid.getTileSize(z);\n      });\n      const length = DEFAULT_MAX_ZOOM + 1;\n      for (let z = resolutions.length; z < length; ++z) {\n        resolutions.push(resolutions[z - 1] / 2);\n        origins.push(origins[z - 1]);\n        tileSizes.push(tileSizes[z - 1]);\n      }\n      tileGrid = new TileGrid({\n        extent: sourceTileGrid.getExtent(),\n        origins: origins,\n        resolutions: resolutions,\n        tileSizes: tileSizes,\n      });\n      this.tileGrids_[code] = tileGrid;\n    }\n    return tileGrid;\n  }\n\n  /**\n   * Get the tile pixel ratio for this source.\n   * @param {number} pixelRatio Pixel ratio.\n   * @return {number} Tile pixel ratio.\n   */\n  getTilePixelRatio(pixelRatio) {\n    return pixelRatio;\n  }\n\n  /**\n   * @param {number} z Z.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {import(\"../size.js\").Size} Tile size.\n   */\n  getTilePixelSize(z, pixelRatio, projection) {\n    const tileGrid = this.getTileGridForProjection(projection);\n    const tileSize = toSize(tileGrid.getTileSize(z), this.tmpSize);\n    return [\n      Math.round(tileSize[0] * pixelRatio),\n      Math.round(tileSize[1] * pixelRatio),\n    ];\n  }\n\n  /**\n   * Increases the cache size if needed\n   * @param {number} tileCount Minimum number of tiles needed.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   */\n  updateCacheSize(tileCount, projection) {\n    super.updateCacheSize(tileCount * 2, projection);\n    this.sourceTileCache.highWaterMark =\n      this.getTileCacheForProjection(projection).highWaterMark;\n  }\n}\n\nexport default VectorTile;\n\n/**\n * Sets the loader for a tile.\n * @param {import(\"../VectorTile.js\").default} tile Vector tile.\n * @param {string} url URL.\n */\nexport function defaultLoadFunction(tile, url) {\n  tile.setLoader(\n    /**\n     * @param {import(\"../extent.js\").Extent} extent Extent.\n     * @param {number} resolution Resolution.\n     * @param {import(\"../proj/Projection.js\").default} projection Projection.\n     */\n    function (extent, resolution, projection) {\n      loadFeaturesXhr(\n        url,\n        tile.getFormat(),\n        extent,\n        resolution,\n        projection,\n        tile.onLoad.bind(tile),\n        tile.onError.bind(tile)\n      );\n    }\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,aAAN,cAAyB,aAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS5B,YAAY,WAAW,OAAO,KAAK,QAAQ,kBAAkB,SAAS;AACpE,UAAM,WAAW,OAAO,OAAO;AAM/B,SAAK,SAAS;AAMd,SAAK,UAAU;AAMf,SAAK,YAAY;AAMjB,SAAK;AAML,SAAK,aAAa;AAMlB,SAAK;AAML,SAAK,oBAAoB;AAMzB,SAAK,OAAO;AAEZ,SAAK,MAAM;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,QAAI,KAAK,SAAS,kBAAU,MAAM;AAChC,WAAK,SAAS,kBAAU,OAAO;AAC/B,WAAK,kBAAkB,MAAM,KAAK,IAAI;AACtC,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK,UAAU;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,UAAU,gBAAgB;AAC/B,SAAK,YAAY,QAAQ;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,SAAS,kBAAU,KAAK;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,UAAU;AACpB,SAAK,YAAY;AACjB,SAAK,SAAS,kBAAU,MAAM;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ;AAChB,SAAK,UAAU;AAAA,EACjB;AACF;AAEA,IAAO,qBAAQ;;;ACpHf,IAAM,aAAa,CAAC;AAEpB,IAAM,mBAAN,cAA+B,aAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlC,YAAY,WAAW,OAAO,cAAc,gBAAgB;AAC1D,UAAM,WAAW,OAAO,EAAC,YAAY,EAAC,CAAC;AAMvC,SAAK,WAAW,CAAC;AAMjB,SAAK,iBAAiB,CAAC;AAMvB,SAAK,0BAA0B,CAAC;AAMhC,SAAK,qBAAqB;AAK1B,SAAK,wBAAwB,CAAC;AAM9B,SAAK,eAAe,CAAC;AAKrB,SAAK,cAAc,CAAC;AAKpB,SAAK,gBAAgB,CAAC;AAKtB,SAAK;AAKL,SAAK,iBAAiB,eAAe,KAAK,QAAW,IAAI;AAKzD,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,OAAO;AAChB,UAAM,MAAM,OAAO,KAAK;AACxB,QAAI,EAAE,OAAO,KAAK,WAAW;AAC3B,WAAK,SAAS,GAAG,IAAI,sBAAsB,GAAG,GAAG,UAAU;AAAA,IAC7D;AACA,WAAO,KAAK,SAAS,GAAG;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,OAAO;AAChB,WAAO,OAAO,KAAK,KAAK,KAAK;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,OAAO;AACd,WAAO,KAAK,WAAW,KAAK,IAAI,KAAK,WAAW,KAAK,EAAE,SAAS;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,OAAO;AACpB,UAAM,MAAM,OAAO,KAAK;AACxB,QAAI,EAAE,OAAO,KAAK,eAAe;AAC/B,WAAK,aAAa,GAAG,IAAI;AAAA,QACvB,OAAO;AAAA,QACP,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,kBAAkB;AAAA,QAClB,wBAAwB;AAAA,QACxB,sBAAsB;AAAA,QACtB,eAAe;AAAA,MACjB;AAAA,IACF;AACA,WAAO,KAAK,aAAa,GAAG;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,eAAW,OAAO,KAAK,UAAU;AAC/B,YAAM,UAAU,KAAK,SAAS,GAAG;AACjC,oBAAc,OAAO;AACrB,iBAAW,KAAK,QAAQ,MAAM;AAC9B,aAAO,KAAK,SAAS,GAAG;AAAA,IAC1B;AACA,UAAM,QAAQ;AAAA,EAChB;AACF;AAEA,IAAO,2BAAQ;;;AChEf,IAAMA,cAAN,cAAyB,gBAAQ;AAAA;AAAA;AAAA;AAAA,EAI/B,YAAY,SAAS;AACnB,UAAM,aAAa,QAAQ,cAAc;AAEzC,UAAM,SAAS,QAAQ,UAAU,qBAAqB,UAAU;AAEhE,UAAM,WACJ,QAAQ,YACR,UAAU;AAAA,MACR;AAAA,MACA,eAAe,QAAQ;AAAA,MACvB,SAAS,QAAQ,YAAY,SAAY,QAAQ,UAAU;AAAA,MAC3D,SAAS,QAAQ;AAAA,MACjB,UAAU,QAAQ,YAAY;AAAA,IAChC,CAAC;AAEH,UAAM;AAAA,MACJ,cAAc,QAAQ;AAAA,MACtB,yBAAyB,QAAQ;AAAA,MACjC,WAAW,QAAQ;AAAA,MACnB,aAAa;AAAA,MACb,QAAQ;AAAA,MACR;AAAA,MACA,OAAO,QAAQ;AAAA,MACf;AAAA,MACA,kBAAkB,QAAQ,mBACtB,QAAQ,mBACR;AAAA,MACJ,iBAAiB,QAAQ;AAAA,MACzB,KAAK,QAAQ;AAAA,MACb,MAAM,QAAQ;AAAA,MACd,OAAO,QAAQ,UAAU,SAAY,OAAO,QAAQ;AAAA,MACpD,YAAY,QAAQ;AAAA,MACpB,YAAY,QAAQ,eAAe,SAAY,IAAI,QAAQ;AAAA,IAC7D,CAAC;AAMD,SAAK,UAAU,QAAQ,SAAS,QAAQ,SAAS;AAMjD,SAAK,kBAAkB,IAAI,kBAAU,KAAK,UAAU,aAAa;AAMjE,SAAK,YAAY,QAAQ,YAAY,SAAY,OAAO,QAAQ;AAMhE,SAAK,YAAY,QAAQ,YAAY,QAAQ,YAAY;AAMzD,SAAK,aAAa,CAAC;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,oBAAoB,QAAQ;AAC1B,UAAM,WAAW,CAAC;AAClB,UAAM,YAAY,KAAK;AACvB,QAAI,UAAU,SAAS,MAAM,GAAG;AAC9B,aAAO;AAAA,IACT;AACA,UAAM,IAAI,QAAQ,UAAU,aAAa,CAAC,EAAE,CAAC;AAC7C,UAAM,WAAW,KAAK;AACtB,cAAU,QAAQ,SAAU,MAAM;AAChC,UAAI,KAAK,UAAU,CAAC,MAAM,KAAK,KAAK,SAAS,MAAM,kBAAU,QAAQ;AACnE;AAAA,MACF;AACA,YAAM,cAAc,KAAK,eAAe;AACxC,eAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,cAAM,aAAa,YAAY,CAAC;AAChC,cAAM,YAAY,WAAW;AAC7B,YAAI,WAAW,QAAQ,SAAS,mBAAmB,SAAS,CAAC,GAAG;AAC9D,gBAAM,eAAe,WAAW,YAAY;AAC5C,cAAI,cAAc;AAChB,qBAAS,IAAI,GAAG,KAAK,aAAa,QAAQ,IAAI,IAAI,EAAE,GAAG;AACrD,oBAAM,YAAY,aAAa,CAAC;AAChC,oBAAM,WAAW,UAAU,YAAY;AACvC,kBAAI,WAAW,QAAQ,SAAS,UAAU,CAAC,GAAG;AAC5C,yBAAS,KAAK,SAAS;AAAA,cACzB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACN,SAAK,UAAU,MAAM;AACrB,SAAK,gBAAgB,MAAM;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,YAAY,WAAW;AACjC,UAAM,YAAY,KAAK,0BAA0B,UAAU;AAC3D,UAAM,kBAAkB,OAAO,KAAK,SAAS,EAAE,OAAO,CAAC,KAAK,QAAQ;AAClE,YAAM,WAAW,sBAAsB,GAAG;AAC1C,YAAM,OAAO,UAAU,KAAK,QAAQ;AACpC,UAAI,MAAM;AACR,cAAM,cAAc,KAAK;AACzB,iBAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,cAAI,YAAY,CAAC,EAAE,OAAO,CAAC,IAAI;AAAA,QACjC;AAAA,MACF;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AACL,UAAM,YAAY,YAAY,SAAS;AACvC,SAAK,gBAAgB,YAAY,eAAe;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,YAAY,YAAY,MAAM;AAC3C,QAAI,KAAK,SAAS,MAAM,kBAAU,MAAM;AACtC,WAAK,SAAS,kBAAU,OAAO;AAC/B,YAAM,eAAe,KAAK;AAC1B,YAAM,WAAW,KAAK,yBAAyB,UAAU;AACzD,YAAM,SAAS,SAAS,mBAAmB,YAAY;AACvD,YAAM,IAAI,aAAa,CAAC;AACxB,YAAM,aAAa,SAAS,cAAc,CAAC;AAE3C,aAAa,QAAQ,CAAC,YAAY,MAAM;AACxC,YAAM,iBAAiB,KAAK;AAC5B,YAAM,eAAe,eAAe,UAAU;AAC9C,UAAI,cAAc;AAChB,wBAAgB,QAAQ,cAAc,MAAM;AAAA,MAC9C;AACA,YAAM,UAAU,eAAe;AAAA,QAC7B;AAAA,QACA,KAAK;AAAA,MACP;AAEA,qBAAe,iBAAiB,QAAQ,SAAS,CAAC,oBAAoB;AACpE,cAAM,UAAU,KAAK;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,cAAM,aAAa,KAAK,gBAAgB,YAAY,OAAO,IACvD,KAAK,gBAAgB,IAAI,OAAO,IAChC,IAAI,KAAK;AAAA,UACP;AAAA,UACA,UAAU,kBAAU,OAAO,kBAAU;AAAA,UACrC;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AACJ,aAAK,YAAY,KAAK,UAAU;AAChC,cAAM,kBAAkB,WAAW,SAAS;AAC5C,YAAI,kBAAkB,kBAAU,QAAQ;AACtC,gBAAM,eAAe,CAAC,UAAU;AAC9B,iBAAK,iBAAiB,KAAK;AAC3B,kBAAM,QAAQ,WAAW,SAAS;AAClC,gBAAI,UAAU,kBAAU,UAAU,UAAU,kBAAU,OAAO;AAC3D,oBAAM,gBAAgB,WAAW,OAAO;AACxC,kBAAI,iBAAiB,KAAK,eAAe;AACvC,oBAAI,WAAW,SAAS,MAAM,kBAAU,QAAQ;AAC9C,yBAAO,KAAK,cAAc,aAAa;AAAA,gBACzC;AAAA,cACF,OAAO;AACL,qBAAK;AAAA,cACP;AACA,kBAAI,UAAU,kBAAU,OAAO;AAC7B,qBAAK,cAAc,aAAa,IAAI;AAAA,cACtC,OAAO;AACL,2BAAW,oBAAoB,kBAAU,QAAQ,YAAY;AAAA,cAC/D;AACA,kBAAI,KAAK,uBAAuB,GAAG;AACjC,qBAAK;AAAA,kBACH,QAAQ,KAAK,aAAa,IACtB,kBAAU,SACV,kBAAU;AAAA,gBAChB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,qBAAW,iBAAiB,kBAAU,QAAQ,YAAY;AAC1D,eAAK;AAAA,QACP;AACA,YAAI,oBAAoB,kBAAU,MAAM;AACtC,qBAAW,SACT,eAAe,mBAAmB,eAAe;AACnD,qBAAW,aAAa;AACxB,qBAAW,aAAa,eAAe;AAAA,YACrC,gBAAgB,CAAC;AAAA,UACnB;AACA,eAAK,gBAAgB,IAAI,SAAS,UAAU;AAC5C,qBAAW,KAAK;AAAA,QAClB;AAAA,MACF,CAAC;AACD,UAAI,CAAC,KAAK,oBAAoB;AAC5B,aAAK;AAAA,UACH,KAAK,YAAY;AAAA,YACf,CAAC,eAAe,WAAW,SAAS,MAAM,kBAAU;AAAA,UACtD,IACI,kBAAU,QACV,kBAAU;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAEA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QAAQ,GAAG,GAAG,GAAG,YAAY,YAAY;AACvC,UAAM,WAAW,UAAU,GAAG,GAAG,CAAC;AAClC,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI;AACJ,QAAI,KAAK,UAAU,YAAY,QAAQ,GAAG;AACxC,aAAO,KAAK,UAAU,IAAI,QAAQ;AAClC,UAAI,KAAK,QAAQ,KAAK;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AACA,UAAM,YAAY,CAAC,GAAG,GAAG,CAAC;AAC1B,QAAI,eAAe,KAAK;AAAA,MACtB;AAAA,MACA;AAAA,IACF;AACA,UAAM,eAAe,KAAK,YAAY,EAAE,UAAU;AAClD,UAAM,WAAW,KAAK,yBAAyB,UAAU;AACzD,QAAI,gBAAgB,cAAc;AAChC,YAAM,aAAa,SAAS,mBAAmB,YAAY;AAE3D,aAAa,YAAY,CAAC,SAAS,cAAc,CAAC,GAAG,UAAU;AAC/D,UAAI,CAAC,WAAW,cAAc,UAAU,GAAG;AACzC,uBAAe;AAAA,MACjB;AAAA,IACF;AACA,QAAI,QAAQ;AACZ,QAAI,iBAAiB,MAAM;AACzB,YAAM,iBAAiB,KAAK;AAC5B,YAAM,aAAa,SAAS,cAAc,CAAC;AAC3C,YAAM,UAAU,eAAe,kBAAkB,YAAY,CAAC;AAE9D,YAAM,SAAS,SAAS,mBAAmB,YAAY;AACvD,aAAa,QAAQ,CAAC,YAAY,MAAM;AACxC,qBAAe,iBAAiB,QAAQ,SAAS,CAAC,oBAAoB;AACpE,gBACE,SACA,CAAC,KAAK,gBAAgB,iBAAiB,YAAY,UAAU;AAAA,MACjE,CAAC;AAAA,IACH;AACA,UAAM,UAAU,IAAI;AAAA,MAClB;AAAA,MACA,QAAQ,kBAAU,QAAQ,kBAAU;AAAA,MACpC;AAAA,MACA,KAAK,eAAe,KAAK,MAAM,YAAY,UAAU;AAAA,IACvD;AAEA,YAAQ,MAAM;AACd,QAAI,MAAM;AACR,cAAQ,cAAc;AACtB,cAAQ,oBAAoB;AAC5B,WAAK,UAAU,QAAQ,UAAU,OAAO;AAAA,IAC1C,OAAO;AACL,WAAK,UAAU,IAAI,UAAU,OAAO;AAAA,IACtC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,YAAY;AACnC,UAAM,OAAO,WAAW,QAAQ;AAChC,QAAI,WAAW,KAAK,WAAW,IAAI;AACnC,QAAI,CAAC,UAAU;AAGb,YAAM,iBAAiB,KAAK;AAC5B,YAAM,cAAc,eAAe,eAAe,EAAE,MAAM;AAC1D,YAAM,UAAU,YAAY,IAAI,SAAU,YAAY,GAAG;AACvD,eAAO,eAAe,UAAU,CAAC;AAAA,MACnC,CAAC;AACD,YAAM,YAAY,YAAY,IAAI,SAAU,YAAY,GAAG;AACzD,eAAO,eAAe,YAAY,CAAC;AAAA,MACrC,CAAC;AACD,YAAM,SAAS,mBAAmB;AAClC,eAAS,IAAI,YAAY,QAAQ,IAAI,QAAQ,EAAE,GAAG;AAChD,oBAAY,KAAK,YAAY,IAAI,CAAC,IAAI,CAAC;AACvC,gBAAQ,KAAK,QAAQ,IAAI,CAAC,CAAC;AAC3B,kBAAU,KAAK,UAAU,IAAI,CAAC,CAAC;AAAA,MACjC;AACA,iBAAW,IAAI,iBAAS;AAAA,QACtB,QAAQ,eAAe,UAAU;AAAA,QACjC;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,WAAK,WAAW,IAAI,IAAI;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,YAAY;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,GAAG,YAAY,YAAY;AAC1C,UAAM,WAAW,KAAK,yBAAyB,UAAU;AACzD,UAAM,WAAW,OAAO,SAAS,YAAY,CAAC,GAAG,KAAK,OAAO;AAC7D,WAAO;AAAA,MACL,KAAK,MAAM,SAAS,CAAC,IAAI,UAAU;AAAA,MACnC,KAAK,MAAM,SAAS,CAAC,IAAI,UAAU;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,WAAW,YAAY;AACrC,UAAM,gBAAgB,YAAY,GAAG,UAAU;AAC/C,SAAK,gBAAgB,gBACnB,KAAK,0BAA0B,UAAU,EAAE;AAAA,EAC/C;AACF;AAEA,IAAOC,sBAAQD;AAOR,SAAS,oBAAoB,MAAM,KAAK;AAC7C,OAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMH,SAAU,QAAQ,YAAY,YAAY;AACxC;AAAA,QACE;AAAA,QACA,KAAK,UAAU;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK,OAAO,KAAK,IAAI;AAAA,QACrB,KAAK,QAAQ,KAAK,IAAI;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACF;", "names": ["VectorTile", "VectorTile_default"]}