import * as Cesium from 'cesium';
import qiche from '/@/assets/icon/汽车.png';
import guoshu from '/@/assets/icon/果树.png';
import anshu from '/@/assets/icon/桉树.png';
import icon from '/@/assets/icon/Info-Point-yellow.png';
// 导入模型路径
// import anshuModel from '/@/assets/model/anshu.glb';
// 使用字符串路径代替直接导入
const ANSHU_MODEL_PATH = '/@/assets/model/anshu.glb';
const GUOSHU_MODEL_PATH = '/@/assets/model/guoshu.glb';
// 导入地形采样器
import TerrainSampler from '../cesium/TerrainSampler';

interface PointData {
	[key: string]: {
		frame: number;
		x: number;
		y: number;
		width: number;
		height: number;
		confidence: number;
		timestamp: string;
		longitude: number;
		latitude: number;
		altitude: number;
		area: number;
		coordList: number[];
		start_time: string;
		end_time: string;
		[key: string]: any;
	}
}

interface JsonData {
	[category: string]: {
		[id: string]: PointData
	}
}

interface PointOptions {
	clampToGround?: boolean; // 是否贴地
	heightReference?: number; // Cesium.HeightReference 枚举值
	modelScale?: number; // 模型缩放
	billboardScale?: number; // 图片缩放
}

class CesiumPointManager {
	viewer: Cesium.Viewer;
	dataSource: Cesium.CustomDataSource;
	clickHandler: Cesium.ScreenSpaceEventHandler | null;
	modelPrimitives: Cesium.PrimitiveCollection; // 用于存储模型primitives的集合
	// 存储模型ID和属性的映射关系
	modelPropertiesMap: Map<any, any>;
	// 使用模型的标志
	useModels: boolean = false; // 默认使用图片加载
	// 贴地选项
	pointOptions: PointOptions = {
		clampToGround: true, // 默认贴地
		heightReference: Cesium.HeightReference.CLAMP_TO_GROUND, // 默认贴地
		modelScale: 1.5, // 默认模型缩放
		billboardScale: 1.2 // 默认图片缩放
	};
	// 地形采样器
	terrainSampler: TerrainSampler;

	/**
	 * 构造函数
	 * @param {Cesium.Viewer} viewer - Cesium 视图对象
	 * @param {PointOptions} options - 点配置选项
	 */
	constructor(viewer: Cesium.Viewer, options?: PointOptions) {
		this.viewer = viewer;
		
		// 初始化地形采样器
		this.terrainSampler = new TerrainSampler(viewer);
		
		// 初始化模型集合，确保它先于数据源创建，这样可以正确维护渲染顺序
		this.modelPrimitives = new Cesium.PrimitiveCollection(); 
		this.viewer.scene.primitives.add(this.modelPrimitives);
		
		// 初始化数据源，在模型之后添加确保正确的渲染顺序
		this.dataSource = new Cesium.CustomDataSource('icons');
		this.viewer.dataSources.add(this.dataSource);
		
		// 确保深度测试正常工作
		this.viewer.scene.globe.depthTestAgainstTerrain = true;
		
		this.clickHandler = null;
		this.modelPropertiesMap = new Map();
		
		// 合并选项
		if (options) {
			this.pointOptions = { ...this.pointOptions, ...options };
		}
	}

	/**
	 * 设置点配置选项
	 * @param {PointOptions} options - 点配置选项
	 */
	setPointOptions(options: PointOptions): void {
		this.pointOptions = { ...this.pointOptions, ...options };
		
		// 如果有加载的点，则重新加载以应用新选项
		if (this._currentPoints && this._currentPoints.length > 0) {
			this.loadLabels(this._currentPoints);
		}
	}

	/**
	 * 设置是否贴地显示
	 * @param {boolean} clampToGround - 是否贴地
	 */
	setClampToGround(clampToGround: boolean): void {
		this.pointOptions.clampToGround = clampToGround;
		this.pointOptions.heightReference = clampToGround ? 
			Cesium.HeightReference.CLAMP_TO_GROUND : Cesium.HeightReference.NONE;
		
		// 重新加载点以应用新设置
		if (this._currentPoints && this._currentPoints.length > 0) {
			this.loadLabels(this._currentPoints);
		}
	}

	/**
	 * 切换显示模式（图片/模型）
	 * @param {boolean} useModels - 是否使用3D模型
	 * @returns {boolean} 切换后的状态
	 */
	toggleDisplayMode(useModels?: boolean): boolean {
		// 如果提供了参数，直接使用；否则切换当前状态
		if (useModels !== undefined) {
			this.useModels = useModels;
		} else {
			this.useModels = !this.useModels;
		}
		
		// 重新加载当前数据
		if (this._currentPoints && this._currentPoints.length > 0) {
			this.loadLabels(this._currentPoints);
		}
		
		return this.useModels;
	}
	
	// 存储当前加载的点数据，便于模式切换时重新加载
	private _currentPoints: any[] = [];

	/**
	 * 从JSON文件加载特定类别的点数据
	 * @param {string} jsonPath - JSON文件路径
	 * @param {string[]} categories - 要加载的类别数组，例如 ["4", "5"]
	 */
	async loadFromJsonByCategories(jsonPath: string, categories: string[]): Promise<void> {
		try {
			// 清除现有数据
			this.dataSource.entities.removeAll();
			this.modelPrimitives.removeAll();
			this.modelPropertiesMap.clear();
			this._currentPoints = []; // 清空当前点数据缓存
			
			// 获取JSON数据
			const response = await fetch(jsonPath);
			if (!response.ok) {
				throw new Error(`无法加载JSON文件: ${response.statusText}`);
			}
			
			const jsonData: JsonData = await response.json();
			
			// 保存所有要加载的点
			const pointsToLoad: any[] = [];
			
			// 遍历需要的类别
			categories.forEach(category => {
				if (jsonData[category]) {
					// 遍历该类别下的所有ID
					Object.keys(jsonData[category]).forEach(id => {
						const pointData = jsonData[category][id];
						// 构建与原loadLabels兼容的点数据结构
						pointsToLoad.push({
							videoProcessingList: {
								longitude: pointData.longitude,
								latitude: pointData.latitude
							},
							videoObjectId: `${category}-${id}`,
							// 将原始数据也存入properties以便使用
							originalData: pointData,
							category: category,
							id: id
						});
					});
				}
			});
			
			// 使用现有方法加载点
			if (pointsToLoad.length > 0) {
				this._currentPoints = pointsToLoad; // 保存加载的点数据
				this.loadLabels(pointsToLoad);
			}
		} catch (error) {
			console.error('加载JSON数据时出错:', error);
		}
	}

	/**
	 * 加载图片标注到 Cesium 地图
	 * @param {Array} points - 包含图片标注数据的数组
	 */
	loadLabels(points: any[]): void {
		// 清除现有数据
		this.dataSource.entities.removeAll();
		this.modelPrimitives.removeAll();
		this.modelPropertiesMap.clear();
		
		// 保存当前加载的点数据
		this._currentPoints = [...points];
		
		// 首先加载所有模型，以确保模型在billboard之前渲染
		if (this.useModels) {
			points.forEach((point) => {
				const { longitude, latitude } = point.videoProcessingList;
				const category = point.category;
				
				// 检查是否有模型可用
				const hasModel: boolean = category === '1' || category === '0';
				
				if (hasModel) {
					// 使用primitives方式加载模型
					const modelUri = category === '1' ? ANSHU_MODEL_PATH : GUOSHU_MODEL_PATH;
					
					// 创建模型primitive
					const modelPrimitive = this.createModelPrimitive(
						longitude,
						latitude,
						modelUri,
						point
					);
					
					// 将模型添加到模型集合中
					if (modelPrimitive) {
						const primitiveId = this.modelPrimitives.add(modelPrimitive);
						// 存储primitive的ID和对应的属性信息
						this.modelPropertiesMap.set(primitiveId, point);
					}
				}
			});
		}
		
		// 然后加载所有billboard
		points.forEach((point) => {
			const { longitude, latitude } = point.videoProcessingList;
			const { videoObjectId } = point;
			const category = point.category; // 获取类别
			
			// 如果已经加载为模型，则跳过billboard创建
			const hasModel: boolean = category === '1' || category === '0';
			if (this.useModels && hasModel) {
				return;
			}
			
			// 根据类别选择不同的图标
			let iconImage = icon; // 默认图标
			
			if (category === '0') {
				iconImage = guoshu;
			} else if (category === '1') {
				iconImage = anshu;
			} else if (category === '4') {
				iconImage = qiche;
			}

			// 创建billboard配置
			const billboardOptions: any = {
				image: iconImage,
				width: 48,
				height: 48,
				scale: this.pointOptions.billboardScale,
				verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
				horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
				eyeOffset: new Cesium.Cartesian3(0, 0, 0), // 设置为零向量
				// 不使用heightReference，我们会手动设置高度
				// heightReference: this.pointOptions.heightReference, 
			};
			
			// 设置深度测试距离为0，这将确保billboard正确参与深度测试
			// Cesium要求disableDepthTestDistance必须大于或等于0.0
			billboardOptions.disableDepthTestDistance = 0;
			
			const lon = parseFloat(longitude);
			const lat = parseFloat(latitude);
			
			// 使用地形采样器获取高度
			if (this.pointOptions.clampToGround) {
				// 先用默认高度创建实体
				const entity = this.dataSource.entities.add({
					position: Cesium.Cartesian3.fromDegrees(lon, lat),
					billboard: billboardOptions,
					properties: point,
				});
				
				// 异步获取地形高度，然后更新实体位置
				this.terrainSampler.getTerrainHeight(lon, lat).then((height: number) => {
					// 确保实体还存在且有效
					if (entity && this.dataSource.entities.contains(entity)) {
						entity.position = new Cesium.ConstantPositionProperty(
							Cesium.Cartesian3.fromDegrees(lon, lat, height)
						);
					}
				}).catch(() => {
					// 如果获取地形高度失败，保持原有高度
				});
			} else {
				// 不贴地时直接添加
				this.dataSource.entities.add({
					position: Cesium.Cartesian3.fromDegrees(lon, lat),
					billboard: billboardOptions,
					properties: point,
				});
			}
		});
		
		this.enableClustering(false);
	}

	/**
	 * 创建模型Primitive
	 * @param {number|string} longitude - 经度
	 * @param {number|string} latitude - 纬度
	 * @param {string} modelUri - 模型路径
	 * @param {any} properties - 属性数据
	 * @returns {Cesium.Model} 创建的模型primitive
	 */
	createModelPrimitive(longitude: string | number, latitude: string | number, modelUri: string, properties: any): Cesium.Model {
		// 解析经纬度
		const lon = parseFloat(longitude as string);
		const lat = parseFloat(latitude as string);
		
		// 创建模型矩阵，用于确定模型的位置和方向
		let position = Cesium.Cartesian3.fromDegrees(lon, lat);
		let modelMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(position);
		
		// 如果需要贴地，则使用地形采样器获取地形高度
		if (this.pointOptions.clampToGround) {
			// 创建一个Promise来处理地形高度获取
			this.terrainSampler.getTerrainHeight(lon, lat).then((height: number) => {
				// 重新计算包含地形高度的位置
				position = Cesium.Cartesian3.fromDegrees(lon, lat, height);
				// 更新模型矩阵
				modelMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(position);
				
				// 应用更新后的位置到模型
				if (this.modelPrimitives && !this.modelPrimitives.isDestroyed()) {
					// 查找现有模型并更新其矩阵
					for (let i = 0; i < this.modelPrimitives.length; i++) {
						const model = this.modelPrimitives.get(i);
						if (model && model.id === properties) {
							model.modelMatrix = modelMatrix;
							break;
						}
					}
				}
			}).catch(() => {
				// 如果获取地形高度失败，保持原始位置不变
			});
		}
		
		// 创建模型选项
		const modelOptions: any = {
			url: modelUri,
			modelMatrix: modelMatrix,
			scale: this.pointOptions.modelScale,
			minimumPixelSize: 32,
			maximumScale: 20000,
			allowPicking: true, // 允许选择模型
			id: properties, // 将属性数据附加到模型上，以便在点击时使用
		};
		
		// 创建模型实例
		return Cesium.Model.fromGltf(modelOptions);
	}

	/**
	 * 获取当前显示模式状态
	 * @returns {boolean} true表示使用模型模式，false表示使用图片模式
	 */
	getDisplayMode(): boolean {
		return this.useModels;
	}

	/**
	 * 获取当前贴地状态
	 * @returns {boolean} 是否启用贴地
	 */
	getClampToGround(): boolean {
		return this.pointOptions.clampToGround === undefined ? true : this.pointOptions.clampToGround;
	}

	/**
	 * 卸载所有图片标注数据
	 */
	unloadLabels(): void {
		this.dataSource.entities.removeAll();
		this.modelPrimitives.removeAll(); // 同时清除模型primitives
		this.modelPropertiesMap.clear(); // 清除属性映射
		this._currentPoints = []; // 清空当前点数据缓存
	}

	/**
	 * 启用或禁用图片标注的聚类功能，并美化聚类视觉效果
	 * @param {boolean} enable - 是否启用聚类
	 */
	enableClustering(enable: boolean): void {
		this.dataSource.clustering.enabled = enable;
		this.dataSource.clustering.pixelRange = 100;
		this.dataSource.clustering.minimumClusterSize = 20;

		// 自定义聚类视觉样式
		this.dataSource.clustering.clusterEvent.addEventListener((clusteredEntities: any, cluster: any) => {
			cluster.billboard.show = false; // 不使用图标

			cluster.label.show = true;
			cluster.label.text = clusteredEntities.length.toString();
			cluster.label.fillColor = Cesium.Color.WHITE;
			cluster.label.font = 'bold 16px sans-serif';
			cluster.label.verticalOrigin = Cesium.VerticalOrigin.CENTER;
			cluster.label.horizontalOrigin = Cesium.HorizontalOrigin.CENTER;
			cluster.label.pixelOffset = new Cesium.Cartesian2(0, -5);
			cluster.label.backgroundColor = new Cesium.Color(1, 0.2, 0.2, 1); // 深色半透明背景
			cluster.label.showBackground = true;
			cluster.label.padding = new Cesium.Cartesian2(8, 5);
			
			// 设置深度测试距离为0，确保标签正确参与深度测试
			if (typeof cluster.label.disableDepthTestDistance !== 'undefined') {
				cluster.label.disableDepthTestDistance = 0;
			}
			
			// 如果启用了贴地，也为聚类标签设置高度参考
			if (this.pointOptions.clampToGround) {
				cluster.label.heightReference = this.pointOptions.heightReference;
			}
		});
	}

	/**
	 * 添加点击事件处理器
	 * @param {Function} callback - 点击图片标注时调用的回调函数，参数为标注的属性
	 */
	addClickEventHandler(callback: (properties: any) => void): void {
		if (this.clickHandler) {
			this.clickHandler.destroy();
		}
		this.clickHandler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
		this.clickHandler.setInputAction((event: any) => {
			// 检查是否点击了实体
			const pickedObject = this.viewer.scene.pick(event.position);
			if (Cesium.defined(pickedObject)) {
				// 如果是实体
				if (pickedObject.id && pickedObject.id.properties) {
					callback(pickedObject.id.properties);
				} 
				// 如果是primitive模型
				else if (pickedObject.primitive && pickedObject.primitive.id) {
					const modelProps = pickedObject.primitive.id;
					if (modelProps) {
						callback(modelProps);
					}
				}
			}
		}, Cesium.ScreenSpaceEventType.LEFT_CLICK);
	}

	/**
	 * 移除点击事件处理器
	 */
	removeClickEventHandler(): void {
		if (this.clickHandler) {
			this.clickHandler.destroy();
			this.clickHandler = null;
		}
	}

	/**
	 * 设置图层可见性
	 * @param {boolean} visible - 是否可见
	 */
	setVisibility(visible: boolean): void {
		// 设置所有实体的可见性
		this.dataSource.entities.values.forEach((entity) => {
			if (entity.billboard) {
				entity.billboard.show = new Cesium.ConstantProperty(visible);
			}
			if (entity.label) {
				entity.label.show = new Cesium.ConstantProperty(visible);
			}
		});

		// 设置所有模型的可见性
		for (let i = 0; i < this.modelPrimitives.length; i++) {
			const primitive = this.modelPrimitives.get(i);
			if (primitive) {
				primitive.show = visible;
			}
		}
	}
}

export default CesiumPointManager;
