import request from "/@/utils/request"

/**
 * 获取列表数据
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function getBusinessTypeList(query?: Object) {
  return request({
    url: '/admin/businessType/list',
    method: 'get',
    params: query
  })
}

/**
 * 获取事件列表数据
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
 export function getBusinessEventListByType(query?: Object) {
  return request({
    url: '/admin/businessEvent/list',
    method: 'get',
    params: query
  })
}
