define(["./Matrix3-a348023f","./defaultValue-0a909f67","./EllipseGeometry-29874fdd","./VertexFormat-ab2e00e6","./Math-e97915da","./Transforms-01e95659","./Matrix2-7146c9ca","./RuntimeError-06c93819","./combine-ca22a614","./ComponentDatatype-77274976","./WebGLConstants-a8cc3e8c","./EllipseGeometryLibrary-e689e77b","./GeometryAttribute-f5d71750","./GeometryAttributes-f06a2792","./GeometryInstance-99908f4f","./GeometryOffsetAttribute-04332ce7","./GeometryPipeline-049a5b67","./AttributeCompression-50c9aeba","./EncodedCartesian3-0fb84db0","./IndexDatatype-2149f06c","./IntersectionTests-0bb04fde","./Plane-8575e17c"],(function(e,t,i,r,o,n,a,s,l,m,d,c,u,p,y,_,x,G,h,f,g,E){"use strict";function b(e){const r=(e=t.defaultValue(e,t.defaultValue.EMPTY_OBJECT)).radius,o={center:e.center,semiMajorAxis:r,semiMinorAxis:r,ellipsoid:e.ellipsoid,height:e.height,extrudedHeight:e.extrudedHeight,granularity:e.granularity,vertexFormat:e.vertexFormat,stRotation:e.stRotation,shadowVolume:e.shadowVolume};this._ellipseGeometry=new i.EllipseGeometry(o),this._workerName="createCircleGeometry"}b.packedLength=i.EllipseGeometry.packedLength,b.pack=function(e,t,r){return i.EllipseGeometry.pack(e._ellipseGeometry,t,r)};const w=new i.EllipseGeometry({center:new e.Cartesian3,semiMajorAxis:1,semiMinorAxis:1}),A={center:new e.Cartesian3,radius:void 0,ellipsoid:e.Ellipsoid.clone(e.Ellipsoid.UNIT_SPHERE),height:void 0,extrudedHeight:void 0,granularity:void 0,vertexFormat:new r.VertexFormat,stRotation:void 0,semiMajorAxis:void 0,semiMinorAxis:void 0,shadowVolume:void 0};return b.unpack=function(o,n,a){const s=i.EllipseGeometry.unpack(o,n,w);return A.center=e.Cartesian3.clone(s._center,A.center),A.ellipsoid=e.Ellipsoid.clone(s._ellipsoid,A.ellipsoid),A.height=s._height,A.extrudedHeight=s._extrudedHeight,A.granularity=s._granularity,A.vertexFormat=r.VertexFormat.clone(s._vertexFormat,A.vertexFormat),A.stRotation=s._stRotation,A.shadowVolume=s._shadowVolume,t.defined(a)?(A.semiMajorAxis=s._semiMajorAxis,A.semiMinorAxis=s._semiMinorAxis,a._ellipseGeometry=new i.EllipseGeometry(A),a):(A.radius=s._semiMajorAxis,new b(A))},b.createGeometry=function(e){return i.EllipseGeometry.createGeometry(e._ellipseGeometry)},b.createShadowVolume=function(e,t,i){const o=e._ellipseGeometry._granularity,n=e._ellipseGeometry._ellipsoid,a=t(o,n),s=i(o,n);return new b({center:e._ellipseGeometry._center,radius:e._ellipseGeometry._semiMajorAxis,ellipsoid:n,stRotation:e._ellipseGeometry._stRotation,granularity:o,extrudedHeight:a,height:s,vertexFormat:r.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(b.prototype,{rectangle:{get:function(){return this._ellipseGeometry.rectangle}},textureCoordinateRotationPoints:{get:function(){return this._ellipseGeometry.textureCoordinateRotationPoints}}}),function(i,r){return t.defined(r)&&(i=b.unpack(i,r)),i._ellipseGeometry._center=e.Cartesian3.clone(i._ellipseGeometry._center),i._ellipseGeometry._ellipsoid=e.Ellipsoid.clone(i._ellipseGeometry._ellipsoid),b.createGeometry(i)}}));
