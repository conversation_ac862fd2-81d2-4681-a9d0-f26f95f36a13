{"version": 3, "sources": ["../../geotiff/dist-module/predictor.js", "../../geotiff/dist-module/compression/basedecoder.js"], "sourcesContent": ["function decodeRowAcc(row, stride) {\n  let length = row.length - stride;\n  let offset = 0;\n  do {\n    for (let i = stride; i > 0; i--) {\n      row[offset + stride] += row[offset];\n      offset++;\n    }\n\n    length -= stride;\n  } while (length > 0);\n}\n\nfunction decodeRowFloatingPoint(row, stride, bytesPerSample) {\n  let index = 0;\n  let count = row.length;\n  const wc = count / bytesPerSample;\n\n  while (count > stride) {\n    for (let i = stride; i > 0; --i) {\n      row[index + stride] += row[index];\n      ++index;\n    }\n    count -= stride;\n  }\n\n  const copy = row.slice();\n  for (let i = 0; i < wc; ++i) {\n    for (let b = 0; b < bytesPerSample; ++b) {\n      row[(bytesPerSample * i) + b] = copy[((bytesPerSample - b - 1) * wc) + i];\n    }\n  }\n}\n\nexport function applyPredictor(block, predictor, width, height, bitsPerSample,\n  planarConfiguration) {\n  if (!predictor || predictor === 1) {\n    return block;\n  }\n\n  for (let i = 0; i < bitsPerSample.length; ++i) {\n    if (bitsPerSample[i] % 8 !== 0) {\n      throw new Error('When decoding with predictor, only multiple of 8 bits are supported.');\n    }\n    if (bitsPerSample[i] !== bitsPerSample[0]) {\n      throw new Error('When decoding with predictor, all samples must have the same size.');\n    }\n  }\n\n  const bytesPerSample = bitsPerSample[0] / 8;\n  const stride = planarConfiguration === 2 ? 1 : bitsPerSample.length;\n\n  for (let i = 0; i < height; ++i) {\n    // Last strip will be truncated if height % stripHeight != 0\n    if (i * stride * width * bytesPerSample >= block.byteLength) {\n      break;\n    }\n    let row;\n    if (predictor === 2) { // horizontal prediction\n      switch (bitsPerSample[0]) {\n        case 8:\n          row = new Uint8Array(\n            block, i * stride * width * bytesPerSample, stride * width * bytesPerSample,\n          );\n          break;\n        case 16:\n          row = new Uint16Array(\n            block, i * stride * width * bytesPerSample, stride * width * bytesPerSample / 2,\n          );\n          break;\n        case 32:\n          row = new Uint32Array(\n            block, i * stride * width * bytesPerSample, stride * width * bytesPerSample / 4,\n          );\n          break;\n        default:\n          throw new Error(`Predictor 2 not allowed with ${bitsPerSample[0]} bits per sample.`);\n      }\n      decodeRowAcc(row, stride, bytesPerSample);\n    } else if (predictor === 3) { // horizontal floating point\n      row = new Uint8Array(\n        block, i * stride * width * bytesPerSample, stride * width * bytesPerSample,\n      );\n      decodeRowFloatingPoint(row, stride, bytesPerSample);\n    }\n  }\n  return block;\n}\n", "import { applyPredictor } from '../predictor.js';\n\nexport default class BaseDecoder {\n  async decode(fileDirectory, buffer) {\n    const decoded = await this.decodeBlock(buffer);\n    const predictor = fileDirectory.Predictor || 1;\n    if (predictor !== 1) {\n      const isTiled = !fileDirectory.StripOffsets;\n      const tileWidth = isTiled ? fileDirectory.TileWidth : fileDirectory.ImageWidth;\n      const tileHeight = isTiled ? fileDirectory.TileLength : (\n        fileDirectory.RowsPerStrip || fileDirectory.ImageLength\n      );\n      return applyPredictor(\n        decoded, predictor, tileWidth, tileHeight, fileDirectory.BitsPerSample,\n        fileDirectory.PlanarConfiguration,\n      );\n    }\n    return decoded;\n  }\n}\n"], "mappings": ";AAAA,SAAS,aAAa,KAAK,QAAQ;AACjC,MAAI,SAAS,IAAI,SAAS;AAC1B,MAAI,SAAS;AACb,KAAG;AACD,aAAS,IAAI,QAAQ,IAAI,GAAG,KAAK;AAC/B,UAAI,SAAS,MAAM,KAAK,IAAI,MAAM;AAClC;AAAA,IACF;AAEA,cAAU;AAAA,EACZ,SAAS,SAAS;AACpB;AAEA,SAAS,uBAAuB,KAAK,QAAQ,gBAAgB;AAC3D,MAAI,QAAQ;AACZ,MAAI,QAAQ,IAAI;AAChB,QAAM,KAAK,QAAQ;AAEnB,SAAO,QAAQ,QAAQ;AACrB,aAAS,IAAI,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC/B,UAAI,QAAQ,MAAM,KAAK,IAAI,KAAK;AAChC,QAAE;AAAA,IACJ;AACA,aAAS;AAAA,EACX;AAEA,QAAM,OAAO,IAAI,MAAM;AACvB,WAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,aAAS,IAAI,GAAG,IAAI,gBAAgB,EAAE,GAAG;AACvC,UAAK,iBAAiB,IAAK,CAAC,IAAI,MAAO,iBAAiB,IAAI,KAAK,KAAM,CAAC;AAAA,IAC1E;AAAA,EACF;AACF;AAEO,SAAS,eAAe,OAAO,WAAW,OAAO,QAAQ,eAC9D,qBAAqB;AACrB,MAAI,CAAC,aAAa,cAAc,GAAG;AACjC,WAAO;AAAA,EACT;AAEA,WAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,EAAE,GAAG;AAC7C,QAAI,cAAc,CAAC,IAAI,MAAM,GAAG;AAC9B,YAAM,IAAI,MAAM,sEAAsE;AAAA,IACxF;AACA,QAAI,cAAc,CAAC,MAAM,cAAc,CAAC,GAAG;AACzC,YAAM,IAAI,MAAM,oEAAoE;AAAA,IACtF;AAAA,EACF;AAEA,QAAM,iBAAiB,cAAc,CAAC,IAAI;AAC1C,QAAM,SAAS,wBAAwB,IAAI,IAAI,cAAc;AAE7D,WAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAE/B,QAAI,IAAI,SAAS,QAAQ,kBAAkB,MAAM,YAAY;AAC3D;AAAA,IACF;AACA,QAAI;AACJ,QAAI,cAAc,GAAG;AACnB,cAAQ,cAAc,CAAC,GAAG;AAAA,QACxB,KAAK;AACH,gBAAM,IAAI;AAAA,YACR;AAAA,YAAO,IAAI,SAAS,QAAQ;AAAA,YAAgB,SAAS,QAAQ;AAAA,UAC/D;AACA;AAAA,QACF,KAAK;AACH,gBAAM,IAAI;AAAA,YACR;AAAA,YAAO,IAAI,SAAS,QAAQ;AAAA,YAAgB,SAAS,QAAQ,iBAAiB;AAAA,UAChF;AACA;AAAA,QACF,KAAK;AACH,gBAAM,IAAI;AAAA,YACR;AAAA,YAAO,IAAI,SAAS,QAAQ;AAAA,YAAgB,SAAS,QAAQ,iBAAiB;AAAA,UAChF;AACA;AAAA,QACF;AACE,gBAAM,IAAI,MAAM,gCAAgC,cAAc,CAAC,CAAC,mBAAmB;AAAA,MACvF;AACA,mBAAa,KAAK,QAAQ,cAAc;AAAA,IAC1C,WAAW,cAAc,GAAG;AAC1B,YAAM,IAAI;AAAA,QACR;AAAA,QAAO,IAAI,SAAS,QAAQ;AAAA,QAAgB,SAAS,QAAQ;AAAA,MAC/D;AACA,6BAAuB,KAAK,QAAQ,cAAc;AAAA,IACpD;AAAA,EACF;AACA,SAAO;AACT;;;ACrFA,IAAqB,cAArB,MAAiC;AAAA,EAC/B,MAAM,OAAO,eAAe,QAAQ;AAClC,UAAM,UAAU,MAAM,KAAK,YAAY,MAAM;AAC7C,UAAM,YAAY,cAAc,aAAa;AAC7C,QAAI,cAAc,GAAG;AACnB,YAAM,UAAU,CAAC,cAAc;AAC/B,YAAM,YAAY,UAAU,cAAc,YAAY,cAAc;AACpE,YAAM,aAAa,UAAU,cAAc,aACzC,cAAc,gBAAgB,cAAc;AAE9C,aAAO;AAAA,QACL;AAAA,QAAS;AAAA,QAAW;AAAA,QAAW;AAAA,QAAY,cAAc;AAAA,QACzD,cAAc;AAAA,MAChB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;", "names": []}