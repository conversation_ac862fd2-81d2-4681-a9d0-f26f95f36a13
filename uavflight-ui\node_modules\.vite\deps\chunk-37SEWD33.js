import {
  assert
} from "./chunk-IGSFLGV4.js";

// node_modules/ol/format/IIIFInfo.js
var Versions = {
  VERSION1: "version1",
  VERSION2: "version2",
  VERSION3: "version3"
};
var IIIF_PROFILE_VALUES = {};
IIIF_PROFILE_VALUES[Versions.VERSION1] = {
  "level0": {
    supports: [],
    formats: [],
    qualities: ["native"]
  },
  "level1": {
    supports: ["regionByPx", "sizeByW", "sizeByH", "sizeByPct"],
    formats: ["jpg"],
    qualities: ["native"]
  },
  "level2": {
    supports: [
      "regionByPx",
      "regionByPct",
      "sizeByW",
      "sizeByH",
      "sizeByPct",
      "sizeByConfinedWh",
      "sizeByWh"
    ],
    formats: ["jpg", "png"],
    qualities: ["native", "color", "grey", "bitonal"]
  }
};
IIIF_PROFILE_VALUES[Versions.VERSION2] = {
  "level0": {
    supports: [],
    formats: ["jpg"],
    qualities: ["default"]
  },
  "level1": {
    supports: ["regionByPx", "sizeByW", "sizeByH", "sizeByPct"],
    formats: ["jpg"],
    qualities: ["default"]
  },
  "level2": {
    supports: [
      "regionByPx",
      "regionByPct",
      "sizeByW",
      "sizeByH",
      "sizeByPct",
      "sizeByConfinedWh",
      "sizeByDistortedWh",
      "sizeByWh"
    ],
    formats: ["jpg", "png"],
    qualities: ["default", "bitonal"]
  }
};
IIIF_PROFILE_VALUES[Versions.VERSION3] = {
  "level0": {
    supports: [],
    formats: ["jpg"],
    qualities: ["default"]
  },
  "level1": {
    supports: ["regionByPx", "regionSquare", "sizeByW", "sizeByH", "sizeByWh"],
    formats: ["jpg"],
    qualities: ["default"]
  },
  "level2": {
    supports: [
      "regionByPx",
      "regionSquare",
      "regionByPct",
      "sizeByW",
      "sizeByH",
      "sizeByPct",
      "sizeByConfinedWh",
      "sizeByWh"
    ],
    formats: ["jpg", "png"],
    qualities: ["default"]
  }
};
IIIF_PROFILE_VALUES["none"] = {
  "none": {
    supports: [],
    formats: [],
    qualities: []
  }
};
var COMPLIANCE_VERSION1 = /^https?:\/\/library\.stanford\.edu\/iiif\/image-api\/(?:1\.1\/)?compliance\.html#level[0-2]$/;
var COMPLIANCE_VERSION2 = /^https?:\/\/iiif\.io\/api\/image\/2\/level[0-2](?:\.json)?$/;
var COMPLIANCE_VERSION3 = /(^https?:\/\/iiif\.io\/api\/image\/3\/level[0-2](?:\.json)?$)|(^level[0-2]$)/;
function generateVersion1Options(iiifInfo) {
  let levelProfile = iiifInfo.getComplianceLevelSupportedFeatures();
  if (levelProfile === void 0) {
    levelProfile = IIIF_PROFILE_VALUES[Versions.VERSION1]["level0"];
  }
  return {
    url: iiifInfo.imageInfo["@id"] === void 0 ? void 0 : iiifInfo.imageInfo["@id"].replace(/\/?(?:info\.json)?$/g, ""),
    supports: levelProfile.supports,
    formats: [
      ...levelProfile.formats,
      iiifInfo.imageInfo.formats === void 0 ? [] : iiifInfo.imageInfo.formats
    ],
    qualities: [
      ...levelProfile.qualities,
      iiifInfo.imageInfo.qualities === void 0 ? [] : iiifInfo.imageInfo.qualities
    ],
    resolutions: iiifInfo.imageInfo.scale_factors,
    tileSize: iiifInfo.imageInfo.tile_width !== void 0 ? iiifInfo.imageInfo.tile_height !== void 0 ? [iiifInfo.imageInfo.tile_width, iiifInfo.imageInfo.tile_height] : [iiifInfo.imageInfo.tile_width, iiifInfo.imageInfo.tile_width] : iiifInfo.imageInfo.tile_height != void 0 ? [iiifInfo.imageInfo.tile_height, iiifInfo.imageInfo.tile_height] : void 0
  };
}
function generateVersion2Options(iiifInfo) {
  const levelProfile = iiifInfo.getComplianceLevelSupportedFeatures(), additionalProfile = Array.isArray(iiifInfo.imageInfo.profile) && iiifInfo.imageInfo.profile.length > 1, profileSupports = additionalProfile && iiifInfo.imageInfo.profile[1].supports ? iiifInfo.imageInfo.profile[1].supports : [], profileFormats = additionalProfile && iiifInfo.imageInfo.profile[1].formats ? iiifInfo.imageInfo.profile[1].formats : [], profileQualities = additionalProfile && iiifInfo.imageInfo.profile[1].qualities ? iiifInfo.imageInfo.profile[1].qualities : [];
  return {
    url: iiifInfo.imageInfo["@id"].replace(/\/?(?:info\.json)?$/g, ""),
    sizes: iiifInfo.imageInfo.sizes === void 0 ? void 0 : iiifInfo.imageInfo.sizes.map(function(size) {
      return [size.width, size.height];
    }),
    tileSize: iiifInfo.imageInfo.tiles === void 0 ? void 0 : [
      iiifInfo.imageInfo.tiles.map(function(tile) {
        return tile.width;
      })[0],
      iiifInfo.imageInfo.tiles.map(function(tile) {
        return tile.height === void 0 ? tile.width : tile.height;
      })[0]
    ],
    resolutions: iiifInfo.imageInfo.tiles === void 0 ? void 0 : iiifInfo.imageInfo.tiles.map(function(tile) {
      return tile.scaleFactors;
    })[0],
    supports: [...levelProfile.supports, ...profileSupports],
    formats: [...levelProfile.formats, ...profileFormats],
    qualities: [...levelProfile.qualities, ...profileQualities]
  };
}
function generateVersion3Options(iiifInfo) {
  const levelProfile = iiifInfo.getComplianceLevelSupportedFeatures(), formats = iiifInfo.imageInfo.extraFormats === void 0 ? levelProfile.formats : [...levelProfile.formats, ...iiifInfo.imageInfo.extraFormats], preferredFormat = iiifInfo.imageInfo.preferredFormats !== void 0 && Array.isArray(iiifInfo.imageInfo.preferredFormats) && iiifInfo.imageInfo.preferredFormats.length > 0 ? iiifInfo.imageInfo.preferredFormats.filter(function(format) {
    return ["jpg", "png", "gif"].includes(format);
  }).reduce(function(acc, format) {
    return acc === void 0 && formats.includes(format) ? format : acc;
  }, void 0) : void 0;
  return {
    url: iiifInfo.imageInfo["id"],
    sizes: iiifInfo.imageInfo.sizes === void 0 ? void 0 : iiifInfo.imageInfo.sizes.map(function(size) {
      return [size.width, size.height];
    }),
    tileSize: iiifInfo.imageInfo.tiles === void 0 ? void 0 : [
      iiifInfo.imageInfo.tiles.map(function(tile) {
        return tile.width;
      })[0],
      iiifInfo.imageInfo.tiles.map(function(tile) {
        return tile.height;
      })[0]
    ],
    resolutions: iiifInfo.imageInfo.tiles === void 0 ? void 0 : iiifInfo.imageInfo.tiles.map(function(tile) {
      return tile.scaleFactors;
    })[0],
    supports: iiifInfo.imageInfo.extraFeatures === void 0 ? levelProfile.supports : [...levelProfile.supports, ...iiifInfo.imageInfo.extraFeatures],
    formats,
    qualities: iiifInfo.imageInfo.extraQualities === void 0 ? levelProfile.qualities : [...levelProfile.qualities, ...iiifInfo.imageInfo.extraQualities],
    preferredFormat
  };
}
var versionFunctions = {};
versionFunctions[Versions.VERSION1] = generateVersion1Options;
versionFunctions[Versions.VERSION2] = generateVersion2Options;
versionFunctions[Versions.VERSION3] = generateVersion3Options;
var IIIFInfo = class {
  /**
   * @param {string|ImageInformationResponse} imageInfo
   * Deserialized image information JSON response object or JSON response as string
   */
  constructor(imageInfo) {
    this.setImageInfo(imageInfo);
  }
  /**
   * @param {string|ImageInformationResponse} imageInfo
   * Deserialized image information JSON response object or JSON response as string
   * @api
   */
  setImageInfo(imageInfo) {
    if (typeof imageInfo == "string") {
      this.imageInfo = JSON.parse(imageInfo);
    } else {
      this.imageInfo = imageInfo;
    }
  }
  /**
   * @return {Versions|undefined} Major IIIF version.
   * @api
   */
  getImageApiVersion() {
    if (this.imageInfo === void 0) {
      return void 0;
    }
    let context = this.imageInfo["@context"] || "ol-no-context";
    if (typeof context == "string") {
      context = [context];
    }
    for (let i = 0; i < context.length; i++) {
      switch (context[i]) {
        case "http://library.stanford.edu/iiif/image-api/1.1/context.json":
        case "http://iiif.io/api/image/1/context.json":
          return Versions.VERSION1;
        case "http://iiif.io/api/image/2/context.json":
          return Versions.VERSION2;
        case "http://iiif.io/api/image/3/context.json":
          return Versions.VERSION3;
        case "ol-no-context":
          if (this.getComplianceLevelEntryFromProfile(Versions.VERSION1) && this.imageInfo.identifier) {
            return Versions.VERSION1;
          }
          break;
        default:
      }
    }
    assert(false, 61);
  }
  /**
   * @param {Versions} version Optional IIIF image API version
   * @return {string|undefined} Compliance level as it appears in the IIIF image information
   * response.
   */
  getComplianceLevelEntryFromProfile(version) {
    if (this.imageInfo === void 0 || this.imageInfo.profile === void 0) {
      return void 0;
    }
    if (version === void 0) {
      version = this.getImageApiVersion();
    }
    switch (version) {
      case Versions.VERSION1:
        if (COMPLIANCE_VERSION1.test(this.imageInfo.profile)) {
          return this.imageInfo.profile;
        }
        break;
      case Versions.VERSION3:
        if (COMPLIANCE_VERSION3.test(this.imageInfo.profile)) {
          return this.imageInfo.profile;
        }
        break;
      case Versions.VERSION2:
        if (typeof this.imageInfo.profile === "string" && COMPLIANCE_VERSION2.test(this.imageInfo.profile)) {
          return this.imageInfo.profile;
        }
        if (Array.isArray(this.imageInfo.profile) && this.imageInfo.profile.length > 0 && typeof this.imageInfo.profile[0] === "string" && COMPLIANCE_VERSION2.test(this.imageInfo.profile[0])) {
          return this.imageInfo.profile[0];
        }
        break;
      default:
    }
    return void 0;
  }
  /**
   * @param {Versions} version Optional IIIF image API version
   * @return {string} Compliance level, on of 'level0', 'level1' or 'level2' or undefined
   */
  getComplianceLevelFromProfile(version) {
    const complianceLevel = this.getComplianceLevelEntryFromProfile(version);
    if (complianceLevel === void 0) {
      return void 0;
    }
    const level = complianceLevel.match(/level[0-2](?:\.json)?$/g);
    return Array.isArray(level) ? level[0].replace(".json", "") : void 0;
  }
  /**
   * @return {SupportedFeatures|undefined} Image formats, qualities and region / size calculation
   * methods that are supported by the IIIF service.
   */
  getComplianceLevelSupportedFeatures() {
    if (this.imageInfo === void 0) {
      return void 0;
    }
    const version = this.getImageApiVersion();
    const level = this.getComplianceLevelFromProfile(version);
    if (level === void 0) {
      return IIIF_PROFILE_VALUES["none"]["none"];
    }
    return IIIF_PROFILE_VALUES[version][level];
  }
  /**
   * @param {PreferredOptions} [preferredOptions] Optional options for preferred format and quality.
   * @return {import("../source/IIIF.js").Options|undefined} IIIF tile source ready constructor options.
   * @api
   */
  getTileSourceOptions(preferredOptions) {
    const options = preferredOptions || {}, version = this.getImageApiVersion();
    if (version === void 0) {
      return void 0;
    }
    const imageOptions = version === void 0 ? void 0 : versionFunctions[version](this);
    if (imageOptions === void 0) {
      return void 0;
    }
    return {
      url: imageOptions.url,
      version,
      size: [this.imageInfo.width, this.imageInfo.height],
      sizes: imageOptions.sizes,
      format: options.format !== void 0 && imageOptions.formats.includes(options.format) ? options.format : imageOptions.preferredFormat !== void 0 ? imageOptions.preferredFormat : "jpg",
      supports: imageOptions.supports,
      quality: options.quality && imageOptions.qualities.includes(options.quality) ? options.quality : imageOptions.qualities.includes("native") ? "native" : "default",
      resolutions: Array.isArray(imageOptions.resolutions) ? imageOptions.resolutions.sort(function(a, b) {
        return b - a;
      }) : void 0,
      tileSize: imageOptions.tileSize
    };
  }
};
var IIIFInfo_default = IIIFInfo;

export {
  Versions,
  IIIFInfo_default
};
//# sourceMappingURL=chunk-37SEWD33.js.map
