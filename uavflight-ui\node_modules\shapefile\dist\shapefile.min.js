// https://github.com/mbostock/shapefile Version 0.6.6. Copyright 2017 <PERSON>.
!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n(e.shapefile={})}(this,function(e){"use strict";function n(e){return new t(e instanceof Uint8Array?e:new Uint8Array(e))}function t(e){this._array=e}function r(e){return("function"==typeof fetch?v:_)(e)}function o(e){return"function"==typeof e.read?e:e.getReader()}function i(e,n){if(!e.length)return n;if(!n.length)return e;var t=new Uint8Array(e.length+n.length);return t.set(e),t.set(n,e.length),t}function u(e){return"function"==typeof e.slice?e:new a("function"==typeof e.read?e:e.getReader())}function a(e){this._source=e,this._array=b,this._index=0}function f(e,n,t,r){this._source=e,this._decode=n.decode.bind(n),this._recordLength=t.getUint16(10,!0),this._fields=[];for(var o=0;13!==r.getUint8(o);o+=32){for(var i=0;i<11&&0!==r.getUint8(o+i);++i);this._fields.push({name:this._decode(new Uint8Array(r.buffer,r.byteOffset+o,i)),type:String.fromCharCode(r.getUint8(o+11)),length:r.getUint8(o+16)})}}function s(e){if((n=e.length)<4)return!1;for(var n,t=0,r=e[n-1][1]*e[0][0]-e[n-1][0]*e[0][1];++t<n;)r+=e[t-1][1]*e[t][0]-e[t-1][0]*e[t][1];return r>=0}function c(e,n){for(var t,r=-1,o=n.length;++r<o;)if(t=l(e,n[r]))return t>0;return!1}function l(e,n){for(var t=n[0],r=n[1],o=-1,i=0,u=e.length,a=u-1;i<u;a=i++){var f=e[i],s=f[0],c=f[1],l=e[a],d=l[0],y=l[1];if(h(f,l,n))return 0;c>r!=y>r&&t<(d-s)*(r-c)/(y-c)+s&&(o=-o)}return o}function h(e,n,t){var r=t[0]-e[0],o=t[1]-e[1];if(0===r&&0===o)return!0;var i=n[0]-e[0],u=n[1]-e[1];if(0===i&&0===u)return!1;var a=(r*i+o*u)/(i*i+u*u);return!(a<0||a>1)&&(0===a||1===a||a*i===r&&a*u===o)}function d(e,n){var t=n.getInt32(32,!0);if(!(t in D))throw new Error("unsupported shape type: "+t);this._source=e,this._type=t,this._index=0,this._parse=D[t],this.bbox=[n.getFloat64(36,!0),n.getFloat64(44,!0),n.getFloat64(52,!0),n.getFloat64(60,!0)]}function y(){}function g(e,n){this._shp=e,this._dbf=n,this.bbox=e.bbox}function p(e,t,i){return"string"==typeof t?(/\.dbf$/.test(t)||(t+=".dbf"),t=r(t,i)):t instanceof ArrayBuffer||t instanceof Uint8Array?t=n(t):null!=t&&(t=o(t)),"string"==typeof e?(/\.shp$/.test(e)||(e+=".shp"),void 0===t&&(t=r(e.substring(0,e.length-4)+".dbf",i).catch(function(){})),e=r(e,i)):e=e instanceof ArrayBuffer||e instanceof Uint8Array?n(e):o(e),Promise.all([e,t]).then(function(e){var n=e[0],t=e[1],r="windows-1252";return i&&null!=i.encoding&&(r=i.encoding),E(n,t,t&&new TextDecoder(r))})}t.prototype.read=function(){var e=this._array;return this._array=null,Promise.resolve(e?{done:!1,value:e}:{done:!0,value:void 0})},t.prototype.cancel=function(){return this._array=null,Promise.resolve()};var v=function(e){return fetch(e).then(function(e){return e.body&&e.body.getReader?e.body.getReader():e.arrayBuffer().then(n)})},_=function(e){return new Promise(function(t,r){var o=new XMLHttpRequest;o.responseType="arraybuffer",o.onload=function(){t(n(o.response))},o.onerror=r,o.ontimeout=r,o.open("GET",e,!0),o.send()})},b=new Uint8Array(0);a.prototype.read=function(){var e=this,n=e._array.subarray(e._index);return e._source.read().then(function(t){return e._array=b,e._index=0,t.done?n.length>0?{done:!1,value:n}:{done:!0,value:void 0}:{done:!1,value:i(n,t.value)}})},a.prototype.slice=function(e){if((e|=0)<0)throw new Error("invalid length");var n=this,t=this._array.length-this._index;if(this._index+e<=this._array.length)return Promise.resolve(this._array.subarray(this._index,this._index+=e));var r=new Uint8Array(e);return r.set(this._array.subarray(this._index)),function o(){return n._source.read().then(function(i){return i.done?(n._array=b,n._index=0,t>0?r.subarray(0,t):null):t+i.value.length>=e?(n._array=i.value,n._index=e-t,r.set(i.value.subarray(0,e-t),t),r):(r.set(i.value,t),t+=i.value.length,o())})}()},a.prototype.cancel=function(){return this._source.cancel()};var w=function(e){return!(e=e.trim())||isNaN(e=+e)?null:e},x={B:w,C:function(e){return e.trim()||null},D:function(e){return new Date(+e.substring(0,4),e.substring(4,6)-1,+e.substring(6,8))},F:w,L:function(e){return!/^[nf]$/i.test(e)&&(!!/^[yt]$/i.test(e)||null)},M:w,N:w},m=function(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)},A=function(e,n){return(e=u(e)).slice(32).then(function(t){var r=m(t);return e.slice(r.getUint16(8,!0)-32).then(function(t){return new f(e,n,r,m(t))})})},U=f.prototype;U.read=function(){var e=this,n=1;return e._source.slice(e._recordLength).then(function(t){return t&&26!==t[0]?{done:!1,value:e._fields.reduce(function(r,o){return r[o.name]=x[o.type](e._decode(t.subarray(n,n+=o.length))),r},{})}:{done:!0,value:void 0}})},U.cancel=function(){return this._source.cancel()};var F=function(e){var n,t=40,r=e.getInt32(36,!0),o=new Array(r);for(n=0;n<r;++n,t+=16)o[n]=[e.getFloat64(t,!0),e.getFloat64(t+8,!0)];return{type:"MultiPoint",coordinates:o}},P=function(e){return{type:"Point",coordinates:[e.getFloat64(4,!0),e.getFloat64(12,!0)]}},I=function(e){var n,t=44,r=e.getInt32(36,!0),o=e.getInt32(40,!0),i=new Array(r),u=new Array(o),a=[],f=[];for(n=0;n<r;++n,t+=4)i[n]=e.getInt32(t,!0);for(n=0;n<o;++n,t+=16)u[n]=[e.getFloat64(t,!0),e.getFloat64(t+8,!0)];return i.forEach(function(e,n){var t=u.slice(e,i[n+1]);s(t)?a.push([t]):f.push(t)}),f.forEach(function(e){a.some(function(n){if(c(n[0],e))return n.push(e),!0})||a.push([e])}),1===a.length?{type:"Polygon",coordinates:a[0]}:{type:"MultiPolygon",coordinates:a}},L=function(e){var n,t=44,r=e.getInt32(36,!0),o=e.getInt32(40,!0),i=new Array(r),u=new Array(o);for(n=0;n<r;++n,t+=4)i[n]=e.getInt32(t,!0);for(n=0;n<o;++n,t+=16)u[n]=[e.getFloat64(t,!0),e.getFloat64(t+8,!0)];return 1===r?{type:"LineString",coordinates:u}:{type:"MultiLineString",coordinates:i.map(function(e,n){return u.slice(e,i[n+1])})}},B=function(e,n){var t=new Uint8Array(e.length+n.length);return t.set(e,0),t.set(n,e.length),t},D={0:function(){return null},1:P,3:L,5:I,8:F,11:P,13:L,15:I,18:F,21:P,23:L,25:I,28:F},M=function(e){return(e=u(e)).slice(100).then(function(n){return new d(e,m(n))})},$=d.prototype;$.read=function(){var e=this;return++e._index,e._source.slice(12).then(function(n){function t(){return e._source.slice(4).then(function(i){return null==i?{done:!0,value:void 0}:(o=m(n=B(n.slice(4),i))).getInt32(0,!1)!==e._index?t():r()})}function r(){var r=2*o.getInt32(4,!1)-4,i=o.getInt32(8,!0);return r<0||i&&i!==e._type?t():e._source.slice(r).then(function(t){return{done:!1,value:i?e._parse(m(B(n.slice(8),t))):null}})}if(null==n)return{done:!0,value:void 0};var o=m(n);return r()})},$.cancel=function(){return this._source.cancel()};var E=function(e,n,t){return Promise.all([M(e),n&&A(n,t)]).then(function(e){return new g(e[0],e[1])})},R=g.prototype;R.read=function(){var e=this;return Promise.all([e._dbf?e._dbf.read():{value:{}},e._shp.read()]).then(function(e){var n=e[0],t=e[1];return t.done?t:{done:!1,value:{type:"Feature",properties:n.value,geometry:t.value}}})},R.cancel=function(){return Promise.all([this._dbf&&this._dbf.cancel(),this._shp.cancel()]).then(y)},e.open=p,e.openShp=function(e,t){return"string"==typeof e?(/\.shp$/.test(e)||(e+=".shp"),e=r(e,t)):e=e instanceof ArrayBuffer||e instanceof Uint8Array?n(e):o(e),Promise.resolve(e).then(M)},e.openDbf=function(e,t){var i="windows-1252";return t&&null!=t.encoding&&(i=t.encoding),i=new TextDecoder(i),"string"==typeof e?(/\.dbf$/.test(e)||(e+=".dbf"),e=r(e,t)):e=e instanceof ArrayBuffer||e instanceof Uint8Array?n(e):o(e),Promise.resolve(e).then(function(e){return A(e,i)})},e.read=function(e,n,t){return p(e,n,t).then(function(e){var n=[],t={type:"FeatureCollection",features:n,bbox:e.bbox};return e.read().then(function r(o){return o.done?t:(n.push(o.value),e.read().then(r))})})},Object.defineProperty(e,"__esModule",{value:!0})});