define(["./defaultValue-0a909f67","./PrimitivePipeline-75e51f37","./createTaskProcessorWorker","./Transforms-01e95659","./Matrix3-a348023f","./Math-e97915da","./Matrix2-7146c9ca","./RuntimeError-06c93819","./combine-ca22a614","./ComponentDatatype-77274976","./WebGLConstants-a8cc3e8c","./GeometryAttribute-f5d71750","./GeometryAttributes-f06a2792","./GeometryPipeline-049a5b67","./AttributeCompression-50c9aeba","./EncodedCartesian3-0fb84db0","./IndexDatatype-2149f06c","./IntersectionTests-0bb04fde","./Plane-8575e17c","./WebMercatorProjection-f4deae14"],(function(e,t,r,n,o,i,a,s,c,f,u,d,m,b,l,p,y,P,k,C){"use strict";const G={};function W(t){let r=G[t];return e.defined(r)||("object"==typeof exports?G[r]=r=require(`Workers/${t}`):require([`Workers/${t}`],(function(e){r=e,G[r]=e}))),r}return r((function(r,n){const o=r.subTasks,i=o.length,a=new Array(i);for(let t=0;t<i;t++){const r=o[t],n=r.geometry,i=r.moduleName;if(e.defined(i)){const e=W(i);a[t]=e(n,r.offset)}else a[t]=n}return Promise.all(a).then((function(e){return t.PrimitivePipeline.packCreateGeometryResults(e,n)}))}))}));
