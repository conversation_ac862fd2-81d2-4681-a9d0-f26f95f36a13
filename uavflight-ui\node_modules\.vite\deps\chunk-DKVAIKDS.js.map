{"version": 3, "sources": ["../../ol/renderer/Map.js", "../../ol/renderer/Composite.js", "../../ol/layer/Group.js", "../../ol/pointer/EventType.js", "../../ol/MapBrowserEventHandler.js", "../../ol/MapProperty.js", "../../ol/control/Control.js", "../../ol/control/Attribution.js", "../../ol/control/Rotate.js", "../../ol/control/Zoom.js", "../../ol/control/defaults.js", "../../ol/Map.js"], "sourcesContent": ["/**\n * @module ol/renderer/Map\n */\nimport Disposable from '../Disposable.js';\nimport {TRUE} from '../functions.js';\nimport {abstract} from '../util.js';\nimport {compose as composeTransform, makeInverse} from '../transform.js';\nimport {getWidth} from '../extent.js';\nimport {shared as iconImageCache} from '../style/IconImageCache.js';\nimport {inView} from '../layer/Layer.js';\nimport {wrapX} from '../coordinate.js';\n\n/**\n * @template T\n * @typedef HitMatch\n * @property {import(\"../Feature.js\").FeatureLike} feature Feature.\n * @property {import(\"../layer/Layer.js\").default} layer Layer.\n * @property {import(\"../geom/SimpleGeometry.js\").default} geometry Geometry.\n * @property {number} distanceSq Squared distance.\n * @property {import(\"./vector.js\").FeatureCallback<T>} callback Callback.\n */\n\n/**\n * @abstract\n */\nclass MapRenderer extends Disposable {\n  /**\n   * @param {import(\"../Map.js\").default} map Map.\n   */\n  constructor(map) {\n    super();\n\n    /**\n     * @private\n     * @type {import(\"../Map.js\").default}\n     */\n    this.map_ = map;\n  }\n\n  /**\n   * @abstract\n   * @param {import(\"../render/EventType.js\").default} type Event type.\n   * @param {import(\"../Map.js\").FrameState} frameState Frame state.\n   */\n  dispatchRenderEvent(type, frameState) {\n    abstract();\n  }\n\n  /**\n   * @param {import(\"../Map.js\").FrameState} frameState FrameState.\n   * @protected\n   */\n  calculateMatrices2D(frameState) {\n    const viewState = frameState.viewState;\n    const coordinateToPixelTransform = frameState.coordinateToPixelTransform;\n    const pixelToCoordinateTransform = frameState.pixelToCoordinateTransform;\n\n    composeTransform(\n      coordinateToPixelTransform,\n      frameState.size[0] / 2,\n      frameState.size[1] / 2,\n      1 / viewState.resolution,\n      -1 / viewState.resolution,\n      -viewState.rotation,\n      -viewState.center[0],\n      -viewState.center[1]\n    );\n\n    makeInverse(pixelToCoordinateTransform, coordinateToPixelTransform);\n  }\n\n  /**\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @param {import(\"../Map.js\").FrameState} frameState FrameState.\n   * @param {number} hitTolerance Hit tolerance in pixels.\n   * @param {boolean} checkWrapped Check for wrapped geometries.\n   * @param {import(\"./vector.js\").FeatureCallback<T>} callback Feature callback.\n   * @param {S} thisArg Value to use as `this` when executing `callback`.\n   * @param {function(this: U, import(\"../layer/Layer.js\").default): boolean} layerFilter Layer filter\n   *     function, only layers which are visible and for which this function\n   *     returns `true` will be tested for features.  By default, all visible\n   *     layers will be tested.\n   * @param {U} thisArg2 Value to use as `this` when executing `layerFilter`.\n   * @return {T|undefined} Callback result.\n   * @template S,T,U\n   */\n  forEachFeatureAtCoordinate(\n    coordinate,\n    frameState,\n    hitTolerance,\n    checkWrapped,\n    callback,\n    thisArg,\n    layerFilter,\n    thisArg2\n  ) {\n    let result;\n    const viewState = frameState.viewState;\n\n    /**\n     * @param {boolean} managed Managed layer.\n     * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n     * @param {import(\"../layer/Layer.js\").default} layer Layer.\n     * @param {import(\"../geom/Geometry.js\").default} geometry Geometry.\n     * @return {T|undefined} Callback result.\n     */\n    function forEachFeatureAtCoordinate(managed, feature, layer, geometry) {\n      return callback.call(thisArg, feature, managed ? layer : null, geometry);\n    }\n\n    const projection = viewState.projection;\n\n    const translatedCoordinate = wrapX(coordinate.slice(), projection);\n    const offsets = [[0, 0]];\n    if (projection.canWrapX() && checkWrapped) {\n      const projectionExtent = projection.getExtent();\n      const worldWidth = getWidth(projectionExtent);\n      offsets.push([-worldWidth, 0], [worldWidth, 0]);\n    }\n\n    const layerStates = frameState.layerStatesArray;\n    const numLayers = layerStates.length;\n\n    const matches = /** @type {Array<HitMatch<T>>} */ ([]);\n    const tmpCoord = [];\n    for (let i = 0; i < offsets.length; i++) {\n      for (let j = numLayers - 1; j >= 0; --j) {\n        const layerState = layerStates[j];\n        const layer = layerState.layer;\n        if (\n          layer.hasRenderer() &&\n          inView(layerState, viewState) &&\n          layerFilter.call(thisArg2, layer)\n        ) {\n          const layerRenderer = layer.getRenderer();\n          const source = layer.getSource();\n          if (layerRenderer && source) {\n            const coordinates = source.getWrapX()\n              ? translatedCoordinate\n              : coordinate;\n            const callback = forEachFeatureAtCoordinate.bind(\n              null,\n              layerState.managed\n            );\n            tmpCoord[0] = coordinates[0] + offsets[i][0];\n            tmpCoord[1] = coordinates[1] + offsets[i][1];\n            result = layerRenderer.forEachFeatureAtCoordinate(\n              tmpCoord,\n              frameState,\n              hitTolerance,\n              callback,\n              matches\n            );\n          }\n          if (result) {\n            return result;\n          }\n        }\n      }\n    }\n    if (matches.length === 0) {\n      return undefined;\n    }\n    const order = 1 / matches.length;\n    matches.forEach((m, i) => (m.distanceSq += i * order));\n    matches.sort((a, b) => a.distanceSq - b.distanceSq);\n    matches.some((m) => {\n      return (result = m.callback(m.feature, m.layer, m.geometry));\n    });\n    return result;\n  }\n\n  /**\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @param {import(\"../Map.js\").FrameState} frameState FrameState.\n   * @param {number} hitTolerance Hit tolerance in pixels.\n   * @param {boolean} checkWrapped Check for wrapped geometries.\n   * @param {function(this: U, import(\"../layer/Layer.js\").default): boolean} layerFilter Layer filter\n   *     function, only layers which are visible and for which this function\n   *     returns `true` will be tested for features.  By default, all visible\n   *     layers will be tested.\n   * @param {U} thisArg Value to use as `this` when executing `layerFilter`.\n   * @return {boolean} Is there a feature at the given coordinate?\n   * @template U\n   */\n  hasFeatureAtCoordinate(\n    coordinate,\n    frameState,\n    hitTolerance,\n    checkWrapped,\n    layerFilter,\n    thisArg\n  ) {\n    const hasFeature = this.forEachFeatureAtCoordinate(\n      coordinate,\n      frameState,\n      hitTolerance,\n      checkWrapped,\n      TRUE,\n      this,\n      layerFilter,\n      thisArg\n    );\n\n    return hasFeature !== undefined;\n  }\n\n  /**\n   * @return {import(\"../Map.js\").default} Map.\n   */\n  getMap() {\n    return this.map_;\n  }\n\n  /**\n   * Render.\n   * @abstract\n   * @param {?import(\"../Map.js\").FrameState} frameState Frame state.\n   */\n  renderFrame(frameState) {\n    abstract();\n  }\n\n  /**\n   * @param {import(\"../Map.js\").FrameState} frameState Frame state.\n   */\n  flushDeclutterItems(frameState) {}\n\n  /**\n   * @param {import(\"../Map.js\").FrameState} frameState Frame state.\n   * @protected\n   */\n  scheduleExpireIconCache(frameState) {\n    if (iconImageCache.canExpireCache()) {\n      frameState.postRenderFunctions.push(expireIconCache);\n    }\n  }\n}\n\n/**\n * @param {import(\"../Map.js\").default} map Map.\n * @param {import(\"../Map.js\").FrameState} frameState Frame state.\n */\nfunction expireIconCache(map, frameState) {\n  iconImageCache.expire();\n}\n\nexport default MapRenderer;\n", "/**\n * @module ol/renderer/Composite\n */\nimport MapRenderer from './Map.js';\nimport ObjectEventType from '../ObjectEventType.js';\nimport RenderEvent from '../render/Event.js';\nimport RenderEventType from '../render/EventType.js';\nimport {CLASS_UNSELECTABLE} from '../css.js';\nimport {checkedFonts} from '../render/canvas.js';\nimport {inView} from '../layer/Layer.js';\nimport {listen, unlistenByKey} from '../events.js';\nimport {replaceChildren} from '../dom.js';\n\n/**\n * @classdesc\n * Canvas map renderer.\n * @api\n */\nclass CompositeMapRenderer extends MapRenderer {\n  /**\n   * @param {import(\"../Map.js\").default} map Map.\n   */\n  constructor(map) {\n    super(map);\n\n    /**\n     * @type {import(\"../events.js\").EventsKey}\n     */\n    this.fontChangeListenerKey_ = listen(\n      checkedFonts,\n      ObjectEventType.PROPERTYCHANGE,\n      map.redrawText.bind(map)\n    );\n\n    /**\n     * @private\n     * @type {HTMLDivElement}\n     */\n    this.element_ = document.createElement('div');\n    const style = this.element_.style;\n    style.position = 'absolute';\n    style.width = '100%';\n    style.height = '100%';\n    style.zIndex = '0';\n\n    this.element_.className = CLASS_UNSELECTABLE + ' ol-layers';\n\n    const container = map.getViewport();\n    container.insertBefore(this.element_, container.firstChild || null);\n\n    /**\n     * @private\n     * @type {Array<HTMLElement>}\n     */\n    this.children_ = [];\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.renderedVisible_ = true;\n\n    /**\n     * @type {Array<import(\"../layer/BaseVector.js\").default>}\n     */\n    this.declutterLayers_ = [];\n  }\n\n  /**\n   * @param {import(\"../render/EventType.js\").default} type Event type.\n   * @param {import(\"../Map.js\").FrameState} frameState Frame state.\n   */\n  dispatchRenderEvent(type, frameState) {\n    const map = this.getMap();\n    if (map.hasListener(type)) {\n      const event = new RenderEvent(type, undefined, frameState);\n      map.dispatchEvent(event);\n    }\n  }\n\n  disposeInternal() {\n    unlistenByKey(this.fontChangeListenerKey_);\n    this.element_.parentNode.removeChild(this.element_);\n    super.disposeInternal();\n  }\n\n  /**\n   * Render.\n   * @param {?import(\"../Map.js\").FrameState} frameState Frame state.\n   */\n  renderFrame(frameState) {\n    if (!frameState) {\n      if (this.renderedVisible_) {\n        this.element_.style.display = 'none';\n        this.renderedVisible_ = false;\n      }\n      return;\n    }\n\n    this.calculateMatrices2D(frameState);\n    this.dispatchRenderEvent(RenderEventType.PRECOMPOSE, frameState);\n\n    const layerStatesArray = frameState.layerStatesArray.sort(function (a, b) {\n      return a.zIndex - b.zIndex;\n    });\n    const viewState = frameState.viewState;\n\n    this.children_.length = 0;\n\n    const declutterLayers = this.declutterLayers_;\n    declutterLayers.length = 0;\n\n    let previousElement = null;\n    for (let i = 0, ii = layerStatesArray.length; i < ii; ++i) {\n      const layerState = layerStatesArray[i];\n      frameState.layerIndex = i;\n\n      const layer = layerState.layer;\n      const sourceState = layer.getSourceState();\n      if (\n        !inView(layerState, viewState) ||\n        (sourceState != 'ready' && sourceState != 'undefined')\n      ) {\n        layer.unrender();\n        continue;\n      }\n\n      const element = layer.render(frameState, previousElement);\n      if (!element) {\n        continue;\n      }\n      if (element !== previousElement) {\n        this.children_.push(element);\n        previousElement = element;\n      }\n      if ('getDeclutter' in layer) {\n        declutterLayers.push(\n          /** @type {import(\"../layer/BaseVector.js\").default} */ (layer)\n        );\n      }\n    }\n    this.flushDeclutterItems(frameState);\n\n    replaceChildren(this.element_, this.children_);\n\n    this.dispatchRenderEvent(RenderEventType.POSTCOMPOSE, frameState);\n\n    if (!this.renderedVisible_) {\n      this.element_.style.display = '';\n      this.renderedVisible_ = true;\n    }\n\n    this.scheduleExpireIconCache(frameState);\n  }\n\n  /**\n   * @param {import(\"../Map.js\").FrameState} frameState Frame state.\n   */\n  flushDeclutterItems(frameState) {\n    const layers = this.declutterLayers_;\n    for (let i = layers.length - 1; i >= 0; --i) {\n      layers[i].renderDeclutter(frameState);\n    }\n    frameState.declutterTree = null;\n    layers.length = 0;\n  }\n}\n\nexport default CompositeMapRenderer;\n", "/**\n * @module ol/layer/Group\n */\nimport BaseLayer from './Base.js';\nimport Collection from '../Collection.js';\nimport CollectionEventType from '../CollectionEventType.js';\nimport Event from '../events/Event.js';\nimport EventType from '../events/EventType.js';\nimport ObjectEventType from '../ObjectEventType.js';\nimport {assert} from '../asserts.js';\nimport {clear} from '../obj.js';\nimport {getIntersection} from '../extent.js';\nimport {getUid} from '../util.js';\nimport {listen, unlistenByKey} from '../events.js';\n\n/**\n * @typedef {'addlayer'|'removelayer'} EventType\n */\n\n/**\n * @classdesc\n * A layer group triggers 'addlayer' and 'removelayer' events when layers are added to or removed from\n * the group or one of its child groups.  When a layer group is added to or removed from another layer group,\n * a single event will be triggered (instead of one per layer in the group added or removed).\n */\nexport class GroupEvent extends Event {\n  /**\n   * @param {EventType} type The event type.\n   * @param {BaseLayer} layer The layer.\n   */\n  constructor(type, layer) {\n    super(type);\n\n    /**\n     * The added or removed layer.\n     * @type {BaseLayer}\n     * @api\n     */\n    this.layer = layer;\n  }\n}\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"./Base\").BaseLayerObjectEventTypes|\n *     'change:layers', import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"./Base\").BaseLayerObjectEventTypes|'change:layers', Return>} GroupOnSignature\n */\n\n/**\n * @typedef {Object} Options\n * @property {number} [opacity=1] Opacity (0, 1).\n * @property {boolean} [visible=true] Visibility.\n * @property {import(\"../extent.js\").Extent} [extent] The bounding extent for layer rendering.  The layer will not be\n * rendered outside of this extent.\n * @property {number} [zIndex] The z-index for layer rendering.  At rendering time, the layers\n * will be ordered, first by Z-index and then by position. When `undefined`, a `zIndex` of 0 is assumed\n * for layers that are added to the map's `layers` collection, or `Infinity` when the layer's `setMap()`\n * method was used.\n * @property {number} [minResolution] The minimum resolution (inclusive) at which this layer will be\n * visible.\n * @property {number} [maxResolution] The maximum resolution (exclusive) below which this layer will\n * be visible.\n * @property {number} [minZoom] The minimum view zoom level (exclusive) above which this layer will be\n * visible.\n * @property {number} [maxZoom] The maximum view zoom level (inclusive) at which this layer will\n * be visible.\n * @property {Array<import(\"./Base.js\").default>|Collection<import(\"./Base.js\").default>} [layers] Child layers.\n * @property {Object<string, *>} [properties] Arbitrary observable properties. Can be accessed with `#get()` and `#set()`.\n */\n\n/**\n * @enum {string}\n * @private\n */\nconst Property = {\n  LAYERS: 'layers',\n};\n\n/**\n * @classdesc\n * A {@link module:ol/Collection~Collection} of layers that are handled together.\n *\n * A generic `change` event is triggered when the group/Collection changes.\n *\n * @api\n */\nclass LayerGroup extends BaseLayer {\n  /**\n   * @param {Options} [options] Layer options.\n   */\n  constructor(options) {\n    options = options || {};\n    const baseOptions = /** @type {Options} */ (Object.assign({}, options));\n    delete baseOptions.layers;\n\n    let layers = options.layers;\n\n    super(baseOptions);\n\n    /***\n     * @type {GroupOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {GroupOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {GroupOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * @private\n     * @type {Array<import(\"../events.js\").EventsKey>}\n     */\n    this.layersListenerKeys_ = [];\n\n    /**\n     * @private\n     * @type {Object<string, Array<import(\"../events.js\").EventsKey>>}\n     */\n    this.listenerKeys_ = {};\n\n    this.addChangeListener(Property.LAYERS, this.handleLayersChanged_);\n\n    if (layers) {\n      if (Array.isArray(layers)) {\n        layers = new Collection(layers.slice(), {unique: true});\n      } else {\n        assert(typeof (/** @type {?} */ (layers).getArray) === 'function', 43); // Expected `layers` to be an array or a `Collection`\n      }\n    } else {\n      layers = new Collection(undefined, {unique: true});\n    }\n\n    this.setLayers(layers);\n  }\n\n  /**\n   * @private\n   */\n  handleLayerChange_() {\n    this.changed();\n  }\n\n  /**\n   * @private\n   */\n  handleLayersChanged_() {\n    this.layersListenerKeys_.forEach(unlistenByKey);\n    this.layersListenerKeys_.length = 0;\n\n    const layers = this.getLayers();\n    this.layersListenerKeys_.push(\n      listen(layers, CollectionEventType.ADD, this.handleLayersAdd_, this),\n      listen(layers, CollectionEventType.REMOVE, this.handleLayersRemove_, this)\n    );\n\n    for (const id in this.listenerKeys_) {\n      this.listenerKeys_[id].forEach(unlistenByKey);\n    }\n    clear(this.listenerKeys_);\n\n    const layersArray = layers.getArray();\n    for (let i = 0, ii = layersArray.length; i < ii; i++) {\n      const layer = layersArray[i];\n      this.registerLayerListeners_(layer);\n      this.dispatchEvent(new GroupEvent('addlayer', layer));\n    }\n    this.changed();\n  }\n\n  /**\n   * @param {BaseLayer} layer The layer.\n   */\n  registerLayerListeners_(layer) {\n    const listenerKeys = [\n      listen(\n        layer,\n        ObjectEventType.PROPERTYCHANGE,\n        this.handleLayerChange_,\n        this\n      ),\n      listen(layer, EventType.CHANGE, this.handleLayerChange_, this),\n    ];\n\n    if (layer instanceof LayerGroup) {\n      listenerKeys.push(\n        listen(layer, 'addlayer', this.handleLayerGroupAdd_, this),\n        listen(layer, 'removelayer', this.handleLayerGroupRemove_, this)\n      );\n    }\n\n    this.listenerKeys_[getUid(layer)] = listenerKeys;\n  }\n\n  /**\n   * @param {GroupEvent} event The layer group event.\n   */\n  handleLayerGroupAdd_(event) {\n    this.dispatchEvent(new GroupEvent('addlayer', event.layer));\n  }\n\n  /**\n   * @param {GroupEvent} event The layer group event.\n   */\n  handleLayerGroupRemove_(event) {\n    this.dispatchEvent(new GroupEvent('removelayer', event.layer));\n  }\n\n  /**\n   * @param {import(\"../Collection.js\").CollectionEvent<import(\"./Base.js\").default>} collectionEvent CollectionEvent.\n   * @private\n   */\n  handleLayersAdd_(collectionEvent) {\n    const layer = collectionEvent.element;\n    this.registerLayerListeners_(layer);\n    this.dispatchEvent(new GroupEvent('addlayer', layer));\n    this.changed();\n  }\n\n  /**\n   * @param {import(\"../Collection.js\").CollectionEvent<import(\"./Base.js\").default>} collectionEvent CollectionEvent.\n   * @private\n   */\n  handleLayersRemove_(collectionEvent) {\n    const layer = collectionEvent.element;\n    const key = getUid(layer);\n    this.listenerKeys_[key].forEach(unlistenByKey);\n    delete this.listenerKeys_[key];\n    this.dispatchEvent(new GroupEvent('removelayer', layer));\n    this.changed();\n  }\n\n  /**\n   * Returns the {@link module:ol/Collection~Collection collection} of {@link module:ol/layer/Layer~Layer layers}\n   * in this group.\n   * @return {!Collection<import(\"./Base.js\").default>} Collection of\n   *   {@link module:ol/layer/Base~BaseLayer layers} that are part of this group.\n   * @observable\n   * @api\n   */\n  getLayers() {\n    return /** @type {!Collection<import(\"./Base.js\").default>} */ (\n      this.get(Property.LAYERS)\n    );\n  }\n\n  /**\n   * Set the {@link module:ol/Collection~Collection collection} of {@link module:ol/layer/Layer~Layer layers}\n   * in this group.\n   * @param {!Collection<import(\"./Base.js\").default>} layers Collection of\n   *   {@link module:ol/layer/Base~BaseLayer layers} that are part of this group.\n   * @observable\n   * @api\n   */\n  setLayers(layers) {\n    const collection = this.getLayers();\n    if (collection) {\n      const currentLayers = collection.getArray();\n      for (let i = 0, ii = currentLayers.length; i < ii; ++i) {\n        this.dispatchEvent(new GroupEvent('removelayer', currentLayers[i]));\n      }\n    }\n\n    this.set(Property.LAYERS, layers);\n  }\n\n  /**\n   * @param {Array<import(\"./Layer.js\").default>} [array] Array of layers (to be modified in place).\n   * @return {Array<import(\"./Layer.js\").default>} Array of layers.\n   */\n  getLayersArray(array) {\n    array = array !== undefined ? array : [];\n    this.getLayers().forEach(function (layer) {\n      layer.getLayersArray(array);\n    });\n    return array;\n  }\n\n  /**\n   * Get the layer states list and use this groups z-index as the default\n   * for all layers in this and nested groups, if it is unset at this point.\n   * If dest is not provided and this group's z-index is undefined\n   * 0 is used a the default z-index.\n   * @param {Array<import(\"./Layer.js\").State>} [dest] Optional list\n   * of layer states (to be modified in place).\n   * @return {Array<import(\"./Layer.js\").State>} List of layer states.\n   */\n  getLayerStatesArray(dest) {\n    const states = dest !== undefined ? dest : [];\n    const pos = states.length;\n\n    this.getLayers().forEach(function (layer) {\n      layer.getLayerStatesArray(states);\n    });\n\n    const ownLayerState = this.getLayerState();\n    let defaultZIndex = ownLayerState.zIndex;\n    if (!dest && ownLayerState.zIndex === undefined) {\n      defaultZIndex = 0;\n    }\n    for (let i = pos, ii = states.length; i < ii; i++) {\n      const layerState = states[i];\n      layerState.opacity *= ownLayerState.opacity;\n      layerState.visible = layerState.visible && ownLayerState.visible;\n      layerState.maxResolution = Math.min(\n        layerState.maxResolution,\n        ownLayerState.maxResolution\n      );\n      layerState.minResolution = Math.max(\n        layerState.minResolution,\n        ownLayerState.minResolution\n      );\n      layerState.minZoom = Math.max(layerState.minZoom, ownLayerState.minZoom);\n      layerState.maxZoom = Math.min(layerState.maxZoom, ownLayerState.maxZoom);\n      if (ownLayerState.extent !== undefined) {\n        if (layerState.extent !== undefined) {\n          layerState.extent = getIntersection(\n            layerState.extent,\n            ownLayerState.extent\n          );\n        } else {\n          layerState.extent = ownLayerState.extent;\n        }\n      }\n      if (layerState.zIndex === undefined) {\n        layerState.zIndex = defaultZIndex;\n      }\n    }\n\n    return states;\n  }\n\n  /**\n   * @return {import(\"../source/Source.js\").State} Source state.\n   */\n  getSourceState() {\n    return 'ready';\n  }\n}\n\nexport default LayerGroup;\n", "/**\n * @module ol/pointer/EventType\n */\n\n/**\n * Constants for event names.\n * @enum {string}\n */\nexport default {\n  POINTERMOVE: 'pointermove',\n  POINTERDOWN: 'pointerdown',\n  POINTERUP: 'pointerup',\n  POINTEROVER: 'pointerover',\n  POINTEROUT: 'pointerout',\n  POINTERENTER: 'pointerenter',\n  POINTERLEAVE: 'pointerleave',\n  POINTERCANCEL: 'pointercancel',\n};\n", "/**\n * @module ol/MapBrowserEventHandler\n */\n\nimport EventType from './events/EventType.js';\nimport MapBrowserEvent from './MapBrowserEvent.js';\nimport MapBrowserEventType from './MapBrowserEventType.js';\nimport PointerEventType from './pointer/EventType.js';\nimport Target from './events/Target.js';\nimport {PASSIVE_EVENT_LISTENERS} from './has.js';\nimport {listen, unlistenByKey} from './events.js';\n\nclass MapBrowserEventHandler extends Target {\n  /**\n   * @param {import(\"./Map.js\").default} map The map with the viewport to listen to events on.\n   * @param {number} [moveTolerance] The minimal distance the pointer must travel to trigger a move.\n   */\n  constructor(map, moveTolerance) {\n    super(map);\n\n    /**\n     * This is the element that we will listen to the real events on.\n     * @type {import(\"./Map.js\").default}\n     * @private\n     */\n    this.map_ = map;\n\n    /**\n     * @type {ReturnType<typeof setTimeout>}\n     * @private\n     */\n    this.clickTimeoutId_;\n\n    /**\n     * Emulate dblclick and singleclick. Will be true when only one pointer is active.\n     * @type {boolean}\n     */\n    this.emulateClicks_ = false;\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.dragging_ = false;\n\n    /**\n     * @type {!Array<import(\"./events.js\").EventsKey>}\n     * @private\n     */\n    this.dragListenerKeys_ = [];\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.moveTolerance_ = moveTolerance === undefined ? 1 : moveTolerance;\n\n    /**\n     * The most recent \"down\" type event (or null if none have occurred).\n     * Set on pointerdown.\n     * @type {PointerEvent|null}\n     * @private\n     */\n    this.down_ = null;\n\n    const element = this.map_.getViewport();\n\n    /**\n     * @type {Array<PointerEvent>}\n     * @private\n     */\n    this.activePointers_ = [];\n\n    /**\n     * @type {!Object<number, Event>}\n     * @private\n     */\n    this.trackedTouches_ = {};\n\n    this.element_ = element;\n\n    /**\n     * @type {?import(\"./events.js\").EventsKey}\n     * @private\n     */\n    this.pointerdownListenerKey_ = listen(\n      element,\n      PointerEventType.POINTERDOWN,\n      this.handlePointerDown_,\n      this\n    );\n\n    /**\n     * @type {PointerEvent}\n     * @private\n     */\n    this.originalPointerMoveEvent_;\n\n    /**\n     * @type {?import(\"./events.js\").EventsKey}\n     * @private\n     */\n    this.relayedListenerKey_ = listen(\n      element,\n      PointerEventType.POINTERMOVE,\n      this.relayMoveEvent_,\n      this\n    );\n\n    /**\n     * @private\n     */\n    this.boundHandleTouchMove_ = this.handleTouchMove_.bind(this);\n\n    this.element_.addEventListener(\n      EventType.TOUCHMOVE,\n      this.boundHandleTouchMove_,\n      PASSIVE_EVENT_LISTENERS ? {passive: false} : false\n    );\n  }\n\n  /**\n   * @param {PointerEvent} pointerEvent Pointer\n   * event.\n   * @private\n   */\n  emulateClick_(pointerEvent) {\n    let newEvent = new MapBrowserEvent(\n      MapBrowserEventType.CLICK,\n      this.map_,\n      pointerEvent\n    );\n    this.dispatchEvent(newEvent);\n    if (this.clickTimeoutId_ !== undefined) {\n      // double-click\n      clearTimeout(this.clickTimeoutId_);\n      this.clickTimeoutId_ = undefined;\n      newEvent = new MapBrowserEvent(\n        MapBrowserEventType.DBLCLICK,\n        this.map_,\n        pointerEvent\n      );\n      this.dispatchEvent(newEvent);\n    } else {\n      // click\n      this.clickTimeoutId_ = setTimeout(() => {\n        this.clickTimeoutId_ = undefined;\n        const newEvent = new MapBrowserEvent(\n          MapBrowserEventType.SINGLECLICK,\n          this.map_,\n          pointerEvent\n        );\n        this.dispatchEvent(newEvent);\n      }, 250);\n    }\n  }\n\n  /**\n   * Keeps track on how many pointers are currently active.\n   *\n   * @param {PointerEvent} pointerEvent Pointer\n   * event.\n   * @private\n   */\n  updateActivePointers_(pointerEvent) {\n    const event = pointerEvent;\n    const id = event.pointerId;\n\n    if (\n      event.type == MapBrowserEventType.POINTERUP ||\n      event.type == MapBrowserEventType.POINTERCANCEL\n    ) {\n      delete this.trackedTouches_[id];\n      for (const pointerId in this.trackedTouches_) {\n        if (this.trackedTouches_[pointerId].target !== event.target) {\n          // Some platforms assign a new pointerId when the target changes.\n          // If this happens, delete one tracked pointer. If there is more\n          // than one tracked pointer for the old target, it will be cleared\n          // by subsequent POINTERUP events from other pointers.\n          delete this.trackedTouches_[pointerId];\n          break;\n        }\n      }\n    } else if (\n      event.type == MapBrowserEventType.POINTERDOWN ||\n      event.type == MapBrowserEventType.POINTERMOVE\n    ) {\n      this.trackedTouches_[id] = event;\n    }\n    this.activePointers_ = Object.values(this.trackedTouches_);\n  }\n\n  /**\n   * @param {PointerEvent} pointerEvent Pointer\n   * event.\n   * @private\n   */\n  handlePointerUp_(pointerEvent) {\n    this.updateActivePointers_(pointerEvent);\n    const newEvent = new MapBrowserEvent(\n      MapBrowserEventType.POINTERUP,\n      this.map_,\n      pointerEvent,\n      undefined,\n      undefined,\n      this.activePointers_\n    );\n    this.dispatchEvent(newEvent);\n\n    // We emulate click events on left mouse button click, touch contact, and pen\n    // contact. isMouseActionButton returns true in these cases (evt.button is set\n    // to 0).\n    // See http://www.w3.org/TR/pointerevents/#button-states\n    // We only fire click, singleclick, and doubleclick if nobody has called\n    // event.preventDefault().\n    if (\n      this.emulateClicks_ &&\n      !newEvent.defaultPrevented &&\n      !this.dragging_ &&\n      this.isMouseActionButton_(pointerEvent)\n    ) {\n      this.emulateClick_(this.down_);\n    }\n\n    if (this.activePointers_.length === 0) {\n      this.dragListenerKeys_.forEach(unlistenByKey);\n      this.dragListenerKeys_.length = 0;\n      this.dragging_ = false;\n      this.down_ = null;\n    }\n  }\n\n  /**\n   * @param {PointerEvent} pointerEvent Pointer\n   * event.\n   * @return {boolean} If the left mouse button was pressed.\n   * @private\n   */\n  isMouseActionButton_(pointerEvent) {\n    return pointerEvent.button === 0;\n  }\n\n  /**\n   * @param {PointerEvent} pointerEvent Pointer\n   * event.\n   * @private\n   */\n  handlePointerDown_(pointerEvent) {\n    this.emulateClicks_ = this.activePointers_.length === 0;\n    this.updateActivePointers_(pointerEvent);\n    const newEvent = new MapBrowserEvent(\n      MapBrowserEventType.POINTERDOWN,\n      this.map_,\n      pointerEvent,\n      undefined,\n      undefined,\n      this.activePointers_\n    );\n    this.dispatchEvent(newEvent);\n\n    this.down_ = new PointerEvent(pointerEvent.type, pointerEvent);\n    Object.defineProperty(this.down_, 'target', {\n      writable: false,\n      value: pointerEvent.target,\n    });\n\n    if (this.dragListenerKeys_.length === 0) {\n      const doc = this.map_.getOwnerDocument();\n      this.dragListenerKeys_.push(\n        listen(\n          doc,\n          MapBrowserEventType.POINTERMOVE,\n          this.handlePointerMove_,\n          this\n        ),\n        listen(doc, MapBrowserEventType.POINTERUP, this.handlePointerUp_, this),\n        /* Note that the listener for `pointercancel is set up on\n         * `pointerEventHandler_` and not `documentPointerEventHandler_` like\n         * the `pointerup` and `pointermove` listeners.\n         *\n         * The reason for this is the following: `TouchSource.vacuumTouches_()`\n         * issues `pointercancel` events, when there was no `touchend` for a\n         * `touchstart`. Now, let's say a first `touchstart` is registered on\n         * `pointerEventHandler_`. The `documentPointerEventHandler_` is set up.\n         * But `documentPointerEventHandler_` doesn't know about the first\n         * `touchstart`. If there is no `touchend` for the `touchstart`, we can\n         * only receive a `touchcancel` from `pointerEventHandler_`, because it is\n         * only registered there.\n         */\n        listen(\n          this.element_,\n          MapBrowserEventType.POINTERCANCEL,\n          this.handlePointerUp_,\n          this\n        )\n      );\n      if (this.element_.getRootNode && this.element_.getRootNode() !== doc) {\n        this.dragListenerKeys_.push(\n          listen(\n            this.element_.getRootNode(),\n            MapBrowserEventType.POINTERUP,\n            this.handlePointerUp_,\n            this\n          )\n        );\n      }\n    }\n  }\n\n  /**\n   * @param {PointerEvent} pointerEvent Pointer\n   * event.\n   * @private\n   */\n  handlePointerMove_(pointerEvent) {\n    // Between pointerdown and pointerup, pointermove events are triggered.\n    // To avoid a 'false' touchmove event to be dispatched, we test if the pointer\n    // moved a significant distance.\n    if (this.isMoving_(pointerEvent)) {\n      this.updateActivePointers_(pointerEvent);\n      this.dragging_ = true;\n      const newEvent = new MapBrowserEvent(\n        MapBrowserEventType.POINTERDRAG,\n        this.map_,\n        pointerEvent,\n        this.dragging_,\n        undefined,\n        this.activePointers_\n      );\n      this.dispatchEvent(newEvent);\n    }\n  }\n\n  /**\n   * Wrap and relay a pointermove event.\n   * @param {PointerEvent} pointerEvent Pointer\n   * event.\n   * @private\n   */\n  relayMoveEvent_(pointerEvent) {\n    this.originalPointerMoveEvent_ = pointerEvent;\n    const dragging = !!(this.down_ && this.isMoving_(pointerEvent));\n    this.dispatchEvent(\n      new MapBrowserEvent(\n        MapBrowserEventType.POINTERMOVE,\n        this.map_,\n        pointerEvent,\n        dragging\n      )\n    );\n  }\n\n  /**\n   * Flexible handling of a `touch-action: none` css equivalent: because calling\n   * `preventDefault()` on a `pointermove` event does not stop native page scrolling\n   * and zooming, we also listen for `touchmove` and call `preventDefault()` on it\n   * when an interaction (currently `DragPan` handles the event.\n   * @param {TouchEvent} event Event.\n   * @private\n   */\n  handleTouchMove_(event) {\n    // Due to https://github.com/mpizenberg/elm-pep/issues/2, `this.originalPointerMoveEvent_`\n    // may not be initialized yet when we get here on a platform without native pointer events,\n    // when elm-pep is used as pointer events polyfill.\n    const originalEvent = this.originalPointerMoveEvent_;\n    if (\n      (!originalEvent || originalEvent.defaultPrevented) &&\n      (typeof event.cancelable !== 'boolean' || event.cancelable === true)\n    ) {\n      event.preventDefault();\n    }\n  }\n\n  /**\n   * @param {PointerEvent} pointerEvent Pointer\n   * event.\n   * @return {boolean} Is moving.\n   * @private\n   */\n  isMoving_(pointerEvent) {\n    return (\n      this.dragging_ ||\n      Math.abs(pointerEvent.clientX - this.down_.clientX) >\n        this.moveTolerance_ ||\n      Math.abs(pointerEvent.clientY - this.down_.clientY) > this.moveTolerance_\n    );\n  }\n\n  /**\n   * Clean up.\n   */\n  disposeInternal() {\n    if (this.relayedListenerKey_) {\n      unlistenByKey(this.relayedListenerKey_);\n      this.relayedListenerKey_ = null;\n    }\n    this.element_.removeEventListener(\n      EventType.TOUCHMOVE,\n      this.boundHandleTouchMove_\n    );\n\n    if (this.pointerdownListenerKey_) {\n      unlistenByKey(this.pointerdownListenerKey_);\n      this.pointerdownListenerKey_ = null;\n    }\n\n    this.dragListenerKeys_.forEach(unlistenByKey);\n    this.dragListenerKeys_.length = 0;\n\n    this.element_ = null;\n    super.disposeInternal();\n  }\n}\n\nexport default MapBrowserEventHandler;\n", "/**\n * @module ol/MapProperty\n */\n\n/**\n * @enum {string}\n */\nexport default {\n  LAYERGROUP: 'layergroup',\n  SIZE: 'size',\n  TARGET: 'target',\n  VIEW: 'view',\n};\n", "/**\n * @module ol/control/Control\n */\nimport BaseObject from '../Object.js';\nimport MapEventType from '../MapEventType.js';\nimport {VOID} from '../functions.js';\nimport {listen, unlisten<PERSON>y<PERSON><PERSON>} from '../events.js';\nimport {removeNode} from '../dom.js';\n\n/**\n * @typedef {Object} Options\n * @property {HTMLElement} [element] The element is the control's\n * container element. This only needs to be specified if you're developing\n * a custom control.\n * @property {function(import(\"../MapEvent.js\").default):void} [render] Function called when\n * the control should be re-rendered. This is called in a `requestAnimationFrame`\n * callback.\n * @property {HTMLElement|string} [target] Specify a target if you want\n * the control to be rendered outside of the map's viewport.\n */\n\n/**\n * @classdesc\n * A control is a visible widget with a DOM element in a fixed position on the\n * screen. They can involve user input (buttons), or be informational only;\n * the position is determined using CSS. By default these are placed in the\n * container with CSS class name `ol-overlaycontainer-stopevent`, but can use\n * any outside DOM element.\n *\n * This is the base class for controls. You can use it for simple custom\n * controls by creating the element with listeners, creating an instance:\n * ```js\n * const myControl = new Control({element: myElement});\n * ```\n * and then adding this to the map.\n *\n * The main advantage of having this as a control rather than a simple separate\n * DOM element is that preventing propagation is handled for you. Controls\n * will also be objects in a {@link module:ol/Collection~Collection}, so you can use their methods.\n *\n * You can also extend this base for your own control class. See\n * examples/custom-controls for an example of how to do this.\n *\n * @api\n */\nclass Control extends BaseObject {\n  /**\n   * @param {Options} options Control options.\n   */\n  constructor(options) {\n    super();\n\n    const element = options.element;\n    if (element && !options.target && !element.style.pointerEvents) {\n      element.style.pointerEvents = 'auto';\n    }\n\n    /**\n     * @protected\n     * @type {HTMLElement}\n     */\n    this.element = element ? element : null;\n\n    /**\n     * @private\n     * @type {HTMLElement}\n     */\n    this.target_ = null;\n\n    /**\n     * @private\n     * @type {import(\"../Map.js\").default|null}\n     */\n    this.map_ = null;\n\n    /**\n     * @protected\n     * @type {!Array<import(\"../events.js\").EventsKey>}\n     */\n    this.listenerKeys = [];\n\n    if (options.render) {\n      this.render = options.render;\n    }\n\n    if (options.target) {\n      this.setTarget(options.target);\n    }\n  }\n\n  /**\n   * Clean up.\n   */\n  disposeInternal() {\n    removeNode(this.element);\n    super.disposeInternal();\n  }\n\n  /**\n   * Get the map associated with this control.\n   * @return {import(\"../Map.js\").default|null} Map.\n   * @api\n   */\n  getMap() {\n    return this.map_;\n  }\n\n  /**\n   * Remove the control from its current map and attach it to the new map.\n   * Pass `null` to just remove the control from the current map.\n   * Subclasses may set up event handlers to get notified about changes to\n   * the map here.\n   * @param {import(\"../Map.js\").default|null} map Map.\n   * @api\n   */\n  setMap(map) {\n    if (this.map_) {\n      removeNode(this.element);\n    }\n    for (let i = 0, ii = this.listenerKeys.length; i < ii; ++i) {\n      unlistenByKey(this.listenerKeys[i]);\n    }\n    this.listenerKeys.length = 0;\n    this.map_ = map;\n    if (map) {\n      const target = this.target_\n        ? this.target_\n        : map.getOverlayContainerStopEvent();\n      target.appendChild(this.element);\n      if (this.render !== VOID) {\n        this.listenerKeys.push(\n          listen(map, MapEventType.POSTRENDER, this.render, this)\n        );\n      }\n      map.render();\n    }\n  }\n\n  /**\n   * Renders the control.\n   * @param {import(\"../MapEvent.js\").default} mapEvent Map event.\n   * @api\n   */\n  render(mapEvent) {}\n\n  /**\n   * This function is used to set a target element for the control. It has no\n   * effect if it is called after the control has been added to the map (i.e.\n   * after `setMap` is called on the control). If no `target` is set in the\n   * options passed to the control constructor and if `setTarget` is not called\n   * then the control is added to the map's overlay container.\n   * @param {HTMLElement|string} target Target.\n   * @api\n   */\n  setTarget(target) {\n    this.target_ =\n      typeof target === 'string' ? document.getElementById(target) : target;\n  }\n}\n\nexport default Control;\n", "/**\n * @module ol/control/Attribution\n */\nimport Control from './Control.js';\nimport EventType from '../events/EventType.js';\nimport {CLASS_COLLAPSED, CLASS_CONTROL, CLASS_UNSELECTABLE} from '../css.js';\nimport {equals} from '../array.js';\nimport {removeChildren, replaceNode} from '../dom.js';\n\n/**\n * @typedef {Object} Options\n * @property {string} [className='ol-attribution'] CSS class name.\n * @property {HTMLElement|string} [target] Specify a target if you\n * want the control to be rendered outside of the map's\n * viewport.\n * @property {boolean} [collapsible] Specify if attributions can\n * be collapsed. If not specified, sources control this behavior with their\n * `attributionsCollapsible` setting.\n * @property {boolean} [collapsed=true] Specify if attributions should\n * be collapsed at startup.\n * @property {string} [tipLabel='Attributions'] Text label to use for the button tip.\n * @property {string|HTMLElement} [label='i'] Text label to use for the\n * collapsed attributions button.\n * Instead of text, also an element (e.g. a `span` element) can be used.\n * @property {string} [expandClassName=className + '-expand'] CSS class name for the\n * collapsed attributions button.\n * @property {string|HTMLElement} [collapseLabel='›'] Text label to use\n * for the expanded attributions button.\n * Instead of text, also an element (e.g. a `span` element) can be used.\n * @property {string} [collapseClassName=className + '-collapse'] CSS class name for the\n * expanded attributions button.\n * @property {function(import(\"../MapEvent.js\").default):void} [render] Function called when\n * the control should be re-rendered. This is called in a `requestAnimationFrame`\n * callback.\n */\n\n/**\n * @classdesc\n * Control to show all the attributions associated with the layer sources\n * in the map. This control is one of the default controls included in maps.\n * By default it will show in the bottom right portion of the map, but this can\n * be changed by using a css selector for `.ol-attribution`.\n *\n * @api\n */\nclass Attribution extends Control {\n  /**\n   * @param {Options} [options] Attribution options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    super({\n      element: document.createElement('div'),\n      render: options.render,\n      target: options.target,\n    });\n\n    /**\n     * @private\n     * @type {HTMLElement}\n     */\n    this.ulElement_ = document.createElement('ul');\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.collapsed_ =\n      options.collapsed !== undefined ? options.collapsed : true;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.userCollapsed_ = this.collapsed_;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.overrideCollapsible_ = options.collapsible !== undefined;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.collapsible_ =\n      options.collapsible !== undefined ? options.collapsible : true;\n\n    if (!this.collapsible_) {\n      this.collapsed_ = false;\n    }\n\n    const className =\n      options.className !== undefined ? options.className : 'ol-attribution';\n\n    const tipLabel =\n      options.tipLabel !== undefined ? options.tipLabel : 'Attributions';\n\n    const expandClassName =\n      options.expandClassName !== undefined\n        ? options.expandClassName\n        : className + '-expand';\n\n    const collapseLabel =\n      options.collapseLabel !== undefined ? options.collapseLabel : '\\u203A';\n\n    const collapseClassName =\n      options.collapseClassName !== undefined\n        ? options.collapseClassName\n        : className + '-collapse';\n\n    if (typeof collapseLabel === 'string') {\n      /**\n       * @private\n       * @type {HTMLElement}\n       */\n      this.collapseLabel_ = document.createElement('span');\n      this.collapseLabel_.textContent = collapseLabel;\n      this.collapseLabel_.className = collapseClassName;\n    } else {\n      this.collapseLabel_ = collapseLabel;\n    }\n\n    const label = options.label !== undefined ? options.label : 'i';\n\n    if (typeof label === 'string') {\n      /**\n       * @private\n       * @type {HTMLElement}\n       */\n      this.label_ = document.createElement('span');\n      this.label_.textContent = label;\n      this.label_.className = expandClassName;\n    } else {\n      this.label_ = label;\n    }\n\n    const activeLabel =\n      this.collapsible_ && !this.collapsed_ ? this.collapseLabel_ : this.label_;\n\n    /**\n     * @private\n     * @type {HTMLElement}\n     */\n    this.toggleButton_ = document.createElement('button');\n    this.toggleButton_.setAttribute('type', 'button');\n    this.toggleButton_.setAttribute('aria-expanded', String(!this.collapsed_));\n    this.toggleButton_.title = tipLabel;\n    this.toggleButton_.appendChild(activeLabel);\n\n    this.toggleButton_.addEventListener(\n      EventType.CLICK,\n      this.handleClick_.bind(this),\n      false\n    );\n\n    const cssClasses =\n      className +\n      ' ' +\n      CLASS_UNSELECTABLE +\n      ' ' +\n      CLASS_CONTROL +\n      (this.collapsed_ && this.collapsible_ ? ' ' + CLASS_COLLAPSED : '') +\n      (this.collapsible_ ? '' : ' ol-uncollapsible');\n    const element = this.element;\n    element.className = cssClasses;\n    element.appendChild(this.toggleButton_);\n    element.appendChild(this.ulElement_);\n\n    /**\n     * A list of currently rendered resolutions.\n     * @type {Array<string>}\n     * @private\n     */\n    this.renderedAttributions_ = [];\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.renderedVisible_ = true;\n  }\n\n  /**\n   * Collect a list of visible attributions and set the collapsible state.\n   * @param {import(\"../Map.js\").FrameState} frameState Frame state.\n   * @return {Array<string>} Attributions.\n   * @private\n   */\n  collectSourceAttributions_(frameState) {\n    const visibleAttributions = Array.from(\n      new Set(\n        this.getMap()\n          .getAllLayers()\n          .flatMap((layer) => layer.getAttributions(frameState))\n      )\n    );\n\n    const collapsible = !this.getMap()\n      .getAllLayers()\n      .some(\n        (layer) =>\n          layer.getSource() &&\n          layer.getSource().getAttributionsCollapsible() === false\n      );\n    if (!this.overrideCollapsible_) {\n      this.setCollapsible(collapsible);\n    }\n    return visibleAttributions;\n  }\n\n  /**\n   * @private\n   * @param {?import(\"../Map.js\").FrameState} frameState Frame state.\n   */\n  updateElement_(frameState) {\n    if (!frameState) {\n      if (this.renderedVisible_) {\n        this.element.style.display = 'none';\n        this.renderedVisible_ = false;\n      }\n      return;\n    }\n\n    const attributions = this.collectSourceAttributions_(frameState);\n\n    const visible = attributions.length > 0;\n    if (this.renderedVisible_ != visible) {\n      this.element.style.display = visible ? '' : 'none';\n      this.renderedVisible_ = visible;\n    }\n\n    if (equals(attributions, this.renderedAttributions_)) {\n      return;\n    }\n\n    removeChildren(this.ulElement_);\n\n    // append the attributions\n    for (let i = 0, ii = attributions.length; i < ii; ++i) {\n      const element = document.createElement('li');\n      element.innerHTML = attributions[i];\n      this.ulElement_.appendChild(element);\n    }\n\n    this.renderedAttributions_ = attributions;\n  }\n\n  /**\n   * @param {MouseEvent} event The event to handle\n   * @private\n   */\n  handleClick_(event) {\n    event.preventDefault();\n    this.handleToggle_();\n    this.userCollapsed_ = this.collapsed_;\n  }\n\n  /**\n   * @private\n   */\n  handleToggle_() {\n    this.element.classList.toggle(CLASS_COLLAPSED);\n    if (this.collapsed_) {\n      replaceNode(this.collapseLabel_, this.label_);\n    } else {\n      replaceNode(this.label_, this.collapseLabel_);\n    }\n    this.collapsed_ = !this.collapsed_;\n    this.toggleButton_.setAttribute('aria-expanded', String(!this.collapsed_));\n  }\n\n  /**\n   * Return `true` if the attribution is collapsible, `false` otherwise.\n   * @return {boolean} True if the widget is collapsible.\n   * @api\n   */\n  getCollapsible() {\n    return this.collapsible_;\n  }\n\n  /**\n   * Set whether the attribution should be collapsible.\n   * @param {boolean} collapsible True if the widget is collapsible.\n   * @api\n   */\n  setCollapsible(collapsible) {\n    if (this.collapsible_ === collapsible) {\n      return;\n    }\n    this.collapsible_ = collapsible;\n    this.element.classList.toggle('ol-uncollapsible');\n    if (this.userCollapsed_) {\n      this.handleToggle_();\n    }\n  }\n\n  /**\n   * Collapse or expand the attribution according to the passed parameter. Will\n   * not do anything if the attribution isn't collapsible or if the current\n   * collapsed state is already the one requested.\n   * @param {boolean} collapsed True if the widget is collapsed.\n   * @api\n   */\n  setCollapsed(collapsed) {\n    this.userCollapsed_ = collapsed;\n    if (!this.collapsible_ || this.collapsed_ === collapsed) {\n      return;\n    }\n    this.handleToggle_();\n  }\n\n  /**\n   * Return `true` when the attribution is currently collapsed or `false`\n   * otherwise.\n   * @return {boolean} True if the widget is collapsed.\n   * @api\n   */\n  getCollapsed() {\n    return this.collapsed_;\n  }\n\n  /**\n   * Update the attribution element.\n   * @param {import(\"../MapEvent.js\").default} mapEvent Map event.\n   * @override\n   */\n  render(mapEvent) {\n    this.updateElement_(mapEvent.frameState);\n  }\n}\n\nexport default Attribution;\n", "/**\n * @module ol/control/Rotate\n */\nimport Control from './Control.js';\nimport EventType from '../events/EventType.js';\nimport {CLASS_CONTROL, CLASS_HIDDEN, CLASS_UNSELECTABLE} from '../css.js';\nimport {easeOut} from '../easing.js';\n\n/**\n * @typedef {Object} Options\n * @property {string} [className='ol-rotate'] CSS class name.\n * @property {string|HTMLElement} [label='⇧'] Text label to use for the rotate button.\n * Instead of text, also an element (e.g. a `span` element) can be used.\n * @property {string} [tipLabel='Reset rotation'] Text label to use for the rotate tip.\n * @property {string} [compassClassName='ol-compass'] CSS class name for the compass.\n * @property {number} [duration=250] Animation duration in milliseconds.\n * @property {boolean} [autoHide=true] Hide the control when rotation is 0.\n * @property {function(import(\"../MapEvent.js\").default):void} [render] Function called when the control should\n * be re-rendered. This is called in a `requestAnimationFrame` callback.\n * @property {function():void} [resetNorth] Function called when the control is clicked.\n * This will override the default `resetNorth`.\n * @property {HTMLElement|string} [target] Specify a target if you want the control to be\n * rendered outside of the map's viewport.\n */\n\n/**\n * @classdesc\n * A button control to reset rotation to 0.\n * To style this control use css selector `.ol-rotate`. A `.ol-hidden` css\n * selector is added to the button when the rotation is 0.\n *\n * @api\n */\nclass Rotate extends Control {\n  /**\n   * @param {Options} [options] Rotate options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    super({\n      element: document.createElement('div'),\n      render: options.render,\n      target: options.target,\n    });\n\n    const className =\n      options.className !== undefined ? options.className : 'ol-rotate';\n\n    const label = options.label !== undefined ? options.label : '\\u21E7';\n\n    const compassClassName =\n      options.compassClassName !== undefined\n        ? options.compassClassName\n        : 'ol-compass';\n\n    /**\n     * @type {HTMLElement}\n     * @private\n     */\n    this.label_ = null;\n\n    if (typeof label === 'string') {\n      this.label_ = document.createElement('span');\n      this.label_.className = compassClassName;\n      this.label_.textContent = label;\n    } else {\n      this.label_ = label;\n      this.label_.classList.add(compassClassName);\n    }\n\n    const tipLabel = options.tipLabel ? options.tipLabel : 'Reset rotation';\n\n    const button = document.createElement('button');\n    button.className = className + '-reset';\n    button.setAttribute('type', 'button');\n    button.title = tipLabel;\n    button.appendChild(this.label_);\n\n    button.addEventListener(\n      EventType.CLICK,\n      this.handleClick_.bind(this),\n      false\n    );\n\n    const cssClasses =\n      className + ' ' + CLASS_UNSELECTABLE + ' ' + CLASS_CONTROL;\n    const element = this.element;\n    element.className = cssClasses;\n    element.appendChild(button);\n\n    this.callResetNorth_ = options.resetNorth ? options.resetNorth : undefined;\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.duration_ = options.duration !== undefined ? options.duration : 250;\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.autoHide_ = options.autoHide !== undefined ? options.autoHide : true;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.rotation_ = undefined;\n\n    if (this.autoHide_) {\n      this.element.classList.add(CLASS_HIDDEN);\n    }\n  }\n\n  /**\n   * @param {MouseEvent} event The event to handle\n   * @private\n   */\n  handleClick_(event) {\n    event.preventDefault();\n    if (this.callResetNorth_ !== undefined) {\n      this.callResetNorth_();\n    } else {\n      this.resetNorth_();\n    }\n  }\n\n  /**\n   * @private\n   */\n  resetNorth_() {\n    const map = this.getMap();\n    const view = map.getView();\n    if (!view) {\n      // the map does not have a view, so we can't act\n      // upon it\n      return;\n    }\n    const rotation = view.getRotation();\n    if (rotation !== undefined) {\n      if (this.duration_ > 0 && rotation % (2 * Math.PI) !== 0) {\n        view.animate({\n          rotation: 0,\n          duration: this.duration_,\n          easing: easeOut,\n        });\n      } else {\n        view.setRotation(0);\n      }\n    }\n  }\n\n  /**\n   * Update the rotate control element.\n   * @param {import(\"../MapEvent.js\").default} mapEvent Map event.\n   * @override\n   */\n  render(mapEvent) {\n    const frameState = mapEvent.frameState;\n    if (!frameState) {\n      return;\n    }\n    const rotation = frameState.viewState.rotation;\n    if (rotation != this.rotation_) {\n      const transform = 'rotate(' + rotation + 'rad)';\n      if (this.autoHide_) {\n        const contains = this.element.classList.contains(CLASS_HIDDEN);\n        if (!contains && rotation === 0) {\n          this.element.classList.add(CLASS_HIDDEN);\n        } else if (contains && rotation !== 0) {\n          this.element.classList.remove(CLASS_HIDDEN);\n        }\n      }\n      this.label_.style.transform = transform;\n    }\n    this.rotation_ = rotation;\n  }\n}\n\nexport default Rotate;\n", "/**\n * @module ol/control/Zoom\n */\nimport Control from './Control.js';\nimport EventType from '../events/EventType.js';\nimport {CLASS_CONTROL, CLASS_UNSELECTABLE} from '../css.js';\nimport {easeOut} from '../easing.js';\n\n/**\n * @typedef {Object} Options\n * @property {number} [duration=250] Animation duration in milliseconds.\n * @property {string} [className='ol-zoom'] CSS class name.\n * @property {string} [zoomInClassName=className + '-in'] CSS class name for the zoom-in button.\n * @property {string} [zoomOutClassName=className + '-out'] CSS class name for the zoom-out button.\n * @property {string|HTMLElement} [zoomInLabel='+'] Text label to use for the zoom-in\n * button. Instead of text, also an element (e.g. a `span` element) can be used.\n * @property {string|HTMLElement} [zoomOutLabel='–'] Text label to use for the zoom-out button.\n * Instead of text, also an element (e.g. a `span` element) can be used.\n * @property {string} [zoomInTipLabel='Zoom in'] Text label to use for the button tip.\n * @property {string} [zoomOutTipLabel='Zoom out'] Text label to use for the button tip.\n * @property {number} [delta=1] The zoom delta applied on each click.\n * @property {HTMLElement|string} [target] Specify a target if you want the control to be\n * rendered outside of the map's viewport.\n */\n\n/**\n * @classdesc\n * A control with 2 buttons, one for zoom in and one for zoom out.\n * This control is one of the default controls of a map. To style this control\n * use css selectors `.ol-zoom-in` and `.ol-zoom-out`.\n *\n * @api\n */\nclass Zoom extends Control {\n  /**\n   * @param {Options} [options] Zoom options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    super({\n      element: document.createElement('div'),\n      target: options.target,\n    });\n\n    const className =\n      options.className !== undefined ? options.className : 'ol-zoom';\n\n    const delta = options.delta !== undefined ? options.delta : 1;\n\n    const zoomInClassName =\n      options.zoomInClassName !== undefined\n        ? options.zoomInClassName\n        : className + '-in';\n\n    const zoomOutClassName =\n      options.zoomOutClassName !== undefined\n        ? options.zoomOutClassName\n        : className + '-out';\n\n    const zoomInLabel =\n      options.zoomInLabel !== undefined ? options.zoomInLabel : '+';\n    const zoomOutLabel =\n      options.zoomOutLabel !== undefined ? options.zoomOutLabel : '\\u2013';\n\n    const zoomInTipLabel =\n      options.zoomInTipLabel !== undefined ? options.zoomInTipLabel : 'Zoom in';\n    const zoomOutTipLabel =\n      options.zoomOutTipLabel !== undefined\n        ? options.zoomOutTipLabel\n        : 'Zoom out';\n\n    const inElement = document.createElement('button');\n    inElement.className = zoomInClassName;\n    inElement.setAttribute('type', 'button');\n    inElement.title = zoomInTipLabel;\n    inElement.appendChild(\n      typeof zoomInLabel === 'string'\n        ? document.createTextNode(zoomInLabel)\n        : zoomInLabel\n    );\n\n    inElement.addEventListener(\n      EventType.CLICK,\n      this.handleClick_.bind(this, delta),\n      false\n    );\n\n    const outElement = document.createElement('button');\n    outElement.className = zoomOutClassName;\n    outElement.setAttribute('type', 'button');\n    outElement.title = zoomOutTipLabel;\n    outElement.appendChild(\n      typeof zoomOutLabel === 'string'\n        ? document.createTextNode(zoomOutLabel)\n        : zoomOutLabel\n    );\n\n    outElement.addEventListener(\n      EventType.CLICK,\n      this.handleClick_.bind(this, -delta),\n      false\n    );\n\n    const cssClasses =\n      className + ' ' + CLASS_UNSELECTABLE + ' ' + CLASS_CONTROL;\n    const element = this.element;\n    element.className = cssClasses;\n    element.appendChild(inElement);\n    element.appendChild(outElement);\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.duration_ = options.duration !== undefined ? options.duration : 250;\n  }\n\n  /**\n   * @param {number} delta Zoom delta.\n   * @param {MouseEvent} event The event to handle\n   * @private\n   */\n  handleClick_(delta, event) {\n    event.preventDefault();\n    this.zoomByDelta_(delta);\n  }\n\n  /**\n   * @param {number} delta Zoom delta.\n   * @private\n   */\n  zoomByDelta_(delta) {\n    const map = this.getMap();\n    const view = map.getView();\n    if (!view) {\n      // the map does not have a view, so we can't act\n      // upon it\n      return;\n    }\n    const currentZoom = view.getZoom();\n    if (currentZoom !== undefined) {\n      const newZoom = view.getConstrainedZoom(currentZoom + delta);\n      if (this.duration_ > 0) {\n        if (view.getAnimating()) {\n          view.cancelAnimations();\n        }\n        view.animate({\n          zoom: newZoom,\n          duration: this.duration_,\n          easing: easeOut,\n        });\n      } else {\n        view.setZoom(newZoom);\n      }\n    }\n  }\n}\n\nexport default Zoom;\n", "/**\n * @module ol/control/defaults\n */\nimport Attribution from './Attribution.js';\nimport Collection from '../Collection.js';\nimport Rotate from './Rotate.js';\nimport Zoom from './Zoom.js';\n\n/**\n * @typedef {Object} DefaultsOptions\n * @property {boolean} [attribution=true] Include\n * {@link module:ol/control/Attribution~Attribution}.\n * @property {import(\"./Attribution.js\").Options} [attributionOptions]\n * Options for {@link module:ol/control/Attribution~Attribution}.\n * @property {boolean} [rotate=true] Include\n * {@link module:ol/control/Rotate~Rotate}.\n * @property {import(\"./Rotate.js\").Options} [rotateOptions] Options\n * for {@link module:ol/control/Rotate~Rotate}.\n * @property {boolean} [zoom] Include {@link module:ol/control/Zoom~Zoom}.\n * @property {import(\"./Zoom.js\").Options} [zoomOptions] Options for\n * {@link module:ol/control/Zoom~Zoom}.\n */\n\n/**\n * Set of controls included in maps by default. Unless configured otherwise,\n * this returns a collection containing an instance of each of the following\n * controls:\n * * {@link module:ol/control/Zoom~Zoom}\n * * {@link module:ol/control/Rotate~Rotate}\n * * {@link module:ol/control/Attribution~Attribution}\n *\n * @param {DefaultsOptions} [options] Options for the default controls.\n * @return {Collection<import(\"./Control.js\").default>} A collection of controls\n * to be used with the {@link module:ol/Map~Map} constructor's `controls` option.\n * @api\n */\nexport function defaults(options) {\n  options = options ? options : {};\n\n  /** @type {Collection<import(\"./Control.js\").default>} */\n  const controls = new Collection();\n\n  const zoomControl = options.zoom !== undefined ? options.zoom : true;\n  if (zoomControl) {\n    controls.push(new Zoom(options.zoomOptions));\n  }\n\n  const rotateControl = options.rotate !== undefined ? options.rotate : true;\n  if (rotateControl) {\n    controls.push(new Rotate(options.rotateOptions));\n  }\n\n  const attributionControl =\n    options.attribution !== undefined ? options.attribution : true;\n  if (attributionControl) {\n    controls.push(new Attribution(options.attributionOptions));\n  }\n\n  return controls;\n}\n", "/**\n * @module ol/Map\n */\nimport BaseObject from './Object.js';\nimport Collection from './Collection.js';\nimport CollectionEventType from './CollectionEventType.js';\nimport CompositeMapRenderer from './renderer/Composite.js';\nimport EventType from './events/EventType.js';\nimport Layer from './layer/Layer.js';\nimport LayerGroup, {GroupEvent} from './layer/Group.js';\nimport MapBrowserEvent from './MapBrowserEvent.js';\nimport MapBrowserEventHandler from './MapBrowserEventHandler.js';\nimport MapBrowserEventType from './MapBrowserEventType.js';\nimport MapEvent from './MapEvent.js';\nimport MapEventType from './MapEventType.js';\nimport MapProperty from './MapProperty.js';\nimport ObjectEventType from './ObjectEventType.js';\nimport PointerEventType from './pointer/EventType.js';\nimport RenderEventType from './render/EventType.js';\nimport TileQueue, {getTilePriority} from './TileQueue.js';\nimport View from './View.js';\nimport ViewHint from './ViewHint.js';\nimport {DEVICE_PIXEL_RATIO, PASSIVE_EVENT_LISTENERS} from './has.js';\nimport {TRUE} from './functions.js';\nimport {\n  apply as applyTransform,\n  create as createTransform,\n} from './transform.js';\nimport {assert} from './asserts.js';\nimport {\n  clone,\n  createOrUpdateEmpty,\n  equals as equalsExtent,\n  getForViewAndSize,\n  isEmpty,\n} from './extent.js';\nimport {defaults as defaultControls} from './control/defaults.js';\nimport {defaults as defaultInteractions} from './interaction/defaults.js';\nimport {equals} from './array.js';\nimport {fromUserCoordinate, toUserCoordinate} from './proj.js';\nimport {getUid} from './util.js';\nimport {hasArea} from './size.js';\nimport {listen, unlistenByKey} from './events.js';\nimport {removeNode} from './dom.js';\nimport {warn} from './console.js';\n\n/**\n * State of the current frame. Only `pixelRatio`, `time` and `viewState` should\n * be used in applications.\n * @typedef {Object} FrameState\n * @property {number} pixelRatio The pixel ratio of the frame.\n * @property {number} time The time when rendering of the frame was requested.\n * @property {import(\"./View.js\").State} viewState The state of the current view.\n * @property {boolean} animate Animate.\n * @property {import(\"./transform.js\").Transform} coordinateToPixelTransform CoordinateToPixelTransform.\n * @property {import(\"rbush\").default} declutterTree DeclutterTree.\n * @property {null|import(\"./extent.js\").Extent} extent Extent (in view projection coordinates).\n * @property {import(\"./extent.js\").Extent} [nextExtent] Next extent during an animation series.\n * @property {number} index Index.\n * @property {Array<import(\"./layer/Layer.js\").State>} layerStatesArray LayerStatesArray.\n * @property {number} layerIndex LayerIndex.\n * @property {import(\"./transform.js\").Transform} pixelToCoordinateTransform PixelToCoordinateTransform.\n * @property {Array<PostRenderFunction>} postRenderFunctions PostRenderFunctions.\n * @property {import(\"./size.js\").Size} size Size.\n * @property {TileQueue} tileQueue TileQueue.\n * @property {!Object<string, Object<string, boolean>>} usedTiles UsedTiles.\n * @property {Array<number>} viewHints ViewHints.\n * @property {!Object<string, Object<string, boolean>>} wantedTiles WantedTiles.\n * @property {string} mapId The id of the map.\n * @property {Object<string, boolean>} renderTargets Identifiers of previously rendered elements.\n */\n\n/**\n * @typedef {function(Map, ?FrameState): any} PostRenderFunction\n */\n\n/**\n * @typedef {Object} AtPixelOptions\n * @property {undefined|function(import(\"./layer/Layer.js\").default<import(\"./source/Source\").default>): boolean} [layerFilter] Layer filter\n * function. The filter function will receive one argument, the\n * {@link module:ol/layer/Layer~Layer layer-candidate} and it should return a boolean value.\n * Only layers which are visible and for which this function returns `true`\n * will be tested for features. By default, all visible layers will be tested.\n * @property {number} [hitTolerance=0] Hit-detection tolerance in css pixels. Pixels\n * inside the radius around the given position will be checked for features.\n * @property {boolean} [checkWrapped=true] Check-Wrapped Will check for wrapped geometries inside the range of\n *   +/- 1 world width. Works only if a projection is used that can be wrapped.\n */\n\n/**\n * @typedef {Object} MapOptionsInternal\n * @property {Collection<import(\"./control/Control.js\").default>} [controls] Controls.\n * @property {Collection<import(\"./interaction/Interaction.js\").default>} [interactions] Interactions.\n * @property {HTMLElement|Document} keyboardEventTarget KeyboardEventTarget.\n * @property {Collection<import(\"./Overlay.js\").default>} overlays Overlays.\n * @property {Object<string, *>} values Values.\n */\n\n/**\n * @typedef {import(\"./ObjectEventType\").Types|'change:layergroup'|'change:size'|'change:target'|'change:view'} MapObjectEventTypes\n */\n\n/***\n * @template Return\n * @typedef {import(\"./Observable\").OnSignature<import(\"./Observable\").EventTypes, import(\"./events/Event.js\").default, Return> &\n *    import(\"./Observable\").OnSignature<MapObjectEventTypes, import(\"./Object\").ObjectEvent, Return> &\n *    import(\"./Observable\").OnSignature<import(\"./MapBrowserEventType\").Types, import(\"./MapBrowserEvent\").default, Return> &\n *    import(\"./Observable\").OnSignature<import(\"./MapEventType\").Types, import(\"./MapEvent\").default, Return> &\n *    import(\"./Observable\").OnSignature<import(\"./render/EventType\").MapRenderEventTypes, import(\"./render/Event\").default, Return> &\n *    import(\"./Observable\").CombinedOnSignature<import(\"./Observable\").EventTypes|MapObjectEventTypes|\n *      import(\"./MapBrowserEventType\").Types|import(\"./MapEventType\").Types|\n *      import(\"./render/EventType\").MapRenderEventTypes, Return>} MapEventHandler\n */\n\n/**\n * Object literal with config options for the map.\n * @typedef {Object} MapOptions\n * @property {Collection<import(\"./control/Control.js\").default>|Array<import(\"./control/Control.js\").default>} [controls]\n * Controls initially added to the map. If not specified,\n * {@link module:ol/control/defaults.defaults} is used.\n * @property {number} [pixelRatio=window.devicePixelRatio] The ratio between\n * physical pixels and device-independent pixels (dips) on the device.\n * @property {Collection<import(\"./interaction/Interaction.js\").default>|Array<import(\"./interaction/Interaction.js\").default>} [interactions]\n * Interactions that are initially added to the map. If not specified,\n * {@link module:ol/interaction/defaults.defaults} is used.\n * @property {HTMLElement|Document|string} [keyboardEventTarget] The element to\n * listen to keyboard events on. This determines when the `KeyboardPan` and\n * `KeyboardZoom` interactions trigger. For example, if this option is set to\n * `document` the keyboard interactions will always trigger. If this option is\n * not specified, the element the library listens to keyboard events on is the\n * map target (i.e. the user-provided div for the map). If this is not\n * `document`, the target element needs to be focused for key events to be\n * emitted, requiring that the target element has a `tabindex` attribute.\n * @property {Array<import(\"./layer/Base.js\").default>|Collection<import(\"./layer/Base.js\").default>|LayerGroup} [layers]\n * Layers. If this is not defined, a map with no layers will be rendered. Note\n * that layers are rendered in the order supplied, so if you want, for example,\n * a vector layer to appear on top of a tile layer, it must come after the tile\n * layer.\n * @property {number} [maxTilesLoading=16] Maximum number tiles to load\n * simultaneously.\n * @property {number} [moveTolerance=1] The minimum distance in pixels the\n * cursor must move to be detected as a map move event instead of a click.\n * Increasing this value can make it easier to click on the map.\n * @property {Collection<import(\"./Overlay.js\").default>|Array<import(\"./Overlay.js\").default>} [overlays]\n * Overlays initially added to the map. By default, no overlays are added.\n * @property {HTMLElement|string} [target] The container for the map, either the\n * element itself or the `id` of the element. If not specified at construction\n * time, {@link module:ol/Map~Map#setTarget} must be called for the map to be\n * rendered. If passed by element, the container can be in a secondary document.\n * **Note:** CSS `transform` support for the target element is limited to `scale`.\n * @property {View|Promise<import(\"./View.js\").ViewOptions>} [view] The map's view.  No layer sources will be\n * fetched unless this is specified at construction time or through\n * {@link module:ol/Map~Map#setView}.\n */\n\n/**\n * @param {import(\"./layer/Base.js\").default} layer Layer.\n */\nfunction removeLayerMapProperty(layer) {\n  if (layer instanceof Layer) {\n    layer.setMapInternal(null);\n    return;\n  }\n  if (layer instanceof LayerGroup) {\n    layer.getLayers().forEach(removeLayerMapProperty);\n  }\n}\n\n/**\n * @param {import(\"./layer/Base.js\").default} layer Layer.\n * @param {Map} map Map.\n */\nfunction setLayerMapProperty(layer, map) {\n  if (layer instanceof Layer) {\n    layer.setMapInternal(map);\n    return;\n  }\n  if (layer instanceof LayerGroup) {\n    const layers = layer.getLayers().getArray();\n    for (let i = 0, ii = layers.length; i < ii; ++i) {\n      setLayerMapProperty(layers[i], map);\n    }\n  }\n}\n\n/**\n * @classdesc\n * The map is the core component of OpenLayers. For a map to render, a view,\n * one or more layers, and a target container are needed:\n *\n *     import Map from 'ol/Map.js';\n *     import View from 'ol/View.js';\n *     import TileLayer from 'ol/layer/Tile.js';\n *     import OSM from 'ol/source/OSM.js';\n *\n *     const map = new Map({\n *       view: new View({\n *         center: [0, 0],\n *         zoom: 1,\n *       }),\n *       layers: [\n *         new TileLayer({\n *           source: new OSM(),\n *         }),\n *       ],\n *       target: 'map',\n *     });\n *\n * The above snippet creates a map using a {@link module:ol/layer/Tile~TileLayer} to\n * display {@link module:ol/source/OSM~OSM} OSM data and render it to a DOM\n * element with the id `map`.\n *\n * The constructor places a viewport container (with CSS class name\n * `ol-viewport`) in the target element (see `getViewport()`), and then two\n * further elements within the viewport: one with CSS class name\n * `ol-overlaycontainer-stopevent` for controls and some overlays, and one with\n * CSS class name `ol-overlaycontainer` for other overlays (see the `stopEvent`\n * option of {@link module:ol/Overlay~Overlay} for the difference). The map\n * itself is placed in a further element within the viewport.\n *\n * Layers are stored as a {@link module:ol/Collection~Collection} in\n * layerGroups. A top-level group is provided by the library. This is what is\n * accessed by `getLayerGroup` and `setLayerGroup`. Layers entered in the\n * options are added to this group, and `addLayer` and `removeLayer` change the\n * layer collection in the group. `getLayers` is a convenience function for\n * `getLayerGroup().getLayers()`. Note that {@link module:ol/layer/Group~LayerGroup}\n * is a subclass of {@link module:ol/layer/Base~BaseLayer}, so layers entered in the\n * options or added with `addLayer` can be groups, which can contain further\n * groups, and so on.\n *\n * @fires import(\"./MapBrowserEvent.js\").MapBrowserEvent\n * @fires import(\"./MapEvent.js\").MapEvent\n * @fires import(\"./render/Event.js\").default#precompose\n * @fires import(\"./render/Event.js\").default#postcompose\n * @fires import(\"./render/Event.js\").default#rendercomplete\n * @api\n */\nclass Map extends BaseObject {\n  /**\n   * @param {MapOptions} [options] Map options.\n   */\n  constructor(options) {\n    super();\n\n    options = options || {};\n\n    /***\n     * @type {MapEventHandler<import(\"./events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {MapEventHandler<import(\"./events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {MapEventHandler<void>}\n     */\n    this.un;\n\n    const optionsInternal = createOptionsInternal(options);\n\n    /**\n     * @private\n     * @type {boolean|undefined}\n     */\n    this.renderComplete_;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.loaded_ = true;\n\n    /** @private */\n    this.boundHandleBrowserEvent_ = this.handleBrowserEvent.bind(this);\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.maxTilesLoading_ =\n      options.maxTilesLoading !== undefined ? options.maxTilesLoading : 16;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.pixelRatio_ =\n      options.pixelRatio !== undefined\n        ? options.pixelRatio\n        : DEVICE_PIXEL_RATIO;\n\n    /**\n     * @private\n     * @type {ReturnType<typeof setTimeout>}\n     */\n    this.postRenderTimeoutHandle_;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.animationDelayKey_;\n\n    /**\n     * @private\n     */\n    this.animationDelay_ = this.animationDelay_.bind(this);\n\n    /**\n     * @private\n     * @type {import(\"./transform.js\").Transform}\n     */\n    this.coordinateToPixelTransform_ = createTransform();\n\n    /**\n     * @private\n     * @type {import(\"./transform.js\").Transform}\n     */\n    this.pixelToCoordinateTransform_ = createTransform();\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.frameIndex_ = 0;\n\n    /**\n     * @private\n     * @type {?FrameState}\n     */\n    this.frameState_ = null;\n\n    /**\n     * The extent at the previous 'moveend' event.\n     * @private\n     * @type {import(\"./extent.js\").Extent}\n     */\n    this.previousExtent_ = null;\n\n    /**\n     * @private\n     * @type {?import(\"./events.js\").EventsKey}\n     */\n    this.viewPropertyListenerKey_ = null;\n\n    /**\n     * @private\n     * @type {?import(\"./events.js\").EventsKey}\n     */\n    this.viewChangeListenerKey_ = null;\n\n    /**\n     * @private\n     * @type {?Array<import(\"./events.js\").EventsKey>}\n     */\n    this.layerGroupPropertyListenerKeys_ = null;\n\n    /**\n     * @private\n     * @type {!HTMLElement}\n     */\n    this.viewport_ = document.createElement('div');\n    this.viewport_.className =\n      'ol-viewport' + ('ontouchstart' in window ? ' ol-touch' : '');\n    this.viewport_.style.position = 'relative';\n    this.viewport_.style.overflow = 'hidden';\n    this.viewport_.style.width = '100%';\n    this.viewport_.style.height = '100%';\n\n    /**\n     * @private\n     * @type {!HTMLElement}\n     */\n    this.overlayContainer_ = document.createElement('div');\n    this.overlayContainer_.style.position = 'absolute';\n    this.overlayContainer_.style.zIndex = '0';\n    this.overlayContainer_.style.width = '100%';\n    this.overlayContainer_.style.height = '100%';\n    this.overlayContainer_.style.pointerEvents = 'none';\n    this.overlayContainer_.className = 'ol-overlaycontainer';\n    this.viewport_.appendChild(this.overlayContainer_);\n\n    /**\n     * @private\n     * @type {!HTMLElement}\n     */\n    this.overlayContainerStopEvent_ = document.createElement('div');\n    this.overlayContainerStopEvent_.style.position = 'absolute';\n    this.overlayContainerStopEvent_.style.zIndex = '0';\n    this.overlayContainerStopEvent_.style.width = '100%';\n    this.overlayContainerStopEvent_.style.height = '100%';\n    this.overlayContainerStopEvent_.style.pointerEvents = 'none';\n    this.overlayContainerStopEvent_.className = 'ol-overlaycontainer-stopevent';\n    this.viewport_.appendChild(this.overlayContainerStopEvent_);\n\n    /**\n     * @private\n     * @type {MapBrowserEventHandler}\n     */\n    this.mapBrowserEventHandler_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.moveTolerance_ = options.moveTolerance;\n\n    /**\n     * @private\n     * @type {HTMLElement|Document}\n     */\n    this.keyboardEventTarget_ = optionsInternal.keyboardEventTarget;\n\n    /**\n     * @private\n     * @type {?Array<import(\"./events.js\").EventsKey>}\n     */\n    this.targetChangeHandlerKeys_ = null;\n\n    /**\n     * @private\n     * @type {HTMLElement|null}\n     */\n    this.targetElement_ = null;\n\n    /**\n     * @type {ResizeObserver}\n     */\n    this.resizeObserver_ = new ResizeObserver(() => this.updateSize());\n\n    /**\n     * @type {Collection<import(\"./control/Control.js\").default>}\n     * @protected\n     */\n    this.controls = optionsInternal.controls || defaultControls();\n\n    /**\n     * @type {Collection<import(\"./interaction/Interaction.js\").default>}\n     * @protected\n     */\n    this.interactions =\n      optionsInternal.interactions ||\n      defaultInteractions({\n        onFocusOnly: true,\n      });\n\n    /**\n     * @type {Collection<import(\"./Overlay.js\").default>}\n     * @private\n     */\n    this.overlays_ = optionsInternal.overlays;\n\n    /**\n     * A lookup of overlays by id.\n     * @private\n     * @type {Object<string, import(\"./Overlay.js\").default>}\n     */\n    this.overlayIdIndex_ = {};\n\n    /**\n     * @type {import(\"./renderer/Map.js\").default|null}\n     * @private\n     */\n    this.renderer_ = null;\n\n    /**\n     * @private\n     * @type {!Array<PostRenderFunction>}\n     */\n    this.postRenderFunctions_ = [];\n\n    /**\n     * @private\n     * @type {TileQueue}\n     */\n    this.tileQueue_ = new TileQueue(\n      this.getTilePriority.bind(this),\n      this.handleTileChange_.bind(this)\n    );\n\n    this.addChangeListener(\n      MapProperty.LAYERGROUP,\n      this.handleLayerGroupChanged_\n    );\n    this.addChangeListener(MapProperty.VIEW, this.handleViewChanged_);\n    this.addChangeListener(MapProperty.SIZE, this.handleSizeChanged_);\n    this.addChangeListener(MapProperty.TARGET, this.handleTargetChanged_);\n\n    // setProperties will trigger the rendering of the map if the map\n    // is \"defined\" already.\n    this.setProperties(optionsInternal.values);\n\n    const map = this;\n    if (options.view && !(options.view instanceof View)) {\n      options.view.then(function (viewOptions) {\n        map.setView(new View(viewOptions));\n      });\n    }\n\n    this.controls.addEventListener(\n      CollectionEventType.ADD,\n      /**\n       * @param {import(\"./Collection.js\").CollectionEvent<import(\"./control/Control.js\").default>} event CollectionEvent\n       */\n      (event) => {\n        event.element.setMap(this);\n      }\n    );\n\n    this.controls.addEventListener(\n      CollectionEventType.REMOVE,\n      /**\n       * @param {import(\"./Collection.js\").CollectionEvent<import(\"./control/Control.js\").default>} event CollectionEvent.\n       */\n      (event) => {\n        event.element.setMap(null);\n      }\n    );\n\n    this.interactions.addEventListener(\n      CollectionEventType.ADD,\n      /**\n       * @param {import(\"./Collection.js\").CollectionEvent<import(\"./interaction/Interaction.js\").default>} event CollectionEvent.\n       */\n      (event) => {\n        event.element.setMap(this);\n      }\n    );\n\n    this.interactions.addEventListener(\n      CollectionEventType.REMOVE,\n      /**\n       * @param {import(\"./Collection.js\").CollectionEvent<import(\"./interaction/Interaction.js\").default>} event CollectionEvent.\n       */\n      (event) => {\n        event.element.setMap(null);\n      }\n    );\n\n    this.overlays_.addEventListener(\n      CollectionEventType.ADD,\n      /**\n       * @param {import(\"./Collection.js\").CollectionEvent<import(\"./Overlay.js\").default>} event CollectionEvent.\n       */\n      (event) => {\n        this.addOverlayInternal_(event.element);\n      }\n    );\n\n    this.overlays_.addEventListener(\n      CollectionEventType.REMOVE,\n      /**\n       * @param {import(\"./Collection.js\").CollectionEvent<import(\"./Overlay.js\").default>} event CollectionEvent.\n       */\n      (event) => {\n        const id = event.element.getId();\n        if (id !== undefined) {\n          delete this.overlayIdIndex_[id.toString()];\n        }\n        event.element.setMap(null);\n      }\n    );\n\n    this.controls.forEach(\n      /**\n       * @param {import(\"./control/Control.js\").default} control Control.\n       */\n      (control) => {\n        control.setMap(this);\n      }\n    );\n\n    this.interactions.forEach(\n      /**\n       * @param {import(\"./interaction/Interaction.js\").default} interaction Interaction.\n       */\n      (interaction) => {\n        interaction.setMap(this);\n      }\n    );\n\n    this.overlays_.forEach(this.addOverlayInternal_.bind(this));\n  }\n\n  /**\n   * Add the given control to the map.\n   * @param {import(\"./control/Control.js\").default} control Control.\n   * @api\n   */\n  addControl(control) {\n    this.getControls().push(control);\n  }\n\n  /**\n   * Add the given interaction to the map. If you want to add an interaction\n   * at another point of the collection use `getInteractions()` and the methods\n   * available on {@link module:ol/Collection~Collection}. This can be used to\n   * stop the event propagation from the handleEvent function. The interactions\n   * get to handle the events in the reverse order of this collection.\n   * @param {import(\"./interaction/Interaction.js\").default} interaction Interaction to add.\n   * @api\n   */\n  addInteraction(interaction) {\n    this.getInteractions().push(interaction);\n  }\n\n  /**\n   * Adds the given layer to the top of this map. If you want to add a layer\n   * elsewhere in the stack, use `getLayers()` and the methods available on\n   * {@link module:ol/Collection~Collection}.\n   * @param {import(\"./layer/Base.js\").default} layer Layer.\n   * @api\n   */\n  addLayer(layer) {\n    const layers = this.getLayerGroup().getLayers();\n    layers.push(layer);\n  }\n\n  /**\n   * @param {import(\"./layer/Group.js\").GroupEvent} event The layer add event.\n   * @private\n   */\n  handleLayerAdd_(event) {\n    setLayerMapProperty(event.layer, this);\n  }\n\n  /**\n   * Add the given overlay to the map.\n   * @param {import(\"./Overlay.js\").default} overlay Overlay.\n   * @api\n   */\n  addOverlay(overlay) {\n    this.getOverlays().push(overlay);\n  }\n\n  /**\n   * This deals with map's overlay collection changes.\n   * @param {import(\"./Overlay.js\").default} overlay Overlay.\n   * @private\n   */\n  addOverlayInternal_(overlay) {\n    const id = overlay.getId();\n    if (id !== undefined) {\n      this.overlayIdIndex_[id.toString()] = overlay;\n    }\n    overlay.setMap(this);\n  }\n\n  /**\n   *\n   * Clean up.\n   */\n  disposeInternal() {\n    this.controls.clear();\n    this.interactions.clear();\n    this.overlays_.clear();\n    this.resizeObserver_.disconnect();\n    this.setTarget(null);\n    super.disposeInternal();\n  }\n\n  /**\n   * Detect features that intersect a pixel on the viewport, and execute a\n   * callback with each intersecting feature. Layers included in the detection can\n   * be configured through the `layerFilter` option in `options`.\n   * @param {import(\"./pixel.js\").Pixel} pixel Pixel.\n   * @param {function(import(\"./Feature.js\").FeatureLike, import(\"./layer/Layer.js\").default<import(\"./source/Source\").default>, import(\"./geom/SimpleGeometry.js\").default): T} callback Feature callback. The callback will be\n   *     called with two arguments. The first argument is one\n   *     {@link module:ol/Feature~Feature feature} or\n   *     {@link module:ol/render/Feature~RenderFeature render feature} at the pixel, the second is\n   *     the {@link module:ol/layer/Layer~Layer layer} of the feature and will be null for\n   *     unmanaged layers. To stop detection, callback functions can return a\n   *     truthy value.\n   * @param {AtPixelOptions} [options] Optional options.\n   * @return {T|undefined} Callback result, i.e. the return value of last\n   * callback execution, or the first truthy callback return value.\n   * @template T\n   * @api\n   */\n  forEachFeatureAtPixel(pixel, callback, options) {\n    if (!this.frameState_ || !this.renderer_) {\n      return;\n    }\n    const coordinate = this.getCoordinateFromPixelInternal(pixel);\n    options = options !== undefined ? options : {};\n    const hitTolerance =\n      options.hitTolerance !== undefined ? options.hitTolerance : 0;\n    const layerFilter =\n      options.layerFilter !== undefined ? options.layerFilter : TRUE;\n    const checkWrapped = options.checkWrapped !== false;\n    return this.renderer_.forEachFeatureAtCoordinate(\n      coordinate,\n      this.frameState_,\n      hitTolerance,\n      checkWrapped,\n      callback,\n      null,\n      layerFilter,\n      null\n    );\n  }\n\n  /**\n   * Get all features that intersect a pixel on the viewport.\n   * @param {import(\"./pixel.js\").Pixel} pixel Pixel.\n   * @param {AtPixelOptions} [options] Optional options.\n   * @return {Array<import(\"./Feature.js\").FeatureLike>} The detected features or\n   * an empty array if none were found.\n   * @api\n   */\n  getFeaturesAtPixel(pixel, options) {\n    const features = [];\n    this.forEachFeatureAtPixel(\n      pixel,\n      function (feature) {\n        features.push(feature);\n      },\n      options\n    );\n    return features;\n  }\n\n  /**\n   * Get all layers from all layer groups.\n   * @return {Array<import(\"./layer/Layer.js\").default>} Layers.\n   * @api\n   */\n  getAllLayers() {\n    const layers = [];\n    function addLayersFrom(layerGroup) {\n      layerGroup.forEach(function (layer) {\n        if (layer instanceof LayerGroup) {\n          addLayersFrom(layer.getLayers());\n        } else {\n          layers.push(layer);\n        }\n      });\n    }\n    addLayersFrom(this.getLayers());\n    return layers;\n  }\n\n  /**\n   * Detect if features intersect a pixel on the viewport. Layers included in the\n   * detection can be configured through the `layerFilter` option.\n   * @param {import(\"./pixel.js\").Pixel} pixel Pixel.\n   * @param {AtPixelOptions} [options] Optional options.\n   * @return {boolean} Is there a feature at the given pixel?\n   * @api\n   */\n  hasFeatureAtPixel(pixel, options) {\n    if (!this.frameState_ || !this.renderer_) {\n      return false;\n    }\n    const coordinate = this.getCoordinateFromPixelInternal(pixel);\n    options = options !== undefined ? options : {};\n    const layerFilter =\n      options.layerFilter !== undefined ? options.layerFilter : TRUE;\n    const hitTolerance =\n      options.hitTolerance !== undefined ? options.hitTolerance : 0;\n    const checkWrapped = options.checkWrapped !== false;\n    return this.renderer_.hasFeatureAtCoordinate(\n      coordinate,\n      this.frameState_,\n      hitTolerance,\n      checkWrapped,\n      layerFilter,\n      null\n    );\n  }\n\n  /**\n   * Returns the coordinate in user projection for a browser event.\n   * @param {MouseEvent} event Event.\n   * @return {import(\"./coordinate.js\").Coordinate} Coordinate.\n   * @api\n   */\n  getEventCoordinate(event) {\n    return this.getCoordinateFromPixel(this.getEventPixel(event));\n  }\n\n  /**\n   * Returns the coordinate in view projection for a browser event.\n   * @param {MouseEvent} event Event.\n   * @return {import(\"./coordinate.js\").Coordinate} Coordinate.\n   */\n  getEventCoordinateInternal(event) {\n    return this.getCoordinateFromPixelInternal(this.getEventPixel(event));\n  }\n\n  /**\n   * Returns the map pixel position for a browser event relative to the viewport.\n   * @param {UIEvent|{clientX: number, clientY: number}} event Event.\n   * @return {import(\"./pixel.js\").Pixel} Pixel.\n   * @api\n   */\n  getEventPixel(event) {\n    const viewport = this.viewport_;\n    const viewportPosition = viewport.getBoundingClientRect();\n    const viewportSize = this.getSize();\n    const scaleX = viewportPosition.width / viewportSize[0];\n    const scaleY = viewportPosition.height / viewportSize[1];\n    const eventPosition =\n      //FIXME Are we really calling this with a TouchEvent anywhere?\n      'changedTouches' in event\n        ? /** @type {TouchEvent} */ (event).changedTouches[0]\n        : /** @type {MouseEvent} */ (event);\n\n    return [\n      (eventPosition.clientX - viewportPosition.left) / scaleX,\n      (eventPosition.clientY - viewportPosition.top) / scaleY,\n    ];\n  }\n\n  /**\n   * Get the target in which this map is rendered.\n   * Note that this returns what is entered as an option or in setTarget:\n   * if that was an element, it returns an element; if a string, it returns that.\n   * @return {HTMLElement|string|undefined} The Element or id of the Element that the\n   *     map is rendered in.\n   * @observable\n   * @api\n   */\n  getTarget() {\n    return /** @type {HTMLElement|string|undefined} */ (\n      this.get(MapProperty.TARGET)\n    );\n  }\n\n  /**\n   * Get the DOM element into which this map is rendered. In contrast to\n   * `getTarget` this method always return an `Element`, or `null` if the\n   * map has no target.\n   * @return {HTMLElement} The element that the map is rendered in.\n   * @api\n   */\n  getTargetElement() {\n    return this.targetElement_;\n  }\n\n  /**\n   * Get the coordinate for a given pixel.  This returns a coordinate in the\n   * user projection.\n   * @param {import(\"./pixel.js\").Pixel} pixel Pixel position in the map viewport.\n   * @return {import(\"./coordinate.js\").Coordinate} The coordinate for the pixel position.\n   * @api\n   */\n  getCoordinateFromPixel(pixel) {\n    return toUserCoordinate(\n      this.getCoordinateFromPixelInternal(pixel),\n      this.getView().getProjection()\n    );\n  }\n\n  /**\n   * Get the coordinate for a given pixel.  This returns a coordinate in the\n   * map view projection.\n   * @param {import(\"./pixel.js\").Pixel} pixel Pixel position in the map viewport.\n   * @return {import(\"./coordinate.js\").Coordinate} The coordinate for the pixel position.\n   */\n  getCoordinateFromPixelInternal(pixel) {\n    const frameState = this.frameState_;\n    if (!frameState) {\n      return null;\n    }\n    return applyTransform(frameState.pixelToCoordinateTransform, pixel.slice());\n  }\n\n  /**\n   * Get the map controls. Modifying this collection changes the controls\n   * associated with the map.\n   * @return {Collection<import(\"./control/Control.js\").default>} Controls.\n   * @api\n   */\n  getControls() {\n    return this.controls;\n  }\n\n  /**\n   * Get the map overlays. Modifying this collection changes the overlays\n   * associated with the map.\n   * @return {Collection<import(\"./Overlay.js\").default>} Overlays.\n   * @api\n   */\n  getOverlays() {\n    return this.overlays_;\n  }\n\n  /**\n   * Get an overlay by its identifier (the value returned by overlay.getId()).\n   * Note that the index treats string and numeric identifiers as the same. So\n   * `map.getOverlayById(2)` will return an overlay with id `'2'` or `2`.\n   * @param {string|number} id Overlay identifier.\n   * @return {import(\"./Overlay.js\").default} Overlay.\n   * @api\n   */\n  getOverlayById(id) {\n    const overlay = this.overlayIdIndex_[id.toString()];\n    return overlay !== undefined ? overlay : null;\n  }\n\n  /**\n   * Get the map interactions. Modifying this collection changes the interactions\n   * associated with the map.\n   *\n   * Interactions are used for e.g. pan, zoom and rotate.\n   * @return {Collection<import(\"./interaction/Interaction.js\").default>} Interactions.\n   * @api\n   */\n  getInteractions() {\n    return this.interactions;\n  }\n\n  /**\n   * Get the layergroup associated with this map.\n   * @return {LayerGroup} A layer group containing the layers in this map.\n   * @observable\n   * @api\n   */\n  getLayerGroup() {\n    return /** @type {LayerGroup} */ (this.get(MapProperty.LAYERGROUP));\n  }\n\n  /**\n   * Clear any existing layers and add layers to the map.\n   * @param {Array<import(\"./layer/Base.js\").default>|Collection<import(\"./layer/Base.js\").default>} layers The layers to be added to the map.\n   * @api\n   */\n  setLayers(layers) {\n    const group = this.getLayerGroup();\n    if (layers instanceof Collection) {\n      group.setLayers(layers);\n      return;\n    }\n\n    const collection = group.getLayers();\n    collection.clear();\n    collection.extend(layers);\n  }\n\n  /**\n   * Get the collection of layers associated with this map.\n   * @return {!Collection<import(\"./layer/Base.js\").default>} Layers.\n   * @api\n   */\n  getLayers() {\n    const layers = this.getLayerGroup().getLayers();\n    return layers;\n  }\n\n  /**\n   * @return {boolean} Layers have sources that are still loading.\n   */\n  getLoadingOrNotReady() {\n    const layerStatesArray = this.getLayerGroup().getLayerStatesArray();\n    for (let i = 0, ii = layerStatesArray.length; i < ii; ++i) {\n      const state = layerStatesArray[i];\n      if (!state.visible) {\n        continue;\n      }\n      const renderer = state.layer.getRenderer();\n      if (renderer && !renderer.ready) {\n        return true;\n      }\n      const source = state.layer.getSource();\n      if (source && source.loading) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * Get the pixel for a coordinate.  This takes a coordinate in the user\n   * projection and returns the corresponding pixel.\n   * @param {import(\"./coordinate.js\").Coordinate} coordinate A map coordinate.\n   * @return {import(\"./pixel.js\").Pixel} A pixel position in the map viewport.\n   * @api\n   */\n  getPixelFromCoordinate(coordinate) {\n    const viewCoordinate = fromUserCoordinate(\n      coordinate,\n      this.getView().getProjection()\n    );\n    return this.getPixelFromCoordinateInternal(viewCoordinate);\n  }\n\n  /**\n   * Get the pixel for a coordinate.  This takes a coordinate in the map view\n   * projection and returns the corresponding pixel.\n   * @param {import(\"./coordinate.js\").Coordinate} coordinate A map coordinate.\n   * @return {import(\"./pixel.js\").Pixel} A pixel position in the map viewport.\n   */\n  getPixelFromCoordinateInternal(coordinate) {\n    const frameState = this.frameState_;\n    if (!frameState) {\n      return null;\n    }\n    return applyTransform(\n      frameState.coordinateToPixelTransform,\n      coordinate.slice(0, 2)\n    );\n  }\n\n  /**\n   * Get the map renderer.\n   * @return {import(\"./renderer/Map.js\").default|null} Renderer\n   */\n  getRenderer() {\n    return this.renderer_;\n  }\n\n  /**\n   * Get the size of this map.\n   * @return {import(\"./size.js\").Size|undefined} The size in pixels of the map in the DOM.\n   * @observable\n   * @api\n   */\n  getSize() {\n    return /** @type {import(\"./size.js\").Size|undefined} */ (\n      this.get(MapProperty.SIZE)\n    );\n  }\n\n  /**\n   * Get the view associated with this map. A view manages properties such as\n   * center and resolution.\n   * @return {View} The view that controls this map.\n   * @observable\n   * @api\n   */\n  getView() {\n    return /** @type {View} */ (this.get(MapProperty.VIEW));\n  }\n\n  /**\n   * Get the element that serves as the map viewport.\n   * @return {HTMLElement} Viewport.\n   * @api\n   */\n  getViewport() {\n    return this.viewport_;\n  }\n\n  /**\n   * Get the element that serves as the container for overlays.  Elements added to\n   * this container will let mousedown and touchstart events through to the map,\n   * so clicks and gestures on an overlay will trigger {@link module:ol/MapBrowserEvent~MapBrowserEvent}\n   * events.\n   * @return {!HTMLElement} The map's overlay container.\n   */\n  getOverlayContainer() {\n    return this.overlayContainer_;\n  }\n\n  /**\n   * Get the element that serves as a container for overlays that don't allow\n   * event propagation. Elements added to this container won't let mousedown and\n   * touchstart events through to the map, so clicks and gestures on an overlay\n   * don't trigger any {@link module:ol/MapBrowserEvent~MapBrowserEvent}.\n   * @return {!HTMLElement} The map's overlay container that stops events.\n   */\n  getOverlayContainerStopEvent() {\n    return this.overlayContainerStopEvent_;\n  }\n\n  /**\n   * @return {!Document} The document where the map is displayed.\n   */\n  getOwnerDocument() {\n    const targetElement = this.getTargetElement();\n    return targetElement ? targetElement.ownerDocument : document;\n  }\n\n  /**\n   * @param {import(\"./Tile.js\").default} tile Tile.\n   * @param {string} tileSourceKey Tile source key.\n   * @param {import(\"./coordinate.js\").Coordinate} tileCenter Tile center.\n   * @param {number} tileResolution Tile resolution.\n   * @return {number} Tile priority.\n   */\n  getTilePriority(tile, tileSourceKey, tileCenter, tileResolution) {\n    return getTilePriority(\n      this.frameState_,\n      tile,\n      tileSourceKey,\n      tileCenter,\n      tileResolution\n    );\n  }\n\n  /**\n   * @param {UIEvent} browserEvent Browser event.\n   * @param {string} [type] Type.\n   */\n  handleBrowserEvent(browserEvent, type) {\n    type = type || browserEvent.type;\n    const mapBrowserEvent = new MapBrowserEvent(type, this, browserEvent);\n    this.handleMapBrowserEvent(mapBrowserEvent);\n  }\n\n  /**\n   * @param {MapBrowserEvent} mapBrowserEvent The event to handle.\n   */\n  handleMapBrowserEvent(mapBrowserEvent) {\n    if (!this.frameState_) {\n      // With no view defined, we cannot translate pixels into geographical\n      // coordinates so interactions cannot be used.\n      return;\n    }\n    const originalEvent = /** @type {PointerEvent} */ (\n      mapBrowserEvent.originalEvent\n    );\n    const eventType = originalEvent.type;\n    if (\n      eventType === PointerEventType.POINTERDOWN ||\n      eventType === EventType.WHEEL ||\n      eventType === EventType.KEYDOWN\n    ) {\n      const doc = this.getOwnerDocument();\n      const rootNode = this.viewport_.getRootNode\n        ? this.viewport_.getRootNode()\n        : doc;\n      const target = /** @type {Node} */ (originalEvent.target);\n      if (\n        // Abort if the target is a child of the container for elements whose events are not meant\n        // to be handled by map interactions.\n        this.overlayContainerStopEvent_.contains(target) ||\n        // Abort if the event target is a child of the container that is no longer in the page.\n        // It's possible for the target to no longer be in the page if it has been removed in an\n        // event listener, this might happen in a Control that recreates it's content based on\n        // user interaction either manually or via a render in something like https://reactjs.org/\n        !(rootNode === doc ? doc.documentElement : rootNode).contains(target)\n      ) {\n        return;\n      }\n    }\n    mapBrowserEvent.frameState = this.frameState_;\n    if (this.dispatchEvent(mapBrowserEvent) !== false) {\n      const interactionsArray = this.getInteractions().getArray().slice();\n      for (let i = interactionsArray.length - 1; i >= 0; i--) {\n        const interaction = interactionsArray[i];\n        if (\n          interaction.getMap() !== this ||\n          !interaction.getActive() ||\n          !this.getTargetElement()\n        ) {\n          continue;\n        }\n        const cont = interaction.handleEvent(mapBrowserEvent);\n        if (!cont || mapBrowserEvent.propagationStopped) {\n          break;\n        }\n      }\n    }\n  }\n\n  /**\n   * @protected\n   */\n  handlePostRender() {\n    const frameState = this.frameState_;\n\n    // Manage the tile queue\n    // Image loads are expensive and a limited resource, so try to use them\n    // efficiently:\n    // * When the view is static we allow a large number of parallel tile loads\n    //   to complete the frame as quickly as possible.\n    // * When animating or interacting, image loads can cause janks, so we reduce\n    //   the maximum number of loads per frame and limit the number of parallel\n    //   tile loads to remain reactive to view changes and to reduce the chance of\n    //   loading tiles that will quickly disappear from view.\n    const tileQueue = this.tileQueue_;\n    if (!tileQueue.isEmpty()) {\n      let maxTotalLoading = this.maxTilesLoading_;\n      let maxNewLoads = maxTotalLoading;\n      if (frameState) {\n        const hints = frameState.viewHints;\n        if (hints[ViewHint.ANIMATING] || hints[ViewHint.INTERACTING]) {\n          const lowOnFrameBudget = Date.now() - frameState.time > 8;\n          maxTotalLoading = lowOnFrameBudget ? 0 : 8;\n          maxNewLoads = lowOnFrameBudget ? 0 : 2;\n        }\n      }\n      if (tileQueue.getTilesLoading() < maxTotalLoading) {\n        tileQueue.reprioritize(); // FIXME only call if view has changed\n        tileQueue.loadMoreTiles(maxTotalLoading, maxNewLoads);\n      }\n    }\n\n    if (frameState && this.renderer_ && !frameState.animate) {\n      if (this.renderComplete_ === true) {\n        if (this.hasListener(RenderEventType.RENDERCOMPLETE)) {\n          this.renderer_.dispatchRenderEvent(\n            RenderEventType.RENDERCOMPLETE,\n            frameState\n          );\n        }\n        if (this.loaded_ === false) {\n          this.loaded_ = true;\n          this.dispatchEvent(\n            new MapEvent(MapEventType.LOADEND, this, frameState)\n          );\n        }\n      } else if (this.loaded_ === true) {\n        this.loaded_ = false;\n        this.dispatchEvent(\n          new MapEvent(MapEventType.LOADSTART, this, frameState)\n        );\n      }\n    }\n\n    const postRenderFunctions = this.postRenderFunctions_;\n    for (let i = 0, ii = postRenderFunctions.length; i < ii; ++i) {\n      postRenderFunctions[i](this, frameState);\n    }\n    postRenderFunctions.length = 0;\n  }\n\n  /**\n   * @private\n   */\n  handleSizeChanged_() {\n    if (this.getView() && !this.getView().getAnimating()) {\n      this.getView().resolveConstraints(0);\n    }\n\n    this.render();\n  }\n\n  /**\n   * @private\n   */\n  handleTargetChanged_() {\n    if (this.mapBrowserEventHandler_) {\n      for (let i = 0, ii = this.targetChangeHandlerKeys_.length; i < ii; ++i) {\n        unlistenByKey(this.targetChangeHandlerKeys_[i]);\n      }\n      this.targetChangeHandlerKeys_ = null;\n      this.viewport_.removeEventListener(\n        EventType.CONTEXTMENU,\n        this.boundHandleBrowserEvent_\n      );\n      this.viewport_.removeEventListener(\n        EventType.WHEEL,\n        this.boundHandleBrowserEvent_\n      );\n      this.mapBrowserEventHandler_.dispose();\n      this.mapBrowserEventHandler_ = null;\n      removeNode(this.viewport_);\n    }\n\n    if (this.targetElement_) {\n      this.resizeObserver_.unobserve(this.targetElement_);\n      const rootNode = this.targetElement_.getRootNode();\n      if (rootNode instanceof ShadowRoot) {\n        this.resizeObserver_.unobserve(rootNode.host);\n      }\n      this.setSize(undefined);\n    }\n\n    // target may be undefined, null, a string or an Element.\n    // If it's a string we convert it to an Element before proceeding.\n    // If it's not now an Element we remove the viewport from the DOM.\n    // If it's an Element we append the viewport element to it.\n\n    const target = this.getTarget();\n    const targetElement =\n      typeof target === 'string' ? document.getElementById(target) : target;\n    this.targetElement_ = targetElement;\n    if (!targetElement) {\n      if (this.renderer_) {\n        clearTimeout(this.postRenderTimeoutHandle_);\n        this.postRenderTimeoutHandle_ = undefined;\n        this.postRenderFunctions_.length = 0;\n        this.renderer_.dispose();\n        this.renderer_ = null;\n      }\n      if (this.animationDelayKey_) {\n        cancelAnimationFrame(this.animationDelayKey_);\n        this.animationDelayKey_ = undefined;\n      }\n    } else {\n      targetElement.appendChild(this.viewport_);\n      if (!this.renderer_) {\n        this.renderer_ = new CompositeMapRenderer(this);\n      }\n\n      this.mapBrowserEventHandler_ = new MapBrowserEventHandler(\n        this,\n        this.moveTolerance_\n      );\n      for (const key in MapBrowserEventType) {\n        this.mapBrowserEventHandler_.addEventListener(\n          MapBrowserEventType[key],\n          this.handleMapBrowserEvent.bind(this)\n        );\n      }\n      this.viewport_.addEventListener(\n        EventType.CONTEXTMENU,\n        this.boundHandleBrowserEvent_,\n        false\n      );\n      this.viewport_.addEventListener(\n        EventType.WHEEL,\n        this.boundHandleBrowserEvent_,\n        PASSIVE_EVENT_LISTENERS ? {passive: false} : false\n      );\n\n      const keyboardEventTarget = !this.keyboardEventTarget_\n        ? targetElement\n        : this.keyboardEventTarget_;\n      this.targetChangeHandlerKeys_ = [\n        listen(\n          keyboardEventTarget,\n          EventType.KEYDOWN,\n          this.handleBrowserEvent,\n          this\n        ),\n        listen(\n          keyboardEventTarget,\n          EventType.KEYPRESS,\n          this.handleBrowserEvent,\n          this\n        ),\n      ];\n      const rootNode = targetElement.getRootNode();\n      if (rootNode instanceof ShadowRoot) {\n        this.resizeObserver_.observe(rootNode.host);\n      }\n      this.resizeObserver_.observe(targetElement);\n    }\n\n    this.updateSize();\n    // updateSize calls setSize, so no need to call this.render\n    // ourselves here.\n  }\n\n  /**\n   * @private\n   */\n  handleTileChange_() {\n    this.render();\n  }\n\n  /**\n   * @private\n   */\n  handleViewPropertyChanged_() {\n    this.render();\n  }\n\n  /**\n   * @private\n   */\n  handleViewChanged_() {\n    if (this.viewPropertyListenerKey_) {\n      unlistenByKey(this.viewPropertyListenerKey_);\n      this.viewPropertyListenerKey_ = null;\n    }\n    if (this.viewChangeListenerKey_) {\n      unlistenByKey(this.viewChangeListenerKey_);\n      this.viewChangeListenerKey_ = null;\n    }\n    const view = this.getView();\n    if (view) {\n      this.updateViewportSize_();\n\n      this.viewPropertyListenerKey_ = listen(\n        view,\n        ObjectEventType.PROPERTYCHANGE,\n        this.handleViewPropertyChanged_,\n        this\n      );\n      this.viewChangeListenerKey_ = listen(\n        view,\n        EventType.CHANGE,\n        this.handleViewPropertyChanged_,\n        this\n      );\n\n      view.resolveConstraints(0);\n    }\n    this.render();\n  }\n\n  /**\n   * @private\n   */\n  handleLayerGroupChanged_() {\n    if (this.layerGroupPropertyListenerKeys_) {\n      this.layerGroupPropertyListenerKeys_.forEach(unlistenByKey);\n      this.layerGroupPropertyListenerKeys_ = null;\n    }\n    const layerGroup = this.getLayerGroup();\n    if (layerGroup) {\n      this.handleLayerAdd_(new GroupEvent('addlayer', layerGroup));\n      this.layerGroupPropertyListenerKeys_ = [\n        listen(layerGroup, ObjectEventType.PROPERTYCHANGE, this.render, this),\n        listen(layerGroup, EventType.CHANGE, this.render, this),\n        listen(layerGroup, 'addlayer', this.handleLayerAdd_, this),\n        listen(layerGroup, 'removelayer', this.handleLayerRemove_, this),\n      ];\n    }\n    this.render();\n  }\n\n  /**\n   * @return {boolean} Is rendered.\n   */\n  isRendered() {\n    return !!this.frameState_;\n  }\n\n  /**\n   * @private\n   */\n  animationDelay_() {\n    this.animationDelayKey_ = undefined;\n    this.renderFrame_(Date.now());\n  }\n\n  /**\n   * Requests an immediate render in a synchronous manner.\n   * @api\n   */\n  renderSync() {\n    if (this.animationDelayKey_) {\n      cancelAnimationFrame(this.animationDelayKey_);\n    }\n    this.animationDelay_();\n  }\n\n  /**\n   * Redraws all text after new fonts have loaded\n   */\n  redrawText() {\n    const layerStates = this.getLayerGroup().getLayerStatesArray();\n    for (let i = 0, ii = layerStates.length; i < ii; ++i) {\n      const layer = layerStates[i].layer;\n      if (layer.hasRenderer()) {\n        layer.getRenderer().handleFontsChanged();\n      }\n    }\n  }\n\n  /**\n   * Request a map rendering (at the next animation frame).\n   * @api\n   */\n  render() {\n    if (this.renderer_ && this.animationDelayKey_ === undefined) {\n      this.animationDelayKey_ = requestAnimationFrame(this.animationDelay_);\n    }\n  }\n\n  /**\n   * This method is meant to be called in a layer's `prerender` listener. It causes all collected\n   * declutter items to be decluttered and rendered on the map immediately. This is useful for\n   * layers that need to appear entirely above the decluttered items of layers lower in the layer\n   * stack.\n   * @api\n   */\n  flushDeclutterItems() {\n    const frameState = this.frameState_;\n    if (!frameState) {\n      return;\n    }\n    this.renderer_.flushDeclutterItems(frameState);\n  }\n\n  /**\n   * Remove the given control from the map.\n   * @param {import(\"./control/Control.js\").default} control Control.\n   * @return {import(\"./control/Control.js\").default|undefined} The removed control (or undefined\n   *     if the control was not found).\n   * @api\n   */\n  removeControl(control) {\n    return this.getControls().remove(control);\n  }\n\n  /**\n   * Remove the given interaction from the map.\n   * @param {import(\"./interaction/Interaction.js\").default} interaction Interaction to remove.\n   * @return {import(\"./interaction/Interaction.js\").default|undefined} The removed interaction (or\n   *     undefined if the interaction was not found).\n   * @api\n   */\n  removeInteraction(interaction) {\n    return this.getInteractions().remove(interaction);\n  }\n\n  /**\n   * Removes the given layer from the map.\n   * @param {import(\"./layer/Base.js\").default} layer Layer.\n   * @return {import(\"./layer/Base.js\").default|undefined} The removed layer (or undefined if the\n   *     layer was not found).\n   * @api\n   */\n  removeLayer(layer) {\n    const layers = this.getLayerGroup().getLayers();\n    return layers.remove(layer);\n  }\n\n  /**\n   * @param {import(\"./layer/Group.js\").GroupEvent} event The layer remove event.\n   * @private\n   */\n  handleLayerRemove_(event) {\n    removeLayerMapProperty(event.layer);\n  }\n\n  /**\n   * Remove the given overlay from the map.\n   * @param {import(\"./Overlay.js\").default} overlay Overlay.\n   * @return {import(\"./Overlay.js\").default|undefined} The removed overlay (or undefined\n   *     if the overlay was not found).\n   * @api\n   */\n  removeOverlay(overlay) {\n    return this.getOverlays().remove(overlay);\n  }\n\n  /**\n   * @param {number} time Time.\n   * @private\n   */\n  renderFrame_(time) {\n    const size = this.getSize();\n    const view = this.getView();\n    const previousFrameState = this.frameState_;\n    /** @type {?FrameState} */\n    let frameState = null;\n    if (size !== undefined && hasArea(size) && view && view.isDef()) {\n      const viewHints = view.getHints(\n        this.frameState_ ? this.frameState_.viewHints : undefined\n      );\n      const viewState = view.getState();\n      frameState = {\n        animate: false,\n        coordinateToPixelTransform: this.coordinateToPixelTransform_,\n        declutterTree: null,\n        extent: getForViewAndSize(\n          viewState.center,\n          viewState.resolution,\n          viewState.rotation,\n          size\n        ),\n        index: this.frameIndex_++,\n        layerIndex: 0,\n        layerStatesArray: this.getLayerGroup().getLayerStatesArray(),\n        pixelRatio: this.pixelRatio_,\n        pixelToCoordinateTransform: this.pixelToCoordinateTransform_,\n        postRenderFunctions: [],\n        size: size,\n        tileQueue: this.tileQueue_,\n        time: time,\n        usedTiles: {},\n        viewState: viewState,\n        viewHints: viewHints,\n        wantedTiles: {},\n        mapId: getUid(this),\n        renderTargets: {},\n      };\n      if (viewState.nextCenter && viewState.nextResolution) {\n        const rotation = isNaN(viewState.nextRotation)\n          ? viewState.rotation\n          : viewState.nextRotation;\n\n        frameState.nextExtent = getForViewAndSize(\n          viewState.nextCenter,\n          viewState.nextResolution,\n          rotation,\n          size\n        );\n      }\n    }\n\n    this.frameState_ = frameState;\n    this.renderer_.renderFrame(frameState);\n\n    if (frameState) {\n      if (frameState.animate) {\n        this.render();\n      }\n      Array.prototype.push.apply(\n        this.postRenderFunctions_,\n        frameState.postRenderFunctions\n      );\n\n      if (previousFrameState) {\n        const moveStart =\n          !this.previousExtent_ ||\n          (!isEmpty(this.previousExtent_) &&\n            !equalsExtent(frameState.extent, this.previousExtent_));\n        if (moveStart) {\n          this.dispatchEvent(\n            new MapEvent(MapEventType.MOVESTART, this, previousFrameState)\n          );\n          this.previousExtent_ = createOrUpdateEmpty(this.previousExtent_);\n        }\n      }\n\n      const idle =\n        this.previousExtent_ &&\n        !frameState.viewHints[ViewHint.ANIMATING] &&\n        !frameState.viewHints[ViewHint.INTERACTING] &&\n        !equalsExtent(frameState.extent, this.previousExtent_);\n\n      if (idle) {\n        this.dispatchEvent(\n          new MapEvent(MapEventType.MOVEEND, this, frameState)\n        );\n        clone(frameState.extent, this.previousExtent_);\n      }\n    }\n\n    this.dispatchEvent(new MapEvent(MapEventType.POSTRENDER, this, frameState));\n\n    this.renderComplete_ =\n      this.hasListener(MapEventType.LOADSTART) ||\n      this.hasListener(MapEventType.LOADEND) ||\n      this.hasListener(RenderEventType.RENDERCOMPLETE)\n        ? !this.tileQueue_.getTilesLoading() &&\n          !this.tileQueue_.getCount() &&\n          !this.getLoadingOrNotReady()\n        : undefined;\n\n    if (!this.postRenderTimeoutHandle_) {\n      this.postRenderTimeoutHandle_ = setTimeout(() => {\n        this.postRenderTimeoutHandle_ = undefined;\n        this.handlePostRender();\n      }, 0);\n    }\n  }\n\n  /**\n   * Sets the layergroup of this map.\n   * @param {LayerGroup} layerGroup A layer group containing the layers in this map.\n   * @observable\n   * @api\n   */\n  setLayerGroup(layerGroup) {\n    const oldLayerGroup = this.getLayerGroup();\n    if (oldLayerGroup) {\n      this.handleLayerRemove_(new GroupEvent('removelayer', oldLayerGroup));\n    }\n    this.set(MapProperty.LAYERGROUP, layerGroup);\n  }\n\n  /**\n   * Set the size of this map.\n   * @param {import(\"./size.js\").Size|undefined} size The size in pixels of the map in the DOM.\n   * @observable\n   * @api\n   */\n  setSize(size) {\n    this.set(MapProperty.SIZE, size);\n  }\n\n  /**\n   * Set the target element to render this map into.\n   * @param {HTMLElement|string} [target] The Element or id of the Element\n   *     that the map is rendered in.\n   * @observable\n   * @api\n   */\n  setTarget(target) {\n    this.set(MapProperty.TARGET, target);\n  }\n\n  /**\n   * Set the view for this map.\n   * @param {View|Promise<import(\"./View.js\").ViewOptions>} view The view that controls this map.\n   * It is also possible to pass a promise that resolves to options for constructing a view.  This\n   * alternative allows view properties to be resolved by sources or other components that load\n   * view-related metadata.\n   * @observable\n   * @api\n   */\n  setView(view) {\n    if (!view || view instanceof View) {\n      this.set(MapProperty.VIEW, view);\n      return;\n    }\n    this.set(MapProperty.VIEW, new View());\n\n    const map = this;\n    view.then(function (viewOptions) {\n      map.setView(new View(viewOptions));\n    });\n  }\n\n  /**\n   * Force a recalculation of the map viewport size.  This should be called when\n   * third-party code changes the size of the map viewport.\n   * @api\n   */\n  updateSize() {\n    const targetElement = this.getTargetElement();\n\n    let size = undefined;\n    if (targetElement) {\n      const computedStyle = getComputedStyle(targetElement);\n      const width =\n        targetElement.offsetWidth -\n        parseFloat(computedStyle['borderLeftWidth']) -\n        parseFloat(computedStyle['paddingLeft']) -\n        parseFloat(computedStyle['paddingRight']) -\n        parseFloat(computedStyle['borderRightWidth']);\n      const height =\n        targetElement.offsetHeight -\n        parseFloat(computedStyle['borderTopWidth']) -\n        parseFloat(computedStyle['paddingTop']) -\n        parseFloat(computedStyle['paddingBottom']) -\n        parseFloat(computedStyle['borderBottomWidth']);\n      if (!isNaN(width) && !isNaN(height)) {\n        size = [width, height];\n        if (\n          !hasArea(size) &&\n          !!(\n            targetElement.offsetWidth ||\n            targetElement.offsetHeight ||\n            targetElement.getClientRects().length\n          )\n        ) {\n          warn(\n            \"No map visible because the map container's width or height are 0.\"\n          );\n        }\n      }\n    }\n\n    const oldSize = this.getSize();\n    if (size && (!oldSize || !equals(size, oldSize))) {\n      this.setSize(size);\n      this.updateViewportSize_();\n    }\n  }\n\n  /**\n   * Recomputes the viewport size and save it on the view object (if any)\n   * @private\n   */\n  updateViewportSize_() {\n    const view = this.getView();\n    if (view) {\n      let size = undefined;\n      const computedStyle = getComputedStyle(this.viewport_);\n      if (computedStyle.width && computedStyle.height) {\n        size = [\n          parseInt(computedStyle.width, 10),\n          parseInt(computedStyle.height, 10),\n        ];\n      }\n      view.setViewportSize(size);\n    }\n  }\n}\n\n/**\n * @param {MapOptions} options Map options.\n * @return {MapOptionsInternal} Internal map options.\n */\nfunction createOptionsInternal(options) {\n  /**\n   * @type {HTMLElement|Document}\n   */\n  let keyboardEventTarget = null;\n  if (options.keyboardEventTarget !== undefined) {\n    keyboardEventTarget =\n      typeof options.keyboardEventTarget === 'string'\n        ? document.getElementById(options.keyboardEventTarget)\n        : options.keyboardEventTarget;\n  }\n\n  /**\n   * @type {Object<string, *>}\n   */\n  const values = {};\n\n  const layerGroup =\n    options.layers &&\n    typeof (/** @type {?} */ (options.layers).getLayers) === 'function'\n      ? /** @type {LayerGroup} */ (options.layers)\n      : new LayerGroup({\n          layers:\n            /** @type {Collection<import(\"./layer/Base.js\").default>|Array<import(\"./layer/Base.js\").default>} */ (\n              options.layers\n            ),\n        });\n  values[MapProperty.LAYERGROUP] = layerGroup;\n\n  values[MapProperty.TARGET] = options.target;\n\n  values[MapProperty.VIEW] =\n    options.view instanceof View ? options.view : new View();\n\n  /** @type {Collection<import(\"./control/Control.js\").default>} */\n  let controls;\n  if (options.controls !== undefined) {\n    if (Array.isArray(options.controls)) {\n      controls = new Collection(options.controls.slice());\n    } else {\n      assert(\n        typeof (/** @type {?} */ (options.controls).getArray) === 'function',\n        47\n      ); // Expected `controls` to be an array or an `import(\"./Collection.js\").Collection`\n      controls = options.controls;\n    }\n  }\n\n  /** @type {Collection<import(\"./interaction/Interaction\").default>} */\n  let interactions;\n  if (options.interactions !== undefined) {\n    if (Array.isArray(options.interactions)) {\n      interactions = new Collection(options.interactions.slice());\n    } else {\n      assert(\n        typeof (/** @type {?} */ (options.interactions).getArray) ===\n          'function',\n        48\n      ); // Expected `interactions` to be an array or an `import(\"./Collection.js\").Collection`\n      interactions = options.interactions;\n    }\n  }\n\n  /** @type {Collection<import(\"./Overlay.js\").default>} */\n  let overlays;\n  if (options.overlays !== undefined) {\n    if (Array.isArray(options.overlays)) {\n      overlays = new Collection(options.overlays.slice());\n    } else {\n      assert(\n        typeof (/** @type {?} */ (options.overlays).getArray) === 'function',\n        49\n      ); // Expected `overlays` to be an array or an `import(\"./Collection.js\").Collection`\n      overlays = options.overlays;\n    }\n  } else {\n    overlays = new Collection();\n  }\n\n  return {\n    controls: controls,\n    interactions: interactions,\n    keyboardEventTarget: keyboardEventTarget,\n    overlays: overlays,\n    values: values,\n  };\n}\nexport default Map;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,IAAM,cAAN,cAA0B,mBAAW;AAAA;AAAA;AAAA;AAAA,EAInC,YAAY,KAAK;AACf,UAAM;AAMN,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,MAAM,YAAY;AACpC,aAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,YAAY;AAC9B,UAAM,YAAY,WAAW;AAC7B,UAAM,6BAA6B,WAAW;AAC9C,UAAM,6BAA6B,WAAW;AAE9C;AAAA,MACE;AAAA,MACA,WAAW,KAAK,CAAC,IAAI;AAAA,MACrB,WAAW,KAAK,CAAC,IAAI;AAAA,MACrB,IAAI,UAAU;AAAA,MACd,KAAK,UAAU;AAAA,MACf,CAAC,UAAU;AAAA,MACX,CAAC,UAAU,OAAO,CAAC;AAAA,MACnB,CAAC,UAAU,OAAO,CAAC;AAAA,IACrB;AAEA,gBAAY,4BAA4B,0BAA0B;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,2BACE,YACA,YACA,cACA,cACA,UACA,SACA,aACA,UACA;AACA,QAAI;AACJ,UAAM,YAAY,WAAW;AAS7B,aAAS,2BAA2B,SAAS,SAAS,OAAO,UAAU;AACrE,aAAO,SAAS,KAAK,SAAS,SAAS,UAAU,QAAQ,MAAM,QAAQ;AAAA,IACzE;AAEA,UAAM,aAAa,UAAU;AAE7B,UAAM,uBAAuB,MAAM,WAAW,MAAM,GAAG,UAAU;AACjE,UAAM,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;AACvB,QAAI,WAAW,SAAS,KAAK,cAAc;AACzC,YAAM,mBAAmB,WAAW,UAAU;AAC9C,YAAM,aAAa,SAAS,gBAAgB;AAC5C,cAAQ,KAAK,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAAA,IAChD;AAEA,UAAM,cAAc,WAAW;AAC/B,UAAM,YAAY,YAAY;AAE9B,UAAM;AAAA;AAAA,MAA6C,CAAC;AAAA;AACpD,UAAM,WAAW,CAAC;AAClB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,eAAS,IAAI,YAAY,GAAG,KAAK,GAAG,EAAE,GAAG;AACvC,cAAM,aAAa,YAAY,CAAC;AAChC,cAAM,QAAQ,WAAW;AACzB,YACE,MAAM,YAAY,KAClB,OAAO,YAAY,SAAS,KAC5B,YAAY,KAAK,UAAU,KAAK,GAChC;AACA,gBAAM,gBAAgB,MAAM,YAAY;AACxC,gBAAM,SAAS,MAAM,UAAU;AAC/B,cAAI,iBAAiB,QAAQ;AAC3B,kBAAM,cAAc,OAAO,SAAS,IAChC,uBACA;AACJ,kBAAMA,YAAW,2BAA2B;AAAA,cAC1C;AAAA,cACA,WAAW;AAAA,YACb;AACA,qBAAS,CAAC,IAAI,YAAY,CAAC,IAAI,QAAQ,CAAC,EAAE,CAAC;AAC3C,qBAAS,CAAC,IAAI,YAAY,CAAC,IAAI,QAAQ,CAAC,EAAE,CAAC;AAC3C,qBAAS,cAAc;AAAA,cACrB;AAAA,cACA;AAAA,cACA;AAAA,cACAA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AACA,cAAI,QAAQ;AACV,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,IAAI,QAAQ;AAC1B,YAAQ,QAAQ,CAAC,GAAG,MAAO,EAAE,cAAc,IAAI,KAAM;AACrD,YAAQ,KAAK,CAAC,GAAG,MAAM,EAAE,aAAa,EAAE,UAAU;AAClD,YAAQ,KAAK,CAAC,MAAM;AAClB,aAAQ,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ;AAAA,IAC5D,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,uBACE,YACA,YACA,cACA,cACA,aACA,SACA;AACA,UAAM,aAAa,KAAK;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,WAAO,eAAe;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,YAAY;AACtB,aAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,YAAY;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjC,wBAAwB,YAAY;AAClC,QAAI,OAAe,eAAe,GAAG;AACnC,iBAAW,oBAAoB,KAAK,eAAe;AAAA,IACrD;AAAA,EACF;AACF;AAMA,SAAS,gBAAgB,KAAK,YAAY;AACxC,SAAe,OAAO;AACxB;AAEA,IAAO,cAAQ;;;ACrOf,IAAM,uBAAN,cAAmC,YAAY;AAAA;AAAA;AAAA;AAAA,EAI7C,YAAY,KAAK;AACf,UAAM,GAAG;AAKT,SAAK,yBAAyB;AAAA,MAC5B;AAAA,MACA,wBAAgB;AAAA,MAChB,IAAI,WAAW,KAAK,GAAG;AAAA,IACzB;AAMA,SAAK,WAAW,SAAS,cAAc,KAAK;AAC5C,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,WAAW;AACjB,UAAM,QAAQ;AACd,UAAM,SAAS;AACf,UAAM,SAAS;AAEf,SAAK,SAAS,YAAY,qBAAqB;AAE/C,UAAM,YAAY,IAAI,YAAY;AAClC,cAAU,aAAa,KAAK,UAAU,UAAU,cAAc,IAAI;AAMlE,SAAK,YAAY,CAAC;AAMlB,SAAK,mBAAmB;AAKxB,SAAK,mBAAmB,CAAC;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,MAAM,YAAY;AACpC,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,IAAI,YAAY,IAAI,GAAG;AACzB,YAAM,QAAQ,IAAIC,eAAY,MAAM,QAAW,UAAU;AACzD,UAAI,cAAc,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EAEA,kBAAkB;AAChB,kBAAc,KAAK,sBAAsB;AACzC,SAAK,SAAS,WAAW,YAAY,KAAK,QAAQ;AAClD,UAAM,gBAAgB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,YAAY;AACtB,QAAI,CAAC,YAAY;AACf,UAAI,KAAK,kBAAkB;AACzB,aAAK,SAAS,MAAM,UAAU;AAC9B,aAAK,mBAAmB;AAAA,MAC1B;AACA;AAAA,IACF;AAEA,SAAK,oBAAoB,UAAU;AACnC,SAAK,oBAAoBC,mBAAgB,YAAY,UAAU;AAE/D,UAAM,mBAAmB,WAAW,iBAAiB,KAAK,SAAU,GAAG,GAAG;AACxE,aAAO,EAAE,SAAS,EAAE;AAAA,IACtB,CAAC;AACD,UAAM,YAAY,WAAW;AAE7B,SAAK,UAAU,SAAS;AAExB,UAAM,kBAAkB,KAAK;AAC7B,oBAAgB,SAAS;AAEzB,QAAI,kBAAkB;AACtB,aAAS,IAAI,GAAG,KAAK,iBAAiB,QAAQ,IAAI,IAAI,EAAE,GAAG;AACzD,YAAM,aAAa,iBAAiB,CAAC;AACrC,iBAAW,aAAa;AAExB,YAAM,QAAQ,WAAW;AACzB,YAAM,cAAc,MAAM,eAAe;AACzC,UACE,CAAC,OAAO,YAAY,SAAS,KAC5B,eAAe,WAAW,eAAe,aAC1C;AACA,cAAM,SAAS;AACf;AAAA,MACF;AAEA,YAAM,UAAU,MAAM,OAAO,YAAY,eAAe;AACxD,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,UAAI,YAAY,iBAAiB;AAC/B,aAAK,UAAU,KAAK,OAAO;AAC3B,0BAAkB;AAAA,MACpB;AACA,UAAI,kBAAkB,OAAO;AAC3B,wBAAgB;AAAA;AAAA,UAC2C;AAAA,QAC3D;AAAA,MACF;AAAA,IACF;AACA,SAAK,oBAAoB,UAAU;AAEnC,oBAAgB,KAAK,UAAU,KAAK,SAAS;AAE7C,SAAK,oBAAoBA,mBAAgB,aAAa,UAAU;AAEhE,QAAI,CAAC,KAAK,kBAAkB;AAC1B,WAAK,SAAS,MAAM,UAAU;AAC9B,WAAK,mBAAmB;AAAA,IAC1B;AAEA,SAAK,wBAAwB,UAAU;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,YAAY;AAC9B,UAAM,SAAS,KAAK;AACpB,aAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3C,aAAO,CAAC,EAAE,gBAAgB,UAAU;AAAA,IACtC;AACA,eAAW,gBAAgB;AAC3B,WAAO,SAAS;AAAA,EAClB;AACF;AAEA,IAAO,oBAAQ;;;AC/IR,IAAM,aAAN,cAAyB,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpC,YAAY,MAAM,OAAO;AACvB,UAAM,IAAI;AAOV,SAAK,QAAQ;AAAA,EACf;AACF;AAoCA,IAAM,WAAW;AAAA,EACf,QAAQ;AACV;AAUA,IAAM,aAAN,MAAM,oBAAmB,aAAU;AAAA;AAAA;AAAA;AAAA,EAIjC,YAAY,SAAS;AACnB,cAAU,WAAW,CAAC;AACtB,UAAM;AAAA;AAAA,MAAsC,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA;AACrE,WAAO,YAAY;AAEnB,QAAI,SAAS,QAAQ;AAErB,UAAM,WAAW;AAKjB,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,SAAK,sBAAsB,CAAC;AAM5B,SAAK,gBAAgB,CAAC;AAEtB,SAAK,kBAAkB,SAAS,QAAQ,KAAK,oBAAoB;AAEjE,QAAI,QAAQ;AACV,UAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,iBAAS,IAAI,mBAAW,OAAO,MAAM,GAAG,EAAC,QAAQ,KAAI,CAAC;AAAA,MACxD,OAAO;AACL,eAAO;AAAA,QAA0B,OAAQ,aAAc,YAAY,EAAE;AAAA,MACvE;AAAA,IACF,OAAO;AACL,eAAS,IAAI,mBAAW,QAAW,EAAC,QAAQ,KAAI,CAAC;AAAA,IACnD;AAEA,SAAK,UAAU,MAAM;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,SAAK,oBAAoB,QAAQ,aAAa;AAC9C,SAAK,oBAAoB,SAAS;AAElC,UAAM,SAAS,KAAK,UAAU;AAC9B,SAAK,oBAAoB;AAAA,MACvB,OAAO,QAAQ,4BAAoB,KAAK,KAAK,kBAAkB,IAAI;AAAA,MACnE,OAAO,QAAQ,4BAAoB,QAAQ,KAAK,qBAAqB,IAAI;AAAA,IAC3E;AAEA,eAAW,MAAM,KAAK,eAAe;AACnC,WAAK,cAAc,EAAE,EAAE,QAAQ,aAAa;AAAA,IAC9C;AACA,UAAM,KAAK,aAAa;AAExB,UAAM,cAAc,OAAO,SAAS;AACpC,aAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AACpD,YAAM,QAAQ,YAAY,CAAC;AAC3B,WAAK,wBAAwB,KAAK;AAClC,WAAK,cAAc,IAAI,WAAW,YAAY,KAAK,CAAC;AAAA,IACtD;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,OAAO;AAC7B,UAAM,eAAe;AAAA,MACnB;AAAA,QACE;AAAA,QACA,wBAAgB;AAAA,QAChB,KAAK;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAO,OAAO,kBAAU,QAAQ,KAAK,oBAAoB,IAAI;AAAA,IAC/D;AAEA,QAAI,iBAAiB,aAAY;AAC/B,mBAAa;AAAA,QACX,OAAO,OAAO,YAAY,KAAK,sBAAsB,IAAI;AAAA,QACzD,OAAO,OAAO,eAAe,KAAK,yBAAyB,IAAI;AAAA,MACjE;AAAA,IACF;AAEA,SAAK,cAAc,OAAO,KAAK,CAAC,IAAI;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,OAAO;AAC1B,SAAK,cAAc,IAAI,WAAW,YAAY,MAAM,KAAK,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,OAAO;AAC7B,SAAK,cAAc,IAAI,WAAW,eAAe,MAAM,KAAK,CAAC;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,iBAAiB;AAChC,UAAM,QAAQ,gBAAgB;AAC9B,SAAK,wBAAwB,KAAK;AAClC,SAAK,cAAc,IAAI,WAAW,YAAY,KAAK,CAAC;AACpD,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,iBAAiB;AACnC,UAAM,QAAQ,gBAAgB;AAC9B,UAAM,MAAM,OAAO,KAAK;AACxB,SAAK,cAAc,GAAG,EAAE,QAAQ,aAAa;AAC7C,WAAO,KAAK,cAAc,GAAG;AAC7B,SAAK,cAAc,IAAI,WAAW,eAAe,KAAK,CAAC;AACvD,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY;AACV;AAAA;AAAA,MACE,KAAK,IAAI,SAAS,MAAM;AAAA;AAAA,EAE5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,UAAU,QAAQ;AAChB,UAAM,aAAa,KAAK,UAAU;AAClC,QAAI,YAAY;AACd,YAAM,gBAAgB,WAAW,SAAS;AAC1C,eAAS,IAAI,GAAG,KAAK,cAAc,QAAQ,IAAI,IAAI,EAAE,GAAG;AACtD,aAAK,cAAc,IAAI,WAAW,eAAe,cAAc,CAAC,CAAC,CAAC;AAAA,MACpE;AAAA,IACF;AAEA,SAAK,IAAI,SAAS,QAAQ,MAAM;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,OAAO;AACpB,YAAQ,UAAU,SAAY,QAAQ,CAAC;AACvC,SAAK,UAAU,EAAE,QAAQ,SAAU,OAAO;AACxC,YAAM,eAAe,KAAK;AAAA,IAC5B,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,oBAAoB,MAAM;AACxB,UAAM,SAAS,SAAS,SAAY,OAAO,CAAC;AAC5C,UAAM,MAAM,OAAO;AAEnB,SAAK,UAAU,EAAE,QAAQ,SAAU,OAAO;AACxC,YAAM,oBAAoB,MAAM;AAAA,IAClC,CAAC;AAED,UAAM,gBAAgB,KAAK,cAAc;AACzC,QAAI,gBAAgB,cAAc;AAClC,QAAI,CAAC,QAAQ,cAAc,WAAW,QAAW;AAC/C,sBAAgB;AAAA,IAClB;AACA,aAAS,IAAI,KAAK,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AACjD,YAAM,aAAa,OAAO,CAAC;AAC3B,iBAAW,WAAW,cAAc;AACpC,iBAAW,UAAU,WAAW,WAAW,cAAc;AACzD,iBAAW,gBAAgB,KAAK;AAAA,QAC9B,WAAW;AAAA,QACX,cAAc;AAAA,MAChB;AACA,iBAAW,gBAAgB,KAAK;AAAA,QAC9B,WAAW;AAAA,QACX,cAAc;AAAA,MAChB;AACA,iBAAW,UAAU,KAAK,IAAI,WAAW,SAAS,cAAc,OAAO;AACvE,iBAAW,UAAU,KAAK,IAAI,WAAW,SAAS,cAAc,OAAO;AACvE,UAAI,cAAc,WAAW,QAAW;AACtC,YAAI,WAAW,WAAW,QAAW;AACnC,qBAAW,SAAS;AAAA,YAClB,WAAW;AAAA,YACX,cAAc;AAAA,UAChB;AAAA,QACF,OAAO;AACL,qBAAW,SAAS,cAAc;AAAA,QACpC;AAAA,MACF;AACA,UAAI,WAAW,WAAW,QAAW;AACnC,mBAAW,SAAS;AAAA,MACtB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,WAAO;AAAA,EACT;AACF;AAEA,IAAO,gBAAQ;;;ACnVf,IAAOC,qBAAQ;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AAAA,EACb,WAAW;AAAA,EACX,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,eAAe;AACjB;;;ACLA,IAAM,yBAAN,cAAqC,eAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1C,YAAY,KAAK,eAAe;AAC9B,UAAM,GAAG;AAOT,SAAK,OAAO;AAMZ,SAAK;AAML,SAAK,iBAAiB;AAMtB,SAAK,YAAY;AAMjB,SAAK,oBAAoB,CAAC;AAM1B,SAAK,iBAAiB,kBAAkB,SAAY,IAAI;AAQxD,SAAK,QAAQ;AAEb,UAAM,UAAU,KAAK,KAAK,YAAY;AAMtC,SAAK,kBAAkB,CAAC;AAMxB,SAAK,kBAAkB,CAAC;AAExB,SAAK,WAAW;AAMhB,SAAK,0BAA0B;AAAA,MAC7B;AAAA,MACAC,mBAAiB;AAAA,MACjB,KAAK;AAAA,MACL;AAAA,IACF;AAMA,SAAK;AAML,SAAK,sBAAsB;AAAA,MACzB;AAAA,MACAA,mBAAiB;AAAA,MACjB,KAAK;AAAA,MACL;AAAA,IACF;AAKA,SAAK,wBAAwB,KAAK,iBAAiB,KAAK,IAAI;AAE5D,SAAK,SAAS;AAAA,MACZ,kBAAU;AAAA,MACV,KAAK;AAAA,MACL,0BAA0B,EAAC,SAAS,MAAK,IAAI;AAAA,IAC/C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,cAAc;AAC1B,QAAI,WAAW,IAAI;AAAA,MACjB,4BAAoB;AAAA,MACpB,KAAK;AAAA,MACL;AAAA,IACF;AACA,SAAK,cAAc,QAAQ;AAC3B,QAAI,KAAK,oBAAoB,QAAW;AAEtC,mBAAa,KAAK,eAAe;AACjC,WAAK,kBAAkB;AACvB,iBAAW,IAAI;AAAA,QACb,4BAAoB;AAAA,QACpB,KAAK;AAAA,QACL;AAAA,MACF;AACA,WAAK,cAAc,QAAQ;AAAA,IAC7B,OAAO;AAEL,WAAK,kBAAkB,WAAW,MAAM;AACtC,aAAK,kBAAkB;AACvB,cAAMC,YAAW,IAAI;AAAA,UACnB,4BAAoB;AAAA,UACpB,KAAK;AAAA,UACL;AAAA,QACF;AACA,aAAK,cAAcA,SAAQ;AAAA,MAC7B,GAAG,GAAG;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,sBAAsB,cAAc;AAClC,UAAM,QAAQ;AACd,UAAM,KAAK,MAAM;AAEjB,QACE,MAAM,QAAQ,4BAAoB,aAClC,MAAM,QAAQ,4BAAoB,eAClC;AACA,aAAO,KAAK,gBAAgB,EAAE;AAC9B,iBAAW,aAAa,KAAK,iBAAiB;AAC5C,YAAI,KAAK,gBAAgB,SAAS,EAAE,WAAW,MAAM,QAAQ;AAK3D,iBAAO,KAAK,gBAAgB,SAAS;AACrC;AAAA,QACF;AAAA,MACF;AAAA,IACF,WACE,MAAM,QAAQ,4BAAoB,eAClC,MAAM,QAAQ,4BAAoB,aAClC;AACA,WAAK,gBAAgB,EAAE,IAAI;AAAA,IAC7B;AACA,SAAK,kBAAkB,OAAO,OAAO,KAAK,eAAe;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,cAAc;AAC7B,SAAK,sBAAsB,YAAY;AACvC,UAAM,WAAW,IAAI;AAAA,MACnB,4BAAoB;AAAA,MACpB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACP;AACA,SAAK,cAAc,QAAQ;AAQ3B,QACE,KAAK,kBACL,CAAC,SAAS,oBACV,CAAC,KAAK,aACN,KAAK,qBAAqB,YAAY,GACtC;AACA,WAAK,cAAc,KAAK,KAAK;AAAA,IAC/B;AAEA,QAAI,KAAK,gBAAgB,WAAW,GAAG;AACrC,WAAK,kBAAkB,QAAQ,aAAa;AAC5C,WAAK,kBAAkB,SAAS;AAChC,WAAK,YAAY;AACjB,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,cAAc;AACjC,WAAO,aAAa,WAAW;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,cAAc;AAC/B,SAAK,iBAAiB,KAAK,gBAAgB,WAAW;AACtD,SAAK,sBAAsB,YAAY;AACvC,UAAM,WAAW,IAAI;AAAA,MACnB,4BAAoB;AAAA,MACpB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACP;AACA,SAAK,cAAc,QAAQ;AAE3B,SAAK,QAAQ,IAAI,aAAa,aAAa,MAAM,YAAY;AAC7D,WAAO,eAAe,KAAK,OAAO,UAAU;AAAA,MAC1C,UAAU;AAAA,MACV,OAAO,aAAa;AAAA,IACtB,CAAC;AAED,QAAI,KAAK,kBAAkB,WAAW,GAAG;AACvC,YAAM,MAAM,KAAK,KAAK,iBAAiB;AACvC,WAAK,kBAAkB;AAAA,QACrB;AAAA,UACE;AAAA,UACA,4BAAoB;AAAA,UACpB,KAAK;AAAA,UACL;AAAA,QACF;AAAA,QACA,OAAO,KAAK,4BAAoB,WAAW,KAAK,kBAAkB,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QActE;AAAA,UACE,KAAK;AAAA,UACL,4BAAoB;AAAA,UACpB,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK,SAAS,eAAe,KAAK,SAAS,YAAY,MAAM,KAAK;AACpE,aAAK,kBAAkB;AAAA,UACrB;AAAA,YACE,KAAK,SAAS,YAAY;AAAA,YAC1B,4BAAoB;AAAA,YACpB,KAAK;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,cAAc;AAI/B,QAAI,KAAK,UAAU,YAAY,GAAG;AAChC,WAAK,sBAAsB,YAAY;AACvC,WAAK,YAAY;AACjB,YAAM,WAAW,IAAI;AAAA,QACnB,4BAAoB;AAAA,QACpB,KAAK;AAAA,QACL;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA,KAAK;AAAA,MACP;AACA,WAAK,cAAc,QAAQ;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,cAAc;AAC5B,SAAK,4BAA4B;AACjC,UAAM,WAAW,CAAC,EAAE,KAAK,SAAS,KAAK,UAAU,YAAY;AAC7D,SAAK;AAAA,MACH,IAAI;AAAA,QACF,4BAAoB;AAAA,QACpB,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,iBAAiB,OAAO;AAItB,UAAM,gBAAgB,KAAK;AAC3B,SACG,CAAC,iBAAiB,cAAc,sBAChC,OAAO,MAAM,eAAe,aAAa,MAAM,eAAe,OAC/D;AACA,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,cAAc;AACtB,WACE,KAAK,aACL,KAAK,IAAI,aAAa,UAAU,KAAK,MAAM,OAAO,IAChD,KAAK,kBACP,KAAK,IAAI,aAAa,UAAU,KAAK,MAAM,OAAO,IAAI,KAAK;AAAA,EAE/D;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,QAAI,KAAK,qBAAqB;AAC5B,oBAAc,KAAK,mBAAmB;AACtC,WAAK,sBAAsB;AAAA,IAC7B;AACA,SAAK,SAAS;AAAA,MACZ,kBAAU;AAAA,MACV,KAAK;AAAA,IACP;AAEA,QAAI,KAAK,yBAAyB;AAChC,oBAAc,KAAK,uBAAuB;AAC1C,WAAK,0BAA0B;AAAA,IACjC;AAEA,SAAK,kBAAkB,QAAQ,aAAa;AAC5C,SAAK,kBAAkB,SAAS;AAEhC,SAAK,WAAW;AAChB,UAAM,gBAAgB;AAAA,EACxB;AACF;AAEA,IAAO,iCAAQ;;;ACvZf,IAAO,sBAAQ;AAAA,EACb,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AACR;;;ACiCA,IAAM,UAAN,cAAsB,eAAW;AAAA;AAAA;AAAA;AAAA,EAI/B,YAAY,SAAS;AACnB,UAAM;AAEN,UAAM,UAAU,QAAQ;AACxB,QAAI,WAAW,CAAC,QAAQ,UAAU,CAAC,QAAQ,MAAM,eAAe;AAC9D,cAAQ,MAAM,gBAAgB;AAAA,IAChC;AAMA,SAAK,UAAU,UAAU,UAAU;AAMnC,SAAK,UAAU;AAMf,SAAK,OAAO;AAMZ,SAAK,eAAe,CAAC;AAErB,QAAI,QAAQ,QAAQ;AAClB,WAAK,SAAS,QAAQ;AAAA,IACxB;AAEA,QAAI,QAAQ,QAAQ;AAClB,WAAK,UAAU,QAAQ,MAAM;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,eAAW,KAAK,OAAO;AACvB,UAAM,gBAAgB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,KAAK;AACV,QAAI,KAAK,MAAM;AACb,iBAAW,KAAK,OAAO;AAAA,IACzB;AACA,aAAS,IAAI,GAAG,KAAK,KAAK,aAAa,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC1D,oBAAc,KAAK,aAAa,CAAC,CAAC;AAAA,IACpC;AACA,SAAK,aAAa,SAAS;AAC3B,SAAK,OAAO;AACZ,QAAI,KAAK;AACP,YAAM,SAAS,KAAK,UAChB,KAAK,UACL,IAAI,6BAA6B;AACrC,aAAO,YAAY,KAAK,OAAO;AAC/B,UAAI,KAAK,WAAW,MAAM;AACxB,aAAK,aAAa;AAAA,UAChB,OAAO,KAAK,qBAAa,YAAY,KAAK,QAAQ,IAAI;AAAA,QACxD;AAAA,MACF;AACA,UAAI,OAAO;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,UAAU;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWlB,UAAU,QAAQ;AAChB,SAAK,UACH,OAAO,WAAW,WAAW,SAAS,eAAe,MAAM,IAAI;AAAA,EACnE;AACF;AAEA,IAAO,kBAAQ;;;ACnHf,IAAM,cAAN,cAA0B,gBAAQ;AAAA;AAAA;AAAA;AAAA,EAIhC,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM;AAAA,MACJ,SAAS,SAAS,cAAc,KAAK;AAAA,MACrC,QAAQ,QAAQ;AAAA,MAChB,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAMD,SAAK,aAAa,SAAS,cAAc,IAAI;AAM7C,SAAK,aACH,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAMxD,SAAK,iBAAiB,KAAK;AAM3B,SAAK,uBAAuB,QAAQ,gBAAgB;AAMpD,SAAK,eACH,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAE5D,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,aAAa;AAAA,IACpB;AAEA,UAAM,YACJ,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAExD,UAAM,WACJ,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAEtD,UAAM,kBACJ,QAAQ,oBAAoB,SACxB,QAAQ,kBACR,YAAY;AAElB,UAAM,gBACJ,QAAQ,kBAAkB,SAAY,QAAQ,gBAAgB;AAEhE,UAAM,oBACJ,QAAQ,sBAAsB,SAC1B,QAAQ,oBACR,YAAY;AAElB,QAAI,OAAO,kBAAkB,UAAU;AAKrC,WAAK,iBAAiB,SAAS,cAAc,MAAM;AACnD,WAAK,eAAe,cAAc;AAClC,WAAK,eAAe,YAAY;AAAA,IAClC,OAAO;AACL,WAAK,iBAAiB;AAAA,IACxB;AAEA,UAAM,QAAQ,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAE5D,QAAI,OAAO,UAAU,UAAU;AAK7B,WAAK,SAAS,SAAS,cAAc,MAAM;AAC3C,WAAK,OAAO,cAAc;AAC1B,WAAK,OAAO,YAAY;AAAA,IAC1B,OAAO;AACL,WAAK,SAAS;AAAA,IAChB;AAEA,UAAM,cACJ,KAAK,gBAAgB,CAAC,KAAK,aAAa,KAAK,iBAAiB,KAAK;AAMrE,SAAK,gBAAgB,SAAS,cAAc,QAAQ;AACpD,SAAK,cAAc,aAAa,QAAQ,QAAQ;AAChD,SAAK,cAAc,aAAa,iBAAiB,OAAO,CAAC,KAAK,UAAU,CAAC;AACzE,SAAK,cAAc,QAAQ;AAC3B,SAAK,cAAc,YAAY,WAAW;AAE1C,SAAK,cAAc;AAAA,MACjB,kBAAU;AAAA,MACV,KAAK,aAAa,KAAK,IAAI;AAAA,MAC3B;AAAA,IACF;AAEA,UAAM,aACJ,YACA,MACA,qBACA,MACA,iBACC,KAAK,cAAc,KAAK,eAAe,MAAM,kBAAkB,OAC/D,KAAK,eAAe,KAAK;AAC5B,UAAM,UAAU,KAAK;AACrB,YAAQ,YAAY;AACpB,YAAQ,YAAY,KAAK,aAAa;AACtC,YAAQ,YAAY,KAAK,UAAU;AAOnC,SAAK,wBAAwB,CAAC;AAM9B,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,2BAA2B,YAAY;AACrC,UAAM,sBAAsB,MAAM;AAAA,MAChC,IAAI;AAAA,QACF,KAAK,OAAO,EACT,aAAa,EACb,QAAQ,CAAC,UAAU,MAAM,gBAAgB,UAAU,CAAC;AAAA,MACzD;AAAA,IACF;AAEA,UAAM,cAAc,CAAC,KAAK,OAAO,EAC9B,aAAa,EACb;AAAA,MACC,CAAC,UACC,MAAM,UAAU,KAChB,MAAM,UAAU,EAAE,2BAA2B,MAAM;AAAA,IACvD;AACF,QAAI,CAAC,KAAK,sBAAsB;AAC9B,WAAK,eAAe,WAAW;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,YAAY;AACzB,QAAI,CAAC,YAAY;AACf,UAAI,KAAK,kBAAkB;AACzB,aAAK,QAAQ,MAAM,UAAU;AAC7B,aAAK,mBAAmB;AAAA,MAC1B;AACA;AAAA,IACF;AAEA,UAAM,eAAe,KAAK,2BAA2B,UAAU;AAE/D,UAAM,UAAU,aAAa,SAAS;AACtC,QAAI,KAAK,oBAAoB,SAAS;AACpC,WAAK,QAAQ,MAAM,UAAU,UAAU,KAAK;AAC5C,WAAK,mBAAmB;AAAA,IAC1B;AAEA,QAAIC,QAAO,cAAc,KAAK,qBAAqB,GAAG;AACpD;AAAA,IACF;AAEA,mBAAe,KAAK,UAAU;AAG9B,aAAS,IAAI,GAAG,KAAK,aAAa,QAAQ,IAAI,IAAI,EAAE,GAAG;AACrD,YAAM,UAAU,SAAS,cAAc,IAAI;AAC3C,cAAQ,YAAY,aAAa,CAAC;AAClC,WAAK,WAAW,YAAY,OAAO;AAAA,IACrC;AAEA,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,OAAO;AAClB,UAAM,eAAe;AACrB,SAAK,cAAc;AACnB,SAAK,iBAAiB,KAAK;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,SAAK,QAAQ,UAAU,OAAO,eAAe;AAC7C,QAAI,KAAK,YAAY;AACnB,kBAAY,KAAK,gBAAgB,KAAK,MAAM;AAAA,IAC9C,OAAO;AACL,kBAAY,KAAK,QAAQ,KAAK,cAAc;AAAA,IAC9C;AACA,SAAK,aAAa,CAAC,KAAK;AACxB,SAAK,cAAc,aAAa,iBAAiB,OAAO,CAAC,KAAK,UAAU,CAAC;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,aAAa;AAC1B,QAAI,KAAK,iBAAiB,aAAa;AACrC;AAAA,IACF;AACA,SAAK,eAAe;AACpB,SAAK,QAAQ,UAAU,OAAO,kBAAkB;AAChD,QAAI,KAAK,gBAAgB;AACvB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,WAAW;AACtB,SAAK,iBAAiB;AACtB,QAAI,CAAC,KAAK,gBAAgB,KAAK,eAAe,WAAW;AACvD;AAAA,IACF;AACA,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,UAAU;AACf,SAAK,eAAe,SAAS,UAAU;AAAA,EACzC;AACF;AAEA,IAAO,sBAAQ;;;AC7Sf,IAAM,SAAN,cAAqB,gBAAQ;AAAA;AAAA;AAAA;AAAA,EAI3B,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM;AAAA,MACJ,SAAS,SAAS,cAAc,KAAK;AAAA,MACrC,QAAQ,QAAQ;AAAA,MAChB,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAED,UAAM,YACJ,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAExD,UAAM,QAAQ,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAE5D,UAAM,mBACJ,QAAQ,qBAAqB,SACzB,QAAQ,mBACR;AAMN,SAAK,SAAS;AAEd,QAAI,OAAO,UAAU,UAAU;AAC7B,WAAK,SAAS,SAAS,cAAc,MAAM;AAC3C,WAAK,OAAO,YAAY;AACxB,WAAK,OAAO,cAAc;AAAA,IAC5B,OAAO;AACL,WAAK,SAAS;AACd,WAAK,OAAO,UAAU,IAAI,gBAAgB;AAAA,IAC5C;AAEA,UAAM,WAAW,QAAQ,WAAW,QAAQ,WAAW;AAEvD,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,YAAY,YAAY;AAC/B,WAAO,aAAa,QAAQ,QAAQ;AACpC,WAAO,QAAQ;AACf,WAAO,YAAY,KAAK,MAAM;AAE9B,WAAO;AAAA,MACL,kBAAU;AAAA,MACV,KAAK,aAAa,KAAK,IAAI;AAAA,MAC3B;AAAA,IACF;AAEA,UAAM,aACJ,YAAY,MAAM,qBAAqB,MAAM;AAC/C,UAAM,UAAU,KAAK;AACrB,YAAQ,YAAY;AACpB,YAAQ,YAAY,MAAM;AAE1B,SAAK,kBAAkB,QAAQ,aAAa,QAAQ,aAAa;AAMjE,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAMrE,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAMrE,SAAK,YAAY;AAEjB,QAAI,KAAK,WAAW;AAClB,WAAK,QAAQ,UAAU,IAAI,YAAY;AAAA,IACzC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,OAAO;AAClB,UAAM,eAAe;AACrB,QAAI,KAAK,oBAAoB,QAAW;AACtC,WAAK,gBAAgB;AAAA,IACvB,OAAO;AACL,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,OAAO,IAAI,QAAQ;AACzB,QAAI,CAAC,MAAM;AAGT;AAAA,IACF;AACA,UAAM,WAAW,KAAK,YAAY;AAClC,QAAI,aAAa,QAAW;AAC1B,UAAI,KAAK,YAAY,KAAK,YAAY,IAAI,KAAK,QAAQ,GAAG;AACxD,aAAK,QAAQ;AAAA,UACX,UAAU;AAAA,UACV,UAAU,KAAK;AAAA,UACf,QAAQ;AAAA,QACV,CAAC;AAAA,MACH,OAAO;AACL,aAAK,YAAY,CAAC;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,UAAU;AACf,UAAM,aAAa,SAAS;AAC5B,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,UAAM,WAAW,WAAW,UAAU;AACtC,QAAI,YAAY,KAAK,WAAW;AAC9B,YAAM,YAAY,YAAY,WAAW;AACzC,UAAI,KAAK,WAAW;AAClB,cAAM,WAAW,KAAK,QAAQ,UAAU,SAAS,YAAY;AAC7D,YAAI,CAAC,YAAY,aAAa,GAAG;AAC/B,eAAK,QAAQ,UAAU,IAAI,YAAY;AAAA,QACzC,WAAW,YAAY,aAAa,GAAG;AACrC,eAAK,QAAQ,UAAU,OAAO,YAAY;AAAA,QAC5C;AAAA,MACF;AACA,WAAK,OAAO,MAAM,YAAY;AAAA,IAChC;AACA,SAAK,YAAY;AAAA,EACnB;AACF;AAEA,IAAO,iBAAQ;;;ACpJf,IAAM,OAAN,cAAmB,gBAAQ;AAAA;AAAA;AAAA;AAAA,EAIzB,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM;AAAA,MACJ,SAAS,SAAS,cAAc,KAAK;AAAA,MACrC,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAED,UAAM,YACJ,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAExD,UAAM,QAAQ,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAE5D,UAAM,kBACJ,QAAQ,oBAAoB,SACxB,QAAQ,kBACR,YAAY;AAElB,UAAM,mBACJ,QAAQ,qBAAqB,SACzB,QAAQ,mBACR,YAAY;AAElB,UAAM,cACJ,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAC5D,UAAM,eACJ,QAAQ,iBAAiB,SAAY,QAAQ,eAAe;AAE9D,UAAM,iBACJ,QAAQ,mBAAmB,SAAY,QAAQ,iBAAiB;AAClE,UAAM,kBACJ,QAAQ,oBAAoB,SACxB,QAAQ,kBACR;AAEN,UAAM,YAAY,SAAS,cAAc,QAAQ;AACjD,cAAU,YAAY;AACtB,cAAU,aAAa,QAAQ,QAAQ;AACvC,cAAU,QAAQ;AAClB,cAAU;AAAA,MACR,OAAO,gBAAgB,WACnB,SAAS,eAAe,WAAW,IACnC;AAAA,IACN;AAEA,cAAU;AAAA,MACR,kBAAU;AAAA,MACV,KAAK,aAAa,KAAK,MAAM,KAAK;AAAA,MAClC;AAAA,IACF;AAEA,UAAM,aAAa,SAAS,cAAc,QAAQ;AAClD,eAAW,YAAY;AACvB,eAAW,aAAa,QAAQ,QAAQ;AACxC,eAAW,QAAQ;AACnB,eAAW;AAAA,MACT,OAAO,iBAAiB,WACpB,SAAS,eAAe,YAAY,IACpC;AAAA,IACN;AAEA,eAAW;AAAA,MACT,kBAAU;AAAA,MACV,KAAK,aAAa,KAAK,MAAM,CAAC,KAAK;AAAA,MACnC;AAAA,IACF;AAEA,UAAM,aACJ,YAAY,MAAM,qBAAqB,MAAM;AAC/C,UAAM,UAAU,KAAK;AACrB,YAAQ,YAAY;AACpB,YAAQ,YAAY,SAAS;AAC7B,YAAQ,YAAY,UAAU;AAM9B,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,OAAO,OAAO;AACzB,UAAM,eAAe;AACrB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,OAAO;AAClB,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,OAAO,IAAI,QAAQ;AACzB,QAAI,CAAC,MAAM;AAGT;AAAA,IACF;AACA,UAAM,cAAc,KAAK,QAAQ;AACjC,QAAI,gBAAgB,QAAW;AAC7B,YAAM,UAAU,KAAK,mBAAmB,cAAc,KAAK;AAC3D,UAAI,KAAK,YAAY,GAAG;AACtB,YAAI,KAAK,aAAa,GAAG;AACvB,eAAK,iBAAiB;AAAA,QACxB;AACA,aAAK,QAAQ;AAAA,UACX,MAAM;AAAA,UACN,UAAU,KAAK;AAAA,UACf,QAAQ;AAAA,QACV,CAAC;AAAA,MACH,OAAO;AACL,aAAK,QAAQ,OAAO;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,eAAQ;;;AC3HR,SAASC,UAAS,SAAS;AAChC,YAAU,UAAU,UAAU,CAAC;AAG/B,QAAM,WAAW,IAAI,mBAAW;AAEhC,QAAM,cAAc,QAAQ,SAAS,SAAY,QAAQ,OAAO;AAChE,MAAI,aAAa;AACf,aAAS,KAAK,IAAI,aAAK,QAAQ,WAAW,CAAC;AAAA,EAC7C;AAEA,QAAM,gBAAgB,QAAQ,WAAW,SAAY,QAAQ,SAAS;AACtE,MAAI,eAAe;AACjB,aAAS,KAAK,IAAI,eAAO,QAAQ,aAAa,CAAC;AAAA,EACjD;AAEA,QAAM,qBACJ,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAC5D,MAAI,oBAAoB;AACtB,aAAS,KAAK,IAAI,oBAAY,QAAQ,kBAAkB,CAAC;AAAA,EAC3D;AAEA,SAAO;AACT;;;ACmGA,SAAS,uBAAuB,OAAO;AACrC,MAAI,iBAAiB,eAAO;AAC1B,UAAM,eAAe,IAAI;AACzB;AAAA,EACF;AACA,MAAI,iBAAiB,eAAY;AAC/B,UAAM,UAAU,EAAE,QAAQ,sBAAsB;AAAA,EAClD;AACF;AAMA,SAAS,oBAAoB,OAAO,KAAK;AACvC,MAAI,iBAAiB,eAAO;AAC1B,UAAM,eAAe,GAAG;AACxB;AAAA,EACF;AACA,MAAI,iBAAiB,eAAY;AAC/B,UAAM,SAAS,MAAM,UAAU,EAAE,SAAS;AAC1C,aAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,0BAAoB,OAAO,CAAC,GAAG,GAAG;AAAA,IACpC;AAAA,EACF;AACF;AAsDA,IAAM,MAAN,cAAkB,eAAW;AAAA;AAAA;AAAA;AAAA,EAI3B,YAAY,SAAS;AACnB,UAAM;AAEN,cAAU,WAAW,CAAC;AAKtB,SAAK;AAKL,SAAK;AAKL,SAAK;AAEL,UAAM,kBAAkB,sBAAsB,OAAO;AAMrD,SAAK;AAML,SAAK,UAAU;AAGf,SAAK,2BAA2B,KAAK,mBAAmB,KAAK,IAAI;AAMjE,SAAK,mBACH,QAAQ,oBAAoB,SAAY,QAAQ,kBAAkB;AAMpE,SAAK,cACH,QAAQ,eAAe,SACnB,QAAQ,aACR;AAMN,SAAK;AAML,SAAK;AAKL,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AAMrD,SAAK,8BAA8B,OAAgB;AAMnD,SAAK,8BAA8B,OAAgB;AAMnD,SAAK,cAAc;AAMnB,SAAK,cAAc;AAOnB,SAAK,kBAAkB;AAMvB,SAAK,2BAA2B;AAMhC,SAAK,yBAAyB;AAM9B,SAAK,kCAAkC;AAMvC,SAAK,YAAY,SAAS,cAAc,KAAK;AAC7C,SAAK,UAAU,YACb,iBAAiB,kBAAkB,SAAS,cAAc;AAC5D,SAAK,UAAU,MAAM,WAAW;AAChC,SAAK,UAAU,MAAM,WAAW;AAChC,SAAK,UAAU,MAAM,QAAQ;AAC7B,SAAK,UAAU,MAAM,SAAS;AAM9B,SAAK,oBAAoB,SAAS,cAAc,KAAK;AACrD,SAAK,kBAAkB,MAAM,WAAW;AACxC,SAAK,kBAAkB,MAAM,SAAS;AACtC,SAAK,kBAAkB,MAAM,QAAQ;AACrC,SAAK,kBAAkB,MAAM,SAAS;AACtC,SAAK,kBAAkB,MAAM,gBAAgB;AAC7C,SAAK,kBAAkB,YAAY;AACnC,SAAK,UAAU,YAAY,KAAK,iBAAiB;AAMjD,SAAK,6BAA6B,SAAS,cAAc,KAAK;AAC9D,SAAK,2BAA2B,MAAM,WAAW;AACjD,SAAK,2BAA2B,MAAM,SAAS;AAC/C,SAAK,2BAA2B,MAAM,QAAQ;AAC9C,SAAK,2BAA2B,MAAM,SAAS;AAC/C,SAAK,2BAA2B,MAAM,gBAAgB;AACtD,SAAK,2BAA2B,YAAY;AAC5C,SAAK,UAAU,YAAY,KAAK,0BAA0B;AAM1D,SAAK,0BAA0B;AAM/B,SAAK,iBAAiB,QAAQ;AAM9B,SAAK,uBAAuB,gBAAgB;AAM5C,SAAK,2BAA2B;AAMhC,SAAK,iBAAiB;AAKtB,SAAK,kBAAkB,IAAI,eAAe,MAAM,KAAK,WAAW,CAAC;AAMjE,SAAK,WAAW,gBAAgB,YAAYC,UAAgB;AAM5D,SAAK,eACH,gBAAgB,gBAChB,SAAoB;AAAA,MAClB,aAAa;AAAA,IACf,CAAC;AAMH,SAAK,YAAY,gBAAgB;AAOjC,SAAK,kBAAkB,CAAC;AAMxB,SAAK,YAAY;AAMjB,SAAK,uBAAuB,CAAC;AAM7B,SAAK,aAAa,IAAI;AAAA,MACpB,KAAK,gBAAgB,KAAK,IAAI;AAAA,MAC9B,KAAK,kBAAkB,KAAK,IAAI;AAAA,IAClC;AAEA,SAAK;AAAA,MACH,oBAAY;AAAA,MACZ,KAAK;AAAA,IACP;AACA,SAAK,kBAAkB,oBAAY,MAAM,KAAK,kBAAkB;AAChE,SAAK,kBAAkB,oBAAY,MAAM,KAAK,kBAAkB;AAChE,SAAK,kBAAkB,oBAAY,QAAQ,KAAK,oBAAoB;AAIpE,SAAK,cAAc,gBAAgB,MAAM;AAEzC,UAAM,MAAM;AACZ,QAAI,QAAQ,QAAQ,EAAE,QAAQ,gBAAgB,eAAO;AACnD,cAAQ,KAAK,KAAK,SAAU,aAAa;AACvC,YAAI,QAAQ,IAAI,aAAK,WAAW,CAAC;AAAA,MACnC,CAAC;AAAA,IACH;AAEA,SAAK,SAAS;AAAA,MACZ,4BAAoB;AAAA;AAAA;AAAA;AAAA,MAIpB,CAAC,UAAU;AACT,cAAM,QAAQ,OAAO,IAAI;AAAA,MAC3B;AAAA,IACF;AAEA,SAAK,SAAS;AAAA,MACZ,4BAAoB;AAAA;AAAA;AAAA;AAAA,MAIpB,CAAC,UAAU;AACT,cAAM,QAAQ,OAAO,IAAI;AAAA,MAC3B;AAAA,IACF;AAEA,SAAK,aAAa;AAAA,MAChB,4BAAoB;AAAA;AAAA;AAAA;AAAA,MAIpB,CAAC,UAAU;AACT,cAAM,QAAQ,OAAO,IAAI;AAAA,MAC3B;AAAA,IACF;AAEA,SAAK,aAAa;AAAA,MAChB,4BAAoB;AAAA;AAAA;AAAA;AAAA,MAIpB,CAAC,UAAU;AACT,cAAM,QAAQ,OAAO,IAAI;AAAA,MAC3B;AAAA,IACF;AAEA,SAAK,UAAU;AAAA,MACb,4BAAoB;AAAA;AAAA;AAAA;AAAA,MAIpB,CAAC,UAAU;AACT,aAAK,oBAAoB,MAAM,OAAO;AAAA,MACxC;AAAA,IACF;AAEA,SAAK,UAAU;AAAA,MACb,4BAAoB;AAAA;AAAA;AAAA;AAAA,MAIpB,CAAC,UAAU;AACT,cAAM,KAAK,MAAM,QAAQ,MAAM;AAC/B,YAAI,OAAO,QAAW;AACpB,iBAAO,KAAK,gBAAgB,GAAG,SAAS,CAAC;AAAA,QAC3C;AACA,cAAM,QAAQ,OAAO,IAAI;AAAA,MAC3B;AAAA,IACF;AAEA,SAAK,SAAS;AAAA;AAAA;AAAA;AAAA,MAIZ,CAAC,YAAY;AACX,gBAAQ,OAAO,IAAI;AAAA,MACrB;AAAA,IACF;AAEA,SAAK,aAAa;AAAA;AAAA;AAAA;AAAA,MAIhB,CAAC,gBAAgB;AACf,oBAAY,OAAO,IAAI;AAAA,MACzB;AAAA,IACF;AAEA,SAAK,UAAU,QAAQ,KAAK,oBAAoB,KAAK,IAAI,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,SAAS;AAClB,SAAK,YAAY,EAAE,KAAK,OAAO;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,eAAe,aAAa;AAC1B,SAAK,gBAAgB,EAAE,KAAK,WAAW;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,OAAO;AACd,UAAM,SAAS,KAAK,cAAc,EAAE,UAAU;AAC9C,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,OAAO;AACrB,wBAAoB,MAAM,OAAO,IAAI;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,SAAS;AAClB,SAAK,YAAY,EAAE,KAAK,OAAO;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,SAAS;AAC3B,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,OAAO,QAAW;AACpB,WAAK,gBAAgB,GAAG,SAAS,CAAC,IAAI;AAAA,IACxC;AACA,YAAQ,OAAO,IAAI;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,SAAK,SAAS,MAAM;AACpB,SAAK,aAAa,MAAM;AACxB,SAAK,UAAU,MAAM;AACrB,SAAK,gBAAgB,WAAW;AAChC,SAAK,UAAU,IAAI;AACnB,UAAM,gBAAgB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,sBAAsB,OAAO,UAAU,SAAS;AAC9C,QAAI,CAAC,KAAK,eAAe,CAAC,KAAK,WAAW;AACxC;AAAA,IACF;AACA,UAAM,aAAa,KAAK,+BAA+B,KAAK;AAC5D,cAAU,YAAY,SAAY,UAAU,CAAC;AAC7C,UAAM,eACJ,QAAQ,iBAAiB,SAAY,QAAQ,eAAe;AAC9D,UAAM,cACJ,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAC5D,UAAM,eAAe,QAAQ,iBAAiB;AAC9C,WAAO,KAAK,UAAU;AAAA,MACpB;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,mBAAmB,OAAO,SAAS;AACjC,UAAM,WAAW,CAAC;AAClB,SAAK;AAAA,MACH;AAAA,MACA,SAAU,SAAS;AACjB,iBAAS,KAAK,OAAO;AAAA,MACvB;AAAA,MACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe;AACb,UAAM,SAAS,CAAC;AAChB,aAAS,cAAc,YAAY;AACjC,iBAAW,QAAQ,SAAU,OAAO;AAClC,YAAI,iBAAiB,eAAY;AAC/B,wBAAc,MAAM,UAAU,CAAC;AAAA,QACjC,OAAO;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AACA,kBAAc,KAAK,UAAU,CAAC;AAC9B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,kBAAkB,OAAO,SAAS;AAChC,QAAI,CAAC,KAAK,eAAe,CAAC,KAAK,WAAW;AACxC,aAAO;AAAA,IACT;AACA,UAAM,aAAa,KAAK,+BAA+B,KAAK;AAC5D,cAAU,YAAY,SAAY,UAAU,CAAC;AAC7C,UAAM,cACJ,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAC5D,UAAM,eACJ,QAAQ,iBAAiB,SAAY,QAAQ,eAAe;AAC9D,UAAM,eAAe,QAAQ,iBAAiB;AAC9C,WAAO,KAAK,UAAU;AAAA,MACpB;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB,OAAO;AACxB,WAAO,KAAK,uBAAuB,KAAK,cAAc,KAAK,CAAC;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,2BAA2B,OAAO;AAChC,WAAO,KAAK,+BAA+B,KAAK,cAAc,KAAK,CAAC;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,OAAO;AACnB,UAAM,WAAW,KAAK;AACtB,UAAM,mBAAmB,SAAS,sBAAsB;AACxD,UAAM,eAAe,KAAK,QAAQ;AAClC,UAAM,SAAS,iBAAiB,QAAQ,aAAa,CAAC;AACtD,UAAM,SAAS,iBAAiB,SAAS,aAAa,CAAC;AACvD,UAAM;AAAA;AAAA,MAEJ,oBAAoB;AAAA;AAAA,QACW,MAAO,eAAe,CAAC;AAAA;AAAA;AAAA,QACvB;AAAA;AAAA;AAEjC,WAAO;AAAA,OACJ,cAAc,UAAU,iBAAiB,QAAQ;AAAA,OACjD,cAAc,UAAU,iBAAiB,OAAO;AAAA,IACnD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,YAAY;AACV;AAAA;AAAA,MACE,KAAK,IAAI,oBAAY,MAAM;AAAA;AAAA,EAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,uBAAuB,OAAO;AAC5B,WAAO;AAAA,MACL,KAAK,+BAA+B,KAAK;AAAA,MACzC,KAAK,QAAQ,EAAE,cAAc;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,+BAA+B,OAAO;AACpC,UAAM,aAAa,KAAK;AACxB,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AACA,WAAO,MAAe,WAAW,4BAA4B,MAAM,MAAM,CAAC;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,IAAI;AACjB,UAAM,UAAU,KAAK,gBAAgB,GAAG,SAAS,CAAC;AAClD,WAAO,YAAY,SAAY,UAAU;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB;AACd;AAAA;AAAA,MAAkC,KAAK,IAAI,oBAAY,UAAU;AAAA;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ;AAChB,UAAM,QAAQ,KAAK,cAAc;AACjC,QAAI,kBAAkB,oBAAY;AAChC,YAAM,UAAU,MAAM;AACtB;AAAA,IACF;AAEA,UAAM,aAAa,MAAM,UAAU;AACnC,eAAW,MAAM;AACjB,eAAW,OAAO,MAAM;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,UAAM,SAAS,KAAK,cAAc,EAAE,UAAU;AAC9C,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,UAAM,mBAAmB,KAAK,cAAc,EAAE,oBAAoB;AAClE,aAAS,IAAI,GAAG,KAAK,iBAAiB,QAAQ,IAAI,IAAI,EAAE,GAAG;AACzD,YAAM,QAAQ,iBAAiB,CAAC;AAChC,UAAI,CAAC,MAAM,SAAS;AAClB;AAAA,MACF;AACA,YAAM,WAAW,MAAM,MAAM,YAAY;AACzC,UAAI,YAAY,CAAC,SAAS,OAAO;AAC/B,eAAO;AAAA,MACT;AACA,YAAM,SAAS,MAAM,MAAM,UAAU;AACrC,UAAI,UAAU,OAAO,SAAS;AAC5B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,uBAAuB,YAAY;AACjC,UAAM,iBAAiB;AAAA,MACrB;AAAA,MACA,KAAK,QAAQ,EAAE,cAAc;AAAA,IAC/B;AACA,WAAO,KAAK,+BAA+B,cAAc;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,+BAA+B,YAAY;AACzC,UAAM,aAAa,KAAK;AACxB,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AACA,WAAO;AAAA,MACL,WAAW;AAAA,MACX,WAAW,MAAM,GAAG,CAAC;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU;AACR;AAAA;AAAA,MACE,KAAK,IAAI,oBAAY,IAAI;AAAA;AAAA,EAE7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU;AACR;AAAA;AAAA,MAA4B,KAAK,IAAI,oBAAY,IAAI;AAAA;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,sBAAsB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,+BAA+B;AAC7B,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,UAAM,gBAAgB,KAAK,iBAAiB;AAC5C,WAAO,gBAAgB,cAAc,gBAAgB;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,MAAM,eAAe,YAAY,gBAAgB;AAC/D,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,cAAc,MAAM;AACrC,WAAO,QAAQ,aAAa;AAC5B,UAAM,kBAAkB,IAAI,wBAAgB,MAAM,MAAM,YAAY;AACpE,SAAK,sBAAsB,eAAe;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,iBAAiB;AACrC,QAAI,CAAC,KAAK,aAAa;AAGrB;AAAA,IACF;AACA,UAAM;AAAA;AAAA,MACJ,gBAAgB;AAAA;AAElB,UAAM,YAAY,cAAc;AAChC,QACE,cAAcC,mBAAiB,eAC/B,cAAc,kBAAU,SACxB,cAAc,kBAAU,SACxB;AACA,YAAM,MAAM,KAAK,iBAAiB;AAClC,YAAM,WAAW,KAAK,UAAU,cAC5B,KAAK,UAAU,YAAY,IAC3B;AACJ,YAAM;AAAA;AAAA,QAA8B,cAAc;AAAA;AAClD;AAAA;AAAA;AAAA,QAGE,KAAK,2BAA2B,SAAS,MAAM;AAAA;AAAA;AAAA;AAAA,QAK/C,EAAE,aAAa,MAAM,IAAI,kBAAkB,UAAU,SAAS,MAAM;AAAA,QACpE;AACA;AAAA,MACF;AAAA,IACF;AACA,oBAAgB,aAAa,KAAK;AAClC,QAAI,KAAK,cAAc,eAAe,MAAM,OAAO;AACjD,YAAM,oBAAoB,KAAK,gBAAgB,EAAE,SAAS,EAAE,MAAM;AAClE,eAAS,IAAI,kBAAkB,SAAS,GAAG,KAAK,GAAG,KAAK;AACtD,cAAM,cAAc,kBAAkB,CAAC;AACvC,YACE,YAAY,OAAO,MAAM,QACzB,CAAC,YAAY,UAAU,KACvB,CAAC,KAAK,iBAAiB,GACvB;AACA;AAAA,QACF;AACA,cAAM,OAAO,YAAY,YAAY,eAAe;AACpD,YAAI,CAAC,QAAQ,gBAAgB,oBAAoB;AAC/C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,UAAM,aAAa,KAAK;AAWxB,UAAM,YAAY,KAAK;AACvB,QAAI,CAAC,UAAU,QAAQ,GAAG;AACxB,UAAI,kBAAkB,KAAK;AAC3B,UAAI,cAAc;AAClB,UAAI,YAAY;AACd,cAAM,QAAQ,WAAW;AACzB,YAAI,MAAM,iBAAS,SAAS,KAAK,MAAM,iBAAS,WAAW,GAAG;AAC5D,gBAAM,mBAAmB,KAAK,IAAI,IAAI,WAAW,OAAO;AACxD,4BAAkB,mBAAmB,IAAI;AACzC,wBAAc,mBAAmB,IAAI;AAAA,QACvC;AAAA,MACF;AACA,UAAI,UAAU,gBAAgB,IAAI,iBAAiB;AACjD,kBAAU,aAAa;AACvB,kBAAU,cAAc,iBAAiB,WAAW;AAAA,MACtD;AAAA,IACF;AAEA,QAAI,cAAc,KAAK,aAAa,CAAC,WAAW,SAAS;AACvD,UAAI,KAAK,oBAAoB,MAAM;AACjC,YAAI,KAAK,YAAYA,mBAAgB,cAAc,GAAG;AACpD,eAAK,UAAU;AAAA,YACbA,mBAAgB;AAAA,YAChB;AAAA,UACF;AAAA,QACF;AACA,YAAI,KAAK,YAAY,OAAO;AAC1B,eAAK,UAAU;AACf,eAAK;AAAA,YACH,IAAI,iBAAS,qBAAa,SAAS,MAAM,UAAU;AAAA,UACrD;AAAA,QACF;AAAA,MACF,WAAW,KAAK,YAAY,MAAM;AAChC,aAAK,UAAU;AACf,aAAK;AAAA,UACH,IAAI,iBAAS,qBAAa,WAAW,MAAM,UAAU;AAAA,QACvD;AAAA,MACF;AAAA,IACF;AAEA,UAAM,sBAAsB,KAAK;AACjC,aAAS,IAAI,GAAG,KAAK,oBAAoB,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC5D,0BAAoB,CAAC,EAAE,MAAM,UAAU;AAAA,IACzC;AACA,wBAAoB,SAAS;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,QAAI,KAAK,QAAQ,KAAK,CAAC,KAAK,QAAQ,EAAE,aAAa,GAAG;AACpD,WAAK,QAAQ,EAAE,mBAAmB,CAAC;AAAA,IACrC;AAEA,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,QAAI,KAAK,yBAAyB;AAChC,eAAS,IAAI,GAAG,KAAK,KAAK,yBAAyB,QAAQ,IAAI,IAAI,EAAE,GAAG;AACtE,sBAAc,KAAK,yBAAyB,CAAC,CAAC;AAAA,MAChD;AACA,WAAK,2BAA2B;AAChC,WAAK,UAAU;AAAA,QACb,kBAAU;AAAA,QACV,KAAK;AAAA,MACP;AACA,WAAK,UAAU;AAAA,QACb,kBAAU;AAAA,QACV,KAAK;AAAA,MACP;AACA,WAAK,wBAAwB,QAAQ;AACrC,WAAK,0BAA0B;AAC/B,iBAAW,KAAK,SAAS;AAAA,IAC3B;AAEA,QAAI,KAAK,gBAAgB;AACvB,WAAK,gBAAgB,UAAU,KAAK,cAAc;AAClD,YAAM,WAAW,KAAK,eAAe,YAAY;AACjD,UAAI,oBAAoB,YAAY;AAClC,aAAK,gBAAgB,UAAU,SAAS,IAAI;AAAA,MAC9C;AACA,WAAK,QAAQ,MAAS;AAAA,IACxB;AAOA,UAAM,SAAS,KAAK,UAAU;AAC9B,UAAM,gBACJ,OAAO,WAAW,WAAW,SAAS,eAAe,MAAM,IAAI;AACjE,SAAK,iBAAiB;AACtB,QAAI,CAAC,eAAe;AAClB,UAAI,KAAK,WAAW;AAClB,qBAAa,KAAK,wBAAwB;AAC1C,aAAK,2BAA2B;AAChC,aAAK,qBAAqB,SAAS;AACnC,aAAK,UAAU,QAAQ;AACvB,aAAK,YAAY;AAAA,MACnB;AACA,UAAI,KAAK,oBAAoB;AAC3B,6BAAqB,KAAK,kBAAkB;AAC5C,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACF,OAAO;AACL,oBAAc,YAAY,KAAK,SAAS;AACxC,UAAI,CAAC,KAAK,WAAW;AACnB,aAAK,YAAY,IAAI,kBAAqB,IAAI;AAAA,MAChD;AAEA,WAAK,0BAA0B,IAAI;AAAA,QACjC;AAAA,QACA,KAAK;AAAA,MACP;AACA,iBAAW,OAAO,6BAAqB;AACrC,aAAK,wBAAwB;AAAA,UAC3B,4BAAoB,GAAG;AAAA,UACvB,KAAK,sBAAsB,KAAK,IAAI;AAAA,QACtC;AAAA,MACF;AACA,WAAK,UAAU;AAAA,QACb,kBAAU;AAAA,QACV,KAAK;AAAA,QACL;AAAA,MACF;AACA,WAAK,UAAU;AAAA,QACb,kBAAU;AAAA,QACV,KAAK;AAAA,QACL,0BAA0B,EAAC,SAAS,MAAK,IAAI;AAAA,MAC/C;AAEA,YAAM,sBAAsB,CAAC,KAAK,uBAC9B,gBACA,KAAK;AACT,WAAK,2BAA2B;AAAA,QAC9B;AAAA,UACE;AAAA,UACA,kBAAU;AAAA,UACV,KAAK;AAAA,UACL;AAAA,QACF;AAAA,QACA;AAAA,UACE;AAAA,UACA,kBAAU;AAAA,UACV,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW,cAAc,YAAY;AAC3C,UAAI,oBAAoB,YAAY;AAClC,aAAK,gBAAgB,QAAQ,SAAS,IAAI;AAAA,MAC5C;AACA,WAAK,gBAAgB,QAAQ,aAAa;AAAA,IAC5C;AAEA,SAAK,WAAW;AAAA,EAGlB;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,6BAA6B;AAC3B,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,QAAI,KAAK,0BAA0B;AACjC,oBAAc,KAAK,wBAAwB;AAC3C,WAAK,2BAA2B;AAAA,IAClC;AACA,QAAI,KAAK,wBAAwB;AAC/B,oBAAc,KAAK,sBAAsB;AACzC,WAAK,yBAAyB;AAAA,IAChC;AACA,UAAM,OAAO,KAAK,QAAQ;AAC1B,QAAI,MAAM;AACR,WAAK,oBAAoB;AAEzB,WAAK,2BAA2B;AAAA,QAC9B;AAAA,QACA,wBAAgB;AAAA,QAChB,KAAK;AAAA,QACL;AAAA,MACF;AACA,WAAK,yBAAyB;AAAA,QAC5B;AAAA,QACA,kBAAU;AAAA,QACV,KAAK;AAAA,QACL;AAAA,MACF;AAEA,WAAK,mBAAmB,CAAC;AAAA,IAC3B;AACA,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,2BAA2B;AACzB,QAAI,KAAK,iCAAiC;AACxC,WAAK,gCAAgC,QAAQ,aAAa;AAC1D,WAAK,kCAAkC;AAAA,IACzC;AACA,UAAM,aAAa,KAAK,cAAc;AACtC,QAAI,YAAY;AACd,WAAK,gBAAgB,IAAI,WAAW,YAAY,UAAU,CAAC;AAC3D,WAAK,kCAAkC;AAAA,QACrC,OAAO,YAAY,wBAAgB,gBAAgB,KAAK,QAAQ,IAAI;AAAA,QACpE,OAAO,YAAY,kBAAU,QAAQ,KAAK,QAAQ,IAAI;AAAA,QACtD,OAAO,YAAY,YAAY,KAAK,iBAAiB,IAAI;AAAA,QACzD,OAAO,YAAY,eAAe,KAAK,oBAAoB,IAAI;AAAA,MACjE;AAAA,IACF;AACA,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,SAAK,qBAAqB;AAC1B,SAAK,aAAa,KAAK,IAAI,CAAC;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa;AACX,QAAI,KAAK,oBAAoB;AAC3B,2BAAqB,KAAK,kBAAkB;AAAA,IAC9C;AACA,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,UAAM,cAAc,KAAK,cAAc,EAAE,oBAAoB;AAC7D,aAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,YAAM,QAAQ,YAAY,CAAC,EAAE;AAC7B,UAAI,MAAM,YAAY,GAAG;AACvB,cAAM,YAAY,EAAE,mBAAmB;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,QAAI,KAAK,aAAa,KAAK,uBAAuB,QAAW;AAC3D,WAAK,qBAAqB,sBAAsB,KAAK,eAAe;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,sBAAsB;AACpB,UAAM,aAAa,KAAK;AACxB,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,SAAK,UAAU,oBAAoB,UAAU;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,SAAS;AACrB,WAAO,KAAK,YAAY,EAAE,OAAO,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,kBAAkB,aAAa;AAC7B,WAAO,KAAK,gBAAgB,EAAE,OAAO,WAAW;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,OAAO;AACjB,UAAM,SAAS,KAAK,cAAc,EAAE,UAAU;AAC9C,WAAO,OAAO,OAAO,KAAK;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,OAAO;AACxB,2BAAuB,MAAM,KAAK;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,SAAS;AACrB,WAAO,KAAK,YAAY,EAAE,OAAO,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,MAAM;AACjB,UAAM,OAAO,KAAK,QAAQ;AAC1B,UAAM,OAAO,KAAK,QAAQ;AAC1B,UAAM,qBAAqB,KAAK;AAEhC,QAAI,aAAa;AACjB,QAAI,SAAS,UAAa,QAAQ,IAAI,KAAK,QAAQ,KAAK,MAAM,GAAG;AAC/D,YAAM,YAAY,KAAK;AAAA,QACrB,KAAK,cAAc,KAAK,YAAY,YAAY;AAAA,MAClD;AACA,YAAM,YAAY,KAAK,SAAS;AAChC,mBAAa;AAAA,QACX,SAAS;AAAA,QACT,4BAA4B,KAAK;AAAA,QACjC,eAAe;AAAA,QACf,QAAQ;AAAA,UACN,UAAU;AAAA,UACV,UAAU;AAAA,UACV,UAAU;AAAA,UACV;AAAA,QACF;AAAA,QACA,OAAO,KAAK;AAAA,QACZ,YAAY;AAAA,QACZ,kBAAkB,KAAK,cAAc,EAAE,oBAAoB;AAAA,QAC3D,YAAY,KAAK;AAAA,QACjB,4BAA4B,KAAK;AAAA,QACjC,qBAAqB,CAAC;AAAA,QACtB;AAAA,QACA,WAAW,KAAK;AAAA,QAChB;AAAA,QACA,WAAW,CAAC;AAAA,QACZ;AAAA,QACA;AAAA,QACA,aAAa,CAAC;AAAA,QACd,OAAO,OAAO,IAAI;AAAA,QAClB,eAAe,CAAC;AAAA,MAClB;AACA,UAAI,UAAU,cAAc,UAAU,gBAAgB;AACpD,cAAM,WAAW,MAAM,UAAU,YAAY,IACzC,UAAU,WACV,UAAU;AAEd,mBAAW,aAAa;AAAA,UACtB,UAAU;AAAA,UACV,UAAU;AAAA,UACV;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,SAAK,cAAc;AACnB,SAAK,UAAU,YAAY,UAAU;AAErC,QAAI,YAAY;AACd,UAAI,WAAW,SAAS;AACtB,aAAK,OAAO;AAAA,MACd;AACA,YAAM,UAAU,KAAK;AAAA,QACnB,KAAK;AAAA,QACL,WAAW;AAAA,MACb;AAEA,UAAI,oBAAoB;AACtB,cAAM,YACJ,CAAC,KAAK,mBACL,CAAC,QAAQ,KAAK,eAAe,KAC5B,CAAC,OAAa,WAAW,QAAQ,KAAK,eAAe;AACzD,YAAI,WAAW;AACb,eAAK;AAAA,YACH,IAAI,iBAAS,qBAAa,WAAW,MAAM,kBAAkB;AAAA,UAC/D;AACA,eAAK,kBAAkB,oBAAoB,KAAK,eAAe;AAAA,QACjE;AAAA,MACF;AAEA,YAAM,OACJ,KAAK,mBACL,CAAC,WAAW,UAAU,iBAAS,SAAS,KACxC,CAAC,WAAW,UAAU,iBAAS,WAAW,KAC1C,CAAC,OAAa,WAAW,QAAQ,KAAK,eAAe;AAEvD,UAAI,MAAM;AACR,aAAK;AAAA,UACH,IAAI,iBAAS,qBAAa,SAAS,MAAM,UAAU;AAAA,QACrD;AACA,cAAM,WAAW,QAAQ,KAAK,eAAe;AAAA,MAC/C;AAAA,IACF;AAEA,SAAK,cAAc,IAAI,iBAAS,qBAAa,YAAY,MAAM,UAAU,CAAC;AAE1E,SAAK,kBACH,KAAK,YAAY,qBAAa,SAAS,KACvC,KAAK,YAAY,qBAAa,OAAO,KACrC,KAAK,YAAYA,mBAAgB,cAAc,IAC3C,CAAC,KAAK,WAAW,gBAAgB,KACjC,CAAC,KAAK,WAAW,SAAS,KAC1B,CAAC,KAAK,qBAAqB,IAC3B;AAEN,QAAI,CAAC,KAAK,0BAA0B;AAClC,WAAK,2BAA2B,WAAW,MAAM;AAC/C,aAAK,2BAA2B;AAChC,aAAK,iBAAiB;AAAA,MACxB,GAAG,CAAC;AAAA,IACN;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,YAAY;AACxB,UAAM,gBAAgB,KAAK,cAAc;AACzC,QAAI,eAAe;AACjB,WAAK,mBAAmB,IAAI,WAAW,eAAe,aAAa,CAAC;AAAA,IACtE;AACA,SAAK,IAAI,oBAAY,YAAY,UAAU;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,MAAM;AACZ,SAAK,IAAI,oBAAY,MAAM,IAAI;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,QAAQ;AAChB,SAAK,IAAI,oBAAY,QAAQ,MAAM;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,QAAQ,MAAM;AACZ,QAAI,CAAC,QAAQ,gBAAgB,cAAM;AACjC,WAAK,IAAI,oBAAY,MAAM,IAAI;AAC/B;AAAA,IACF;AACA,SAAK,IAAI,oBAAY,MAAM,IAAI,aAAK,CAAC;AAErC,UAAM,MAAM;AACZ,SAAK,KAAK,SAAU,aAAa;AAC/B,UAAI,QAAQ,IAAI,aAAK,WAAW,CAAC;AAAA,IACnC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX,UAAM,gBAAgB,KAAK,iBAAiB;AAE5C,QAAI,OAAO;AACX,QAAI,eAAe;AACjB,YAAM,gBAAgB,iBAAiB,aAAa;AACpD,YAAM,QACJ,cAAc,cACd,WAAW,cAAc,iBAAiB,CAAC,IAC3C,WAAW,cAAc,aAAa,CAAC,IACvC,WAAW,cAAc,cAAc,CAAC,IACxC,WAAW,cAAc,kBAAkB,CAAC;AAC9C,YAAM,SACJ,cAAc,eACd,WAAW,cAAc,gBAAgB,CAAC,IAC1C,WAAW,cAAc,YAAY,CAAC,IACtC,WAAW,cAAc,eAAe,CAAC,IACzC,WAAW,cAAc,mBAAmB,CAAC;AAC/C,UAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG;AACnC,eAAO,CAAC,OAAO,MAAM;AACrB,YACE,CAAC,QAAQ,IAAI,KACb,CAAC,EACC,cAAc,eACd,cAAc,gBACd,cAAc,eAAe,EAAE,SAEjC;AACA;AAAA,YACE;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,UAAM,UAAU,KAAK,QAAQ;AAC7B,QAAI,SAAS,CAAC,WAAW,CAACC,QAAO,MAAM,OAAO,IAAI;AAChD,WAAK,QAAQ,IAAI;AACjB,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB;AACpB,UAAM,OAAO,KAAK,QAAQ;AAC1B,QAAI,MAAM;AACR,UAAI,OAAO;AACX,YAAM,gBAAgB,iBAAiB,KAAK,SAAS;AACrD,UAAI,cAAc,SAAS,cAAc,QAAQ;AAC/C,eAAO;AAAA,UACL,SAAS,cAAc,OAAO,EAAE;AAAA,UAChC,SAAS,cAAc,QAAQ,EAAE;AAAA,QACnC;AAAA,MACF;AACA,WAAK,gBAAgB,IAAI;AAAA,IAC3B;AAAA,EACF;AACF;AAMA,SAAS,sBAAsB,SAAS;AAItC,MAAI,sBAAsB;AAC1B,MAAI,QAAQ,wBAAwB,QAAW;AAC7C,0BACE,OAAO,QAAQ,wBAAwB,WACnC,SAAS,eAAe,QAAQ,mBAAmB,IACnD,QAAQ;AAAA,EAChB;AAKA,QAAM,SAAS,CAAC;AAEhB,QAAM,aACJ,QAAQ,UACR;AAAA,EAA0B,QAAQ,OAAQ,cAAe;AAAA;AAAA,IAC1B,QAAQ;AAAA,MACnC,IAAI,cAAW;AAAA,IACb;AAAA;AAAA,MAEI,QAAQ;AAAA;AAAA,EAEd,CAAC;AACP,SAAO,oBAAY,UAAU,IAAI;AAEjC,SAAO,oBAAY,MAAM,IAAI,QAAQ;AAErC,SAAO,oBAAY,IAAI,IACrB,QAAQ,gBAAgB,eAAO,QAAQ,OAAO,IAAI,aAAK;AAGzD,MAAI;AACJ,MAAI,QAAQ,aAAa,QAAW;AAClC,QAAI,MAAM,QAAQ,QAAQ,QAAQ,GAAG;AACnC,iBAAW,IAAI,mBAAW,QAAQ,SAAS,MAAM,CAAC;AAAA,IACpD,OAAO;AACL;AAAA,QACE;AAAA,QAA0B,QAAQ,SAAU,aAAc;AAAA,QAC1D;AAAA,MACF;AACA,iBAAW,QAAQ;AAAA,IACrB;AAAA,EACF;AAGA,MAAI;AACJ,MAAI,QAAQ,iBAAiB,QAAW;AACtC,QAAI,MAAM,QAAQ,QAAQ,YAAY,GAAG;AACvC,qBAAe,IAAI,mBAAW,QAAQ,aAAa,MAAM,CAAC;AAAA,IAC5D,OAAO;AACL;AAAA,QACE;AAAA,QAA0B,QAAQ,aAAc,aAC9C;AAAA,QACF;AAAA,MACF;AACA,qBAAe,QAAQ;AAAA,IACzB;AAAA,EACF;AAGA,MAAI;AACJ,MAAI,QAAQ,aAAa,QAAW;AAClC,QAAI,MAAM,QAAQ,QAAQ,QAAQ,GAAG;AACnC,iBAAW,IAAI,mBAAW,QAAQ,SAAS,MAAM,CAAC;AAAA,IACpD,OAAO;AACL;AAAA,QACE;AAAA,QAA0B,QAAQ,SAAU,aAAc;AAAA,QAC1D;AAAA,MACF;AACA,iBAAW,QAAQ;AAAA,IACrB;AAAA,EACF,OAAO;AACL,eAAW,IAAI,mBAAW;AAAA,EAC5B;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAOC,eAAQ;", "names": ["callback", "Event_default", "EventType_default", "EventType_default", "EventType_default", "newEvent", "equals", "defaults", "defaults", "EventType_default", "equals", "Map_default"]}