<!--
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-07-21 08:32:04
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-07-29 10:43:16
 * @FilePath: \uavflight-ui\src\views\onedragon\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="odm-task-monitor">
    <div class="header">
      <h2>无人机影像处理任务监控</h2>
      <div class="actions">
        <span class="refresh-info" v-if="lastRefreshTime">
          上次刷新: {{ formatRefreshTime(lastRefreshTime) }}
          <template v-if="autoRefresh && nextRefreshCountdown > 0">
            | 下次刷新: {{ nextRefreshCountdown }}秒
          </template>
        </span>
        <el-button type="primary" :icon="Refresh" @click="handleManualRefresh" :loading="loading">刷新数据</el-button>
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          inactive-text=""
          @change="toggleAutoRefresh"
        />
      </div>
    </div>

    <div class="filter-bar">
      <div class="search-area">
        <el-input
          v-model="searchQuery"
          placeholder="输入任务ID搜索"
          clearable
          prefix-icon="Search"
          style="width: 300px"
          @input="handleSearch"
        />
        <el-button 
          v-if="!hasActiveFilters"
          type="primary" 
          :icon="Filter" 
          @click="openAdvancedFilter"
        >高级筛选</el-button>
        <el-button 
          v-else
          type="warning" 
          :icon="Delete" 
          @click="clearAllFilters"
        >取消筛选</el-button>
        <el-button type="success" :icon="PictureFilled" @click="openMapCompare">图层比较</el-button>
        <el-button type="warning" :icon="RefreshLeft" @click="openBatchReset">批量重置任务</el-button>
      </div>
      
      <div class="task-count" v-if="!loading && !error">
        共 <strong>{{ totalTasks }}</strong> 个任务，当前显示 <strong>{{ filteredTasks.length }}</strong> 个
        <el-tag v-if="hasActiveFilters" size="small" type="info">已应用筛选</el-tag>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
      <el-skeleton :rows="5" animated style="margin-top: 20px" />
    </div>
    
    <div v-else-if="error" class="error-container">
      <el-empty description="数据加载失败">
        <template #description>
          <p>{{ error }}</p>
        </template>
        <el-button type="primary" @click="fetchTasks">重试</el-button>
      </el-empty>
    </div>
    
    <div v-else-if="filteredTasks.length === 0" class="empty-container">
      <el-empty :description="tasks.length > 0 ? '没有符合条件的任务' : '暂无任务数据'" />
    </div>
    
    <div v-else class="task-cards">
      <el-card v-for="task in paginatedTasks" :key="task.task_id" class="task-card" :class="getTaskStatusClass(task.status)">
        <template #header>
          <div class="card-header">
            <div class="title-with-actions">
              <h3>任务ID: {{ task.task_id }}</h3>
              <el-tooltip v-if="task.status !== '未开始'" content="重置任务" placement="top">
                <el-button 
                  type="info" 
                  size="small"
                  circle
                  :icon="Refresh"
                  @click="confirmReset(task)"
                  :loading="resetLoadingMap[task.task_id]"
                />
              </el-tooltip>
            </div>
            <div class="task-status">
             
              
              <!-- 已完成任务显示比较按钮和预览按钮 -->
              <el-tooltip 
                v-if="task.status === '完成' && task.details?.geoserver_publish?.layer_name" 
                content="图层比较" 
                placement="top"
              >
                <el-button 
                  type="primary" 
                  size="small"
                  circle
                  :icon="PictureFilled"
                  @click="openMapCompareWithLayer(task.details.geoserver_publish.layer_name)"
                />
              </el-tooltip>
              <!-- 如果map_config中有layer_id，则添加比较按钮 -->
              <el-tooltip 
                v-else-if="task.status === '完成' && task.details?.map_config?.layer_id" 
                content="图层比较" 
                placement="top"
              >
                <el-button 
                  type="primary" 
                  size="small"
                  circle
                  :icon="PictureFilled"
                  @click="openMapCompareWithLayer(task.details.map_config.layer_id)"
                />
              </el-tooltip>
              <!-- 已完成任务显示预览按钮，紧挨着状态标签 -->
              <el-tooltip 
                v-if="task.status === '完成' && task.details?.geoserver_publish?.layer_name" 
                content="预览图层" 
                placement="top"
              >
                <el-button 
                  type="success" 
                  size="small"
                  circle
                  :icon="View"
                  @click="openMapPreview(task.details.geoserver_publish.layer_name)"
                />
              </el-tooltip>
              <!-- 如果map_config中有layer_id，优先使用该值 -->
              <el-tooltip 
                v-else-if="task.status === '完成' && task.details?.map_config?.layer_id" 
                content="预览图层" 
                placement="top"
              >
                <el-button 
                  type="success" 
                  size="small"
                  circle
                  :icon="View"
                  @click="openMapPreview(task.details.map_config.layer_id)"
                />
              </el-tooltip>
              <el-tag :type="getDisplayStatus(task.status) === '进行中' ? 'primary' : getStatusTagType(task.status)">{{ getDisplayStatus(task.status) }}</el-tag>
              <span class="task-duration">耗时: {{ calculateDuration(task.start_time, task.end_time) }}</span>
            </div>
          </div>
        </template>
        
        <div class="task-info">
          <div class="task-times">
            <p><i class="el-icon-time"></i> 开始时间: {{ formatDateTime(task.start_time) }}</p>
            <p v-if="task.end_time"><i class="el-icon-time"></i> 结束时间: {{ formatDateTime(task.end_time) }}</p>
          </div>
          
          <div class="process-timeline">
            <el-timeline>
              <el-timeline-item
                v-for="(process, name) in getProcesses(task)"
                :key="name"
                :type="getStatusTimelineItemType(process.status)"
                :color="getStatusColor(process.status)"
                :timestamp="formatTimeRange(process.start_time, process.end_time)"
                :hollow="process.status !== '处理中'"
                :size="process.status === '处理中' ? 'large' : 'normal'"
              >
                <div class="process-item">
                  <div class="process-name">
                    {{ getProcessLabel(name) }}
                    <el-tag 
                      v-if="process.retry_count && process.retry_count > 0" 
                      size="small" 
                      effect="dark" 
                      type="warning"
                    >
                      重试{{ process.retry_count }}次
                    </el-tag>
                    <el-tooltip
                      v-if="process.task_id"
                      content="查看处理日志"
                      placement="top"
                    >
                      <el-button
                        type="primary"
                        size="small"
                        circle
                        class="query-task-btn"
                        @click="openTaskDetail(process, name)"
                      >
                        <el-icon><Document /></el-icon>
                      </el-button>
                    </el-tooltip>
                  </div>
                  <div class="process-status">
                    <el-tag size="small" :type="getDisplayStatus(process.status) === '进行中' ? 'primary' : getStatusTagType(process.status)">{{ getDisplayStatus(process.status) }}</el-tag>
                    <span v-if="process.status === '完成'" class="process-duration">
                      耗时: {{ calculateDuration(process.start_time, process.end_time) }}
                    </span>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </el-card>
    </div>
    
    <el-pagination
      v-if="filteredTasks.length > 0"
      background
      layout="prev, pager, next"
      :total="filteredTasks.length"
      :page-size="pageSize"
      :current-page="currentPage"
      @current-change="handlePageChange"
      class="pagination"
    />
    
    <!-- 任务详情弹窗 -->
    <TaskDetailDialog
      v-model:visible="taskDetailVisible"
      :task-id="currentTaskId"
      :task-list="currentTaskList"
      :process-name="currentProcessName"
      :process-type="currentProcessType"
    />
    
    <!-- 图层预览弹窗 -->
    <MapPreviewDialog
      v-model:visible="mapPreviewVisible"
      :layer-id="currentLayerId"
    />

    <!-- 高级筛选弹窗 -->
    <AdvancedFilterDialog
      v-model:visible="advancedFilterVisible"
      :filter-settings="currentFilterSettings"
      @apply-filters="applyAdvancedFilters"
    />
    
    <!-- 图层比较弹窗 -->
    <MapCompareDialog
      v-model:visible="mapCompareVisible"
      :initial-layer-id="selectedCompareLayerId"
    />

    <!-- 批量重置任务弹窗 -->
    <el-dialog
      v-model="batchResetVisible"
      title="批量重置任务"
      width="85%"
      top="5vh"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="batch-reset-dialog">
        <!-- 筛选区域 -->
        <div class="filter-section">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-select
                v-model="batchResetFilters.status"
                placeholder="筛选任务状态"
                clearable
                multiple
                collapse-tags
                style="width: 100%"
              >
                <el-option label="进行中" value="进行中" />
                <el-option label="完成" value="完成" />
                <el-option label="失败" value="失败" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-input
                v-model="batchResetFilters.taskId"
                placeholder="搜索任务ID"
                clearable
                prefix-icon="Search"
              />
            </el-col>
            <el-col :span="8">
              <el-button type="primary" @click="refreshBatchResetList">刷新列表</el-button>
              <el-button @click="clearBatchResetFilters">清空筛选</el-button>
              <el-button type="info" @click="showAllTasks">显示所有任务</el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 任务列表 -->
        <div class="task-list-section">
          <div class="list-header">
            <el-checkbox
              v-model="selectAll"
              :indeterminate="isIndeterminate"
              @change="handleSelectAll"
            >
              全选 (已选择 {{ selectedTasks.length }} 个任务)
            </el-checkbox>
            <span class="task-count">
              共 {{ filteredBatchTasks.length }} 个可重置任务
              (原始数据: {{ batchResetTasks.length }} 个)
            </span>
          </div>

          <el-table
            ref="batchResetTable"
            :data="filteredBatchTasks"
            style="width: 100%"
            height="500"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="task_id" label="任务ID" width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(getDisplayStatus(row.status))">
                  {{ getDisplayStatus(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_time" label="创建时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.created_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="updated_time" label="更新时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.updated_time) }}
              </template>
            </el-table-column>
            <el-table-column label="进度" min-width="150">
              <template #default="{ row }">
                <div class="progress-info">
                  <span>{{ row.completed_steps || 0 }}/{{ row.total_steps || 0 }}</span>
                  <el-progress
                    :percentage="getTaskProgress(row)"
                    :status="getProgressStatus(row.status)"
                    :stroke-width="6"
                    style="margin-top: 4px"
                  />
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchResetVisible = false">取消</el-button>
          <el-button
            type="danger"
            @click="confirmBatchReset"
            :disabled="selectedTasks.length === 0"
            :loading="batchResetLoading"
          >
            重置选中任务 ({{ selectedTasks.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" name="onedragon" setup>
import { ref, onMounted, computed, onBeforeUnmount, watchEffect } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Refresh, Search, View, Document, List, Filter, PictureFilled, Delete, RefreshLeft } from '@element-plus/icons-vue';
import axios from 'axios';
import TaskDetailDialog from './TaskDetailDialog.vue';
import MapPreviewDialog from './MapPreviewDialog.vue';
import AdvancedFilterDialog from './AdvancedFilterDialog.vue';
import MapCompareDialog from './MapCompareDialog.vue';

// 状态
const loading = ref(false);
const error = ref('');
const tasks = ref<any[]>([]);
const totalTasks = ref(0);
const currentPage = ref(1);
const pageSize = ref(10); // 修改为每页10条
const searchQuery = ref('');
const autoRefresh = ref(true);
const refreshInterval = ref<number | null>(null);
const countdownInterval = ref<number | null>(null);
const lastRefreshTime = ref<Date | null>(null);
const nextRefreshCountdown = ref(60);

// 任务详情弹窗相关
const taskDetailVisible = ref(false);
const currentTaskId = ref('');
const currentTaskList = ref<string[]>([]);
const currentProcessName = ref('');
const currentProcessType = ref('');

// 图层预览弹窗相关
const mapPreviewVisible = ref(false);
const currentLayerId = ref('');

// 高级筛选弹窗相关
const advancedFilterVisible = ref(false);
const currentFilterSettings = ref({
  status: [] as string[],
  timeRange: null as [string, string] | null,
  sortField: 'default_priority', // 默认使用状态优先级排序
  sortOrder: 'ascending'
});

// 图层比较弹窗相关
const mapCompareVisible = ref(false);
const selectedCompareLayerId = ref('');

// 重置任务状态
const resetLoadingMap = ref<{ [taskId: string]: boolean }>({});

// 批量重置任务相关
const batchResetVisible = ref(false);
const batchResetLoading = ref(false);
const selectedTasks = ref<any[]>([]);
const selectAll = ref(false);
const isIndeterminate = ref(false);
const batchResetFilters = ref({
  status: [] as string[],
  taskId: ''
});
const batchResetTasks = ref<any[]>([]);

// 自动刷新控制
const toggleAutoRefresh = (val: boolean) => {
  if (val) {
    startAutoRefresh();
    startCountdown();
  } else {
    stopAutoRefresh();
    stopCountdown();
  }
};

const startAutoRefresh = () => {
  stopAutoRefresh(); // 先清除可能存在的定时器
  refreshInterval.value = window.setInterval(() => {
    fetchTasks();
  }, 60000); // 每1分钟刷新一次
  console.log('启动自动刷新');
};

const stopAutoRefresh = () => {
  if (refreshInterval.value !== null) {
    clearInterval(refreshInterval.value);
    refreshInterval.value = null;
    console.log('停止自动刷新');
  }
};

// 倒计时功能
const startCountdown = () => {
  stopCountdown(); // 先清除可能存在的定时器
  nextRefreshCountdown.value = 60; // 初始化倒计时为60秒
  
  countdownInterval.value = window.setInterval(() => {
    if (nextRefreshCountdown.value > 0) {
      nextRefreshCountdown.value--;
    } else {
      // 倒计时结束，触发刷新
      fetchTasks();
      // 不需要手动重置倒计时，因为fetchTasks会调用startCountdown
    }
  }, 1000);
  
  console.log('启动倒计时');
};

const stopCountdown = () => {
  if (countdownInterval.value !== null) {
    clearInterval(countdownInterval.value);
    countdownInterval.value = null;
    console.log('停止倒计时');
  }
};

// 格式化刷新时间
const formatRefreshTime = (date: Date) => {
  if (!date) return '';
  
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  
  if (diff < 60000) { // 小于1分钟
    return `${Math.floor(diff / 1000)}秒前`;
  } else if (diff < 3600000) { // 小于1小时
    return `${Math.floor(diff / 60000)}分钟前`;
  } else {
    return date.toLocaleTimeString('zh-CN');
  }
};

// 是否有激活的筛选条件
const hasActiveFilters = computed(() => {
  return currentFilterSettings.value.status.length > 0 ||
         currentFilterSettings.value.timeRange !== null ||
         searchQuery.value.trim() !== '';
});

// 过滤任务
const filteredTasks = computed(() => {
  // 开始使用原始任务列表
  let result = [...tasks.value];
  
  // 应用搜索查询
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    result = result.filter(task => task.task_id.toLowerCase().includes(query));
  }
  
  // 应用高级筛选 - 状态筛选
  if (currentFilterSettings.value.status.length > 0) {
    result = result.filter(task => {
      const displayStatus = getDisplayStatus(task.status);
      return currentFilterSettings.value.status.includes(displayStatus);
    });
  }
  
  // 应用高级筛选 - 时间范围筛选
  if (currentFilterSettings.value.timeRange) {
    const [startDate, endDate] = currentFilterSettings.value.timeRange;
    const startTime = new Date(startDate).getTime();
    const endTime = new Date(endDate + ' 23:59:59').getTime();
    
    result = result.filter(task => {
      const taskStartTime = new Date(task.start_time).getTime();
      return taskStartTime >= startTime && taskStartTime <= endTime;
    });
  }
  
  // 应用高级筛选 - 排序
  const { sortField, sortOrder } = currentFilterSettings.value;
  result = result.sort((a, b) => {
    let valueA, valueB;
    
    switch (sortField) {
      case 'default_priority':
        // 默认排序：按状态优先级排序，同状态内按任务ID升序
        const priorityA = getStatusPriority(a.status);
        const priorityB = getStatusPriority(b.status);

        if (priorityA !== priorityB) {
          // 状态优先级不同，按优先级排序
          return sortOrder === 'ascending' ? priorityA - priorityB : priorityB - priorityA;
        } else {
          // 状态优先级相同，按任务ID升序排序
          return a.task_id.localeCompare(b.task_id);
        }
      case 'task_id':
        valueA = a.task_id;
        valueB = b.task_id;
        break;
      case 'status':
        valueA = getDisplayStatus(a.status);
        valueB = getDisplayStatus(b.status);
        break;
      case 'start_time':
        valueA = a.start_time ? new Date(a.start_time).getTime() : 0;
        valueB = b.start_time ? new Date(b.start_time).getTime() : 0;
        break;
      case 'end_time':
        valueA = a.end_time ? new Date(a.end_time).getTime() : Infinity;
        valueB = b.end_time ? new Date(b.end_time).getTime() : Infinity;
        break;
      case 'duration':
        valueA = calculateDurationMs(a.start_time, a.end_time);
        valueB = calculateDurationMs(b.start_time, b.end_time);
        break;
      default:
        valueA = a.task_id;
        valueB = b.task_id;
    }

    // 如果是默认优先级排序，已经在上面处理了，直接返回
    if (sortField === 'default_priority') {
      return 0; // 这行不会执行到，因为上面已经return了
    }
    
    // 根据排序方向比较值
    if (sortOrder === 'ascending') {
      return valueA > valueB ? 1 : -1;
    } else {
      return valueA < valueB ? 1 : -1;
    }
  });
  
  return result;
});

// 计算耗时（毫秒）用于排序
const calculateDurationMs = (startTime: string, endTime: string) => {
  if (!startTime) return 0;
  if (!endTime) return Infinity; // 进行中的任务放在最后
  
  try {
    const start = new Date(startTime).getTime();
    const end = new Date(endTime).getTime();
    return end - start;
  } catch (e) {
    return 0;
  }
};

// 分页后的任务数据
const paginatedTasks = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredTasks.value.slice(start, end);
});

// 批量重置任务筛选
const filteredBatchTasks = computed(() => {
  let filtered = batchResetTasks.value.filter(task => {
    // 只显示可以重置的任务（排除"未开始"状态）
    // 使用 getDisplayStatus 来统一状态判断
    const displayStatus = getDisplayStatus(task.status);
    return displayStatus !== '未开始';
  });

  // 按状态筛选
  if (batchResetFilters.value.status.length > 0) {
    filtered = filtered.filter(task => {
      const displayStatus = getDisplayStatus(task.status);
      return batchResetFilters.value.status.includes(displayStatus);
    });
  }

  // 按任务ID搜索
  if (batchResetFilters.value.taskId.trim()) {
    const searchTerm = batchResetFilters.value.taskId.trim().toLowerCase();
    filtered = filtered.filter(task =>
      task.task_id.toLowerCase().includes(searchTerm)
    );
  }

  return filtered;
});

const handleSearch = () => {
  currentPage.value = 1; // 搜索时重置到第一页
};

// API相关
const getApiUrl = () => {
  const host = import.meta.env.VITE_GEOSERVER_HOST || 'localhost';
  const port = import.meta.env.VITE_GEOSERVER_HD_PORT || '8080';
  return `http://${host}:${port}/api/map/odm/tasks`;
};

// 获取任务数据
const fetchTasks = async () => {
  loading.value = true;
  error.value = '';
  
  try {
    const response = await axios.get(getApiUrl());
    
    if (response.data.status === 'success') {
      tasks.value = response.data.tasks || [];
      totalTasks.value = response.data.count || 0;
      lastRefreshTime.value = new Date();
      
      // 重新开始倒计时（如果自动刷新开启）
      if (autoRefresh.value) {
        startCountdown();
      }
    } else {
      throw new Error(response.data.message || '请求失败');
    }
  } catch (err) {
    console.error('获取任务数据失败:', err);
    error.value = err instanceof Error ? err.message : '未知错误，请重试';
    ElMessage.error('获取任务数据失败');
  } finally {
    loading.value = false;
  }
};

// 确认重置任务
const confirmReset = (task: any) => {
  ElMessageBox.confirm(
    `确定要重置任务 ${task.task_id} 吗？`,
    '重置任务',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
  .then(() => {
    resetTask(task);
  })
  .catch(() => {
    ElMessage.info('已取消重置');
  });
};

// 检查是否有正在进行中的任务
const hasRunningTask = () => {
  return tasks.value.some(task => getDisplayStatus(task.status) === '进行中');
};

// 重置任务
const resetTask = async (task: any) => {
  const taskId = task.task_id;
  const isRunning = getDisplayStatus(task.status) === '进行中';
  const shouldResetConfig = isRunning || !hasRunningTask(); // 当前任务正在进行中或没有任何进行中的任务
  
  if (resetLoadingMap.value[taskId]) {
    return;
  }
  
  resetLoadingMap.value[taskId] = true;
  
  try {
    // 构建API URL，使用与getApiUrl相同的主机和端口
    const host = import.meta.env.VITE_GEOSERVER_HOST || 'localhost';
    const port = import.meta.env.VITE_GEOSERVER_HD_PORT || '8080';
    
    // 调用重置信息API
    const resetApiUrl = `http://${host}:${port}/api/map/odm/task/rename-info`;
    const response = await axios.get(resetApiUrl, {
      params: { task_id: taskId }
    });
    
    // 如果是进行中的任务，或者没有进行中的任务，调用重置任务配置接口
    if (shouldResetConfig) {
      const resetConfigUrl = `http://${host}:${port}/api/map/odm/reset-task-config`;
      await axios.get(resetConfigUrl);
    }
    
    if (response.data && response.data.status === 'success') {
      ElMessage.success('任务已重置');
      fetchTasks(); // 刷新任务列表
    } else {
      throw new Error(response.data?.message || '任务重置失败');
    }
  } catch (err) {
    console.error('重置任务失败:', err);
    ElMessage.error(err instanceof Error ? err.message : '任务重置失败');
  } finally {
    resetLoadingMap.value[taskId] = false;
  }
};

// 批量重置任务相关方法
const openBatchReset = async () => {
  batchResetVisible.value = true;
  await refreshBatchResetList();
};

const refreshBatchResetList = async () => {
  try {
    // 重新获取任务列表
    await fetchTasks();
    // 复制任务数据到批量重置列表
    batchResetTasks.value = [...tasks.value];
    // 清空选择
    selectedTasks.value = [];
    selectAll.value = false;
    isIndeterminate.value = false;
    console.log(`批量重置列表已刷新，共 ${batchResetTasks.value.length} 个任务`);
  } catch (error) {
    console.error('刷新批量重置列表失败:', error);
    ElMessage.error('刷新列表失败');
  }
};

const clearBatchResetFilters = () => {
  batchResetFilters.value = {
    status: [],
    taskId: ''
  };
};

const showAllTasks = () => {
  console.log('显示所有任务，包括未开始状态');
  // 临时修改筛选逻辑，显示所有任务
  batchResetTasks.value = [...tasks.value];
  clearBatchResetFilters();
};

const handleSelectAll = (val: boolean) => {
  if (val) {
    selectedTasks.value = [...filteredBatchTasks.value];
  } else {
    selectedTasks.value = [];
  }
  isIndeterminate.value = false;
};

const handleSelectionChange = (selection: any[]) => {
  selectedTasks.value = selection;
  const total = filteredBatchTasks.value.length;
  const selected = selection.length;

  selectAll.value = selected === total && total > 0;
  isIndeterminate.value = selected > 0 && selected < total;
};



const confirmBatchReset = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请选择要重置的任务');
    return;
  }

  ElMessageBox.confirm(
    `确定要重置选中的 ${selectedTasks.value.length} 个任务吗？`,
    '批量重置任务',
    {
      confirmButtonText: '确定重置',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger'
    }
  )
  .then(() => {
    executeBatchReset();
  })
  .catch(() => {
    ElMessage.info('已取消批量重置');
  });
};

const executeBatchReset = async () => {
  if (batchResetLoading.value) return;

  batchResetLoading.value = true;

  try {
    const host = import.meta.env.VITE_GEOSERVER_HOST || 'localhost';
    const port = import.meta.env.VITE_GEOSERVER_HD_PORT || '8080';

    let successCount = 0;
    let failCount = 0;
    const errors: string[] = [];

    // 检查是否需要重置配置
    const hasRunningTasks = selectedTasks.value.some(task => getDisplayStatus(task.status) === '进行中');
    const shouldResetConfig = hasRunningTasks || !hasRunningTask();

    // 逐个重置任务
    for (const task of selectedTasks.value) {
      try {
        // 调用重置信息API
        const resetApiUrl = `http://${host}:${port}/api/map/odm/task/rename-info`;
        const response = await axios.get(resetApiUrl, {
          params: { task_id: task.task_id }
        });

        if (response.data && response.data.status === 'success') {
          successCount++;
        } else {
          failCount++;
          errors.push(`任务 ${task.task_id}: ${response.data?.message || '重置失败'}`);
        }
      } catch (error) {
        failCount++;
        errors.push(`任务 ${task.task_id}: ${error instanceof Error ? error.message : '重置失败'}`);
      }
    }

    // 如果需要，重置任务配置
    if (shouldResetConfig && successCount > 0) {
      try {
        const resetConfigUrl = `http://${host}:${port}/api/map/odm/reset-task-config`;
        await axios.get(resetConfigUrl);
      } catch (error) {
        console.error('重置任务配置失败:', error);
        ElMessage.warning('任务重置成功，但配置重置失败');
      }
    }

    // 显示结果
    if (successCount > 0 && failCount === 0) {
      ElMessage.success(`成功重置 ${successCount} 个任务`);
    } else if (successCount > 0 && failCount > 0) {
      ElMessage.warning(`成功重置 ${successCount} 个任务，${failCount} 个任务重置失败`);
      console.error('批量重置错误:', errors);
    } else {
      ElMessage.error(`所有任务重置失败`);
      console.error('批量重置错误:', errors);
    }

    // 刷新任务列表
    await fetchTasks();
    await refreshBatchResetList();

    // 如果全部成功，关闭弹窗
    if (failCount === 0) {
      batchResetVisible.value = false;
    }

  } catch (error) {
    console.error('批量重置任务失败:', error);
    ElMessage.error('批量重置任务失败');
  } finally {
    batchResetLoading.value = false;
  }
};

// 格式化日期时间
const formatDateTime = (dateTimeStr: string) => {
  if (!dateTimeStr) return '暂无';
  
  // 已经是格式化好的时间字符串，直接返回
  if (dateTimeStr.includes('-') && dateTimeStr.includes(':')) {
    return dateTimeStr;
  }
  
  // 时间戳转换
  try {
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).replace(/\//g, '-');
  } catch (e) {
    return dateTimeStr || '暂无';
  }
};

// 计算任务耗时
const calculateDuration = (startTime: string, endTime: string) => {
  if (!startTime || !endTime) return '进行中';
  
  try {
    const start = new Date(startTime).getTime();
    const end = new Date(endTime).getTime();
    const diff = end - start;
    
    // 换算成分钟和秒
    const minutes = Math.floor(diff / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    
    if (minutes > 60) {
      const hours = Math.floor(minutes / 60);
      const remainMinutes = minutes % 60;
      return `${hours}小时${remainMinutes}分${seconds}秒`;
    }
    
    return `${minutes}分${seconds}秒`;
  } catch (e) {
    return '计算错误';
  }
};

// 格式化时间范围
const formatTimeRange = (startTime: string, endTime: string) => {
  const start = startTime ? formatDateTime(startTime) : '未开始';
  const end = endTime ? formatDateTime(endTime) : '进行中';
  
  if (!endTime) {
    return start;
  }
  
  return `${start} 至 ${end}`;
};

// 获取任务中的处理流程
const getProcesses = (task: any) => {
  if (!task.details) return {};
  
  return {
    odm_process: task.details.odm_process || { status: '等待开始' },
    tif_process: task.details.tif_process || { status: '等待开始' },
    geoserver_publish: task.details.geoserver_publish || { status: '等待开始' },
    map_config: task.details.map_config || { status: '等待开始' }
  };
};

// 获取流程的显示名称
const getProcessLabel = (processName: string) => {
  const processLabels: Record<string, string> = {
    odm_process: '影像拼接',
    tif_process: '影像预处理',
    geoserver_publish: '发布影像',
    map_config: '写入地图配置'
  };
  
  return processLabels[processName] || processName;
};

// 获取任务状态对应的标签类型
const getStatusTagType = (status: string) => {
  if (getDisplayStatus(status) === '进行中') {
    return 'primary'; // 进行中状态统一为蓝色
  }
  
  switch (status) {
    case '完成':
      return 'success';
    case '失败':
      return 'danger';
    case '等待开始':
    case '未开始':
      return 'info';
    default:
      return 'primary';
  }
};

// 获取显示的状态文本
const getDisplayStatus = (status: string) => {
  if (status === '完成' || status === '等待开始' || status === '未开始' || status === '失败') {
    return status;
  }
  return '进行中';
};

// 获取状态优先级（用于默认排序）
const getStatusPriority = (status: string) => {
  const displayStatus = getDisplayStatus(status);
  switch (displayStatus) {
    case '进行中': return 1;
    case '未开始': return 2;
    case '完成': return 3;
    case '失败': return 4;
    default: return 5;
  }
};

// 获取任务状态对应的类名
const getTaskStatusClass = (status: string) => {
  if (getDisplayStatus(status) === '进行中') {
    return 'task-status-处理中'; // 进行中状态统一使用处理中的样式类
  }
  return `task-status-${status}`;
};

// 获取状态对应的时间线项类型
const getStatusTimelineItemType = (status: string) => {
  if (getDisplayStatus(status) === '进行中') {
    return 'primary'; // 进行中状态统一为蓝色
  }
  
  switch (status) {
    case '完成':
      return 'success';
    case '失败':
      return 'danger';
    case '等待开始':
    case '未开始':
      return 'info';
    default:
      return '';
  }
};

// 获取状态对应的颜色
const getStatusColor = (status: string) => {
  if (getDisplayStatus(status) === '进行中') {
    return '#409EFF'; // 进行中状态统一为蓝色
  }
  
  switch (status) {
    case '完成':
      return '#67C23A';
    case '失败':
      return '#F56C6C';
    case '等待开始':
    case '未开始':
      return '#909399';
    default:
      return '';
  }
};

// 页面切换处理
const handlePageChange = (page: number) => {
  currentPage.value = page;
};

// 手动刷新
const handleManualRefresh = () => {
  fetchTasks();
  if (autoRefresh.value) {
    startCountdown(); // 手动刷新时重置倒计时
  }
};

// 打开任务详情弹窗
const openTaskDetail = (process: any, processName: string) => {
  if (!process.task_id) {
    ElMessage.warning('当前任务没有可查询的ID');
    return;
  }
  
  currentTaskId.value = process.task_id;
  currentTaskList.value = Array.isArray(process.task_list) ? process.task_list : [process.task_id];
  currentProcessName.value = getProcessLabel(processName);
  currentProcessType.value = processName; // 保存原始处理类型名称
  taskDetailVisible.value = true;
};

// 打开图层预览弹窗
const openMapPreview = (layerId: string) => {
  currentLayerId.value = layerId;
  mapPreviewVisible.value = true;
};

// 打开高级筛选弹窗
const openAdvancedFilter = () => {
  advancedFilterVisible.value = true;
};

// 应用高级筛选
const applyAdvancedFilters = (filters: any) => {
  currentFilterSettings.value = { ...filters };
  currentPage.value = 1; // 筛选后回到第一页
};

// 打开图层比较弹窗
const openMapCompare = () => {
  // 查找第一个完成的任务的图层ID作为初始图层
  const completedTask = tasks.value.find(task => task.status === '完成');
  if (completedTask) {
    selectedCompareLayerId.value = 
      completedTask.details?.geoserver_publish?.layer_name ||
      completedTask.details?.map_config?.layer_id || '';
  }
  
  mapCompareVisible.value = true;
};

// 清除所有筛选条件
const clearAllFilters = () => {
  searchQuery.value = '';
  currentFilterSettings.value = {
    status: [],
    timeRange: null,
    sortField: 'default_priority', // 重置为默认排序
    sortOrder: 'ascending'
  };
  currentPage.value = 1; // 重置到第一页
};

// 打开图层比较弹窗并设置初始图层
const openMapCompareWithLayer = (layerId: string) => {
  selectedCompareLayerId.value = layerId;
  mapCompareVisible.value = true;
};

// 初始化
onMounted(() => {
  fetchTasks();
  if (autoRefresh.value) {
    startAutoRefresh();
    startCountdown(); // 启动倒计时
  }
});

// 组件销毁前清理
onBeforeUnmount(() => {
  stopAutoRefresh();
  stopCountdown();
});
</script>

<style lang="scss" scoped>
.odm-task-monitor {
  padding: 20px;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: #303133;
    }
    
    .actions {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .refresh-info {
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .search-area {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .task-count {
      color: #606266;
      font-size: 14px;
      
      strong {
        color: #409EFF;
      }
      
      .el-tag {
        margin-left: 10px;
      }
    }
  }
  
  .loading-container, .error-container, .empty-container {
    padding: 40px 0;
    text-align: center;
  }
  
  .task-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
    
    .task-card {
      border-left: 4px solid #909399;
      transition: all 0.3s;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
      
      &.task-status-完成 {
        border-left-color: #67C23A;
      }
      
      &.task-status-处理中 {
        border-left-color: #409EFF;
      }
      
      &.task-status-失败 {
        border-left-color: #F56C6C;
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: bold;
          color: #303133;
        }
        
        .title-with-actions {
          display: flex;
          align-items: center;
          gap: 8px;
        }
        
        .card-actions {
          display: flex;
          align-items: center;
          gap: 10px;
        }
        
        .task-status {
          display: flex;
          align-items: center;
          gap: 10px;
          
          .task-duration {
            font-size: 12px;
            color: #909399;
          }
          
          .el-button {
            margin-left: 2px;
          }
        }
      }
      
      .task-info {
        .task-times {
          margin-bottom: 15px;
          color: #606266;
          
          p {
            margin: 5px 0;
            font-size: 14px;
          }
        }
        
        .process-timeline {
          .process-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            
            .process-name {
              font-weight: bold;
              display: flex;
              align-items: center;
              gap: 5px;
              
              .query-task-btn {
                margin-left: 5px;
                padding: 2px;
                font-size: 12px;
              }
            }
            
            .process-status {
              display: flex;
              align-items: center;
              gap: 10px;
              
              .process-duration {
                font-size: 12px;
                color: #909399;
              }
            }
          }
        }
      }
    }
  }
  
  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}

// 批量重置弹窗样式
.batch-reset-dialog {
  height: 70vh;
  display: flex;
  flex-direction: column;

  .filter-section {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 8px;
    flex-shrink: 0;
  }

  .task-list-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;

    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px 0;
      border-bottom: 1px solid #ebeef5;
      flex-shrink: 0;

      .task-count {
        color: #606266;
        font-size: 14px;
      }
    }

    .el-table {
      flex: 1;
    }

    .progress-info {
      span {
        font-size: 12px;
        color: #606266;
        margin-bottom: 4px;
        display: block;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
    flex-shrink: 0;
  }
}
</style>