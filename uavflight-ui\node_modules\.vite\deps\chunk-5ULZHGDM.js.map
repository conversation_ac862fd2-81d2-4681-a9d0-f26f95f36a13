{"version": 3, "sources": ["../../ol/structs/LRUCache.js", "../../ol/TileCache.js", "../../ol/tilegrid.js", "../../ol/source/Tile.js", "../../ol/tileurlfunction.js", "../../ol/source/TileEventType.js", "../../ol/source/UrlTile.js"], "sourcesContent": ["/**\n * @module ol/structs/LRUCache\n */\n\nimport {assert} from '../asserts.js';\n\n/**\n * @typedef {Object} Entry\n * @property {string} key_ Key.\n * @property {Object} newer Newer.\n * @property {Object} older Older.\n * @property {*} value_ Value.\n */\n\n/**\n * @classdesc\n * Implements a Least-Recently-Used cache where the keys do not conflict with\n * Object's properties (e.g. 'hasOwnProperty' is not allowed as a key). Expiring\n * items from the cache is the responsibility of the user.\n *\n * @fires import(\"../events/Event.js\").default\n * @template T\n */\nclass LRUCache {\n  /**\n   * @param {number} [highWaterMark] High water mark.\n   */\n  constructor(highWaterMark) {\n    /**\n     * Desired max cache size after expireCache(). If set to 0, no cache entries\n     * will be pruned at all.\n     * @type {number}\n     */\n    this.highWaterMark = highWaterMark !== undefined ? highWaterMark : 2048;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.count_ = 0;\n\n    /**\n     * @private\n     * @type {!Object<string, Entry>}\n     */\n    this.entries_ = {};\n\n    /**\n     * @private\n     * @type {?Entry}\n     */\n    this.oldest_ = null;\n\n    /**\n     * @private\n     * @type {?Entry}\n     */\n    this.newest_ = null;\n  }\n\n  /**\n   * @return {boolean} Can expire cache.\n   */\n  canExpireCache() {\n    return this.highWaterMark > 0 && this.getCount() > this.highWaterMark;\n  }\n\n  /**\n   * Expire the cache.\n   * @param {!Object<string, boolean>} [keep] Keys to keep. To be implemented by subclasses.\n   */\n  expireCache(keep) {\n    while (this.canExpireCache()) {\n      this.pop();\n    }\n  }\n\n  /**\n   * FIXME empty description for jsdoc\n   */\n  clear() {\n    this.count_ = 0;\n    this.entries_ = {};\n    this.oldest_ = null;\n    this.newest_ = null;\n  }\n\n  /**\n   * @param {string} key Key.\n   * @return {boolean} Contains key.\n   */\n  containsKey(key) {\n    return this.entries_.hasOwnProperty(key);\n  }\n\n  /**\n   * @param {function(T, string, LRUCache<T>): ?} f The function\n   *     to call for every entry from the oldest to the newer. This function takes\n   *     3 arguments (the entry value, the entry key and the LRUCache object).\n   *     The return value is ignored.\n   */\n  forEach(f) {\n    let entry = this.oldest_;\n    while (entry) {\n      f(entry.value_, entry.key_, this);\n      entry = entry.newer;\n    }\n  }\n\n  /**\n   * @param {string} key Key.\n   * @param {*} [options] Options (reserved for subclasses).\n   * @return {T} Value.\n   */\n  get(key, options) {\n    const entry = this.entries_[key];\n    assert(entry !== undefined, 15); // Tried to get a value for a key that does not exist in the cache\n    if (entry === this.newest_) {\n      return entry.value_;\n    }\n    if (entry === this.oldest_) {\n      this.oldest_ = /** @type {Entry} */ (this.oldest_.newer);\n      this.oldest_.older = null;\n    } else {\n      entry.newer.older = entry.older;\n      entry.older.newer = entry.newer;\n    }\n    entry.newer = null;\n    entry.older = this.newest_;\n    this.newest_.newer = entry;\n    this.newest_ = entry;\n    return entry.value_;\n  }\n\n  /**\n   * Remove an entry from the cache.\n   * @param {string} key The entry key.\n   * @return {T} The removed entry.\n   */\n  remove(key) {\n    const entry = this.entries_[key];\n    assert(entry !== undefined, 15); // Tried to get a value for a key that does not exist in the cache\n    if (entry === this.newest_) {\n      this.newest_ = /** @type {Entry} */ (entry.older);\n      if (this.newest_) {\n        this.newest_.newer = null;\n      }\n    } else if (entry === this.oldest_) {\n      this.oldest_ = /** @type {Entry} */ (entry.newer);\n      if (this.oldest_) {\n        this.oldest_.older = null;\n      }\n    } else {\n      entry.newer.older = entry.older;\n      entry.older.newer = entry.newer;\n    }\n    delete this.entries_[key];\n    --this.count_;\n    return entry.value_;\n  }\n\n  /**\n   * @return {number} Count.\n   */\n  getCount() {\n    return this.count_;\n  }\n\n  /**\n   * @return {Array<string>} Keys.\n   */\n  getKeys() {\n    const keys = new Array(this.count_);\n    let i = 0;\n    let entry;\n    for (entry = this.newest_; entry; entry = entry.older) {\n      keys[i++] = entry.key_;\n    }\n    return keys;\n  }\n\n  /**\n   * @return {Array<T>} Values.\n   */\n  getValues() {\n    const values = new Array(this.count_);\n    let i = 0;\n    let entry;\n    for (entry = this.newest_; entry; entry = entry.older) {\n      values[i++] = entry.value_;\n    }\n    return values;\n  }\n\n  /**\n   * @return {T} Last value.\n   */\n  peekLast() {\n    return this.oldest_.value_;\n  }\n\n  /**\n   * @return {string} Last key.\n   */\n  peekLastKey() {\n    return this.oldest_.key_;\n  }\n\n  /**\n   * Get the key of the newest item in the cache.  Throws if the cache is empty.\n   * @return {string} The newest key.\n   */\n  peekFirstKey() {\n    return this.newest_.key_;\n  }\n\n  /**\n   * Return an entry without updating least recently used time.\n   * @param {string} key Key.\n   * @return {T} Value.\n   */\n  peek(key) {\n    if (!this.containsKey(key)) {\n      return undefined;\n    }\n    return this.entries_[key].value_;\n  }\n\n  /**\n   * @return {T} value Value.\n   */\n  pop() {\n    const entry = this.oldest_;\n    delete this.entries_[entry.key_];\n    if (entry.newer) {\n      entry.newer.older = null;\n    }\n    this.oldest_ = /** @type {Entry} */ (entry.newer);\n    if (!this.oldest_) {\n      this.newest_ = null;\n    }\n    --this.count_;\n    return entry.value_;\n  }\n\n  /**\n   * @param {string} key Key.\n   * @param {T} value Value.\n   */\n  replace(key, value) {\n    this.get(key); // update `newest_`\n    this.entries_[key].value_ = value;\n  }\n\n  /**\n   * @param {string} key Key.\n   * @param {T} value Value.\n   */\n  set(key, value) {\n    assert(!(key in this.entries_), 16); // Tried to set a value for a key that is used already\n    const entry = {\n      key_: key,\n      newer: null,\n      older: this.newest_,\n      value_: value,\n    };\n    if (!this.newest_) {\n      this.oldest_ = entry;\n    } else {\n      this.newest_.newer = entry;\n    }\n    this.newest_ = entry;\n    this.entries_[key] = entry;\n    ++this.count_;\n  }\n\n  /**\n   * Set a maximum number of entries for the cache.\n   * @param {number} size Cache size.\n   * @api\n   */\n  setSize(size) {\n    this.highWaterMark = size;\n  }\n}\n\nexport default LRUCache;\n", "/**\n * @module ol/TileCache\n */\nimport LRUCache from './structs/LRUCache.js';\nimport {fromKey, getKey} from './tilecoord.js';\n\nclass TileCache extends LRUCache {\n  clear() {\n    while (this.getCount() > 0) {\n      this.pop().release();\n    }\n    super.clear();\n  }\n\n  /**\n   * @param {!Object<string, boolean>} usedTiles Used tiles.\n   */\n  expireCache(usedTiles) {\n    while (this.canExpireCache()) {\n      const tile = this.peekLast();\n      if (tile.getKey() in usedTiles) {\n        break;\n      } else {\n        this.pop().release();\n      }\n    }\n  }\n\n  /**\n   * Prune all tiles from the cache that don't have the same z as the newest tile.\n   */\n  pruneExceptNewestZ() {\n    if (this.getCount() === 0) {\n      return;\n    }\n    const key = this.peekFirstKey();\n    const tileCoord = fromKey(key);\n    const z = tileCoord[0];\n    this.forEach((tile) => {\n      if (tile.tileCoord[0] !== z) {\n        this.remove(getKey(tile.tileCoord));\n        tile.release();\n      }\n    });\n  }\n}\n\nexport default TileCache;\n", "/**\n * @module ol/tilegrid\n */\nimport TileGrid from './tilegrid/TileGrid.js';\nimport {DEFAULT_MAX_ZOOM, DEFAULT_TILE_SIZE} from './tilegrid/common.js';\nimport {METERS_PER_UNIT, get as getProjection} from './proj.js';\nimport {\n  containsCoordinate,\n  createOrUpdate,\n  getCorner,\n  getHeight,\n  getWidth,\n} from './extent.js';\nimport {toSize} from './size.js';\n\n/**\n * @param {import(\"./proj/Projection.js\").default} projection Projection.\n * @return {!TileGrid} Default tile grid for the\n * passed projection.\n */\nexport function getForProjection(projection) {\n  let tileGrid = projection.getDefaultTileGrid();\n  if (!tileGrid) {\n    tileGrid = createForProjection(projection);\n    projection.setDefaultTileGrid(tileGrid);\n  }\n  return tileGrid;\n}\n\n/**\n * @param {TileGrid} tileGrid Tile grid.\n * @param {import(\"./tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n * @param {import(\"./proj/Projection.js\").default} projection Projection.\n * @return {import(\"./tilecoord.js\").TileCoord} Tile coordinate.\n */\nexport function wrapX(tileGrid, tileCoord, projection) {\n  const z = tileCoord[0];\n  const center = tileGrid.getTileCoordCenter(tileCoord);\n  const projectionExtent = extentFromProjection(projection);\n  if (!containsCoordinate(projectionExtent, center)) {\n    const worldWidth = getWidth(projectionExtent);\n    const worldsAway = Math.ceil(\n      (projectionExtent[0] - center[0]) / worldWidth\n    );\n    center[0] += worldWidth * worldsAway;\n    return tileGrid.getTileCoordForCoordAndZ(center, z);\n  }\n  return tileCoord;\n}\n\n/**\n * @param {import(\"./extent.js\").Extent} extent Extent.\n * @param {number} [maxZoom] Maximum zoom level (default is\n *     DEFAULT_MAX_ZOOM).\n * @param {number|import(\"./size.js\").Size} [tileSize] Tile size (default uses\n *     DEFAULT_TILE_SIZE).\n * @param {import(\"./extent.js\").Corner} [corner] Extent corner (default is `'top-left'`).\n * @return {!TileGrid} TileGrid instance.\n */\nexport function createForExtent(extent, maxZoom, tileSize, corner) {\n  corner = corner !== undefined ? corner : 'top-left';\n\n  const resolutions = resolutionsFromExtent(extent, maxZoom, tileSize);\n\n  return new TileGrid({\n    extent: extent,\n    origin: getCorner(extent, corner),\n    resolutions: resolutions,\n    tileSize: tileSize,\n  });\n}\n\n/**\n * @typedef {Object} XYZOptions\n * @property {import(\"./extent.js\").Extent} [extent] Extent for the tile grid. The origin for an XYZ tile grid is the\n * top-left corner of the extent. If `maxResolution` is not provided the zero level of the grid is defined by the resolution\n * at which one tile fits in the provided extent. If not provided, the extent of the EPSG:3857 projection is used.\n * @property {number} [maxResolution] Resolution at level zero.\n * @property {number} [maxZoom] Maximum zoom. The default is `42`. This determines the number of levels\n * in the grid set. For example, a `maxZoom` of 21 means there are 22 levels in the grid set.\n * @property {number} [minZoom=0] Minimum zoom.\n * @property {number|import(\"./size.js\").Size} [tileSize=[256, 256]] Tile size in pixels.\n */\n\n/**\n * Creates a tile grid with a standard XYZ tiling scheme.\n * @param {XYZOptions} [options] Tile grid options.\n * @return {!TileGrid} Tile grid instance.\n * @api\n */\nexport function createXYZ(options) {\n  const xyzOptions = options || {};\n\n  const extent = xyzOptions.extent || getProjection('EPSG:3857').getExtent();\n\n  const gridOptions = {\n    extent: extent,\n    minZoom: xyzOptions.minZoom,\n    tileSize: xyzOptions.tileSize,\n    resolutions: resolutionsFromExtent(\n      extent,\n      xyzOptions.maxZoom,\n      xyzOptions.tileSize,\n      xyzOptions.maxResolution\n    ),\n  };\n  return new TileGrid(gridOptions);\n}\n\n/**\n * Create a resolutions array from an extent.  A zoom factor of 2 is assumed.\n * @param {import(\"./extent.js\").Extent} extent Extent.\n * @param {number} [maxZoom] Maximum zoom level (default is\n *     DEFAULT_MAX_ZOOM).\n * @param {number|import(\"./size.js\").Size} [tileSize] Tile size (default uses\n *     DEFAULT_TILE_SIZE).\n * @param {number} [maxResolution] Resolution at level zero.\n * @return {!Array<number>} Resolutions array.\n */\nfunction resolutionsFromExtent(extent, maxZoom, tileSize, maxResolution) {\n  maxZoom = maxZoom !== undefined ? maxZoom : DEFAULT_MAX_ZOOM;\n  tileSize = toSize(tileSize !== undefined ? tileSize : DEFAULT_TILE_SIZE);\n\n  const height = getHeight(extent);\n  const width = getWidth(extent);\n\n  maxResolution =\n    maxResolution > 0\n      ? maxResolution\n      : Math.max(width / tileSize[0], height / tileSize[1]);\n\n  const length = maxZoom + 1;\n  const resolutions = new Array(length);\n  for (let z = 0; z < length; ++z) {\n    resolutions[z] = maxResolution / Math.pow(2, z);\n  }\n  return resolutions;\n}\n\n/**\n * @param {import(\"./proj.js\").ProjectionLike} projection Projection.\n * @param {number} [maxZoom] Maximum zoom level (default is\n *     DEFAULT_MAX_ZOOM).\n * @param {number|import(\"./size.js\").Size} [tileSize] Tile size (default uses\n *     DEFAULT_TILE_SIZE).\n * @param {import(\"./extent.js\").Corner} [corner] Extent corner (default is `'top-left'`).\n * @return {!TileGrid} TileGrid instance.\n */\nexport function createForProjection(projection, maxZoom, tileSize, corner) {\n  const extent = extentFromProjection(projection);\n  return createForExtent(extent, maxZoom, tileSize, corner);\n}\n\n/**\n * Generate a tile grid extent from a projection.  If the projection has an\n * extent, it is used.  If not, a global extent is assumed.\n * @param {import(\"./proj.js\").ProjectionLike} projection Projection.\n * @return {import(\"./extent.js\").Extent} Extent.\n */\nexport function extentFromProjection(projection) {\n  projection = getProjection(projection);\n  let extent = projection.getExtent();\n  if (!extent) {\n    const half =\n      (180 * METERS_PER_UNIT.degrees) / projection.getMetersPerUnit();\n    extent = createOrUpdate(-half, -half, half, half);\n  }\n  return extent;\n}\n", "/**\n * @module ol/source/Tile\n */\nimport Event from '../events/Event.js';\nimport Source from './Source.js';\nimport TileCache from '../TileCache.js';\nimport TileState from '../TileState.js';\nimport {abstract} from '../util.js';\nimport {assert} from '../asserts.js';\nimport {equivalent} from '../proj.js';\nimport {getKeyZXY, withinExtentAndZ} from '../tilecoord.js';\nimport {\n  getForProjection as getTileGridForProjection,\n  wrapX,\n} from '../tilegrid.js';\nimport {scale as scaleSize, toSize} from '../size.js';\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types, import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<import(\"./TileEventType\").TileSourceEventTypes, TileSourceEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types|\n *     import(\"./TileEventType\").TileSourceEventTypes, Return>} TileSourceOnSignature\n */\n\n/**\n * @typedef {Object} Options\n * @property {import(\"./Source.js\").AttributionLike} [attributions] Attributions.\n * @property {boolean} [attributionsCollapsible=true] Attributions are collapsible.\n * @property {number} [cacheSize] CacheSize.\n * @property {boolean} [opaque=false] Whether the layer is opaque.\n * @property {number} [tilePixelRatio] TilePixelRatio.\n * @property {import(\"../proj.js\").ProjectionLike} [projection] Projection.\n * @property {import(\"./Source.js\").State} [state] State.\n * @property {import(\"../tilegrid/TileGrid.js\").default} [tileGrid] TileGrid.\n * @property {boolean} [wrapX=false] WrapX.\n * @property {number} [transition] Transition.\n * @property {string} [key] Key.\n * @property {number|import(\"../array.js\").NearestDirectionFunction} [zDirection=0] ZDirection.\n * @property {boolean} [interpolate=false] Use interpolated values when resampling.  By default,\n * the nearest neighbor is used when resampling.\n */\n\n/**\n * @classdesc\n * Abstract base class; normally only used for creating subclasses and not\n * instantiated in apps.\n * Base class for sources providing images divided into a tile grid.\n * @abstract\n * @api\n */\nclass TileSource extends Source {\n  /**\n   * @param {Options} options SourceTile source options.\n   */\n  constructor(options) {\n    super({\n      attributions: options.attributions,\n      attributionsCollapsible: options.attributionsCollapsible,\n      projection: options.projection,\n      state: options.state,\n      wrapX: options.wrapX,\n      interpolate: options.interpolate,\n    });\n\n    /***\n     * @type {TileSourceOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {TileSourceOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {TileSourceOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.opaque_ = options.opaque !== undefined ? options.opaque : false;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.tilePixelRatio_ =\n      options.tilePixelRatio !== undefined ? options.tilePixelRatio : 1;\n\n    /**\n     * @type {import(\"../tilegrid/TileGrid.js\").default|null}\n     */\n    this.tileGrid = options.tileGrid !== undefined ? options.tileGrid : null;\n\n    const tileSize = [256, 256];\n    if (this.tileGrid) {\n      toSize(this.tileGrid.getTileSize(this.tileGrid.getMinZoom()), tileSize);\n    }\n\n    /**\n     * @protected\n     * @type {import(\"../TileCache.js\").default}\n     */\n    this.tileCache = new TileCache(options.cacheSize || 0);\n\n    /**\n     * @protected\n     * @type {import(\"../size.js\").Size}\n     */\n    this.tmpSize = [0, 0];\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.key_ = options.key || '';\n\n    /**\n     * @protected\n     * @type {import(\"../Tile.js\").Options}\n     */\n    this.tileOptions = {\n      transition: options.transition,\n      interpolate: options.interpolate,\n    };\n\n    /**\n     * zDirection hint, read by the renderer. Indicates which resolution should be used\n     * by a renderer if the views resolution does not match any resolution of the tile source.\n     * If 0, the nearest resolution will be used. If 1, the nearest lower resolution\n     * will be used. If -1, the nearest higher resolution will be used.\n     * @type {number|import(\"../array.js\").NearestDirectionFunction}\n     */\n    this.zDirection = options.zDirection ? options.zDirection : 0;\n  }\n\n  /**\n   * @return {boolean} Can expire cache.\n   */\n  canExpireCache() {\n    return this.tileCache.canExpireCache();\n  }\n\n  /**\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @param {!Object<string, boolean>} usedTiles Used tiles.\n   */\n  expireCache(projection, usedTiles) {\n    const tileCache = this.getTileCacheForProjection(projection);\n    if (tileCache) {\n      tileCache.expireCache(usedTiles);\n    }\n  }\n\n  /**\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @param {number} z Zoom level.\n   * @param {import(\"../TileRange.js\").default} tileRange Tile range.\n   * @param {function(import(\"../Tile.js\").default):(boolean|void)} callback Called with each\n   *     loaded tile.  If the callback returns `false`, the tile will not be\n   *     considered loaded.\n   * @return {boolean} The tile range is fully covered with loaded tiles.\n   */\n  forEachLoadedTile(projection, z, tileRange, callback) {\n    const tileCache = this.getTileCacheForProjection(projection);\n    if (!tileCache) {\n      return false;\n    }\n\n    let covered = true;\n    let tile, tileCoordKey, loaded;\n    for (let x = tileRange.minX; x <= tileRange.maxX; ++x) {\n      for (let y = tileRange.minY; y <= tileRange.maxY; ++y) {\n        tileCoordKey = getKeyZXY(z, x, y);\n        loaded = false;\n        if (tileCache.containsKey(tileCoordKey)) {\n          tile = /** @type {!import(\"../Tile.js\").default} */ (\n            tileCache.get(tileCoordKey)\n          );\n          loaded = tile.getState() === TileState.LOADED;\n          if (loaded) {\n            loaded = callback(tile) !== false;\n          }\n        }\n        if (!loaded) {\n          covered = false;\n        }\n      }\n    }\n    return covered;\n  }\n\n  /**\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {number} Gutter.\n   */\n  getGutterForProjection(projection) {\n    return 0;\n  }\n\n  /**\n   * Return the key to be used for all tiles in the source.\n   * @return {string} The key for all tiles.\n   */\n  getKey() {\n    return this.key_;\n  }\n\n  /**\n   * Set the value to be used as the key for all tiles in the source.\n   * @param {string} key The key for tiles.\n   * @protected\n   */\n  setKey(key) {\n    if (this.key_ !== key) {\n      this.key_ = key;\n      this.changed();\n    }\n  }\n\n  /**\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {boolean} Opaque.\n   */\n  getOpaque(projection) {\n    return this.opaque_;\n  }\n\n  /**\n   * @param {import(\"../proj/Projection\").default} [projection] Projection.\n   * @return {Array<number>|null} Resolutions.\n   */\n  getResolutions(projection) {\n    const tileGrid = projection\n      ? this.getTileGridForProjection(projection)\n      : this.tileGrid;\n    if (!tileGrid) {\n      return null;\n    }\n    return tileGrid.getResolutions();\n  }\n\n  /**\n   * @abstract\n   * @param {number} z Tile coordinate z.\n   * @param {number} x Tile coordinate x.\n   * @param {number} y Tile coordinate y.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {!import(\"../Tile.js\").default} Tile.\n   */\n  getTile(z, x, y, pixelRatio, projection) {\n    return abstract();\n  }\n\n  /**\n   * Return the tile grid of the tile source.\n   * @return {import(\"../tilegrid/TileGrid.js\").default|null} Tile grid.\n   * @api\n   */\n  getTileGrid() {\n    return this.tileGrid;\n  }\n\n  /**\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {!import(\"../tilegrid/TileGrid.js\").default} Tile grid.\n   */\n  getTileGridForProjection(projection) {\n    if (!this.tileGrid) {\n      return getTileGridForProjection(projection);\n    }\n    return this.tileGrid;\n  }\n\n  /**\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {import(\"../TileCache.js\").default} Tile cache.\n   * @protected\n   */\n  getTileCacheForProjection(projection) {\n    const sourceProjection = this.getProjection();\n    assert(\n      sourceProjection === null || equivalent(sourceProjection, projection),\n      68 // A VectorTile source can only be rendered if it has a projection compatible with the view projection.\n    );\n    return this.tileCache;\n  }\n\n  /**\n   * Get the tile pixel ratio for this source. Subclasses may override this\n   * method, which is meant to return a supported pixel ratio that matches the\n   * provided `pixelRatio` as close as possible.\n   * @param {number} pixelRatio Pixel ratio.\n   * @return {number} Tile pixel ratio.\n   */\n  getTilePixelRatio(pixelRatio) {\n    return this.tilePixelRatio_;\n  }\n\n  /**\n   * @param {number} z Z.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {import(\"../size.js\").Size} Tile size.\n   */\n  getTilePixelSize(z, pixelRatio, projection) {\n    const tileGrid = this.getTileGridForProjection(projection);\n    const tilePixelRatio = this.getTilePixelRatio(pixelRatio);\n    const tileSize = toSize(tileGrid.getTileSize(z), this.tmpSize);\n    if (tilePixelRatio == 1) {\n      return tileSize;\n    }\n    return scaleSize(tileSize, tilePixelRatio, this.tmpSize);\n  }\n\n  /**\n   * Returns a tile coordinate wrapped around the x-axis. When the tile coordinate\n   * is outside the resolution and extent range of the tile grid, `null` will be\n   * returned.\n   * @param {import(\"../tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n   * @param {import(\"../proj/Projection.js\").default} [projection] Projection.\n   * @return {import(\"../tilecoord.js\").TileCoord} Tile coordinate to be passed to the tileUrlFunction or\n   *     null if no tile URL should be created for the passed `tileCoord`.\n   */\n  getTileCoordForTileUrlFunction(tileCoord, projection) {\n    projection = projection !== undefined ? projection : this.getProjection();\n    const tileGrid = this.getTileGridForProjection(projection);\n    if (this.getWrapX() && projection.isGlobal()) {\n      tileCoord = wrapX(tileGrid, tileCoord, projection);\n    }\n    return withinExtentAndZ(tileCoord, tileGrid) ? tileCoord : null;\n  }\n\n  /**\n   * Remove all cached tiles from the source. The next render cycle will fetch new tiles.\n   * @api\n   */\n  clear() {\n    this.tileCache.clear();\n  }\n\n  refresh() {\n    this.clear();\n    super.refresh();\n  }\n\n  /**\n   * Increases the cache size if needed\n   * @param {number} tileCount Minimum number of tiles needed.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   */\n  updateCacheSize(tileCount, projection) {\n    const tileCache = this.getTileCacheForProjection(projection);\n    if (tileCount > tileCache.highWaterMark) {\n      tileCache.highWaterMark = tileCount;\n    }\n  }\n\n  /**\n   * Marks a tile coord as being used, without triggering a load.\n   * @abstract\n   * @param {number} z Tile coordinate z.\n   * @param {number} x Tile coordinate x.\n   * @param {number} y Tile coordinate y.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   */\n  useTile(z, x, y, projection) {}\n}\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/source/Tile~TileSource} instances are instances of this\n * type.\n */\nexport class TileSourceEvent extends Event {\n  /**\n   * @param {string} type Type.\n   * @param {import(\"../Tile.js\").default} tile The tile.\n   */\n  constructor(type, tile) {\n    super(type);\n\n    /**\n     * The tile related to the event.\n     * @type {import(\"../Tile.js\").default}\n     * @api\n     */\n    this.tile = tile;\n  }\n}\n\nexport default TileSource;\n", "/**\n * @module ol/tileurlfunction\n */\nimport {assert} from './asserts.js';\nimport {modulo} from './math.js';\nimport {hash as tileCoordHash} from './tilecoord.js';\n\n/**\n * @param {string} template Template.\n * @param {import(\"./tilegrid/TileGrid.js\").default} tileGrid Tile grid.\n * @return {import(\"./Tile.js\").UrlFunction} Tile URL function.\n */\nexport function createFromTemplate(template, tileGrid) {\n  const zRegEx = /\\{z\\}/g;\n  const xRegEx = /\\{x\\}/g;\n  const yRegEx = /\\{y\\}/g;\n  const dashYRegEx = /\\{-y\\}/g;\n  return (\n    /**\n     * @param {import(\"./tilecoord.js\").TileCoord} tileCoord Tile Coordinate.\n     * @param {number} pixelRatio Pixel ratio.\n     * @param {import(\"./proj/Projection.js\").default} projection Projection.\n     * @return {string|undefined} Tile URL.\n     */\n    function (tileCoord, pixelRatio, projection) {\n      if (!tileCoord) {\n        return undefined;\n      }\n      return template\n        .replace(zRegEx, tileCoord[0].toString())\n        .replace(xRegEx, tileCoord[1].toString())\n        .replace(yRegEx, tileCoord[2].toString())\n        .replace(dashYRegEx, function () {\n          const z = tileCoord[0];\n          const range = tileGrid.getFullTileRange(z);\n          assert(range, 55); // The {-y} placeholder requires a tile grid with extent\n          const y = range.getHeight() - tileCoord[2] - 1;\n          return y.toString();\n        });\n    }\n  );\n}\n\n/**\n * @param {Array<string>} templates Templates.\n * @param {import(\"./tilegrid/TileGrid.js\").default} tileGrid Tile grid.\n * @return {import(\"./Tile.js\").UrlFunction} Tile URL function.\n */\nexport function createFromTemplates(templates, tileGrid) {\n  const len = templates.length;\n  const tileUrlFunctions = new Array(len);\n  for (let i = 0; i < len; ++i) {\n    tileUrlFunctions[i] = createFromTemplate(templates[i], tileGrid);\n  }\n  return createFromTileUrlFunctions(tileUrlFunctions);\n}\n\n/**\n * @param {Array<import(\"./Tile.js\").UrlFunction>} tileUrlFunctions Tile URL Functions.\n * @return {import(\"./Tile.js\").UrlFunction} Tile URL function.\n */\nexport function createFromTileUrlFunctions(tileUrlFunctions) {\n  if (tileUrlFunctions.length === 1) {\n    return tileUrlFunctions[0];\n  }\n  return (\n    /**\n     * @param {import(\"./tilecoord.js\").TileCoord} tileCoord Tile Coordinate.\n     * @param {number} pixelRatio Pixel ratio.\n     * @param {import(\"./proj/Projection.js\").default} projection Projection.\n     * @return {string|undefined} Tile URL.\n     */\n    function (tileCoord, pixelRatio, projection) {\n      if (!tileCoord) {\n        return undefined;\n      }\n      const h = tileCoordHash(tileCoord);\n      const index = modulo(h, tileUrlFunctions.length);\n      return tileUrlFunctions[index](tileCoord, pixelRatio, projection);\n    }\n  );\n}\n\n/**\n * @param {import(\"./tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n * @param {number} pixelRatio Pixel ratio.\n * @param {import(\"./proj/Projection.js\").default} projection Projection.\n * @return {string|undefined} Tile URL.\n */\nexport function nullTileUrlFunction(tileCoord, pixelRatio, projection) {\n  return undefined;\n}\n\n/**\n * @param {string} url URL.\n * @return {Array<string>} Array of urls.\n */\nexport function expandUrl(url) {\n  const urls = [];\n  let match = /\\{([a-z])-([a-z])\\}/.exec(url);\n  if (match) {\n    // char range\n    const startCharCode = match[1].charCodeAt(0);\n    const stopCharCode = match[2].charCodeAt(0);\n    let charCode;\n    for (charCode = startCharCode; charCode <= stopCharCode; ++charCode) {\n      urls.push(url.replace(match[0], String.fromCharCode(charCode)));\n    }\n    return urls;\n  }\n  match = /\\{(\\d+)-(\\d+)\\}/.exec(url);\n  if (match) {\n    // number range\n    const stop = parseInt(match[2], 10);\n    for (let i = parseInt(match[1], 10); i <= stop; i++) {\n      urls.push(url.replace(match[0], i.toString()));\n    }\n    return urls;\n  }\n  urls.push(url);\n  return urls;\n}\n", "/**\n * @module ol/source/TileEventType\n */\n\n/**\n * @enum {string}\n */\nexport default {\n  /**\n   * Triggered when a tile starts loading.\n   * @event module:ol/source/Tile.TileSourceEvent#tileloadstart\n   * @api\n   */\n  TILELOADSTART: 'tileloadstart',\n\n  /**\n   * Triggered when a tile finishes loading, either when its data is loaded,\n   * or when loading was aborted because the tile is no longer needed.\n   * @event module:ol/source/Tile.TileSourceEvent#tileloadend\n   * @api\n   */\n  TILELOADEND: 'tileloadend',\n\n  /**\n   * Triggered if tile loading results in an error. Note that this is not the\n   * right place to re-fetch tiles. See {@link module:ol/ImageTile~ImageTile#load}\n   * for details.\n   * @event module:ol/source/Tile.TileSourceEvent#tileloaderror\n   * @api\n   */\n  TILELOADERROR: 'tileloaderror',\n};\n\n/**\n * @typedef {'tileloadstart'|'tileloadend'|'tileloaderror'} TileSourceEventTypes\n */\n", "/**\n * @module ol/source/UrlTile\n */\nimport TileEventType from './TileEventType.js';\nimport TileSource, {TileSourceEvent} from './Tile.js';\nimport TileState from '../TileState.js';\nimport {createFromTemplates, expandUrl} from '../tileurlfunction.js';\nimport {getKeyZXY} from '../tilecoord.js';\nimport {getUid} from '../util.js';\n\n/**\n * @typedef {Object} Options\n * @property {import(\"./Source.js\").AttributionLike} [attributions] Attributions.\n * @property {boolean} [attributionsCollapsible=true] Attributions are collapsible.\n * @property {number} [cacheSize] Cache size.\n * @property {boolean} [opaque=false] Whether the layer is opaque.\n * @property {import(\"../proj.js\").ProjectionLike} [projection] Projection.\n * @property {import(\"./Source.js\").State} [state] State.\n * @property {import(\"../tilegrid/TileGrid.js\").default} [tileGrid] TileGrid.\n * @property {import(\"../Tile.js\").LoadFunction} tileLoadFunction TileLoadFunction.\n * @property {number} [tilePixelRatio] TilePixelRatio.\n * @property {import(\"../Tile.js\").UrlFunction} [tileUrlFunction] TileUrlFunction.\n * @property {string} [url] Url.\n * @property {Array<string>} [urls] Urls.\n * @property {boolean} [wrapX=true] WrapX.\n * @property {number} [transition] Transition.\n * @property {string} [key] Key.\n * @property {number|import(\"../array.js\").NearestDirectionFunction} [zDirection=0] ZDirection.\n * @property {boolean} [interpolate=false] Use interpolated values when resampling.  By default,\n * the nearest neighbor is used when resampling.\n */\n\n/**\n * @classdesc\n * Base class for sources providing tiles divided into a tile grid over http.\n *\n * @fires import(\"./Tile.js\").TileSourceEvent\n */\nclass UrlTile extends TileSource {\n  /**\n   * @param {Options} options Image tile options.\n   */\n  constructor(options) {\n    super({\n      attributions: options.attributions,\n      cacheSize: options.cacheSize,\n      opaque: options.opaque,\n      projection: options.projection,\n      state: options.state,\n      tileGrid: options.tileGrid,\n      tilePixelRatio: options.tilePixelRatio,\n      wrapX: options.wrapX,\n      transition: options.transition,\n      interpolate: options.interpolate,\n      key: options.key,\n      attributionsCollapsible: options.attributionsCollapsible,\n      zDirection: options.zDirection,\n    });\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.generateTileUrlFunction_ =\n      this.tileUrlFunction === UrlTile.prototype.tileUrlFunction;\n\n    /**\n     * @protected\n     * @type {import(\"../Tile.js\").LoadFunction}\n     */\n    this.tileLoadFunction = options.tileLoadFunction;\n\n    if (options.tileUrlFunction) {\n      this.tileUrlFunction = options.tileUrlFunction;\n    }\n\n    /**\n     * @protected\n     * @type {!Array<string>|null}\n     */\n    this.urls = null;\n\n    if (options.urls) {\n      this.setUrls(options.urls);\n    } else if (options.url) {\n      this.setUrl(options.url);\n    }\n\n    /**\n     * @private\n     * @type {!Object<string, boolean>}\n     */\n    this.tileLoadingKeys_ = {};\n  }\n\n  /**\n   * Return the tile load function of the source.\n   * @return {import(\"../Tile.js\").LoadFunction} TileLoadFunction\n   * @api\n   */\n  getTileLoadFunction() {\n    return this.tileLoadFunction;\n  }\n\n  /**\n   * Return the tile URL function of the source.\n   * @return {import(\"../Tile.js\").UrlFunction} TileUrlFunction\n   * @api\n   */\n  getTileUrlFunction() {\n    return Object.getPrototypeOf(this).tileUrlFunction === this.tileUrlFunction\n      ? this.tileUrlFunction.bind(this)\n      : this.tileUrlFunction;\n  }\n\n  /**\n   * Return the URLs used for this source.\n   * When a tileUrlFunction is used instead of url or urls,\n   * null will be returned.\n   * @return {!Array<string>|null} URLs.\n   * @api\n   */\n  getUrls() {\n    return this.urls;\n  }\n\n  /**\n   * Handle tile change events.\n   * @param {import(\"../events/Event.js\").default} event Event.\n   * @protected\n   */\n  handleTileChange(event) {\n    const tile = /** @type {import(\"../Tile.js\").default} */ (event.target);\n    const uid = getUid(tile);\n    const tileState = tile.getState();\n    let type;\n    if (tileState == TileState.LOADING) {\n      this.tileLoadingKeys_[uid] = true;\n      type = TileEventType.TILELOADSTART;\n    } else if (uid in this.tileLoadingKeys_) {\n      delete this.tileLoadingKeys_[uid];\n      type =\n        tileState == TileState.ERROR\n          ? TileEventType.TILELOADERROR\n          : tileState == TileState.LOADED\n          ? TileEventType.TILELOADEND\n          : undefined;\n    }\n    if (type != undefined) {\n      this.dispatchEvent(new TileSourceEvent(type, tile));\n    }\n  }\n\n  /**\n   * Set the tile load function of the source.\n   * @param {import(\"../Tile.js\").LoadFunction} tileLoadFunction Tile load function.\n   * @api\n   */\n  setTileLoadFunction(tileLoadFunction) {\n    this.tileCache.clear();\n    this.tileLoadFunction = tileLoadFunction;\n    this.changed();\n  }\n\n  /**\n   * Set the tile URL function of the source.\n   * @param {import(\"../Tile.js\").UrlFunction} tileUrlFunction Tile URL function.\n   * @param {string} [key] Optional new tile key for the source.\n   * @api\n   */\n  setTileUrlFunction(tileUrlFunction, key) {\n    this.tileUrlFunction = tileUrlFunction;\n    this.tileCache.pruneExceptNewestZ();\n    if (typeof key !== 'undefined') {\n      this.setKey(key);\n    } else {\n      this.changed();\n    }\n  }\n\n  /**\n   * Set the URL to use for requests.\n   * @param {string} url URL.\n   * @api\n   */\n  setUrl(url) {\n    const urls = expandUrl(url);\n    this.urls = urls;\n    this.setUrls(urls);\n  }\n\n  /**\n   * Set the URLs to use for requests.\n   * @param {Array<string>} urls URLs.\n   * @api\n   */\n  setUrls(urls) {\n    this.urls = urls;\n    const key = urls.join('\\n');\n    if (this.generateTileUrlFunction_) {\n      this.setTileUrlFunction(createFromTemplates(urls, this.tileGrid), key);\n    } else {\n      this.setKey(key);\n    }\n  }\n\n  /**\n   * @param {import(\"../tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {string|undefined} Tile URL.\n   */\n  tileUrlFunction(tileCoord, pixelRatio, projection) {\n    return undefined;\n  }\n\n  /**\n   * Marks a tile coord as being used, without triggering a load.\n   * @param {number} z Tile coordinate z.\n   * @param {number} x Tile coordinate x.\n   * @param {number} y Tile coordinate y.\n   */\n  useTile(z, x, y) {\n    const tileCoordKey = getKeyZXY(z, x, y);\n    if (this.tileCache.containsKey(tileCoordKey)) {\n      this.tileCache.get(tileCoordKey);\n    }\n  }\n}\n\nexport default UrlTile;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,IAAM,WAAN,MAAe;AAAA;AAAA;AAAA;AAAA,EAIb,YAAY,eAAe;AAMzB,SAAK,gBAAgB,kBAAkB,SAAY,gBAAgB;AAMnE,SAAK,SAAS;AAMd,SAAK,WAAW,CAAC;AAMjB,SAAK,UAAU;AAMf,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,WAAO,KAAK,gBAAgB,KAAK,KAAK,SAAS,IAAI,KAAK;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,MAAM;AAChB,WAAO,KAAK,eAAe,GAAG;AAC5B,WAAK,IAAI;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,SAAK,SAAS;AACd,SAAK,WAAW,CAAC;AACjB,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,KAAK;AACf,WAAO,KAAK,SAAS,eAAe,GAAG;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,GAAG;AACT,QAAI,QAAQ,KAAK;AACjB,WAAO,OAAO;AACZ,QAAE,MAAM,QAAQ,MAAM,MAAM,IAAI;AAChC,cAAQ,MAAM;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,KAAK,SAAS;AAChB,UAAM,QAAQ,KAAK,SAAS,GAAG;AAC/B,WAAO,UAAU,QAAW,EAAE;AAC9B,QAAI,UAAU,KAAK,SAAS;AAC1B,aAAO,MAAM;AAAA,IACf;AACA,QAAI,UAAU,KAAK,SAAS;AAC1B,WAAK;AAAA,MAAgC,KAAK,QAAQ;AAClD,WAAK,QAAQ,QAAQ;AAAA,IACvB,OAAO;AACL,YAAM,MAAM,QAAQ,MAAM;AAC1B,YAAM,MAAM,QAAQ,MAAM;AAAA,IAC5B;AACA,UAAM,QAAQ;AACd,UAAM,QAAQ,KAAK;AACnB,SAAK,QAAQ,QAAQ;AACrB,SAAK,UAAU;AACf,WAAO,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,KAAK;AACV,UAAM,QAAQ,KAAK,SAAS,GAAG;AAC/B,WAAO,UAAU,QAAW,EAAE;AAC9B,QAAI,UAAU,KAAK,SAAS;AAC1B,WAAK;AAAA,MAAgC,MAAM;AAC3C,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ,QAAQ;AAAA,MACvB;AAAA,IACF,WAAW,UAAU,KAAK,SAAS;AACjC,WAAK;AAAA,MAAgC,MAAM;AAC3C,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ,QAAQ;AAAA,MACvB;AAAA,IACF,OAAO;AACL,YAAM,MAAM,QAAQ,MAAM;AAC1B,YAAM,MAAM,QAAQ,MAAM;AAAA,IAC5B;AACA,WAAO,KAAK,SAAS,GAAG;AACxB,MAAE,KAAK;AACP,WAAO,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,UAAM,OAAO,IAAI,MAAM,KAAK,MAAM;AAClC,QAAI,IAAI;AACR,QAAI;AACJ,SAAK,QAAQ,KAAK,SAAS,OAAO,QAAQ,MAAM,OAAO;AACrD,WAAK,GAAG,IAAI,MAAM;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,UAAM,SAAS,IAAI,MAAM,KAAK,MAAM;AACpC,QAAI,IAAI;AACR,QAAI;AACJ,SAAK,QAAQ,KAAK,SAAS,OAAO,QAAQ,MAAM,OAAO;AACrD,aAAO,GAAG,IAAI,MAAM;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AACb,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,KAAK;AACR,QAAI,CAAC,KAAK,YAAY,GAAG,GAAG;AAC1B,aAAO;AAAA,IACT;AACA,WAAO,KAAK,SAAS,GAAG,EAAE;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM;AACJ,UAAM,QAAQ,KAAK;AACnB,WAAO,KAAK,SAAS,MAAM,IAAI;AAC/B,QAAI,MAAM,OAAO;AACf,YAAM,MAAM,QAAQ;AAAA,IACtB;AACA,SAAK;AAAA,IAAgC,MAAM;AAC3C,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AAAA,IACjB;AACA,MAAE,KAAK;AACP,WAAO,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,KAAK,OAAO;AAClB,SAAK,IAAI,GAAG;AACZ,SAAK,SAAS,GAAG,EAAE,SAAS;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,KAAK,OAAO;AACd,WAAO,EAAE,OAAO,KAAK,WAAW,EAAE;AAClC,UAAM,QAAQ;AAAA,MACZ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO,KAAK;AAAA,MACZ,QAAQ;AAAA,IACV;AACA,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AAAA,IACjB,OAAO;AACL,WAAK,QAAQ,QAAQ;AAAA,IACvB;AACA,SAAK,UAAU;AACf,SAAK,SAAS,GAAG,IAAI;AACrB,MAAE,KAAK;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,MAAM;AACZ,SAAK,gBAAgB;AAAA,EACvB;AACF;AAEA,IAAO,mBAAQ;;;ACxRf,IAAM,YAAN,cAAwB,iBAAS;AAAA,EAC/B,QAAQ;AACN,WAAO,KAAK,SAAS,IAAI,GAAG;AAC1B,WAAK,IAAI,EAAE,QAAQ;AAAA,IACrB;AACA,UAAM,MAAM;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,WAAW;AACrB,WAAO,KAAK,eAAe,GAAG;AAC5B,YAAM,OAAO,KAAK,SAAS;AAC3B,UAAI,KAAK,OAAO,KAAK,WAAW;AAC9B;AAAA,MACF,OAAO;AACL,aAAK,IAAI,EAAE,QAAQ;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,QAAI,KAAK,SAAS,MAAM,GAAG;AACzB;AAAA,IACF;AACA,UAAM,MAAM,KAAK,aAAa;AAC9B,UAAM,YAAY,QAAQ,GAAG;AAC7B,UAAM,IAAI,UAAU,CAAC;AACrB,SAAK,QAAQ,CAAC,SAAS;AACrB,UAAI,KAAK,UAAU,CAAC,MAAM,GAAG;AAC3B,aAAK,OAAO,OAAO,KAAK,SAAS,CAAC;AAClC,aAAK,QAAQ;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,IAAO,oBAAQ;;;AC3BR,SAAS,iBAAiB,YAAY;AAC3C,MAAI,WAAW,WAAW,mBAAmB;AAC7C,MAAI,CAAC,UAAU;AACb,eAAW,oBAAoB,UAAU;AACzC,eAAW,mBAAmB,QAAQ;AAAA,EACxC;AACA,SAAO;AACT;AAQO,SAAS,MAAM,UAAU,WAAW,YAAY;AACrD,QAAM,IAAI,UAAU,CAAC;AACrB,QAAM,SAAS,SAAS,mBAAmB,SAAS;AACpD,QAAM,mBAAmB,qBAAqB,UAAU;AACxD,MAAI,CAAC,mBAAmB,kBAAkB,MAAM,GAAG;AACjD,UAAM,aAAa,SAAS,gBAAgB;AAC5C,UAAM,aAAa,KAAK;AAAA,OACrB,iBAAiB,CAAC,IAAI,OAAO,CAAC,KAAK;AAAA,IACtC;AACA,WAAO,CAAC,KAAK,aAAa;AAC1B,WAAO,SAAS,yBAAyB,QAAQ,CAAC;AAAA,EACpD;AACA,SAAO;AACT;AAWO,SAAS,gBAAgB,QAAQ,SAAS,UAAU,QAAQ;AACjE,WAAS,WAAW,SAAY,SAAS;AAEzC,QAAM,cAAc,sBAAsB,QAAQ,SAAS,QAAQ;AAEnE,SAAO,IAAI,iBAAS;AAAA,IAClB;AAAA,IACA,QAAQ,UAAU,QAAQ,MAAM;AAAA,IAChC;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAoBO,SAAS,UAAU,SAAS;AACjC,QAAM,aAAa,WAAW,CAAC;AAE/B,QAAM,SAAS,WAAW,UAAU,IAAc,WAAW,EAAE,UAAU;AAEzE,QAAM,cAAc;AAAA,IAClB;AAAA,IACA,SAAS,WAAW;AAAA,IACpB,UAAU,WAAW;AAAA,IACrB,aAAa;AAAA,MACX;AAAA,MACA,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,IACb;AAAA,EACF;AACA,SAAO,IAAI,iBAAS,WAAW;AACjC;AAYA,SAAS,sBAAsB,QAAQ,SAAS,UAAU,eAAe;AACvE,YAAU,YAAY,SAAY,UAAU;AAC5C,aAAW,OAAO,aAAa,SAAY,WAAW,iBAAiB;AAEvE,QAAM,SAAS,UAAU,MAAM;AAC/B,QAAM,QAAQ,SAAS,MAAM;AAE7B,kBACE,gBAAgB,IACZ,gBACA,KAAK,IAAI,QAAQ,SAAS,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC;AAExD,QAAM,SAAS,UAAU;AACzB,QAAM,cAAc,IAAI,MAAM,MAAM;AACpC,WAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,gBAAY,CAAC,IAAI,gBAAgB,KAAK,IAAI,GAAG,CAAC;AAAA,EAChD;AACA,SAAO;AACT;AAWO,SAAS,oBAAoB,YAAY,SAAS,UAAU,QAAQ;AACzE,QAAM,SAAS,qBAAqB,UAAU;AAC9C,SAAO,gBAAgB,QAAQ,SAAS,UAAU,MAAM;AAC1D;AAQO,SAAS,qBAAqB,YAAY;AAC/C,eAAa,IAAc,UAAU;AACrC,MAAI,SAAS,WAAW,UAAU;AAClC,MAAI,CAAC,QAAQ;AACX,UAAM,OACH,MAAM,gBAAgB,UAAW,WAAW,iBAAiB;AAChE,aAAS,eAAe,CAAC,MAAM,CAAC,MAAM,MAAM,IAAI;AAAA,EAClD;AACA,SAAO;AACT;;;ACpHA,IAAM,aAAN,cAAyB,eAAO;AAAA;AAAA;AAAA;AAAA,EAI9B,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ,cAAc,QAAQ;AAAA,MACtB,yBAAyB,QAAQ;AAAA,MACjC,YAAY,QAAQ;AAAA,MACpB,OAAO,QAAQ;AAAA,MACf,OAAO,QAAQ;AAAA,MACf,aAAa,QAAQ;AAAA,IACvB,CAAC;AAKD,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAM/D,SAAK,kBACH,QAAQ,mBAAmB,SAAY,QAAQ,iBAAiB;AAKlE,SAAK,WAAW,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAEpE,UAAM,WAAW,CAAC,KAAK,GAAG;AAC1B,QAAI,KAAK,UAAU;AACjB,aAAO,KAAK,SAAS,YAAY,KAAK,SAAS,WAAW,CAAC,GAAG,QAAQ;AAAA,IACxE;AAMA,SAAK,YAAY,IAAI,kBAAU,QAAQ,aAAa,CAAC;AAMrD,SAAK,UAAU,CAAC,GAAG,CAAC;AAMpB,SAAK,OAAO,QAAQ,OAAO;AAM3B,SAAK,cAAc;AAAA,MACjB,YAAY,QAAQ;AAAA,MACpB,aAAa,QAAQ;AAAA,IACvB;AASA,SAAK,aAAa,QAAQ,aAAa,QAAQ,aAAa;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,WAAO,KAAK,UAAU,eAAe;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,YAAY,WAAW;AACjC,UAAM,YAAY,KAAK,0BAA0B,UAAU;AAC3D,QAAI,WAAW;AACb,gBAAU,YAAY,SAAS;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,kBAAkB,YAAY,GAAG,WAAW,UAAU;AACpD,UAAM,YAAY,KAAK,0BAA0B,UAAU;AAC3D,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AAEA,QAAI,UAAU;AACd,QAAI,MAAM,cAAc;AACxB,aAAS,IAAI,UAAU,MAAM,KAAK,UAAU,MAAM,EAAE,GAAG;AACrD,eAAS,IAAI,UAAU,MAAM,KAAK,UAAU,MAAM,EAAE,GAAG;AACrD,uBAAe,UAAU,GAAG,GAAG,CAAC;AAChC,iBAAS;AACT,YAAI,UAAU,YAAY,YAAY,GAAG;AACvC;AAAA,UACE,UAAU,IAAI,YAAY;AAE5B,mBAAS,KAAK,SAAS,MAAM,kBAAU;AACvC,cAAI,QAAQ;AACV,qBAAS,SAAS,IAAI,MAAM;AAAA,UAC9B;AAAA,QACF;AACA,YAAI,CAAC,QAAQ;AACX,oBAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,YAAY;AACjC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,KAAK;AACV,QAAI,KAAK,SAAS,KAAK;AACrB,WAAK,OAAO;AACZ,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,YAAY;AACpB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,YAAY;AACzB,UAAM,WAAW,aACb,KAAK,yBAAyB,UAAU,IACxC,KAAK;AACT,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,WAAO,SAAS,eAAe;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,QAAQ,GAAG,GAAG,GAAG,YAAY,YAAY;AACvC,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,YAAY;AACnC,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO,iBAAyB,UAAU;AAAA,IAC5C;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,0BAA0B,YAAY;AACpC,UAAM,mBAAmB,KAAK,cAAc;AAC5C;AAAA,MACE,qBAAqB,QAAQ,WAAW,kBAAkB,UAAU;AAAA,MACpE;AAAA;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,kBAAkB,YAAY;AAC5B,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,GAAG,YAAY,YAAY;AAC1C,UAAM,WAAW,KAAK,yBAAyB,UAAU;AACzD,UAAM,iBAAiB,KAAK,kBAAkB,UAAU;AACxD,UAAM,WAAW,OAAO,SAAS,YAAY,CAAC,GAAG,KAAK,OAAO;AAC7D,QAAI,kBAAkB,GAAG;AACvB,aAAO;AAAA,IACT;AACA,WAAO,MAAU,UAAU,gBAAgB,KAAK,OAAO;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,+BAA+B,WAAW,YAAY;AACpD,iBAAa,eAAe,SAAY,aAAa,KAAK,cAAc;AACxE,UAAM,WAAW,KAAK,yBAAyB,UAAU;AACzD,QAAI,KAAK,SAAS,KAAK,WAAW,SAAS,GAAG;AAC5C,kBAAY,MAAM,UAAU,WAAW,UAAU;AAAA,IACnD;AACA,WAAO,iBAAiB,WAAW,QAAQ,IAAI,YAAY;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACN,SAAK,UAAU,MAAM;AAAA,EACvB;AAAA,EAEA,UAAU;AACR,SAAK,MAAM;AACX,UAAM,QAAQ;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,WAAW,YAAY;AACrC,UAAM,YAAY,KAAK,0BAA0B,UAAU;AAC3D,QAAI,YAAY,UAAU,eAAe;AACvC,gBAAU,gBAAgB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QAAQ,GAAG,GAAG,GAAG,YAAY;AAAA,EAAC;AAChC;AAOO,IAAM,kBAAN,cAA8B,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzC,YAAY,MAAM,MAAM;AACtB,UAAM,IAAI;AAOV,SAAK,OAAO;AAAA,EACd;AACF;AAEA,IAAO,eAAQ;;;ACjYR,SAAS,mBAAmB,UAAU,UAAU;AACrD,QAAM,SAAS;AACf,QAAM,SAAS;AACf,QAAM,SAAS;AACf,QAAM,aAAa;AACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOE,SAAU,WAAW,YAAY,YAAY;AAC3C,UAAI,CAAC,WAAW;AACd,eAAO;AAAA,MACT;AACA,aAAO,SACJ,QAAQ,QAAQ,UAAU,CAAC,EAAE,SAAS,CAAC,EACvC,QAAQ,QAAQ,UAAU,CAAC,EAAE,SAAS,CAAC,EACvC,QAAQ,QAAQ,UAAU,CAAC,EAAE,SAAS,CAAC,EACvC,QAAQ,YAAY,WAAY;AAC/B,cAAM,IAAI,UAAU,CAAC;AACrB,cAAM,QAAQ,SAAS,iBAAiB,CAAC;AACzC,eAAO,OAAO,EAAE;AAChB,cAAM,IAAI,MAAM,UAAU,IAAI,UAAU,CAAC,IAAI;AAC7C,eAAO,EAAE,SAAS;AAAA,MACpB,CAAC;AAAA,IACL;AAAA;AAEJ;AAOO,SAAS,oBAAoB,WAAW,UAAU;AACvD,QAAM,MAAM,UAAU;AACtB,QAAM,mBAAmB,IAAI,MAAM,GAAG;AACtC,WAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,qBAAiB,CAAC,IAAI,mBAAmB,UAAU,CAAC,GAAG,QAAQ;AAAA,EACjE;AACA,SAAO,2BAA2B,gBAAgB;AACpD;AAMO,SAAS,2BAA2B,kBAAkB;AAC3D,MAAI,iBAAiB,WAAW,GAAG;AACjC,WAAO,iBAAiB,CAAC;AAAA,EAC3B;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOE,SAAU,WAAW,YAAY,YAAY;AAC3C,UAAI,CAAC,WAAW;AACd,eAAO;AAAA,MACT;AACA,YAAM,IAAI,KAAc,SAAS;AACjC,YAAM,QAAQ,OAAO,GAAG,iBAAiB,MAAM;AAC/C,aAAO,iBAAiB,KAAK,EAAE,WAAW,YAAY,UAAU;AAAA,IAClE;AAAA;AAEJ;AAQO,SAAS,oBAAoB,WAAW,YAAY,YAAY;AACrE,SAAO;AACT;AAMO,SAAS,UAAU,KAAK;AAC7B,QAAM,OAAO,CAAC;AACd,MAAI,QAAQ,sBAAsB,KAAK,GAAG;AAC1C,MAAI,OAAO;AAET,UAAM,gBAAgB,MAAM,CAAC,EAAE,WAAW,CAAC;AAC3C,UAAM,eAAe,MAAM,CAAC,EAAE,WAAW,CAAC;AAC1C,QAAI;AACJ,SAAK,WAAW,eAAe,YAAY,cAAc,EAAE,UAAU;AACnE,WAAK,KAAK,IAAI,QAAQ,MAAM,CAAC,GAAG,OAAO,aAAa,QAAQ,CAAC,CAAC;AAAA,IAChE;AACA,WAAO;AAAA,EACT;AACA,UAAQ,kBAAkB,KAAK,GAAG;AAClC,MAAI,OAAO;AAET,UAAM,OAAO,SAAS,MAAM,CAAC,GAAG,EAAE;AAClC,aAAS,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE,GAAG,KAAK,MAAM,KAAK;AACnD,WAAK,KAAK,IAAI,QAAQ,MAAM,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AAAA,IAC/C;AACA,WAAO;AAAA,EACT;AACA,OAAK,KAAK,GAAG;AACb,SAAO;AACT;;;AClHA,IAAO,wBAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQf,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASb,eAAe;AACjB;;;ACOA,IAAM,UAAN,MAAM,iBAAgB,aAAW;AAAA;AAAA;AAAA;AAAA,EAI/B,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ,cAAc,QAAQ;AAAA,MACtB,WAAW,QAAQ;AAAA,MACnB,QAAQ,QAAQ;AAAA,MAChB,YAAY,QAAQ;AAAA,MACpB,OAAO,QAAQ;AAAA,MACf,UAAU,QAAQ;AAAA,MAClB,gBAAgB,QAAQ;AAAA,MACxB,OAAO,QAAQ;AAAA,MACf,YAAY,QAAQ;AAAA,MACpB,aAAa,QAAQ;AAAA,MACrB,KAAK,QAAQ;AAAA,MACb,yBAAyB,QAAQ;AAAA,MACjC,YAAY,QAAQ;AAAA,IACtB,CAAC;AAMD,SAAK,2BACH,KAAK,oBAAoB,SAAQ,UAAU;AAM7C,SAAK,mBAAmB,QAAQ;AAEhC,QAAI,QAAQ,iBAAiB;AAC3B,WAAK,kBAAkB,QAAQ;AAAA,IACjC;AAMA,SAAK,OAAO;AAEZ,QAAI,QAAQ,MAAM;AAChB,WAAK,QAAQ,QAAQ,IAAI;AAAA,IAC3B,WAAW,QAAQ,KAAK;AACtB,WAAK,OAAO,QAAQ,GAAG;AAAA,IACzB;AAMA,SAAK,mBAAmB,CAAC;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,qBAAqB;AACnB,WAAO,OAAO,eAAe,IAAI,EAAE,oBAAoB,KAAK,kBACxD,KAAK,gBAAgB,KAAK,IAAI,IAC9B,KAAK;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,OAAO;AACtB,UAAM;AAAA;AAAA,MAAoD,MAAM;AAAA;AAChE,UAAM,MAAM,OAAO,IAAI;AACvB,UAAM,YAAY,KAAK,SAAS;AAChC,QAAI;AACJ,QAAI,aAAa,kBAAU,SAAS;AAClC,WAAK,iBAAiB,GAAG,IAAI;AAC7B,aAAO,sBAAc;AAAA,IACvB,WAAW,OAAO,KAAK,kBAAkB;AACvC,aAAO,KAAK,iBAAiB,GAAG;AAChC,aACE,aAAa,kBAAU,QACnB,sBAAc,gBACd,aAAa,kBAAU,SACvB,sBAAc,cACd;AAAA,IACR;AACA,QAAI,QAAQ,QAAW;AACrB,WAAK,cAAc,IAAI,gBAAgB,MAAM,IAAI,CAAC;AAAA,IACpD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,kBAAkB;AACpC,SAAK,UAAU,MAAM;AACrB,SAAK,mBAAmB;AACxB,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB,iBAAiB,KAAK;AACvC,SAAK,kBAAkB;AACvB,SAAK,UAAU,mBAAmB;AAClC,QAAI,OAAO,QAAQ,aAAa;AAC9B,WAAK,OAAO,GAAG;AAAA,IACjB,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,KAAK;AACV,UAAM,OAAO,UAAU,GAAG;AAC1B,SAAK,OAAO;AACZ,SAAK,QAAQ,IAAI;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,MAAM;AACZ,SAAK,OAAO;AACZ,UAAM,MAAM,KAAK,KAAK,IAAI;AAC1B,QAAI,KAAK,0BAA0B;AACjC,WAAK,mBAAmB,oBAAoB,MAAM,KAAK,QAAQ,GAAG,GAAG;AAAA,IACvE,OAAO;AACL,WAAK,OAAO,GAAG;AAAA,IACjB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,WAAW,YAAY,YAAY;AACjD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,GAAG,GAAG,GAAG;AACf,UAAM,eAAe,UAAU,GAAG,GAAG,CAAC;AACtC,QAAI,KAAK,UAAU,YAAY,YAAY,GAAG;AAC5C,WAAK,UAAU,IAAI,YAAY;AAAA,IACjC;AAAA,EACF;AACF;AAEA,IAAO,kBAAQ;", "names": []}