<!--
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-06-27 15:30:44
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-07-30 09:42:41
 * @FilePath: \uavflight-ui\src\views\oneMap\index.vue
 * @Description: 一体化地图页面，包含左侧图层管理和右侧高精度图层管理
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="one-map-container">
    <!-- 标题栏 -->
    <div class="title-bar">
      <div class="system-title">
        <div class="decor-line"></div>
        <span class="title-text">扶绥县自然资源"一张图"系统</span>
        <div class="decor-line"></div>
      </div>
    </div>
    
    <!-- 地图容器 -->
    <div id="map" ref="mapContainer"></div>
    
    <!-- 左侧图层管理面板 -->
    <div class="layer-panel" :class="{ 'collapsed': isLayerPanelCollapsed }">
      <!-- 图层管理器组件 -->
      <ol-layer-manager 
        :visible="true"
        position="left"
        @initialized="handleLayerInitialized"
        @toggle-collapse="toggleLayerPanel"
        ref="layerManagerRef"
      />
    </div>
    
    <!-- 左侧面板收缩按钮 (位于左侧面板右侧) -->
    <div class="layer-panel-collapse-btn panel-button-base" 
         :class="{ 'collapsed': isLayerPanelCollapsed }"
         @click="toggleLayerPanel">
      <div class="icon-container">
        <el-icon><ArrowLeft v-if="!isLayerPanelCollapsed" /><ArrowRight v-else /></el-icon>
      </div>
    </div>
    
    <!-- 右侧高精度图层管理面板 -->
    <div class="hd-layer-panel" :class="{ 'collapsed': isHDLayerPanelCollapsed }">
      <!-- 高精度图层管理器组件 -->
      <OlHDLayerManager
        :visible="true"
        position="right"
        @initialized="handleHDLayerInitialized"
        ref="hdLayerManagerRef"
      />
    </div>
    
    <!-- 右侧面板收缩按钮 (位于右侧面板左侧) -->
    <div class="hd-layer-panel-collapse-btn panel-button-base"
         :class="{ 'collapsed': isHDLayerPanelCollapsed }"
         @click="toggleHDLayerPanel">
      <div class="icon-container">
        <el-icon><ArrowRight v-if="!isHDLayerPanelCollapsed" /><ArrowLeft v-else /></el-icon>
      </div>
    </div>
    
    <!-- GIS工具组件 -->
    <GisTools 
      :mapStore="mapStore" 
      @drawStart="handleDrawStart" 
      @drawEnd="handleDrawEnd"
      @attributeInfo="handleAttributeInfo"
      ref="gisToolsRef"
    />
  </div>
</template>

<script setup lang="ts">
// Vue相关导入
import { onMounted, onBeforeUnmount, ref, watch } from 'vue';

// 第三方库
import 'ol/ol.css';
import { 
  ArrowRight, 
  ArrowLeft, 
  ArrowUp, 
  ArrowDown
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { Vector as VectorSource } from 'ol/source';
import { Vector as VectorLayer } from 'ol/layer';
import { unByKey } from 'ol/Observable';

// 本地组件和状态管理
import { useOLMapStore } from '/@/stores/olMapStore';
import { OlHDLayerManager, OlLayerManager, GisTools } from '/@/components';
import { DrawStartEvent, DrawEndEvent, AttributeInfoEvent } from '/@/components/GisTools/types';

// ===== 状态与引用 =====

// 地图相关
const mapStore = useOLMapStore();
const mapContainer = ref<HTMLElement | null>(null);

// 组件引用
const layerManagerRef = ref<any>(null);
const hdLayerManagerRef = ref<any>(null);
const layerQueryRef = ref<any>(null);
const gisToolsRef = ref<any>(null);

// 面板状态
const isLayerPanelCollapsed = ref(false);
const isHDLayerPanelCollapsed = ref(false);

// ===== 事件处理函数 =====

/**
 * 切换左侧面板收缩状态
 */
function toggleLayerPanel() {
  isLayerPanelCollapsed.value = !isLayerPanelCollapsed.value;
  console.log(`左侧图层面板${isLayerPanelCollapsed.value ? '已收缩' : '已展开'}`);
}

/**
 * 切换右侧高精度图层面板收缩状态
 */
function toggleHDLayerPanel() {
  isHDLayerPanelCollapsed.value = !isHDLayerPanelCollapsed.value;
  console.log(`高精度图层面板${isHDLayerPanelCollapsed.value ? '已收缩' : '已展开'}`);
}

/**
 * 处理图层初始化完成事件
 */
function handleLayerInitialized(count: number) {
  console.log(`图层管理器已初始化，共加载 ${count} 个图层`);
}

/**
 * 处理高精度图层初始化完成事件
 */
function handleHDLayerInitialized(count: number) {
  console.log(`高精度图层管理器已初始化，共加载 ${count} 个高精度图层`);
}

/**
 * 处理绘制开始事件
 */
function handleDrawStart(event: DrawStartEvent) {
  console.log(`开始${event.tool}绘制`);
}

/**
 * 处理绘制结束事件
 */
function handleDrawEnd(event: DrawEndEvent) {
  console.log(`结束${event.tool}绘制`);
  // 这里可以处理绘制结果，例如保存到数据库或显示属性编辑对话框
}

/**
 * 处理要素属性信息事件
 */
function handleAttributeInfo(event: AttributeInfoEvent) {
  // 将属性信息传递给高精度图层面板
  if (hdLayerManagerRef.value) {
    // 确保右侧面板是可见的
    if (isHDLayerPanelCollapsed.value) {
      isHDLayerPanelCollapsed.value = false;
      console.log('展开高精度图层面板以显示属性信息');
    }
    
    // 显示属性面板
    hdLayerManagerRef.value.showAttributesPanel(
      event.attributes,
      event.layerId,
      `${event.layerName}-属性信息`
    );
    
    // 记录日志
    console.log(`在高精度图层面板中显示图层 ${event.layerName} 的属性信息`);
  } else {
    ElMessage.warning('高精度图层面板未初始化，无法显示属性信息');
  }
}

/**
 * 关闭要素属性面板（已不再使用，由olHDLayerManager内部处理）
 */
function closeAttributesPanel() {
  // 当属性面板在olHDLayerManager中显示时，由其内部处理关闭逻辑
  if (hdLayerManagerRef.value) {
    hdLayerManagerRef.value.closeAttributesPanel();
  }
}

/**
 * 处理窗口大小变化
 */
function handleResize() {
  if (mapStore.map?.map) {
    mapStore.map.map.updateSize();
  }
}

// ===== 生命周期钩子 =====

onMounted(async () => {
  console.log('oneMap组件挂载，准备初始化地图...');
  
  // 初始化地图
  if (mapContainer.value) {
    mapStore.initMap(mapContainer.value);
    
    // 监听地图渲染完成事件
    if (mapStore.map?.map) {
      mapStore.map.map.once('rendercomplete', () => {
        console.log('地图渲染完成');
      });
    }
  }
  
  // 确保初始状态下面板是展开的
  isLayerPanelCollapsed.value = false;
  isHDLayerPanelCollapsed.value = false;
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  // 清理资源
  if (mapStore.map?.map) {
    mapStore.map.map.setTarget(undefined);
  }
  
  // 清除相机监控
  mapStore.clearCameraMonitor();
  
  // 移除事件监听
  window.removeEventListener('resize', handleResize);
});
</script>

<style lang="scss">
@import "../../theme/common/panel-buttons.scss";
/* ===== 容器样式 ===== */
.one-map-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden; /* 防止溢出 */
}

#map {
  position: absolute;
  top: 10%; /* 从标题栏下方开始 */
  left: 0;
  width: 100%;
  height: 90%; /* 剩余90%的高度 */
}

/* ===== 标题栏样式 ===== */
.title-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 10%; /* 标题栏高度 */
  background-color: rgba(2, 49, 52, 0.8); /* 深绿色背景，与图片一致 */
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: none;
  border-bottom: 1px solid #0a3b32;
}

/* 系统标题 */
.system-title {
  display: flex;
  align-items: center;
  gap: 20px;
}

.title-text {
  font-size: 28px;
  color: white;
  letter-spacing: 2px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'DingTalk', sans-serif;
}

.decor-line {
  width: 60px;
  height: 3px;
  background-color: #2a7729; /* 绿色装饰线 */
}

/* ===== 左侧图层面板样式 ===== */
.layer-panel {
  position: absolute;
  left: 0;
  top: 11%; /* 标题栏下方 */
  width: 360px;
  height: 89%; /* 地图容器下方 */
  background-color: rgba(2, 49, 52, 0.85);
  z-index: 3;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  color: white;
  overflow: hidden;
  transition: all 0.3s ease;
}

.layer-panel.collapsed {
  transform: translateX(-100%);
  box-shadow: none;
}

/* 移除旧的抽屉控制按钮样式和备用按钮样式 */
.drawer-control, 
.panel-control-button {
  display: none;
}

/* ===== 左侧面板收缩按钮 ===== */
.layer-panel-collapse-btn {
  position: absolute;
  left: 360px;
  top: calc(10% + (90vh / 2)); /* 调整位置，考虑标题栏高度 */
  transform: translateY(-50%);
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 4;
  transition: all 0.3s ease;
}

.layer-panel-collapse-btn.collapsed {
  left: 0;
}

/* ===== 右侧高精度图层面板样式 ===== */
.hd-layer-panel {
  position: absolute;
  right: 0;
  top: calc(18% + 7px); /* 标题栏下方，略微下移 */
  bottom: 10px; /* 底部按钮上方 */
  width: 450px;
  height: calc(82% - 7px); /* 考虑到顶部和底部的间距 */
  background-color: rgba(15, 21, 32, 0.9);
  backdrop-filter: blur(10px);
  z-index: 99;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  color: white;
  overflow: hidden;
  transition: all 0.3s ease;
  border-radius: 4px 0 0 4px;
  border-left: 2px solid rgba(64, 158, 255, 0.5);
}

.hd-layer-panel.collapsed {
  transform: translateX(100%);
  box-shadow: none;
}

/* ===== 右侧面板收缩按钮 ===== */
.hd-layer-panel-collapse-btn {
  position: absolute;
  right: 450px;
  top: calc(10% + ((90% - 14px) / 2)); /* 调整位置，考虑标题栏高度和面板高度 */
  transform: translateY(-50%);
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 100;
  transition: all 0.3s ease;
}

.hd-layer-panel-collapse-btn.collapsed {
  right: 0;
}

/* ===== 通用按钮样式 ===== */
/* 按钮基础样式 */
.panel-button-base {
  position: absolute;
  width: 24px;
  height: 80px;
  background-color: rgba(16, 64, 70, 0.95);
  z-index: 100;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.panel-button-base:hover {
  background-color: rgba(22, 80, 85, 1);
}

.panel-button-base .icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  width: 100%;
  height: 40px;
}

.panel-button-base .el-icon {
  font-size: 16px;
  background-color: rgba(59, 179, 59, 0.9);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
}

/* ===== 图标样式已移至通用按钮样式 ===== */

/* 定义过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from {
  opacity: 0;
}

.fade-leave-to {
  opacity: 0;
}

/* ===== 测量工具样式 ===== */
.ol-tooltip {
  position: relative;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  color: white;
  padding: 4px 8px;
  white-space: nowrap;
  font-size: 12px;
  pointer-events: none;
}

.ol-tooltip-measure {
  opacity: 1;
  font-weight: bold;
}

.ol-tooltip-static {
  background-color: rgba(64, 158, 255, 0.7);
  color: white;
  border: 1px solid white;
}

.ol-tooltip-measure:before,
.ol-tooltip-static:before {
  border-top: 6px solid rgba(0, 0, 0, 0.7);
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
  content: "";
  position: absolute;
  bottom: -6px;
  margin-left: -7px;
  left: 50%;
}

.ol-tooltip-static:before {
  border-top-color: rgba(64, 158, 255, 0.7);
}

/* ===== GIS工具组件样式 ===== */
:deep(.gis-tools) {
  top: 12% !important; /* 从标题栏下方开始，稍微下移一点 */
  z-index: 98;
}

/* 调整右侧控制面板中的内容，保持与顶部的间距 */
:deep(.gis-tools .tool-panel) {
  max-height: 88vh;
}
</style> 