<!--
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-06-27 15:30:44
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-07-30 09:42:41
 * @FilePath: \uavflight-ui\src\views\oneMap\index.vue
 * @Description: 一体化地图页面，包含左侧图层管理和右侧高精度图层管理
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="one-map-container">
    <!-- 标题栏 -->
    <div class="title-bar">
      <div class="left-title">
        <img src="/@/assets/pigx-app.png" alt="Logo" class="logo-image" />
        <span class="title-text">扶绥县自然资源"一张图"系统</span>
      </div>

      <!-- 右侧按钮组 -->
      <div class="right-buttons">
        <el-tooltip content="数据大屏" placement="bottom" effect="dark">
          <el-button @click="goToBigScreen" class="nav-button">数据大屏</el-button>
        </el-tooltip>
        <el-tooltip content="首页" placement="bottom" effect="dark">
          <el-button @click="goToHomeFun" class="nav-button">首页</el-button>
        </el-tooltip>
      </div>
    </div>
    
    <!-- 地图容器 -->
    <div id="map" ref="mapContainer"></div>
    
    <!-- 左侧图层管理面板 -->
    <div class="layer-panel" :class="{ 'collapsed': isLayerPanelCollapsed }">
      <!-- 图层管理器组件 -->
      <ol-layer-manager 
        :visible="true"
        position="left"
        @initialized="handleLayerInitialized"
        @toggle-collapse="toggleLayerPanel"
        ref="layerManagerRef"
      />
    </div>
    
    <!-- 左侧面板收缩按钮 (位于左侧面板右侧) -->
    <div class="layer-panel-collapse-btn panel-button-base" 
         :class="{ 'collapsed': isLayerPanelCollapsed }"
         @click="toggleLayerPanel">
      <div class="icon-container">
        <el-icon><ArrowLeft v-if="!isLayerPanelCollapsed" /><ArrowRight v-else /></el-icon>
      </div>
    </div>
    
    <!-- 右侧高精度图层管理面板 -->
    <div class="hd-layer-panel" :class="{ 'collapsed': isHDLayerPanelCollapsed }">
      <!-- 高精度图层管理器组件 -->
      <OlHDLayerManager
        :visible="true"
        position="right"
        @initialized="handleHDLayerInitialized"
        ref="hdLayerManagerRef"
      />
    </div>
    
    <!-- 右侧面板收缩按钮 (位于右侧面板左侧) -->
    <div class="hd-layer-panel-collapse-btn panel-button-base"
         :class="{ 'collapsed': isHDLayerPanelCollapsed }"
         @click="toggleHDLayerPanel">
      <div class="icon-container">
        <el-icon><ArrowRight v-if="!isHDLayerPanelCollapsed" /><ArrowLeft v-else /></el-icon>
      </div>
    </div>
    
    <!-- GIS工具组件 -->
    <GisTools 
      :mapStore="mapStore" 
      @drawStart="handleDrawStart" 
      @drawEnd="handleDrawEnd"
      @attributeInfo="handleAttributeInfo"
      ref="gisToolsRef"
    />
  </div>
</template>

<script setup lang="ts">
// Vue相关导入
import { onMounted, onBeforeUnmount, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

// 第三方库
import 'ol/ol.css';
import { 
  ArrowRight, 
  ArrowLeft, 
  ArrowUp, 
  ArrowDown
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { Vector as VectorSource } from 'ol/source';
import { Vector as VectorLayer } from 'ol/layer';
import { unByKey } from 'ol/Observable';

// 本地组件和状态管理
import { useOLMapStore } from '/@/stores/olMapStore';
import { OlHDLayerManager, OlLayerManager, GisTools } from '/@/components';
import { DrawStartEvent, DrawEndEvent, AttributeInfoEvent } from '/@/components/GisTools/types';

// ===== 状态与引用 =====

// 路由
const router = useRouter();

// 地图相关
const mapStore = useOLMapStore();
const mapContainer = ref<HTMLElement | null>(null);

// 组件引用
const layerManagerRef = ref<any>(null);
const hdLayerManagerRef = ref<any>(null);
const layerQueryRef = ref<any>(null);
const gisToolsRef = ref<any>(null);

// 面板状态
const isLayerPanelCollapsed = ref(false);
const isHDLayerPanelCollapsed = ref(false);

// ===== 事件处理函数 =====

/**
 * 切换左侧面板收缩状态
 */
function toggleLayerPanel() {
  isLayerPanelCollapsed.value = !isLayerPanelCollapsed.value;
  console.log(`左侧图层面板${isLayerPanelCollapsed.value ? '已收缩' : '已展开'}`);
}

/**
 * 切换右侧高精度图层面板收缩状态
 */
function toggleHDLayerPanel() {
  isHDLayerPanelCollapsed.value = !isHDLayerPanelCollapsed.value;
  console.log(`高精度图层面板${isHDLayerPanelCollapsed.value ? '已收缩' : '已展开'}`);
}

/**
 * 跳转到数据大屏页面
 */
function goToBigScreen() {
  // 在新窗口打开数据大屏页面
  const routeUrl = router.resolve({
    path: '/bigScreen'
  });
  window.open(routeUrl.href, '_blank');
}

const goToHomeFun = async () => {
	router.push('/home');
};

/**
 * 处理图层初始化完成事件
 */
function handleLayerInitialized(count: number) {
  console.log(`图层管理器已初始化，共加载 ${count} 个图层`);
}

/**
 * 处理高精度图层初始化完成事件
 */
function handleHDLayerInitialized(count: number) {
  console.log(`高精度图层管理器已初始化，共加载 ${count} 个高精度图层`);
}

/**
 * 处理绘制开始事件
 */
function handleDrawStart(event: DrawStartEvent) {
  console.log(`开始${event.tool}绘制`);
}

/**
 * 处理绘制结束事件
 */
function handleDrawEnd(event: DrawEndEvent) {
  console.log(`结束${event.tool}绘制`);
  // 这里可以处理绘制结果，例如保存到数据库或显示属性编辑对话框
}

/**
 * 处理要素属性信息事件
 */
function handleAttributeInfo(event: AttributeInfoEvent) {
  // 将属性信息传递给高精度图层面板
  if (hdLayerManagerRef.value) {
    // 确保右侧面板是可见的
    if (isHDLayerPanelCollapsed.value) {
      isHDLayerPanelCollapsed.value = false;
      console.log('展开高精度图层面板以显示属性信息');
    }
    
    // 显示属性面板
    hdLayerManagerRef.value.showAttributesPanel(
      event.attributes,
      event.layerId,
      `${event.layerName}-属性信息`
    );
    
    // 记录日志
    console.log(`在高精度图层面板中显示图层 ${event.layerName} 的属性信息`);
  } else {
    ElMessage.warning('高精度图层面板未初始化，无法显示属性信息');
  }
}

/**
 * 关闭要素属性面板（已不再使用，由olHDLayerManager内部处理）
 */
function closeAttributesPanel() {
  // 当属性面板在olHDLayerManager中显示时，由其内部处理关闭逻辑
  if (hdLayerManagerRef.value) {
    hdLayerManagerRef.value.closeAttributesPanel();
  }
}

/**
 * 处理窗口大小变化
 */
function handleResize() {
  if (mapStore.map?.map) {
    mapStore.map.map.updateSize();
  }
}

// ===== 生命周期钩子 =====

onMounted(async () => {
  console.log('oneMap组件挂载，准备初始化地图...');
  
  // 初始化地图
  if (mapContainer.value) {
    mapStore.initMap(mapContainer.value);
    
    // 监听地图渲染完成事件
    if (mapStore.map?.map) {
      mapStore.map.map.once('rendercomplete', () => {
        console.log('地图渲染完成');
      });
    }
  }
  
  // 确保初始状态下面板是展开的
  isLayerPanelCollapsed.value = false;
  isHDLayerPanelCollapsed.value = false;
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  // 清理资源
  if (mapStore.map?.map) {
    mapStore.map.map.setTarget(undefined);
  }
  
  // 清除相机监控
  mapStore.clearCameraMonitor();
  
  // 移除事件监听
  window.removeEventListener('resize', handleResize);
});
</script>

<style lang="scss">
@import "../../theme/common/panel-buttons.scss";
/* ===== 容器样式 ===== */
.one-map-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden; /* 防止溢出 */
}

#map {
  position: absolute;
  top: 0; /* 从顶部开始，标题栏悬浮在上方 */
  left: 0;
  width: 100%;
  height: 100%; /* 占满全屏 */
}

/* ===== 标题栏样式 ===== */
.title-bar {
  position: absolute;
  top: 0; /* 铺满顶部 */
  left: 0; /* 铺满左侧 */
  right: 0; /* 铺满右侧 */
  width: 100%; /* 铺满宽度 */
  height: 10%; /* 标题栏高度 */
  background-color: rgba(15, 21, 32, 0.8);/* 使用图层管理的背景色，50%透明度 */
  /* 移除毛玻璃效果 */
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* 只保留底部边框 */
  overflow: hidden;
}

/* 添加地图主题的装饰效果 */
.title-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    radial-gradient(circle at 80% 50%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    radial-gradient(circle at 40% 20%, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    radial-gradient(circle at 60% 80%, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 50px 50px, 60px 60px, 40px 40px, 70px 70px;
  animation: mapPattern 20s linear infinite;
  pointer-events: none;
}

@keyframes mapPattern {
  0% { transform: translateX(0); }
  100% { transform: translateX(100px); }
}

/* 左侧标题 */
.left-title {
  display: flex;
  align-items: center;
  gap: 15px; /* logo和文字之间的间距 */
}

/* Logo样式 */
.logo-image {
  height: 40px; /* 设置logo高度 */
  width: auto; /* 保持宽高比 */
  object-fit: contain; /* 保持图片比例 */
  padding: 4px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%
  );
  border-radius: 12px;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 6px rgba(13, 71, 161, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 3;
}

/* Logo悬停效果 */
.logo-image:hover {
  transform: scale(1.05);
  box-shadow:
    0 6px 16px rgba(0, 0, 0, 0.2),
    0 3px 8px rgba(13, 71, 161, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
}

/* 标题文字样式 */
.title-text {
  font-size: 28px;
  color: #ffffff;
  letter-spacing: 2px;
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.3),
    2px 2px 4px rgba(13, 71, 161, 0.8),
    0 0 20px rgba(30, 136, 229, 0.2);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'DingTalk', sans-serif;
  // font-weight: 600;
  position: relative;
  z-index: 2;
}

/* 右侧按钮组 */
.right-buttons {
  display: flex;
  gap: 15px;
  position: relative;
  z-index: 2;
}

/* 导航按钮样式 */
.nav-button {
  font-family: 'DingTalk', sans-serif;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 50%,
    rgba(255, 255, 255, 0.95) 100%
  );
  color: #000000;
  border: none; /* 去掉边框 */
  padding: 12px 24px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 16px;
  // font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(15px);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.15),
    0 2px 8px rgba(13, 71, 161, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  text-shadow: none;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(21, 101, 192, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.nav-button:hover {
  background: rgba(64, 158, 255, 0.8);/* 浅蓝透明色 */
  color: #ffffff;
  border: none; /* 去掉边框 */
  transform: translateY(-2px) scale(1.03);
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.2),
    0 4px 15px rgba(135, 206, 250, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.nav-button:hover::before {
  left: 100%;
}

.nav-button:active {
  background: rgba(13, 132, 207, 0.342); /* active时浅蓝透明色更深 */
  transform: translateY(-1px) scale(1.01);
  border: none; /* 去掉边框 */
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(135, 206, 250, 0.4);
}

/* ===== 左侧图层面板样式 ===== */
.layer-panel {
  position: absolute;
  left: 5px; /* 悬浮在底图之上，距离左边20px */
  top: calc(11%); /* 标题栏下方，增加一些间距 */
  width: 360px;
  height: calc(89% - 10px); /* 调整高度以适应新的定位 */
  background-color: rgba(15, 21, 32, 0.5); /* 50%透明度 */
  /* 移除毛玻璃效果 */
  z-index: 3;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4); /* 增强阴影效果 */
  border-radius: 4px; /* 添加圆角 */
  border: 1px solid rgba(255, 255, 255, 0.1); /* 添加边框 */
  display: flex;
  flex-direction: column;
  color: white;
  overflow: hidden;
  transition: all 0.3s ease;
}

.layer-panel.collapsed {
  transform: translateX(-380px); /* 向左移动面板宽度+边距 */
  box-shadow: none;
}

/* 移除旧的抽屉控制按钮样式和备用按钮样式 */
.drawer-control, 
.panel-control-button {
  display: none;
}

/* ===== 左侧面板收缩按钮 ===== */
.layer-panel-collapse-btn {
  position: absolute;
  left: 365px;
  top: calc(10% + (90vh / 2)); /* 调整位置，考虑标题栏高度 */
  transform: translateY(-50%);
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 4;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.layer-panel-collapse-btn.collapsed {
  left: 10px; /* 悬浮在左侧，距离边缘10px */
  width: 50px; /* 圆形悬浮球宽度 */
  height: 50px; /* 圆形悬浮球高度 */
  border-radius: 50%; /* 变成圆形 */
  background: linear-gradient(135deg,
    rgba(15, 21, 32, 0.9) 0%,
    rgba(15, 21, 32, 0.8) 50%,
    rgba(15, 21, 32, 0.9) 100%
  ); /* 与GIS工具栏一致的渐变背景 */
  border: 2px solid rgba(15, 21, 32, 0.6);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 4px 15px rgba(15, 21, 32, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* ===== 右侧高精度图层面板样式 ===== */
.hd-layer-panel {
  position: absolute;
  right: 10px; /* 悬浮在页面上，距离右边10px */
  top: calc(10% + 80px); /* GIS工具条下方，保持10px间隔 */
  bottom: 10px; /* 距离底部20px */
  width: 455px;
  height: calc(90% - 90px); /* 调整高度：从GIS工具条下方到底部，减去间距 */
  background-color: rgba(15, 21, 32, 0.5) !important; /* 强制设置与图层管理一致的背景色 */
  backdrop-filter: none; /* 移除毛玻璃效果 */
  z-index: 99;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4) !important; /* 强制设置与图层管理一致的阴影 */
  display: flex;
  flex-direction: column;
  color: white;
  overflow: hidden;
  transition: all 0.3s ease;
  border-radius: 4px !important; /* 强制设置与图层管理一致的圆角 */
  border: 1px solid rgba(255, 255, 255, 0.1) !important; /* 强制设置与图层管理一致的边框 */
}

.hd-layer-panel.collapsed {
  transform: translateX(465px); /* 向右移动面板宽度+边距 */
  box-shadow: none;
}

/* ===== 右侧面板收缩按钮 ===== */
.hd-layer-panel-collapse-btn {
  position: absolute;
  right: 465px; /* 面板宽度455px + 边距10px */
  top: 50%; /* 垂直居中 */
  transform: translateY(-50%);
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 100;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.hd-layer-panel-collapse-btn.collapsed {
  right: 10px; /* 悬浮在右侧 */
  width: 50px; /* 圆形悬浮球宽度 */
  height: 50px; /* 圆形悬浮球高度 */
  border-radius: 50%; /* 变成圆形 */
  background: linear-gradient(135deg,
    rgba(15, 21, 32, 0.9) 0%,
    rgba(15, 21, 32, 0.8) 50%,
    rgba(15, 21, 32, 0.9) 100%
  ); /* 与GIS工具栏一致的渐变背景 */
  border: 2px solid rgba(15, 21, 32, 0.6);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 4px 15px rgba(15, 21, 32, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* ===== 收缩状态下图标样式 ===== */
.layer-panel-collapse-btn.collapsed .el-icon,
.hd-layer-panel-collapse-btn.collapsed .el-icon {
  font-size: 18px;
  color: rgba(0, 0, 0, 0.8); /* 黑色箭头 */
  background-color: rgba(255, 255, 255, 0.95); /* 白色圆圈背景 */
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow:
    0 3px 8px rgba(0, 0, 0, 0.25),
    0 2px 4px rgba(15, 21, 32, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 1);
}

.layer-panel-collapse-btn.collapsed:hover .el-icon,
.hd-layer-panel-collapse-btn.collapsed:hover .el-icon {
  background-color: rgba(255, 255, 255, 1); /* hover时白色圆圈背景 */
  color: rgba(0, 0, 0, 1); /* hover时黑色箭头 */
  transform: scale(1.15);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.3),
    0 3px 6px rgba(15, 21, 32, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* ===== 强制快拼图层样式统一 ===== */
:deep(.hd-layer-manager-container) {
  background-color: rgba(15, 21, 32, 0.5) !important;
  backdrop-filter: none !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4) !important;
  border-radius: 4px !important;
}

/* ===== 收缩悬浮球hover效果 ===== */
.layer-panel-collapse-btn.collapsed:hover,
.hd-layer-panel-collapse-btn.collapsed:hover {
  background: linear-gradient(135deg,
    rgba(15, 21, 32, 1) 0%,
    rgba(15, 21, 32, 0.9) 50%,
    rgba(15, 21, 32, 1) 100%
  ); /* hover时渐变背景 */
  box-shadow:
    0 12px 35px rgba(0, 0, 0, 0.4),
    0 6px 20px rgba(15, 21, 32, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* ===== 通用收缩按钮样式 ===== */
/* 按钮基础样式 */
.panel-button-base {
  position: absolute;
  width: 24px;
  height: 80px;
  background-color: rgba(15, 21, 32, 0.9); /* 统一背景色 */
  z-index: 100;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(15, 21, 32, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(15, 21, 32, 0.6);
}

.panel-button-base:hover {
  background-color: rgba(15, 21, 32, 1); /* hover时不透明 */
  transform: translateY(-50%) scale(1.05);
  box-shadow:
    0 6px 18px rgba(0, 0, 0, 0.4),
    0 3px 12px rgba(15, 21, 32, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 收缩状态下的悬浮球样式 */
.panel-button-base.collapsed {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg,
    rgba(15, 21, 32, 0.9) 0%,
    rgba(15, 21, 32, 0.8) 50%,
    rgba(15, 21, 32, 0.9) 100%
  ); /* 与GIS工具栏一致的渐变背景 */
  /* 移除毛玻璃效果 */
  border: 2px solid rgba(15, 21, 32, 0.6);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 4px 15px rgba(15, 21, 32, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  /* 移除浮动动画 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.panel-button-base.collapsed:hover {
  transform: translateY(-50%) scale(1.15);
  background: linear-gradient(135deg,
    rgba(15, 21, 32, 1) 0%,
    rgba(15, 21, 32, 0.9) 50%,
    rgba(15, 21, 32, 1) 100%
  ); /* hover时渐变背景 */
  box-shadow:
    0 12px 35px rgba(0, 0, 0, 0.4),
    0 6px 20px rgba(15, 21, 32, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 收缩状态下图标样式 */
.panel-button-base.collapsed .el-icon {
  font-size: 18px;
  color: rgba(0, 0, 0, 0.8); /* 黑色箭头 */
  background-color: rgba(255, 255, 255, 0.95); /* 白色圆圈背景 */
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow:
    0 3px 8px rgba(0, 0, 0, 0.25),
    0 2px 4px rgba(15, 21, 32, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 1);
}

.panel-button-base.collapsed:hover .el-icon {
  background-color: rgba(255, 255, 255, 1); /* hover时白色圆圈背景 */
  color: rgba(0, 0, 0, 1); /* hover时黑色箭头 */
  transform: scale(1.15);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.3),
    0 3px 6px rgba(15, 21, 32, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

@keyframes universalFloatBounce {
  0%, 100% { transform: translateY(-50%) scale(1); }
  50% { transform: translateY(-55%) scale(1.05); }
}

/* 图标容器样式 */
.panel-button-base .icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  width: 100%;
  height: 40px;
  transition: all 0.3s ease;
}

/* 收缩状态下的图标容器 */
.panel-button-base.collapsed .icon-container {
  width: 100%;
  height: 100%;
}

/* 图标基础样式 */
.panel-button-base .el-icon {
  font-size: 16px;
  background-color: rgba(255, 255, 255, 0.95);
  color: rgba(13, 71, 161, 0.9);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 2px 6px rgba(0, 0, 0, 0.2),
    0 1px 3px rgba(13, 71, 161, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

/* 收缩状态下的图标样式 */
.panel-button-base.collapsed .el-icon {
  font-size: 18px;
  width: 28px;
  height: 28px;
  background-color: rgba(255, 255, 255, 0.98);
  color: rgba(13, 71, 161, 1);
  box-shadow:
    0 3px 8px rgba(0, 0, 0, 0.25),
    0 2px 4px rgba(13, 71, 161, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 1);
}

/* 图标悬停效果 */
.panel-button-base:hover .el-icon {
  background-color: rgba(255, 255, 255, 1);
  color: rgba(21, 101, 192, 1);
  transform: scale(1.1);
  box-shadow:
    0 3px 8px rgba(0, 0, 0, 0.3),
    0 2px 4px rgba(21, 101, 192, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 1);
}

.panel-button-base.collapsed:hover .el-icon {
  transform: scale(1.15);
  color: rgba(30, 136, 229, 1);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.3),
    0 3px 6px rgba(30, 136, 229, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 1);
}

/* ===== 图标样式已移至通用按钮样式 ===== */

/* 定义过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from {
  opacity: 0;
}

.fade-leave-to {
  opacity: 0;
}

/* ===== 测量工具样式 ===== */
.ol-tooltip {
  position: relative;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  color: white;
  padding: 4px 8px;
  white-space: nowrap;
  font-size: 12px;
  pointer-events: none;
}

.ol-tooltip-measure {
  opacity: 1;
  font-weight: bold;
}

.ol-tooltip-static {
  background-color: rgba(64, 158, 255, 0.7);
  color: white;
  border: 1px solid white;
}

.ol-tooltip-measure:before,
.ol-tooltip-static:before {
  border-top: 6px solid rgba(0, 0, 0, 0.7);
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
  content: "";
  position: absolute;
  bottom: -6px;
  margin-left: -7px;
  left: 50%;
}

.ol-tooltip-static:before {
  border-top-color: rgba(64, 158, 255, 0.7);
}

/* ===== GIS工具组件样式 ===== */
/* 移除样式覆盖，让组件自己的悬浮样式生效 */
</style> 