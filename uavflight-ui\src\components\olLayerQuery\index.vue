<!--
 * @file index.vue
 * @description OpenLayers图层查询组件，提供点击地图查询位置图层的功能
 * 
 * 该组件提供以下功能：
 * - 查询位置图层按钮
 * - 地图点击事件处理
 * - 查询结果弹窗展示
 * - 加载选中图层
 * 
 * <AUTHOR>
 * @date 2025-07-12
-->
<template>
  <!-- 查询图层按钮 -->
  <!-- <div class="map-tools">
    <el-tooltip content="查询图层" placement="left">
      <el-button
        class="query-btn"
        type="primary"
        :class="{ 'active-tool': isQueryModeActive }"
        @click="toggleQueryMode"
      >
        查询图层
      </el-button>
    </el-tooltip> -->
    
    

  <!-- 图层查询结果弹窗 -->
  <el-dialog
    v-model="layerQueryDialogVisible"
    title="图层查询结果"
    width="500px"
    :show-close="true"
    :close-on-click-modal="false"
    center
    destroy-on-close
  >
    <div v-if="layerQueryStatus === 'loading'" class="query-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <p>正在查询图层，请稍候...</p>
    </div>
    
    <div v-else-if="layerQueryStatus === 'error'" class="query-error">
      <el-icon><CircleClose /></el-icon>
      <p>查询失败: {{ layerQueryError }}</p>
      <el-button type="primary" @click="retryLayerQuery">重试</el-button>
    </div>
    
    <div v-else-if="layerQueryStatus === 'success'" class="query-result">
      <div v-if="layerQueryResult.count === 0" class="no-layers-found">
        <p>该位置未找到图层</p>
      </div>
      <div v-else class="layers-found">
        <p class="result-summary">共找到 {{ layerQueryResult.count }} 个图层</p>
        
        <el-divider />
        
        <el-table :data="layerQueryResult.layers" style="width: 100%">
          <el-table-column width="80">
            <template #default="scope">
              <!-- 如果图层已加载，显示已加载标记；否则显示复选框 -->
              <el-checkbox 
                v-if="!isLayerLoaded(scope.row.id)" 
                v-model="scope.row.selected" 
              />
              <el-tag v-else size="small" type="success" class="loaded-tag">已加载</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="名称">
            <template #default="scope">
              {{ getLayerName(scope.row.id) }}
            </template>
          </el-table-column>
          <el-table-column label="主题">
            <template #default="scope">
              {{ getLayerTheme(scope.row.id) || '未分类' }}
            </template>
          </el-table-column>
        </el-table>
        
        <div class="dialog-footer">
          <el-button @click="layerQueryDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="loadSelectedLayers">加载选中图层</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useMapLayerManagerStore } from '/@/stores/olMapLayer/mapLayerManager';
import { useOLMapStore } from '/@/stores/olMapStore';
import { Search, Loading, CircleClose, InfoFilled } from '@element-plus/icons-vue';
import { transform } from 'ol/proj';
import type { MapBrowserEvent } from 'ol';
import { ElMessage } from 'element-plus';
import axios from 'axios';
import { addLayerOnTop } from '/@/components/olLayerManager/useLayerList';
import { buildLayerQueryUrl } from '/@/utils/geoserver';
import { loadMapConfigFile } from '/@/utils/mapConfig';

const props = defineProps<{
  mapContainer?: HTMLElement | null;
}>();

const emit = defineEmits<{
  (e: 'query-mode-change', active: boolean): void
}>();

// Store
const mapStore = useOLMapStore();
const mapLayerStore = useMapLayerManagerStore();

// 位置查询相关状态
const isQueryModeActive = ref(false);
const queryResult = ref({
  longitude: 0,
  latitude: 0
});

// 图层查询相关状态
const layerQueryDialogVisible = ref(false);
const layerQueryStatus = ref<'idle' | 'loading' | 'success' | 'error'>('idle');
const layerQueryResult = ref<{
  count: number;
  layers: Array<{
    id: string;
    name: string;
    workspace: string;
    store: string;
    type: string;
    point_inside_bbox: boolean;
    bbox: {
      minx: number;
      miny: number;
      maxx: number;
      maxy: number;
    };
    selected?: boolean;
  }>;
  status: string;
}>({
  count: 0,
  layers: [],
  status: ''
});
const layerQueryError = ref('');
const defaultWorkspace = ref('test_myworkspace');
const lastQueryCoords = ref({ longitude: 0, latitude: 0 });

// 切换位置查询模式
function toggleQueryMode() {
  isQueryModeActive.value = !isQueryModeActive.value;
  
  // 通知父组件查询模式改变
  emit('query-mode-change', isQueryModeActive.value);
  
  if (isQueryModeActive.value) {
    // 启用查询模式时，修改鼠标样式
    if (props.mapContainer) {
      props.mapContainer.style.cursor = 'crosshair';
    }
    
    // 添加地图点击事件监听器
    if (mapStore.map?.map) {
      mapStore.map.map.on('click', handleMapClick);
    }
    
    // 显示激活提示
    ElMessage({
      message: '查询模式已激活，点击地图位置查询栅格图层，再次点击按钮可关闭',
      type: 'success',
      duration: 3000
    });
  } else {
    // 禁用查询模式时，恢复默认鼠标样式
    if (props.mapContainer) {
      props.mapContainer.style.cursor = '';
    }
    
    // 移除地图点击事件监听器
    if (mapStore.map?.map) {
      mapStore.map.map.un('click', handleMapClick);
    }
    
    // 显示停用提示
    ElMessage({
      message: '查询模式已关闭',
      type: 'info',
      duration: 2000
    });
  }
}

// 处理地图点击事件
async function handleMapClick(event: MapBrowserEvent<UIEvent>) {
  // 获取点击位置的坐标（Web墨卡托投影）
  const clickCoord = event.coordinate;
  
  // 将坐标从Web墨卡托投影转换为WGS84经纬度
  const wgs84Coord = transform(clickCoord, 'EPSG:3857', 'EPSG:4326');
  
  // 更新查询结果
  queryResult.value = {
    longitude: wgs84Coord[0],
    latitude: wgs84Coord[1]
  };
  
  // 保存当前查询坐标
  lastQueryCoords.value = { ...queryResult.value };
  
  // 查询该位置的图层
  await queryLayersAtLocation(wgs84Coord[1], wgs84Coord[0]);
}

// 查询指定位置的图层
async function queryLayersAtLocation(lat: number, lon: number) {
  layerQueryStatus.value = 'loading';
  layerQueryDialogVisible.value = true;
  
  try {
    // 使用工具函数构建查询URL
    const apiUrl = buildLayerQueryUrl(lat, lon, defaultWorkspace.value);
    const response = await axios.get(apiUrl);
    
    if (response.data.status === 'success') {
      // 先保存原始结果
      const originalResult = response.data;
      
      // 获取当前项目配置中的所有图层ID
      const configLayers = mapLayerStore.mapConfig?.layers || [];
      const configLayerIds = configLayers.map(layer => layer.id);
      
      // 过滤查询结果，只保留存在于配置中的图层
      const filteredLayers = originalResult.layers.filter((layer: {id: string}) => 
        configLayerIds.includes(layer.id)
      );
      
      // 更新过滤后的结果
      layerQueryResult.value = {
        ...originalResult,
        count: filteredLayers.length,
        layers: filteredLayers
      };
      
      // 记录过滤情况
      const filteredOutCount = originalResult.count - filteredLayers.length;
      if (filteredOutCount > 0) {
        console.log(`已过滤掉 ${filteredOutCount} 个不在项目配置中的图层`);
      }
      
      // 为每个图层添加selected属性，默认为true（选中）
      layerQueryResult.value.layers.forEach(layer => {
        layer.selected = !isLayerLoaded(layer.id);
      });
      
      layerQueryStatus.value = 'success';
    } else {
      layerQueryError.value = response.data.message || '查询失败';
      layerQueryStatus.value = 'error';
    }
  } catch (error) {
    console.error('图层查询失败:', error);
    layerQueryError.value = error instanceof Error ? error.message : '网络请求失败';
    layerQueryStatus.value = 'error';
  }
}

// 重试图层查询
async function retryLayerQuery() {
  await queryLayersAtLocation(lastQueryCoords.value.latitude, lastQueryCoords.value.longitude);
}

// 检查图层是否已加载
function isLayerLoaded(layerId: string): boolean {
  return mapLayerStore.isLayerLoaded(layerId);
}

// 获取图层主题
function getLayerTheme(layerId: string): string {
  const configLayers = mapLayerStore.mapConfig?.layers || [];
  const layer = configLayers.find(layer => layer.id === layerId);
  // 使用类型断言来访问theme属性
  return (layer as any)?.theme || '';
}

// 获取图层名称
function getLayerName(layerId: string): string {
  const configLayers = mapLayerStore.mapConfig?.layers || [];
  const layer = configLayers.find(layer => layer.id === layerId);
  // 从配置文件中获取图层名称，如果找不到则返回ID作为后备
  return layer?.name || layerId;
}

// 加载选中的图层
async function loadSelectedLayers() {
  const selectedLayers = layerQueryResult.value.layers.filter(layer => layer.selected && !isLayerLoaded(layer.id));
  
  if (selectedLayers.length === 0) {
    layerQueryDialogVisible.value = false;
    return;
  }
  
  // 显示加载进度
  ElMessage({
    message: `正在加载 ${selectedLayers.length} 个图层...`,
    type: 'info',
    duration: 2000
  });
  
  let successCount = 0;
  
  // 逐个加载选中的图层
  for (const layer of selectedLayers) {
    try {
      const success = await addLayerOnTop(layer.id);
      if (success) {
        successCount++;
      }
    } catch (error) {
      console.error(`加载图层 ${layer.id} 失败:`, error);
    }
  }
  
  // 显示加载结果
  if (successCount > 0) {
    ElMessage({
      message: `成功加载 ${successCount}/${selectedLayers.length} 个图层`,
      type: 'success',
      duration: 3000
    });
  } else if (selectedLayers.length > 0) {
    ElMessage({
      message: '图层加载失败',
      type: 'error',
      duration: 3000
    });
  }
  
  // 关闭弹窗
  layerQueryDialogVisible.value = false;
}

// 加载配置
async function loadConfig() {
  try {
    // 使用新的工具函数从API加载配置
    console.log('正在加载图层查询配置...');
    
    const config = await loadMapConfigFile('baseMap2');
    
    if (config.mapConfig?.queryConfig?.defaultWorkspace) {
      defaultWorkspace.value = config.mapConfig.queryConfig.defaultWorkspace;
    }
  } catch (error) {
    console.error('加载地图配置失败:', error);
  }
}

// 对外暴露的方法
defineExpose({
  toggleQueryMode,
  queryLayersAtLocation
});

onMounted(async () => {
  // 加载地图配置
  await loadConfig();
});

onBeforeUnmount(() => {
  // 如果组件卸载时查询模式仍然激活，清理监听器
  if (isQueryModeActive.value && mapStore.map?.map) {
    mapStore.map.map.un('click', handleMapClick);
  }
});
</script>

<style scoped>
/* 右上角工具栏样式 */
.map-tools {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.query-btn {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  font-size: 14px;
  font-weight: 500;
  padding: 8px 16px;
}

.active-tool {
  background-color: #67c23a !important;
  border-color: #67c23a !important;
}

/* 查询提示样式 */
.query-instructions {
  margin-top: 10px;
  width: 240px;
}

.query-instructions .el-alert {
  padding: 10px;
}

.query-instructions p {
  margin: 8px 0 0;
  font-size: 13px;
  line-height: 1.4;
}

.query-instructions .close-tip {
  margin-top: 12px;
  font-weight: 500;
  color: #409eff;
  border-top: 1px dashed rgba(64, 158, 255, 0.3);
  padding-top: 8px;
}

/* 图层查询结果样式 */
.query-loading, .query-error, .no-layers-found {
  text-align: center;
  padding: 20px 0;
}

.query-loading .el-icon, .query-error .el-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.query-error .el-icon {
  color: #f56c6c;
}

.result-summary {
  font-weight: bold;
  margin-bottom: 10px;
}

.dialog-footer {
  margin-top: 20px;
  text-align: right;
}

/* 已加载图标样式 */
.loaded-tag {
  white-space: nowrap;
  display: inline-block;
  padding: 0 8px;
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style> 