import {
  applyTransform,
  approximatelyEquals,
  boundingExtent,
  buffer,
  clone,
  closestSquaredDistanceXY,
  containsCoordinate,
  containsExtent,
  containsXY,
  coordinateRelationship,
  createEmpty,
  createOrUpdate,
  createOrUpdateEmpty,
  createOrUpdateFromCoordinate,
  createOrUpdateFromCoordinates,
  createOrUpdateFromFlatCoordinates,
  createOrUpdateFromRings,
  equals,
  extend,
  extendCoordinate,
  extendCoordinates,
  extendFlatCoordinates,
  extendRings,
  extendXY,
  forEachCorner,
  getArea,
  getBottomLeft,
  getBottomRight,
  getCenter,
  getCorner,
  getEnlargedArea,
  getForViewAndSize,
  getHeight,
  getIntersection,
  getIntersectionArea,
  getMargin,
  getRotatedViewport,
  getSize,
  getTopLeft,
  getTopRight,
  getWidth,
  intersects,
  intersectsSegment,
  isEmpty,
  returnOrUpdate,
  scaleFromCenter,
  wrapAndSliceX,
  wrapX
} from "./chunk-3L7AZTGC.js";
import "./chunk-F2MRU6YO.js";
import "./chunk-PLDDJCW6.js";
export {
  applyTransform,
  approximatelyEquals,
  boundingExtent,
  buffer,
  clone,
  closestSquaredDistanceXY,
  containsCoordinate,
  containsExtent,
  containsXY,
  coordinateRelationship,
  createEmpty,
  createOrUpdate,
  createOrUpdateEmpty,
  createOrUpdateFromCoordinate,
  createOrUpdateFromCoordinates,
  createOrUpdateFromFlatCoordinates,
  createOrUpdateFromRings,
  equals,
  extend,
  extendCoordinate,
  extendCoordinates,
  extendFlatCoordinates,
  extendRings,
  extendXY,
  forEachCorner,
  getArea,
  getBottomLeft,
  getBottomRight,
  getCenter,
  getCorner,
  getEnlargedArea,
  getForViewAndSize,
  getHeight,
  getIntersection,
  getIntersectionArea,
  getMargin,
  getRotatedViewport,
  getSize,
  getTopLeft,
  getTopRight,
  getWidth,
  intersects,
  intersectsSegment,
  isEmpty,
  returnOrUpdate,
  scaleFromCenter,
  wrapAndSliceX,
  wrapX
};
//# sourceMappingURL=ol_extent.js.map
