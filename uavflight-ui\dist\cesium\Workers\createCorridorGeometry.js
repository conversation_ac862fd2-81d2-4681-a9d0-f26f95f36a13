define(["./arrayRemoveDuplicates-e9673044","./Transforms-01e95659","./Matrix3-a348023f","./ComponentDatatype-77274976","./PolylineVolumeGeometryLibrary-d240c597","./CorridorGeometryLibrary-a1dbf938","./defaultValue-0a909f67","./GeometryAttribute-f5d71750","./GeometryAttributes-f06a2792","./GeometryOffsetAttribute-04332ce7","./IndexDatatype-2149f06c","./Math-e97915da","./PolygonPipeline-f5f5011c","./Matrix2-7146c9ca","./VertexFormat-ab2e00e6","./combine-ca22a614","./RuntimeError-06c93819","./WebGLConstants-a8cc3e8c","./EllipsoidTangentPlane-6308603a","./AxisAlignedBoundingBox-65ccb1a5","./IntersectionTests-0bb04fde","./Plane-8575e17c","./PolylinePipeline-e5af032d","./EllipsoidGeodesic-f4dd0b26","./EllipsoidRhumbLine-9b24aab2"],(function(t,e,r,a,i,o,n,s,l,d,u,c,m,f,y,p,g,h,C,b,A,_,w,v,T){"use strict";const G=new r.Cartesian3,E=new r.Cartesian3,V=new r.Cartesian3,x=new r.Cartesian3,L=new r.Cartesian3,P=new r.Cartesian3,F=new r.Cartesian3,N=new r.Cartesian3;function M(t,e){for(let r=0;r<t.length;r++)t[r]=e.scaleToGeodeticSurface(t[r],t[r]);return t}function D(t,e,a,i,n,s){const l=t.normals,d=t.tangents,u=t.bitangents,c=r.Cartesian3.normalize(r.Cartesian3.cross(a,e,F),F);s.normal&&o.CorridorGeometryLibrary.addAttribute(l,e,i,n),s.tangent&&o.CorridorGeometryLibrary.addAttribute(d,c,i,n),s.bitangent&&o.CorridorGeometryLibrary.addAttribute(u,a,i,n)}function O(t,e,i){const d=t.positions,m=t.corners,f=t.endPositions,y=t.lefts,p=t.normals,g=new l.GeometryAttributes;let h,C,b,A=0,_=0,w=0;for(C=0;C<d.length;C+=2)b=d[C].length-3,A+=b,w+=2*b,_+=d[C+1].length-3;for(A+=3,_+=3,C=0;C<m.length;C++){h=m[C];const t=m[C].leftPositions;n.defined(t)?(b=t.length,A+=b,w+=b):(b=m[C].rightPositions.length,_+=b,w+=b)}const v=n.defined(f);let T;v&&(T=f[0].length-3,A+=T,_+=T,T/=3,w+=6*T);const L=A+_,M=new Float64Array(L),O={normals:e.normal?new Float32Array(L):void 0,tangents:e.tangent?new Float32Array(L):void 0,bitangents:e.bitangent?new Float32Array(L):void 0};let I,S,R,k,H,z,B=0,U=L-1,Y=G,W=E;const q=T/2,J=u.IndexDatatype.createTypedArray(L/3,w);let j=0;if(v){z=V,H=x;const t=f[0];for(Y=r.Cartesian3.fromArray(p,0,Y),W=r.Cartesian3.fromArray(y,0,W),C=0;C<q;C++)z=r.Cartesian3.fromArray(t,3*(q-1-C),z),H=r.Cartesian3.fromArray(t,3*(q+C),H),o.CorridorGeometryLibrary.addAttribute(M,H,B),o.CorridorGeometryLibrary.addAttribute(M,z,void 0,U),D(O,Y,W,B,U,e),S=B/3,k=S+1,I=(U-2)/3,R=I-1,J[j++]=I,J[j++]=S,J[j++]=R,J[j++]=R,J[j++]=S,J[j++]=k,B+=3,U-=3}let K,Q,X=0,Z=0,$=d[X++],tt=d[X++];for(M.set($,B),M.set(tt,U-tt.length+1),W=r.Cartesian3.fromArray(y,Z,W),b=tt.length-3,C=0;C<b;C+=3)K=i.geodeticSurfaceNormal(r.Cartesian3.fromArray($,C,F),F),Q=i.geodeticSurfaceNormal(r.Cartesian3.fromArray(tt,b-C,N),N),Y=r.Cartesian3.normalize(r.Cartesian3.add(K,Q,Y),Y),D(O,Y,W,B,U,e),S=B/3,k=S+1,I=(U-2)/3,R=I-1,J[j++]=I,J[j++]=S,J[j++]=R,J[j++]=R,J[j++]=S,J[j++]=k,B+=3,U-=3;for(K=i.geodeticSurfaceNormal(r.Cartesian3.fromArray($,b,F),F),Q=i.geodeticSurfaceNormal(r.Cartesian3.fromArray(tt,b,N),N),Y=r.Cartesian3.normalize(r.Cartesian3.add(K,Q,Y),Y),Z+=3,C=0;C<m.length;C++){let t;h=m[C];const a=h.leftPositions,s=h.rightPositions;let l,u,c=P,f=V,g=x;if(Y=r.Cartesian3.fromArray(p,Z,Y),n.defined(a)){for(D(O,Y,W,void 0,U,e),U-=3,l=k,u=R,t=0;t<a.length/3;t++)c=r.Cartesian3.fromArray(a,3*t,c),J[j++]=l,J[j++]=u-t-1,J[j++]=u-t,o.CorridorGeometryLibrary.addAttribute(M,c,void 0,U),f=r.Cartesian3.fromArray(M,3*(u-t-1),f),g=r.Cartesian3.fromArray(M,3*l,g),W=r.Cartesian3.normalize(r.Cartesian3.subtract(f,g,W),W),D(O,Y,W,void 0,U,e),U-=3;c=r.Cartesian3.fromArray(M,3*l,c),f=r.Cartesian3.subtract(r.Cartesian3.fromArray(M,3*u,f),c,f),g=r.Cartesian3.subtract(r.Cartesian3.fromArray(M,3*(u-t),g),c,g),W=r.Cartesian3.normalize(r.Cartesian3.add(f,g,W),W),D(O,Y,W,B,void 0,e),B+=3}else{for(D(O,Y,W,B,void 0,e),B+=3,l=R,u=k,t=0;t<s.length/3;t++)c=r.Cartesian3.fromArray(s,3*t,c),J[j++]=l,J[j++]=u+t,J[j++]=u+t+1,o.CorridorGeometryLibrary.addAttribute(M,c,B),f=r.Cartesian3.fromArray(M,3*l,f),g=r.Cartesian3.fromArray(M,3*(u+t),g),W=r.Cartesian3.normalize(r.Cartesian3.subtract(f,g,W),W),D(O,Y,W,B,void 0,e),B+=3;c=r.Cartesian3.fromArray(M,3*l,c),f=r.Cartesian3.subtract(r.Cartesian3.fromArray(M,3*(u+t),f),c,f),g=r.Cartesian3.subtract(r.Cartesian3.fromArray(M,3*u,g),c,g),W=r.Cartesian3.normalize(r.Cartesian3.negate(r.Cartesian3.add(g,f,W),W),W),D(O,Y,W,void 0,U,e),U-=3}for($=d[X++],tt=d[X++],$.splice(0,3),tt.splice(tt.length-3,3),M.set($,B),M.set(tt,U-tt.length+1),b=tt.length-3,Z+=3,W=r.Cartesian3.fromArray(y,Z,W),t=0;t<tt.length;t+=3)K=i.geodeticSurfaceNormal(r.Cartesian3.fromArray($,t,F),F),Q=i.geodeticSurfaceNormal(r.Cartesian3.fromArray(tt,b-t,N),N),Y=r.Cartesian3.normalize(r.Cartesian3.add(K,Q,Y),Y),D(O,Y,W,B,U,e),k=B/3,S=k-1,R=(U-2)/3,I=R+1,J[j++]=I,J[j++]=S,J[j++]=R,J[j++]=R,J[j++]=S,J[j++]=k,B+=3,U-=3;B-=3,U+=3}if(Y=r.Cartesian3.fromArray(p,p.length-3,Y),D(O,Y,W,B,U,e),v){B+=3,U-=3,z=V,H=x;const t=f[1];for(C=0;C<q;C++)z=r.Cartesian3.fromArray(t,3*(T-C-1),z),H=r.Cartesian3.fromArray(t,3*C,H),o.CorridorGeometryLibrary.addAttribute(M,z,void 0,U),o.CorridorGeometryLibrary.addAttribute(M,H,B),D(O,Y,W,B,U,e),k=B/3,S=k-1,R=(U-2)/3,I=R+1,J[j++]=I,J[j++]=S,J[j++]=R,J[j++]=R,J[j++]=S,J[j++]=k,B+=3,U-=3}if(g.position=new s.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:M}),e.st){const t=new Float32Array(L/3*2);let e,r,i=0;if(v){A/=3,_/=3;const a=Math.PI/(T+1);let o;r=1/(A-T+1),e=1/(_-T+1);const n=T/2;for(C=n+1;C<T+1;C++)o=c.CesiumMath.PI_OVER_TWO+a*C,t[i++]=e*(1+Math.cos(o)),t[i++]=.5*(1+Math.sin(o));for(C=1;C<_-T+1;C++)t[i++]=C*e,t[i++]=0;for(C=T;C>n;C--)o=c.CesiumMath.PI_OVER_TWO-C*a,t[i++]=1-e*(1+Math.cos(o)),t[i++]=.5*(1+Math.sin(o));for(C=n;C>0;C--)o=c.CesiumMath.PI_OVER_TWO-a*C,t[i++]=1-r*(1+Math.cos(o)),t[i++]=.5*(1+Math.sin(o));for(C=A-T;C>0;C--)t[i++]=C*r,t[i++]=1;for(C=1;C<n+1;C++)o=c.CesiumMath.PI_OVER_TWO+a*C,t[i++]=r*(1+Math.cos(o)),t[i++]=.5*(1+Math.sin(o))}else{for(A/=3,_/=3,r=1/(A-1),e=1/(_-1),C=0;C<_;C++)t[i++]=C*e,t[i++]=0;for(C=A;C>0;C--)t[i++]=(C-1)*r,t[i++]=1}g.st=new s.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:t})}return e.normal&&(g.normal=new s.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:O.normals})),e.tangent&&(g.tangent=new s.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:O.tangents})),e.bitangent&&(g.bitangent=new s.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:O.bitangents})),{attributes:g,indices:J}}function I(t,e,r){r[e++]=t[0],r[e++]=t[1],r[e++]=t[2];for(let a=3;a<t.length;a+=3){const i=t[a],o=t[a+1],n=t[a+2];r[e++]=i,r[e++]=o,r[e++]=n,r[e++]=i,r[e++]=o,r[e++]=n}return r[e++]=t[0],r[e++]=t[1],r[e++]=t[2],r}function S(t,e){const i=new y.VertexFormat({position:e.position,normal:e.normal||e.bitangent||t.shadowVolume,tangent:e.tangent,bitangent:e.normal||e.bitangent,st:e.st}),l=t.ellipsoid,c=O(o.CorridorGeometryLibrary.computePositions(t),i,l),f=t.height,p=t.extrudedHeight;let g=c.attributes;const h=c.indices;let C=g.position.values,b=C.length;const A=new Float64Array(6*b);let _=new Float64Array(b);_.set(C);let w,v=new Float64Array(4*b);C=m.PolygonPipeline.scaleToGeodeticHeight(C,f,l),v=I(C,0,v),_=m.PolygonPipeline.scaleToGeodeticHeight(_,p,l),v=I(_,2*b,v),A.set(C),A.set(_,b),A.set(v,2*b),g.position.values=A,g=function(t,e){if(!(e.normal||e.tangent||e.bitangent||e.st))return t;const a=t.position.values;let i,n;(e.normal||e.bitangent)&&(i=t.normal.values,n=t.bitangent.values);const s=t.position.values.length/18,l=3*s,d=2*s,u=2*l;let c;if(e.normal||e.bitangent||e.tangent){const s=e.normal?new Float32Array(6*l):void 0,d=e.tangent?new Float32Array(6*l):void 0,m=e.bitangent?new Float32Array(6*l):void 0;let f=G,y=E,p=V,g=x,h=L,C=P,b=u;for(c=0;c<l;c+=3){const t=b+u;f=r.Cartesian3.fromArray(a,c,f),y=r.Cartesian3.fromArray(a,c+l,y),p=r.Cartesian3.fromArray(a,(c+3)%l,p),y=r.Cartesian3.subtract(y,f,y),p=r.Cartesian3.subtract(p,f,p),g=r.Cartesian3.normalize(r.Cartesian3.cross(y,p,g),g),e.normal&&(o.CorridorGeometryLibrary.addAttribute(s,g,t),o.CorridorGeometryLibrary.addAttribute(s,g,t+3),o.CorridorGeometryLibrary.addAttribute(s,g,b),o.CorridorGeometryLibrary.addAttribute(s,g,b+3)),(e.tangent||e.bitangent)&&(C=r.Cartesian3.fromArray(i,c,C),e.bitangent&&(o.CorridorGeometryLibrary.addAttribute(m,C,t),o.CorridorGeometryLibrary.addAttribute(m,C,t+3),o.CorridorGeometryLibrary.addAttribute(m,C,b),o.CorridorGeometryLibrary.addAttribute(m,C,b+3)),e.tangent&&(h=r.Cartesian3.normalize(r.Cartesian3.cross(C,g,h),h),o.CorridorGeometryLibrary.addAttribute(d,h,t),o.CorridorGeometryLibrary.addAttribute(d,h,t+3),o.CorridorGeometryLibrary.addAttribute(d,h,b),o.CorridorGeometryLibrary.addAttribute(d,h,b+3))),b+=6}if(e.normal){for(s.set(i),c=0;c<l;c+=3)s[c+l]=-i[c],s[c+l+1]=-i[c+1],s[c+l+2]=-i[c+2];t.normal.values=s}else t.normal=void 0;if(e.bitangent?(m.set(n),m.set(n,l),t.bitangent.values=m):t.bitangent=void 0,e.tangent){const e=t.tangent.values;d.set(e),d.set(e,l),t.tangent.values=d}}if(e.st){const e=t.st.values,r=new Float32Array(6*d);r.set(e),r.set(e,d);let a=2*d;for(let t=0;t<2;t++){for(r[a++]=e[0],r[a++]=e[1],c=2;c<d;c+=2){const t=e[c],i=e[c+1];r[a++]=t,r[a++]=i,r[a++]=t,r[a++]=i}r[a++]=e[0],r[a++]=e[1]}t.st.values=r}return t}(g,e);const T=b/3;if(t.shadowVolume){const t=g.normal.values;b=t.length;let r=new Float32Array(6*b);for(w=0;w<b;w++)t[w]=-t[w];r.set(t,b),r=I(t,4*b,r),g.extrudeDirection=new s.GeometryAttribute({componentDatatype:a.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:r}),e.normal||(g.normal=void 0)}if(n.defined(t.offsetAttribute)){let e=new Uint8Array(6*T);if(t.offsetAttribute===d.GeometryOffsetAttribute.TOP)e=e.fill(1,0,T).fill(1,2*T,4*T);else{const r=t.offsetAttribute===d.GeometryOffsetAttribute.NONE?0:1;e=e.fill(r)}g.applyOffset=new s.GeometryAttribute({componentDatatype:a.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:e})}const F=h.length,N=T+T,M=u.IndexDatatype.createTypedArray(A.length/3,2*F+3*N);M.set(h);let D,S,R,k,H=F;for(w=0;w<F;w+=3){const t=h[w],e=h[w+1],r=h[w+2];M[H++]=r+T,M[H++]=e+T,M[H++]=t+T}for(w=0;w<N;w+=2)D=w+N,S=D+N,R=D+1,k=S+1,M[H++]=D,M[H++]=S,M[H++]=R,M[H++]=R,M[H++]=S,M[H++]=k;return{attributes:g,indices:M}}const R=new r.Cartesian3,k=new r.Cartesian3,H=new r.Cartographic;function z(t,e,a,i,o,n){const s=r.Cartesian3.subtract(e,t,R);r.Cartesian3.normalize(s,s);const l=a.geodeticSurfaceNormal(t,k),d=r.Cartesian3.cross(s,l,R);r.Cartesian3.multiplyByScalar(d,i,d);let u=o.latitude,c=o.longitude,m=n.latitude,f=n.longitude;r.Cartesian3.add(t,d,k),a.cartesianToCartographic(k,H);let y=H.latitude,p=H.longitude;u=Math.min(u,y),c=Math.min(c,p),m=Math.max(m,y),f=Math.max(f,p),r.Cartesian3.subtract(t,d,k),a.cartesianToCartographic(k,H),y=H.latitude,p=H.longitude,u=Math.min(u,y),c=Math.min(c,p),m=Math.max(m,y),f=Math.max(f,p),o.latitude=u,o.longitude=c,n.latitude=m,n.longitude=f}const B=new r.Cartesian3,U=new r.Cartesian3,Y=new r.Cartographic,W=new r.Cartographic;function q(e,a,o,s,l){e=M(e,a);const d=t.arrayRemoveDuplicates(e,r.Cartesian3.equalsEpsilon),u=d.length;if(u<2||o<=0)return new f.Rectangle;const c=.5*o;let m,y;if(Y.latitude=Number.POSITIVE_INFINITY,Y.longitude=Number.POSITIVE_INFINITY,W.latitude=Number.NEGATIVE_INFINITY,W.longitude=Number.NEGATIVE_INFINITY,s===i.CornerType.ROUNDED){const t=d[0];r.Cartesian3.subtract(t,d[1],B),r.Cartesian3.normalize(B,B),r.Cartesian3.multiplyByScalar(B,c,B),r.Cartesian3.add(t,B,U),a.cartesianToCartographic(U,H),m=H.latitude,y=H.longitude,Y.latitude=Math.min(Y.latitude,m),Y.longitude=Math.min(Y.longitude,y),W.latitude=Math.max(W.latitude,m),W.longitude=Math.max(W.longitude,y)}for(let t=0;t<u-1;++t)z(d[t],d[t+1],a,c,Y,W);const p=d[u-1];r.Cartesian3.subtract(p,d[u-2],B),r.Cartesian3.normalize(B,B),r.Cartesian3.multiplyByScalar(B,c,B),r.Cartesian3.add(p,B,U),z(p,U,a,c,Y,W),s===i.CornerType.ROUNDED&&(a.cartesianToCartographic(U,H),m=H.latitude,y=H.longitude,Y.latitude=Math.min(Y.latitude,m),Y.longitude=Math.min(Y.longitude,y),W.latitude=Math.max(W.latitude,m),W.longitude=Math.max(W.longitude,y));const g=n.defined(l)?l:new f.Rectangle;return g.north=W.latitude,g.south=Y.latitude,g.east=W.longitude,g.west=Y.longitude,g}function J(t){const e=(t=n.defaultValue(t,n.defaultValue.EMPTY_OBJECT)).positions,a=t.width,o=n.defaultValue(t.height,0),s=n.defaultValue(t.extrudedHeight,o);this._positions=e,this._ellipsoid=r.Ellipsoid.clone(n.defaultValue(t.ellipsoid,r.Ellipsoid.WGS84)),this._vertexFormat=y.VertexFormat.clone(n.defaultValue(t.vertexFormat,y.VertexFormat.DEFAULT)),this._width=a,this._height=Math.max(o,s),this._extrudedHeight=Math.min(o,s),this._cornerType=n.defaultValue(t.cornerType,i.CornerType.ROUNDED),this._granularity=n.defaultValue(t.granularity,c.CesiumMath.RADIANS_PER_DEGREE),this._shadowVolume=n.defaultValue(t.shadowVolume,!1),this._workerName="createCorridorGeometry",this._offsetAttribute=t.offsetAttribute,this._rectangle=void 0,this.packedLength=1+e.length*r.Cartesian3.packedLength+r.Ellipsoid.packedLength+y.VertexFormat.packedLength+7}J.pack=function(t,e,a){a=n.defaultValue(a,0);const i=t._positions,o=i.length;e[a++]=o;for(let t=0;t<o;++t,a+=r.Cartesian3.packedLength)r.Cartesian3.pack(i[t],e,a);return r.Ellipsoid.pack(t._ellipsoid,e,a),a+=r.Ellipsoid.packedLength,y.VertexFormat.pack(t._vertexFormat,e,a),a+=y.VertexFormat.packedLength,e[a++]=t._width,e[a++]=t._height,e[a++]=t._extrudedHeight,e[a++]=t._cornerType,e[a++]=t._granularity,e[a++]=t._shadowVolume?1:0,e[a]=n.defaultValue(t._offsetAttribute,-1),e};const j=r.Ellipsoid.clone(r.Ellipsoid.UNIT_SPHERE),K=new y.VertexFormat,Q={positions:void 0,ellipsoid:j,vertexFormat:K,width:void 0,height:void 0,extrudedHeight:void 0,cornerType:void 0,granularity:void 0,shadowVolume:void 0,offsetAttribute:void 0};return J.unpack=function(t,e,a){e=n.defaultValue(e,0);const i=t[e++],o=new Array(i);for(let a=0;a<i;++a,e+=r.Cartesian3.packedLength)o[a]=r.Cartesian3.unpack(t,e);const s=r.Ellipsoid.unpack(t,e,j);e+=r.Ellipsoid.packedLength;const l=y.VertexFormat.unpack(t,e,K);e+=y.VertexFormat.packedLength;const d=t[e++],u=t[e++],c=t[e++],m=t[e++],f=t[e++],p=1===t[e++],g=t[e];return n.defined(a)?(a._positions=o,a._ellipsoid=r.Ellipsoid.clone(s,a._ellipsoid),a._vertexFormat=y.VertexFormat.clone(l,a._vertexFormat),a._width=d,a._height=u,a._extrudedHeight=c,a._cornerType=m,a._granularity=f,a._shadowVolume=p,a._offsetAttribute=-1===g?void 0:g,a):(Q.positions=o,Q.width=d,Q.height=u,Q.extrudedHeight=c,Q.cornerType=m,Q.granularity=f,Q.shadowVolume=p,Q.offsetAttribute=-1===g?void 0:g,new J(Q))},J.computeRectangle=function(t,e){const a=(t=n.defaultValue(t,n.defaultValue.EMPTY_OBJECT)).positions,o=t.width;return q(a,n.defaultValue(t.ellipsoid,r.Ellipsoid.WGS84),o,n.defaultValue(t.cornerType,i.CornerType.ROUNDED),e)},J.createGeometry=function(i){let l=i._positions;const u=i._width,f=i._ellipsoid;l=M(l,f);const y=t.arrayRemoveDuplicates(l,r.Cartesian3.equalsEpsilon);if(y.length<2||u<=0)return;const p=i._height,g=i._extrudedHeight,h=!c.CesiumMath.equalsEpsilon(p,g,0,c.CesiumMath.EPSILON2),C=i._vertexFormat,b={ellipsoid:f,positions:y,width:u,cornerType:i._cornerType,granularity:i._granularity,saveAttributes:!0};let A;if(h)b.height=p,b.extrudedHeight=g,b.shadowVolume=i._shadowVolume,b.offsetAttribute=i._offsetAttribute,A=S(b,C);else{if(A=O(o.CorridorGeometryLibrary.computePositions(b),C,f),A.attributes.position.values=m.PolygonPipeline.scaleToGeodeticHeight(A.attributes.position.values,p,f),n.defined(i._offsetAttribute)){const t=i._offsetAttribute===d.GeometryOffsetAttribute.NONE?0:1,e=A.attributes.position.values.length,r=new Uint8Array(e/3).fill(t);A.attributes.applyOffset=new s.GeometryAttribute({componentDatatype:a.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:r})}}const _=A.attributes,w=e.BoundingSphere.fromVertices(_.position.values,void 0,3);return C.position||(A.attributes.position.values=void 0),new s.Geometry({attributes:_,indices:A.indices,primitiveType:s.PrimitiveType.TRIANGLES,boundingSphere:w,offsetAttribute:i._offsetAttribute})},J.createShadowVolume=function(t,e,r){const a=t._granularity,i=t._ellipsoid,o=e(a,i),n=r(a,i);return new J({positions:t._positions,width:t._width,cornerType:t._cornerType,ellipsoid:i,granularity:a,extrudedHeight:o,height:n,vertexFormat:y.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(J.prototype,{rectangle:{get:function(){return n.defined(this._rectangle)||(this._rectangle=q(this._positions,this._ellipsoid,this._width,this._cornerType)),this._rectangle}},textureCoordinateRotationPoints:{get:function(){return[0,0,0,1,1,0]}}}),function(t,e){return n.defined(e)&&(t=J.unpack(t,e)),t._ellipsoid=r.Ellipsoid.clone(t._ellipsoid),J.createGeometry(t)}}));
