{"name": "proj4", "version": "2.19.10", "description": "Proj4js is a JavaScript library to transform point coordinates from one coordinate system to another, including datum transformations.", "homepage": "https://proj4js.github.io/proj4js/", "main": "dist/proj4-src.js", "module": "lib/index.js", "types": "dist/index.d.ts", "funding": "https://github.com/sponsors/ahocevar", "scripts": {"prepare": "npm run test", "build": "grunt && npm run rollup", "build:tmerc": "grunt build:tmerc && npm run rollup", "rollup": "rollup -c", "pretest": "npm run lint && npm run types", "lint": "eslint *.js *.mjs scripts lib test", "format": "npm run lint -- --fix", "test": "npm run build && npm run test:all", "test:coverage": "nyc npm run test:ci", "test:all": "npm run test:ci && npm run test:browser", "test:browser": "node test/puppeteer-tests.mjs", "test:ci": "npx -y mocha test/test-ci.mjs --reporter dot", "types": "tsc"}, "repository": {"type": "git", "url": "git://github.com/proj4js/proj4js.git"}, "author": "", "license": "MIT", "devDependencies": {"@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@stylistic/eslint-plugin": "^4.2.0", "chai": "^5.0.0", "eslint": "^9.25.1", "geotiff": "^2.1.4-beta.0", "grunt": "^1.6.1", "grunt-cli": "^1.4.3", "mocha": "^10.2.0", "nyc": "^17.1.0", "puppeteer": "^24.2.0", "rollup": "^4.9.6", "typescript": "^5.8.3"}, "dependencies": {"mgrs": "1.0.0", "wkt-parser": "^1.5.1"}}