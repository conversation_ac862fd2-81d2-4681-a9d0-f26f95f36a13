define(["./defaultValue-0a909f67","./Matrix3-a348023f","./ArcType-ce2e50ab","./Transforms-01e95659","./Color-485badd3","./ComponentDatatype-77274976","./GeometryAttribute-f5d71750","./GeometryAttributes-f06a2792","./IndexDatatype-2149f06c","./Math-e97915da","./PolylinePipeline-e5af032d","./Matrix2-7146c9ca","./RuntimeError-06c93819","./combine-ca22a614","./WebGLConstants-a8cc3e8c","./EllipsoidGeodesic-f4dd0b26","./EllipsoidRhumbLine-9b24aab2","./IntersectionTests-0bb04fde","./Plane-8575e17c"],(function(e,o,t,l,r,n,i,a,s,c,p,d,f,y,u,h,C,T,g){"use strict";function m(e,o,t,l,n,i,a){const s=p.PolylinePipeline.numberOfPoints(e,o,n);let c;const d=t.red,f=t.green,y=t.blue,u=t.alpha,h=l.red,C=l.green,T=l.blue,g=l.alpha;if(r.Color.equals(t,l)){for(c=0;c<s;c++)i[a++]=r.Color.floatToByte(d),i[a++]=r.Color.floatToByte(f),i[a++]=r.Color.floatToByte(y),i[a++]=r.Color.floatToByte(u);return a}const m=(h-d)/s,b=(C-f)/s,P=(T-y)/s,_=(g-u)/s;let B=a;for(c=0;c<s;c++)i[B++]=r.Color.floatToByte(d+c*m),i[B++]=r.Color.floatToByte(f+c*b),i[B++]=r.Color.floatToByte(y+c*P),i[B++]=r.Color.floatToByte(u+c*_);return B}function b(l){const n=(l=e.defaultValue(l,e.defaultValue.EMPTY_OBJECT)).positions,i=l.colors,a=e.defaultValue(l.colorsPerVertex,!1);this._positions=n,this._colors=i,this._colorsPerVertex=a,this._arcType=e.defaultValue(l.arcType,t.ArcType.GEODESIC),this._granularity=e.defaultValue(l.granularity,c.CesiumMath.RADIANS_PER_DEGREE),this._ellipsoid=e.defaultValue(l.ellipsoid,o.Ellipsoid.WGS84),this._workerName="createSimplePolylineGeometry";let s=1+n.length*o.Cartesian3.packedLength;s+=e.defined(i)?1+i.length*r.Color.packedLength:1,this.packedLength=s+o.Ellipsoid.packedLength+3}b.pack=function(t,l,n){let i;n=e.defaultValue(n,0);const a=t._positions;let s=a.length;for(l[n++]=s,i=0;i<s;++i,n+=o.Cartesian3.packedLength)o.Cartesian3.pack(a[i],l,n);const c=t._colors;for(s=e.defined(c)?c.length:0,l[n++]=s,i=0;i<s;++i,n+=r.Color.packedLength)r.Color.pack(c[i],l,n);return o.Ellipsoid.pack(t._ellipsoid,l,n),n+=o.Ellipsoid.packedLength,l[n++]=t._colorsPerVertex?1:0,l[n++]=t._arcType,l[n]=t._granularity,l},b.unpack=function(t,l,n){let i;l=e.defaultValue(l,0);let a=t[l++];const s=new Array(a);for(i=0;i<a;++i,l+=o.Cartesian3.packedLength)s[i]=o.Cartesian3.unpack(t,l);a=t[l++];const c=a>0?new Array(a):void 0;for(i=0;i<a;++i,l+=r.Color.packedLength)c[i]=r.Color.unpack(t,l);const p=o.Ellipsoid.unpack(t,l);l+=o.Ellipsoid.packedLength;const d=1===t[l++],f=t[l++],y=t[l];return e.defined(n)?(n._positions=s,n._colors=c,n._ellipsoid=p,n._colorsPerVertex=d,n._arcType=f,n._granularity=y,n):new b({positions:s,colors:c,ellipsoid:p,colorsPerVertex:d,arcType:f,granularity:y})};const P=new Array(2),_=new Array(2),B={positions:P,height:_,ellipsoid:void 0,minDistance:void 0,granularity:void 0};return b.createGeometry=function(d){const f=d._positions,y=d._colors,u=d._colorsPerVertex,h=d._arcType,C=d._granularity,T=d._ellipsoid,g=c.CesiumMath.chordLength(C,T.maximumRadius),b=e.defined(y)&&!u;let A;const E=f.length;let k,G,D,L,w=0;if(h===t.ArcType.GEODESIC||h===t.ArcType.RHUMB){let o,l,n;h===t.ArcType.GEODESIC?(o=c.CesiumMath.chordLength(C,T.maximumRadius),l=p.PolylinePipeline.numberOfPoints,n=p.PolylinePipeline.generateArc):(o=C,l=p.PolylinePipeline.numberOfPointsRhumbLine,n=p.PolylinePipeline.generateRhumbArc);const i=p.PolylinePipeline.extractHeights(f,T),a=B;if(h===t.ArcType.GEODESIC?a.minDistance=g:a.granularity=C,a.ellipsoid=T,b){let t=0;for(A=0;A<E-1;A++)t+=l(f[A],f[A+1],o)+1;k=new Float64Array(3*t),D=new Uint8Array(4*t),a.positions=P,a.height=_;let s=0;for(A=0;A<E-1;++A){P[0]=f[A],P[1]=f[A+1],_[0]=i[A],_[1]=i[A+1];const o=n(a);if(e.defined(y)){const e=o.length/3;L=y[A];for(let o=0;o<e;++o)D[s++]=r.Color.floatToByte(L.red),D[s++]=r.Color.floatToByte(L.green),D[s++]=r.Color.floatToByte(L.blue),D[s++]=r.Color.floatToByte(L.alpha)}k.set(o,w),w+=o.length}}else if(a.positions=f,a.height=i,k=new Float64Array(n(a)),e.defined(y)){for(D=new Uint8Array(k.length/3*4),A=0;A<E-1;++A){w=m(f[A],f[A+1],y[A],y[A+1],g,D,w)}const e=y[E-1];D[w++]=r.Color.floatToByte(e.red),D[w++]=r.Color.floatToByte(e.green),D[w++]=r.Color.floatToByte(e.blue),D[w++]=r.Color.floatToByte(e.alpha)}}else{G=b?2*E-2:E,k=new Float64Array(3*G),D=e.defined(y)?new Uint8Array(4*G):void 0;let t=0,l=0;for(A=0;A<E;++A){const n=f[A];if(b&&A>0&&(o.Cartesian3.pack(n,k,t),t+=3,L=y[A-1],D[l++]=r.Color.floatToByte(L.red),D[l++]=r.Color.floatToByte(L.green),D[l++]=r.Color.floatToByte(L.blue),D[l++]=r.Color.floatToByte(L.alpha)),b&&A===E-1)break;o.Cartesian3.pack(n,k,t),t+=3,e.defined(y)&&(L=y[A],D[l++]=r.Color.floatToByte(L.red),D[l++]=r.Color.floatToByte(L.green),D[l++]=r.Color.floatToByte(L.blue),D[l++]=r.Color.floatToByte(L.alpha))}}const V=new a.GeometryAttributes;V.position=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:k}),e.defined(y)&&(V.color=new i.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:4,values:D,normalize:!0})),G=k.length/3;const x=2*(G-1),S=s.IndexDatatype.createTypedArray(G,x);let I=0;for(A=0;A<G-1;++A)S[I++]=A,S[I++]=A+1;return new i.Geometry({attributes:V,indices:S,primitiveType:i.PrimitiveType.LINES,boundingSphere:l.BoundingSphere.fromPoints(f)})},function(t,l){return e.defined(l)&&(t=b.unpack(t,l)),t._ellipsoid=o.Ellipsoid.clone(t._ellipsoid),b.createGeometry(t)}}));
