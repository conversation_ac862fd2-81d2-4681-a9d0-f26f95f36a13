<template>
	<div class="divApp">
		<div class="dt" id="EarthContainer" ref="EarthContainer"></div>
		<nav class="nav-bar">
			<!-- 左侧标题 -->
			<div class="left-title">
				<img src="/@/assets/pigx-app.png" alt="Logo" class="logo-image" />
				<span class="title-text">扶绥县"AI+无人机场"监测监管平台（渠旧试点）</span>
			</div>

			<!-- 右侧按钮组 -->
			<div class="right-buttons">
				<el-button @click="toggleLayerPanel" class="nav-button">图层管理</el-button>
				<el-button @click="resultCenterFun" class="nav-button">结果中心</el-button>
				<el-tooltip content="一张图" placement="bottom" effect="dark">
					<el-button @click="goToOneMap" class="nav-button">一张图</el-button>
				</el-tooltip>
				<el-button @click="goToHomeFun" class="nav-button">首页</el-button>

			</div>
		</nav>
		<!-- 主内容区 -->
		<div id="main-content">
			<!-- B区域 -->
			<aside class="b ydBox" id="left" v-show="!isLayerManagementMode">
				<div class="b-section ddBox">
					<div class="item-title" style="flex: 1">
						<div style="flex: 5">
							<span class="item-title-1">/</span>
							<span class="item-title-2">/ </span>
							<span>事件数Top7</span>
						</div>
						<TransparentSelect
							v-model="eventTop10Select"
							@update:modelValue="selectEventTop10Change"
							:options="[
								{ value: '本年', label: '本年' },
								{ value: '本月', label: '本月' },
								{ value: '本日', label: '本日' },
							]"
							placeholder="请选择"
						/>
					</div>
					<hr class="item-title-hr" />
					<div
						class="auto-list"
						v-loading="top10Loading"
						element-loading-text="加载中..."
						element-loading-background="rgba(0, 0, 0, 0)"
						ref="sjCountChartRef"
						v-show="tuShow"
					></div>
					<div v-show="!tuShow">
						<div style="text-align: center; font-size: 20px; margin-top: 100px; color: #c8dede">暂无数据</div>
					</div>
				</div>
				<div class="b-section ydBox no-hide">
					<div class="item-title">
						<div style="flex: 5">
							<span class="item-title-1">/</span>
							<span class="item-title-2">/ </span>
							<span>航飞任务</span>
						</div>
						<!-- <el-button class="c-bottom-font" style="float: right" @click="SwitchDronePanelStatus(val.taskId)">更多 ></el-button> -->
						<el-button v-show="dtBig" class="c-bottom-font" @click="restoreAll">关闭</el-button>
					</div>
					<hr class="item-title-hr" />
					<div class="auto-list">
						<div v-for="(val, index) in realTImeTaskList" :key="index">
							<div class="task-item" :class="{ highlight: val.isSelected }" @click="handleTaskClick(val)">
								<div class="task-item-1">
									<span>{{ val.airLineName }}</span>
									<span style="float: right">执行成功</span>
								</div>
								<div class="task-item-2">
									<span>{{ val.uavName }}</span>
									<span style="margin-left: 30px">{{ val.uavModel }}</span>
									<span style="float: right">{{ val.startTime }}</span>
								</div>
							</div>
						</div>
					</div>
					<VideoControlCom v-if="DroneControlStore.DronePanelShow"></VideoControlCom>
				</div>
			</aside>

			<!-- C区域 -->
			<main class="c" v-show="!isLayerManagementMode">
				<div class="c-top ydBox ddBox" id="top">
					<div class="c-item">
						<div class="c-item-postion">
							机场总数
							<p class="c-item-1">{{ uavAndEventInfo.airportNum }}</p>
						</div>
					</div>
					<div class="c-item">
						<div class="c-item-postion">
							飞行器总数
							<p class="c-item-2">{{ uavAndEventInfo.uavNum }}</p>
						</div>
					</div>
					<div class="c-item">
						<div class="c-item-postion">
							航线总数
							<p class="c-item-3">52</p>
						</div>
					</div>
					<div class="c-item">
						<div class="c-item-postion">
							执飞架次数
							<p class="c-item-4">{{ uavAndEventInfo.airTaskNum }}</p>
						</div>
					</div>
					<div class="c-item">
						<div class="c-item-postion">
							有效事件数
							<p class="c-item-5">{{ uavAndEventInfo.yxEventNum }}</p>
						</div>
					</div>
				</div>
				<div class="c-middle"></div>
				<div class="c-bottom ydBox ddBox" id="bottom">
					<div class="c-bottom-2">
						<div class="c-bottom-item c-bottom-item-1">
							<div class="c-item-postion">
								<span class="c-bottom-font-big" style="color: rgb(205, 129, 47)">{{ serviceCountInfo.dayServiceCount }}</span>
								<span> / </span>
								<span class="c-bottom-font">{{ serviceCountInfo.allServiceCount }}</span>
								<div>本日发布服务数</div>
							</div>
						</div>
						<div class="c-bottom-item c-bottom-item-2">
							<div class="c-item-postion">
								<span class="c-bottom-font-big">{{ serviceCountInfo.monthServiceCount }}</span>
								<span> / </span>
								<span class="c-bottom-font">{{ serviceCountInfo.allServiceCount }}</span>
								<div>本月发布服务数</div>
							</div>
						</div>
						<div class="c-bottom-item c-bottom-item-2">
							<div class="c-item-postion">
								<span class="c-bottom-font-big">{{ serviceCountInfo.yearServiceCount }}</span>
								<span> / </span>
								<span class="c-bottom-font">{{ serviceCountInfo.allServiceCount }}</span>
								<div>本年发布服务数</div>
							</div>
						</div>
						<div class="c-bottom-item c-bottom-item-2">
							<div class="c-item-postion">
								<span class="c-bottom-font-big">{{ uavAndEventInfo.eventDayCount }}</span>
								<span> / </span>
								<span class="c-bottom-font">{{ uavAndEventInfo.eventNum }}</span>
								<div>本日事件数</div>
							</div>
						</div>
						<div class="c-bottom-item c-bottom-item-2">
							<div class="c-item-postion">
								<span class="c-bottom-font-big">{{ uavAndEventInfo.eventMonthCount }}</span>
								<span> / </span>
								<span class="c-bottom-font">{{ uavAndEventInfo.eventNum }}</span>
								<div>本月事件数</div>
							</div>
						</div>
						<div class="c-bottom-item c-bottom-item-2">
							<div class="c-item-postion">
								<span class="c-bottom-font-big">{{ uavAndEventInfo.eventYearCount }}</span>
								<span> / </span>
								<span class="c-bottom-font">{{ uavAndEventInfo.eventNum }}</span>
								<div>本年事件数</div>
							</div>
						</div>
					</div>
				</div>
			</main>

			<!-- D区域 -->
			<aside class="d ydBox ddBox" id="right" v-show="!isLayerManagementMode">
				<div class="d-chart">
					<div class="item-title">
						<div style="flex: 5">
							<span class="item-title-1">/</span>
							<span class="item-title-2">/ </span>
							<span>事件类型</span>
						</div>
						<TransparentSelect
							v-model="eventTypeSelect"
							@update:modelValue="selectEventTypeChange"
							:options="[
								{ value: '本年', label: '本年' },
								{ value: '本月', label: '本月' },
								{ value: '本日', label: '本日' },
							]"
							placeholder="请选择"
						/>
					</div>
					<hr class="item-title-hr" />
					<div class="auto-list">
						<div
							style="height: 30vh"
							ref="sjTypeChartRef"
							v-loading="enevtTypeLoading"
							element-loading-text="加载中..."
							element-loading-background="rgba(0, 0, 0, 0)"
						></div>
					</div>
				</div>
				<div class="d-chart">
					<div class="item-title" style="flex: 1">
						<div style="flex: 5">
							<span class="item-title-1">/</span>
							<span class="item-title-2">/ </span>
							<span>实时事件</span>
						</div>
						<!-- <span class="more-calss">更多 ></span> -->
					</div>
					<hr class="item-title-hr" />
					<div class="auto-list table-wrapper">
						<el-table :data="realEventList" :row-class-name="tableRowClassName" style="width: 100%; height: 38vh; color: #ffffff">
							<el-table-column prop="name" align="center" label="名称" />
							<el-table-column prop="startTime" width="180" align="center" label="时间" />
							<el-table-column prop="status" align="center" width="60" label="状态">
								<template #default="{ row }">
									<span v-if="row.status === '0'"> 未审核 </span>
									<span v-else> 已审核 </span>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</div>
			</aside>
		</div>
		
		<!-- 图层管理面板 -->
		<LayerPanel v-if="showLayerPanel" @close="closeLayerPanel" />
	</div>
</template>

<script lang="ts" name="bigScreenIndex" setup>
import { NextLoading } from '/@/utils/loading';
import * as Cesium from 'cesium';
import { fa } from 'element-plus/es/locale';
import { onMounted, ref, reactive, nextTick } from 'vue';
import * as echarts from 'echarts';
import { markRaw } from 'vue';
import {
	getBusinessResultList,
	getRealTimeTaskList,
	getVideoInfoByTaskId,
	getRealEventList,
	getUavAndEventInfo,
	getEventDataHx,
	getEventTop10,
	getServiceCount,
} from '/@/api/admin/bigScreen';
import http from '/@/api/cesium/drone';
import VideoControlCom from '/@/components/Video/VideoControlCom.vue';
import { useEarthStore } from '/@/stores/earth';
import { useDroneControlStore } from '/@/stores/droneControl/droneControl.ts';
import TransparentSelect from '/@/components/TransparentSelect/index.vue';
import { useMapLayerManagerStore } from '/@/stores/mapLayer';
import { ElMessage } from 'element-plus';
import { CircleClose, Menu } from '@element-plus/icons-vue';
import { LayerPanel } from '/@/components/LayerManager';

const DroneControlStore = useDroneControlStore();
const router = useRouter();

const viewer = ref();

const sjCountChartRef = ref();
const sjTypeChartRef = ref();
const CharOption = reactive({
	sjCountOption: {
		textStyle: {
			fontFamily: 'DingTalk', // 字体类型
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				// Use axis to trigger tooltip
				type: 'line', // 'shadow' as default; can also be 'line' or 'shadow'
			},
			show: true,
		},
		legend: { show: false },
		grid: {
			left: '2%',
			right: '0%',
			bottom: '0%',
			top: '8%',
			// containLabel: true,
		},
		xAxis: {
			type: 'value',
			show: false,
		},
		yAxis: {
			type: 'category',
			data: [],
			// data: ['5 中东镇', '4 东罗镇', '3 东门镇', '2 昌平乡', '1 渠旧镇'],
			axisTick: { show: false }, //是否显示坐标轴刻度
			axisLine: {
				show: false, //是否显示坐标轴轴线
			},
			axisLabel: {
				// 控制坐标轴标签
				show: true,
				inside: true,
				interval: 0, //横轴信息全部显
				textStyle: {
					color: '#C8DEDE',
					verticalAlign: 'bottom',
					fontSize: 16,
					align: 'left',
					padding: [0, 0, 10, -5],
				},
				padding: [0, 0, 10, 0],
				margin: 0,
			},
		},
		series: [
			// {
			// 	name: '违法种植树木',
			// 	type: 'bar',
			// 	stack: 'total',
			// 	barWidth: 10,
			// 	data: [320, 302, 301, 334, 390],
			// },
			// {
			// 	name: '违法动土',
			// 	type: 'bar',
			// 	stack: 'total',
			// 	barWidth: 10,
			// 	data: [120, 132, 101, 134, 90],
			// },
		],
	},
	sjTypeCharOption: {
		textStyle: {
			fontFamily: 'DingTalk', // 字体类型
		},
		tooltip: {
			trigger: 'item',
		},
		legend: {
			top: '5%',
			left: 'center',
			textStyle: {
				color: '#C8DEDE', // 图例文字颜色
			},
		},
		series: [
			{
				name: '事件类型',
				type: 'pie',
				radius: ['60%', '30%'],
				center: ['50%', '60%'],
				avoidLabelOverlap: false,
				padAngle: 5,
				itemStyle: {
					borderRadius: 10,
					marginTop: 100,
				},
				label: {
					show: false,
					position: 'center',
				},
				emphasis: {
					label: {
						show: false,
						fontSize: 20,
						fontWeight: 'bold',
					},
				},
				labelLine: {
					show: false,
				},
				data: [],
				color: ['#dd6b66','#759aa0','#e69d87','#8dc1a9','#ea7e53','#eedd78','#73a373','#73b9bc','#7289ab', '#91ca8c','#f49f42'],
			},
		],
	},
});

const tuShow = ref(true);

const getSjCountCharts = async () => {
	if (eventTop10Data.value.resList.length > 0) {
		tuShow.value = true;
	} else {
		tuShow.value = false;
	}
	await sliceData(eventTop10Data.value);
	const sjCountChart = markRaw(echarts.init(sjCountChartRef.value));
	sjCountChart.setOption(CharOption.sjCountOption);
	top10Loading.value = false;
};

const sliceData = (data: any) => {
	CharOption.sjCountOption.yAxis.data = data.cityNames.slice(-7);
	data.resList.map((item, index) => {
		item.data = item.data.slice(-7);
		if (index + 1 === data.resList.length) {
			CharOption.sjCountOption.series = data.resList;
		}
	});
};

const enevtTypeLoading = ref(true);
const getSjTypeCharts = () => {
	CharOption.sjTypeCharOption.series[0].data = eventTypeData.value;
	const sjTypeChart = markRaw(echarts.init(sjTypeChartRef.value));
	sjTypeChart.setOption(CharOption.sjTypeCharOption);
	enevtTypeLoading.value = false;
};

const earthStore = useEarthStore();
const EarthContainer = ref(null);

const dtBig = ref(false);
const visible = ref(false);
const isLoading = ref(true);

// 地图全屏
const toggleFullscreen = () => {
	let isHidden = false;
	let vw = Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0);
	let vh = Math.max(document.documentElement.clientHeight || 0, window.innerHeight || 0);

	const boxes = document.querySelectorAll('.ydBox');
	dtBig.value = !dtBig.value;
	boxes.forEach((box) => {
		// 检查元素是否有 'no-hide' 类，如果有则跳过
		if (box.classList.contains('no-hide')) {
			return;
		}
		const rect = box.getBoundingClientRect();
		box.style.transitionDuration = '0.6s';

		if (!isHidden) {
			box.style.setProperty('--move-x', `${vw}px`);
			box.style.setProperty('--move-y', `${vh}px`);
		}

		box.classList.toggle('hidden');
	});

	isHidden = !isHidden;
};

const closeBigScreen = () => {
	window.close();
};

const realTImeTaskList = ref({});

const getRealTimeTaskListFun = async () => {
	const res = await getRealTimeTaskList({});
	realTImeTaskList.value = res.data.map((task: any) => ({ ...task, isSelected: false }));
};

const getVideoInfoFun = async (id: string) => {
	const res = await getVideoInfoByTaskId({ taskId: id });
	// console.log(res.data[0]["videoList"])
	DroneControlStore.setData(res.data[0]['videoList']);
};

// 实时事件
interface RealEvent {
	name: string;
	startTime: string;
	status: '0' | '1';
}

const realEventList = ref<RealEvent[]>([]);

// 行样式判断
const tableRowClassName = ({ row }: { row: RealEvent }) => {
	return row.status === '0' ? 'highlight-row' : '';
};

// 审核点击事件
const handleAudit = (row: RealEvent) => {
	console.log('审核项目：', row.name);
	// 这里添加审核逻辑
};

const getRealEventListFun = async () => {
	const res = await getRealEventList({});
	console.log(res);
	realEventList.value = res.data;
};

const uavAndEventInfo = ref({
	airportNum: 0,        // 机场总数
	uavNum: 0,           // 飞行器总数
	airLineNum: 0,       // 航线总数
	airTaskNum: 0,       // 执飞架次数
	yxEventNum: 0,       // 有效事件数
	eventDayCount: 0,    // 本日事件数
	eventMonthCount: 0,  // 本月事件数
	eventYearCount: 0,   // 本年事件数
	eventNum: 0,         // 事件总数
	taskMonthCount: 0,   // 本月任务数
	taskYearCount: 0     // 本年任务数
});

const getUavAndEventInfoFun = async () => {
	try {
		const res = await getUavAndEventInfo();
		console.log('getUavAndEventInfo 接口返回数据:', res.data);
		uavAndEventInfo.value = res.data;
	} catch (error) {
		console.error('获取无人机和事件信息失败:', error);
	}
};

// 发布服务数统计数据
const serviceCountInfo = ref({
	allServiceCount: 0,      // 发布服务总数
	dayServiceCount: 0,      // 每日发布服务数
	monthServiceCount: 0,    // 每月发布服务数
	yearServiceCount: 0      // 每年发布服务数
});

const getServiceCountFun = async () => {
	try {
		const res = await getServiceCount({});
		serviceCountInfo.value = res.data;
	} catch (error) {
		console.error('获取发布服务数失败:', error);
	}
};

// var DronePanelShow = ref(false);

const moreTask = async () => {
	console.log(121313);
};

const resultCenterFun = async () => {
	router.push('/manage/verify/index');
};

const goToHomeFun = async () => {
	router.push('/home');
};

// 跳转到一张图页面
const goToOneMap = () => {
	// 在新窗口打开一张图页面
	const routeUrl = router.resolve({
		path: '/oneMap/index'
	});
	window.open(routeUrl.href, '_blank');
};

const eventTop10Select = ref('本年');
const top10Loading = ref(true);
const selectEventTop10Change = async () => {
	top10Loading.value = true;
	await getEventTop10Fun();
	getSjCountCharts();
};

const eventTypeSelect = ref('本年');
const selectEventTypeChange = async () => {
	enevtTypeLoading.value = true;
	await getEventTypeDataFun();
	getSjTypeCharts();
};

const getBusinessResultListFun = async (videoResultId: any) => {
	// const res = await getBusinessResultList({ videoResultId: videoResultId });
};

const SwitchDronePanelStatus = (taskId: any) => {
	DroneControlStore.taskId = taskId;
	DroneControlStore.DronePanelShow = true;

	let vw = Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0);
	let vh = Math.max(document.documentElement.clientHeight || 0, window.innerHeight || 0);

	const boxes = document.querySelectorAll('.ddBox');
	boxes.forEach((box) => {
		// 检查元素是否有 'no-hide' 类，如果有则跳过
		if (box.classList.contains('no-hide')) {
			return;
		}
		// 检查元素是否已经隐藏，如果已经隐藏则不做处理
		if (box.classList.contains('hidden')) {
			return;
		}
		const rect = box.getBoundingClientRect();
		box.style.transitionDuration = '0.6s';
		box.style.setProperty('--move-x', `${vw}px`);
		box.style.setProperty('--move-y', `${vh}px`);
		box.classList.add('hidden');
	});
};

// 新增：还原全部隐藏的大屏内容，并且取消选中的数据
const restoreAll = () => {
	// 还原隐藏的大屏内容
	let vw = Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0);
	let vh = Math.max(document.documentElement.clientHeight || 0, window.innerHeight || 0);

	const boxes = document.querySelectorAll('.ddBox');
	dtBig.value = false;
	boxes.forEach((box) => {
		if (box.classList.contains('no-hide')) {
			return;
		}
		const rect = box.getBoundingClientRect();
		box.style.transitionDuration = '0.6s';
		box.style.setProperty('--move-x', '0px');
		box.style.setProperty('--move-y', '0px');
		box.classList.remove('hidden');
	});

	// 取消选中的数据
	eventTop10Select.value = '本年';
	eventTypeSelect.value = '本年';
	DroneControlStore.DronePanelShow = false;
	realTImeTaskList.value.forEach((task) => (task.isSelected = false));
};

// 处理任务点击事件
const handleTaskClick = async (task: { isSelected: boolean; taskId: any }) => {
	if (DroneControlStore.DronePanelShow) {
		DroneControlStore.DronePanelShow = false;
		// 使用nextTick等待组件完全卸载
		await nextTick();
		// 确保有足够的时间执行onUnmounted钩子
	}

	realTImeTaskList.value.forEach((t) => (t.isSelected = false));
	task.isSelected = true;
	SwitchDronePanelStatus(task.taskId);
	dtBig.value = true;
};

const eventTypeData = ref([]);

const getEventTypeDataFun = async () => {
	let dateStr = 'year';
	if (eventTypeSelect.value === '本日') {
		dateStr = 'today';
	} else if (eventTypeSelect.value === '本月') {
		dateStr = 'month';
	}
	const res = await getEventDataHx({ date: dateStr });
	eventTypeData.value = res.data;
};

const eventTop10Data = ref([]);
const getEventTop10Fun = async () => {
	let dateStr = 'year';
	if (eventTop10Select.value === '本日') {
		dateStr = 'today';
	} else if (eventTop10Select.value === '本月') {
		dateStr = 'month';
	}
	const res = await getEventTop10({ date: dateStr });
	eventTop10Data.value = res.data;
};

const loadData = () => {
	selectEventTypeChange();
	selectEventTop10Change();
	getRealTimeTaskListFun();
	// getVideoInfoFun();
	getRealEventListFun();
	getUavAndEventInfoFun();
	getServiceCountFun(); // 获取发布服务数统计
	// getBusinessResultListFun("test01_v1_m1")
};

// 页面加载时
onMounted(() => {
	NextLoading.done();
	loadData();
	// 移除Cesium.Ion令牌设置，避免尝试访问外网资源
	// Cesium.Ion.defaultAccessToken = '...';
	
	let viewer = new Cesium.Viewer(EarthContainer.value, {
		geocoder: false, //位置查找工具
		homeButton: false, //复位按钮
		sceneModePicker: false, //模式切换
		baseLayerPicker: false, //图层选择
		navigationHelpButton: false, //帮助按钮
		animation: false, //速度控制
		timeline: false, //时间轴
		infoBox: false,
		// 禁用默认的Bing Maps底图
		imageryProvider: false,
		// 禁止默认在线地形服务
		terrainProvider: new Cesium.EllipsoidTerrainProvider()
	});
	earthStore.setViewer(viewer);
});

/**
 * 切换点图层显示模式（图片/3D模型）
 */
const qiehuanModel = () => {
	// 获取图层管理器实例
	const layerManager = useMapLayerManagerStore();

	// 遍历所有已加载的图层
	if (layerManager.mapConfig) {
		// 查找类型为CustomPoint的图层
		const pointLayers = layerManager.mapConfig.layers.filter(
			(layer) => layer.type === 'CustomPoint' && layer.protocol === 'CustomPointJSON' && layer.active && layer.layerInstance
		);

		// 对每个找到的点图层执行toggleDisplayMode
		if (pointLayers.length > 0) {
			pointLayers.forEach((layer) => {
				if (layer.layerInstance && typeof layer.layerInstance.toggleDisplayMode === 'function') {
					// 切换显示模式
					const isUsingModels = layer.layerInstance.toggleDisplayMode();
					// 显示提示消息
					ElMessage.success(`已切换为${isUsingModels ? '3D模型' : '图片'}显示模式`);
				}
			});
		} else {
			ElMessage.info('没有找到可切换的点图层');
		}
	} else {
		ElMessage.warning('地图配置未加载');
	}
};

// 图层管理面板显示状态
const showLayerPanel = ref(false);

// 图层管理模式状态（控制其他模块的显示/隐藏）
const isLayerManagementMode = ref(false);

// 切换图层管理面板显示/隐藏
const toggleLayerPanel = () => {
	showLayerPanel.value = !showLayerPanel.value;
	isLayerManagementMode.value = showLayerPanel.value; // 同步图层管理模式状态
};

// 关闭图层管理面板
const closeLayerPanel = () => {
	showLayerPanel.value = false;
	isLayerManagementMode.value = false; // 恢复所有模块显示
};
</script>

<style scoped>
.more-calss {
	float: right;
	cursor: pointer;
}

.divApp {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
	font-family: 'DingTalk';
}

/* 顶部导航栏 */
.nav-bar {
	height: 10vh;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 20px;
	background-color: rgba(2, 49, 52, 0.8);
	position: relative;
	z-index: 2;
}

/* 左侧标题 */
.left-title {
	display: flex;
	align-items: center;
	gap: 15px; /* logo和文字之间的间距 */
}

/* Logo样式 */
.logo-image {
	height: 40px; /* 设置logo高度 */
	width: auto; /* 保持宽高比 */
	object-fit: contain; /* 保持图片比例 */
	padding: 4px;
	background: linear-gradient(135deg,
		rgba(255, 255, 255, 0.95) 0%,
		rgba(248, 250, 252, 0.9) 100%
	);
	border-radius: 12px;
	box-shadow:
		0 4px 12px rgba(0, 0, 0, 0.15),
		0 2px 6px rgba(13, 71, 161, 0.2),
		inset 0 1px 0 rgba(255, 255, 255, 0.8);
	border: 1px solid rgba(255, 255, 255, 0.3);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	z-index: 3;
}

.logo-image::before {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: linear-gradient(45deg,
		transparent,
		rgba(34, 139, 94, 0.1),
		transparent
	);
	transform: rotate(45deg);
	transition: all 0.6s ease;
	opacity: 0;
}

.logo-image:hover {
	transform: scale(1.08) rotate(2deg);
	box-shadow:
		0 8px 25px rgba(0, 0, 0, 0.3),
		0 4px 12px rgba(34, 139, 94, 0.4),
		inset 0 1px 0 rgba(255, 255, 255, 1);
	border-color: rgba(34, 139, 94, 0.7);
}

.logo-image:hover::before {
	opacity: 1;
	transform: rotate(45deg) translate(50%, 50%);
}

/* 右侧按钮组 */
.right-buttons {
	display: flex;
	gap: 15px;
	position: relative;
	z-index: 2;
}

/* 导航按钮样式 */
.nav-button {
	background: linear-gradient(135deg,
		rgba(34, 139, 94, 0.9) 0%,
		rgba(46, 160, 108, 0.8) 50%,
		rgba(34, 139, 94, 0.9) 100%
	);
	color: #ffffff;
	border: 2px solid rgba(34, 139, 94, 0.6);
	padding: 12px 24px;
	border-radius: 10px;
	cursor: pointer;
	font-size: 16px;
	/* font-weight: 600; */
	letter-spacing: 0.5px;
	transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
	backdrop-filter: blur(10px);
	box-shadow:
		0 4px 15px rgba(0, 0, 0, 0.2),
		inset 0 1px 0 rgba(255, 255, 255, 0.2);
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.nav-button::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg,
		transparent,
		rgba(255, 255, 255, 0.2),
		transparent
	);
	transition: left 0.6s ease;
}

.nav-button:hover {
	background: linear-gradient(135deg,
		rgba(46, 160, 108, 1) 0%,
		rgba(67, 211, 129, 0.9) 50%,
		rgba(46, 160, 108, 1) 100%
	);
	border-color: rgba(67, 211, 129, 0.8);
	transform: translateY(-2px) scale(1.05);
	box-shadow:
		0 8px 25px rgba(0, 0, 0, 0.3),
		0 4px 15px rgba(34, 139, 94, 0.4),
		inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.nav-button:hover::before {
	left: 100%;
}

.nav-button:active {
	transform: translateY(0) scale(1.02);
	box-shadow:
		0 4px 15px rgba(0, 0, 0, 0.2),
		inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 标题文字样式 */
.title-text {
	font-size: 28px;
	color: white;
	letter-spacing: 2px;
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.icon-button {
	width: 32px;
	height: 32px;
	border-radius: 30%;
	/* background-color: rgba(24, 144, 255, 0.8); */
	background-color: #2a7729;
	border: none;
	color: white;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s;
}

.icon-button:hover {
	background-color: rgba(24, 144, 255, 1);
}

/* 主内容区 */
#main-content {
	display: flex;
	gap: 0;
	padding: 0;
	min-height: 90vh;
}

/* B区域 */
.b {
	flex: 1.5;
	border-radius: 12px;
	padding: 5px;
	display: flex;
	flex-direction: column;
	gap: 0px;
	height: 90vh;
}

.b-section {
	padding: 15px;
	flex: 1;
	overflow: hidden;
	z-index: 2;
	border-radius: 10px;
	background-color: rgba(2, 49, 52, 0.8);
	margin-top: 10px;
}

.b-section:first-child {
	flex: 5;
}

.b-section:last-child {
	flex: 5;
	margin-top: 10px;
}

.auto-list {
	height: 200px;
	height: 90%;
	overflow-y: auto;
	/* 隐藏滚动条 - 兼容所有浏览器 */
	scrollbar-width: none; /* Firefox */
	-ms-overflow-style: none; /* IE 和 Edge */
}

/* 隐藏 Webkit 浏览器（Chrome、Safari、新版 Edge）的滚动条 */
.auto-list::-webkit-scrollbar {
	display: none;
}

.task-item {
	display: flex;
	flex-direction: column;
	gap: 10px;
	padding: 10px;
	border: 1px solid rgba(255, 255, 255, 0.3);
	background: radial-gradient(50% 50% at 50% 50%, #fff0, #ffffff1a);
	border-radius: 10px;
	cursor: pointer;
	color: white;
	margin-top: 10px;
}

.item-title {
	/* flex: 0.5; */
	color: #c8dede;
	font-size: 16px;
	display: flex;
	align-items: center;
	gap: 5px;
	/* height: 10%; */
}

.item-title-hr {
	margin-top: 8px;
	color: rgb(1, 143, 143);
}

/* C区域 */
.c {
	flex: 4;
	background-color: rgb(2, 49, 52);
	border-radius: 10px;
	padding: 10px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
	display: flex;
	flex-direction: column;
	grid-template-rows: auto 1fr auto;
	gap: 10px;
	height: 90vh;
}

.c-top {
	flex: 0.8;
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	gap: 10px;
	z-index: 2;
}

.item-title-1 {
	color: rgb(1, 255, 254);
}

.item-title-2 {
	color: rgb(1, 143, 143);
}

.c-middle {
	flex: 4;
	z-index: -1;
}

.c-bottom {
	flex: 0.5;
	display: flex;
	flex-direction: column;
	z-index: 2;
}

.c-bottom-2 {
	display: grid;
	grid-template-columns: repeat(6, 1fr);
	gap: 20px;
	/* flex: 1; */
	margin-top: 10px;
}

.c-item {
	flex: 1;
	height: 100px;
	display: flex;
	align-items: center;
	justify-content: center; /* 添加水平居中 */
	/* background: rgb(65, 81, 82); */
	border-radius: 10px;
	/* padding: 10px; */
	text-align: center;
	cursor: pointer;
	color: #ffffff;
	font-size: 24px;
	transition: 0.3s;
	/* background: linear-gradient(to right, rgba(205, 129, 47, 0.2), rgba(43, 43, 29, 0.5)); */
	background-color: rgba(2, 49, 52, 0.8);
	padding: 22px;
}
.c-item-postion {
	text-align: center;
}

.c-bottom-item {
	border-radius: 8px;
	padding: 10px;
	text-align: center;
	transition: transform 0.2s;
	cursor: pointer;
	color: #ffffff;
	font-size: 20px;
	display: flex;
	justify-content: center; /* 水平居中，如果需要的话 */
	align-items: center;
}

.c-bottom-item-1 {
	border: 1px solid rgb(205, 129, 47);
	background: linear-gradient(to right, rgba(205, 129, 47, 0.2), rgba(250, 247, 247, 0.1));
	background-color: rgba(2, 49, 52, 0.8);
}

.c-bottom-item-2 {
	border: 1px solid rgb(93, 104, 106);
	background: linear-gradient(to right, rgba(205, 129, 47, 0.2), rgba(43, 43, 29, 0.1));
	background-color: rgba(2, 49, 52, 0.8);
}

.c-bottom-font-big {
	font-size: 30px;
}

.c-bottom-font {
	background: transparent;
	border: none;
	color: #c8dede;
}

.c-item-1 {
	font-size: 36px;
	color: rgb(15, 245, 245);
	text-align: center;
	margin: 0; /* 消除p标签默认margin */
}

.c-item-2 {
	font-size: 36px;
	color: rgb(15, 245, 118);
	text-align: center;
	margin: 0; /* 消除p标签默认margin */
}

.c-item-3 {
	font-size: 36px;
	color: rgb(119, 130, 230);
	text-align: center;
	margin: 0; /* 消除p标签默认margin */
}

.c-item-4 {
	font-size: 36px;
	color: rgb(59, 209, 122);
	text-align: center;
	margin: 0; /* 消除p标签默认margin */
}

.c-item-5 {
	font-size: 36px;
	color: rgb(134, 15, 245);
	text-align: center;
	margin: 0; /* 消除p标签默认margin */
}

.c-item:hover {
	transform: translateY(-3px);
}

.dt {
	position: absolute;
	z-index: 1;
	background-color: rebeccapurple;
	height: 100vh;
	width: 100%;
}

/* D区域 */
.d {
	flex: 1.5;
	border-radius: 12px;
	padding: 5px;
	display: flex;
	flex-direction: column;
	gap: 0px;
	height: 90vh;
}

.d-chart {
	border-radius: 10px;
	padding: 15px;
	flex: 1;
	z-index: 2;
	margin-top: 10px;
	background-color: rgba(2, 49, 52, 0.8);
}

.d-chart:first-child {
	flex: 5;
}

.d-chart:last-child {
	flex: 5;
	margin-top: 10px;
}

/* 消失状态 */
.hidden#top {
	transform: translateX(-5%) translateY(-100vh);
}

.hidden#bottom {
	transform: translateX(-5%) translateY(100vh);
}

.hidden#left {
	transform: translateX(-100vw) translateY(-50%);
}

.hidden#right {
	transform: translateX(100vw) translateY(-50%);
}

.table-wrapper {
	margin-top: 10px;
}
/*最外层透明*/
.table-wrapper >>> .el-table,
.table-wrapper >>> .el-table__expanded-cell {
	background-color: transparent;
}
/* 表格内背景颜色 */
.table-wrapper >>> .el-table th,
.table-wrapper >>> .el-table tr,
.table-wrapper >>> .el-table td {
	background-color: transparent;
}

.table-wrapper >>> .el-table th {
	background: rgba(2, 49, 52, 0.9);
	font-weight: 100;
	border-bottom: 1px solid rgba(200, 222, 222, 0.2);
}

.table-wrapper >>> .el-table td {
	background-color: rgba(2, 49, 52, 0.3);
	font-size: 12px;
	border-bottom: 1px solid rgba(200, 222, 222, 0.1);
}

.table-wrapper >>> .el-table__body tr:hover > td {
	background-color: rgba(2, 49, 52, 0.6) !important;
}

/* 表格行间距和边框 */
.table-wrapper >>> .el-table__row {
	margin-bottom: 2px;
}

/* 奇偶行颜色区分 */
.table-wrapper >>> .el-table__body tr:nth-child(even) td {
	background-color: rgba(2, 49, 52, 0.4);
}

.table-wrapper >>> .el-table__body tr:nth-child(odd) td {
	background-color: rgba(2, 49, 52, 0.2);
}
.table-wrapper >>> .custom-table::before,
.custom-table::after {
	content: none;
}
/**表格背景透明end */
.table-wrapper >>> .el-table__row > td {
	/* 去除表格线 */
	border: none;
}
.table-wrapper >>> .el-table th.el-table__cell.is-leaf {
	/* 去除上边框 */
	border-bottom: none !important;
}
.table-wrapper >>> .el-table__inner-wrapper::before {
	/* 去除下边框 */
	height: 0;
}

.table-wrapper >>> .el-table__body {
	-webkit-border-vertical-spacing: 5px;
}

.highlight {
	background-color: rgba(2, 49, 52, 0.8) !important; /* 与界面主题一致的高亮颜色 */
}

/* 特殊行样式 */
:deep(.el-table .highlight-row) {
	background: linear-gradient(to right, rgba(2, 49, 52, 0.8), rgba(1, 143, 143, 0.6)) !important;
	/* background-size: 100% 100%; */
}

/* 表头文字颜色 */
:deep(.el-table th.el-table__cell) {
	color: white !important;
}

/* 移除默认表格线 */
:deep(.el-table::before) {
	display: none;
}

/* 响应式设计 */
@media (max-width: 1200px) {
	#main-content {
		flex-wrap: wrap;
	}
	.b,
	.d {
		flex: 0 0 calc(50% - 10px);
	}
	.c {
		flex: 0 0 100%;
		order: -1;
	}
}

@media (max-width: 768px) {
	.nav-bar {
		padding: 10px 15px;
	}
	.title {
		font-size: 1.5rem;
	}
	.nav-item {
		padding: 6px 10px;
		font-size: 0.9rem;
	}
	.b,
	.d {
		flex: 0 0 100%;
	}
	.c-top,
	.c-bottom {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (max-width: 480px) {
	.nav-left {
		gap: 15px;
	}
	.nav-item::before {
		width: 16px;
		height: 16px;
	}
	.c-top,
	.c-bottom {
		grid-template-columns: 1fr;
	}
}
.loading-box {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgb(0 0 0 / 20%);

	position: absolute;
	top: 0;
	left: 0;
}
</style>
