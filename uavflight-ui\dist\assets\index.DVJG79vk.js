const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/form.CUQIuPsm.js","assets/index.C0-0gsfl.js","assets/vue.CnN__PXn.js","assets/index.CB8Sa4Ps.css","assets/template.CyJxF9SQ.js","assets/form.nt5OGudM.css"])))=>i.map(i=>d[i]);
import{v as Z,a as ee,d as ae,c as w,__tla as te}from"./index.C0-0gsfl.js";import{d as E,k as y,A as le,B as r,m as L,a as oe,b as p,f as P,t,q as u,x as ne,u as e,v as o,I as re,E as _,G as h,e as f,H as se,J as ie,j as de}from"./vue.CnN__PXn.js";import{u as me,__tla as ce}from"./table.BupIqdAe.js";import{f as pe,d as ue,o as _e,__tla as ye}from"./template.CyJxF9SQ.js";let V,he=Promise.all([(()=>{try{return te}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return ye}catch{}})()]).then(async()=>{let k,S,T,D;k={class:"layout-padding"},S={class:"layout-padding-auto layout-padding-view"},T={class:"mb8",style:{width:"100%"}},D=E({name:"systemGenTemplate"}),V=E({...D,setup(fe){const z=de(()=>ee(()=>import("./form.CUQIuPsm.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([0,1,2,3,4,5]))),{t:s}=Z.useI18n(),C=y(),v=y(!1),q=y(),b=y(!0),x=y([]),B=y(!0),i=le({queryForm:{},pageList:pe,descs:["create_time"]}),{getDataList:d,currentChangeHandle:G,sizeChangeHandle:H,downBlobFile:I,tableStyle:F}=me(i),A=()=>{q.value.resetFields(),x.value=[],d()},K=()=>{I("/gen/template/export",i.queryForm,"template.xlsx")},Q=a=>{x.value=a.map(({id:l})=>l),B.value=!a.length},N=async a=>{try{await ae().confirm(s("common.delConfirmText"))}catch{return}try{await ue(a),d(),w().success(s("common.delSuccessText"))}catch(l){w().error(l.msg)}},U=async()=>{try{v.value=!0;const{data:a}=await _e();d(),w().success(a)}catch(a){w().error(a.msg)}finally{v.value=!1}};return(a,l)=>{const j=r("el-input"),$=r("el-form-item"),m=r("el-button"),J=r("el-form"),R=r("el-row"),O=r("right-toolbar"),c=r("el-table-column"),M=r("el-table"),W=r("pagination"),g=L("auth"),X=L("loading");return p(),oe("div",k,[P("div",S,[u(t(R,{class:"ml10"},{default:o(()=>[t(J,{inline:!0,model:e(i).queryForm,onKeyup:re(e(d),["enter"]),ref_key:"queryRef",ref:q},{default:o(()=>[t($,{label:a.$t("template.templateName"),prop:"templateName"},{default:o(()=>[t(j,{placeholder:e(s)("template.inputTemplateNameTip"),style:{"max-width":"180px"},modelValue:e(i).queryForm.templateName,"onUpdate:modelValue":l[0]||(l[0]=n=>e(i).queryForm.templateName=n)},null,8,["placeholder","modelValue"])]),_:1},8,["label"]),t($,null,{default:o(()=>[t(m,{onClick:e(d),formDialogRef:"",icon:"search",type:"primary"},{default:o(()=>[_(h(a.$t("common.queryBtn")),1)]),_:1},8,["onClick"]),t(m,{onClick:A,formDialogRef:"",icon:"Refresh"},{default:o(()=>[_(h(a.$t("common.resetBtn")),1)]),_:1})]),_:1})]),_:1},8,["model","onKeyup"])]),_:1},512),[[ne,e(b)]]),t(R,null,{default:o(()=>[P("div",T,[u((p(),f(m,{onClick:l[1]||(l[1]=n=>e(C).openDialog()),class:"ml10",icon:"folder-add",type:"primary"},{default:o(()=>[_(h(a.$t("common.addBtn")),1)]),_:1})),[[g,"codegen_template_add"]]),u((p(),f(m,{plain:"",disabled:e(B),onClick:l[2]||(l[2]=n=>N(e(x))),class:"ml10",icon:"Delete",type:"primary"},{default:o(()=>[_(h(a.$t("common.delBtn")),1)]),_:1},8,["disabled"])),[[g,"codegen_template_del"]]),u((p(),f(m,{onClick:U,class:"ml10",icon:"download",plain:"",disabled:e(v)},{default:o(()=>l[5]||(l[5]=[_(" \u66F4\u65B0 ")])),_:1},8,["disabled"])),[[g,"codegen_template_add"]]),t(O,{export:"codegen_template_export",onExportExcel:K,onQueryTable:e(d),class:"ml10",style:{float:"right","margin-right":"20px"},showSearch:e(b),"onUpdate:showSearch":l[3]||(l[3]=n=>se(b)?b.value=n:null)},null,8,["onQueryTable","showSearch"])])]),_:1}),u((p(),f(M,{data:e(i).dataList,onSelectionChange:Q,style:{width:"100%"},border:"","cell-style":e(F).cellStyle,"header-cell-style":e(F).headerCellStyle},{default:o(()=>[t(c,{align:"center",type:"selection",width:"40"}),t(c,{label:e(s)("template.index"),type:"index",width:"60"},null,8,["label"]),t(c,{label:e(s)("template.templateName"),prop:"templateName","show-overflow-tooltip":""},null,8,["label"]),t(c,{label:e(s)("template.generatorPath"),prop:"generatorPath","show-overflow-tooltip":""},null,8,["label"]),t(c,{label:e(s)("template.desc"),prop:"templateDesc","show-overflow-tooltip":""},null,8,["label"]),t(c,{label:e(s)("template.createTime"),prop:"createTime","show-overflow-tooltip":""},null,8,["label"]),t(c,{label:a.$t("common.action"),width:"150"},{default:o(n=>[u((p(),f(m,{icon:"edit-pen",onClick:Y=>e(C).openDialog(n.row.id),text:"",type:"primary"},{default:o(()=>[_(h(a.$t("common.editBtn")),1)]),_:2},1032,["onClick"])),[[g,"codegen_template_edit"]]),u((p(),f(m,{icon:"delete",onClick:Y=>N([n.row.id]),text:"",type:"primary"},{default:o(()=>[_(h(a.$t("common.delBtn")),1)]),_:2},1032,["onClick"])),[[g,"codegen_template_del"]])]),_:1},8,["label"])]),_:1},8,["data","cell-style","header-cell-style"])),[[X,e(i).loading]]),t(W,ie({onCurrentChange:e(G),onSizeChange:e(H)},e(i).pagination),null,16,["onCurrentChange","onSizeChange"])]),t(e(z),{onRefresh:l[4]||(l[4]=n=>e(d)()),ref_key:"formDialogRef",ref:C},null,512)])}}})});export{he as __tla,V as default};
