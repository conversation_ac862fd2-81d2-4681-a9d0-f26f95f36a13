/**
 * @file GeoserverLayer.ts
 * @description Geoserver图层创建和管理工具类
 * 
 * 该类提供了创建和管理Geoserver图层的方法，包括：
 * - WMS图层创建
 * - WFS图层创建
 * - 图层样式设置
 * - 图层事件处理
 * 
 * <AUTHOR>
 * @date 2025-06-28
 */
import TileLayer from 'ol/layer/Tile';
import VectorLayer from 'ol/layer/Vector';
import TileWMS from 'ol/source/TileWMS';
import VectorSource from 'ol/source/Vector';
import GeoJSON from 'ol/format/GeoJSON';
import { bbox as bboxStrategy } from 'ol/loadingstrategy';
import { Style } from 'ol/style';
import { transformExtent } from 'ol/proj';
import WMTS from 'ol/source/WMTS';
import WMTSTileGrid from 'ol/tilegrid/WMTS';
import { get as getProjection } from 'ol/proj';
import { getTopLeft, getWidth } from 'ol/extent';

import {
  buildGeoserverWfsUrl,
  buildGeoserverWmsUrl,
  buildGeoserverWmtsUrl,
  getGeoserverBaseUrl
} from '../../geoserver';

/**
 * Geoserver图层创建和管理工具类
 */
export class GeoserverLayer {
  /**
   * 创建WMS图层
   * @param workspace 工作区名称
   * @param layerName 图层名称
   * @param options 附加选项
   * @returns TileLayer实例
   */
  static createWmsLayer(workspace: string, layerName: string, options: any = {}): TileLayer<TileWMS> {
    const url = buildGeoserverWmsUrl(workspace, layerName, options.params || {});
    
    // 处理图层范围
    let extent = null;
    if (options.params && options.params.bbox) {
      extent = options.params.bbox;
      console.log(`WMS图层 ${workspace}:${layerName} 使用配置的范围:`, extent);
    }
    const styleNames = ["Blue","Brown","Cyan","Gold","Gray","Green","Lime","Magenta","Navy","Olive","Orange","Pink","Purple","Red","Teal","White","Yellow","Coral","Tomato"]
		const randomStyle = styleNames[Math.floor(Math.random() * styleNames.length)];
					// 传统方式创建
          
    // 创建WMS源
    const wmsSource = new TileWMS({
      url: url,
      params: {
        'LAYERS': `${workspace}:${layerName}`,
        'TILED': true,
        'FORMAT': 'image/png',
        'TRANSPARENT': true,
       
        ...options.params
      },
      serverType: 'geoserver',
      crossOrigin: 'anonymous',
      transition: 0
    });
    
    // 创建图层
    const layer = new TileLayer({
      source: wmsSource,
      visible: options.visible !== undefined ? options.visible : true,
      opacity: options.opacity !== undefined ? options.opacity : 1,
      zIndex: options.zIndex !== undefined ? options.zIndex : 1
    });
    
    // 如果有范围信息，保存到图层属性中
    if (extent) {
      // 确保范围是EPSG:3857坐标系
      if (options.params && options.params.bbox_srs === 'EPSG:4326') {
        // 如果范围是WGS84坐标，转换为Web墨卡托
        extent = transformExtent(extent, 'EPSG:4326', 'EPSG:3857');
      }
      
      // 将范围保存到图层实例
      (layer as any).extent = extent;
    } else {
      // 尝试从Geoserver获取图层范围信息
      // 这里我们可以添加一个异步请求来获取图层的范围信息
      // 但由于createWmsLayer是同步函数，我们只能在创建后异步更新
      
      // 设置一个默认的中国范围作为初始值
      (layer as any).extent = [7000000, 2000000, 14000000, 6000000]; // 中国大致范围的Web墨卡托坐标
      
      // 记录图层的工作区和名称，以便后续可以查询
      (layer as any).workspace = workspace;
      (layer as any).layerName = layerName;
    }
    
    return layer;
  }
  
  /**
   * 创建WMTS图层
   * @param workspace 工作区名称
   * @param layerName 图层名称
   * @param options 附加选项
   * @returns TileLayer实例
   */
  static createWmtsLayer(workspace: string, layerName: string, options: any = {}): TileLayer<WMTS> {
    console.log(`GeoserverLayer.createWmtsLayer - 创建WMTS图层 ${workspace}:${layerName}`, options);
    
    // 获取EPSG:4326投影
    const projectionName = options.params?.tileMatrixSet || 'EPSG:4326';
    const projection = getProjection(projectionName);
    
    if (!projection) {
      console.error(`无法获取${projectionName}投影`);
      throw new Error(`无法获取${projectionName}投影`);
    }
    
    console.log(`WMTS图层使用投影: ${projectionName}`, projection);

    // 处理图层范围
    let extent = null;
    if (options.params && options.params.extent) {
      extent = options.params.extent;
      console.log(`使用配置的范围:`, extent);
    } else {
      // 使用投影的默认范围
      extent = projection.getExtent();
      console.log(`使用投影的默认范围:`, extent);
    }
    
    // 设置WMTS参数
    const tileMatrixSet = projectionName;
    const format = options.params?.format || 'image/png';
    const style = options.params?.style || '';
    
    // 获取完整的WMTS服务URL (KVP格式)
    const baseUrl = getGeoserverBaseUrl();
    const wmtsEndpoint = `${baseUrl}/gwc/service/wmts`;
    console.log(`WMTS服务端点: ${wmtsEndpoint}`);
    
    // 准备标准WMTS参数
    const wmtsParams = {
      layer: `${workspace}:${layerName}`,
      tileMatrixSet,
      format,
      style
    };
    
    console.log(`WMTS参数:`, {
      层名: wmtsParams.layer,
      瓦片矩阵集: wmtsParams.tileMatrixSet,
      格式: wmtsParams.format,
      样式: wmtsParams.style || '默认'
    });
    
    // 创建WMTS图层的瓦片网格
    const tileGrid = this.createWmtsTileGrid(projection, tileMatrixSet);
    
    // 创建WMTS源
    const wmtsSource = new WMTS({
      url: wmtsEndpoint,
      layer: wmtsParams.layer,
      matrixSet: wmtsParams.tileMatrixSet,
      format: wmtsParams.format,
      projection: projection,
      tileGrid: tileGrid,
      style: wmtsParams.style,
      // 关键配置: 使用KVP编码而非REST，这样能正确处理参数
      requestEncoding: 'KVP',
      wrapX: true,
      transition: 0
    });
    
    // 添加事件处理
    wmtsSource.on('tileloaderror', (event: any) => {
      const tile = event.tile;
      const url = tile.src_ || '';
      console.warn('WMTS图层加载失败:', url);
    });
    
    wmtsSource.on('tileloadend', () => {
      console.log('WMTS图层部分加载成功');
    });
    
    // 创建瓦片图层
    const layer = new TileLayer({
      source: wmtsSource,
      visible: options.visible !== undefined ? options.visible : true,
      opacity: options.opacity !== undefined ? options.opacity : 1,
      zIndex: options.zIndex !== undefined ? options.zIndex : 0
    });
    
    // 保存图层信息
    (layer as any).workspace = workspace;
    (layer as any).layerName = layerName;
    (layer as any).extent = extent;
    
    return layer;
  }
  
  /**
   * 创建WMTS图层的瓦片网格
   * @param projection 投影
   * @param gridSetId 网格集ID
   * @returns 瓦片网格
   */
  static createWmtsTileGrid(projection: any, gridSetId: string): WMTSTileGrid {
    const projectionExtent = projection.getExtent();
    
    // 根据不同的坐标系创建合适的参数
    let origin, resolutions, matrixIds;
    
    if (gridSetId === 'EPSG:4326') {
      // EPSG:4326 特殊处理
      origin = [-180, 90]; // 正确的原点
      
      // 标准的EPSG:4326分辨率
      resolutions = [
        0.703125,              // 层级 0
        0.3515625,             // 层级 1
        0.17578125,            // 层级 2
        0.087890625,           // 层级 3
        0.0439453125,          // 层级 4
        0.02197265625,         // 层级 5
        0.010986328125,        // 层级 6
        0.0054931640625,       // 层级 7
        0.00274658203125,      // 层级 8
        0.001373291015625,     // 层级 9
        0.0006866455078125,    // 层级 10
        0.0003433227539062,    // 层级 11
        0.0001716613769531,    // 层级 12
        0.0000858306884766,    // 层级 13
        0.0000429153442383,    // 层级 14
        0.0000214576721191,    // 层级 15
        0.0000107288360596,    // 层级 16
        0.0000053644180298,    // 层级 17
        0.0000026822090149,    // 层级 18
        0.0000013411045074,    // 层级 19
        0.0000006705522537,    // 层级 20
        0.0000003352761269,    // 层级 21
        0.00000016763806345,   // 层级 22
        0.00000008381903173,   // 层级 23
        0.00000004190951586,   // 层级 24
        0.00000002095475793    // 层级 25
    ];
    
      
      // 标准的EPSG:4326 GeoServer矩阵ID
      matrixIds = [];
      for (let i = 0; i < resolutions.length; i++) {
        matrixIds.push(`${gridSetId}:${i}`);
      }
    } else {
      // 默认情况下，使用自动计算的值
      origin = getTopLeft(projectionExtent);
      const size = Math.max(
        projectionExtent[2] - projectionExtent[0],
        projectionExtent[3] - projectionExtent[1]
      );
      const maxResolution = size / 256;
      
      resolutions = [];
      matrixIds = [];
      for (let i = 0; i < 20; i++) {
        resolutions.push(maxResolution / Math.pow(2, i));
        // 使用标准GeoServer格式矩阵ID
        matrixIds.push(`${gridSetId}:${i}`);
      }
    }
    
    console.log(`创建WMTS瓦片网格:`, {
      网格集: gridSetId,
      原点: origin,
      分辨率数: resolutions.length
    });
    
    return new WMTSTileGrid({
      origin: origin,
      resolutions: resolutions,
      matrixIds: matrixIds
    });
  }
  
  /**
   * 创建WFS图层
   * @param workspace 工作区名称
   * @param layerName 图层名称
   * @param options 附加选项
   * @returns VectorLayer实例
   */
  static createWfsLayer(workspace: string, layerName: string, options: any = {}): VectorLayer<VectorSource> {
    // 确保必须设置outputFormat为application/json
    const params = {
      ...options.params,
      outputFormat: 'application/json'
    };
    
    const url = buildGeoserverWfsUrl(workspace, layerName, params);
    
    console.log(`创建WFS图层: ${workspace}:${layerName}, URL: ${url}`);
    
    const vectorSource = new VectorSource({
      format: new GeoJSON({
        // 添加数据投影定义，指定从服务器返回的数据使用EPSG:4326坐标系
        dataProjection: 'EPSG:4326',
        // 指定要素在地图上显示时使用的投影
        featureProjection: 'EPSG:3857'
      }),
      url: url,
      strategy: bboxStrategy,
      // 添加加载错误处理
      attributions: '© GeoServer WFS',
      overlaps: false
    });
    
    // 添加错误处理事件
    vectorSource.on('featuresloaderror', function(event) {
      console.error('WFS数据加载失败:', event);
    });
    
    return new VectorLayer({
      source: vectorSource,
      style: options.style || new Style({}),
      visible: options.visible !== undefined ? options.visible : true,
      opacity: options.opacity !== undefined ? options.opacity : 1,
      zIndex: options.zIndex !== undefined ? options.zIndex : 2
    });
  }
}