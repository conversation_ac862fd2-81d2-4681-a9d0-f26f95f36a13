{"version": 3, "sources": ["../../ol/render/canvas.js", "../../ol/style/IconImageCache.js"], "sourcesContent": ["/**\n * @module ol/render/canvas\n */\nimport BaseObject from '../Object.js';\nimport {WORKER_OFFSCREEN_CANVAS} from '../has.js';\nimport {clear} from '../obj.js';\nimport {createCanvasContext2D} from '../dom.js';\nimport {getFontParameters} from '../css.js';\n\n/**\n * @typedef {'Circle' | 'Image' | 'LineString' | 'Polygon' | 'Text' | 'Default'} BuilderType\n */\n\n/**\n * @typedef {Object} FillState\n * @property {import(\"../colorlike.js\").ColorLike} fillStyle FillStyle.\n */\n\n/**\n * @typedef Label\n * @property {number} width Width.\n * @property {number} height Height.\n * @property {Array<string|number>} contextInstructions ContextInstructions.\n */\n\n/**\n * @typedef {Object} FillStrokeState\n * @property {import(\"../colorlike.js\").ColorLike} [currentFillStyle] Current FillStyle.\n * @property {import(\"../colorlike.js\").ColorLike} [currentStrokeStyle] Current StrokeStyle.\n * @property {CanvasLineCap} [currentLineCap] Current LineCap.\n * @property {Array<number>} currentLineDash Current LineDash.\n * @property {number} [currentLineDashOffset] Current LineDashOffset.\n * @property {CanvasLineJoin} [currentLineJoin] Current LineJoin.\n * @property {number} [currentLineWidth] Current LineWidth.\n * @property {number} [currentMiterLimit] Current MiterLimit.\n * @property {number} [lastStroke] Last stroke.\n * @property {import(\"../colorlike.js\").ColorLike} [fillStyle] FillStyle.\n * @property {import(\"../colorlike.js\").ColorLike} [strokeStyle] StrokeStyle.\n * @property {CanvasLineCap} [lineCap] LineCap.\n * @property {Array<number>} lineDash LineDash.\n * @property {number} [lineDashOffset] LineDashOffset.\n * @property {CanvasLineJoin} [lineJoin] LineJoin.\n * @property {number} [lineWidth] LineWidth.\n * @property {number} [miterLimit] MiterLimit.\n */\n\n/**\n * @typedef {Object} StrokeState\n * @property {CanvasLineCap} lineCap LineCap.\n * @property {Array<number>} lineDash LineDash.\n * @property {number} lineDashOffset LineDashOffset.\n * @property {CanvasLineJoin} lineJoin LineJoin.\n * @property {number} lineWidth LineWidth.\n * @property {number} miterLimit MiterLimit.\n * @property {import(\"../colorlike.js\").ColorLike} strokeStyle StrokeStyle.\n */\n\n/**\n * @typedef {Object} TextState\n * @property {string} font Font.\n * @property {CanvasTextAlign} [textAlign] TextAlign.\n * @property {number} [repeat] Repeat.\n * @property {import(\"../style/Text.js\").TextJustify} [justify] Justify.\n * @property {CanvasTextBaseline} textBaseline TextBaseline.\n * @property {import(\"../style/Text.js\").TextPlacement} [placement] Placement.\n * @property {number} [maxAngle] MaxAngle.\n * @property {boolean} [overflow] Overflow.\n * @property {import(\"../style/Fill.js\").default} [backgroundFill] BackgroundFill.\n * @property {import(\"../style/Stroke.js\").default} [backgroundStroke] BackgroundStroke.\n * @property {import(\"../size.js\").Size} [scale] Scale.\n * @property {Array<number>} [padding] Padding.\n */\n\n/**\n * @typedef {Object} SerializableInstructions\n * @property {Array<*>} instructions The rendering instructions.\n * @property {Array<*>} hitDetectionInstructions The rendering hit detection instructions.\n * @property {Array<number>} coordinates The array of all coordinates.\n * @property {!Object<string, TextState>} [textStates] The text states (decluttering).\n * @property {!Object<string, FillState>} [fillStates] The fill states (decluttering).\n * @property {!Object<string, StrokeState>} [strokeStates] The stroke states (decluttering).\n */\n\n/**\n * @typedef {Object<number, import(\"./canvas/Executor.js\").ReplayImageOrLabelArgs>} DeclutterImageWithText\n */\n\n/**\n * @const\n * @type {string}\n */\nexport const defaultFont = '10px sans-serif';\n\n/**\n * @const\n * @type {string}\n */\nexport const defaultFillStyle = '#000';\n\n/**\n * @const\n * @type {CanvasLineCap}\n */\nexport const defaultLineCap = 'round';\n\n/**\n * @const\n * @type {Array<number>}\n */\nexport const defaultLineDash = [];\n\n/**\n * @const\n * @type {number}\n */\nexport const defaultLineDashOffset = 0;\n\n/**\n * @const\n * @type {CanvasLineJoin}\n */\nexport const defaultLineJoin = 'round';\n\n/**\n * @const\n * @type {number}\n */\nexport const defaultMiterLimit = 10;\n\n/**\n * @const\n * @type {import(\"../colorlike.js\").ColorLike}\n */\nexport const defaultStrokeStyle = '#000';\n\n/**\n * @const\n * @type {CanvasTextAlign}\n */\nexport const defaultTextAlign = 'center';\n\n/**\n * @const\n * @type {CanvasTextBaseline}\n */\nexport const defaultTextBaseline = 'middle';\n\n/**\n * @const\n * @type {Array<number>}\n */\nexport const defaultPadding = [0, 0, 0, 0];\n\n/**\n * @const\n * @type {number}\n */\nexport const defaultLineWidth = 1;\n\n/**\n * @type {BaseObject}\n */\nexport const checkedFonts = new BaseObject();\n\n/**\n * @type {CanvasRenderingContext2D}\n */\nlet measureContext = null;\n\n/**\n * @type {string}\n */\nlet measureFont;\n\n/**\n * @type {!Object<string, number>}\n */\nexport const textHeights = {};\n\n/**\n * Clears the label cache when a font becomes available.\n * @param {string} fontSpec CSS font spec.\n */\nexport const registerFont = (function () {\n  const retries = 100;\n  const size = '32px ';\n  const referenceFonts = ['monospace', 'serif'];\n  const len = referenceFonts.length;\n  const text = 'wmytzilWMYTZIL@#/&?$%10\\uF013';\n  let interval, referenceWidth;\n\n  /**\n   * @param {string} fontStyle Css font-style\n   * @param {string} fontWeight Css font-weight\n   * @param {*} fontFamily Css font-family\n   * @return {boolean} Font with style and weight is available\n   */\n  function isAvailable(fontStyle, fontWeight, fontFamily) {\n    let available = true;\n    for (let i = 0; i < len; ++i) {\n      const referenceFont = referenceFonts[i];\n      referenceWidth = measureTextWidth(\n        fontStyle + ' ' + fontWeight + ' ' + size + referenceFont,\n        text\n      );\n      if (fontFamily != referenceFont) {\n        const width = measureTextWidth(\n          fontStyle +\n            ' ' +\n            fontWeight +\n            ' ' +\n            size +\n            fontFamily +\n            ',' +\n            referenceFont,\n          text\n        );\n        // If width and referenceWidth are the same, then the fallback was used\n        // instead of the font we wanted, so the font is not available.\n        available = available && width != referenceWidth;\n      }\n    }\n    if (available) {\n      return true;\n    }\n    return false;\n  }\n\n  function check() {\n    let done = true;\n    const fonts = checkedFonts.getKeys();\n    for (let i = 0, ii = fonts.length; i < ii; ++i) {\n      const font = fonts[i];\n      if (checkedFonts.get(font) < retries) {\n        if (isAvailable.apply(this, font.split('\\n'))) {\n          clear(textHeights);\n          // Make sure that loaded fonts are picked up by Safari\n          measureContext = null;\n          measureFont = undefined;\n          checkedFonts.set(font, retries);\n        } else {\n          checkedFonts.set(font, checkedFonts.get(font) + 1, true);\n          done = false;\n        }\n      }\n    }\n    if (done) {\n      clearInterval(interval);\n      interval = undefined;\n    }\n  }\n\n  return function (fontSpec) {\n    const font = getFontParameters(fontSpec);\n    if (!font) {\n      return;\n    }\n    const families = font.families;\n    for (let i = 0, ii = families.length; i < ii; ++i) {\n      const family = families[i];\n      const key = font.style + '\\n' + font.weight + '\\n' + family;\n      if (checkedFonts.get(key) === undefined) {\n        checkedFonts.set(key, retries, true);\n        if (!isAvailable(font.style, font.weight, family)) {\n          checkedFonts.set(key, 0, true);\n          if (interval === undefined) {\n            interval = setInterval(check, 32);\n          }\n        }\n      }\n    }\n  };\n})();\n\n/**\n * @param {string} font Font to use for measuring.\n * @return {import(\"../size.js\").Size} Measurement.\n */\nexport const measureTextHeight = (function () {\n  /**\n   * @type {HTMLDivElement}\n   */\n  let measureElement;\n  return function (fontSpec) {\n    let height = textHeights[fontSpec];\n    if (height == undefined) {\n      if (WORKER_OFFSCREEN_CANVAS) {\n        const font = getFontParameters(fontSpec);\n        const metrics = measureText(fontSpec, 'Žg');\n        const lineHeight = isNaN(Number(font.lineHeight))\n          ? 1.2\n          : Number(font.lineHeight);\n        height =\n          lineHeight *\n          (metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent);\n      } else {\n        if (!measureElement) {\n          measureElement = document.createElement('div');\n          measureElement.innerHTML = 'M';\n          measureElement.style.minHeight = '0';\n          measureElement.style.maxHeight = 'none';\n          measureElement.style.height = 'auto';\n          measureElement.style.padding = '0';\n          measureElement.style.border = 'none';\n          measureElement.style.position = 'absolute';\n          measureElement.style.display = 'block';\n          measureElement.style.left = '-99999px';\n        }\n        measureElement.style.font = fontSpec;\n        document.body.appendChild(measureElement);\n        height = measureElement.offsetHeight;\n        document.body.removeChild(measureElement);\n      }\n      textHeights[fontSpec] = height;\n    }\n    return height;\n  };\n})();\n\n/**\n * @param {string} font Font.\n * @param {string} text Text.\n * @return {TextMetrics} Text metrics.\n */\nfunction measureText(font, text) {\n  if (!measureContext) {\n    measureContext = createCanvasContext2D(1, 1);\n  }\n  if (font != measureFont) {\n    measureContext.font = font;\n    measureFont = measureContext.font;\n  }\n  return measureContext.measureText(text);\n}\n\n/**\n * @param {string} font Font.\n * @param {string} text Text.\n * @return {number} Width.\n */\nexport function measureTextWidth(font, text) {\n  return measureText(font, text).width;\n}\n\n/**\n * Measure text width using a cache.\n * @param {string} font The font.\n * @param {string} text The text to measure.\n * @param {Object<string, number>} cache A lookup of cached widths by text.\n * @return {number} The text width.\n */\nexport function measureAndCacheTextWidth(font, text, cache) {\n  if (text in cache) {\n    return cache[text];\n  }\n  const width = text\n    .split('\\n')\n    .reduce((prev, curr) => Math.max(prev, measureTextWidth(font, curr)), 0);\n  cache[text] = width;\n  return width;\n}\n\n/**\n * @param {TextState} baseStyle Base style.\n * @param {Array<string>} chunks Text chunks to measure.\n * @return {{width: number, height: number, widths: Array<number>, heights: Array<number>, lineWidths: Array<number>}}} Text metrics.\n */\nexport function getTextDimensions(baseStyle, chunks) {\n  const widths = [];\n  const heights = [];\n  const lineWidths = [];\n  let width = 0;\n  let lineWidth = 0;\n  let height = 0;\n  let lineHeight = 0;\n  for (let i = 0, ii = chunks.length; i <= ii; i += 2) {\n    const text = chunks[i];\n    if (text === '\\n' || i === ii) {\n      width = Math.max(width, lineWidth);\n      lineWidths.push(lineWidth);\n      lineWidth = 0;\n      height += lineHeight;\n      continue;\n    }\n    const font = chunks[i + 1] || baseStyle.font;\n    const currentWidth = measureTextWidth(font, text);\n    widths.push(currentWidth);\n    lineWidth += currentWidth;\n    const currentHeight = measureTextHeight(font);\n    heights.push(currentHeight);\n    lineHeight = Math.max(lineHeight, currentHeight);\n  }\n  return {width, height, widths, heights, lineWidths};\n}\n\n/**\n * @param {CanvasRenderingContext2D} context Context.\n * @param {number} rotation Rotation.\n * @param {number} offsetX X offset.\n * @param {number} offsetY Y offset.\n */\nexport function rotateAtOffset(context, rotation, offsetX, offsetY) {\n  if (rotation !== 0) {\n    context.translate(offsetX, offsetY);\n    context.rotate(rotation);\n    context.translate(-offsetX, -offsetY);\n  }\n}\n\n/**\n * @param {CanvasRenderingContext2D} context Context.\n * @param {import(\"../transform.js\").Transform|null} transform Transform.\n * @param {number} opacity Opacity.\n * @param {Label|HTMLCanvasElement|HTMLImageElement|HTMLVideoElement} labelOrImage Label.\n * @param {number} originX Origin X.\n * @param {number} originY Origin Y.\n * @param {number} w Width.\n * @param {number} h Height.\n * @param {number} x X.\n * @param {number} y Y.\n * @param {import(\"../size.js\").Size} scale Scale.\n */\nexport function drawImageOrLabel(\n  context,\n  transform,\n  opacity,\n  labelOrImage,\n  originX,\n  originY,\n  w,\n  h,\n  x,\n  y,\n  scale\n) {\n  context.save();\n\n  if (opacity !== 1) {\n    context.globalAlpha *= opacity;\n  }\n  if (transform) {\n    context.setTransform.apply(context, transform);\n  }\n\n  if (/** @type {*} */ (labelOrImage).contextInstructions) {\n    // label\n    context.translate(x, y);\n    context.scale(scale[0], scale[1]);\n    executeLabelInstructions(/** @type {Label} */ (labelOrImage), context);\n  } else if (scale[0] < 0 || scale[1] < 0) {\n    // flipped image\n    context.translate(x, y);\n    context.scale(scale[0], scale[1]);\n    context.drawImage(\n      /** @type {HTMLCanvasElement|HTMLImageElement|HTMLVideoElement} */ (\n        labelOrImage\n      ),\n      originX,\n      originY,\n      w,\n      h,\n      0,\n      0,\n      w,\n      h\n    );\n  } else {\n    // if image not flipped translate and scale can be avoided\n    context.drawImage(\n      /** @type {HTMLCanvasElement|HTMLImageElement|HTMLVideoElement} */ (\n        labelOrImage\n      ),\n      originX,\n      originY,\n      w,\n      h,\n      x,\n      y,\n      w * scale[0],\n      h * scale[1]\n    );\n  }\n\n  context.restore();\n}\n\n/**\n * @param {Label} label Label.\n * @param {CanvasRenderingContext2D} context Context.\n */\nfunction executeLabelInstructions(label, context) {\n  const contextInstructions = label.contextInstructions;\n  for (let i = 0, ii = contextInstructions.length; i < ii; i += 2) {\n    if (Array.isArray(contextInstructions[i + 1])) {\n      context[contextInstructions[i]].apply(\n        context,\n        contextInstructions[i + 1]\n      );\n    } else {\n      context[contextInstructions[i]] = contextInstructions[i + 1];\n    }\n  }\n}\n", "/**\n * @module ol/style/IconImageCache\n */\nimport {asString} from '../color.js';\n\n/**\n * @classdesc\n * Singleton class. Available through {@link module:ol/style/IconImageCache.shared}.\n */\nclass IconImageCache {\n  constructor() {\n    /**\n     * @type {!Object<string, import(\"./IconImage.js\").default>}\n     * @private\n     */\n    this.cache_ = {};\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.cacheSize_ = 0;\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.maxCacheSize_ = 32;\n  }\n\n  /**\n   * FIXME empty description for jsdoc\n   */\n  clear() {\n    this.cache_ = {};\n    this.cacheSize_ = 0;\n  }\n\n  /**\n   * @return {boolean} Can expire cache.\n   */\n  canExpireCache() {\n    return this.cacheSize_ > this.maxCacheSize_;\n  }\n\n  /**\n   * FIXME empty description for jsdoc\n   */\n  expire() {\n    if (this.canExpireCache()) {\n      let i = 0;\n      for (const key in this.cache_) {\n        const iconImage = this.cache_[key];\n        if ((i++ & 3) === 0 && !iconImage.hasListener()) {\n          delete this.cache_[key];\n          --this.cacheSize_;\n        }\n      }\n    }\n  }\n\n  /**\n   * @param {string} src Src.\n   * @param {?string} crossOrigin Cross origin.\n   * @param {import(\"../color.js\").Color} color Color.\n   * @return {import(\"./IconImage.js\").default} Icon image.\n   */\n  get(src, crossOrigin, color) {\n    const key = getKey(src, crossOrigin, color);\n    return key in this.cache_ ? this.cache_[key] : null;\n  }\n\n  /**\n   * @param {string} src Src.\n   * @param {?string} crossOrigin Cross origin.\n   * @param {import(\"../color.js\").Color} color Color.\n   * @param {import(\"./IconImage.js\").default} iconImage Icon image.\n   */\n  set(src, crossOrigin, color, iconImage) {\n    const key = getKey(src, crossOrigin, color);\n    this.cache_[key] = iconImage;\n    ++this.cacheSize_;\n  }\n\n  /**\n   * Set the cache size of the icon cache. Default is `32`. Change this value when\n   * your map uses more than 32 different icon images and you are not caching icon\n   * styles on the application level.\n   * @param {number} maxCacheSize Cache max size.\n   * @api\n   */\n  setSize(maxCacheSize) {\n    this.maxCacheSize_ = maxCacheSize;\n    this.expire();\n  }\n}\n\n/**\n * @param {string} src Src.\n * @param {?string} crossOrigin Cross origin.\n * @param {import(\"../color.js\").Color} color Color.\n * @return {string} Cache key.\n */\nfunction getKey(src, crossOrigin, color) {\n  const colorString = color ? asString(color) : 'null';\n  return crossOrigin + ':' + src + ':' + colorString;\n}\n\nexport default IconImageCache;\n\n/**\n * The {@link module:ol/style/IconImageCache~IconImageCache} for\n * {@link module:ol/style/Icon~Icon} images.\n * @api\n */\nexport const shared = new IconImageCache();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AA2FO,IAAM,cAAc;AAMpB,IAAM,mBAAmB;AAMzB,IAAM,iBAAiB;AAMvB,IAAM,kBAAkB,CAAC;AAMzB,IAAM,wBAAwB;AAM9B,IAAM,kBAAkB;AAMxB,IAAM,oBAAoB;AAM1B,IAAM,qBAAqB;AAM3B,IAAM,mBAAmB;AAMzB,IAAM,sBAAsB;AAM5B,IAAM,iBAAiB,CAAC,GAAG,GAAG,GAAG,CAAC;AAMlC,IAAM,mBAAmB;AAKzB,IAAM,eAAe,IAAI,eAAW;AAK3C,IAAI,iBAAiB;AAKrB,IAAI;AAKG,IAAM,cAAc,CAAC;AAMrB,IAAM,eAAgB,WAAY;AACvC,QAAM,UAAU;AAChB,QAAM,OAAO;AACb,QAAM,iBAAiB,CAAC,aAAa,OAAO;AAC5C,QAAM,MAAM,eAAe;AAC3B,QAAM,OAAO;AACb,MAAI,UAAU;AAQd,WAAS,YAAY,WAAW,YAAY,YAAY;AACtD,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,YAAM,gBAAgB,eAAe,CAAC;AACtC,uBAAiB;AAAA,QACf,YAAY,MAAM,aAAa,MAAM,OAAO;AAAA,QAC5C;AAAA,MACF;AACA,UAAI,cAAc,eAAe;AAC/B,cAAM,QAAQ;AAAA,UACZ,YACE,MACA,aACA,MACA,OACA,aACA,MACA;AAAA,UACF;AAAA,QACF;AAGA,oBAAY,aAAa,SAAS;AAAA,MACpC;AAAA,IACF;AACA,QAAI,WAAW;AACb,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAEA,WAAS,QAAQ;AACf,QAAI,OAAO;AACX,UAAM,QAAQ,aAAa,QAAQ;AACnC,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,YAAM,OAAO,MAAM,CAAC;AACpB,UAAI,aAAa,IAAI,IAAI,IAAI,SAAS;AACpC,YAAI,YAAY,MAAM,MAAM,KAAK,MAAM,IAAI,CAAC,GAAG;AAC7C,gBAAM,WAAW;AAEjB,2BAAiB;AACjB,wBAAc;AACd,uBAAa,IAAI,MAAM,OAAO;AAAA,QAChC,OAAO;AACL,uBAAa,IAAI,MAAM,aAAa,IAAI,IAAI,IAAI,GAAG,IAAI;AACvD,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM;AACR,oBAAc,QAAQ;AACtB,iBAAW;AAAA,IACb;AAAA,EACF;AAEA,SAAO,SAAU,UAAU;AACzB,UAAM,OAAO,kBAAkB,QAAQ;AACvC,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,UAAM,WAAW,KAAK;AACtB,aAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,EAAE,GAAG;AACjD,YAAM,SAAS,SAAS,CAAC;AACzB,YAAM,MAAM,KAAK,QAAQ,OAAO,KAAK,SAAS,OAAO;AACrD,UAAI,aAAa,IAAI,GAAG,MAAM,QAAW;AACvC,qBAAa,IAAI,KAAK,SAAS,IAAI;AACnC,YAAI,CAAC,YAAY,KAAK,OAAO,KAAK,QAAQ,MAAM,GAAG;AACjD,uBAAa,IAAI,KAAK,GAAG,IAAI;AAC7B,cAAI,aAAa,QAAW;AAC1B,uBAAW,YAAY,OAAO,EAAE;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,EAAG;AAMI,IAAM,oBAAqB,2BAAY;AAI5C,MAAI;AACJ,SAAO,SAAU,UAAU;AACzB,QAAI,SAAS,YAAY,QAAQ;AACjC,QAAI,UAAU,QAAW;AACvB,UAAI,yBAAyB;AAC3B,cAAM,OAAO,kBAAkB,QAAQ;AACvC,cAAM,UAAU,YAAY,UAAU,IAAI;AAC1C,cAAM,aAAa,MAAM,OAAO,KAAK,UAAU,CAAC,IAC5C,MACA,OAAO,KAAK,UAAU;AAC1B,iBACE,cACC,QAAQ,0BAA0B,QAAQ;AAAA,MAC/C,OAAO;AACL,YAAI,CAAC,gBAAgB;AACnB,2BAAiB,SAAS,cAAc,KAAK;AAC7C,yBAAe,YAAY;AAC3B,yBAAe,MAAM,YAAY;AACjC,yBAAe,MAAM,YAAY;AACjC,yBAAe,MAAM,SAAS;AAC9B,yBAAe,MAAM,UAAU;AAC/B,yBAAe,MAAM,SAAS;AAC9B,yBAAe,MAAM,WAAW;AAChC,yBAAe,MAAM,UAAU;AAC/B,yBAAe,MAAM,OAAO;AAAA,QAC9B;AACA,uBAAe,MAAM,OAAO;AAC5B,iBAAS,KAAK,YAAY,cAAc;AACxC,iBAAS,eAAe;AACxB,iBAAS,KAAK,YAAY,cAAc;AAAA,MAC1C;AACA,kBAAY,QAAQ,IAAI;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AACF,EAAG;AAOH,SAAS,YAAY,MAAM,MAAM;AAC/B,MAAI,CAAC,gBAAgB;AACnB,qBAAiB,sBAAsB,GAAG,CAAC;AAAA,EAC7C;AACA,MAAI,QAAQ,aAAa;AACvB,mBAAe,OAAO;AACtB,kBAAc,eAAe;AAAA,EAC/B;AACA,SAAO,eAAe,YAAY,IAAI;AACxC;AAOO,SAAS,iBAAiB,MAAM,MAAM;AAC3C,SAAO,YAAY,MAAM,IAAI,EAAE;AACjC;AASO,SAAS,yBAAyB,MAAM,MAAM,OAAO;AAC1D,MAAI,QAAQ,OAAO;AACjB,WAAO,MAAM,IAAI;AAAA,EACnB;AACA,QAAM,QAAQ,KACX,MAAM,IAAI,EACV,OAAO,CAAC,MAAM,SAAS,KAAK,IAAI,MAAM,iBAAiB,MAAM,IAAI,CAAC,GAAG,CAAC;AACzE,QAAM,IAAI,IAAI;AACd,SAAO;AACT;AAOO,SAAS,kBAAkB,WAAW,QAAQ;AACnD,QAAM,SAAS,CAAC;AAChB,QAAM,UAAU,CAAC;AACjB,QAAM,aAAa,CAAC;AACpB,MAAI,QAAQ;AACZ,MAAI,YAAY;AAChB,MAAI,SAAS;AACb,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,KAAK,IAAI,KAAK,GAAG;AACnD,UAAM,OAAO,OAAO,CAAC;AACrB,QAAI,SAAS,QAAQ,MAAM,IAAI;AAC7B,cAAQ,KAAK,IAAI,OAAO,SAAS;AACjC,iBAAW,KAAK,SAAS;AACzB,kBAAY;AACZ,gBAAU;AACV;AAAA,IACF;AACA,UAAM,OAAO,OAAO,IAAI,CAAC,KAAK,UAAU;AACxC,UAAM,eAAe,iBAAiB,MAAM,IAAI;AAChD,WAAO,KAAK,YAAY;AACxB,iBAAa;AACb,UAAM,gBAAgB,kBAAkB,IAAI;AAC5C,YAAQ,KAAK,aAAa;AAC1B,iBAAa,KAAK,IAAI,YAAY,aAAa;AAAA,EACjD;AACA,SAAO,EAAC,OAAO,QAAQ,QAAQ,SAAS,WAAU;AACpD;AA6BO,SAAS,iBACd,SACA,WACA,SACA,cACA,SACA,SACA,GACA,GACA,GACA,GACA,OACA;AACA,UAAQ,KAAK;AAEb,MAAI,YAAY,GAAG;AACjB,YAAQ,eAAe;AAAA,EACzB;AACA,MAAI,WAAW;AACb,YAAQ,aAAa,MAAM,SAAS,SAAS;AAAA,EAC/C;AAEA;AAAA;AAAA,IAAsB,aAAc;AAAA,IAAqB;AAEvD,YAAQ,UAAU,GAAG,CAAC;AACtB,YAAQ,MAAM,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAChC;AAAA;AAAA,MAA+C;AAAA,MAAe;AAAA,IAAO;AAAA,EACvE,WAAW,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,GAAG;AAEvC,YAAQ,UAAU,GAAG,CAAC;AACtB,YAAQ,MAAM,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAChC,YAAQ;AAAA;AAAA,MAEJ;AAAA,MAEF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,OAAO;AAEL,YAAQ;AAAA;AAAA,MAEJ;AAAA,MAEF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI,MAAM,CAAC;AAAA,MACX,IAAI,MAAM,CAAC;AAAA,IACb;AAAA,EACF;AAEA,UAAQ,QAAQ;AAClB;AAMA,SAAS,yBAAyB,OAAO,SAAS;AAChD,QAAM,sBAAsB,MAAM;AAClC,WAAS,IAAI,GAAG,KAAK,oBAAoB,QAAQ,IAAI,IAAI,KAAK,GAAG;AAC/D,QAAI,MAAM,QAAQ,oBAAoB,IAAI,CAAC,CAAC,GAAG;AAC7C,cAAQ,oBAAoB,CAAC,CAAC,EAAE;AAAA,QAC9B;AAAA,QACA,oBAAoB,IAAI,CAAC;AAAA,MAC3B;AAAA,IACF,OAAO;AACL,cAAQ,oBAAoB,CAAC,CAAC,IAAI,oBAAoB,IAAI,CAAC;AAAA,IAC7D;AAAA,EACF;AACF;;;AC7eA,IAAM,iBAAN,MAAqB;AAAA,EACnB,cAAc;AAKZ,SAAK,SAAS,CAAC;AAMf,SAAK,aAAa;AAMlB,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,SAAK,SAAS,CAAC;AACf,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,WAAO,KAAK,aAAa,KAAK;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,QAAI,KAAK,eAAe,GAAG;AACzB,UAAI,IAAI;AACR,iBAAW,OAAO,KAAK,QAAQ;AAC7B,cAAM,YAAY,KAAK,OAAO,GAAG;AACjC,aAAK,MAAM,OAAO,KAAK,CAAC,UAAU,YAAY,GAAG;AAC/C,iBAAO,KAAK,OAAO,GAAG;AACtB,YAAE,KAAK;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,KAAK,aAAa,OAAO;AAC3B,UAAM,MAAM,OAAO,KAAK,aAAa,KAAK;AAC1C,WAAO,OAAO,KAAK,SAAS,KAAK,OAAO,GAAG,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,KAAK,aAAa,OAAO,WAAW;AACtC,UAAM,MAAM,OAAO,KAAK,aAAa,KAAK;AAC1C,SAAK,OAAO,GAAG,IAAI;AACnB,MAAE,KAAK;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,cAAc;AACpB,SAAK,gBAAgB;AACrB,SAAK,OAAO;AAAA,EACd;AACF;AAQA,SAAS,OAAO,KAAK,aAAa,OAAO;AACvC,QAAM,cAAc,QAAQ,SAAS,KAAK,IAAI;AAC9C,SAAO,cAAc,MAAM,MAAM,MAAM;AACzC;AASO,IAAM,SAAS,IAAI,eAAe;", "names": []}