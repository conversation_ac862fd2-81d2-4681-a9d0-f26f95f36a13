import request from "/@/utils/request"

/**
 * 获取列表数据
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function getInspectReportData(query?: Object) {
  return request({
    url: '/admin//inspect/report/getInspectReportData',
    method: 'get',
    params: query
  })
}

/**
 * 获取各业务和事件的监测统计数
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function getBusinessTypeAndEventCountData(query?: Object) {
  return request({
    url: '/admin//inspect/report/getBusinessTypeAndEventCountData',
    method: 'get',
    params: query
  })
}

/**
 * 社区事件数量TOP10
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function getEventTop10Data(query?: Object) {
  return request({
    url: '/admin//inspect/report/getEventTop10Data',
    method: 'get',
    params: query
  })
}

