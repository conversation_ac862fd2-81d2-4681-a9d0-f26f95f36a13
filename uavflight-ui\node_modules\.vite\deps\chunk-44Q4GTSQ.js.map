{"version": 3, "sources": ["../../geotiff/dist-module/globals.js"], "sourcesContent": ["export const fieldTagNames = {\n  // TIFF Baseline\n  0x013B: 'Artist',\n  0x0102: 'BitsPerSample',\n  0x0109: 'CellLength',\n  0x0108: 'CellWidth',\n  0x0140: 'ColorMap',\n  0x0103: 'Compression',\n  0x8298: 'Copyright',\n  0x0132: 'DateTime',\n  0x0152: 'ExtraSamples',\n  0x010A: 'FillOrder',\n  0x0121: 'FreeByteCounts',\n  0x0120: 'FreeOffsets',\n  0x0123: 'GrayResponseCurve',\n  0x0122: 'GrayResponseUnit',\n  0x013C: 'HostComputer',\n  0x010E: 'ImageDescription',\n  0x0101: 'ImageLength',\n  0x0100: 'ImageWidth',\n  0x010F: 'Make',\n  0x0119: 'MaxSampleValue',\n  0x0118: 'MinSampleValue',\n  0x0110: 'Model',\n  0x00FE: 'NewSubfileType',\n  0x0112: 'Orientation',\n  0x0106: 'PhotometricInterpretation',\n  0x011C: 'PlanarConfiguration',\n  0x0128: 'ResolutionUnit',\n  0x0116: 'RowsPerStrip',\n  0x0115: 'SamplesPerPixel',\n  0x0131: 'Software',\n  0x0117: 'StripByteCounts',\n  0x0111: 'StripOffsets',\n  0x00FF: 'SubfileType',\n  0x0107: 'Threshholding',\n  0x011A: 'XResolution',\n  0x011B: 'YResolution',\n\n  // TIFF Extended\n  0x0146: 'BadFaxLines',\n  0x0147: 'CleanFaxData',\n  0x0157: 'ClipPath',\n  0x0148: 'ConsecutiveBadFaxLines',\n  0x01B1: 'Decode',\n  0x01B2: 'DefaultImageColor',\n  0x010D: 'DocumentName',\n  0x0150: 'DotRange',\n  0x0141: 'HalftoneHints',\n  0x015A: 'Indexed',\n  0x015B: 'JPEGTables',\n  0x011D: 'PageName',\n  0x0129: 'PageNumber',\n  0x013D: 'Predictor',\n  0x013F: 'PrimaryChromaticities',\n  0x0214: 'ReferenceBlackWhite',\n  0x0153: 'SampleFormat',\n  0x0154: 'SMinSampleValue',\n  0x0155: 'SMaxSampleValue',\n  0x022F: 'StripRowCounts',\n  0x014A: 'SubIFDs',\n  0x0124: 'T4Options',\n  0x0125: 'T6Options',\n  0x0145: 'TileByteCounts',\n  0x0143: 'TileLength',\n  0x0144: 'TileOffsets',\n  0x0142: 'TileWidth',\n  0x012D: 'TransferFunction',\n  0x013E: 'WhitePoint',\n  0x0158: 'XClipPathUnits',\n  0x011E: 'XPosition',\n  0x0211: 'YCbCrCoefficients',\n  0x0213: 'YCbCrPositioning',\n  0x0212: 'YCbCrSubSampling',\n  0x0159: 'YClipPathUnits',\n  0x011F: 'YPosition',\n\n  // EXIF\n  0x9202: 'ApertureValue',\n  0xA001: 'ColorSpace',\n  0x9004: 'DateTimeDigitized',\n  0x9003: 'DateTimeOriginal',\n  0x8769: 'Exif IFD',\n  0x9000: 'ExifVersion',\n  0x829A: 'ExposureTime',\n  0xA300: 'FileSource',\n  0x9209: 'Flash',\n  0xA000: 'FlashpixVersion',\n  0x829D: 'FNumber',\n  0xA420: 'ImageUniqueID',\n  0x9208: 'LightSource',\n  0x927C: 'MakerNote',\n  0x9201: 'ShutterSpeedValue',\n  0x9286: 'UserComment',\n\n  // IPTC\n  0x83BB: 'IPTC',\n\n  // ICC\n  0x8773: 'ICC Profile',\n\n  // XMP\n  0x02BC: 'XMP',\n\n  // GDAL\n  0xA480: 'GDAL_METADATA',\n  0xA481: 'GDAL_NODATA',\n\n  // Photoshop\n  0x8649: 'Photoshop',\n\n  // GeoTiff\n  0x830E: 'ModelPixelScale',\n  0x8482: 'ModelTiepoint',\n  0x85D8: 'ModelTransformation',\n  0x87AF: 'GeoKeyDirectory',\n  0x87B0: 'GeoDoubleParams',\n  0x87B1: 'GeoAsciiParams',\n\n  // LERC\n  0xC5F2: 'LercParameters',\n};\n\nexport const fieldTags = {};\nfor (const key in fieldTagNames) {\n  if (fieldTagNames.hasOwnProperty(key)) {\n    fieldTags[fieldTagNames[key]] = parseInt(key, 10);\n  }\n}\n\nexport const fieldTagTypes = {\n  256: 'SHORT',\n  257: 'SHORT',\n  258: 'SHORT',\n  259: 'SHORT',\n  262: 'SHORT',\n  273: 'LONG',\n  274: 'SHORT',\n  277: 'SHORT',\n  278: 'LONG',\n  279: 'LONG',\n  282: 'RATIONAL',\n  283: 'RATIONAL',\n  284: 'SHORT',\n  286: 'SHORT',\n  287: 'RATIONAL',\n  296: 'SHORT',\n  297: 'SHORT',\n  305: 'ASCII',\n  306: 'ASCII',\n  338: 'SHORT',\n  339: 'SHORT',\n  513: 'LONG',\n  514: 'LONG',\n  1024: 'SHORT',\n  1025: 'SHORT',\n  2048: 'SHORT',\n  2049: 'ASCII',\n  3072: 'SHORT',\n  3073: 'ASCII',\n  33550: 'DOUBLE',\n  33922: 'DOUBLE',\n  34264: 'DOUBLE',\n  34665: 'LONG',\n  34735: 'SHORT',\n  34736: 'DOUBLE',\n  34737: 'ASCII',\n  42113: 'ASCII',\n};\n\nexport const arrayFields = [\n  fieldTags.BitsPerSample,\n  fieldTags.ExtraSamples,\n  fieldTags.SampleFormat,\n  fieldTags.StripByteCounts,\n  fieldTags.StripOffsets,\n  fieldTags.StripRowCounts,\n  fieldTags.TileByteCounts,\n  fieldTags.TileOffsets,\n  fieldTags.SubIFDs,\n];\n\nexport const fieldTypeNames = {\n  0x0001: 'BYTE',\n  0x0002: 'ASCII',\n  0x0003: 'SHORT',\n  0x0004: 'LONG',\n  0x0005: 'RATIONAL',\n  0x0006: 'SBYTE',\n  0x0007: 'UNDEFINED',\n  0x0008: 'SSHORT',\n  0x0009: 'SLONG',\n  0x000A: 'SRATIONAL',\n  0x000B: 'FLOAT',\n  0x000C: 'DOUBLE',\n  // IFD offset, suggested by https://owl.phy.queensu.ca/~phil/exiftool/standards.html\n  0x000D: 'IFD',\n  // introduced by BigTIFF\n  0x0010: 'LONG8',\n  0x0011: 'SLONG8',\n  0x0012: 'IFD8',\n};\n\nexport const fieldTypes = {};\nfor (const key in fieldTypeNames) {\n  if (fieldTypeNames.hasOwnProperty(key)) {\n    fieldTypes[fieldTypeNames[key]] = parseInt(key, 10);\n  }\n}\n\nexport const photometricInterpretations = {\n  WhiteIsZero: 0,\n  BlackIsZero: 1,\n  RGB: 2,\n  Palette: 3,\n  TransparencyMask: 4,\n  CMYK: 5,\n  YCbCr: 6,\n\n  CIELab: 8,\n  ICCLab: 9,\n};\n\nexport const ExtraSamplesValues = {\n  Unspecified: 0,\n  Assocalpha: 1,\n  Unassalpha: 2,\n};\n\nexport const LercParameters = {\n  Version: 0,\n  AddCompression: 1,\n};\n\nexport const LercAddCompression = {\n  None: 0,\n  Deflate: 1,\n  Zstandard: 2,\n};\n\nexport const geoKeyNames = {\n  1024: 'GTModelTypeGeoKey',\n  1025: 'GTRasterTypeGeoKey',\n  1026: 'GTCitationGeoKey',\n  2048: 'GeographicTypeGeoKey',\n  2049: 'GeogCitationGeoKey',\n  2050: 'GeogGeodeticDatumGeoKey',\n  2051: 'GeogPrimeMeridianGeoKey',\n  2052: 'GeogLinearUnitsGeoKey',\n  2053: 'GeogLinearUnitSizeGeoKey',\n  2054: 'GeogAngularUnitsGeoKey',\n  2055: 'GeogAngularUnitSizeGeoKey',\n  2056: 'GeogEllipsoidGeoKey',\n  2057: 'GeogSemiMajorAxisGeoKey',\n  2058: 'GeogSemiMinorAxisGeoKey',\n  2059: 'GeogInvFlatteningGeoKey',\n  2060: 'GeogAzimuthUnitsGeoKey',\n  2061: 'GeogPrimeMeridianLongGeoKey',\n  2062: 'GeogTOWGS84GeoKey',\n  3072: 'ProjectedCSTypeGeoKey',\n  3073: 'PCSCitationGeoKey',\n  3074: 'ProjectionGeoKey',\n  3075: 'ProjCoordTransGeoKey',\n  3076: 'ProjLinearUnitsGeoKey',\n  3077: 'ProjLinearUnitSizeGeoKey',\n  3078: 'ProjStdParallel1GeoKey',\n  3079: 'ProjStdParallel2GeoKey',\n  3080: 'ProjNatOriginLongGeoKey',\n  3081: 'ProjNatOriginLatGeoKey',\n  3082: 'ProjFalseEastingGeoKey',\n  3083: 'ProjFalseNorthingGeoKey',\n  3084: 'ProjFalseOriginLongGeoKey',\n  3085: 'ProjFalseOriginLatGeoKey',\n  3086: 'ProjFalseOriginEastingGeoKey',\n  3087: 'ProjFalseOriginNorthingGeoKey',\n  3088: 'ProjCenterLongGeoKey',\n  3089: 'ProjCenterLatGeoKey',\n  3090: 'ProjCenterEastingGeoKey',\n  3091: 'ProjCenterNorthingGeoKey',\n  3092: 'ProjScaleAtNatOriginGeoKey',\n  3093: 'ProjScaleAtCenterGeoKey',\n  3094: 'ProjAzimuthAngleGeoKey',\n  3095: 'ProjStraightVertPoleLongGeoKey',\n  3096: 'ProjRectifiedGridAngleGeoKey',\n  4096: 'VerticalCSTypeGeoKey',\n  4097: 'VerticalCitationGeoKey',\n  4098: 'VerticalDatumGeoKey',\n  4099: 'VerticalUnitsGeoKey',\n};\n\nexport const geoKeys = {};\nfor (const key in geoKeyNames) {\n  if (geoKeyNames.hasOwnProperty(key)) {\n    geoKeys[geoKeyNames[key]] = parseInt(key, 10);\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,IAAM,gBAAgB;AAAA;AAAA,EAE3B,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,OAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA;AAAA,EAGR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA;AAAA,EAGR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA;AAAA,EAGR,OAAQ;AAAA;AAAA,EAGR,OAAQ;AAAA;AAAA,EAGR,KAAQ;AAAA;AAAA,EAGR,OAAQ;AAAA,EACR,OAAQ;AAAA;AAAA,EAGR,OAAQ;AAAA;AAAA,EAGR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA;AAAA,EAGR,OAAQ;AACV;AAEO,IAAM,YAAY,CAAC;AAC1B,WAAW,OAAO,eAAe;AAC/B,MAAI,cAAc,eAAe,GAAG,GAAG;AACrC,cAAU,cAAc,GAAG,CAAC,IAAI,SAAS,KAAK,EAAE;AAAA,EAClD;AACF;AAEO,IAAM,gBAAgB;AAAA,EAC3B,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACT;AAEO,IAAM,cAAc;AAAA,EACzB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACZ;AAEO,IAAM,iBAAiB;AAAA,EAC5B,GAAQ;AAAA,EACR,GAAQ;AAAA,EACR,GAAQ;AAAA,EACR,GAAQ;AAAA,EACR,GAAQ;AAAA,EACR,GAAQ;AAAA,EACR,GAAQ;AAAA,EACR,GAAQ;AAAA,EACR,GAAQ;AAAA,EACR,IAAQ;AAAA,EACR,IAAQ;AAAA,EACR,IAAQ;AAAA;AAAA,EAER,IAAQ;AAAA;AAAA,EAER,IAAQ;AAAA,EACR,IAAQ;AAAA,EACR,IAAQ;AACV;AAEO,IAAM,aAAa,CAAC;AAC3B,WAAW,OAAO,gBAAgB;AAChC,MAAI,eAAe,eAAe,GAAG,GAAG;AACtC,eAAW,eAAe,GAAG,CAAC,IAAI,SAAS,KAAK,EAAE;AAAA,EACpD;AACF;AAEO,IAAM,6BAA6B;AAAA,EACxC,aAAa;AAAA,EACb,aAAa;AAAA,EACb,KAAK;AAAA,EACL,SAAS;AAAA,EACT,kBAAkB;AAAA,EAClB,MAAM;AAAA,EACN,OAAO;AAAA,EAEP,QAAQ;AAAA,EACR,QAAQ;AACV;AAEO,IAAM,qBAAqB;AAAA,EAChC,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,YAAY;AACd;AAEO,IAAM,iBAAiB;AAAA,EAC5B,SAAS;AAAA,EACT,gBAAgB;AAClB;AAEO,IAAM,qBAAqB;AAAA,EAChC,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AACb;AAEO,IAAM,cAAc;AAAA,EACzB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACR;AAEO,IAAM,UAAU,CAAC;AACxB,WAAW,OAAO,aAAa;AAC7B,MAAI,YAAY,eAAe,GAAG,GAAG;AACnC,YAAQ,YAAY,GAAG,CAAC,IAAI,SAAS,KAAK,EAAE;AAAA,EAC9C;AACF;", "names": []}