<!--
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-06-28 09:45:42
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-07-13 17:17:14
 * @FilePath: \uavflight-ui\src\components\olLayerManager\index.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<!--
 * @file index.vue
 * @description 图层管理组件，提供地图图层的可视化管理界面
 * 
 * 该组件是地图应用的核心控制面板，提供以下功能：
 * - 显示地图中所有图层列表
 * - 通过复选框控制图层的加载/移除
 * - 提供图层样式编辑功能，可以修改点、线、面图层的样式
 * - 提供图层样式的实时预览
 * - 支持右键缩放至图层
 * 
 * 组件与mapLayerManager store紧密集成，通过store提供的API
 * 实现对OpenLayers地图图层的管理和控制。
 * 
 * <AUTHOR>
 * @date 2025-05-19
 -->
<template>
  <div class="layer-manager-container">
    <!-- 标题栏 -->
    <div class="layer-manager-header">
      <h2 class="layer-manager-title">图层管理</h2>
    </div>
    
    <!-- 内容区域 -->
    <div class="panel-content">
      <!-- 图层查询和操作按钮 固定在顶部 -->
      <div class="search-container">
        <el-input
          v-model="searchText"
          placeholder="搜索图层..."
          prefix-icon="search"
          size="small"
          clearable
          class="search-input"
        />
        <el-button
          type="primary"
          size="small"
          @click="openUploadDialog"
          class="upload-button"
        >
          上传图层
        </el-button>
    </div>
    
      <!-- 可滚动的图层列表区域 -->
      <div class="scrollable-content">
        <!-- 空图层提示 -->
        <div v-if="!filteredTreeData.length" class="empty-layer-message">
          暂无图层可用
        </div>
        
        <!-- 图层树 -->
        <el-tree
          v-else
          :data="filteredTreeData"
          :props="{ label: 'name', children: 'children' }"
          node-key="id"
          :default-expanded-keys="defaultExpandedKeys"
          ref="layerTreeRef"
          :expand-on-click-node="true"
          @node-expand="handleNodeExpand"
          @node-collapse="handleNodeCollapse"
          :key="'layer-tree-static'"
        >
        <template #default="{ data }">
            <div
              class="custom-tree-node"
            >
            <div class="node-content" :class="{ 'theme-node': data.isTheme }">
              <!-- 复选框控制图层加载状态 -->
              <el-checkbox 
                v-model="data.loaded" 
                @change="handleLayerToggle(data)"
                @click.stop
                v-if="!data.isTheme"
              ></el-checkbox>
              
              <!-- 主题目录复选框，控制整个主题下所有图层 -->
              <el-checkbox 
                v-if="data.isTheme" 
                v-model="data.loaded" 
                @change="handleThemeToggle(data)"
                @click.stop
                :indeterminate="data.indeterminate"
              ></el-checkbox>
                  
              <!-- 图层类型图标 -->
              <div v-if="!data.isTheme" class="layer-type-indicator">
                <template v-if="data.geometryType === 'Point'">
                  <div class="style-preview">
                    <div class="point-preview">
                      <div class="point-inner"></div>
                  </div>
                </div>
                </template>
                <template v-else-if="data.geometryType === 'LineString'">
                  <div class="style-preview">
                    <div class="line-preview">
                      <div class="line-inner"></div>
                  </div>
                </div>
                </template>
                <template v-else-if="data.geometryType === 'Polygon'">
                  <div class="style-preview">
                    <div class="polygon-preview">
                      <div class="polygon-inner"></div>
              </div>
                  </div>
                </template>
                <template v-else>
                  <div class="raster-indicator">
                    <div class="raster-icon">
                      <div class="raster-grid"></div>
              </div>
            </div>
          </template>
      </div>
      
              <!-- 主题节点指示器 -->
              <div v-if="data.isTheme" class="theme-indicator"></div>
              
              <!-- 图层名称 -->
              <span v-if="data.isTheme" class="theme-name">{{ data.name }}</span>
              <span v-else class="layer-name">{{ data.name }}</span>
              
              <!-- 图层类型标签 -->
              <span v-if="!data.isTheme" class="layer-type">{{ getLayerTypeDisplay(data) }}</span>
                </div>
                
            <!-- 操作按钮 -->
            <div v-if="!data.isTheme" class="node-actions">
              <!-- 缩放至图层范围按钮 -->
              <el-button
                v-if="data.loaded"
                size="small"
                type="text"
                @click.stop="handleZoomToLayer(data)"
                title="缩放至图层范围"
              >
                <el-icon><ZoomIn /></el-icon>
              </el-button>

              <!-- 删除临时文件图层按钮 -->
              <el-button
                v-if="data.loaded && isTemporaryLayer(data)"
                size="small"
                type="text"
                @click.stop="handleDeleteLayer(data)"
                title="删除临时文件"
                class="delete-button"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
    
    
    
    <!-- 右键菜单已移除 -->
    
    <!-- 样式编辑弹窗 -->
    <el-dialog 
      v-model="styleDialog.visible" 
      title="修改图层样式" 
      width="400px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="style-editor-content">
        <template v-if="styleDialog.currentLayer">
          <h3>{{ styleDialog.currentLayer.name }}</h3>
          
          <!-- 通用属性：透明度 -->
          <el-form-item label="透明度">
            <el-slider 
              v-model="styleDialog.style.opacity" 
              :min="0" 
              :max="1" 
              :step="0.1"
              :format-tooltip="formatTooltip"
            ></el-slider>
          </el-form-item>
          
          <!-- 点图层特有属性 -->
          <div v-if="styleDialog.geometryType?.includes('Point')">
            <!-- 点大小 -->
            <el-form-item label="点大小">
              <el-input-number 
                v-model="styleDialog.style.radius" 
                :min="2" 
                :max="20" 
                size="small"
              ></el-input-number>
            </el-form-item>
            
            <!-- 点边框粗细 -->
            <el-form-item label="边框粗细">
              <el-input-number 
                v-model="styleDialog.style.weight" 
                :min="0" 
                :max="5" 
                size="small"
              ></el-input-number>
            </el-form-item>
            
            <!-- 点边框颜色 -->
            <el-form-item label="边框颜色">
              <el-color-picker v-model="styleDialog.style.color"></el-color-picker>
            </el-form-item>
            
            <!-- 点填充颜色 -->
            <el-form-item label="填充颜色">
              <el-color-picker v-model="styleDialog.style.fillColor" show-alpha></el-color-picker>
            </el-form-item>
          </div>
          
          <!-- 线图层特有属性 -->
          <div v-else-if="styleDialog.geometryType?.includes('Line')">
            <!-- 线条粗细 -->
            <el-form-item label="线条粗细">
              <el-input-number 
                v-model="styleDialog.style.weight" 
                :min="1" 
                :max="10" 
                size="small"
              ></el-input-number>
            </el-form-item>
            
            <!-- 线条颜色 -->
            <el-form-item label="线条颜色">
              <el-color-picker v-model="styleDialog.style.color"></el-color-picker>
            </el-form-item>
            
            <!-- 线条样式 -->
            <el-form-item label="线条样式">
              <div class="custom-select">
                <div 
                  class="select-item" 
                  :class="{ 'active': Array.isArray(styleDialog.style.lineDash) && styleDialog.style.lineDash.length === 0 }"
                  @click="styleDialog.style.lineDash = []"
                >
                  <div class="line-preview solid"></div>
                  <span>实线</span>
                </div>
                <div 
                  class="select-item" 
                  :class="{ 'active': Array.isArray(styleDialog.style.lineDash) && styleDialog.style.lineDash.toString() === '4,8' }"
                  @click="styleDialog.style.lineDash = [4, 8]"
                >
                  <div class="line-preview dashed"></div>
                  <span>虚线</span>
                </div>
                <div 
                  class="select-item" 
                  :class="{ 'active': Array.isArray(styleDialog.style.lineDash) && styleDialog.style.lineDash.toString() === '1,4,8,4' }"
                  @click="styleDialog.style.lineDash = [1, 4, 8, 4]"
                >
                  <div class="line-preview dotted"></div>
                  <span>点划线</span>
                </div>
              </div>
            </el-form-item>
          </div>
          
          <!-- 面图层特有属性 -->
          <div v-else>
            <!-- 线条粗细 -->
            <el-form-item label="边框粗细">
              <el-input-number 
                v-model="styleDialog.style.weight" 
                :min="0" 
                :max="10" 
                size="small"
              ></el-input-number>
            </el-form-item>
            
            <!-- 线条颜色 -->
            <el-form-item label="边框颜色">
              <el-color-picker v-model="styleDialog.style.color"></el-color-picker>
            </el-form-item>
            
            <!-- 填充颜色 -->
            <el-form-item label="填充颜色">
              <el-color-picker v-model="styleDialog.style.fillColor" show-alpha></el-color-picker>
            </el-form-item>
          </div>
          
          <!-- 实时预览 -->
          <div class="style-preview-container">
            <h4>样式预览</h4>
            <div class="preview-box">
              <!-- 点预览 -->
              <div 
                v-if="styleDialog.geometryType?.includes('Point')" 
                class="point-preview-large"
              >
                <div class="point-inner" :style="{
                  width: styleDialog.style.radius * 2 + 'px',
                  height: styleDialog.style.radius * 2 + 'px',
                  backgroundColor: styleDialog.style.fillColor,
                  borderColor: styleDialog.style.color,
                  borderWidth: styleDialog.style.weight + 'px',
                  opacity: styleDialog.style.opacity
                }"></div>
              </div>
              
              <!-- 线预览 -->
              <div 
                v-else-if="styleDialog.geometryType?.includes('Line')" 
                class="line-preview-large"
              >
                <div class="line-inner" :style="{
                  borderTop: `${styleDialog.style.weight}px ${Array.isArray(styleDialog.style.lineDash) && styleDialog.style.lineDash.length > 0 ? 
                    (styleDialog.style.lineDash.toString() === '4,8' ? 'dashed' : 'dotted') : 'solid'} ${styleDialog.style.color}`,
                  opacity: styleDialog.style.opacity
                }"></div>
              </div>
              
              <!-- 面预览 -->
              <div 
                v-else 
                class="polygon-preview-large"
              >
                <div class="polygon-inner" :style="{
                  backgroundColor: styleDialog.style.fillColor,
                  borderColor: styleDialog.style.color,
                  borderWidth: styleDialog.style.weight + 'px',
                  opacity: styleDialog.style.opacity
                }"></div>
              </div>
            </div>
          </div>
        </template>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="styleDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="applyStyleChanges">应用</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 图层标注设置对话框 -->
    <el-dialog
      v-model="labelDialog.visible"
      title="设置图层标注"
      width="400px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="label-editor-content">
        <template v-if="labelDialog.currentLayer">
          <h3>{{ labelDialog.currentLayer.name }}</h3>
          
          <!-- 是否显示标注 -->
          <el-form-item label="显示标注">
            <el-switch v-model="labelDialog.showLabels"></el-switch>
          </el-form-item>
          
          <!-- 标注字段选择 -->
          <el-form-item label="标注字段" v-if="labelDialog.showLabels">
            <el-select v-model="labelDialog.selectedField" placeholder="选择标注字段">
              <el-option
                v-for="field in labelDialog.availableFields"
                :key="field"
                :label="field"
                :value="field"
              ></el-option>
            </el-select>
          </el-form-item>
          
          <!-- 标注样式设置 -->
          <template v-if="labelDialog.showLabels">
            <!-- 字体大小 -->
          <el-form-item label="字体大小">
              <el-slider 
                v-model="labelDialog.fontSize" 
                :min="8" 
                :max="24"
                :step="1"
              ></el-slider>
          </el-form-item>
          
            <!-- 文字颜色 -->
            <el-form-item label="文字颜色">
              <el-color-picker v-model="labelDialog.labelColor"></el-color-picker>
          </el-form-item>
          </template>
        </template>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="labelDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="applyLabels">应用</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 上传图层对话框 -->
    <el-dialog
      v-model="uploadDialog.visible"
      title="上传图层"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeUploadDialog"
    >
      <div class="upload-dialog-content">
        <div class="upload-description">
          <el-alert
            title="上传图层文件"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>支持上传以下压缩格式的 Shapefile 文件：</p>
              <ul>
                <li>ZIP 格式 (.zip) - 推荐</li>
                <li>RAR 格式 (.rar)</li>
                <li>7Z 格式 (.7z)</li>
                <li>TAR.GZ 格式 (.tar.gz)</li>
              </ul>
              <p class="file-size-tip">文件大小限制：500MB</p>
            </template>
          </el-alert>
        </div>

        <div class="upload-area">
          <el-upload
            ref="uploadRef"
            :file-list="uploadFileList"
            :before-upload="beforeUpload"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :auto-upload="false"
            :limit="1"
            drag
            accept=".zip,.rar,.7z,.tar.gz"
          >
            <el-icon class="el-icon--upload"><Upload /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传一个压缩文件，且不超过500MB
              </div>
            </template>
          </el-upload>
        </div>

        <!-- 上传进度 -->
        <div v-if="uploadDialog.uploading" class="upload-progress">
          <el-progress
            :percentage="uploadDialog.uploadProgress"
            :stroke-width="8"
            status="success"
          />
          <p class="progress-text">正在上传图层文件...</p>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button
            @click="closeUploadDialog"
            :disabled="uploadDialog.uploading"
          >
            取消
          </el-button>
          <el-button
            type="primary"
            @click="handleUpload"
            :loading="uploadDialog.uploading"
            :disabled="!uploadFile"
          >
            {{ uploadDialog.uploading ? '上传中...' : '开始上传' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue';
import { useMapLayerManagerStore } from '/@/stores/olMapLayer/mapLayerManager';
import { ElMessage, ElMessageBox, ElTree } from 'element-plus';
import { Plus, Folder, Comment, Refresh, ZoomIn, Upload, Delete } from '@element-plus/icons-vue';
import {
  getLayerTypeDisplay,
  zoomToLayer,
  contextMenu
} from './useLayerOperations';
import { 
  initLayerList 
} from './useLayerList';
import {
  getPointPreviewStyle,
  getLinePreviewStyle,
  getPolygonPreviewStyle
} from './useLayerStyles';

import { labelDialog, showLabelDialog, applyLabels } from './useLayerLabels';
import { loadMapConfigFile } from '/@/utils/mapConfig';

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: true
  },
  position: {
    type: String,
    default: 'left'
  }
});

// 定义事件
const emit = defineEmits(['initialized', 'toggle-collapse']);

// 数据与状态
const searchText = ref('');
const treeData = ref<any[]>([]);
const expandedKeys = ref<string[]>([]);
const defaultExpandedKeys = ref<string[]>([]);
const layerTreeRef = ref<any>(null);

// 创建样式对话框状态
const styleDialog = reactive({
  visible: false,
  currentLayer: null as any,
  geometryType: null as string | null,
  style: {
    color: '#000000',
    weight: 1,
    opacity: 1,
    fillColor: '#3388ff',
    lineDash: [] as number[],
    radius: 6
  }
});

// 上传图层对话框状态
const uploadDialog = reactive({
  visible: false,
  uploading: false,
  uploadProgress: 0
});

// 上传文件相关
const uploadFile = ref<File | null>(null);
const uploadFileList = ref<any[]>([]);

// 默认工作区
const defaultWorkspace = ref('test_myworkspace');

// 获取所有节点ID，用于控制展开状态
const getAllNodeIds = (nodes: any[]): string[] => {
  let ids: string[] = [];
  nodes.forEach(node => {
    ids.push(node.id);
    if (node.children && node.children.length > 0) {
      ids = ids.concat(getAllNodeIds(node.children));
  }
});
  return ids;
};

// 添加计算属性 filteredTreeData
const filteredTreeData = computed(() => {
  if (!searchText.value) {
    return treeData.value;
  }
  
  const query = searchText.value.toLowerCase().trim();
  
  // 辅助函数：深度复制节点
  const deepClone = (node: any): any => {
    const clone = { ...node };
    if (node.children) {
      clone.children = node.children.map(deepClone);
    }
    return clone;
  };
    
  // 辅助函数：过滤树数据
  const filterNode = (node: any): any => {
    const nodeCopy = deepClone(node);
    
    // 如果节点名称包含搜索文本，保留整个节点
    if (nodeCopy.name.toLowerCase().includes(query)) {
      return nodeCopy;
    }
    
    // 如果是主题节点，递归处理子节点
    if (nodeCopy.isTheme && nodeCopy.children && nodeCopy.children.length > 0) {
      nodeCopy.children = nodeCopy.children
        .map(filterNode)
        .filter((childNode: any) => childNode !== null);

      // 如果过滤后有子节点保留，则保留该主题节点
      if (nodeCopy.children.length > 0) {
        return nodeCopy;
      }
    }
    
    // 如果不符合以上条件则过滤掉
    return null;
  };
  
  // 过滤整个树数据
  return treeData.value
    .map(filterNode)
    .filter((node: any) => node !== null);
});

// 更新树数据的函数
const updateTreeData = () => {
const mapLayerStore = useMapLayerManagerStore();
  const layers = mapLayerStore.mapConfig?.layers || [];
  
  // 保存当前展开状态
  const currentExpanded = [...expandedKeys.value];
  
  // 按主题分组所有图层
  const themeGroups: Record<string, any[]> = {};
  const noThemeLayers: any[] = [];
  
  // 对所有图层进行分组，同时标记是否已加载
  layers.forEach(layer => {
    // 使用类型断言来避免 theme 属性不存在的问题
    const layerWithTheme = layer as any;
    
    const layerObj = {
      id: layer.id,
      name: layer.name,
      type: layer.type,
      protocol: layer.protocol,
      loaded: mapLayerStore.isLayerLoaded(layer.id),
      visible: layer.active !== false,
      geometryType: layer.geometryType,
      originalLayer: layer  // 保留原始图层引用
    };
    
    // 如果有theme属性，则按theme分组
    if (layerWithTheme.theme) {
      if (!themeGroups[layerWithTheme.theme]) {
        themeGroups[layerWithTheme.theme] = [];
      }
      themeGroups[layerWithTheme.theme].push(layerObj);
    } else {
      // 没有theme的图层直接加入结果
      noThemeLayers.push(layerObj);
  }
  });
  
  // 构建最终结果，将每个theme作为一级节点
  const result: any[] = [];
  
  // 添加按主题分组的图层
  Object.keys(themeGroups).forEach(theme => {
    const children = themeGroups[theme];
    // 计算主题下图层的加载状态
    const loadedCount = children.filter(layer => layer.loaded).length;
    
    result.push({
      id: `theme-${theme}`,
      name: theme,
      isTheme: true,
      loaded: loadedCount === children.length, // 当所有子图层都加载时，主题复选框为选中状态
      indeterminate: loadedCount > 0 && loadedCount < children.length, // 部分加载时为半选状态
      children
    });
  });
  
  // 添加没有主题的图层
  result.push(...noThemeLayers);
  
  // 在更新树数据前保存当前展开状态
  defaultExpandedKeys.value = [...currentExpanded];
  
  // 更新树数据
  treeData.value = result;
};

// 处理节点展开事件
const handleNodeExpand = (data: any) => {
  if (!expandedKeys.value.includes(data.id)) {
    expandedKeys.value.push(data.id);
    // 同步更新defaultExpandedKeys
    defaultExpandedKeys.value = [...expandedKeys.value];
    // 保存展开状态到本地存储
    localStorage.setItem('layerTree-expandedKeys', JSON.stringify(expandedKeys.value));
  }
};

// 处理节点折叠事件
const handleNodeCollapse = (data: any) => {
  const index = expandedKeys.value.indexOf(data.id);
  if (index !== -1) {
    expandedKeys.value.splice(index, 1);
    // 同步更新defaultExpandedKeys
    defaultExpandedKeys.value = [...expandedKeys.value];
    // 保存展开状态到本地存储
    localStorage.setItem('layerTree-expandedKeys', JSON.stringify(expandedKeys.value));
  }
};

// 监听图层加载状态变化，更新界面
watch(() => useMapLayerManagerStore().loadedLayers, () => {
  // 保存当前展开状态
  const currentExpanded = [...expandedKeys.value];
  
  // 更新树数据
  updateTreeData();
  
  // 恢复展开状态
    nextTick(() => {
    expandedKeys.value = currentExpanded;
  });
}, { deep: true });

// 格式化透明度显示
const formatTooltip = (val: number): string => {
  return val * 100 + '%';
};

// 打开样式编辑器
const openStyleEditor = (layer: any) => {
  // 确保是矢量图层
  if (layer.type !== 'vector') {
        return;
      }
    
  // 复制样式到编辑对话框
  styleDialog.currentLayer = layer;
  styleDialog.geometryType = layer.originalLayer?.geometryType;
  
  // 获取当前样式
  const currentStyle = layer.originalLayer?.defaultStyle || {};
  
  // 设置样式值
  styleDialog.style = {
    color: currentStyle.color || '#000000',
    weight: currentStyle.weight !== undefined ? currentStyle.weight : 1,
    opacity: currentStyle.opacity !== undefined ? currentStyle.opacity : 1,
    fillColor: currentStyle.fillColor || '#3388ff',
    lineDash: currentStyle.lineDash || [],
    radius: currentStyle.radius || 6 // 确保 radius 也包含在样式中
  };
  
  // 显示对话框
  styleDialog.visible = true;
};

// 应用样式更改
const applyStyleChanges = () => {
  if (!styleDialog.currentLayer) {
    styleDialog.visible = false;
        return;
      }
      
  const mapLayerStore = useMapLayerManagerStore();
  const layerId = styleDialog.currentLayer.id;
  
  // 创建样式更新对象
  const styleUpdate: { [key: string]: any } = {
    color: styleDialog.style.color,
    weight: styleDialog.style.weight,
    opacity: styleDialog.style.opacity,
    fillColor: styleDialog.style.fillColor
  };
  
  // 对于线条，添加线条样式
  if (styleDialog.geometryType?.includes('Line')) {
    (styleUpdate as any)['lineDash'] = styleDialog.style.lineDash;
  }

  // 对于点，添加点大小
  if (styleDialog.geometryType?.includes('Point')) {
    (styleUpdate as any)['radius'] = styleDialog.style.radius;
  }
  
  // 更新图层样式
  const layer = mapLayerStore.getLayerById(layerId);
  if (layer) {
    // 更新图层默认样式
    if (typeof layer.defaultStyle !== 'string') {
      Object.assign(layer.defaultStyle || {}, styleUpdate);
    }
    
    // 尝试重新加载图层以应用样式
    mapLayerStore.reloadLayer(layerId).then(success => {
      if (success) {
        ElMessage.success('样式已更新');
        } else {
        ElMessage.error('样式更新失败');
      }
    });
  }
  
  styleDialog.visible = false;
};

// 处理单个图层的复选框变化
const handleLayerToggle = async (layer: any) => {
      const mapLayerStore = useMapLayerManagerStore();
  
  if (layer.loaded) {
    // 加载图层
    try {
      const success = await mapLayerStore.addLayer(layer.id);
      if (!success) {
        layer.loaded = false; // 恢复原状态
        ElMessage.error(`加载图层失败: ${layer.name}`);
      } else {
        ElMessage.success(`已加载图层: ${layer.name}`);
      }
    } catch (error) {
      layer.loaded = false; // 恢复原状态
      ElMessage.error(`加载图层出错: ${error instanceof Error ? error.message : String(error)}`);
    }
  } else {
    // 移除图层
    const success = mapLayerStore.removeLayer(layer.id);
    if (success) {
      ElMessage.success(`已移除图层: ${layer.name}`);
    } else {
      layer.loaded = true; // 恢复原状态
      ElMessage.error(`移除图层失败: ${layer.name}`);
    }
  }
  
  // 更新树数据，保持展开状态
  defaultExpandedKeys.value = [...expandedKeys.value];
  updateTreeData();
};

// 处理主题目录的复选框变化
const handleThemeToggle = async (themeNode: any) => {
  const mapLayerStore = useMapLayerManagerStore();
  const children = themeNode.children || [];
  
  // 确保当前主题节点仍然保持展开
  if (!expandedKeys.value.includes(themeNode.id)) {
    expandedKeys.value.push(themeNode.id);
    defaultExpandedKeys.value = [...expandedKeys.value];
  }
  
  if (themeNode.loaded) {
    // 加载所有主题图层
    for (const layer of children) {
      if (!layer.loaded) {
        try {
          const success = await mapLayerStore.addLayer(layer.id);
          if (success) {
            layer.loaded = true;
          } else {
            console.error(`加载主题图层失败: ${layer.name}`);
          }
        } catch (error) {
          console.error(`加载主题图层出错: ${error}`);
        }
      }
    }
    ElMessage.success(`已加载主题 ${themeNode.name} 的所有图层`);
    } else {
    // 移除所有主题图层
    let successCount = 0;
    for (const layer of children) {
      if (layer.loaded) {
        const success = mapLayerStore.removeLayer(layer.id);
        if (success) {
          layer.loaded = false;
          successCount++;
        }
      }
    }
    ElMessage.success(`已移除主题 ${themeNode.name} 的 ${successCount} 个图层`);
    }
    
  // 更新树数据，保持展开状态
  defaultExpandedKeys.value = [...expandedKeys.value];
  updateTreeData();
};

// 刷新图层列表
const refreshLayerList = () => {
  // 调用updateTreeData更新树数据
  updateTreeData();
};

// 加载配置文件获取默认工作区
const loadConfig = async () => {
  try {
    console.log('正在加载baseMap2配置以获取默认工作区...');
    const config = await loadMapConfigFile('baseMap2');

    if (config.mapConfig?.queryConfig?.defaultWorkspace) {
      defaultWorkspace.value = config.mapConfig.queryConfig.defaultWorkspace;
      console.log('已设置默认工作区:', defaultWorkspace.value);
    } else {
      console.warn('未在baseMap2.json中找到默认工作区，使用默认值:', defaultWorkspace.value);
    }
  } catch (error) {
    console.error('加载baseMap2配置失败:', error);
    console.log('使用默认工作区:', defaultWorkspace.value);
  }
};

// 重新加载地图配置
const reloadMapConfig = async () => {
  try {
    console.log('开始重新加载 baseMap2 配置...');
    const mapLayerStore = useMapLayerManagerStore();

    // 显示加载提示
    const loadingMessage = ElMessage({
      message: '正在更新地图配置...',
      type: 'info',
      duration: 0, // 不自动关闭
      showClose: false
    });

    try {
      // 重新加载配置
      const success = await mapLayerStore.loadConfig();

      if (success) {
        console.log('baseMap2 配置重新加载成功');

        // 重新加载默认工作区配置
        await loadConfig();

        // 关闭加载提示
        loadingMessage.close();

        ElMessage.success('地图配置已更新，新上传的图层现在可用');
      } else {
        // 关闭加载提示
        loadingMessage.close();

        console.warn('baseMap2 配置重新加载失败');
        ElMessage.warning('地图配置更新失败，但操作已完成');
      }
    } catch (configError) {
      // 关闭加载提示
      loadingMessage.close();
      throw configError;
    }

  } catch (error) {
    console.error('重新加载 baseMap2 配置时出错:', error);
    ElMessage.warning('地图配置更新失败，但操作已完成');
  }
};

// 打开上传对话框
const openUploadDialog = () => {
  uploadDialog.visible = true;
  uploadFile.value = null;
  uploadFileList.value = [];
  uploadDialog.uploading = false;
  uploadDialog.uploadProgress = 0;
};

// 关闭上传对话框
const closeUploadDialog = () => {
  uploadDialog.visible = false;
  uploadFile.value = null;
  uploadFileList.value = [];
  uploadDialog.uploading = false;
  uploadDialog.uploadProgress = 0;
};

// 处理文件选择
const handleFileChange = (file: any, fileList: any[]) => {
  uploadFile.value = file.raw;
  uploadFileList.value = fileList;
  return false; // 阻止自动上传
};

// 处理文件移除
const handleFileRemove = () => {
  uploadFile.value = null;
  uploadFileList.value = [];
};

// 验证文件类型
const beforeUpload = (file: File) => {
  const allowedTypes = [
    'application/zip',
    'application/x-zip-compressed',
    'application/x-rar-compressed',
    'application/x-7z-compressed',
    'application/gzip',
    'application/x-tar'
  ];

  const isValidType = allowedTypes.includes(file.type) ||
                     file.name.toLowerCase().endsWith('.zip') ||
                     file.name.toLowerCase().endsWith('.rar') ||
                     file.name.toLowerCase().endsWith('.7z') ||
                     file.name.toLowerCase().endsWith('.tar.gz');

  if (!isValidType) {
    ElMessage.error('只支持上传 ZIP、RAR、7Z、TAR.GZ 等压缩格式的文件！');
    return false;
  }

  const isLt50M = file.size / 1024 / 1024 < 50;
  if (!isLt50M) {
    ElMessage.error('文件大小不能超过 50MB！');
    return false;
  }

  return true;
};

// 执行上传
const handleUpload = async () => {
  if (!uploadFile.value) {
    ElMessage.warning('请先选择要上传的文件！');
    return;
  }

  uploadDialog.uploading = true;
  uploadDialog.uploadProgress = 0;

  try {
    // 创建 FormData
    const formData = new FormData();
    formData.append('file', uploadFile.value);
    formData.append('workspace', defaultWorkspace.value);

    // 从环境变量获取地址和端口号
    const host = import.meta.env.VITE_GEOSERVER_HOST || import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const port = import.meta.env.VITE_GEOSERVER_HD_PORT || '5083';
    const apiUrl = `http://${host}:${port}/api/map/archive/upload/`;

    console.log(`开始上传文件到: ${apiUrl}`);
    console.log(`上传文件信息:`, {
      name: uploadFile.value.name,
      size: uploadFile.value.size,
      type: uploadFile.value.type
    });
    console.log(`使用工作区: ${defaultWorkspace.value}`);

    // 使用 XMLHttpRequest 支持上传进度
    const responseData = await new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // 监听上传进度
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          uploadDialog.uploadProgress = Math.round((event.loaded / event.total) * 100);
          console.log(`上传进度: ${uploadDialog.uploadProgress}%`);
        }
      });

      // 监听请求完成
      xhr.addEventListener('load', () => {
        console.log(`上传响应状态: ${xhr.status}`);

        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const data = JSON.parse(xhr.responseText);
            console.log('上传成功，服务器响应:', data);
            resolve(data);
          } catch (parseError) {
            console.log('响应不是JSON格式，原始响应:', xhr.responseText);
            resolve({ message: '上传成功', response: xhr.responseText });
          }
        } else {
          reject(new Error(`HTTP 错误: ${xhr.status} ${xhr.statusText}`));
        }
      });

      // 监听请求错误
      xhr.addEventListener('error', () => {
        reject(new Error('网络错误或请求失败'));
      });

      // 监听请求超时
      xhr.addEventListener('timeout', () => {
        reject(new Error('请求超时'));
      });

      // 配置请求
      xhr.open('POST', apiUrl);
      xhr.timeout = 60000; // 60秒超时

      // 发送请求
      xhr.send(formData);
    });

    // 显示成功消息
    ElMessage.success('图层上传成功！');

    // 记录成功信息
    console.log('图层上传完成，准备重新加载配置和刷新图层列表');

    // 关闭对话框
    closeUploadDialog();

    // 重新加载 baseMap2 配置
    await reloadMapConfig();

    // 刷新图层列表
    refreshLayerList();

  } catch (error: any) {
    console.error('上传失败详细信息:', {
      error: error,
      message: error.message,
      stack: error.stack,
      uploadFile: uploadFile.value ? {
        name: uploadFile.value.name,
        size: uploadFile.value.size,
        type: uploadFile.value.type
      } : null,
      apiUrl: `http://${import.meta.env.VITE_GEOSERVER_HOST || import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1'}:${import.meta.env.VITE_GEOSERVER_HD_PORT || '5083'}/api/map/shapefile/extract`
    });

    ElMessage.error(`上传失败: ${error.message || '未知错误'}`);
  } finally {
    uploadDialog.uploading = false;
    uploadDialog.uploadProgress = 0;
    console.log('上传操作结束，重置状态');
  }
};

// 处理缩放至图层
const handleZoomToLayer = (layer: any) => {
  if (!layer || !layer.loaded) {
    return;
    }

  try {
    // 设置当前图层到contextMenu，以便使用现有的zoomToLayer函数
    contextMenu.currentLayer = layer;

    // 调用现有的缩放函数
    zoomToLayer();
  } catch (error) {
    ElMessage.error(`处理图层缩放时出错: ${error}`);
  }
};

// 判断是否为临时文件图层
const isTemporaryLayer = (layer: any) => {
  if (!layer || !layer.originalLayer) {
    return false;
  }

  // 检查图层的主题是否为"临时文件"
  return layer.originalLayer.theme === '临时文件';
};

// 处理删除临时文件图层
const handleDeleteLayer = async (layer: any) => {
  if (!layer || !layer.originalLayer) {
    ElMessage.error('图层信息不完整');
    return;
  }

  const originalLayer = layer.originalLayer;

  // 确认删除操作
  try {
    await ElMessageBox.confirm(
      `确定要删除临时文件图层 "${originalLayer.name}" 吗？此操作将删除本地文件、GeoServer图层和地图配置，且不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );
  } catch {
    // 用户取消删除
    return;
  }

  try {
    // 从环境变量获取地址和端口号
    const host = import.meta.env.VITE_GEOSERVER_HOST || import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const port = import.meta.env.VITE_GEOSERVER_HD_PORT || '5083';

    // 构建删除API URL
    const deleteUrl = `http://${host}:${port}/api/map/archive/delete/?workspace=${encodeURIComponent(originalLayer.workspace || defaultWorkspace.value)}&layer_name=${encodeURIComponent(originalLayer.layerName || originalLayer.name)}`;

    console.log(`开始删除临时文件图层: ${deleteUrl}`);
    console.log(`删除图层信息:`, {
      workspace: originalLayer.workspace || defaultWorkspace.value,
      layerName: originalLayer.layerName || originalLayer.name,
      theme: originalLayer.theme
    });

    // 发送删除请求
    const response = await fetch(deleteUrl, {
      method: 'GET'
    });

    console.log(`删除响应状态: ${response.status}`);

    if (!response.ok) {
      throw new Error(`HTTP 错误: ${response.status} ${response.statusText}`);
    }

    // 解析响应数据
    let responseData;
    try {
      responseData = await response.json();
      console.log('删除成功，服务器响应:', responseData);
    } catch (parseError) {
      console.log('响应不是JSON格式，原始响应:', await response.text());
      responseData = { message: '删除成功' };
    }

    // 从地图中移除图层
    const mapLayerStore = useMapLayerManagerStore();
    mapLayerStore.removeLayer(layer.id);

    ElMessage.success(`临时文件图层 "${originalLayer.name}" 删除成功！`);

    // 记录成功信息
    console.log('临时文件图层删除完成，准备重新加载配置和刷新图层列表');

    // 重新加载 baseMap2 配置
    await reloadMapConfig();

    // 刷新图层列表
    refreshLayerList();

  } catch (error: any) {
    const host = import.meta.env.VITE_GEOSERVER_HOST || import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const port = import.meta.env.VITE_GEOSERVER_HD_PORT || '5083';

    console.error('删除临时文件图层失败:', {
      error: error,
      message: error.message,
      layer: originalLayer,
      deleteUrl: `http://${host}:${port}/api/map/archive/delete/?workspace=${encodeURIComponent(originalLayer.workspace || defaultWorkspace.value)}&layer_name=${encodeURIComponent(originalLayer.layerName || originalLayer.name)}`
    });

    ElMessage.error(`删除失败: ${error.message || '未知错误'}`);
  }
};

// 过滤图层
const filterLayers = () => {
  const filterText = searchText.value.toLowerCase();
  const filterTreeData = (data: any) => {
    const matches = data.name.toLowerCase().includes(filterText);
    if (data.children) {
      data.children = data.children.map(filterTreeData).filter(Boolean);
    }
    return matches || (data.children && data.children.length > 0);
  };
  treeData.value = treeData.value.map(filterTreeData).filter(Boolean);
};

// 初始化
onMounted(async () => {
  // 加载配置文件获取默认工作区
  await loadConfig();

  // 初始化树数据为空数组
  treeData.value = [];

  // 尝试从本地存储加载展开状态
  try {
    const savedExpandedKeys = localStorage.getItem('layerTree-expandedKeys');
    if (savedExpandedKeys) {
      const parsed = JSON.parse(savedExpandedKeys);
      expandedKeys.value = parsed;
      defaultExpandedKeys.value = parsed;
    }
  } catch (e) {
    console.error('Failed to load expanded keys from localStorage:', e);
  }
  
  // 监听mapConfig变化，初始化图层列表
  watch(() => useMapLayerManagerStore().mapConfig, (newConfig) => {
    if (newConfig) {
      nextTick(() => {
        refreshLayerList();
        // 如果没有保存的展开状态，则默认展开所有主题节点
        if (expandedKeys.value.length === 0 && treeData.value.length > 0) {
          // 获取所有主题节点ID
          const themeIds = treeData.value
            .filter(node => node.isTheme)
            .map(node => node.id);
          expandedKeys.value = themeIds;
          defaultExpandedKeys.value = themeIds;
        }
      });
    }
  }, { immediate: true });
});
</script>

<style lang="scss" scoped>
@import './index.scss';

/* 右侧收缩/展开控件样式 */
.layer-collapse-bar {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 80px;
  background-color: rgba(16, 64, 70, 0.95);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  z-index: 10;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  box-shadow: -2px 0 6px rgba(0, 0, 0, 0.3);
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  
  &:hover {
    background-color: rgba(22, 80, 85, 1);
  }
  
  .collapse-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    width: 100%;
    height: 40px;
    
    .el-icon {
      font-size: 14px;
      background-color: rgba(59, 179, 59, 0.9);
      width: 18px;
      height: 18px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
    }
  }
}

// 搜索容器样式
.search-container {
  display: flex;
  gap: 8px;
  align-items: center;

  .search-input {
    flex: 1;
  }

  .upload-button {
    flex-shrink: 0;
    white-space: nowrap;
  }
}

// 上传对话框样式
.upload-dialog-content {
  .upload-description {
    margin-bottom: 20px;

    .el-alert {
      :deep(.el-alert__content) {
        p {
          margin: 8px 0;
          line-height: 1.5;
        }

        ul {
          margin: 8px 0;
          padding-left: 20px;

          li {
            margin: 4px 0;
          }
        }

        .file-size-tip {
          color: #909399;
          font-size: 12px;
          margin-top: 8px;
        }

        .upload-note {
          color: #67C23A;
          font-size: 12px;
          margin-top: 8px;
          font-weight: 500;
        }
      }
    }
  }

  .upload-area {
    margin-bottom: 20px;

    :deep(.el-upload-dragger) {
      width: 100%;
      height: 120px;
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: border-color 0.3s;

      &:hover {
        border-color: #409EFF;
      }

      .el-icon--upload {
        font-size: 28px;
        color: #c0c4cc;
        margin-bottom: 8px;
      }

      .el-upload__text {
        color: #606266;
        font-size: 14px;

        em {
          color: #409EFF;
          font-style: normal;
        }
      }
    }

    :deep(.el-upload__tip) {
      color: #909399;
      font-size: 12px;
      margin-top: 8px;
      text-align: center;
    }
  }

  .upload-progress {
    margin-top: 20px;

    .progress-text {
      text-align: center;
      color: #606266;
      font-size: 14px;
      margin-top: 8px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

// 删除按钮样式
.delete-button {
  color: #f56c6c !important;

  &:hover {
    color: #f78989 !important;
    background-color: #fef0f0 !important;
  }
}
</style>