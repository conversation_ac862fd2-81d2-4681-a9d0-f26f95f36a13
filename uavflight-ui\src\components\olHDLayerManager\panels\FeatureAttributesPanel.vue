<!--
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-08-05 16:00:00
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-07-31 11:23:09
 * @FilePath: \uavflight-ui\src\components\olHDLayerManager\panels\FeatureAttributesPanel.vue
 * @Description: 要素属性面板，显示图层要素的属性信息
 * 
 * Copyright (c) 2025 by 吴博文, All Rights Reserved. 
-->
<template>
  <div class="hd-feature-attributes">
    <div class="panel-title">
      <span>{{ title || '要素属性信息' }}</span>
    </div>
    <div class="panel-content">
      <el-empty v-if="!attributes || Object.keys(attributes).length === 0" description="暂无要素属性信息">
        <template #description>
          <p>请使用属性查询工具点击地图要素</p>
        </template>
        <el-button type="primary" @click="activateAttributeQuery">激活属性查询</el-button>
      </el-empty>
      <el-scrollbar v-else>
        <el-table :data="attributesArray" stripe style="width: 100%">
          <el-table-column prop="name" label="属性名称" width="140" show-overflow-tooltip />
          <el-table-column prop="value" label="属性值" show-overflow-tooltip />
        </el-table>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, inject } from 'vue';
import { Document, Back } from '@element-plus/icons-vue';

// 使用inject获取父组件提供的数据
const attributes = inject('featureAttributes', ref({}));
const layerId = inject('attributeLayerId', ref(''));
const title = inject('attributeTitle', ref(''));

// 获取GIS工具组件的引用
const gisTools = inject<any>('gisTools', null);

// Emits定义
const emit = defineEmits(['close']);

// 处理属性数据，转换为表格可用的数组形式
const attributesArray = computed(() => {
  if (!attributes.value) return [];
  
  return Object.entries(attributes.value)
    .filter(([key]) => key !== 'layerId' && key !== 'geometry' && key !== 'the_geom')
    .map(([key, value]) => ({
      name: key,
      value: value === null ? '' : String(value)
    }));
});

// 获取父组件的关闭方法
const closeAttributesPanel = inject('closeAttributesPanel', () => {});

// 关闭面板
const close = () => {
  closeAttributesPanel();
};

// 激活属性查询功能
const activateAttributeQuery = () => {
  // 如果能从父组件获取到GIS工具引用，则激活属性查询功能
  if (gisTools && typeof gisTools.activateAttributeInfo === 'function') {
    gisTools.activateAttributeInfo();
  } else {
    // 如果没有获取到GIS工具引用，则提示用户手动激活
    console.warn('无法自动激活属性查询工具，请手动点击GIS工具栏中的属性查询按钮');
  }
};
</script>

<style lang="scss" scoped>
.hd-feature-attributes {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
  padding: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  
  .el-empty {
    color: #fff;
    padding: 20px 0;
    
    .el-empty__description p {
      color: #d3d3d3;
      margin-bottom: 15px;
    }
  }
}

/* 表格样式覆盖 */
:deep(.el-table) {
  background-color: transparent !important;
  
  &::before {
    display: none;
  }
  
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: rgba(64, 158, 255, 0.8) !important;
        color: #ffffff !important;
        border-bottom: 1px solid rgba(64, 158, 255, 0.5);
        
        .cell {
          font-weight: bold;
          color: #ffffff !important;
          font-size: 14px;
        }
      }
    }
  }
  
  .el-table__body-wrapper {
    .el-table__body {
      td {
        background-color: rgba(15, 25, 40, 0.9) !important;
        color: #e6e6e6 !important;
        border-bottom: 1px solid rgba(64, 158, 255, 0.2);
      }
      
      tr:nth-child(even) td {
        background-color: rgba(30, 45, 70, 0.9) !important;
      }
      
      /* 移除悬停效果 - 使用强制选择器 */
      tr:hover > td {
        background-color: rgba(15, 25, 40, 0.9) !important;
        color: #e6e6e6 !important;
      }
      
      tr:nth-child(even):hover > td {
        background-color: rgba(30, 45, 70, 0.9) !important;
        color: #e6e6e6 !important;
      }
    }
  }
  
  /* 完全禁用Element Plus的hover效果 */
  .el-table__body tr.hover-row > td.el-table__cell {
    background-color: rgba(15, 25, 40, 0.9) !important;
  }
  
  .el-table__body tr.hover-row.el-table__row--striped > td.el-table__cell {
    background-color: rgba(30, 45, 70, 0.9) !important;
  }
  
  /* 针对悬停行的特殊处理 */
  .el-table__row--striped.hover-row td.el-table__cell {
    background-color: rgba(30, 45, 70, 0.9) !important;
  }
  
  .hover-row td.el-table__cell {
    background-color: rgba(15, 25, 40, 0.9) !important;
  }
  
  /* 修复Element Plus表格默认文字颜色 */
  .cell {
    color: #e6e6e6 !important;
  }
  
  /* 确保表格边框颜色 */
  &, tr, td, th {
    border-color: rgba(64, 158, 255, 0.2) !important;
  }
}

/* 按钮样式 */
:deep(.el-button) {
  &.el-button--primary {
    background-color: #409eff;
    border-color: #409eff;
    
    &:hover {
      background-color: #66b1ff;
      border-color: #66b1ff;
    }
  }
}
</style> 