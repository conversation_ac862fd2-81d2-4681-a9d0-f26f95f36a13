{"version": 3, "sources": ["../../ol/source/WMTS.js"], "sourcesContent": ["/**\n * @module ol/source/WMTS\n */\n\nimport TileImage from './TileImage.js';\nimport {appendParams} from '../uri.js';\nimport {containsExtent} from '../extent.js';\nimport {createFromCapabilitiesMatrixSet} from '../tilegrid/WMTS.js';\nimport {createFromTileUrlFunctions, expandUrl} from '../tileurlfunction.js';\nimport {equivalent, get as getProjection, transformExtent} from '../proj.js';\n\n/**\n * Request encoding. One of 'KVP', 'REST'.\n * @typedef {'KVP' | 'REST'} RequestEncoding\n */\n\n/**\n * @typedef {Object} Options\n * @property {import(\"./Source.js\").AttributionLike} [attributions] Attributions.\n * @property {boolean} [attributionsCollapsible=true] Attributions are collapsible.\n * @property {number} [cacheSize] Initial tile cache size. Will auto-grow to hold at least the number of tiles in the viewport.\n * @property {null|string} [crossOrigin] The `crossOrigin` attribute for loaded images.  Note that\n * you must provide a `crossOrigin` value if you want to access pixel data with the Canvas renderer.\n * See https://developer.mozilla.org/en-US/docs/Web/HTML/CORS_enabled_image for more detail.\n * @property {boolean} [interpolate=true] Use interpolated values when resampling.  By default,\n * linear interpolation is used when resampling.  Set to false to use the nearest neighbor instead.\n * @property {import(\"../tilegrid/WMTS.js\").default} tileGrid Tile grid.\n * @property {import(\"../proj.js\").ProjectionLike} [projection] Projection. Default is the view projection.\n * @property {number} [reprojectionErrorThreshold=0.5] Maximum allowed reprojection error (in pixels).\n * Higher values can increase reprojection performance, but decrease precision.\n * @property {RequestEncoding} [requestEncoding='KVP'] Request encoding.\n * @property {string} layer Layer name as advertised in the WMTS capabilities.\n * @property {string} style Style name as advertised in the WMTS capabilities.\n * @property {typeof import(\"../ImageTile.js\").default} [tileClass]  Class used to instantiate image tiles. Default is {@link module:ol/ImageTile~ImageTile}.\n * @property {number} [tilePixelRatio=1] The pixel ratio used by the tile service.\n * For example, if the tile service advertizes 256px by 256px tiles but actually sends 512px\n * by 512px images (for retina/hidpi devices) then `tilePixelRatio`\n * should be set to `2`.\n * @property {string} [format='image/jpeg'] Image format. Only used when `requestEncoding` is `'KVP'`.\n * @property {string} [version='1.0.0'] WMTS version.\n * @property {string} matrixSet Matrix set.\n * @property {!Object} [dimensions] Additional \"dimensions\" for tile requests.\n * This is an object with properties named like the advertised WMTS dimensions.\n * @property {string} [url]  A URL for the service.\n * For the RESTful request encoding, this is a URL\n * template.  For KVP encoding, it is normal URL. A `{?-?}` template pattern,\n * for example `subdomain{a-f}.domain.com`, may be used instead of defining\n * each one separately in the `urls` option.\n * @property {import(\"../Tile.js\").LoadFunction} [tileLoadFunction] Optional function to load a tile given a URL. The default is\n * ```js\n * function(imageTile, src) {\n *   imageTile.getImage().src = src;\n * };\n * ```\n * @property {Array<string>} [urls] An array of URLs.\n * Requests will be distributed among the URLs in this array.\n * @property {boolean} [wrapX=false] Whether to wrap the world horizontally.\n * @property {number} [transition] Duration of the opacity transition for rendering.\n * To disable the opacity transition, pass `transition: 0`.\n * @property {number|import(\"../array.js\").NearestDirectionFunction} [zDirection=0]\n * Choose whether to use tiles with a higher or lower zoom level when between integer\n * zoom levels. See {@link module:ol/tilegrid/TileGrid~TileGrid#getZForResolution}.\n */\n\n/**\n * @classdesc\n * Layer source for tile data from WMTS servers.\n * @api\n */\nclass WMTS extends TileImage {\n  /**\n   * @param {Options} options WMTS options.\n   */\n  constructor(options) {\n    // TODO: add support for TileMatrixLimits\n\n    const requestEncoding =\n      options.requestEncoding !== undefined ? options.requestEncoding : 'KVP';\n\n    // FIXME: should we create a default tileGrid?\n    // we could issue a getCapabilities xhr to retrieve missing configuration\n    const tileGrid = options.tileGrid;\n\n    let urls = options.urls;\n    if (urls === undefined && options.url !== undefined) {\n      urls = expandUrl(options.url);\n    }\n\n    super({\n      attributions: options.attributions,\n      attributionsCollapsible: options.attributionsCollapsible,\n      cacheSize: options.cacheSize,\n      crossOrigin: options.crossOrigin,\n      interpolate: options.interpolate,\n      projection: options.projection,\n      reprojectionErrorThreshold: options.reprojectionErrorThreshold,\n      tileClass: options.tileClass,\n      tileGrid: tileGrid,\n      tileLoadFunction: options.tileLoadFunction,\n      tilePixelRatio: options.tilePixelRatio,\n      urls: urls,\n      wrapX: options.wrapX !== undefined ? options.wrapX : false,\n      transition: options.transition,\n      zDirection: options.zDirection,\n    });\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.version_ = options.version !== undefined ? options.version : '1.0.0';\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.format_ = options.format !== undefined ? options.format : 'image/jpeg';\n\n    /**\n     * @private\n     * @type {!Object}\n     */\n    this.dimensions_ =\n      options.dimensions !== undefined ? options.dimensions : {};\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.layer_ = options.layer;\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.matrixSet_ = options.matrixSet;\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.style_ = options.style;\n\n    // FIXME: should we guess this requestEncoding from options.url(s)\n    //        structure? that would mean KVP only if a template is not provided.\n\n    /**\n     * @private\n     * @type {RequestEncoding}\n     */\n    this.requestEncoding_ = requestEncoding;\n\n    this.setKey(this.getKeyForDimensions_());\n\n    if (urls && urls.length > 0) {\n      this.tileUrlFunction = createFromTileUrlFunctions(\n        urls.map(this.createFromWMTSTemplate.bind(this))\n      );\n    }\n  }\n\n  /**\n   * Set the URLs to use for requests.\n   * URLs may contain OGC conform URL Template Variables: {TileMatrix}, {TileRow}, {TileCol}.\n   * @param {Array<string>} urls URLs.\n   */\n  setUrls(urls) {\n    this.urls = urls;\n    const key = urls.join('\\n');\n    this.setTileUrlFunction(\n      createFromTileUrlFunctions(\n        urls.map(this.createFromWMTSTemplate.bind(this))\n      ),\n      key\n    );\n  }\n\n  /**\n   * Get the dimensions, i.e. those passed to the constructor through the\n   * \"dimensions\" option, and possibly updated using the updateDimensions\n   * method.\n   * @return {!Object} Dimensions.\n   * @api\n   */\n  getDimensions() {\n    return this.dimensions_;\n  }\n\n  /**\n   * Return the image format of the WMTS source.\n   * @return {string} Format.\n   * @api\n   */\n  getFormat() {\n    return this.format_;\n  }\n\n  /**\n   * Return the layer of the WMTS source.\n   * @return {string} Layer.\n   * @api\n   */\n  getLayer() {\n    return this.layer_;\n  }\n\n  /**\n   * Return the matrix set of the WMTS source.\n   * @return {string} MatrixSet.\n   * @api\n   */\n  getMatrixSet() {\n    return this.matrixSet_;\n  }\n\n  /**\n   * Return the request encoding, either \"KVP\" or \"REST\".\n   * @return {RequestEncoding} Request encoding.\n   * @api\n   */\n  getRequestEncoding() {\n    return this.requestEncoding_;\n  }\n\n  /**\n   * Return the style of the WMTS source.\n   * @return {string} Style.\n   * @api\n   */\n  getStyle() {\n    return this.style_;\n  }\n\n  /**\n   * Return the version of the WMTS source.\n   * @return {string} Version.\n   * @api\n   */\n  getVersion() {\n    return this.version_;\n  }\n\n  /**\n   * @private\n   * @return {string} The key for the current dimensions.\n   */\n  getKeyForDimensions_() {\n    const res = this.urls ? this.urls.slice(0) : [];\n    for (const key in this.dimensions_) {\n      res.push(key + '-' + this.dimensions_[key]);\n    }\n    return res.join('/');\n  }\n\n  /**\n   * Update the dimensions.\n   * @param {Object} dimensions Dimensions.\n   * @api\n   */\n  updateDimensions(dimensions) {\n    Object.assign(this.dimensions_, dimensions);\n    this.setKey(this.getKeyForDimensions_());\n  }\n\n  /**\n   * @param {string} template Template.\n   * @return {import(\"../Tile.js\").UrlFunction} Tile URL function.\n   */\n  createFromWMTSTemplate(template) {\n    const requestEncoding = this.requestEncoding_;\n\n    // context property names are lower case to allow for a case insensitive\n    // replacement as some services use different naming conventions\n    const context = {\n      'layer': this.layer_,\n      'style': this.style_,\n      'tilematrixset': this.matrixSet_,\n    };\n\n    if (requestEncoding == 'KVP') {\n      Object.assign(context, {\n        'Service': 'WMTS',\n        'Request': 'GetTile',\n        'Version': this.version_,\n        'Format': this.format_,\n      });\n    }\n\n    // TODO: we may want to create our own appendParams function so that params\n    // order conforms to wmts spec guidance, and so that we can avoid to escape\n    // special template params\n\n    template =\n      requestEncoding == 'KVP'\n        ? appendParams(template, context)\n        : template.replace(/\\{(\\w+?)\\}/g, function (m, p) {\n            return p.toLowerCase() in context ? context[p.toLowerCase()] : m;\n          });\n\n    const tileGrid = /** @type {import(\"../tilegrid/WMTS.js\").default} */ (\n      this.tileGrid\n    );\n    const dimensions = this.dimensions_;\n\n    return (\n      /**\n       * @param {import(\"../tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n       * @param {number} pixelRatio Pixel ratio.\n       * @param {import(\"../proj/Projection.js\").default} projection Projection.\n       * @return {string|undefined} Tile URL.\n       */\n      function (tileCoord, pixelRatio, projection) {\n        if (!tileCoord) {\n          return undefined;\n        }\n        const localContext = {\n          'TileMatrix': tileGrid.getMatrixId(tileCoord[0]),\n          'TileCol': tileCoord[1],\n          'TileRow': tileCoord[2],\n        };\n        Object.assign(localContext, dimensions);\n        let url = template;\n        if (requestEncoding == 'KVP') {\n          url = appendParams(url, localContext);\n        } else {\n          url = url.replace(/\\{(\\w+?)\\}/g, function (m, p) {\n            return localContext[p];\n          });\n        }\n        return url;\n      }\n    );\n  }\n}\n\nexport default WMTS;\n\n/**\n * Generate source options from a capabilities object.\n * @param {Object} wmtsCap An object representing the capabilities document.\n * @param {!Object} config Configuration properties for the layer.  Defaults for\n *                  the layer will apply if not provided.\n *\n * Required config properties:\n *  - layer - {string} The layer identifier.\n *\n * Optional config properties:\n *  - matrixSet - {string} The matrix set identifier, required if there is\n *       more than one matrix set in the layer capabilities.\n *  - projection - {string} The desired CRS when no matrixSet is specified.\n *       eg: \"EPSG:3857\". If the desired projection is not available,\n *       an error is thrown.\n *  - requestEncoding - {string} url encoding format for the layer. Default is\n *       the first tile url format found in the GetCapabilities response.\n *  - style - {string} The name of the style\n *  - format - {string} Image format for the layer. Default is the first\n *       format returned in the GetCapabilities response.\n *  - crossOrigin - {string|null|undefined} Cross origin. Default is `undefined`.\n * @return {Options|null} WMTS source options object or `null` if the layer was not found.\n * @api\n */\nexport function optionsFromCapabilities(wmtsCap, config) {\n  const layers = wmtsCap['Contents']['Layer'];\n  const l = layers.find(function (elt) {\n    return elt['Identifier'] == config['layer'];\n  });\n  if (!l) {\n    return null;\n  }\n  const tileMatrixSets = wmtsCap['Contents']['TileMatrixSet'];\n  let idx;\n  if (l['TileMatrixSetLink'].length > 1) {\n    if ('projection' in config) {\n      idx = l['TileMatrixSetLink'].findIndex(function (elt) {\n        const tileMatrixSet = tileMatrixSets.find(function (el) {\n          return el['Identifier'] == elt['TileMatrixSet'];\n        });\n        const supportedCRS = tileMatrixSet['SupportedCRS'];\n        const proj1 = getProjection(supportedCRS);\n        const proj2 = getProjection(config['projection']);\n        if (proj1 && proj2) {\n          return equivalent(proj1, proj2);\n        }\n        return supportedCRS == config['projection'];\n      });\n    } else {\n      idx = l['TileMatrixSetLink'].findIndex(function (elt) {\n        return elt['TileMatrixSet'] == config['matrixSet'];\n      });\n    }\n  } else {\n    idx = 0;\n  }\n  if (idx < 0) {\n    idx = 0;\n  }\n  const matrixSet =\n    /** @type {string} */\n    (l['TileMatrixSetLink'][idx]['TileMatrixSet']);\n  const matrixLimits =\n    /** @type {Array<Object>} */\n    (l['TileMatrixSetLink'][idx]['TileMatrixSetLimits']);\n\n  let format = /** @type {string} */ (l['Format'][0]);\n  if ('format' in config) {\n    format = config['format'];\n  }\n  idx = l['Style'].findIndex(function (elt) {\n    if ('style' in config) {\n      return elt['Title'] == config['style'];\n    }\n    return elt['isDefault'];\n  });\n  if (idx < 0) {\n    idx = 0;\n  }\n  const style = /** @type {string} */ (l['Style'][idx]['Identifier']);\n\n  const dimensions = {};\n  if ('Dimension' in l) {\n    l['Dimension'].forEach(function (elt, index, array) {\n      const key = elt['Identifier'];\n      let value = elt['Default'];\n      if (value === undefined) {\n        value = elt['Value'][0];\n      }\n      dimensions[key] = value;\n    });\n  }\n\n  const matrixSets = wmtsCap['Contents']['TileMatrixSet'];\n  const matrixSetObj = matrixSets.find(function (elt) {\n    return elt['Identifier'] == matrixSet;\n  });\n\n  let projection;\n  const code = matrixSetObj['SupportedCRS'];\n  if (code) {\n    projection = getProjection(code);\n  }\n  if ('projection' in config) {\n    const projConfig = getProjection(config['projection']);\n    if (projConfig) {\n      if (!projection || equivalent(projConfig, projection)) {\n        projection = projConfig;\n      }\n    }\n  }\n\n  let wrapX = false;\n  const switchXY = projection.getAxisOrientation().substr(0, 2) == 'ne';\n\n  let matrix = matrixSetObj.TileMatrix[0];\n\n  // create default matrixLimit\n  let selectedMatrixLimit = {\n    MinTileCol: 0,\n    MinTileRow: 0,\n    // subtract one to end up at tile top left\n    MaxTileCol: matrix.MatrixWidth - 1,\n    MaxTileRow: matrix.MatrixHeight - 1,\n  };\n\n  //in case of matrix limits, use matrix limits to calculate extent\n  if (matrixLimits) {\n    selectedMatrixLimit = matrixLimits[matrixLimits.length - 1];\n    const m = matrixSetObj.TileMatrix.find(\n      (tileMatrixValue) =>\n        tileMatrixValue.Identifier === selectedMatrixLimit.TileMatrix ||\n        matrixSetObj.Identifier + ':' + tileMatrixValue.Identifier ===\n          selectedMatrixLimit.TileMatrix\n    );\n    if (m) {\n      matrix = m;\n    }\n  }\n\n  const resolution =\n    (matrix.ScaleDenominator * 0.00028) / projection.getMetersPerUnit(); // WMTS 1.0.0: standardized rendering pixel size\n  const origin = switchXY\n    ? [matrix.TopLeftCorner[1], matrix.TopLeftCorner[0]]\n    : matrix.TopLeftCorner;\n  const tileSpanX = matrix.TileWidth * resolution;\n  const tileSpanY = matrix.TileHeight * resolution;\n  let matrixSetExtent = matrixSetObj['BoundingBox'];\n  if (matrixSetExtent && switchXY) {\n    matrixSetExtent = [\n      matrixSetExtent[1],\n      matrixSetExtent[0],\n      matrixSetExtent[3],\n      matrixSetExtent[2],\n    ];\n  }\n  let extent = [\n    origin[0] + tileSpanX * selectedMatrixLimit.MinTileCol,\n    // add one to get proper bottom/right coordinate\n    origin[1] - tileSpanY * (1 + selectedMatrixLimit.MaxTileRow),\n    origin[0] + tileSpanX * (1 + selectedMatrixLimit.MaxTileCol),\n    origin[1] - tileSpanY * selectedMatrixLimit.MinTileRow,\n  ];\n\n  if (\n    matrixSetExtent !== undefined &&\n    !containsExtent(matrixSetExtent, extent)\n  ) {\n    const wgs84BoundingBox = l['WGS84BoundingBox'];\n    const wgs84ProjectionExtent = getProjection('EPSG:4326').getExtent();\n    extent = matrixSetExtent;\n    if (wgs84BoundingBox) {\n      wrapX =\n        wgs84BoundingBox[0] === wgs84ProjectionExtent[0] &&\n        wgs84BoundingBox[2] === wgs84ProjectionExtent[2];\n    } else {\n      const wgs84MatrixSetExtent = transformExtent(\n        matrixSetExtent,\n        matrixSetObj['SupportedCRS'],\n        'EPSG:4326'\n      );\n      // Ignore slight deviation from the correct x limits\n      wrapX =\n        wgs84MatrixSetExtent[0] - 1e-10 <= wgs84ProjectionExtent[0] &&\n        wgs84MatrixSetExtent[2] + 1e-10 >= wgs84ProjectionExtent[2];\n    }\n  }\n\n  const tileGrid = createFromCapabilitiesMatrixSet(\n    matrixSetObj,\n    extent,\n    matrixLimits\n  );\n\n  /** @type {!Array<string>} */\n  const urls = [];\n  let requestEncoding = config['requestEncoding'];\n  requestEncoding = requestEncoding !== undefined ? requestEncoding : '';\n\n  if (\n    'OperationsMetadata' in wmtsCap &&\n    'GetTile' in wmtsCap['OperationsMetadata']\n  ) {\n    const gets = wmtsCap['OperationsMetadata']['GetTile']['DCP']['HTTP']['Get'];\n\n    for (let i = 0, ii = gets.length; i < ii; ++i) {\n      if (gets[i]['Constraint']) {\n        const constraint = gets[i]['Constraint'].find(function (element) {\n          return element['name'] == 'GetEncoding';\n        });\n        const encodings = constraint['AllowedValues']['Value'];\n\n        if (requestEncoding === '') {\n          // requestEncoding not provided, use the first encoding from the list\n          requestEncoding = encodings[0];\n        }\n        if (requestEncoding === 'KVP') {\n          if (encodings.includes('KVP')) {\n            urls.push(/** @type {string} */ (gets[i]['href']));\n          }\n        } else {\n          break;\n        }\n      } else if (gets[i]['href']) {\n        requestEncoding = 'KVP';\n        urls.push(/** @type {string} */ (gets[i]['href']));\n      }\n    }\n  }\n  if (urls.length === 0) {\n    requestEncoding = 'REST';\n    l['ResourceURL'].forEach(function (element) {\n      if (element['resourceType'] === 'tile') {\n        format = element['format'];\n        urls.push(/** @type {string} */ (element['template']));\n      }\n    });\n  }\n\n  return {\n    urls: urls,\n    layer: config['layer'],\n    matrixSet: matrixSet,\n    format: format,\n    projection: projection,\n    requestEncoding: requestEncoding,\n    tileGrid: tileGrid,\n    style: style,\n    dimensions: dimensions,\n    wrapX: wrapX,\n    crossOrigin: config['crossOrigin'],\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAqEA,IAAM,OAAN,cAAmB,kBAAU;AAAA;AAAA;AAAA;AAAA,EAI3B,YAAY,SAAS;AAGnB,UAAM,kBACJ,QAAQ,oBAAoB,SAAY,QAAQ,kBAAkB;AAIpE,UAAM,WAAW,QAAQ;AAEzB,QAAI,OAAO,QAAQ;AACnB,QAAI,SAAS,UAAa,QAAQ,QAAQ,QAAW;AACnD,aAAO,UAAU,QAAQ,GAAG;AAAA,IAC9B;AAEA,UAAM;AAAA,MACJ,cAAc,QAAQ;AAAA,MACtB,yBAAyB,QAAQ;AAAA,MACjC,WAAW,QAAQ;AAAA,MACnB,aAAa,QAAQ;AAAA,MACrB,aAAa,QAAQ;AAAA,MACrB,YAAY,QAAQ;AAAA,MACpB,4BAA4B,QAAQ;AAAA,MACpC,WAAW,QAAQ;AAAA,MACnB;AAAA,MACA,kBAAkB,QAAQ;AAAA,MAC1B,gBAAgB,QAAQ;AAAA,MACxB;AAAA,MACA,OAAO,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAAA,MACrD,YAAY,QAAQ;AAAA,MACpB,YAAY,QAAQ;AAAA,IACtB,CAAC;AAMD,SAAK,WAAW,QAAQ,YAAY,SAAY,QAAQ,UAAU;AAMlE,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAM/D,SAAK,cACH,QAAQ,eAAe,SAAY,QAAQ,aAAa,CAAC;AAM3D,SAAK,SAAS,QAAQ;AAMtB,SAAK,aAAa,QAAQ;AAM1B,SAAK,SAAS,QAAQ;AAStB,SAAK,mBAAmB;AAExB,SAAK,OAAO,KAAK,qBAAqB,CAAC;AAEvC,QAAI,QAAQ,KAAK,SAAS,GAAG;AAC3B,WAAK,kBAAkB;AAAA,QACrB,KAAK,IAAI,KAAK,uBAAuB,KAAK,IAAI,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,MAAM;AACZ,SAAK,OAAO;AACZ,UAAM,MAAM,KAAK,KAAK,IAAI;AAC1B,SAAK;AAAA,MACH;AAAA,QACE,KAAK,IAAI,KAAK,uBAAuB,KAAK,IAAI,CAAC;AAAA,MACjD;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB;AACrB,UAAM,MAAM,KAAK,OAAO,KAAK,KAAK,MAAM,CAAC,IAAI,CAAC;AAC9C,eAAW,OAAO,KAAK,aAAa;AAClC,UAAI,KAAK,MAAM,MAAM,KAAK,YAAY,GAAG,CAAC;AAAA,IAC5C;AACA,WAAO,IAAI,KAAK,GAAG;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,YAAY;AAC3B,WAAO,OAAO,KAAK,aAAa,UAAU;AAC1C,SAAK,OAAO,KAAK,qBAAqB,CAAC;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,UAAU;AAC/B,UAAM,kBAAkB,KAAK;AAI7B,UAAM,UAAU;AAAA,MACd,SAAS,KAAK;AAAA,MACd,SAAS,KAAK;AAAA,MACd,iBAAiB,KAAK;AAAA,IACxB;AAEA,QAAI,mBAAmB,OAAO;AAC5B,aAAO,OAAO,SAAS;AAAA,QACrB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW,KAAK;AAAA,QAChB,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AAMA,eACE,mBAAmB,QACf,aAAa,UAAU,OAAO,IAC9B,SAAS,QAAQ,eAAe,SAAU,GAAG,GAAG;AAC9C,aAAO,EAAE,YAAY,KAAK,UAAU,QAAQ,EAAE,YAAY,CAAC,IAAI;AAAA,IACjE,CAAC;AAEP,UAAM;AAAA;AAAA,MACJ,KAAK;AAAA;AAEP,UAAM,aAAa,KAAK;AAExB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOE,SAAU,WAAW,YAAY,YAAY;AAC3C,YAAI,CAAC,WAAW;AACd,iBAAO;AAAA,QACT;AACA,cAAM,eAAe;AAAA,UACnB,cAAc,SAAS,YAAY,UAAU,CAAC,CAAC;AAAA,UAC/C,WAAW,UAAU,CAAC;AAAA,UACtB,WAAW,UAAU,CAAC;AAAA,QACxB;AACA,eAAO,OAAO,cAAc,UAAU;AACtC,YAAI,MAAM;AACV,YAAI,mBAAmB,OAAO;AAC5B,gBAAM,aAAa,KAAK,YAAY;AAAA,QACtC,OAAO;AACL,gBAAM,IAAI,QAAQ,eAAe,SAAU,GAAG,GAAG;AAC/C,mBAAO,aAAa,CAAC;AAAA,UACvB,CAAC;AAAA,QACH;AACA,eAAO;AAAA,MACT;AAAA;AAAA,EAEJ;AACF;AAEA,IAAO,eAAQ;AA0BR,SAAS,wBAAwB,SAAS,QAAQ;AACvD,QAAM,SAAS,QAAQ,UAAU,EAAE,OAAO;AAC1C,QAAM,IAAI,OAAO,KAAK,SAAU,KAAK;AACnC,WAAO,IAAI,YAAY,KAAK,OAAO,OAAO;AAAA,EAC5C,CAAC;AACD,MAAI,CAAC,GAAG;AACN,WAAO;AAAA,EACT;AACA,QAAM,iBAAiB,QAAQ,UAAU,EAAE,eAAe;AAC1D,MAAI;AACJ,MAAI,EAAE,mBAAmB,EAAE,SAAS,GAAG;AACrC,QAAI,gBAAgB,QAAQ;AAC1B,YAAM,EAAE,mBAAmB,EAAE,UAAU,SAAU,KAAK;AACpD,cAAM,gBAAgB,eAAe,KAAK,SAAU,IAAI;AACtD,iBAAO,GAAG,YAAY,KAAK,IAAI,eAAe;AAAA,QAChD,CAAC;AACD,cAAM,eAAe,cAAc,cAAc;AACjD,cAAM,QAAQ,IAAc,YAAY;AACxC,cAAM,QAAQ,IAAc,OAAO,YAAY,CAAC;AAChD,YAAI,SAAS,OAAO;AAClB,iBAAO,WAAW,OAAO,KAAK;AAAA,QAChC;AACA,eAAO,gBAAgB,OAAO,YAAY;AAAA,MAC5C,CAAC;AAAA,IACH,OAAO;AACL,YAAM,EAAE,mBAAmB,EAAE,UAAU,SAAU,KAAK;AACpD,eAAO,IAAI,eAAe,KAAK,OAAO,WAAW;AAAA,MACnD,CAAC;AAAA,IACH;AAAA,EACF,OAAO;AACL,UAAM;AAAA,EACR;AACA,MAAI,MAAM,GAAG;AACX,UAAM;AAAA,EACR;AACA,QAAM;AAAA;AAAA,IAEH,EAAE,mBAAmB,EAAE,GAAG,EAAE,eAAe;AAAA;AAC9C,QAAM;AAAA;AAAA,IAEH,EAAE,mBAAmB,EAAE,GAAG,EAAE,qBAAqB;AAAA;AAEpD,MAAI;AAAA;AAAA,IAAgC,EAAE,QAAQ,EAAE,CAAC;AAAA;AACjD,MAAI,YAAY,QAAQ;AACtB,aAAS,OAAO,QAAQ;AAAA,EAC1B;AACA,QAAM,EAAE,OAAO,EAAE,UAAU,SAAU,KAAK;AACxC,QAAI,WAAW,QAAQ;AACrB,aAAO,IAAI,OAAO,KAAK,OAAO,OAAO;AAAA,IACvC;AACA,WAAO,IAAI,WAAW;AAAA,EACxB,CAAC;AACD,MAAI,MAAM,GAAG;AACX,UAAM;AAAA,EACR;AACA,QAAM;AAAA;AAAA,IAA+B,EAAE,OAAO,EAAE,GAAG,EAAE,YAAY;AAAA;AAEjE,QAAM,aAAa,CAAC;AACpB,MAAI,eAAe,GAAG;AACpB,MAAE,WAAW,EAAE,QAAQ,SAAU,KAAK,OAAO,OAAO;AAClD,YAAM,MAAM,IAAI,YAAY;AAC5B,UAAI,QAAQ,IAAI,SAAS;AACzB,UAAI,UAAU,QAAW;AACvB,gBAAQ,IAAI,OAAO,EAAE,CAAC;AAAA,MACxB;AACA,iBAAW,GAAG,IAAI;AAAA,IACpB,CAAC;AAAA,EACH;AAEA,QAAM,aAAa,QAAQ,UAAU,EAAE,eAAe;AACtD,QAAM,eAAe,WAAW,KAAK,SAAU,KAAK;AAClD,WAAO,IAAI,YAAY,KAAK;AAAA,EAC9B,CAAC;AAED,MAAI;AACJ,QAAM,OAAO,aAAa,cAAc;AACxC,MAAI,MAAM;AACR,iBAAa,IAAc,IAAI;AAAA,EACjC;AACA,MAAI,gBAAgB,QAAQ;AAC1B,UAAM,aAAa,IAAc,OAAO,YAAY,CAAC;AACrD,QAAI,YAAY;AACd,UAAI,CAAC,cAAc,WAAW,YAAY,UAAU,GAAG;AACrD,qBAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAEA,MAAI,QAAQ;AACZ,QAAM,WAAW,WAAW,mBAAmB,EAAE,OAAO,GAAG,CAAC,KAAK;AAEjE,MAAI,SAAS,aAAa,WAAW,CAAC;AAGtC,MAAI,sBAAsB;AAAA,IACxB,YAAY;AAAA,IACZ,YAAY;AAAA;AAAA,IAEZ,YAAY,OAAO,cAAc;AAAA,IACjC,YAAY,OAAO,eAAe;AAAA,EACpC;AAGA,MAAI,cAAc;AAChB,0BAAsB,aAAa,aAAa,SAAS,CAAC;AAC1D,UAAM,IAAI,aAAa,WAAW;AAAA,MAChC,CAAC,oBACC,gBAAgB,eAAe,oBAAoB,cACnD,aAAa,aAAa,MAAM,gBAAgB,eAC9C,oBAAoB;AAAA,IAC1B;AACA,QAAI,GAAG;AACL,eAAS;AAAA,IACX;AAAA,EACF;AAEA,QAAM,aACH,OAAO,mBAAmB,QAAW,WAAW,iBAAiB;AACpE,QAAM,SAAS,WACX,CAAC,OAAO,cAAc,CAAC,GAAG,OAAO,cAAc,CAAC,CAAC,IACjD,OAAO;AACX,QAAM,YAAY,OAAO,YAAY;AACrC,QAAM,YAAY,OAAO,aAAa;AACtC,MAAI,kBAAkB,aAAa,aAAa;AAChD,MAAI,mBAAmB,UAAU;AAC/B,sBAAkB;AAAA,MAChB,gBAAgB,CAAC;AAAA,MACjB,gBAAgB,CAAC;AAAA,MACjB,gBAAgB,CAAC;AAAA,MACjB,gBAAgB,CAAC;AAAA,IACnB;AAAA,EACF;AACA,MAAI,SAAS;AAAA,IACX,OAAO,CAAC,IAAI,YAAY,oBAAoB;AAAA;AAAA,IAE5C,OAAO,CAAC,IAAI,aAAa,IAAI,oBAAoB;AAAA,IACjD,OAAO,CAAC,IAAI,aAAa,IAAI,oBAAoB;AAAA,IACjD,OAAO,CAAC,IAAI,YAAY,oBAAoB;AAAA,EAC9C;AAEA,MACE,oBAAoB,UACpB,CAAC,eAAe,iBAAiB,MAAM,GACvC;AACA,UAAM,mBAAmB,EAAE,kBAAkB;AAC7C,UAAM,wBAAwB,IAAc,WAAW,EAAE,UAAU;AACnE,aAAS;AACT,QAAI,kBAAkB;AACpB,cACE,iBAAiB,CAAC,MAAM,sBAAsB,CAAC,KAC/C,iBAAiB,CAAC,MAAM,sBAAsB,CAAC;AAAA,IACnD,OAAO;AACL,YAAM,uBAAuB;AAAA,QAC3B;AAAA,QACA,aAAa,cAAc;AAAA,QAC3B;AAAA,MACF;AAEA,cACE,qBAAqB,CAAC,IAAI,SAAS,sBAAsB,CAAC,KAC1D,qBAAqB,CAAC,IAAI,SAAS,sBAAsB,CAAC;AAAA,IAC9D;AAAA,EACF;AAEA,QAAM,WAAW;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAGA,QAAM,OAAO,CAAC;AACd,MAAI,kBAAkB,OAAO,iBAAiB;AAC9C,oBAAkB,oBAAoB,SAAY,kBAAkB;AAEpE,MACE,wBAAwB,WACxB,aAAa,QAAQ,oBAAoB,GACzC;AACA,UAAM,OAAO,QAAQ,oBAAoB,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;AAE1E,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAI,KAAK,CAAC,EAAE,YAAY,GAAG;AACzB,cAAM,aAAa,KAAK,CAAC,EAAE,YAAY,EAAE,KAAK,SAAU,SAAS;AAC/D,iBAAO,QAAQ,MAAM,KAAK;AAAA,QAC5B,CAAC;AACD,cAAM,YAAY,WAAW,eAAe,EAAE,OAAO;AAErD,YAAI,oBAAoB,IAAI;AAE1B,4BAAkB,UAAU,CAAC;AAAA,QAC/B;AACA,YAAI,oBAAoB,OAAO;AAC7B,cAAI,UAAU,SAAS,KAAK,GAAG;AAC7B,iBAAK;AAAA;AAAA,cAA4B,KAAK,CAAC,EAAE,MAAM;AAAA,YAAE;AAAA,UACnD;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,WAAW,KAAK,CAAC,EAAE,MAAM,GAAG;AAC1B,0BAAkB;AAClB,aAAK;AAAA;AAAA,UAA4B,KAAK,CAAC,EAAE,MAAM;AAAA,QAAE;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK,WAAW,GAAG;AACrB,sBAAkB;AAClB,MAAE,aAAa,EAAE,QAAQ,SAAU,SAAS;AAC1C,UAAI,QAAQ,cAAc,MAAM,QAAQ;AACtC,iBAAS,QAAQ,QAAQ;AACzB,aAAK;AAAA;AAAA,UAA4B,QAAQ,UAAU;AAAA,QAAE;AAAA,MACvD;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO;AAAA,IACL;AAAA,IACA,OAAO,OAAO,OAAO;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa,OAAO,aAAa;AAAA,EACnC;AACF;", "names": []}