const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.CbRRqz1H.js","assets/index.C0-0gsfl.js","assets/vue.CnN__PXn.js","assets/index.CB8Sa4Ps.css","assets/index.5b3OXrMh.css","assets/tagsView.DYUffJi3.js","assets/sortable.esm.BGML4dzN.js","assets/tagsView.77LB7UQf.css"])))=>i.map(i=>d[i]);
import{u as y,a as _,q as d,__tla as f}from"./index.C0-0gsfl.js";import{d as r,s as h,c as E,a as w,b as l,t as D,e as I,D as P,u as n,j as o}from"./vue.CnN__PXn.js";let u,T=Promise.all([(()=>{try{return f}catch{}})()]).then(async()=>{let t,s;t={class:"layout-navbars-container"},s=r({name:"layoutNavBars"}),u=d(r({...s,setup(b){const c=o(()=>_(()=>import("./index.CbRRqz1H.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([0,1,2,3,4]))),i=o(()=>_(()=>import("./tagsView.DYUffJi3.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([5,1,2,3,6,7]))),p=y(),{themeConfig:m}=h(p),v=E(()=>{let{layout:a,isTagsview:e}=m.value;return a!=="classic"&&e});return(a,e)=>(l(),w("div",t,[D(n(c)),v.value?(l(),I(n(i),{key:0})):P("",!0)]))}}),[["__scopeId","data-v-1613831c"]])});export{T as __tla,u as default};
