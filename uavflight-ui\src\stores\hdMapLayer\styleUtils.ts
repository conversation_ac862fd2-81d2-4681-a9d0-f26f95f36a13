/*
 * @Author: AI Assistant
 * @Date: 2025-07-15
 * @Description: 快拼图层样式工具类
 *
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved.
 */

import { Style, Stroke, Fill, Text, Circle } from 'ol/style';
import { LabelStyle } from './types';

/**
 * 将十六进制颜色转换为RGBA
 * @param hex 十六进制颜色
 * @param alpha 透明度
 * @returns RGBA颜色字符串
 */
function hexToRgba(hex: string, alpha: number = 1): string {
  // 移除可能的#前缀
  hex = hex.replace('#', '');
  
  // 处理缩写形式 (#rgb)
  if (hex.length === 3) {
    hex = hex.split('').map(char => char + char).join('');
  }
  
  // 解析RGB值
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  
  // 如果hex包含alpha通道 (#rrggbbaa)
  let a = alpha;
  if (hex.length === 8) {
    a = parseInt(hex.substring(6, 8), 16) / 255;
  }
  
  return `rgba(${r}, ${g}, ${b}, ${a})`;
}

/**
 * 解析颜色值，支持十六进制和RGB格式
 * @param color 颜色值
 * @param defaultOpacity 默认透明度
 * @returns RGBA颜色
 */
export function parseColor(color: string, defaultOpacity: number = 1): string {
  if (!color) return `rgba(128, 128, 128, ${defaultOpacity})`;

  // 处理rgba格式
  if (color.startsWith('rgba')) {
    return color;
  }

  // 处理rgb格式
  if (color.startsWith('rgb(')) {
    const rgb = color.match(/\d+/g);
    if (rgb && rgb.length >= 3) {
      return `rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, ${defaultOpacity})`;
    }
    return `rgba(128, 128, 128, ${defaultOpacity})`;
  }

  // 处理十六进制格式
  if (color.startsWith('#')) {
    try {
      return hexToRgba(color, defaultOpacity);
    } catch (error) {
      console.error('解析十六进制颜色失败:', color, error);
      return `rgba(128, 128, 128, ${defaultOpacity})`;
    }
  }

  return `rgba(128, 128, 128, ${defaultOpacity})`;
}

/**
 * 从样式对象创建OL样式
 * @param styleObj 样式对象
 * @returns OL样式
 */
export function createStyleFromObject(styleObj: any): Style {
  if (!styleObj) return new Style();

  const {
    color = '#888888',
    weight = 1,
    opacity = 1,
    fillColor = '#88888888',
    fillOpacity = 0.5,
    radius = 5
  } = styleObj;

  // 创建描边样式
  const stroke = new Stroke({
    color: parseColor(color, opacity),
    width: weight
  });

  // 创建填充样式
  const fill = new Fill({
    color: parseColor(fillColor, fillOpacity !== undefined ? fillOpacity : opacity)
  });

  // 创建点样式（如果需要）
  const image = new Circle({
    radius,
    fill,
    stroke
  });

  return new Style({
    stroke,
    fill,
    image
  });
}

/**
 * 创建带标签的样式
 * @param baseStyle 基础样式
 * @param labelText 标签文本
 * @param labelStyle 标签样式
 * @returns 带标签的OL样式
 */
export function createStyleWithLabel(baseStyle: any, labelText: string, labelStyle?: LabelStyle): Style {
  const style = createStyleFromObject(baseStyle);

  if (!labelText) return style;

  const {
    color = '#000000',
    fontSize = 12,
    fontWeight = 'normal',
    offsetX = 0,
    offsetY = -15,
    stroke = true,
    strokeColor = '#FFFFFF',
    strokeWidth = 3
  } = labelStyle || {};

  // 创建文本样式
  const text = new Text({
    text: labelText,
    font: `${fontWeight} ${fontSize}px Arial, sans-serif`,
    fill: new Fill({
      color: color
    }),
    offsetX,
    offsetY,
    padding: [2, 5, 2, 5]
  });

  // 添加描边
  if (stroke) {
    text.setStroke(
      new Stroke({
        color: strokeColor,
        width: strokeWidth
      })
    );
  }

  style.setText(text);
  return style;
}

/**
 * 创建高亮样式
 * @param baseStyle 基础样式
 * @returns 高亮样式
 */
export function createHighlightStyle(baseStyle: any): Style {
  const highlightStyle = { ...baseStyle };
  
  // 增加线宽
  if (highlightStyle.weight) {
    highlightStyle.weight += 2;
  } else {
    highlightStyle.weight = 3;
  }
  
  // 提高不透明度
  highlightStyle.opacity = 1;
  highlightStyle.fillOpacity = 0.7;
  
  return createStyleFromObject(highlightStyle);
}

/**
 * 创建选中样式
 * @param baseStyle 基础样式
 * @returns 选中样式
 */
export function createSelectedStyle(baseStyle: any): Style {
  const selectedStyle = { ...baseStyle };
  
  // 修改颜色为明亮的蓝色
  selectedStyle.color = '#2196F3';
  selectedStyle.weight = 3;
  selectedStyle.opacity = 1;
  
  // 半透明填充
  selectedStyle.fillColor = '#2196F3';
  selectedStyle.fillOpacity = 0.4;
  
  return createStyleFromObject(selectedStyle);
}

/**
 * 创建点样式
 * @param options 选项
 * @returns 点样式
 */
export function createPointStyle(options: any): Style {
  const {
    radius = 6,
    fillColor = '#3388ff',
    strokeColor = '#ffffff',
    strokeWidth = 2
  } = options || {};
  
  return new Style({
    image: new Circle({
      radius,
      fill: new Fill({
        color: fillColor
      }),
      stroke: new Stroke({
        color: strokeColor,
        width: strokeWidth
      })
    })
  });
}

/**
 * 创建点聚合样式
 * @param size 聚合大小
 * @returns 聚合样式
 */
export function createClusterStyle(size: number): Style {
  const radius = Math.min(Math.max(8, size * 0.75), 20);
  const fontSize = Math.min(Math.max(10, size * 0.5), 16);
  
  return new Style({
    image: new Circle({
      radius,
      fill: new Fill({
        color: 'rgba(24, 144, 255, 0.6)'
      }),
      stroke: new Stroke({
        color: '#1890FF',
        width: 2
      })
    }),
    text: new Text({
      text: size.toString(),
      font: `${fontSize}px Arial, sans-serif`,
      fill: new Fill({
        color: '#FFFFFF'
      })
    })
  });
} 