import{d as h,c as L,B as e,a as l,b as a,p as g,F as u,e as d,v as m,t,f as k,G as r,u as c,Q as C,E as S}from"./vue.CnN__PXn.js";import{o as s,__tla as w}from"./index.C0-0gsfl.js";let f,A=Promise.all([(()=>{try{return w}catch{}})()]).then(async()=>{let o,p;o=["onClick"],p=h({name:"navMenuSubItem"}),f=h({...p,props:{chil:{type:Array,default:()=>[]}},setup(y){const _=y,v=L(()=>_.chil);return(B,E)=>{const i=e("SvgIcon"),x=e("sub-item",!0),I=e("el-sub-menu"),b=e("el-menu-item");return a(!0),l(u,null,g(v.value,n=>(a(),l(u,null,[n.children&&n.children.length>0?(a(),d(I,{index:n.path,key:n.path},{title:m(()=>[t(i,{name:n.meta.icon},null,8,["name"]),k("span",null,r(c(s).setMenuI18n(n)),1)]),default:m(()=>[t(x,{chil:n.children},null,8,["chil"])]),_:2},1032,["index"])):(a(),d(b,{index:n.path,key:n.path},{default:m(()=>[!n.meta.isLink||n.meta.isLink&&n.meta.isIframe?(a(),l(u,{key:0},[t(i,{name:n.meta.icon},null,8,["name"]),k("span",null,r(c(s).setMenuI18n(n)),1)],64)):(a(),l("a",{key:1,class:"w100",onClick:C(F=>(M=>{s.handleOpenLink(M)})(n),["prevent"])},[t(i,{name:n.meta.icon},null,8,["name"]),S(" "+r(c(s).setMenuI18n(n)),1)],8,o))]),_:2},1032,["index"]))],64))),256)}}})});export{A as __tla,f as default};
