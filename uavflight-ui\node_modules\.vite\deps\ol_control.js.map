{"version": 3, "sources": ["../../ol/control/FullScreen.js", "../../ol/control/MousePosition.js", "../../ol/control/OverviewMap.js", "../../ol/control/ScaleLine.js", "../../ol/control/ZoomSlider.js", "../../ol/control/ZoomToExtent.js"], "sourcesContent": ["/**\n * @module ol/control/FullScreen\n */\nimport Control from './Control.js';\nimport EventType from '../events/EventType.js';\nimport MapProperty from '../MapProperty.js';\nimport {CLASS_CONTROL, CLASS_UNSELECTABLE, CLASS_UNSUPPORTED} from '../css.js';\nimport {listen, unlistenBy<PERSON>ey} from '../events.js';\nimport {replaceNode} from '../dom.js';\n\nconst events = [\n  'fullscreenchange',\n  'webkitfullscreenchange',\n  'MSFullscreenChange',\n];\n\n/**\n * @enum {string}\n */\nconst FullScreenEventType = {\n  /**\n   * Triggered after the map entered fullscreen.\n   * @event FullScreenEventType#enterfullscreen\n   * @api\n   */\n  ENTERFULLSCREEN: 'enterfullscreen',\n\n  /**\n   * Triggered after the map leave fullscreen.\n   * @event FullScreenEventType#leavefullscreen\n   * @api\n   */\n  LEAVEFULLSCREEN: 'leavefullscreen',\n};\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes|\n *     'enterfullscreen'|'leavefullscreen', import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types, import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|\n *     'enterfullscreen'|'leavefullscreen'|import(\"../ObjectEventType\").Types, Return>} FullScreenOnSignature\n */\n\n/**\n * @typedef {Object} Options\n * @property {string} [className='ol-full-screen'] CSS class name.\n * @property {string|Text|HTMLElement} [label='\\u2922'] Text label to use for the button.\n * Instead of text, also an element (e.g. a `span` element) can be used.\n * @property {string|Text|HTMLElement} [labelActive='\\u00d7'] Text label to use for the\n * button when full-screen is active.\n * Instead of text, also an element (e.g. a `span` element) can be used.\n * @property {string} [activeClassName=className + '-true'] CSS class name for the button\n * when full-screen is active.\n * @property {string} [inactiveClassName=className + '-false'] CSS class name for the button\n * when full-screen is inactive.\n * @property {string} [tipLabel='Toggle full-screen'] Text label to use for the button tip.\n * @property {boolean} [keys=false] Full keyboard access.\n * @property {HTMLElement|string} [target] Specify a target if you want the\n * control to be rendered outside of the map's viewport.\n * @property {HTMLElement|string} [source] The element to be displayed\n * fullscreen. When not provided, the element containing the map viewport will\n * be displayed fullscreen.\n */\n\n/**\n * @classdesc\n * Provides a button that when clicked fills up the full screen with the map.\n * The full screen source element is by default the element containing the map viewport unless\n * overridden by providing the `source` option. In which case, the dom\n * element introduced using this parameter will be displayed in full screen.\n *\n * When in full screen mode, a close button is shown to exit full screen mode.\n * The [Fullscreen API](https://www.w3.org/TR/fullscreen/) is used to\n * toggle the map in full screen mode.\n *\n * @fires FullScreenEventType#enterfullscreen\n * @fires FullScreenEventType#leavefullscreen\n * @api\n */\nclass FullScreen extends Control {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    super({\n      element: document.createElement('div'),\n      target: options.target,\n    });\n\n    /***\n     * @type {FullScreenOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {FullScreenOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {FullScreenOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.keys_ = options.keys !== undefined ? options.keys : false;\n\n    /**\n     * @private\n     * @type {HTMLElement|string|undefined}\n     */\n    this.source_ = options.source;\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.isInFullscreen_ = false;\n\n    /**\n     * @private\n     */\n    this.boundHandleMapTargetChange_ = this.handleMapTargetChange_.bind(this);\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.cssClassName_ =\n      options.className !== undefined ? options.className : 'ol-full-screen';\n\n    /**\n     * @private\n     * @type {Array<import(\"../events.js\").EventsKey>}\n     */\n    this.documentListeners_ = [];\n\n    /**\n     * @private\n     * @type {Array<string>}\n     */\n    this.activeClassName_ =\n      options.activeClassName !== undefined\n        ? options.activeClassName.split(' ')\n        : [this.cssClassName_ + '-true'];\n\n    /**\n     * @private\n     * @type {Array<string>}\n     */\n    this.inactiveClassName_ =\n      options.inactiveClassName !== undefined\n        ? options.inactiveClassName.split(' ')\n        : [this.cssClassName_ + '-false'];\n\n    const label = options.label !== undefined ? options.label : '\\u2922';\n\n    /**\n     * @private\n     * @type {Text|HTMLElement}\n     */\n    this.labelNode_ =\n      typeof label === 'string' ? document.createTextNode(label) : label;\n\n    const labelActive =\n      options.labelActive !== undefined ? options.labelActive : '\\u00d7';\n\n    /**\n     * @private\n     * @type {Text|HTMLElement}\n     */\n    this.labelActiveNode_ =\n      typeof labelActive === 'string'\n        ? document.createTextNode(labelActive)\n        : labelActive;\n\n    const tipLabel = options.tipLabel ? options.tipLabel : 'Toggle full-screen';\n\n    /**\n     * @private\n     * @type {HTMLElement}\n     */\n    this.button_ = document.createElement('button');\n    this.button_.title = tipLabel;\n    this.button_.setAttribute('type', 'button');\n    this.button_.appendChild(this.labelNode_);\n    this.button_.addEventListener(\n      EventType.CLICK,\n      this.handleClick_.bind(this),\n      false\n    );\n    this.setClassName_(this.button_, this.isInFullscreen_);\n\n    this.element.className = `${this.cssClassName_} ${CLASS_UNSELECTABLE} ${CLASS_CONTROL}`;\n    this.element.appendChild(this.button_);\n  }\n\n  /**\n   * @param {MouseEvent} event The event to handle\n   * @private\n   */\n  handleClick_(event) {\n    event.preventDefault();\n    this.handleFullScreen_();\n  }\n\n  /**\n   * @private\n   */\n  handleFullScreen_() {\n    const map = this.getMap();\n    if (!map) {\n      return;\n    }\n    const doc = map.getOwnerDocument();\n    if (!isFullScreenSupported(doc)) {\n      return;\n    }\n    if (isFullScreen(doc)) {\n      exitFullScreen(doc);\n    } else {\n      let element;\n      if (this.source_) {\n        element =\n          typeof this.source_ === 'string'\n            ? doc.getElementById(this.source_)\n            : this.source_;\n      } else {\n        element = map.getTargetElement();\n      }\n      if (this.keys_) {\n        requestFullScreenWithKeys(element);\n      } else {\n        requestFullScreen(element);\n      }\n    }\n  }\n\n  /**\n   * @private\n   */\n  handleFullScreenChange_() {\n    const map = this.getMap();\n    if (!map) {\n      return;\n    }\n    const wasInFullscreen = this.isInFullscreen_;\n    this.isInFullscreen_ = isFullScreen(map.getOwnerDocument());\n    if (wasInFullscreen !== this.isInFullscreen_) {\n      this.setClassName_(this.button_, this.isInFullscreen_);\n      if (this.isInFullscreen_) {\n        replaceNode(this.labelActiveNode_, this.labelNode_);\n        this.dispatchEvent(FullScreenEventType.ENTERFULLSCREEN);\n      } else {\n        replaceNode(this.labelNode_, this.labelActiveNode_);\n        this.dispatchEvent(FullScreenEventType.LEAVEFULLSCREEN);\n      }\n      map.updateSize();\n    }\n  }\n\n  /**\n   * @param {HTMLElement} element Target element\n   * @param {boolean} fullscreen True if fullscreen class name should be active\n   * @private\n   */\n  setClassName_(element, fullscreen) {\n    if (fullscreen) {\n      element.classList.remove(...this.inactiveClassName_);\n      element.classList.add(...this.activeClassName_);\n    } else {\n      element.classList.remove(...this.activeClassName_);\n      element.classList.add(...this.inactiveClassName_);\n    }\n  }\n\n  /**\n   * Remove the control from its current map and attach it to the new map.\n   * Pass `null` to just remove the control from the current map.\n   * Subclasses may set up event handlers to get notified about changes to\n   * the map here.\n   * @param {import(\"../Map.js\").default|null} map Map.\n   * @api\n   */\n  setMap(map) {\n    const oldMap = this.getMap();\n    if (oldMap) {\n      oldMap.removeChangeListener(\n        MapProperty.TARGET,\n        this.boundHandleMapTargetChange_\n      );\n    }\n\n    super.setMap(map);\n\n    this.handleMapTargetChange_();\n    if (map) {\n      map.addChangeListener(\n        MapProperty.TARGET,\n        this.boundHandleMapTargetChange_\n      );\n    }\n  }\n\n  /**\n   * @private\n   */\n  handleMapTargetChange_() {\n    const listeners = this.documentListeners_;\n    for (let i = 0, ii = listeners.length; i < ii; ++i) {\n      unlistenByKey(listeners[i]);\n    }\n    listeners.length = 0;\n\n    const map = this.getMap();\n    if (map) {\n      const doc = map.getOwnerDocument();\n      if (isFullScreenSupported(doc)) {\n        this.element.classList.remove(CLASS_UNSUPPORTED);\n      } else {\n        this.element.classList.add(CLASS_UNSUPPORTED);\n      }\n\n      for (let i = 0, ii = events.length; i < ii; ++i) {\n        listeners.push(\n          listen(doc, events[i], this.handleFullScreenChange_, this)\n        );\n      }\n      this.handleFullScreenChange_();\n    }\n  }\n}\n\n/**\n * @param {Document} doc The root document to check.\n * @return {boolean} Fullscreen is supported by the current platform.\n */\nfunction isFullScreenSupported(doc) {\n  const body = doc.body;\n  return !!(\n    body['webkitRequestFullscreen'] ||\n    (body.requestFullscreen && doc.fullscreenEnabled)\n  );\n}\n\n/**\n * @param {Document} doc The root document to check.\n * @return {boolean} Element is currently in fullscreen.\n */\nfunction isFullScreen(doc) {\n  return !!(doc['webkitIsFullScreen'] || doc.fullscreenElement);\n}\n\n/**\n * Request to fullscreen an element.\n * @param {HTMLElement} element Element to request fullscreen\n */\nfunction requestFullScreen(element) {\n  if (element.requestFullscreen) {\n    element.requestFullscreen();\n  } else if (element['webkitRequestFullscreen']) {\n    element['webkitRequestFullscreen']();\n  }\n}\n\n/**\n * Request to fullscreen an element with keyboard input.\n * @param {HTMLElement} element Element to request fullscreen\n */\nfunction requestFullScreenWithKeys(element) {\n  if (element['webkitRequestFullscreen']) {\n    element['webkitRequestFullscreen']();\n  } else {\n    requestFullScreen(element);\n  }\n}\n\n/**\n * Exit fullscreen.\n * @param {Document} doc The document to exit fullscren from\n */\nfunction exitFullScreen(doc) {\n  if (doc.exitFullscreen) {\n    doc.exitFullscreen();\n  } else if (doc['webkitExitFullscreen']) {\n    doc['webkitExitFullscreen']();\n  }\n}\n\nexport default FullScreen;\n", "/**\n * @module ol/control/MousePosition\n */\n\nimport Control from './Control.js';\nimport EventType from '../pointer/EventType.js';\nimport {\n  get as getProjection,\n  getTransformFromProjections,\n  getUserProjection,\n  identityTransform,\n} from '../proj.js';\nimport {listen} from '../events.js';\nimport {wrapX} from '../coordinate.js';\n\n/**\n * @type {string}\n */\nconst PROJECTION = 'projection';\n\n/**\n * @type {string}\n */\nconst COORDINATE_FORMAT = 'coordinateFormat';\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types|\n *     'change:coordinateFormat'|'change:projection', import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types|\n *     'change:coordinateFormat'|'change:projection', Return>} MousePositionOnSignature\n */\n\n/**\n * @typedef {Object} Options\n * @property {string} [className='ol-mouse-position'] CSS class name.\n * @property {import(\"../coordinate.js\").CoordinateFormat} [coordinateFormat] Coordinate format.\n * @property {import(\"../proj.js\").ProjectionLike} [projection] Projection. Default is the view projection.\n * @property {function(import(\"../MapEvent.js\").default):void} [render] Function called when the\n * control should be re-rendered. This is called in a `requestAnimationFrame`\n * callback.\n * @property {HTMLElement|string} [target] Specify a target if you want the\n * control to be rendered outside of the map's viewport.\n * @property {string} [placeholder] Markup to show when the mouse position is not\n * available (e.g. when the pointer leaves the map viewport).  By default, a non-breaking space is rendered\n * initially and the last position is retained when the mouse leaves the viewport.\n * When a string is provided (e.g. `'no position'` or `''` for an empty string) it is used as a\n * placeholder.\n * @property {boolean} [wrapX=true] Wrap the world horizontally on the projection's antimeridian, if it\n * is a global projection.\n */\n\n/**\n * @classdesc\n * A control to show the 2D coordinates of the mouse cursor. By default, these\n * are in the view projection, but can be in any supported projection.\n * By default the control is shown in the top right corner of the map, but this\n * can be changed by using the css selector `.ol-mouse-position`.\n *\n * On touch devices, which usually do not have a mouse cursor, the coordinates\n * of the currently touched position are shown.\n *\n * @api\n */\nclass MousePosition extends Control {\n  /**\n   * @param {Options} [options] Mouse position options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    const element = document.createElement('div');\n    element.className =\n      options.className !== undefined ? options.className : 'ol-mouse-position';\n\n    super({\n      element: element,\n      render: options.render,\n      target: options.target,\n    });\n\n    /***\n     * @type {MousePositionOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {MousePositionOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {MousePositionOnSignature<void>}\n     */\n    this.un;\n\n    this.addChangeListener(PROJECTION, this.handleProjectionChanged_);\n\n    if (options.coordinateFormat) {\n      this.setCoordinateFormat(options.coordinateFormat);\n    }\n    if (options.projection) {\n      this.setProjection(options.projection);\n    }\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.renderOnMouseOut_ = options.placeholder !== undefined;\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.placeholder_ = this.renderOnMouseOut_ ? options.placeholder : '&#160;';\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.renderedHTML_ = element.innerHTML;\n\n    /**\n     * @private\n     * @type {?import(\"../proj/Projection.js\").default}\n     */\n    this.mapProjection_ = null;\n\n    /**\n     * @private\n     * @type {?import(\"../proj.js\").TransformFunction}\n     */\n    this.transform_ = null;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.wrapX_ = options.wrapX === false ? false : true;\n  }\n\n  /**\n   * @private\n   */\n  handleProjectionChanged_() {\n    this.transform_ = null;\n  }\n\n  /**\n   * Return the coordinate format type used to render the current position or\n   * undefined.\n   * @return {import(\"../coordinate.js\").CoordinateFormat|undefined} The format to render the current\n   *     position in.\n   * @observable\n   * @api\n   */\n  getCoordinateFormat() {\n    return /** @type {import(\"../coordinate.js\").CoordinateFormat|undefined} */ (\n      this.get(COORDINATE_FORMAT)\n    );\n  }\n\n  /**\n   * Return the projection that is used to report the mouse position.\n   * @return {import(\"../proj/Projection.js\").default|undefined} The projection to report mouse\n   *     position in.\n   * @observable\n   * @api\n   */\n  getProjection() {\n    return /** @type {import(\"../proj/Projection.js\").default|undefined} */ (\n      this.get(PROJECTION)\n    );\n  }\n\n  /**\n   * @param {MouseEvent} event Browser event.\n   * @protected\n   */\n  handleMouseMove(event) {\n    const map = this.getMap();\n    this.updateHTML_(map.getEventPixel(event));\n  }\n\n  /**\n   * @param {Event} event Browser event.\n   * @protected\n   */\n  handleMouseOut(event) {\n    this.updateHTML_(null);\n  }\n\n  /**\n   * Remove the control from its current map and attach it to the new map.\n   * Pass `null` to just remove the control from the current map.\n   * Subclasses may set up event handlers to get notified about changes to\n   * the map here.\n   * @param {import(\"../Map.js\").default|null} map Map.\n   * @api\n   */\n  setMap(map) {\n    super.setMap(map);\n    if (map) {\n      const viewport = map.getViewport();\n      this.listenerKeys.push(\n        listen(viewport, EventType.POINTERMOVE, this.handleMouseMove, this)\n      );\n      if (this.renderOnMouseOut_) {\n        this.listenerKeys.push(\n          listen(viewport, EventType.POINTEROUT, this.handleMouseOut, this)\n        );\n      }\n      this.updateHTML_(null);\n    }\n  }\n\n  /**\n   * Set the coordinate format type used to render the current position.\n   * @param {import(\"../coordinate.js\").CoordinateFormat} format The format to render the current\n   *     position in.\n   * @observable\n   * @api\n   */\n  setCoordinateFormat(format) {\n    this.set(COORDINATE_FORMAT, format);\n  }\n\n  /**\n   * Set the projection that is used to report the mouse position.\n   * @param {import(\"../proj.js\").ProjectionLike} projection The projection to report mouse\n   *     position in.\n   * @observable\n   * @api\n   */\n  setProjection(projection) {\n    this.set(PROJECTION, getProjection(projection));\n  }\n\n  /**\n   * @param {?import(\"../pixel.js\").Pixel} pixel Pixel.\n   * @private\n   */\n  updateHTML_(pixel) {\n    let html = this.placeholder_;\n    if (pixel && this.mapProjection_) {\n      if (!this.transform_) {\n        const projection = this.getProjection();\n        if (projection) {\n          this.transform_ = getTransformFromProjections(\n            this.mapProjection_,\n            projection\n          );\n        } else {\n          this.transform_ = identityTransform;\n        }\n      }\n      const map = this.getMap();\n      const coordinate = map.getCoordinateFromPixelInternal(pixel);\n      if (coordinate) {\n        const userProjection = getUserProjection();\n        if (userProjection) {\n          this.transform_ = getTransformFromProjections(\n            this.mapProjection_,\n            userProjection\n          );\n        }\n        this.transform_(coordinate, coordinate);\n        if (this.wrapX_) {\n          const projection =\n            userProjection || this.getProjection() || this.mapProjection_;\n          wrapX(coordinate, projection);\n        }\n        const coordinateFormat = this.getCoordinateFormat();\n        if (coordinateFormat) {\n          html = coordinateFormat(coordinate);\n        } else {\n          html = coordinate.toString();\n        }\n      }\n    }\n    if (!this.renderedHTML_ || html !== this.renderedHTML_) {\n      this.element.innerHTML = html;\n      this.renderedHTML_ = html;\n    }\n  }\n\n  /**\n   * Update the projection. Rendering of the coordinates is done in\n   * `handleMouseMove` and `handleMouseUp`.\n   * @param {import(\"../MapEvent.js\").default} mapEvent Map event.\n   * @override\n   */\n  render(mapEvent) {\n    const frameState = mapEvent.frameState;\n    if (!frameState) {\n      this.mapProjection_ = null;\n    } else {\n      if (this.mapProjection_ != frameState.viewState.projection) {\n        this.mapProjection_ = frameState.viewState.projection;\n        this.transform_ = null;\n      }\n    }\n  }\n}\n\nexport default MousePosition;\n", "/**\n * @module ol/control/OverviewMap\n */\nimport Collection from '../Collection.js';\nimport Control from './Control.js';\nimport EventType from '../events/EventType.js';\nimport Map from '../Map.js';\nimport MapEventType from '../MapEventType.js';\nimport MapProperty from '../MapProperty.js';\nimport ObjectEventType from '../ObjectEventType.js';\nimport Overlay from '../Overlay.js';\nimport View from '../View.js';\nimport ViewProperty from '../ViewProperty.js';\nimport {CLASS_COLLAPSED, CLASS_CONTROL, CLASS_UNSELECTABLE} from '../css.js';\nimport {\n  containsExtent,\n  equals as equalsExtent,\n  getBottomRight,\n  getTopLeft,\n  scaleFromCenter,\n} from '../extent.js';\nimport {listen, listenOnce} from '../events.js';\nimport {fromExtent as polygonFromExtent} from '../geom/Polygon.js';\nimport {replaceNode} from '../dom.js';\n\n/**\n * Maximum width and/or height extent ratio that determines when the overview\n * map should be zoomed out.\n * @type {number}\n */\nconst MAX_RATIO = 0.75;\n\n/**\n * Minimum width and/or height extent ratio that determines when the overview\n * map should be zoomed in.\n * @type {number}\n */\nconst MIN_RATIO = 0.1;\n\n/**\n * @typedef {Object} Options\n * @property {string} [className='ol-overviewmap'] CSS class name.\n * @property {boolean} [collapsed=true] Whether the control should start collapsed or not (expanded).\n * @property {string|HTMLElement} [collapseLabel='‹'] Text label to use for the\n * expanded overviewmap button. Instead of text, also an element (e.g. a `span` element) can be used.\n * @property {boolean} [collapsible=true] Whether the control can be collapsed or not.\n * @property {string|HTMLElement} [label='›'] Text label to use for the collapsed\n * overviewmap button. Instead of text, also an element (e.g. a `span` element) can be used.\n * @property {Array<import(\"../layer/Base.js\").default>|import(\"../Collection.js\").default<import(\"../layer/Base.js\").default>} [layers]\n * Layers for the overview map.\n * @property {function(import(\"../MapEvent.js\").default):void} [render] Function called when the control\n * should be re-rendered. This is called in a `requestAnimationFrame` callback.\n * @property {boolean} [rotateWithView=false] Whether the control view should rotate with the main map view.\n * @property {HTMLElement|string} [target] Specify a target if you want the control\n * to be rendered outside of the map's viewport.\n * @property {string} [tipLabel='Overview map'] Text label to use for the button tip.\n * @property {View} [view] Custom view for the overview map (should use same projection as main map). If not provided,\n * a default view with the same projection as the main map will be used.\n */\n\n/**\n * Create a new control with a map acting as an overview map for another\n * defined map.\n *\n * @api\n */\nclass OverviewMap extends Control {\n  /**\n   * @param {Options} [options] OverviewMap options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    super({\n      element: document.createElement('div'),\n      render: options.render,\n      target: options.target,\n    });\n\n    /**\n     * @private\n     */\n    this.boundHandleRotationChanged_ = this.handleRotationChanged_.bind(this);\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.collapsed_ =\n      options.collapsed !== undefined ? options.collapsed : true;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.collapsible_ =\n      options.collapsible !== undefined ? options.collapsible : true;\n\n    if (!this.collapsible_) {\n      this.collapsed_ = false;\n    }\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.rotateWithView_ =\n      options.rotateWithView !== undefined ? options.rotateWithView : false;\n\n    /**\n     * @private\n     * @type {import(\"../extent.js\").Extent|undefined}\n     */\n    this.viewExtent_ = undefined;\n\n    const className =\n      options.className !== undefined ? options.className : 'ol-overviewmap';\n\n    const tipLabel =\n      options.tipLabel !== undefined ? options.tipLabel : 'Overview map';\n\n    const collapseLabel =\n      options.collapseLabel !== undefined ? options.collapseLabel : '\\u2039';\n\n    if (typeof collapseLabel === 'string') {\n      /**\n       * @private\n       * @type {HTMLElement}\n       */\n      this.collapseLabel_ = document.createElement('span');\n      this.collapseLabel_.textContent = collapseLabel;\n    } else {\n      this.collapseLabel_ = collapseLabel;\n    }\n\n    const label = options.label !== undefined ? options.label : '\\u203A';\n\n    if (typeof label === 'string') {\n      /**\n       * @private\n       * @type {HTMLElement}\n       */\n      this.label_ = document.createElement('span');\n      this.label_.textContent = label;\n    } else {\n      this.label_ = label;\n    }\n\n    const activeLabel =\n      this.collapsible_ && !this.collapsed_ ? this.collapseLabel_ : this.label_;\n    const button = document.createElement('button');\n    button.setAttribute('type', 'button');\n    button.title = tipLabel;\n    button.appendChild(activeLabel);\n\n    button.addEventListener(\n      EventType.CLICK,\n      this.handleClick_.bind(this),\n      false\n    );\n\n    /**\n     * @type {HTMLElement}\n     * @private\n     */\n    this.ovmapDiv_ = document.createElement('div');\n    this.ovmapDiv_.className = 'ol-overviewmap-map';\n\n    /**\n     * Explicitly given view to be used instead of a view derived from the main map.\n     * @type {View}\n     * @private\n     */\n    this.view_ = options.view;\n\n    const ovmap = new Map({\n      view: options.view,\n      controls: new Collection(),\n      interactions: new Collection(),\n    });\n\n    /**\n     * @type {Map}\n     * @private\n     */\n    this.ovmap_ = ovmap;\n\n    if (options.layers) {\n      options.layers.forEach(function (layer) {\n        ovmap.addLayer(layer);\n      });\n    }\n\n    const box = document.createElement('div');\n    box.className = 'ol-overviewmap-box';\n    box.style.boxSizing = 'border-box';\n\n    /**\n     * @type {import(\"../Overlay.js\").default}\n     * @private\n     */\n    this.boxOverlay_ = new Overlay({\n      position: [0, 0],\n      positioning: 'center-center',\n      element: box,\n    });\n    this.ovmap_.addOverlay(this.boxOverlay_);\n\n    const cssClasses =\n      className +\n      ' ' +\n      CLASS_UNSELECTABLE +\n      ' ' +\n      CLASS_CONTROL +\n      (this.collapsed_ && this.collapsible_ ? ' ' + CLASS_COLLAPSED : '') +\n      (this.collapsible_ ? '' : ' ol-uncollapsible');\n    const element = this.element;\n    element.className = cssClasses;\n    element.appendChild(this.ovmapDiv_);\n    element.appendChild(button);\n\n    /* Interactive map */\n\n    const scope = this;\n\n    const overlay = this.boxOverlay_;\n    const overlayBox = this.boxOverlay_.getElement();\n\n    /* Functions definition */\n\n    const computeDesiredMousePosition = function (mousePosition) {\n      return {\n        clientX: mousePosition.clientX,\n        clientY: mousePosition.clientY,\n      };\n    };\n\n    const move = function (event) {\n      const position = /** @type {?} */ (computeDesiredMousePosition(event));\n      const coordinates = ovmap.getEventCoordinateInternal(\n        /** @type {MouseEvent} */ (position)\n      );\n\n      overlay.setPosition(coordinates);\n    };\n\n    const endMoving = function (event) {\n      const coordinates = ovmap.getEventCoordinateInternal(event);\n\n      scope.getMap().getView().setCenterInternal(coordinates);\n\n      window.removeEventListener('mousemove', move);\n      window.removeEventListener('mouseup', endMoving);\n    };\n\n    /* Binding */\n\n    overlayBox.addEventListener('mousedown', function () {\n      window.addEventListener('mousemove', move);\n      window.addEventListener('mouseup', endMoving);\n    });\n  }\n\n  /**\n   * Remove the control from its current map and attach it to the new map.\n   * Pass `null` to just remove the control from the current map.\n   * Subclasses may set up event handlers to get notified about changes to\n   * the map here.\n   * @param {import(\"../Map.js\").default|null} map Map.\n   * @api\n   */\n  setMap(map) {\n    const oldMap = this.getMap();\n    if (map === oldMap) {\n      return;\n    }\n    if (oldMap) {\n      const oldView = oldMap.getView();\n      if (oldView) {\n        this.unbindView_(oldView);\n      }\n      this.ovmap_.setTarget(null);\n    }\n    super.setMap(map);\n\n    if (map) {\n      this.ovmap_.setTarget(this.ovmapDiv_);\n      this.listenerKeys.push(\n        listen(\n          map,\n          ObjectEventType.PROPERTYCHANGE,\n          this.handleMapPropertyChange_,\n          this\n        )\n      );\n\n      const view = map.getView();\n      if (view) {\n        this.bindView_(view);\n        if (view.isDef()) {\n          this.ovmap_.updateSize();\n          this.resetExtent_();\n        }\n      }\n\n      if (!this.ovmap_.isRendered()) {\n        this.updateBoxAfterOvmapIsRendered_();\n      }\n    }\n  }\n\n  /**\n   * Handle map property changes.  This only deals with changes to the map's view.\n   * @param {import(\"../Object.js\").ObjectEvent} event The propertychange event.\n   * @private\n   */\n  handleMapPropertyChange_(event) {\n    if (event.key === MapProperty.VIEW) {\n      const oldView = /** @type {import(\"../View.js\").default} */ (\n        event.oldValue\n      );\n      if (oldView) {\n        this.unbindView_(oldView);\n      }\n      const newView = this.getMap().getView();\n      this.bindView_(newView);\n    } else if (\n      !this.ovmap_.isRendered() &&\n      (event.key === MapProperty.TARGET || event.key === MapProperty.SIZE)\n    ) {\n      this.ovmap_.updateSize();\n    }\n  }\n\n  /**\n   * Register listeners for view property changes.\n   * @param {import(\"../View.js\").default} view The view.\n   * @private\n   */\n  bindView_(view) {\n    if (!this.view_) {\n      // Unless an explicit view definition was given, derive default from whatever main map uses.\n      const newView = new View({\n        projection: view.getProjection(),\n      });\n      this.ovmap_.setView(newView);\n    }\n\n    view.addChangeListener(\n      ViewProperty.ROTATION,\n      this.boundHandleRotationChanged_\n    );\n    // Sync once with the new view\n    this.handleRotationChanged_();\n  }\n\n  /**\n   * Unregister listeners for view property changes.\n   * @param {import(\"../View.js\").default} view The view.\n   * @private\n   */\n  unbindView_(view) {\n    view.removeChangeListener(\n      ViewProperty.ROTATION,\n      this.boundHandleRotationChanged_\n    );\n  }\n\n  /**\n   * Handle rotation changes to the main map.\n   * @private\n   */\n  handleRotationChanged_() {\n    if (this.rotateWithView_) {\n      this.ovmap_.getView().setRotation(this.getMap().getView().getRotation());\n    }\n  }\n\n  /**\n   * Reset the overview map extent if the box size (width or\n   * height) is less than the size of the overview map size times minRatio\n   * or is greater than the size of the overview size times maxRatio.\n   *\n   * If the map extent was not reset, the box size can fits in the defined\n   * ratio sizes. This method then checks if is contained inside the overview\n   * map current extent. If not, recenter the overview map to the current\n   * main map center location.\n   * @private\n   */\n  validateExtent_() {\n    const map = this.getMap();\n    const ovmap = this.ovmap_;\n\n    if (!map.isRendered() || !ovmap.isRendered()) {\n      return;\n    }\n\n    const mapSize = /** @type {import(\"../size.js\").Size} */ (map.getSize());\n\n    const view = map.getView();\n    const extent = view.calculateExtentInternal(mapSize);\n\n    if (this.viewExtent_ && equalsExtent(extent, this.viewExtent_)) {\n      // repeats of the same extent may indicate constraint conflicts leading to an endless cycle\n      return;\n    }\n    this.viewExtent_ = extent;\n\n    const ovmapSize = /** @type {import(\"../size.js\").Size} */ (\n      ovmap.getSize()\n    );\n\n    const ovview = ovmap.getView();\n    const ovextent = ovview.calculateExtentInternal(ovmapSize);\n\n    const topLeftPixel = ovmap.getPixelFromCoordinateInternal(\n      getTopLeft(extent)\n    );\n    const bottomRightPixel = ovmap.getPixelFromCoordinateInternal(\n      getBottomRight(extent)\n    );\n\n    const boxWidth = Math.abs(topLeftPixel[0] - bottomRightPixel[0]);\n    const boxHeight = Math.abs(topLeftPixel[1] - bottomRightPixel[1]);\n\n    const ovmapWidth = ovmapSize[0];\n    const ovmapHeight = ovmapSize[1];\n\n    if (\n      boxWidth < ovmapWidth * MIN_RATIO ||\n      boxHeight < ovmapHeight * MIN_RATIO ||\n      boxWidth > ovmapWidth * MAX_RATIO ||\n      boxHeight > ovmapHeight * MAX_RATIO\n    ) {\n      this.resetExtent_();\n    } else if (!containsExtent(ovextent, extent)) {\n      this.recenter_();\n    }\n  }\n\n  /**\n   * Reset the overview map extent to half calculated min and max ratio times\n   * the extent of the main map.\n   * @private\n   */\n  resetExtent_() {\n    if (MAX_RATIO === 0 || MIN_RATIO === 0) {\n      return;\n    }\n\n    const map = this.getMap();\n    const ovmap = this.ovmap_;\n\n    const mapSize = /** @type {import(\"../size.js\").Size} */ (map.getSize());\n\n    const view = map.getView();\n    const extent = view.calculateExtentInternal(mapSize);\n\n    const ovview = ovmap.getView();\n\n    // get how many times the current map overview could hold different\n    // box sizes using the min and max ratio, pick the step in the middle used\n    // to calculate the extent from the main map to set it to the overview map,\n    const steps = Math.log(MAX_RATIO / MIN_RATIO) / Math.LN2;\n    const ratio = 1 / (Math.pow(2, steps / 2) * MIN_RATIO);\n    scaleFromCenter(extent, ratio);\n    ovview.fitInternal(polygonFromExtent(extent));\n  }\n\n  /**\n   * Set the center of the overview map to the map center without changing its\n   * resolution.\n   * @private\n   */\n  recenter_() {\n    const map = this.getMap();\n    const ovmap = this.ovmap_;\n\n    const view = map.getView();\n\n    const ovview = ovmap.getView();\n\n    ovview.setCenterInternal(view.getCenterInternal());\n  }\n\n  /**\n   * Update the box using the main map extent\n   * @private\n   */\n  updateBox_() {\n    const map = this.getMap();\n    const ovmap = this.ovmap_;\n\n    if (!map.isRendered() || !ovmap.isRendered()) {\n      return;\n    }\n\n    const mapSize = /** @type {import(\"../size.js\").Size} */ (map.getSize());\n\n    const view = map.getView();\n\n    const ovview = ovmap.getView();\n\n    const rotation = this.rotateWithView_ ? 0 : -view.getRotation();\n\n    const overlay = this.boxOverlay_;\n    const box = this.boxOverlay_.getElement();\n    const center = view.getCenterInternal();\n    const resolution = view.getResolution();\n    const ovresolution = ovview.getResolution();\n    const width = (mapSize[0] * resolution) / ovresolution;\n    const height = (mapSize[1] * resolution) / ovresolution;\n\n    // set position using center coordinates\n    overlay.setPosition(center);\n\n    // set box size calculated from map extent size and overview map resolution\n    if (box) {\n      box.style.width = width + 'px';\n      box.style.height = height + 'px';\n      const transform = 'rotate(' + rotation + 'rad)';\n      box.style.transform = transform;\n    }\n  }\n\n  /**\n   * @private\n   */\n  updateBoxAfterOvmapIsRendered_() {\n    if (this.ovmapPostrenderKey_) {\n      return;\n    }\n    this.ovmapPostrenderKey_ = listenOnce(\n      this.ovmap_,\n      MapEventType.POSTRENDER,\n      function (event) {\n        delete this.ovmapPostrenderKey_;\n        this.updateBox_();\n      },\n      this\n    );\n  }\n\n  /**\n   * @param {MouseEvent} event The event to handle\n   * @private\n   */\n  handleClick_(event) {\n    event.preventDefault();\n    this.handleToggle_();\n  }\n\n  /**\n   * @private\n   */\n  handleToggle_() {\n    this.element.classList.toggle(CLASS_COLLAPSED);\n    if (this.collapsed_) {\n      replaceNode(this.collapseLabel_, this.label_);\n    } else {\n      replaceNode(this.label_, this.collapseLabel_);\n    }\n    this.collapsed_ = !this.collapsed_;\n\n    // manage overview map if it had not been rendered before and control\n    // is expanded\n    const ovmap = this.ovmap_;\n    if (!this.collapsed_) {\n      if (ovmap.isRendered()) {\n        this.viewExtent_ = undefined;\n        ovmap.render();\n        return;\n      }\n      ovmap.updateSize();\n      this.resetExtent_();\n      this.updateBoxAfterOvmapIsRendered_();\n    }\n  }\n\n  /**\n   * Return `true` if the overview map is collapsible, `false` otherwise.\n   * @return {boolean} True if the widget is collapsible.\n   * @api\n   */\n  getCollapsible() {\n    return this.collapsible_;\n  }\n\n  /**\n   * Set whether the overview map should be collapsible.\n   * @param {boolean} collapsible True if the widget is collapsible.\n   * @api\n   */\n  setCollapsible(collapsible) {\n    if (this.collapsible_ === collapsible) {\n      return;\n    }\n    this.collapsible_ = collapsible;\n    this.element.classList.toggle('ol-uncollapsible');\n    if (!collapsible && this.collapsed_) {\n      this.handleToggle_();\n    }\n  }\n\n  /**\n   * Collapse or expand the overview map according to the passed parameter. Will\n   * not do anything if the overview map isn't collapsible or if the current\n   * collapsed state is already the one requested.\n   * @param {boolean} collapsed True if the widget is collapsed.\n   * @api\n   */\n  setCollapsed(collapsed) {\n    if (!this.collapsible_ || this.collapsed_ === collapsed) {\n      return;\n    }\n    this.handleToggle_();\n  }\n\n  /**\n   * Determine if the overview map is collapsed.\n   * @return {boolean} The overview map is collapsed.\n   * @api\n   */\n  getCollapsed() {\n    return this.collapsed_;\n  }\n\n  /**\n   * Return `true` if the overview map view can rotate, `false` otherwise.\n   * @return {boolean} True if the control view can rotate.\n   * @api\n   */\n  getRotateWithView() {\n    return this.rotateWithView_;\n  }\n\n  /**\n   * Set whether the overview map view should rotate with the main map view.\n   * @param {boolean} rotateWithView True if the control view should rotate.\n   * @api\n   */\n  setRotateWithView(rotateWithView) {\n    if (this.rotateWithView_ === rotateWithView) {\n      return;\n    }\n    this.rotateWithView_ = rotateWithView;\n    if (this.getMap().getView().getRotation() !== 0) {\n      if (this.rotateWithView_) {\n        this.handleRotationChanged_();\n      } else {\n        this.ovmap_.getView().setRotation(0);\n      }\n      this.viewExtent_ = undefined;\n      this.validateExtent_();\n      this.updateBox_();\n    }\n  }\n\n  /**\n   * Return the overview map.\n   * @return {import(\"../Map.js\").default} Overview map.\n   * @api\n   */\n  getOverviewMap() {\n    return this.ovmap_;\n  }\n\n  /**\n   * Update the overview map element.\n   * @param {import(\"../MapEvent.js\").default} mapEvent Map event.\n   * @override\n   */\n  render(mapEvent) {\n    this.validateExtent_();\n    this.updateBox_();\n  }\n}\n\nexport default OverviewMap;\n", "/**\n * @module ol/control/ScaleLine\n */\nimport Control from './Control.js';\nimport {CLASS_UNSELECTABLE} from '../css.js';\nimport {METERS_PER_UNIT, getPointResolution} from '../proj.js';\nimport {assert} from '../asserts.js';\n\n/**\n * @type {string}\n */\nconst UNITS_PROP = 'units';\n\n/**\n * @typedef {'degrees' | 'imperial' | 'nautical' | 'metric' | 'us'} Units\n * Units for the scale line.\n */\n\n/**\n * @const\n * @type {Array<number>}\n */\nconst LEADING_DIGITS = [1, 2, 5];\n\n/**\n * @const\n * @type {number}\n */\nconst DEFAULT_DPI = 25.4 / 0.28;\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types|\n *     'change:units', import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types\n *     |'change:units', Return>} ScaleLineOnSignature\n */\n\n/**\n * @typedef {Object} Options\n * @property {string} [className] CSS class name. The default is `ol-scale-bar` when configured with\n * `bar: true`. Otherwise the default is `ol-scale-line`.\n * @property {number} [minWidth=64] Minimum width in pixels at the OGC default dpi. The width will be\n * adjusted to match the dpi used.\n * @property {number} [maxWidth] Maximum width in pixels at the OGC default dpi. The width will be\n * adjusted to match the dpi used.\n * @property {function(import(\"../MapEvent.js\").default):void} [render] Function called when the control\n * should be re-rendered. This is called in a `requestAnimationFrame` callback.\n * @property {HTMLElement|string} [target] Specify a target if you want the control\n * to be rendered outside of the map's viewport.\n * @property {Units} [units='metric'] Units.\n * @property {boolean} [bar=false] Render scalebars instead of a line.\n * @property {number} [steps=4] Number of steps the scalebar should use. Use even numbers\n * for best results. Only applies when `bar` is `true`.\n * @property {boolean} [text=false] Render the text scale above of the scalebar. Only applies\n * when `bar` is `true`.\n * @property {number|undefined} [dpi=undefined] dpi of output device such as printer. Only applies\n * when `bar` is `true`. If undefined the OGC default screen pixel size of 0.28mm will be assumed.\n */\n\n/**\n * @classdesc\n * A control displaying rough y-axis distances, calculated for the center of the\n * viewport. For conformal projections (e.g. EPSG:3857, the default view\n * projection in OpenLayers), the scale is valid for all directions.\n * No scale line will be shown when the y-axis distance of a pixel at the\n * viewport center cannot be calculated in the view projection.\n * By default the scale line will show in the bottom left portion of the map,\n * but this can be changed by using the css selector `.ol-scale-line`.\n * When specifying `bar` as `true`, a scalebar will be rendered instead\n * of a scaleline.\n *\n * @api\n */\nclass ScaleLine extends Control {\n  /**\n   * @param {Options} [options] Scale line options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    const element = document.createElement('div');\n    element.style.pointerEvents = 'none';\n\n    super({\n      element: element,\n      render: options.render,\n      target: options.target,\n    });\n\n    /***\n     * @type {ScaleLineOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {ScaleLineOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {ScaleLineOnSignature<void>}\n     */\n    this.un;\n\n    const className =\n      options.className !== undefined\n        ? options.className\n        : options.bar\n        ? 'ol-scale-bar'\n        : 'ol-scale-line';\n\n    /**\n     * @private\n     * @type {HTMLElement}\n     */\n    this.innerElement_ = document.createElement('div');\n    this.innerElement_.className = className + '-inner';\n\n    this.element.className = className + ' ' + CLASS_UNSELECTABLE;\n    this.element.appendChild(this.innerElement_);\n\n    /**\n     * @private\n     * @type {?import(\"../View.js\").State}\n     */\n    this.viewState_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.minWidth_ = options.minWidth !== undefined ? options.minWidth : 64;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.maxWidth_ = options.maxWidth;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.renderedVisible_ = false;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.renderedWidth_ = undefined;\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.renderedHTML_ = '';\n\n    this.addChangeListener(UNITS_PROP, this.handleUnitsChanged_);\n\n    this.setUnits(options.units || 'metric');\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.scaleBar_ = options.bar || false;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.scaleBarSteps_ = options.steps || 4;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.scaleBarText_ = options.text || false;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.dpi_ = options.dpi || undefined;\n  }\n\n  /**\n   * Return the units to use in the scale line.\n   * @return {Units} The units\n   * to use in the scale line.\n   * @observable\n   * @api\n   */\n  getUnits() {\n    return this.get(UNITS_PROP);\n  }\n\n  /**\n   * @private\n   */\n  handleUnitsChanged_() {\n    this.updateElement_();\n  }\n\n  /**\n   * Set the units to use in the scale line.\n   * @param {Units} units The units to use in the scale line.\n   * @observable\n   * @api\n   */\n  setUnits(units) {\n    this.set(UNITS_PROP, units);\n  }\n\n  /**\n   * Specify the dpi of output device such as printer.\n   * @param {number|undefined} dpi The dpi of output device.\n   * @api\n   */\n  setDpi(dpi) {\n    this.dpi_ = dpi;\n  }\n\n  /**\n   * @private\n   */\n  updateElement_() {\n    const viewState = this.viewState_;\n\n    if (!viewState) {\n      if (this.renderedVisible_) {\n        this.element.style.display = 'none';\n        this.renderedVisible_ = false;\n      }\n      return;\n    }\n\n    const center = viewState.center;\n    const projection = viewState.projection;\n    const units = this.getUnits();\n    const pointResolutionUnits = units == 'degrees' ? 'degrees' : 'm';\n    let pointResolution = getPointResolution(\n      projection,\n      viewState.resolution,\n      center,\n      pointResolutionUnits\n    );\n\n    const minWidth =\n      (this.minWidth_ * (this.dpi_ || DEFAULT_DPI)) / DEFAULT_DPI;\n\n    const maxWidth =\n      this.maxWidth_ !== undefined\n        ? (this.maxWidth_ * (this.dpi_ || DEFAULT_DPI)) / DEFAULT_DPI\n        : undefined;\n\n    let nominalCount = minWidth * pointResolution;\n    let suffix = '';\n    if (units == 'degrees') {\n      const metersPerDegree = METERS_PER_UNIT.degrees;\n      nominalCount *= metersPerDegree;\n      if (nominalCount < metersPerDegree / 60) {\n        suffix = '\\u2033'; // seconds\n        pointResolution *= 3600;\n      } else if (nominalCount < metersPerDegree) {\n        suffix = '\\u2032'; // minutes\n        pointResolution *= 60;\n      } else {\n        suffix = '\\u00b0'; // degrees\n      }\n    } else if (units == 'imperial') {\n      if (nominalCount < 0.9144) {\n        suffix = 'in';\n        pointResolution /= 0.0254;\n      } else if (nominalCount < 1609.344) {\n        suffix = 'ft';\n        pointResolution /= 0.3048;\n      } else {\n        suffix = 'mi';\n        pointResolution /= 1609.344;\n      }\n    } else if (units == 'nautical') {\n      pointResolution /= 1852;\n      suffix = 'NM';\n    } else if (units == 'metric') {\n      if (nominalCount < 0.001) {\n        suffix = 'μm';\n        pointResolution *= 1000000;\n      } else if (nominalCount < 1) {\n        suffix = 'mm';\n        pointResolution *= 1000;\n      } else if (nominalCount < 1000) {\n        suffix = 'm';\n      } else {\n        suffix = 'km';\n        pointResolution /= 1000;\n      }\n    } else if (units == 'us') {\n      if (nominalCount < 0.9144) {\n        suffix = 'in';\n        pointResolution *= 39.37;\n      } else if (nominalCount < 1609.344) {\n        suffix = 'ft';\n        pointResolution /= 0.30480061;\n      } else {\n        suffix = 'mi';\n        pointResolution /= 1609.3472;\n      }\n    } else {\n      assert(false, 33); // Invalid units\n    }\n\n    let i = 3 * Math.floor(Math.log(minWidth * pointResolution) / Math.log(10));\n    let count, width, decimalCount;\n    let previousCount, previousWidth, previousDecimalCount;\n    while (true) {\n      decimalCount = Math.floor(i / 3);\n      const decimal = Math.pow(10, decimalCount);\n      count = LEADING_DIGITS[((i % 3) + 3) % 3] * decimal;\n      width = Math.round(count / pointResolution);\n      if (isNaN(width)) {\n        this.element.style.display = 'none';\n        this.renderedVisible_ = false;\n        return;\n      }\n      if (maxWidth !== undefined && width >= maxWidth) {\n        count = previousCount;\n        width = previousWidth;\n        decimalCount = previousDecimalCount;\n        break;\n      } else if (width >= minWidth) {\n        break;\n      }\n      previousCount = count;\n      previousWidth = width;\n      previousDecimalCount = decimalCount;\n      ++i;\n    }\n    const html = this.scaleBar_\n      ? this.createScaleBar(width, count, suffix)\n      : count.toFixed(decimalCount < 0 ? -decimalCount : 0) + ' ' + suffix;\n\n    if (this.renderedHTML_ != html) {\n      this.innerElement_.innerHTML = html;\n      this.renderedHTML_ = html;\n    }\n\n    if (this.renderedWidth_ != width) {\n      this.innerElement_.style.width = width + 'px';\n      this.renderedWidth_ = width;\n    }\n\n    if (!this.renderedVisible_) {\n      this.element.style.display = '';\n      this.renderedVisible_ = true;\n    }\n  }\n\n  /**\n   * @private\n   * @param {number} width The current width of the scalebar.\n   * @param {number} scale The current scale.\n   * @param {string} suffix The suffix to append to the scale text.\n   * @return {string} The stringified HTML of the scalebar.\n   */\n  createScaleBar(width, scale, suffix) {\n    const resolutionScale = this.getScaleForResolution();\n    const mapScale =\n      resolutionScale < 1\n        ? Math.round(1 / resolutionScale).toLocaleString() + ' : 1'\n        : '1 : ' + Math.round(resolutionScale).toLocaleString();\n    const steps = this.scaleBarSteps_;\n    const stepWidth = width / steps;\n    const scaleSteps = [this.createMarker('absolute')];\n    for (let i = 0; i < steps; ++i) {\n      const cls =\n        i % 2 === 0 ? 'ol-scale-singlebar-odd' : 'ol-scale-singlebar-even';\n      scaleSteps.push(\n        '<div>' +\n          '<div ' +\n          `class=\"ol-scale-singlebar ${cls}\" ` +\n          `style=\"width: ${stepWidth}px;\"` +\n          '>' +\n          '</div>' +\n          this.createMarker('relative') +\n          // render text every second step, except when only 2 steps\n          (i % 2 === 0 || steps === 2\n            ? this.createStepText(i, width, false, scale, suffix)\n            : '') +\n          '</div>'\n      );\n    }\n    // render text at the end\n    scaleSteps.push(this.createStepText(steps, width, true, scale, suffix));\n\n    const scaleBarText = this.scaleBarText_\n      ? `<div class=\"ol-scale-text\" style=\"width: ${width}px;\">` +\n        mapScale +\n        '</div>'\n      : '';\n    return scaleBarText + scaleSteps.join('');\n  }\n\n  /**\n   * Creates a marker at given position\n   * @param {'absolute'|'relative'} position The position, absolute or relative\n   * @return {string} The stringified div containing the marker\n   */\n  createMarker(position) {\n    const top = position === 'absolute' ? 3 : -10;\n    return (\n      '<div ' +\n      'class=\"ol-scale-step-marker\" ' +\n      `style=\"position: ${position}; top: ${top}px;\"` +\n      '></div>'\n    );\n  }\n\n  /**\n   * Creates the label for a marker marker at given position\n   * @param {number} i The iterator\n   * @param {number} width The width the scalebar will currently use\n   * @param {boolean} isLast Flag indicating if we add the last step text\n   * @param {number} scale The current scale for the whole scalebar\n   * @param {string} suffix The suffix for the scale\n   * @return {string} The stringified div containing the step text\n   */\n  createStepText(i, width, isLast, scale, suffix) {\n    const length =\n      i === 0 ? 0 : Math.round((scale / this.scaleBarSteps_) * i * 100) / 100;\n    const lengthString = length + (i === 0 ? '' : ' ' + suffix);\n    const margin = i === 0 ? -3 : (width / this.scaleBarSteps_) * -1;\n    const minWidth = i === 0 ? 0 : (width / this.scaleBarSteps_) * 2;\n    return (\n      '<div ' +\n      'class=\"ol-scale-step-text\" ' +\n      'style=\"' +\n      `margin-left: ${margin}px;` +\n      `text-align: ${i === 0 ? 'left' : 'center'};` +\n      `min-width: ${minWidth}px;` +\n      `left: ${isLast ? width + 'px' : 'unset'};` +\n      '\">' +\n      lengthString +\n      '</div>'\n    );\n  }\n\n  /**\n   * Returns the appropriate scale for the given resolution and units.\n   * @return {number} The appropriate scale.\n   */\n  getScaleForResolution() {\n    const resolution = getPointResolution(\n      this.viewState_.projection,\n      this.viewState_.resolution,\n      this.viewState_.center,\n      'm'\n    );\n    const dpi = this.dpi_ || DEFAULT_DPI;\n    const inchesPerMeter = 1000 / 25.4;\n    return resolution * inchesPerMeter * dpi;\n  }\n\n  /**\n   * Update the scale line element.\n   * @param {import(\"../MapEvent.js\").default} mapEvent Map event.\n   * @override\n   */\n  render(mapEvent) {\n    const frameState = mapEvent.frameState;\n    if (!frameState) {\n      this.viewState_ = null;\n    } else {\n      this.viewState_ = frameState.viewState;\n    }\n    this.updateElement_();\n  }\n}\n\nexport default ScaleLine;\n", "/**\n * @module ol/control/ZoomSlider\n */\n\nimport Control from './Control.js';\nimport EventType from '../events/EventType.js';\nimport PointerEventType from '../pointer/EventType.js';\nimport {CLASS_CONTROL, CLASS_UNSELECTABLE} from '../css.js';\nimport {clamp} from '../math.js';\nimport {easeOut} from '../easing.js';\nimport {listen, unlistenBy<PERSON>ey} from '../events.js';\nimport {stopPropagation} from '../events/Event.js';\n\n/**\n * The enum for available directions.\n *\n * @enum {number}\n */\nconst Direction = {\n  VERTICAL: 0,\n  HORIZONTAL: 1,\n};\n\n/**\n * @typedef {Object} Options\n * @property {string} [className='ol-zoomslider'] CSS class name.\n * @property {number} [duration=200] Animation duration in milliseconds.\n * @property {function(import(\"../MapEvent.js\").default):void} [render] Function called when the control\n * should be re-rendered. This is called in a `requestAnimationFrame` callback.\n * @property {HTMLElement|string} [target] Specify a target if you want the control to be\n * rendered outside of the map's viewport.\n */\n\n/**\n * @classdesc\n * A slider type of control for zooming.\n *\n * Example:\n *\n *     map.addControl(new ZoomSlider());\n *\n * @api\n */\nclass ZoomSlider extends Control {\n  /**\n   * @param {Options} [options] Zoom slider options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    super({\n      target: options.target,\n      element: document.createElement('div'),\n      render: options.render,\n    });\n\n    /**\n     * @type {!Array<import(\"../events.js\").EventsKey>}\n     * @private\n     */\n    this.dragListenerKeys_ = [];\n\n    /**\n     * Will hold the current resolution of the view.\n     *\n     * @type {number|undefined}\n     * @private\n     */\n    this.currentResolution_ = undefined;\n\n    /**\n     * The direction of the slider. Will be determined from actual display of the\n     * container and defaults to Direction.VERTICAL.\n     *\n     * @type {Direction}\n     * @private\n     */\n    this.direction_ = Direction.VERTICAL;\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.dragging_;\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.heightLimit_ = 0;\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.widthLimit_ = 0;\n\n    /**\n     * @type {number|undefined}\n     * @private\n     */\n    this.startX_;\n\n    /**\n     * @type {number|undefined}\n     * @private\n     */\n    this.startY_;\n\n    /**\n     * The calculated thumb size (border box plus margins).  Set when initSlider_\n     * is called.\n     * @type {import(\"../size.js\").Size}\n     * @private\n     */\n    this.thumbSize_ = null;\n\n    /**\n     * Whether the slider is initialized.\n     * @type {boolean}\n     * @private\n     */\n    this.sliderInitialized_ = false;\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.duration_ = options.duration !== undefined ? options.duration : 200;\n\n    const className =\n      options.className !== undefined ? options.className : 'ol-zoomslider';\n    const thumbElement = document.createElement('button');\n    thumbElement.setAttribute('type', 'button');\n    thumbElement.className = className + '-thumb ' + CLASS_UNSELECTABLE;\n    const containerElement = this.element;\n    containerElement.className =\n      className + ' ' + CLASS_UNSELECTABLE + ' ' + CLASS_CONTROL;\n    containerElement.appendChild(thumbElement);\n\n    containerElement.addEventListener(\n      PointerEventType.POINTERDOWN,\n      this.handleDraggerStart_.bind(this),\n      false\n    );\n    containerElement.addEventListener(\n      PointerEventType.POINTERMOVE,\n      this.handleDraggerDrag_.bind(this),\n      false\n    );\n    containerElement.addEventListener(\n      PointerEventType.POINTERUP,\n      this.handleDraggerEnd_.bind(this),\n      false\n    );\n\n    containerElement.addEventListener(\n      EventType.CLICK,\n      this.handleContainerClick_.bind(this),\n      false\n    );\n    thumbElement.addEventListener(EventType.CLICK, stopPropagation, false);\n  }\n\n  /**\n   * Remove the control from its current map and attach it to the new map.\n   * Pass `null` to just remove the control from the current map.\n   * Subclasses may set up event handlers to get notified about changes to\n   * the map here.\n   * @param {import(\"../Map.js\").default|null} map Map.\n   * @api\n   */\n  setMap(map) {\n    super.setMap(map);\n    if (map) {\n      map.render();\n    }\n  }\n\n  /**\n   * Initializes the slider element. This will determine and set this controls\n   * direction_ and also constrain the dragging of the thumb to always be within\n   * the bounds of the container.\n   *\n   * @return {boolean} Initialization successful\n   * @private\n   */\n  initSlider_() {\n    const container = this.element;\n    let containerWidth = container.offsetWidth;\n    let containerHeight = container.offsetHeight;\n    if (containerWidth === 0 && containerHeight === 0) {\n      return (this.sliderInitialized_ = false);\n    }\n\n    const containerStyle = getComputedStyle(container);\n    containerWidth -=\n      parseFloat(containerStyle['paddingRight']) +\n      parseFloat(containerStyle['paddingLeft']);\n    containerHeight -=\n      parseFloat(containerStyle['paddingTop']) +\n      parseFloat(containerStyle['paddingBottom']);\n    const thumb = /** @type {HTMLElement} */ (container.firstElementChild);\n    const thumbStyle = getComputedStyle(thumb);\n    const thumbWidth =\n      thumb.offsetWidth +\n      parseFloat(thumbStyle['marginRight']) +\n      parseFloat(thumbStyle['marginLeft']);\n    const thumbHeight =\n      thumb.offsetHeight +\n      parseFloat(thumbStyle['marginTop']) +\n      parseFloat(thumbStyle['marginBottom']);\n    this.thumbSize_ = [thumbWidth, thumbHeight];\n\n    if (containerWidth > containerHeight) {\n      this.direction_ = Direction.HORIZONTAL;\n      this.widthLimit_ = containerWidth - thumbWidth;\n    } else {\n      this.direction_ = Direction.VERTICAL;\n      this.heightLimit_ = containerHeight - thumbHeight;\n    }\n    return (this.sliderInitialized_ = true);\n  }\n\n  /**\n   * @param {PointerEvent} event The browser event to handle.\n   * @private\n   */\n  handleContainerClick_(event) {\n    const view = this.getMap().getView();\n\n    const relativePosition = this.getRelativePosition_(\n      event.offsetX - this.thumbSize_[0] / 2,\n      event.offsetY - this.thumbSize_[1] / 2\n    );\n\n    const resolution = this.getResolutionForPosition_(relativePosition);\n    const zoom = view.getConstrainedZoom(view.getZoomForResolution(resolution));\n\n    view.animateInternal({\n      zoom: zoom,\n      duration: this.duration_,\n      easing: easeOut,\n    });\n  }\n\n  /**\n   * Handle dragger start events.\n   * @param {PointerEvent} event The drag event.\n   * @private\n   */\n  handleDraggerStart_(event) {\n    if (!this.dragging_ && event.target === this.element.firstElementChild) {\n      const element = /** @type {HTMLElement} */ (\n        this.element.firstElementChild\n      );\n      this.getMap().getView().beginInteraction();\n      this.startX_ = event.clientX - parseFloat(element.style.left);\n      this.startY_ = event.clientY - parseFloat(element.style.top);\n      this.dragging_ = true;\n\n      if (this.dragListenerKeys_.length === 0) {\n        const drag = this.handleDraggerDrag_;\n        const end = this.handleDraggerEnd_;\n        const doc = this.getMap().getOwnerDocument();\n        this.dragListenerKeys_.push(\n          listen(doc, PointerEventType.POINTERMOVE, drag, this),\n          listen(doc, PointerEventType.POINTERUP, end, this)\n        );\n      }\n    }\n  }\n\n  /**\n   * Handle dragger drag events.\n   *\n   * @param {PointerEvent} event The drag event.\n   * @private\n   */\n  handleDraggerDrag_(event) {\n    if (this.dragging_) {\n      const deltaX = event.clientX - this.startX_;\n      const deltaY = event.clientY - this.startY_;\n      const relativePosition = this.getRelativePosition_(deltaX, deltaY);\n      this.currentResolution_ =\n        this.getResolutionForPosition_(relativePosition);\n      this.getMap().getView().setResolution(this.currentResolution_);\n    }\n  }\n\n  /**\n   * Handle dragger end events.\n   * @param {PointerEvent} event The drag event.\n   * @private\n   */\n  handleDraggerEnd_(event) {\n    if (this.dragging_) {\n      const view = this.getMap().getView();\n      view.endInteraction();\n\n      this.dragging_ = false;\n      this.startX_ = undefined;\n      this.startY_ = undefined;\n      this.dragListenerKeys_.forEach(unlistenByKey);\n      this.dragListenerKeys_.length = 0;\n    }\n  }\n\n  /**\n   * Positions the thumb inside its container according to the given resolution.\n   *\n   * @param {number} res The res.\n   * @private\n   */\n  setThumbPosition_(res) {\n    const position = this.getPositionForResolution_(res);\n    const thumb = /** @type {HTMLElement} */ (this.element.firstElementChild);\n\n    if (this.direction_ == Direction.HORIZONTAL) {\n      thumb.style.left = this.widthLimit_ * position + 'px';\n    } else {\n      thumb.style.top = this.heightLimit_ * position + 'px';\n    }\n  }\n\n  /**\n   * Calculates the relative position of the thumb given x and y offsets.  The\n   * relative position scales from 0 to 1.  The x and y offsets are assumed to be\n   * in pixel units within the dragger limits.\n   *\n   * @param {number} x Pixel position relative to the left of the slider.\n   * @param {number} y Pixel position relative to the top of the slider.\n   * @return {number} The relative position of the thumb.\n   * @private\n   */\n  getRelativePosition_(x, y) {\n    let amount;\n    if (this.direction_ === Direction.HORIZONTAL) {\n      amount = x / this.widthLimit_;\n    } else {\n      amount = y / this.heightLimit_;\n    }\n    return clamp(amount, 0, 1);\n  }\n\n  /**\n   * Calculates the corresponding resolution of the thumb given its relative\n   * position (where 0 is the minimum and 1 is the maximum).\n   *\n   * @param {number} position The relative position of the thumb.\n   * @return {number} The corresponding resolution.\n   * @private\n   */\n  getResolutionForPosition_(position) {\n    const fn = this.getMap().getView().getResolutionForValueFunction();\n    return fn(1 - position);\n  }\n\n  /**\n   * Determines the relative position of the slider for the given resolution.  A\n   * relative position of 0 corresponds to the minimum view resolution.  A\n   * relative position of 1 corresponds to the maximum view resolution.\n   *\n   * @param {number} res The resolution.\n   * @return {number} The relative position value (between 0 and 1).\n   * @private\n   */\n  getPositionForResolution_(res) {\n    const fn = this.getMap().getView().getValueForResolutionFunction();\n    return clamp(1 - fn(res), 0, 1);\n  }\n\n  /**\n   * Update the zoomslider element.\n   * @param {import(\"../MapEvent.js\").default} mapEvent Map event.\n   * @override\n   */\n  render(mapEvent) {\n    if (!mapEvent.frameState) {\n      return;\n    }\n    if (!this.sliderInitialized_ && !this.initSlider_()) {\n      return;\n    }\n    const res = mapEvent.frameState.viewState.resolution;\n    this.currentResolution_ = res;\n    this.setThumbPosition_(res);\n  }\n}\n\nexport default ZoomSlider;\n", "/**\n * @module ol/control/ZoomToExtent\n */\nimport Control from './Control.js';\nimport EventType from '../events/EventType.js';\nimport {CLASS_CONTROL, CLASS_UNSELECTABLE} from '../css.js';\nimport {fromExtent as polygonFromExtent} from '../geom/Polygon.js';\n\n/**\n * @typedef {Object} Options\n * @property {string} [className='ol-zoom-extent'] Class name.\n * @property {HTMLElement|string} [target] Specify a target if you want the control\n * to be rendered outside of the map's viewport.\n * @property {string|HTMLElement} [label='E'] Text label to use for the button.\n * Instead of text, also an element (e.g. a `span` element) can be used.\n * @property {string} [tipLabel='Fit to extent'] Text label to use for the button tip.\n * @property {import(\"../extent.js\").Extent} [extent] The extent to zoom to. If undefined the validity\n * extent of the view projection is used.\n */\n\n/**\n * @classdesc\n * A button control which, when pressed, changes the map view to a specific\n * extent. To style this control use the css selector `.ol-zoom-extent`.\n *\n * @api\n */\nclass ZoomToExtent extends Control {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    super({\n      element: document.createElement('div'),\n      target: options.target,\n    });\n\n    /**\n     * @type {?import(\"../extent.js\").Extent|null}\n     * @protected\n     */\n    this.extent = options.extent ? options.extent : null;\n\n    const className =\n      options.className !== undefined ? options.className : 'ol-zoom-extent';\n\n    const label = options.label !== undefined ? options.label : 'E';\n    const tipLabel =\n      options.tipLabel !== undefined ? options.tipLabel : 'Fit to extent';\n    const button = document.createElement('button');\n    button.setAttribute('type', 'button');\n    button.title = tipLabel;\n    button.appendChild(\n      typeof label === 'string' ? document.createTextNode(label) : label\n    );\n\n    button.addEventListener(\n      EventType.CLICK,\n      this.handleClick_.bind(this),\n      false\n    );\n\n    const cssClasses =\n      className + ' ' + CLASS_UNSELECTABLE + ' ' + CLASS_CONTROL;\n    const element = this.element;\n    element.className = cssClasses;\n    element.appendChild(button);\n  }\n\n  /**\n   * @param {MouseEvent} event The event to handle\n   * @private\n   */\n  handleClick_(event) {\n    event.preventDefault();\n    this.handleZoomToExtent();\n  }\n\n  /**\n   * @protected\n   */\n  handleZoomToExtent() {\n    const map = this.getMap();\n    const view = map.getView();\n    const extent = !this.extent\n      ? view.getProjection().getExtent()\n      : this.extent;\n    view.fitInternal(polygonFromExtent(extent));\n  }\n}\n\nexport default ZoomToExtent;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AACF;AAKA,IAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB,iBAAiB;AACnB;AA+CA,IAAM,aAAN,cAAyB,gBAAQ;AAAA;AAAA;AAAA;AAAA,EAI/B,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM;AAAA,MACJ,SAAS,SAAS,cAAc,KAAK;AAAA,MACrC,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAKD,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,SAAK,QAAQ,QAAQ,SAAS,SAAY,QAAQ,OAAO;AAMzD,SAAK,UAAU,QAAQ;AAMvB,SAAK,kBAAkB;AAKvB,SAAK,8BAA8B,KAAK,uBAAuB,KAAK,IAAI;AAMxE,SAAK,gBACH,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAMxD,SAAK,qBAAqB,CAAC;AAM3B,SAAK,mBACH,QAAQ,oBAAoB,SACxB,QAAQ,gBAAgB,MAAM,GAAG,IACjC,CAAC,KAAK,gBAAgB,OAAO;AAMnC,SAAK,qBACH,QAAQ,sBAAsB,SAC1B,QAAQ,kBAAkB,MAAM,GAAG,IACnC,CAAC,KAAK,gBAAgB,QAAQ;AAEpC,UAAM,QAAQ,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAM5D,SAAK,aACH,OAAO,UAAU,WAAW,SAAS,eAAe,KAAK,IAAI;AAE/D,UAAM,cACJ,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAM5D,SAAK,mBACH,OAAO,gBAAgB,WACnB,SAAS,eAAe,WAAW,IACnC;AAEN,UAAM,WAAW,QAAQ,WAAW,QAAQ,WAAW;AAMvD,SAAK,UAAU,SAAS,cAAc,QAAQ;AAC9C,SAAK,QAAQ,QAAQ;AACrB,SAAK,QAAQ,aAAa,QAAQ,QAAQ;AAC1C,SAAK,QAAQ,YAAY,KAAK,UAAU;AACxC,SAAK,QAAQ;AAAA,MACX,kBAAU;AAAA,MACV,KAAK,aAAa,KAAK,IAAI;AAAA,MAC3B;AAAA,IACF;AACA,SAAK,cAAc,KAAK,SAAS,KAAK,eAAe;AAErD,SAAK,QAAQ,YAAY,GAAG,KAAK,aAAa,IAAI,kBAAkB,IAAI,aAAa;AACrF,SAAK,QAAQ,YAAY,KAAK,OAAO;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,OAAO;AAClB,UAAM,eAAe;AACrB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,MAAM,IAAI,iBAAiB;AACjC,QAAI,CAAC,sBAAsB,GAAG,GAAG;AAC/B;AAAA,IACF;AACA,QAAI,aAAa,GAAG,GAAG;AACrB,qBAAe,GAAG;AAAA,IACpB,OAAO;AACL,UAAI;AACJ,UAAI,KAAK,SAAS;AAChB,kBACE,OAAO,KAAK,YAAY,WACpB,IAAI,eAAe,KAAK,OAAO,IAC/B,KAAK;AAAA,MACb,OAAO;AACL,kBAAU,IAAI,iBAAiB;AAAA,MACjC;AACA,UAAI,KAAK,OAAO;AACd,kCAA0B,OAAO;AAAA,MACnC,OAAO;AACL,0BAAkB,OAAO;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B;AACxB,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,kBAAkB,KAAK;AAC7B,SAAK,kBAAkB,aAAa,IAAI,iBAAiB,CAAC;AAC1D,QAAI,oBAAoB,KAAK,iBAAiB;AAC5C,WAAK,cAAc,KAAK,SAAS,KAAK,eAAe;AACrD,UAAI,KAAK,iBAAiB;AACxB,oBAAY,KAAK,kBAAkB,KAAK,UAAU;AAClD,aAAK,cAAc,oBAAoB,eAAe;AAAA,MACxD,OAAO;AACL,oBAAY,KAAK,YAAY,KAAK,gBAAgB;AAClD,aAAK,cAAc,oBAAoB,eAAe;AAAA,MACxD;AACA,UAAI,WAAW;AAAA,IACjB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,SAAS,YAAY;AACjC,QAAI,YAAY;AACd,cAAQ,UAAU,OAAO,GAAG,KAAK,kBAAkB;AACnD,cAAQ,UAAU,IAAI,GAAG,KAAK,gBAAgB;AAAA,IAChD,OAAO;AACL,cAAQ,UAAU,OAAO,GAAG,KAAK,gBAAgB;AACjD,cAAQ,UAAU,IAAI,GAAG,KAAK,kBAAkB;AAAA,IAClD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,KAAK;AACV,UAAM,SAAS,KAAK,OAAO;AAC3B,QAAI,QAAQ;AACV,aAAO;AAAA,QACL,oBAAY;AAAA,QACZ,KAAK;AAAA,MACP;AAAA,IACF;AAEA,UAAM,OAAO,GAAG;AAEhB,SAAK,uBAAuB;AAC5B,QAAI,KAAK;AACP,UAAI;AAAA,QACF,oBAAY;AAAA,QACZ,KAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACvB,UAAM,YAAY,KAAK;AACvB,aAAS,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI,EAAE,GAAG;AAClD,oBAAc,UAAU,CAAC,CAAC;AAAA,IAC5B;AACA,cAAU,SAAS;AAEnB,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,KAAK;AACP,YAAM,MAAM,IAAI,iBAAiB;AACjC,UAAI,sBAAsB,GAAG,GAAG;AAC9B,aAAK,QAAQ,UAAU,OAAO,iBAAiB;AAAA,MACjD,OAAO;AACL,aAAK,QAAQ,UAAU,IAAI,iBAAiB;AAAA,MAC9C;AAEA,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,kBAAU;AAAA,UACR,OAAO,KAAK,OAAO,CAAC,GAAG,KAAK,yBAAyB,IAAI;AAAA,QAC3D;AAAA,MACF;AACA,WAAK,wBAAwB;AAAA,IAC/B;AAAA,EACF;AACF;AAMA,SAAS,sBAAsB,KAAK;AAClC,QAAM,OAAO,IAAI;AACjB,SAAO,CAAC,EACN,KAAK,yBAAyB,KAC7B,KAAK,qBAAqB,IAAI;AAEnC;AAMA,SAAS,aAAa,KAAK;AACzB,SAAO,CAAC,EAAE,IAAI,oBAAoB,KAAK,IAAI;AAC7C;AAMA,SAAS,kBAAkB,SAAS;AAClC,MAAI,QAAQ,mBAAmB;AAC7B,YAAQ,kBAAkB;AAAA,EAC5B,WAAW,QAAQ,yBAAyB,GAAG;AAC7C,YAAQ,yBAAyB,EAAE;AAAA,EACrC;AACF;AAMA,SAAS,0BAA0B,SAAS;AAC1C,MAAI,QAAQ,yBAAyB,GAAG;AACtC,YAAQ,yBAAyB,EAAE;AAAA,EACrC,OAAO;AACL,sBAAkB,OAAO;AAAA,EAC3B;AACF;AAMA,SAAS,eAAe,KAAK;AAC3B,MAAI,IAAI,gBAAgB;AACtB,QAAI,eAAe;AAAA,EACrB,WAAW,IAAI,sBAAsB,GAAG;AACtC,QAAI,sBAAsB,EAAE;AAAA,EAC9B;AACF;AAEA,IAAO,qBAAQ;;;ACzXf,IAAM,aAAa;AAKnB,IAAM,oBAAoB;AA0C1B,IAAM,gBAAN,cAA4B,gBAAQ;AAAA;AAAA;AAAA;AAAA,EAIlC,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM,UAAU,SAAS,cAAc,KAAK;AAC5C,YAAQ,YACN,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAExD,UAAM;AAAA,MACJ;AAAA,MACA,QAAQ,QAAQ;AAAA,MAChB,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAKD,SAAK;AAKL,SAAK;AAKL,SAAK;AAEL,SAAK,kBAAkB,YAAY,KAAK,wBAAwB;AAEhE,QAAI,QAAQ,kBAAkB;AAC5B,WAAK,oBAAoB,QAAQ,gBAAgB;AAAA,IACnD;AACA,QAAI,QAAQ,YAAY;AACtB,WAAK,cAAc,QAAQ,UAAU;AAAA,IACvC;AAMA,SAAK,oBAAoB,QAAQ,gBAAgB;AAMjD,SAAK,eAAe,KAAK,oBAAoB,QAAQ,cAAc;AAMnE,SAAK,gBAAgB,QAAQ;AAM7B,SAAK,iBAAiB;AAMtB,SAAK,aAAa;AAMlB,SAAK,SAAS,QAAQ,UAAU,QAAQ,QAAQ;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAKA,2BAA2B;AACzB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,sBAAsB;AACpB;AAAA;AAAA,MACE,KAAK,IAAI,iBAAiB;AAAA;AAAA,EAE9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB;AACd;AAAA;AAAA,MACE,KAAK,IAAI,UAAU;AAAA;AAAA,EAEvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,OAAO;AACrB,UAAM,MAAM,KAAK,OAAO;AACxB,SAAK,YAAY,IAAI,cAAc,KAAK,CAAC;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,OAAO;AACpB,SAAK,YAAY,IAAI;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,KAAK;AACV,UAAM,OAAO,GAAG;AAChB,QAAI,KAAK;AACP,YAAM,WAAW,IAAI,YAAY;AACjC,WAAK,aAAa;AAAA,QAChB,OAAO,UAAUA,mBAAU,aAAa,KAAK,iBAAiB,IAAI;AAAA,MACpE;AACA,UAAI,KAAK,mBAAmB;AAC1B,aAAK,aAAa;AAAA,UAChB,OAAO,UAAUA,mBAAU,YAAY,KAAK,gBAAgB,IAAI;AAAA,QAClE;AAAA,MACF;AACA,WAAK,YAAY,IAAI;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,oBAAoB,QAAQ;AAC1B,SAAK,IAAI,mBAAmB,MAAM;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,YAAY;AACxB,SAAK,IAAI,YAAY,IAAc,UAAU,CAAC;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACjB,QAAI,OAAO,KAAK;AAChB,QAAI,SAAS,KAAK,gBAAgB;AAChC,UAAI,CAAC,KAAK,YAAY;AACpB,cAAM,aAAa,KAAK,cAAc;AACtC,YAAI,YAAY;AACd,eAAK,aAAa;AAAA,YAChB,KAAK;AAAA,YACL;AAAA,UACF;AAAA,QACF,OAAO;AACL,eAAK,aAAa;AAAA,QACpB;AAAA,MACF;AACA,YAAM,MAAM,KAAK,OAAO;AACxB,YAAM,aAAa,IAAI,+BAA+B,KAAK;AAC3D,UAAI,YAAY;AACd,cAAM,iBAAiB,kBAAkB;AACzC,YAAI,gBAAgB;AAClB,eAAK,aAAa;AAAA,YAChB,KAAK;AAAA,YACL;AAAA,UACF;AAAA,QACF;AACA,aAAK,WAAW,YAAY,UAAU;AACtC,YAAI,KAAK,QAAQ;AACf,gBAAM,aACJ,kBAAkB,KAAK,cAAc,KAAK,KAAK;AACjD,gBAAM,YAAY,UAAU;AAAA,QAC9B;AACA,cAAM,mBAAmB,KAAK,oBAAoB;AAClD,YAAI,kBAAkB;AACpB,iBAAO,iBAAiB,UAAU;AAAA,QACpC,OAAO;AACL,iBAAO,WAAW,SAAS;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,KAAK,iBAAiB,SAAS,KAAK,eAAe;AACtD,WAAK,QAAQ,YAAY;AACzB,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,UAAU;AACf,UAAM,aAAa,SAAS;AAC5B,QAAI,CAAC,YAAY;AACf,WAAK,iBAAiB;AAAA,IACxB,OAAO;AACL,UAAI,KAAK,kBAAkB,WAAW,UAAU,YAAY;AAC1D,aAAK,iBAAiB,WAAW,UAAU;AAC3C,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,wBAAQ;;;ACrRf,IAAM,YAAY;AAOlB,IAAM,YAAY;AA6BlB,IAAM,cAAN,cAA0B,gBAAQ;AAAA;AAAA;AAAA;AAAA,EAIhC,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM;AAAA,MACJ,SAAS,SAAS,cAAc,KAAK;AAAA,MACrC,QAAQ,QAAQ;AAAA,MAChB,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAKD,SAAK,8BAA8B,KAAK,uBAAuB,KAAK,IAAI;AAMxE,SAAK,aACH,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAMxD,SAAK,eACH,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAE5D,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,aAAa;AAAA,IACpB;AAMA,SAAK,kBACH,QAAQ,mBAAmB,SAAY,QAAQ,iBAAiB;AAMlE,SAAK,cAAc;AAEnB,UAAM,YACJ,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAExD,UAAM,WACJ,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAEtD,UAAM,gBACJ,QAAQ,kBAAkB,SAAY,QAAQ,gBAAgB;AAEhE,QAAI,OAAO,kBAAkB,UAAU;AAKrC,WAAK,iBAAiB,SAAS,cAAc,MAAM;AACnD,WAAK,eAAe,cAAc;AAAA,IACpC,OAAO;AACL,WAAK,iBAAiB;AAAA,IACxB;AAEA,UAAM,QAAQ,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAE5D,QAAI,OAAO,UAAU,UAAU;AAK7B,WAAK,SAAS,SAAS,cAAc,MAAM;AAC3C,WAAK,OAAO,cAAc;AAAA,IAC5B,OAAO;AACL,WAAK,SAAS;AAAA,IAChB;AAEA,UAAM,cACJ,KAAK,gBAAgB,CAAC,KAAK,aAAa,KAAK,iBAAiB,KAAK;AACrE,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,aAAa,QAAQ,QAAQ;AACpC,WAAO,QAAQ;AACf,WAAO,YAAY,WAAW;AAE9B,WAAO;AAAA,MACL,kBAAU;AAAA,MACV,KAAK,aAAa,KAAK,IAAI;AAAA,MAC3B;AAAA,IACF;AAMA,SAAK,YAAY,SAAS,cAAc,KAAK;AAC7C,SAAK,UAAU,YAAY;AAO3B,SAAK,QAAQ,QAAQ;AAErB,UAAM,QAAQ,IAAI,YAAI;AAAA,MACpB,MAAM,QAAQ;AAAA,MACd,UAAU,IAAI,mBAAW;AAAA,MACzB,cAAc,IAAI,mBAAW;AAAA,IAC/B,CAAC;AAMD,SAAK,SAAS;AAEd,QAAI,QAAQ,QAAQ;AAClB,cAAQ,OAAO,QAAQ,SAAU,OAAO;AACtC,cAAM,SAAS,KAAK;AAAA,MACtB,CAAC;AAAA,IACH;AAEA,UAAM,MAAM,SAAS,cAAc,KAAK;AACxC,QAAI,YAAY;AAChB,QAAI,MAAM,YAAY;AAMtB,SAAK,cAAc,IAAI,gBAAQ;AAAA,MAC7B,UAAU,CAAC,GAAG,CAAC;AAAA,MACf,aAAa;AAAA,MACb,SAAS;AAAA,IACX,CAAC;AACD,SAAK,OAAO,WAAW,KAAK,WAAW;AAEvC,UAAM,aACJ,YACA,MACA,qBACA,MACA,iBACC,KAAK,cAAc,KAAK,eAAe,MAAM,kBAAkB,OAC/D,KAAK,eAAe,KAAK;AAC5B,UAAM,UAAU,KAAK;AACrB,YAAQ,YAAY;AACpB,YAAQ,YAAY,KAAK,SAAS;AAClC,YAAQ,YAAY,MAAM;AAI1B,UAAM,QAAQ;AAEd,UAAM,UAAU,KAAK;AACrB,UAAM,aAAa,KAAK,YAAY,WAAW;AAI/C,UAAM,8BAA8B,SAAU,eAAe;AAC3D,aAAO;AAAA,QACL,SAAS,cAAc;AAAA,QACvB,SAAS,cAAc;AAAA,MACzB;AAAA,IACF;AAEA,UAAM,OAAO,SAAU,OAAO;AAC5B,YAAM;AAAA;AAAA,QAA6B,4BAA4B,KAAK;AAAA;AACpE,YAAM,cAAc,MAAM;AAAA;AAAA,QACG;AAAA,MAC7B;AAEA,cAAQ,YAAY,WAAW;AAAA,IACjC;AAEA,UAAM,YAAY,SAAU,OAAO;AACjC,YAAM,cAAc,MAAM,2BAA2B,KAAK;AAE1D,YAAM,OAAO,EAAE,QAAQ,EAAE,kBAAkB,WAAW;AAEtD,aAAO,oBAAoB,aAAa,IAAI;AAC5C,aAAO,oBAAoB,WAAW,SAAS;AAAA,IACjD;AAIA,eAAW,iBAAiB,aAAa,WAAY;AACnD,aAAO,iBAAiB,aAAa,IAAI;AACzC,aAAO,iBAAiB,WAAW,SAAS;AAAA,IAC9C,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,KAAK;AACV,UAAM,SAAS,KAAK,OAAO;AAC3B,QAAI,QAAQ,QAAQ;AAClB;AAAA,IACF;AACA,QAAI,QAAQ;AACV,YAAM,UAAU,OAAO,QAAQ;AAC/B,UAAI,SAAS;AACX,aAAK,YAAY,OAAO;AAAA,MAC1B;AACA,WAAK,OAAO,UAAU,IAAI;AAAA,IAC5B;AACA,UAAM,OAAO,GAAG;AAEhB,QAAI,KAAK;AACP,WAAK,OAAO,UAAU,KAAK,SAAS;AACpC,WAAK,aAAa;AAAA,QAChB;AAAA,UACE;AAAA,UACA,wBAAgB;AAAA,UAChB,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAEA,YAAM,OAAO,IAAI,QAAQ;AACzB,UAAI,MAAM;AACR,aAAK,UAAU,IAAI;AACnB,YAAI,KAAK,MAAM,GAAG;AAChB,eAAK,OAAO,WAAW;AACvB,eAAK,aAAa;AAAA,QACpB;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,OAAO,WAAW,GAAG;AAC7B,aAAK,+BAA+B;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,yBAAyB,OAAO;AAC9B,QAAI,MAAM,QAAQ,oBAAY,MAAM;AAClC,YAAM;AAAA;AAAA,QACJ,MAAM;AAAA;AAER,UAAI,SAAS;AACX,aAAK,YAAY,OAAO;AAAA,MAC1B;AACA,YAAM,UAAU,KAAK,OAAO,EAAE,QAAQ;AACtC,WAAK,UAAU,OAAO;AAAA,IACxB,WACE,CAAC,KAAK,OAAO,WAAW,MACvB,MAAM,QAAQ,oBAAY,UAAU,MAAM,QAAQ,oBAAY,OAC/D;AACA,WAAK,OAAO,WAAW;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,MAAM;AACd,QAAI,CAAC,KAAK,OAAO;AAEf,YAAM,UAAU,IAAI,aAAK;AAAA,QACvB,YAAY,KAAK,cAAc;AAAA,MACjC,CAAC;AACD,WAAK,OAAO,QAAQ,OAAO;AAAA,IAC7B;AAEA,SAAK;AAAA,MACH,qBAAa;AAAA,MACb,KAAK;AAAA,IACP;AAEA,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,MAAM;AAChB,SAAK;AAAA,MACH,qBAAa;AAAA,MACb,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB;AACvB,QAAI,KAAK,iBAAiB;AACxB,WAAK,OAAO,QAAQ,EAAE,YAAY,KAAK,OAAO,EAAE,QAAQ,EAAE,YAAY,CAAC;AAAA,IACzE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,kBAAkB;AAChB,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,QAAQ,KAAK;AAEnB,QAAI,CAAC,IAAI,WAAW,KAAK,CAAC,MAAM,WAAW,GAAG;AAC5C;AAAA,IACF;AAEA,UAAM;AAAA;AAAA,MAAoD,IAAI,QAAQ;AAAA;AAEtE,UAAM,OAAO,IAAI,QAAQ;AACzB,UAAM,SAAS,KAAK,wBAAwB,OAAO;AAEnD,QAAI,KAAK,eAAe,OAAa,QAAQ,KAAK,WAAW,GAAG;AAE9D;AAAA,IACF;AACA,SAAK,cAAc;AAEnB,UAAM;AAAA;AAAA,MACJ,MAAM,QAAQ;AAAA;AAGhB,UAAM,SAAS,MAAM,QAAQ;AAC7B,UAAM,WAAW,OAAO,wBAAwB,SAAS;AAEzD,UAAM,eAAe,MAAM;AAAA,MACzB,WAAW,MAAM;AAAA,IACnB;AACA,UAAM,mBAAmB,MAAM;AAAA,MAC7B,eAAe,MAAM;AAAA,IACvB;AAEA,UAAM,WAAW,KAAK,IAAI,aAAa,CAAC,IAAI,iBAAiB,CAAC,CAAC;AAC/D,UAAM,YAAY,KAAK,IAAI,aAAa,CAAC,IAAI,iBAAiB,CAAC,CAAC;AAEhE,UAAM,aAAa,UAAU,CAAC;AAC9B,UAAM,cAAc,UAAU,CAAC;AAE/B,QACE,WAAW,aAAa,aACxB,YAAY,cAAc,aAC1B,WAAW,aAAa,aACxB,YAAY,cAAc,WAC1B;AACA,WAAK,aAAa;AAAA,IACpB,WAAW,CAAC,eAAe,UAAU,MAAM,GAAG;AAC5C,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe;AACb,QAAI,cAAc,KAAK,cAAc,GAAG;AACtC;AAAA,IACF;AAEA,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,QAAQ,KAAK;AAEnB,UAAM;AAAA;AAAA,MAAoD,IAAI,QAAQ;AAAA;AAEtE,UAAM,OAAO,IAAI,QAAQ;AACzB,UAAM,SAAS,KAAK,wBAAwB,OAAO;AAEnD,UAAM,SAAS,MAAM,QAAQ;AAK7B,UAAM,QAAQ,KAAK,IAAI,YAAY,SAAS,IAAI,KAAK;AACrD,UAAM,QAAQ,KAAK,KAAK,IAAI,GAAG,QAAQ,CAAC,IAAI;AAC5C,oBAAgB,QAAQ,KAAK;AAC7B,WAAO,YAAY,WAAkB,MAAM,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,QAAQ,KAAK;AAEnB,UAAM,OAAO,IAAI,QAAQ;AAEzB,UAAM,SAAS,MAAM,QAAQ;AAE7B,WAAO,kBAAkB,KAAK,kBAAkB,CAAC;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa;AACX,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,QAAQ,KAAK;AAEnB,QAAI,CAAC,IAAI,WAAW,KAAK,CAAC,MAAM,WAAW,GAAG;AAC5C;AAAA,IACF;AAEA,UAAM;AAAA;AAAA,MAAoD,IAAI,QAAQ;AAAA;AAEtE,UAAM,OAAO,IAAI,QAAQ;AAEzB,UAAM,SAAS,MAAM,QAAQ;AAE7B,UAAM,WAAW,KAAK,kBAAkB,IAAI,CAAC,KAAK,YAAY;AAE9D,UAAM,UAAU,KAAK;AACrB,UAAM,MAAM,KAAK,YAAY,WAAW;AACxC,UAAM,SAAS,KAAK,kBAAkB;AACtC,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,eAAe,OAAO,cAAc;AAC1C,UAAM,QAAS,QAAQ,CAAC,IAAI,aAAc;AAC1C,UAAM,SAAU,QAAQ,CAAC,IAAI,aAAc;AAG3C,YAAQ,YAAY,MAAM;AAG1B,QAAI,KAAK;AACP,UAAI,MAAM,QAAQ,QAAQ;AAC1B,UAAI,MAAM,SAAS,SAAS;AAC5B,YAAM,YAAY,YAAY,WAAW;AACzC,UAAI,MAAM,YAAY;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,iCAAiC;AAC/B,QAAI,KAAK,qBAAqB;AAC5B;AAAA,IACF;AACA,SAAK,sBAAsB;AAAA,MACzB,KAAK;AAAA,MACL,qBAAa;AAAA,MACb,SAAU,OAAO;AACf,eAAO,KAAK;AACZ,aAAK,WAAW;AAAA,MAClB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,OAAO;AAClB,UAAM,eAAe;AACrB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,SAAK,QAAQ,UAAU,OAAO,eAAe;AAC7C,QAAI,KAAK,YAAY;AACnB,kBAAY,KAAK,gBAAgB,KAAK,MAAM;AAAA,IAC9C,OAAO;AACL,kBAAY,KAAK,QAAQ,KAAK,cAAc;AAAA,IAC9C;AACA,SAAK,aAAa,CAAC,KAAK;AAIxB,UAAM,QAAQ,KAAK;AACnB,QAAI,CAAC,KAAK,YAAY;AACpB,UAAI,MAAM,WAAW,GAAG;AACtB,aAAK,cAAc;AACnB,cAAM,OAAO;AACb;AAAA,MACF;AACA,YAAM,WAAW;AACjB,WAAK,aAAa;AAClB,WAAK,+BAA+B;AAAA,IACtC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,aAAa;AAC1B,QAAI,KAAK,iBAAiB,aAAa;AACrC;AAAA,IACF;AACA,SAAK,eAAe;AACpB,SAAK,QAAQ,UAAU,OAAO,kBAAkB;AAChD,QAAI,CAAC,eAAe,KAAK,YAAY;AACnC,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,WAAW;AACtB,QAAI,CAAC,KAAK,gBAAgB,KAAK,eAAe,WAAW;AACvD;AAAA,IACF;AACA,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,gBAAgB;AAChC,QAAI,KAAK,oBAAoB,gBAAgB;AAC3C;AAAA,IACF;AACA,SAAK,kBAAkB;AACvB,QAAI,KAAK,OAAO,EAAE,QAAQ,EAAE,YAAY,MAAM,GAAG;AAC/C,UAAI,KAAK,iBAAiB;AACxB,aAAK,uBAAuB;AAAA,MAC9B,OAAO;AACL,aAAK,OAAO,QAAQ,EAAE,YAAY,CAAC;AAAA,MACrC;AACA,WAAK,cAAc;AACnB,WAAK,gBAAgB;AACrB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAAA,EAClB;AACF;AAEA,IAAO,sBAAQ;;;AC3pBf,IAAM,aAAa;AAWnB,IAAM,iBAAiB,CAAC,GAAG,GAAG,CAAC;AAM/B,IAAM,cAAc,OAAO;AA+C3B,IAAM,YAAN,cAAwB,gBAAQ;AAAA;AAAA;AAAA;AAAA,EAI9B,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM,UAAU,SAAS,cAAc,KAAK;AAC5C,YAAQ,MAAM,gBAAgB;AAE9B,UAAM;AAAA,MACJ;AAAA,MACA,QAAQ,QAAQ;AAAA,MAChB,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAKD,SAAK;AAKL,SAAK;AAKL,SAAK;AAEL,UAAM,YACJ,QAAQ,cAAc,SAClB,QAAQ,YACR,QAAQ,MACR,iBACA;AAMN,SAAK,gBAAgB,SAAS,cAAc,KAAK;AACjD,SAAK,cAAc,YAAY,YAAY;AAE3C,SAAK,QAAQ,YAAY,YAAY,MAAM;AAC3C,SAAK,QAAQ,YAAY,KAAK,aAAa;AAM3C,SAAK,aAAa;AAMlB,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAMrE,SAAK,YAAY,QAAQ;AAMzB,SAAK,mBAAmB;AAMxB,SAAK,iBAAiB;AAMtB,SAAK,gBAAgB;AAErB,SAAK,kBAAkB,YAAY,KAAK,mBAAmB;AAE3D,SAAK,SAAS,QAAQ,SAAS,QAAQ;AAMvC,SAAK,YAAY,QAAQ,OAAO;AAMhC,SAAK,iBAAiB,QAAQ,SAAS;AAMvC,SAAK,gBAAgB,QAAQ,QAAQ;AAMrC,SAAK,OAAO,QAAQ,OAAO;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW;AACT,WAAO,KAAK,IAAI,UAAU;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB;AACpB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO;AACd,SAAK,IAAI,YAAY,KAAK;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,KAAK;AACV,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,UAAM,YAAY,KAAK;AAEvB,QAAI,CAAC,WAAW;AACd,UAAI,KAAK,kBAAkB;AACzB,aAAK,QAAQ,MAAM,UAAU;AAC7B,aAAK,mBAAmB;AAAA,MAC1B;AACA;AAAA,IACF;AAEA,UAAM,SAAS,UAAU;AACzB,UAAM,aAAa,UAAU;AAC7B,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,uBAAuB,SAAS,YAAY,YAAY;AAC9D,QAAI,kBAAkB;AAAA,MACpB;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,IACF;AAEA,UAAM,WACH,KAAK,aAAa,KAAK,QAAQ,eAAgB;AAElD,UAAM,WACJ,KAAK,cAAc,SACd,KAAK,aAAa,KAAK,QAAQ,eAAgB,cAChD;AAEN,QAAI,eAAe,WAAW;AAC9B,QAAI,SAAS;AACb,QAAI,SAAS,WAAW;AACtB,YAAM,kBAAkB,gBAAgB;AACxC,sBAAgB;AAChB,UAAI,eAAe,kBAAkB,IAAI;AACvC,iBAAS;AACT,2BAAmB;AAAA,MACrB,WAAW,eAAe,iBAAiB;AACzC,iBAAS;AACT,2BAAmB;AAAA,MACrB,OAAO;AACL,iBAAS;AAAA,MACX;AAAA,IACF,WAAW,SAAS,YAAY;AAC9B,UAAI,eAAe,QAAQ;AACzB,iBAAS;AACT,2BAAmB;AAAA,MACrB,WAAW,eAAe,UAAU;AAClC,iBAAS;AACT,2BAAmB;AAAA,MACrB,OAAO;AACL,iBAAS;AACT,2BAAmB;AAAA,MACrB;AAAA,IACF,WAAW,SAAS,YAAY;AAC9B,yBAAmB;AACnB,eAAS;AAAA,IACX,WAAW,SAAS,UAAU;AAC5B,UAAI,eAAe,MAAO;AACxB,iBAAS;AACT,2BAAmB;AAAA,MACrB,WAAW,eAAe,GAAG;AAC3B,iBAAS;AACT,2BAAmB;AAAA,MACrB,WAAW,eAAe,KAAM;AAC9B,iBAAS;AAAA,MACX,OAAO;AACL,iBAAS;AACT,2BAAmB;AAAA,MACrB;AAAA,IACF,WAAW,SAAS,MAAM;AACxB,UAAI,eAAe,QAAQ;AACzB,iBAAS;AACT,2BAAmB;AAAA,MACrB,WAAW,eAAe,UAAU;AAClC,iBAAS;AACT,2BAAmB;AAAA,MACrB,OAAO;AACL,iBAAS;AACT,2BAAmB;AAAA,MACrB;AAAA,IACF,OAAO;AACL,aAAO,OAAO,EAAE;AAAA,IAClB;AAEA,QAAI,IAAI,IAAI,KAAK,MAAM,KAAK,IAAI,WAAW,eAAe,IAAI,KAAK,IAAI,EAAE,CAAC;AAC1E,QAAI,OAAO,OAAO;AAClB,QAAI,eAAe,eAAe;AAClC,WAAO,MAAM;AACX,qBAAe,KAAK,MAAM,IAAI,CAAC;AAC/B,YAAM,UAAU,KAAK,IAAI,IAAI,YAAY;AACzC,cAAQ,gBAAiB,IAAI,IAAK,KAAK,CAAC,IAAI;AAC5C,cAAQ,KAAK,MAAM,QAAQ,eAAe;AAC1C,UAAI,MAAM,KAAK,GAAG;AAChB,aAAK,QAAQ,MAAM,UAAU;AAC7B,aAAK,mBAAmB;AACxB;AAAA,MACF;AACA,UAAI,aAAa,UAAa,SAAS,UAAU;AAC/C,gBAAQ;AACR,gBAAQ;AACR,uBAAe;AACf;AAAA,MACF,WAAW,SAAS,UAAU;AAC5B;AAAA,MACF;AACA,sBAAgB;AAChB,sBAAgB;AAChB,6BAAuB;AACvB,QAAE;AAAA,IACJ;AACA,UAAM,OAAO,KAAK,YACd,KAAK,eAAe,OAAO,OAAO,MAAM,IACxC,MAAM,QAAQ,eAAe,IAAI,CAAC,eAAe,CAAC,IAAI,MAAM;AAEhE,QAAI,KAAK,iBAAiB,MAAM;AAC9B,WAAK,cAAc,YAAY;AAC/B,WAAK,gBAAgB;AAAA,IACvB;AAEA,QAAI,KAAK,kBAAkB,OAAO;AAChC,WAAK,cAAc,MAAM,QAAQ,QAAQ;AACzC,WAAK,iBAAiB;AAAA,IACxB;AAEA,QAAI,CAAC,KAAK,kBAAkB;AAC1B,WAAK,QAAQ,MAAM,UAAU;AAC7B,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,OAAO,OAAO,QAAQ;AACnC,UAAM,kBAAkB,KAAK,sBAAsB;AACnD,UAAM,WACJ,kBAAkB,IACd,KAAK,MAAM,IAAI,eAAe,EAAE,eAAe,IAAI,SACnD,SAAS,KAAK,MAAM,eAAe,EAAE,eAAe;AAC1D,UAAM,QAAQ,KAAK;AACnB,UAAM,YAAY,QAAQ;AAC1B,UAAM,aAAa,CAAC,KAAK,aAAa,UAAU,CAAC;AACjD,aAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAC9B,YAAM,MACJ,IAAI,MAAM,IAAI,2BAA2B;AAC3C,iBAAW;AAAA,QACT,uCAE+B,GAAG,mBACf,SAAS,gBAG1B,KAAK,aAAa,UAAU;AAAA,SAE3B,IAAI,MAAM,KAAK,UAAU,IACtB,KAAK,eAAe,GAAG,OAAO,OAAO,OAAO,MAAM,IAClD,MACJ;AAAA,MACJ;AAAA,IACF;AAEA,eAAW,KAAK,KAAK,eAAe,OAAO,OAAO,MAAM,OAAO,MAAM,CAAC;AAEtE,UAAM,eAAe,KAAK,gBACtB,4CAA4C,KAAK,UACjD,WACA,WACA;AACJ,WAAO,eAAe,WAAW,KAAK,EAAE;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,UAAU;AACrB,UAAM,MAAM,aAAa,aAAa,IAAI;AAC1C,WACE,sDAEoB,QAAQ,UAAU,GAAG;AAAA,EAG7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,eAAe,GAAG,OAAO,QAAQ,OAAO,QAAQ;AAC9C,UAAM,SACJ,MAAM,IAAI,IAAI,KAAK,MAAO,QAAQ,KAAK,iBAAkB,IAAI,GAAG,IAAI;AACtE,UAAM,eAAe,UAAU,MAAM,IAAI,KAAK,MAAM;AACpD,UAAM,SAAS,MAAM,IAAI,KAAM,QAAQ,KAAK,iBAAkB;AAC9D,UAAM,WAAW,MAAM,IAAI,IAAK,QAAQ,KAAK,iBAAkB;AAC/D,WACE,uDAGgB,MAAM,kBACP,MAAM,IAAI,SAAS,QAAQ,eAC5B,QAAQ,YACb,SAAS,QAAQ,OAAO,OAAO,QAExC,eACA;AAAA,EAEJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB;AACtB,UAAM,aAAa;AAAA,MACjB,KAAK,WAAW;AAAA,MAChB,KAAK,WAAW;AAAA,MAChB,KAAK,WAAW;AAAA,MAChB;AAAA,IACF;AACA,UAAM,MAAM,KAAK,QAAQ;AACzB,UAAM,iBAAiB,MAAO;AAC9B,WAAO,aAAa,iBAAiB;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,UAAU;AACf,UAAM,aAAa,SAAS;AAC5B,QAAI,CAAC,YAAY;AACf,WAAK,aAAa;AAAA,IACpB,OAAO;AACL,WAAK,aAAa,WAAW;AAAA,IAC/B;AACA,SAAK,eAAe;AAAA,EACtB;AACF;AAEA,IAAO,oBAAQ;;;AC/cf,IAAM,YAAY;AAAA,EAChB,UAAU;AAAA,EACV,YAAY;AACd;AAsBA,IAAM,aAAN,cAAyB,gBAAQ;AAAA;AAAA;AAAA;AAAA,EAI/B,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM;AAAA,MACJ,QAAQ,QAAQ;AAAA,MAChB,SAAS,SAAS,cAAc,KAAK;AAAA,MACrC,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAMD,SAAK,oBAAoB,CAAC;AAQ1B,SAAK,qBAAqB;AAS1B,SAAK,aAAa,UAAU;AAM5B,SAAK;AAML,SAAK,eAAe;AAMpB,SAAK,cAAc;AAMnB,SAAK;AAML,SAAK;AAQL,SAAK,aAAa;AAOlB,SAAK,qBAAqB;AAM1B,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAErE,UAAM,YACJ,QAAQ,cAAc,SAAY,QAAQ,YAAY;AACxD,UAAM,eAAe,SAAS,cAAc,QAAQ;AACpD,iBAAa,aAAa,QAAQ,QAAQ;AAC1C,iBAAa,YAAY,YAAY,YAAY;AACjD,UAAM,mBAAmB,KAAK;AAC9B,qBAAiB,YACf,YAAY,MAAM,qBAAqB,MAAM;AAC/C,qBAAiB,YAAY,YAAY;AAEzC,qBAAiB;AAAA,MACfC,mBAAiB;AAAA,MACjB,KAAK,oBAAoB,KAAK,IAAI;AAAA,MAClC;AAAA,IACF;AACA,qBAAiB;AAAA,MACfA,mBAAiB;AAAA,MACjB,KAAK,mBAAmB,KAAK,IAAI;AAAA,MACjC;AAAA,IACF;AACA,qBAAiB;AAAA,MACfA,mBAAiB;AAAA,MACjB,KAAK,kBAAkB,KAAK,IAAI;AAAA,MAChC;AAAA,IACF;AAEA,qBAAiB;AAAA,MACf,kBAAU;AAAA,MACV,KAAK,sBAAsB,KAAK,IAAI;AAAA,MACpC;AAAA,IACF;AACA,iBAAa,iBAAiB,kBAAU,OAAO,iBAAiB,KAAK;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,KAAK;AACV,UAAM,OAAO,GAAG;AAChB,QAAI,KAAK;AACP,UAAI,OAAO;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,cAAc;AACZ,UAAM,YAAY,KAAK;AACvB,QAAI,iBAAiB,UAAU;AAC/B,QAAI,kBAAkB,UAAU;AAChC,QAAI,mBAAmB,KAAK,oBAAoB,GAAG;AACjD,aAAQ,KAAK,qBAAqB;AAAA,IACpC;AAEA,UAAM,iBAAiB,iBAAiB,SAAS;AACjD,sBACE,WAAW,eAAe,cAAc,CAAC,IACzC,WAAW,eAAe,aAAa,CAAC;AAC1C,uBACE,WAAW,eAAe,YAAY,CAAC,IACvC,WAAW,eAAe,eAAe,CAAC;AAC5C,UAAM;AAAA;AAAA,MAAoC,UAAU;AAAA;AACpD,UAAM,aAAa,iBAAiB,KAAK;AACzC,UAAM,aACJ,MAAM,cACN,WAAW,WAAW,aAAa,CAAC,IACpC,WAAW,WAAW,YAAY,CAAC;AACrC,UAAM,cACJ,MAAM,eACN,WAAW,WAAW,WAAW,CAAC,IAClC,WAAW,WAAW,cAAc,CAAC;AACvC,SAAK,aAAa,CAAC,YAAY,WAAW;AAE1C,QAAI,iBAAiB,iBAAiB;AACpC,WAAK,aAAa,UAAU;AAC5B,WAAK,cAAc,iBAAiB;AAAA,IACtC,OAAO;AACL,WAAK,aAAa,UAAU;AAC5B,WAAK,eAAe,kBAAkB;AAAA,IACxC;AACA,WAAQ,KAAK,qBAAqB;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,OAAO;AAC3B,UAAM,OAAO,KAAK,OAAO,EAAE,QAAQ;AAEnC,UAAM,mBAAmB,KAAK;AAAA,MAC5B,MAAM,UAAU,KAAK,WAAW,CAAC,IAAI;AAAA,MACrC,MAAM,UAAU,KAAK,WAAW,CAAC,IAAI;AAAA,IACvC;AAEA,UAAM,aAAa,KAAK,0BAA0B,gBAAgB;AAClE,UAAM,OAAO,KAAK,mBAAmB,KAAK,qBAAqB,UAAU,CAAC;AAE1E,SAAK,gBAAgB;AAAA,MACnB;AAAA,MACA,UAAU,KAAK;AAAA,MACf,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,OAAO;AACzB,QAAI,CAAC,KAAK,aAAa,MAAM,WAAW,KAAK,QAAQ,mBAAmB;AACtE,YAAM;AAAA;AAAA,QACJ,KAAK,QAAQ;AAAA;AAEf,WAAK,OAAO,EAAE,QAAQ,EAAE,iBAAiB;AACzC,WAAK,UAAU,MAAM,UAAU,WAAW,QAAQ,MAAM,IAAI;AAC5D,WAAK,UAAU,MAAM,UAAU,WAAW,QAAQ,MAAM,GAAG;AAC3D,WAAK,YAAY;AAEjB,UAAI,KAAK,kBAAkB,WAAW,GAAG;AACvC,cAAM,OAAO,KAAK;AAClB,cAAM,MAAM,KAAK;AACjB,cAAM,MAAM,KAAK,OAAO,EAAE,iBAAiB;AAC3C,aAAK,kBAAkB;AAAA,UACrB,OAAO,KAAKA,mBAAiB,aAAa,MAAM,IAAI;AAAA,UACpD,OAAO,KAAKA,mBAAiB,WAAW,KAAK,IAAI;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB,OAAO;AACxB,QAAI,KAAK,WAAW;AAClB,YAAM,SAAS,MAAM,UAAU,KAAK;AACpC,YAAM,SAAS,MAAM,UAAU,KAAK;AACpC,YAAM,mBAAmB,KAAK,qBAAqB,QAAQ,MAAM;AACjE,WAAK,qBACH,KAAK,0BAA0B,gBAAgB;AACjD,WAAK,OAAO,EAAE,QAAQ,EAAE,cAAc,KAAK,kBAAkB;AAAA,IAC/D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,OAAO;AACvB,QAAI,KAAK,WAAW;AAClB,YAAM,OAAO,KAAK,OAAO,EAAE,QAAQ;AACnC,WAAK,eAAe;AAEpB,WAAK,YAAY;AACjB,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,kBAAkB,QAAQ,aAAa;AAC5C,WAAK,kBAAkB,SAAS;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAkB,KAAK;AACrB,UAAM,WAAW,KAAK,0BAA0B,GAAG;AACnD,UAAM;AAAA;AAAA,MAAoC,KAAK,QAAQ;AAAA;AAEvD,QAAI,KAAK,cAAc,UAAU,YAAY;AAC3C,YAAM,MAAM,OAAO,KAAK,cAAc,WAAW;AAAA,IACnD,OAAO;AACL,YAAM,MAAM,MAAM,KAAK,eAAe,WAAW;AAAA,IACnD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,qBAAqB,GAAG,GAAG;AACzB,QAAI;AACJ,QAAI,KAAK,eAAe,UAAU,YAAY;AAC5C,eAAS,IAAI,KAAK;AAAA,IACpB,OAAO;AACL,eAAS,IAAI,KAAK;AAAA,IACpB;AACA,WAAO,MAAM,QAAQ,GAAG,CAAC;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,0BAA0B,UAAU;AAClC,UAAM,KAAK,KAAK,OAAO,EAAE,QAAQ,EAAE,8BAA8B;AACjE,WAAO,GAAG,IAAI,QAAQ;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,0BAA0B,KAAK;AAC7B,UAAM,KAAK,KAAK,OAAO,EAAE,QAAQ,EAAE,8BAA8B;AACjE,WAAO,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,UAAU;AACf,QAAI,CAAC,SAAS,YAAY;AACxB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,sBAAsB,CAAC,KAAK,YAAY,GAAG;AACnD;AAAA,IACF;AACA,UAAM,MAAM,SAAS,WAAW,UAAU;AAC1C,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB,GAAG;AAAA,EAC5B;AACF;AAEA,IAAO,qBAAQ;;;AC3Wf,IAAM,eAAN,cAA2B,gBAAQ;AAAA;AAAA;AAAA;AAAA,EAIjC,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM;AAAA,MACJ,SAAS,SAAS,cAAc,KAAK;AAAA,MACrC,QAAQ,QAAQ;AAAA,IAClB,CAAC;AAMD,SAAK,SAAS,QAAQ,SAAS,QAAQ,SAAS;AAEhD,UAAM,YACJ,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAExD,UAAM,QAAQ,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAC5D,UAAM,WACJ,QAAQ,aAAa,SAAY,QAAQ,WAAW;AACtD,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,aAAa,QAAQ,QAAQ;AACpC,WAAO,QAAQ;AACf,WAAO;AAAA,MACL,OAAO,UAAU,WAAW,SAAS,eAAe,KAAK,IAAI;AAAA,IAC/D;AAEA,WAAO;AAAA,MACL,kBAAU;AAAA,MACV,KAAK,aAAa,KAAK,IAAI;AAAA,MAC3B;AAAA,IACF;AAEA,UAAM,aACJ,YAAY,MAAM,qBAAqB,MAAM;AAC/C,UAAM,UAAU,KAAK;AACrB,YAAQ,YAAY;AACpB,YAAQ,YAAY,MAAM;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,OAAO;AAClB,UAAM,eAAe;AACrB,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,OAAO,IAAI,QAAQ;AACzB,UAAM,SAAS,CAAC,KAAK,SACjB,KAAK,cAAc,EAAE,UAAU,IAC/B,KAAK;AACT,SAAK,YAAY,WAAkB,MAAM,CAAC;AAAA,EAC5C;AACF;AAEA,IAAO,uBAAQ;", "names": ["EventType_default", "EventType_default"]}