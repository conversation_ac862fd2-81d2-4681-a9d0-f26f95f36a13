import{a as hu,b as du,q as pu,_ as gu,f as vu,n as mu,Z as yu}from"./vue.CnN__PXn.js";import{ad as bu,q as xu,__tla as wu}from"./index.C0-0gsfl.js";let Yl,Cu=Promise.all([(()=>{try{return wu}catch{}})()]).then(async()=>{var Ii={exports:{}};Ii.exports=function(){var I=navigator.userAgent,bt=navigator.platform,$=/gecko\/\d/i.test(I),xt=/MSIE \d/.test(I),Lt=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(I),Tt=/Edge\/(\d+)/.exec(I),H=xt||Lt||Tt,P=H&&(xt?document.documentMode||6:+(Tt||Lt)[1]),A=!Tt&&/WebKit\//.test(I),Y=A&&/Qt\/\d+\.\d+/.test(I),R=!Tt&&/Chrome\/(\d+)/.exec(I),W=R&&+R[1],L=/Opera\//.test(I),x=/Apple Computer/.test(navigator.vendor),k=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(I),N=/PhantomJS/.test(I),_=x&&(/Mobile\/\w+/.test(I)||navigator.maxTouchPoints>2),wt=/Android/.test(I),Jt=_||wt||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(I),At=_||/Mac/.test(bt),Zl=/\bCrOS\b/.test(I),Jl=/win/i.test(bt),fe=L&&I.match(/Version\/(\d*\.\d*)/);fe&&(fe=Number(fe[1])),fe&&fe>=15&&(L=!1,A=!0);var zi=At&&(Y||L&&(fe==null||fe<12.11)),wn=$||H&&P>=9;function Oe(t){return new RegExp("(^|\\s)"+t+"(?:$|\\s)\\s*")}var Ae,he=function(t,e){var n=t.className,r=Oe(e).exec(n);if(r){var i=n.slice(r.index+r[0].length);t.className=n.slice(0,r.index)+(i?r[1]+i:"")}};function Qt(t){for(var e=t.childNodes.length;e>0;--e)t.removeChild(t.firstChild);return t}function Mt(t,e){return Qt(t).appendChild(e)}function O(t,e,n,r){var i=document.createElement(t);if(n&&(i.className=n),r&&(i.style.cssText=r),typeof e=="string")i.appendChild(document.createTextNode(e));else if(e)for(var o=0;o<e.length;++o)i.appendChild(e[o]);return i}function De(t,e,n,r){var i=O(t,e,n,r);return i.setAttribute("role","presentation"),i}function te(t,e){if(e.nodeType==3&&(e=e.parentNode),t.contains)return t.contains(e);do if(e.nodeType==11&&(e=e.host),e==t)return!0;while(e=e.parentNode)}function Dt(t){var e,n=t.ownerDocument||t;try{e=t.activeElement}catch{e=n.body||null}for(;e&&e.shadowRoot&&e.shadowRoot.activeElement;)e=e.shadowRoot.activeElement;return e}function de(t,e){var n=t.className;Oe(e).test(n)||(t.className+=(n?" ":"")+e)}function Cn(t,e){for(var n=t.split(" "),r=0;r<n.length;r++)n[r]&&!Oe(n[r]).test(e)&&(e+=" "+n[r]);return e}Ae=document.createRange?function(t,e,n,r){var i=document.createRange();return i.setEnd(r||t,n),i.setStart(t,e),i}:function(t,e,n){var r=document.body.createTextRange();try{r.moveToElementText(t.parentNode)}catch{return r}return r.collapse(!0),r.moveEnd("character",n),r.moveStart("character",e),r};var tr=function(t){t.select()};function er(t){return t.display.wrapper.ownerDocument}function We(t){return pe(t.display.wrapper)}function pe(t){return t.getRootNode?t.getRootNode():t.ownerDocument}function Pr(t){return er(t).defaultView}function Sn(t){var e=Array.prototype.slice.call(arguments,1);return function(){return t.apply(null,e)}}function ge(t,e,n){for(var r in e||(e={}),t)!t.hasOwnProperty(r)||n===!1&&e.hasOwnProperty(r)||(e[r]=t[r]);return e}function Wt(t,e,n,r,i){e==null&&(e=t.search(/[^\s\u00a0]/))==-1&&(e=t.length);for(var o=r||0,l=i||0;;){var a=t.indexOf("	",o);if(a<0||a>=e)return l+(e-o);l+=a-o,l+=n-l%n,o=a+1}}_?tr=function(t){t.selectionStart=0,t.selectionEnd=t.value.length}:H&&(tr=function(t){try{t.select()}catch{}});var ee=function(){this.id=null,this.f=null,this.time=0,this.handler=Sn(this.onTimeout,this)};function st(t,e){for(var n=0;n<t.length;++n)if(t[n]==e)return n;return-1}ee.prototype.onTimeout=function(t){t.id=0,t.time<=+new Date?t.f():setTimeout(t.handler,t.time-+new Date)},ee.prototype.set=function(t,e){this.f=e;var n=+new Date+t;(!this.id||n<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,t),this.time=n)};var Ri=50,Ir={toString:function(){return"CodeMirror.Pass"}},Bt={scroll:!1},kn={origin:"*mouse"},rr={origin:"+move"};function Ln(t,e,n){for(var r=0,i=0;;){var o=t.indexOf("	",r);o==-1&&(o=t.length);var l=o-r;if(o==t.length||i+l>=e)return r+Math.min(l,e-i);if(i+=o-r,r=o+1,(i+=n-i%n)>=e)return r}}var zr=[""];function Tn(t){for(;zr.length<=t;)zr.push(B(zr)+" ");return zr[t]}function B(t){return t[t.length-1]}function Rr(t,e){for(var n=[],r=0;r<t.length;r++)n[r]=e(t[r],r);return n}function Ql(t,e,n){for(var r=0,i=n(e);r<t.length&&n(t[r])<=i;)r++;t.splice(r,0,e)}function Bi(){}function Vi(t,e){var n;return Object.create?n=Object.create(t):(Bi.prototype=t,n=new Bi),e&&ge(e,n),n}var ta=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function Mn(t){return/\w/.test(t)||t>"\x80"&&(t.toUpperCase()!=t.toLowerCase()||ta.test(t))}function Br(t,e){return e?!!(e.source.indexOf("\\w")>-1&&Mn(t))||e.test(t):Mn(t)}function Ui(t){for(var e in t)if(t.hasOwnProperty(e)&&t[e])return!1;return!0}var ea=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function Nn(t){return t.charCodeAt(0)>=768&&ea.test(t)}function Gi(t,e,n){for(;(n<0?e>0:e<t.length)&&Nn(t.charAt(e));)e+=n;return e}function nr(t,e,n){for(var r=e>n?-1:1;;){if(e==n)return e;var i=(e+n)/2,o=r<0?Math.ceil(i):Math.floor(i);if(o==e)return t(o)?e:n;t(o)?n=o:e=o+r}}function ra(t,e,n,r){if(!t)return r(e,n,"ltr",0);for(var i=!1,o=0;o<t.length;++o){var l=t[o];(l.from<n&&l.to>e||e==n&&l.to==e)&&(r(Math.max(l.from,e),Math.min(l.to,n),l.level==1?"rtl":"ltr",o),i=!0)}i||r(e,n,"ltr")}var ir=null;function or(t,e,n){var r;ir=null;for(var i=0;i<t.length;++i){var o=t[i];if(o.from<e&&o.to>e)return i;o.to==e&&(o.from!=o.to&&n=="before"?r=i:ir=i),o.from==e&&(o.from!=o.to&&n!="before"?r=i:ir=i)}return r??ir}var na=function(){var t="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",e="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";function n(u){return u<=247?t.charAt(u):1424<=u&&u<=1524?"R":1536<=u&&u<=1785?e.charAt(u-1536):1774<=u&&u<=2220?"r":8192<=u&&u<=8203?"w":u==8204?"b":"L"}var r=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,i=/[stwN]/,o=/[LRr]/,l=/[Lb1n]/,a=/[1n]/;function s(u,c,f){this.level=u,this.from=c,this.to=f}return function(u,c){var f=c=="ltr"?"L":"R";if(u.length==0||c=="ltr"&&!r.test(u))return!1;for(var h=u.length,d=[],p=0;p<h;++p)d.push(n(u.charCodeAt(p)));for(var g=0,m=f;g<h;++g){var b=d[g];b=="m"?d[g]=m:m=b}for(var w=0,y=f;w<h;++w){var S=d[w];S=="1"&&y=="r"?d[w]="n":o.test(S)&&(y=S,S=="r"&&(d[w]="R"))}for(var C=1,T=d[0];C<h-1;++C){var G=d[C];G=="+"&&T=="1"&&d[C+1]=="1"?d[C]="1":G!=","||T!=d[C+1]||T!="1"&&T!="n"||(d[C]=T),T=G}for(var K=0;K<h;++K){var St=d[K];if(St==",")d[K]="N";else if(St=="%"){var j=void 0;for(j=K+1;j<h&&d[j]=="%";++j);for(var Et=K&&d[K-1]=="!"||j<h&&d[j]=="1"?"1":"N",kt=K;kt<j;++kt)d[kt]=Et;K=j-1}}for(var q=0,mt=f;q<h;++q){var ht=d[q];mt=="L"&&ht=="1"?d[q]="L":o.test(ht)&&(mt=ht)}for(var et=0;et<h;++et)if(i.test(d[et])){var J=void 0;for(J=et+1;J<h&&i.test(d[J]);++J);for(var rt=(et?d[et-1]:f)=="L",yt=rt==((J<h?d[J]:f)=="L")?rt?"L":"R":f,ce=et;ce<J;++ce)d[ce]=yt;et=J-1}for(var Rt,ct=[],nt=0;nt<h;)if(l.test(d[nt])){var Fi=nt;for(++nt;nt<h&&l.test(d[nt]);++nt);ct.push(new s(0,Fi,nt))}else{var Zt=nt,Me=ct.length,Ne=c=="rtl"?1:0;for(++nt;nt<h&&d[nt]!="L";++nt);for(var dt=Zt;dt<nt;)if(a.test(d[dt])){Zt<dt&&(ct.splice(Me,0,new s(1,Zt,dt)),Me+=Ne);var Qe=dt;for(++dt;dt<nt&&a.test(d[dt]);++dt);ct.splice(Me,0,new s(2,Qe,dt)),Me+=Ne,Zt=dt}else++dt;Zt<nt&&ct.splice(Me,0,new s(1,Zt,nt))}return c=="ltr"&&(ct[0].level==1&&(Rt=u.match(/^\s+/))&&(ct[0].from=Rt[0].length,ct.unshift(new s(0,0,Rt[0].length))),B(ct).level==1&&(Rt=u.match(/\s+$/))&&(B(ct).to-=Rt[0].length,ct.push(new s(0,h-Rt[0].length,h)))),c=="rtl"?ct.reverse():ct}}();function _t(t,e){var n=t.order;return n==null&&(n=t.order=na(t.text,e)),n}var Ki=[],D=function(t,e,n){if(t.addEventListener)t.addEventListener(e,n,!1);else if(t.attachEvent)t.attachEvent("on"+e,n);else{var r=t._handlers||(t._handlers={});r[e]=(r[e]||Ki).concat(n)}};function On(t,e){return t._handlers&&t._handlers[e]||Ki}function Nt(t,e,n){if(t.removeEventListener)t.removeEventListener(e,n,!1);else if(t.detachEvent)t.detachEvent("on"+e,n);else{var r=t._handlers,i=r&&r[e];if(i){var o=st(i,n);o>-1&&(r[e]=i.slice(0,o).concat(i.slice(o+1)))}}}function Q(t,e){var n=On(t,e);if(n.length)for(var r=Array.prototype.slice.call(arguments,2),i=0;i<n.length;++i)n[i].apply(null,r)}function it(t,e,n){return typeof e=="string"&&(e={type:e,preventDefault:function(){this.defaultPrevented=!0}}),Q(t,n||e.type,t,e),An(e)||e.codemirrorIgnore}function _i(t){var e=t._handlers&&t._handlers.cursorActivity;if(e)for(var n=t.curOp.cursorActivityHandlers||(t.curOp.cursorActivityHandlers=[]),r=0;r<e.length;++r)st(n,e[r])==-1&&n.push(e[r])}function Ht(t,e){return On(t,e).length>0}function He(t){t.prototype.on=function(e,n){D(this,e,n)},t.prototype.off=function(e,n){Nt(this,e,n)}}function pt(t){t.preventDefault?t.preventDefault():t.returnValue=!1}function $i(t){t.stopPropagation?t.stopPropagation():t.cancelBubble=!0}function An(t){return t.defaultPrevented!=null?t.defaultPrevented:t.returnValue==0}function lr(t){pt(t),$i(t)}function Dn(t){return t.target||t.srcElement}function ji(t){var e=t.which;return e==null&&(1&t.button?e=1:2&t.button?e=3:4&t.button&&(e=2)),At&&t.ctrlKey&&e==1&&(e=3),e}var Wn,Hn,ia=function(){if(H&&P<9)return!1;var t=O("div");return"draggable"in t||"dragDrop"in t}();function oa(t){if(Wn==null){var e=O("span","\u200B");Mt(t,O("span",[e,document.createTextNode("x")])),t.firstChild.offsetHeight!=0&&(Wn=e.offsetWidth<=1&&e.offsetHeight>2&&!(H&&P<8))}var n=Wn?O("span","\u200B"):O("span","\xA0",null,"display: inline-block; width: 1px; margin-right: -1px");return n.setAttribute("cm-text",""),n}function la(t){if(Hn!=null)return Hn;var e=Mt(t,document.createTextNode("A\u062EA")),n=Ae(e,0,1).getBoundingClientRect(),r=Ae(e,1,2).getBoundingClientRect();return Qt(t),!(!n||n.left==n.right)&&(Hn=r.right-n.right<3)}var En,Fn=`

b`.split(/\n/).length!=3?function(t){for(var e=0,n=[],r=t.length;e<=r;){var i=t.indexOf(`
`,e);i==-1&&(i=t.length);var o=t.slice(e,t.charAt(i-1)=="\r"?i-1:i),l=o.indexOf("\r");l!=-1?(n.push(o.slice(0,l)),e+=l+1):(n.push(o),e=i+1)}return n}:function(t){return t.split(/\r\n?|\n/)},aa=window.getSelection?function(t){try{return t.selectionStart!=t.selectionEnd}catch{return!1}}:function(t){var e;try{e=t.ownerDocument.selection.createRange()}catch{}return!(!e||e.parentElement()!=t)&&e.compareEndPoints("StartToEnd",e)!=0},sa="oncopy"in(En=O("div"))||(En.setAttribute("oncopy","return;"),typeof En.oncopy=="function"),Pn=null;function ua(t){if(Pn!=null)return Pn;var e=Mt(t,O("span","x")),n=e.getBoundingClientRect(),r=Ae(e,0,1).getBoundingClientRect();return Pn=Math.abs(n.left-r.left)>1}var In={},Ee={};function ca(t,e){arguments.length>2&&(e.dependencies=Array.prototype.slice.call(arguments,2)),In[t]=e}function fa(t,e){Ee[t]=e}function Vr(t){if(typeof t=="string"&&Ee.hasOwnProperty(t))t=Ee[t];else if(t&&typeof t.name=="string"&&Ee.hasOwnProperty(t.name)){var e=Ee[t.name];typeof e=="string"&&(e={name:e}),(t=Vi(e,t)).name=e.name}else{if(typeof t=="string"&&/^[\w\-]+\/[\w\-]+\+xml$/.test(t))return Vr("application/xml");if(typeof t=="string"&&/^[\w\-]+\/[\w\-]+\+json$/.test(t))return Vr("application/json")}return typeof t=="string"?{name:t}:t||{name:"null"}}function zn(t,e){e=Vr(e);var n=In[e.name];if(!n)return zn(t,"text/plain");var r=n(t,e);if(Fe.hasOwnProperty(e.name)){var i=Fe[e.name];for(var o in i)i.hasOwnProperty(o)&&(r.hasOwnProperty(o)&&(r["_"+o]=r[o]),r[o]=i[o])}if(r.name=e.name,e.helperType&&(r.helperType=e.helperType),e.modeProps)for(var l in e.modeProps)r[l]=e.modeProps[l];return r}var Fe={};function ha(t,e){ge(e,Fe.hasOwnProperty(t)?Fe[t]:Fe[t]={})}function ve(t,e){if(e===!0)return e;if(t.copyState)return t.copyState(e);var n={};for(var r in e){var i=e[r];i instanceof Array&&(i=i.concat([])),n[r]=i}return n}function Rn(t,e){for(var n;t.innerMode&&(n=t.innerMode(e))&&n.mode!=t;)e=n.state,t=n.mode;return n||{mode:t,state:e}}function Xi(t,e,n){return!t.startState||t.startState(e,n)}var tt=function(t,e,n){this.pos=this.start=0,this.string=t,this.tabSize=e||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=n};function M(t,e){if((e-=t.first)<0||e>=t.size)throw new Error("There is no line "+(e+t.first)+" in the document.");for(var n=t;!n.lines;)for(var r=0;;++r){var i=n.children[r],o=i.chunkSize();if(e<o){n=i;break}e-=o}return n.lines[e]}function me(t,e,n){var r=[],i=e.line;return t.iter(e.line,n.line+1,function(o){var l=o.text;i==n.line&&(l=l.slice(0,n.ch)),i==e.line&&(l=l.slice(e.ch)),r.push(l),++i}),r}function Bn(t,e,n){var r=[];return t.iter(e,n,function(i){r.push(i.text)}),r}function Vt(t,e){var n=e-t.height;if(n)for(var r=t;r;r=r.parent)r.height+=n}function V(t){if(t.parent==null)return null;for(var e=t.parent,n=st(e.lines,t),r=e.parent;r;e=r,r=r.parent)for(var i=0;r.children[i]!=e;++i)n+=r.children[i].chunkSize();return n+e.first}function ye(t,e){var n=t.first;t:do{for(var r=0;r<t.children.length;++r){var i=t.children[r],o=i.height;if(e<o){t=i;continue t}e-=o,n+=i.chunkSize()}return n}while(!t.lines);for(var l=0;l<t.lines.length;++l){var a=t.lines[l].height;if(e<a)break;e-=a}return n+l}function ar(t,e){return e>=t.first&&e<t.first+t.size}function Vn(t,e){return String(t.lineNumberFormatter(e+t.firstLineNumber))}function v(t,e,n){if(n===void 0&&(n=null),!(this instanceof v))return new v(t,e,n);this.line=t,this.ch=e,this.sticky=n}function E(t,e){return t.line-e.line||t.ch-e.ch}function Un(t,e){return t.sticky==e.sticky&&E(t,e)==0}function Gn(t){return v(t.line,t.ch)}function Ur(t,e){return E(t,e)<0?e:t}function Gr(t,e){return E(t,e)<0?t:e}function Yi(t,e){return Math.max(t.first,Math.min(e,t.first+t.size-1))}function F(t,e){if(e.line<t.first)return v(t.first,0);var n=t.first+t.size-1;return e.line>n?v(n,M(t,n).text.length):da(e,M(t,e.line).text.length)}function da(t,e){var n=t.ch;return n==null||n>e?v(t.line,e):n<0?v(t.line,0):t}function qi(t,e){for(var n=[],r=0;r<e.length;r++)n[r]=F(t,e[r]);return n}tt.prototype.eol=function(){return this.pos>=this.string.length},tt.prototype.sol=function(){return this.pos==this.lineStart},tt.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},tt.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},tt.prototype.eat=function(t){var e=this.string.charAt(this.pos);if(typeof t=="string"?e==t:e&&(t.test?t.test(e):t(e)))return++this.pos,e},tt.prototype.eatWhile=function(t){for(var e=this.pos;this.eat(t););return this.pos>e},tt.prototype.eatSpace=function(){for(var t=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>t},tt.prototype.skipToEnd=function(){this.pos=this.string.length},tt.prototype.skipTo=function(t){var e=this.string.indexOf(t,this.pos);if(e>-1)return this.pos=e,!0},tt.prototype.backUp=function(t){this.pos-=t},tt.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=Wt(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?Wt(this.string,this.lineStart,this.tabSize):0)},tt.prototype.indentation=function(){return Wt(this.string,null,this.tabSize)-(this.lineStart?Wt(this.string,this.lineStart,this.tabSize):0)},tt.prototype.match=function(t,e,n){if(typeof t!="string"){var r=this.string.slice(this.pos).match(t);return r&&r.index>0?null:(r&&e!==!1&&(this.pos+=r[0].length),r)}var i=function(o){return n?o.toLowerCase():o};if(i(this.string.substr(this.pos,t.length))==i(t))return e!==!1&&(this.pos+=t.length),!0},tt.prototype.current=function(){return this.string.slice(this.start,this.pos)},tt.prototype.hideFirstChars=function(t,e){this.lineStart+=t;try{return e()}finally{this.lineStart-=t}},tt.prototype.lookAhead=function(t){var e=this.lineOracle;return e&&e.lookAhead(t)},tt.prototype.baseToken=function(){var t=this.lineOracle;return t&&t.baseToken(this.pos)};var Kr=function(t,e){this.state=t,this.lookAhead=e},Ut=function(t,e,n,r){this.state=e,this.doc=t,this.line=n,this.maxLookAhead=r||0,this.baseTokens=null,this.baseTokenPos=1};function Zi(t,e,n,r){var i=[t.state.modeGen],o={};no(t,e.text,t.doc.mode,n,function(u,c){return i.push(u,c)},o,r);for(var l=n.state,a=function(u){n.baseTokens=i;var c=t.state.overlays[u],f=1,h=0;n.state=!0,no(t,e.text,c.mode,n,function(d,p){for(var g=f;h<d;){var m=i[f];m>d&&i.splice(f,1,d,i[f+1],m),f+=2,h=Math.min(d,m)}if(p)if(c.opaque)i.splice(g,f-g,d,"overlay "+p),f=g+2;else for(;g<f;g+=2){var b=i[g+1];i[g+1]=(b?b+" ":"")+"overlay "+p}},o),n.state=l,n.baseTokens=null,n.baseTokenPos=1},s=0;s<t.state.overlays.length;++s)a(s);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function Ji(t,e,n){if(!e.styles||e.styles[0]!=t.state.modeGen){var r=sr(t,V(e)),i=e.text.length>t.options.maxHighlightLength&&ve(t.doc.mode,r.state),o=Zi(t,e,r);i&&(r.state=i),e.stateAfter=r.save(!i),e.styles=o.styles,o.classes?e.styleClasses=o.classes:e.styleClasses&&(e.styleClasses=null),n===t.doc.highlightFrontier&&(t.doc.modeFrontier=Math.max(t.doc.modeFrontier,++t.doc.highlightFrontier))}return e.styles}function sr(t,e,n){var r=t.doc,i=t.display;if(!r.mode.startState)return new Ut(r,!0,e);var o=pa(t,e,n),l=o>r.first&&M(r,o-1).stateAfter,a=l?Ut.fromSaved(r,l,o):new Ut(r,Xi(r.mode),o);return r.iter(o,e,function(s){Kn(t,s.text,a);var u=a.line;s.stateAfter=u==e-1||u%5==0||u>=i.viewFrom&&u<i.viewTo?a.save():null,a.nextLine()}),n&&(r.modeFrontier=a.line),a}function Kn(t,e,n,r){var i=t.doc.mode,o=new tt(e,t.options.tabSize,n);for(o.start=o.pos=r||0,e==""&&Qi(i,n.state);!o.eol();)_n(i,o,n.state),o.start=o.pos}function Qi(t,e){if(t.blankLine)return t.blankLine(e);if(t.innerMode){var n=Rn(t,e);return n.mode.blankLine?n.mode.blankLine(n.state):void 0}}function _n(t,e,n,r){for(var i=0;i<10;i++){r&&(r[0]=Rn(t,n).mode);var o=t.token(e,n);if(e.pos>e.start)return o}throw new Error("Mode "+t.name+" failed to advance stream.")}Ut.prototype.lookAhead=function(t){var e=this.doc.getLine(this.line+t);return e!=null&&t>this.maxLookAhead&&(this.maxLookAhead=t),e},Ut.prototype.baseToken=function(t){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=t;)this.baseTokenPos+=2;var e=this.baseTokens[this.baseTokenPos+1];return{type:e&&e.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-t}},Ut.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},Ut.fromSaved=function(t,e,n){return e instanceof Kr?new Ut(t,ve(t.mode,e.state),n,e.lookAhead):new Ut(t,ve(t.mode,e),n)},Ut.prototype.save=function(t){var e=t!==!1?ve(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new Kr(e,this.maxLookAhead):e};var to=function(t,e,n){this.start=t.start,this.end=t.pos,this.string=t.current(),this.type=e||null,this.state=n};function eo(t,e,n,r){var i,o,l=t.doc,a=l.mode,s=M(l,(e=F(l,e)).line),u=sr(t,e.line,n),c=new tt(s.text,t.options.tabSize,u);for(r&&(o=[]);(r||c.pos<e.ch)&&!c.eol();)c.start=c.pos,i=_n(a,c,u.state),r&&o.push(new to(c,i,ve(l.mode,u.state)));return r?o:new to(c,i,u.state)}function ro(t,e){if(t)for(;;){var n=t.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!n)break;t=t.slice(0,n.index)+t.slice(n.index+n[0].length);var r=n[1]?"bgClass":"textClass";e[r]==null?e[r]=n[2]:new RegExp("(?:^|\\s)"+n[2]+"(?:$|\\s)").test(e[r])||(e[r]+=" "+n[2])}return t}function no(t,e,n,r,i,o,l){var a=n.flattenSpans;a==null&&(a=t.options.flattenSpans);var s,u=0,c=null,f=new tt(e,t.options.tabSize,r),h=t.options.addModeClass&&[null];for(e==""&&ro(Qi(n,r.state),o);!f.eol();){if(f.pos>t.options.maxHighlightLength?(a=!1,l&&Kn(t,e,r,f.pos),f.pos=e.length,s=null):s=ro(_n(n,f,r.state,h),o),h){var d=h[0].name;d&&(s="m-"+(s?d+" "+s:d))}if(!a||c!=s){for(;u<f.start;)i(u=Math.min(f.start,u+5e3),c);c=s}f.start=f.pos}for(;u<f.pos;){var p=Math.min(f.pos,u+5e3);i(p,c),u=p}}function pa(t,e,n){for(var r,i,o=t.doc,l=n?-1:e-(t.doc.mode.innerMode?1e3:100),a=e;a>l;--a){if(a<=o.first)return o.first;var s=M(o,a-1),u=s.stateAfter;if(u&&(!n||a+(u instanceof Kr?u.lookAhead:0)<=o.modeFrontier))return a;var c=Wt(s.text,null,t.options.tabSize);(i==null||r>c)&&(i=a-1,r=c)}return i}function ga(t,e){if(t.modeFrontier=Math.min(t.modeFrontier,e),!(t.highlightFrontier<e-10)){for(var n=t.first,r=e-1;r>n;r--){var i=M(t,r).stateAfter;if(i&&(!(i instanceof Kr)||r+i.lookAhead<e)){n=r+1;break}}t.highlightFrontier=Math.min(t.highlightFrontier,n)}}var io=!1,$t=!1;function va(){io=!0}function ma(){$t=!0}function _r(t,e,n){this.marker=t,this.from=e,this.to=n}function ur(t,e){if(t)for(var n=0;n<t.length;++n){var r=t[n];if(r.marker==e)return r}}function ya(t,e){for(var n,r=0;r<t.length;++r)t[r]!=e&&(n||(n=[])).push(t[r]);return n}function ba(t,e,n){var r=n&&window.WeakSet&&(n.markedSpans||(n.markedSpans=new WeakSet));r&&t.markedSpans&&r.has(t.markedSpans)?t.markedSpans.push(e):(t.markedSpans=t.markedSpans?t.markedSpans.concat([e]):[e],r&&r.add(t.markedSpans)),e.marker.attachLine(t)}function xa(t,e,n){var r;if(t)for(var i=0;i<t.length;++i){var o=t[i],l=o.marker;if(o.from==null||(l.inclusiveLeft?o.from<=e:o.from<e)||o.from==e&&l.type=="bookmark"&&(!n||!o.marker.insertLeft)){var a=o.to==null||(l.inclusiveRight?o.to>=e:o.to>e);(r||(r=[])).push(new _r(l,o.from,a?null:o.to))}}return r}function wa(t,e,n){var r;if(t)for(var i=0;i<t.length;++i){var o=t[i],l=o.marker;if(o.to==null||(l.inclusiveRight?o.to>=e:o.to>e)||o.from==e&&l.type=="bookmark"&&(!n||o.marker.insertLeft)){var a=o.from==null||(l.inclusiveLeft?o.from<=e:o.from<e);(r||(r=[])).push(new _r(l,a?null:o.from-e,o.to==null?null:o.to-e))}}return r}function $n(t,e){if(e.full)return null;var n=ar(t,e.from.line)&&M(t,e.from.line).markedSpans,r=ar(t,e.to.line)&&M(t,e.to.line).markedSpans;if(!n&&!r)return null;var i=e.from.ch,o=e.to.ch,l=E(e.from,e.to)==0,a=xa(n,i,l),s=wa(r,o,l),u=e.text.length==1,c=B(e.text).length+(u?i:0);if(a)for(var f=0;f<a.length;++f){var h=a[f];if(h.to==null){var d=ur(s,h.marker);d?u&&(h.to=d.to==null?null:d.to+c):h.to=i}}if(s)for(var p=0;p<s.length;++p){var g=s[p];g.to!=null&&(g.to+=c),g.from==null?ur(a,g.marker)||(g.from=c,u&&(a||(a=[])).push(g)):(g.from+=c,u&&(a||(a=[])).push(g))}a&&(a=oo(a)),s&&s!=a&&(s=oo(s));var m=[a];if(!u){var b,w=e.text.length-2;if(w>0&&a)for(var y=0;y<a.length;++y)a[y].to==null&&(b||(b=[])).push(new _r(a[y].marker,null,null));for(var S=0;S<w;++S)m.push(b);m.push(s)}return m}function oo(t){for(var e=0;e<t.length;++e){var n=t[e];n.from!=null&&n.from==n.to&&n.marker.clearWhenEmpty!==!1&&t.splice(e--,1)}return t.length?t:null}function Ca(t,e,n){var r=null;if(t.iter(e.line,n.line+1,function(d){if(d.markedSpans)for(var p=0;p<d.markedSpans.length;++p){var g=d.markedSpans[p].marker;!g.readOnly||r&&st(r,g)!=-1||(r||(r=[])).push(g)}}),!r)return null;for(var i=[{from:e,to:n}],o=0;o<r.length;++o)for(var l=r[o],a=l.find(0),s=0;s<i.length;++s){var u=i[s];if(!(E(u.to,a.from)<0||E(u.from,a.to)>0)){var c=[s,1],f=E(u.from,a.from),h=E(u.to,a.to);(f<0||!l.inclusiveLeft&&!f)&&c.push({from:u.from,to:a.from}),(h>0||!l.inclusiveRight&&!h)&&c.push({from:a.to,to:u.to}),i.splice.apply(i,c),s+=c.length-3}}return i}function lo(t){var e=t.markedSpans;if(e){for(var n=0;n<e.length;++n)e[n].marker.detachLine(t);t.markedSpans=null}}function ao(t,e){if(e){for(var n=0;n<e.length;++n)e[n].marker.attachLine(t);t.markedSpans=e}}function $r(t){return t.inclusiveLeft?-1:0}function jr(t){return t.inclusiveRight?1:0}function jn(t,e){var n=t.lines.length-e.lines.length;if(n!=0)return n;var r=t.find(),i=e.find(),o=E(r.from,i.from)||$r(t)-$r(e);if(o)return-o;var l=E(r.to,i.to)||jr(t)-jr(e);return l||e.id-t.id}function so(t,e){var n,r=$t&&t.markedSpans;if(r)for(var i=void 0,o=0;o<r.length;++o)(i=r[o]).marker.collapsed&&(e?i.from:i.to)==null&&(!n||jn(n,i.marker)<0)&&(n=i.marker);return n}function uo(t){return so(t,!0)}function Xr(t){return so(t,!1)}function Sa(t,e){var n,r=$t&&t.markedSpans;if(r)for(var i=0;i<r.length;++i){var o=r[i];o.marker.collapsed&&(o.from==null||o.from<e)&&(o.to==null||o.to>e)&&(!n||jn(n,o.marker)<0)&&(n=o.marker)}return n}function co(t,e,n,r,i){var o=M(t,e),l=$t&&o.markedSpans;if(l)for(var a=0;a<l.length;++a){var s=l[a];if(s.marker.collapsed){var u=s.marker.find(0),c=E(u.from,n)||$r(s.marker)-$r(i),f=E(u.to,r)||jr(s.marker)-jr(i);if(!(c>=0&&f<=0||c<=0&&f>=0)&&(c<=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?E(u.to,n)>=0:E(u.to,n)>0)||c>=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?E(u.from,r)<=0:E(u.from,r)<0)))return!0}}}function Ft(t){for(var e;e=uo(t);)t=e.find(-1,!0).line;return t}function ka(t){for(var e;e=Xr(t);)t=e.find(1,!0).line;return t}function La(t){for(var e,n;e=Xr(t);)t=e.find(1,!0).line,(n||(n=[])).push(t);return n}function Xn(t,e){var n=M(t,e),r=Ft(n);return n==r?e:V(r)}function fo(t,e){if(e>t.lastLine())return e;var n,r=M(t,e);if(!re(t,r))return e;for(;n=Xr(r);)r=n.find(1,!0).line;return V(r)+1}function re(t,e){var n=$t&&e.markedSpans;if(n){for(var r=void 0,i=0;i<n.length;++i)if((r=n[i]).marker.collapsed&&(r.from==null||!r.marker.widgetNode&&r.from==0&&r.marker.inclusiveLeft&&Yn(t,e,r)))return!0}}function Yn(t,e,n){if(n.to==null){var r=n.marker.find(1,!0);return Yn(t,r.line,ur(r.line.markedSpans,n.marker))}if(n.marker.inclusiveRight&&n.to==e.text.length)return!0;for(var i=void 0,o=0;o<e.markedSpans.length;++o)if((i=e.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==n.to&&(i.to==null||i.to!=n.from)&&(i.marker.inclusiveLeft||n.marker.inclusiveRight)&&Yn(t,e,i))return!0}function jt(t){for(var e=0,n=(t=Ft(t)).parent,r=0;r<n.lines.length;++r){var i=n.lines[r];if(i==t)break;e+=i.height}for(var o=n.parent;o;o=(n=o).parent)for(var l=0;l<o.children.length;++l){var a=o.children[l];if(a==n)break;e+=a.height}return e}function Yr(t){if(t.height==0)return 0;for(var e,n=t.text.length,r=t;e=uo(r);){var i=e.find(0,!0);r=i.from.line,n+=i.from.ch-i.to.ch}for(r=t;e=Xr(r);){var o=e.find(0,!0);n-=r.text.length-o.from.ch,n+=(r=o.to.line).text.length-o.to.ch}return n}function qn(t){var e=t.display,n=t.doc;e.maxLine=M(n,n.first),e.maxLineLength=Yr(e.maxLine),e.maxLineChanged=!0,n.iter(function(r){var i=Yr(r);i>e.maxLineLength&&(e.maxLineLength=i,e.maxLine=r)})}var Pe=function(t,e,n){this.text=t,ao(this,e),this.height=n?n(this):1};function Ta(t,e,n,r){t.text=e,t.stateAfter&&(t.stateAfter=null),t.styles&&(t.styles=null),t.order!=null&&(t.order=null),lo(t),ao(t,n);var i=r?r(t):1;i!=t.height&&Vt(t,i)}function Ma(t){t.parent=null,lo(t)}Pe.prototype.lineNo=function(){return V(this)},He(Pe);var Na={},Oa={};function ho(t,e){if(!t||/^\s*$/.test(t))return null;var n=e.addModeClass?Oa:Na;return n[t]||(n[t]=t.replace(/\S+/g,"cm-$&"))}function po(t,e){var n=De("span",null,null,A?"padding-right: .1px":null),r={pre:De("pre",[n],"CodeMirror-line"),content:n,col:0,pos:0,cm:t,trailingSpace:!1,splitSpaces:t.getOption("lineWrapping")};e.measure={};for(var i=0;i<=(e.rest?e.rest.length:0);i++){var o=i?e.rest[i-1]:e.line,l=void 0;r.pos=0,r.addToken=Da,la(t.display.measure)&&(l=_t(o,t.doc.direction))&&(r.addToken=Ha(r.addToken,l)),r.map=[],Ea(o,r,Ji(t,o,e!=t.display.externalMeasured&&V(o))),o.styleClasses&&(o.styleClasses.bgClass&&(r.bgClass=Cn(o.styleClasses.bgClass,r.bgClass||"")),o.styleClasses.textClass&&(r.textClass=Cn(o.styleClasses.textClass,r.textClass||""))),r.map.length==0&&r.map.push(0,0,r.content.appendChild(oa(t.display.measure))),i==0?(e.measure.map=r.map,e.measure.cache={}):((e.measure.maps||(e.measure.maps=[])).push(r.map),(e.measure.caches||(e.measure.caches=[])).push({}))}if(A){var a=r.content.lastChild;(/\bcm-tab\b/.test(a.className)||a.querySelector&&a.querySelector(".cm-tab"))&&(r.content.className="cm-tab-wrap-hack")}return Q(t,"renderLine",t,e.line,r.pre),r.pre.className&&(r.textClass=Cn(r.pre.className,r.textClass||"")),r}function Aa(t){var e=O("span","\u2022","cm-invalidchar");return e.title="\\u"+t.charCodeAt(0).toString(16),e.setAttribute("aria-label",e.title),e}function Da(t,e,n,r,i,o,l){if(e){var a,s=t.splitSpaces?Wa(e,t.trailingSpace):e,u=t.cm.state.specialChars,c=!1;if(u.test(e)){a=document.createDocumentFragment();for(var f=0;;){u.lastIndex=f;var h=u.exec(e),d=h?h.index-f:e.length-f;if(d){var p=document.createTextNode(s.slice(f,f+d));H&&P<9?a.appendChild(O("span",[p])):a.appendChild(p),t.map.push(t.pos,t.pos+d,p),t.col+=d,t.pos+=d}if(!h)break;f+=d+1;var g=void 0;if(h[0]=="	"){var m=t.cm.options.tabSize,b=m-t.col%m;(g=a.appendChild(O("span",Tn(b),"cm-tab"))).setAttribute("role","presentation"),g.setAttribute("cm-text","	"),t.col+=b}else h[0]=="\r"||h[0]==`
`?((g=a.appendChild(O("span",h[0]=="\r"?"\u240D":"\u2424","cm-invalidchar"))).setAttribute("cm-text",h[0]),t.col+=1):((g=t.cm.options.specialCharPlaceholder(h[0])).setAttribute("cm-text",h[0]),H&&P<9?a.appendChild(O("span",[g])):a.appendChild(g),t.col+=1);t.map.push(t.pos,t.pos+1,g),t.pos++}}else t.col+=e.length,a=document.createTextNode(s),t.map.push(t.pos,t.pos+e.length,a),H&&P<9&&(c=!0),t.pos+=e.length;if(t.trailingSpace=s.charCodeAt(e.length-1)==32,n||r||i||c||o||l){var w=n||"";r&&(w+=r),i&&(w+=i);var y=O("span",[a],w,o);if(l)for(var S in l)l.hasOwnProperty(S)&&S!="style"&&S!="class"&&y.setAttribute(S,l[S]);return t.content.appendChild(y)}t.content.appendChild(a)}}function Wa(t,e){if(t.length>1&&!/  /.test(t))return t;for(var n=e,r="",i=0;i<t.length;i++){var o=t.charAt(i);o!=" "||!n||i!=t.length-1&&t.charCodeAt(i+1)!=32||(o="\xA0"),r+=o,n=o==" "}return r}function Ha(t,e){return function(n,r,i,o,l,a,s){i=i?i+" cm-force-border":"cm-force-border";for(var u=n.pos,c=u+r.length;;){for(var f=void 0,h=0;h<e.length&&!((f=e[h]).to>u&&f.from<=u);h++);if(f.to>=c)return t(n,r,i,o,l,a,s);t(n,r.slice(0,f.to-u),i,o,null,a,s),o=null,r=r.slice(f.to-u),u=f.to}}}function go(t,e,n,r){var i=!r&&n.widgetNode;i&&t.map.push(t.pos,t.pos+e,i),!r&&t.cm.display.input.needsContentAttribute&&(i||(i=t.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",n.id)),i&&(t.cm.display.input.setUneditable(i),t.content.appendChild(i)),t.pos+=e,t.trailingSpace=!1}function Ea(t,e,n){var r=t.markedSpans,i=t.text,o=0;if(r)for(var l,a,s,u,c,f,h,d=i.length,p=0,g=1,m="",b=0;;){if(b==p){s=u=c=a="",h=null,f=null,b=1/0;for(var w=[],y=void 0,S=0;S<r.length;++S){var C=r[S],T=C.marker;if(T.type=="bookmark"&&C.from==p&&T.widgetNode)w.push(T);else if(C.from<=p&&(C.to==null||C.to>p||T.collapsed&&C.to==p&&C.from==p)){if(C.to!=null&&C.to!=p&&b>C.to&&(b=C.to,u=""),T.className&&(s+=" "+T.className),T.css&&(a=(a?a+";":"")+T.css),T.startStyle&&C.from==p&&(c+=" "+T.startStyle),T.endStyle&&C.to==b&&(y||(y=[])).push(T.endStyle,C.to),T.title&&((h||(h={})).title=T.title),T.attributes)for(var G in T.attributes)(h||(h={}))[G]=T.attributes[G];T.collapsed&&(!f||jn(f.marker,T)<0)&&(f=C)}else C.from>p&&b>C.from&&(b=C.from)}if(y)for(var K=0;K<y.length;K+=2)y[K+1]==b&&(u+=" "+y[K]);if(!f||f.from==p)for(var St=0;St<w.length;++St)go(e,0,w[St]);if(f&&(f.from||0)==p){if(go(e,(f.to==null?d+1:f.to)-p,f.marker,f.from==null),f.to==null)return;f.to==p&&(f=!1)}}if(p>=d)break;for(var j=Math.min(d,b);;){if(m){var Et=p+m.length;if(!f){var kt=Et>j?m.slice(0,j-p):m;e.addToken(e,kt,l?l+s:s,c,p+kt.length==b?u:"",a,h)}if(Et>=j){m=m.slice(j-p),p=j;break}p=Et,c=""}m=i.slice(o,o=n[g++]),l=ho(n[g++],e.cm.options)}}else for(var q=1;q<n.length;q+=2)e.addToken(e,i.slice(o,o=n[q]),ho(n[q+1],e.cm.options))}function vo(t,e,n){this.line=e,this.rest=La(e),this.size=this.rest?V(B(this.rest))-n+1:1,this.node=this.text=null,this.hidden=re(t,e)}function qr(t,e,n){for(var r,i=[],o=e;o<n;o=r){var l=new vo(t.doc,M(t.doc,o),o);r=o+l.size,i.push(l)}return i}var Ie=null;function Fa(t){Ie?Ie.ops.push(t):t.ownsGroup=Ie={ops:[t],delayedCallbacks:[]}}function Pa(t){var e=t.delayedCallbacks,n=0;do{for(;n<e.length;n++)e[n].call(null);for(var r=0;r<t.ops.length;r++){var i=t.ops[r];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(n<e.length)}function Ia(t,e){var n=t.ownsGroup;if(n)try{Pa(n)}finally{Ie=null,e(n)}}var cr=null;function ot(t,e){var n=On(t,e);if(n.length){var r,i=Array.prototype.slice.call(arguments,2);Ie?r=Ie.delayedCallbacks:cr?r=cr:(r=cr=[],setTimeout(za,0));for(var o=function(a){r.push(function(){return n[a].apply(null,i)})},l=0;l<n.length;++l)o(l)}}function za(){var t=cr;cr=null;for(var e=0;e<t.length;++e)t[e]()}function mo(t,e,n,r){for(var i=0;i<e.changes.length;i++){var o=e.changes[i];o=="text"?Ba(t,e):o=="gutter"?bo(t,e,n,r):o=="class"?Zn(t,e):o=="widget"&&Va(t,e,r)}e.changes=null}function fr(t){return t.node==t.text&&(t.node=O("div",null,null,"position: relative"),t.text.parentNode&&t.text.parentNode.replaceChild(t.node,t.text),t.node.appendChild(t.text),H&&P<8&&(t.node.style.zIndex=2)),t.node}function Ra(t,e){var n=e.bgClass?e.bgClass+" "+(e.line.bgClass||""):e.line.bgClass;if(n&&(n+=" CodeMirror-linebackground"),e.background)n?e.background.className=n:(e.background.parentNode.removeChild(e.background),e.background=null);else if(n){var r=fr(e);e.background=r.insertBefore(O("div",null,n),r.firstChild),t.display.input.setUneditable(e.background)}}function yo(t,e){var n=t.display.externalMeasured;return n&&n.line==e.line?(t.display.externalMeasured=null,e.measure=n.measure,n.built):po(t,e)}function Ba(t,e){var n=e.text.className,r=yo(t,e);e.text==e.node&&(e.node=r.pre),e.text.parentNode.replaceChild(r.pre,e.text),e.text=r.pre,r.bgClass!=e.bgClass||r.textClass!=e.textClass?(e.bgClass=r.bgClass,e.textClass=r.textClass,Zn(t,e)):n&&(e.text.className=n)}function Zn(t,e){Ra(t,e),e.line.wrapClass?fr(e).className=e.line.wrapClass:e.node!=e.text&&(e.node.className="");var n=e.textClass?e.textClass+" "+(e.line.textClass||""):e.line.textClass;e.text.className=n||""}function bo(t,e,n,r){if(e.gutter&&(e.node.removeChild(e.gutter),e.gutter=null),e.gutterBackground&&(e.node.removeChild(e.gutterBackground),e.gutterBackground=null),e.line.gutterClass){var i=fr(e);e.gutterBackground=O("div",null,"CodeMirror-gutter-background "+e.line.gutterClass,"left: "+(t.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px; width: "+r.gutterTotalWidth+"px"),t.display.input.setUneditable(e.gutterBackground),i.insertBefore(e.gutterBackground,e.text)}var o=e.line.gutterMarkers;if(t.options.lineNumbers||o){var l=fr(e),a=e.gutter=O("div",null,"CodeMirror-gutter-wrapper","left: "+(t.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px");if(a.setAttribute("aria-hidden","true"),t.display.input.setUneditable(a),l.insertBefore(a,e.text),e.line.gutterClass&&(a.className+=" "+e.line.gutterClass),!t.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(e.lineNumber=a.appendChild(O("div",Vn(t.options,n),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+r.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+t.display.lineNumInnerWidth+"px"))),o)for(var s=0;s<t.display.gutterSpecs.length;++s){var u=t.display.gutterSpecs[s].className,c=o.hasOwnProperty(u)&&o[u];c&&a.appendChild(O("div",[c],"CodeMirror-gutter-elt","left: "+r.gutterLeft[u]+"px; width: "+r.gutterWidth[u]+"px"))}}}function Va(t,e,n){e.alignable&&(e.alignable=null);for(var r=Oe("CodeMirror-linewidget"),i=e.node.firstChild,o=void 0;i;i=o)o=i.nextSibling,r.test(i.className)&&e.node.removeChild(i);xo(t,e,n)}function Ua(t,e,n,r){var i=yo(t,e);return e.text=e.node=i.pre,i.bgClass&&(e.bgClass=i.bgClass),i.textClass&&(e.textClass=i.textClass),Zn(t,e),bo(t,e,n,r),xo(t,e,r),e.node}function xo(t,e,n){if(wo(t,e.line,e,n,!0),e.rest)for(var r=0;r<e.rest.length;r++)wo(t,e.rest[r],e,n,!1)}function wo(t,e,n,r,i){if(e.widgets)for(var o=fr(n),l=0,a=e.widgets;l<a.length;++l){var s=a[l],u=O("div",[s.node],"CodeMirror-linewidget"+(s.className?" "+s.className:""));s.handleMouseEvents||u.setAttribute("cm-ignore-events","true"),Ga(s,u,n,r),t.display.input.setUneditable(u),i&&s.above?o.insertBefore(u,n.gutter||n.text):o.appendChild(u),ot(s,"redraw")}}function Ga(t,e,n,r){if(t.noHScroll){(n.alignable||(n.alignable=[])).push(e);var i=r.wrapperWidth;e.style.left=r.fixedPos+"px",t.coverGutter||(i-=r.gutterTotalWidth,e.style.paddingLeft=r.gutterTotalWidth+"px"),e.style.width=i+"px"}t.coverGutter&&(e.style.zIndex=5,e.style.position="relative",t.noHScroll||(e.style.marginLeft=-r.gutterTotalWidth+"px"))}function hr(t){if(t.height!=null)return t.height;var e=t.doc.cm;if(!e)return 0;if(!te(document.body,t.node)){var n="position: relative;";t.coverGutter&&(n+="margin-left: -"+e.display.gutters.offsetWidth+"px;"),t.noHScroll&&(n+="width: "+e.display.wrapper.clientWidth+"px;"),Mt(e.display.measure,O("div",[t.node],null,n))}return t.height=t.node.parentNode.offsetHeight}function Xt(t,e){for(var n=Dn(e);n!=t.wrapper;n=n.parentNode)if(!n||n.nodeType==1&&n.getAttribute("cm-ignore-events")=="true"||n.parentNode==t.sizer&&n!=t.mover)return!0}function Zr(t){return t.lineSpace.offsetTop}function Jn(t){return t.mover.offsetHeight-t.lineSpace.offsetHeight}function Co(t){if(t.cachedPaddingH)return t.cachedPaddingH;var e=Mt(t.measure,O("pre","x","CodeMirror-line-like")),n=window.getComputedStyle?window.getComputedStyle(e):e.currentStyle,r={left:parseInt(n.paddingLeft),right:parseInt(n.paddingRight)};return isNaN(r.left)||isNaN(r.right)||(t.cachedPaddingH=r),r}function Gt(t){return Ri-t.display.nativeBarWidth}function be(t){return t.display.scroller.clientWidth-Gt(t)-t.display.barWidth}function Qn(t){return t.display.scroller.clientHeight-Gt(t)-t.display.barHeight}function Ka(t,e,n){var r=t.options.lineWrapping,i=r&&be(t);if(!e.measure.heights||r&&e.measure.width!=i){var o=e.measure.heights=[];if(r){e.measure.width=i;for(var l=e.text.firstChild.getClientRects(),a=0;a<l.length-1;a++){var s=l[a],u=l[a+1];Math.abs(s.bottom-u.bottom)>2&&o.push((s.bottom+u.top)/2-n.top)}}o.push(n.bottom-n.top)}}function So(t,e,n){if(t.line==e)return{map:t.measure.map,cache:t.measure.cache};if(t.rest){for(var r=0;r<t.rest.length;r++)if(t.rest[r]==e)return{map:t.measure.maps[r],cache:t.measure.caches[r]};for(var i=0;i<t.rest.length;i++)if(V(t.rest[i])>n)return{map:t.measure.maps[i],cache:t.measure.caches[i],before:!0}}}function _a(t,e){var n=V(e=Ft(e)),r=t.display.externalMeasured=new vo(t.doc,e,n);r.lineN=n;var i=r.built=po(t,r);return r.text=i.pre,Mt(t.display.lineMeasure,i.pre),r}function ko(t,e,n,r){return Kt(t,ze(t,e),n,r)}function ti(t,e){if(e>=t.display.viewFrom&&e<t.display.viewTo)return t.display.view[Ce(t,e)];var n=t.display.externalMeasured;return n&&e>=n.lineN&&e<n.lineN+n.size?n:void 0}function ze(t,e){var n=V(e),r=ti(t,n);r&&!r.text?r=null:r&&r.changes&&(mo(t,r,n,oi(t)),t.curOp.forceUpdate=!0),r||(r=_a(t,e));var i=So(r,e,n);return{line:e,view:r,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function Kt(t,e,n,r,i){e.before&&(n=-1);var o,l=n+(r||"");return e.cache.hasOwnProperty(l)?o=e.cache[l]:(e.rect||(e.rect=e.view.text.getBoundingClientRect()),e.hasHeights||(Ka(t,e.view,e.rect),e.hasHeights=!0),(o=ja(t,e,n,r)).bogus||(e.cache[l]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}var xe,Lo={left:0,right:0,top:0,bottom:0};function To(t,e,n){for(var r,i,o,l,a,s,u=0;u<t.length;u+=3)if(a=t[u],s=t[u+1],e<a?(i=0,o=1,l="left"):e<s?o=1+(i=e-a):(u==t.length-3||e==s&&t[u+3]>e)&&(i=(o=s-a)-1,e>=s&&(l="right")),i!=null){if(r=t[u+2],a==s&&n==(r.insertLeft?"left":"right")&&(l=n),n=="left"&&i==0)for(;u&&t[u-2]==t[u-3]&&t[u-1].insertLeft;)r=t[2+(u-=3)],l="left";if(n=="right"&&i==s-a)for(;u<t.length-3&&t[u+3]==t[u+4]&&!t[u+5].insertLeft;)r=t[(u+=3)+2],l="right";break}return{node:r,start:i,end:o,collapse:l,coverStart:a,coverEnd:s}}function $a(t,e){var n=Lo;if(e=="left")for(var r=0;r<t.length&&(n=t[r]).left==n.right;r++);else for(var i=t.length-1;i>=0&&(n=t[i]).left==n.right;i--);return n}function ja(t,e,n,r){var i,o=To(e.map,n,r),l=o.node,a=o.start,s=o.end,u=o.collapse;if(l.nodeType==3){for(var c=0;c<4;c++){for(;a&&Nn(e.line.text.charAt(o.coverStart+a));)--a;for(;o.coverStart+s<o.coverEnd&&Nn(e.line.text.charAt(o.coverStart+s));)++s;if((i=H&&P<9&&a==0&&s==o.coverEnd-o.coverStart?l.parentNode.getBoundingClientRect():$a(Ae(l,a,s).getClientRects(),r)).left||i.right||a==0)break;s=a,a-=1,u="right"}H&&P<11&&(i=Xa(t.display.measure,i))}else{var f;a>0&&(u=r="right"),i=t.options.lineWrapping&&(f=l.getClientRects()).length>1?f[r=="right"?f.length-1:0]:l.getBoundingClientRect()}if(H&&P<9&&!a&&(!i||!i.left&&!i.right)){var h=l.parentNode.getClientRects()[0];i=h?{left:h.left,right:h.left+Be(t.display),top:h.top,bottom:h.bottom}:Lo}for(var d=i.top-e.rect.top,p=i.bottom-e.rect.top,g=(d+p)/2,m=e.view.measure.heights,b=0;b<m.length-1&&!(g<m[b]);b++);var w=b?m[b-1]:0,y=m[b],S={left:(u=="right"?i.right:i.left)-e.rect.left,right:(u=="left"?i.left:i.right)-e.rect.left,top:w,bottom:y};return i.left||i.right||(S.bogus=!0),t.options.singleCursorHeightPerLine||(S.rtop=d,S.rbottom=p),S}function Xa(t,e){if(!window.screen||screen.logicalXDPI==null||screen.logicalXDPI==screen.deviceXDPI||!ua(t))return e;var n=screen.logicalXDPI/screen.deviceXDPI,r=screen.logicalYDPI/screen.deviceYDPI;return{left:e.left*n,right:e.right*n,top:e.top*r,bottom:e.bottom*r}}function Mo(t){if(t.measure&&(t.measure.cache={},t.measure.heights=null,t.rest))for(var e=0;e<t.rest.length;e++)t.measure.caches[e]={}}function No(t){t.display.externalMeasure=null,Qt(t.display.lineMeasure);for(var e=0;e<t.display.view.length;e++)Mo(t.display.view[e])}function dr(t){No(t),t.display.cachedCharWidth=t.display.cachedTextHeight=t.display.cachedPaddingH=null,t.options.lineWrapping||(t.display.maxLineChanged=!0),t.display.lineNumChars=null}function Oo(t){return R&&wt?-(t.body.getBoundingClientRect().left-parseInt(getComputedStyle(t.body).marginLeft)):t.defaultView.pageXOffset||(t.documentElement||t.body).scrollLeft}function Ao(t){return R&&wt?-(t.body.getBoundingClientRect().top-parseInt(getComputedStyle(t.body).marginTop)):t.defaultView.pageYOffset||(t.documentElement||t.body).scrollTop}function ei(t){var e=Ft(t).widgets,n=0;if(e)for(var r=0;r<e.length;++r)e[r].above&&(n+=hr(e[r]));return n}function Jr(t,e,n,r,i){if(!i){var o=ei(e);n.top+=o,n.bottom+=o}if(r=="line")return n;r||(r="local");var l=jt(e);if(r=="local"?l+=Zr(t.display):l-=t.display.viewOffset,r=="page"||r=="window"){var a=t.display.lineSpace.getBoundingClientRect();l+=a.top+(r=="window"?0:Ao(er(t)));var s=a.left+(r=="window"?0:Oo(er(t)));n.left+=s,n.right+=s}return n.top+=l,n.bottom+=l,n}function Do(t,e,n){if(n=="div")return e;var r=e.left,i=e.top;if(n=="page")r-=Oo(er(t)),i-=Ao(er(t));else if(n=="local"||!n){var o=t.display.sizer.getBoundingClientRect();r+=o.left,i+=o.top}var l=t.display.lineSpace.getBoundingClientRect();return{left:r-l.left,top:i-l.top}}function Qr(t,e,n,r,i){return r||(r=M(t.doc,e.line)),Jr(t,r,ko(t,r,e.ch,i),n)}function Pt(t,e,n,r,i,o){function l(p,g){var m=Kt(t,i,p,g?"right":"left",o);return g?m.left=m.right:m.right=m.left,Jr(t,r,m,n)}r=r||M(t.doc,e.line),i||(i=ze(t,r));var a=_t(r,t.doc.direction),s=e.ch,u=e.sticky;if(s>=r.text.length?(s=r.text.length,u="before"):s<=0&&(s=0,u="after"),!a)return l(u=="before"?s-1:s,u=="before");function c(p,g,m){return l(m?p-1:p,a[g].level==1!=m)}var f=or(a,s,u),h=ir,d=c(s,f,u=="before");return h!=null&&(d.other=c(s,h,u!="before")),d}function Wo(t,e){var n=0;e=F(t.doc,e),t.options.lineWrapping||(n=Be(t.display)*e.ch);var r=M(t.doc,e.line),i=jt(r)+Zr(t.display);return{left:n,right:n,top:i,bottom:i+r.height}}function ri(t,e,n,r,i){var o=v(t,e,n);return o.xRel=i,r&&(o.outside=r),o}function ni(t,e,n){var r=t.doc;if((n+=t.display.viewOffset)<0)return ri(r.first,0,null,-1,-1);var i=ye(r,n),o=r.first+r.size-1;if(i>o)return ri(r.first+r.size-1,M(r,o).text.length,null,1,1);e<0&&(e=0);for(var l=M(r,i);;){var a=Ya(t,l,i,e,n),s=Sa(l,a.ch+(a.xRel>0||a.outside>0?1:0));if(!s)return a;var u=s.find(1);if(u.line==i)return u;l=M(r,i=u.line)}}function Ho(t,e,n,r){r-=ei(e);var i=e.text.length,o=nr(function(l){return Kt(t,n,l-1).bottom<=r},i,0);return{begin:o,end:i=nr(function(l){return Kt(t,n,l).top>r},o,i)}}function Eo(t,e,n,r){return n||(n=ze(t,e)),Ho(t,e,n,Jr(t,e,Kt(t,n,r),"line").top)}function ii(t,e,n,r){return!(t.bottom<=n)&&(t.top>n||(r?t.left:t.right)>e)}function Ya(t,e,n,r,i){i-=jt(e);var o=ze(t,e),l=ei(e),a=0,s=e.text.length,u=!0,c=_t(e,t.doc.direction);if(c){var f=(t.options.lineWrapping?Za:qa)(t,e,n,o,c,r,i);a=(u=f.level!=1)?f.from:f.to-1,s=u?f.to:f.from-1}var h,d,p=null,g=null,m=nr(function(C){var T=Kt(t,o,C);return T.top+=l,T.bottom+=l,!!ii(T,r,i,!1)&&(T.top<=i&&T.left<=r&&(p=C,g=T),!0)},a,s),b=!1;if(g){var w=r-g.left<g.right-r,y=w==u;m=p+(y?0:1),d=y?"after":"before",h=w?g.left:g.right}else{u||m!=s&&m!=a||m++,d=m==0?"after":m==e.text.length?"before":Kt(t,o,m-(u?1:0)).bottom+l<=i==u?"after":"before";var S=Pt(t,v(n,m,d),"line",e,o);h=S.left,b=i<S.top?-1:i>=S.bottom?1:0}return ri(n,m=Gi(e.text,m,1),d,b,r-h)}function qa(t,e,n,r,i,o,l){var a=nr(function(f){var h=i[f],d=h.level!=1;return ii(Pt(t,v(n,d?h.to:h.from,d?"before":"after"),"line",e,r),o,l,!0)},0,i.length-1),s=i[a];if(a>0){var u=s.level!=1,c=Pt(t,v(n,u?s.from:s.to,u?"after":"before"),"line",e,r);ii(c,o,l,!0)&&c.top>l&&(s=i[a-1])}return s}function Za(t,e,n,r,i,o,l){var a=Ho(t,e,r,l),s=a.begin,u=a.end;/\s/.test(e.text.charAt(u-1))&&u--;for(var c=null,f=null,h=0;h<i.length;h++){var d=i[h];if(!(d.from>=u||d.to<=s)){var p=Kt(t,r,d.level!=1?Math.min(u,d.to)-1:Math.max(s,d.from)).right,g=p<o?o-p+1e9:p-o;(!c||f>g)&&(c=d,f=g)}}return c||(c=i[i.length-1]),c.from<s&&(c={from:s,to:c.to,level:c.level}),c.to>u&&(c={from:c.from,to:u,level:c.level}),c}function Re(t){if(t.cachedTextHeight!=null)return t.cachedTextHeight;if(xe==null){xe=O("pre",null,"CodeMirror-line-like");for(var e=0;e<49;++e)xe.appendChild(document.createTextNode("x")),xe.appendChild(O("br"));xe.appendChild(document.createTextNode("x"))}Mt(t.measure,xe);var n=xe.offsetHeight/50;return n>3&&(t.cachedTextHeight=n),Qt(t.measure),n||1}function Be(t){if(t.cachedCharWidth!=null)return t.cachedCharWidth;var e=O("span","xxxxxxxxxx"),n=O("pre",[e],"CodeMirror-line-like");Mt(t.measure,n);var r=e.getBoundingClientRect(),i=(r.right-r.left)/10;return i>2&&(t.cachedCharWidth=i),i||10}function oi(t){for(var e=t.display,n={},r={},i=e.gutters.clientLeft,o=e.gutters.firstChild,l=0;o;o=o.nextSibling,++l){var a=t.display.gutterSpecs[l].className;n[a]=o.offsetLeft+o.clientLeft+i,r[a]=o.clientWidth}return{fixedPos:li(e),gutterTotalWidth:e.gutters.offsetWidth,gutterLeft:n,gutterWidth:r,wrapperWidth:e.wrapper.clientWidth}}function li(t){return t.scroller.getBoundingClientRect().left-t.sizer.getBoundingClientRect().left}function Fo(t){var e=Re(t.display),n=t.options.lineWrapping,r=n&&Math.max(5,t.display.scroller.clientWidth/Be(t.display)-3);return function(i){if(re(t.doc,i))return 0;var o=0;if(i.widgets)for(var l=0;l<i.widgets.length;l++)i.widgets[l].height&&(o+=i.widgets[l].height);return n?o+(Math.ceil(i.text.length/r)||1)*e:o+e}}function ai(t){var e=t.doc,n=Fo(t);e.iter(function(r){var i=n(r);i!=r.height&&Vt(r,i)})}function we(t,e,n,r){var i=t.display;if(!n&&Dn(e).getAttribute("cm-not-content")=="true")return null;var o,l,a=i.lineSpace.getBoundingClientRect();try{o=e.clientX-a.left,l=e.clientY-a.top}catch{return null}var s,u=ni(t,o,l);if(r&&u.xRel>0&&(s=M(t.doc,u.line).text).length==u.ch){var c=Wt(s,s.length,t.options.tabSize)-s.length;u=v(u.line,Math.max(0,Math.round((o-Co(t.display).left)/Be(t.display))-c))}return u}function Ce(t,e){if(e>=t.display.viewTo||(e-=t.display.viewFrom)<0)return null;for(var n=t.display.view,r=0;r<n.length;r++)if((e-=n[r].size)<0)return r}function gt(t,e,n,r){e==null&&(e=t.doc.first),n==null&&(n=t.doc.first+t.doc.size),r||(r=0);var i=t.display;if(r&&n<i.viewTo&&(i.updateLineNumbers==null||i.updateLineNumbers>e)&&(i.updateLineNumbers=e),t.curOp.viewChanged=!0,e>=i.viewTo)$t&&Xn(t.doc,e)<i.viewTo&&ie(t);else if(n<=i.viewFrom)$t&&fo(t.doc,n+r)>i.viewFrom?ie(t):(i.viewFrom+=r,i.viewTo+=r);else if(e<=i.viewFrom&&n>=i.viewTo)ie(t);else if(e<=i.viewFrom){var o=tn(t,n,n+r,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=r):ie(t)}else if(n>=i.viewTo){var l=tn(t,e,e,-1);l?(i.view=i.view.slice(0,l.index),i.viewTo=l.lineN):ie(t)}else{var a=tn(t,e,e,-1),s=tn(t,n,n+r,1);a&&s?(i.view=i.view.slice(0,a.index).concat(qr(t,a.lineN,s.lineN)).concat(i.view.slice(s.index)),i.viewTo+=r):ie(t)}var u=i.externalMeasured;u&&(n<u.lineN?u.lineN+=r:e<u.lineN+u.size&&(i.externalMeasured=null))}function ne(t,e,n){t.curOp.viewChanged=!0;var r=t.display,i=t.display.externalMeasured;if(i&&e>=i.lineN&&e<i.lineN+i.size&&(r.externalMeasured=null),!(e<r.viewFrom||e>=r.viewTo)){var o=r.view[Ce(t,e)];if(o.node!=null){var l=o.changes||(o.changes=[]);st(l,n)==-1&&l.push(n)}}}function ie(t){t.display.viewFrom=t.display.viewTo=t.doc.first,t.display.view=[],t.display.viewOffset=0}function tn(t,e,n,r){var i,o=Ce(t,e),l=t.display.view;if(!$t||n==t.doc.first+t.doc.size)return{index:o,lineN:n};for(var a=t.display.viewFrom,s=0;s<o;s++)a+=l[s].size;if(a!=e){if(r>0){if(o==l.length-1)return null;i=a+l[o].size-e,o++}else i=a-e;e+=i,n+=i}for(;Xn(t.doc,n)!=n;){if(o==(r<0?0:l.length-1))return null;n+=r*l[o-(r<0?1:0)].size,o+=r}return{index:o,lineN:n}}function Ja(t,e,n){var r=t.display;r.view.length==0||e>=r.viewTo||n<=r.viewFrom?(r.view=qr(t,e,n),r.viewFrom=e):(r.viewFrom>e?r.view=qr(t,e,r.viewFrom).concat(r.view):r.viewFrom<e&&(r.view=r.view.slice(Ce(t,e))),r.viewFrom=e,r.viewTo<n?r.view=r.view.concat(qr(t,r.viewTo,n)):r.viewTo>n&&(r.view=r.view.slice(0,Ce(t,n)))),r.viewTo=n}function Po(t){for(var e=t.display.view,n=0,r=0;r<e.length;r++){var i=e[r];i.hidden||i.node&&!i.changes||++n}return n}function pr(t){t.display.input.showSelection(t.display.input.prepareSelection())}function Io(t,e){e===void 0&&(e=!0);var n=t.doc,r={},i=r.cursors=document.createDocumentFragment(),o=r.selection=document.createDocumentFragment(),l=t.options.$customCursor;l&&(e=!0);for(var a=0;a<n.sel.ranges.length;a++)if(e||a!=n.sel.primIndex){var s=n.sel.ranges[a];if(!(s.from().line>=t.display.viewTo||s.to().line<t.display.viewFrom)){var u=s.empty();if(l){var c=l(t,s);c&&si(t,c,i)}else(u||t.options.showCursorWhenSelecting)&&si(t,s.head,i);u||Qa(t,s,o)}}return r}function si(t,e,n){var r=Pt(t,e,"div",null,null,!t.options.singleCursorHeightPerLine),i=n.appendChild(O("div","\xA0","CodeMirror-cursor"));if(i.style.left=r.left+"px",i.style.top=r.top+"px",i.style.height=Math.max(0,r.bottom-r.top)*t.options.cursorHeight+"px",/\bcm-fat-cursor\b/.test(t.getWrapperElement().className)){var o=Qr(t,e,"div",null,null),l=o.right-o.left;i.style.width=(l>0?l:t.defaultCharWidth())+"px"}if(r.other){var a=n.appendChild(O("div","\xA0","CodeMirror-cursor CodeMirror-secondarycursor"));a.style.display="",a.style.left=r.other.left+"px",a.style.top=r.other.top+"px",a.style.height=.85*(r.other.bottom-r.other.top)+"px"}}function en(t,e){return t.top-e.top||t.left-e.left}function Qa(t,e,n){var r=t.display,i=t.doc,o=document.createDocumentFragment(),l=Co(t.display),a=l.left,s=Math.max(r.sizerWidth,be(t)-r.sizer.offsetLeft)-l.right,u=i.direction=="ltr";function c(y,S,C,T){S<0&&(S=0),S=Math.round(S),T=Math.round(T),o.appendChild(O("div",null,"CodeMirror-selected","position: absolute; left: "+y+`px;
                             top: `+S+"px; width: "+(C??s-y)+`px;
                             height: `+(T-S)+"px"))}function f(y,S,C){var T,G,K=M(i,y),St=K.text.length;function j(q,mt){return Qr(t,v(y,q),"div",K,mt)}function Et(q,mt,ht){var et=Eo(t,K,null,q),J=mt=="ltr"==(ht=="after")?"left":"right";return j(ht=="after"?et.begin:et.end-(/\s/.test(K.text.charAt(et.end-1))?2:1),J)[J]}var kt=_t(K,i.direction);return ra(kt,S||0,C??St,function(q,mt,ht,et){var J=ht=="ltr",rt=j(q,J?"left":"right"),yt=j(mt-1,J?"right":"left"),ce=S==null&&q==0,Rt=C==null&&mt==St,ct=et==0,nt=!kt||et==kt.length-1;if(yt.top-rt.top<=3){var Fi=(u?Rt:ce)&&nt,Zt=(u?ce:Rt)&&ct?a:(J?rt:yt).left,Me=Fi?s:(J?yt:rt).right;c(Zt,rt.top,Me-Zt,rt.bottom)}else{var Ne,dt,Qe,Pi;J?(Ne=u&&ce&&ct?a:rt.left,dt=u?s:Et(q,ht,"before"),Qe=u?a:Et(mt,ht,"after"),Pi=u&&Rt&&nt?s:yt.right):(Ne=u?Et(q,ht,"before"):a,dt=!u&&ce&&ct?s:rt.right,Qe=!u&&Rt&&nt?a:yt.left,Pi=u?Et(mt,ht,"after"):s),c(Ne,rt.top,dt-Ne,rt.bottom),rt.bottom<yt.top&&c(a,rt.bottom,null,yt.top),c(Qe,yt.top,Pi-Qe,yt.bottom)}(!T||en(rt,T)<0)&&(T=rt),en(yt,T)<0&&(T=yt),(!G||en(rt,G)<0)&&(G=rt),en(yt,G)<0&&(G=yt)}),{start:T,end:G}}var h=e.from(),d=e.to();if(h.line==d.line)f(h.line,h.ch,d.ch);else{var p=M(i,h.line),g=M(i,d.line),m=Ft(p)==Ft(g),b=f(h.line,h.ch,m?p.text.length+1:null).end,w=f(d.line,m?0:null,d.ch).start;m&&(b.top<w.top-2?(c(b.right,b.top,null,b.bottom),c(a,w.top,w.left,w.bottom)):c(b.right,b.top,w.left-b.right,b.bottom)),b.bottom<w.top&&c(a,b.bottom,null,w.top)}n.appendChild(o)}function ui(t){if(t.state.focused){var e=t.display;clearInterval(e.blinker);var n=!0;e.cursorDiv.style.visibility="",t.options.cursorBlinkRate>0?e.blinker=setInterval(function(){t.hasFocus()||Ve(t),e.cursorDiv.style.visibility=(n=!n)?"":"hidden"},t.options.cursorBlinkRate):t.options.cursorBlinkRate<0&&(e.cursorDiv.style.visibility="hidden")}}function zo(t){t.hasFocus()||(t.display.input.focus(),t.state.focused||fi(t))}function ci(t){t.state.delayingBlurEvent=!0,setTimeout(function(){t.state.delayingBlurEvent&&(t.state.delayingBlurEvent=!1,t.state.focused&&Ve(t))},100)}function fi(t,e){t.state.delayingBlurEvent&&!t.state.draggingText&&(t.state.delayingBlurEvent=!1),t.options.readOnly!="nocursor"&&(t.state.focused||(Q(t,"focus",t,e),t.state.focused=!0,de(t.display.wrapper,"CodeMirror-focused"),t.curOp||t.display.selForContextMenu==t.doc.sel||(t.display.input.reset(),A&&setTimeout(function(){return t.display.input.reset(!0)},20)),t.display.input.receivedFocus()),ui(t))}function Ve(t,e){t.state.delayingBlurEvent||(t.state.focused&&(Q(t,"blur",t,e),t.state.focused=!1,he(t.display.wrapper,"CodeMirror-focused")),clearInterval(t.display.blinker),setTimeout(function(){t.state.focused||(t.display.shift=!1)},150))}function rn(t){for(var e=t.display,n=e.lineDiv.offsetTop,r=Math.max(0,e.scroller.getBoundingClientRect().top),i=e.lineDiv.getBoundingClientRect().top,o=0,l=0;l<e.view.length;l++){var a=e.view[l],s=t.options.lineWrapping,u=void 0,c=0;if(!a.hidden){if(i+=a.line.height,H&&P<8){var f=a.node.offsetTop+a.node.offsetHeight;u=f-n,n=f}else{var h=a.node.getBoundingClientRect();u=h.bottom-h.top,!s&&a.text.firstChild&&(c=a.text.firstChild.getBoundingClientRect().right-h.left-1)}var d=a.line.height-u;if((d>.005||d<-.005)&&(i<r&&(o-=d),Vt(a.line,u),Ro(a.line),a.rest))for(var p=0;p<a.rest.length;p++)Ro(a.rest[p]);if(c>t.display.sizerWidth){var g=Math.ceil(c/Be(t.display));g>t.display.maxLineLength&&(t.display.maxLineLength=g,t.display.maxLine=a.line,t.display.maxLineChanged=!0)}}}Math.abs(o)>2&&(e.scroller.scrollTop+=o)}function Ro(t){if(t.widgets)for(var e=0;e<t.widgets.length;++e){var n=t.widgets[e],r=n.node.parentNode;r&&(n.height=r.offsetHeight)}}function nn(t,e,n){var r=n&&n.top!=null?Math.max(0,n.top):t.scroller.scrollTop;r=Math.floor(r-Zr(t));var i=n&&n.bottom!=null?n.bottom:r+t.wrapper.clientHeight,o=ye(e,r),l=ye(e,i);if(n&&n.ensure){var a=n.ensure.from.line,s=n.ensure.to.line;a<o?(o=a,l=ye(e,jt(M(e,a))+t.wrapper.clientHeight)):Math.min(s,e.lastLine())>=l&&(o=ye(e,jt(M(e,s))-t.wrapper.clientHeight),l=s)}return{from:o,to:Math.max(l,o+1)}}function ts(t,e){if(!it(t,"scrollCursorIntoView")){var n=t.display,r=n.sizer.getBoundingClientRect(),i=null,o=n.wrapper.ownerDocument;if(e.top+r.top<0?i=!0:e.bottom+r.top>(o.defaultView.innerHeight||o.documentElement.clientHeight)&&(i=!1),i!=null&&!N){var l=O("div","\u200B",null,`position: absolute;
                         top: `+(e.top-n.viewOffset-Zr(t.display))+`px;
                         height: `+(e.bottom-e.top+Gt(t)+n.barHeight)+`px;
                         left: `+e.left+"px; width: "+Math.max(2,e.right-e.left)+"px;");t.display.lineSpace.appendChild(l),l.scrollIntoView(i),t.display.lineSpace.removeChild(l)}}}function es(t,e,n,r){var i;r==null&&(r=0),t.options.lineWrapping||e!=n||(n=e.sticky=="before"?v(e.line,e.ch+1,"before"):e,e=e.ch?v(e.line,e.sticky=="before"?e.ch-1:e.ch,"after"):e);for(var o=0;o<5;o++){var l=!1,a=Pt(t,e),s=n&&n!=e?Pt(t,n):a,u=hi(t,i={left:Math.min(a.left,s.left),top:Math.min(a.top,s.top)-r,right:Math.max(a.left,s.left),bottom:Math.max(a.bottom,s.bottom)+r}),c=t.doc.scrollTop,f=t.doc.scrollLeft;if(u.scrollTop!=null&&(vr(t,u.scrollTop),Math.abs(t.doc.scrollTop-c)>1&&(l=!0)),u.scrollLeft!=null&&(Se(t,u.scrollLeft),Math.abs(t.doc.scrollLeft-f)>1&&(l=!0)),!l)break}return i}function rs(t,e){var n=hi(t,e);n.scrollTop!=null&&vr(t,n.scrollTop),n.scrollLeft!=null&&Se(t,n.scrollLeft)}function hi(t,e){var n=t.display,r=Re(t.display);e.top<0&&(e.top=0);var i=t.curOp&&t.curOp.scrollTop!=null?t.curOp.scrollTop:n.scroller.scrollTop,o=Qn(t),l={};e.bottom-e.top>o&&(e.bottom=e.top+o);var a=t.doc.height+Jn(n),s=e.top<r,u=e.bottom>a-r;if(e.top<i)l.scrollTop=s?0:e.top;else if(e.bottom>i+o){var c=Math.min(e.top,(u?a:e.bottom)-o);c!=i&&(l.scrollTop=c)}var f=t.options.fixedGutter?0:n.gutters.offsetWidth,h=t.curOp&&t.curOp.scrollLeft!=null?t.curOp.scrollLeft:n.scroller.scrollLeft-f,d=be(t)-n.gutters.offsetWidth,p=e.right-e.left>d;return p&&(e.right=e.left+d),e.left<10?l.scrollLeft=0:e.left<h?l.scrollLeft=Math.max(0,e.left+f-(p?0:10)):e.right>d+h-3&&(l.scrollLeft=e.right+(p?0:10)-d),l}function di(t,e){e!=null&&(on(t),t.curOp.scrollTop=(t.curOp.scrollTop==null?t.doc.scrollTop:t.curOp.scrollTop)+e)}function Ue(t){on(t);var e=t.getCursor();t.curOp.scrollToPos={from:e,to:e,margin:t.options.cursorScrollMargin}}function gr(t,e,n){e==null&&n==null||on(t),e!=null&&(t.curOp.scrollLeft=e),n!=null&&(t.curOp.scrollTop=n)}function ns(t,e){on(t),t.curOp.scrollToPos=e}function on(t){var e=t.curOp.scrollToPos;e&&(t.curOp.scrollToPos=null,Bo(t,Wo(t,e.from),Wo(t,e.to),e.margin))}function Bo(t,e,n,r){var i=hi(t,{left:Math.min(e.left,n.left),top:Math.min(e.top,n.top)-r,right:Math.max(e.right,n.right),bottom:Math.max(e.bottom,n.bottom)+r});gr(t,i.scrollLeft,i.scrollTop)}function vr(t,e){Math.abs(t.doc.scrollTop-e)<2||($||gi(t,{top:e}),Vo(t,e,!0),$&&gi(t),br(t,100))}function Vo(t,e,n){e=Math.max(0,Math.min(t.display.scroller.scrollHeight-t.display.scroller.clientHeight,e)),(t.display.scroller.scrollTop!=e||n)&&(t.doc.scrollTop=e,t.display.scrollbars.setScrollTop(e),t.display.scroller.scrollTop!=e&&(t.display.scroller.scrollTop=e))}function Se(t,e,n,r){e=Math.max(0,Math.min(e,t.display.scroller.scrollWidth-t.display.scroller.clientWidth)),(n?e==t.doc.scrollLeft:Math.abs(t.doc.scrollLeft-e)<2)&&!r||(t.doc.scrollLeft=e,$o(t),t.display.scroller.scrollLeft!=e&&(t.display.scroller.scrollLeft=e),t.display.scrollbars.setScrollLeft(e))}function mr(t){var e=t.display,n=e.gutters.offsetWidth,r=Math.round(t.doc.height+Jn(t.display));return{clientHeight:e.scroller.clientHeight,viewHeight:e.wrapper.clientHeight,scrollWidth:e.scroller.scrollWidth,clientWidth:e.scroller.clientWidth,viewWidth:e.wrapper.clientWidth,barLeft:t.options.fixedGutter?n:0,docHeight:r,scrollHeight:r+Gt(t)+e.barHeight,nativeBarWidth:e.nativeBarWidth,gutterWidth:n}}var ke=function(t,e,n){this.cm=n;var r=this.vert=O("div",[O("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=O("div",[O("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");r.tabIndex=i.tabIndex=-1,t(r),t(i),D(r,"scroll",function(){r.clientHeight&&e(r.scrollTop,"vertical")}),D(i,"scroll",function(){i.clientWidth&&e(i.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,H&&P<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};ke.prototype.update=function(t){var e=t.scrollWidth>t.clientWidth+1,n=t.scrollHeight>t.clientHeight+1,r=t.nativeBarWidth;if(n){this.vert.style.display="block",this.vert.style.bottom=e?r+"px":"0";var i=t.viewHeight-(e?r:0);this.vert.firstChild.style.height=Math.max(0,t.scrollHeight-t.clientHeight+i)+"px"}else this.vert.scrollTop=0,this.vert.style.display="",this.vert.firstChild.style.height="0";if(e){this.horiz.style.display="block",this.horiz.style.right=n?r+"px":"0",this.horiz.style.left=t.barLeft+"px";var o=t.viewWidth-t.barLeft-(n?r:0);this.horiz.firstChild.style.width=Math.max(0,t.scrollWidth-t.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&t.clientHeight>0&&(r==0&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:n?r:0,bottom:e?r:0}},ke.prototype.setScrollLeft=function(t){this.horiz.scrollLeft!=t&&(this.horiz.scrollLeft=t),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},ke.prototype.setScrollTop=function(t){this.vert.scrollTop!=t&&(this.vert.scrollTop=t),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},ke.prototype.zeroWidthHack=function(){var t=At&&!k?"12px":"18px";this.horiz.style.height=this.vert.style.width=t,this.horiz.style.visibility=this.vert.style.visibility="hidden",this.disableHoriz=new ee,this.disableVert=new ee},ke.prototype.enableZeroWidthBar=function(t,e,n){function r(){var i=t.getBoundingClientRect();(n=="vert"?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1))!=t?t.style.visibility="hidden":e.set(1e3,r)}t.style.visibility="",e.set(1e3,r)},ke.prototype.clear=function(){var t=this.horiz.parentNode;t.removeChild(this.horiz),t.removeChild(this.vert)};var yr=function(){};function Ge(t,e){e||(e=mr(t));var n=t.display.barWidth,r=t.display.barHeight;Uo(t,e);for(var i=0;i<4&&n!=t.display.barWidth||r!=t.display.barHeight;i++)n!=t.display.barWidth&&t.options.lineWrapping&&rn(t),Uo(t,mr(t)),n=t.display.barWidth,r=t.display.barHeight}function Uo(t,e){var n=t.display,r=n.scrollbars.update(e);n.sizer.style.paddingRight=(n.barWidth=r.right)+"px",n.sizer.style.paddingBottom=(n.barHeight=r.bottom)+"px",n.heightForcer.style.borderBottom=r.bottom+"px solid transparent",r.right&&r.bottom?(n.scrollbarFiller.style.display="block",n.scrollbarFiller.style.height=r.bottom+"px",n.scrollbarFiller.style.width=r.right+"px"):n.scrollbarFiller.style.display="",r.bottom&&t.options.coverGutterNextToScrollbar&&t.options.fixedGutter?(n.gutterFiller.style.display="block",n.gutterFiller.style.height=r.bottom+"px",n.gutterFiller.style.width=e.gutterWidth+"px"):n.gutterFiller.style.display=""}yr.prototype.update=function(){return{bottom:0,right:0}},yr.prototype.setScrollLeft=function(){},yr.prototype.setScrollTop=function(){},yr.prototype.clear=function(){};var Go={native:ke,null:yr};function Ko(t){t.display.scrollbars&&(t.display.scrollbars.clear(),t.display.scrollbars.addClass&&he(t.display.wrapper,t.display.scrollbars.addClass)),t.display.scrollbars=new Go[t.options.scrollbarStyle](function(e){t.display.wrapper.insertBefore(e,t.display.scrollbarFiller),D(e,"mousedown",function(){t.state.focused&&setTimeout(function(){return t.display.input.focus()},0)}),e.setAttribute("cm-not-content","true")},function(e,n){n=="horizontal"?Se(t,e):vr(t,e)},t),t.display.scrollbars.addClass&&de(t.display.wrapper,t.display.scrollbars.addClass)}var is=0;function Le(t){t.curOp={cm:t,viewChanged:!1,startHeight:t.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++is,markArrays:null},Fa(t.curOp)}function Te(t){var e=t.curOp;e&&Ia(e,function(n){for(var r=0;r<n.ops.length;r++)n.ops[r].cm.curOp=null;os(n)})}function os(t){for(var e=t.ops,n=0;n<e.length;n++)ls(e[n]);for(var r=0;r<e.length;r++)as(e[r]);for(var i=0;i<e.length;i++)ss(e[i]);for(var o=0;o<e.length;o++)us(e[o]);for(var l=0;l<e.length;l++)cs(e[l])}function ls(t){var e=t.cm,n=e.display;hs(e),t.updateMaxLine&&qn(e),t.mustUpdate=t.viewChanged||t.forceUpdate||t.scrollTop!=null||t.scrollToPos&&(t.scrollToPos.from.line<n.viewFrom||t.scrollToPos.to.line>=n.viewTo)||n.maxLineChanged&&e.options.lineWrapping,t.update=t.mustUpdate&&new ln(e,t.mustUpdate&&{top:t.scrollTop,ensure:t.scrollToPos},t.forceUpdate)}function as(t){t.updatedDisplay=t.mustUpdate&&pi(t.cm,t.update)}function ss(t){var e=t.cm,n=e.display;t.updatedDisplay&&rn(e),t.barMeasure=mr(e),n.maxLineChanged&&!e.options.lineWrapping&&(t.adjustWidthTo=ko(e,n.maxLine,n.maxLine.text.length).left+3,e.display.sizerWidth=t.adjustWidthTo,t.barMeasure.scrollWidth=Math.max(n.scroller.clientWidth,n.sizer.offsetLeft+t.adjustWidthTo+Gt(e)+e.display.barWidth),t.maxScrollLeft=Math.max(0,n.sizer.offsetLeft+t.adjustWidthTo-be(e))),(t.updatedDisplay||t.selectionChanged)&&(t.preparedSelection=n.input.prepareSelection())}function us(t){var e=t.cm;t.adjustWidthTo!=null&&(e.display.sizer.style.minWidth=t.adjustWidthTo+"px",t.maxScrollLeft<e.doc.scrollLeft&&Se(e,Math.min(e.display.scroller.scrollLeft,t.maxScrollLeft),!0),e.display.maxLineChanged=!1);var n=t.focus&&t.focus==Dt(We(e));t.preparedSelection&&e.display.input.showSelection(t.preparedSelection,n),(t.updatedDisplay||t.startHeight!=e.doc.height)&&Ge(e,t.barMeasure),t.updatedDisplay&&mi(e,t.barMeasure),t.selectionChanged&&ui(e),e.state.focused&&t.updateInput&&e.display.input.reset(t.typing),n&&zo(t.cm)}function cs(t){var e=t.cm,n=e.display,r=e.doc;t.updatedDisplay&&_o(e,t.update),n.wheelStartX==null||t.scrollTop==null&&t.scrollLeft==null&&!t.scrollToPos||(n.wheelStartX=n.wheelStartY=null),t.scrollTop!=null&&Vo(e,t.scrollTop,t.forceScroll),t.scrollLeft!=null&&Se(e,t.scrollLeft,!0,!0),t.scrollToPos&&ts(e,es(e,F(r,t.scrollToPos.from),F(r,t.scrollToPos.to),t.scrollToPos.margin));var i=t.maybeHiddenMarkers,o=t.maybeUnhiddenMarkers;if(i)for(var l=0;l<i.length;++l)i[l].lines.length||Q(i[l],"hide");if(o)for(var a=0;a<o.length;++a)o[a].lines.length&&Q(o[a],"unhide");n.wrapper.offsetHeight&&(r.scrollTop=e.display.scroller.scrollTop),t.changeObjs&&Q(e,"changes",e,t.changeObjs),t.update&&t.update.finish()}function Ct(t,e){if(t.curOp)return e();Le(t);try{return e()}finally{Te(t)}}function lt(t,e){return function(){if(t.curOp)return e.apply(t,arguments);Le(t);try{return e.apply(t,arguments)}finally{Te(t)}}}function ft(t){return function(){if(this.curOp)return t.apply(this,arguments);Le(this);try{return t.apply(this,arguments)}finally{Te(this)}}}function at(t){return function(){var e=this.cm;if(!e||e.curOp)return t.apply(this,arguments);Le(e);try{return t.apply(this,arguments)}finally{Te(e)}}}function br(t,e){t.doc.highlightFrontier<t.display.viewTo&&t.state.highlight.set(e,Sn(fs,t))}function fs(t){var e=t.doc;if(!(e.highlightFrontier>=t.display.viewTo)){var n=+new Date+t.options.workTime,r=sr(t,e.highlightFrontier),i=[];e.iter(r.line,Math.min(e.first+e.size,t.display.viewTo+500),function(o){if(r.line>=t.display.viewFrom){var l=o.styles,a=o.text.length>t.options.maxHighlightLength?ve(e.mode,r.state):null,s=Zi(t,o,r,!0);a&&(r.state=a),o.styles=s.styles;var u=o.styleClasses,c=s.classes;c?o.styleClasses=c:u&&(o.styleClasses=null);for(var f=!l||l.length!=o.styles.length||u!=c&&(!u||!c||u.bgClass!=c.bgClass||u.textClass!=c.textClass),h=0;!f&&h<l.length;++h)f=l[h]!=o.styles[h];f&&i.push(r.line),o.stateAfter=r.save(),r.nextLine()}else o.text.length<=t.options.maxHighlightLength&&Kn(t,o.text,r),o.stateAfter=r.line%5==0?r.save():null,r.nextLine();if(+new Date>n)return br(t,t.options.workDelay),!0}),e.highlightFrontier=r.line,e.modeFrontier=Math.max(e.modeFrontier,r.line),i.length&&Ct(t,function(){for(var o=0;o<i.length;o++)ne(t,i[o],"text")})}}var ln=function(t,e,n){var r=t.display;this.viewport=e,this.visible=nn(r,t.doc,e),this.editorIsHidden=!r.wrapper.offsetWidth,this.wrapperHeight=r.wrapper.clientHeight,this.wrapperWidth=r.wrapper.clientWidth,this.oldDisplayWidth=be(t),this.force=n,this.dims=oi(t),this.events=[]};function hs(t){var e=t.display;!e.scrollbarsClipped&&e.scroller.offsetWidth&&(e.nativeBarWidth=e.scroller.offsetWidth-e.scroller.clientWidth,e.heightForcer.style.height=Gt(t)+"px",e.sizer.style.marginBottom=-e.nativeBarWidth+"px",e.sizer.style.borderRightWidth=Gt(t)+"px",e.scrollbarsClipped=!0)}function ds(t){if(t.hasFocus())return null;var e=Dt(We(t));if(!e||!te(t.display.lineDiv,e))return null;var n={activeElt:e};if(window.getSelection){var r=Pr(t).getSelection();r.anchorNode&&r.extend&&te(t.display.lineDiv,r.anchorNode)&&(n.anchorNode=r.anchorNode,n.anchorOffset=r.anchorOffset,n.focusNode=r.focusNode,n.focusOffset=r.focusOffset)}return n}function ps(t){if(t&&t.activeElt&&t.activeElt!=Dt(pe(t.activeElt))&&(t.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(t.activeElt.nodeName)&&t.anchorNode&&te(document.body,t.anchorNode)&&te(document.body,t.focusNode))){var e=t.activeElt.ownerDocument,n=e.defaultView.getSelection(),r=e.createRange();r.setEnd(t.anchorNode,t.anchorOffset),r.collapse(!1),n.removeAllRanges(),n.addRange(r),n.extend(t.focusNode,t.focusOffset)}}function pi(t,e){var n=t.display,r=t.doc;if(e.editorIsHidden)return ie(t),!1;if(!e.force&&e.visible.from>=n.viewFrom&&e.visible.to<=n.viewTo&&(n.updateLineNumbers==null||n.updateLineNumbers>=n.viewTo)&&n.renderedView==n.view&&Po(t)==0)return!1;jo(t)&&(ie(t),e.dims=oi(t));var i=r.first+r.size,o=Math.max(e.visible.from-t.options.viewportMargin,r.first),l=Math.min(i,e.visible.to+t.options.viewportMargin);n.viewFrom<o&&o-n.viewFrom<20&&(o=Math.max(r.first,n.viewFrom)),n.viewTo>l&&n.viewTo-l<20&&(l=Math.min(i,n.viewTo)),$t&&(o=Xn(t.doc,o),l=fo(t.doc,l));var a=o!=n.viewFrom||l!=n.viewTo||n.lastWrapHeight!=e.wrapperHeight||n.lastWrapWidth!=e.wrapperWidth;Ja(t,o,l),n.viewOffset=jt(M(t.doc,n.viewFrom)),t.display.mover.style.top=n.viewOffset+"px";var s=Po(t);if(!a&&s==0&&!e.force&&n.renderedView==n.view&&(n.updateLineNumbers==null||n.updateLineNumbers>=n.viewTo))return!1;var u=ds(t);return s>4&&(n.lineDiv.style.display="none"),gs(t,n.updateLineNumbers,e.dims),s>4&&(n.lineDiv.style.display=""),n.renderedView=n.view,ps(u),Qt(n.cursorDiv),Qt(n.selectionDiv),n.gutters.style.height=n.sizer.style.minHeight=0,a&&(n.lastWrapHeight=e.wrapperHeight,n.lastWrapWidth=e.wrapperWidth,br(t,400)),n.updateLineNumbers=null,!0}function _o(t,e){for(var n=e.viewport,r=!0;;r=!1){if(r&&t.options.lineWrapping&&e.oldDisplayWidth!=be(t))r&&(e.visible=nn(t.display,t.doc,n));else if(n&&n.top!=null&&(n={top:Math.min(t.doc.height+Jn(t.display)-Qn(t),n.top)}),e.visible=nn(t.display,t.doc,n),e.visible.from>=t.display.viewFrom&&e.visible.to<=t.display.viewTo)break;if(!pi(t,e))break;rn(t);var i=mr(t);pr(t),Ge(t,i),mi(t,i),e.force=!1}e.signal(t,"update",t),t.display.viewFrom==t.display.reportedViewFrom&&t.display.viewTo==t.display.reportedViewTo||(e.signal(t,"viewportChange",t,t.display.viewFrom,t.display.viewTo),t.display.reportedViewFrom=t.display.viewFrom,t.display.reportedViewTo=t.display.viewTo)}function gi(t,e){var n=new ln(t,e);if(pi(t,n)){rn(t),_o(t,n);var r=mr(t);pr(t),Ge(t,r),mi(t,r),n.finish()}}function gs(t,e,n){var r=t.display,i=t.options.lineNumbers,o=r.lineDiv,l=o.firstChild;function a(p){var g=p.nextSibling;return A&&At&&t.display.currentWheelTarget==p?p.style.display="none":p.parentNode.removeChild(p),g}for(var s=r.view,u=r.viewFrom,c=0;c<s.length;c++){var f=s[c];if(!f.hidden)if(f.node&&f.node.parentNode==o){for(;l!=f.node;)l=a(l);var h=i&&e!=null&&e<=u&&f.lineNumber;f.changes&&(st(f.changes,"gutter")>-1&&(h=!1),mo(t,f,u,n)),h&&(Qt(f.lineNumber),f.lineNumber.appendChild(document.createTextNode(Vn(t.options,u)))),l=f.node.nextSibling}else{var d=Ua(t,f,u,n);o.insertBefore(d,l)}u+=f.size}for(;l;)l=a(l)}function vi(t){var e=t.gutters.offsetWidth;t.sizer.style.marginLeft=e+"px",ot(t,"gutterChanged",t)}function mi(t,e){t.display.sizer.style.minHeight=e.docHeight+"px",t.display.heightForcer.style.top=e.docHeight+"px",t.display.gutters.style.height=e.docHeight+t.display.barHeight+Gt(t)+"px"}function $o(t){var e=t.display,n=e.view;if(e.alignWidgets||e.gutters.firstChild&&t.options.fixedGutter){for(var r=li(e)-e.scroller.scrollLeft+t.doc.scrollLeft,i=e.gutters.offsetWidth,o=r+"px",l=0;l<n.length;l++)if(!n[l].hidden){t.options.fixedGutter&&(n[l].gutter&&(n[l].gutter.style.left=o),n[l].gutterBackground&&(n[l].gutterBackground.style.left=o));var a=n[l].alignable;if(a)for(var s=0;s<a.length;s++)a[s].style.left=o}t.options.fixedGutter&&(e.gutters.style.left=r+i+"px")}}function jo(t){if(!t.options.lineNumbers)return!1;var e=t.doc,n=Vn(t.options,e.first+e.size-1),r=t.display;if(n.length!=r.lineNumChars){var i=r.measure.appendChild(O("div",[O("div",n)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,l=i.offsetWidth-o;return r.lineGutter.style.width="",r.lineNumInnerWidth=Math.max(o,r.lineGutter.offsetWidth-l)+1,r.lineNumWidth=r.lineNumInnerWidth+l,r.lineNumChars=r.lineNumInnerWidth?n.length:-1,r.lineGutter.style.width=r.lineNumWidth+"px",vi(t.display),!0}return!1}function yi(t,e){for(var n=[],r=!1,i=0;i<t.length;i++){var o=t[i],l=null;if(typeof o!="string"&&(l=o.style,o=o.className),o=="CodeMirror-linenumbers"){if(!e)continue;r=!0}n.push({className:o,style:l})}return e&&!r&&n.push({className:"CodeMirror-linenumbers",style:null}),n}function Xo(t){var e=t.gutters,n=t.gutterSpecs;Qt(e),t.lineGutter=null;for(var r=0;r<n.length;++r){var i=n[r],o=i.className,l=i.style,a=e.appendChild(O("div",null,"CodeMirror-gutter "+o));l&&(a.style.cssText=l),o=="CodeMirror-linenumbers"&&(t.lineGutter=a,a.style.width=(t.lineNumWidth||1)+"px")}e.style.display=n.length?"":"none",vi(t)}function xr(t){Xo(t.display),gt(t),$o(t)}function vs(t,e,n,r){var i=this;this.input=n,i.scrollbarFiller=O("div",null,"CodeMirror-scrollbar-filler"),i.scrollbarFiller.setAttribute("cm-not-content","true"),i.gutterFiller=O("div",null,"CodeMirror-gutter-filler"),i.gutterFiller.setAttribute("cm-not-content","true"),i.lineDiv=De("div",null,"CodeMirror-code"),i.selectionDiv=O("div",null,null,"position: relative; z-index: 1"),i.cursorDiv=O("div",null,"CodeMirror-cursors"),i.measure=O("div",null,"CodeMirror-measure"),i.lineMeasure=O("div",null,"CodeMirror-measure"),i.lineSpace=De("div",[i.measure,i.lineMeasure,i.selectionDiv,i.cursorDiv,i.lineDiv],null,"position: relative; outline: none");var o=De("div",[i.lineSpace],"CodeMirror-lines");i.mover=O("div",[o],null,"position: relative"),i.sizer=O("div",[i.mover],"CodeMirror-sizer"),i.sizerWidth=null,i.heightForcer=O("div",null,null,"position: absolute; height: "+Ri+"px; width: 1px;"),i.gutters=O("div",null,"CodeMirror-gutters"),i.lineGutter=null,i.scroller=O("div",[i.sizer,i.heightForcer,i.gutters],"CodeMirror-scroll"),i.scroller.setAttribute("tabIndex","-1"),i.wrapper=O("div",[i.scrollbarFiller,i.gutterFiller,i.scroller],"CodeMirror"),R&&W>=105&&(i.wrapper.style.clipPath="inset(0px)"),i.wrapper.setAttribute("translate","no"),H&&P<8&&(i.gutters.style.zIndex=-1,i.scroller.style.paddingRight=0),A||$&&Jt||(i.scroller.draggable=!0),t&&(t.appendChild?t.appendChild(i.wrapper):t(i.wrapper)),i.viewFrom=i.viewTo=e.first,i.reportedViewFrom=i.reportedViewTo=e.first,i.view=[],i.renderedView=null,i.externalMeasured=null,i.viewOffset=0,i.lastWrapHeight=i.lastWrapWidth=0,i.updateLineNumbers=null,i.nativeBarWidth=i.barHeight=i.barWidth=0,i.scrollbarsClipped=!1,i.lineNumWidth=i.lineNumInnerWidth=i.lineNumChars=null,i.alignWidgets=!1,i.cachedCharWidth=i.cachedTextHeight=i.cachedPaddingH=null,i.maxLine=null,i.maxLineLength=0,i.maxLineChanged=!1,i.wheelDX=i.wheelDY=i.wheelStartX=i.wheelStartY=null,i.shift=!1,i.selForContextMenu=null,i.activeTouch=null,i.gutterSpecs=yi(r.gutters,r.lineNumbers),Xo(i),n.init(i)}ln.prototype.signal=function(t,e){Ht(t,e)&&this.events.push(arguments)},ln.prototype.finish=function(){for(var t=0;t<this.events.length;t++)Q.apply(null,this.events[t])};var an=0,Yt=null;function Yo(t){var e=t.wheelDeltaX,n=t.wheelDeltaY;return e==null&&t.detail&&t.axis==t.HORIZONTAL_AXIS&&(e=t.detail),n==null&&t.detail&&t.axis==t.VERTICAL_AXIS?n=t.detail:n==null&&(n=t.wheelDelta),{x:e,y:n}}function ms(t){var e=Yo(t);return e.x*=Yt,e.y*=Yt,e}function qo(t,e){R&&W==102&&(t.display.chromeScrollHack==null?t.display.sizer.style.pointerEvents="none":clearTimeout(t.display.chromeScrollHack),t.display.chromeScrollHack=setTimeout(function(){t.display.chromeScrollHack=null,t.display.sizer.style.pointerEvents=""},100));var n=Yo(e),r=n.x,i=n.y,o=Yt;e.deltaMode===0&&(r=e.deltaX,i=e.deltaY,o=1);var l=t.display,a=l.scroller,s=a.scrollWidth>a.clientWidth,u=a.scrollHeight>a.clientHeight;if(r&&s||i&&u){if(i&&At&&A){t:for(var c=e.target,f=l.view;c!=a;c=c.parentNode)for(var h=0;h<f.length;h++)if(f[h].node==c){t.display.currentWheelTarget=c;break t}}if(r&&!$&&!L&&o!=null)return i&&u&&vr(t,Math.max(0,a.scrollTop+i*o)),Se(t,Math.max(0,a.scrollLeft+r*o)),(!i||i&&u)&&pt(e),void(l.wheelStartX=null);if(i&&o!=null){var d=i*o,p=t.doc.scrollTop,g=p+l.wrapper.clientHeight;d<0?p=Math.max(0,p+d-50):g=Math.min(t.doc.height,g+d+50),gi(t,{top:p,bottom:g})}an<20&&e.deltaMode!==0&&(l.wheelStartX==null?(l.wheelStartX=a.scrollLeft,l.wheelStartY=a.scrollTop,l.wheelDX=r,l.wheelDY=i,setTimeout(function(){if(l.wheelStartX!=null){var m=a.scrollLeft-l.wheelStartX,b=a.scrollTop-l.wheelStartY,w=b&&l.wheelDY&&b/l.wheelDY||m&&l.wheelDX&&m/l.wheelDX;l.wheelStartX=l.wheelStartY=null,w&&(Yt=(Yt*an+w)/(an+1),++an)}},200)):(l.wheelDX+=r,l.wheelDY+=i))}}H?Yt=-.53:$?Yt=15:R?Yt=-.7:x&&(Yt=-1/3);var Ot=function(t,e){this.ranges=t,this.primIndex=e};Ot.prototype.primary=function(){return this.ranges[this.primIndex]},Ot.prototype.equals=function(t){if(t==this)return!0;if(t.primIndex!=this.primIndex||t.ranges.length!=this.ranges.length)return!1;for(var e=0;e<this.ranges.length;e++){var n=this.ranges[e],r=t.ranges[e];if(!Un(n.anchor,r.anchor)||!Un(n.head,r.head))return!1}return!0},Ot.prototype.deepCopy=function(){for(var t=[],e=0;e<this.ranges.length;e++)t[e]=new z(Gn(this.ranges[e].anchor),Gn(this.ranges[e].head));return new Ot(t,this.primIndex)},Ot.prototype.somethingSelected=function(){for(var t=0;t<this.ranges.length;t++)if(!this.ranges[t].empty())return!0;return!1},Ot.prototype.contains=function(t,e){e||(e=t);for(var n=0;n<this.ranges.length;n++){var r=this.ranges[n];if(E(e,r.from())>=0&&E(t,r.to())<=0)return n}return-1};var z=function(t,e){this.anchor=t,this.head=e};function It(t,e,n){var r=t&&t.options.selectionsMayTouch,i=e[n];e.sort(function(h,d){return E(h.from(),d.from())}),n=st(e,i);for(var o=1;o<e.length;o++){var l=e[o],a=e[o-1],s=E(a.to(),l.from());if(r&&!l.empty()?s>0:s>=0){var u=Gr(a.from(),l.from()),c=Ur(a.to(),l.to()),f=a.empty()?l.from()==l.head:a.from()==a.head;o<=n&&--n,e.splice(--o,2,new z(f?c:u,f?u:c))}}return new Ot(e,n)}function oe(t,e){return new Ot([new z(t,e||t)],0)}function le(t){return t.text?v(t.from.line+t.text.length-1,B(t.text).length+(t.text.length==1?t.from.ch:0)):t.to}function Zo(t,e){if(E(t,e.from)<0)return t;if(E(t,e.to)<=0)return le(e);var n=t.line+e.text.length-(e.to.line-e.from.line)-1,r=t.ch;return t.line==e.to.line&&(r+=le(e).ch-e.to.ch),v(n,r)}function bi(t,e){for(var n=[],r=0;r<t.sel.ranges.length;r++){var i=t.sel.ranges[r];n.push(new z(Zo(i.anchor,e),Zo(i.head,e)))}return It(t.cm,n,t.sel.primIndex)}function Jo(t,e,n){return t.line==e.line?v(n.line,t.ch-e.ch+n.ch):v(n.line+(t.line-e.line),t.ch)}function ys(t,e,n){for(var r=[],i=v(t.first,0),o=i,l=0;l<e.length;l++){var a=e[l],s=Jo(a.from,i,o),u=Jo(le(a),i,o);if(i=a.to,o=u,n=="around"){var c=t.sel.ranges[l],f=E(c.head,c.anchor)<0;r[l]=new z(f?u:s,f?s:u)}else r[l]=new z(s,s)}return new Ot(r,t.sel.primIndex)}function xi(t){t.doc.mode=zn(t.options,t.doc.modeOption),wr(t)}function wr(t){t.doc.iter(function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)}),t.doc.modeFrontier=t.doc.highlightFrontier=t.doc.first,br(t,100),t.state.modeGen++,t.curOp&&gt(t)}function Qo(t,e){return e.from.ch==0&&e.to.ch==0&&B(e.text)==""&&(!t.cm||t.cm.options.wholeLineUpdateBefore)}function wi(t,e,n,r){function i(w){return n?n[w]:null}function o(w,y,S){Ta(w,y,S,r),ot(w,"change",w,e)}function l(w,y){for(var S=[],C=w;C<y;++C)S.push(new Pe(u[C],i(C),r));return S}var a=e.from,s=e.to,u=e.text,c=M(t,a.line),f=M(t,s.line),h=B(u),d=i(u.length-1),p=s.line-a.line;if(e.full)t.insert(0,l(0,u.length)),t.remove(u.length,t.size-u.length);else if(Qo(t,e)){var g=l(0,u.length-1);o(f,f.text,d),p&&t.remove(a.line,p),g.length&&t.insert(a.line,g)}else if(c==f)if(u.length==1)o(c,c.text.slice(0,a.ch)+h+c.text.slice(s.ch),d);else{var m=l(1,u.length-1);m.push(new Pe(h+c.text.slice(s.ch),d,r)),o(c,c.text.slice(0,a.ch)+u[0],i(0)),t.insert(a.line+1,m)}else if(u.length==1)o(c,c.text.slice(0,a.ch)+u[0]+f.text.slice(s.ch),i(0)),t.remove(a.line+1,p);else{o(c,c.text.slice(0,a.ch)+u[0],i(0)),o(f,h+f.text.slice(s.ch),d);var b=l(1,u.length-1);p>1&&t.remove(a.line+1,p-1),t.insert(a.line+1,b)}ot(t,"change",t,e)}function ae(t,e,n){function r(i,o,l){if(i.linked)for(var a=0;a<i.linked.length;++a){var s=i.linked[a];if(s.doc!=o){var u=l&&s.sharedHist;n&&!u||(e(s.doc,u),r(s.doc,i,u))}}}r(t,null,!0)}function tl(t,e){if(e.cm)throw new Error("This document is already in use.");t.doc=e,e.cm=t,ai(t),xi(t),el(t),t.options.direction=e.direction,t.options.lineWrapping||qn(t),t.options.mode=e.modeOption,gt(t)}function el(t){(t.doc.direction=="rtl"?de:he)(t.display.lineDiv,"CodeMirror-rtl")}function bs(t){Ct(t,function(){el(t),gt(t)})}function sn(t){this.done=[],this.undone=[],this.undoDepth=t?t.undoDepth:1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=t?t.maxGeneration:1}function Ci(t,e){var n={from:Gn(e.from),to:le(e),text:me(t,e.from,e.to)};return il(t,n,e.from.line,e.to.line+1),ae(t,function(r){return il(r,n,e.from.line,e.to.line+1)},!0),n}function rl(t){for(;t.length&&B(t).ranges;)t.pop()}function xs(t,e){return e?(rl(t.done),B(t.done)):t.done.length&&!B(t.done).ranges?B(t.done):t.done.length>1&&!t.done[t.done.length-2].ranges?(t.done.pop(),B(t.done)):void 0}function nl(t,e,n,r){var i=t.history;i.undone.length=0;var o,l,a=+new Date;if((i.lastOp==r||i.lastOrigin==e.origin&&e.origin&&(e.origin.charAt(0)=="+"&&i.lastModTime>a-(t.cm?t.cm.options.historyEventDelay:500)||e.origin.charAt(0)=="*"))&&(o=xs(i,i.lastOp==r)))l=B(o.changes),E(e.from,e.to)==0&&E(e.from,l.to)==0?l.to=le(e):o.changes.push(Ci(t,e));else{var s=B(i.done);for(s&&s.ranges||un(t.sel,i.done),o={changes:[Ci(t,e)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(n),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=a,i.lastOp=i.lastSelOp=r,i.lastOrigin=i.lastSelOrigin=e.origin,l||Q(t,"historyAdded")}function ws(t,e,n,r){var i=e.charAt(0);return i=="*"||i=="+"&&n.ranges.length==r.ranges.length&&n.somethingSelected()==r.somethingSelected()&&new Date-t.history.lastSelTime<=(t.cm?t.cm.options.historyEventDelay:500)}function Cs(t,e,n,r){var i=t.history,o=r&&r.origin;n==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||ws(t,o,B(i.done),e))?i.done[i.done.length-1]=e:un(e,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=n,r&&r.clearRedo!==!1&&rl(i.undone)}function un(t,e){var n=B(e);n&&n.ranges&&n.equals(t)||e.push(t)}function il(t,e,n,r){var i=e["spans_"+t.id],o=0;t.iter(Math.max(t.first,n),Math.min(t.first+t.size,r),function(l){l.markedSpans&&((i||(i=e["spans_"+t.id]={}))[o]=l.markedSpans),++o})}function Ss(t){if(!t)return null;for(var e,n=0;n<t.length;++n)t[n].marker.explicitlyCleared?e||(e=t.slice(0,n)):e&&e.push(t[n]);return e?e.length?e:null:t}function ks(t,e){var n=e["spans_"+t.id];if(!n)return null;for(var r=[],i=0;i<e.text.length;++i)r.push(Ss(n[i]));return r}function ol(t,e){var n=ks(t,e),r=$n(t,e);if(!n)return r;if(!r)return n;for(var i=0;i<n.length;++i){var o=n[i],l=r[i];if(o&&l)t:for(var a=0;a<l.length;++a){for(var s=l[a],u=0;u<o.length;++u)if(o[u].marker==s.marker)continue t;o.push(s)}else l&&(n[i]=l)}return n}function Ke(t,e,n){for(var r=[],i=0;i<t.length;++i){var o=t[i];if(o.ranges)r.push(n?Ot.prototype.deepCopy.call(o):o);else{var l=o.changes,a=[];r.push({changes:a});for(var s=0;s<l.length;++s){var u=l[s],c=void 0;if(a.push({from:u.from,to:u.to,text:u.text}),e)for(var f in u)(c=f.match(/^spans_(\d+)$/))&&st(e,Number(c[1]))>-1&&(B(a)[f]=u[f],delete u[f])}}}return r}function Si(t,e,n,r){if(r){var i=t.anchor;if(n){var o=E(e,i)<0;o!=E(n,i)<0?(i=e,e=n):o!=E(e,n)<0&&(e=n)}return new z(i,e)}return new z(n||e,e)}function cn(t,e,n,r,i){i==null&&(i=t.cm&&(t.cm.display.shift||t.extend)),ut(t,new Ot([Si(t.sel.primary(),e,n,i)],0),r)}function ll(t,e,n){for(var r=[],i=t.cm&&(t.cm.display.shift||t.extend),o=0;o<t.sel.ranges.length;o++)r[o]=Si(t.sel.ranges[o],e[o],null,i);ut(t,It(t.cm,r,t.sel.primIndex),n)}function ki(t,e,n,r){var i=t.sel.ranges.slice(0);i[e]=n,ut(t,It(t.cm,i,t.sel.primIndex),r)}function al(t,e,n,r){ut(t,oe(e,n),r)}function Ls(t,e,n){var r={ranges:e.ranges,update:function(i){this.ranges=[];for(var o=0;o<i.length;o++)this.ranges[o]=new z(F(t,i[o].anchor),F(t,i[o].head))},origin:n&&n.origin};return Q(t,"beforeSelectionChange",t,r),t.cm&&Q(t.cm,"beforeSelectionChange",t.cm,r),r.ranges!=e.ranges?It(t.cm,r.ranges,r.ranges.length-1):e}function sl(t,e,n){var r=t.history.done,i=B(r);i&&i.ranges?(r[r.length-1]=e,fn(t,e,n)):ut(t,e,n)}function ut(t,e,n){fn(t,e,n),Cs(t,t.sel,t.cm?t.cm.curOp.id:NaN,n)}function fn(t,e,n){(Ht(t,"beforeSelectionChange")||t.cm&&Ht(t.cm,"beforeSelectionChange"))&&(e=Ls(t,e,n));var r=n&&n.bias||(E(e.primary().head,t.sel.primary().head)<0?-1:1);ul(t,fl(t,e,r,!0)),n&&n.scroll===!1||!t.cm||t.cm.getOption("readOnly")=="nocursor"||Ue(t.cm)}function ul(t,e){e.equals(t.sel)||(t.sel=e,t.cm&&(t.cm.curOp.updateInput=1,t.cm.curOp.selectionChanged=!0,_i(t.cm)),ot(t,"cursorActivity",t))}function cl(t){ul(t,fl(t,t.sel,null,!1))}function fl(t,e,n,r){for(var i,o=0;o<e.ranges.length;o++){var l=e.ranges[o],a=e.ranges.length==t.sel.ranges.length&&t.sel.ranges[o],s=hn(t,l.anchor,a&&a.anchor,n,r),u=l.head==l.anchor?s:hn(t,l.head,a&&a.head,n,r);(i||s!=l.anchor||u!=l.head)&&(i||(i=e.ranges.slice(0,o)),i[o]=new z(s,u))}return i?It(t.cm,i,e.primIndex):e}function _e(t,e,n,r,i){var o=M(t,e.line);if(o.markedSpans)for(var l=0;l<o.markedSpans.length;++l){var a=o.markedSpans[l],s=a.marker,u="selectLeft"in s?!s.selectLeft:s.inclusiveLeft,c="selectRight"in s?!s.selectRight:s.inclusiveRight;if((a.from==null||(u?a.from<=e.ch:a.from<e.ch))&&(a.to==null||(c?a.to>=e.ch:a.to>e.ch))){if(i&&(Q(s,"beforeCursorEnter"),s.explicitlyCleared)){if(o.markedSpans){--l;continue}break}if(!s.atomic)continue;if(n){var f=s.find(r<0?1:-1),h=void 0;if((r<0?c:u)&&(f=hl(t,f,-r,f&&f.line==e.line?o:null)),f&&f.line==e.line&&(h=E(f,n))&&(r<0?h<0:h>0))return _e(t,f,e,r,i)}var d=s.find(r<0?-1:1);return(r<0?u:c)&&(d=hl(t,d,r,d.line==e.line?o:null)),d?_e(t,d,e,r,i):null}}return e}function hn(t,e,n,r,i){var o=r||1,l=_e(t,e,n,o,i)||!i&&_e(t,e,n,o,!0)||_e(t,e,n,-o,i)||!i&&_e(t,e,n,-o,!0);return l||(t.cantEdit=!0,v(t.first,0))}function hl(t,e,n,r){return n<0&&e.ch==0?e.line>t.first?F(t,v(e.line-1)):null:n>0&&e.ch==(r||M(t,e.line)).text.length?e.line<t.first+t.size-1?v(e.line+1,0):null:new v(e.line,e.ch+n)}function dl(t){t.setSelection(v(t.firstLine(),0),v(t.lastLine()),Bt)}function pl(t,e,n){var r={canceled:!1,from:e.from,to:e.to,text:e.text,origin:e.origin,cancel:function(){return r.canceled=!0}};return n&&(r.update=function(i,o,l,a){i&&(r.from=F(t,i)),o&&(r.to=F(t,o)),l&&(r.text=l),a!==void 0&&(r.origin=a)}),Q(t,"beforeChange",t,r),t.cm&&Q(t.cm,"beforeChange",t.cm,r),r.canceled?(t.cm&&(t.cm.curOp.updateInput=2),null):{from:r.from,to:r.to,text:r.text,origin:r.origin}}function $e(t,e,n){if(t.cm){if(!t.cm.curOp)return lt(t.cm,$e)(t,e,n);if(t.cm.state.suppressEdits)return}if(!(Ht(t,"beforeChange")||t.cm&&Ht(t.cm,"beforeChange"))||(e=pl(t,e,!0))){var r=io&&!n&&Ca(t,e.from,e.to);if(r)for(var i=r.length-1;i>=0;--i)gl(t,{from:r[i].from,to:r[i].to,text:i?[""]:e.text,origin:e.origin});else gl(t,e)}}function gl(t,e){if(e.text.length!=1||e.text[0]!=""||E(e.from,e.to)!=0){var n=bi(t,e);nl(t,e,n,t.cm?t.cm.curOp.id:NaN),Cr(t,e,n,$n(t,e));var r=[];ae(t,function(i,o){o||st(r,i.history)!=-1||(bl(i.history,e),r.push(i.history)),Cr(i,e,null,$n(i,e))})}}function dn(t,e,n){var r=t.cm&&t.cm.state.suppressEdits;if(!r||n){for(var i,o=t.history,l=t.sel,a=e=="undo"?o.done:o.undone,s=e=="undo"?o.undone:o.done,u=0;u<a.length&&(i=a[u],n?!i.ranges||i.equals(t.sel):i.ranges);u++);if(u!=a.length){for(o.lastOrigin=o.lastSelOrigin=null;;){if(!(i=a.pop()).ranges){if(r)return void a.push(i);break}if(un(i,s),n&&!i.equals(t.sel))return void ut(t,i,{clearRedo:!1});l=i}var c=[];un(l,s),s.push({changes:c,generation:o.generation}),o.generation=i.generation||++o.maxGeneration;for(var f=Ht(t,"beforeChange")||t.cm&&Ht(t.cm,"beforeChange"),h=function(g){var m=i.changes[g];if(m.origin=e,f&&!pl(t,m,!1))return a.length=0,{};c.push(Ci(t,m));var b=g?bi(t,m):B(a);Cr(t,m,b,ol(t,m)),!g&&t.cm&&t.cm.scrollIntoView({from:m.from,to:le(m)});var w=[];ae(t,function(y,S){S||st(w,y.history)!=-1||(bl(y.history,m),w.push(y.history)),Cr(y,m,null,ol(y,m))})},d=i.changes.length-1;d>=0;--d){var p=h(d);if(p)return p.v}}}}function vl(t,e){if(e!=0&&(t.first+=e,t.sel=new Ot(Rr(t.sel.ranges,function(i){return new z(v(i.anchor.line+e,i.anchor.ch),v(i.head.line+e,i.head.ch))}),t.sel.primIndex),t.cm)){gt(t.cm,t.first,t.first-e,e);for(var n=t.cm.display,r=n.viewFrom;r<n.viewTo;r++)ne(t.cm,r,"gutter")}}function Cr(t,e,n,r){if(t.cm&&!t.cm.curOp)return lt(t.cm,Cr)(t,e,n,r);if(e.to.line<t.first)vl(t,e.text.length-1-(e.to.line-e.from.line));else if(!(e.from.line>t.lastLine())){if(e.from.line<t.first){var i=e.text.length-1-(t.first-e.from.line);vl(t,i),e={from:v(t.first,0),to:v(e.to.line+i,e.to.ch),text:[B(e.text)],origin:e.origin}}var o=t.lastLine();e.to.line>o&&(e={from:e.from,to:v(o,M(t,o).text.length),text:[e.text[0]],origin:e.origin}),e.removed=me(t,e.from,e.to),n||(n=bi(t,e)),t.cm?Ts(t.cm,e,r):wi(t,e,r),fn(t,n,Bt),t.cantEdit&&hn(t,v(t.firstLine(),0))&&(t.cantEdit=!1)}}function Ts(t,e,n){var r=t.doc,i=t.display,o=e.from,l=e.to,a=!1,s=o.line;t.options.lineWrapping||(s=V(Ft(M(r,o.line))),r.iter(s,l.line+1,function(d){if(d==i.maxLine)return a=!0,!0})),r.sel.contains(e.from,e.to)>-1&&_i(t),wi(r,e,n,Fo(t)),t.options.lineWrapping||(r.iter(s,o.line+e.text.length,function(d){var p=Yr(d);p>i.maxLineLength&&(i.maxLine=d,i.maxLineLength=p,i.maxLineChanged=!0,a=!1)}),a&&(t.curOp.updateMaxLine=!0)),ga(r,o.line),br(t,400);var u=e.text.length-(l.line-o.line)-1;e.full?gt(t):o.line!=l.line||e.text.length!=1||Qo(t.doc,e)?gt(t,o.line,l.line+1,u):ne(t,o.line,"text");var c=Ht(t,"changes"),f=Ht(t,"change");if(f||c){var h={from:o,to:l,text:e.text,removed:e.removed,origin:e.origin};f&&ot(t,"change",t,h),c&&(t.curOp.changeObjs||(t.curOp.changeObjs=[])).push(h)}t.display.selForContextMenu=null}function je(t,e,n,r,i){var o;r||(r=n),E(r,n)<0&&(n=(o=[r,n])[0],r=o[1]),typeof e=="string"&&(e=t.splitLines(e)),$e(t,{from:n,to:r,text:e,origin:i})}function ml(t,e,n,r){n<t.line?t.line+=r:e<t.line&&(t.line=e,t.ch=0)}function yl(t,e,n,r){for(var i=0;i<t.length;++i){var o=t[i],l=!0;if(o.ranges){o.copied||((o=t[i]=o.deepCopy()).copied=!0);for(var a=0;a<o.ranges.length;a++)ml(o.ranges[a].anchor,e,n,r),ml(o.ranges[a].head,e,n,r)}else{for(var s=0;s<o.changes.length;++s){var u=o.changes[s];if(n<u.from.line)u.from=v(u.from.line+r,u.from.ch),u.to=v(u.to.line+r,u.to.ch);else if(e<=u.to.line){l=!1;break}}l||(t.splice(0,i+1),i=0)}}}function bl(t,e){var n=e.from.line,r=e.to.line,i=e.text.length-(r-n)-1;yl(t.done,n,r,i),yl(t.undone,n,r,i)}function Sr(t,e,n,r){var i=e,o=e;return typeof e=="number"?o=M(t,Yi(t,e)):i=V(e),i==null?null:(r(o,i)&&t.cm&&ne(t.cm,i,n),o)}function kr(t){this.lines=t,this.parent=null;for(var e=0,n=0;n<t.length;++n)t[n].parent=this,e+=t[n].height;this.height=e}function Lr(t){this.children=t;for(var e=0,n=0,r=0;r<t.length;++r){var i=t[r];e+=i.chunkSize(),n+=i.height,i.parent=this}this.size=e,this.height=n,this.parent=null}z.prototype.from=function(){return Gr(this.anchor,this.head)},z.prototype.to=function(){return Ur(this.anchor,this.head)},z.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},kr.prototype={chunkSize:function(){return this.lines.length},removeInner:function(t,e){for(var n=t,r=t+e;n<r;++n){var i=this.lines[n];this.height-=i.height,Ma(i),ot(i,"delete")}this.lines.splice(t,e)},collapse:function(t){t.push.apply(t,this.lines)},insertInner:function(t,e,n){this.height+=n,this.lines=this.lines.slice(0,t).concat(e).concat(this.lines.slice(t));for(var r=0;r<e.length;++r)e[r].parent=this},iterN:function(t,e,n){for(var r=t+e;t<r;++t)if(n(this.lines[t]))return!0}},Lr.prototype={chunkSize:function(){return this.size},removeInner:function(t,e){this.size-=e;for(var n=0;n<this.children.length;++n){var r=this.children[n],i=r.chunkSize();if(t<i){var o=Math.min(e,i-t),l=r.height;if(r.removeInner(t,o),this.height-=l-r.height,i==o&&(this.children.splice(n--,1),r.parent=null),(e-=o)==0)break;t=0}else t-=i}if(this.size-e<25&&(this.children.length>1||!(this.children[0]instanceof kr))){var a=[];this.collapse(a),this.children=[new kr(a)],this.children[0].parent=this}},collapse:function(t){for(var e=0;e<this.children.length;++e)this.children[e].collapse(t)},insertInner:function(t,e,n){this.size+=e.length,this.height+=n;for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(t<=o){if(i.insertInner(t,e,n),i.lines&&i.lines.length>50){for(var l=i.lines.length%25+25,a=l;a<i.lines.length;){var s=new kr(i.lines.slice(a,a+=25));i.height-=s.height,this.children.splice(++r,0,s),s.parent=this}i.lines=i.lines.slice(0,l),this.maybeSpill()}break}t-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var t=this;do{var e=new Lr(t.children.splice(t.children.length-5,5));if(t.parent){t.size-=e.size,t.height-=e.height;var n=st(t.parent.children,t);t.parent.children.splice(n+1,0,e)}else{var r=new Lr(t.children);r.parent=t,t.children=[r,e],t=r}e.parent=t.parent}while(t.children.length>10);t.parent.maybeSpill()}},iterN:function(t,e,n){for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(t<o){var l=Math.min(e,o-t);if(i.iterN(t,l,n))return!0;if((e-=l)==0)break;t=0}else t-=o}}};var Tr=function(t,e,n){if(n)for(var r in n)n.hasOwnProperty(r)&&(this[r]=n[r]);this.doc=t,this.node=e};function xl(t,e,n){jt(e)<(t.curOp&&t.curOp.scrollTop||t.doc.scrollTop)&&di(t,n)}function Ms(t,e,n,r){var i=new Tr(t,n,r),o=t.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),Sr(t,e,"widget",function(l){var a=l.widgets||(l.widgets=[]);if(i.insertAt==null?a.push(i):a.splice(Math.min(a.length,Math.max(0,i.insertAt)),0,i),i.line=l,o&&!re(t,l)){var s=jt(l)<t.scrollTop;Vt(l,l.height+hr(i)),s&&di(o,i.height),o.curOp.forceUpdate=!0}return!0}),o&&ot(o,"lineWidgetAdded",o,i,typeof e=="number"?e:V(e)),i}Tr.prototype.clear=function(){var t=this.doc.cm,e=this.line.widgets,n=this.line,r=V(n);if(r!=null&&e){for(var i=0;i<e.length;++i)e[i]==this&&e.splice(i--,1);e.length||(n.widgets=null);var o=hr(this);Vt(n,Math.max(0,n.height-o)),t&&(Ct(t,function(){xl(t,n,-o),ne(t,r,"widget")}),ot(t,"lineWidgetCleared",t,this,r))}},Tr.prototype.changed=function(){var t=this,e=this.height,n=this.doc.cm,r=this.line;this.height=null;var i=hr(this)-e;i&&(re(this.doc,r)||Vt(r,r.height+i),n&&Ct(n,function(){n.curOp.forceUpdate=!0,xl(n,r,i),ot(n,"lineWidgetChanged",n,t,V(r))}))},He(Tr);var wl=0,se=function(t,e){this.lines=[],this.type=e,this.doc=t,this.id=++wl};function Xe(t,e,n,r,i){if(r&&r.shared)return Ns(t,e,n,r,i);if(t.cm&&!t.cm.curOp)return lt(t.cm,Xe)(t,e,n,r,i);var o=new se(t,i),l=E(e,n);if(r&&ge(r,o,!1),l>0||l==0&&o.clearWhenEmpty!==!1)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=De("span",[o.replacedWith],"CodeMirror-widget"),r.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),r.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(co(t,e.line,e,n,o)||e.line!=n.line&&co(t,n.line,e,n,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");ma()}o.addToHistory&&nl(t,{from:e,to:n,origin:"markText"},t.sel,NaN);var a,s=e.line,u=t.cm;if(t.iter(s,n.line+1,function(f){u&&o.collapsed&&!u.options.lineWrapping&&Ft(f)==u.display.maxLine&&(a=!0),o.collapsed&&s!=e.line&&Vt(f,0),ba(f,new _r(o,s==e.line?e.ch:null,s==n.line?n.ch:null),t.cm&&t.cm.curOp),++s}),o.collapsed&&t.iter(e.line,n.line+1,function(f){re(t,f)&&Vt(f,0)}),o.clearOnEnter&&D(o,"beforeCursorEnter",function(){return o.clear()}),o.readOnly&&(va(),(t.history.done.length||t.history.undone.length)&&t.clearHistory()),o.collapsed&&(o.id=++wl,o.atomic=!0),u){if(a&&(u.curOp.updateMaxLine=!0),o.collapsed)gt(u,e.line,n.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var c=e.line;c<=n.line;c++)ne(u,c,"text");o.atomic&&cl(u.doc),ot(u,"markerAdded",u,o)}return o}se.prototype.clear=function(){if(!this.explicitlyCleared){var t=this.doc.cm,e=t&&!t.curOp;if(e&&Le(t),Ht(this,"clear")){var n=this.find();n&&ot(this,"clear",n.from,n.to)}for(var r=null,i=null,o=0;o<this.lines.length;++o){var l=this.lines[o],a=ur(l.markedSpans,this);t&&!this.collapsed?ne(t,V(l),"text"):t&&(a.to!=null&&(i=V(l)),a.from!=null&&(r=V(l))),l.markedSpans=ya(l.markedSpans,a),a.from==null&&this.collapsed&&!re(this.doc,l)&&t&&Vt(l,Re(t.display))}if(t&&this.collapsed&&!t.options.lineWrapping)for(var s=0;s<this.lines.length;++s){var u=Ft(this.lines[s]),c=Yr(u);c>t.display.maxLineLength&&(t.display.maxLine=u,t.display.maxLineLength=c,t.display.maxLineChanged=!0)}r!=null&&t&&this.collapsed&&gt(t,r,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,t&&cl(t.doc)),t&&ot(t,"markerCleared",t,this,r,i),e&&Te(t),this.parent&&this.parent.clear()}},se.prototype.find=function(t,e){var n,r;t==null&&this.type=="bookmark"&&(t=1);for(var i=0;i<this.lines.length;++i){var o=this.lines[i],l=ur(o.markedSpans,this);if(l.from!=null&&(n=v(e?o:V(o),l.from),t==-1))return n;if(l.to!=null&&(r=v(e?o:V(o),l.to),t==1))return r}return n&&{from:n,to:r}},se.prototype.changed=function(){var t=this,e=this.find(-1,!0),n=this,r=this.doc.cm;e&&r&&Ct(r,function(){var i=e.line,o=V(e.line),l=ti(r,o);if(l&&(Mo(l),r.curOp.selectionChanged=r.curOp.forceUpdate=!0),r.curOp.updateMaxLine=!0,!re(n.doc,i)&&n.height!=null){var a=n.height;n.height=null;var s=hr(n)-a;s&&Vt(i,i.height+s)}ot(r,"markerChanged",r,t)})},se.prototype.attachLine=function(t){if(!this.lines.length&&this.doc.cm){var e=this.doc.cm.curOp;e.maybeHiddenMarkers&&st(e.maybeHiddenMarkers,this)!=-1||(e.maybeUnhiddenMarkers||(e.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(t)},se.prototype.detachLine=function(t){if(this.lines.splice(st(this.lines,t),1),!this.lines.length&&this.doc.cm){var e=this.doc.cm.curOp;(e.maybeHiddenMarkers||(e.maybeHiddenMarkers=[])).push(this)}},He(se);var Mr=function(t,e){this.markers=t,this.primary=e;for(var n=0;n<t.length;++n)t[n].parent=this};function Ns(t,e,n,r,i){(r=ge(r)).shared=!1;var o=[Xe(t,e,n,r,i)],l=o[0],a=r.widgetNode;return ae(t,function(s){a&&(r.widgetNode=a.cloneNode(!0)),o.push(Xe(s,F(s,e),F(s,n),r,i));for(var u=0;u<s.linked.length;++u)if(s.linked[u].isParent)return;l=B(o)}),new Mr(o,l)}function Cl(t){return t.findMarks(v(t.first,0),t.clipPos(v(t.lastLine())),function(e){return e.parent})}function Os(t,e){for(var n=0;n<e.length;n++){var r=e[n],i=r.find(),o=t.clipPos(i.from),l=t.clipPos(i.to);if(E(o,l)){var a=Xe(t,o,l,r.primary,r.primary.type);r.markers.push(a),a.parent=r}}}function As(t){for(var e=function(r){var i=t[r],o=[i.primary.doc];ae(i.primary.doc,function(s){return o.push(s)});for(var l=0;l<i.markers.length;l++){var a=i.markers[l];st(o,a.doc)==-1&&(a.parent=null,i.markers.splice(l--,1))}},n=0;n<t.length;n++)e(n)}Mr.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var t=0;t<this.markers.length;++t)this.markers[t].clear();ot(this,"clear")}},Mr.prototype.find=function(t,e){return this.primary.find(t,e)},He(Mr);var Ds=0,vt=function(t,e,n,r,i){if(!(this instanceof vt))return new vt(t,e,n,r,i);n==null&&(n=0),Lr.call(this,[new kr([new Pe("",null)])]),this.first=n,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=n;var o=v(n,0);this.sel=oe(o),this.history=new sn(null),this.id=++Ds,this.modeOption=e,this.lineSep=r,this.direction=i=="rtl"?"rtl":"ltr",this.extend=!1,typeof t=="string"&&(t=this.splitLines(t)),wi(this,{from:o,to:o,text:t}),ut(this,oe(o),Bt)};vt.prototype=Vi(Lr.prototype,{constructor:vt,iter:function(t,e,n){n?this.iterN(t-this.first,e-t,n):this.iterN(this.first,this.first+this.size,t)},insert:function(t,e){for(var n=0,r=0;r<e.length;++r)n+=e[r].height;this.insertInner(t-this.first,e,n)},remove:function(t,e){this.removeInner(t-this.first,e)},getValue:function(t){var e=Bn(this,this.first,this.first+this.size);return t===!1?e:e.join(t||this.lineSeparator())},setValue:at(function(t){var e=v(this.first,0),n=this.first+this.size-1;$e(this,{from:e,to:v(n,M(this,n).text.length),text:this.splitLines(t),origin:"setValue",full:!0},!0),this.cm&&gr(this.cm,0,0),ut(this,oe(e),Bt)}),replaceRange:function(t,e,n,r){je(this,t,e=F(this,e),n=n?F(this,n):e,r)},getRange:function(t,e,n){var r=me(this,F(this,t),F(this,e));return n===!1?r:n===""?r.join(""):r.join(n||this.lineSeparator())},getLine:function(t){var e=this.getLineHandle(t);return e&&e.text},getLineHandle:function(t){if(ar(this,t))return M(this,t)},getLineNumber:function(t){return V(t)},getLineHandleVisualStart:function(t){return typeof t=="number"&&(t=M(this,t)),Ft(t)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(t){return F(this,t)},getCursor:function(t){var e=this.sel.primary();return t==null||t=="head"?e.head:t=="anchor"?e.anchor:t=="end"||t=="to"||t===!1?e.to():e.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:at(function(t,e,n){al(this,F(this,typeof t=="number"?v(t,e||0):t),null,n)}),setSelection:at(function(t,e,n){al(this,F(this,t),F(this,e||t),n)}),extendSelection:at(function(t,e,n){cn(this,F(this,t),e&&F(this,e),n)}),extendSelections:at(function(t,e){ll(this,qi(this,t),e)}),extendSelectionsBy:at(function(t,e){ll(this,qi(this,Rr(this.sel.ranges,t)),e)}),setSelections:at(function(t,e,n){if(t.length){for(var r=[],i=0;i<t.length;i++)r[i]=new z(F(this,t[i].anchor),F(this,t[i].head||t[i].anchor));e==null&&(e=Math.min(t.length-1,this.sel.primIndex)),ut(this,It(this.cm,r,e),n)}}),addSelection:at(function(t,e,n){var r=this.sel.ranges.slice(0);r.push(new z(F(this,t),F(this,e||t))),ut(this,It(this.cm,r,r.length-1),n)}),getSelection:function(t){for(var e,n=this.sel.ranges,r=0;r<n.length;r++){var i=me(this,n[r].from(),n[r].to());e=e?e.concat(i):i}return t===!1?e:e.join(t||this.lineSeparator())},getSelections:function(t){for(var e=[],n=this.sel.ranges,r=0;r<n.length;r++){var i=me(this,n[r].from(),n[r].to());t!==!1&&(i=i.join(t||this.lineSeparator())),e[r]=i}return e},replaceSelection:function(t,e,n){for(var r=[],i=0;i<this.sel.ranges.length;i++)r[i]=t;this.replaceSelections(r,e,n||"+input")},replaceSelections:at(function(t,e,n){for(var r=[],i=this.sel,o=0;o<i.ranges.length;o++){var l=i.ranges[o];r[o]={from:l.from(),to:l.to(),text:this.splitLines(t[o]),origin:n}}for(var a=e&&e!="end"&&ys(this,r,e),s=r.length-1;s>=0;s--)$e(this,r[s]);a?sl(this,a):this.cm&&Ue(this.cm)}),undo:at(function(){dn(this,"undo")}),redo:at(function(){dn(this,"redo")}),undoSelection:at(function(){dn(this,"undo",!0)}),redoSelection:at(function(){dn(this,"redo",!0)}),setExtending:function(t){this.extend=t},getExtending:function(){return this.extend},historySize:function(){for(var t=this.history,e=0,n=0,r=0;r<t.done.length;r++)t.done[r].ranges||++e;for(var i=0;i<t.undone.length;i++)t.undone[i].ranges||++n;return{undo:e,redo:n}},clearHistory:function(){var t=this;this.history=new sn(this.history),ae(this,function(e){return e.history=t.history},!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(t){return t&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(t){return this.history.generation==(t||this.cleanGeneration)},getHistory:function(){return{done:Ke(this.history.done),undone:Ke(this.history.undone)}},setHistory:function(t){var e=this.history=new sn(this.history);e.done=Ke(t.done.slice(0),null,!0),e.undone=Ke(t.undone.slice(0),null,!0)},setGutterMarker:at(function(t,e,n){return Sr(this,t,"gutter",function(r){var i=r.gutterMarkers||(r.gutterMarkers={});return i[e]=n,!n&&Ui(i)&&(r.gutterMarkers=null),!0})}),clearGutter:at(function(t){var e=this;this.iter(function(n){n.gutterMarkers&&n.gutterMarkers[t]&&Sr(e,n,"gutter",function(){return n.gutterMarkers[t]=null,Ui(n.gutterMarkers)&&(n.gutterMarkers=null),!0})})}),lineInfo:function(t){var e;if(typeof t=="number"){if(!ar(this,t)||(e=t,!(t=M(this,t))))return null}else if((e=V(t))==null)return null;return{line:e,handle:t,text:t.text,gutterMarkers:t.gutterMarkers,textClass:t.textClass,bgClass:t.bgClass,wrapClass:t.wrapClass,widgets:t.widgets}},addLineClass:at(function(t,e,n){return Sr(this,t,e=="gutter"?"gutter":"class",function(r){var i=e=="text"?"textClass":e=="background"?"bgClass":e=="gutter"?"gutterClass":"wrapClass";if(r[i]){if(Oe(n).test(r[i]))return!1;r[i]+=" "+n}else r[i]=n;return!0})}),removeLineClass:at(function(t,e,n){return Sr(this,t,e=="gutter"?"gutter":"class",function(r){var i=e=="text"?"textClass":e=="background"?"bgClass":e=="gutter"?"gutterClass":"wrapClass",o=r[i];if(!o)return!1;if(n==null)r[i]=null;else{var l=o.match(Oe(n));if(!l)return!1;var a=l.index+l[0].length;r[i]=o.slice(0,l.index)+(l.index&&a!=o.length?" ":"")+o.slice(a)||null}return!0})}),addLineWidget:at(function(t,e,n){return Ms(this,t,e,n)}),removeLineWidget:function(t){t.clear()},markText:function(t,e,n){return Xe(this,F(this,t),F(this,e),n,n&&n.type||"range")},setBookmark:function(t,e){var n={replacedWith:e&&(e.nodeType==null?e.widget:e),insertLeft:e&&e.insertLeft,clearWhenEmpty:!1,shared:e&&e.shared,handleMouseEvents:e&&e.handleMouseEvents};return Xe(this,t=F(this,t),t,n,"bookmark")},findMarksAt:function(t){var e=[],n=M(this,(t=F(this,t)).line).markedSpans;if(n)for(var r=0;r<n.length;++r){var i=n[r];(i.from==null||i.from<=t.ch)&&(i.to==null||i.to>=t.ch)&&e.push(i.marker.parent||i.marker)}return e},findMarks:function(t,e,n){t=F(this,t),e=F(this,e);var r=[],i=t.line;return this.iter(t.line,e.line+1,function(o){var l=o.markedSpans;if(l)for(var a=0;a<l.length;a++){var s=l[a];s.to!=null&&i==t.line&&t.ch>=s.to||s.from==null&&i!=t.line||s.from!=null&&i==e.line&&s.from>=e.ch||n&&!n(s.marker)||r.push(s.marker.parent||s.marker)}++i}),r},getAllMarks:function(){var t=[];return this.iter(function(e){var n=e.markedSpans;if(n)for(var r=0;r<n.length;++r)n[r].from!=null&&t.push(n[r].marker)}),t},posFromIndex:function(t){var e,n=this.first,r=this.lineSeparator().length;return this.iter(function(i){var o=i.text.length+r;if(o>t)return e=t,!0;t-=o,++n}),F(this,v(n,e))},indexFromPos:function(t){var e=(t=F(this,t)).ch;if(t.line<this.first||t.ch<0)return 0;var n=this.lineSeparator().length;return this.iter(this.first,t.line,function(r){e+=r.text.length+n}),e},copy:function(t){var e=new vt(Bn(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return e.scrollTop=this.scrollTop,e.scrollLeft=this.scrollLeft,e.sel=this.sel,e.extend=!1,t&&(e.history.undoDepth=this.history.undoDepth,e.setHistory(this.getHistory())),e},linkedDoc:function(t){t||(t={});var e=this.first,n=this.first+this.size;t.from!=null&&t.from>e&&(e=t.from),t.to!=null&&t.to<n&&(n=t.to);var r=new vt(Bn(this,e,n),t.mode||this.modeOption,e,this.lineSep,this.direction);return t.sharedHist&&(r.history=this.history),(this.linked||(this.linked=[])).push({doc:r,sharedHist:t.sharedHist}),r.linked=[{doc:this,isParent:!0,sharedHist:t.sharedHist}],Os(r,Cl(this)),r},unlinkDoc:function(t){if(t instanceof X&&(t=t.doc),this.linked){for(var e=0;e<this.linked.length;++e)if(this.linked[e].doc==t){this.linked.splice(e,1),t.unlinkDoc(this),As(Cl(this));break}}if(t.history==this.history){var n=[t.id];ae(t,function(r){return n.push(r.id)},!0),t.history=new sn(null),t.history.done=Ke(this.history.done,n),t.history.undone=Ke(this.history.undone,n)}},iterLinkedDocs:function(t){ae(this,t)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(t){return this.lineSep?t.split(this.lineSep):Fn(t)},lineSeparator:function(){return this.lineSep||`
`},setDirection:at(function(t){t!="rtl"&&(t="ltr"),t!=this.direction&&(this.direction=t,this.iter(function(e){return e.order=null}),this.cm&&bs(this.cm))})}),vt.prototype.eachLine=vt.prototype.iter;var Sl=0;function Ws(t){var e=this;if(kl(e),!it(e,t)&&!Xt(e.display,t)){pt(t),H&&(Sl=+new Date);var n=we(e,t,!0),r=t.dataTransfer.files;if(n&&!e.isReadOnly())if(r&&r.length&&window.FileReader&&window.File)for(var i=r.length,o=Array(i),l=0,a=function(){++l==i&&lt(e,function(){var d={from:n=F(e.doc,n),to:n,text:e.doc.splitLines(o.filter(function(p){return p!=null}).join(e.doc.lineSeparator())),origin:"paste"};$e(e.doc,d),sl(e.doc,oe(F(e.doc,n),F(e.doc,le(d))))})()},s=function(d,p){if(e.options.allowDropFileTypes&&st(e.options.allowDropFileTypes,d.type)==-1)a();else{var g=new FileReader;g.onerror=function(){return a()},g.onload=function(){var m=g.result;/[\x00-\x08\x0e-\x1f]{2}/.test(m)||(o[p]=m),a()},g.readAsText(d)}},u=0;u<r.length;u++)s(r[u],u);else{if(e.state.draggingText&&e.doc.sel.contains(n)>-1)return e.state.draggingText(t),void setTimeout(function(){return e.display.input.focus()},20);try{var c=t.dataTransfer.getData("Text");if(c){var f;if(e.state.draggingText&&!e.state.draggingText.copy&&(f=e.listSelections()),fn(e.doc,oe(n,n)),f)for(var h=0;h<f.length;++h)je(e.doc,"",f[h].anchor,f[h].head,"drag");e.replaceSelection(c,"around","paste"),e.display.input.focus()}}catch{}}}}function Hs(t,e){if(H&&(!t.state.draggingText||+new Date-Sl<100))lr(e);else if(!it(t,e)&&!Xt(t.display,e)&&(e.dataTransfer.setData("Text",t.getSelection()),e.dataTransfer.effectAllowed="copyMove",e.dataTransfer.setDragImage&&!x)){var n=O("img",null,null,"position: fixed; left: 0; top: 0;");n.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",L&&(n.width=n.height=1,t.display.wrapper.appendChild(n),n._top=n.offsetTop),e.dataTransfer.setDragImage(n,0,0),L&&n.parentNode.removeChild(n)}}function Es(t,e){var n=we(t,e);if(n){var r=document.createDocumentFragment();si(t,n,r),t.display.dragCursor||(t.display.dragCursor=O("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),t.display.lineSpace.insertBefore(t.display.dragCursor,t.display.cursorDiv)),Mt(t.display.dragCursor,r)}}function kl(t){t.display.dragCursor&&(t.display.lineSpace.removeChild(t.display.dragCursor),t.display.dragCursor=null)}function Ll(t){if(document.getElementsByClassName){for(var e=document.getElementsByClassName("CodeMirror"),n=[],r=0;r<e.length;r++){var i=e[r].CodeMirror;i&&n.push(i)}n.length&&n[0].operation(function(){for(var o=0;o<n.length;o++)t(n[o])})}}var Tl=!1;function Fs(){Tl||(Ps(),Tl=!0)}function Ps(){var t;D(window,"resize",function(){t==null&&(t=setTimeout(function(){t=null,Ll(Is)},100))}),D(window,"blur",function(){return Ll(Ve)})}function Is(t){var e=t.display;e.cachedCharWidth=e.cachedTextHeight=e.cachedPaddingH=null,e.scrollbarsClipped=!1,t.setSize()}for(var ue={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Nr=0;Nr<10;Nr++)ue[Nr+48]=ue[Nr+96]=String(Nr);for(var pn=65;pn<=90;pn++)ue[pn]=String.fromCharCode(pn);for(var Or=1;Or<=12;Or++)ue[Or+111]=ue[Or+63235]="F"+Or;var qt={};function zs(t){var e,n,r,i,o=t.split(/-(?!$)/);t=o[o.length-1];for(var l=0;l<o.length-1;l++){var a=o[l];if(/^(cmd|meta|m)$/i.test(a))i=!0;else if(/^a(lt)?$/i.test(a))e=!0;else if(/^(c|ctrl|control)$/i.test(a))n=!0;else{if(!/^s(hift)?$/i.test(a))throw new Error("Unrecognized modifier name: "+a);r=!0}}return e&&(t="Alt-"+t),n&&(t="Ctrl-"+t),i&&(t="Cmd-"+t),r&&(t="Shift-"+t),t}function Rs(t){var e={};for(var n in t)if(t.hasOwnProperty(n)){var r=t[n];if(/^(name|fallthrough|(de|at)tach)$/.test(n))continue;if(r=="..."){delete t[n];continue}for(var i=Rr(n.split(" "),zs),o=0;o<i.length;o++){var l=void 0,a=void 0;o==i.length-1?(a=i.join(" "),l=r):(a=i.slice(0,o+1).join(" "),l="...");var s=e[a];if(s){if(s!=l)throw new Error("Inconsistent bindings for "+a)}else e[a]=l}delete t[n]}for(var u in e)t[u]=e[u];return t}function Ye(t,e,n,r){var i=(e=gn(e)).call?e.call(t,r):e[t];if(i===!1)return"nothing";if(i==="...")return"multi";if(i!=null&&n(i))return"handled";if(e.fallthrough){if(Object.prototype.toString.call(e.fallthrough)!="[object Array]")return Ye(t,e.fallthrough,n,r);for(var o=0;o<e.fallthrough.length;o++){var l=Ye(t,e.fallthrough[o],n,r);if(l)return l}}}function Ml(t){var e=typeof t=="string"?t:ue[t.keyCode];return e=="Ctrl"||e=="Alt"||e=="Shift"||e=="Mod"}function Nl(t,e,n){var r=t;return e.altKey&&r!="Alt"&&(t="Alt-"+t),(zi?e.metaKey:e.ctrlKey)&&r!="Ctrl"&&(t="Ctrl-"+t),(zi?e.ctrlKey:e.metaKey)&&r!="Mod"&&(t="Cmd-"+t),!n&&e.shiftKey&&r!="Shift"&&(t="Shift-"+t),t}function Ol(t,e){if(L&&t.keyCode==34&&t.char)return!1;var n=ue[t.keyCode];return n!=null&&!t.altGraphKey&&(t.keyCode==3&&t.code&&(n=t.code),Nl(n,t,e))}function gn(t){return typeof t=="string"?qt[t]:t}function qe(t,e){for(var n=t.doc.sel.ranges,r=[],i=0;i<n.length;i++){for(var o=e(n[i]);r.length&&E(o.from,B(r).to)<=0;){var l=r.pop();if(E(l.from,o.from)<0){o.from=l.from;break}}r.push(o)}Ct(t,function(){for(var a=r.length-1;a>=0;a--)je(t.doc,"",r[a].from,r[a].to,"+delete");Ue(t)})}function Li(t,e,n){var r=Gi(t.text,e+n,n);return r<0||r>t.text.length?null:r}function Ti(t,e,n){var r=Li(t,e.ch,n);return r==null?null:new v(e.line,r,n<0?"after":"before")}function Mi(t,e,n,r,i){if(t){e.doc.direction=="rtl"&&(i=-i);var o=_t(n,e.doc.direction);if(o){var l,a=i<0?B(o):o[0],s=i<0==(a.level==1)?"after":"before";if(a.level>0||e.doc.direction=="rtl"){var u=ze(e,n);l=i<0?n.text.length-1:0;var c=Kt(e,u,l).top;l=nr(function(f){return Kt(e,u,f).top==c},i<0==(a.level==1)?a.from:a.to-1,l),s=="before"&&(l=Li(n,l,1))}else l=i<0?a.to:a.from;return new v(r,l,s)}}return new v(r,i<0?n.text.length:0,i<0?"before":"after")}function Bs(t,e,n,r){var i=_t(e,t.doc.direction);if(!i)return Ti(e,n,r);n.ch>=e.text.length?(n.ch=e.text.length,n.sticky="before"):n.ch<=0&&(n.ch=0,n.sticky="after");var o=or(i,n.ch,n.sticky),l=i[o];if(t.doc.direction=="ltr"&&l.level%2==0&&(r>0?l.to>n.ch:l.from<n.ch))return Ti(e,n,r);var a,s=function(b,w){return Li(e,b instanceof v?b.ch:b,w)},u=function(b){return t.options.lineWrapping?(a=a||ze(t,e),Eo(t,e,a,b)):{begin:0,end:e.text.length}},c=u(n.sticky=="before"?s(n,-1):n.ch);if(t.doc.direction=="rtl"||l.level==1){var f=l.level==1==r<0,h=s(n,f?1:-1);if(h!=null&&(f?h<=l.to&&h<=c.end:h>=l.from&&h>=c.begin)){var d=f?"before":"after";return new v(n.line,h,d)}}var p=function(b,w,y){for(var S=function(K,St){return St?new v(n.line,s(K,1),"before"):new v(n.line,K,"after")};b>=0&&b<i.length;b+=w){var C=i[b],T=w>0==(C.level!=1),G=T?y.begin:s(y.end,-1);if(C.from<=G&&G<C.to||(G=T?C.from:s(C.to,-1),y.begin<=G&&G<y.end))return S(G,T)}},g=p(o+r,r,c);if(g)return g;var m=r>0?c.end:s(c.begin,-1);return m==null||r>0&&m==e.text.length||!(g=p(r>0?0:i.length-1,r,u(m)))?null:g}qt.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},qt.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},qt.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},qt.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},qt.default=At?qt.macDefault:qt.pcDefault;var Ar={selectAll:dl,singleSelection:function(t){return t.setSelection(t.getCursor("anchor"),t.getCursor("head"),Bt)},killLine:function(t){return qe(t,function(e){if(e.empty()){var n=M(t.doc,e.head.line).text.length;return e.head.ch==n&&e.head.line<t.lastLine()?{from:e.head,to:v(e.head.line+1,0)}:{from:e.head,to:v(e.head.line,n)}}return{from:e.from(),to:e.to()}})},deleteLine:function(t){return qe(t,function(e){return{from:v(e.from().line,0),to:F(t.doc,v(e.to().line+1,0))}})},delLineLeft:function(t){return qe(t,function(e){return{from:v(e.from().line,0),to:e.from()}})},delWrappedLineLeft:function(t){return qe(t,function(e){var n=t.charCoords(e.head,"div").top+5;return{from:t.coordsChar({left:0,top:n},"div"),to:e.from()}})},delWrappedLineRight:function(t){return qe(t,function(e){var n=t.charCoords(e.head,"div").top+5,r=t.coordsChar({left:t.display.lineDiv.offsetWidth+100,top:n},"div");return{from:e.from(),to:r}})},undo:function(t){return t.undo()},redo:function(t){return t.redo()},undoSelection:function(t){return t.undoSelection()},redoSelection:function(t){return t.redoSelection()},goDocStart:function(t){return t.extendSelection(v(t.firstLine(),0))},goDocEnd:function(t){return t.extendSelection(v(t.lastLine()))},goLineStart:function(t){return t.extendSelectionsBy(function(e){return Al(t,e.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(t){return t.extendSelectionsBy(function(e){return Dl(t,e.head)},{origin:"+move",bias:1})},goLineEnd:function(t){return t.extendSelectionsBy(function(e){return Vs(t,e.head.line)},{origin:"+move",bias:-1})},goLineRight:function(t){return t.extendSelectionsBy(function(e){var n=t.cursorCoords(e.head,"div").top+5;return t.coordsChar({left:t.display.lineDiv.offsetWidth+100,top:n},"div")},rr)},goLineLeft:function(t){return t.extendSelectionsBy(function(e){var n=t.cursorCoords(e.head,"div").top+5;return t.coordsChar({left:0,top:n},"div")},rr)},goLineLeftSmart:function(t){return t.extendSelectionsBy(function(e){var n=t.cursorCoords(e.head,"div").top+5,r=t.coordsChar({left:0,top:n},"div");return r.ch<t.getLine(r.line).search(/\S/)?Dl(t,e.head):r},rr)},goLineUp:function(t){return t.moveV(-1,"line")},goLineDown:function(t){return t.moveV(1,"line")},goPageUp:function(t){return t.moveV(-1,"page")},goPageDown:function(t){return t.moveV(1,"page")},goCharLeft:function(t){return t.moveH(-1,"char")},goCharRight:function(t){return t.moveH(1,"char")},goColumnLeft:function(t){return t.moveH(-1,"column")},goColumnRight:function(t){return t.moveH(1,"column")},goWordLeft:function(t){return t.moveH(-1,"word")},goGroupRight:function(t){return t.moveH(1,"group")},goGroupLeft:function(t){return t.moveH(-1,"group")},goWordRight:function(t){return t.moveH(1,"word")},delCharBefore:function(t){return t.deleteH(-1,"codepoint")},delCharAfter:function(t){return t.deleteH(1,"char")},delWordBefore:function(t){return t.deleteH(-1,"word")},delWordAfter:function(t){return t.deleteH(1,"word")},delGroupBefore:function(t){return t.deleteH(-1,"group")},delGroupAfter:function(t){return t.deleteH(1,"group")},indentAuto:function(t){return t.indentSelection("smart")},indentMore:function(t){return t.indentSelection("add")},indentLess:function(t){return t.indentSelection("subtract")},insertTab:function(t){return t.replaceSelection("	")},insertSoftTab:function(t){for(var e=[],n=t.listSelections(),r=t.options.tabSize,i=0;i<n.length;i++){var o=n[i].from(),l=Wt(t.getLine(o.line),o.ch,r);e.push(Tn(r-l%r))}t.replaceSelections(e)},defaultTab:function(t){t.somethingSelected()?t.indentSelection("add"):t.execCommand("insertTab")},transposeChars:function(t){return Ct(t,function(){for(var e=t.listSelections(),n=[],r=0;r<e.length;r++)if(e[r].empty()){var i=e[r].head,o=M(t.doc,i.line).text;if(o){if(i.ch==o.length&&(i=new v(i.line,i.ch-1)),i.ch>0)i=new v(i.line,i.ch+1),t.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),v(i.line,i.ch-2),i,"+transpose");else if(i.line>t.doc.first){var l=M(t.doc,i.line-1).text;l&&(i=new v(i.line,1),t.replaceRange(o.charAt(0)+t.doc.lineSeparator()+l.charAt(l.length-1),v(i.line-1,l.length-1),i,"+transpose"))}}n.push(new z(i,i))}t.setSelections(n)})},newlineAndIndent:function(t){return Ct(t,function(){for(var e=t.listSelections(),n=e.length-1;n>=0;n--)t.replaceRange(t.doc.lineSeparator(),e[n].anchor,e[n].head,"+input");e=t.listSelections();for(var r=0;r<e.length;r++)t.indentLine(e[r].from().line,null,!0);Ue(t)})},openLine:function(t){return t.replaceSelection(`
`,"start")},toggleOverwrite:function(t){return t.toggleOverwrite()}};function Al(t,e){var n=M(t.doc,e),r=Ft(n);return r!=n&&(e=V(r)),Mi(!0,t,r,e,1)}function Vs(t,e){var n=M(t.doc,e),r=ka(n);return r!=n&&(e=V(r)),Mi(!0,t,n,e,-1)}function Dl(t,e){var n=Al(t,e.line),r=M(t.doc,n.line),i=_t(r,t.doc.direction);if(!i||i[0].level==0){var o=Math.max(n.ch,r.text.search(/\S/)),l=e.line==n.line&&e.ch<=o&&e.ch;return v(n.line,l?0:o,n.sticky)}return n}function vn(t,e,n){if(typeof e=="string"&&!(e=Ar[e]))return!1;t.display.input.ensurePolled();var r=t.display.shift,i=!1;try{t.isReadOnly()&&(t.state.suppressEdits=!0),n&&(t.display.shift=!1),i=e(t)!=Ir}finally{t.display.shift=r,t.state.suppressEdits=!1}return i}function Us(t,e,n){for(var r=0;r<t.state.keyMaps.length;r++){var i=Ye(e,t.state.keyMaps[r],n,t);if(i)return i}return t.options.extraKeys&&Ye(e,t.options.extraKeys,n,t)||Ye(e,t.options.keyMap,n,t)}var Gs=new ee;function Dr(t,e,n,r){var i=t.state.keySeq;if(i){if(Ml(e))return"handled";if(/\'$/.test(e)?t.state.keySeq=null:Gs.set(50,function(){t.state.keySeq==i&&(t.state.keySeq=null,t.display.input.reset())}),Wl(t,i+" "+e,n,r))return!0}return Wl(t,e,n,r)}function Wl(t,e,n,r){var i=Us(t,e,r);return i=="multi"&&(t.state.keySeq=e),i=="handled"&&ot(t,"keyHandled",t,e,n),i!="handled"&&i!="multi"||(pt(n),ui(t)),!!i}function Hl(t,e){var n=Ol(e,!0);return!!n&&(e.shiftKey&&!t.state.keySeq?Dr(t,"Shift-"+n,e,function(r){return vn(t,r,!0)})||Dr(t,n,e,function(r){if(typeof r=="string"?/^go[A-Z]/.test(r):r.motion)return vn(t,r)}):Dr(t,n,e,function(r){return vn(t,r)}))}function Ks(t,e,n){return Dr(t,"'"+n+"'",e,function(r){return vn(t,r,!0)})}var Ni=null;function El(t){var e=this;if(!(t.target&&t.target!=e.display.input.getField()||(e.curOp.focus=Dt(We(e)),it(e,t)))){H&&P<11&&t.keyCode==27&&(t.returnValue=!1);var n=t.keyCode;e.display.shift=n==16||t.shiftKey;var r=Hl(e,t);L&&(Ni=r?n:null,r||n!=88||sa||!(At?t.metaKey:t.ctrlKey)||e.replaceSelection("",null,"cut")),$&&!At&&!r&&n==46&&t.shiftKey&&!t.ctrlKey&&document.execCommand&&document.execCommand("cut"),n!=18||/\bCodeMirror-crosshair\b/.test(e.display.lineDiv.className)||_s(e)}}function _s(t){var e=t.display.lineDiv;function n(r){r.keyCode!=18&&r.altKey||(he(e,"CodeMirror-crosshair"),Nt(document,"keyup",n),Nt(document,"mouseover",n))}de(e,"CodeMirror-crosshair"),D(document,"keyup",n),D(document,"mouseover",n)}function Fl(t){t.keyCode==16&&(this.doc.sel.shift=!1),it(this,t)}function Pl(t){var e=this;if(!(t.target&&t.target!=e.display.input.getField()||Xt(e.display,t)||it(e,t)||t.ctrlKey&&!t.altKey||At&&t.metaKey)){var n=t.keyCode,r=t.charCode;if(L&&n==Ni)return Ni=null,void pt(t);if(!L||t.which&&!(t.which<10)||!Hl(e,t)){var i=String.fromCharCode(r??n);i!="\b"&&(Ks(e,t,i)||e.display.input.onKeyPress(t))}}}var Wr,Hr,$s=400,Oi=function(t,e,n){this.time=t,this.pos=e,this.button=n};function js(t,e){var n=+new Date;return Hr&&Hr.compare(n,t,e)?(Wr=Hr=null,"triple"):Wr&&Wr.compare(n,t,e)?(Hr=new Oi(n,t,e),Wr=null,"double"):(Wr=new Oi(n,t,e),Hr=null,"single")}function Il(t){var e=this,n=e.display;if(!(it(e,t)||n.activeTouch&&n.input.supportsTouch())){if(n.input.ensurePolled(),n.shift=t.shiftKey,Xt(n,t))A||(n.scroller.draggable=!1,setTimeout(function(){return n.scroller.draggable=!0},100));else if(!Ai(e,t)){var r=we(e,t),i=ji(t),o=r?js(r,i):"single";Pr(e).focus(),i==1&&e.state.selectingText&&e.state.selectingText(t),r&&Xs(e,i,r,o,t)||(i==1?r?qs(e,r,o,t):Dn(t)==n.scroller&&pt(t):i==2?(r&&cn(e.doc,r),setTimeout(function(){return n.input.focus()},20)):i==3&&(wn?e.display.input.onContextMenu(t):ci(e)))}}}function Xs(t,e,n,r,i){var o="Click";return r=="double"?o="Double"+o:r=="triple"&&(o="Triple"+o),Dr(t,Nl(o=(e==1?"Left":e==2?"Middle":"Right")+o,i),i,function(l){if(typeof l=="string"&&(l=Ar[l]),!l)return!1;var a=!1;try{t.isReadOnly()&&(t.state.suppressEdits=!0),a=l(t,n)!=Ir}finally{t.state.suppressEdits=!1}return a})}function Ys(t,e,n){var r=t.getOption("configureMouse"),i=r?r(t,e,n):{};if(i.unit==null){var o=Zl?n.shiftKey&&n.metaKey:n.altKey;i.unit=o?"rectangle":e=="single"?"char":e=="double"?"word":"line"}return(i.extend==null||t.doc.extend)&&(i.extend=t.doc.extend||n.shiftKey),i.addNew==null&&(i.addNew=At?n.metaKey:n.ctrlKey),i.moveOnDrag==null&&(i.moveOnDrag=!(At?n.altKey:n.ctrlKey)),i}function qs(t,e,n,r){H?setTimeout(Sn(zo,t),0):t.curOp.focus=Dt(We(t));var i,o=Ys(t,n,r),l=t.doc.sel;t.options.dragDrop&&ia&&!t.isReadOnly()&&n=="single"&&(i=l.contains(e))>-1&&(E((i=l.ranges[i]).from(),e)<0||e.xRel>0)&&(E(i.to(),e)>0||e.xRel<0)?Zs(t,r,e,o):Js(t,r,e,o)}function Zs(t,e,n,r){var i=t.display,o=!1,l=lt(t,function(u){A&&(i.scroller.draggable=!1),t.state.draggingText=!1,t.state.delayingBlurEvent&&(t.hasFocus()?t.state.delayingBlurEvent=!1:ci(t)),Nt(i.wrapper.ownerDocument,"mouseup",l),Nt(i.wrapper.ownerDocument,"mousemove",a),Nt(i.scroller,"dragstart",s),Nt(i.scroller,"drop",l),o||(pt(u),r.addNew||cn(t.doc,n,null,null,r.extend),A&&!x||H&&P==9?setTimeout(function(){i.wrapper.ownerDocument.body.focus({preventScroll:!0}),i.input.focus()},20):i.input.focus())}),a=function(u){o=o||Math.abs(e.clientX-u.clientX)+Math.abs(e.clientY-u.clientY)>=10},s=function(){return o=!0};A&&(i.scroller.draggable=!0),t.state.draggingText=l,l.copy=!r.moveOnDrag,D(i.wrapper.ownerDocument,"mouseup",l),D(i.wrapper.ownerDocument,"mousemove",a),D(i.scroller,"dragstart",s),D(i.scroller,"drop",l),t.state.delayingBlurEvent=!0,setTimeout(function(){return i.input.focus()},20),i.scroller.dragDrop&&i.scroller.dragDrop()}function zl(t,e,n){if(n=="char")return new z(e,e);if(n=="word")return t.findWordAt(e);if(n=="line")return new z(v(e.line,0),F(t.doc,v(e.line+1,0)));var r=n(t,e);return new z(r.from,r.to)}function Js(t,e,n,r){H&&ci(t);var i=t.display,o=t.doc;pt(e);var l,a,s=o.sel,u=s.ranges;if(r.addNew&&!r.extend?(a=o.sel.contains(n),l=a>-1?u[a]:new z(n,n)):(l=o.sel.primary(),a=o.sel.primIndex),r.unit=="rectangle")r.addNew||(l=new z(n,n)),n=we(t,e,!0,!0),a=-1;else{var c=zl(t,n,r.unit);l=r.extend?Si(l,c.anchor,c.head,r.extend):c}r.addNew?a==-1?(a=u.length,ut(o,It(t,u.concat([l]),a),{scroll:!1,origin:"*mouse"})):u.length>1&&u[a].empty()&&r.unit=="char"&&!r.extend?(ut(o,It(t,u.slice(0,a).concat(u.slice(a+1)),0),{scroll:!1,origin:"*mouse"}),s=o.sel):ki(o,a,l,kn):(a=0,ut(o,new Ot([l],0),kn),s=o.sel);var f=n;function h(y){if(E(f,y)!=0)if(f=y,r.unit=="rectangle"){for(var S=[],C=t.options.tabSize,T=Wt(M(o,n.line).text,n.ch,C),G=Wt(M(o,y.line).text,y.ch,C),K=Math.min(T,G),St=Math.max(T,G),j=Math.min(n.line,y.line),Et=Math.min(t.lastLine(),Math.max(n.line,y.line));j<=Et;j++){var kt=M(o,j).text,q=Ln(kt,K,C);K==St?S.push(new z(v(j,q),v(j,q))):kt.length>q&&S.push(new z(v(j,q),v(j,Ln(kt,St,C))))}S.length||S.push(new z(n,n)),ut(o,It(t,s.ranges.slice(0,a).concat(S),a),{origin:"*mouse",scroll:!1}),t.scrollIntoView(y)}else{var mt,ht=l,et=zl(t,y,r.unit),J=ht.anchor;E(et.anchor,J)>0?(mt=et.head,J=Gr(ht.from(),et.anchor)):(mt=et.anchor,J=Ur(ht.to(),et.head));var rt=s.ranges.slice(0);rt[a]=Qs(t,new z(F(o,J),mt)),ut(o,It(t,rt,a),kn)}}var d=i.wrapper.getBoundingClientRect(),p=0;function g(y){var S=++p,C=we(t,y,!0,r.unit=="rectangle");if(C)if(E(C,f)!=0){t.curOp.focus=Dt(We(t)),h(C);var T=nn(i,o);(C.line>=T.to||C.line<T.from)&&setTimeout(lt(t,function(){p==S&&g(y)}),150)}else{var G=y.clientY<d.top?-20:y.clientY>d.bottom?20:0;G&&setTimeout(lt(t,function(){p==S&&(i.scroller.scrollTop+=G,g(y))}),50)}}function m(y){t.state.selectingText=!1,p=1/0,y&&(pt(y),i.input.focus()),Nt(i.wrapper.ownerDocument,"mousemove",b),Nt(i.wrapper.ownerDocument,"mouseup",w),o.history.lastSelOrigin=null}var b=lt(t,function(y){y.buttons!==0&&ji(y)?g(y):m(y)}),w=lt(t,m);t.state.selectingText=w,D(i.wrapper.ownerDocument,"mousemove",b),D(i.wrapper.ownerDocument,"mouseup",w)}function Qs(t,e){var n=e.anchor,r=e.head,i=M(t.doc,n.line);if(E(n,r)==0&&n.sticky==r.sticky)return e;var o=_t(i);if(!o)return e;var l=or(o,n.ch,n.sticky),a=o[l];if(a.from!=n.ch&&a.to!=n.ch)return e;var s,u=l+(a.from==n.ch==(a.level!=1)?0:1);if(u==0||u==o.length)return e;if(r.line!=n.line)s=(r.line-n.line)*(t.doc.direction=="ltr"?1:-1)>0;else{var c=or(o,r.ch,r.sticky),f=c-l||(r.ch-n.ch)*(a.level==1?-1:1);s=c==u-1||c==u?f<0:f>0}var h=o[u+(s?-1:0)],d=s==(h.level==1),p=d?h.from:h.to,g=d?"after":"before";return n.ch==p&&n.sticky==g?e:new z(new v(n.line,p,g),r)}function Rl(t,e,n,r){var i,o;if(e.touches)i=e.touches[0].clientX,o=e.touches[0].clientY;else try{i=e.clientX,o=e.clientY}catch{return!1}if(i>=Math.floor(t.display.gutters.getBoundingClientRect().right))return!1;r&&pt(e);var l=t.display,a=l.lineDiv.getBoundingClientRect();if(o>a.bottom||!Ht(t,n))return An(e);o-=a.top-l.viewOffset;for(var s=0;s<t.display.gutterSpecs.length;++s){var u=l.gutters.childNodes[s];if(u&&u.getBoundingClientRect().right>=i)return Q(t,n,t,ye(t.doc,o),t.display.gutterSpecs[s].className,e),An(e)}}function Ai(t,e){return Rl(t,e,"gutterClick",!0)}function Bl(t,e){Xt(t.display,e)||tu(t,e)||it(t,e,"contextmenu")||wn||t.display.input.onContextMenu(e)}function tu(t,e){return!!Ht(t,"gutterContextMenu")&&Rl(t,e,"gutterContextMenu",!1)}function Vl(t){t.display.wrapper.className=t.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+t.options.theme.replace(/(^|\s)\s*/g," cm-s-"),dr(t)}Oi.prototype.compare=function(t,e,n){return this.time+$s>t&&E(e,this.pos)==0&&n==this.button};var Ze={toString:function(){return"CodeMirror.Init"}},Ul={},mn={};function eu(t){var e=t.optionHandlers;function n(r,i,o,l){t.defaults[r]=i,o&&(e[r]=l?function(a,s,u){u!=Ze&&o(a,s,u)}:o)}t.defineOption=n,t.Init=Ze,n("value","",function(r,i){return r.setValue(i)},!0),n("mode",null,function(r,i){r.doc.modeOption=i,xi(r)},!0),n("indentUnit",2,xi,!0),n("indentWithTabs",!1),n("smartIndent",!0),n("tabSize",4,function(r){wr(r),dr(r),gt(r)},!0),n("lineSeparator",null,function(r,i){if(r.doc.lineSep=i,i){var o=[],l=r.doc.first;r.doc.iter(function(s){for(var u=0;;){var c=s.text.indexOf(i,u);if(c==-1)break;u=c+i.length,o.push(v(l,c))}l++});for(var a=o.length-1;a>=0;a--)je(r.doc,i,o[a],v(o[a].line,o[a].ch+i.length))}}),n("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b\u200e\u200f\u2028\u2029\u202d\u202e\u2066\u2067\u2069\ufeff\ufff9-\ufffc]/g,function(r,i,o){r.state.specialChars=new RegExp(i.source+(i.test("	")?"":"|	"),"g"),o!=Ze&&r.refresh()}),n("specialCharPlaceholder",Aa,function(r){return r.refresh()},!0),n("electricChars",!0),n("inputStyle",Jt?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),n("spellcheck",!1,function(r,i){return r.getInputField().spellcheck=i},!0),n("autocorrect",!1,function(r,i){return r.getInputField().autocorrect=i},!0),n("autocapitalize",!1,function(r,i){return r.getInputField().autocapitalize=i},!0),n("rtlMoveVisually",!Jl),n("wholeLineUpdateBefore",!0),n("theme","default",function(r){Vl(r),xr(r)},!0),n("keyMap","default",function(r,i,o){var l=gn(i),a=o!=Ze&&gn(o);a&&a.detach&&a.detach(r,l),l.attach&&l.attach(r,a||null)}),n("extraKeys",null),n("configureMouse",null),n("lineWrapping",!1,nu,!0),n("gutters",[],function(r,i){r.display.gutterSpecs=yi(i,r.options.lineNumbers),xr(r)},!0),n("fixedGutter",!0,function(r,i){r.display.gutters.style.left=i?li(r.display)+"px":"0",r.refresh()},!0),n("coverGutterNextToScrollbar",!1,function(r){return Ge(r)},!0),n("scrollbarStyle","native",function(r){Ko(r),Ge(r),r.display.scrollbars.setScrollTop(r.doc.scrollTop),r.display.scrollbars.setScrollLeft(r.doc.scrollLeft)},!0),n("lineNumbers",!1,function(r,i){r.display.gutterSpecs=yi(r.options.gutters,i),xr(r)},!0),n("firstLineNumber",1,xr,!0),n("lineNumberFormatter",function(r){return r},xr,!0),n("showCursorWhenSelecting",!1,pr,!0),n("resetSelectionOnContextMenu",!0),n("lineWiseCopyCut",!0),n("pasteLinesPerSelection",!0),n("selectionsMayTouch",!1),n("readOnly",!1,function(r,i){i=="nocursor"&&(Ve(r),r.display.input.blur()),r.display.input.readOnlyChanged(i)}),n("screenReaderLabel",null,function(r,i){i=i===""?null:i,r.display.input.screenReaderLabelChanged(i)}),n("disableInput",!1,function(r,i){i||r.display.input.reset()},!0),n("dragDrop",!0,ru),n("allowDropFileTypes",null),n("cursorBlinkRate",530),n("cursorScrollMargin",0),n("cursorHeight",1,pr,!0),n("singleCursorHeightPerLine",!0,pr,!0),n("workTime",100),n("workDelay",100),n("flattenSpans",!0,wr,!0),n("addModeClass",!1,wr,!0),n("pollInterval",100),n("undoDepth",200,function(r,i){return r.doc.history.undoDepth=i}),n("historyEventDelay",1250),n("viewportMargin",10,function(r){return r.refresh()},!0),n("maxHighlightLength",1e4,wr,!0),n("moveInputWithCursor",!0,function(r,i){i||r.display.input.resetPosition()}),n("tabindex",null,function(r,i){return r.display.input.getField().tabIndex=i||""}),n("autofocus",null),n("direction","ltr",function(r,i){return r.doc.setDirection(i)},!0),n("phrases",null)}function ru(t,e,n){if(!e!=!(n&&n!=Ze)){var r=t.display.dragFunctions,i=e?D:Nt;i(t.display.scroller,"dragstart",r.start),i(t.display.scroller,"dragenter",r.enter),i(t.display.scroller,"dragover",r.over),i(t.display.scroller,"dragleave",r.leave),i(t.display.scroller,"drop",r.drop)}}function nu(t){t.options.lineWrapping?(de(t.display.wrapper,"CodeMirror-wrap"),t.display.sizer.style.minWidth="",t.display.sizerWidth=null):(he(t.display.wrapper,"CodeMirror-wrap"),qn(t)),ai(t),gt(t),dr(t),setTimeout(function(){return Ge(t)},100)}function X(t,e){var n=this;if(!(this instanceof X))return new X(t,e);this.options=e=e?ge(e):{},ge(Ul,e,!1);var r=e.value;typeof r=="string"?r=new vt(r,e.mode,null,e.lineSeparator,e.direction):e.mode&&(r.modeOption=e.mode),this.doc=r;var i=new X.inputStyles[e.inputStyle](this),o=this.display=new vs(t,r,i,e);for(var l in o.wrapper.CodeMirror=this,Vl(this),e.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),Ko(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new ee,keySeq:null,specialChars:null},e.autofocus&&!Jt&&o.input.focus(),H&&P<11&&setTimeout(function(){return n.display.input.reset(!0)},20),iu(this),Fs(),Le(this),this.curOp.forceUpdate=!0,tl(this,r),e.autofocus&&!Jt||this.hasFocus()?setTimeout(function(){n.hasFocus()&&!n.state.focused&&fi(n)},20):Ve(this),mn)mn.hasOwnProperty(l)&&mn[l](this,e[l],Ze);jo(this),e.finishInit&&e.finishInit(this);for(var a=0;a<Di.length;++a)Di[a](this);Te(this),A&&e.lineWrapping&&getComputedStyle(o.lineDiv).textRendering=="optimizelegibility"&&(o.lineDiv.style.textRendering="auto")}function iu(t){var e=t.display;D(e.scroller,"mousedown",lt(t,Il)),D(e.scroller,"dblclick",H&&P<11?lt(t,function(s){if(!it(t,s)){var u=we(t,s);if(u&&!Ai(t,s)&&!Xt(t.display,s)){pt(s);var c=t.findWordAt(u);cn(t.doc,c.anchor,c.head)}}}):function(s){return it(t,s)||pt(s)}),D(e.scroller,"contextmenu",function(s){return Bl(t,s)}),D(e.input.getField(),"contextmenu",function(s){e.scroller.contains(s.target)||Bl(t,s)});var n,r={end:0};function i(){e.activeTouch&&(n=setTimeout(function(){return e.activeTouch=null},1e3),(r=e.activeTouch).end=+new Date)}function o(s){if(s.touches.length!=1)return!1;var u=s.touches[0];return u.radiusX<=1&&u.radiusY<=1}function l(s,u){if(u.left==null)return!0;var c=u.left-s.left,f=u.top-s.top;return c*c+f*f>400}D(e.scroller,"touchstart",function(s){if(!it(t,s)&&!o(s)&&!Ai(t,s)){e.input.ensurePolled(),clearTimeout(n);var u=+new Date;e.activeTouch={start:u,moved:!1,prev:u-r.end<=300?r:null},s.touches.length==1&&(e.activeTouch.left=s.touches[0].pageX,e.activeTouch.top=s.touches[0].pageY)}}),D(e.scroller,"touchmove",function(){e.activeTouch&&(e.activeTouch.moved=!0)}),D(e.scroller,"touchend",function(s){var u=e.activeTouch;if(u&&!Xt(e,s)&&u.left!=null&&!u.moved&&new Date-u.start<300){var c,f=t.coordsChar(e.activeTouch,"page");c=!u.prev||l(u,u.prev)?new z(f,f):!u.prev.prev||l(u,u.prev.prev)?t.findWordAt(f):new z(v(f.line,0),F(t.doc,v(f.line+1,0))),t.setSelection(c.anchor,c.head),t.focus(),pt(s)}i()}),D(e.scroller,"touchcancel",i),D(e.scroller,"scroll",function(){e.scroller.clientHeight&&(vr(t,e.scroller.scrollTop),Se(t,e.scroller.scrollLeft,!0),Q(t,"scroll",t))}),D(e.scroller,"mousewheel",function(s){return qo(t,s)}),D(e.scroller,"DOMMouseScroll",function(s){return qo(t,s)}),D(e.wrapper,"scroll",function(){return e.wrapper.scrollTop=e.wrapper.scrollLeft=0}),e.dragFunctions={enter:function(s){it(t,s)||lr(s)},over:function(s){it(t,s)||(Es(t,s),lr(s))},start:function(s){return Hs(t,s)},drop:lt(t,Ws),leave:function(s){it(t,s)||kl(t)}};var a=e.input.getField();D(a,"keyup",function(s){return Fl.call(t,s)}),D(a,"keydown",lt(t,El)),D(a,"keypress",lt(t,Pl)),D(a,"focus",function(s){return fi(t,s)}),D(a,"blur",function(s){return Ve(t,s)})}X.defaults=Ul,X.optionHandlers=mn;var Di=[];function Er(t,e,n,r){var i,o=t.doc;n==null&&(n="add"),n=="smart"&&(o.mode.indent?i=sr(t,e).state:n="prev");var l=t.options.tabSize,a=M(o,e),s=Wt(a.text,null,l);a.stateAfter&&(a.stateAfter=null);var u,c=a.text.match(/^\s*/)[0];if(r||/\S/.test(a.text)){if(n=="smart"&&((u=o.mode.indent(i,a.text.slice(c.length),a.text))==Ir||u>150)){if(!r)return;n="prev"}}else u=0,n="not";n=="prev"?u=e>o.first?Wt(M(o,e-1).text,null,l):0:n=="add"?u=s+t.options.indentUnit:n=="subtract"?u=s-t.options.indentUnit:typeof n=="number"&&(u=s+n),u=Math.max(0,u);var f="",h=0;if(t.options.indentWithTabs)for(var d=Math.floor(u/l);d;--d)h+=l,f+="	";if(h<u&&(f+=Tn(u-h)),f!=c)return je(o,f,v(e,0),v(e,c.length),"+input"),a.stateAfter=null,!0;for(var p=0;p<o.sel.ranges.length;p++){var g=o.sel.ranges[p];if(g.head.line==e&&g.head.ch<c.length){var m=v(e,c.length);ki(o,p,new z(m,m));break}}}X.defineInitHook=function(t){return Di.push(t)};var zt=null;function yn(t){zt=t}function Wi(t,e,n,r,i){var o=t.doc;t.display.shift=!1,r||(r=o.sel);var l=+new Date-200,a=i=="paste"||t.state.pasteIncoming>l,s=Fn(e),u=null;if(a&&r.ranges.length>1)if(zt&&zt.text.join(`
`)==e){if(r.ranges.length%zt.text.length==0){u=[];for(var c=0;c<zt.text.length;c++)u.push(o.splitLines(zt.text[c]))}}else s.length==r.ranges.length&&t.options.pasteLinesPerSelection&&(u=Rr(s,function(b){return[b]}));for(var f=t.curOp.updateInput,h=r.ranges.length-1;h>=0;h--){var d=r.ranges[h],p=d.from(),g=d.to();d.empty()&&(n&&n>0?p=v(p.line,p.ch-n):t.state.overwrite&&!a?g=v(g.line,Math.min(M(o,g.line).text.length,g.ch+B(s).length)):a&&zt&&zt.lineWise&&zt.text.join(`
`)==s.join(`
`)&&(p=g=v(p.line,0)));var m={from:p,to:g,text:u?u[h%u.length]:s,origin:i||(a?"paste":t.state.cutIncoming>l?"cut":"+input")};$e(t.doc,m),ot(t,"inputRead",t,m)}e&&!a&&Kl(t,e),Ue(t),t.curOp.updateInput<2&&(t.curOp.updateInput=f),t.curOp.typing=!0,t.state.pasteIncoming=t.state.cutIncoming=-1}function Gl(t,e){var n=t.clipboardData&&t.clipboardData.getData("Text");if(n)return t.preventDefault(),e.isReadOnly()||e.options.disableInput||!e.hasFocus()||Ct(e,function(){return Wi(e,n,0,null,"paste")}),!0}function Kl(t,e){if(t.options.electricChars&&t.options.smartIndent)for(var n=t.doc.sel,r=n.ranges.length-1;r>=0;r--){var i=n.ranges[r];if(!(i.head.ch>100||r&&n.ranges[r-1].head.line==i.head.line)){var o=t.getModeAt(i.head),l=!1;if(o.electricChars){for(var a=0;a<o.electricChars.length;a++)if(e.indexOf(o.electricChars.charAt(a))>-1){l=Er(t,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(M(t.doc,i.head.line).text.slice(0,i.head.ch))&&(l=Er(t,i.head.line,"smart"));l&&ot(t,"electricInput",t,i.head.line)}}}function _l(t){for(var e=[],n=[],r=0;r<t.doc.sel.ranges.length;r++){var i=t.doc.sel.ranges[r].head.line,o={anchor:v(i,0),head:v(i+1,0)};n.push(o),e.push(t.getRange(o.anchor,o.head))}return{text:e,ranges:n}}function Hi(t,e,n,r){t.setAttribute("autocorrect",n?"on":"off"),t.setAttribute("autocapitalize",r?"on":"off"),t.setAttribute("spellcheck",!!e)}function $l(){var t=O("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; min-height: 1em; outline: none"),e=O("div",[t],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return A?t.style.width="1000px":t.setAttribute("wrap","off"),_&&(t.style.border="1px solid black"),e}function ou(t){var e=t.optionHandlers,n=t.helpers={};t.prototype={constructor:t,focus:function(){Pr(this).focus(),this.display.input.focus()},setOption:function(r,i){var o=this.options,l=o[r];o[r]==i&&r!="mode"||(o[r]=i,e.hasOwnProperty(r)&&lt(this,e[r])(this,i,l),Q(this,"optionChange",this,r))},getOption:function(r){return this.options[r]},getDoc:function(){return this.doc},addKeyMap:function(r,i){this.state.keyMaps[i?"push":"unshift"](gn(r))},removeKeyMap:function(r){for(var i=this.state.keyMaps,o=0;o<i.length;++o)if(i[o]==r||i[o].name==r)return i.splice(o,1),!0},addOverlay:ft(function(r,i){var o=r.token?r:t.getMode(this.options,r);if(o.startState)throw new Error("Overlays may not be stateful.");Ql(this.state.overlays,{mode:o,modeSpec:r,opaque:i&&i.opaque,priority:i&&i.priority||0},function(l){return l.priority}),this.state.modeGen++,gt(this)}),removeOverlay:ft(function(r){for(var i=this.state.overlays,o=0;o<i.length;++o){var l=i[o].modeSpec;if(l==r||typeof r=="string"&&l.name==r)return i.splice(o,1),this.state.modeGen++,void gt(this)}}),indentLine:ft(function(r,i,o){typeof i!="string"&&typeof i!="number"&&(i=i==null?this.options.smartIndent?"smart":"prev":i?"add":"subtract"),ar(this.doc,r)&&Er(this,r,i,o)}),indentSelection:ft(function(r){for(var i=this.doc.sel.ranges,o=-1,l=0;l<i.length;l++){var a=i[l];if(a.empty())a.head.line>o&&(Er(this,a.head.line,r,!0),o=a.head.line,l==this.doc.sel.primIndex&&Ue(this));else{var s=a.from(),u=a.to(),c=Math.max(o,s.line);o=Math.min(this.lastLine(),u.line-(u.ch?0:1))+1;for(var f=c;f<o;++f)Er(this,f,r);var h=this.doc.sel.ranges;s.ch==0&&i.length==h.length&&h[l].from().ch>0&&ki(this.doc,l,new z(s,h[l].to()),Bt)}}}),getTokenAt:function(r,i){return eo(this,r,i)},getLineTokens:function(r,i){return eo(this,v(r),i,!0)},getTokenTypeAt:function(r){r=F(this.doc,r);var i,o=Ji(this,M(this.doc,r.line)),l=0,a=(o.length-1)/2,s=r.ch;if(s==0)i=o[2];else for(;;){var u=l+a>>1;if((u?o[2*u-1]:0)>=s)a=u;else{if(!(o[2*u+1]<s)){i=o[2*u+2];break}l=u+1}}var c=i?i.indexOf("overlay "):-1;return c<0?i:c==0?null:i.slice(0,c-1)},getModeAt:function(r){var i=this.doc.mode;return i.innerMode?t.innerMode(i,this.getTokenAt(r).state).mode:i},getHelper:function(r,i){return this.getHelpers(r,i)[0]},getHelpers:function(r,i){var o=[];if(!n.hasOwnProperty(i))return o;var l=n[i],a=this.getModeAt(r);if(typeof a[i]=="string")l[a[i]]&&o.push(l[a[i]]);else if(a[i])for(var s=0;s<a[i].length;s++){var u=l[a[i][s]];u&&o.push(u)}else a.helperType&&l[a.helperType]?o.push(l[a.helperType]):l[a.name]&&o.push(l[a.name]);for(var c=0;c<l._global.length;c++){var f=l._global[c];f.pred(a,this)&&st(o,f.val)==-1&&o.push(f.val)}return o},getStateAfter:function(r,i){var o=this.doc;return sr(this,(r=Yi(o,r??o.first+o.size-1))+1,i).state},cursorCoords:function(r,i){var o=this.doc.sel.primary();return Pt(this,r==null?o.head:typeof r=="object"?F(this.doc,r):r?o.from():o.to(),i||"page")},charCoords:function(r,i){return Qr(this,F(this.doc,r),i||"page")},coordsChar:function(r,i){return ni(this,(r=Do(this,r,i||"page")).left,r.top)},lineAtHeight:function(r,i){return r=Do(this,{top:r,left:0},i||"page").top,ye(this.doc,r+this.display.viewOffset)},heightAtLine:function(r,i,o){var l,a=!1;if(typeof r=="number"){var s=this.doc.first+this.doc.size-1;r<this.doc.first?r=this.doc.first:r>s&&(r=s,a=!0),l=M(this.doc,r)}else l=r;return Jr(this,l,{top:0,left:0},i||"page",o||a).top+(a?this.doc.height-jt(l):0)},defaultTextHeight:function(){return Re(this.display)},defaultCharWidth:function(){return Be(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(r,i,o,l,a){var s=this.display,u=(r=Pt(this,F(this.doc,r))).bottom,c=r.left;if(i.style.position="absolute",i.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(i),s.sizer.appendChild(i),l=="over")u=r.top;else if(l=="above"||l=="near"){var f=Math.max(s.wrapper.clientHeight,this.doc.height),h=Math.max(s.sizer.clientWidth,s.lineSpace.clientWidth);(l=="above"||r.bottom+i.offsetHeight>f)&&r.top>i.offsetHeight?u=r.top-i.offsetHeight:r.bottom+i.offsetHeight<=f&&(u=r.bottom),c+i.offsetWidth>h&&(c=h-i.offsetWidth)}i.style.top=u+"px",i.style.left=i.style.right="",a=="right"?(c=s.sizer.clientWidth-i.offsetWidth,i.style.right="0px"):(a=="left"?c=0:a=="middle"&&(c=(s.sizer.clientWidth-i.offsetWidth)/2),i.style.left=c+"px"),o&&rs(this,{left:c,top:u,right:c+i.offsetWidth,bottom:u+i.offsetHeight})},triggerOnKeyDown:ft(El),triggerOnKeyPress:ft(Pl),triggerOnKeyUp:Fl,triggerOnMouseDown:ft(Il),execCommand:function(r){if(Ar.hasOwnProperty(r))return Ar[r].call(null,this)},triggerElectric:ft(function(r){Kl(this,r)}),findPosH:function(r,i,o,l){var a=1;i<0&&(a=-1,i=-i);for(var s=F(this.doc,r),u=0;u<i&&!(s=Ei(this.doc,s,a,o,l)).hitSide;++u);return s},moveH:ft(function(r,i){var o=this;this.extendSelectionsBy(function(l){return o.display.shift||o.doc.extend||l.empty()?Ei(o.doc,l.head,r,i,o.options.rtlMoveVisually):r<0?l.from():l.to()},rr)}),deleteH:ft(function(r,i){var o=this.doc.sel,l=this.doc;o.somethingSelected()?l.replaceSelection("",null,"+delete"):qe(this,function(a){var s=Ei(l,a.head,r,i,!1);return r<0?{from:s,to:a.head}:{from:a.head,to:s}})}),findPosV:function(r,i,o,l){var a=1,s=l;i<0&&(a=-1,i=-i);for(var u=F(this.doc,r),c=0;c<i;++c){var f=Pt(this,u,"div");if(s==null?s=f.left:f.left=s,(u=jl(this,f,a,o)).hitSide)break}return u},moveV:ft(function(r,i){var o=this,l=this.doc,a=[],s=!this.display.shift&&!l.extend&&l.sel.somethingSelected();if(l.extendSelectionsBy(function(c){if(s)return r<0?c.from():c.to();var f=Pt(o,c.head,"div");c.goalColumn!=null&&(f.left=c.goalColumn),a.push(f.left);var h=jl(o,f,r,i);return i=="page"&&c==l.sel.primary()&&di(o,Qr(o,h,"div").top-f.top),h},rr),a.length)for(var u=0;u<l.sel.ranges.length;u++)l.sel.ranges[u].goalColumn=a[u]}),findWordAt:function(r){var i=M(this.doc,r.line).text,o=r.ch,l=r.ch;if(i){var a=this.getHelper(r,"wordChars");r.sticky!="before"&&l!=i.length||!o?++l:--o;for(var s=i.charAt(o),u=Br(s,a)?function(c){return Br(c,a)}:/\s/.test(s)?function(c){return/\s/.test(c)}:function(c){return!/\s/.test(c)&&!Br(c)};o>0&&u(i.charAt(o-1));)--o;for(;l<i.length&&u(i.charAt(l));)++l}return new z(v(r.line,o),v(r.line,l))},toggleOverwrite:function(r){r!=null&&r==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?de(this.display.cursorDiv,"CodeMirror-overwrite"):he(this.display.cursorDiv,"CodeMirror-overwrite"),Q(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==Dt(We(this))},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:ft(function(r,i){gr(this,r,i)}),getScrollInfo:function(){var r=this.display.scroller;return{left:r.scrollLeft,top:r.scrollTop,height:r.scrollHeight-Gt(this)-this.display.barHeight,width:r.scrollWidth-Gt(this)-this.display.barWidth,clientHeight:Qn(this),clientWidth:be(this)}},scrollIntoView:ft(function(r,i){r==null?(r={from:this.doc.sel.primary().head,to:null},i==null&&(i=this.options.cursorScrollMargin)):typeof r=="number"?r={from:v(r,0),to:null}:r.from==null&&(r={from:r,to:null}),r.to||(r.to=r.from),r.margin=i||0,r.from.line!=null?ns(this,r):Bo(this,r.from,r.to,r.margin)}),setSize:ft(function(r,i){var o=this,l=function(s){return typeof s=="number"||/^\d+$/.test(String(s))?s+"px":s};r!=null&&(this.display.wrapper.style.width=l(r)),i!=null&&(this.display.wrapper.style.height=l(i)),this.options.lineWrapping&&No(this);var a=this.display.viewFrom;this.doc.iter(a,this.display.viewTo,function(s){if(s.widgets){for(var u=0;u<s.widgets.length;u++)if(s.widgets[u].noHScroll){ne(o,a,"widget");break}}++a}),this.curOp.forceUpdate=!0,Q(this,"refresh",this)}),operation:function(r){return Ct(this,r)},startOperation:function(){return Le(this)},endOperation:function(){return Te(this)},refresh:ft(function(){var r=this.display.cachedTextHeight;gt(this),this.curOp.forceUpdate=!0,dr(this),gr(this,this.doc.scrollLeft,this.doc.scrollTop),vi(this.display),(r==null||Math.abs(r-Re(this.display))>.5||this.options.lineWrapping)&&ai(this),Q(this,"refresh",this)}),swapDoc:ft(function(r){var i=this.doc;return i.cm=null,this.state.selectingText&&this.state.selectingText(),tl(this,r),dr(this),this.display.input.reset(),gr(this,r.scrollLeft,r.scrollTop),this.curOp.forceScroll=!0,ot(this,"swapDoc",this,i),i}),phrase:function(r){var i=this.options.phrases;return i&&Object.prototype.hasOwnProperty.call(i,r)?i[r]:r},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},He(t),t.registerHelper=function(r,i,o){n.hasOwnProperty(r)||(n[r]=t[r]={_global:[]}),n[r][i]=o},t.registerGlobalHelper=function(r,i,o,l){t.registerHelper(r,i,l),n[r]._global.push({pred:o,val:l})}}function Ei(t,e,n,r,i){var o=e,l=n,a=M(t,e.line),s=i&&t.direction=="rtl"?-n:n;function u(){var w=e.line+s;return!(w<t.first||w>=t.first+t.size)&&(e=new v(w,e.ch,e.sticky),a=M(t,w))}function c(w){var y;if(r=="codepoint"){var S=a.text.charCodeAt(e.ch+(n>0?0:-1));if(isNaN(S))y=null;else{var C=n>0?S>=55296&&S<56320:S>=56320&&S<57343;y=new v(e.line,Math.max(0,Math.min(a.text.length,e.ch+n*(C?2:1))),-n)}}else y=i?Bs(t.cm,a,e,n):Ti(a,e,n);if(y==null){if(w||!u())return!1;e=Mi(i,t.cm,a,e.line,s)}else e=y;return!0}if(r=="char"||r=="codepoint")c();else if(r=="column")c(!0);else if(r=="word"||r=="group")for(var f=null,h=r=="group",d=t.cm&&t.cm.getHelper(e,"wordChars"),p=!0;!(n<0)||c(!p);p=!1){var g=a.text.charAt(e.ch)||`
`,m=Br(g,d)?"w":h&&g==`
`?"n":!h||/\s/.test(g)?null:"p";if(!h||p||m||(m="s"),f&&f!=m){n<0&&(n=1,c(),e.sticky="after");break}if(m&&(f=m),n>0&&!c(!p))break}var b=hn(t,e,o,l,!0);return Un(o,b)&&(b.hitSide=!0),b}function jl(t,e,n,r){var i,o,l=t.doc,a=e.left;if(r=="page"){var s=Math.min(t.display.wrapper.clientHeight,Pr(t).innerHeight||l(t).documentElement.clientHeight),u=Math.max(s-.5*Re(t.display),3);i=(n>0?e.bottom:e.top)+n*u}else r=="line"&&(i=n>0?e.bottom+3:e.top-3);for(;(o=ni(t,a,i)).outside;){if(n<0?i<=0:i>=l.height){o.hitSide=!0;break}i+=5*n}return o}var U=function(t){this.cm=t,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new ee,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function Xl(t,e){var n=ti(t,e.line);if(!n||n.hidden)return null;var r=M(t.doc,e.line),i=So(n,r,e.line),o=_t(r,t.doc.direction),l="left";o&&(l=or(o,e.ch)%2?"right":"left");var a=To(i.map,e.ch,l);return a.offset=a.collapse=="right"?a.end:a.start,a}function lu(t){for(var e=t;e;e=e.parentNode)if(/CodeMirror-gutter-wrapper/.test(e.className))return!0;return!1}function Je(t,e){return e&&(t.bad=!0),t}function au(t,e,n,r,i){var o="",l=!1,a=t.doc.lineSeparator(),s=!1;function u(d){return function(p){return p.id==d}}function c(){l&&(o+=a,s&&(o+=a),l=s=!1)}function f(d){d&&(c(),o+=d)}function h(d){if(d.nodeType==1){var p=d.getAttribute("cm-text");if(p)return void f(p);var g,m=d.getAttribute("cm-marker");if(m){var b=t.findMarks(v(r,0),v(i+1,0),u(+m));return void(b.length&&(g=b[0].find(0))&&f(me(t.doc,g.from,g.to).join(a)))}if(d.getAttribute("contenteditable")=="false")return;var w=/^(pre|div|p|li|table|br)$/i.test(d.nodeName);if(!/^br$/i.test(d.nodeName)&&d.textContent.length==0)return;w&&c();for(var y=0;y<d.childNodes.length;y++)h(d.childNodes[y]);/^(pre|p)$/i.test(d.nodeName)&&(s=!0),w&&(l=!0)}else d.nodeType==3&&f(d.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;h(e),e!=n;)e=e.nextSibling,s=!1;return o}function bn(t,e,n){var r;if(e==t.display.lineDiv){if(!(r=t.display.lineDiv.childNodes[n]))return Je(t.clipPos(v(t.display.viewTo-1)),!0);e=null,n=0}else for(r=e;;r=r.parentNode){if(!r||r==t.display.lineDiv)return null;if(r.parentNode&&r.parentNode==t.display.lineDiv)break}for(var i=0;i<t.display.view.length;i++){var o=t.display.view[i];if(o.node==r)return su(o,e,n)}}function su(t,e,n){var r=t.text.firstChild,i=!1;if(!e||!te(r,e))return Je(v(V(t.line),0),!0);if(e==r&&(i=!0,e=r.childNodes[n],n=0,!e)){var o=t.rest?B(t.rest):t.line;return Je(v(V(o),o.text.length),i)}var l=e.nodeType==3?e:null,a=e;for(l||e.childNodes.length!=1||e.firstChild.nodeType!=3||(l=e.firstChild,n&&(n=l.nodeValue.length));a.parentNode!=r;)a=a.parentNode;var s=t.measure,u=s.maps;function c(m,b,w){for(var y=-1;y<(u?u.length:0);y++)for(var S=y<0?s.map:u[y],C=0;C<S.length;C+=3){var T=S[C+2];if(T==m||T==b){var G=V(y<0?t.line:t.rest[y]),K=S[C]+w;return(w<0||T!=m)&&(K=S[C+(w?1:0)]),v(G,K)}}}var f=c(l,a,n);if(f)return Je(f,i);for(var h=a.nextSibling,d=l?l.nodeValue.length-n:0;h;h=h.nextSibling){if(f=c(h,h.firstChild,0))return Je(v(f.line,f.ch-d),i);d+=h.textContent.length}for(var p=a.previousSibling,g=n;p;p=p.previousSibling){if(f=c(p,p.firstChild,-1))return Je(v(f.line,f.ch+g),i);g+=p.textContent.length}}U.prototype.init=function(t){var e=this,n=this,r=n.cm,i=n.div=t.lineDiv;function o(a){for(var s=a.target;s;s=s.parentNode){if(s==i)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(s.className))break}return!1}function l(a){if(o(a)&&!it(r,a)){if(r.somethingSelected())yn({lineWise:!1,text:r.getSelections()}),a.type=="cut"&&r.replaceSelection("",null,"cut");else{if(!r.options.lineWiseCopyCut)return;var s=_l(r);yn({lineWise:!0,text:s.text}),a.type=="cut"&&r.operation(function(){r.setSelections(s.ranges,0,Bt),r.replaceSelection("",null,"cut")})}if(a.clipboardData){a.clipboardData.clearData();var u=zt.text.join(`
`);if(a.clipboardData.setData("Text",u),a.clipboardData.getData("Text")==u)return void a.preventDefault()}var c=$l(),f=c.firstChild;Hi(f),r.display.lineSpace.insertBefore(c,r.display.lineSpace.firstChild),f.value=zt.text.join(`
`);var h=Dt(pe(i));tr(f),setTimeout(function(){r.display.lineSpace.removeChild(c),h.focus(),h==i&&n.showPrimarySelection()},50)}}i.contentEditable=!0,Hi(i,r.options.spellcheck,r.options.autocorrect,r.options.autocapitalize),D(i,"paste",function(a){!o(a)||it(r,a)||Gl(a,r)||P<=11&&setTimeout(lt(r,function(){return e.updateFromDOM()}),20)}),D(i,"compositionstart",function(a){e.composing={data:a.data,done:!1}}),D(i,"compositionupdate",function(a){e.composing||(e.composing={data:a.data,done:!1})}),D(i,"compositionend",function(a){e.composing&&(a.data!=e.composing.data&&e.readFromDOMSoon(),e.composing.done=!0)}),D(i,"touchstart",function(){return n.forceCompositionEnd()}),D(i,"input",function(){e.composing||e.readFromDOMSoon()}),D(i,"copy",l),D(i,"cut",l)},U.prototype.screenReaderLabelChanged=function(t){t?this.div.setAttribute("aria-label",t):this.div.removeAttribute("aria-label")},U.prototype.prepareSelection=function(){var t=Io(this.cm,!1);return t.focus=Dt(pe(this.div))==this.div,t},U.prototype.showSelection=function(t,e){t&&this.cm.display.view.length&&((t.focus||e)&&this.showPrimarySelection(),this.showMultipleSelections(t))},U.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},U.prototype.showPrimarySelection=function(){var t=this.getSelection(),e=this.cm,n=e.doc.sel.primary(),r=n.from(),i=n.to();if(e.display.viewTo==e.display.viewFrom||r.line>=e.display.viewTo||i.line<e.display.viewFrom)t.removeAllRanges();else{var o=bn(e,t.anchorNode,t.anchorOffset),l=bn(e,t.focusNode,t.focusOffset);if(!o||o.bad||!l||l.bad||E(Gr(o,l),r)!=0||E(Ur(o,l),i)!=0){var a=e.display.view,s=r.line>=e.display.viewFrom&&Xl(e,r)||{node:a[0].measure.map[2],offset:0},u=i.line<e.display.viewTo&&Xl(e,i);if(!u){var c=a[a.length-1].measure,f=c.maps?c.maps[c.maps.length-1]:c.map;u={node:f[f.length-1],offset:f[f.length-2]-f[f.length-3]}}if(s&&u){var h,d=t.rangeCount&&t.getRangeAt(0);try{h=Ae(s.node,s.offset,u.offset,u.node)}catch{}h&&(!$&&e.state.focused?(t.collapse(s.node,s.offset),h.collapsed||(t.removeAllRanges(),t.addRange(h))):(t.removeAllRanges(),t.addRange(h)),d&&t.anchorNode==null?t.addRange(d):$&&this.startGracePeriod()),this.rememberSelection()}else t.removeAllRanges()}}},U.prototype.startGracePeriod=function(){var t=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){t.gracePeriod=!1,t.selectionChanged()&&t.cm.operation(function(){return t.cm.curOp.selectionChanged=!0})},20)},U.prototype.showMultipleSelections=function(t){Mt(this.cm.display.cursorDiv,t.cursors),Mt(this.cm.display.selectionDiv,t.selection)},U.prototype.rememberSelection=function(){var t=this.getSelection();this.lastAnchorNode=t.anchorNode,this.lastAnchorOffset=t.anchorOffset,this.lastFocusNode=t.focusNode,this.lastFocusOffset=t.focusOffset},U.prototype.selectionInEditor=function(){var t=this.getSelection();if(!t.rangeCount)return!1;var e=t.getRangeAt(0).commonAncestorContainer;return te(this.div,e)},U.prototype.focus=function(){this.cm.options.readOnly!="nocursor"&&(this.selectionInEditor()&&Dt(pe(this.div))==this.div||this.showSelection(this.prepareSelection(),!0),this.div.focus())},U.prototype.blur=function(){this.div.blur()},U.prototype.getField=function(){return this.div},U.prototype.supportsTouch=function(){return!0},U.prototype.receivedFocus=function(){var t=this,e=this;function n(){e.cm.state.focused&&(e.pollSelection(),e.polling.set(e.cm.options.pollInterval,n))}this.selectionInEditor()?setTimeout(function(){return t.pollSelection()},20):Ct(this.cm,function(){return e.cm.curOp.selectionChanged=!0}),this.polling.set(this.cm.options.pollInterval,n)},U.prototype.selectionChanged=function(){var t=this.getSelection();return t.anchorNode!=this.lastAnchorNode||t.anchorOffset!=this.lastAnchorOffset||t.focusNode!=this.lastFocusNode||t.focusOffset!=this.lastFocusOffset},U.prototype.pollSelection=function(){if(this.readDOMTimeout==null&&!this.gracePeriod&&this.selectionChanged()){var t=this.getSelection(),e=this.cm;if(wt&&R&&this.cm.display.gutterSpecs.length&&lu(t.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var n=bn(e,t.anchorNode,t.anchorOffset),r=bn(e,t.focusNode,t.focusOffset);n&&r&&Ct(e,function(){ut(e.doc,oe(n,r),Bt),(n.bad||r.bad)&&(e.curOp.selectionChanged=!0)})}}},U.prototype.pollContent=function(){this.readDOMTimeout!=null&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var t,e,n,r=this.cm,i=r.display,o=r.doc.sel.primary(),l=o.from(),a=o.to();if(l.ch==0&&l.line>r.firstLine()&&(l=v(l.line-1,M(r.doc,l.line-1).length)),a.ch==M(r.doc,a.line).text.length&&a.line<r.lastLine()&&(a=v(a.line+1,0)),l.line<i.viewFrom||a.line>i.viewTo-1)return!1;l.line==i.viewFrom||(t=Ce(r,l.line))==0?(e=V(i.view[0].line),n=i.view[0].node):(e=V(i.view[t].line),n=i.view[t-1].node.nextSibling);var s,u,c=Ce(r,a.line);if(c==i.view.length-1?(s=i.viewTo-1,u=i.lineDiv.lastChild):(s=V(i.view[c+1].line)-1,u=i.view[c+1].node.previousSibling),!n)return!1;for(var f=r.doc.splitLines(au(r,n,u,e,s)),h=me(r.doc,v(e,0),v(s,M(r.doc,s).text.length));f.length>1&&h.length>1;)if(B(f)==B(h))f.pop(),h.pop(),s--;else{if(f[0]!=h[0])break;f.shift(),h.shift(),e++}for(var d=0,p=0,g=f[0],m=h[0],b=Math.min(g.length,m.length);d<b&&g.charCodeAt(d)==m.charCodeAt(d);)++d;for(var w=B(f),y=B(h),S=Math.min(w.length-(f.length==1?d:0),y.length-(h.length==1?d:0));p<S&&w.charCodeAt(w.length-p-1)==y.charCodeAt(y.length-p-1);)++p;if(f.length==1&&h.length==1&&e==l.line)for(;d&&d>l.ch&&w.charCodeAt(w.length-p-1)==y.charCodeAt(y.length-p-1);)d--,p++;f[f.length-1]=w.slice(0,w.length-p).replace(/^\u200b+/,""),f[0]=f[0].slice(d).replace(/\u200b+$/,"");var C=v(e,d),T=v(s,h.length?B(h).length-p:0);return f.length>1||f[0]||E(C,T)?(je(r.doc,f,C,T,"+input"),!0):void 0},U.prototype.ensurePolled=function(){this.forceCompositionEnd()},U.prototype.reset=function(){this.forceCompositionEnd()},U.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},U.prototype.readFromDOMSoon=function(){var t=this;this.readDOMTimeout==null&&(this.readDOMTimeout=setTimeout(function(){if(t.readDOMTimeout=null,t.composing){if(!t.composing.done)return;t.composing=null}t.updateFromDOM()},80))},U.prototype.updateFromDOM=function(){var t=this;!this.cm.isReadOnly()&&this.pollContent()||Ct(this.cm,function(){return gt(t.cm)})},U.prototype.setUneditable=function(t){t.contentEditable="false"},U.prototype.onKeyPress=function(t){t.charCode==0||this.composing||(t.preventDefault(),this.cm.isReadOnly()||lt(this.cm,Wi)(this.cm,String.fromCharCode(t.charCode==null?t.keyCode:t.charCode),0))},U.prototype.readOnlyChanged=function(t){this.div.contentEditable=String(t!="nocursor")},U.prototype.onContextMenu=function(){},U.prototype.resetPosition=function(){},U.prototype.needsContentAttribute=!0;var Z=function(t){this.cm=t,this.prevInput="",this.pollingFast=!1,this.polling=new ee,this.hasSelection=!1,this.composing=null,this.resetting=!1};function uu(t,e){if((e=e?ge(e):{}).value=t.value,!e.tabindex&&t.tabIndex&&(e.tabindex=t.tabIndex),!e.placeholder&&t.placeholder&&(e.placeholder=t.placeholder),e.autofocus==null){var n=Dt(pe(t));e.autofocus=n==t||t.getAttribute("autofocus")!=null&&n==document.body}function r(){t.value=a.getValue()}var i;if(t.form&&(D(t.form,"submit",r),!e.leaveSubmitMethodAlone)){var o=t.form;i=o.submit;try{var l=o.submit=function(){r(),o.submit=i,o.submit(),o.submit=l}}catch{}}e.finishInit=function(s){s.save=r,s.getTextArea=function(){return t},s.toTextArea=function(){s.toTextArea=isNaN,r(),t.parentNode.removeChild(s.getWrapperElement()),t.style.display="",t.form&&(Nt(t.form,"submit",r),e.leaveSubmitMethodAlone||typeof t.form.submit!="function"||(t.form.submit=i))}},t.style.display="none";var a=X(function(s){return t.parentNode.insertBefore(s,t.nextSibling)},e);return a}function cu(t){t.off=Nt,t.on=D,t.wheelEventPixels=ms,t.Doc=vt,t.splitLines=Fn,t.countColumn=Wt,t.findColumn=Ln,t.isWordChar=Mn,t.Pass=Ir,t.signal=Q,t.Line=Pe,t.changeEnd=le,t.scrollbarModel=Go,t.Pos=v,t.cmpPos=E,t.modes=In,t.mimeModes=Ee,t.resolveMode=Vr,t.getMode=zn,t.modeExtensions=Fe,t.extendMode=ha,t.copyState=ve,t.startState=Xi,t.innerMode=Rn,t.commands=Ar,t.keyMap=qt,t.keyName=Ol,t.isModifierKey=Ml,t.lookupKey=Ye,t.normalizeKeyMap=Rs,t.StringStream=tt,t.SharedTextMarker=Mr,t.TextMarker=se,t.LineWidget=Tr,t.e_preventDefault=pt,t.e_stopPropagation=$i,t.e_stop=lr,t.addClass=de,t.contains=te,t.rmClass=he,t.keyNames=ue}Z.prototype.init=function(t){var e=this,n=this,r=this.cm;this.createField(t);var i=this.textarea;function o(l){if(!it(r,l)){if(r.somethingSelected())yn({lineWise:!1,text:r.getSelections()});else{if(!r.options.lineWiseCopyCut)return;var a=_l(r);yn({lineWise:!0,text:a.text}),l.type=="cut"?r.setSelections(a.ranges,null,Bt):(n.prevInput="",i.value=a.text.join(`
`),tr(i))}l.type=="cut"&&(r.state.cutIncoming=+new Date)}}t.wrapper.insertBefore(this.wrapper,t.wrapper.firstChild),_&&(i.style.width="0px"),D(i,"input",function(){H&&P>=9&&e.hasSelection&&(e.hasSelection=null),n.poll()}),D(i,"paste",function(l){it(r,l)||Gl(l,r)||(r.state.pasteIncoming=+new Date,n.fastPoll())}),D(i,"cut",o),D(i,"copy",o),D(t.scroller,"paste",function(l){if(!Xt(t,l)&&!it(r,l)){if(!i.dispatchEvent)return r.state.pasteIncoming=+new Date,void n.focus();var a=new Event("paste");a.clipboardData=l.clipboardData,i.dispatchEvent(a)}}),D(t.lineSpace,"selectstart",function(l){Xt(t,l)||pt(l)}),D(i,"compositionstart",function(){var l=r.getCursor("from");n.composing&&n.composing.range.clear(),n.composing={start:l,range:r.markText(l,r.getCursor("to"),{className:"CodeMirror-composing"})}}),D(i,"compositionend",function(){n.composing&&(n.poll(),n.composing.range.clear(),n.composing=null)})},Z.prototype.createField=function(t){this.wrapper=$l(),this.textarea=this.wrapper.firstChild;var e=this.cm.options;Hi(this.textarea,e.spellcheck,e.autocorrect,e.autocapitalize)},Z.prototype.screenReaderLabelChanged=function(t){t?this.textarea.setAttribute("aria-label",t):this.textarea.removeAttribute("aria-label")},Z.prototype.prepareSelection=function(){var t=this.cm,e=t.display,n=t.doc,r=Io(t);if(t.options.moveInputWithCursor){var i=Pt(t,n.sel.primary().head,"div"),o=e.wrapper.getBoundingClientRect(),l=e.lineDiv.getBoundingClientRect();r.teTop=Math.max(0,Math.min(e.wrapper.clientHeight-10,i.top+l.top-o.top)),r.teLeft=Math.max(0,Math.min(e.wrapper.clientWidth-10,i.left+l.left-o.left))}return r},Z.prototype.showSelection=function(t){var e=this.cm.display;Mt(e.cursorDiv,t.cursors),Mt(e.selectionDiv,t.selection),t.teTop!=null&&(this.wrapper.style.top=t.teTop+"px",this.wrapper.style.left=t.teLeft+"px")},Z.prototype.reset=function(t){if(!(this.contextMenuPending||this.composing&&t)){var e=this.cm;if(this.resetting=!0,e.somethingSelected()){this.prevInput="";var n=e.getSelection();this.textarea.value=n,e.state.focused&&tr(this.textarea),H&&P>=9&&(this.hasSelection=n)}else t||(this.prevInput=this.textarea.value="",H&&P>=9&&(this.hasSelection=null));this.resetting=!1}},Z.prototype.getField=function(){return this.textarea},Z.prototype.supportsTouch=function(){return!1},Z.prototype.focus=function(){if(this.cm.options.readOnly!="nocursor"&&(!Jt||Dt(pe(this.textarea))!=this.textarea))try{this.textarea.focus()}catch{}},Z.prototype.blur=function(){this.textarea.blur()},Z.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},Z.prototype.receivedFocus=function(){this.slowPoll()},Z.prototype.slowPoll=function(){var t=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){t.poll(),t.cm.state.focused&&t.slowPoll()})},Z.prototype.fastPoll=function(){var t=!1,e=this;function n(){e.poll()||t?(e.pollingFast=!1,e.slowPoll()):(t=!0,e.polling.set(60,n))}e.pollingFast=!0,e.polling.set(20,n)},Z.prototype.poll=function(){var t=this,e=this.cm,n=this.textarea,r=this.prevInput;if(this.contextMenuPending||this.resetting||!e.state.focused||aa(n)&&!r&&!this.composing||e.isReadOnly()||e.options.disableInput||e.state.keySeq)return!1;var i=n.value;if(i==r&&!e.somethingSelected())return!1;if(H&&P>=9&&this.hasSelection===i||At&&/[\uf700-\uf7ff]/.test(i))return e.display.input.reset(),!1;if(e.doc.sel==e.display.selForContextMenu){var o=i.charCodeAt(0);if(o!=8203||r||(r="\u200B"),o==8666)return this.reset(),this.cm.execCommand("undo")}for(var l=0,a=Math.min(r.length,i.length);l<a&&r.charCodeAt(l)==i.charCodeAt(l);)++l;return Ct(e,function(){Wi(e,i.slice(l),r.length-l,null,t.composing?"*compose":null),i.length>1e3||i.indexOf(`
`)>-1?n.value=t.prevInput="":t.prevInput=i,t.composing&&(t.composing.range.clear(),t.composing.range=e.markText(t.composing.start,e.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},Z.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},Z.prototype.onKeyPress=function(){H&&P>=9&&(this.hasSelection=null),this.fastPoll()},Z.prototype.onContextMenu=function(t){var e=this,n=e.cm,r=n.display,i=e.textarea;e.contextMenuPending&&e.contextMenuPending();var o=we(n,t),l=r.scroller.scrollTop;if(o&&!L){n.options.resetSelectionOnContextMenu&&n.doc.sel.contains(o)==-1&&lt(n,ut)(n.doc,oe(o),Bt);var a,s=i.style.cssText,u=e.wrapper.style.cssText,c=e.wrapper.offsetParent.getBoundingClientRect();if(e.wrapper.style.cssText="position: static",i.style.cssText=`position: absolute; width: 30px; height: 30px;
      top: `+(t.clientY-c.top-5)+"px; left: "+(t.clientX-c.left-5)+`px;
      z-index: 1000; background: `+(H?"rgba(255, 255, 255, .05)":"transparent")+`;
      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);`,A&&(a=i.ownerDocument.defaultView.scrollY),r.input.focus(),A&&i.ownerDocument.defaultView.scrollTo(null,a),r.input.reset(),n.somethingSelected()||(i.value=e.prevInput=" "),e.contextMenuPending=d,r.selForContextMenu=n.doc.sel,clearTimeout(r.detectingSelectAll),H&&P>=9&&h(),wn){lr(t);var f=function(){Nt(window,"mouseup",f),setTimeout(d,20)};D(window,"mouseup",f)}else setTimeout(d,50)}function h(){if(i.selectionStart!=null){var p=n.somethingSelected(),g="\u200B"+(p?i.value:"");i.value="\u21DA",i.value=g,e.prevInput=p?"":"\u200B",i.selectionStart=1,i.selectionEnd=g.length,r.selForContextMenu=n.doc.sel}}function d(){if(e.contextMenuPending==d&&(e.contextMenuPending=!1,e.wrapper.style.cssText=u,i.style.cssText=s,H&&P<9&&r.scrollbars.setScrollTop(r.scroller.scrollTop=l),i.selectionStart!=null)){(!H||H&&P<9)&&h();var p=0,g=function(){r.selForContextMenu==n.doc.sel&&i.selectionStart==0&&i.selectionEnd>0&&e.prevInput=="\u200B"?lt(n,dl)(n):p++<10?r.detectingSelectAll=setTimeout(g,500):(r.selForContextMenu=null,r.input.reset())};r.detectingSelectAll=setTimeout(g,200)}}},Z.prototype.readOnlyChanged=function(t){t||this.reset(),this.textarea.disabled=t=="nocursor",this.textarea.readOnly=!!t},Z.prototype.setUneditable=function(){},Z.prototype.needsContentAttribute=!1,eu(X),ou(X);var fu="iter insert remove copy getEditor constructor".split(" ");for(var xn in vt.prototype)vt.prototype.hasOwnProperty(xn)&&st(fu,xn)<0&&(X.prototype[xn]=function(t){return function(){return t.apply(this.doc,arguments)}}(vt.prototype[xn]));return He(vt),X.inputStyles={textarea:Z,contenteditable:U},X.defineMode=function(t){X.defaults.mode||t=="null"||(X.defaults.mode=t),ca.apply(this,arguments)},X.defineMIME=fa,X.defineMode("null",function(){return{token:function(t){return t.skipToEnd()}}}),X.defineMIME("text/plain","null"),X.defineExtension=function(t,e){X.prototype[t]=e},X.defineDocExtension=function(t,e){vt.prototype[t]=e},X.fromTextArea=uu,cu(X),X.version="5.65.18",X}();var Fr=Ii.exports;const ql=bu(Fr);(function(I){var bt="CodeMirror-activeline",$="CodeMirror-activeline-background",xt="CodeMirror-activeline-gutter";function Lt(A){for(var Y=0;Y<A.state.activeLines.length;Y++)A.removeLineClass(A.state.activeLines[Y],"wrap",bt),A.removeLineClass(A.state.activeLines[Y],"background",$),A.removeLineClass(A.state.activeLines[Y],"gutter",xt)}function Tt(A,Y){if(A.length!=Y.length)return!1;for(var R=0;R<A.length;R++)if(A[R]!=Y[R])return!1;return!0}function H(A,Y){for(var R=[],W=0;W<Y.length;W++){var L=Y[W],x=A.getOption("styleActiveLine");if(typeof x=="object"&&x.nonEmpty?L.anchor.line==L.head.line:L.empty()){var k=A.getLineHandleVisualStart(L.head.line);R[R.length-1]!=k&&R.push(k)}}Tt(A.state.activeLines,R)||A.operation(function(){Lt(A);for(var N=0;N<R.length;N++)A.addLineClass(R[N],"wrap",bt),A.addLineClass(R[N],"background",$),A.addLineClass(R[N],"gutter",xt);A.state.activeLines=R})}function P(A,Y){H(A,Y.ranges)}I.defineOption("styleActiveLine",!1,function(A,Y,R){var W=R!=I.Init&&R;Y!=W&&(W&&(A.off("beforeSelectionChange",P),Lt(A),delete A.state.activeLines),Y&&(A.state.activeLines=[],H(A,A.listSelections()),A.on("beforeSelectionChange",P)))})})(Fr),function(I){I.defineMode("velocity",function(){function bt(W){for(var L={},x=W.split(" "),k=0;k<x.length;++k)L[x[k]]=!0;return L}var $=bt("#end #else #break #stop #[[ #]] #{end} #{else} #{break} #{stop}"),xt=bt("#if #elseif #foreach #set #include #parse #macro #define #evaluate #{if} #{elseif} #{foreach} #{set} #{include} #{parse} #{macro} #{define} #{evaluate}"),Lt=bt("$foreach.count $foreach.hasNext $foreach.first $foreach.last $foreach.topmost $foreach.parent.count $foreach.parent.hasNext $foreach.parent.first $foreach.parent.last $foreach.parent $velocityCount $!bodyContent $bodyContent"),Tt=/[+\-*&%=<>!?:\/|]/;function H(W,L,x){return L.tokenize=x,x(W,L)}function P(W,L){var x=L.beforeParams;L.beforeParams=!1;var k=W.next();if(k=="'"&&!L.inString&&L.inParams)return L.lastTokenWasBuiltin=!1,H(W,L,A(k));if(k!='"'){if(/[\[\]{}\(\),;\.]/.test(k))return k=="("&&x?L.inParams=!0:k==")"&&(L.inParams=!1,L.lastTokenWasBuiltin=!0),null;if(/\d/.test(k))return L.lastTokenWasBuiltin=!1,W.eatWhile(/[\w\.]/),"number";if(k=="#"&&W.eat("*"))return L.lastTokenWasBuiltin=!1,H(W,L,Y);if(k=="#"&&W.match(/ *\[ *\[/))return L.lastTokenWasBuiltin=!1,H(W,L,R);if(k=="#"&&W.eat("#"))return L.lastTokenWasBuiltin=!1,W.skipToEnd(),"comment";if(k=="$")return W.eat("!"),W.eatWhile(/[\w\d\$_\.{}-]/),Lt&&Lt.propertyIsEnumerable(W.current())?"keyword":(L.lastTokenWasBuiltin=!0,L.beforeParams=!0,"builtin");if(Tt.test(k))return L.lastTokenWasBuiltin=!1,W.eatWhile(Tt),"operator";W.eatWhile(/[\w\$_{}@]/);var N=W.current();return $&&$.propertyIsEnumerable(N)?"keyword":xt&&xt.propertyIsEnumerable(N)||W.current().match(/^#@?[a-z0-9_]+ *$/i)&&W.peek()=="("&&(!xt||!xt.propertyIsEnumerable(N.toLowerCase()))?(L.beforeParams=!0,L.lastTokenWasBuiltin=!1,"keyword"):L.inString?(L.lastTokenWasBuiltin=!1,"string"):W.pos>N.length&&W.string.charAt(W.pos-N.length-1)=="."&&L.lastTokenWasBuiltin?"builtin":(L.lastTokenWasBuiltin=!1,null)}return L.lastTokenWasBuiltin=!1,L.inString?(L.inString=!1,"string"):L.inParams?H(W,L,A(k)):void 0}function A(W){return function(L,x){for(var k,N=!1,_=!1;(k=L.next())!=null;){if(k==W&&!N){_=!0;break}if(W=='"'&&L.peek()=="$"&&!N){x.inString=!0,_=!0;break}N=!N&&k=="\\"}return _&&(x.tokenize=P),"string"}}function Y(W,L){for(var x,k=!1;x=W.next();){if(x=="#"&&k){L.tokenize=P;break}k=x=="*"}return"comment"}function R(W,L){for(var x,k=0;x=W.next();){if(x=="#"&&k==2){L.tokenize=P;break}x=="]"?k++:x!=" "&&(k=0)}return"meta"}return{startState:function(){return{tokenize:P,beforeParams:!1,inParams:!1,inString:!1,lastTokenWasBuiltin:!1}},token:function(W,L){return W.eatSpace()?null:L.tokenize(W,L)},blockCommentStart:"#*",blockCommentEnd:"*#",lineComment:"##",fold:"velocity"}}),I.defineMIME("text/velocity","velocity")}(Fr),function(I){I.defineMode("go",function(bt){var $,xt=bt.indentUnit,Lt={break:!0,case:!0,chan:!0,const:!0,continue:!0,default:!0,defer:!0,else:!0,fallthrough:!0,for:!0,func:!0,go:!0,goto:!0,if:!0,import:!0,interface:!0,map:!0,package:!0,range:!0,return:!0,select:!0,struct:!0,switch:!0,type:!0,var:!0,bool:!0,byte:!0,complex64:!0,complex128:!0,float32:!0,float64:!0,int8:!0,int16:!0,int32:!0,int64:!0,string:!0,uint8:!0,uint16:!0,uint32:!0,uint64:!0,int:!0,uint:!0,uintptr:!0,error:!0,rune:!0,any:!0,comparable:!0},Tt={true:!0,false:!0,iota:!0,nil:!0,append:!0,cap:!0,close:!0,complex:!0,copy:!0,delete:!0,imag:!0,len:!0,make:!0,new:!0,panic:!0,print:!0,println:!0,real:!0,recover:!0},H=/[+\-*&^%:=<>!|\/]/;function P(x,k){var N=x.next();if(N=='"'||N=="'"||N=="`")return k.tokenize=A(N),k.tokenize(x,k);if(/[\d\.]/.test(N))return N=="."?x.match(/^[0-9_]+([eE][\-+]?[0-9_]+)?/):N=="0"?x.match(/^[xX][0-9a-fA-F_]+/)||x.match(/^[0-7_]+/):x.match(/^[0-9_]*\.?[0-9_]*([eE][\-+]?[0-9_]+)?/),"number";if(/[\[\]{}\(\),;\:\.]/.test(N))return $=N,null;if(N=="/"){if(x.eat("*"))return k.tokenize=Y,Y(x,k);if(x.eat("/"))return x.skipToEnd(),"comment"}if(H.test(N))return x.eatWhile(H),"operator";x.eatWhile(/[\w\$_\xa1-\uffff]/);var _=x.current();return Lt.propertyIsEnumerable(_)?(_!="case"&&_!="default"||($="case"),"keyword"):Tt.propertyIsEnumerable(_)?"atom":"variable"}function A(x){return function(k,N){for(var _,wt=!1,Jt=!1;(_=k.next())!=null;){if(_==x&&!wt){Jt=!0;break}wt=!wt&&x!="`"&&_=="\\"}return(Jt||!wt&&x!="`")&&(N.tokenize=P),"string"}}function Y(x,k){for(var N,_=!1;N=x.next();){if(N=="/"&&_){k.tokenize=P;break}_=N=="*"}return"comment"}function R(x,k,N,_,wt){this.indented=x,this.column=k,this.type=N,this.align=_,this.prev=wt}function W(x,k,N){return x.context=new R(x.indented,k,N,null,x.context)}function L(x){if(x.context.prev){var k=x.context.type;return k!=")"&&k!="]"&&k!="}"||(x.indented=x.context.indented),x.context=x.context.prev}}return{startState:function(x){return{tokenize:null,context:new R((x||0)-xt,0,"top",!1),indented:0,startOfLine:!0}},token:function(x,k){var N=k.context;if(x.sol()&&(N.align==null&&(N.align=!1),k.indented=x.indentation(),k.startOfLine=!0,N.type=="case"&&(N.type="}")),x.eatSpace())return null;$=null;var _=(k.tokenize||P)(x,k);return _=="comment"||(N.align==null&&(N.align=!0),$=="{"?W(k,x.column(),"}"):$=="["?W(k,x.column(),"]"):$=="("?W(k,x.column(),")"):$=="case"?N.type="case":($=="}"&&N.type=="}"||$==N.type)&&L(k),k.startOfLine=!1),_},indent:function(x,k){if(x.tokenize!=P&&x.tokenize!=null)return I.Pass;var N=x.context,_=k&&k.charAt(0);if(N.type=="case"&&/^(?:case|default)\b/.test(k))return x.context.type="}",N.indented;var wt=_==N.type;return N.align?N.column+(wt?0:1):N.indented+(wt?0:xt)},electricChars:"{}):",closeBrackets:"()[]{}''\"\"``",fold:"brace",blockCommentStart:"/*",blockCommentEnd:"*/",lineComment:"//"}}),I.defineMIME("text/x-go","go")}(Fr),Yl=xu({props:{modelValue:{type:String,default:""},mode:{type:String,default:"go"},height:{type:[String,Number],default:300},options:{type:Object,default:()=>{}},theme:{type:String,default:"idea"},readOnly:{type:Boolean,default:!1}},data(){return{contentValue:this.modelValue,coder:null,opt:{theme:this.theme,styleActiveLine:!0,lineNumbers:!0,lineWrapping:!1,tabSize:4,indentUnit:4,indentWithTabs:!0,mode:this.mode,readOnly:this.readOnly,...this.options}}},computed:{_height(){return Number(this.height)?Number(this.height)+"px":this.height}},watch:{modelValue(I){this.contentValue=I,I!==this.coder.getValue()&&this.coder.setValue(I)}},mounted(){this.init()},methods:{init(){this.coder=yu(ql.fromTextArea(this.$refs.textarea,this.opt)),this.coder.on("change",I=>{this.contentValue=I.getValue(),this.$emit("update:modelValue",this.contentValue)})},formatStrInJson:I=>JSON.stringify(JSON.parse(I),null,4)}},[["render",function(I,bt,$,xt,Lt,Tt){return du(),hu("div",{class:"code-editor",style:mu({height:Tt._height})},[pu(vu("textarea",{ref:"textarea","onUpdate:modelValue":bt[0]||(bt[0]=H=>Lt.contentValue=H)},null,512),[[gu,Lt.contentValue]])],4)}],["__scopeId","data-v-b95091c5"]])});export{Cu as __tla,Yl as default};
