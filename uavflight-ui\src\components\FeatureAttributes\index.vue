<!--
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-08-05 10:00:00
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-07-30 18:56:59
 * @FilePath: \uavflight-ui\src\components\FeatureAttributes\index.vue
 * @Description: 要素属性查看组件，用于显示GeoServer发布的WMS图层要素属性
 * 
 * Copyright (c) 2025 by 吴博文, All Rights Reserved. 
-->
<template>
  <div 
    v-if="visible" 
    class="feature-attributes-panel" 
    :style="{ top: position.top + 'px', left: position.left + 'px' }"
    ref="attributePanel">
    <!-- 头部工具栏 -->
    <div class="feature-attributes-header" @mousedown="startDrag">
      <div class="feature-title">
        <el-icon><Document /></el-icon>
        <span>{{ title || '要素属性信息' }}</span>
      </div>
      <div class="feature-actions">
        <el-button type="primary" size="small" circle @click="close">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </div>
    
    <!-- 属性内容区域 -->
    <div class="feature-attributes-content">
      <el-empty v-if="!attributes || Object.keys(attributes).length === 0" description="暂无属性信息" />
      <el-scrollbar v-else height="calc(100% - 10px)">
        <el-table :data="attributesArray" stripe style="width: 100%">
          <el-table-column prop="name" label="属性名称" width="140" show-overflow-tooltip />
          <el-table-column prop="value" label="属性值" show-overflow-tooltip />
        </el-table>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { Document, Close } from '@element-plus/icons-vue';

// Props定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  attributes: {
    type: Object,
    default: () => ({})
  },
  layerId: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  initialPosition: {
    type: Object,
    default: () => ({ top: 120, left: 120 })
  }
});

// Emits定义
const emit = defineEmits(['update:visible', 'close']);

// 组件状态
const position = ref(props.initialPosition);
const isDragging = ref(false);
const dragOffset = ref({ x: 0, y: 0 });
const attributePanel = ref<HTMLElement | null>(null);

// 处理属性数据，转换为表格可用的数组形式
const attributesArray = computed(() => {
  if (!props.attributes) return [];
  
  return Object.entries(props.attributes)
    .filter(([key]) => key !== 'layerId' && key !== 'geometry' && key !== 'the_geom')
    .map(([key, value]) => ({
      name: key,
      value: value === null ? '' : String(value)
    }));
});

// 关闭面板
const close = () => {
  emit('update:visible', false);
  emit('close');
};

// 开始拖拽
const startDrag = (e: MouseEvent) => {
  if (attributePanel.value) {
    isDragging.value = true;
    dragOffset.value = {
      x: e.clientX - position.value.left,
      y: e.clientY - position.value.top
    };
    
    // 阻止默认行为和事件冒泡
    e.preventDefault();
    e.stopPropagation();
  }
};

// 拖拽过程
const onDrag = (e: MouseEvent) => {
  if (!isDragging.value) return;
  
  // 计算新位置
  position.value = {
    left: e.clientX - dragOffset.value.x,
    top: e.clientY - dragOffset.value.y
  };
  
  // 边界检查，确保不会被拖出可视区域
  if (position.value.left < 0) position.value.left = 0;
  if (position.value.top < 0) position.value.top = 0;
  
  const maxRight = window.innerWidth - (attributePanel.value?.offsetWidth || 400);
  const maxBottom = window.innerHeight - (attributePanel.value?.offsetHeight || 300);
  
  if (position.value.left > maxRight) position.value.left = maxRight;
  if (position.value.top > maxBottom) position.value.top = maxBottom;
};

// 结束拖拽
const endDrag = () => {
  isDragging.value = false;
};

// 生命周期钩子
onMounted(() => {
  // 添加全局拖拽事件监听器
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', endDrag);
});

onBeforeUnmount(() => {
  // 移除全局拖拽事件监听器
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', endDrag);
});
</script>

<style lang="scss">
/* 属性面板样式 */
.feature-attributes-panel {
  position: absolute;
  width: 400px;
  height: 300px;
  background-color: rgba(15, 21, 32, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(64, 158, 255, 0.5);
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  z-index: 999;
  overflow: hidden;
  transition: opacity 0.3s;
  
  /* 头部工具栏 */
  .feature-attributes-header {
    height: 40px;
    background-color: rgba(16, 64, 70, 0.95);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    border-bottom: 1px solid rgba(64, 158, 255, 0.5);
    cursor: move;
    user-select: none;
    
    .feature-title {
      display: flex;
      align-items: center;
      gap: 5px;
      color: white;
      font-size: 14px;
      font-weight: bold;
      
      .el-icon {
        color: #409eff;
      }
    }
    
    .feature-actions {
      .el-button {
        padding: 5px;
      }
    }
  }
  
  /* 内容区域 */
  .feature-attributes-content {
    flex: 1;
    padding: 10px;
    overflow: hidden;
    color: white;
    
    .el-table {
      background-color: transparent;
      color: white;
      
      &::before {
        display: none;
      }
      
      .el-table__header-wrapper {
        .el-table__header {
          th {
            background-color: rgba(16, 64, 70, 0.95);
            color: #fff;
            border-bottom: 1px solid rgba(64, 158, 255, 0.5);
            
            .cell {
              font-weight: bold;
            }
          }
        }
      }
      
      .el-table__body-wrapper {
        .el-table__body {
          td {
            background-color: rgba(15, 21, 32, 0.85);
            color: #fff;
            border-bottom: 1px solid rgba(64, 158, 255, 0.2);
          }
          
          tr:nth-child(even) td {
            background-color: rgba(26, 41, 61, 0.95);
          }
          
          tr:hover td {
            background-color: rgba(64, 158, 255, 0.3);
          }
        }
      }
    }
    
    .el-empty {
      color: #909399;
    }
  }
}
</style> 