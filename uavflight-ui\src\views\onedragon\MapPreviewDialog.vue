<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`图层预览: ${layerId || '未知图层'}`"
    width="80%"
    destroy-on-close
    :before-close="handleClose"
  >
    <div class="map-preview-container">
      <div v-if="loading" class="loading-overlay">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <p>正在加载图层...</p>
      </div>
      <div id="preview-map" ref="mapContainer" class="map-container"></div>
    </div>
  </el-dialog>
</template>

<script lang="ts">
export default {
  name: 'MapPreviewDialog'
}
</script>

<script lang="ts" setup>
import { ref, onMounted, defineProps, defineEmits, watch, onBeforeUnmount } from 'vue';
import { ElMessage } from 'element-plus';
import Map from 'ol/Map';
import View from 'ol/View';
import TileLayer from 'ol/layer/Tile';
import WMTS from 'ol/source/WMTS';
import WMTSTileGrid from 'ol/tilegrid/WMTS';
import { get as getProjection } from 'ol/proj';
import { getTopLeft } from 'ol/extent';
import { Loading } from '@element-plus/icons-vue';
import { fromLonLat } from 'ol/proj';
import { defaults as defaultControls, ScaleLine, ZoomSlider, FullScreen } from 'ol/control';
import { getLayerBbox } from '/@/utils/geoserver';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  layerId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible']);

const dialogVisible = ref(false);
const loading = ref(false);
const mapContainer = ref<HTMLElement | null>(null);
const map = ref<Map | null>(null);

// 监听props变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal && props.layerId) {
    // 延迟初始化地图，确保DOM已经渲染
    setTimeout(() => {
      initMap();
      loadWMTSLayer(props.layerId);
    }, 300);
  }
});

watch(() => dialogVisible.value, (newVal) => {
  emit('update:visible', newVal);
  
  if (!newVal && map.value) {
    // 清理地图
    cleanupMap();
  }
});

// 初始化地图
const initMap = () => {
  if (map.value || !mapContainer.value) return;
  
  try {
    loading.value = true;
    
    // 使用合适的中国南方坐标作为初始中心点
    const initialCenter = fromLonLat([108.3, 22.8]); // 南宁附近
    
    // 创建地图控件
    const scaleLine = new ScaleLine({
      units: 'metric',
      bar: true,
      steps: 4,
      minWidth: 140
    });
    
    const zoomSlider = new ZoomSlider();
    const fullScreen = new FullScreen();
    
    map.value = new Map({
      target: mapContainer.value,
      layers: [], // 不添加底图，只使用GeoServer图层
      view: new View({
        center: initialCenter,
        zoom: 10
      }),
      controls: defaultControls({
        zoom: true,
        rotate: false,
        attribution: false
      }).extend([
        scaleLine,
        zoomSlider,
        fullScreen
      ])
    });
    
    loading.value = false;
  } catch (error) {
    console.error('初始化地图失败:', error);
    ElMessage.error('初始化地图失败');
    loading.value = false;
  }
};

// 加载WMTS图层
const loadWMTSLayer = (layerId: string) => {
  if (!map.value || !layerId) return;
  
  try {
    loading.value = true;
    
    // 解析工作空间和图层名
    const parts = layerId.split(':');
    if (parts.length !== 2) {
      throw new Error(`图层ID格式不正确: ${layerId}`);
    }
    
    const workspace = parts[0];
    const layerName = parts[1];
    
    // 获取Geoserver基础URL
    const host = import.meta.env.VITE_GEOSERVER_HOST || 'localhost';
    const port = import.meta.env.VITE_GEOSERVER_PORT || '8080'; 
    const geoserverUrl = `http://${host}:${port}/geoserver`;
    
    console.log(`创建WMTS图层: ${layerId}, 工作区: ${workspace}, 图层名: ${layerName}`);
    console.log(`WMTS服务端点: ${geoserverUrl}/gwc/service/wmts`);
    
    // 设置默认值
    const tileMatrixSet = 'EPSG:4326';
    const format = 'image/png';
    const style = '';
    
    // 获取投影
    const projection = getProjection(tileMatrixSet);
    if (!projection) {
      throw new Error(`无法获取投影系统: ${tileMatrixSet}`);
    }
    
    // 获取完整的WMTS服务URL (KVP格式)
    const wmtsEndpoint = `${geoserverUrl}/gwc/service/wmts`;
    
    // 创建WMTS源，使用KVP编码方式，这里需要参考hdMapLayer的实现
    const source = new WMTS({
      url: wmtsEndpoint,
      layer: layerId, // 直接使用完整的图层ID，如"testodm:20250705171602"
      matrixSet: tileMatrixSet,
      format: format,
      projection: projection,
      style: style,
      requestEncoding: 'KVP',
      tileGrid: createWmtsTileGrid(projection, tileMatrixSet),
      wrapX: true,
      transition: 0,
      crossOrigin: 'anonymous' // 添加跨域支持
    });
    
    // 添加事件处理
    source.on('tileloaderror', (event) => {
      console.warn(`WMTS图层 ${layerId} 加载失败:`, event);
      // 记录详细的错误信息
      const urls = source.getUrls();
      if (urls && urls.length > 0) {
        console.error('请求URL示例:', urls[0]);
      }
    });
    
    source.on('tileloadend', () => {
      console.log(`WMTS图层 ${layerId} 部分加载成功`);
    });
    
    // 创建并添加图层，将其置于OSM底图之上
    const tileLayer = new TileLayer({
      source: source,
      visible: true,
      opacity: 1,
      zIndex: 1 // 由于没有底图，设置为1即可
    });
    
    // 添加到地图
    map.value.addLayer(tileLayer);
    
    // 调用API获取图层范围并缩放
    fetchLayerExtentAndZoom(workspace, layerName);
    
  } catch (error) {
    console.error('加载图层失败:', error);
    ElMessage.error('加载图层失败');
    loading.value = false;
  }
};

// 获取图层范围并缩放到合适的位置
const fetchLayerExtentAndZoom = async (workspace: string, layerName: string) => {
  try {
    console.log(`正在从API获取图层 ${workspace}:${layerName} 的边界框...`);
    const bboxData = await getLayerBbox(workspace, layerName);
    
    // 检查返回数据格式
    if (bboxData && bboxData.status === 'success' && bboxData.bbox && bboxData.bbox.latLon) {
      const { minx, miny, maxx, maxy } = bboxData.bbox.latLon;
      
      // 检查获取的边界框是否有效
      if (minx === -180 && miny === -90 && maxx === 180 && maxy === 90) {
        console.warn(`API返回全球范围作为图层 ${workspace}:${layerName} 的边界框，尝试使用备用方法`);
        
        // 如果是全球范围，尝试使用native属性
        if (bboxData.bbox.native && 
            !(bboxData.bbox.native.minx === -180 && 
              bboxData.bbox.native.miny === -90 && 
              bboxData.bbox.native.maxx === 180 && 
              bboxData.bbox.native.maxy === 90)) {
          // 使用native属性
          const nativeBox = bboxData.bbox.native;
          console.log(`使用native边界框：`, nativeBox);
          
          // 转换为OpenLayers可用的范围
          const bottomLeft = fromLonLat([nativeBox.minx, nativeBox.miny]);
          const topRight = fromLonLat([nativeBox.maxx, nativeBox.maxy]);
          
          // 构建extent数组
          const extent = [bottomLeft[0], bottomLeft[1], topRight[0], topRight[1]];
          
          console.log(`成功从API native属性获取并转换图层边界框:`, extent);
          fitToExtent(extent);
          return;
        } else {
          // 如果都是全球范围，使用默认范围
          fitToDefaultExtent();
        }
      } else {
        // 如果不是全球范围，则进行正常转换
        console.log(`API返回的有效边界框: minx=${minx}, miny=${miny}, maxx=${maxx}, maxy=${maxy}`);
        
        // 转换为OpenLayers可用的范围
        const bottomLeft = fromLonLat([minx, miny]);
        const topRight = fromLonLat([maxx, maxy]);
        
        // 构建extent数组
        const extent = [bottomLeft[0], bottomLeft[1], topRight[0], topRight[1]];
        
        // 检查转换后的坐标是否有效
        if (extent.some(val => !isFinite(val))) {
          console.warn(`转换后的坐标包含无效值:`, extent);
          fitToDefaultExtent();
        } else {
          console.log(`成功从API获取并转换图层边界框:`, extent);
          fitToExtent(extent);
        }
      }
    } else {
      console.warn('API返回的边界框数据格式不正确:', bboxData);
      fitToDefaultExtent();
    }
  } catch (error) {
    console.error(`获取图层边界框失败:`, error);
    fitToDefaultExtent();
  } finally {
    loading.value = false;
  }
};

// 缩放到指定范围
const fitToExtent = (extent: number[]): void => {
  if (!map.value) return;
  
  try {
    map.value.getView().fit(extent, {
      padding: [50, 50, 50, 50],
      maxZoom: 18
    });
    console.log(`已缩放至范围: ${extent}`);
  } catch (error) {
    console.error(`缩放至范围失败: ${error}`);
    fitToDefaultExtent();
  }
};

// 缩放到默认范围（南宁区域的大致范围）
const fitToDefaultExtent = (): void => {
  if (!map.value) return;
  
  try {
    // 南宁市区范围
    const extent = fromLonLat([108.2, 22.7]).concat(fromLonLat([108.5, 23.0]));
    map.value.getView().fit(extent, {
      padding: [50, 50, 50, 50],
      maxZoom: 15
    });
    console.log(`已缩放至默认范围`);
  } catch (error) {
    console.error(`缩放至默认范围失败: ${error}`);
  }
};

// 创建WMTS图层的瓦片网格，参考mapLayerManager.ts中的实现
const createWmtsTileGrid = (projection: any, gridSetId: string): WMTSTileGrid => {
  const projectionExtent = projection.getExtent();
  
  // 根据不同的坐标系创建合适的参数
  let origin, resolutions, matrixIds;
  
  if (gridSetId === 'EPSG:4326') {
    // EPSG:4326 特殊处理
    origin = [-180, 90]; // 正确的原点
    
    // 标准的EPSG:4326分辨率
    resolutions = [
    0.703125,              // 层级 0
    0.3515625,             // 层级 1
    0.17578125,            // 层级 2
    0.087890625,           // 层级 3
    0.0439453125,          // 层级 4
    0.02197265625,         // 层级 5
    0.010986328125,        // 层级 6
    0.0054931640625,       // 层级 7
    0.00274658203125,      // 层级 8
    0.001373291015625,     // 层级 9
    0.0006866455078125,    // 层级 10
    0.0003433227539062,    // 层级 11
    0.0001716613769531,    // 层级 12
    0.0000858306884766,    // 层级 13
    0.0000429153442383,    // 层级 14
    0.0000214576721191,    // 层级 15
    0.0000107288360596,    // 层级 16
    0.0000053644180298,    // 层级 17
    0.0000026822090149,    // 层级 18
    0.0000013411045074,    // 层级 19
    0.0000006705522537,    // 层级 20
    0.0000003352761269,    // 层级 21
    0.00000016763806345,   // 层级 22
    0.00000008381903173,   // 层级 23
    0.00000004190951586,   // 层级 24
    0.00000002095475793    // 层级 25
];

    
    // 标准的EPSG:4326 GeoServer矩阵ID
    matrixIds = [];
    for (let i = 0; i < resolutions.length; i++) {
      matrixIds.push(`${gridSetId}:${i}`);
    }
  } else {
    // 默认情况下，使用自动计算的值
    origin = getTopLeft(projectionExtent);
    const size = Math.max(
      projectionExtent[2] - projectionExtent[0],
      projectionExtent[3] - projectionExtent[1]
    );
    const maxResolution = size / 256;
    
    resolutions = [];
    matrixIds = [];
    for (let i = 0; i < 20; i++) {
      resolutions.push(maxResolution / Math.pow(2, i));
      // 使用标准GeoServer格式矩阵ID
      matrixIds.push(`${gridSetId}:${i}`);
    }
  }
  
  return new WMTSTileGrid({
    origin: origin,
    resolutions: resolutions,
    matrixIds: matrixIds
  });
};

// 清理地图
const cleanupMap = () => {
  if (map.value) {
    map.value.setTarget(undefined);
    map.value = null;
  }
};

// 对话框关闭前的处理
const handleClose = () => {
  cleanupMap();
  dialogVisible.value = false;
};

// 组件销毁前清理
onBeforeUnmount(() => {
  cleanupMap();
});
</script>

<style lang="scss" scoped>
.map-preview-container {
  position: relative;
  width: 100%;
  height: 600px;
  
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
    
    .loading-icon {
      font-size: 24px;
      color: #409EFF;
      animation: rotate 2s linear infinite;
    }
    
    p {
      margin-top: 10px;
      font-size: 14px;
      color: #606266;
    }
  }
  
  .map-container {
    width: 100%;
    height: 100%;
    background-color: #f0f0f0; /* 更浅的背景色，适合只显示图层 */
  }
}
</style> 