<template>
	<div class="layout-padding">
		<splitpanes>
			<pane size="20" style="margin-left: 10px; margin-right: 15px; overflow: hidden">
				<el-card style="height: 90vh; overflow: auto">
					<div>
						<div class="query-condition-container">
							<div class="title-container">
								<div class="title-before">&nbsp;</div>
								<span class="title">查询条件</span>
							</div>
							<div class="form-item">
								<label>行政区数据权限过滤</label>
								<el-tree-select
									:data="auditRegionData"
									:props="{ value: 'id', label: 'name', children: 'children' }"
									check-strictly
									clearable
									style="width: 100%"
									placeholder="请选择行政区"
									v-model="queryForm.cityCode"
								/>
							</div>
							<div class="form-item">
								<label>业务场景</label>
								<el-select v-model="queryForm.businessType" @change="changeBusinessType" placeholder="请选择业务类型" style="width: 100%" clearable>
									<el-option
										v-for="item in businessTypeData"
										:key="item.businessTypeId"
										:label="item.businessTypeName"
										:value="item.businessTypeId"
									/>
								</el-select>
							</div>
							<div class="form-item">
								<label>事件</label>
								<el-select v-model="queryForm.businessEvent" placeholder="请选择事件类型">
									<el-option v-for="item in businessEventData" :key="item.type" :label="item.name" :value="item.type" />
								</el-select>
							</div>
							<div class="button-container">
								<el-button type="default">重置</el-button>
								<el-button type="success" style="background-color: #2a7729">查询</el-button>
							</div>
						</div>
					</div>
				</el-card>
			</pane>
			<pane class="ml8">
				<el-card style="height: 90vh">
					<div class="top-title">巡检报告</div>
					<div class="detail-card">
						<div class="detail-container">
							<h4>航线执飞情况</h4>
							<div style="padding: 5px">
								<el-table :data="tableData" border show-summary :summary-method="getSummaries" :span-method="objectSpanMethod" style="width: 100%">
									<el-table-column align="center" prop="region" label="区域" width="180" />
									<el-table-column align="center" prop="regioTown" label="街道" width="180" />
									<el-table-column align="center" prop="businessType" label="场景" />
									<el-table-column align="center" prop="eventCount" label="事件数" />
									<el-table-column align="center" prop="effectiveEventCount" label="有效事件数" />
								</el-table>
							</div>
						</div>
						<div class="detail-container">
							<h4>场景事件数量统计图</h4>
							<div class="echarts-div">
								<NestedPieChart :showLegend="false" :inner-data="innerData" :outer-data="outerData" />
							</div>
						</div>
						<div class="detail-container">
							<h4>社区事件数量TOP10</h4>
							<div class="echarts-div">
								<StackBarChart
									title=""
									:x-axis-data="['1月', '2月', '3月', '4月', '5月', '6月']"
									:series-data="[
										{ name: '电商', data: [120, 132, 101, 134, 90, 230] },
										{ name: '门店', data: [220, 182, 191, 234, 290, 330] },
										{ name: '批发', data: [150, 232, 201, 154, 190, 130] },
									]"
								/>
							</div>
						</div>
						<div class="detail-container event-detail">
							<h4>事件明细</h4>

							<div v-for="(item, index) in tableEventData" :key="index">
								<div style="margin-bottom: 10px">
									<table>
										<tr>
											<td rowspan="4">1</td>
											<th>事件</th>
											<td>{{ item.aaaa }}</td>
											<th>街道</th>
											<td>
												{{ item.bbb }}
												<el-icon style="float: right; cursor: pointer" @click="handleEventIcon(22323)" size="20" color="blue">
													<Postcard />
												</el-icon>
											</td>
											<!-- <th rowspan="2" class="icon-cell"><img src="calendar-icon-link" alt="calendar" class="calendar-icon" /></th> -->
											<!-- 请将calendar-icon-link替换为实际图标链接 -->
										</tr>
										<tr>
											<th>社区</th>
											<td>洛阳社区居委会</td>
											<th>网格</th>
											<td>洛阳社区居委会</td>
										</tr>
										<tr>
											<th>时间</th>
											<td colspan="1">2025-04-12 15:07:51</td>
											<td></td>
											<td></td>
										</tr>
										<tr>
											<th colspan="4">
												<img src="http://127.0.0.1:81/1744677600380.jpg" />
											</th>
										</tr>
									</table>
								</div>
							</div>

							<!-- <el-pagination
								background
								layout="prev, pager, next"
								:total="1000"
								style="float: left"
								@current-change="currentChangeHandle"
								@size-change="sizeChangeHandle"
							/> -->
							<div>
								<el-pagination
									v-model:current-page="currentPage4"
									v-model:page-size="pageSize4"
									:page-sizes="[10, 20, 30, 40]"
									:small="small"
									:disabled="disabled"
									:background="background"
									layout="total, sizes, prev, pager, next, jumper"
									:total="400"
									@size-change="handleSizeChange"
									@current-change="handleCurrentChange"
								/>
							</div>
							<div style="height: 50px"></div>
						</div>
					</div>
				</el-card>
			</pane>
		</splitpanes>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { TableColumnCtx, TableSummaryMethod } from 'element-plus';
import { auditRegionTree } from '/@/api/manage/auditDepartment';
import { getInspectReportData, getBusinessTypeAndEventCountData } from '/@/api/manage/inspectionReport';
import { getBusinessTypeList, getBusinessEventListByType } from '/@/api/manage/businessType';
import NestedPieChart from '/@/components/Echarts/NestedPieChart/index.vue';
import StackBarChart from '/@/components/Echarts/StackBarChart/index.vue';
// import * as echarts from 'echarts';

const pagination = ref(1);

const currentChangeHandle = () => {};

const sizeChangeHandle = () => {};
const tableEventData = ref([
	{
		aaaa: '11',
		bbb: '22',
	},
	{
		aaaa: '11',
		bbb: '22',
	},
	{
		aaaa: '11',
		bbb: '22',
	},
]);

// 点击事件表格图标
const handleEventIcon = (val: any) => {
	console.log(val);
};

// 加载事件数量饼状图
const loadEventCountChart = async () => {};

const queryForm = ref({
	cityCode: '',
	businessType: '',
	businessEvent: '',
});

const businessTypeData = ref<any[]>([]);
// 初始化业务场景数据
const getBusinessTypeDataFun = () => {
	getBusinessTypeList().then((res) => {
		businessTypeData.value = res.data;
	});
};

// 业务事件
const businessEventData = ref<any[]>([]);
const changeBusinessType = async (val) => {
	state.queryForm.businessEvent = '';
	const res = await getBusinessEventListByType({ businessType: val });
	businessEventData.value = res.data;
};

const auditRegionData = ref<any[]>([]);
// 初始化行政区数据
const getAuditRegionDataFun = () => {
	// 获取行政区数据
	auditRegionTree().then((res) => {
		auditRegionData.value = res.data;
	});
};

const loadData = async () => {
	try {
		getAuditRegionDataFun();
		getBusinessTypeDataFun();
		loadEventCountChart();
		getInspectReportDataFun();
		getBusinessTypeAndEventCountFun();
	} catch (error) {
		console.error('数据加载失败:', error);
	}
};

onMounted(() => {
	loadData();
});

interface TableData {
	region: string;
	regioTown: string;
	businessType: string;
	eventCount: number;
	effectiveEventCount: number;
}

const innerData = ref([]);

const outerData = ref([]);

const getBusinessTypeAndEventCountFun = async () => {
	const res = await getBusinessTypeAndEventCountData({});
	innerData.value = res.data.businessTypeCount;
	outerData.value = res.data.eventCount;
};

const getInspectReportDataFun = async () => {
	const res = await getInspectReportData({});
	tableData.value = res.data;
};

// 示例数据
const tableData = ref<TableItem[]>([]);

// 合并逻辑（确保不处理汇总行）
const objectSpanMethod = ({ rowIndex, columnIndex }: { rowIndex: number; columnIndex: number }) => {
	// 关键点：跳过汇总行
	if (rowIndex === tableData.value.length) return [1, 1];

	// 区域列合并逻辑
	if (columnIndex === 0) {
		if (rowIndex === 0 || tableData.value[rowIndex].region !== tableData.value[rowIndex - 1].region) {
			let count = 1;
			for (let i = rowIndex + 1; i < tableData.value.length; i++) {
				if (tableData.value[i].region === tableData.value[rowIndex].region) count++;
			}
			return [count, 1];
		}
		return [0, 0];
	}

	// 街道列合并逻辑
	if (columnIndex === 1) {
		if (rowIndex === 0 || tableData.value[rowIndex].regioTown !== tableData.value[rowIndex - 1].regioTown) {
			let count = 1;
			for (let i = rowIndex + 1; i < tableData.value.length; i++) {
				if (tableData.value[i].regioTown === tableData.value[rowIndex].regioTown) count++;
			}
			return [count, 1];
		}
		return [0, 0];
	}

	return [1, 1];
};

// 关键修改点：生成5列的汇总数据
const getSummaries: TableSummaryMethod<TableData> = ({ columns }) => {
	const sums = columns.map((column, index) => {
		// 第一列显示"总计"
		if (index === 0) return '总计';
		// 保留场景列显示"-"
		// if (column.property === 'businessType') return '-';
		// 数值列计算总和
		if (['eventCount', 'effectiveEventCount'].includes(column.property)) {
			const total = tableData.value.reduce((sum, row) => {
				if (row.businessType !== '合计') {
					return sum + parseInt(row[column.property as keyof TableData]);
				} else {
					return sum;
				}
			}, 0);
			return `${total}`;
		}

		return '-'; // 其他非数值列留空
	});
	return sums; // 返回包含5个元素的数组
};
</script>

<style scoped>
.top-title {
	text-align: center;
	font-size: 14px;
}

.detail-card {
	height: 90vh;
	overflow-y: auto;
	scrollbar-width: none; /* 可选：auto | thin | none */
}

.event-detail {
	height: auto;
}

.detail-container {
	margin-bottom: 10px;
}
.detail-container h4 {
	margin-bottom: 10px;
	position: relative;
	padding-left: 15px;
	font-size: 15px;
}

.detail-container h4::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 5px;
	height: 15px;
	background-color: #2a7729; /* 标题前竖线颜色 */
}

.detail-container h4 {
	margin-bottom: 10px;
	position: relative;
	padding-left: 15px;
	font-size: 15px;
}

.query-condition-container {
	background-color: #ffffff;
	padding: 0px;
	/* border-radius: 5px;
	box-shadow: 0 0 5px rgba(0, 0, 0, 0.1); */
}
.title-container {
	display: flex;
	align-items: center;
	margin-bottom: 20px;
}
.title-before {
	height: 20px;
	width: 6px;
	background: green;
	margin-right: 8px;
}
.title {
	font-size: 18px;
	font-weight: bold;
	margin-left: 0px;
}
.form-item {
	margin-bottom: 20px;
}
.form-item label {
	display: block;
	margin-bottom: 5px;
}

.button-container {
	display: flex;
	justify-content: flex-end;
	margin-top: 20px;
}

.echarts-div {
	height: 400px;
	width: 100%;
	margin: 0 auto;
	margin-bottom: 50px;
	margin-top: 50px;
	margin-left: 200px;
}
table {
	width: 100%;
	border-collapse: collapse;
}
th,
td {
	border: 1px solid black;
	padding: 8px;
	text-align: center;
}
th {
}
</style>
