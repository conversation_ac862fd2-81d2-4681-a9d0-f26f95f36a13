{"version": 3, "sources": ["../../ol/geom/flat/length.js"], "sourcesContent": ["/**\n * @module ol/geom/flat/length\n */\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @return {number} Length.\n */\nexport function lineStringLength(flatCoordinates, offset, end, stride) {\n  let x1 = flatCoordinates[offset];\n  let y1 = flatCoordinates[offset + 1];\n  let length = 0;\n  for (let i = offset + stride; i < end; i += stride) {\n    const x2 = flatCoordinates[i];\n    const y2 = flatCoordinates[i + 1];\n    length += Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));\n    x1 = x2;\n    y1 = y2;\n  }\n  return length;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @return {number} Perimeter.\n */\nexport function linearRingLength(flatCoordinates, offset, end, stride) {\n  let perimeter = lineStringLength(flatCoordinates, offset, end, stride);\n  const dx = flatCoordinates[end - stride] - flatCoordinates[offset];\n  const dy = flatCoordinates[end - stride + 1] - flatCoordinates[offset + 1];\n  perimeter += Math.sqrt(dx * dx + dy * dy);\n  return perimeter;\n}\n"], "mappings": ";AAWO,SAAS,iBAAiB,iBAAiB,QAAQ,KAAK,QAAQ;AACrE,MAAI,KAAK,gBAAgB,MAAM;AAC/B,MAAI,KAAK,gBAAgB,SAAS,CAAC;AACnC,MAAI,SAAS;AACb,WAAS,IAAI,SAAS,QAAQ,IAAI,KAAK,KAAK,QAAQ;AAClD,UAAM,KAAK,gBAAgB,CAAC;AAC5B,UAAM,KAAK,gBAAgB,IAAI,CAAC;AAChC,cAAU,KAAK,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,GAAG;AACjE,SAAK;AACL,SAAK;AAAA,EACP;AACA,SAAO;AACT;", "names": []}