<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'任务详情: ' + currentTaskId"
    width="60%"
    destroy-on-close
  >
    <div class="task-detail">
      <div class="select-container">
        <span class="label">选择任务ID:</span>
        <el-select v-model="selectedTaskId" placeholder="请选择任务ID" @change="handleTaskChange">
          <el-option
            v-for="taskId in formattedTaskList"
            :key="taskId.value"
            :label="taskId.label"
            :value="taskId.value"
          />
        </el-select>
      </div>
      
      <div class="content-box">
        <div class="content-header">
          <span>任务日志:</span>
          <el-button type="primary" size="small" :loading="loading" @click="refreshTaskInfo">
            刷新
          </el-button>
        </div>
        <div class="content-body">
          <div v-if="loading" class="loading-content">
            <el-skeleton :rows="10" animated />
          </div>
          <div v-else-if="!taskInfo" class="empty-content">
            暂无任务详细信息，请点击刷新按钮获取
          </div>
          <pre v-else class="task-info-content">{{ taskInfo }}</pre>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts">
export default {
  name: 'TaskDetailDialog'
}
</script>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, watch, computed } from 'vue';
import axios from 'axios';
import { ElMessage } from 'element-plus';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  taskId: {
    type: String,
    default: ''
  },
  taskList: {
    type: Array as () => string[],
    default: () => []
  },
  processName: {
    type: String,
    default: ''
  },
  processType: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible', 'refresh']);

// 响应式状态
const dialogVisible = ref(false);
const currentTaskId = ref('');
const selectedTaskId = ref('');
const taskInfo = ref('');
const loading = ref(false);

// 格式化任务列表，为当前执行的任务添加标识
const formattedTaskList = computed(() => {
  if (!props.taskList || !props.taskId) return [];
  
  // 确保当前执行的任务ID排在第一位
  const orderedList = [...props.taskList];
  const currentIndex = orderedList.findIndex(id => id === props.taskId);
  
  if (currentIndex > 0) {
    // 如果当前任务不在第一位，将其移到第一位
    const currentTask = orderedList.splice(currentIndex, 1)[0];
    orderedList.unshift(currentTask);
  }
  
  // 为每个任务ID创建带标签的选项
  return orderedList.map(id => ({
    value: id,
    label: id === props.taskId ? `${id} (当前执行)` : id
  }));
});

// 监听props变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal && props.taskId) {
    currentTaskId.value = props.taskId;
    selectedTaskId.value = props.taskId; // 始终选择当前执行的任务ID
    // 打开弹窗时自动获取信息
    refreshTaskInfo();
  }
});

watch(() => dialogVisible.value, (newVal) => {
  emit('update:visible', newVal);
});

// 切换任务
const handleTaskChange = () => {
  currentTaskId.value = selectedTaskId.value;
  refreshTaskInfo();
};

// 获取对应的日志类型
const getLogType = (processType: string): string => {
  const logTypeMap: Record<string, string> = {
    'odm_process': 'batlog',
    'geoserver_publish': 'geolog',
    'tif_process': 'tiflog',
    'map_config': 'configlog' // 假设map_config对应的日志类型
  };
  
  return logTypeMap[processType] || 'batlog'; // 默认使用batlog
};

// 获取API地址
const getApiUrl = () => {
  const host = import.meta.env.VITE_GEOSERVER_HOST || 'localhost';
  const port = import.meta.env.VITE_GEOSERVER_HD_PORT || '8080';
  return `http://${host}:${port}/api/map/logs/`;
};

// 刷新任务信息
const refreshTaskInfo = async () => {
  if (!selectedTaskId.value) return;
  
  loading.value = true;
  taskInfo.value = '';
  
  try {
    const logType = getLogType(props.processType);
    const logId = selectedTaskId.value;
    
    // 调用API获取日志信息
    const response = await axios.get(getApiUrl(), {
      params: {
        log_type: logType,
        log_id: logId
      }
    });
    
    if (response.data && response.data.status === 'success') {
      // 显示日志内容
      taskInfo.value = response.data.content || '暂无日志信息';
    } else {
      throw new Error(response.data?.message || '获取日志失败');
    }
  } catch (error) {
    console.error('获取任务信息失败:', error);
    taskInfo.value = '获取任务信息失败，请重试';
    ElMessage.error('获取日志信息失败');
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.task-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .select-container {
    display: flex;
    align-items: center;
    gap: 10px;

    .label {
      font-weight: bold;
      min-width: 100px;
    }
  }

  .content-box {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    
    .content-header {
      padding: 10px 15px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
    }
    
    .content-body {
      padding: 15px;
      min-height: 300px;
      max-height: 500px;
      overflow-y: auto;
      
      .loading-content, .empty-content {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #909399;
      }
      
      .task-info-content {
        margin: 0;
        white-space: pre-wrap;
        font-family: monospace;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
}
</style> 