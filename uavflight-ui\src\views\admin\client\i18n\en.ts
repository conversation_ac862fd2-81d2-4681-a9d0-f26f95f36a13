export default {
	client: {
		index: '#',
		importsysOauthClientDetailsTip: 'import SysOauthClientDetails',
		id: 'id',
		clientId: 'client id',
		resourceIds: 'resourceIds',
		clientSecret: 'client secret',
		scope: 'scope',
		authorizedGrantTypes: 'authorizedGrantTypes',
		webServerRedirectUri: 'webServerRedirectUri',
		authorities: 'authorities',
		accessTokenValidity: 'accessTokenValidity',
		refreshTokenValidity: 'refreshTokenValidity',
		additionalInformation: 'additionalInformation',
		autoapprove: 'autoapprove',
		delFlag: 'delFlag',
		createBy: 'createBy',
		updateBy: 'updateBy',
		createTime: 'createTime',
		updateTime: 'updateTime',
		tenantId: 'tenantId',
		captchaFlag: 'captchaFlag',
		encFlag: 'encFlag',
		onlineQuantity: 'onlineQuantity',
		inputIdTip: 'input id',
		inputClientIdTip: 'input clientId',
		inputResourceIdsTip: 'input resourceIds',
		inputClientSecretTip: 'input clientSecret',
		inputScopeTip: 'input scope',
		inputAuthorizedGrantTypesTip: 'input authorizedGrantTypes',
		inputWebServerRedirectUriTip: 'input webServerRedirectUri',
		inputAuthoritiesTip: 'input authorities',
		inputAccessTokenValidityTip: 'input accessTokenValidity',
		inputRefreshTokenValidityTip: 'input refreshTokenValidity',
		inputAdditionalInformationTip: 'input additionalInformation',
		inputAutoapproveTip: 'input autoapprove',
		inputDelFlagTip: 'input delFlag',
		inputCreateByTip: 'input createBy',
		inputUpdateByTip: 'input updateBy',
		inputCreateTimeTip: 'input createTime',
		inputUpdateTimeTip: 'input updateTime',
		inputTenantIdTip: 'input tenantId',
	},
};
