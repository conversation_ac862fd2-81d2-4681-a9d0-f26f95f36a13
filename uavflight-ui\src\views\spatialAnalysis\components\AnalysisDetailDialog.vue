<template>
  <el-dialog
    v-model="visible"
    title="分析详情"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="task" class="detail-content">
      <!-- 基本信息 -->
      <div class="basic-info">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="任务ID">{{ task.id }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ formatDateTime(task.endTime) }}</el-descriptions-item>
          <el-descriptions-item label="图层名称">{{ task.layer_name }}</el-descriptions-item>
          <el-descriptions-item label="主题">{{ task.theme }}</el-descriptions-item>
          <el-descriptions-item label="分析数">{{ task.analysisCount }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag type="success">已完成</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 左右布局区域 -->
      <div class="detail-layout">
        <!-- 左侧：分析任务 -->
        <div class="left-section">
          <div class="analysis-tasks">
            <div class="task-header">
              <h4>分析任务</h4>
              <el-button type="primary" size="small" :icon="Plus" @click="addAnalysisTask">
                添加任务
              </el-button>
            </div>

            <div class="task-list">
              <el-empty v-if="analysisTasks.length === 0" description="暂无分析任务">
                <template #image>
                  <el-icon size="60" color="#c0c4cc">
                    <List />
                  </el-icon>
                </template>
                <template #description>
                  <p>暂无分析任务</p>
                  <p>点击上方按钮添加新任务</p>
                </template>
              </el-empty>

              <div v-else class="task-items">
                <div
                  v-for="(analysisTask, index) in analysisTasks"
                  :key="index"
                  class="task-item"
                >
                  <div class="task-info">
                    <div class="task-name">{{ analysisTask.name }}</div>
                    <div class="task-type">{{ analysisTask.type }}</div>
                    <div class="task-status">
                      <el-tag :type="getTaskStatusType(analysisTask.status)">
                        {{ analysisTask.status }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="task-actions">
                    <el-button size="small" type="primary" @click="viewTaskDetail(analysisTask)">
                      查看
                    </el-button>
                    <el-button size="small" type="danger" @click="removeTask(index)">
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧：地图展示 -->
        <div class="right-section">
          <div class="map-display">
            <h4>地图展示</h4>
            <div class="map-container">
              <div v-if="mapLoading" class="loading-overlay">
                <el-icon class="loading-icon"><Loading /></el-icon>
                <p>正在加载图层...</p>
              </div>
              <div :id="`analysis-map-${Date.now()}`" ref="mapContainer" class="map-content"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport" disabled>导出报告</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, List, Loading } from '@element-plus/icons-vue';
import Map from 'ol/Map';
import View from 'ol/View';
import TileLayer from 'ol/layer/Tile';
import WMTS from 'ol/source/WMTS';
import WMTSTileGrid from 'ol/tilegrid/WMTS';
import { get as getProjection } from 'ol/proj';
import { getTopLeft } from 'ol/extent';
import { fromLonLat } from 'ol/proj';
import { defaults as defaultControls, ScaleLine, ZoomSlider, FullScreen } from 'ol/control';
import { getLayerBbox } from '/@/utils/geoserver';

// 接口定义
interface BBox {
  minx: number;
  miny: number;
  maxx: number;
  maxy: number;
  crs: string;
}

interface ProcessedTask {
  id: string;
  endTime: string;
  bbox: BBox;
  analysisCount: number;
  layer_name: string;
  theme: string;
}

interface AnalysisTask {
  name: string;
  type: string;
  status: string;
  createTime: string;
  description?: string;
}

// Props
interface Props {
  modelValue: boolean;
  task: ProcessedTask | null;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  task: null
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

// 响应式数据
const visible = ref(props.modelValue);
const mapLoading = ref(false);
const mapContainer = ref<HTMLElement | null>(null);
const map = ref<Map | null>(null);
const analysisTasks = ref<AnalysisTask[]>([
  // 示例数据
  {
    name: '植被覆盖分析',
    type: '遥感分析',
    status: '已完成',
    createTime: '2025-01-15 10:30:00',
    description: '分析区域内植被覆盖情况'
  },
  {
    name: '土地利用分类',
    type: '分类分析',
    status: '进行中',
    createTime: '2025-01-15 14:20:00',
    description: '对区域进行土地利用类型分类'
  }
]);

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal && props.task) {
    console.log('弹窗打开，准备初始化地图，任务信息:', props.task);
    // 延迟初始化地图，确保DOM已经渲染
    setTimeout(() => {
      console.log('开始初始化地图和加载图层');
      initMap();
      loadWMTSLayer();
    }, 300);
  }
});

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal);

  if (!newVal && map.value) {
    // 清理地图
    cleanupMap();
  }
});

// 地图相关方法
const initMap = () => {
  console.log('initMap 被调用');
  console.log('map.value:', map.value);
  console.log('mapContainer.value:', mapContainer.value);

  if (map.value) {
    console.log('地图已存在，跳过初始化');
    return;
  }

  if (!mapContainer.value) {
    console.error('地图容器不存在');
    return;
  }

  try {
    mapLoading.value = true;
    console.log('开始初始化地图...');

    // 使用合适的中国南方坐标作为初始中心点
    const initialCenter = fromLonLat([108.3, 22.8]); // 南宁附近
    console.log('初始中心点:', initialCenter);

    // 创建地图控件
    const scaleLine = new ScaleLine({
      units: 'metric',
      bar: true,
      steps: 4,
      minWidth: 140
    });

    const zoomSlider = new ZoomSlider();
    const fullScreen = new FullScreen();

    console.log('创建地图实例...');
    map.value = new Map({
      target: mapContainer.value,
      layers: [], // 不添加底图，只使用GeoServer图层
      view: new View({
        center: initialCenter,
        zoom: 10
      }),
      controls: defaultControls({
        zoom: true,
        rotate: false,
        attribution: false
      }).extend([
        scaleLine,
        zoomSlider,
        fullScreen
      ])
    });

    mapLoading.value = false;
    console.log('地图初始化成功，地图实例:', map.value);
  } catch (error) {
    console.error('地图初始化失败:', error);
    ElMessage.error('地图初始化失败');
    mapLoading.value = false;
  }
};

const loadWMTSLayer = () => {
  if (!map.value || !props.task?.layer_name) return;

  try {
    mapLoading.value = true;

    // 解析工作空间和图层名
    const parts = props.task.layer_name.split(':');
    if (parts.length !== 2) {
      throw new Error(`图层ID格式不正确: ${props.task.layer_name}`);
    }

    const workspace = parts[0];
    const layerName = parts[1];

    // 获取Geoserver基础URL - 使用正确的环境变量
    const host = import.meta.env.VITE_GEOSERVER_HOST || import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const port = import.meta.env.VITE_GEOSERVER_PORT || import.meta.env.VITE_MAP_SERVER_PORT_GEOSERVER || '8083';
    const geoserverUrl = `http://${host}:${port}/geoserver`;

    console.log(`创建WMTS图层: ${props.task.layer_name}, 工作区: ${workspace}, 图层名: ${layerName}`);
    console.log(`WMTS服务端点: ${geoserverUrl}/gwc/service/wmts`);

    // 设置默认值
    const tileMatrixSet = 'EPSG:4326';
    const format = 'image/png';
    const style = '';

    // 获取投影
    const projection = getProjection(tileMatrixSet);
    if (!projection) {
      throw new Error(`无法获取投影系统: ${tileMatrixSet}`);
    }

    // 获取完整的WMTS服务URL (KVP格式)
    const wmtsEndpoint = `${geoserverUrl}/gwc/service/wmts`;

    // 创建WMTS源，使用KVP编码方式
    const source = new WMTS({
      url: wmtsEndpoint,
      layer: props.task.layer_name, // 直接使用完整的图层ID
      matrixSet: tileMatrixSet,
      format: format,
      projection: projection,
      style: style,
      requestEncoding: 'KVP',
      tileGrid: createWmtsTileGrid(projection, tileMatrixSet),
      wrapX: true,
      transition: 0,
      crossOrigin: 'anonymous' // 添加跨域支持
    });

    // 添加事件处理
    source.on('tileloaderror', (event) => {
      console.warn(`WMTS图层 ${props.task.layer_name} 加载失败:`, event);
      const urls = source.getUrls();
      if (urls && urls.length > 0) {
        console.error('请求URL示例:', urls[0]);
      }
    });

    source.on('tileloadend', () => {
      console.log(`WMTS图层 ${props.task.layer_name} 部分加载成功`);
    });

    // 创建并添加图层
    const tileLayer = new TileLayer({
      source: source,
      visible: true,
      opacity: 1,
      zIndex: 1
    });

    // 添加到地图
    map.value.addLayer(tileLayer);

    // 调用API获取图层范围并缩放
    fetchLayerExtentAndZoom(workspace, layerName);

  } catch (error) {
    console.error('加载图层失败:', error);
    ElMessage.error('加载图层失败');
    mapLoading.value = false;
  }
};

// 创建WMTS瓦片网格
const createWmtsTileGrid = (projection: any, tileMatrixSet: string) => {
  const projectionExtent = projection.getExtent();
  const size = Math.max(
    projectionExtent[2] - projectionExtent[0],
    projectionExtent[3] - projectionExtent[1]
  );
  const resolutions = new Array(19);
  const matrixIds = new Array(19);

  for (let z = 0; z < 19; ++z) {
    resolutions[z] = size / Math.pow(2, z);
    matrixIds[z] = tileMatrixSet + ':' + z;
  }

  return new WMTSTileGrid({
    origin: getTopLeft(projectionExtent),
    resolutions: resolutions,
    matrixIds: matrixIds
  });
};

// 获取图层范围并缩放到合适的位置
const fetchLayerExtentAndZoom = async (workspace: string, layerName: string) => {
  try {
    console.log(`正在从API获取图层 ${workspace}:${layerName} 的边界框...`);
    const bboxData = await getLayerBbox(workspace, layerName);

    // 检查返回数据格式
    if (bboxData && bboxData.status === 'success' && bboxData.bbox && bboxData.bbox.latLon) {
      const { minx, miny, maxx, maxy } = bboxData.bbox.latLon;

      // 检查获取的边界框是否有效
      if (minx === -180 && miny === -90 && maxx === 180 && maxy === 90) {
        console.warn(`API返回全球范围作为图层 ${workspace}:${layerName} 的边界框，尝试使用备用方法`);

        // 如果是全球范围，尝试使用native属性
        if (bboxData.bbox.native &&
            !(bboxData.bbox.native.minx === -180 &&
              bboxData.bbox.native.miny === -90 &&
              bboxData.bbox.native.maxx === 180 &&
              bboxData.bbox.native.maxy === 90)) {
          // 使用native属性
          const nativeBox = bboxData.bbox.native;
          console.log(`使用native边界框：`, nativeBox);

          // 转换为OpenLayers可用的范围
          const bottomLeft = fromLonLat([nativeBox.minx, nativeBox.miny]);
          const topRight = fromLonLat([nativeBox.maxx, nativeBox.maxy]);

          // 构建extent数组
          const extent = [bottomLeft[0], bottomLeft[1], topRight[0], topRight[1]];

          console.log(`成功从API native属性获取并转换图层边界框:`, extent);
          fitToExtent(extent);
          return;
        } else {
          // 如果都是全球范围，使用默认范围
          fitToDefaultExtent();
        }
      } else {
        // 如果不是全球范围，则进行正常转换
        console.log(`API返回的有效边界框: minx=${minx}, miny=${miny}, maxx=${maxx}, maxy=${maxy}`);

        // 转换为OpenLayers可用的范围
        const bottomLeft = fromLonLat([minx, miny]);
        const topRight = fromLonLat([maxx, maxy]);

        // 构建extent数组
        const extent = [bottomLeft[0], bottomLeft[1], topRight[0], topRight[1]];

        // 检查转换后的坐标是否有效
        if (extent.some(val => !isFinite(val))) {
          console.warn(`转换后的坐标包含无效值:`, extent);
          fitToDefaultExtent();
        } else {
          console.log(`成功从API获取并转换图层边界框:`, extent);
          fitToExtent(extent);
        }
      }
    } else {
      console.warn('API返回的边界框数据格式不正确:', bboxData);
      fitToDefaultExtent();
    }
  } catch (error) {
    console.error(`获取图层边界框失败:`, error);
    fitToDefaultExtent();
  } finally {
    mapLoading.value = false;
  }
};

// 缩放到指定范围
const fitToExtent = (extent: number[]): void => {
  if (!map.value) return;

  try {
    map.value.getView().fit(extent, {
      padding: [50, 50, 50, 50],
      maxZoom: 18
    });
    console.log(`已缩放至范围: ${extent}`);
  } catch (error) {
    console.error(`缩放至范围失败: ${error}`);
    fitToDefaultExtent();
  }
};

// 缩放到默认范围（南宁区域的大致范围）
const fitToDefaultExtent = (): void => {
  if (!map.value) return;

  try {
    // 南宁区域的大致范围
    const defaultExtent = fromLonLat([108.0, 22.5]).concat(fromLonLat([109.0, 23.5]));
    map.value.getView().fit(defaultExtent, {
      padding: [50, 50, 50, 50],
      maxZoom: 12
    });
    console.log('已缩放至默认范围（南宁区域）');
  } catch (error) {
    console.error(`缩放至默认范围失败: ${error}`);
  }
};

const cleanupMap = () => {
  if (map.value) {
    map.value.setTarget(undefined);
    map.value = null;
  }
};

// 分析任务相关方法
const addAnalysisTask = async () => {
  try {
    const { value: taskName } = await ElMessageBox.prompt('请输入任务名称', '添加分析任务', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /\S+/,
      inputErrorMessage: '任务名称不能为空'
    });

    const newTask: AnalysisTask = {
      name: taskName,
      type: '自定义分析',
      status: '待开始',
      createTime: new Date().toLocaleString('zh-CN'),
      description: '用户自定义分析任务'
    };

    analysisTasks.value.push(newTask);
    ElMessage.success('任务添加成功');
  } catch {
    // 用户取消操作
  }
};

const removeTask = async (index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个分析任务吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    analysisTasks.value.splice(index, 1);
    ElMessage.success('任务删除成功');
  } catch {
    // 用户取消操作
  }
};

const viewTaskDetail = (task: AnalysisTask) => {
  ElMessage.info(`查看任务详情: ${task.name} (功能开发中)`);
};

const getTaskStatusType = (status: string) => {
  switch (status) {
    case '已完成':
      return 'success';
    case '进行中':
      return 'warning';
    case '待开始':
      return 'info';
    case '失败':
      return 'danger';
    default:
      return 'info';
  }
};

// 其他方法
const handleClose = () => {
  visible.value = false;
};

const handleExport = () => {
  // 导出功能，后续实现
  console.log('导出报告功能开发中...');
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

const formatCoordinate = (coord: number) => {
  return coord.toFixed(6);
};

// 生命周期
onBeforeUnmount(() => {
  cleanupMap();
});
</script>

<style lang="scss" scoped>
.detail-content {
  .basic-info {
    margin-bottom: 24px;
  }
  
  .detail-layout {
    display: flex;
    gap: 24px;
    min-height: 400px;
    
    .left-section {
      flex: 1;

      .analysis-tasks {
        .task-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          h4 {
            margin: 0;
            color: #303133;
            font-size: 16px;
            font-weight: 600;
            border-bottom: 1px solid #ebeef5;
            padding-bottom: 8px;
            flex: 1;
          }
        }

        .task-list {
          max-height: 400px;
          overflow-y: auto;

          .task-items {
            .task-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 12px;
              margin-bottom: 8px;
              background-color: #f8f9fa;
              border-radius: 8px;
              border: 1px solid #e4e7ed;
              transition: all 0.3s;

              &:hover {
                background-color: #ecf5ff;
                border-color: #b3d8ff;
              }

              .task-info {
                flex: 1;

                .task-name {
                  font-size: 14px;
                  font-weight: 600;
                  color: #303133;
                  margin-bottom: 4px;
                }

                .task-type {
                  font-size: 12px;
                  color: #909399;
                  margin-bottom: 4px;
                }

                .task-status {
                  .el-tag {
                    font-size: 12px;
                  }
                }
              }

              .task-actions {
                display: flex;
                gap: 8px;
              }
            }
          }
        }
      }
    }
    
    .right-section {
      flex: 1;

      .map-display {
        h4 {
          margin: 0 0 16px 0;
          color: #303133;
          font-size: 16px;
          font-weight: 600;
          border-bottom: 1px solid #ebeef5;
          padding-bottom: 8px;
        }

        .map-container {
          position: relative;
          height: 450px;
          border: 1px solid #dcdfe6;
          border-radius: 8px;
          overflow: hidden;

          .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;

            .loading-icon {
              font-size: 32px;
              color: #409EFF;
              animation: rotate 2s linear infinite;
              margin-bottom: 12px;
            }

            p {
              color: #606266;
              font-size: 14px;
              margin: 0;
            }
          }

          .map-content {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 动画效果
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// OpenLayers地图样式覆盖
:deep(.ol-control) {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
}

:deep(.ol-zoom) {
  top: 8px;
  left: 8px;
}

:deep(.ol-attribution) {
  bottom: 8px;
  right: 8px;
  background-color: rgba(255, 255, 255, 0.8);
}

:deep(.ol-scale-line) {
  bottom: 8px;
  left: 8px;
  background-color: rgba(255, 255, 255, 0.8);
}
</style>
