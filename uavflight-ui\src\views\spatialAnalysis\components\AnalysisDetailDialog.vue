<template>
  <el-dialog
    v-model="visible"
    title="分析详情"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="task" class="detail-content">
      <!-- 基本信息 -->
      <div class="basic-info">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="任务ID">{{ task.id }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ formatDateTime(task.endTime) }}</el-descriptions-item>
          <el-descriptions-item label="图层名称">{{ task.layer_name }}</el-descriptions-item>
          <el-descriptions-item label="主题">{{ task.theme }}</el-descriptions-item>
          <el-descriptions-item label="分析数">{{ task.analysisCount }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag type="success">已完成</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 左右布局区域 -->
      <div class="detail-layout">
        <!-- 左侧：空间范围详情 -->
        <div class="left-section">
          <div class="bbox-detail">
            <h4>空间范围详情</h4>
            <div class="coord-grid">
              <div class="coord-item">
                <label>最小经度：</label>
                <span>{{ formatCoordinate(task.bbox.minx) }}</span>
              </div>
              <div class="coord-item">
                <label>最大经度：</label>
                <span>{{ formatCoordinate(task.bbox.maxx) }}</span>
              </div>
              <div class="coord-item">
                <label>最小纬度：</label>
                <span>{{ formatCoordinate(task.bbox.miny) }}</span>
              </div>
              <div class="coord-item">
                <label>最大纬度：</label>
                <span>{{ formatCoordinate(task.bbox.maxy) }}</span>
              </div>
              <div class="coord-item">
                <label>坐标系：</label>
                <span>{{ task.bbox.crs }}</span>
              </div>
            </div>
            
            <!-- 范围可视化 -->
            <div class="bbox-visual">
              <h5>范围可视化</h5>
              <div class="bbox-map">
                <div class="bbox-info-text">
                  <p>经度范围：{{ formatCoordinate(task.bbox.minx) }} ~ {{ formatCoordinate(task.bbox.maxx) }}</p>
                  <p>纬度范围：{{ formatCoordinate(task.bbox.miny) }} ~ {{ formatCoordinate(task.bbox.maxy) }}</p>
                  <p>覆盖面积：约 {{ calculateArea() }} 平方公里</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧：分析详情 -->
        <div class="right-section">
          <div class="analysis-detail">
            <h4>分析详情</h4>
            <div class="analysis-placeholder">
              <el-empty description="分析详情功能开发中...">
                <template #image>
                  <el-icon size="60" color="#c0c4cc">
                    <Document />
                  </el-icon>
                </template>
                <template #description>
                  <p>分析详情功能正在开发中</p>
                  <p>敬请期待更多功能</p>
                </template>
              </el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport" disabled>导出报告</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Document } from '@element-plus/icons-vue';

// 接口定义
interface BBox {
  minx: number;
  miny: number;
  maxx: number;
  maxy: number;
  crs: string;
}

interface ProcessedTask {
  id: string;
  endTime: string;
  bbox: BBox;
  analysisCount: number;
  layer_name: string;
  theme: string;
}

// Props
interface Props {
  modelValue: boolean;
  task: ProcessedTask | null;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  task: null
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

// 响应式数据
const visible = ref(props.modelValue);

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
});

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal);
});

// 方法
const handleClose = () => {
  visible.value = false;
};

const handleExport = () => {
  // 导出功能，后续实现
  console.log('导出报告功能开发中...');
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

const formatCoordinate = (coord: number) => {
  return coord.toFixed(6);
};

const calculateArea = () => {
  if (!props.task?.bbox) return '0';
  
  const { minx, miny, maxx, maxy } = props.task.bbox;
  
  // 简单的面积计算（近似值）
  const latDiff = maxy - miny;
  const lonDiff = maxx - minx;
  
  // 1度约等于111公里（纬度）
  const area = latDiff * lonDiff * 111 * 111;
  
  return area.toFixed(2);
};
</script>

<style lang="scss" scoped>
.detail-content {
  .basic-info {
    margin-bottom: 24px;
  }
  
  .detail-layout {
    display: flex;
    gap: 24px;
    min-height: 400px;
    
    .left-section {
      flex: 1;
      
      .bbox-detail {
        h4 {
          margin: 0 0 16px 0;
          color: #303133;
          font-size: 16px;
          font-weight: 600;
          border-bottom: 1px solid #ebeef5;
          padding-bottom: 8px;
        }
        
        .coord-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 12px;
          margin-bottom: 24px;
          
          .coord-item {
            display: flex;
            align-items: center;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
            
            label {
              color: #606266;
              font-size: 14px;
              width: 80px;
              flex-shrink: 0;
            }
            
            span {
              color: #303133;
              font-size: 14px;
              font-family: 'Courier New', monospace;
              font-weight: 500;
            }
          }
        }
        
        .bbox-visual {
          h5 {
            margin: 0 0 12px 0;
            color: #606266;
            font-size: 14px;
            font-weight: 600;
          }
          
          .bbox-map {
            padding: 16px;
            background-color: #f0f2f5;
            border-radius: 8px;
            border: 1px dashed #d9d9d9;
            
            .bbox-info-text {
              p {
                margin: 8px 0;
                color: #606266;
                font-size: 13px;
                
                &:first-child {
                  margin-top: 0;
                }
                
                &:last-child {
                  margin-bottom: 0;
                  font-weight: 600;
                  color: #409EFF;
                }
              }
            }
          }
        }
      }
    }
    
    .right-section {
      flex: 1;
      
      .analysis-detail {
        h4 {
          margin: 0 0 16px 0;
          color: #303133;
          font-size: 16px;
          font-weight: 600;
          border-bottom: 1px solid #ebeef5;
          padding-bottom: 8px;
        }
        
        .analysis-placeholder {
          height: 350px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px dashed #dcdfe6;
          border-radius: 8px;
          background-color: #fafafa;
          
          :deep(.el-empty__description) {
            p {
              margin: 4px 0;
              color: #909399;
              
              &:first-child {
                font-weight: 600;
                color: #606266;
              }
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
