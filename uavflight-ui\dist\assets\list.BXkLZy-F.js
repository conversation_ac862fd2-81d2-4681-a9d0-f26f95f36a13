import{d as c,B as t,a as n,b as l,t as p,v as s,e as r,D as m,F as w,p as b,f as C,g as I}from"./vue.CnN__PXn.js";import{q,__tla as z}from"./index.C0-0gsfl.js";let d,A=Promise.all([(()=>{try{return z}catch{}})()]).then(async()=>{let i,o;i={class:"icon-selector-warp-row"},o=c({name:"iconSelectorList"}),d=q(c({...o,props:{list:{type:Array,default:()=>[]},empty:{type:String,default:()=>"\u65E0\u76F8\u5173\u56FE\u6807"},prefix:{type:String,default:()=>""}},emits:["get-icon"],setup(e,{emit:f}){const u=e,g=f;return(B,D)=>{const y=t("SvgIcon"),_=t("el-col"),v=t("el-row"),k=t("el-empty"),x=t("el-scrollbar");return l(),n("div",i,[p(x,{ref:"selectorScrollbarRef"},{default:s(()=>[u.list.length>0?(l(),r(v,{key:0,gutter:10},{default:s(()=>[(l(!0),n(w,null,b(e.list,(a,S)=>(l(),r(_,{xs:6,sm:4,md:4,lg:4,xl:4,key:S,onClick:F=>(h=>{g("get-icon",h)})(a)},{default:s(()=>[C("div",{class:I(["icon-selector-warp-item",{"icon-selector-active":e.prefix===a}])},[p(y,{name:a},null,8,["name"])],2)]),_:2},1032,["onClick"]))),128))]),_:1})):m("",!0),e.list.length<=0?(l(),r(k,{key:1,"image-size":100,description:e.empty},null,8,["description"])):m("",!0)]),_:1},512)])}}}),[["__scopeId","data-v-455512ec"]])});export{A as __tla,d as default};
