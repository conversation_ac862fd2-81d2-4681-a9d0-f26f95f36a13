import { reactive } from 'vue';
import { Style, Fill, Stroke, Circle as CircleStyle, Text } from 'ol/style';
import { useMapLayerManagerStore } from '/@/stores/olMapLayer/mapLayerManager';
import { ElMessage } from 'element-plus';
import Feature from 'ol/Feature';

// 样式编辑对话框状态
export const styleDialog = reactive({
  visible: false,
  currentLayer: null as any,
  geometryType: null as string | null,
  style: {
    opacity: 1,
    weight: 2,
    color: '#3388ff',
    fillColor: 'rgba(51, 136, 255, 0.3)',
    radius: 6,
    lineDash: [] as number[]
  }
});

// 获取点图层预览样式
export function getPointPreviewStyle(layer: any) {
  // 确保layer和layer.originalLayer存在
  if (!layer || !layer.originalLayer) {
    return {
      backgroundColor: 'rgba(51, 136, 255, 0.6)',
      borderColor: '#3388ff',
      borderWidth: '1px',
      opacity: 1
    };
  }

  const mapLayerStore = useMapLayerManagerStore();
  // 尝试从originalLayer获取style
  let style = layer.originalLayer.defaultStyle || {};
  
  // 如果在originalLayer中没有样式或样式为字符串，尝试从mapLayerManager重新获取
  if (!style || typeof style === 'string') {
    const originalLayer = mapLayerStore.getLayerById(layer.id);
    style = originalLayer?.defaultStyle || {};
    
    // 如果依然是字符串（样式名称），则从styleLibrary获取
    if (typeof style === 'string') {
      style = mapLayerStore.styleLibrary[style] || {};
    }
  }
  
  return {
    backgroundColor: style.fillColor || 'rgba(51, 136, 255, 0.6)',
    borderColor: style.color || '#3388ff',
    borderWidth: (style.weight || 1) + 'px',
    opacity: style.opacity !== undefined ? style.opacity : 1
  };
}

// 获取线图层预览样式
export function getLinePreviewStyle(layer: any) {
  // 确保layer和layer.originalLayer存在
  if (!layer || !layer.originalLayer) {
    return {
      backgroundColor: 'transparent',
      borderTop: '2px solid #3388ff',
      opacity: 1
    };
  }

  const mapLayerStore = useMapLayerManagerStore();
  // 尝试从originalLayer获取style
  let style = layer.originalLayer.defaultStyle || {};
  
  // 如果在originalLayer中没有样式或样式为字符串，尝试从mapLayerManager重新获取
  if (!style || typeof style === 'string') {
    const originalLayer = mapLayerStore.getLayerById(layer.id);
    style = originalLayer?.defaultStyle || {};
    
    // 如果依然是字符串（样式名称），则从styleLibrary获取
    if (typeof style === 'string') {
      style = mapLayerStore.styleLibrary[style] || {};
    }
  }
  
  // 判断线型
  let borderStyle = 'solid';
  if (style.hasOwnProperty('lineDash') && Array.isArray((style as any).lineDash)) {
    const lineDash = (style as any).lineDash;
    if (lineDash.length > 0) {
      if (lineDash.toString() === '4,8') {
        borderStyle = 'dashed';
      } else if (lineDash.toString() === '1,4,8,4') {
        borderStyle = 'dotted';
      }
    }
  }
  
  return {
    backgroundColor: 'transparent',
    borderTop: `${style.weight || 2}px ${borderStyle} ${style.color || '#3388ff'}`,
    opacity: style.opacity !== undefined ? style.opacity : 1
  };
}

// 获取面图层预览样式
export function getPolygonPreviewStyle(layer: any) {
  // 确保layer和layer.originalLayer存在
  if (!layer || !layer.originalLayer) {
    return {
      backgroundColor: 'rgba(51, 136, 255, 0.3)',
      borderColor: '#3388ff',
      borderWidth: '2px',
      opacity: 1
    };
  }

  const mapLayerStore = useMapLayerManagerStore();
  // 尝试从originalLayer获取style
  let style = layer.originalLayer.defaultStyle || {};
  
  // 如果在originalLayer中没有样式或样式为字符串，尝试从mapLayerManager重新获取
  if (!style || typeof style === 'string') {
    const originalLayer = mapLayerStore.getLayerById(layer.id);
    style = originalLayer?.defaultStyle || {};
    
    // 如果依然是字符串（样式名称），则从styleLibrary获取
    if (typeof style === 'string') {
      style = mapLayerStore.styleLibrary[style] || {};
    }
  }
  
  return {
    backgroundColor: style.fillColor || 'rgba(51, 136, 255, 0.3)',
    borderColor: style.color || '#3388ff',
    borderWidth: (style.weight || 2) + 'px',
    opacity: style.opacity !== undefined ? style.opacity : 1
  };
}

// 打开样式编辑器
export function openStyleEditor(layer: any) {
  if (!layer) {
    ElMessage.error('图层信息不完整，无法编辑样式');
    return;
  }

  const mapLayerStore = useMapLayerManagerStore();
  styleDialog.currentLayer = layer;
  
  // 首先尝试从传入的layer.originalLayer获取样式信息
  let currentStyle: any = {};
  
  // 获取当前图层的样式和几何类型
  if (layer.originalLayer && layer.originalLayer.defaultStyle) {
    if (typeof layer.originalLayer.defaultStyle === 'string') {
      // 如果样式是字符串引用，尝试从styleLibrary获取
      currentStyle = mapLayerStore.styleLibrary[layer.originalLayer.defaultStyle] || {};
    } else {
      // 直接使用对象样式
      currentStyle = layer.originalLayer.defaultStyle;
    }
  }
  
  // 如果从layer中没有获取到样式，再尝试从mapLayerManager获取
  if (Object.keys(currentStyle).length === 0) {
    const originalLayer = mapLayerStore.getLayerById(layer.id);
    
    if (originalLayer?.defaultStyle) {
      if (typeof originalLayer.defaultStyle === 'string') {
        // 如果样式是字符串引用，尝试从styleLibrary获取
        currentStyle = mapLayerStore.styleLibrary[originalLayer.defaultStyle] || {};
      } else {
        // 直接使用对象样式
        currentStyle = originalLayer.defaultStyle;
      }
    }
  }
  
  console.log('打开样式编辑器，当前样式:', currentStyle);
  
  // 设置几何类型，优先使用传入的layer中的信息
  let geometryType = '';
  if (layer.originalLayer && layer.originalLayer.geometryType) {
    geometryType = layer.originalLayer.geometryType;
  } else {
    const layerFromStore = mapLayerStore.getLayerById(layer.id);
    if (layerFromStore && layerFromStore.geometryType) {
      geometryType = layerFromStore.geometryType;
    } else {
      geometryType = 'Polygon'; // 默认值
    }
  }
  
  styleDialog.geometryType = geometryType;
  
  // 为不同几何类型准备默认样式
  if (styleDialog.geometryType?.includes('Line')) {
    // 线图层专有样式设置
    styleDialog.style = {
      opacity: currentStyle.opacity !== undefined ? currentStyle.opacity : 1,
      weight: currentStyle.weight || 2,
      color: currentStyle.color || '#3388ff',
      fillColor: '', // 线不需要填充颜色
      radius: 0, // 线不需要半径
      lineDash: Array.isArray(currentStyle.lineDash) ? [...currentStyle.lineDash] : []
    };
  } else if (styleDialog.geometryType?.includes('Point')) {
    // 点图层样式设置
    styleDialog.style = {
      opacity: currentStyle.opacity !== undefined ? currentStyle.opacity : 1,
      weight: currentStyle.weight || 1,
      color: currentStyle.color || '#3388ff',
      fillColor: currentStyle.fillColor || 'rgba(51, 136, 255, 0.3)',
      radius: currentStyle.radius || 6,
      lineDash: []
    };
  } else {
    // 面图层样式设置
    styleDialog.style = {
      opacity: currentStyle.opacity !== undefined ? currentStyle.opacity : 1,
      weight: currentStyle.weight || 2,
      color: currentStyle.color || '#3388ff',
      fillColor: currentStyle.fillColor || 'rgba(51, 136, 255, 0.3)',
      radius: 0,
      lineDash: []
    };
  }
  
  styleDialog.visible = true;
}

// 应用样式
export function applyStyle(layerListState: any) {
  if (!styleDialog.currentLayer) return;
  
  try {
    const mapLayerStore = useMapLayerManagerStore();
    // 更新图层样式
    const layerId = styleDialog.currentLayer.id;
    const newStyle = { ...styleDialog.style };
    
    // 检查图层实例是否存在
    const layer = mapLayerStore.getLayerById(layerId);
    if (layer && layer.layerInstance) {
      let olStyle;
      
      if (styleDialog.geometryType?.includes('Point')) {
        // 点图层样式
        olStyle = new Style({
          image: new CircleStyle({
            radius: newStyle.radius,
            fill: new Fill({
              color: newStyle.fillColor
            }),
            stroke: new Stroke({
              color: newStyle.color,
              width: newStyle.weight
            })
          })
        });
      } else if (styleDialog.geometryType?.includes('Line')) {
        // 线图层样式 - 安全处理lineDash
        const lineDash = Array.isArray(newStyle.lineDash) ? [...newStyle.lineDash] : [];
        
        olStyle = new Style({
          stroke: new Stroke({
            color: newStyle.color,
            width: newStyle.weight,
            lineDash: lineDash
          })
        });
        
        // 线图层应该只保留相关属性
        newStyle.fillColor = '';
        newStyle.radius = 0;
      } else {
        // 面图层样式
        olStyle = new Style({
          stroke: new Stroke({
            color: newStyle.color,
            width: newStyle.weight
          }),
          fill: new Fill({
            color: newStyle.fillColor
          })
        });
        
        // 面图层没有lineDash属性
        newStyle.lineDash = [];
        newStyle.radius = 0;
      }
      
      // 更新样式
      if (typeof layer.layerInstance.setStyle === 'function') {
        layer.layerInstance.setStyle(olStyle);
        
        // 更新原始图层的样式配置 - 确保不同图层类型只保存需要的属性
        let updatedStyle;
        
        if (styleDialog.geometryType?.includes('Line')) {
          // 线图层只保存需要的属性
          updatedStyle = {
            opacity: newStyle.opacity,
            weight: newStyle.weight,
            color: newStyle.color,
            lineDash: Array.isArray(newStyle.lineDash) ? [...newStyle.lineDash] : []
          };
        } else if (styleDialog.geometryType?.includes('Point')) {
          // 点图层属性
          updatedStyle = {
            opacity: newStyle.opacity,
            weight: newStyle.weight,
            color: newStyle.color,
            fillColor: newStyle.fillColor,
            radius: newStyle.radius
          };
        } else {
          // 面图层属性
          updatedStyle = {
            opacity: newStyle.opacity,
            weight: newStyle.weight,
            color: newStyle.color,
            fillColor: newStyle.fillColor
          };
        }
        
        // 更新图层样式
        layer.defaultStyle = updatedStyle as any; // 使用类型断言解决lineDash属性不在类型定义中的问题
        
        // 更新当前树节点的样式预览
        const treeNode = layerListState.items.find((item: any) => item.id === layerId);
        if (treeNode && treeNode.originalLayer) {
          // 避免引用同一个对象，创建新对象赋值
          treeNode.originalLayer.defaultStyle = { ...updatedStyle };
        }
        
        ElMessage.success('图层样式已更新');
      } else {
        ElMessage.warning('该图层不支持样式设置');
      }
    } else {
      ElMessage.error('图层不存在或未加载');
    }
    
    styleDialog.visible = false;
  } catch (error) {
    console.error('应用样式时出错:', error);
    ElMessage.error('更新样式失败');
  }
}

// 格式化透明度工具提示
export const formatTooltip = (val: number): string => {
  return `${Math.round(val * 100)}%`;
}; 