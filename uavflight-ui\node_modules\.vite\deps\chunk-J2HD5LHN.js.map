{"version": 3, "sources": ["../../earcut/src/earcut.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = earcut;\nmodule.exports.default = earcut;\n\nfunction earcut(data, holeIndices, dim) {\n\n    dim = dim || 2;\n\n    var hasHoles = holeIndices && holeIndices.length,\n        outerLen = hasHoles ? holeIndices[0] * dim : data.length,\n        outerNode = linkedList(data, 0, outerLen, dim, true),\n        triangles = [];\n\n    if (!outerNode || outerNode.next === outerNode.prev) return triangles;\n\n    var minX, minY, maxX, maxY, x, y, invSize;\n\n    if (hasHoles) outerNode = eliminateHoles(data, holeIndices, outerNode, dim);\n\n    // if the shape is not too simple, we'll use z-order curve hash later; calculate polygon bbox\n    if (data.length > 80 * dim) {\n        minX = maxX = data[0];\n        minY = maxY = data[1];\n\n        for (var i = dim; i < outerLen; i += dim) {\n            x = data[i];\n            y = data[i + 1];\n            if (x < minX) minX = x;\n            if (y < minY) minY = y;\n            if (x > maxX) maxX = x;\n            if (y > maxY) maxY = y;\n        }\n\n        // minX, minY and invSize are later used to transform coords into integers for z-order calculation\n        invSize = Math.max(maxX - minX, maxY - minY);\n        invSize = invSize !== 0 ? 32767 / invSize : 0;\n    }\n\n    earcutLinked(outerNode, triangles, dim, minX, minY, invSize, 0);\n\n    return triangles;\n}\n\n// create a circular doubly linked list from polygon points in the specified winding order\nfunction linkedList(data, start, end, dim, clockwise) {\n    var i, last;\n\n    if (clockwise === (signedArea(data, start, end, dim) > 0)) {\n        for (i = start; i < end; i += dim) last = insertNode(i, data[i], data[i + 1], last);\n    } else {\n        for (i = end - dim; i >= start; i -= dim) last = insertNode(i, data[i], data[i + 1], last);\n    }\n\n    if (last && equals(last, last.next)) {\n        removeNode(last);\n        last = last.next;\n    }\n\n    return last;\n}\n\n// eliminate colinear or duplicate points\nfunction filterPoints(start, end) {\n    if (!start) return start;\n    if (!end) end = start;\n\n    var p = start,\n        again;\n    do {\n        again = false;\n\n        if (!p.steiner && (equals(p, p.next) || area(p.prev, p, p.next) === 0)) {\n            removeNode(p);\n            p = end = p.prev;\n            if (p === p.next) break;\n            again = true;\n\n        } else {\n            p = p.next;\n        }\n    } while (again || p !== end);\n\n    return end;\n}\n\n// main ear slicing loop which triangulates a polygon (given as a linked list)\nfunction earcutLinked(ear, triangles, dim, minX, minY, invSize, pass) {\n    if (!ear) return;\n\n    // interlink polygon nodes in z-order\n    if (!pass && invSize) indexCurve(ear, minX, minY, invSize);\n\n    var stop = ear,\n        prev, next;\n\n    // iterate through ears, slicing them one by one\n    while (ear.prev !== ear.next) {\n        prev = ear.prev;\n        next = ear.next;\n\n        if (invSize ? isEarHashed(ear, minX, minY, invSize) : isEar(ear)) {\n            // cut off the triangle\n            triangles.push(prev.i / dim | 0);\n            triangles.push(ear.i / dim | 0);\n            triangles.push(next.i / dim | 0);\n\n            removeNode(ear);\n\n            // skipping the next vertex leads to less sliver triangles\n            ear = next.next;\n            stop = next.next;\n\n            continue;\n        }\n\n        ear = next;\n\n        // if we looped through the whole remaining polygon and can't find any more ears\n        if (ear === stop) {\n            // try filtering points and slicing again\n            if (!pass) {\n                earcutLinked(filterPoints(ear), triangles, dim, minX, minY, invSize, 1);\n\n            // if this didn't work, try curing all small self-intersections locally\n            } else if (pass === 1) {\n                ear = cureLocalIntersections(filterPoints(ear), triangles, dim);\n                earcutLinked(ear, triangles, dim, minX, minY, invSize, 2);\n\n            // as a last resort, try splitting the remaining polygon into two\n            } else if (pass === 2) {\n                splitEarcut(ear, triangles, dim, minX, minY, invSize);\n            }\n\n            break;\n        }\n    }\n}\n\n// check whether a polygon node forms a valid ear with adjacent nodes\nfunction isEar(ear) {\n    var a = ear.prev,\n        b = ear,\n        c = ear.next;\n\n    if (area(a, b, c) >= 0) return false; // reflex, can't be an ear\n\n    // now make sure we don't have other points inside the potential ear\n    var ax = a.x, bx = b.x, cx = c.x, ay = a.y, by = b.y, cy = c.y;\n\n    // triangle bbox; min & max are calculated like this for speed\n    var x0 = ax < bx ? (ax < cx ? ax : cx) : (bx < cx ? bx : cx),\n        y0 = ay < by ? (ay < cy ? ay : cy) : (by < cy ? by : cy),\n        x1 = ax > bx ? (ax > cx ? ax : cx) : (bx > cx ? bx : cx),\n        y1 = ay > by ? (ay > cy ? ay : cy) : (by > cy ? by : cy);\n\n    var p = c.next;\n    while (p !== a) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, p.x, p.y) &&\n            area(p.prev, p, p.next) >= 0) return false;\n        p = p.next;\n    }\n\n    return true;\n}\n\nfunction isEarHashed(ear, minX, minY, invSize) {\n    var a = ear.prev,\n        b = ear,\n        c = ear.next;\n\n    if (area(a, b, c) >= 0) return false; // reflex, can't be an ear\n\n    var ax = a.x, bx = b.x, cx = c.x, ay = a.y, by = b.y, cy = c.y;\n\n    // triangle bbox; min & max are calculated like this for speed\n    var x0 = ax < bx ? (ax < cx ? ax : cx) : (bx < cx ? bx : cx),\n        y0 = ay < by ? (ay < cy ? ay : cy) : (by < cy ? by : cy),\n        x1 = ax > bx ? (ax > cx ? ax : cx) : (bx > cx ? bx : cx),\n        y1 = ay > by ? (ay > cy ? ay : cy) : (by > cy ? by : cy);\n\n    // z-order range for the current triangle bbox;\n    var minZ = zOrder(x0, y0, minX, minY, invSize),\n        maxZ = zOrder(x1, y1, minX, minY, invSize);\n\n    var p = ear.prevZ,\n        n = ear.nextZ;\n\n    // look for points inside the triangle in both directions\n    while (p && p.z >= minZ && n && n.z <= maxZ) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 && p !== a && p !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, p.x, p.y) && area(p.prev, p, p.next) >= 0) return false;\n        p = p.prevZ;\n\n        if (n.x >= x0 && n.x <= x1 && n.y >= y0 && n.y <= y1 && n !== a && n !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, n.x, n.y) && area(n.prev, n, n.next) >= 0) return false;\n        n = n.nextZ;\n    }\n\n    // look for remaining points in decreasing z-order\n    while (p && p.z >= minZ) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 && p !== a && p !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, p.x, p.y) && area(p.prev, p, p.next) >= 0) return false;\n        p = p.prevZ;\n    }\n\n    // look for remaining points in increasing z-order\n    while (n && n.z <= maxZ) {\n        if (n.x >= x0 && n.x <= x1 && n.y >= y0 && n.y <= y1 && n !== a && n !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, n.x, n.y) && area(n.prev, n, n.next) >= 0) return false;\n        n = n.nextZ;\n    }\n\n    return true;\n}\n\n// go through all polygon nodes and cure small local self-intersections\nfunction cureLocalIntersections(start, triangles, dim) {\n    var p = start;\n    do {\n        var a = p.prev,\n            b = p.next.next;\n\n        if (!equals(a, b) && intersects(a, p, p.next, b) && locallyInside(a, b) && locallyInside(b, a)) {\n\n            triangles.push(a.i / dim | 0);\n            triangles.push(p.i / dim | 0);\n            triangles.push(b.i / dim | 0);\n\n            // remove two nodes involved\n            removeNode(p);\n            removeNode(p.next);\n\n            p = start = b;\n        }\n        p = p.next;\n    } while (p !== start);\n\n    return filterPoints(p);\n}\n\n// try splitting polygon into two and triangulate them independently\nfunction splitEarcut(start, triangles, dim, minX, minY, invSize) {\n    // look for a valid diagonal that divides the polygon into two\n    var a = start;\n    do {\n        var b = a.next.next;\n        while (b !== a.prev) {\n            if (a.i !== b.i && isValidDiagonal(a, b)) {\n                // split the polygon in two by the diagonal\n                var c = splitPolygon(a, b);\n\n                // filter colinear points around the cuts\n                a = filterPoints(a, a.next);\n                c = filterPoints(c, c.next);\n\n                // run earcut on each half\n                earcutLinked(a, triangles, dim, minX, minY, invSize, 0);\n                earcutLinked(c, triangles, dim, minX, minY, invSize, 0);\n                return;\n            }\n            b = b.next;\n        }\n        a = a.next;\n    } while (a !== start);\n}\n\n// link every hole into the outer loop, producing a single-ring polygon without holes\nfunction eliminateHoles(data, holeIndices, outerNode, dim) {\n    var queue = [],\n        i, len, start, end, list;\n\n    for (i = 0, len = holeIndices.length; i < len; i++) {\n        start = holeIndices[i] * dim;\n        end = i < len - 1 ? holeIndices[i + 1] * dim : data.length;\n        list = linkedList(data, start, end, dim, false);\n        if (list === list.next) list.steiner = true;\n        queue.push(getLeftmost(list));\n    }\n\n    queue.sort(compareX);\n\n    // process holes from left to right\n    for (i = 0; i < queue.length; i++) {\n        outerNode = eliminateHole(queue[i], outerNode);\n    }\n\n    return outerNode;\n}\n\nfunction compareX(a, b) {\n    return a.x - b.x;\n}\n\n// find a bridge between vertices that connects hole with an outer ring and and link it\nfunction eliminateHole(hole, outerNode) {\n    var bridge = findHoleBridge(hole, outerNode);\n    if (!bridge) {\n        return outerNode;\n    }\n\n    var bridgeReverse = splitPolygon(bridge, hole);\n\n    // filter collinear points around the cuts\n    filterPoints(bridgeReverse, bridgeReverse.next);\n    return filterPoints(bridge, bridge.next);\n}\n\n// David Eberly's algorithm for finding a bridge between hole and outer polygon\nfunction findHoleBridge(hole, outerNode) {\n    var p = outerNode,\n        hx = hole.x,\n        hy = hole.y,\n        qx = -Infinity,\n        m;\n\n    // find a segment intersected by a ray from the hole's leftmost point to the left;\n    // segment's endpoint with lesser x will be potential connection point\n    do {\n        if (hy <= p.y && hy >= p.next.y && p.next.y !== p.y) {\n            var x = p.x + (hy - p.y) * (p.next.x - p.x) / (p.next.y - p.y);\n            if (x <= hx && x > qx) {\n                qx = x;\n                m = p.x < p.next.x ? p : p.next;\n                if (x === hx) return m; // hole touches outer segment; pick leftmost endpoint\n            }\n        }\n        p = p.next;\n    } while (p !== outerNode);\n\n    if (!m) return null;\n\n    // look for points inside the triangle of hole point, segment intersection and endpoint;\n    // if there are no points found, we have a valid connection;\n    // otherwise choose the point of the minimum angle with the ray as connection point\n\n    var stop = m,\n        mx = m.x,\n        my = m.y,\n        tanMin = Infinity,\n        tan;\n\n    p = m;\n\n    do {\n        if (hx >= p.x && p.x >= mx && hx !== p.x &&\n                pointInTriangle(hy < my ? hx : qx, hy, mx, my, hy < my ? qx : hx, hy, p.x, p.y)) {\n\n            tan = Math.abs(hy - p.y) / (hx - p.x); // tangential\n\n            if (locallyInside(p, hole) &&\n                (tan < tanMin || (tan === tanMin && (p.x > m.x || (p.x === m.x && sectorContainsSector(m, p)))))) {\n                m = p;\n                tanMin = tan;\n            }\n        }\n\n        p = p.next;\n    } while (p !== stop);\n\n    return m;\n}\n\n// whether sector in vertex m contains sector in vertex p in the same coordinates\nfunction sectorContainsSector(m, p) {\n    return area(m.prev, m, p.prev) < 0 && area(p.next, m, m.next) < 0;\n}\n\n// interlink polygon nodes in z-order\nfunction indexCurve(start, minX, minY, invSize) {\n    var p = start;\n    do {\n        if (p.z === 0) p.z = zOrder(p.x, p.y, minX, minY, invSize);\n        p.prevZ = p.prev;\n        p.nextZ = p.next;\n        p = p.next;\n    } while (p !== start);\n\n    p.prevZ.nextZ = null;\n    p.prevZ = null;\n\n    sortLinked(p);\n}\n\n// Simon Tatham's linked list merge sort algorithm\n// http://www.chiark.greenend.org.uk/~sgtatham/algorithms/listsort.html\nfunction sortLinked(list) {\n    var i, p, q, e, tail, numMerges, pSize, qSize,\n        inSize = 1;\n\n    do {\n        p = list;\n        list = null;\n        tail = null;\n        numMerges = 0;\n\n        while (p) {\n            numMerges++;\n            q = p;\n            pSize = 0;\n            for (i = 0; i < inSize; i++) {\n                pSize++;\n                q = q.nextZ;\n                if (!q) break;\n            }\n            qSize = inSize;\n\n            while (pSize > 0 || (qSize > 0 && q)) {\n\n                if (pSize !== 0 && (qSize === 0 || !q || p.z <= q.z)) {\n                    e = p;\n                    p = p.nextZ;\n                    pSize--;\n                } else {\n                    e = q;\n                    q = q.nextZ;\n                    qSize--;\n                }\n\n                if (tail) tail.nextZ = e;\n                else list = e;\n\n                e.prevZ = tail;\n                tail = e;\n            }\n\n            p = q;\n        }\n\n        tail.nextZ = null;\n        inSize *= 2;\n\n    } while (numMerges > 1);\n\n    return list;\n}\n\n// z-order of a point given coords and inverse of the longer side of data bbox\nfunction zOrder(x, y, minX, minY, invSize) {\n    // coords are transformed into non-negative 15-bit integer range\n    x = (x - minX) * invSize | 0;\n    y = (y - minY) * invSize | 0;\n\n    x = (x | (x << 8)) & 0x00FF00FF;\n    x = (x | (x << 4)) & 0x0F0F0F0F;\n    x = (x | (x << 2)) & 0x33333333;\n    x = (x | (x << 1)) & 0x55555555;\n\n    y = (y | (y << 8)) & 0x00FF00FF;\n    y = (y | (y << 4)) & 0x0F0F0F0F;\n    y = (y | (y << 2)) & 0x33333333;\n    y = (y | (y << 1)) & 0x55555555;\n\n    return x | (y << 1);\n}\n\n// find the leftmost node of a polygon ring\nfunction getLeftmost(start) {\n    var p = start,\n        leftmost = start;\n    do {\n        if (p.x < leftmost.x || (p.x === leftmost.x && p.y < leftmost.y)) leftmost = p;\n        p = p.next;\n    } while (p !== start);\n\n    return leftmost;\n}\n\n// check if a point lies within a convex triangle\nfunction pointInTriangle(ax, ay, bx, by, cx, cy, px, py) {\n    return (cx - px) * (ay - py) >= (ax - px) * (cy - py) &&\n           (ax - px) * (by - py) >= (bx - px) * (ay - py) &&\n           (bx - px) * (cy - py) >= (cx - px) * (by - py);\n}\n\n// check if a diagonal between two polygon nodes is valid (lies in polygon interior)\nfunction isValidDiagonal(a, b) {\n    return a.next.i !== b.i && a.prev.i !== b.i && !intersectsPolygon(a, b) && // dones't intersect other edges\n           (locallyInside(a, b) && locallyInside(b, a) && middleInside(a, b) && // locally visible\n            (area(a.prev, a, b.prev) || area(a, b.prev, b)) || // does not create opposite-facing sectors\n            equals(a, b) && area(a.prev, a, a.next) > 0 && area(b.prev, b, b.next) > 0); // special zero-length case\n}\n\n// signed area of a triangle\nfunction area(p, q, r) {\n    return (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);\n}\n\n// check if two points are equal\nfunction equals(p1, p2) {\n    return p1.x === p2.x && p1.y === p2.y;\n}\n\n// check if two segments intersect\nfunction intersects(p1, q1, p2, q2) {\n    var o1 = sign(area(p1, q1, p2));\n    var o2 = sign(area(p1, q1, q2));\n    var o3 = sign(area(p2, q2, p1));\n    var o4 = sign(area(p2, q2, q1));\n\n    if (o1 !== o2 && o3 !== o4) return true; // general case\n\n    if (o1 === 0 && onSegment(p1, p2, q1)) return true; // p1, q1 and p2 are collinear and p2 lies on p1q1\n    if (o2 === 0 && onSegment(p1, q2, q1)) return true; // p1, q1 and q2 are collinear and q2 lies on p1q1\n    if (o3 === 0 && onSegment(p2, p1, q2)) return true; // p2, q2 and p1 are collinear and p1 lies on p2q2\n    if (o4 === 0 && onSegment(p2, q1, q2)) return true; // p2, q2 and q1 are collinear and q1 lies on p2q2\n\n    return false;\n}\n\n// for collinear points p, q, r, check if point q lies on segment pr\nfunction onSegment(p, q, r) {\n    return q.x <= Math.max(p.x, r.x) && q.x >= Math.min(p.x, r.x) && q.y <= Math.max(p.y, r.y) && q.y >= Math.min(p.y, r.y);\n}\n\nfunction sign(num) {\n    return num > 0 ? 1 : num < 0 ? -1 : 0;\n}\n\n// check if a polygon diagonal intersects any polygon segments\nfunction intersectsPolygon(a, b) {\n    var p = a;\n    do {\n        if (p.i !== a.i && p.next.i !== a.i && p.i !== b.i && p.next.i !== b.i &&\n                intersects(p, p.next, a, b)) return true;\n        p = p.next;\n    } while (p !== a);\n\n    return false;\n}\n\n// check if a polygon diagonal is locally inside the polygon\nfunction locallyInside(a, b) {\n    return area(a.prev, a, a.next) < 0 ?\n        area(a, b, a.next) >= 0 && area(a, a.prev, b) >= 0 :\n        area(a, b, a.prev) < 0 || area(a, a.next, b) < 0;\n}\n\n// check if the middle point of a polygon diagonal is inside the polygon\nfunction middleInside(a, b) {\n    var p = a,\n        inside = false,\n        px = (a.x + b.x) / 2,\n        py = (a.y + b.y) / 2;\n    do {\n        if (((p.y > py) !== (p.next.y > py)) && p.next.y !== p.y &&\n                (px < (p.next.x - p.x) * (py - p.y) / (p.next.y - p.y) + p.x))\n            inside = !inside;\n        p = p.next;\n    } while (p !== a);\n\n    return inside;\n}\n\n// link two polygon vertices with a bridge; if the vertices belong to the same ring, it splits polygon into two;\n// if one belongs to the outer ring and another to a hole, it merges it into a single ring\nfunction splitPolygon(a, b) {\n    var a2 = new Node(a.i, a.x, a.y),\n        b2 = new Node(b.i, b.x, b.y),\n        an = a.next,\n        bp = b.prev;\n\n    a.next = b;\n    b.prev = a;\n\n    a2.next = an;\n    an.prev = a2;\n\n    b2.next = a2;\n    a2.prev = b2;\n\n    bp.next = b2;\n    b2.prev = bp;\n\n    return b2;\n}\n\n// create a node and optionally link it with previous one (in a circular doubly linked list)\nfunction insertNode(i, x, y, last) {\n    var p = new Node(i, x, y);\n\n    if (!last) {\n        p.prev = p;\n        p.next = p;\n\n    } else {\n        p.next = last.next;\n        p.prev = last;\n        last.next.prev = p;\n        last.next = p;\n    }\n    return p;\n}\n\nfunction removeNode(p) {\n    p.next.prev = p.prev;\n    p.prev.next = p.next;\n\n    if (p.prevZ) p.prevZ.nextZ = p.nextZ;\n    if (p.nextZ) p.nextZ.prevZ = p.prevZ;\n}\n\nfunction Node(i, x, y) {\n    // vertex index in coordinates array\n    this.i = i;\n\n    // vertex coordinates\n    this.x = x;\n    this.y = y;\n\n    // previous and next vertex nodes in a polygon ring\n    this.prev = null;\n    this.next = null;\n\n    // z-order curve value\n    this.z = 0;\n\n    // previous and next nodes in z-order\n    this.prevZ = null;\n    this.nextZ = null;\n\n    // indicates whether this is a steiner point\n    this.steiner = false;\n}\n\n// return a percentage difference between the polygon area and its triangulation area;\n// used to verify correctness of triangulation\nearcut.deviation = function (data, holeIndices, dim, triangles) {\n    var hasHoles = holeIndices && holeIndices.length;\n    var outerLen = hasHoles ? holeIndices[0] * dim : data.length;\n\n    var polygonArea = Math.abs(signedArea(data, 0, outerLen, dim));\n    if (hasHoles) {\n        for (var i = 0, len = holeIndices.length; i < len; i++) {\n            var start = holeIndices[i] * dim;\n            var end = i < len - 1 ? holeIndices[i + 1] * dim : data.length;\n            polygonArea -= Math.abs(signedArea(data, start, end, dim));\n        }\n    }\n\n    var trianglesArea = 0;\n    for (i = 0; i < triangles.length; i += 3) {\n        var a = triangles[i] * dim;\n        var b = triangles[i + 1] * dim;\n        var c = triangles[i + 2] * dim;\n        trianglesArea += Math.abs(\n            (data[a] - data[c]) * (data[b + 1] - data[a + 1]) -\n            (data[a] - data[b]) * (data[c + 1] - data[a + 1]));\n    }\n\n    return polygonArea === 0 && trianglesArea === 0 ? 0 :\n        Math.abs((trianglesArea - polygonArea) / polygonArea);\n};\n\nfunction signedArea(data, start, end, dim) {\n    var sum = 0;\n    for (var i = start, j = end - dim; i < end; i += dim) {\n        sum += (data[j] - data[i]) * (data[i + 1] + data[j + 1]);\n        j = i;\n    }\n    return sum;\n}\n\n// turn a polygon in a multi-dimensional array form (e.g. as in GeoJSON) into a form Earcut accepts\nearcut.flatten = function (data) {\n    var dim = data[0][0].length,\n        result = {vertices: [], holes: [], dimensions: dim},\n        holeIndex = 0;\n\n    for (var i = 0; i < data.length; i++) {\n        for (var j = 0; j < data[i].length; j++) {\n            for (var d = 0; d < dim; d++) result.vertices.push(data[i][j][d]);\n        }\n        if (i > 0) {\n            holeIndex += data[i - 1].length;\n            result.holes.push(holeIndex);\n        }\n    }\n    return result;\n};\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,QAAQ,UAAU;AAEzB,aAAS,OAAO,MAAM,aAAa,KAAK;AAEpC,YAAM,OAAO;AAEb,UAAI,WAAW,eAAe,YAAY,QACtC,WAAW,WAAW,YAAY,CAAC,IAAI,MAAM,KAAK,QAClD,YAAY,WAAW,MAAM,GAAG,UAAU,KAAK,IAAI,GACnD,YAAY,CAAC;AAEjB,UAAI,CAAC,aAAa,UAAU,SAAS,UAAU,KAAM,QAAO;AAE5D,UAAI,MAAM,MAAM,MAAM,MAAM,GAAG,GAAG;AAElC,UAAI,SAAU,aAAY,eAAe,MAAM,aAAa,WAAW,GAAG;AAG1E,UAAI,KAAK,SAAS,KAAK,KAAK;AACxB,eAAO,OAAO,KAAK,CAAC;AACpB,eAAO,OAAO,KAAK,CAAC;AAEpB,iBAAS,IAAI,KAAK,IAAI,UAAU,KAAK,KAAK;AACtC,cAAI,KAAK,CAAC;AACV,cAAI,KAAK,IAAI,CAAC;AACd,cAAI,IAAI,KAAM,QAAO;AACrB,cAAI,IAAI,KAAM,QAAO;AACrB,cAAI,IAAI,KAAM,QAAO;AACrB,cAAI,IAAI,KAAM,QAAO;AAAA,QACzB;AAGA,kBAAU,KAAK,IAAI,OAAO,MAAM,OAAO,IAAI;AAC3C,kBAAU,YAAY,IAAI,QAAQ,UAAU;AAAA,MAChD;AAEA,mBAAa,WAAW,WAAW,KAAK,MAAM,MAAM,SAAS,CAAC;AAE9D,aAAO;AAAA,IACX;AAGA,aAAS,WAAW,MAAM,OAAO,KAAK,KAAK,WAAW;AAClD,UAAI,GAAG;AAEP,UAAI,cAAe,WAAW,MAAM,OAAO,KAAK,GAAG,IAAI,GAAI;AACvD,aAAK,IAAI,OAAO,IAAI,KAAK,KAAK,IAAK,QAAO,WAAW,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,IAAI;AAAA,MACtF,OAAO;AACH,aAAK,IAAI,MAAM,KAAK,KAAK,OAAO,KAAK,IAAK,QAAO,WAAW,GAAG,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,IAAI;AAAA,MAC7F;AAEA,UAAI,QAAQ,OAAO,MAAM,KAAK,IAAI,GAAG;AACjC,mBAAW,IAAI;AACf,eAAO,KAAK;AAAA,MAChB;AAEA,aAAO;AAAA,IACX;AAGA,aAAS,aAAa,OAAO,KAAK;AAC9B,UAAI,CAAC,MAAO,QAAO;AACnB,UAAI,CAAC,IAAK,OAAM;AAEhB,UAAI,IAAI,OACJ;AACJ,SAAG;AACC,gBAAQ;AAER,YAAI,CAAC,EAAE,YAAY,OAAO,GAAG,EAAE,IAAI,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE,IAAI,MAAM,IAAI;AACpE,qBAAW,CAAC;AACZ,cAAI,MAAM,EAAE;AACZ,cAAI,MAAM,EAAE,KAAM;AAClB,kBAAQ;AAAA,QAEZ,OAAO;AACH,cAAI,EAAE;AAAA,QACV;AAAA,MACJ,SAAS,SAAS,MAAM;AAExB,aAAO;AAAA,IACX;AAGA,aAAS,aAAa,KAAK,WAAW,KAAK,MAAM,MAAM,SAAS,MAAM;AAClE,UAAI,CAAC,IAAK;AAGV,UAAI,CAAC,QAAQ,QAAS,YAAW,KAAK,MAAM,MAAM,OAAO;AAEzD,UAAI,OAAO,KACP,MAAM;AAGV,aAAO,IAAI,SAAS,IAAI,MAAM;AAC1B,eAAO,IAAI;AACX,eAAO,IAAI;AAEX,YAAI,UAAU,YAAY,KAAK,MAAM,MAAM,OAAO,IAAI,MAAM,GAAG,GAAG;AAE9D,oBAAU,KAAK,KAAK,IAAI,MAAM,CAAC;AAC/B,oBAAU,KAAK,IAAI,IAAI,MAAM,CAAC;AAC9B,oBAAU,KAAK,KAAK,IAAI,MAAM,CAAC;AAE/B,qBAAW,GAAG;AAGd,gBAAM,KAAK;AACX,iBAAO,KAAK;AAEZ;AAAA,QACJ;AAEA,cAAM;AAGN,YAAI,QAAQ,MAAM;AAEd,cAAI,CAAC,MAAM;AACP,yBAAa,aAAa,GAAG,GAAG,WAAW,KAAK,MAAM,MAAM,SAAS,CAAC;AAAA,UAG1E,WAAW,SAAS,GAAG;AACnB,kBAAM,uBAAuB,aAAa,GAAG,GAAG,WAAW,GAAG;AAC9D,yBAAa,KAAK,WAAW,KAAK,MAAM,MAAM,SAAS,CAAC;AAAA,UAG5D,WAAW,SAAS,GAAG;AACnB,wBAAY,KAAK,WAAW,KAAK,MAAM,MAAM,OAAO;AAAA,UACxD;AAEA;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAGA,aAAS,MAAM,KAAK;AAChB,UAAI,IAAI,IAAI,MACR,IAAI,KACJ,IAAI,IAAI;AAEZ,UAAI,KAAK,GAAG,GAAG,CAAC,KAAK,EAAG,QAAO;AAG/B,UAAI,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE;AAG7D,UAAI,KAAK,KAAK,KAAM,KAAK,KAAK,KAAK,KAAO,KAAK,KAAK,KAAK,IACrD,KAAK,KAAK,KAAM,KAAK,KAAK,KAAK,KAAO,KAAK,KAAK,KAAK,IACrD,KAAK,KAAK,KAAM,KAAK,KAAK,KAAK,KAAO,KAAK,KAAK,KAAK,IACrD,KAAK,KAAK,KAAM,KAAK,KAAK,KAAK,KAAO,KAAK,KAAK,KAAK;AAEzD,UAAI,IAAI,EAAE;AACV,aAAO,MAAM,GAAG;AACZ,YAAI,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,MAC9C,gBAAgB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC,KAChD,KAAK,EAAE,MAAM,GAAG,EAAE,IAAI,KAAK,EAAG,QAAO;AACzC,YAAI,EAAE;AAAA,MACV;AAEA,aAAO;AAAA,IACX;AAEA,aAAS,YAAY,KAAK,MAAM,MAAM,SAAS;AAC3C,UAAI,IAAI,IAAI,MACR,IAAI,KACJ,IAAI,IAAI;AAEZ,UAAI,KAAK,GAAG,GAAG,CAAC,KAAK,EAAG,QAAO;AAE/B,UAAI,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE;AAG7D,UAAI,KAAK,KAAK,KAAM,KAAK,KAAK,KAAK,KAAO,KAAK,KAAK,KAAK,IACrD,KAAK,KAAK,KAAM,KAAK,KAAK,KAAK,KAAO,KAAK,KAAK,KAAK,IACrD,KAAK,KAAK,KAAM,KAAK,KAAK,KAAK,KAAO,KAAK,KAAK,KAAK,IACrD,KAAK,KAAK,KAAM,KAAK,KAAK,KAAK,KAAO,KAAK,KAAK,KAAK;AAGzD,UAAI,OAAO,OAAO,IAAI,IAAI,MAAM,MAAM,OAAO,GACzC,OAAO,OAAO,IAAI,IAAI,MAAM,MAAM,OAAO;AAE7C,UAAI,IAAI,IAAI,OACR,IAAI,IAAI;AAGZ,aAAO,KAAK,EAAE,KAAK,QAAQ,KAAK,EAAE,KAAK,MAAM;AACzC,YAAI,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,MAAM,KAAK,MAAM,KACrE,gBAAgB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE,IAAI,KAAK,EAAG,QAAO;AAC9F,YAAI,EAAE;AAEN,YAAI,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,MAAM,KAAK,MAAM,KACrE,gBAAgB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE,IAAI,KAAK,EAAG,QAAO;AAC9F,YAAI,EAAE;AAAA,MACV;AAGA,aAAO,KAAK,EAAE,KAAK,MAAM;AACrB,YAAI,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,MAAM,KAAK,MAAM,KACrE,gBAAgB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE,IAAI,KAAK,EAAG,QAAO;AAC9F,YAAI,EAAE;AAAA,MACV;AAGA,aAAO,KAAK,EAAE,KAAK,MAAM;AACrB,YAAI,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,MAAM,KAAK,MAAM,KACrE,gBAAgB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE,IAAI,KAAK,EAAG,QAAO;AAC9F,YAAI,EAAE;AAAA,MACV;AAEA,aAAO;AAAA,IACX;AAGA,aAAS,uBAAuB,OAAO,WAAW,KAAK;AACnD,UAAI,IAAI;AACR,SAAG;AACC,YAAI,IAAI,EAAE,MACN,IAAI,EAAE,KAAK;AAEf,YAAI,CAAC,OAAO,GAAG,CAAC,KAAK,WAAW,GAAG,GAAG,EAAE,MAAM,CAAC,KAAK,cAAc,GAAG,CAAC,KAAK,cAAc,GAAG,CAAC,GAAG;AAE5F,oBAAU,KAAK,EAAE,IAAI,MAAM,CAAC;AAC5B,oBAAU,KAAK,EAAE,IAAI,MAAM,CAAC;AAC5B,oBAAU,KAAK,EAAE,IAAI,MAAM,CAAC;AAG5B,qBAAW,CAAC;AACZ,qBAAW,EAAE,IAAI;AAEjB,cAAI,QAAQ;AAAA,QAChB;AACA,YAAI,EAAE;AAAA,MACV,SAAS,MAAM;AAEf,aAAO,aAAa,CAAC;AAAA,IACzB;AAGA,aAAS,YAAY,OAAO,WAAW,KAAK,MAAM,MAAM,SAAS;AAE7D,UAAI,IAAI;AACR,SAAG;AACC,YAAI,IAAI,EAAE,KAAK;AACf,eAAO,MAAM,EAAE,MAAM;AACjB,cAAI,EAAE,MAAM,EAAE,KAAK,gBAAgB,GAAG,CAAC,GAAG;AAEtC,gBAAI,IAAI,aAAa,GAAG,CAAC;AAGzB,gBAAI,aAAa,GAAG,EAAE,IAAI;AAC1B,gBAAI,aAAa,GAAG,EAAE,IAAI;AAG1B,yBAAa,GAAG,WAAW,KAAK,MAAM,MAAM,SAAS,CAAC;AACtD,yBAAa,GAAG,WAAW,KAAK,MAAM,MAAM,SAAS,CAAC;AACtD;AAAA,UACJ;AACA,cAAI,EAAE;AAAA,QACV;AACA,YAAI,EAAE;AAAA,MACV,SAAS,MAAM;AAAA,IACnB;AAGA,aAAS,eAAe,MAAM,aAAa,WAAW,KAAK;AACvD,UAAI,QAAQ,CAAC,GACT,GAAG,KAAK,OAAO,KAAK;AAExB,WAAK,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AAChD,gBAAQ,YAAY,CAAC,IAAI;AACzB,cAAM,IAAI,MAAM,IAAI,YAAY,IAAI,CAAC,IAAI,MAAM,KAAK;AACpD,eAAO,WAAW,MAAM,OAAO,KAAK,KAAK,KAAK;AAC9C,YAAI,SAAS,KAAK,KAAM,MAAK,UAAU;AACvC,cAAM,KAAK,YAAY,IAAI,CAAC;AAAA,MAChC;AAEA,YAAM,KAAK,QAAQ;AAGnB,WAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC/B,oBAAY,cAAc,MAAM,CAAC,GAAG,SAAS;AAAA,MACjD;AAEA,aAAO;AAAA,IACX;AAEA,aAAS,SAAS,GAAG,GAAG;AACpB,aAAO,EAAE,IAAI,EAAE;AAAA,IACnB;AAGA,aAAS,cAAc,MAAM,WAAW;AACpC,UAAI,SAAS,eAAe,MAAM,SAAS;AAC3C,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,MACX;AAEA,UAAI,gBAAgB,aAAa,QAAQ,IAAI;AAG7C,mBAAa,eAAe,cAAc,IAAI;AAC9C,aAAO,aAAa,QAAQ,OAAO,IAAI;AAAA,IAC3C;AAGA,aAAS,eAAe,MAAM,WAAW;AACrC,UAAI,IAAI,WACJ,KAAK,KAAK,GACV,KAAK,KAAK,GACV,KAAK,WACL;AAIJ,SAAG;AACC,YAAI,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,GAAG;AACjD,cAAI,IAAI,EAAE,KAAK,KAAK,EAAE,MAAM,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,KAAK,IAAI,EAAE;AAC5D,cAAI,KAAK,MAAM,IAAI,IAAI;AACnB,iBAAK;AACL,gBAAI,EAAE,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE;AAC3B,gBAAI,MAAM,GAAI,QAAO;AAAA,UACzB;AAAA,QACJ;AACA,YAAI,EAAE;AAAA,MACV,SAAS,MAAM;AAEf,UAAI,CAAC,EAAG,QAAO;AAMf,UAAI,OAAO,GACP,KAAK,EAAE,GACP,KAAK,EAAE,GACP,SAAS,UACT;AAEJ,UAAI;AAEJ,SAAG;AACC,YAAI,MAAM,EAAE,KAAK,EAAE,KAAK,MAAM,OAAO,EAAE,KAC/B,gBAAgB,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG;AAErF,gBAAM,KAAK,IAAI,KAAK,EAAE,CAAC,KAAK,KAAK,EAAE;AAEnC,cAAI,cAAc,GAAG,IAAI,MACpB,MAAM,UAAW,QAAQ,WAAW,EAAE,IAAI,EAAE,KAAM,EAAE,MAAM,EAAE,KAAK,qBAAqB,GAAG,CAAC,KAAO;AAClG,gBAAI;AACJ,qBAAS;AAAA,UACb;AAAA,QACJ;AAEA,YAAI,EAAE;AAAA,MACV,SAAS,MAAM;AAEf,aAAO;AAAA,IACX;AAGA,aAAS,qBAAqB,GAAG,GAAG;AAChC,aAAO,KAAK,EAAE,MAAM,GAAG,EAAE,IAAI,IAAI,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE,IAAI,IAAI;AAAA,IACpE;AAGA,aAAS,WAAW,OAAO,MAAM,MAAM,SAAS;AAC5C,UAAI,IAAI;AACR,SAAG;AACC,YAAI,EAAE,MAAM,EAAG,GAAE,IAAI,OAAO,EAAE,GAAG,EAAE,GAAG,MAAM,MAAM,OAAO;AACzD,UAAE,QAAQ,EAAE;AACZ,UAAE,QAAQ,EAAE;AACZ,YAAI,EAAE;AAAA,MACV,SAAS,MAAM;AAEf,QAAE,MAAM,QAAQ;AAChB,QAAE,QAAQ;AAEV,iBAAW,CAAC;AAAA,IAChB;AAIA,aAAS,WAAW,MAAM;AACtB,UAAI,GAAG,GAAG,GAAG,GAAG,MAAM,WAAW,OAAO,OACpC,SAAS;AAEb,SAAG;AACC,YAAI;AACJ,eAAO;AACP,eAAO;AACP,oBAAY;AAEZ,eAAO,GAAG;AACN;AACA,cAAI;AACJ,kBAAQ;AACR,eAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AACzB;AACA,gBAAI,EAAE;AACN,gBAAI,CAAC,EAAG;AAAA,UACZ;AACA,kBAAQ;AAER,iBAAO,QAAQ,KAAM,QAAQ,KAAK,GAAI;AAElC,gBAAI,UAAU,MAAM,UAAU,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI;AAClD,kBAAI;AACJ,kBAAI,EAAE;AACN;AAAA,YACJ,OAAO;AACH,kBAAI;AACJ,kBAAI,EAAE;AACN;AAAA,YACJ;AAEA,gBAAI,KAAM,MAAK,QAAQ;AAAA,gBAClB,QAAO;AAEZ,cAAE,QAAQ;AACV,mBAAO;AAAA,UACX;AAEA,cAAI;AAAA,QACR;AAEA,aAAK,QAAQ;AACb,kBAAU;AAAA,MAEd,SAAS,YAAY;AAErB,aAAO;AAAA,IACX;AAGA,aAAS,OAAO,GAAG,GAAG,MAAM,MAAM,SAAS;AAEvC,WAAK,IAAI,QAAQ,UAAU;AAC3B,WAAK,IAAI,QAAQ,UAAU;AAE3B,WAAK,IAAK,KAAK,KAAM;AACrB,WAAK,IAAK,KAAK,KAAM;AACrB,WAAK,IAAK,KAAK,KAAM;AACrB,WAAK,IAAK,KAAK,KAAM;AAErB,WAAK,IAAK,KAAK,KAAM;AACrB,WAAK,IAAK,KAAK,KAAM;AACrB,WAAK,IAAK,KAAK,KAAM;AACrB,WAAK,IAAK,KAAK,KAAM;AAErB,aAAO,IAAK,KAAK;AAAA,IACrB;AAGA,aAAS,YAAY,OAAO;AACxB,UAAI,IAAI,OACJ,WAAW;AACf,SAAG;AACC,YAAI,EAAE,IAAI,SAAS,KAAM,EAAE,MAAM,SAAS,KAAK,EAAE,IAAI,SAAS,EAAI,YAAW;AAC7E,YAAI,EAAE;AAAA,MACV,SAAS,MAAM;AAEf,aAAO;AAAA,IACX;AAGA,aAAS,gBAAgB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACrD,cAAQ,KAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,KAAK,QAC1C,KAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,KAAK,QAC1C,KAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,KAAK;AAAA,IACtD;AAGA,aAAS,gBAAgB,GAAG,GAAG;AAC3B,aAAO,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,CAAC,kBAAkB,GAAG,CAAC;AAAA,OAC9D,cAAc,GAAG,CAAC,KAAK,cAAc,GAAG,CAAC,KAAK,aAAa,GAAG,CAAC;AAAA,OAC9D,KAAK,EAAE,MAAM,GAAG,EAAE,IAAI,KAAK,KAAK,GAAG,EAAE,MAAM,CAAC;AAAA,MAC7C,OAAO,GAAG,CAAC,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE,IAAI,IAAI,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE,IAAI,IAAI;AAAA,IACrF;AAGA,aAAS,KAAK,GAAG,GAAG,GAAG;AACnB,cAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;AAAA,IAC9D;AAGA,aAAS,OAAO,IAAI,IAAI;AACpB,aAAO,GAAG,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG;AAAA,IACxC;AAGA,aAAS,WAAW,IAAI,IAAI,IAAI,IAAI;AAChC,UAAI,KAAK,KAAK,KAAK,IAAI,IAAI,EAAE,CAAC;AAC9B,UAAI,KAAK,KAAK,KAAK,IAAI,IAAI,EAAE,CAAC;AAC9B,UAAI,KAAK,KAAK,KAAK,IAAI,IAAI,EAAE,CAAC;AAC9B,UAAI,KAAK,KAAK,KAAK,IAAI,IAAI,EAAE,CAAC;AAE9B,UAAI,OAAO,MAAM,OAAO,GAAI,QAAO;AAEnC,UAAI,OAAO,KAAK,UAAU,IAAI,IAAI,EAAE,EAAG,QAAO;AAC9C,UAAI,OAAO,KAAK,UAAU,IAAI,IAAI,EAAE,EAAG,QAAO;AAC9C,UAAI,OAAO,KAAK,UAAU,IAAI,IAAI,EAAE,EAAG,QAAO;AAC9C,UAAI,OAAO,KAAK,UAAU,IAAI,IAAI,EAAE,EAAG,QAAO;AAE9C,aAAO;AAAA,IACX;AAGA,aAAS,UAAU,GAAG,GAAG,GAAG;AACxB,aAAO,EAAE,KAAK,KAAK,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,EAAE,GAAG,EAAE,CAAC;AAAA,IAC1H;AAEA,aAAS,KAAK,KAAK;AACf,aAAO,MAAM,IAAI,IAAI,MAAM,IAAI,KAAK;AAAA,IACxC;AAGA,aAAS,kBAAkB,GAAG,GAAG;AAC7B,UAAI,IAAI;AACR,SAAG;AACC,YAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,MAAM,EAAE,KAC7D,WAAW,GAAG,EAAE,MAAM,GAAG,CAAC,EAAG,QAAO;AAC5C,YAAI,EAAE;AAAA,MACV,SAAS,MAAM;AAEf,aAAO;AAAA,IACX;AAGA,aAAS,cAAc,GAAG,GAAG;AACzB,aAAO,KAAK,EAAE,MAAM,GAAG,EAAE,IAAI,IAAI,IAC7B,KAAK,GAAG,GAAG,EAAE,IAAI,KAAK,KAAK,KAAK,GAAG,EAAE,MAAM,CAAC,KAAK,IACjD,KAAK,GAAG,GAAG,EAAE,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,MAAM,CAAC,IAAI;AAAA,IACvD;AAGA,aAAS,aAAa,GAAG,GAAG;AACxB,UAAI,IAAI,GACJ,SAAS,OACT,MAAM,EAAE,IAAI,EAAE,KAAK,GACnB,MAAM,EAAE,IAAI,EAAE,KAAK;AACvB,SAAG;AACC,YAAM,EAAE,IAAI,OAAS,EAAE,KAAK,IAAI,MAAQ,EAAE,KAAK,MAAM,EAAE,KAC9C,MAAM,EAAE,KAAK,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE,KAAK,IAAI,EAAE,KAAK,EAAE;AAC/D,mBAAS,CAAC;AACd,YAAI,EAAE;AAAA,MACV,SAAS,MAAM;AAEf,aAAO;AAAA,IACX;AAIA,aAAS,aAAa,GAAG,GAAG;AACxB,UAAI,KAAK,IAAI,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,GAC3B,KAAK,IAAI,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,GAC3B,KAAK,EAAE,MACP,KAAK,EAAE;AAEX,QAAE,OAAO;AACT,QAAE,OAAO;AAET,SAAG,OAAO;AACV,SAAG,OAAO;AAEV,SAAG,OAAO;AACV,SAAG,OAAO;AAEV,SAAG,OAAO;AACV,SAAG,OAAO;AAEV,aAAO;AAAA,IACX;AAGA,aAAS,WAAW,GAAG,GAAG,GAAG,MAAM;AAC/B,UAAI,IAAI,IAAI,KAAK,GAAG,GAAG,CAAC;AAExB,UAAI,CAAC,MAAM;AACP,UAAE,OAAO;AACT,UAAE,OAAO;AAAA,MAEb,OAAO;AACH,UAAE,OAAO,KAAK;AACd,UAAE,OAAO;AACT,aAAK,KAAK,OAAO;AACjB,aAAK,OAAO;AAAA,MAChB;AACA,aAAO;AAAA,IACX;AAEA,aAAS,WAAW,GAAG;AACnB,QAAE,KAAK,OAAO,EAAE;AAChB,QAAE,KAAK,OAAO,EAAE;AAEhB,UAAI,EAAE,MAAO,GAAE,MAAM,QAAQ,EAAE;AAC/B,UAAI,EAAE,MAAO,GAAE,MAAM,QAAQ,EAAE;AAAA,IACnC;AAEA,aAAS,KAAK,GAAG,GAAG,GAAG;AAEnB,WAAK,IAAI;AAGT,WAAK,IAAI;AACT,WAAK,IAAI;AAGT,WAAK,OAAO;AACZ,WAAK,OAAO;AAGZ,WAAK,IAAI;AAGT,WAAK,QAAQ;AACb,WAAK,QAAQ;AAGb,WAAK,UAAU;AAAA,IACnB;AAIA,WAAO,YAAY,SAAU,MAAM,aAAa,KAAK,WAAW;AAC5D,UAAI,WAAW,eAAe,YAAY;AAC1C,UAAI,WAAW,WAAW,YAAY,CAAC,IAAI,MAAM,KAAK;AAEtD,UAAI,cAAc,KAAK,IAAI,WAAW,MAAM,GAAG,UAAU,GAAG,CAAC;AAC7D,UAAI,UAAU;AACV,iBAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACpD,cAAI,QAAQ,YAAY,CAAC,IAAI;AAC7B,cAAI,MAAM,IAAI,MAAM,IAAI,YAAY,IAAI,CAAC,IAAI,MAAM,KAAK;AACxD,yBAAe,KAAK,IAAI,WAAW,MAAM,OAAO,KAAK,GAAG,CAAC;AAAA,QAC7D;AAAA,MACJ;AAEA,UAAI,gBAAgB;AACpB,WAAK,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK,GAAG;AACtC,YAAI,IAAI,UAAU,CAAC,IAAI;AACvB,YAAI,IAAI,UAAU,IAAI,CAAC,IAAI;AAC3B,YAAI,IAAI,UAAU,IAAI,CAAC,IAAI;AAC3B,yBAAiB,KAAK;AAAA,WACjB,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,MAC9C,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AAAA,QAAE;AAAA,MACzD;AAEA,aAAO,gBAAgB,KAAK,kBAAkB,IAAI,IAC9C,KAAK,KAAK,gBAAgB,eAAe,WAAW;AAAA,IAC5D;AAEA,aAAS,WAAW,MAAM,OAAO,KAAK,KAAK;AACvC,UAAI,MAAM;AACV,eAAS,IAAI,OAAO,IAAI,MAAM,KAAK,IAAI,KAAK,KAAK,KAAK;AAClD,gBAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AACtD,YAAI;AAAA,MACR;AACA,aAAO;AAAA,IACX;AAGA,WAAO,UAAU,SAAU,MAAM;AAC7B,UAAI,MAAM,KAAK,CAAC,EAAE,CAAC,EAAE,QACjB,SAAS,EAAC,UAAU,CAAC,GAAG,OAAO,CAAC,GAAG,YAAY,IAAG,GAClD,YAAY;AAEhB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,iBAAS,IAAI,GAAG,IAAI,KAAK,CAAC,EAAE,QAAQ,KAAK;AACrC,mBAAS,IAAI,GAAG,IAAI,KAAK,IAAK,QAAO,SAAS,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,QACpE;AACA,YAAI,IAAI,GAAG;AACP,uBAAa,KAAK,IAAI,CAAC,EAAE;AACzB,iBAAO,MAAM,KAAK,SAAS;AAAA,QAC/B;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA;AAAA;", "names": []}