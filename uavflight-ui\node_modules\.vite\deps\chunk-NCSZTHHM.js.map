{"version": 3, "sources": ["../../ol/array.js"], "sourcesContent": ["/**\n * @module ol/array\n */\n\n/**\n * Performs a binary search on the provided sorted list and returns the index of the item if found. If it can't be found it'll return -1.\n * https://github.com/darkskyapp/binary-search\n *\n * @param {Array<*>} haystack Items to search through.\n * @param {*} needle The item to look for.\n * @param {Function} [comparator] Comparator function.\n * @return {number} The index of the item if found, -1 if not.\n */\nexport function binarySearch(haystack, needle, comparator) {\n  let mid, cmp;\n  comparator = comparator || ascending;\n  let low = 0;\n  let high = haystack.length;\n  let found = false;\n\n  while (low < high) {\n    /* Note that \"(low + high) >>> 1\" may overflow, and results in a typecast\n     * to double (which gives the wrong results). */\n    mid = low + ((high - low) >> 1);\n    cmp = +comparator(haystack[mid], needle);\n\n    if (cmp < 0.0) {\n      /* Too low. */\n      low = mid + 1;\n    } else {\n      /* Key found or too high */\n      high = mid;\n      found = !cmp;\n    }\n  }\n\n  /* Key not found. */\n  return found ? low : ~low;\n}\n\n/**\n * Compare function sorting arrays in ascending order.  Safe to use for numeric values.\n * @param {*} a The first object to be compared.\n * @param {*} b The second object to be compared.\n * @return {number} A negative number, zero, or a positive number as the first\n *     argument is less than, equal to, or greater than the second.\n */\nexport function ascending(a, b) {\n  return a > b ? 1 : a < b ? -1 : 0;\n}\n\n/**\n * Compare function sorting arrays in descending order.  Safe to use for numeric values.\n * @param {*} a The first object to be compared.\n * @param {*} b The second object to be compared.\n * @return {number} A negative number, zero, or a positive number as the first\n *     argument is greater than, equal to, or less than the second.\n */\nexport function descending(a, b) {\n  return a < b ? 1 : a > b ? -1 : 0;\n}\n\n/**\n * {@link module:ol/tilegrid/TileGrid~TileGrid#getZForResolution} can use a function\n * of this type to determine which nearest resolution to use.\n *\n * This function takes a `{number}` representing a value between two array entries,\n * a `{number}` representing the value of the nearest higher entry and\n * a `{number}` representing the value of the nearest lower entry\n * as arguments and returns a `{number}`. If a negative number or zero is returned\n * the lower value will be used, if a positive number is returned the higher value\n * will be used.\n * @typedef {function(number, number, number): number} NearestDirectionFunction\n * @api\n */\n\n/**\n * @param {Array<number>} arr Array in descending order.\n * @param {number} target Target.\n * @param {number|NearestDirectionFunction} direction\n *    0 means return the nearest,\n *    > 0 means return the largest nearest,\n *    < 0 means return the smallest nearest.\n * @return {number} Index.\n */\nexport function linearFindNearest(arr, target, direction) {\n  if (arr[0] <= target) {\n    return 0;\n  }\n\n  const n = arr.length;\n  if (target <= arr[n - 1]) {\n    return n - 1;\n  }\n\n  if (typeof direction === 'function') {\n    for (let i = 1; i < n; ++i) {\n      const candidate = arr[i];\n      if (candidate === target) {\n        return i;\n      }\n      if (candidate < target) {\n        if (direction(target, arr[i - 1], candidate) > 0) {\n          return i - 1;\n        }\n        return i;\n      }\n    }\n    return n - 1;\n  }\n\n  if (direction > 0) {\n    for (let i = 1; i < n; ++i) {\n      if (arr[i] < target) {\n        return i - 1;\n      }\n    }\n    return n - 1;\n  }\n\n  if (direction < 0) {\n    for (let i = 1; i < n; ++i) {\n      if (arr[i] <= target) {\n        return i;\n      }\n    }\n    return n - 1;\n  }\n\n  for (let i = 1; i < n; ++i) {\n    if (arr[i] == target) {\n      return i;\n    }\n    if (arr[i] < target) {\n      if (arr[i - 1] - target < target - arr[i]) {\n        return i - 1;\n      }\n      return i;\n    }\n  }\n  return n - 1;\n}\n\n/**\n * @param {Array<*>} arr Array.\n * @param {number} begin Begin index.\n * @param {number} end End index.\n */\nexport function reverseSubArray(arr, begin, end) {\n  while (begin < end) {\n    const tmp = arr[begin];\n    arr[begin] = arr[end];\n    arr[end] = tmp;\n    ++begin;\n    --end;\n  }\n}\n\n/**\n * @param {Array<VALUE>} arr The array to modify.\n * @param {!Array<VALUE>|VALUE} data The elements or arrays of elements to add to arr.\n * @template VALUE\n */\nexport function extend(arr, data) {\n  const extension = Array.isArray(data) ? data : [data];\n  const length = extension.length;\n  for (let i = 0; i < length; i++) {\n    arr[arr.length] = extension[i];\n  }\n}\n\n/**\n * @param {Array<VALUE>} arr The array to modify.\n * @param {VALUE} obj The element to remove.\n * @template VALUE\n * @return {boolean} If the element was removed.\n */\nexport function remove(arr, obj) {\n  const i = arr.indexOf(obj);\n  const found = i > -1;\n  if (found) {\n    arr.splice(i, 1);\n  }\n  return found;\n}\n\n/**\n * @param {Array|Uint8ClampedArray} arr1 The first array to compare.\n * @param {Array|Uint8ClampedArray} arr2 The second array to compare.\n * @return {boolean} Whether the two arrays are equal.\n */\nexport function equals(arr1, arr2) {\n  const len1 = arr1.length;\n  if (len1 !== arr2.length) {\n    return false;\n  }\n  for (let i = 0; i < len1; i++) {\n    if (arr1[i] !== arr2[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Sort the passed array such that the relative order of equal elements is preserved.\n * See https://en.wikipedia.org/wiki/Sorting_algorithm#Stability for details.\n * @param {Array<*>} arr The array to sort (modifies original).\n * @param {!function(*, *): number} compareFnc Comparison function.\n * @api\n */\nexport function stableSort(arr, compareFnc) {\n  const length = arr.length;\n  const tmp = Array(arr.length);\n  let i;\n  for (i = 0; i < length; i++) {\n    tmp[i] = {index: i, value: arr[i]};\n  }\n  tmp.sort(function (a, b) {\n    return compareFnc(a.value, b.value) || a.index - b.index;\n  });\n  for (i = 0; i < arr.length; i++) {\n    arr[i] = tmp[i].value;\n  }\n}\n\n/**\n * @param {Array<*>} arr The array to test.\n * @param {Function} [func] Comparison function.\n * @param {boolean} [strict] Strictly sorted (default false).\n * @return {boolean} Return index.\n */\nexport function isSorted(arr, func, strict) {\n  const compare = func || ascending;\n  return arr.every(function (currentVal, index) {\n    if (index === 0) {\n      return true;\n    }\n    const res = compare(arr[index - 1], currentVal);\n    return !(res > 0 || (strict && res === 0));\n  });\n}\n"], "mappings": ";AAaO,SAAS,aAAa,UAAU,QAAQ,YAAY;AACzD,MAAI,KAAK;AACT,eAAa,cAAc;AAC3B,MAAI,MAAM;AACV,MAAI,OAAO,SAAS;AACpB,MAAI,QAAQ;AAEZ,SAAO,MAAM,MAAM;AAGjB,UAAM,OAAQ,OAAO,OAAQ;AAC7B,UAAM,CAAC,WAAW,SAAS,GAAG,GAAG,MAAM;AAEvC,QAAI,MAAM,GAAK;AAEb,YAAM,MAAM;AAAA,IACd,OAAO;AAEL,aAAO;AACP,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AAGA,SAAO,QAAQ,MAAM,CAAC;AACxB;AASO,SAAS,UAAU,GAAG,GAAG;AAC9B,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AAClC;AASO,SAAS,WAAW,GAAG,GAAG;AAC/B,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AAClC;AAyBO,SAAS,kBAAkB,KAAK,QAAQ,WAAW;AACxD,MAAI,IAAI,CAAC,KAAK,QAAQ;AACpB,WAAO;AAAA,EACT;AAEA,QAAM,IAAI,IAAI;AACd,MAAI,UAAU,IAAI,IAAI,CAAC,GAAG;AACxB,WAAO,IAAI;AAAA,EACb;AAEA,MAAI,OAAO,cAAc,YAAY;AACnC,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,YAAM,YAAY,IAAI,CAAC;AACvB,UAAI,cAAc,QAAQ;AACxB,eAAO;AAAA,MACT;AACA,UAAI,YAAY,QAAQ;AACtB,YAAI,UAAU,QAAQ,IAAI,IAAI,CAAC,GAAG,SAAS,IAAI,GAAG;AAChD,iBAAO,IAAI;AAAA,QACb;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,IAAI;AAAA,EACb;AAEA,MAAI,YAAY,GAAG;AACjB,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAI,IAAI,CAAC,IAAI,QAAQ;AACnB,eAAO,IAAI;AAAA,MACb;AAAA,IACF;AACA,WAAO,IAAI;AAAA,EACb;AAEA,MAAI,YAAY,GAAG;AACjB,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAI,IAAI,CAAC,KAAK,QAAQ;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,IAAI;AAAA,EACb;AAEA,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,QAAI,IAAI,CAAC,KAAK,QAAQ;AACpB,aAAO;AAAA,IACT;AACA,QAAI,IAAI,CAAC,IAAI,QAAQ;AACnB,UAAI,IAAI,IAAI,CAAC,IAAI,SAAS,SAAS,IAAI,CAAC,GAAG;AACzC,eAAO,IAAI;AAAA,MACb;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,IAAI;AACb;AAOO,SAAS,gBAAgB,KAAK,OAAO,KAAK;AAC/C,SAAO,QAAQ,KAAK;AAClB,UAAM,MAAM,IAAI,KAAK;AACrB,QAAI,KAAK,IAAI,IAAI,GAAG;AACpB,QAAI,GAAG,IAAI;AACX,MAAE;AACF,MAAE;AAAA,EACJ;AACF;AAOO,SAAS,OAAO,KAAK,MAAM;AAChC,QAAM,YAAY,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AACpD,QAAM,SAAS,UAAU;AACzB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,QAAI,IAAI,MAAM,IAAI,UAAU,CAAC;AAAA,EAC/B;AACF;AAsBO,SAAS,OAAO,MAAM,MAAM;AACjC,QAAM,OAAO,KAAK;AAClB,MAAI,SAAS,KAAK,QAAQ;AACxB,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,QAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AA8BO,SAAS,SAAS,KAAK,MAAM,QAAQ;AAC1C,QAAM,UAAU,QAAQ;AACxB,SAAO,IAAI,MAAM,SAAU,YAAY,OAAO;AAC5C,QAAI,UAAU,GAAG;AACf,aAAO;AAAA,IACT;AACA,UAAM,MAAM,QAAQ,IAAI,QAAQ,CAAC,GAAG,UAAU;AAC9C,WAAO,EAAE,MAAM,KAAM,UAAU,QAAQ;AAAA,EACzC,CAAC;AACH;", "names": []}