<!--
 * @Author: AI Assistant 
 * @Date: 2025-07-17 
 * @Description: 快拼图层查询结果弹窗组件
 *  
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved. 
-->
<template>
  <teleport to="body">
    <div v-if="visible" class="custom-layer-query-dialog" :style="{left: dialogPosition.x, top: dialogPosition.y}">
      <div class="dialog-header" ref="dialogHeader" @mousedown="handleHeaderMouseDown">
        <span class="dialog-title">图层查询结果</span>
        <el-icon class="dialog-close" @click="closeDialog"><Close /></el-icon>
      </div>
      
      <div class="dialog-body">
        <div v-if="status === 'loading'" class="query-loading">
          <el-icon class="is-loading"><Loading /></el-icon>
          <p>正在查询图层，请稍候...</p>
        </div>
        
        <div v-else-if="status === 'error'" class="query-error">
          <el-icon><CircleClose /></el-icon>
          <p>查询失败: {{ error }}</p>
          <el-button type="primary" @click="retryQuery">重试</el-button>
        </div>
        
        <div v-else-if="status === 'success'" class="query-result">
          <!-- 显示查询点经纬度 -->
          <div class="query-location">
            <span>查询位置: 经度 {{ queryLocation.longitude.toFixed(6) }}, 纬度 {{ queryLocation.latitude.toFixed(6) }}</span>
          </div>

          <div v-if="queryLayers.length === 0" class="no-layers-found">
            <p>该位置未找到图层</p>
          </div>
          <div v-else class="layers-found">
            <p class="result-summary">共找到 {{ queryLayers.length }} 个图层</p>
            
            <el-table :data="queryLayers" style="width: 100%" size="small">
              <el-table-column width="50">
                <template #default="scope">
                  <el-checkbox 
                    v-model="scope.row.selected" 
                    @change="handleLayerCheckboxChange(scope.row, $event)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="名称">
                <template #default="scope">
                  {{ getLayerName(scope.row.id) }}
                </template>
              </el-table-column>
              <el-table-column label="主题" width="100">
                <template #default="scope">
                  {{ getLayerTheme(scope.row.id) || '未分类' }}
                </template>
              </el-table-column>
            </el-table>
            
            <div class="dialog-footer">
              <el-button size="small" @click="closeDialog">关闭</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, defineProps, defineEmits, watch } from 'vue';
import { Close, Loading, CircleClose } from '@element-plus/icons-vue';
import { useHDMapLayerStore } from '/@/stores/hdMapLayer';
import { ElMessage } from 'element-plus';

// 导入Store
const hdMapLayerStore = useHDMapLayerStore();

// 定义Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    default: 'idle' // idle, loading, success, error
  },
  queryLayers: {
    type: Array,
    default: () => []
  },
  error: {
    type: String,
    default: ''
  },
  queryLocation: {
    type: Object,
    default: () => ({ longitude: 0, latitude: 0 })
  }
});

// 定义事件
const emit = defineEmits(['close', 'retry', 'toggle-layer']);

// 弹窗位置控制
const dialogPosition = reactive({ x: 'calc(100% - 550px)', y: '100px' });
const isDragging = ref(false);
const dragOffset = reactive({ x: 0, y: 0 });
const dialogHeader = ref(null);

// 关闭弹窗
const closeDialog = () => {
  emit('close');
};

// 重试查询
const retryQuery = () => {
  emit('retry');
};

// 处理图层勾选状态变化
const handleLayerCheckboxChange = (layer, checked) => {
  emit('toggle-layer', { layer, checked });
};

// 获取图层名称
const getLayerName = (layerId) => {
  const configLayers = hdMapLayerStore.mapConfig?.layers || [];
  const layer = configLayers.find(layer => layer.id === layerId);
  return layer?.name || layerId;
};

// 获取图层主题
const getLayerTheme = (layerId) => {
  const configLayers = hdMapLayerStore.mapConfig?.layers || [];
  const layer = configLayers.find(layer => layer.id === layerId);
  return layer?.theme || '';
};

// 弹窗拖拽相关方法
function handleHeaderMouseDown(e) {
  isDragging.value = true;
  // 计算鼠标点击位置与弹窗左上角的偏移量
  const rect = e.currentTarget.getBoundingClientRect();
  dragOffset.x = e.clientX - rect.left;
  dragOffset.y = e.clientY - rect.top;
  
  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
}

function handleMouseMove(e) {
  if (isDragging.value) {
    // 计算新位置 (使用px以避免计算误差)
    const newX = `${e.clientX - dragOffset.x}px`;
    const newY = `${e.clientY - dragOffset.y}px`;
    
    // 更新弹窗位置
    dialogPosition.x = newX;
    dialogPosition.y = newY;
  }
}

function handleMouseUp() {
  isDragging.value = false;
  // 移除全局事件监听
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
}

// 在组件卸载前清除事件监听器
onBeforeUnmount(() => {
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
});

// 组件初始化
onMounted(() => {
  // 如果初始可见，确保弹窗显示在视口内
  if (props.visible) {
    // 可以在此添加初始化逻辑
  }
});
</script>

<style lang="scss" scoped>
.custom-layer-query-dialog {
  position: fixed;
  width: 450px;
  background-color: #1a2133;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.5);
  color: white;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-height: 80vh;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    background-color: rgb(25, 48, 76);
    cursor: move;
    user-select: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .dialog-title {
      font-size: 15px;
      font-weight: bold;
      color: white;
    }

    .dialog-close {
      font-size: 18px;
      color: rgba(255, 255, 255, 0.8);
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: white;
      }
    }
  }

  .dialog-body {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
    background-color: #1a2133;
    text-align: left;
    color: white;
    max-height: calc(80vh - 40px);
  }
  
  .query-loading, .query-error, .no-layers-found {
    text-align: center;
    padding: 15px 0;
    
    .el-icon {
      font-size: 24px;
      margin-bottom: 8px;
    }
  }
  
  .query-error {
    .el-icon {
      color: #f56c6c;
    }
  }
  
  .query-location {
    background-color: rgba(25, 48, 76, 0.3);
    padding: 6px 10px;
    border-radius: 3px;
    margin-bottom: 10px;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.9);
    font-family: monospace;
  }
  
  .result-summary {
    font-weight: bold;
    margin: 8px 0;
    font-size: 13px;
  }
  
  .loaded-tag {
    margin: 0 auto;
    display: block;
    text-align: center;
  }
  
  .dialog-footer {
    margin-top: 12px;
    display: flex;
    justify-content: flex-end;
  }
  
  :deep(.el-table) {
    background-color: transparent;
    color: white;
    font-size: 13px;
    
    .el-table__header th {
      background-color: rgba(25, 48, 76, 0.5);
      color: white;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding: 6px 0;
      font-size: 13px;
      font-weight: normal;
    }
    
    .el-table__row {
      background-color: transparent;
      
      &:hover > td {
        background-color: rgba(255, 255, 255, 0.1) !important;
      }
      
      td {
        background-color: transparent;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        padding: 4px 0;
      }
    }
    
    .el-checkbox__inner {
      border-color: rgba(255, 255, 255, 0.5);
    }
  }
}
</style> 