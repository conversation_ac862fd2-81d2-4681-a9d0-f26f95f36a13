<!--
 * @file index.vue
 * @description 大屏展示页面，提供地图显示和图层管理功能
 * 
 * 该页面是系统的主要展示界面，包含以下功能：
 * - OpenLayers地图的初始化和显示
 * - 顶部导航栏，包含系统标题和控制按钮
 * - 图层管理面板的集成，用于控制地图图层
 * - 响应式布局，适应不同屏幕尺寸
 * - 要素选择功能，支持选中线和面要素
 * - 属性展示面板，显示选中要素的属性信息
 * 
 * 页面在加载时会初始化地图，并通过图层管理器加载配置的图层。
 * 用户可以通过左侧的图层管理按钮打开图层控制面板，管理地图上的各种图层。
 * 
 * <AUTHOR>
 * @date 2025-05-19
 -->
<template>
	<div class="divApp">
		<div class="dt" id="EarthContainer" ref="EarthContainer"></div>
		<nav class="nav-bar">
			<!-- 系统标题区域 -->
			<div class="system-title">
				<div class="decor-line"></div>
				<span class="title-text">苍梧县设施农用地智慧选址系统</span>
				<div class="decor-line"></div>
			</div>
			
			<!-- 导航栏按钮区域 -->
			<div class="nav-actions">
				<el-button type="success" @click="toggleFacilityLandList">
					<el-icon><List /></el-icon>
					<span>设施农用地</span>
				</el-button>
				
				<el-button type="success" @click="togglePlantingAreaList">
					<el-icon><List /></el-icon>
					<span>种植区</span>
				</el-button>
				
				<el-button type="success" @click="toggleInvestmentAreaList">
					<el-icon><List /></el-icon>
					<span>招商引资片区</span>
					</el-button>
				
				<feature-selector
					v-bind:map="mapStore.map?.map"
					@feature-selected="handleFeatureSelected"
					@feature-deselected="handleFeatureDeselected"
					ref="featureSelectorRef"
				/>
			</div>
		</nav>
		
		<!-- 左侧图层管理面板 -->
		<div class="layer-panel" :class="{ 'collapsed': isLayerPanelCollapsed }">
			<div class="panel-header">
				<div class="title-container">
					<span class="panel-title">图层管理</span>
				</div>
				<el-button type="primary" size="small" class="collapse-button" @click="toggleLayerPanel">
					<el-icon><component :is="isLayerPanelCollapsed ? 'Expand' : 'Fold'" /></el-icon>
				</el-button>
			</div>
			<layer-manager 
				:visible="true"
				position="left"
				@initialized="handleLayerInitialized"
				ref="layerManagerRef"
			/>
		</div>
		
		<!-- 右侧属性展示面板 -->
		<div class="attribute-panel" v-show="selectedFeature">
			<div class="panel-header">
				<span class="panel-title">属性信息</span>
				<el-button type="danger" size="small" class="close-button" @click="closeAttributePanel">
					<el-icon><Close /></el-icon>
				</el-button>
			</div>
			<div class="attribute-content">
				<div v-if="!selectedFeature" class="empty-attribute-message">
					请选择要素查看属性
				</div>
				<template v-else>
					<div class="feature-type">
						<span class="type-label">几何类型:</span>
						<span class="type-value">{{ selectedFeatureType }}</span>
					</div>
					<el-table 
						:data="attributeTableData" 
						border 
						stripe 
						style="width: 100%"
						:header-cell-style="{ background: 'rgba(42, 119, 41, 0.7)', color: 'white' }"
					>
						<el-table-column prop="key" label="属性名" width="120" />
						<el-table-column prop="value" label="属性值" />
					</el-table>
				</template>
			</div>
		</div>
		
		<!-- 功能面板容器 -->
		<div class="feature-panels-container">
			<!-- 设施农用地要素列表面板 -->
			<div v-if="showFacilityLandList" class="feature-list-panel" :class="{ 'collapsed': isFeaturePanelCollapsed }">
				<facility-land-feature-list 
					ref="facilityLandListRef" 
					:map="mapStore.map?.map" 
					layerName="招商引资片区交设施农用地"
					@close="closeFacilityLandList"
					@layer-loaded="handleLayerLoaded"
					@toggle-collapse="handleFeaturePanelCollapse"
				/>
			</div>
			
			<!-- 种植区要素列表面板 -->
			<div v-if="showPlantingAreaList" class="feature-list-panel" :class="{ 'collapsed': isFeaturePanelCollapsed }">
				<planting-area-feature-list 
					ref="plantingAreaListRef" 
					:map="mapStore.map?.map" 
					layerName="招商引资片区交种植区"
					@close="closePlantingAreaList"
					@layer-loaded="handleLayerLoaded"
					@toggle-collapse="handleFeaturePanelCollapse"
				/>
			</div>
			
			<!-- 招商引资片区要素列表面板 -->
			<div v-if="showInvestmentAreaList" class="feature-list-panel" :class="{ 'collapsed': isFeaturePanelCollapsed }">
				<investment-area-feature-list 
					ref="investmentAreaListRef" 
					:map="mapStore.map?.map" 
					layerName="招商引资片区"
					@close="closeInvestmentAreaList"
					@layer-loaded="handleLayerLoaded"
					@toggle-collapse="handleFeaturePanelCollapse"
				/>
			</div>
		</div>
	</div>
</template>

<script lang="ts" name="bigScreenIndex" setup>
import { NextLoading } from '/@/utils/loading';
import { onMounted, ref, onBeforeUnmount, computed } from 'vue';
import { useOLMapStore } from '/@/stores/olMapStore';
import { useMapLayerManagerStore } from '/@/stores/mapLayer/mapLayerManager';
import { LayerManager, FeatureSelector, FacilityLandFeatureList, PlantingAreaFeatureList, InvestmentAreaFeatureList } from '/@/components';
import { List, Close, Expand, Fold, Menu } from '@element-plus/icons-vue';

// 使用OpenLayers地图存储
const mapStore = useOLMapStore();
const mapLayerManager = useMapLayerManagerStore();
const EarthContainer = ref(null);

// 图层管理面板引用
const layerManagerRef = ref<InstanceType<typeof LayerManager> | null>(null);

// 选中要素状态
const selectedFeature = ref<any>(null);
const selectedFeatureType = ref('');

// 设施农用地要素列表显示状态
const showFacilityLandList = ref(false);

// 种植区要素列表显示状态
const showPlantingAreaList = ref(false);

// 招商引资片区要素列表显示状态
const showInvestmentAreaList = ref(false);

// 引用设施农用地List组件
const facilityLandListRef = ref<{ clearHighlight: () => void } | null>(null);

// 引用种植区List组件
const plantingAreaListRef = ref<{ clearHighlight: () => void } | null>(null);

// 引用招商引资片区List组件
const investmentAreaListRef = ref<{ clearHighlight: () => void } | null>(null);

// 添加对 FeatureSelector 组件的引用
const featureSelectorRef = ref<{ clearSelectedFeatures: () => boolean } | null>(null);

// 字段别名映射
const fieldAliasMap: Record<string, string> = {
	'XZMC': '乡镇名称',
	'XZCMC': '行政村名称',
	'GNFQ': '功能分区',
	'MJFL': '面积分类',
	'PJDJ': '评价等级',
	'PJFZ': '评价分值',
	'YJDL': '一级地类',
	'DLBM': '地类编码',
	'DLMC': '地类名称',
	'PDJB': '坡度级别',
	'JTQK': '交通情况',
	'SYJL': '水源距离',
	'DYQK': '电源情况',
	'DKBH': '地块编号',
	'MJ': '面积（亩）',
	'BZ': '备注'
};

// 获取字段别名，如果没有映射则返回原字段名
function getFieldAlias(fieldName: string): string {
	return fieldAliasMap[fieldName] || fieldName;
}

// 属性表格数据
const attributeTableData = computed(() => {
	if (!selectedFeature.value) return [];
	
	const properties = selectedFeature.value.getProperties();
	const data = [];
	
	// 过滤掉几何属性和内部属性
	for (const key in properties) {
		if (key !== 'geometry' && !key.startsWith('_')) {
			data.push({
				key: getFieldAlias(key),
				value: String(properties[key])
			});
		}
	}
	
	return data;
});

// 切换设施农用地要素列表显示状态
function toggleFacilityLandList() {
	// 如果其他面板已打开，先关闭
	closePlantingAreaList();
	closeInvestmentAreaList();
	
	// 如果功能面板处于收缩状态，先展开
	if (isFeaturePanelCollapsed.value) {
		isFeaturePanelCollapsed.value = false;
	}
	
	// 然后打开此面板
	showFacilityLandList.value = true;
}

// 切换种植区要素列表显示状态
function togglePlantingAreaList() {
	// 如果其他面板已打开，先关闭
	closeFacilityLandList();
	closeInvestmentAreaList();
	
	// 如果功能面板处于收缩状态，先展开
	if (isFeaturePanelCollapsed.value) {
		isFeaturePanelCollapsed.value = false;
	}
	
	// 然后打开此面板
	showPlantingAreaList.value = true;
}

// 切换招商引资片区要素列表显示状态
function toggleInvestmentAreaList() {
	// 如果其他面板已打开，先关闭
	closeFacilityLandList();
	closePlantingAreaList();
	
	// 如果功能面板处于收缩状态，先展开
	if (isFeaturePanelCollapsed.value) {
		isFeaturePanelCollapsed.value = false;
	}
	
	// 然后打开此面板
	showInvestmentAreaList.value = true;
}

// 处理要素选中事件
function handleFeatureSelected(feature: any) {
	selectedFeature.value = feature;
	const geometry = feature.getGeometry();
	selectedFeatureType.value = geometry ? geometry.getType() : 'unknown';
	
	// 输出要素属性，便于调试
	console.log('选中要素属性:', feature.getProperties());
}

// 处理要素取消选中事件
function handleFeatureDeselected() {
	selectedFeature.value = null;
	selectedFeatureType.value = '';
}

// 处理图层初始化完成事件
function handleLayerInitialized(count: number) {
	console.log(`图层管理器已初始化，共加载 ${count} 个图层`);
}

// 关闭设施农用地要素列表面板
function closeFacilityLandList() {
	// 清除高亮
	if (facilityLandListRef.value) {
		facilityLandListRef.value.clearHighlight();
	}
	// 同时清除选中要素
	if (featureSelectorRef.value) {
		featureSelectorRef.value.clearSelectedFeatures();
	}
	showFacilityLandList.value = false;
}

// 关闭种植区要素列表面板
function closePlantingAreaList() {
	// 清除高亮
	if (plantingAreaListRef.value) {
		plantingAreaListRef.value.clearHighlight();
	}
	// 同时清除选中要素
	if (featureSelectorRef.value) {
		featureSelectorRef.value.clearSelectedFeatures();
	}
	showPlantingAreaList.value = false;
}

// 关闭招商引资片区要素列表面板
function closeInvestmentAreaList() {
	// 清除高亮
	if (investmentAreaListRef.value) {
		investmentAreaListRef.value.clearHighlight();
	}
	// 同时清除选中要素
	if (featureSelectorRef.value) {
		featureSelectorRef.value.clearSelectedFeatures();
	}
	showInvestmentAreaList.value = false;
}

// 添加图层加载事件处理函数
const handleLayerLoaded = () => {
	// LayerManager 组件现在会自动响应图层变化，无需手动更新
	console.log('图层已加载，LayerManager 将自动更新');
};

// 关闭属性信息面板
function closeAttributePanel() {
	selectedFeature.value = null;
	selectedFeatureType.value = '';
	// 同时清除选中要素
	if (featureSelectorRef.value) {
		featureSelectorRef.value.clearSelectedFeatures();
	}
}

// 收缩面板状态
const isLayerPanelCollapsed = ref(false);

// 右侧功能面板收缩状态
const isFeaturePanelCollapsed = ref(false);

// 切换面板收缩状态
function toggleLayerPanel() {
	isLayerPanelCollapsed.value = !isLayerPanelCollapsed.value;
}

// 切换右侧功能面板收缩状态
function toggleFeaturePanel() {
	isFeaturePanelCollapsed.value = !isFeaturePanelCollapsed.value;
}

// 处理功能面板收缩事件
function handleFeaturePanelCollapse(collapsed: boolean) {
	isFeaturePanelCollapsed.value = collapsed;
}

// 页面加载时
onMounted(() => {
	NextLoading.done();
	
	// 禁用默认的右键菜单
	document.addEventListener('contextmenu', (e) => {
		e.preventDefault();
	});
	
	// 初始化OpenLayers地图
	if (EarthContainer.value) {
		mapStore.initMap(EarthContainer.value);
		
		// 通过延时确保OpenLayers地图已完全初始化
		setTimeout(() => {
			console.log('开始加载地图配置...');
			
			// 验证地图实例
			if (mapStore.map && mapStore.map.map) {
				console.log('地图实例已就绪，初始化图层管理器...');
				mapLayerManager.loadMapConfigAndInitialize(mapStore.map as any)
					.then(success => {
						if (success) {
							console.log('地图配置和图层加载成功');
							// 图层加载成功后初始化图层列表
							setTimeout(() => {
								if (layerManagerRef.value) {
									console.log('图层列表已初始化');
									// 注意：LayerManager 组件可能没有 initLayerList 方法
									// layerManagerRef.value.initLayerList();
								}
							}, 500);
						} else {
							console.error('地图配置或图层加载失败');
						}
					})
					.catch(error => {
						console.error('加载地图配置出错:', error);
					});
			} else {
				console.error('地图实例未正确初始化，无法加载图层', {
					mapStore: !!mapStore.map,
					olMap: mapStore.map ? !!mapStore.map.map : false
				});
			}
		}, 500);
	}
});
</script>

<style scoped>
.divApp {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
	font-family: 'DingTalk';
	position: relative;
}

/* 顶部导航栏 */
.nav-bar {
	height: 10vh;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 20px;
	background-color: rgba(2, 49, 52, 0.8);
	position: relative;
	z-index: 2;
}

/* 系统标题 */
.system-title {
	display: flex;
	align-items: center;
	gap: 20px;
	margin: 0; /* 移除居中 */
}

.title-text {
	font-size: 24px; /* 稍微减小字体大小 */
	color: white;
	letter-spacing: 2px;
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.decor-line {
	width: 40px; /* 减小装饰线宽度 */
	height: 3px;
	background-color: #2a7729;
}

/* 导航操作区 */
.nav-actions {
	display: flex;
	gap: 15px;
	align-items: center;
	margin-left: auto; /* 将按钮区域推到右侧 */
}

/* 左侧图层面板 */
.layer-panel {
	position: absolute;
	left: 20px;
	top: calc(10vh + 20px); /* 导航栏下方，加上外边距 */
	width: 360px; /* 增加宽度 */
	height: calc(90vh - 40px);
	background-color: rgba(2, 49, 52, 0.85);
	z-index: 3;
	box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
	display: flex;
	flex-direction: column;
	color: white;
	overflow: hidden;
	border-radius: 12px;
	transition: all 0.1s ease;
}

/* 右侧属性面板 */
.attribute-panel {
	position: absolute;
	right: 20px;
	top: calc(10vh + 80px); /* 导航栏下方，加上外边距，再下移100px */
	width: 360px; /* 与左侧面板宽度一致 */
	height: calc(90vh - 100px); /* 减少100px高度 */
	background-color: rgba(2, 49, 52, 0.85);
	z-index: 5; /* 提高z-index确保在功能面板之上 */
	box-shadow: -2px 0 10px rgba(0, 0, 0, 0.3);
	display: flex;
	flex-direction: column;
	color: white;
	overflow: hidden;
	border-radius: 12px;
}

/* 功能面板容器 */
.feature-panels-container {
	position: absolute;
	right: 0;
	top: calc(10vh);
	width: 100%;
	height: calc(90vh);
	pointer-events: none; /* 使容器不阻挡下层点击 */
	z-index: 4;
	transition: all 0.1s ease;
}

/* 各类要素列表面板通用样式 */
.feature-list-panel {
	position: absolute;
	right: 20px;
	top: 20px; /* 相对于容器的位置 */
	width: 40%; /* 调整为原来的一半 */
	max-width: 600px; /* 相应调整最大宽度 */
	height: calc(100% - 40px);
	background-color: rgba(2, 49, 52, 0.85);
	pointer-events: auto; /* 恢复面板的点击功能 */
	box-shadow: -2px 0 10px rgba(0, 0, 0, 0.3);
	display: flex;
	flex-direction: column;
	color: white;
	overflow: hidden;
	border-radius: 12px;
	transition: all 0.1s ease;
}

.panel-header {
	padding: 15px;
	border-bottom: 1px solid rgba(255, 255, 255, 0.2);
	background-color: rgba(42, 119, 41, 0.9);
	border-radius: 12px 12px 0 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.title-container {
	overflow: hidden;
	transition: all 0.1s ease;
	flex: 1;
}

.collapsed .title-container {
	width: 0;
	opacity: 0;
}

.panel-header .close-button {
	background-color: #a93131 !important;
	border-color: #a93131 !important;
	color: white !important;
	padding: 8px 10px !important;
}

.panel-header .close-button:hover {
	background-color: #c13838 !important;
	border-color: #c13838 !important;
}

.panel-title-container {
	display: flex;
	align-items: center;
}

.panel-title {
	font-size: 18px;
	font-weight: bold;
	color: white;
	letter-spacing: 1px;
	text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
	position: relative;
	transition: all 0.1s ease;
	white-space: nowrap;
	padding-left: 30px; /* 为图标留出空间 */
}

.panel-title::before {
	content: "";
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 24px;
	height: 24px;
	background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12,3L2,12h3v8h6v-6h2v6h6v-8h3L12,3z"/></svg>');
	background-repeat: no-repeat;
	background-position: center;
	background-size: contain;
}

.panel-content {
	flex: 1;
	overflow: hidden;
}

.attribute-content {
	padding: 15px;
	overflow-y: auto;
	height: calc(100% - 50px);
	scrollbar-width: thin;
	scrollbar-color: rgba(42, 119, 41, 0.5) rgba(255, 255, 255, 0.1);
}

/* 自定义滚动条样式 */
.attribute-content::-webkit-scrollbar {
	width: 8px;
}

.attribute-content::-webkit-scrollbar-track {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 4px;
}

.attribute-content::-webkit-scrollbar-thumb {
	background: rgba(42, 119, 41, 0.5);
	border-radius: 4px;
}

.attribute-content::-webkit-scrollbar-thumb:hover {
	background: rgba(42, 119, 41, 0.8);
}

.empty-attribute-message {
	text-align: center;
	color: #aaa;
	padding: 20px;
}

.feature-type {
	background-color: rgba(255, 255, 255, 0.1);
	border-radius: 8px;
	padding: 10px;
	margin-bottom: 15px;
	display: flex;
	justify-content: space-between;
}

.type-label {
	font-weight: bold;
	color: #aaa;
}

.type-value {
	color: #fff;
}

.dt {
	position: absolute;
	z-index: 1;
	height: 100vh;
	width: 100%;
}

/* 确保地图内容不被左侧面板遮挡 */
:deep(.layer-manager-container) {
	height: calc(100% - 50px) !important;
	overflow-y: auto;
}

:deep(.layer-item) {
	padding: 10px 15px;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
	font-size: 15px;
}

:deep(.layer-control-buttons .el-button) {
	padding: 6px 10px;
	font-size: 14px;
}

:deep(.el-checkbox__label) {
	color: white;
	font-size: 15px;
}

:deep(.el-slider__runway) {
	background-color: rgba(255, 255, 255, 0.2);
}

:deep(.el-slider__bar) {
	background-color: #2a7729;
}

:deep(.el-slider__button) {
	border-color: #2a7729;
}

/* 属性表格样式 */
:deep(.el-table) {
	background-color: transparent !important;
	color: white;
}

:deep(.el-table__inner-wrapper::before) {
	display: none;
}

:deep(.el-table tr) {
	background-color: rgba(255, 255, 255, 0.05) !important;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
	background-color: rgba(255, 255, 255, 0.1) !important;
}

:deep(.el-table td, .el-table th) {
	border-color: rgba(255, 255, 255, 0.1);
}

:deep(.el-table--border) {
	border-color: rgba(255, 255, 255, 0.1);
}

/* 禁用表格行悬停效果 */
:deep(.el-table tbody tr:hover > td) {
	background-color: transparent !important;
}

/* 表格滚动条样式 */
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
	width: 8px;
	height: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
	background: rgba(42, 119, 41, 0.5);
	border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
	background: rgba(42, 119, 41, 0.8);
}

:deep(.el-table__body-wrapper) {
	scrollbar-width: thin;
	scrollbar-color: rgba(42, 119, 41, 0.5) rgba(255, 255, 255, 0.1);
}

/* 按钮样式优化 */
.nav-actions .el-button {
	height: 40px;
	padding: 0 15px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
}

.nav-actions .el-button .el-icon {
	margin-right: 5px;
}

/* 右侧功能面板收缩样式 */
.feature-list-panel.collapsed {
	width: 48px !important;
	height: 48px !important;
	overflow: hidden;
	border-radius: 50% !important;
	background-color: #2a7729 !important;
	border: none;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
	right: 20px;
	top: 20px;
}

.feature-list-panel.collapsed :deep(.facility-land-list-container),
.feature-list-panel.collapsed :deep(.planting-area-list-container),
.feature-list-panel.collapsed :deep(.investment-area-list-container) {
	background-color: transparent !important;
}

.feature-list-panel.collapsed :deep(.facility-land-list-content),
.feature-list-panel.collapsed :deep(.planting-area-list-content),
.feature-list-panel.collapsed :deep(.investment-area-list-content) {
	display: none;
}

.feature-list-panel.collapsed :deep(.facility-land-list-header),
.feature-list-panel.collapsed :deep(.planting-area-list-header),
.feature-list-panel.collapsed :deep(.investment-area-list-header) {
	border-bottom: none;
	background-color: transparent !important;
	height: 100%;
	padding: 0;
}

.feature-list-panel.collapsed :deep(.facility-land-list-title),
.feature-list-panel.collapsed :deep(.planting-area-list-title),
.feature-list-panel.collapsed :deep(.investment-area-list-title) {
	display: none;
}

.feature-list-panel.collapsed :deep(.facility-land-list-actions),
.feature-list-panel.collapsed :deep(.planting-area-list-actions),
.feature-list-panel.collapsed :deep(.investment-area-list-actions) {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	margin: 0;
}

.feature-list-panel.collapsed :deep(.filter-button),
.feature-list-panel.collapsed :deep(.close-button) {
	display: none;
}

.feature-list-panel.collapsed :deep(.collapse-button) {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	margin: 0;
	background-color: rgba(255, 255, 255, 0.2) !important;
	border-radius: 50%;
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.feature-list-panel.collapsed :deep(.collapse-button:hover) {
	background-color: rgba(255, 255, 255, 0.3) !important;
}

.feature-list-panel.collapsed :deep(.collapse-button .el-icon) {
	font-size: 18px;
	color: white;
}

/* 左侧图层面板收缩样式 */
.layer-panel.collapsed {
	width: 48px !important;
	height: 48px !important;
	overflow: hidden;
	border-radius: 50% !important;
	background-color: #2a7729 !important;
	border: none;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

.layer-panel.collapsed .panel-header {
	border-bottom: none;
	background-color: transparent !important;
	height: 100%;
	padding: 0;
}

.layer-panel.collapsed .title-container {
	width: 0;
	opacity: 0;
}

.layer-panel.collapsed :deep(.layer-manager-container) {
	opacity: 0;
	visibility: hidden;
	height: 0 !important;
}

.layer-panel.collapsed .collapse-button {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	margin: 0;
	background-color: rgba(255, 255, 255, 0.2) !important;
	border-radius: 50%;
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.layer-panel.collapsed .collapse-button:hover {
	background-color: rgba(255, 255, 255, 0.3) !important;
}

.layer-panel.collapsed .collapse-button .el-icon {
	font-size: 18px;
	color: white;
}

/* 收缩按钮通用样式 */
.collapse-button {
	margin-left: auto;
	background-color: transparent !important;
	border: none !important;
	padding: 8px !important;
}

.collapse-button:hover {
	background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 收缩后的小图标样式 - 已不再需要 */
.layer-panel-icon {
	display: none;
	transition: all 0.1s ease;
}
</style>
