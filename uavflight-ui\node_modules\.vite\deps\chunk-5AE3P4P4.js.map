{"version": 3, "sources": ["../../ol/TileState.js"], "sourcesContent": ["/**\n * @module ol/TileState\n */\n\n/**\n * @enum {number}\n */\nexport default {\n  IDLE: 0,\n  LOADING: 1,\n  LOADED: 2,\n  /**\n   * Indicates that tile loading failed\n   * @type {number}\n   */\n  ERROR: 3,\n  EMPTY: 4,\n};\n"], "mappings": ";AAOA,IAAO,oBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKR,OAAO;AAAA,EACP,OAAO;AACT;", "names": []}