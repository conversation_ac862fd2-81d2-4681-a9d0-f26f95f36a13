{"version": 3, "sources": ["../../ol/sphere.js"], "sourcesContent": ["/**\n * @module ol/sphere\n */\nimport {toDegrees, toRadians} from './math.js';\n\n/**\n * Object literal with options for the {@link getLength} or {@link getArea}\n * functions.\n * @typedef {Object} SphereMetricOptions\n * @property {import(\"./proj.js\").ProjectionLike} [projection='EPSG:3857']\n * Projection of the  geometry.  By default, the geometry is assumed to be in\n * Web Mercator.\n * @property {number} [radius=6371008.8] Sphere radius.  By default, the\n * [mean Earth radius](https://en.wikipedia.org/wiki/Earth_radius#Mean_radius)\n * for the WGS84 ellipsoid is used.\n */\n\n/**\n * The mean Earth radius (1/3 * (2a + b)) for the WGS84 ellipsoid.\n * https://en.wikipedia.org/wiki/Earth_radius#Mean_radius\n * @type {number}\n */\nexport const DEFAULT_RADIUS = 6371008.8;\n\n/**\n * Get the great circle distance (in meters) between two geographic coordinates.\n * @param {Array} c1 Starting coordinate.\n * @param {Array} c2 Ending coordinate.\n * @param {number} [radius] The sphere radius to use.  Defaults to the Earth's\n *     mean radius using the WGS84 ellipsoid.\n * @return {number} The great circle distance between the points (in meters).\n * @api\n */\nexport function getDistance(c1, c2, radius) {\n  radius = radius || DEFAULT_RADIUS;\n  const lat1 = toRadians(c1[1]);\n  const lat2 = toRadians(c2[1]);\n  const deltaLatBy2 = (lat2 - lat1) / 2;\n  const deltaLonBy2 = toRadians(c2[0] - c1[0]) / 2;\n  const a =\n    Math.sin(deltaLatBy2) * Math.sin(deltaLatBy2) +\n    Math.sin(deltaLonBy2) *\n      Math.sin(deltaLonBy2) *\n      Math.cos(lat1) *\n      Math.cos(lat2);\n  return 2 * radius * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n}\n\n/**\n * Get the cumulative great circle length of linestring coordinates (geographic).\n * @param {Array} coordinates Linestring coordinates.\n * @param {number} radius The sphere radius to use.\n * @return {number} The length (in meters).\n */\nfunction getLengthInternal(coordinates, radius) {\n  let length = 0;\n  for (let i = 0, ii = coordinates.length; i < ii - 1; ++i) {\n    length += getDistance(coordinates[i], coordinates[i + 1], radius);\n  }\n  return length;\n}\n\n/**\n * Get the spherical length of a geometry.  This length is the sum of the\n * great circle distances between coordinates.  For polygons, the length is\n * the sum of all rings.  For points, the length is zero.  For multi-part\n * geometries, the length is the sum of the length of each part.\n * @param {import(\"./geom/Geometry.js\").default} geometry A geometry.\n * @param {SphereMetricOptions} [options] Options for the\n * length calculation.  By default, geometries are assumed to be in 'EPSG:3857'.\n * You can change this by providing a `projection` option.\n * @return {number} The spherical length (in meters).\n * @api\n */\nexport function getLength(geometry, options) {\n  options = options || {};\n  const radius = options.radius || DEFAULT_RADIUS;\n  const projection = options.projection || 'EPSG:3857';\n  const type = geometry.getType();\n  if (type !== 'GeometryCollection') {\n    geometry = geometry.clone().transform(projection, 'EPSG:4326');\n  }\n  let length = 0;\n  let coordinates, coords, i, ii, j, jj;\n  switch (type) {\n    case 'Point':\n    case 'MultiPoint': {\n      break;\n    }\n    case 'LineString':\n    case 'LinearRing': {\n      coordinates = /** @type {import(\"./geom/SimpleGeometry.js\").default} */ (\n        geometry\n      ).getCoordinates();\n      length = getLengthInternal(coordinates, radius);\n      break;\n    }\n    case 'MultiLineString':\n    case 'Polygon': {\n      coordinates = /** @type {import(\"./geom/SimpleGeometry.js\").default} */ (\n        geometry\n      ).getCoordinates();\n      for (i = 0, ii = coordinates.length; i < ii; ++i) {\n        length += getLengthInternal(coordinates[i], radius);\n      }\n      break;\n    }\n    case 'MultiPolygon': {\n      coordinates = /** @type {import(\"./geom/SimpleGeometry.js\").default} */ (\n        geometry\n      ).getCoordinates();\n      for (i = 0, ii = coordinates.length; i < ii; ++i) {\n        coords = coordinates[i];\n        for (j = 0, jj = coords.length; j < jj; ++j) {\n          length += getLengthInternal(coords[j], radius);\n        }\n      }\n      break;\n    }\n    case 'GeometryCollection': {\n      const geometries =\n        /** @type {import(\"./geom/GeometryCollection.js\").default} */ (\n          geometry\n        ).getGeometries();\n      for (i = 0, ii = geometries.length; i < ii; ++i) {\n        length += getLength(geometries[i], options);\n      }\n      break;\n    }\n    default: {\n      throw new Error('Unsupported geometry type: ' + type);\n    }\n  }\n  return length;\n}\n\n/**\n * Returns the spherical area for a list of coordinates.\n *\n * [Reference](https://trs.jpl.nasa.gov/handle/2014/40409)\n * Robert. G. Chamberlain and William H. Duquette, \"Some Algorithms for\n * Polygons on a Sphere\", JPL Publication 07-03, Jet Propulsion\n * Laboratory, Pasadena, CA, June 2007\n *\n * @param {Array<import(\"./coordinate.js\").Coordinate>} coordinates List of coordinates of a linear\n * ring. If the ring is oriented clockwise, the area will be positive,\n * otherwise it will be negative.\n * @param {number} radius The sphere radius.\n * @return {number} Area (in square meters).\n */\nfunction getAreaInternal(coordinates, radius) {\n  let area = 0;\n  const len = coordinates.length;\n  let x1 = coordinates[len - 1][0];\n  let y1 = coordinates[len - 1][1];\n  for (let i = 0; i < len; i++) {\n    const x2 = coordinates[i][0];\n    const y2 = coordinates[i][1];\n    area +=\n      toRadians(x2 - x1) *\n      (2 + Math.sin(toRadians(y1)) + Math.sin(toRadians(y2)));\n    x1 = x2;\n    y1 = y2;\n  }\n  return (area * radius * radius) / 2.0;\n}\n\n/**\n * Get the spherical area of a geometry.  This is the area (in meters) assuming\n * that polygon edges are segments of great circles on a sphere.\n * @param {import(\"./geom/Geometry.js\").default} geometry A geometry.\n * @param {SphereMetricOptions} [options] Options for the area\n *     calculation.  By default, geometries are assumed to be in 'EPSG:3857'.\n *     You can change this by providing a `projection` option.\n * @return {number} The spherical area (in square meters).\n * @api\n */\nexport function getArea(geometry, options) {\n  options = options || {};\n  const radius = options.radius || DEFAULT_RADIUS;\n  const projection = options.projection || 'EPSG:3857';\n  const type = geometry.getType();\n  if (type !== 'GeometryCollection') {\n    geometry = geometry.clone().transform(projection, 'EPSG:4326');\n  }\n  let area = 0;\n  let coordinates, coords, i, ii, j, jj;\n  switch (type) {\n    case 'Point':\n    case 'MultiPoint':\n    case 'LineString':\n    case 'MultiLineString':\n    case 'LinearRing': {\n      break;\n    }\n    case 'Polygon': {\n      coordinates = /** @type {import(\"./geom/Polygon.js\").default} */ (\n        geometry\n      ).getCoordinates();\n      area = Math.abs(getAreaInternal(coordinates[0], radius));\n      for (i = 1, ii = coordinates.length; i < ii; ++i) {\n        area -= Math.abs(getAreaInternal(coordinates[i], radius));\n      }\n      break;\n    }\n    case 'MultiPolygon': {\n      coordinates = /** @type {import(\"./geom/SimpleGeometry.js\").default} */ (\n        geometry\n      ).getCoordinates();\n      for (i = 0, ii = coordinates.length; i < ii; ++i) {\n        coords = coordinates[i];\n        area += Math.abs(getAreaInternal(coords[0], radius));\n        for (j = 1, jj = coords.length; j < jj; ++j) {\n          area -= Math.abs(getAreaInternal(coords[j], radius));\n        }\n      }\n      break;\n    }\n    case 'GeometryCollection': {\n      const geometries =\n        /** @type {import(\"./geom/GeometryCollection.js\").default} */ (\n          geometry\n        ).getGeometries();\n      for (i = 0, ii = geometries.length; i < ii; ++i) {\n        area += getArea(geometries[i], options);\n      }\n      break;\n    }\n    default: {\n      throw new Error('Unsupported geometry type: ' + type);\n    }\n  }\n  return area;\n}\n\n/**\n * Returns the coordinate at the given distance and bearing from `c1`.\n *\n * @param {import(\"./coordinate.js\").Coordinate} c1 The origin point (`[lon, lat]` in degrees).\n * @param {number} distance The great-circle distance between the origin\n *     point and the target point.\n * @param {number} bearing The bearing (in radians).\n * @param {number} [radius] The sphere radius to use.  Defaults to the Earth's\n *     mean radius using the WGS84 ellipsoid.\n * @return {import(\"./coordinate.js\").Coordinate} The target point.\n */\nexport function offset(c1, distance, bearing, radius) {\n  radius = radius || DEFAULT_RADIUS;\n  const lat1 = toRadians(c1[1]);\n  const lon1 = toRadians(c1[0]);\n  const dByR = distance / radius;\n  const lat = Math.asin(\n    Math.sin(lat1) * Math.cos(dByR) +\n      Math.cos(lat1) * Math.sin(dByR) * Math.cos(bearing)\n  );\n  const lon =\n    lon1 +\n    Math.atan2(\n      Math.sin(bearing) * Math.sin(dByR) * Math.cos(lat1),\n      Math.cos(dByR) - Math.sin(lat1) * Math.sin(lat)\n    );\n  return [toDegrees(lon), toDegrees(lat)];\n}\n"], "mappings": ";;;;;;AAsBO,IAAM,iBAAiB;AAWvB,SAAS,YAAY,IAAI,IAAI,QAAQ;AAC1C,WAAS,UAAU;AACnB,QAAM,OAAO,UAAU,GAAG,CAAC,CAAC;AAC5B,QAAM,OAAO,UAAU,GAAG,CAAC,CAAC;AAC5B,QAAM,eAAe,OAAO,QAAQ;AACpC,QAAM,cAAc,UAAU,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI;AAC/C,QAAM,IACJ,KAAK,IAAI,WAAW,IAAI,KAAK,IAAI,WAAW,IAC5C,KAAK,IAAI,WAAW,IAClB,KAAK,IAAI,WAAW,IACpB,KAAK,IAAI,IAAI,IACb,KAAK,IAAI,IAAI;AACjB,SAAO,IAAI,SAAS,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC;AAC/D;AAQA,SAAS,kBAAkB,aAAa,QAAQ;AAC9C,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,KAAK,GAAG,EAAE,GAAG;AACxD,cAAU,YAAY,YAAY,CAAC,GAAG,YAAY,IAAI,CAAC,GAAG,MAAM;AAAA,EAClE;AACA,SAAO;AACT;AAcO,SAAS,UAAU,UAAU,SAAS;AAC3C,YAAU,WAAW,CAAC;AACtB,QAAM,SAAS,QAAQ,UAAU;AACjC,QAAM,aAAa,QAAQ,cAAc;AACzC,QAAM,OAAO,SAAS,QAAQ;AAC9B,MAAI,SAAS,sBAAsB;AACjC,eAAW,SAAS,MAAM,EAAE,UAAU,YAAY,WAAW;AAAA,EAC/D;AACA,MAAI,SAAS;AACb,MAAI,aAAa,QAAQ,GAAG,IAAI,GAAG;AACnC,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK,cAAc;AACjB;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL,KAAK,cAAc;AACjB;AAAA,MACE,SACA,eAAe;AACjB,eAAS,kBAAkB,aAAa,MAAM;AAC9C;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL,KAAK,WAAW;AACd;AAAA,MACE,SACA,eAAe;AACjB,WAAK,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AAChD,kBAAU,kBAAkB,YAAY,CAAC,GAAG,MAAM;AAAA,MACpD;AACA;AAAA,IACF;AAAA,IACA,KAAK,gBAAgB;AACnB;AAAA,MACE,SACA,eAAe;AACjB,WAAK,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AAChD,iBAAS,YAAY,CAAC;AACtB,aAAK,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC3C,oBAAU,kBAAkB,OAAO,CAAC,GAAG,MAAM;AAAA,QAC/C;AAAA,MACF;AACA;AAAA,IACF;AAAA,IACA,KAAK,sBAAsB;AACzB,YAAM;AAAA;AAAA,QAEF,SACA,cAAc;AAAA;AAClB,WAAK,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,kBAAU,UAAU,WAAW,CAAC,GAAG,OAAO;AAAA,MAC5C;AACA;AAAA,IACF;AAAA,IACA,SAAS;AACP,YAAM,IAAI,MAAM,gCAAgC,IAAI;AAAA,IACtD;AAAA,EACF;AACA,SAAO;AACT;AAgBA,SAAS,gBAAgB,aAAa,QAAQ;AAC5C,MAAI,OAAO;AACX,QAAM,MAAM,YAAY;AACxB,MAAI,KAAK,YAAY,MAAM,CAAC,EAAE,CAAC;AAC/B,MAAI,KAAK,YAAY,MAAM,CAAC,EAAE,CAAC;AAC/B,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,KAAK,YAAY,CAAC,EAAE,CAAC;AAC3B,UAAM,KAAK,YAAY,CAAC,EAAE,CAAC;AAC3B,YACE,UAAU,KAAK,EAAE,KAChB,IAAI,KAAK,IAAI,UAAU,EAAE,CAAC,IAAI,KAAK,IAAI,UAAU,EAAE,CAAC;AACvD,SAAK;AACL,SAAK;AAAA,EACP;AACA,SAAQ,OAAO,SAAS,SAAU;AACpC;AAYO,SAAS,QAAQ,UAAU,SAAS;AACzC,YAAU,WAAW,CAAC;AACtB,QAAM,SAAS,QAAQ,UAAU;AACjC,QAAM,aAAa,QAAQ,cAAc;AACzC,QAAM,OAAO,SAAS,QAAQ;AAC9B,MAAI,SAAS,sBAAsB;AACjC,eAAW,SAAS,MAAM,EAAE,UAAU,YAAY,WAAW;AAAA,EAC/D;AACA,MAAI,OAAO;AACX,MAAI,aAAa,QAAQ,GAAG,IAAI,GAAG;AACnC,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,cAAc;AACjB;AAAA,IACF;AAAA,IACA,KAAK,WAAW;AACd;AAAA,MACE,SACA,eAAe;AACjB,aAAO,KAAK,IAAI,gBAAgB,YAAY,CAAC,GAAG,MAAM,CAAC;AACvD,WAAK,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AAChD,gBAAQ,KAAK,IAAI,gBAAgB,YAAY,CAAC,GAAG,MAAM,CAAC;AAAA,MAC1D;AACA;AAAA,IACF;AAAA,IACA,KAAK,gBAAgB;AACnB;AAAA,MACE,SACA,eAAe;AACjB,WAAK,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AAChD,iBAAS,YAAY,CAAC;AACtB,gBAAQ,KAAK,IAAI,gBAAgB,OAAO,CAAC,GAAG,MAAM,CAAC;AACnD,aAAK,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC3C,kBAAQ,KAAK,IAAI,gBAAgB,OAAO,CAAC,GAAG,MAAM,CAAC;AAAA,QACrD;AAAA,MACF;AACA;AAAA,IACF;AAAA,IACA,KAAK,sBAAsB;AACzB,YAAM;AAAA;AAAA,QAEF,SACA,cAAc;AAAA;AAClB,WAAK,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,gBAAQ,QAAQ,WAAW,CAAC,GAAG,OAAO;AAAA,MACxC;AACA;AAAA,IACF;AAAA,IACA,SAAS;AACP,YAAM,IAAI,MAAM,gCAAgC,IAAI;AAAA,IACtD;AAAA,EACF;AACA,SAAO;AACT;AAaO,SAAS,OAAO,IAAI,UAAU,SAAS,QAAQ;AACpD,WAAS,UAAU;AACnB,QAAM,OAAO,UAAU,GAAG,CAAC,CAAC;AAC5B,QAAM,OAAO,UAAU,GAAG,CAAC,CAAC;AAC5B,QAAM,OAAO,WAAW;AACxB,QAAM,MAAM,KAAK;AAAA,IACf,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAC5B,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,OAAO;AAAA,EACtD;AACA,QAAM,MACJ,OACA,KAAK;AAAA,IACH,KAAK,IAAI,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAAA,IAClD,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,EAChD;AACF,SAAO,CAAC,UAAU,GAAG,GAAG,UAAU,GAAG,CAAC;AACxC;", "names": []}