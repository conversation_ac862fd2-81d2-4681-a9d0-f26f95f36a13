import "./chunk-PLDDJCW6.js";

// node_modules/array-source/cancel.js
function cancel_default() {
  this._array = null;
  return Promise.resolve();
}

// node_modules/array-source/read.js
function read_default() {
  var array2 = this._array;
  this._array = null;
  return Promise.resolve(array2 ? { done: false, value: array2 } : { done: true, value: void 0 });
}

// node_modules/array-source/index.js
function array(array2) {
  return new ArraySource(array2 instanceof Uint8Array ? array2 : new Uint8Array(array2));
}
function ArraySource(array2) {
  this._array = array2;
}
ArraySource.prototype.read = read_default;
ArraySource.prototype.cancel = cancel_default;

// node_modules/path-source/fetch.js
function fetch_default(url) {
  return fetch(url).then(function(response) {
    return response.body && response.body.getReader ? response.body.getReader() : response.arrayBuffer().then(array);
  });
}

// node_modules/path-source/request.js
function request_default(url) {
  return new Promise(function(resolve, reject) {
    var request = new XMLHttpRequest();
    request.responseType = "arraybuffer";
    request.onload = function() {
      resolve(array(request.response));
    };
    request.onerror = reject;
    request.ontimeout = reject;
    request.open("GET", url, true);
    request.send();
  });
}

// node_modules/path-source/index.js
function path(path2) {
  return (typeof fetch === "function" ? fetch_default : request_default)(path2);
}

// node_modules/stream-source/index.js
function stream(source) {
  return typeof source.read === "function" ? source : source.getReader();
}

// node_modules/slice-source/empty.js
var empty_default = new Uint8Array(0);

// node_modules/slice-source/cancel.js
function cancel_default2() {
  return this._source.cancel();
}

// node_modules/slice-source/concat.js
function concat(a, b) {
  if (!a.length) return b;
  if (!b.length) return a;
  var c = new Uint8Array(a.length + b.length);
  c.set(a);
  c.set(b, a.length);
  return c;
}

// node_modules/slice-source/read.js
function read_default2() {
  var that = this, array2 = that._array.subarray(that._index);
  return that._source.read().then(function(result) {
    that._array = empty_default;
    that._index = 0;
    return result.done ? array2.length > 0 ? { done: false, value: array2 } : { done: true, value: void 0 } : { done: false, value: concat(array2, result.value) };
  });
}

// node_modules/slice-source/slice.js
function slice_default(length) {
  if ((length |= 0) < 0) throw new Error("invalid length");
  var that = this, index = this._array.length - this._index;
  if (this._index + length <= this._array.length) {
    return Promise.resolve(this._array.subarray(this._index, this._index += length));
  }
  var array2 = new Uint8Array(length);
  array2.set(this._array.subarray(this._index));
  return function read2() {
    return that._source.read().then(function(result) {
      if (result.done) {
        that._array = empty_default;
        that._index = 0;
        return index > 0 ? array2.subarray(0, index) : null;
      }
      if (index + result.value.length >= length) {
        that._array = result.value;
        that._index = length - index;
        array2.set(result.value.subarray(0, length - index), index);
        return array2;
      }
      array2.set(result.value, index);
      index += result.value.length;
      return read2();
    });
  }();
}

// node_modules/slice-source/index.js
function slice(source) {
  return typeof source.slice === "function" ? source : new SliceSource(typeof source.read === "function" ? source : source.getReader());
}
function SliceSource(source) {
  this._source = source;
  this._array = empty_default;
  this._index = 0;
}
SliceSource.prototype.read = read_default2;
SliceSource.prototype.slice = slice_default;
SliceSource.prototype.cancel = cancel_default2;

// node_modules/shapefile/dbf/cancel.js
function cancel_default3() {
  return this._source.cancel();
}

// node_modules/shapefile/dbf/boolean.js
function boolean_default(value) {
  return /^[nf]$/i.test(value) ? false : /^[yt]$/i.test(value) ? true : null;
}

// node_modules/shapefile/dbf/date.js
function date_default(value) {
  return new Date(+value.substring(0, 4), value.substring(4, 6) - 1, +value.substring(6, 8));
}

// node_modules/shapefile/dbf/number.js
function number_default(value) {
  return !(value = value.trim()) || isNaN(value = +value) ? null : value;
}

// node_modules/shapefile/dbf/string.js
function string_default(value) {
  return value.trim() || null;
}

// node_modules/shapefile/dbf/read.js
var types = {
  B: number_default,
  C: string_default,
  D: date_default,
  F: number_default,
  L: boolean_default,
  M: number_default,
  N: number_default
};
function read_default3() {
  var that = this, i = 1;
  return that._source.slice(that._recordLength).then(function(value) {
    return value && value[0] !== 26 ? { done: false, value: that._fields.reduce(function(p, f) {
      p[f.name] = types[f.type](that._decode(value.subarray(i, i += f.length)));
      return p;
    }, {}) } : { done: true, value: void 0 };
  });
}

// node_modules/shapefile/view.js
function view_default(array2) {
  return new DataView(array2.buffer, array2.byteOffset, array2.byteLength);
}

// node_modules/shapefile/dbf/index.js
function dbf_default(source, decoder) {
  source = slice(source);
  return source.slice(32).then(function(array2) {
    var head = view_default(array2);
    return source.slice(head.getUint16(8, true) - 32).then(function(array3) {
      return new Dbf(source, decoder, head, view_default(array3));
    });
  });
}
function Dbf(source, decoder, head, body) {
  this._source = source;
  this._decode = decoder.decode.bind(decoder);
  this._recordLength = head.getUint16(10, true);
  this._fields = [];
  for (var n = 0; body.getUint8(n) !== 13; n += 32) {
    for (var j = 0; j < 11; ++j) if (body.getUint8(n + j) === 0) break;
    this._fields.push({
      name: this._decode(new Uint8Array(body.buffer, body.byteOffset + n, j)),
      type: String.fromCharCode(body.getUint8(n + 11)),
      length: body.getUint8(n + 16)
    });
  }
}
var prototype = Dbf.prototype;
prototype.read = read_default3;
prototype.cancel = cancel_default3;

// node_modules/shapefile/shp/cancel.js
function cancel() {
  return this._source.cancel();
}

// node_modules/shapefile/shp/multipoint.js
function multipoint_default(record) {
  var i = 40, j, n = record.getInt32(36, true), coordinates = new Array(n);
  for (j = 0; j < n; ++j, i += 16) coordinates[j] = [record.getFloat64(i, true), record.getFloat64(i + 8, true)];
  return { type: "MultiPoint", coordinates };
}

// node_modules/shapefile/shp/null.js
function null_default() {
  return null;
}

// node_modules/shapefile/shp/point.js
function point_default(record) {
  return { type: "Point", coordinates: [record.getFloat64(4, true), record.getFloat64(12, true)] };
}

// node_modules/shapefile/shp/polygon.js
function polygon_default(record) {
  var i = 44, j, n = record.getInt32(36, true), m = record.getInt32(40, true), parts = new Array(n), points = new Array(m), polygons = [], holes = [];
  for (j = 0; j < n; ++j, i += 4) parts[j] = record.getInt32(i, true);
  for (j = 0; j < m; ++j, i += 16) points[j] = [record.getFloat64(i, true), record.getFloat64(i + 8, true)];
  parts.forEach(function(i2, j2) {
    var ring = points.slice(i2, parts[j2 + 1]);
    if (ringClockwise(ring)) polygons.push([ring]);
    else holes.push(ring);
  });
  holes.forEach(function(hole) {
    polygons.some(function(polygon) {
      if (ringContainsSome(polygon[0], hole)) {
        polygon.push(hole);
        return true;
      }
    }) || polygons.push([hole]);
  });
  return polygons.length === 1 ? { type: "Polygon", coordinates: polygons[0] } : { type: "MultiPolygon", coordinates: polygons };
}
function ringClockwise(ring) {
  if ((n = ring.length) < 4) return false;
  var i = 0, n, area = ring[n - 1][1] * ring[0][0] - ring[n - 1][0] * ring[0][1];
  while (++i < n) area += ring[i - 1][1] * ring[i][0] - ring[i - 1][0] * ring[i][1];
  return area >= 0;
}
function ringContainsSome(ring, hole) {
  var i = -1, n = hole.length, c;
  while (++i < n) {
    if (c = ringContains(ring, hole[i])) {
      return c > 0;
    }
  }
  return false;
}
function ringContains(ring, point) {
  var x = point[0], y = point[1], contains = -1;
  for (var i = 0, n = ring.length, j = n - 1; i < n; j = i++) {
    var pi = ring[i], xi = pi[0], yi = pi[1], pj = ring[j], xj = pj[0], yj = pj[1];
    if (segmentContains(pi, pj, point)) {
      return 0;
    }
    if (yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi) {
      contains = -contains;
    }
  }
  return contains;
}
function segmentContains(p0, p1, p2) {
  var x20 = p2[0] - p0[0], y20 = p2[1] - p0[1];
  if (x20 === 0 && y20 === 0) return true;
  var x10 = p1[0] - p0[0], y10 = p1[1] - p0[1];
  if (x10 === 0 && y10 === 0) return false;
  var t = (x20 * x10 + y20 * y10) / (x10 * x10 + y10 * y10);
  return t < 0 || t > 1 ? false : t === 0 || t === 1 ? true : t * x10 === x20 && t * y10 === y20;
}

// node_modules/shapefile/shp/polyline.js
function polyline_default(record) {
  var i = 44, j, n = record.getInt32(36, true), m = record.getInt32(40, true), parts = new Array(n), points = new Array(m);
  for (j = 0; j < n; ++j, i += 4) parts[j] = record.getInt32(i, true);
  for (j = 0; j < m; ++j, i += 16) points[j] = [record.getFloat64(i, true), record.getFloat64(i + 8, true)];
  return n === 1 ? { type: "LineString", coordinates: points } : { type: "MultiLineString", coordinates: parts.map(function(i2, j2) {
    return points.slice(i2, parts[j2 + 1]);
  }) };
}

// node_modules/shapefile/shp/concat.js
function concat_default(a, b) {
  var ab = new Uint8Array(a.length + b.length);
  ab.set(a, 0);
  ab.set(b, a.length);
  return ab;
}

// node_modules/shapefile/shp/read.js
function read_default4() {
  var that = this;
  ++that._index;
  return that._source.slice(12).then(function(array2) {
    if (array2 == null) return { done: true, value: void 0 };
    var header = view_default(array2);
    function skip() {
      return that._source.slice(4).then(function(chunk) {
        if (chunk == null) return { done: true, value: void 0 };
        header = view_default(array2 = concat_default(array2.slice(4), chunk));
        return header.getInt32(0, false) !== that._index ? skip() : read2();
      });
    }
    function read2() {
      var length = header.getInt32(4, false) * 2 - 4, type = header.getInt32(8, true);
      return length < 0 || type && type !== that._type ? skip() : that._source.slice(length).then(function(chunk) {
        return { done: false, value: type ? that._parse(view_default(concat_default(array2.slice(8), chunk))) : null };
      });
    }
    return read2();
  });
}

// node_modules/shapefile/shp/index.js
var parsers = {
  0: null_default,
  1: point_default,
  3: polyline_default,
  5: polygon_default,
  8: multipoint_default,
  11: point_default,
  // PointZ
  13: polyline_default,
  // PolyLineZ
  15: polygon_default,
  // PolygonZ
  18: multipoint_default,
  // MultiPointZ
  21: point_default,
  // PointM
  23: polyline_default,
  // PolyLineM
  25: polygon_default,
  // PolygonM
  28: multipoint_default
  // MultiPointM
};
function shp_default(source) {
  source = slice(source);
  return source.slice(100).then(function(array2) {
    return new Shp(source, view_default(array2));
  });
}
function Shp(source, header) {
  var type = header.getInt32(32, true);
  if (!(type in parsers)) throw new Error("unsupported shape type: " + type);
  this._source = source;
  this._type = type;
  this._index = 0;
  this._parse = parsers[type];
  this.bbox = [header.getFloat64(36, true), header.getFloat64(44, true), header.getFloat64(52, true), header.getFloat64(60, true)];
}
var prototype2 = Shp.prototype;
prototype2.read = read_default4;
prototype2.cancel = cancel;

// node_modules/shapefile/shapefile/cancel.js
function noop() {
}
function cancel_default4() {
  return Promise.all([
    this._dbf && this._dbf.cancel(),
    this._shp.cancel()
  ]).then(noop);
}

// node_modules/shapefile/shapefile/read.js
function read_default5() {
  var that = this;
  return Promise.all([
    that._dbf ? that._dbf.read() : { value: {} },
    that._shp.read()
  ]).then(function(results) {
    var dbf = results[0], shp = results[1];
    return shp.done ? shp : {
      done: false,
      value: {
        type: "Feature",
        properties: dbf.value,
        geometry: shp.value
      }
    };
  });
}

// node_modules/shapefile/shapefile/index.js
function shapefile_default(shpSource, dbfSource, decoder) {
  return Promise.all([
    shp_default(shpSource),
    dbfSource && dbf_default(dbfSource, decoder)
  ]).then(function(sources) {
    return new Shapefile(sources[0], sources[1]);
  });
}
function Shapefile(shp, dbf) {
  this._shp = shp;
  this._dbf = dbf;
  this.bbox = shp.bbox;
}
var prototype3 = Shapefile.prototype;
prototype3.read = read_default5;
prototype3.cancel = cancel_default4;

// node_modules/shapefile/index.js
function open(shp, dbf, options) {
  if (typeof dbf === "string") {
    if (!/\.dbf$/.test(dbf)) dbf += ".dbf";
    dbf = path(dbf, options);
  } else if (dbf instanceof ArrayBuffer || dbf instanceof Uint8Array) {
    dbf = array(dbf);
  } else if (dbf != null) {
    dbf = stream(dbf);
  }
  if (typeof shp === "string") {
    if (!/\.shp$/.test(shp)) shp += ".shp";
    if (dbf === void 0) dbf = path(shp.substring(0, shp.length - 4) + ".dbf", options).catch(function() {
    });
    shp = path(shp, options);
  } else if (shp instanceof ArrayBuffer || shp instanceof Uint8Array) {
    shp = array(shp);
  } else {
    shp = stream(shp);
  }
  return Promise.all([shp, dbf]).then(function(sources) {
    var shp2 = sources[0], dbf2 = sources[1], encoding = "windows-1252";
    if (options && options.encoding != null) encoding = options.encoding;
    return shapefile_default(shp2, dbf2, dbf2 && new TextDecoder(encoding));
  });
}
function openShp(source, options) {
  if (typeof source === "string") {
    if (!/\.shp$/.test(source)) source += ".shp";
    source = path(source, options);
  } else if (source instanceof ArrayBuffer || source instanceof Uint8Array) {
    source = array(source);
  } else {
    source = stream(source);
  }
  return Promise.resolve(source).then(shp_default);
}
function openDbf(source, options) {
  var encoding = "windows-1252";
  if (options && options.encoding != null) encoding = options.encoding;
  encoding = new TextDecoder(encoding);
  if (typeof source === "string") {
    if (!/\.dbf$/.test(source)) source += ".dbf";
    source = path(source, options);
  } else if (source instanceof ArrayBuffer || source instanceof Uint8Array) {
    source = array(source);
  } else {
    source = stream(source);
  }
  return Promise.resolve(source).then(function(source2) {
    return dbf_default(source2, encoding);
  });
}
function read(shp, dbf, options) {
  return open(shp, dbf, options).then(function(source) {
    var features = [], collection = { type: "FeatureCollection", features, bbox: source.bbox };
    return source.read().then(function read2(result) {
      if (result.done) return collection;
      features.push(result.value);
      return source.read().then(read2);
    });
  });
}
export {
  open,
  openDbf,
  openShp,
  read
};
//# sourceMappingURL=shapefile.js.map
