<!--
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-08-05 16:00:00
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-08-05 16:00:00
 * @FilePath: \uavflight-ui\src\components\FeatureAttributesPanel.vue
 * @Description: 要素属性面板，直接在oneMap中使用
 * 
 * Copyright (c) 2025 by 吴博文, All Rights Reserved. 
-->
<template>
  <div class="feature-attributes-panel">
    <!-- 面板标题 -->
    <div class="panel-header">
      <div class="panel-title">
        <el-icon><Document /></el-icon>
        <span>{{ title || '要素属性信息' }}</span>
      </div>
      <div class="panel-actions">
        <el-button type="primary" size="small" text @click="close">
          <el-icon><Back /></el-icon>
          返回图层
        </el-button>
      </div>
    </div>
    
    <!-- 属性内容区域 -->
    <div class="panel-content">
      <el-empty v-if="!attributes || Object.keys(attributes).length === 0" description="暂无要素属性信息">
        <template #description>
          <p>请使用属性查询工具点击地图要素</p>
        </template>
        <el-button type="primary" @click="activateAttributeQuery">激活属性查询</el-button>
      </el-empty>
      <el-scrollbar v-else height="calc(100% - 10px)">
        <el-table :data="attributesArray" stripe style="width: 100%">
          <el-table-column prop="name" label="属性名称" width="140" show-overflow-tooltip />
          <el-table-column prop="value" label="属性值" show-overflow-tooltip />
        </el-table>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Document, Back } from '@element-plus/icons-vue';

// Props定义
const props = defineProps({
  attributes: {
    type: Object,
    default: () => ({})
  },
  layerId: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  gisToolsRef: {
    type: Object,
    default: null
  }
});

// Emits定义
const emit = defineEmits(['close']);

// 处理属性数据，转换为表格可用的数组形式
const attributesArray = computed(() => {
  if (!props.attributes) return [];
  
  return Object.entries(props.attributes)
    .filter(([key]) => key !== 'layerId' && key !== 'geometry' && key !== 'the_geom')
    .map(([key, value]) => ({
      name: key,
      value: value === null ? '' : String(value)
    }));
});

// 关闭面板
const close = () => {
  emit('close');
};

// 激活属性查询功能
const activateAttributeQuery = () => {
  // 如果能从props获取到GIS工具引用，则激活属性查询功能
  if (props.gisToolsRef && typeof props.gisToolsRef.activateAttributeInfo === 'function') {
    props.gisToolsRef.activateAttributeInfo();
  } else {
    console.warn('无法自动激活属性查询工具，请手动点击GIS工具栏中的属性查询按钮');
  }
};
</script>

<style lang="scss" scoped>
.feature-attributes-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .panel-header {
    height: 40px;
    padding: 0 10px;
    background-color: rgba(16, 64, 70, 0.95);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(64, 158, 255, 0.5);
    
    .panel-title {
      display: flex;
      align-items: center;
      gap: 5px;
      color: white;
      font-size: 14px;
      font-weight: bold;
      
      .el-icon {
        color: #409eff;
      }
    }
  }
  
  .panel-content {
    flex: 1;
    padding: 10px;
    overflow: hidden;
    
    .el-table {
      background-color: transparent;
      color: white;
      
      &::before {
        display: none;
      }
      
      .el-table__header-wrapper {
        .el-table__header {
          th {
            background-color: rgba(16, 64, 70, 0.95);
            color: #fff;
            border-bottom: 1px solid rgba(64, 158, 255, 0.5);
            
            .cell {
              font-weight: bold;
            }
          }
        }
      }
      
      .el-table__body-wrapper {
        .el-table__body {
          td {
            background-color: rgba(15, 21, 32, 0.85);
            color: #fff;
            border-bottom: 1px solid rgba(64, 158, 255, 0.2);
          }
          
          tr:nth-child(even) td {
            background-color: rgba(26, 41, 61, 0.95);
          }
          
          tr:hover td {
            background-color: rgba(64, 158, 255, 0.3);
          }
        }
      }
    }
    
    .el-empty {
      color: #909399;
    }
  }
}
</style> 