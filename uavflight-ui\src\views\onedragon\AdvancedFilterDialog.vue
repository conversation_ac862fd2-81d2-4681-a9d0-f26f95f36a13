<template>
  <el-dialog
    v-model="dialogVisible"
    title="高级筛选与排序"
    width="550px"
    destroy-on-close
    :before-close="handleClose"
  >
    <div class="advanced-filter-container">
      <!-- 状态筛选 -->
      <div class="filter-section">
        <h3>任务状态筛选</h3>
        <el-checkbox-group v-model="selectedStatus">
          <el-checkbox label="未开始">未开始</el-checkbox>
          <el-checkbox label="进行中">进行中</el-checkbox>
          <el-checkbox label="完成">完成</el-checkbox>
          <el-checkbox label="失败">失败</el-checkbox>
        </el-checkbox-group>
      </div>
      
      <!-- 排序 -->
      <div class="filter-section">
        <h3>排序设置</h3>
        <div class="sort-options">
          <div class="sort-field">
            <span>排序字段:</span>
            <el-select v-model="sortField" placeholder="选择排序字段">
              <el-option label="任务ID" value="task_id" />
              <el-option label="状态" value="status" />
              <el-option label="开始时间" value="start_time" />
              <el-option label="结束时间" value="end_time" />
              <el-option label="处理时长" value="duration" />
            </el-select>
          </div>
          <div class="sort-order">
            <span>排序方式:</span>
            <el-radio-group v-model="sortOrder">
              <el-radio label="ascending">升序</el-radio>
              <el-radio label="descending">降序</el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>
      
      <!-- 时间范围筛选 -->
      <div class="filter-section">
        <h3>时间范围筛选</h3>
        <div class="date-range">
          <el-date-picker
            v-model="timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :shortcuts="dateShortcuts"
            style="width: 100%"
          />
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="resetFilters">重置</el-button>
        <el-button type="primary" @click="applyFilters">应用</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
export default {
  name: 'AdvancedFilterDialog'
}
</script>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  // 筛选设置，用于初始化
  filterSettings: {
    type: Object,
    default: () => ({
      status: [],
      timeRange: null,
      sortField: 'task_id',
      sortOrder: 'ascending'
    })
  }
});

const emit = defineEmits(['update:visible', 'apply-filters']);

// 弹窗可见性
const dialogVisible = ref(false);

// 筛选选项
const selectedStatus = ref<string[]>([]);
const timeRange = ref<[string, string] | null>(null);
const sortField = ref('task_id');
const sortOrder = ref('ascending');

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

// 监听props变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    // 初始化筛选条件
    initFilters();
  }
});

watch(() => dialogVisible.value, (newVal) => {
  emit('update:visible', newVal);
});

// 初始化筛选条件
const initFilters = () => {
  selectedStatus.value = [...(props.filterSettings?.status || [])];
  timeRange.value = props.filterSettings?.timeRange || null;
  sortField.value = props.filterSettings?.sortField || 'task_id';
  sortOrder.value = props.filterSettings?.sortOrder || 'ascending';
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};

// 应用筛选条件
const applyFilters = () => {
  const filters = {
    status: selectedStatus.value,
    timeRange: timeRange.value,
    sortField: sortField.value,
    sortOrder: sortOrder.value
  };
  
  emit('apply-filters', filters);
  dialogVisible.value = false;
};

// 重置所有筛选条件
const resetFilters = () => {
  selectedStatus.value = [];
  timeRange.value = null;
  sortField.value = 'task_id';
  sortOrder.value = 'ascending';
};
</script>

<style lang="scss" scoped>
.advanced-filter-container {
  .filter-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
    
    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 16px;
      color: #303133;
    }
    
    .el-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
    }
    
    .sort-options {
      display: flex;
      flex-direction: column;
      gap: 15px;
      
      .sort-field, .sort-order {
        display: flex;
        align-items: center;
        gap: 10px;
        
        span {
          min-width: 70px;
        }
        
        .el-select {
          width: 150px;
        }
      }
    }
    
    .date-range {
      margin-top: 10px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 