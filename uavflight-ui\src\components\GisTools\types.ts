/*
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-07-13 21:51:45
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-08-05 10:10:00
 * @FilePath: \uavflight-ui\src\components\GisTools\types.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Feature } from 'ol';

/**
 * GIS绘制工具的类型
 */
export type DrawToolType = 'Point' | 'LineString' | 'Polygon' | 'Rectangle' | 'Circle' | 'MeasureDistance' | 'MeasureArea' | 'AttributeInfo' | 'Edit' | '';

/**
 * 绘制开始事件的数据类型
 */
export interface DrawStartEvent {
  tool: DrawToolType;
}

/**
 * 绘制结束事件的数据类型
 */
export interface DrawEndEvent {
  tool: DrawToolType;
  feature: Feature;
}

/**
 * 属性查询结果事件的数据类型
 */
export interface AttributeInfoEvent {
  layerId: string;
  layerName: string;
  attributes: Record<string, any>;
  coordinate: number[];
}

/**
 * GisTools组件的Props接口
 */
export interface GisToolsProps {
  mapStore: any; // OpenLayers地图存储对象
}

/**
 * GisTools组件暴露的方法
 */
export interface GisToolsExpose {
  clearDrawings: () => void;
  getActiveDrawTool: () => DrawToolType;
  removeDrawInteraction: () => void;
} 