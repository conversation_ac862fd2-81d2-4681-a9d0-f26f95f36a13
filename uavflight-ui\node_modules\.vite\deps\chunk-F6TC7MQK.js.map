{"version": 3, "sources": ["../../ol/MapEvent.js", "../../ol/MapBrowserEvent.js", "../../ol/interaction/Property.js", "../../ol/interaction/Interaction.js", "../../ol/interaction/Pointer.js"], "sourcesContent": ["/**\n * @module ol/MapEvent\n */\nimport Event from './events/Event.js';\n\n/**\n * @classdesc\n * Events emitted as map events are instances of this type.\n * See {@link module:ol/Map~Map} for which events trigger a map event.\n */\nclass MapEvent extends Event {\n  /**\n   * @param {string} type Event type.\n   * @param {import(\"./Map.js\").default} map Map.\n   * @param {?import(\"./Map.js\").FrameState} [frameState] Frame state.\n   */\n  constructor(type, map, frameState) {\n    super(type);\n\n    /**\n     * The map where the event occurred.\n     * @type {import(\"./Map.js\").default}\n     * @api\n     */\n    this.map = map;\n\n    /**\n     * The frame state at the time of the event.\n     * @type {?import(\"./Map.js\").FrameState}\n     * @api\n     */\n    this.frameState = frameState !== undefined ? frameState : null;\n  }\n}\n\nexport default MapEvent;\n", "/**\n * @module ol/MapBrowserEvent\n */\nimport MapEvent from './MapEvent.js';\n\n/**\n * @classdesc\n * Events emitted as map browser events are instances of this type.\n * See {@link module:ol/Map~Map} for which events trigger a map browser event.\n * @template {UIEvent} EVENT\n */\nclass MapBrowserEvent extends MapEvent {\n  /**\n   * @param {string} type Event type.\n   * @param {import(\"./Map.js\").default} map Map.\n   * @param {EVENT} originalEvent Original event.\n   * @param {boolean} [dragging] Is the map currently being dragged?\n   * @param {import(\"./Map.js\").FrameState} [frameState] Frame state.\n   * @param {Array<PointerEvent>} [activePointers] Active pointers.\n   */\n  constructor(type, map, originalEvent, dragging, frameState, activePointers) {\n    super(type, map, frameState);\n\n    /**\n     * The original browser event.\n     * @const\n     * @type {EVENT}\n     * @api\n     */\n    this.originalEvent = originalEvent;\n\n    /**\n     * The map pixel relative to the viewport corresponding to the original browser event.\n     * @type {?import(\"./pixel.js\").Pixel}\n     */\n    this.pixel_ = null;\n\n    /**\n     * The coordinate in the user projection corresponding to the original browser event.\n     * @type {?import(\"./coordinate.js\").Coordinate}\n     */\n    this.coordinate_ = null;\n\n    /**\n     * Indicates if the map is currently being dragged. Only set for\n     * `POINTERDRAG` and `POINTERMOVE` events. Default is `false`.\n     *\n     * @type {boolean}\n     * @api\n     */\n    this.dragging = dragging !== undefined ? dragging : false;\n\n    /**\n     * @type {Array<PointerEvent>|undefined}\n     */\n    this.activePointers = activePointers;\n  }\n\n  /**\n   * The map pixel relative to the viewport corresponding to the original event.\n   * @type {import(\"./pixel.js\").Pixel}\n   * @api\n   */\n  get pixel() {\n    if (!this.pixel_) {\n      this.pixel_ = this.map.getEventPixel(this.originalEvent);\n    }\n    return this.pixel_;\n  }\n  set pixel(pixel) {\n    this.pixel_ = pixel;\n  }\n\n  /**\n   * The coordinate corresponding to the original browser event.  This will be in the user\n   * projection if one is set.  Otherwise it will be in the view projection.\n   * @type {import(\"./coordinate.js\").Coordinate}\n   * @api\n   */\n  get coordinate() {\n    if (!this.coordinate_) {\n      this.coordinate_ = this.map.getCoordinateFromPixel(this.pixel);\n    }\n    return this.coordinate_;\n  }\n  set coordinate(coordinate) {\n    this.coordinate_ = coordinate;\n  }\n\n  /**\n   * Prevents the default browser action.\n   * See https://developer.mozilla.org/en-US/docs/Web/API/event.preventDefault.\n   * @api\n   */\n  preventDefault() {\n    super.preventDefault();\n    if ('preventDefault' in this.originalEvent) {\n      /** @type {UIEvent} */ (this.originalEvent).preventDefault();\n    }\n  }\n\n  /**\n   * Prevents further propagation of the current event.\n   * See https://developer.mozilla.org/en-US/docs/Web/API/event.stopPropagation.\n   * @api\n   */\n  stopPropagation() {\n    super.stopPropagation();\n    if ('stopPropagation' in this.originalEvent) {\n      /** @type {UIEvent} */ (this.originalEvent).stopPropagation();\n    }\n  }\n}\n\nexport default MapBrowserEvent;\n", "/**\n * @module ol/interaction/Property\n */\n\n/**\n * @enum {string}\n */\nexport default {\n  ACTIVE: 'active',\n};\n", "/**\n * @module ol/interaction/Interaction\n */\nimport BaseObject from '../Object.js';\nimport InteractionProperty from './Property.js';\nimport {easeOut, linear} from '../easing.js';\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types|\n *     'change:active', import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types|\n *     'change:active', Return>} InteractionOnSignature\n */\n\n/**\n * Object literal with config options for interactions.\n * @typedef {Object} InteractionOptions\n * @property {function(import(\"../MapBrowserEvent.js\").default):boolean} handleEvent\n * Method called by the map to notify the interaction that a browser event was\n * dispatched to the map. If the function returns a falsy value, propagation of\n * the event to other interactions in the map's interactions chain will be\n * prevented (this includes functions with no explicit return). The interactions\n * are traversed in reverse order of the interactions collection of the map.\n */\n\n/**\n * @classdesc\n * Abstract base class; normally only used for creating subclasses and not\n * instantiated in apps.\n * User actions that change the state of the map. Some are similar to controls,\n * but are not associated with a DOM element.\n * For example, {@link module:ol/interaction/KeyboardZoom~KeyboardZoom} is\n * functionally the same as {@link module:ol/control/Zoom~Zoom}, but triggered\n * by a keyboard event not a button element event.\n * Although interactions do not have a DOM element, some of them do render\n * vectors and so are visible on the screen.\n * @api\n */\nclass Interaction extends BaseObject {\n  /**\n   * @param {InteractionOptions} [options] Options.\n   */\n  constructor(options) {\n    super();\n\n    /***\n     * @type {InteractionOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {InteractionOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {InteractionOnSignature<void>}\n     */\n    this.un;\n\n    if (options && options.handleEvent) {\n      this.handleEvent = options.handleEvent;\n    }\n\n    /**\n     * @private\n     * @type {import(\"../Map.js\").default|null}\n     */\n    this.map_ = null;\n\n    this.setActive(true);\n  }\n\n  /**\n   * Return whether the interaction is currently active.\n   * @return {boolean} `true` if the interaction is active, `false` otherwise.\n   * @observable\n   * @api\n   */\n  getActive() {\n    return /** @type {boolean} */ (this.get(InteractionProperty.ACTIVE));\n  }\n\n  /**\n   * Get the map associated with this interaction.\n   * @return {import(\"../Map.js\").default|null} Map.\n   * @api\n   */\n  getMap() {\n    return this.map_;\n  }\n\n  /**\n   * Handles the {@link module:ol/MapBrowserEvent~MapBrowserEvent map browser event}.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n   * @return {boolean} `false` to stop event propagation.\n   * @api\n   */\n  handleEvent(mapBrowserEvent) {\n    return true;\n  }\n\n  /**\n   * Activate or deactivate the interaction.\n   * @param {boolean} active Active.\n   * @observable\n   * @api\n   */\n  setActive(active) {\n    this.set(InteractionProperty.ACTIVE, active);\n  }\n\n  /**\n   * Remove the interaction from its current map and attach it to the new map.\n   * Subclasses may set up event handlers to get notified about changes to\n   * the map here.\n   * @param {import(\"../Map.js\").default|null} map Map.\n   */\n  setMap(map) {\n    this.map_ = map;\n  }\n}\n\n/**\n * @param {import(\"../View.js\").default} view View.\n * @param {import(\"../coordinate.js\").Coordinate} delta Delta.\n * @param {number} [duration] Duration.\n */\nexport function pan(view, delta, duration) {\n  const currentCenter = view.getCenterInternal();\n  if (currentCenter) {\n    const center = [currentCenter[0] + delta[0], currentCenter[1] + delta[1]];\n    view.animateInternal({\n      duration: duration !== undefined ? duration : 250,\n      easing: linear,\n      center: view.getConstrainedCenter(center),\n    });\n  }\n}\n\n/**\n * @param {import(\"../View.js\").default} view View.\n * @param {number} delta Delta from previous zoom level.\n * @param {import(\"../coordinate.js\").Coordinate} [anchor] Anchor coordinate in the user projection.\n * @param {number} [duration] Duration.\n */\nexport function zoomByDelta(view, delta, anchor, duration) {\n  const currentZoom = view.getZoom();\n\n  if (currentZoom === undefined) {\n    return;\n  }\n\n  const newZoom = view.getConstrainedZoom(currentZoom + delta);\n  const newResolution = view.getResolutionForZoom(newZoom);\n\n  if (view.getAnimating()) {\n    view.cancelAnimations();\n  }\n  view.animate({\n    resolution: newResolution,\n    anchor: anchor,\n    duration: duration !== undefined ? duration : 250,\n    easing: easeOut,\n  });\n}\n\nexport default Interaction;\n", "/**\n * @module ol/interaction/Pointer\n */\nimport Interaction from './Interaction.js';\nimport MapBrowserEventType from '../MapBrowserEventType.js';\n\n/**\n * @typedef {Object} Options\n * @property {function(import(\"../MapBrowserEvent.js\").default):boolean} [handleDownEvent]\n * Function handling \"down\" events. If the function returns `true` then a drag\n * sequence is started.\n * @property {function(import(\"../MapBrowserEvent.js\").default):void} [handleDragEvent]\n * Function handling \"drag\" events. This function is called on \"move\" events\n * during a drag sequence.\n * @property {function(import(\"../MapBrowserEvent.js\").default):boolean} [handleEvent]\n * Method called by the map to notify the interaction that a browser event was\n * dispatched to the map. The function may return `false` to prevent the\n * propagation of the event to other interactions in the map's interactions\n * chain.\n * @property {function(import(\"../MapBrowserEvent.js\").default):void} [handleMoveEvent]\n * Function handling \"move\" events. This function is called on \"move\" events.\n * This functions is also called during a drag sequence, so during a drag\n * sequence both the `handleDragEvent` function and this function are called.\n * If `handleDownEvent` is defined and it returns true this function will not\n * be called during a drag sequence.\n * @property {function(import(\"../MapBrowserEvent.js\").default):boolean} [handleUpEvent]\n *  Function handling \"up\" events. If the function returns `false` then the\n * current drag sequence is stopped.\n * @property {function(boolean):boolean} [stopDown]\n * Should the down event be propagated to other interactions, or should be\n * stopped?\n */\n\n/**\n * @classdesc\n * Base class that calls user-defined functions on `down`, `move` and `up`\n * events. This class also manages \"drag sequences\".\n *\n * When the `handleDownEvent` user function returns `true` a drag sequence is\n * started. During a drag sequence the `handleDragEvent` user function is\n * called on `move` events. The drag sequence ends when the `handleUpEvent`\n * user function is called and returns `false`.\n * @api\n */\nclass PointerInteraction extends Interaction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    super(\n      /** @type {import(\"./Interaction.js\").InteractionOptions} */ (options)\n    );\n\n    if (options.handleDownEvent) {\n      this.handleDownEvent = options.handleDownEvent;\n    }\n\n    if (options.handleDragEvent) {\n      this.handleDragEvent = options.handleDragEvent;\n    }\n\n    if (options.handleMoveEvent) {\n      this.handleMoveEvent = options.handleMoveEvent;\n    }\n\n    if (options.handleUpEvent) {\n      this.handleUpEvent = options.handleUpEvent;\n    }\n\n    if (options.stopDown) {\n      this.stopDown = options.stopDown;\n    }\n\n    /**\n     * @type {boolean}\n     * @protected\n     */\n    this.handlingDownUpSequence = false;\n\n    /**\n     * @type {Array<PointerEvent>}\n     * @protected\n     */\n    this.targetPointers = [];\n  }\n\n  /**\n   * Returns the current number of pointers involved in the interaction,\n   * e.g. `2` when two fingers are used.\n   * @return {number} The number of pointers.\n   * @api\n   */\n  getPointerCount() {\n    return this.targetPointers.length;\n  }\n\n  /**\n   * Handle pointer down events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   * @protected\n   */\n  handleDownEvent(mapBrowserEvent) {\n    return false;\n  }\n\n  /**\n   * Handle pointer drag events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @protected\n   */\n  handleDragEvent(mapBrowserEvent) {}\n\n  /**\n   * Handles the {@link module:ol/MapBrowserEvent~MapBrowserEvent map browser event} and may call into\n   * other functions, if event sequences like e.g. 'drag' or 'down-up' etc. are\n   * detected.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n   * @return {boolean} `false` to stop event propagation.\n   * @api\n   */\n  handleEvent(mapBrowserEvent) {\n    if (!mapBrowserEvent.originalEvent) {\n      return true;\n    }\n\n    let stopEvent = false;\n    this.updateTrackedPointers_(mapBrowserEvent);\n    if (this.handlingDownUpSequence) {\n      if (mapBrowserEvent.type == MapBrowserEventType.POINTERDRAG) {\n        this.handleDragEvent(mapBrowserEvent);\n        // prevent page scrolling during dragging\n        mapBrowserEvent.originalEvent.preventDefault();\n      } else if (mapBrowserEvent.type == MapBrowserEventType.POINTERUP) {\n        const handledUp = this.handleUpEvent(mapBrowserEvent);\n        this.handlingDownUpSequence =\n          handledUp && this.targetPointers.length > 0;\n      }\n    } else {\n      if (mapBrowserEvent.type == MapBrowserEventType.POINTERDOWN) {\n        const handled = this.handleDownEvent(mapBrowserEvent);\n        this.handlingDownUpSequence = handled;\n        stopEvent = this.stopDown(handled);\n      } else if (mapBrowserEvent.type == MapBrowserEventType.POINTERMOVE) {\n        this.handleMoveEvent(mapBrowserEvent);\n      }\n    }\n    return !stopEvent;\n  }\n\n  /**\n   * Handle pointer move events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @protected\n   */\n  handleMoveEvent(mapBrowserEvent) {}\n\n  /**\n   * Handle pointer up events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   * @protected\n   */\n  handleUpEvent(mapBrowserEvent) {\n    return false;\n  }\n\n  /**\n   * This function is used to determine if \"down\" events should be propagated\n   * to other interactions or should be stopped.\n   * @param {boolean} handled Was the event handled by the interaction?\n   * @return {boolean} Should the `down` event be stopped?\n   */\n  stopDown(handled) {\n    return handled;\n  }\n\n  /**\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @private\n   */\n  updateTrackedPointers_(mapBrowserEvent) {\n    if (mapBrowserEvent.activePointers) {\n      this.targetPointers = mapBrowserEvent.activePointers;\n    }\n  }\n}\n\n/**\n * @param {Array<PointerEvent>} pointerEvents List of events.\n * @return {{clientX: number, clientY: number}} Centroid pixel.\n */\nexport function centroid(pointerEvents) {\n  const length = pointerEvents.length;\n  let clientX = 0;\n  let clientY = 0;\n  for (let i = 0; i < length; i++) {\n    clientX += pointerEvents[i].clientX;\n    clientY += pointerEvents[i].clientY;\n  }\n  return {clientX: clientX / length, clientY: clientY / length};\n}\n\nexport default PointerInteraction;\n"], "mappings": ";;;;;;;;;;;;;;;AAUA,IAAM,WAAN,cAAuB,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3B,YAAY,MAAM,KAAK,YAAY;AACjC,UAAM,IAAI;AAOV,SAAK,MAAM;AAOX,SAAK,aAAa,eAAe,SAAY,aAAa;AAAA,EAC5D;AACF;AAEA,IAAO,mBAAQ;;;ACxBf,IAAM,kBAAN,cAA8B,iBAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASrC,YAAY,MAAM,KAAK,eAAe,UAAU,YAAY,gBAAgB;AAC1E,UAAM,MAAM,KAAK,UAAU;AAQ3B,SAAK,gBAAgB;AAMrB,SAAK,SAAS;AAMd,SAAK,cAAc;AASnB,SAAK,WAAW,aAAa,SAAY,WAAW;AAKpD,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,QAAQ;AACV,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,SAAS,KAAK,IAAI,cAAc,KAAK,aAAa;AAAA,IACzD;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,aAAa;AACf,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc,KAAK,IAAI,uBAAuB,KAAK,KAAK;AAAA,IAC/D;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,YAAY;AACzB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,UAAM,eAAe;AACrB,QAAI,oBAAoB,KAAK,eAAe;AACnB,MAAC,KAAK,cAAe,eAAe;AAAA,IAC7D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB;AAChB,UAAM,gBAAgB;AACtB,QAAI,qBAAqB,KAAK,eAAe;AACpB,MAAC,KAAK,cAAe,gBAAgB;AAAA,IAC9D;AAAA,EACF;AACF;AAEA,IAAO,0BAAQ;;;AC3Gf,IAAO,mBAAQ;AAAA,EACb,QAAQ;AACV;;;AC+BA,IAAM,cAAN,cAA0B,eAAW;AAAA;AAAA;AAAA;AAAA,EAInC,YAAY,SAAS;AACnB,UAAM;AAKN,SAAK;AAKL,SAAK;AAKL,SAAK;AAEL,QAAI,WAAW,QAAQ,aAAa;AAClC,WAAK,cAAc,QAAQ;AAAA,IAC7B;AAMA,SAAK,OAAO;AAEZ,SAAK,UAAU,IAAI;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV;AAAA;AAAA,MAA+B,KAAK,IAAI,iBAAoB,MAAM;AAAA;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,iBAAiB;AAC3B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,SAAK,IAAI,iBAAoB,QAAQ,MAAM;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KAAK;AACV,SAAK,OAAO;AAAA,EACd;AACF;AAOO,SAAS,IAAI,MAAM,OAAO,UAAU;AACzC,QAAM,gBAAgB,KAAK,kBAAkB;AAC7C,MAAI,eAAe;AACjB,UAAM,SAAS,CAAC,cAAc,CAAC,IAAI,MAAM,CAAC,GAAG,cAAc,CAAC,IAAI,MAAM,CAAC,CAAC;AACxE,SAAK,gBAAgB;AAAA,MACnB,UAAU,aAAa,SAAY,WAAW;AAAA,MAC9C,QAAQ;AAAA,MACR,QAAQ,KAAK,qBAAqB,MAAM;AAAA,IAC1C,CAAC;AAAA,EACH;AACF;AAQO,SAAS,YAAY,MAAM,OAAO,QAAQ,UAAU;AACzD,QAAM,cAAc,KAAK,QAAQ;AAEjC,MAAI,gBAAgB,QAAW;AAC7B;AAAA,EACF;AAEA,QAAM,UAAU,KAAK,mBAAmB,cAAc,KAAK;AAC3D,QAAM,gBAAgB,KAAK,qBAAqB,OAAO;AAEvD,MAAI,KAAK,aAAa,GAAG;AACvB,SAAK,iBAAiB;AAAA,EACxB;AACA,OAAK,QAAQ;AAAA,IACX,YAAY;AAAA,IACZ;AAAA,IACA,UAAU,aAAa,SAAY,WAAW;AAAA,IAC9C,QAAQ;AAAA,EACV,CAAC;AACH;AAEA,IAAO,sBAAQ;;;AC7Hf,IAAM,qBAAN,cAAiC,oBAAY;AAAA;AAAA;AAAA;AAAA,EAI3C,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B;AAAA;AAAA,MACgE;AAAA,IAChE;AAEA,QAAI,QAAQ,iBAAiB;AAC3B,WAAK,kBAAkB,QAAQ;AAAA,IACjC;AAEA,QAAI,QAAQ,iBAAiB;AAC3B,WAAK,kBAAkB,QAAQ;AAAA,IACjC;AAEA,QAAI,QAAQ,iBAAiB;AAC3B,WAAK,kBAAkB,QAAQ;AAAA,IACjC;AAEA,QAAI,QAAQ,eAAe;AACzB,WAAK,gBAAgB,QAAQ;AAAA,IAC/B;AAEA,QAAI,QAAQ,UAAU;AACpB,WAAK,WAAW,QAAQ;AAAA,IAC1B;AAMA,SAAK,yBAAyB;AAM9B,SAAK,iBAAiB,CAAC;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAkB;AAChB,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,iBAAiB;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,iBAAiB;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUlC,YAAY,iBAAiB;AAC3B,QAAI,CAAC,gBAAgB,eAAe;AAClC,aAAO;AAAA,IACT;AAEA,QAAI,YAAY;AAChB,SAAK,uBAAuB,eAAe;AAC3C,QAAI,KAAK,wBAAwB;AAC/B,UAAI,gBAAgB,QAAQ,4BAAoB,aAAa;AAC3D,aAAK,gBAAgB,eAAe;AAEpC,wBAAgB,cAAc,eAAe;AAAA,MAC/C,WAAW,gBAAgB,QAAQ,4BAAoB,WAAW;AAChE,cAAM,YAAY,KAAK,cAAc,eAAe;AACpD,aAAK,yBACH,aAAa,KAAK,eAAe,SAAS;AAAA,MAC9C;AAAA,IACF,OAAO;AACL,UAAI,gBAAgB,QAAQ,4BAAoB,aAAa;AAC3D,cAAM,UAAU,KAAK,gBAAgB,eAAe;AACpD,aAAK,yBAAyB;AAC9B,oBAAY,KAAK,SAAS,OAAO;AAAA,MACnC,WAAW,gBAAgB,QAAQ,4BAAoB,aAAa;AAClE,aAAK,gBAAgB,eAAe;AAAA,MACtC;AAAA,IACF;AACA,WAAO,CAAC;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,iBAAiB;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlC,cAAc,iBAAiB;AAC7B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,SAAS;AAChB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,iBAAiB;AACtC,QAAI,gBAAgB,gBAAgB;AAClC,WAAK,iBAAiB,gBAAgB;AAAA,IACxC;AAAA,EACF;AACF;AAMO,SAAS,SAAS,eAAe;AACtC,QAAM,SAAS,cAAc;AAC7B,MAAI,UAAU;AACd,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,eAAW,cAAc,CAAC,EAAE;AAC5B,eAAW,cAAc,CAAC,EAAE;AAAA,EAC9B;AACA,SAAO,EAAC,SAAS,UAAU,QAAQ,SAAS,UAAU,OAAM;AAC9D;AAEA,IAAO,kBAAQ;", "names": []}