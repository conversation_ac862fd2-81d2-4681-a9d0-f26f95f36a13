const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/iframes.CbVi4s8e.js","assets/vue.CnN__PXn.js"])))=>i.map(i=>d[i]);
import{b,u as x,e as _,S as D,a as O,__tla as j}from"./index.C0-0gsfl.js";import{d,l as q,z,s as V,A as B,c as A,h as S,y as L,o as U,i as F,w as G,B as H,a as J,b as f,t as r,v as o,C as h,u as e,e as y,K as M,q as w,D as g,r as k,x as p,j as Q}from"./vue.CnN__PXn.js";let P,W=Promise.all([(()=>{try{return j}catch{}})()]).then(async()=>{let v,R;v={class:"layout-parent"},R=d({name:"layoutParentView"}),P=d({...R,setup(X){const C=Q(()=>O(()=>import("./iframes.CbVi4s8e.js"),__vite__mapDeps([0,1]))),t=q(),I=z(),N=b(),T=x(),{keepAliveNames:u,cachedViews:E}=V(N),{themeConfig:c}=V(T),a=B({refreshRouterViewKey:"",iframeRefreshKey:"",keepAliveNameList:[],iframeList:[]}),l=A(()=>c.value.animation),n=A(()=>t.meta.isIframe);return S(()=>{a.keepAliveNameList=u.value,_.on("onTagsViewRefreshRouterView",s=>{a.keepAliveNameList=u.value.filter(i=>t.name!==i),a.refreshRouterViewKey="",a.iframeRefreshKey="",L(()=>{a.refreshRouterViewKey=s,a.iframeRefreshKey=s,a.keepAliveNameList=u.value})})}),U(()=>{(async()=>I.getRoutes().forEach(s=>{s.meta.isIframe&&(s.meta.isIframeOpen=!1,s.meta.loading=!0,a.iframeList.push({...s}))}))(),L(()=>{setTimeout(()=>{if(c.value.isCacheTagsView){let s=D.get("tagsViewList")||[];E.value=s.filter(i=>{var m;return(m=i.meta)==null?void 0:m.isKeepAlive}).map(i=>i.name)}},0)})}),F(()=>{_.off("onTagsViewRefreshRouterView",()=>{})}),G(()=>t.fullPath,()=>{a.refreshRouterViewKey=decodeURI(t.fullPath)},{immediate:!0}),(s,i)=>{const m=H("router-view");return f(),J("div",v,[r(m,null,{default:o(({Component:K})=>[r(h,{name:e(l),mode:"out-in"},{default:o(()=>[(f(),y(M,null,[e(t).meta.isKeepAlive?w((f(),y(k(K),{key:e(a).refreshRouterViewKey,class:"w100"})),[[p,!e(n)]]):g("",!0)],1024))]),_:2},1032,["name"]),r(h,{name:e(l),mode:"out-in"},{default:o(()=>[e(t).meta.isKeepAlive?g("",!0):w((f(),y(k(K),{key:e(a).refreshRouterViewKey,class:"w100"})),[[p,!e(n)]])]),_:2},1032,["name"])]),_:1}),r(h,{name:e(l),mode:"out-in"},{default:o(()=>[w(r(e(C),{class:"w100",refreshKey:e(a).iframeRefreshKey,name:e(l),list:e(a).iframeList},null,8,["refreshKey","name","list"]),[[p,e(n)]])]),_:1},8,["name"])])}}})});export{W as __tla,P as default};
