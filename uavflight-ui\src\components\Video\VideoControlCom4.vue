<template>
	<div class="video-control-container">
		<div class="video-play-box" ref="dragBox">
			<!-- 拖动窗口标题 -->
			<div class="video-control-title" ref="dragHandle">无人机视频浏览</div>

			<!-- 视频 -->
			<video class="video" ref="videoD" loop muted preload="auto" crossorigin="anonymous" @timeupdate="updateTime" @loadedmetadata="onLoadedMetadata">
				<source src="E:\gty\uavflight-file\processData\test01_v1_m1\test01_v1_m1.mp4" type="video/mp4" />
			</video>

			<!-- 控制栏 -->
			<div class="video-controls">
				<!-- 播放/暂停按钮 -->
				<button @click="togglePlay">
					{{ playing ? '暂停' : '播放' }}
				</button>

				<!-- 进度条 -->
				<input type="range" min="0" :max="duration" step="0.1" v-model="currentTime" @input="seekTo" />

				<!-- 播放时间 -->
				<span class="video-time"> {{ formatTime(currentTime) }} / {{ formatTime(duration) }} </span>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useDroneControlStore } from '/@/stores/droneControl/droneControl.ts';
import { getBusinessResultList, getRealTimeTaskList, getVideoInfoByTaskId, getRealEventList } from '/@/api/admin/bigScreen';
import CesiumPointManager from '/@/utils/map/CesiumPointManager';
import { useEarthStore } from '/@/stores/earth';

const DroneControlStore = useDroneControlStore();
const videoD = ref(null);
const videoControler = ref(null);
const dragBox = ref(null);
const dragHandle = ref(null);
const playing = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const earthStore = useEarthStore();
let points = {};
onMounted(async () => {
	initDrag();
	await getVideoInfoFun(DroneControlStore.taskId);
	videoControler.value = DroneControlStore.videoControler;
	videoControler.value.initVideoPlayer(videoD.value);
	let res = await getBusinessResultListFun('test01_v1_m1');

	points = new CesiumPointManager(earthStore.getViewer());
	points.loadLabels(res);
	points.enableClustering(true);
	points.addClickEventHandler(debugfun);
});

onUnmounted(() => {
	DroneControlStore.closeUAVVideoShow();
	points.unloadLabels();
	points.removeClickEventHandler();
	document.removeEventListener('mousemove', onDrag);
	document.removeEventListener('mouseup', stopDrag);
});

const debugfun = (id) => {
	console.log(id);
};

const getVideoInfoFun = async (id) => {
	const res = await getVideoInfoByTaskId({ taskId: id });

	DroneControlStore.setData(res.data[0]['videoList']);
};
const getBusinessResultListFun = async (videoResultId: any) => {
	const res = await getBusinessResultList({ videoResultId: videoResultId });
	console.log(res);
	return res.data;
};
// 切换播放状态
const togglePlay = () => {
	const videoDOM = videoD.value;
	if (!videoDOM) return;

	if (playing.value) {
		videoControler.value.pause();
		videoDOM.pause();
	} else {
		videoControler.value.start();
		setTimeout(() => videoDOM.play(), 200);
	}

	playing.value = !playing.value;
};

// 更新播放时间
const updateTime = () => {
	if (videoD.value) {
		currentTime.value = videoD.value.currentTime;
	}
};

// 视频加载完成后，获取总时长
const onLoadedMetadata = () => {
	if (videoD.value) {
		duration.value = videoD.value.duration;
	}
};

// 拖动进度条调整播放进度
const seekTo = () => {
	if (videoD.value) {
		videoD.value.currentTime = currentTime.value;
		videoControler.value.moveToByTime(currentTime.value);
	}
};

// 格式化时间（秒 -> 分钟:秒）
const formatTime = (seconds) => {
	const min = Math.floor(seconds / 60);
	const sec = Math.floor(seconds % 60);
	return `${String(min).padStart(2, '0')}:${String(sec).padStart(2, '0')}`;
};

// 拖拽功能
let offsetX = 0,
	offsetY = 0,
	isDragging = false;

const initDrag = () => {
	const box = dragBox.value;
	const handle = dragHandle.value;

	box.style.left = `20px`;
	box.style.top = `20px`;

	handle.addEventListener('mousedown', (e) => {
		isDragging = true;
		offsetX = e.clientX - box.getBoundingClientRect().left;
		offsetY = e.clientY - box.getBoundingClientRect().top;

		document.addEventListener('mousemove', onDrag);
		document.addEventListener('mouseup', stopDrag);
	});
};

const onDrag = (e) => {
	if (isDragging) {
		const box = dragBox.value;
		box.style.left = `${e.clientX - offsetX}px`;
		box.style.top = `${e.clientY - offsetY}px`;
	}
};

const stopDrag = () => {
	isDragging = false;
	document.removeEventListener('mousemove', onDrag);
	document.removeEventListener('mouseup', stopDrag);
};
</script>

<style lang="less" scoped>
.video-control-container {
	position: fixed;
	top: 20px;
	left: 20px;
	z-index: 1000;
	display: flex;
	flex-direction: column;
	align-items: center;
	background: rgb(2, 49, 52);
}

.video-play-box {
	position: fixed;
	background: rgb(2, 49, 52);
	border-radius: 12px;
	padding: 15px;
	box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
	display: flex;
	flex-direction: column;
	align-items: center;
	cursor: grab;
}

.video-control-title {
	width: 100%;
	text-align: center;
	color: #fff;
	font-size: 20px;
	font-weight: bold;
	margin-bottom: 5px;
	padding: 5px;
	cursor: grab;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 8px;
}

.video-control-title:active {
	cursor: grabbing;
}

.video {
	width: 500px;
	border-radius: 8px;
}

.video-controls {
	display: flex;
	align-items: center;
	gap: 10px;
	width: 100%;
	margin-top: 10px;
}

button {
	padding: 8px 16px;
	font-size: 16px;
	border: none;
	border-radius: 5px;
	cursor: pointer;
	background-color: #007bff;
	color: white;
	transition: 0.3s;
}

button:hover {
	background-color: #0056b3;
}

input[type='range'] {
	width: 200px;
	cursor: pointer;
}

.video-time {
	color: #fff;
	font-size: 14px;
}
</style>
