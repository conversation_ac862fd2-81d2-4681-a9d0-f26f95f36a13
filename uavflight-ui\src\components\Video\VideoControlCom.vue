<template>
	<div class="video-control-container">
		<!-- 遮罩层 -->
		<div v-if="loading" class="loading-mask">
			<el-table
				v-loading="isLoading"
				element-loading-background="rgba(0, 0, 0, 0.5)"
				element-loading-text="拼命加载中···"
				class="loading-box"
			></el-table>
		</div>
		<div class="video-play-box" ref="dragBox">
			<!-- 拖动窗口标题 -->
			<div class="video-control-title" ref="dragHandle">无人机视频浏览</div>
			<!-- 视频 -->
			<video class="video" ref="videoD" @timeupdate="updateTime" @loadedmetadata="onLoadedMetadata">
				<source :src="videoUrl" type="video/mp4" />
			</video>

			<!-- 视频控制栏 -->
			<div class="video-controls">
				<!-- 播放/暂停按钮 -->
				<button @click="togglePlay" style="background-color: #2a7729">
					{{ playing ? '暂停' : '播放' }}
				</button>

				<!-- 进度条 -->
				<input type="range" min="0" :max="duration" step="0.1" v-model="currentTime" @input="seekTo" style="color: #2a7729" />

				<!-- 视频时间显示 -->
				<span class="video-time"> {{ formatTime(currentTime) }} / {{ formatTime(duration) }} </span>
				<!-- 返回按钮 -->
				<button class="return-button" @click="emitReturn">结束播放</button>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
// 导入所需的Vue组件和工具
import { ref, onMounted, onUnmounted } from 'vue';
import { useDroneControlStore } from '/@/stores/droneControl/droneControl';
import { getBusinessResultList, getRealTimeTaskList, getVideoInfoByTaskId, getRealEventList } from '/@/api/admin/bigScreen';
import CesiumPointManager from '/@/utils/map/CesiumPointManager';
import { useEarthStore } from '/@/stores/earth';

// 状态管理
const DroneControlStore = useDroneControlStore();
const earthStore = useEarthStore();
let points = {};
const loading = ref(true); //加载状态
const isLoading = ref(true);
const videoD = ref(null);
const videoControler = ref({});
const currentTime = ref(0);
const duration = ref(0);
const playing = ref(false);
onMounted(async () => {
	try {
		initDrag();
		await getVideoInfoFun(DroneControlStore.taskId);
		await getBusinessResultListFun(DroneControlStore.taskId + '_v1_MYolov8n');
		console.log("taskId",DroneControlStore.taskId)
		setTimeout(() => {

			videoControler.value = DroneControlStore.videoControler;
			videoControler.value.initVideoPlayer(videoD.value);
			points = new CesiumPointManager(earthStore.getViewer());
			earthStore.toggleEventLayers(DroneControlStore.taskId,true)
			// points.loadLabels(res);
			points.enableClustering(true);
			points.addClickEventHandler(debugfun);
			// 加载完成，移除遮罩层
			loading.value = false;
			isLoading.value = false;
		}, 500);
	} catch (error) {
		console.error('加载出错:', error);
		// 出错时也移除遮罩层
		loading.value = false;
	}
});

/**
 * 组件卸载时的清理工作
 */
onUnmounted(() => {
	DroneControlStore.closeUAVVideoShow();
	// console.log('closeUAVVideoShow');
	// points.unloadLabels();
	points.removeClickEventHandler();
	earthStore.toggleEventLayers(DroneControlStore.taskId,false)
	document.removeEventListener('mousemove', onDrag);
	document.removeEventListener('mouseup', stopDrag);
});

// 调试函数
const debugfun = (id: any) => {
	// console.log(id);
};

const videoUrl = ref('');
/**
 * 获取视频信息
 * @param id 任务ID
 */
const getVideoInfoFun = async (id: string) => {
	const res = await getVideoInfoByTaskId({ taskId: id });
	videoUrl.value = res.data[0].processedVideoPath;
	nextTick(() => {
		videoD.value?.load();
	});
	DroneControlStore.setData(res.data[0]['videoList']);
};

/**
 * 获取业务结果列表
 * @param videoResultId 视频结果ID
 */
const getBusinessResultListFun = async (videoResultId: any) => {
	// const res = await getBusinessResultList({ videoResultId: videoResultId });
	// console.log(res);
	// return res.data;
};

/**
 * 切换视频播放/暂停状态
 */
const togglePlay = () => {
	const videoDOM = videoD.value;
	if (!videoDOM) return;

	if (playing.value) {
		// 只需要调用videoControler的暂停方法
		// videoControler内部会处理视频暂停和无人机停止
		videoControler.value.pause();
	} else {
		// 只需要调用videoControler的开始方法
		// videoControler内部会处理视频播放和无人机同步移动
		videoControler.value.start();
	}

	playing.value = !playing.value;
};

/**
 * 更新视频当前播放时间
 */
const updateTime = () => {
	if (videoD.value) {
		// 只更新UI显示的当前时间，不需要手动同步无人机位置
		// 因为videoControler内部已经监听了视频的timeupdate事件
		currentTime.value = videoD.value.currentTime;
	}
};

/**
 * 视频元数据加载完成后的处理
 */
const onLoadedMetadata = () => {
	if (videoD.value) {
		duration.value = videoD.value.duration;
	}
};

/**
 * 返回任务列表处理
 */
const emitReturn = () => {
	if (points && typeof points.stopLoading === 'function') {
		points.stopLoading();
		// clearFormAndData();
		// isDialogVisible.value = false;
	}
	DroneControlStore.DronePanelShow = !DroneControlStore.DronePanelShow;
};

/**
 * 视频进度跳转
 */
const seekTo = () => {
	if (videoD.value) {
		// 只需要调用videoControler的moveToByTime方法
		// videoControler内部会处理视频时间和无人机位置的同步
		videoControler.value.moveToByTime(currentTime.value);
		
		// 不再需要手动设置视频时间，因为moveToByTime会设置
		// videoD.value.currentTime = currentTime.value;
	}
};

/**
 * 时间格式化
 * @param seconds 秒数
 * @returns 格式化后的时间字符串 (MM:SS)
 */
const formatTime = (seconds: number) => {
	const min = Math.floor(seconds / 60);
	const sec = Math.floor(seconds % 60);
	return `${String(min).padStart(2, '0')}:${String(sec).padStart(2, '0')}`;
};

// 拖拽相关变量
let offsetX = 0,
	offsetY = 0,
	isDragging = false;

/**
 * 初始化拖拽功能
 */
const dragBox = ref(null);
const dragHandle = ref(null);
const initDrag = () => {
	const box = dragBox.value;
	const handle = dragHandle.value;

	box.style.left = `65%`;
	box.style.top = `40%`;

	handle.addEventListener('mousedown', (e: { clientX: number; clientY: number }) => {
		isDragging = true;
		offsetX = e.clientX - box.getBoundingClientRect().left;
		offsetY = e.clientY - box.getBoundingClientRect().top;

		document.addEventListener('mousemove', onDrag);
		document.addEventListener('mouseup', stopDrag);
	});
};

/**
 * 处理拖拽移动
 */
const onDrag = (e: { clientX: number; clientY: number }) => {
	if (isDragging) {
		const box = dragBox.value;
		box.style.left = `${e.clientX - offsetX}px`;
		box.style.top = `${e.clientY - offsetY}px`;
	}
};

/**
 * 停止拖拽
 */
const stopDrag = () => {
	isDragging = false;
	document.removeEventListener('mousemove', onDrag);
	document.removeEventListener('mouseup', stopDrag);
};
</script>

<style lang="less" scoped>
.video-control-container {
	position: fixed;
	top: 20px;
	left: 20px;
	z-index: 1000;
	display: flex;
	flex-direction: column;
	align-items: center;
	background: rgb(2, 49, 52);
}

.video-play-box {
	position: fixed;
	background: rgb(2, 49, 52);
	border-radius: 12px;
	padding: 15px;
	box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
	display: flex;
	flex-direction: column;
	align-items: center;
	cursor: grab;
}

.video-control-title {
	width: 100%;
	text-align: center;
	color: #fff;
	font-size: 20px;
	font-weight: bold;
	margin-bottom: 5px;
	padding: 5px;
	cursor: grab;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 8px;
}

.video-control-title:active {
	cursor: grabbing;
}

.video {
	width: 480px;
	border-radius: 8px;
}

.video-controls {
	display: flex;
	align-items: center;
	gap: 10px;
	width: 100%;
	margin-top: 10px;
}

button {
	padding: 8px 16px;
	font-size: 16px;
	border: none;
	border-radius: 5px;
	cursor: pointer;
	background-color: #007bff;
	color: white;
	transition: 0.3s;
}

button:hover {
	background-color: #0056b3;
}

input[type='range'] {
	width: 40%;
	cursor: pointer;
}

.video-time {
	color: #fff;
	font-size: 14px;
}
.return-button {
	padding: 8px 16px;
	font-size: 14px;
	border: none;
	border-radius: 5px;
	cursor: pointer;
	background-color: #2a7729;
	color: white;
	transition: 0.3s;
	margin-left: 10px;
}

.return-button:hover {
	background-color: #30862e;
}
.loading-mask {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1001; /* 确保遮罩层在最上层 */
}
.loading-box {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.2);
	position: absolute;
	top: 0;
	left: 0;
}
</style>
