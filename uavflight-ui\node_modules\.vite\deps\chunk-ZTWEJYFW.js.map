{"version": 3, "sources": ["../../ol/source/wms.js", "../../ol/source/TileWMS.js"], "sourcesContent": ["/**\n * @module ol/source/wms\n */\n\n/**\n * Default WMS version.\n * @type {string}\n */\nexport const DEFAULT_VERSION = '1.3.0';\n\n/**\n * @api\n * @typedef {'carmentaserver' | 'geoserver' | 'mapserver' | 'qgis'} ServerType\n * Set the server type to use implementation-specific parameters beyond the WMS specification.\n *  - `'carmentaserver'`: HiDPI support for [Carmenta Server](https://www.carmenta.com/en/products/carmenta-server)\n *  - `'geoserver'`: HiDPI support for [GeoServer](https://geoserver.org/)\n *  - `'mapserver'`: HiDPI support for [MapServer](https://mapserver.org/)\n *  - `'qgis'`: HiDPI support for [QGIS](https://qgis.org/)\n */\n", "/**\n * @module ol/source/TileWMS\n */\n\nimport TileImage from './TileImage.js';\nimport {DEFAULT_VERSION} from './wms.js';\nimport {appendParams} from '../uri.js';\nimport {assert} from '../asserts.js';\nimport {buffer, createEmpty} from '../extent.js';\nimport {buffer as bufferSize, scale as scaleSize, toSize} from '../size.js';\nimport {calculateSourceResolution} from '../reproj.js';\nimport {compareVersions} from '../string.js';\nimport {get as getProjection, transform, transformExtent} from '../proj.js';\nimport {modulo} from '../math.js';\nimport {hash as tileCoordHash} from '../tilecoord.js';\n\n/**\n * @typedef {Object} Options\n * @property {import(\"./Source.js\").AttributionLike} [attributions] Attributions.\n * @property {boolean} [attributionsCollapsible=true] Attributions are collapsible.\n * @property {number} [cacheSize] Initial tile cache size. Will auto-grow to hold at least the number of tiles in the viewport.\n * @property {null|string} [crossOrigin] The `crossOrigin` attribute for loaded images.  Note that\n * you must provide a `crossOrigin` value if you want to access pixel data with the Canvas renderer.\n * See https://developer.mozilla.org/en-US/docs/Web/HTML/CORS_enabled_image for more detail.\n * @property {boolean} [interpolate=true] Use interpolated values when resampling.  By default,\n * linear interpolation is used when resampling.  Set to false to use the nearest neighbor instead.\n * @property {Object<string,*>} params WMS request parameters.\n * At least a `LAYERS` param is required. `STYLES` is\n * `''` by default. `VERSION` is `1.3.0` by default. `WIDTH`, `HEIGHT`, `BBOX`\n * and `CRS` (`SRS` for WMS version < 1.3.0) will be set dynamically.\n * @property {number} [gutter=0]\n * The size in pixels of the gutter around image tiles to ignore. By setting\n * this property to a non-zero value, images will be requested that are wider\n * and taller than the tile size by a value of `2 x gutter`.\n * Using a non-zero value allows artifacts of rendering at tile edges to be\n * ignored. If you control the WMS service it is recommended to address\n * \"artifacts at tile edges\" issues by properly configuring the WMS service. For\n * example, MapServer has a `tile_map_edge_buffer` configuration parameter for\n * this. See https://mapserver.org/output/tile_mode.html.\n * @property {boolean} [hidpi=true] Use the `ol/Map#pixelRatio` value when requesting\n * the image from the remote server.\n * @property {import(\"../proj.js\").ProjectionLike} [projection] Projection. Default is the view projection.\n * @property {number} [reprojectionErrorThreshold=0.5] Maximum allowed reprojection error (in pixels).\n * Higher values can increase reprojection performance, but decrease precision.\n * @property {typeof import(\"../ImageTile.js\").default} [tileClass] Class used to instantiate image tiles.\n * Default is {@link module:ol/ImageTile~ImageTile}.\n * @property {import(\"../tilegrid/TileGrid.js\").default} [tileGrid] Tile grid. Base this on the resolutions,\n * tilesize and extent supported by the server.\n * If this is not defined, a default grid will be used: if there is a projection\n * extent, the grid will be based on that; if not, a grid based on a global\n * extent with origin at 0,0 will be used.\n * @property {import(\"./wms.js\").ServerType} [serverType] The type of\n * the remote WMS server: `mapserver`, `geoserver`, `carmentaserver`, or `qgis`.\n * Only needed if `hidpi` is `true`.\n * @property {import(\"../Tile.js\").LoadFunction} [tileLoadFunction] Optional function to load a tile given a URL. The default is\n * ```js\n * function(imageTile, src) {\n *   imageTile.getImage().src = src;\n * };\n * ```\n * @property {string} [url] WMS service URL.\n * @property {Array<string>} [urls] WMS service urls.\n * Use this instead of `url` when the WMS supports multiple urls for GetMap requests.\n * @property {boolean} [wrapX=true] Whether to wrap the world horizontally.\n * When set to `false`, only one world\n * will be rendered. When `true`, tiles will be requested for one world only,\n * but they will be wrapped horizontally to render multiple worlds.\n * @property {number} [transition] Duration of the opacity transition for rendering.\n * To disable the opacity transition, pass `transition: 0`.\n * @property {number|import(\"../array.js\").NearestDirectionFunction} [zDirection=0]\n * Choose whether to use tiles with a higher or lower zoom level when between integer\n * zoom levels. See {@link module:ol/tilegrid/TileGrid~TileGrid#getZForResolution}.\n */\n\n/**\n * @classdesc\n * Layer source for tile data from WMS servers.\n * @api\n */\nclass TileWMS extends TileImage {\n  /**\n   * @param {Options} [options] Tile WMS options.\n   */\n  constructor(options) {\n    options = options ? options : /** @type {Options} */ ({});\n\n    const params = Object.assign({}, options.params);\n\n    const transparent = 'TRANSPARENT' in params ? params['TRANSPARENT'] : true;\n\n    super({\n      attributions: options.attributions,\n      attributionsCollapsible: options.attributionsCollapsible,\n      cacheSize: options.cacheSize,\n      crossOrigin: options.crossOrigin,\n      interpolate: options.interpolate,\n      opaque: !transparent,\n      projection: options.projection,\n      reprojectionErrorThreshold: options.reprojectionErrorThreshold,\n      tileClass: options.tileClass,\n      tileGrid: options.tileGrid,\n      tileLoadFunction: options.tileLoadFunction,\n      url: options.url,\n      urls: options.urls,\n      wrapX: options.wrapX !== undefined ? options.wrapX : true,\n      transition: options.transition,\n      zDirection: options.zDirection,\n    });\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.gutter_ = options.gutter !== undefined ? options.gutter : 0;\n\n    /**\n     * @private\n     * @type {!Object}\n     */\n    this.params_ = params;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.v13_ = true;\n\n    /**\n     * @private\n     * @type {import(\"./wms.js\").ServerType}\n     */\n    this.serverType_ = options.serverType;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.hidpi_ = options.hidpi !== undefined ? options.hidpi : true;\n\n    /**\n     * @private\n     * @type {import(\"../extent.js\").Extent}\n     */\n    this.tmpExtent_ = createEmpty();\n\n    this.updateV13_();\n    this.setKey(this.getKeyForParams_());\n  }\n\n  /**\n   * Return the GetFeatureInfo URL for the passed coordinate, resolution, and\n   * projection. Return `undefined` if the GetFeatureInfo URL cannot be\n   * constructed.\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @param {number} resolution Resolution.\n   * @param {import(\"../proj.js\").ProjectionLike} projection Projection.\n   * @param {!Object} params GetFeatureInfo params. `INFO_FORMAT` at least should\n   *     be provided. If `QUERY_LAYERS` is not provided then the layers specified\n   *     in the `LAYERS` parameter will be used. `VERSION` should not be\n   *     specified here.\n   * @return {string|undefined} GetFeatureInfo URL.\n   * @api\n   */\n  getFeatureInfoUrl(coordinate, resolution, projection, params) {\n    const projectionObj = getProjection(projection);\n    const sourceProjectionObj = this.getProjection();\n\n    let tileGrid = this.getTileGrid();\n    if (!tileGrid) {\n      tileGrid = this.getTileGridForProjection(projectionObj);\n    }\n\n    const z = tileGrid.getZForResolution(resolution, this.zDirection);\n    const tileCoord = tileGrid.getTileCoordForCoordAndZ(coordinate, z);\n\n    if (tileGrid.getResolutions().length <= tileCoord[0]) {\n      return undefined;\n    }\n\n    let tileResolution = tileGrid.getResolution(tileCoord[0]);\n    let tileExtent = tileGrid.getTileCoordExtent(tileCoord, this.tmpExtent_);\n    let tileSize = toSize(tileGrid.getTileSize(tileCoord[0]), this.tmpSize);\n\n    const gutter = this.gutter_;\n    if (gutter !== 0) {\n      tileSize = bufferSize(tileSize, gutter, this.tmpSize);\n      tileExtent = buffer(tileExtent, tileResolution * gutter, tileExtent);\n    }\n\n    if (sourceProjectionObj && sourceProjectionObj !== projectionObj) {\n      tileResolution = calculateSourceResolution(\n        sourceProjectionObj,\n        projectionObj,\n        coordinate,\n        tileResolution\n      );\n      tileExtent = transformExtent(\n        tileExtent,\n        projectionObj,\n        sourceProjectionObj\n      );\n      coordinate = transform(coordinate, projectionObj, sourceProjectionObj);\n    }\n\n    const baseParams = {\n      'SERVICE': 'WMS',\n      'VERSION': DEFAULT_VERSION,\n      'REQUEST': 'GetFeatureInfo',\n      'FORMAT': 'image/png',\n      'TRANSPARENT': true,\n      'QUERY_LAYERS': this.params_['LAYERS'],\n    };\n    Object.assign(baseParams, this.params_, params);\n\n    const x = Math.floor((coordinate[0] - tileExtent[0]) / tileResolution);\n    const y = Math.floor((tileExtent[3] - coordinate[1]) / tileResolution);\n\n    baseParams[this.v13_ ? 'I' : 'X'] = x;\n    baseParams[this.v13_ ? 'J' : 'Y'] = y;\n\n    return this.getRequestUrl_(\n      tileCoord,\n      tileSize,\n      tileExtent,\n      1,\n      sourceProjectionObj || projectionObj,\n      baseParams\n    );\n  }\n\n  /**\n   * Return the GetLegendGraphic URL, optionally optimized for the passed\n   * resolution and possibly including any passed specific parameters. Returns\n   * `undefined` if the GetLegendGraphic URL cannot be constructed.\n   *\n   * @param {number} [resolution] Resolution. If set to undefined, `SCALE`\n   *     will not be calculated and included in URL.\n   * @param {Object} [params] GetLegendGraphic params. If `LAYER` is set, the\n   *     request is generated for this wms layer, else it will try to use the\n   *     configured wms layer. Default `FORMAT` is `image/png`.\n   *     `VERSION` should not be specified here.\n   * @return {string|undefined} GetLegendGraphic URL.\n   * @api\n   */\n  getLegendUrl(resolution, params) {\n    if (this.urls[0] === undefined) {\n      return undefined;\n    }\n\n    const baseParams = {\n      'SERVICE': 'WMS',\n      'VERSION': DEFAULT_VERSION,\n      'REQUEST': 'GetLegendGraphic',\n      'FORMAT': 'image/png',\n    };\n\n    if (params === undefined || params['LAYER'] === undefined) {\n      const layers = this.params_.LAYERS;\n      const isSingleLayer = !Array.isArray(layers) || layers.length === 1;\n      if (!isSingleLayer) {\n        return undefined;\n      }\n      baseParams['LAYER'] = layers;\n    }\n\n    if (resolution !== undefined) {\n      const mpu = this.getProjection()\n        ? this.getProjection().getMetersPerUnit()\n        : 1;\n      const pixelSize = 0.00028;\n      baseParams['SCALE'] = (resolution * mpu) / pixelSize;\n    }\n\n    Object.assign(baseParams, params);\n\n    return appendParams(/** @type {string} */ (this.urls[0]), baseParams);\n  }\n\n  /**\n   * @return {number} Gutter.\n   */\n  getGutter() {\n    return this.gutter_;\n  }\n\n  /**\n   * Get the user-provided params, i.e. those passed to the constructor through\n   * the \"params\" option, and possibly updated using the updateParams method.\n   * @return {Object} Params.\n   * @api\n   */\n  getParams() {\n    return this.params_;\n  }\n\n  /**\n   * @param {import(\"../tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n   * @param {import(\"../size.js\").Size} tileSize Tile size.\n   * @param {import(\"../extent.js\").Extent} tileExtent Tile extent.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @param {Object} params Params.\n   * @return {string|undefined} Request URL.\n   * @private\n   */\n  getRequestUrl_(\n    tileCoord,\n    tileSize,\n    tileExtent,\n    pixelRatio,\n    projection,\n    params\n  ) {\n    const urls = this.urls;\n    if (!urls) {\n      return undefined;\n    }\n\n    params['WIDTH'] = tileSize[0];\n    params['HEIGHT'] = tileSize[1];\n\n    params[this.v13_ ? 'CRS' : 'SRS'] = projection.getCode();\n\n    if (!('STYLES' in this.params_)) {\n      params['STYLES'] = '';\n    }\n\n    if (pixelRatio != 1) {\n      switch (this.serverType_) {\n        case 'geoserver':\n          const dpi = (90 * pixelRatio + 0.5) | 0;\n          if ('FORMAT_OPTIONS' in params) {\n            params['FORMAT_OPTIONS'] += ';dpi:' + dpi;\n          } else {\n            params['FORMAT_OPTIONS'] = 'dpi:' + dpi;\n          }\n          break;\n        case 'mapserver':\n          params['MAP_RESOLUTION'] = 90 * pixelRatio;\n          break;\n        case 'carmentaserver':\n        case 'qgis':\n          params['DPI'] = 90 * pixelRatio;\n          break;\n        default: // Unknown `serverType` configured\n          assert(false, 52);\n          break;\n      }\n    }\n\n    const axisOrientation = projection.getAxisOrientation();\n    const bbox = tileExtent;\n    if (this.v13_ && axisOrientation.substr(0, 2) == 'ne') {\n      let tmp;\n      tmp = tileExtent[0];\n      bbox[0] = tileExtent[1];\n      bbox[1] = tmp;\n      tmp = tileExtent[2];\n      bbox[2] = tileExtent[3];\n      bbox[3] = tmp;\n    }\n    params['BBOX'] = bbox.join(',');\n\n    let url;\n    if (urls.length == 1) {\n      url = urls[0];\n    } else {\n      const index = modulo(tileCoordHash(tileCoord), urls.length);\n      url = urls[index];\n    }\n    return appendParams(url, params);\n  }\n\n  /**\n   * Get the tile pixel ratio for this source.\n   * @param {number} pixelRatio Pixel ratio.\n   * @return {number} Tile pixel ratio.\n   */\n  getTilePixelRatio(pixelRatio) {\n    return !this.hidpi_ || this.serverType_ === undefined ? 1 : pixelRatio;\n  }\n\n  /**\n   * @private\n   * @return {string} The key for the current params.\n   */\n  getKeyForParams_() {\n    let i = 0;\n    const res = [];\n    for (const key in this.params_) {\n      res[i++] = key + '-' + this.params_[key];\n    }\n    return res.join('/');\n  }\n\n  /**\n   * Update the user-provided params.\n   * @param {Object} params Params.\n   * @api\n   */\n  updateParams(params) {\n    Object.assign(this.params_, params);\n    this.updateV13_();\n    this.setKey(this.getKeyForParams_());\n  }\n\n  /**\n   * @private\n   */\n  updateV13_() {\n    const version = this.params_['VERSION'] || DEFAULT_VERSION;\n    this.v13_ = compareVersions(version, '1.3') >= 0;\n  }\n\n  /**\n   * @param {import(\"../tilecoord.js\").TileCoord} tileCoord The tile coordinate\n   * @param {number} pixelRatio The pixel ratio\n   * @param {import(\"../proj/Projection.js\").default} projection The projection\n   * @return {string|undefined} The tile URL\n   * @override\n   */\n  tileUrlFunction(tileCoord, pixelRatio, projection) {\n    let tileGrid = this.getTileGrid();\n    if (!tileGrid) {\n      tileGrid = this.getTileGridForProjection(projection);\n    }\n\n    if (tileGrid.getResolutions().length <= tileCoord[0]) {\n      return undefined;\n    }\n\n    if (pixelRatio != 1 && (!this.hidpi_ || this.serverType_ === undefined)) {\n      pixelRatio = 1;\n    }\n\n    const tileResolution = tileGrid.getResolution(tileCoord[0]);\n    let tileExtent = tileGrid.getTileCoordExtent(tileCoord, this.tmpExtent_);\n    let tileSize = toSize(tileGrid.getTileSize(tileCoord[0]), this.tmpSize);\n\n    const gutter = this.gutter_;\n    if (gutter !== 0) {\n      tileSize = bufferSize(tileSize, gutter, this.tmpSize);\n      tileExtent = buffer(tileExtent, tileResolution * gutter, tileExtent);\n    }\n\n    if (pixelRatio != 1) {\n      tileSize = scaleSize(tileSize, pixelRatio, this.tmpSize);\n    }\n\n    const baseParams = {\n      'SERVICE': 'WMS',\n      'VERSION': DEFAULT_VERSION,\n      'REQUEST': 'GetMap',\n      'FORMAT': 'image/png',\n      'TRANSPARENT': true,\n    };\n    Object.assign(baseParams, this.params_);\n\n    return this.getRequestUrl_(\n      tileCoord,\n      tileSize,\n      tileExtent,\n      pixelRatio,\n      projection,\n      baseParams\n    );\n  }\n}\n\nexport default TileWMS;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQO,IAAM,kBAAkB;;;ACuE/B,IAAM,UAAN,cAAsB,kBAAU;AAAA;AAAA;AAAA;AAAA,EAI9B,YAAY,SAAS;AACnB,cAAU,UAAU;AAAA;AAAA,MAAkC,CAAC;AAAA;AAEvD,UAAM,SAAS,OAAO,OAAO,CAAC,GAAG,QAAQ,MAAM;AAE/C,UAAM,cAAc,iBAAiB,SAAS,OAAO,aAAa,IAAI;AAEtE,UAAM;AAAA,MACJ,cAAc,QAAQ;AAAA,MACtB,yBAAyB,QAAQ;AAAA,MACjC,WAAW,QAAQ;AAAA,MACnB,aAAa,QAAQ;AAAA,MACrB,aAAa,QAAQ;AAAA,MACrB,QAAQ,CAAC;AAAA,MACT,YAAY,QAAQ;AAAA,MACpB,4BAA4B,QAAQ;AAAA,MACpC,WAAW,QAAQ;AAAA,MACnB,UAAU,QAAQ;AAAA,MAClB,kBAAkB,QAAQ;AAAA,MAC1B,KAAK,QAAQ;AAAA,MACb,MAAM,QAAQ;AAAA,MACd,OAAO,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAAA,MACrD,YAAY,QAAQ;AAAA,MACpB,YAAY,QAAQ;AAAA,IACtB,CAAC;AAMD,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAM/D,SAAK,UAAU;AAMf,SAAK,OAAO;AAMZ,SAAK,cAAc,QAAQ;AAM3B,SAAK,SAAS,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAM5D,SAAK,aAAa,YAAY;AAE9B,SAAK,WAAW;AAChB,SAAK,OAAO,KAAK,iBAAiB,CAAC;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,kBAAkB,YAAY,YAAY,YAAY,QAAQ;AAC5D,UAAM,gBAAgB,IAAc,UAAU;AAC9C,UAAM,sBAAsB,KAAK,cAAc;AAE/C,QAAI,WAAW,KAAK,YAAY;AAChC,QAAI,CAAC,UAAU;AACb,iBAAW,KAAK,yBAAyB,aAAa;AAAA,IACxD;AAEA,UAAM,IAAI,SAAS,kBAAkB,YAAY,KAAK,UAAU;AAChE,UAAM,YAAY,SAAS,yBAAyB,YAAY,CAAC;AAEjE,QAAI,SAAS,eAAe,EAAE,UAAU,UAAU,CAAC,GAAG;AACpD,aAAO;AAAA,IACT;AAEA,QAAI,iBAAiB,SAAS,cAAc,UAAU,CAAC,CAAC;AACxD,QAAI,aAAa,SAAS,mBAAmB,WAAW,KAAK,UAAU;AACvE,QAAI,WAAW,OAAO,SAAS,YAAY,UAAU,CAAC,CAAC,GAAG,KAAK,OAAO;AAEtE,UAAM,SAAS,KAAK;AACpB,QAAI,WAAW,GAAG;AAChB,iBAAWA,QAAW,UAAU,QAAQ,KAAK,OAAO;AACpD,mBAAa,OAAO,YAAY,iBAAiB,QAAQ,UAAU;AAAA,IACrE;AAEA,QAAI,uBAAuB,wBAAwB,eAAe;AAChE,uBAAiB;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,mBAAa;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,mBAAa,UAAU,YAAY,eAAe,mBAAmB;AAAA,IACvE;AAEA,UAAM,aAAa;AAAA,MACjB,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,eAAe;AAAA,MACf,gBAAgB,KAAK,QAAQ,QAAQ;AAAA,IACvC;AACA,WAAO,OAAO,YAAY,KAAK,SAAS,MAAM;AAE9C,UAAM,IAAI,KAAK,OAAO,WAAW,CAAC,IAAI,WAAW,CAAC,KAAK,cAAc;AACrE,UAAM,IAAI,KAAK,OAAO,WAAW,CAAC,IAAI,WAAW,CAAC,KAAK,cAAc;AAErE,eAAW,KAAK,OAAO,MAAM,GAAG,IAAI;AACpC,eAAW,KAAK,OAAO,MAAM,GAAG,IAAI;AAEpC,WAAO,KAAK;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,uBAAuB;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,aAAa,YAAY,QAAQ;AAC/B,QAAI,KAAK,KAAK,CAAC,MAAM,QAAW;AAC9B,aAAO;AAAA,IACT;AAEA,UAAM,aAAa;AAAA,MACjB,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,IACZ;AAEA,QAAI,WAAW,UAAa,OAAO,OAAO,MAAM,QAAW;AACzD,YAAM,SAAS,KAAK,QAAQ;AAC5B,YAAM,gBAAgB,CAAC,MAAM,QAAQ,MAAM,KAAK,OAAO,WAAW;AAClE,UAAI,CAAC,eAAe;AAClB,eAAO;AAAA,MACT;AACA,iBAAW,OAAO,IAAI;AAAA,IACxB;AAEA,QAAI,eAAe,QAAW;AAC5B,YAAM,MAAM,KAAK,cAAc,IAC3B,KAAK,cAAc,EAAE,iBAAiB,IACtC;AACJ,YAAM,YAAY;AAClB,iBAAW,OAAO,IAAK,aAAa,MAAO;AAAA,IAC7C;AAEA,WAAO,OAAO,YAAY,MAAM;AAEhC,WAAO;AAAA;AAAA,MAAoC,KAAK,KAAK,CAAC;AAAA,MAAI;AAAA,IAAU;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,eACE,WACA,UACA,YACA,YACA,YACA,QACA;AACA,UAAM,OAAO,KAAK;AAClB,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AAEA,WAAO,OAAO,IAAI,SAAS,CAAC;AAC5B,WAAO,QAAQ,IAAI,SAAS,CAAC;AAE7B,WAAO,KAAK,OAAO,QAAQ,KAAK,IAAI,WAAW,QAAQ;AAEvD,QAAI,EAAE,YAAY,KAAK,UAAU;AAC/B,aAAO,QAAQ,IAAI;AAAA,IACrB;AAEA,QAAI,cAAc,GAAG;AACnB,cAAQ,KAAK,aAAa;AAAA,QACxB,KAAK;AACH,gBAAM,MAAO,KAAK,aAAa,MAAO;AACtC,cAAI,oBAAoB,QAAQ;AAC9B,mBAAO,gBAAgB,KAAK,UAAU;AAAA,UACxC,OAAO;AACL,mBAAO,gBAAgB,IAAI,SAAS;AAAA,UACtC;AACA;AAAA,QACF,KAAK;AACH,iBAAO,gBAAgB,IAAI,KAAK;AAChC;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,KAAK,IAAI,KAAK;AACrB;AAAA,QACF;AACE,iBAAO,OAAO,EAAE;AAChB;AAAA,MACJ;AAAA,IACF;AAEA,UAAM,kBAAkB,WAAW,mBAAmB;AACtD,UAAM,OAAO;AACb,QAAI,KAAK,QAAQ,gBAAgB,OAAO,GAAG,CAAC,KAAK,MAAM;AACrD,UAAI;AACJ,YAAM,WAAW,CAAC;AAClB,WAAK,CAAC,IAAI,WAAW,CAAC;AACtB,WAAK,CAAC,IAAI;AACV,YAAM,WAAW,CAAC;AAClB,WAAK,CAAC,IAAI,WAAW,CAAC;AACtB,WAAK,CAAC,IAAI;AAAA,IACZ;AACA,WAAO,MAAM,IAAI,KAAK,KAAK,GAAG;AAE9B,QAAI;AACJ,QAAI,KAAK,UAAU,GAAG;AACpB,YAAM,KAAK,CAAC;AAAA,IACd,OAAO;AACL,YAAM,QAAQ,OAAO,KAAc,SAAS,GAAG,KAAK,MAAM;AAC1D,YAAM,KAAK,KAAK;AAAA,IAClB;AACA,WAAO,aAAa,KAAK,MAAM;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,YAAY;AAC5B,WAAO,CAAC,KAAK,UAAU,KAAK,gBAAgB,SAAY,IAAI;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB;AACjB,QAAI,IAAI;AACR,UAAM,MAAM,CAAC;AACb,eAAW,OAAO,KAAK,SAAS;AAC9B,UAAI,GAAG,IAAI,MAAM,MAAM,KAAK,QAAQ,GAAG;AAAA,IACzC;AACA,WAAO,IAAI,KAAK,GAAG;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,QAAQ;AACnB,WAAO,OAAO,KAAK,SAAS,MAAM;AAClC,SAAK,WAAW;AAChB,SAAK,OAAO,KAAK,iBAAiB,CAAC;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,UAAM,UAAU,KAAK,QAAQ,SAAS,KAAK;AAC3C,SAAK,OAAO,gBAAgB,SAAS,KAAK,KAAK;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,WAAW,YAAY,YAAY;AACjD,QAAI,WAAW,KAAK,YAAY;AAChC,QAAI,CAAC,UAAU;AACb,iBAAW,KAAK,yBAAyB,UAAU;AAAA,IACrD;AAEA,QAAI,SAAS,eAAe,EAAE,UAAU,UAAU,CAAC,GAAG;AACpD,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,MAAM,CAAC,KAAK,UAAU,KAAK,gBAAgB,SAAY;AACvE,mBAAa;AAAA,IACf;AAEA,UAAM,iBAAiB,SAAS,cAAc,UAAU,CAAC,CAAC;AAC1D,QAAI,aAAa,SAAS,mBAAmB,WAAW,KAAK,UAAU;AACvE,QAAI,WAAW,OAAO,SAAS,YAAY,UAAU,CAAC,CAAC,GAAG,KAAK,OAAO;AAEtE,UAAM,SAAS,KAAK;AACpB,QAAI,WAAW,GAAG;AAChB,iBAAWA,QAAW,UAAU,QAAQ,KAAK,OAAO;AACpD,mBAAa,OAAO,YAAY,iBAAiB,QAAQ,UAAU;AAAA,IACrE;AAEA,QAAI,cAAc,GAAG;AACnB,iBAAW,MAAU,UAAU,YAAY,KAAK,OAAO;AAAA,IACzD;AAEA,UAAM,aAAa;AAAA,MACjB,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,eAAe;AAAA,IACjB;AACA,WAAO,OAAO,YAAY,KAAK,OAAO;AAEtC,WAAO,KAAK;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,kBAAQ;", "names": ["buffer"]}