import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// browser-external:zlib
var require_zlib = __commonJS({
  "browser-external:zlib"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "zlib" has been externalized for browser compatibility. Cannot access "zlib.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});
export default require_zlib();
//# sourceMappingURL=zlib-6GM6MPU7.js.map
