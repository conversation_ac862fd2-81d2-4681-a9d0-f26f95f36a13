import {
  all,
  altKeyOnly,
  altShiftKeysOnly,
  always,
  click,
  doubleClick,
  focus,
  focusWithTabindex,
  mouseActionButton,
  mouseOnly,
  never,
  noModifierKeys,
  penOnly,
  platformModifierKey,
  platformModifierKeyOnly,
  pointerMove,
  primaryAction,
  shiftKeyOnly,
  singleClick,
  targetNotEditable,
  touchOnly
} from "./chunk-A6TNV2SE.js";
import "./chunk-6YBVBLXZ.js";
import "./chunk-G3K3A47D.js";
import "./chunk-NCSZTHHM.js";
import "./chunk-F2MRU6YO.js";
import "./chunk-PLDDJCW6.js";
export {
  all,
  altKeyOnly,
  altShiftKeysOnly,
  always,
  click,
  doubleClick,
  focus,
  focusWithTabindex,
  mouseActionButton,
  mouseOnly,
  never,
  noModifierKeys,
  penOnly,
  platformModifierKey,
  platformModifierKeyOnly,
  pointerMove,
  primaryAction,
  shiftKeyOnly,
  singleClick,
  targetNotEditable,
  touchOnly
};
//# sourceMappingURL=ol_events_condition.js.map
