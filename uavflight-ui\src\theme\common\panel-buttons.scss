/**
 * 面板按钮通用样式
 * 提供统一的面板收缩/展开按钮样式
 */

/* 按钮基础样式 - 垂直方向 */
.panel-button-base {
  position: absolute;
  width: 24px;
  height: 80px;
  background-color: rgba(16, 64, 70, 0.95);
  z-index: 100;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.panel-button-base:hover {
  background-color: rgba(22, 80, 85, 1);
}

.panel-button-base .icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  width: 100%;
  height: 40px;
}

.panel-button-base .el-icon {
  font-size: 16px;
  background-color: rgba(59, 179, 59, 0.9);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
}

/* 按钮基础样式 - 水平方向 */
.panel-button-horizontal {
  // background-color: rgba(16, 64, 70, 0.95);
  background-color: rgba(15, 21, 32, 0.5);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.panel-button-horizontal:hover {
  background-color: rgba(15, 21, 32, 1); /* hover时稍微不透明 */
  transform: translateY(-0%) scale(1.05);
  box-shadow:
    0 6px 18px rgba(0, 0, 0, 0.4),
    0 3px 12px rgba(15, 21, 32, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}


.panel-button-horizontal .icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  width: 100%;
  height: 100%;
}

.panel-button-horizontal .el-icon {
  font-size: 16px;
  // background-color: rgba(59, 179, 59, 0.9);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
} 