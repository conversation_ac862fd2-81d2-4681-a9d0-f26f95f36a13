/*
 * @Author: AI Assistant
 * @Date: 2025-07-15
 * @Description: 快拼图层管理器实现
 *
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved.
 */

import { useOLMapStore } from '/@/stores/olMapStore';
import { LayerConfig, StyleConfig, MapConfig, ViewConfig } from './types';
import OLMap from 'ol/Map';
import { Tile as TileLayer, Vector as VectorLayer, Layer } from 'ol/layer';
import { TileWMS, WMTS, XYZ, Vector as VectorSource } from 'ol/source';
import { Style, Stroke, Fill, Text, Circle } from 'ol/style';
import { WMTSCapabilities } from 'ol/format';
import { optionsFromCapabilities } from 'ol/source/WMTS';
import WMTSTileGrid from 'ol/tilegrid/WMTS';
import { fromLonLat, get as getProjection } from 'ol/proj';
import { getCenter, getTopLeft } from 'ol/extent';
import { GeoJSON } from 'ol/format';
import * as styleUtils from './styleUtils';
import { ElMessage } from 'element-plus';
import axios from 'axios';
import { getGeoserverBaseUrl, getLayerBbox } from '../../utils/geoserver';
import TileState from 'ol/TileState';

// 默认图层样式
const DEFAULT_STYLE = {
  color: '#888888',
  weight: 1,
  opacity: 0.5,
  fillColor: '#88888888',
  fillOpacity: 0.5
};

// ======= 优先级瓦片加载配置 =======

// 瓦片加载超时时间（毫秒）
// 超过这个时间没加载完就认为"卡住"，主动释放
const tileTimeoutMs = 3000;

// 待重试队列（高优先级瓦片的 key 会存到这里）
const retryQueue: string[] = [];

// 正在加载的瓦片集合： key → 定时器ID
// 用来在 onload/onerror 时清理对应的超时定时器
const loadingTiles = new Map<string, number>();

/**
 * 根据 tile 对象生成唯一 key
 * @param tile - 当前加载的瓦片对象
 * @returns 唯一标识，例如 "z/x/y"
 */
function getTileKey(tile: any): string {
  const coord = tile.getTileCoord(); // [z, x, y]
  return coord.join('/');
}

/**
 * 自定义 tileLoadFunction
 * 核心逻辑：
 * 1. 开始加载瓦片时，启动一个超时计时器
 * 2. 如果超时仍未加载完 → 释放并行请求槽位（tile.setState(ERROR)）
 * 3. 释放的瓦片加入 retryQueue，等待优先重试
 */
function priorityTileLoadFunction(tile: any, src: string): void {
  const img = tile.getImage();   // 获取 HTMLImageElement
  const key = getTileKey(tile);  // 唯一标识
  let loaded = false;            // 标记瓦片是否已完成（成功/失败）

  // ======== 瓦片加载成功事件 ========
  img.onload = () => {
    loaded = true;
    const timeoutId = loadingTiles.get(key);
    if (timeoutId) {
      clearTimeout(timeoutId); // 清理超时定时器
    }
    loadingTiles.delete(key);            // 从正在加载集合中移除
    tile.setState(TileState.LOADED);     // 标记为已加载
  };

  // ======== 瓦片加载失败事件 ========
  img.onerror = () => {
    loaded = true;
    const timeoutId = loadingTiles.get(key);
    if (timeoutId) {
      clearTimeout(timeoutId); // 清理超时定时器
    }
    loadingTiles.delete(key);
    tile.setState(TileState.ERROR);      // 标记为加载失败
  };

  // ======== 超时释放机制 ========
  const timeoutId = setTimeout(() => {
    if (!loaded && tile.getState() === TileState.LOADING) {
      console.warn(`Tile ${key} timeout → releasing & queueing retry`);

      // 将该瓦片加入高优先级重试队列
      retryQueue.push(key);

      // 强制标记为 ERROR → 立即释放浏览器并行请求槽位
      tile.setState(TileState.ERROR);
    }
  }, tileTimeoutMs);

  // 把该瓦片加入"正在加载集合"
  loadingTiles.set(key, timeoutId);

  // ======== 正式触发加载 ========
  img.src = src;
}

// 生成随机颜色
function getRandomColor() {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
}

/**
 * 快拼图层管理器类
 */
export default class HDMapLayerManager {
  private map: OLMap | null = null;
  private mapConfig: MapConfig | null = null;
  private styleConfig: StyleConfig | null = null;
  private layers: Map<string, Layer> = new Map();
  private layerSources: Map<string, any> = new Map();
  private layerStyles: Map<string, any> = new Map();
  private defaultWorkspace: string = '';
  private initialView: ViewConfig | null = null;
  private geoserverUrl: string = import.meta.env.VITE_GEOSERVER_URL || getGeoserverBaseUrl();

  /**
   * 构造函数
   * @param mapConfig 地图配置
   * @param styleConfig 样式配置
   */
  constructor(mapConfig: MapConfig | null, styleConfig: StyleConfig | null) {
    // 获取地图实例
    const mapStore = useOLMapStore();
    this.map = mapStore.map?.map || null;
    
    if (!this.map) {
      console.warn('地图实例不存在，快拼图层管理器初始化失败');
      return;
    }

    this.mapConfig = mapConfig;
    this.styleConfig = styleConfig;
    
    // 解析初始配置
    if (mapConfig?.mapConfig?.queryConfig?.defaultWorkspace) {
      this.defaultWorkspace = mapConfig.mapConfig.queryConfig.defaultWorkspace;
    }
    
    if (mapConfig?.mapConfig?.initialView) {
      this.initialView = {
        center: mapConfig.mapConfig.initialView.center || [0, 0],
        zoom: mapConfig.mapConfig.initialView.zoom || 4,
        duration: mapConfig.mapConfig.initialView.duration || 1000
      };
    }
    
    // 初始加载图层
    if (mapConfig?.layers) {
      mapConfig.layers.forEach(layer => {
        if (layer.initialLoad) {
          this.addLayer(layer);
        }
      });
    }
  }

  /**
   * 添加图层
   * @param layer 图层配置
   * @returns 添加的图层
   */
  addLayer(layer: LayerConfig): Layer | null {
    if (!this.map) return null;
    
    // 检查是否已存在
    if (this.layers.has(layer.id)) {
      return this.layers.get(layer.id) || null;
    }
    
    try {
      let olLayer: Layer | null = null;
      
      // 解析图层ID，如果格式是workspace:layerName，则提取workspace和layerName
      if (layer.id && layer.id.includes(':') && (!layer.workspace || !layer.layerName)) {
        const parts = layer.id.split(':');
        if (parts.length === 2) {
          console.log(`从图层ID ${layer.id} 解析出 workspace=${parts[0]}, layerName=${parts[1]}`);
          layer.workspace = parts[0];
          layer.layerName = parts[1];
        }
      }
      
      // 根据图层类型创建不同的图层
      switch (layer.type) {
        case 'raster':
          olLayer = this.createRasterLayer(layer);
          break;
        case 'vector':
          olLayer = this.createVectorLayer(layer);
          break;
        default:
          console.warn(`未支持的图层类型: ${layer.type}`);
          return null;
      }
      
      if (!olLayer) return null;
      
      // 设置图层属性
      olLayer.set('id', layer.id);
      olLayer.set('name', layer.name);
      olLayer.set('type', layer.type);
      
      // 重要：设置workspace和layerName属性到图层实例
      if (layer.workspace) {
        olLayer.set('workspace', layer.workspace);
      }
      if (layer.layerName) {
        olLayer.set('layerName', layer.layerName);
      }
      
      // 保存图层实例
      this.layers.set(layer.id, olLayer);
      
      // 添加到地图
      this.map.addLayer(olLayer);
      
      // 设置透明度
      const opacity = layer.opacity !== undefined ? layer.opacity / 100 : 1;
      olLayer.setOpacity(opacity);
      
      return olLayer;
    } catch (error) {
      console.error(`添加图层失败: ${layer.name}`, error);
      return null;
    }
  }

  /**
   * 创建栅格图层
   * @param layer 图层配置
   * @returns 栅格图层
   */
  private createRasterLayer(layer: LayerConfig): Layer | null {
    if (!this.map) return null;

    try {
      let source: any;
      let workspace = layer.workspace || this.defaultWorkspace;
      
      switch (layer.protocol) {
        case 'WMS':
          source = new TileWMS({
            url: `${this.geoserverUrl}/${workspace}/wms`,
            params: {
              'LAYERS': layer.layerName,
              'TILED': true
            },
            serverType: 'geoserver',
            crossOrigin: 'anonymous'
          });
          break;
        
        case 'WMTS':
          // 使用与olMapLayer相同的WMTS图层创建方法
          try {
            if (!workspace || !layer.layerName) {
              console.error(`WMTS图层 ${layer.id} 缺少workspace或layerName配置`);
              break;
            }
            
            console.log(`创建WMTS图层: ${layer.id}, 工作区: ${workspace}, 图层名: ${layer.layerName}`);
            
            // 设置默认值
            const tileMatrixSet = 'EPSG:4326';
            const format = 'image/png';
            const style = '';
            
            // 获取投影
            const projection = getProjection(tileMatrixSet);
            if (!projection) {
              console.error(`无法获取投影系统: ${tileMatrixSet}`);
              break;
            }
            
            console.log(`WMTS投影系统:`, projection);
            
            // 获取完整的WMTS服务URL (KVP格式)
            const wmtsEndpoint = `${this.geoserverUrl}/gwc/service/wmts`;
            console.log(`WMTS服务端点: ${wmtsEndpoint}`);
            
            // 创建WMTS源，使用KVP编码方式和优先级瓦片加载
            source = new WMTS({
              url: wmtsEndpoint,
              layer: `${workspace}:${layer.layerName}`,
              matrixSet: tileMatrixSet,
              format: format,
              projection: projection,
              style: style,
              // 关键参数：使用KVP编码而非REST
              requestEncoding: 'KVP',
              // 使用自定义TileGrid
              tileGrid: this.createWmtsTileGrid(projection, tileMatrixSet),
              wrapX: true,
              transition: 0,
              // 使用优先级瓦片加载函数
              tileLoadFunction: priorityTileLoadFunction
            });

            // 添加事件处理
            source.on('tileloaderror', () => {
              console.warn(`WMTS图层 ${layer.name} 加载失败`);
            });

            // 监听瓦片加载完成事件
            // 当有瓦片完成时，如果有待重试的瓦片 → 优先重试它们
            source.on('tileloadend', () => {
              console.log(`WMTS图层 ${layer.name} 部分加载成功`);

              if (retryQueue.length > 0) {
                const nextRetry = retryQueue.shift(); // 取一个高优先级瓦片
                console.log(`Retrying high-priority tile: ${nextRetry}`);

                // 刷新数据源 → 会重新加载当前视图可见的所有瓦片
                // 由于该瓦片是可见的，它会马上进入加载队列（优先级较高）
                source.refresh();
              }
            });
          } catch (error) {
            console.error(`WMTS图层 ${layer.name} 创建失败:`, error);
            // 回退到WMS
            const wmsUrl = workspace ? 
              `${this.geoserverUrl}/${workspace}/wms` : 
              `${this.geoserverUrl}/wms`;
              
            source = new TileWMS({
              url: wmsUrl,
              params: {
                'LAYERS': layer.layerName || '',
                'TILED': true
              },
              serverType: 'geoserver',
              crossOrigin: 'anonymous'
            });
            console.log(`已将WMTS图层 ${layer.name} 回退到WMS模式`);
          }
          break;
        
        case 'XYZ':
          source = new XYZ({
            url: layer.url,
            crossOrigin: 'anonymous'
          });
          break;
        
        default:
          console.warn(`未支持的栅格协议: ${layer.protocol}`);
          return null;
      }
      
      // 创建并返回栅格图层
      const tileLayer = new TileLayer({
        preload: Infinity,  // 开启所有级别预加载
        source: source,
        visible: true,
        opacity: layer.opacity !== undefined ? layer.opacity / 100 : 1,
        zIndex: layer.zIndex || 0
      });
      
      // 重要：将workspace和layerName属性设置到图层实例上
      if (workspace) {
        tileLayer.set('workspace', workspace);
      }
      if (layer.layerName) {
        tileLayer.set('layerName', layer.layerName);
      }
      
      // 保存图层源
      this.layerSources.set(layer.id, source);
      
      return tileLayer;
    } catch (error) {
      console.error(`创建栅格图层失败: ${layer.name}`, error);
      return null;
    }
  }

  /**
   * 创建矢量图层
   * @param layer 图层配置
   * @returns 矢量图层
   */
  private createVectorLayer(layer: LayerConfig): Layer | null {
    if (!this.map) return null;
    
    try {
      // 创建矢量数据源
      const source = new VectorSource({
        format: new GeoJSON()
      });
      
      // 获取图层样式
      const style = this.getLayerStyle(layer);
      
      // 创建样式函数
      const styleFn = (feature: any) => {
        // 处理自定义样式规则
        if (layer.styleRules && layer.styleRules.length > 0) {
          for (const rule of layer.styleRules) {
            const propertyValue = feature.get(rule.filter.property);
            
            if (this.evaluateFilter(propertyValue, rule.filter.operator, rule.filter.value)) {
              return styleUtils.createStyleFromObject(rule.style);
            }
          }
        }
        
        // 处理标签
        if (layer.showLabels && layer.labelField) {
          const labelValue = feature.get(layer.labelField);
          if (labelValue) {
            return styleUtils.createStyleWithLabel(
              style,
              labelValue.toString(),
              layer.labelStyle
            );
          }
        }
        
        // 使用默认样式
        return styleUtils.createStyleFromObject(style);
      };
      
      // 创建矢量图层
      const vectorLayer = new VectorLayer({
        source: source,
        style: styleFn,
        visible: true
      });
      
      // 保存图层源和样式
      this.layerSources.set(layer.id, source);
      this.layerStyles.set(layer.id, style);
      
      // 加载WFS数据
      if (layer.protocol === 'WFS') {
        this.loadWFSData(layer, source);
      }
      
      return vectorLayer;
    } catch (error) {
      console.error(`创建矢量图层失败: ${layer.name}`, error);
      return null;
    }
  }

  /**
   * 加载WFS数据
   * @param layer 图层配置
   * @param source 矢量数据源
   */
  private async loadWFSData(layer: LayerConfig, source: VectorSource): Promise<void> {
    try {
      const workspace = layer.workspace || this.defaultWorkspace;
      const layerName = layer.layerName || layer.id;
      const url = `${this.geoserverUrl}/${workspace}/wfs?service=WFS&version=1.1.0&request=GetFeature&typename=${layerName}&outputFormat=application/json&srsname=EPSG:3857`;
      
      const response = await axios.get(url);
      const features = new GeoJSON().readFeatures(response.data);
      
      source.addFeatures(features);
      
      // 如果需要缩放到图层范围
      // this.zoomToLayerSource(source);
    } catch (error) {
      console.error(`加载WFS数据失败: ${layer.name}`, error);
      ElMessage.error(`加载图层数据失败: ${layer.name}`);
    }
  }

  /**
   * 评估过滤条件
   * @param propertyValue 属性值
   * @param operator 操作符
   * @param filterValue 过滤值
   * @returns 是否符合条件
   */
  private evaluateFilter(propertyValue: any, operator: string, filterValue: any): boolean {
    switch (operator) {
      case '=': return propertyValue == filterValue;
      case '!=': return propertyValue != filterValue;
      case '>': return propertyValue > filterValue;
      case '<': return propertyValue < filterValue;
      case '>=': return propertyValue >= filterValue;
      case '<=': return propertyValue <= filterValue;
      default: return false;
    }
  }

  /**
   * 获取图层样式
   * @param layer 图层配置
   * @returns 样式对象
   */
  getLayerStyle(layer: LayerConfig): any {
    if (!this.styleConfig) return DEFAULT_STYLE;
    
    // 如果图层有默认样式，从样式配置中获取
    if (layer.defaultStyle && this.styleConfig.styles[layer.defaultStyle]) {
      const style = { ...this.styleConfig.styles[layer.defaultStyle] };
      
      // 处理随机颜色
      if (style.color === '#random') {
        style.color = getRandomColor();
      }
      
      if (style.fillColor === '#random') {
        style.fillColor = getRandomColor();
      } else if (style.fillColor && style.fillColor.includes('#random')) {
        // 处理带透明度的随机颜色 如 #random88
        const opacity = style.fillColor.substring(7);
        style.fillColor = getRandomColor() + opacity;
      }
      
      return style;
    }
    
    return DEFAULT_STYLE;
  }

  /**
   * 移除图层
   * @param layerId 图层ID
   */
  removeLayer(layerId: string): void {
    if (!this.map) return;
    
    const layer = this.layers.get(layerId);
    if (layer) {
      this.map.removeLayer(layer);
      this.layers.delete(layerId);
      this.layerSources.delete(layerId);
      this.layerStyles.delete(layerId);
    }
  }

  /**
   * 设置图层透明度
   * @param layerId 图层ID
   * @param opacity 透明度（0-1）
   */
  setLayerOpacity(layerId: string, opacity: number): void {
    const layer = this.layers.get(layerId);
    if (layer) {
      layer.setOpacity(opacity);
    }
  }

  /**
   * 更新图层样式
   * @param layerId 图层ID
   * @param style 新样式
   */
  updateLayerStyle(layerId: string, style: any): void {
    const layer = this.layers.get(layerId) as VectorLayer<any>;
    if (layer && layer.getSource() instanceof VectorSource) {
      this.layerStyles.set(layerId, style);
      
      // 创建新的样式函数
      const styleFn = (feature: any) => {
        return styleUtils.createStyleFromObject(style);
      };
      
      // 更新图层样式
      layer.setStyle(styleFn);
    }
  }

  /**
   * 缩放到图层
   * @param layerId 图层ID
   */
  zoomToLayer(layerId: string): void {
    if (!this.map) {
      console.error('地图实例不存在，无法缩放');
      return;
    }
    
    console.log(`zoomToLayer被调用，图层ID: ${layerId}`);
    
    // 获取图层实例
    const layer = this.layers.get(layerId);
    if (!layer) {
      console.warn(`图层 ${layerId} 不存在，无法缩放`);
      // 检查所有已加载的图层
      console.log('当前已加载的图层:', Array.from(this.layers.keys()));
      return;
    }
    
    console.log(`找到图层实例: ${layer.get('name') || '未命名'}, 类型: ${layer.get('type') || '未知'}`);
    
    // 首先尝试从API获取范围（优先使用API）
    const layerConfig = layer;
    
    // 如果是栅格图层且有workspace和layerName，尝试从API获取
    if (layerConfig instanceof TileLayer) {
      const workspace = layerConfig.get('workspace');
      const layerName = layerConfig.get('layerName');
      
      console.log(`图层属性检查 - workspace: ${workspace}, layerName: ${layerName}`);
      
      if (workspace && layerName) {
        console.log(`尝试从API获取图层 ${layerId} (${workspace}:${layerName}) 的边界框...`);
        
        // 异步获取边界框
        this.fetchLayerExtent(workspace, layerName)
          .then(apiExtent => {
            if (apiExtent) {
              // 使用API获取的范围进行缩放
              console.log(`使用API获取的边界框进行缩放: ${apiExtent}`);
              this.fitToExtent(apiExtent);
            } else {
              // 如果API获取失败，尝试从图层源获取范围
              console.log('API未返回有效边界框，尝试从图层源获取范围');
              this.zoomToLayerFromSource(layerId);
            }
          })
          .catch(error => {
            console.error(`从API获取图层 ${layerId} 边界框失败: ${error}`);
            // 如果API获取失败，尝试从图层源获取范围
            this.zoomToLayerFromSource(layerId);
          });
        
        return; // 异步操作，直接返回
      } else {
        console.log(`图层缺少workspace或layerName属性，无法从API获取边界框`);
      }
    } else {
      console.log(`图层不是TileLayer类型，无法从API获取边界框`);
    }
    
    // 如果不是栅格图层或没有workspace/layerName，尝试从图层源获取范围
    this.zoomToLayerFromSource(layerId);
  }
  
  /**
   * 从图层源获取范围并缩放
   * @param layerId 图层ID
   */
  private zoomToLayerFromSource(layerId: string): void {
    let extent = null;
    const source = this.layerSources.get(layerId);
    
    if (source && source.getExtent) {
      try {
        extent = source.getExtent();
        // 检查范围是否有效
        if (extent && !isNaN(extent[0]) && !isNaN(extent[1]) && !isNaN(extent[2]) && !isNaN(extent[3])) {
          console.log(`从图层源获取到范围: ${extent}`);
          // 使用获取到的范围进行缩放
          this.fitToExtent(extent);
          return;
        }
      } catch (error) {
        console.warn(`从图层源获取范围失败: ${error}`);
      }
    }
    
    // 如果无法从源获取有效范围，使用默认范围
    console.log(`无法获取图层 ${layerId} 的有效范围，使用默认范围`);
    this.fitToDefaultExtent();
  }
  
  /**
   * 从API获取图层边界框
   * @param workspace 工作空间
   * @param layerName 图层名称
   * @returns 图层范围数组 [minx, miny, maxx, maxy] 或 null
   */
  private async fetchLayerExtent(workspace: string, layerName: string): Promise<number[] | null> {
    try {
      console.log(`正在从API获取图层 ${workspace}:${layerName} 的边界框...`);
      const bboxData = await getLayerBbox(workspace, layerName);
      
      // 检查返回数据格式
      if (bboxData && bboxData.status === 'success' && bboxData.bbox && bboxData.bbox.latLon) {
        const { minx, miny, maxx, maxy } = bboxData.bbox.latLon;
        
        // 检查获取的边界框是否有效
        if (minx === -180 && miny === -90 && maxx === 180 && maxy === 90) {
          console.warn(`API返回全球范围作为图层 ${workspace}:${layerName} 的边界框，尝试使用备用方法`);
          
          // 如果是全球范围，尝试使用native属性
          if (bboxData.bbox.native && 
              !(bboxData.bbox.native.minx === -180 && 
                bboxData.bbox.native.miny === -90 && 
                bboxData.bbox.native.maxx === 180 && 
                bboxData.bbox.native.maxy === 90)) {
            // 使用native属性
            const nativeBox = bboxData.bbox.native;
            console.log(`使用native边界框：`, nativeBox);
            
            // 转换为OpenLayers可用的范围
            const bottomLeft = fromLonLat([nativeBox.minx, nativeBox.miny]);
            const topRight = fromLonLat([nativeBox.maxx, nativeBox.maxy]);
            
            // 构建extent数组
            const extent = [bottomLeft[0], bottomLeft[1], topRight[0], topRight[1]];
            
            console.log(`成功从API native属性获取并转换图层边界框:`, extent);
            return extent;
          } else {
            // 如果都是全球范围，返回null
            return null;
          }
        }
        
        // 如果不是全球范围，则进行正常转换
        console.log(`API返回的有效边界框: minx=${minx}, miny=${miny}, maxx=${maxx}, maxy=${maxy}`);
        
        // 转换为OpenLayers可用的范围
        const bottomLeft = fromLonLat([minx, miny]);
        const topRight = fromLonLat([maxx, maxy]);
        
        // 构建extent数组
        const extent = [bottomLeft[0], bottomLeft[1], topRight[0], topRight[1]];
        
        // 检查转换后的坐标是否有效
        if (extent.some(val => !isFinite(val))) {
          console.warn(`转换后的坐标包含无效值:`, extent);
          return null;
        }
        
        console.log(`成功从API获取并转换图层边界框:`, extent);
        return extent;
      } else {
        console.warn(`API返回的边界框数据格式不正确:`, bboxData);
      }
    } catch (error) {
      console.error(`获取图层边界框失败:`, error);
    }
    
    return null;
  }
  
  /**
   * 缩放到指定范围
   * @param extent 范围数组 [minx, miny, maxx, maxy]
   */
  private fitToExtent(extent: number[]): void {
    if (!this.map) return;
    
    try {
      this.map.getView().fit(extent, {
        duration: 1000,
        padding: [50, 50, 50, 50],
        maxZoom: 18 // 添加最大缩放级别限制，防止过度缩放
      });
      console.log(`已缩放至范围: ${extent}`);
    } catch (error) {
      console.error(`缩放至范围失败: ${error}`);
      this.fitToDefaultExtent();
    }
  }
  
  /**
   * 缩放到默认范围（中国大致范围）
   */
  private fitToDefaultExtent(): void {
    if (!this.map) return;
    
    try {
      // 使用南宁区域的大致范围作为默认值
      const defaultExtent = [11800000, 2500000, 12500000, 3000000];
      this.map.getView().fit(defaultExtent, {
        duration: 1000,
        padding: [50, 50, 50, 50],
        maxZoom: 10
      });
      console.log(`已缩放至默认范围`);
    } catch (error) {
      console.error(`缩放至默认范围失败: ${error}`);
    }
  }

  /**
   * 切换图层标签显示
   * @param layerId 图层ID
   * @param showLabels 是否显示标签
   * @param labelField 标签字段
   * @param labelStyle 标签样式
   */
  toggleLayerLabels(layerId: string, showLabels: boolean, labelField: string, labelStyle?: any): void {
    const layer = this.layers.get(layerId) as VectorLayer<any>;
    if (!layer || !(layer.getSource() instanceof VectorSource)) return;
    
    const baseStyle = this.layerStyles.get(layerId) || DEFAULT_STYLE;
    
    // 创建新的样式函数
    const styleFn = (feature: any) => {
      if (showLabels) {
        const labelValue = feature.get(labelField);
        if (labelValue) {
          return styleUtils.createStyleWithLabel(
            baseStyle,
            labelValue.toString(),
            labelStyle
          );
        }
      }
      return styleUtils.createStyleFromObject(baseStyle);
    };
    
    // 更新图层样式
    layer.setStyle(styleFn);
  }

  /**
   * 获取图层
   * @param layerId 图层ID
   * @returns 图层实例
   */
  getLayer(layerId: string): Layer | undefined {
    return this.layers.get(layerId);
  }

  /**
   * 清理优先级瓦片加载资源
   * 在图层管理器销毁时调用
   */
  dispose(): void {
    // 清理所有正在加载的瓦片的超时定时器
    loadingTiles.forEach((timeoutId) => {
      clearTimeout(timeoutId);
    });
    loadingTiles.clear();

    // 清空重试队列
    retryQueue.length = 0;

    console.log('优先级瓦片加载资源已清理');
  }

  /**
   * 创建WMTS图层的瓦片网格
   * @param projection 投影
   * @param gridSetId 网格集ID
   * @returns 瓦片网格
   */
  private createWmtsTileGrid(projection: any, gridSetId: string): WMTSTileGrid {
    const projectionExtent = projection.getExtent();
    
    // 根据不同的坐标系创建合适的参数
    let origin, resolutions, matrixIds;
    
    if (gridSetId === 'EPSG:4326') {
      // EPSG:4326 特殊处理
      origin = [-180, 90]; // 正确的原点
      
      // 标准的EPSG:4326分辨率
      resolutions = [
        0.703125,              // 层级 0
        0.3515625,             // 层级 1
        0.17578125,            // 层级 2
        0.087890625,           // 层级 3
        0.0439453125,          // 层级 4
        0.02197265625,         // 层级 5
        0.010986328125,        // 层级 6
        0.0054931640625,       // 层级 7
        0.00274658203125,      // 层级 8
        0.001373291015625,     // 层级 9
        0.0006866455078125,    // 层级 10
        0.0003433227539062,    // 层级 11
        0.0001716613769531,    // 层级 12
        0.0000858306884766,    // 层级 13
        0.0000429153442383,    // 层级 14
        0.0000214576721191,    // 层级 15
        0.0000107288360596,    // 层级 16
        0.0000053644180298,    // 层级 17
        0.0000026822090149,    // 层级 18
        0.0000013411045074,    // 层级 19
        0.0000006705522537,    // 层级 20
        0.0000003352761269,    // 层级 21
        0.00000016763806345,   // 层级 22
        0.00000008381903173,   // 层级 23
        0.00000004190951586,   // 层级 24
        0.00000002095475793    // 层级 25
    ];
    
      
      // 标准的EPSG:4326 GeoServer矩阵ID
      matrixIds = [];
      for (let i = 0; i < resolutions.length; i++) {
        matrixIds.push(`${gridSetId}:${i}`);
      }
    } else {
      // 默认情况下，使用自动计算的值
      origin = getTopLeft(projectionExtent);
      const size = Math.max(
        projectionExtent[2] - projectionExtent[0],
        projectionExtent[3] - projectionExtent[1]
      );
      const maxResolution = size / 256;
      
      resolutions = [];
      matrixIds = [];
      for (let i = 0; i < 20; i++) {
        resolutions.push(maxResolution / Math.pow(2, i));
        // 使用标准GeoServer格式矩阵ID
        matrixIds.push(`${gridSetId}:${i}`);
      }
    }
    
    console.log(`创建WMTS瓦片网格:`, {
      网格集: gridSetId,
      原点: origin,
      分辨率数: resolutions.length
    });
    
    return new WMTSTileGrid({
      origin: origin,
      resolutions: resolutions,
      matrixIds: matrixIds
    });
  }
} 