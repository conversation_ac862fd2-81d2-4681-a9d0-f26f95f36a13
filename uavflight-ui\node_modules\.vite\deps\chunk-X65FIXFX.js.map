{"version": 3, "sources": ["../../ol/geom/flat/segments.js", "../../ol/geom/flat/contains.js", "../../ol/geom/flat/intersectsextent.js"], "sourcesContent": ["/**\n * @module ol/geom/flat/segments\n */\n\n/**\n * This function calls `callback` for each segment of the flat coordinates\n * array. If the callback returns a truthy value the function returns that\n * value immediately. Otherwise the function returns `false`.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {function(import(\"../../coordinate.js\").Coordinate, import(\"../../coordinate.js\").Coordinate): T} callback Function\n *     called for each segment.\n * @return {T|boolean} Value.\n * @template T\n */\nexport function forEach(flatCoordinates, offset, end, stride, callback) {\n  let ret;\n  offset += stride;\n  for (; offset < end; offset += stride) {\n    ret = callback(\n      flatCoordinates.slice(offset - stride, offset),\n      flatCoordinates.slice(offset, offset + stride)\n    );\n    if (ret) {\n      return ret;\n    }\n  }\n  return false;\n}\n", "/**\n * @module ol/geom/flat/contains\n */\nimport {forEach<PERSON>orner} from '../../extent.js';\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {import(\"../../extent.js\").Extent} extent Extent.\n * @return {boolean} Contains extent.\n */\nexport function linearRingContainsExtent(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  extent\n) {\n  const outside = forEachCorner(\n    extent,\n    /**\n     * @param {import(\"../../coordinate.js\").Coordinate} coordinate Coordinate.\n     * @return {boolean} Contains (x, y).\n     */\n    function (coordinate) {\n      return !linearRingContainsXY(\n        flatCoordinates,\n        offset,\n        end,\n        stride,\n        coordinate[0],\n        coordinate[1]\n      );\n    }\n  );\n  return !outside;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} x X.\n * @param {number} y Y.\n * @return {boolean} Contains (x, y).\n */\nexport function linearRingContainsXY(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  x,\n  y\n) {\n  // https://geomalgorithms.com/a03-_inclusion.html\n  // Copyright 2000 softSurfer, 2012 Dan Sunday\n  // This code may be freely used and modified for any purpose\n  // providing that this copyright notice is included with it.\n  // SoftSurfer makes no warranty for this code, and cannot be held\n  // liable for any real or imagined damage resulting from its use.\n  // Users of this code must verify correctness for their application.\n  let wn = 0;\n  let x1 = flatCoordinates[end - stride];\n  let y1 = flatCoordinates[end - stride + 1];\n  for (; offset < end; offset += stride) {\n    const x2 = flatCoordinates[offset];\n    const y2 = flatCoordinates[offset + 1];\n    if (y1 <= y) {\n      if (y2 > y && (x2 - x1) * (y - y1) - (x - x1) * (y2 - y1) > 0) {\n        wn++;\n      }\n    } else if (y2 <= y && (x2 - x1) * (y - y1) - (x - x1) * (y2 - y1) < 0) {\n      wn--;\n    }\n    x1 = x2;\n    y1 = y2;\n  }\n  return wn !== 0;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {number} x X.\n * @param {number} y Y.\n * @return {boolean} Contains (x, y).\n */\nexport function linearRingsContainsXY(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  x,\n  y\n) {\n  if (ends.length === 0) {\n    return false;\n  }\n  if (!linearRingContainsXY(flatCoordinates, offset, ends[0], stride, x, y)) {\n    return false;\n  }\n  for (let i = 1, ii = ends.length; i < ii; ++i) {\n    if (\n      linearRingContainsXY(flatCoordinates, ends[i - 1], ends[i], stride, x, y)\n    ) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {number} x X.\n * @param {number} y Y.\n * @return {boolean} Contains (x, y).\n */\nexport function linearRingssContainsXY(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  x,\n  y\n) {\n  if (endss.length === 0) {\n    return false;\n  }\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    if (linearRingsContainsXY(flatCoordinates, offset, ends, stride, x, y)) {\n      return true;\n    }\n    offset = ends[ends.length - 1];\n  }\n  return false;\n}\n", "/**\n * @module ol/geom/flat/intersectsextent\n */\nimport {\n  containsExtent,\n  createEmpty,\n  extendFlatCoordinates,\n  intersects,\n  intersectsSegment,\n} from '../../extent.js';\nimport {forEach as forEachSegment} from './segments.js';\nimport {linearRingContainsExtent, linearRingContainsXY} from './contains.js';\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {import(\"../../extent.js\").Extent} extent Extent.\n * @return {boolean} True if the geometry and the extent intersect.\n */\nexport function intersectsLineString(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  extent\n) {\n  const coordinatesExtent = extendFlatCoordinates(\n    createEmpty(),\n    flatCoordinates,\n    offset,\n    end,\n    stride\n  );\n  if (!intersects(extent, coordinatesExtent)) {\n    return false;\n  }\n  if (containsExtent(extent, coordinatesExtent)) {\n    return true;\n  }\n  if (coordinatesExtent[0] >= extent[0] && coordinatesExtent[2] <= extent[2]) {\n    return true;\n  }\n  if (coordinatesExtent[1] >= extent[1] && coordinatesExtent[3] <= extent[3]) {\n    return true;\n  }\n  return forEachSegment(\n    flatCoordinates,\n    offset,\n    end,\n    stride,\n    /**\n     * @param {import(\"../../coordinate.js\").Coordinate} point1 Start point.\n     * @param {import(\"../../coordinate.js\").Coordinate} point2 End point.\n     * @return {boolean} `true` if the segment and the extent intersect,\n     *     `false` otherwise.\n     */\n    function (point1, point2) {\n      return intersectsSegment(extent, point1, point2);\n    }\n  );\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {import(\"../../extent.js\").Extent} extent Extent.\n * @return {boolean} True if the geometry and the extent intersect.\n */\nexport function intersectsLineStringArray(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  extent\n) {\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    if (\n      intersectsLineString(flatCoordinates, offset, ends[i], stride, extent)\n    ) {\n      return true;\n    }\n    offset = ends[i];\n  }\n  return false;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {import(\"../../extent.js\").Extent} extent Extent.\n * @return {boolean} True if the geometry and the extent intersect.\n */\nexport function intersectsLinearRing(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  extent\n) {\n  if (intersectsLineString(flatCoordinates, offset, end, stride, extent)) {\n    return true;\n  }\n  if (\n    linearRingContainsXY(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      extent[0],\n      extent[1]\n    )\n  ) {\n    return true;\n  }\n  if (\n    linearRingContainsXY(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      extent[0],\n      extent[3]\n    )\n  ) {\n    return true;\n  }\n  if (\n    linearRingContainsXY(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      extent[2],\n      extent[1]\n    )\n  ) {\n    return true;\n  }\n  if (\n    linearRingContainsXY(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      extent[2],\n      extent[3]\n    )\n  ) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {import(\"../../extent.js\").Extent} extent Extent.\n * @return {boolean} True if the geometry and the extent intersect.\n */\nexport function intersectsLinearRingArray(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  extent\n) {\n  if (!intersectsLinearRing(flatCoordinates, offset, ends[0], stride, extent)) {\n    return false;\n  }\n  if (ends.length === 1) {\n    return true;\n  }\n  for (let i = 1, ii = ends.length; i < ii; ++i) {\n    if (\n      linearRingContainsExtent(\n        flatCoordinates,\n        ends[i - 1],\n        ends[i],\n        stride,\n        extent\n      )\n    ) {\n      if (\n        !intersectsLineString(\n          flatCoordinates,\n          ends[i - 1],\n          ends[i],\n          stride,\n          extent\n        )\n      ) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {import(\"../../extent.js\").Extent} extent Extent.\n * @return {boolean} True if the geometry and the extent intersect.\n */\nexport function intersectsLinearRingMultiArray(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  extent\n) {\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    if (\n      intersectsLinearRingArray(flatCoordinates, offset, ends, stride, extent)\n    ) {\n      return true;\n    }\n    offset = ends[ends.length - 1];\n  }\n  return false;\n}\n"], "mappings": ";;;;;;;;;;AAiBO,SAAS,QAAQ,iBAAiB,QAAQ,KAAK,QAAQ,UAAU;AACtE,MAAI;AACJ,YAAU;AACV,SAAO,SAAS,KAAK,UAAU,QAAQ;AACrC,UAAM;AAAA,MACJ,gBAAgB,MAAM,SAAS,QAAQ,MAAM;AAAA,MAC7C,gBAAgB,MAAM,QAAQ,SAAS,MAAM;AAAA,IAC/C;AACA,QAAI,KAAK;AACP,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;ACjBO,SAAS,yBACd,iBACA,QACA,KACA,QACA,QACA;AACA,QAAM,UAAU;AAAA,IACd;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,SAAU,YAAY;AACpB,aAAO,CAAC;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW,CAAC;AAAA,QACZ,WAAW,CAAC;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC;AACV;AAWO,SAAS,qBACd,iBACA,QACA,KACA,QACA,GACA,GACA;AAQA,MAAI,KAAK;AACT,MAAI,KAAK,gBAAgB,MAAM,MAAM;AACrC,MAAI,KAAK,gBAAgB,MAAM,SAAS,CAAC;AACzC,SAAO,SAAS,KAAK,UAAU,QAAQ;AACrC,UAAM,KAAK,gBAAgB,MAAM;AACjC,UAAM,KAAK,gBAAgB,SAAS,CAAC;AACrC,QAAI,MAAM,GAAG;AACX,UAAI,KAAK,MAAM,KAAK,OAAO,IAAI,OAAO,IAAI,OAAO,KAAK,MAAM,GAAG;AAC7D;AAAA,MACF;AAAA,IACF,WAAW,MAAM,MAAM,KAAK,OAAO,IAAI,OAAO,IAAI,OAAO,KAAK,MAAM,GAAG;AACrE;AAAA,IACF;AACA,SAAK;AACL,SAAK;AAAA,EACP;AACA,SAAO,OAAO;AAChB;AAWO,SAAS,sBACd,iBACA,QACA,MACA,QACA,GACA,GACA;AACA,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,qBAAqB,iBAAiB,QAAQ,KAAK,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG;AACzE,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,QACE,qBAAqB,iBAAiB,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,GAAG,CAAC,GACxE;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAWO,SAAS,uBACd,iBACA,QACA,OACA,QACA,GACA,GACA;AACA,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,sBAAsB,iBAAiB,QAAQ,MAAM,QAAQ,GAAG,CAAC,GAAG;AACtE,aAAO;AAAA,IACT;AACA,aAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;;;AC3HO,SAAS,qBACd,iBACA,QACA,KACA,QACA,QACA;AACA,QAAM,oBAAoB;AAAA,IACxB,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,CAAC,WAAW,QAAQ,iBAAiB,GAAG;AAC1C,WAAO;AAAA,EACT;AACA,MAAI,eAAe,QAAQ,iBAAiB,GAAG;AAC7C,WAAO;AAAA,EACT;AACA,MAAI,kBAAkB,CAAC,KAAK,OAAO,CAAC,KAAK,kBAAkB,CAAC,KAAK,OAAO,CAAC,GAAG;AAC1E,WAAO;AAAA,EACT;AACA,MAAI,kBAAkB,CAAC,KAAK,OAAO,CAAC,KAAK,kBAAkB,CAAC,KAAK,OAAO,CAAC,GAAG;AAC1E,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,SAAU,QAAQ,QAAQ;AACxB,aAAO,kBAAkB,QAAQ,QAAQ,MAAM;AAAA,IACjD;AAAA,EACF;AACF;AAUO,SAAS,0BACd,iBACA,QACA,MACA,QACA,QACA;AACA,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,QACE,qBAAqB,iBAAiB,QAAQ,KAAK,CAAC,GAAG,QAAQ,MAAM,GACrE;AACA,aAAO;AAAA,IACT;AACA,aAAS,KAAK,CAAC;AAAA,EACjB;AACA,SAAO;AACT;AAUO,SAAS,qBACd,iBACA,QACA,KACA,QACA,QACA;AACA,MAAI,qBAAqB,iBAAiB,QAAQ,KAAK,QAAQ,MAAM,GAAG;AACtE,WAAO;AAAA,EACT;AACA,MACE;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,CAAC;AAAA,IACR,OAAO,CAAC;AAAA,EACV,GACA;AACA,WAAO;AAAA,EACT;AACA,MACE;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,CAAC;AAAA,IACR,OAAO,CAAC;AAAA,EACV,GACA;AACA,WAAO;AAAA,EACT;AACA,MACE;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,CAAC;AAAA,IACR,OAAO,CAAC;AAAA,EACV,GACA;AACA,WAAO;AAAA,EACT;AACA,MACE;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,CAAC;AAAA,IACR,OAAO,CAAC;AAAA,EACV,GACA;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAUO,SAAS,0BACd,iBACA,QACA,MACA,QACA,QACA;AACA,MAAI,CAAC,qBAAqB,iBAAiB,QAAQ,KAAK,CAAC,GAAG,QAAQ,MAAM,GAAG;AAC3E,WAAO;AAAA,EACT;AACA,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,QACE;AAAA,MACE;AAAA,MACA,KAAK,IAAI,CAAC;AAAA,MACV,KAAK,CAAC;AAAA,MACN;AAAA,MACA;AAAA,IACF,GACA;AACA,UACE,CAAC;AAAA,QACC;AAAA,QACA,KAAK,IAAI,CAAC;AAAA,QACV,KAAK,CAAC;AAAA,QACN;AAAA,QACA;AAAA,MACF,GACA;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAUO,SAAS,+BACd,iBACA,QACA,OACA,QACA,QACA;AACA,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,QACE,0BAA0B,iBAAiB,QAAQ,MAAM,QAAQ,MAAM,GACvE;AACA,aAAO;AAAA,IACT;AACA,aAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;", "names": []}