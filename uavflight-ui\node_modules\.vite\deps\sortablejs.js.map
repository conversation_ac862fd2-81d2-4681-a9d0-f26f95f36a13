{"version": 3, "sources": ["../../sortablejs/modular/sortable.esm.js"], "sourcesContent": ["/**!\n * Sortable 1.15.3\n * <AUTHOR>   <<EMAIL>>\n * <AUTHOR>    <<EMAIL>>\n * @license MIT\n */\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar version = \"1.15.3\";\n\nfunction userAgent(pattern) {\n  if (typeof window !== 'undefined' && window.navigator) {\n    return !! /*@__PURE__*/navigator.userAgent.match(pattern);\n  }\n}\nvar IE11OrLess = userAgent(/(?:Trident.*rv[ :]?11\\.|msie|iemobile|Windows Phone)/i);\nvar Edge = userAgent(/Edge/i);\nvar FireFox = userAgent(/firefox/i);\nvar Safari = userAgent(/safari/i) && !userAgent(/chrome/i) && !userAgent(/android/i);\nvar IOS = userAgent(/iP(ad|od|hone)/i);\nvar ChromeForAndroid = userAgent(/chrome/i) && userAgent(/android/i);\n\nvar captureMode = {\n  capture: false,\n  passive: false\n};\nfunction on(el, event, fn) {\n  el.addEventListener(event, fn, !IE11OrLess && captureMode);\n}\nfunction off(el, event, fn) {\n  el.removeEventListener(event, fn, !IE11OrLess && captureMode);\n}\nfunction matches( /**HTMLElement*/el, /**String*/selector) {\n  if (!selector) return;\n  selector[0] === '>' && (selector = selector.substring(1));\n  if (el) {\n    try {\n      if (el.matches) {\n        return el.matches(selector);\n      } else if (el.msMatchesSelector) {\n        return el.msMatchesSelector(selector);\n      } else if (el.webkitMatchesSelector) {\n        return el.webkitMatchesSelector(selector);\n      }\n    } catch (_) {\n      return false;\n    }\n  }\n  return false;\n}\nfunction getParentOrHost(el) {\n  return el.host && el !== document && el.host.nodeType ? el.host : el.parentNode;\n}\nfunction closest( /**HTMLElement*/el, /**String*/selector, /**HTMLElement*/ctx, includeCTX) {\n  if (el) {\n    ctx = ctx || document;\n    do {\n      if (selector != null && (selector[0] === '>' ? el.parentNode === ctx && matches(el, selector) : matches(el, selector)) || includeCTX && el === ctx) {\n        return el;\n      }\n      if (el === ctx) break;\n      /* jshint boss:true */\n    } while (el = getParentOrHost(el));\n  }\n  return null;\n}\nvar R_SPACE = /\\s+/g;\nfunction toggleClass(el, name, state) {\n  if (el && name) {\n    if (el.classList) {\n      el.classList[state ? 'add' : 'remove'](name);\n    } else {\n      var className = (' ' + el.className + ' ').replace(R_SPACE, ' ').replace(' ' + name + ' ', ' ');\n      el.className = (className + (state ? ' ' + name : '')).replace(R_SPACE, ' ');\n    }\n  }\n}\nfunction css(el, prop, val) {\n  var style = el && el.style;\n  if (style) {\n    if (val === void 0) {\n      if (document.defaultView && document.defaultView.getComputedStyle) {\n        val = document.defaultView.getComputedStyle(el, '');\n      } else if (el.currentStyle) {\n        val = el.currentStyle;\n      }\n      return prop === void 0 ? val : val[prop];\n    } else {\n      if (!(prop in style) && prop.indexOf('webkit') === -1) {\n        prop = '-webkit-' + prop;\n      }\n      style[prop] = val + (typeof val === 'string' ? '' : 'px');\n    }\n  }\n}\nfunction matrix(el, selfOnly) {\n  var appliedTransforms = '';\n  if (typeof el === 'string') {\n    appliedTransforms = el;\n  } else {\n    do {\n      var transform = css(el, 'transform');\n      if (transform && transform !== 'none') {\n        appliedTransforms = transform + ' ' + appliedTransforms;\n      }\n      /* jshint boss:true */\n    } while (!selfOnly && (el = el.parentNode));\n  }\n  var matrixFn = window.DOMMatrix || window.WebKitCSSMatrix || window.CSSMatrix || window.MSCSSMatrix;\n  /*jshint -W056 */\n  return matrixFn && new matrixFn(appliedTransforms);\n}\nfunction find(ctx, tagName, iterator) {\n  if (ctx) {\n    var list = ctx.getElementsByTagName(tagName),\n      i = 0,\n      n = list.length;\n    if (iterator) {\n      for (; i < n; i++) {\n        iterator(list[i], i);\n      }\n    }\n    return list;\n  }\n  return [];\n}\nfunction getWindowScrollingElement() {\n  var scrollingElement = document.scrollingElement;\n  if (scrollingElement) {\n    return scrollingElement;\n  } else {\n    return document.documentElement;\n  }\n}\n\n/**\r\n * Returns the \"bounding client rect\" of given element\r\n * @param  {HTMLElement} el                       The element whose boundingClientRect is wanted\r\n * @param  {[Boolean]} relativeToContainingBlock  Whether the rect should be relative to the containing block of (including) the container\r\n * @param  {[Boolean]} relativeToNonStaticParent  Whether the rect should be relative to the relative parent of (including) the contaienr\r\n * @param  {[Boolean]} undoScale                  Whether the container's scale() should be undone\r\n * @param  {[HTMLElement]} container              The parent the element will be placed in\r\n * @return {Object}                               The boundingClientRect of el, with specified adjustments\r\n */\nfunction getRect(el, relativeToContainingBlock, relativeToNonStaticParent, undoScale, container) {\n  if (!el.getBoundingClientRect && el !== window) return;\n  var elRect, top, left, bottom, right, height, width;\n  if (el !== window && el.parentNode && el !== getWindowScrollingElement()) {\n    elRect = el.getBoundingClientRect();\n    top = elRect.top;\n    left = elRect.left;\n    bottom = elRect.bottom;\n    right = elRect.right;\n    height = elRect.height;\n    width = elRect.width;\n  } else {\n    top = 0;\n    left = 0;\n    bottom = window.innerHeight;\n    right = window.innerWidth;\n    height = window.innerHeight;\n    width = window.innerWidth;\n  }\n  if ((relativeToContainingBlock || relativeToNonStaticParent) && el !== window) {\n    // Adjust for translate()\n    container = container || el.parentNode;\n\n    // solves #1123 (see: https://stackoverflow.com/a/37953806/6088312)\n    // Not needed on <= IE11\n    if (!IE11OrLess) {\n      do {\n        if (container && container.getBoundingClientRect && (css(container, 'transform') !== 'none' || relativeToNonStaticParent && css(container, 'position') !== 'static')) {\n          var containerRect = container.getBoundingClientRect();\n\n          // Set relative to edges of padding box of container\n          top -= containerRect.top + parseInt(css(container, 'border-top-width'));\n          left -= containerRect.left + parseInt(css(container, 'border-left-width'));\n          bottom = top + elRect.height;\n          right = left + elRect.width;\n          break;\n        }\n        /* jshint boss:true */\n      } while (container = container.parentNode);\n    }\n  }\n  if (undoScale && el !== window) {\n    // Adjust for scale()\n    var elMatrix = matrix(container || el),\n      scaleX = elMatrix && elMatrix.a,\n      scaleY = elMatrix && elMatrix.d;\n    if (elMatrix) {\n      top /= scaleY;\n      left /= scaleX;\n      width /= scaleX;\n      height /= scaleY;\n      bottom = top + height;\n      right = left + width;\n    }\n  }\n  return {\n    top: top,\n    left: left,\n    bottom: bottom,\n    right: right,\n    width: width,\n    height: height\n  };\n}\n\n/**\r\n * Checks if a side of an element is scrolled past a side of its parents\r\n * @param  {HTMLElement}  el           The element who's side being scrolled out of view is in question\r\n * @param  {String}       elSide       Side of the element in question ('top', 'left', 'right', 'bottom')\r\n * @param  {String}       parentSide   Side of the parent in question ('top', 'left', 'right', 'bottom')\r\n * @return {HTMLElement}               The parent scroll element that the el's side is scrolled past, or null if there is no such element\r\n */\nfunction isScrolledPast(el, elSide, parentSide) {\n  var parent = getParentAutoScrollElement(el, true),\n    elSideVal = getRect(el)[elSide];\n\n  /* jshint boss:true */\n  while (parent) {\n    var parentSideVal = getRect(parent)[parentSide],\n      visible = void 0;\n    if (parentSide === 'top' || parentSide === 'left') {\n      visible = elSideVal >= parentSideVal;\n    } else {\n      visible = elSideVal <= parentSideVal;\n    }\n    if (!visible) return parent;\n    if (parent === getWindowScrollingElement()) break;\n    parent = getParentAutoScrollElement(parent, false);\n  }\n  return false;\n}\n\n/**\r\n * Gets nth child of el, ignoring hidden children, sortable's elements (does not ignore clone if it's visible)\r\n * and non-draggable elements\r\n * @param  {HTMLElement} el       The parent element\r\n * @param  {Number} childNum      The index of the child\r\n * @param  {Object} options       Parent Sortable's options\r\n * @return {HTMLElement}          The child at index childNum, or null if not found\r\n */\nfunction getChild(el, childNum, options, includeDragEl) {\n  var currentChild = 0,\n    i = 0,\n    children = el.children;\n  while (i < children.length) {\n    if (children[i].style.display !== 'none' && children[i] !== Sortable.ghost && (includeDragEl || children[i] !== Sortable.dragged) && closest(children[i], options.draggable, el, false)) {\n      if (currentChild === childNum) {\n        return children[i];\n      }\n      currentChild++;\n    }\n    i++;\n  }\n  return null;\n}\n\n/**\r\n * Gets the last child in the el, ignoring ghostEl or invisible elements (clones)\r\n * @param  {HTMLElement} el       Parent element\r\n * @param  {selector} selector    Any other elements that should be ignored\r\n * @return {HTMLElement}          The last child, ignoring ghostEl\r\n */\nfunction lastChild(el, selector) {\n  var last = el.lastElementChild;\n  while (last && (last === Sortable.ghost || css(last, 'display') === 'none' || selector && !matches(last, selector))) {\n    last = last.previousElementSibling;\n  }\n  return last || null;\n}\n\n/**\r\n * Returns the index of an element within its parent for a selected set of\r\n * elements\r\n * @param  {HTMLElement} el\r\n * @param  {selector} selector\r\n * @return {number}\r\n */\nfunction index(el, selector) {\n  var index = 0;\n  if (!el || !el.parentNode) {\n    return -1;\n  }\n\n  /* jshint boss:true */\n  while (el = el.previousElementSibling) {\n    if (el.nodeName.toUpperCase() !== 'TEMPLATE' && el !== Sortable.clone && (!selector || matches(el, selector))) {\n      index++;\n    }\n  }\n  return index;\n}\n\n/**\r\n * Returns the scroll offset of the given element, added with all the scroll offsets of parent elements.\r\n * The value is returned in real pixels.\r\n * @param  {HTMLElement} el\r\n * @return {Array}             Offsets in the format of [left, top]\r\n */\nfunction getRelativeScrollOffset(el) {\n  var offsetLeft = 0,\n    offsetTop = 0,\n    winScroller = getWindowScrollingElement();\n  if (el) {\n    do {\n      var elMatrix = matrix(el),\n        scaleX = elMatrix.a,\n        scaleY = elMatrix.d;\n      offsetLeft += el.scrollLeft * scaleX;\n      offsetTop += el.scrollTop * scaleY;\n    } while (el !== winScroller && (el = el.parentNode));\n  }\n  return [offsetLeft, offsetTop];\n}\n\n/**\r\n * Returns the index of the object within the given array\r\n * @param  {Array} arr   Array that may or may not hold the object\r\n * @param  {Object} obj  An object that has a key-value pair unique to and identical to a key-value pair in the object you want to find\r\n * @return {Number}      The index of the object in the array, or -1\r\n */\nfunction indexOfObject(arr, obj) {\n  for (var i in arr) {\n    if (!arr.hasOwnProperty(i)) continue;\n    for (var key in obj) {\n      if (obj.hasOwnProperty(key) && obj[key] === arr[i][key]) return Number(i);\n    }\n  }\n  return -1;\n}\nfunction getParentAutoScrollElement(el, includeSelf) {\n  // skip to window\n  if (!el || !el.getBoundingClientRect) return getWindowScrollingElement();\n  var elem = el;\n  var gotSelf = false;\n  do {\n    // we don't need to get elem css if it isn't even overflowing in the first place (performance)\n    if (elem.clientWidth < elem.scrollWidth || elem.clientHeight < elem.scrollHeight) {\n      var elemCSS = css(elem);\n      if (elem.clientWidth < elem.scrollWidth && (elemCSS.overflowX == 'auto' || elemCSS.overflowX == 'scroll') || elem.clientHeight < elem.scrollHeight && (elemCSS.overflowY == 'auto' || elemCSS.overflowY == 'scroll')) {\n        if (!elem.getBoundingClientRect || elem === document.body) return getWindowScrollingElement();\n        if (gotSelf || includeSelf) return elem;\n        gotSelf = true;\n      }\n    }\n    /* jshint boss:true */\n  } while (elem = elem.parentNode);\n  return getWindowScrollingElement();\n}\nfunction extend(dst, src) {\n  if (dst && src) {\n    for (var key in src) {\n      if (src.hasOwnProperty(key)) {\n        dst[key] = src[key];\n      }\n    }\n  }\n  return dst;\n}\nfunction isRectEqual(rect1, rect2) {\n  return Math.round(rect1.top) === Math.round(rect2.top) && Math.round(rect1.left) === Math.round(rect2.left) && Math.round(rect1.height) === Math.round(rect2.height) && Math.round(rect1.width) === Math.round(rect2.width);\n}\nvar _throttleTimeout;\nfunction throttle(callback, ms) {\n  return function () {\n    if (!_throttleTimeout) {\n      var args = arguments,\n        _this = this;\n      if (args.length === 1) {\n        callback.call(_this, args[0]);\n      } else {\n        callback.apply(_this, args);\n      }\n      _throttleTimeout = setTimeout(function () {\n        _throttleTimeout = void 0;\n      }, ms);\n    }\n  };\n}\nfunction cancelThrottle() {\n  clearTimeout(_throttleTimeout);\n  _throttleTimeout = void 0;\n}\nfunction scrollBy(el, x, y) {\n  el.scrollLeft += x;\n  el.scrollTop += y;\n}\nfunction clone(el) {\n  var Polymer = window.Polymer;\n  var $ = window.jQuery || window.Zepto;\n  if (Polymer && Polymer.dom) {\n    return Polymer.dom(el).cloneNode(true);\n  } else if ($) {\n    return $(el).clone(true)[0];\n  } else {\n    return el.cloneNode(true);\n  }\n}\nfunction setRect(el, rect) {\n  css(el, 'position', 'absolute');\n  css(el, 'top', rect.top);\n  css(el, 'left', rect.left);\n  css(el, 'width', rect.width);\n  css(el, 'height', rect.height);\n}\nfunction unsetRect(el) {\n  css(el, 'position', '');\n  css(el, 'top', '');\n  css(el, 'left', '');\n  css(el, 'width', '');\n  css(el, 'height', '');\n}\nfunction getChildContainingRectFromElement(container, options, ghostEl) {\n  var rect = {};\n  Array.from(container.children).forEach(function (child) {\n    var _rect$left, _rect$top, _rect$right, _rect$bottom;\n    if (!closest(child, options.draggable, container, false) || child.animated || child === ghostEl) return;\n    var childRect = getRect(child);\n    rect.left = Math.min((_rect$left = rect.left) !== null && _rect$left !== void 0 ? _rect$left : Infinity, childRect.left);\n    rect.top = Math.min((_rect$top = rect.top) !== null && _rect$top !== void 0 ? _rect$top : Infinity, childRect.top);\n    rect.right = Math.max((_rect$right = rect.right) !== null && _rect$right !== void 0 ? _rect$right : -Infinity, childRect.right);\n    rect.bottom = Math.max((_rect$bottom = rect.bottom) !== null && _rect$bottom !== void 0 ? _rect$bottom : -Infinity, childRect.bottom);\n  });\n  rect.width = rect.right - rect.left;\n  rect.height = rect.bottom - rect.top;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\nvar expando = 'Sortable' + new Date().getTime();\n\nfunction AnimationStateManager() {\n  var animationStates = [],\n    animationCallbackId;\n  return {\n    captureAnimationState: function captureAnimationState() {\n      animationStates = [];\n      if (!this.options.animation) return;\n      var children = [].slice.call(this.el.children);\n      children.forEach(function (child) {\n        if (css(child, 'display') === 'none' || child === Sortable.ghost) return;\n        animationStates.push({\n          target: child,\n          rect: getRect(child)\n        });\n        var fromRect = _objectSpread2({}, animationStates[animationStates.length - 1].rect);\n\n        // If animating: compensate for current animation\n        if (child.thisAnimationDuration) {\n          var childMatrix = matrix(child, true);\n          if (childMatrix) {\n            fromRect.top -= childMatrix.f;\n            fromRect.left -= childMatrix.e;\n          }\n        }\n        child.fromRect = fromRect;\n      });\n    },\n    addAnimationState: function addAnimationState(state) {\n      animationStates.push(state);\n    },\n    removeAnimationState: function removeAnimationState(target) {\n      animationStates.splice(indexOfObject(animationStates, {\n        target: target\n      }), 1);\n    },\n    animateAll: function animateAll(callback) {\n      var _this = this;\n      if (!this.options.animation) {\n        clearTimeout(animationCallbackId);\n        if (typeof callback === 'function') callback();\n        return;\n      }\n      var animating = false,\n        animationTime = 0;\n      animationStates.forEach(function (state) {\n        var time = 0,\n          target = state.target,\n          fromRect = target.fromRect,\n          toRect = getRect(target),\n          prevFromRect = target.prevFromRect,\n          prevToRect = target.prevToRect,\n          animatingRect = state.rect,\n          targetMatrix = matrix(target, true);\n        if (targetMatrix) {\n          // Compensate for current animation\n          toRect.top -= targetMatrix.f;\n          toRect.left -= targetMatrix.e;\n        }\n        target.toRect = toRect;\n        if (target.thisAnimationDuration) {\n          // Could also check if animatingRect is between fromRect and toRect\n          if (isRectEqual(prevFromRect, toRect) && !isRectEqual(fromRect, toRect) &&\n          // Make sure animatingRect is on line between toRect & fromRect\n          (animatingRect.top - toRect.top) / (animatingRect.left - toRect.left) === (fromRect.top - toRect.top) / (fromRect.left - toRect.left)) {\n            // If returning to same place as started from animation and on same axis\n            time = calculateRealTime(animatingRect, prevFromRect, prevToRect, _this.options);\n          }\n        }\n\n        // if fromRect != toRect: animate\n        if (!isRectEqual(toRect, fromRect)) {\n          target.prevFromRect = fromRect;\n          target.prevToRect = toRect;\n          if (!time) {\n            time = _this.options.animation;\n          }\n          _this.animate(target, animatingRect, toRect, time);\n        }\n        if (time) {\n          animating = true;\n          animationTime = Math.max(animationTime, time);\n          clearTimeout(target.animationResetTimer);\n          target.animationResetTimer = setTimeout(function () {\n            target.animationTime = 0;\n            target.prevFromRect = null;\n            target.fromRect = null;\n            target.prevToRect = null;\n            target.thisAnimationDuration = null;\n          }, time);\n          target.thisAnimationDuration = time;\n        }\n      });\n      clearTimeout(animationCallbackId);\n      if (!animating) {\n        if (typeof callback === 'function') callback();\n      } else {\n        animationCallbackId = setTimeout(function () {\n          if (typeof callback === 'function') callback();\n        }, animationTime);\n      }\n      animationStates = [];\n    },\n    animate: function animate(target, currentRect, toRect, duration) {\n      if (duration) {\n        css(target, 'transition', '');\n        css(target, 'transform', '');\n        var elMatrix = matrix(this.el),\n          scaleX = elMatrix && elMatrix.a,\n          scaleY = elMatrix && elMatrix.d,\n          translateX = (currentRect.left - toRect.left) / (scaleX || 1),\n          translateY = (currentRect.top - toRect.top) / (scaleY || 1);\n        target.animatingX = !!translateX;\n        target.animatingY = !!translateY;\n        css(target, 'transform', 'translate3d(' + translateX + 'px,' + translateY + 'px,0)');\n        this.forRepaintDummy = repaint(target); // repaint\n\n        css(target, 'transition', 'transform ' + duration + 'ms' + (this.options.easing ? ' ' + this.options.easing : ''));\n        css(target, 'transform', 'translate3d(0,0,0)');\n        typeof target.animated === 'number' && clearTimeout(target.animated);\n        target.animated = setTimeout(function () {\n          css(target, 'transition', '');\n          css(target, 'transform', '');\n          target.animated = false;\n          target.animatingX = false;\n          target.animatingY = false;\n        }, duration);\n      }\n    }\n  };\n}\nfunction repaint(target) {\n  return target.offsetWidth;\n}\nfunction calculateRealTime(animatingRect, fromRect, toRect, options) {\n  return Math.sqrt(Math.pow(fromRect.top - animatingRect.top, 2) + Math.pow(fromRect.left - animatingRect.left, 2)) / Math.sqrt(Math.pow(fromRect.top - toRect.top, 2) + Math.pow(fromRect.left - toRect.left, 2)) * options.animation;\n}\n\nvar plugins = [];\nvar defaults = {\n  initializeByDefault: true\n};\nvar PluginManager = {\n  mount: function mount(plugin) {\n    // Set default static properties\n    for (var option in defaults) {\n      if (defaults.hasOwnProperty(option) && !(option in plugin)) {\n        plugin[option] = defaults[option];\n      }\n    }\n    plugins.forEach(function (p) {\n      if (p.pluginName === plugin.pluginName) {\n        throw \"Sortable: Cannot mount plugin \".concat(plugin.pluginName, \" more than once\");\n      }\n    });\n    plugins.push(plugin);\n  },\n  pluginEvent: function pluginEvent(eventName, sortable, evt) {\n    var _this = this;\n    this.eventCanceled = false;\n    evt.cancel = function () {\n      _this.eventCanceled = true;\n    };\n    var eventNameGlobal = eventName + 'Global';\n    plugins.forEach(function (plugin) {\n      if (!sortable[plugin.pluginName]) return;\n      // Fire global events if it exists in this sortable\n      if (sortable[plugin.pluginName][eventNameGlobal]) {\n        sortable[plugin.pluginName][eventNameGlobal](_objectSpread2({\n          sortable: sortable\n        }, evt));\n      }\n\n      // Only fire plugin event if plugin is enabled in this sortable,\n      // and plugin has event defined\n      if (sortable.options[plugin.pluginName] && sortable[plugin.pluginName][eventName]) {\n        sortable[plugin.pluginName][eventName](_objectSpread2({\n          sortable: sortable\n        }, evt));\n      }\n    });\n  },\n  initializePlugins: function initializePlugins(sortable, el, defaults, options) {\n    plugins.forEach(function (plugin) {\n      var pluginName = plugin.pluginName;\n      if (!sortable.options[pluginName] && !plugin.initializeByDefault) return;\n      var initialized = new plugin(sortable, el, sortable.options);\n      initialized.sortable = sortable;\n      initialized.options = sortable.options;\n      sortable[pluginName] = initialized;\n\n      // Add default options from plugin\n      _extends(defaults, initialized.defaults);\n    });\n    for (var option in sortable.options) {\n      if (!sortable.options.hasOwnProperty(option)) continue;\n      var modified = this.modifyOption(sortable, option, sortable.options[option]);\n      if (typeof modified !== 'undefined') {\n        sortable.options[option] = modified;\n      }\n    }\n  },\n  getEventProperties: function getEventProperties(name, sortable) {\n    var eventProperties = {};\n    plugins.forEach(function (plugin) {\n      if (typeof plugin.eventProperties !== 'function') return;\n      _extends(eventProperties, plugin.eventProperties.call(sortable[plugin.pluginName], name));\n    });\n    return eventProperties;\n  },\n  modifyOption: function modifyOption(sortable, name, value) {\n    var modifiedValue;\n    plugins.forEach(function (plugin) {\n      // Plugin must exist on the Sortable\n      if (!sortable[plugin.pluginName]) return;\n\n      // If static option listener exists for this option, call in the context of the Sortable's instance of this plugin\n      if (plugin.optionListeners && typeof plugin.optionListeners[name] === 'function') {\n        modifiedValue = plugin.optionListeners[name].call(sortable[plugin.pluginName], value);\n      }\n    });\n    return modifiedValue;\n  }\n};\n\nfunction dispatchEvent(_ref) {\n  var sortable = _ref.sortable,\n    rootEl = _ref.rootEl,\n    name = _ref.name,\n    targetEl = _ref.targetEl,\n    cloneEl = _ref.cloneEl,\n    toEl = _ref.toEl,\n    fromEl = _ref.fromEl,\n    oldIndex = _ref.oldIndex,\n    newIndex = _ref.newIndex,\n    oldDraggableIndex = _ref.oldDraggableIndex,\n    newDraggableIndex = _ref.newDraggableIndex,\n    originalEvent = _ref.originalEvent,\n    putSortable = _ref.putSortable,\n    extraEventProperties = _ref.extraEventProperties;\n  sortable = sortable || rootEl && rootEl[expando];\n  if (!sortable) return;\n  var evt,\n    options = sortable.options,\n    onName = 'on' + name.charAt(0).toUpperCase() + name.substr(1);\n  // Support for new CustomEvent feature\n  if (window.CustomEvent && !IE11OrLess && !Edge) {\n    evt = new CustomEvent(name, {\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    evt = document.createEvent('Event');\n    evt.initEvent(name, true, true);\n  }\n  evt.to = toEl || rootEl;\n  evt.from = fromEl || rootEl;\n  evt.item = targetEl || rootEl;\n  evt.clone = cloneEl;\n  evt.oldIndex = oldIndex;\n  evt.newIndex = newIndex;\n  evt.oldDraggableIndex = oldDraggableIndex;\n  evt.newDraggableIndex = newDraggableIndex;\n  evt.originalEvent = originalEvent;\n  evt.pullMode = putSortable ? putSortable.lastPutMode : undefined;\n  var allEventProperties = _objectSpread2(_objectSpread2({}, extraEventProperties), PluginManager.getEventProperties(name, sortable));\n  for (var option in allEventProperties) {\n    evt[option] = allEventProperties[option];\n  }\n  if (rootEl) {\n    rootEl.dispatchEvent(evt);\n  }\n  if (options[onName]) {\n    options[onName].call(sortable, evt);\n  }\n}\n\nvar _excluded = [\"evt\"];\nvar pluginEvent = function pluginEvent(eventName, sortable) {\n  var _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n    originalEvent = _ref.evt,\n    data = _objectWithoutProperties(_ref, _excluded);\n  PluginManager.pluginEvent.bind(Sortable)(eventName, sortable, _objectSpread2({\n    dragEl: dragEl,\n    parentEl: parentEl,\n    ghostEl: ghostEl,\n    rootEl: rootEl,\n    nextEl: nextEl,\n    lastDownEl: lastDownEl,\n    cloneEl: cloneEl,\n    cloneHidden: cloneHidden,\n    dragStarted: moved,\n    putSortable: putSortable,\n    activeSortable: Sortable.active,\n    originalEvent: originalEvent,\n    oldIndex: oldIndex,\n    oldDraggableIndex: oldDraggableIndex,\n    newIndex: newIndex,\n    newDraggableIndex: newDraggableIndex,\n    hideGhostForTarget: _hideGhostForTarget,\n    unhideGhostForTarget: _unhideGhostForTarget,\n    cloneNowHidden: function cloneNowHidden() {\n      cloneHidden = true;\n    },\n    cloneNowShown: function cloneNowShown() {\n      cloneHidden = false;\n    },\n    dispatchSortableEvent: function dispatchSortableEvent(name) {\n      _dispatchEvent({\n        sortable: sortable,\n        name: name,\n        originalEvent: originalEvent\n      });\n    }\n  }, data));\n};\nfunction _dispatchEvent(info) {\n  dispatchEvent(_objectSpread2({\n    putSortable: putSortable,\n    cloneEl: cloneEl,\n    targetEl: dragEl,\n    rootEl: rootEl,\n    oldIndex: oldIndex,\n    oldDraggableIndex: oldDraggableIndex,\n    newIndex: newIndex,\n    newDraggableIndex: newDraggableIndex\n  }, info));\n}\nvar dragEl,\n  parentEl,\n  ghostEl,\n  rootEl,\n  nextEl,\n  lastDownEl,\n  cloneEl,\n  cloneHidden,\n  oldIndex,\n  newIndex,\n  oldDraggableIndex,\n  newDraggableIndex,\n  activeGroup,\n  putSortable,\n  awaitingDragStarted = false,\n  ignoreNextClick = false,\n  sortables = [],\n  tapEvt,\n  touchEvt,\n  lastDx,\n  lastDy,\n  tapDistanceLeft,\n  tapDistanceTop,\n  moved,\n  lastTarget,\n  lastDirection,\n  pastFirstInvertThresh = false,\n  isCircumstantialInvert = false,\n  targetMoveDistance,\n  // For positioning ghost absolutely\n  ghostRelativeParent,\n  ghostRelativeParentInitialScroll = [],\n  // (left, top)\n\n  _silent = false,\n  savedInputChecked = [];\n\n/** @const */\nvar documentExists = typeof document !== 'undefined',\n  PositionGhostAbsolutely = IOS,\n  CSSFloatProperty = Edge || IE11OrLess ? 'cssFloat' : 'float',\n  // This will not pass for IE9, because IE9 DnD only works on anchors\n  supportDraggable = documentExists && !ChromeForAndroid && !IOS && 'draggable' in document.createElement('div'),\n  supportCssPointerEvents = function () {\n    if (!documentExists) return;\n    // false when <= IE11\n    if (IE11OrLess) {\n      return false;\n    }\n    var el = document.createElement('x');\n    el.style.cssText = 'pointer-events:auto';\n    return el.style.pointerEvents === 'auto';\n  }(),\n  _detectDirection = function _detectDirection(el, options) {\n    var elCSS = css(el),\n      elWidth = parseInt(elCSS.width) - parseInt(elCSS.paddingLeft) - parseInt(elCSS.paddingRight) - parseInt(elCSS.borderLeftWidth) - parseInt(elCSS.borderRightWidth),\n      child1 = getChild(el, 0, options),\n      child2 = getChild(el, 1, options),\n      firstChildCSS = child1 && css(child1),\n      secondChildCSS = child2 && css(child2),\n      firstChildWidth = firstChildCSS && parseInt(firstChildCSS.marginLeft) + parseInt(firstChildCSS.marginRight) + getRect(child1).width,\n      secondChildWidth = secondChildCSS && parseInt(secondChildCSS.marginLeft) + parseInt(secondChildCSS.marginRight) + getRect(child2).width;\n    if (elCSS.display === 'flex') {\n      return elCSS.flexDirection === 'column' || elCSS.flexDirection === 'column-reverse' ? 'vertical' : 'horizontal';\n    }\n    if (elCSS.display === 'grid') {\n      return elCSS.gridTemplateColumns.split(' ').length <= 1 ? 'vertical' : 'horizontal';\n    }\n    if (child1 && firstChildCSS[\"float\"] && firstChildCSS[\"float\"] !== 'none') {\n      var touchingSideChild2 = firstChildCSS[\"float\"] === 'left' ? 'left' : 'right';\n      return child2 && (secondChildCSS.clear === 'both' || secondChildCSS.clear === touchingSideChild2) ? 'vertical' : 'horizontal';\n    }\n    return child1 && (firstChildCSS.display === 'block' || firstChildCSS.display === 'flex' || firstChildCSS.display === 'table' || firstChildCSS.display === 'grid' || firstChildWidth >= elWidth && elCSS[CSSFloatProperty] === 'none' || child2 && elCSS[CSSFloatProperty] === 'none' && firstChildWidth + secondChildWidth > elWidth) ? 'vertical' : 'horizontal';\n  },\n  _dragElInRowColumn = function _dragElInRowColumn(dragRect, targetRect, vertical) {\n    var dragElS1Opp = vertical ? dragRect.left : dragRect.top,\n      dragElS2Opp = vertical ? dragRect.right : dragRect.bottom,\n      dragElOppLength = vertical ? dragRect.width : dragRect.height,\n      targetS1Opp = vertical ? targetRect.left : targetRect.top,\n      targetS2Opp = vertical ? targetRect.right : targetRect.bottom,\n      targetOppLength = vertical ? targetRect.width : targetRect.height;\n    return dragElS1Opp === targetS1Opp || dragElS2Opp === targetS2Opp || dragElS1Opp + dragElOppLength / 2 === targetS1Opp + targetOppLength / 2;\n  },\n  /**\r\n   * Detects first nearest empty sortable to X and Y position using emptyInsertThreshold.\r\n   * @param  {Number} x      X position\r\n   * @param  {Number} y      Y position\r\n   * @return {HTMLElement}   Element of the first found nearest Sortable\r\n   */\n  _detectNearestEmptySortable = function _detectNearestEmptySortable(x, y) {\n    var ret;\n    sortables.some(function (sortable) {\n      var threshold = sortable[expando].options.emptyInsertThreshold;\n      if (!threshold || lastChild(sortable)) return;\n      var rect = getRect(sortable),\n        insideHorizontally = x >= rect.left - threshold && x <= rect.right + threshold,\n        insideVertically = y >= rect.top - threshold && y <= rect.bottom + threshold;\n      if (insideHorizontally && insideVertically) {\n        return ret = sortable;\n      }\n    });\n    return ret;\n  },\n  _prepareGroup = function _prepareGroup(options) {\n    function toFn(value, pull) {\n      return function (to, from, dragEl, evt) {\n        var sameGroup = to.options.group.name && from.options.group.name && to.options.group.name === from.options.group.name;\n        if (value == null && (pull || sameGroup)) {\n          // Default pull value\n          // Default pull and put value if same group\n          return true;\n        } else if (value == null || value === false) {\n          return false;\n        } else if (pull && value === 'clone') {\n          return value;\n        } else if (typeof value === 'function') {\n          return toFn(value(to, from, dragEl, evt), pull)(to, from, dragEl, evt);\n        } else {\n          var otherGroup = (pull ? to : from).options.group.name;\n          return value === true || typeof value === 'string' && value === otherGroup || value.join && value.indexOf(otherGroup) > -1;\n        }\n      };\n    }\n    var group = {};\n    var originalGroup = options.group;\n    if (!originalGroup || _typeof(originalGroup) != 'object') {\n      originalGroup = {\n        name: originalGroup\n      };\n    }\n    group.name = originalGroup.name;\n    group.checkPull = toFn(originalGroup.pull, true);\n    group.checkPut = toFn(originalGroup.put);\n    group.revertClone = originalGroup.revertClone;\n    options.group = group;\n  },\n  _hideGhostForTarget = function _hideGhostForTarget() {\n    if (!supportCssPointerEvents && ghostEl) {\n      css(ghostEl, 'display', 'none');\n    }\n  },\n  _unhideGhostForTarget = function _unhideGhostForTarget() {\n    if (!supportCssPointerEvents && ghostEl) {\n      css(ghostEl, 'display', '');\n    }\n  };\n\n// #1184 fix - Prevent click event on fallback if dragged but item not changed position\nif (documentExists && !ChromeForAndroid) {\n  document.addEventListener('click', function (evt) {\n    if (ignoreNextClick) {\n      evt.preventDefault();\n      evt.stopPropagation && evt.stopPropagation();\n      evt.stopImmediatePropagation && evt.stopImmediatePropagation();\n      ignoreNextClick = false;\n      return false;\n    }\n  }, true);\n}\nvar nearestEmptyInsertDetectEvent = function nearestEmptyInsertDetectEvent(evt) {\n  if (dragEl) {\n    evt = evt.touches ? evt.touches[0] : evt;\n    var nearest = _detectNearestEmptySortable(evt.clientX, evt.clientY);\n    if (nearest) {\n      // Create imitation event\n      var event = {};\n      for (var i in evt) {\n        if (evt.hasOwnProperty(i)) {\n          event[i] = evt[i];\n        }\n      }\n      event.target = event.rootEl = nearest;\n      event.preventDefault = void 0;\n      event.stopPropagation = void 0;\n      nearest[expando]._onDragOver(event);\n    }\n  }\n};\nvar _checkOutsideTargetEl = function _checkOutsideTargetEl(evt) {\n  if (dragEl) {\n    dragEl.parentNode[expando]._isOutsideThisEl(evt.target);\n  }\n};\n\n/**\r\n * @class  Sortable\r\n * @param  {HTMLElement}  el\r\n * @param  {Object}       [options]\r\n */\nfunction Sortable(el, options) {\n  if (!(el && el.nodeType && el.nodeType === 1)) {\n    throw \"Sortable: `el` must be an HTMLElement, not \".concat({}.toString.call(el));\n  }\n  this.el = el; // root element\n  this.options = options = _extends({}, options);\n\n  // Export instance\n  el[expando] = this;\n  var defaults = {\n    group: null,\n    sort: true,\n    disabled: false,\n    store: null,\n    handle: null,\n    draggable: /^[uo]l$/i.test(el.nodeName) ? '>li' : '>*',\n    swapThreshold: 1,\n    // percentage; 0 <= x <= 1\n    invertSwap: false,\n    // invert always\n    invertedSwapThreshold: null,\n    // will be set to same as swapThreshold if default\n    removeCloneOnHide: true,\n    direction: function direction() {\n      return _detectDirection(el, this.options);\n    },\n    ghostClass: 'sortable-ghost',\n    chosenClass: 'sortable-chosen',\n    dragClass: 'sortable-drag',\n    ignore: 'a, img',\n    filter: null,\n    preventOnFilter: true,\n    animation: 0,\n    easing: null,\n    setData: function setData(dataTransfer, dragEl) {\n      dataTransfer.setData('Text', dragEl.textContent);\n    },\n    dropBubble: false,\n    dragoverBubble: false,\n    dataIdAttr: 'data-id',\n    delay: 0,\n    delayOnTouchOnly: false,\n    touchStartThreshold: (Number.parseInt ? Number : window).parseInt(window.devicePixelRatio, 10) || 1,\n    forceFallback: false,\n    fallbackClass: 'sortable-fallback',\n    fallbackOnBody: false,\n    fallbackTolerance: 0,\n    fallbackOffset: {\n      x: 0,\n      y: 0\n    },\n    supportPointer: Sortable.supportPointer !== false && 'PointerEvent' in window && !Safari,\n    emptyInsertThreshold: 5\n  };\n  PluginManager.initializePlugins(this, el, defaults);\n\n  // Set default options\n  for (var name in defaults) {\n    !(name in options) && (options[name] = defaults[name]);\n  }\n  _prepareGroup(options);\n\n  // Bind all private methods\n  for (var fn in this) {\n    if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n      this[fn] = this[fn].bind(this);\n    }\n  }\n\n  // Setup drag mode\n  this.nativeDraggable = options.forceFallback ? false : supportDraggable;\n  if (this.nativeDraggable) {\n    // Touch start threshold cannot be greater than the native dragstart threshold\n    this.options.touchStartThreshold = 1;\n  }\n\n  // Bind events\n  if (options.supportPointer) {\n    on(el, 'pointerdown', this._onTapStart);\n  } else {\n    on(el, 'mousedown', this._onTapStart);\n    on(el, 'touchstart', this._onTapStart);\n  }\n  if (this.nativeDraggable) {\n    on(el, 'dragover', this);\n    on(el, 'dragenter', this);\n  }\n  sortables.push(this.el);\n\n  // Restore sorting\n  options.store && options.store.get && this.sort(options.store.get(this) || []);\n\n  // Add animation state manager\n  _extends(this, AnimationStateManager());\n}\nSortable.prototype = /** @lends Sortable.prototype */{\n  constructor: Sortable,\n  _isOutsideThisEl: function _isOutsideThisEl(target) {\n    if (!this.el.contains(target) && target !== this.el) {\n      lastTarget = null;\n    }\n  },\n  _getDirection: function _getDirection(evt, target) {\n    return typeof this.options.direction === 'function' ? this.options.direction.call(this, evt, target, dragEl) : this.options.direction;\n  },\n  _onTapStart: function _onTapStart( /** Event|TouchEvent */evt) {\n    if (!evt.cancelable) return;\n    var _this = this,\n      el = this.el,\n      options = this.options,\n      preventOnFilter = options.preventOnFilter,\n      type = evt.type,\n      touch = evt.touches && evt.touches[0] || evt.pointerType && evt.pointerType === 'touch' && evt,\n      target = (touch || evt).target,\n      originalTarget = evt.target.shadowRoot && (evt.path && evt.path[0] || evt.composedPath && evt.composedPath()[0]) || target,\n      filter = options.filter;\n    _saveInputCheckedState(el);\n\n    // Don't trigger start event when an element is been dragged, otherwise the evt.oldindex always wrong when set option.group.\n    if (dragEl) {\n      return;\n    }\n    if (/mousedown|pointerdown/.test(type) && evt.button !== 0 || options.disabled) {\n      return; // only left button and enabled\n    }\n\n    // cancel dnd if original target is content editable\n    if (originalTarget.isContentEditable) {\n      return;\n    }\n\n    // Safari ignores further event handling after mousedown\n    if (!this.nativeDraggable && Safari && target && target.tagName.toUpperCase() === 'SELECT') {\n      return;\n    }\n    target = closest(target, options.draggable, el, false);\n    if (target && target.animated) {\n      return;\n    }\n    if (lastDownEl === target) {\n      // Ignoring duplicate `down`\n      return;\n    }\n\n    // Get the index of the dragged element within its parent\n    oldIndex = index(target);\n    oldDraggableIndex = index(target, options.draggable);\n\n    // Check filter\n    if (typeof filter === 'function') {\n      if (filter.call(this, evt, target, this)) {\n        _dispatchEvent({\n          sortable: _this,\n          rootEl: originalTarget,\n          name: 'filter',\n          targetEl: target,\n          toEl: el,\n          fromEl: el\n        });\n        pluginEvent('filter', _this, {\n          evt: evt\n        });\n        preventOnFilter && evt.cancelable && evt.preventDefault();\n        return; // cancel dnd\n      }\n    } else if (filter) {\n      filter = filter.split(',').some(function (criteria) {\n        criteria = closest(originalTarget, criteria.trim(), el, false);\n        if (criteria) {\n          _dispatchEvent({\n            sortable: _this,\n            rootEl: criteria,\n            name: 'filter',\n            targetEl: target,\n            fromEl: el,\n            toEl: el\n          });\n          pluginEvent('filter', _this, {\n            evt: evt\n          });\n          return true;\n        }\n      });\n      if (filter) {\n        preventOnFilter && evt.cancelable && evt.preventDefault();\n        return; // cancel dnd\n      }\n    }\n    if (options.handle && !closest(originalTarget, options.handle, el, false)) {\n      return;\n    }\n\n    // Prepare `dragstart`\n    this._prepareDragStart(evt, touch, target);\n  },\n  _prepareDragStart: function _prepareDragStart( /** Event */evt, /** Touch */touch, /** HTMLElement */target) {\n    var _this = this,\n      el = _this.el,\n      options = _this.options,\n      ownerDocument = el.ownerDocument,\n      dragStartFn;\n    if (target && !dragEl && target.parentNode === el) {\n      var dragRect = getRect(target);\n      rootEl = el;\n      dragEl = target;\n      parentEl = dragEl.parentNode;\n      nextEl = dragEl.nextSibling;\n      lastDownEl = target;\n      activeGroup = options.group;\n      Sortable.dragged = dragEl;\n      tapEvt = {\n        target: dragEl,\n        clientX: (touch || evt).clientX,\n        clientY: (touch || evt).clientY\n      };\n      tapDistanceLeft = tapEvt.clientX - dragRect.left;\n      tapDistanceTop = tapEvt.clientY - dragRect.top;\n      this._lastX = (touch || evt).clientX;\n      this._lastY = (touch || evt).clientY;\n      dragEl.style['will-change'] = 'all';\n      dragStartFn = function dragStartFn() {\n        pluginEvent('delayEnded', _this, {\n          evt: evt\n        });\n        if (Sortable.eventCanceled) {\n          _this._onDrop();\n          return;\n        }\n        // Delayed drag has been triggered\n        // we can re-enable the events: touchmove/mousemove\n        _this._disableDelayedDragEvents();\n        if (!FireFox && _this.nativeDraggable) {\n          dragEl.draggable = true;\n        }\n\n        // Bind the events: dragstart/dragend\n        _this._triggerDragStart(evt, touch);\n\n        // Drag start event\n        _dispatchEvent({\n          sortable: _this,\n          name: 'choose',\n          originalEvent: evt\n        });\n\n        // Chosen item\n        toggleClass(dragEl, options.chosenClass, true);\n      };\n\n      // Disable \"draggable\"\n      options.ignore.split(',').forEach(function (criteria) {\n        find(dragEl, criteria.trim(), _disableDraggable);\n      });\n      on(ownerDocument, 'dragover', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'mousemove', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'touchmove', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'mouseup', _this._onDrop);\n      on(ownerDocument, 'touchend', _this._onDrop);\n      on(ownerDocument, 'touchcancel', _this._onDrop);\n\n      // Make dragEl draggable (must be before delay for FireFox)\n      if (FireFox && this.nativeDraggable) {\n        this.options.touchStartThreshold = 4;\n        dragEl.draggable = true;\n      }\n      pluginEvent('delayStart', this, {\n        evt: evt\n      });\n\n      // Delay is impossible for native DnD in Edge or IE\n      if (options.delay && (!options.delayOnTouchOnly || touch) && (!this.nativeDraggable || !(Edge || IE11OrLess))) {\n        if (Sortable.eventCanceled) {\n          this._onDrop();\n          return;\n        }\n        // If the user moves the pointer or let go the click or touch\n        // before the delay has been reached:\n        // disable the delayed drag\n        on(ownerDocument, 'mouseup', _this._disableDelayedDrag);\n        on(ownerDocument, 'touchend', _this._disableDelayedDrag);\n        on(ownerDocument, 'touchcancel', _this._disableDelayedDrag);\n        on(ownerDocument, 'mousemove', _this._delayedDragTouchMoveHandler);\n        on(ownerDocument, 'touchmove', _this._delayedDragTouchMoveHandler);\n        options.supportPointer && on(ownerDocument, 'pointermove', _this._delayedDragTouchMoveHandler);\n        _this._dragStartTimer = setTimeout(dragStartFn, options.delay);\n      } else {\n        dragStartFn();\n      }\n    }\n  },\n  _delayedDragTouchMoveHandler: function _delayedDragTouchMoveHandler( /** TouchEvent|PointerEvent **/e) {\n    var touch = e.touches ? e.touches[0] : e;\n    if (Math.max(Math.abs(touch.clientX - this._lastX), Math.abs(touch.clientY - this._lastY)) >= Math.floor(this.options.touchStartThreshold / (this.nativeDraggable && window.devicePixelRatio || 1))) {\n      this._disableDelayedDrag();\n    }\n  },\n  _disableDelayedDrag: function _disableDelayedDrag() {\n    dragEl && _disableDraggable(dragEl);\n    clearTimeout(this._dragStartTimer);\n    this._disableDelayedDragEvents();\n  },\n  _disableDelayedDragEvents: function _disableDelayedDragEvents() {\n    var ownerDocument = this.el.ownerDocument;\n    off(ownerDocument, 'mouseup', this._disableDelayedDrag);\n    off(ownerDocument, 'touchend', this._disableDelayedDrag);\n    off(ownerDocument, 'touchcancel', this._disableDelayedDrag);\n    off(ownerDocument, 'mousemove', this._delayedDragTouchMoveHandler);\n    off(ownerDocument, 'touchmove', this._delayedDragTouchMoveHandler);\n    off(ownerDocument, 'pointermove', this._delayedDragTouchMoveHandler);\n  },\n  _triggerDragStart: function _triggerDragStart( /** Event */evt, /** Touch */touch) {\n    touch = touch || evt.pointerType == 'touch' && evt;\n    if (!this.nativeDraggable || touch) {\n      if (this.options.supportPointer) {\n        on(document, 'pointermove', this._onTouchMove);\n      } else if (touch) {\n        on(document, 'touchmove', this._onTouchMove);\n      } else {\n        on(document, 'mousemove', this._onTouchMove);\n      }\n    } else {\n      on(dragEl, 'dragend', this);\n      on(rootEl, 'dragstart', this._onDragStart);\n    }\n    try {\n      if (document.selection) {\n        // Timeout neccessary for IE9\n        _nextTick(function () {\n          document.selection.empty();\n        });\n      } else {\n        window.getSelection().removeAllRanges();\n      }\n    } catch (err) {}\n  },\n  _dragStarted: function _dragStarted(fallback, evt) {\n    awaitingDragStarted = false;\n    if (rootEl && dragEl) {\n      pluginEvent('dragStarted', this, {\n        evt: evt\n      });\n      if (this.nativeDraggable) {\n        on(document, 'dragover', _checkOutsideTargetEl);\n      }\n      var options = this.options;\n\n      // Apply effect\n      !fallback && toggleClass(dragEl, options.dragClass, false);\n      toggleClass(dragEl, options.ghostClass, true);\n      Sortable.active = this;\n      fallback && this._appendGhost();\n\n      // Drag start event\n      _dispatchEvent({\n        sortable: this,\n        name: 'start',\n        originalEvent: evt\n      });\n    } else {\n      this._nulling();\n    }\n  },\n  _emulateDragOver: function _emulateDragOver() {\n    if (touchEvt) {\n      this._lastX = touchEvt.clientX;\n      this._lastY = touchEvt.clientY;\n      _hideGhostForTarget();\n      var target = document.elementFromPoint(touchEvt.clientX, touchEvt.clientY);\n      var parent = target;\n      while (target && target.shadowRoot) {\n        target = target.shadowRoot.elementFromPoint(touchEvt.clientX, touchEvt.clientY);\n        if (target === parent) break;\n        parent = target;\n      }\n      dragEl.parentNode[expando]._isOutsideThisEl(target);\n      if (parent) {\n        do {\n          if (parent[expando]) {\n            var inserted = void 0;\n            inserted = parent[expando]._onDragOver({\n              clientX: touchEvt.clientX,\n              clientY: touchEvt.clientY,\n              target: target,\n              rootEl: parent\n            });\n            if (inserted && !this.options.dragoverBubble) {\n              break;\n            }\n          }\n          target = parent; // store last element\n        }\n        /* jshint boss:true */ while (parent = getParentOrHost(parent));\n      }\n      _unhideGhostForTarget();\n    }\n  },\n  _onTouchMove: function _onTouchMove( /**TouchEvent*/evt) {\n    if (tapEvt) {\n      var options = this.options,\n        fallbackTolerance = options.fallbackTolerance,\n        fallbackOffset = options.fallbackOffset,\n        touch = evt.touches ? evt.touches[0] : evt,\n        ghostMatrix = ghostEl && matrix(ghostEl, true),\n        scaleX = ghostEl && ghostMatrix && ghostMatrix.a,\n        scaleY = ghostEl && ghostMatrix && ghostMatrix.d,\n        relativeScrollOffset = PositionGhostAbsolutely && ghostRelativeParent && getRelativeScrollOffset(ghostRelativeParent),\n        dx = (touch.clientX - tapEvt.clientX + fallbackOffset.x) / (scaleX || 1) + (relativeScrollOffset ? relativeScrollOffset[0] - ghostRelativeParentInitialScroll[0] : 0) / (scaleX || 1),\n        dy = (touch.clientY - tapEvt.clientY + fallbackOffset.y) / (scaleY || 1) + (relativeScrollOffset ? relativeScrollOffset[1] - ghostRelativeParentInitialScroll[1] : 0) / (scaleY || 1);\n\n      // only set the status to dragging, when we are actually dragging\n      if (!Sortable.active && !awaitingDragStarted) {\n        if (fallbackTolerance && Math.max(Math.abs(touch.clientX - this._lastX), Math.abs(touch.clientY - this._lastY)) < fallbackTolerance) {\n          return;\n        }\n        this._onDragStart(evt, true);\n      }\n      if (ghostEl) {\n        if (ghostMatrix) {\n          ghostMatrix.e += dx - (lastDx || 0);\n          ghostMatrix.f += dy - (lastDy || 0);\n        } else {\n          ghostMatrix = {\n            a: 1,\n            b: 0,\n            c: 0,\n            d: 1,\n            e: dx,\n            f: dy\n          };\n        }\n        var cssMatrix = \"matrix(\".concat(ghostMatrix.a, \",\").concat(ghostMatrix.b, \",\").concat(ghostMatrix.c, \",\").concat(ghostMatrix.d, \",\").concat(ghostMatrix.e, \",\").concat(ghostMatrix.f, \")\");\n        css(ghostEl, 'webkitTransform', cssMatrix);\n        css(ghostEl, 'mozTransform', cssMatrix);\n        css(ghostEl, 'msTransform', cssMatrix);\n        css(ghostEl, 'transform', cssMatrix);\n        lastDx = dx;\n        lastDy = dy;\n        touchEvt = touch;\n      }\n      evt.cancelable && evt.preventDefault();\n    }\n  },\n  _appendGhost: function _appendGhost() {\n    // Bug if using scale(): https://stackoverflow.com/questions/2637058\n    // Not being adjusted for\n    if (!ghostEl) {\n      var container = this.options.fallbackOnBody ? document.body : rootEl,\n        rect = getRect(dragEl, true, PositionGhostAbsolutely, true, container),\n        options = this.options;\n\n      // Position absolutely\n      if (PositionGhostAbsolutely) {\n        // Get relatively positioned parent\n        ghostRelativeParent = container;\n        while (css(ghostRelativeParent, 'position') === 'static' && css(ghostRelativeParent, 'transform') === 'none' && ghostRelativeParent !== document) {\n          ghostRelativeParent = ghostRelativeParent.parentNode;\n        }\n        if (ghostRelativeParent !== document.body && ghostRelativeParent !== document.documentElement) {\n          if (ghostRelativeParent === document) ghostRelativeParent = getWindowScrollingElement();\n          rect.top += ghostRelativeParent.scrollTop;\n          rect.left += ghostRelativeParent.scrollLeft;\n        } else {\n          ghostRelativeParent = getWindowScrollingElement();\n        }\n        ghostRelativeParentInitialScroll = getRelativeScrollOffset(ghostRelativeParent);\n      }\n      ghostEl = dragEl.cloneNode(true);\n      toggleClass(ghostEl, options.ghostClass, false);\n      toggleClass(ghostEl, options.fallbackClass, true);\n      toggleClass(ghostEl, options.dragClass, true);\n      css(ghostEl, 'transition', '');\n      css(ghostEl, 'transform', '');\n      css(ghostEl, 'box-sizing', 'border-box');\n      css(ghostEl, 'margin', 0);\n      css(ghostEl, 'top', rect.top);\n      css(ghostEl, 'left', rect.left);\n      css(ghostEl, 'width', rect.width);\n      css(ghostEl, 'height', rect.height);\n      css(ghostEl, 'opacity', '0.8');\n      css(ghostEl, 'position', PositionGhostAbsolutely ? 'absolute' : 'fixed');\n      css(ghostEl, 'zIndex', '100000');\n      css(ghostEl, 'pointerEvents', 'none');\n      Sortable.ghost = ghostEl;\n      container.appendChild(ghostEl);\n\n      // Set transform-origin\n      css(ghostEl, 'transform-origin', tapDistanceLeft / parseInt(ghostEl.style.width) * 100 + '% ' + tapDistanceTop / parseInt(ghostEl.style.height) * 100 + '%');\n    }\n  },\n  _onDragStart: function _onDragStart( /**Event*/evt, /**boolean*/fallback) {\n    var _this = this;\n    var dataTransfer = evt.dataTransfer;\n    var options = _this.options;\n    pluginEvent('dragStart', this, {\n      evt: evt\n    });\n    if (Sortable.eventCanceled) {\n      this._onDrop();\n      return;\n    }\n    pluginEvent('setupClone', this);\n    if (!Sortable.eventCanceled) {\n      cloneEl = clone(dragEl);\n      cloneEl.removeAttribute(\"id\");\n      cloneEl.draggable = false;\n      cloneEl.style['will-change'] = '';\n      this._hideClone();\n      toggleClass(cloneEl, this.options.chosenClass, false);\n      Sortable.clone = cloneEl;\n    }\n\n    // #1143: IFrame support workaround\n    _this.cloneId = _nextTick(function () {\n      pluginEvent('clone', _this);\n      if (Sortable.eventCanceled) return;\n      if (!_this.options.removeCloneOnHide) {\n        rootEl.insertBefore(cloneEl, dragEl);\n      }\n      _this._hideClone();\n      _dispatchEvent({\n        sortable: _this,\n        name: 'clone'\n      });\n    });\n    !fallback && toggleClass(dragEl, options.dragClass, true);\n\n    // Set proper drop events\n    if (fallback) {\n      ignoreNextClick = true;\n      _this._loopId = setInterval(_this._emulateDragOver, 50);\n    } else {\n      // Undo what was set in _prepareDragStart before drag started\n      off(document, 'mouseup', _this._onDrop);\n      off(document, 'touchend', _this._onDrop);\n      off(document, 'touchcancel', _this._onDrop);\n      if (dataTransfer) {\n        dataTransfer.effectAllowed = 'move';\n        options.setData && options.setData.call(_this, dataTransfer, dragEl);\n      }\n      on(document, 'drop', _this);\n\n      // #1276 fix:\n      css(dragEl, 'transform', 'translateZ(0)');\n    }\n    awaitingDragStarted = true;\n    _this._dragStartId = _nextTick(_this._dragStarted.bind(_this, fallback, evt));\n    on(document, 'selectstart', _this);\n    moved = true;\n    if (Safari) {\n      css(document.body, 'user-select', 'none');\n    }\n  },\n  // Returns true - if no further action is needed (either inserted or another condition)\n  _onDragOver: function _onDragOver( /**Event*/evt) {\n    var el = this.el,\n      target = evt.target,\n      dragRect,\n      targetRect,\n      revert,\n      options = this.options,\n      group = options.group,\n      activeSortable = Sortable.active,\n      isOwner = activeGroup === group,\n      canSort = options.sort,\n      fromSortable = putSortable || activeSortable,\n      vertical,\n      _this = this,\n      completedFired = false;\n    if (_silent) return;\n    function dragOverEvent(name, extra) {\n      pluginEvent(name, _this, _objectSpread2({\n        evt: evt,\n        isOwner: isOwner,\n        axis: vertical ? 'vertical' : 'horizontal',\n        revert: revert,\n        dragRect: dragRect,\n        targetRect: targetRect,\n        canSort: canSort,\n        fromSortable: fromSortable,\n        target: target,\n        completed: completed,\n        onMove: function onMove(target, after) {\n          return _onMove(rootEl, el, dragEl, dragRect, target, getRect(target), evt, after);\n        },\n        changed: changed\n      }, extra));\n    }\n\n    // Capture animation state\n    function capture() {\n      dragOverEvent('dragOverAnimationCapture');\n      _this.captureAnimationState();\n      if (_this !== fromSortable) {\n        fromSortable.captureAnimationState();\n      }\n    }\n\n    // Return invocation when dragEl is inserted (or completed)\n    function completed(insertion) {\n      dragOverEvent('dragOverCompleted', {\n        insertion: insertion\n      });\n      if (insertion) {\n        // Clones must be hidden before folding animation to capture dragRectAbsolute properly\n        if (isOwner) {\n          activeSortable._hideClone();\n        } else {\n          activeSortable._showClone(_this);\n        }\n        if (_this !== fromSortable) {\n          // Set ghost class to new sortable's ghost class\n          toggleClass(dragEl, putSortable ? putSortable.options.ghostClass : activeSortable.options.ghostClass, false);\n          toggleClass(dragEl, options.ghostClass, true);\n        }\n        if (putSortable !== _this && _this !== Sortable.active) {\n          putSortable = _this;\n        } else if (_this === Sortable.active && putSortable) {\n          putSortable = null;\n        }\n\n        // Animation\n        if (fromSortable === _this) {\n          _this._ignoreWhileAnimating = target;\n        }\n        _this.animateAll(function () {\n          dragOverEvent('dragOverAnimationComplete');\n          _this._ignoreWhileAnimating = null;\n        });\n        if (_this !== fromSortable) {\n          fromSortable.animateAll();\n          fromSortable._ignoreWhileAnimating = null;\n        }\n      }\n\n      // Null lastTarget if it is not inside a previously swapped element\n      if (target === dragEl && !dragEl.animated || target === el && !target.animated) {\n        lastTarget = null;\n      }\n\n      // no bubbling and not fallback\n      if (!options.dragoverBubble && !evt.rootEl && target !== document) {\n        dragEl.parentNode[expando]._isOutsideThisEl(evt.target);\n\n        // Do not detect for empty insert if already inserted\n        !insertion && nearestEmptyInsertDetectEvent(evt);\n      }\n      !options.dragoverBubble && evt.stopPropagation && evt.stopPropagation();\n      return completedFired = true;\n    }\n\n    // Call when dragEl has been inserted\n    function changed() {\n      newIndex = index(dragEl);\n      newDraggableIndex = index(dragEl, options.draggable);\n      _dispatchEvent({\n        sortable: _this,\n        name: 'change',\n        toEl: el,\n        newIndex: newIndex,\n        newDraggableIndex: newDraggableIndex,\n        originalEvent: evt\n      });\n    }\n    if (evt.preventDefault !== void 0) {\n      evt.cancelable && evt.preventDefault();\n    }\n    target = closest(target, options.draggable, el, true);\n    dragOverEvent('dragOver');\n    if (Sortable.eventCanceled) return completedFired;\n    if (dragEl.contains(evt.target) || target.animated && target.animatingX && target.animatingY || _this._ignoreWhileAnimating === target) {\n      return completed(false);\n    }\n    ignoreNextClick = false;\n    if (activeSortable && !options.disabled && (isOwner ? canSort || (revert = parentEl !== rootEl) // Reverting item into the original list\n    : putSortable === this || (this.lastPutMode = activeGroup.checkPull(this, activeSortable, dragEl, evt)) && group.checkPut(this, activeSortable, dragEl, evt))) {\n      vertical = this._getDirection(evt, target) === 'vertical';\n      dragRect = getRect(dragEl);\n      dragOverEvent('dragOverValid');\n      if (Sortable.eventCanceled) return completedFired;\n      if (revert) {\n        parentEl = rootEl; // actualization\n        capture();\n        this._hideClone();\n        dragOverEvent('revert');\n        if (!Sortable.eventCanceled) {\n          if (nextEl) {\n            rootEl.insertBefore(dragEl, nextEl);\n          } else {\n            rootEl.appendChild(dragEl);\n          }\n        }\n        return completed(true);\n      }\n      var elLastChild = lastChild(el, options.draggable);\n      if (!elLastChild || _ghostIsLast(evt, vertical, this) && !elLastChild.animated) {\n        // Insert to end of list\n\n        // If already at end of list: Do not insert\n        if (elLastChild === dragEl) {\n          return completed(false);\n        }\n\n        // if there is a last element, it is the target\n        if (elLastChild && el === evt.target) {\n          target = elLastChild;\n        }\n        if (target) {\n          targetRect = getRect(target);\n        }\n        if (_onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, !!target) !== false) {\n          capture();\n          if (elLastChild && elLastChild.nextSibling) {\n            // the last draggable element is not the last node\n            el.insertBefore(dragEl, elLastChild.nextSibling);\n          } else {\n            el.appendChild(dragEl);\n          }\n          parentEl = el; // actualization\n\n          changed();\n          return completed(true);\n        }\n      } else if (elLastChild && _ghostIsFirst(evt, vertical, this)) {\n        // Insert to start of list\n        var firstChild = getChild(el, 0, options, true);\n        if (firstChild === dragEl) {\n          return completed(false);\n        }\n        target = firstChild;\n        targetRect = getRect(target);\n        if (_onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, false) !== false) {\n          capture();\n          el.insertBefore(dragEl, firstChild);\n          parentEl = el; // actualization\n\n          changed();\n          return completed(true);\n        }\n      } else if (target.parentNode === el) {\n        targetRect = getRect(target);\n        var direction = 0,\n          targetBeforeFirstSwap,\n          differentLevel = dragEl.parentNode !== el,\n          differentRowCol = !_dragElInRowColumn(dragEl.animated && dragEl.toRect || dragRect, target.animated && target.toRect || targetRect, vertical),\n          side1 = vertical ? 'top' : 'left',\n          scrolledPastTop = isScrolledPast(target, 'top', 'top') || isScrolledPast(dragEl, 'top', 'top'),\n          scrollBefore = scrolledPastTop ? scrolledPastTop.scrollTop : void 0;\n        if (lastTarget !== target) {\n          targetBeforeFirstSwap = targetRect[side1];\n          pastFirstInvertThresh = false;\n          isCircumstantialInvert = !differentRowCol && options.invertSwap || differentLevel;\n        }\n        direction = _getSwapDirection(evt, target, targetRect, vertical, differentRowCol ? 1 : options.swapThreshold, options.invertedSwapThreshold == null ? options.swapThreshold : options.invertedSwapThreshold, isCircumstantialInvert, lastTarget === target);\n        var sibling;\n        if (direction !== 0) {\n          // Check if target is beside dragEl in respective direction (ignoring hidden elements)\n          var dragIndex = index(dragEl);\n          do {\n            dragIndex -= direction;\n            sibling = parentEl.children[dragIndex];\n          } while (sibling && (css(sibling, 'display') === 'none' || sibling === ghostEl));\n        }\n        // If dragEl is already beside target: Do not insert\n        if (direction === 0 || sibling === target) {\n          return completed(false);\n        }\n        lastTarget = target;\n        lastDirection = direction;\n        var nextSibling = target.nextElementSibling,\n          after = false;\n        after = direction === 1;\n        var moveVector = _onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, after);\n        if (moveVector !== false) {\n          if (moveVector === 1 || moveVector === -1) {\n            after = moveVector === 1;\n          }\n          _silent = true;\n          setTimeout(_unsilent, 30);\n          capture();\n          if (after && !nextSibling) {\n            el.appendChild(dragEl);\n          } else {\n            target.parentNode.insertBefore(dragEl, after ? nextSibling : target);\n          }\n\n          // Undo chrome's scroll adjustment (has no effect on other browsers)\n          if (scrolledPastTop) {\n            scrollBy(scrolledPastTop, 0, scrollBefore - scrolledPastTop.scrollTop);\n          }\n          parentEl = dragEl.parentNode; // actualization\n\n          // must be done before animation\n          if (targetBeforeFirstSwap !== undefined && !isCircumstantialInvert) {\n            targetMoveDistance = Math.abs(targetBeforeFirstSwap - getRect(target)[side1]);\n          }\n          changed();\n          return completed(true);\n        }\n      }\n      if (el.contains(dragEl)) {\n        return completed(false);\n      }\n    }\n    return false;\n  },\n  _ignoreWhileAnimating: null,\n  _offMoveEvents: function _offMoveEvents() {\n    off(document, 'mousemove', this._onTouchMove);\n    off(document, 'touchmove', this._onTouchMove);\n    off(document, 'pointermove', this._onTouchMove);\n    off(document, 'dragover', nearestEmptyInsertDetectEvent);\n    off(document, 'mousemove', nearestEmptyInsertDetectEvent);\n    off(document, 'touchmove', nearestEmptyInsertDetectEvent);\n  },\n  _offUpEvents: function _offUpEvents() {\n    var ownerDocument = this.el.ownerDocument;\n    off(ownerDocument, 'mouseup', this._onDrop);\n    off(ownerDocument, 'touchend', this._onDrop);\n    off(ownerDocument, 'pointerup', this._onDrop);\n    off(ownerDocument, 'touchcancel', this._onDrop);\n    off(document, 'selectstart', this);\n  },\n  _onDrop: function _onDrop( /**Event*/evt) {\n    var el = this.el,\n      options = this.options;\n\n    // Get the index of the dragged element within its parent\n    newIndex = index(dragEl);\n    newDraggableIndex = index(dragEl, options.draggable);\n    pluginEvent('drop', this, {\n      evt: evt\n    });\n    parentEl = dragEl && dragEl.parentNode;\n\n    // Get again after plugin event\n    newIndex = index(dragEl);\n    newDraggableIndex = index(dragEl, options.draggable);\n    if (Sortable.eventCanceled) {\n      this._nulling();\n      return;\n    }\n    awaitingDragStarted = false;\n    isCircumstantialInvert = false;\n    pastFirstInvertThresh = false;\n    clearInterval(this._loopId);\n    clearTimeout(this._dragStartTimer);\n    _cancelNextTick(this.cloneId);\n    _cancelNextTick(this._dragStartId);\n\n    // Unbind events\n    if (this.nativeDraggable) {\n      off(document, 'drop', this);\n      off(el, 'dragstart', this._onDragStart);\n    }\n    this._offMoveEvents();\n    this._offUpEvents();\n    if (Safari) {\n      css(document.body, 'user-select', '');\n    }\n    css(dragEl, 'transform', '');\n    if (evt) {\n      if (moved) {\n        evt.cancelable && evt.preventDefault();\n        !options.dropBubble && evt.stopPropagation();\n      }\n      ghostEl && ghostEl.parentNode && ghostEl.parentNode.removeChild(ghostEl);\n      if (rootEl === parentEl || putSortable && putSortable.lastPutMode !== 'clone') {\n        // Remove clone(s)\n        cloneEl && cloneEl.parentNode && cloneEl.parentNode.removeChild(cloneEl);\n      }\n      if (dragEl) {\n        if (this.nativeDraggable) {\n          off(dragEl, 'dragend', this);\n        }\n        _disableDraggable(dragEl);\n        dragEl.style['will-change'] = '';\n\n        // Remove classes\n        // ghostClass is added in dragStarted\n        if (moved && !awaitingDragStarted) {\n          toggleClass(dragEl, putSortable ? putSortable.options.ghostClass : this.options.ghostClass, false);\n        }\n        toggleClass(dragEl, this.options.chosenClass, false);\n\n        // Drag stop event\n        _dispatchEvent({\n          sortable: this,\n          name: 'unchoose',\n          toEl: parentEl,\n          newIndex: null,\n          newDraggableIndex: null,\n          originalEvent: evt\n        });\n        if (rootEl !== parentEl) {\n          if (newIndex >= 0) {\n            // Add event\n            _dispatchEvent({\n              rootEl: parentEl,\n              name: 'add',\n              toEl: parentEl,\n              fromEl: rootEl,\n              originalEvent: evt\n            });\n\n            // Remove event\n            _dispatchEvent({\n              sortable: this,\n              name: 'remove',\n              toEl: parentEl,\n              originalEvent: evt\n            });\n\n            // drag from one list and drop into another\n            _dispatchEvent({\n              rootEl: parentEl,\n              name: 'sort',\n              toEl: parentEl,\n              fromEl: rootEl,\n              originalEvent: evt\n            });\n            _dispatchEvent({\n              sortable: this,\n              name: 'sort',\n              toEl: parentEl,\n              originalEvent: evt\n            });\n          }\n          putSortable && putSortable.save();\n        } else {\n          if (newIndex !== oldIndex) {\n            if (newIndex >= 0) {\n              // drag & drop within the same list\n              _dispatchEvent({\n                sortable: this,\n                name: 'update',\n                toEl: parentEl,\n                originalEvent: evt\n              });\n              _dispatchEvent({\n                sortable: this,\n                name: 'sort',\n                toEl: parentEl,\n                originalEvent: evt\n              });\n            }\n          }\n        }\n        if (Sortable.active) {\n          /* jshint eqnull:true */\n          if (newIndex == null || newIndex === -1) {\n            newIndex = oldIndex;\n            newDraggableIndex = oldDraggableIndex;\n          }\n          _dispatchEvent({\n            sortable: this,\n            name: 'end',\n            toEl: parentEl,\n            originalEvent: evt\n          });\n\n          // Save sorting\n          this.save();\n        }\n      }\n    }\n    this._nulling();\n  },\n  _nulling: function _nulling() {\n    pluginEvent('nulling', this);\n    rootEl = dragEl = parentEl = ghostEl = nextEl = cloneEl = lastDownEl = cloneHidden = tapEvt = touchEvt = moved = newIndex = newDraggableIndex = oldIndex = oldDraggableIndex = lastTarget = lastDirection = putSortable = activeGroup = Sortable.dragged = Sortable.ghost = Sortable.clone = Sortable.active = null;\n    savedInputChecked.forEach(function (el) {\n      el.checked = true;\n    });\n    savedInputChecked.length = lastDx = lastDy = 0;\n  },\n  handleEvent: function handleEvent( /**Event*/evt) {\n    switch (evt.type) {\n      case 'drop':\n      case 'dragend':\n        this._onDrop(evt);\n        break;\n      case 'dragenter':\n      case 'dragover':\n        if (dragEl) {\n          this._onDragOver(evt);\n          _globalDragOver(evt);\n        }\n        break;\n      case 'selectstart':\n        evt.preventDefault();\n        break;\n    }\n  },\n  /**\r\n   * Serializes the item into an array of string.\r\n   * @returns {String[]}\r\n   */\n  toArray: function toArray() {\n    var order = [],\n      el,\n      children = this.el.children,\n      i = 0,\n      n = children.length,\n      options = this.options;\n    for (; i < n; i++) {\n      el = children[i];\n      if (closest(el, options.draggable, this.el, false)) {\n        order.push(el.getAttribute(options.dataIdAttr) || _generateId(el));\n      }\n    }\n    return order;\n  },\n  /**\r\n   * Sorts the elements according to the array.\r\n   * @param  {String[]}  order  order of the items\r\n   */\n  sort: function sort(order, useAnimation) {\n    var items = {},\n      rootEl = this.el;\n    this.toArray().forEach(function (id, i) {\n      var el = rootEl.children[i];\n      if (closest(el, this.options.draggable, rootEl, false)) {\n        items[id] = el;\n      }\n    }, this);\n    useAnimation && this.captureAnimationState();\n    order.forEach(function (id) {\n      if (items[id]) {\n        rootEl.removeChild(items[id]);\n        rootEl.appendChild(items[id]);\n      }\n    });\n    useAnimation && this.animateAll();\n  },\n  /**\r\n   * Save the current sorting\r\n   */\n  save: function save() {\n    var store = this.options.store;\n    store && store.set && store.set(this);\n  },\n  /**\r\n   * For each element in the set, get the first element that matches the selector by testing the element itself and traversing up through its ancestors in the DOM tree.\r\n   * @param   {HTMLElement}  el\r\n   * @param   {String}       [selector]  default: `options.draggable`\r\n   * @returns {HTMLElement|null}\r\n   */\n  closest: function closest$1(el, selector) {\n    return closest(el, selector || this.options.draggable, this.el, false);\n  },\n  /**\r\n   * Set/get option\r\n   * @param   {string} name\r\n   * @param   {*}      [value]\r\n   * @returns {*}\r\n   */\n  option: function option(name, value) {\n    var options = this.options;\n    if (value === void 0) {\n      return options[name];\n    } else {\n      var modifiedValue = PluginManager.modifyOption(this, name, value);\n      if (typeof modifiedValue !== 'undefined') {\n        options[name] = modifiedValue;\n      } else {\n        options[name] = value;\n      }\n      if (name === 'group') {\n        _prepareGroup(options);\n      }\n    }\n  },\n  /**\r\n   * Destroy\r\n   */\n  destroy: function destroy() {\n    pluginEvent('destroy', this);\n    var el = this.el;\n    el[expando] = null;\n    off(el, 'mousedown', this._onTapStart);\n    off(el, 'touchstart', this._onTapStart);\n    off(el, 'pointerdown', this._onTapStart);\n    if (this.nativeDraggable) {\n      off(el, 'dragover', this);\n      off(el, 'dragenter', this);\n    }\n    // Remove draggable attributes\n    Array.prototype.forEach.call(el.querySelectorAll('[draggable]'), function (el) {\n      el.removeAttribute('draggable');\n    });\n    this._onDrop();\n    this._disableDelayedDragEvents();\n    sortables.splice(sortables.indexOf(this.el), 1);\n    this.el = el = null;\n  },\n  _hideClone: function _hideClone() {\n    if (!cloneHidden) {\n      pluginEvent('hideClone', this);\n      if (Sortable.eventCanceled) return;\n      css(cloneEl, 'display', 'none');\n      if (this.options.removeCloneOnHide && cloneEl.parentNode) {\n        cloneEl.parentNode.removeChild(cloneEl);\n      }\n      cloneHidden = true;\n    }\n  },\n  _showClone: function _showClone(putSortable) {\n    if (putSortable.lastPutMode !== 'clone') {\n      this._hideClone();\n      return;\n    }\n    if (cloneHidden) {\n      pluginEvent('showClone', this);\n      if (Sortable.eventCanceled) return;\n\n      // show clone at dragEl or original position\n      if (dragEl.parentNode == rootEl && !this.options.group.revertClone) {\n        rootEl.insertBefore(cloneEl, dragEl);\n      } else if (nextEl) {\n        rootEl.insertBefore(cloneEl, nextEl);\n      } else {\n        rootEl.appendChild(cloneEl);\n      }\n      if (this.options.group.revertClone) {\n        this.animate(dragEl, cloneEl);\n      }\n      css(cloneEl, 'display', '');\n      cloneHidden = false;\n    }\n  }\n};\nfunction _globalDragOver( /**Event*/evt) {\n  if (evt.dataTransfer) {\n    evt.dataTransfer.dropEffect = 'move';\n  }\n  evt.cancelable && evt.preventDefault();\n}\nfunction _onMove(fromEl, toEl, dragEl, dragRect, targetEl, targetRect, originalEvent, willInsertAfter) {\n  var evt,\n    sortable = fromEl[expando],\n    onMoveFn = sortable.options.onMove,\n    retVal;\n  // Support for new CustomEvent feature\n  if (window.CustomEvent && !IE11OrLess && !Edge) {\n    evt = new CustomEvent('move', {\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    evt = document.createEvent('Event');\n    evt.initEvent('move', true, true);\n  }\n  evt.to = toEl;\n  evt.from = fromEl;\n  evt.dragged = dragEl;\n  evt.draggedRect = dragRect;\n  evt.related = targetEl || toEl;\n  evt.relatedRect = targetRect || getRect(toEl);\n  evt.willInsertAfter = willInsertAfter;\n  evt.originalEvent = originalEvent;\n  fromEl.dispatchEvent(evt);\n  if (onMoveFn) {\n    retVal = onMoveFn.call(sortable, evt, originalEvent);\n  }\n  return retVal;\n}\nfunction _disableDraggable(el) {\n  el.draggable = false;\n}\nfunction _unsilent() {\n  _silent = false;\n}\nfunction _ghostIsFirst(evt, vertical, sortable) {\n  var firstElRect = getRect(getChild(sortable.el, 0, sortable.options, true));\n  var childContainingRect = getChildContainingRectFromElement(sortable.el, sortable.options, ghostEl);\n  var spacer = 10;\n  return vertical ? evt.clientX < childContainingRect.left - spacer || evt.clientY < firstElRect.top && evt.clientX < firstElRect.right : evt.clientY < childContainingRect.top - spacer || evt.clientY < firstElRect.bottom && evt.clientX < firstElRect.left;\n}\nfunction _ghostIsLast(evt, vertical, sortable) {\n  var lastElRect = getRect(lastChild(sortable.el, sortable.options.draggable));\n  var childContainingRect = getChildContainingRectFromElement(sortable.el, sortable.options, ghostEl);\n  var spacer = 10;\n  return vertical ? evt.clientX > childContainingRect.right + spacer || evt.clientY > lastElRect.bottom && evt.clientX > lastElRect.left : evt.clientY > childContainingRect.bottom + spacer || evt.clientX > lastElRect.right && evt.clientY > lastElRect.top;\n}\nfunction _getSwapDirection(evt, target, targetRect, vertical, swapThreshold, invertedSwapThreshold, invertSwap, isLastTarget) {\n  var mouseOnAxis = vertical ? evt.clientY : evt.clientX,\n    targetLength = vertical ? targetRect.height : targetRect.width,\n    targetS1 = vertical ? targetRect.top : targetRect.left,\n    targetS2 = vertical ? targetRect.bottom : targetRect.right,\n    invert = false;\n  if (!invertSwap) {\n    // Never invert or create dragEl shadow when target movemenet causes mouse to move past the end of regular swapThreshold\n    if (isLastTarget && targetMoveDistance < targetLength * swapThreshold) {\n      // multiplied only by swapThreshold because mouse will already be inside target by (1 - threshold) * targetLength / 2\n      // check if past first invert threshold on side opposite of lastDirection\n      if (!pastFirstInvertThresh && (lastDirection === 1 ? mouseOnAxis > targetS1 + targetLength * invertedSwapThreshold / 2 : mouseOnAxis < targetS2 - targetLength * invertedSwapThreshold / 2)) {\n        // past first invert threshold, do not restrict inverted threshold to dragEl shadow\n        pastFirstInvertThresh = true;\n      }\n      if (!pastFirstInvertThresh) {\n        // dragEl shadow (target move distance shadow)\n        if (lastDirection === 1 ? mouseOnAxis < targetS1 + targetMoveDistance // over dragEl shadow\n        : mouseOnAxis > targetS2 - targetMoveDistance) {\n          return -lastDirection;\n        }\n      } else {\n        invert = true;\n      }\n    } else {\n      // Regular\n      if (mouseOnAxis > targetS1 + targetLength * (1 - swapThreshold) / 2 && mouseOnAxis < targetS2 - targetLength * (1 - swapThreshold) / 2) {\n        return _getInsertDirection(target);\n      }\n    }\n  }\n  invert = invert || invertSwap;\n  if (invert) {\n    // Invert of regular\n    if (mouseOnAxis < targetS1 + targetLength * invertedSwapThreshold / 2 || mouseOnAxis > targetS2 - targetLength * invertedSwapThreshold / 2) {\n      return mouseOnAxis > targetS1 + targetLength / 2 ? 1 : -1;\n    }\n  }\n  return 0;\n}\n\n/**\r\n * Gets the direction dragEl must be swapped relative to target in order to make it\r\n * seem that dragEl has been \"inserted\" into that element's position\r\n * @param  {HTMLElement} target       The target whose position dragEl is being inserted at\r\n * @return {Number}                   Direction dragEl must be swapped\r\n */\nfunction _getInsertDirection(target) {\n  if (index(dragEl) < index(target)) {\n    return 1;\n  } else {\n    return -1;\n  }\n}\n\n/**\r\n * Generate id\r\n * @param   {HTMLElement} el\r\n * @returns {String}\r\n * @private\r\n */\nfunction _generateId(el) {\n  var str = el.tagName + el.className + el.src + el.href + el.textContent,\n    i = str.length,\n    sum = 0;\n  while (i--) {\n    sum += str.charCodeAt(i);\n  }\n  return sum.toString(36);\n}\nfunction _saveInputCheckedState(root) {\n  savedInputChecked.length = 0;\n  var inputs = root.getElementsByTagName('input');\n  var idx = inputs.length;\n  while (idx--) {\n    var el = inputs[idx];\n    el.checked && savedInputChecked.push(el);\n  }\n}\nfunction _nextTick(fn) {\n  return setTimeout(fn, 0);\n}\nfunction _cancelNextTick(id) {\n  return clearTimeout(id);\n}\n\n// Fixed #973:\nif (documentExists) {\n  on(document, 'touchmove', function (evt) {\n    if ((Sortable.active || awaitingDragStarted) && evt.cancelable) {\n      evt.preventDefault();\n    }\n  });\n}\n\n// Export utils\nSortable.utils = {\n  on: on,\n  off: off,\n  css: css,\n  find: find,\n  is: function is(el, selector) {\n    return !!closest(el, selector, el, false);\n  },\n  extend: extend,\n  throttle: throttle,\n  closest: closest,\n  toggleClass: toggleClass,\n  clone: clone,\n  index: index,\n  nextTick: _nextTick,\n  cancelNextTick: _cancelNextTick,\n  detectDirection: _detectDirection,\n  getChild: getChild,\n  expando: expando\n};\n\n/**\r\n * Get the Sortable instance of an element\r\n * @param  {HTMLElement} element The element\r\n * @return {Sortable|undefined}         The instance of Sortable\r\n */\nSortable.get = function (element) {\n  return element[expando];\n};\n\n/**\r\n * Mount a plugin to Sortable\r\n * @param  {...SortablePlugin|SortablePlugin[]} plugins       Plugins being mounted\r\n */\nSortable.mount = function () {\n  for (var _len = arguments.length, plugins = new Array(_len), _key = 0; _key < _len; _key++) {\n    plugins[_key] = arguments[_key];\n  }\n  if (plugins[0].constructor === Array) plugins = plugins[0];\n  plugins.forEach(function (plugin) {\n    if (!plugin.prototype || !plugin.prototype.constructor) {\n      throw \"Sortable: Mounted plugin must be a constructor function, not \".concat({}.toString.call(plugin));\n    }\n    if (plugin.utils) Sortable.utils = _objectSpread2(_objectSpread2({}, Sortable.utils), plugin.utils);\n    PluginManager.mount(plugin);\n  });\n};\n\n/**\r\n * Create sortable instance\r\n * @param {HTMLElement}  el\r\n * @param {Object}      [options]\r\n */\nSortable.create = function (el, options) {\n  return new Sortable(el, options);\n};\n\n// Export\nSortable.version = version;\n\nvar autoScrolls = [],\n  scrollEl,\n  scrollRootEl,\n  scrolling = false,\n  lastAutoScrollX,\n  lastAutoScrollY,\n  touchEvt$1,\n  pointerElemChangedInterval;\nfunction AutoScrollPlugin() {\n  function AutoScroll() {\n    this.defaults = {\n      scroll: true,\n      forceAutoScrollFallback: false,\n      scrollSensitivity: 30,\n      scrollSpeed: 10,\n      bubbleScroll: true\n    };\n\n    // Bind all private methods\n    for (var fn in this) {\n      if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n        this[fn] = this[fn].bind(this);\n      }\n    }\n  }\n  AutoScroll.prototype = {\n    dragStarted: function dragStarted(_ref) {\n      var originalEvent = _ref.originalEvent;\n      if (this.sortable.nativeDraggable) {\n        on(document, 'dragover', this._handleAutoScroll);\n      } else {\n        if (this.options.supportPointer) {\n          on(document, 'pointermove', this._handleFallbackAutoScroll);\n        } else if (originalEvent.touches) {\n          on(document, 'touchmove', this._handleFallbackAutoScroll);\n        } else {\n          on(document, 'mousemove', this._handleFallbackAutoScroll);\n        }\n      }\n    },\n    dragOverCompleted: function dragOverCompleted(_ref2) {\n      var originalEvent = _ref2.originalEvent;\n      // For when bubbling is canceled and using fallback (fallback 'touchmove' always reached)\n      if (!this.options.dragOverBubble && !originalEvent.rootEl) {\n        this._handleAutoScroll(originalEvent);\n      }\n    },\n    drop: function drop() {\n      if (this.sortable.nativeDraggable) {\n        off(document, 'dragover', this._handleAutoScroll);\n      } else {\n        off(document, 'pointermove', this._handleFallbackAutoScroll);\n        off(document, 'touchmove', this._handleFallbackAutoScroll);\n        off(document, 'mousemove', this._handleFallbackAutoScroll);\n      }\n      clearPointerElemChangedInterval();\n      clearAutoScrolls();\n      cancelThrottle();\n    },\n    nulling: function nulling() {\n      touchEvt$1 = scrollRootEl = scrollEl = scrolling = pointerElemChangedInterval = lastAutoScrollX = lastAutoScrollY = null;\n      autoScrolls.length = 0;\n    },\n    _handleFallbackAutoScroll: function _handleFallbackAutoScroll(evt) {\n      this._handleAutoScroll(evt, true);\n    },\n    _handleAutoScroll: function _handleAutoScroll(evt, fallback) {\n      var _this = this;\n      var x = (evt.touches ? evt.touches[0] : evt).clientX,\n        y = (evt.touches ? evt.touches[0] : evt).clientY,\n        elem = document.elementFromPoint(x, y);\n      touchEvt$1 = evt;\n\n      // IE does not seem to have native autoscroll,\n      // Edge's autoscroll seems too conditional,\n      // MACOS Safari does not have autoscroll,\n      // Firefox and Chrome are good\n      if (fallback || this.options.forceAutoScrollFallback || Edge || IE11OrLess || Safari) {\n        autoScroll(evt, this.options, elem, fallback);\n\n        // Listener for pointer element change\n        var ogElemScroller = getParentAutoScrollElement(elem, true);\n        if (scrolling && (!pointerElemChangedInterval || x !== lastAutoScrollX || y !== lastAutoScrollY)) {\n          pointerElemChangedInterval && clearPointerElemChangedInterval();\n          // Detect for pointer elem change, emulating native DnD behaviour\n          pointerElemChangedInterval = setInterval(function () {\n            var newElem = getParentAutoScrollElement(document.elementFromPoint(x, y), true);\n            if (newElem !== ogElemScroller) {\n              ogElemScroller = newElem;\n              clearAutoScrolls();\n            }\n            autoScroll(evt, _this.options, newElem, fallback);\n          }, 10);\n          lastAutoScrollX = x;\n          lastAutoScrollY = y;\n        }\n      } else {\n        // if DnD is enabled (and browser has good autoscrolling), first autoscroll will already scroll, so get parent autoscroll of first autoscroll\n        if (!this.options.bubbleScroll || getParentAutoScrollElement(elem, true) === getWindowScrollingElement()) {\n          clearAutoScrolls();\n          return;\n        }\n        autoScroll(evt, this.options, getParentAutoScrollElement(elem, false), false);\n      }\n    }\n  };\n  return _extends(AutoScroll, {\n    pluginName: 'scroll',\n    initializeByDefault: true\n  });\n}\nfunction clearAutoScrolls() {\n  autoScrolls.forEach(function (autoScroll) {\n    clearInterval(autoScroll.pid);\n  });\n  autoScrolls = [];\n}\nfunction clearPointerElemChangedInterval() {\n  clearInterval(pointerElemChangedInterval);\n}\nvar autoScroll = throttle(function (evt, options, rootEl, isFallback) {\n  // Bug: https://bugzilla.mozilla.org/show_bug.cgi?id=505521\n  if (!options.scroll) return;\n  var x = (evt.touches ? evt.touches[0] : evt).clientX,\n    y = (evt.touches ? evt.touches[0] : evt).clientY,\n    sens = options.scrollSensitivity,\n    speed = options.scrollSpeed,\n    winScroller = getWindowScrollingElement();\n  var scrollThisInstance = false,\n    scrollCustomFn;\n\n  // New scroll root, set scrollEl\n  if (scrollRootEl !== rootEl) {\n    scrollRootEl = rootEl;\n    clearAutoScrolls();\n    scrollEl = options.scroll;\n    scrollCustomFn = options.scrollFn;\n    if (scrollEl === true) {\n      scrollEl = getParentAutoScrollElement(rootEl, true);\n    }\n  }\n  var layersOut = 0;\n  var currentParent = scrollEl;\n  do {\n    var el = currentParent,\n      rect = getRect(el),\n      top = rect.top,\n      bottom = rect.bottom,\n      left = rect.left,\n      right = rect.right,\n      width = rect.width,\n      height = rect.height,\n      canScrollX = void 0,\n      canScrollY = void 0,\n      scrollWidth = el.scrollWidth,\n      scrollHeight = el.scrollHeight,\n      elCSS = css(el),\n      scrollPosX = el.scrollLeft,\n      scrollPosY = el.scrollTop;\n    if (el === winScroller) {\n      canScrollX = width < scrollWidth && (elCSS.overflowX === 'auto' || elCSS.overflowX === 'scroll' || elCSS.overflowX === 'visible');\n      canScrollY = height < scrollHeight && (elCSS.overflowY === 'auto' || elCSS.overflowY === 'scroll' || elCSS.overflowY === 'visible');\n    } else {\n      canScrollX = width < scrollWidth && (elCSS.overflowX === 'auto' || elCSS.overflowX === 'scroll');\n      canScrollY = height < scrollHeight && (elCSS.overflowY === 'auto' || elCSS.overflowY === 'scroll');\n    }\n    var vx = canScrollX && (Math.abs(right - x) <= sens && scrollPosX + width < scrollWidth) - (Math.abs(left - x) <= sens && !!scrollPosX);\n    var vy = canScrollY && (Math.abs(bottom - y) <= sens && scrollPosY + height < scrollHeight) - (Math.abs(top - y) <= sens && !!scrollPosY);\n    if (!autoScrolls[layersOut]) {\n      for (var i = 0; i <= layersOut; i++) {\n        if (!autoScrolls[i]) {\n          autoScrolls[i] = {};\n        }\n      }\n    }\n    if (autoScrolls[layersOut].vx != vx || autoScrolls[layersOut].vy != vy || autoScrolls[layersOut].el !== el) {\n      autoScrolls[layersOut].el = el;\n      autoScrolls[layersOut].vx = vx;\n      autoScrolls[layersOut].vy = vy;\n      clearInterval(autoScrolls[layersOut].pid);\n      if (vx != 0 || vy != 0) {\n        scrollThisInstance = true;\n        /* jshint loopfunc:true */\n        autoScrolls[layersOut].pid = setInterval(function () {\n          // emulate drag over during autoscroll (fallback), emulating native DnD behaviour\n          if (isFallback && this.layer === 0) {\n            Sortable.active._onTouchMove(touchEvt$1); // To move ghost if it is positioned absolutely\n          }\n          var scrollOffsetY = autoScrolls[this.layer].vy ? autoScrolls[this.layer].vy * speed : 0;\n          var scrollOffsetX = autoScrolls[this.layer].vx ? autoScrolls[this.layer].vx * speed : 0;\n          if (typeof scrollCustomFn === 'function') {\n            if (scrollCustomFn.call(Sortable.dragged.parentNode[expando], scrollOffsetX, scrollOffsetY, evt, touchEvt$1, autoScrolls[this.layer].el) !== 'continue') {\n              return;\n            }\n          }\n          scrollBy(autoScrolls[this.layer].el, scrollOffsetX, scrollOffsetY);\n        }.bind({\n          layer: layersOut\n        }), 24);\n      }\n    }\n    layersOut++;\n  } while (options.bubbleScroll && currentParent !== winScroller && (currentParent = getParentAutoScrollElement(currentParent, false)));\n  scrolling = scrollThisInstance; // in case another function catches scrolling as false in between when it is not\n}, 30);\n\nvar drop = function drop(_ref) {\n  var originalEvent = _ref.originalEvent,\n    putSortable = _ref.putSortable,\n    dragEl = _ref.dragEl,\n    activeSortable = _ref.activeSortable,\n    dispatchSortableEvent = _ref.dispatchSortableEvent,\n    hideGhostForTarget = _ref.hideGhostForTarget,\n    unhideGhostForTarget = _ref.unhideGhostForTarget;\n  if (!originalEvent) return;\n  var toSortable = putSortable || activeSortable;\n  hideGhostForTarget();\n  var touch = originalEvent.changedTouches && originalEvent.changedTouches.length ? originalEvent.changedTouches[0] : originalEvent;\n  var target = document.elementFromPoint(touch.clientX, touch.clientY);\n  unhideGhostForTarget();\n  if (toSortable && !toSortable.el.contains(target)) {\n    dispatchSortableEvent('spill');\n    this.onSpill({\n      dragEl: dragEl,\n      putSortable: putSortable\n    });\n  }\n};\nfunction Revert() {}\nRevert.prototype = {\n  startIndex: null,\n  dragStart: function dragStart(_ref2) {\n    var oldDraggableIndex = _ref2.oldDraggableIndex;\n    this.startIndex = oldDraggableIndex;\n  },\n  onSpill: function onSpill(_ref3) {\n    var dragEl = _ref3.dragEl,\n      putSortable = _ref3.putSortable;\n    this.sortable.captureAnimationState();\n    if (putSortable) {\n      putSortable.captureAnimationState();\n    }\n    var nextSibling = getChild(this.sortable.el, this.startIndex, this.options);\n    if (nextSibling) {\n      this.sortable.el.insertBefore(dragEl, nextSibling);\n    } else {\n      this.sortable.el.appendChild(dragEl);\n    }\n    this.sortable.animateAll();\n    if (putSortable) {\n      putSortable.animateAll();\n    }\n  },\n  drop: drop\n};\n_extends(Revert, {\n  pluginName: 'revertOnSpill'\n});\nfunction Remove() {}\nRemove.prototype = {\n  onSpill: function onSpill(_ref4) {\n    var dragEl = _ref4.dragEl,\n      putSortable = _ref4.putSortable;\n    var parentSortable = putSortable || this.sortable;\n    parentSortable.captureAnimationState();\n    dragEl.parentNode && dragEl.parentNode.removeChild(dragEl);\n    parentSortable.animateAll();\n  },\n  drop: drop\n};\n_extends(Remove, {\n  pluginName: 'removeOnSpill'\n});\n\nvar lastSwapEl;\nfunction SwapPlugin() {\n  function Swap() {\n    this.defaults = {\n      swapClass: 'sortable-swap-highlight'\n    };\n  }\n  Swap.prototype = {\n    dragStart: function dragStart(_ref) {\n      var dragEl = _ref.dragEl;\n      lastSwapEl = dragEl;\n    },\n    dragOverValid: function dragOverValid(_ref2) {\n      var completed = _ref2.completed,\n        target = _ref2.target,\n        onMove = _ref2.onMove,\n        activeSortable = _ref2.activeSortable,\n        changed = _ref2.changed,\n        cancel = _ref2.cancel;\n      if (!activeSortable.options.swap) return;\n      var el = this.sortable.el,\n        options = this.options;\n      if (target && target !== el) {\n        var prevSwapEl = lastSwapEl;\n        if (onMove(target) !== false) {\n          toggleClass(target, options.swapClass, true);\n          lastSwapEl = target;\n        } else {\n          lastSwapEl = null;\n        }\n        if (prevSwapEl && prevSwapEl !== lastSwapEl) {\n          toggleClass(prevSwapEl, options.swapClass, false);\n        }\n      }\n      changed();\n      completed(true);\n      cancel();\n    },\n    drop: function drop(_ref3) {\n      var activeSortable = _ref3.activeSortable,\n        putSortable = _ref3.putSortable,\n        dragEl = _ref3.dragEl;\n      var toSortable = putSortable || this.sortable;\n      var options = this.options;\n      lastSwapEl && toggleClass(lastSwapEl, options.swapClass, false);\n      if (lastSwapEl && (options.swap || putSortable && putSortable.options.swap)) {\n        if (dragEl !== lastSwapEl) {\n          toSortable.captureAnimationState();\n          if (toSortable !== activeSortable) activeSortable.captureAnimationState();\n          swapNodes(dragEl, lastSwapEl);\n          toSortable.animateAll();\n          if (toSortable !== activeSortable) activeSortable.animateAll();\n        }\n      }\n    },\n    nulling: function nulling() {\n      lastSwapEl = null;\n    }\n  };\n  return _extends(Swap, {\n    pluginName: 'swap',\n    eventProperties: function eventProperties() {\n      return {\n        swapItem: lastSwapEl\n      };\n    }\n  });\n}\nfunction swapNodes(n1, n2) {\n  var p1 = n1.parentNode,\n    p2 = n2.parentNode,\n    i1,\n    i2;\n  if (!p1 || !p2 || p1.isEqualNode(n2) || p2.isEqualNode(n1)) return;\n  i1 = index(n1);\n  i2 = index(n2);\n  if (p1.isEqualNode(p2) && i1 < i2) {\n    i2++;\n  }\n  p1.insertBefore(n2, p1.children[i1]);\n  p2.insertBefore(n1, p2.children[i2]);\n}\n\nvar multiDragElements = [],\n  multiDragClones = [],\n  lastMultiDragSelect,\n  // for selection with modifier key down (SHIFT)\n  multiDragSortable,\n  initialFolding = false,\n  // Initial multi-drag fold when drag started\n  folding = false,\n  // Folding any other time\n  dragStarted = false,\n  dragEl$1,\n  clonesFromRect,\n  clonesHidden;\nfunction MultiDragPlugin() {\n  function MultiDrag(sortable) {\n    // Bind all private methods\n    for (var fn in this) {\n      if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n        this[fn] = this[fn].bind(this);\n      }\n    }\n    if (!sortable.options.avoidImplicitDeselect) {\n      if (sortable.options.supportPointer) {\n        on(document, 'pointerup', this._deselectMultiDrag);\n      } else {\n        on(document, 'mouseup', this._deselectMultiDrag);\n        on(document, 'touchend', this._deselectMultiDrag);\n      }\n    }\n    on(document, 'keydown', this._checkKeyDown);\n    on(document, 'keyup', this._checkKeyUp);\n    this.defaults = {\n      selectedClass: 'sortable-selected',\n      multiDragKey: null,\n      avoidImplicitDeselect: false,\n      setData: function setData(dataTransfer, dragEl) {\n        var data = '';\n        if (multiDragElements.length && multiDragSortable === sortable) {\n          multiDragElements.forEach(function (multiDragElement, i) {\n            data += (!i ? '' : ', ') + multiDragElement.textContent;\n          });\n        } else {\n          data = dragEl.textContent;\n        }\n        dataTransfer.setData('Text', data);\n      }\n    };\n  }\n  MultiDrag.prototype = {\n    multiDragKeyDown: false,\n    isMultiDrag: false,\n    delayStartGlobal: function delayStartGlobal(_ref) {\n      var dragged = _ref.dragEl;\n      dragEl$1 = dragged;\n    },\n    delayEnded: function delayEnded() {\n      this.isMultiDrag = ~multiDragElements.indexOf(dragEl$1);\n    },\n    setupClone: function setupClone(_ref2) {\n      var sortable = _ref2.sortable,\n        cancel = _ref2.cancel;\n      if (!this.isMultiDrag) return;\n      for (var i = 0; i < multiDragElements.length; i++) {\n        multiDragClones.push(clone(multiDragElements[i]));\n        multiDragClones[i].sortableIndex = multiDragElements[i].sortableIndex;\n        multiDragClones[i].draggable = false;\n        multiDragClones[i].style['will-change'] = '';\n        toggleClass(multiDragClones[i], this.options.selectedClass, false);\n        multiDragElements[i] === dragEl$1 && toggleClass(multiDragClones[i], this.options.chosenClass, false);\n      }\n      sortable._hideClone();\n      cancel();\n    },\n    clone: function clone(_ref3) {\n      var sortable = _ref3.sortable,\n        rootEl = _ref3.rootEl,\n        dispatchSortableEvent = _ref3.dispatchSortableEvent,\n        cancel = _ref3.cancel;\n      if (!this.isMultiDrag) return;\n      if (!this.options.removeCloneOnHide) {\n        if (multiDragElements.length && multiDragSortable === sortable) {\n          insertMultiDragClones(true, rootEl);\n          dispatchSortableEvent('clone');\n          cancel();\n        }\n      }\n    },\n    showClone: function showClone(_ref4) {\n      var cloneNowShown = _ref4.cloneNowShown,\n        rootEl = _ref4.rootEl,\n        cancel = _ref4.cancel;\n      if (!this.isMultiDrag) return;\n      insertMultiDragClones(false, rootEl);\n      multiDragClones.forEach(function (clone) {\n        css(clone, 'display', '');\n      });\n      cloneNowShown();\n      clonesHidden = false;\n      cancel();\n    },\n    hideClone: function hideClone(_ref5) {\n      var _this = this;\n      var sortable = _ref5.sortable,\n        cloneNowHidden = _ref5.cloneNowHidden,\n        cancel = _ref5.cancel;\n      if (!this.isMultiDrag) return;\n      multiDragClones.forEach(function (clone) {\n        css(clone, 'display', 'none');\n        if (_this.options.removeCloneOnHide && clone.parentNode) {\n          clone.parentNode.removeChild(clone);\n        }\n      });\n      cloneNowHidden();\n      clonesHidden = true;\n      cancel();\n    },\n    dragStartGlobal: function dragStartGlobal(_ref6) {\n      var sortable = _ref6.sortable;\n      if (!this.isMultiDrag && multiDragSortable) {\n        multiDragSortable.multiDrag._deselectMultiDrag();\n      }\n      multiDragElements.forEach(function (multiDragElement) {\n        multiDragElement.sortableIndex = index(multiDragElement);\n      });\n\n      // Sort multi-drag elements\n      multiDragElements = multiDragElements.sort(function (a, b) {\n        return a.sortableIndex - b.sortableIndex;\n      });\n      dragStarted = true;\n    },\n    dragStarted: function dragStarted(_ref7) {\n      var _this2 = this;\n      var sortable = _ref7.sortable;\n      if (!this.isMultiDrag) return;\n      if (this.options.sort) {\n        // Capture rects,\n        // hide multi drag elements (by positioning them absolute),\n        // set multi drag elements rects to dragRect,\n        // show multi drag elements,\n        // animate to rects,\n        // unset rects & remove from DOM\n\n        sortable.captureAnimationState();\n        if (this.options.animation) {\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            css(multiDragElement, 'position', 'absolute');\n          });\n          var dragRect = getRect(dragEl$1, false, true, true);\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            setRect(multiDragElement, dragRect);\n          });\n          folding = true;\n          initialFolding = true;\n        }\n      }\n      sortable.animateAll(function () {\n        folding = false;\n        initialFolding = false;\n        if (_this2.options.animation) {\n          multiDragElements.forEach(function (multiDragElement) {\n            unsetRect(multiDragElement);\n          });\n        }\n\n        // Remove all auxiliary multidrag items from el, if sorting enabled\n        if (_this2.options.sort) {\n          removeMultiDragElements();\n        }\n      });\n    },\n    dragOver: function dragOver(_ref8) {\n      var target = _ref8.target,\n        completed = _ref8.completed,\n        cancel = _ref8.cancel;\n      if (folding && ~multiDragElements.indexOf(target)) {\n        completed(false);\n        cancel();\n      }\n    },\n    revert: function revert(_ref9) {\n      var fromSortable = _ref9.fromSortable,\n        rootEl = _ref9.rootEl,\n        sortable = _ref9.sortable,\n        dragRect = _ref9.dragRect;\n      if (multiDragElements.length > 1) {\n        // Setup unfold animation\n        multiDragElements.forEach(function (multiDragElement) {\n          sortable.addAnimationState({\n            target: multiDragElement,\n            rect: folding ? getRect(multiDragElement) : dragRect\n          });\n          unsetRect(multiDragElement);\n          multiDragElement.fromRect = dragRect;\n          fromSortable.removeAnimationState(multiDragElement);\n        });\n        folding = false;\n        insertMultiDragElements(!this.options.removeCloneOnHide, rootEl);\n      }\n    },\n    dragOverCompleted: function dragOverCompleted(_ref10) {\n      var sortable = _ref10.sortable,\n        isOwner = _ref10.isOwner,\n        insertion = _ref10.insertion,\n        activeSortable = _ref10.activeSortable,\n        parentEl = _ref10.parentEl,\n        putSortable = _ref10.putSortable;\n      var options = this.options;\n      if (insertion) {\n        // Clones must be hidden before folding animation to capture dragRectAbsolute properly\n        if (isOwner) {\n          activeSortable._hideClone();\n        }\n        initialFolding = false;\n        // If leaving sort:false root, or already folding - Fold to new location\n        if (options.animation && multiDragElements.length > 1 && (folding || !isOwner && !activeSortable.options.sort && !putSortable)) {\n          // Fold: Set all multi drag elements's rects to dragEl's rect when multi-drag elements are invisible\n          var dragRectAbsolute = getRect(dragEl$1, false, true, true);\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            setRect(multiDragElement, dragRectAbsolute);\n\n            // Move element(s) to end of parentEl so that it does not interfere with multi-drag clones insertion if they are inserted\n            // while folding, and so that we can capture them again because old sortable will no longer be fromSortable\n            parentEl.appendChild(multiDragElement);\n          });\n          folding = true;\n        }\n\n        // Clones must be shown (and check to remove multi drags) after folding when interfering multiDragElements are moved out\n        if (!isOwner) {\n          // Only remove if not folding (folding will remove them anyways)\n          if (!folding) {\n            removeMultiDragElements();\n          }\n          if (multiDragElements.length > 1) {\n            var clonesHiddenBefore = clonesHidden;\n            activeSortable._showClone(sortable);\n\n            // Unfold animation for clones if showing from hidden\n            if (activeSortable.options.animation && !clonesHidden && clonesHiddenBefore) {\n              multiDragClones.forEach(function (clone) {\n                activeSortable.addAnimationState({\n                  target: clone,\n                  rect: clonesFromRect\n                });\n                clone.fromRect = clonesFromRect;\n                clone.thisAnimationDuration = null;\n              });\n            }\n          } else {\n            activeSortable._showClone(sortable);\n          }\n        }\n      }\n    },\n    dragOverAnimationCapture: function dragOverAnimationCapture(_ref11) {\n      var dragRect = _ref11.dragRect,\n        isOwner = _ref11.isOwner,\n        activeSortable = _ref11.activeSortable;\n      multiDragElements.forEach(function (multiDragElement) {\n        multiDragElement.thisAnimationDuration = null;\n      });\n      if (activeSortable.options.animation && !isOwner && activeSortable.multiDrag.isMultiDrag) {\n        clonesFromRect = _extends({}, dragRect);\n        var dragMatrix = matrix(dragEl$1, true);\n        clonesFromRect.top -= dragMatrix.f;\n        clonesFromRect.left -= dragMatrix.e;\n      }\n    },\n    dragOverAnimationComplete: function dragOverAnimationComplete() {\n      if (folding) {\n        folding = false;\n        removeMultiDragElements();\n      }\n    },\n    drop: function drop(_ref12) {\n      var evt = _ref12.originalEvent,\n        rootEl = _ref12.rootEl,\n        parentEl = _ref12.parentEl,\n        sortable = _ref12.sortable,\n        dispatchSortableEvent = _ref12.dispatchSortableEvent,\n        oldIndex = _ref12.oldIndex,\n        putSortable = _ref12.putSortable;\n      var toSortable = putSortable || this.sortable;\n      if (!evt) return;\n      var options = this.options,\n        children = parentEl.children;\n\n      // Multi-drag selection\n      if (!dragStarted) {\n        if (options.multiDragKey && !this.multiDragKeyDown) {\n          this._deselectMultiDrag();\n        }\n        toggleClass(dragEl$1, options.selectedClass, !~multiDragElements.indexOf(dragEl$1));\n        if (!~multiDragElements.indexOf(dragEl$1)) {\n          multiDragElements.push(dragEl$1);\n          dispatchEvent({\n            sortable: sortable,\n            rootEl: rootEl,\n            name: 'select',\n            targetEl: dragEl$1,\n            originalEvent: evt\n          });\n\n          // Modifier activated, select from last to dragEl\n          if (evt.shiftKey && lastMultiDragSelect && sortable.el.contains(lastMultiDragSelect)) {\n            var lastIndex = index(lastMultiDragSelect),\n              currentIndex = index(dragEl$1);\n            if (~lastIndex && ~currentIndex && lastIndex !== currentIndex) {\n              // Must include lastMultiDragSelect (select it), in case modified selection from no selection\n              // (but previous selection existed)\n              var n, i;\n              if (currentIndex > lastIndex) {\n                i = lastIndex;\n                n = currentIndex;\n              } else {\n                i = currentIndex;\n                n = lastIndex + 1;\n              }\n              for (; i < n; i++) {\n                if (~multiDragElements.indexOf(children[i])) continue;\n                toggleClass(children[i], options.selectedClass, true);\n                multiDragElements.push(children[i]);\n                dispatchEvent({\n                  sortable: sortable,\n                  rootEl: rootEl,\n                  name: 'select',\n                  targetEl: children[i],\n                  originalEvent: evt\n                });\n              }\n            }\n          } else {\n            lastMultiDragSelect = dragEl$1;\n          }\n          multiDragSortable = toSortable;\n        } else {\n          multiDragElements.splice(multiDragElements.indexOf(dragEl$1), 1);\n          lastMultiDragSelect = null;\n          dispatchEvent({\n            sortable: sortable,\n            rootEl: rootEl,\n            name: 'deselect',\n            targetEl: dragEl$1,\n            originalEvent: evt\n          });\n        }\n      }\n\n      // Multi-drag drop\n      if (dragStarted && this.isMultiDrag) {\n        folding = false;\n        // Do not \"unfold\" after around dragEl if reverted\n        if ((parentEl[expando].options.sort || parentEl !== rootEl) && multiDragElements.length > 1) {\n          var dragRect = getRect(dragEl$1),\n            multiDragIndex = index(dragEl$1, ':not(.' + this.options.selectedClass + ')');\n          if (!initialFolding && options.animation) dragEl$1.thisAnimationDuration = null;\n          toSortable.captureAnimationState();\n          if (!initialFolding) {\n            if (options.animation) {\n              dragEl$1.fromRect = dragRect;\n              multiDragElements.forEach(function (multiDragElement) {\n                multiDragElement.thisAnimationDuration = null;\n                if (multiDragElement !== dragEl$1) {\n                  var rect = folding ? getRect(multiDragElement) : dragRect;\n                  multiDragElement.fromRect = rect;\n\n                  // Prepare unfold animation\n                  toSortable.addAnimationState({\n                    target: multiDragElement,\n                    rect: rect\n                  });\n                }\n              });\n            }\n\n            // Multi drag elements are not necessarily removed from the DOM on drop, so to reinsert\n            // properly they must all be removed\n            removeMultiDragElements();\n            multiDragElements.forEach(function (multiDragElement) {\n              if (children[multiDragIndex]) {\n                parentEl.insertBefore(multiDragElement, children[multiDragIndex]);\n              } else {\n                parentEl.appendChild(multiDragElement);\n              }\n              multiDragIndex++;\n            });\n\n            // If initial folding is done, the elements may have changed position because they are now\n            // unfolding around dragEl, even though dragEl may not have his index changed, so update event\n            // must be fired here as Sortable will not.\n            if (oldIndex === index(dragEl$1)) {\n              var update = false;\n              multiDragElements.forEach(function (multiDragElement) {\n                if (multiDragElement.sortableIndex !== index(multiDragElement)) {\n                  update = true;\n                  return;\n                }\n              });\n              if (update) {\n                dispatchSortableEvent('update');\n                dispatchSortableEvent('sort');\n              }\n            }\n          }\n\n          // Must be done after capturing individual rects (scroll bar)\n          multiDragElements.forEach(function (multiDragElement) {\n            unsetRect(multiDragElement);\n          });\n          toSortable.animateAll();\n        }\n        multiDragSortable = toSortable;\n      }\n\n      // Remove clones if necessary\n      if (rootEl === parentEl || putSortable && putSortable.lastPutMode !== 'clone') {\n        multiDragClones.forEach(function (clone) {\n          clone.parentNode && clone.parentNode.removeChild(clone);\n        });\n      }\n    },\n    nullingGlobal: function nullingGlobal() {\n      this.isMultiDrag = dragStarted = false;\n      multiDragClones.length = 0;\n    },\n    destroyGlobal: function destroyGlobal() {\n      this._deselectMultiDrag();\n      off(document, 'pointerup', this._deselectMultiDrag);\n      off(document, 'mouseup', this._deselectMultiDrag);\n      off(document, 'touchend', this._deselectMultiDrag);\n      off(document, 'keydown', this._checkKeyDown);\n      off(document, 'keyup', this._checkKeyUp);\n    },\n    _deselectMultiDrag: function _deselectMultiDrag(evt) {\n      if (typeof dragStarted !== \"undefined\" && dragStarted) return;\n\n      // Only deselect if selection is in this sortable\n      if (multiDragSortable !== this.sortable) return;\n\n      // Only deselect if target is not item in this sortable\n      if (evt && closest(evt.target, this.options.draggable, this.sortable.el, false)) return;\n\n      // Only deselect if left click\n      if (evt && evt.button !== 0) return;\n      while (multiDragElements.length) {\n        var el = multiDragElements[0];\n        toggleClass(el, this.options.selectedClass, false);\n        multiDragElements.shift();\n        dispatchEvent({\n          sortable: this.sortable,\n          rootEl: this.sortable.el,\n          name: 'deselect',\n          targetEl: el,\n          originalEvent: evt\n        });\n      }\n    },\n    _checkKeyDown: function _checkKeyDown(evt) {\n      if (evt.key === this.options.multiDragKey) {\n        this.multiDragKeyDown = true;\n      }\n    },\n    _checkKeyUp: function _checkKeyUp(evt) {\n      if (evt.key === this.options.multiDragKey) {\n        this.multiDragKeyDown = false;\n      }\n    }\n  };\n  return _extends(MultiDrag, {\n    // Static methods & properties\n    pluginName: 'multiDrag',\n    utils: {\n      /**\r\n       * Selects the provided multi-drag item\r\n       * @param  {HTMLElement} el    The element to be selected\r\n       */\n      select: function select(el) {\n        var sortable = el.parentNode[expando];\n        if (!sortable || !sortable.options.multiDrag || ~multiDragElements.indexOf(el)) return;\n        if (multiDragSortable && multiDragSortable !== sortable) {\n          multiDragSortable.multiDrag._deselectMultiDrag();\n          multiDragSortable = sortable;\n        }\n        toggleClass(el, sortable.options.selectedClass, true);\n        multiDragElements.push(el);\n      },\n      /**\r\n       * Deselects the provided multi-drag item\r\n       * @param  {HTMLElement} el    The element to be deselected\r\n       */\n      deselect: function deselect(el) {\n        var sortable = el.parentNode[expando],\n          index = multiDragElements.indexOf(el);\n        if (!sortable || !sortable.options.multiDrag || !~index) return;\n        toggleClass(el, sortable.options.selectedClass, false);\n        multiDragElements.splice(index, 1);\n      }\n    },\n    eventProperties: function eventProperties() {\n      var _this3 = this;\n      var oldIndicies = [],\n        newIndicies = [];\n      multiDragElements.forEach(function (multiDragElement) {\n        oldIndicies.push({\n          multiDragElement: multiDragElement,\n          index: multiDragElement.sortableIndex\n        });\n\n        // multiDragElements will already be sorted if folding\n        var newIndex;\n        if (folding && multiDragElement !== dragEl$1) {\n          newIndex = -1;\n        } else if (folding) {\n          newIndex = index(multiDragElement, ':not(.' + _this3.options.selectedClass + ')');\n        } else {\n          newIndex = index(multiDragElement);\n        }\n        newIndicies.push({\n          multiDragElement: multiDragElement,\n          index: newIndex\n        });\n      });\n      return {\n        items: _toConsumableArray(multiDragElements),\n        clones: [].concat(multiDragClones),\n        oldIndicies: oldIndicies,\n        newIndicies: newIndicies\n      };\n    },\n    optionListeners: {\n      multiDragKey: function multiDragKey(key) {\n        key = key.toLowerCase();\n        if (key === 'ctrl') {\n          key = 'Control';\n        } else if (key.length > 1) {\n          key = key.charAt(0).toUpperCase() + key.substr(1);\n        }\n        return key;\n      }\n    }\n  });\n}\nfunction insertMultiDragElements(clonesInserted, rootEl) {\n  multiDragElements.forEach(function (multiDragElement, i) {\n    var target = rootEl.children[multiDragElement.sortableIndex + (clonesInserted ? Number(i) : 0)];\n    if (target) {\n      rootEl.insertBefore(multiDragElement, target);\n    } else {\n      rootEl.appendChild(multiDragElement);\n    }\n  });\n}\n\n/**\r\n * Insert multi-drag clones\r\n * @param  {[Boolean]} elementsInserted  Whether the multi-drag elements are inserted\r\n * @param  {HTMLElement} rootEl\r\n */\nfunction insertMultiDragClones(elementsInserted, rootEl) {\n  multiDragClones.forEach(function (clone, i) {\n    var target = rootEl.children[clone.sortableIndex + (elementsInserted ? Number(i) : 0)];\n    if (target) {\n      rootEl.insertBefore(clone, target);\n    } else {\n      rootEl.appendChild(clone);\n    }\n  });\n}\nfunction removeMultiDragElements() {\n  multiDragElements.forEach(function (multiDragElement) {\n    if (multiDragElement === dragEl$1) return;\n    multiDragElement.parentNode && multiDragElement.parentNode.removeChild(multiDragElement);\n  });\n}\n\nSortable.mount(new AutoScrollPlugin());\nSortable.mount(Remove, Revert);\n\nexport default Sortable;\nexport { MultiDragPlugin as MultiDrag, Sortable, SwapPlugin as Swap };\n"], "mappings": ";;;AAMA,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,QAAI,gBAAgB;AAClB,gBAAU,QAAQ,OAAO,SAAU,KAAK;AACtC,eAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,MACtD,CAAC;AAAA,IACH;AACA,SAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EAC/B;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AACpD,QAAI,IAAI,GAAG;AACT,cAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AACnD,wBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAC1C,CAAC;AAAA,IACH,WAAW,OAAO,2BAA2B;AAC3C,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAC1E,OAAO;AACL,cAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC7C,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MACjF,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,QAAQ,KAAK;AACpB;AAEA,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AACvE,cAAU,SAAUA,MAAK;AACvB,aAAO,OAAOA;AAAA,IAChB;AAAA,EACF,OAAO;AACL,cAAU,SAAUA,MAAK;AACvB,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAC3H;AAAA,EACF;AACA,SAAO,QAAQ,GAAG;AACpB;AACA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,WAAW;AAClB,aAAW,OAAO,UAAU,SAAU,QAAQ;AAC5C,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AACA,SAAS,8BAA8B,QAAQ,UAAU;AACvD,MAAI,UAAU,KAAM,QAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AACT,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AACA,SAAO;AACT;AACA,SAAS,yBAAyB,QAAQ,UAAU;AAClD,MAAI,UAAU,KAAM,QAAO,CAAC;AAC5B,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAC3D,MAAI,KAAK;AACT,MAAI,OAAO,uBAAuB;AAChC,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAC1D,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC5C,YAAM,iBAAiB,CAAC;AACxB,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAC9D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAmB;AACpH;AACA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO,kBAAkB,GAAG;AACtD;AACA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,WAAW,eAAe,KAAK,OAAO,QAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK,KAAM,QAAO,MAAM,KAAK,IAAI;AAC1H;AACA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC,EAAG;AACR,MAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AACjH;AACA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAC/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AACpE,SAAO;AACT;AACA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;AAEA,IAAI,UAAU;AAEd,SAAS,UAAU,SAAS;AAC1B,MAAI,OAAO,WAAW,eAAe,OAAO,WAAW;AACrD,WAAO,CAAC,CAAe,UAAU,UAAU,MAAM,OAAO;AAAA,EAC1D;AACF;AACA,IAAI,aAAa,UAAU,uDAAuD;AAClF,IAAI,OAAO,UAAU,OAAO;AAC5B,IAAI,UAAU,UAAU,UAAU;AAClC,IAAI,SAAS,UAAU,SAAS,KAAK,CAAC,UAAU,SAAS,KAAK,CAAC,UAAU,UAAU;AACnF,IAAI,MAAM,UAAU,iBAAiB;AACrC,IAAI,mBAAmB,UAAU,SAAS,KAAK,UAAU,UAAU;AAEnE,IAAI,cAAc;AAAA,EAChB,SAAS;AAAA,EACT,SAAS;AACX;AACA,SAAS,GAAG,IAAI,OAAO,IAAI;AACzB,KAAG,iBAAiB,OAAO,IAAI,CAAC,cAAc,WAAW;AAC3D;AACA,SAAS,IAAI,IAAI,OAAO,IAAI;AAC1B,KAAG,oBAAoB,OAAO,IAAI,CAAC,cAAc,WAAW;AAC9D;AACA,SAAS,QAAyB,IAAe,UAAU;AACzD,MAAI,CAAC,SAAU;AACf,WAAS,CAAC,MAAM,QAAQ,WAAW,SAAS,UAAU,CAAC;AACvD,MAAI,IAAI;AACN,QAAI;AACF,UAAI,GAAG,SAAS;AACd,eAAO,GAAG,QAAQ,QAAQ;AAAA,MAC5B,WAAW,GAAG,mBAAmB;AAC/B,eAAO,GAAG,kBAAkB,QAAQ;AAAA,MACtC,WAAW,GAAG,uBAAuB;AACnC,eAAO,GAAG,sBAAsB,QAAQ;AAAA,MAC1C;AAAA,IACF,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,IAAI;AAC3B,SAAO,GAAG,QAAQ,OAAO,YAAY,GAAG,KAAK,WAAW,GAAG,OAAO,GAAG;AACvE;AACA,SAAS,QAAyB,IAAe,UAA0B,KAAK,YAAY;AAC1F,MAAI,IAAI;AACN,UAAM,OAAO;AACb,OAAG;AACD,UAAI,YAAY,SAAS,SAAS,CAAC,MAAM,MAAM,GAAG,eAAe,OAAO,QAAQ,IAAI,QAAQ,IAAI,QAAQ,IAAI,QAAQ,MAAM,cAAc,OAAO,KAAK;AAClJ,eAAO;AAAA,MACT;AACA,UAAI,OAAO,IAAK;AAAA,IAElB,SAAS,KAAK,gBAAgB,EAAE;AAAA,EAClC;AACA,SAAO;AACT;AACA,IAAI,UAAU;AACd,SAAS,YAAY,IAAI,MAAM,OAAO;AACpC,MAAI,MAAM,MAAM;AACd,QAAI,GAAG,WAAW;AAChB,SAAG,UAAU,QAAQ,QAAQ,QAAQ,EAAE,IAAI;AAAA,IAC7C,OAAO;AACL,UAAI,aAAa,MAAM,GAAG,YAAY,KAAK,QAAQ,SAAS,GAAG,EAAE,QAAQ,MAAM,OAAO,KAAK,GAAG;AAC9F,SAAG,aAAa,aAAa,QAAQ,MAAM,OAAO,KAAK,QAAQ,SAAS,GAAG;AAAA,IAC7E;AAAA,EACF;AACF;AACA,SAAS,IAAI,IAAI,MAAM,KAAK;AAC1B,MAAI,QAAQ,MAAM,GAAG;AACrB,MAAI,OAAO;AACT,QAAI,QAAQ,QAAQ;AAClB,UAAI,SAAS,eAAe,SAAS,YAAY,kBAAkB;AACjE,cAAM,SAAS,YAAY,iBAAiB,IAAI,EAAE;AAAA,MACpD,WAAW,GAAG,cAAc;AAC1B,cAAM,GAAG;AAAA,MACX;AACA,aAAO,SAAS,SAAS,MAAM,IAAI,IAAI;AAAA,IACzC,OAAO;AACL,UAAI,EAAE,QAAQ,UAAU,KAAK,QAAQ,QAAQ,MAAM,IAAI;AACrD,eAAO,aAAa;AAAA,MACtB;AACA,YAAM,IAAI,IAAI,OAAO,OAAO,QAAQ,WAAW,KAAK;AAAA,IACtD;AAAA,EACF;AACF;AACA,SAAS,OAAO,IAAI,UAAU;AAC5B,MAAI,oBAAoB;AACxB,MAAI,OAAO,OAAO,UAAU;AAC1B,wBAAoB;AAAA,EACtB,OAAO;AACL,OAAG;AACD,UAAI,YAAY,IAAI,IAAI,WAAW;AACnC,UAAI,aAAa,cAAc,QAAQ;AACrC,4BAAoB,YAAY,MAAM;AAAA,MACxC;AAAA,IAEF,SAAS,CAAC,aAAa,KAAK,GAAG;AAAA,EACjC;AACA,MAAI,WAAW,OAAO,aAAa,OAAO,mBAAmB,OAAO,aAAa,OAAO;AAExF,SAAO,YAAY,IAAI,SAAS,iBAAiB;AACnD;AACA,SAAS,KAAK,KAAK,SAAS,UAAU;AACpC,MAAI,KAAK;AACP,QAAI,OAAO,IAAI,qBAAqB,OAAO,GACzC,IAAI,GACJ,IAAI,KAAK;AACX,QAAI,UAAU;AACZ,aAAO,IAAI,GAAG,KAAK;AACjB,iBAAS,KAAK,CAAC,GAAG,CAAC;AAAA,MACrB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,CAAC;AACV;AACA,SAAS,4BAA4B;AACnC,MAAI,mBAAmB,SAAS;AAChC,MAAI,kBAAkB;AACpB,WAAO;AAAA,EACT,OAAO;AACL,WAAO,SAAS;AAAA,EAClB;AACF;AAWA,SAAS,QAAQ,IAAI,2BAA2B,2BAA2B,WAAW,WAAW;AAC/F,MAAI,CAAC,GAAG,yBAAyB,OAAO,OAAQ;AAChD,MAAI,QAAQ,KAAK,MAAM,QAAQ,OAAO,QAAQ;AAC9C,MAAI,OAAO,UAAU,GAAG,cAAc,OAAO,0BAA0B,GAAG;AACxE,aAAS,GAAG,sBAAsB;AAClC,UAAM,OAAO;AACb,WAAO,OAAO;AACd,aAAS,OAAO;AAChB,YAAQ,OAAO;AACf,aAAS,OAAO;AAChB,YAAQ,OAAO;AAAA,EACjB,OAAO;AACL,UAAM;AACN,WAAO;AACP,aAAS,OAAO;AAChB,YAAQ,OAAO;AACf,aAAS,OAAO;AAChB,YAAQ,OAAO;AAAA,EACjB;AACA,OAAK,6BAA6B,8BAA8B,OAAO,QAAQ;AAE7E,gBAAY,aAAa,GAAG;AAI5B,QAAI,CAAC,YAAY;AACf,SAAG;AACD,YAAI,aAAa,UAAU,0BAA0B,IAAI,WAAW,WAAW,MAAM,UAAU,6BAA6B,IAAI,WAAW,UAAU,MAAM,WAAW;AACpK,cAAI,gBAAgB,UAAU,sBAAsB;AAGpD,iBAAO,cAAc,MAAM,SAAS,IAAI,WAAW,kBAAkB,CAAC;AACtE,kBAAQ,cAAc,OAAO,SAAS,IAAI,WAAW,mBAAmB,CAAC;AACzE,mBAAS,MAAM,OAAO;AACtB,kBAAQ,OAAO,OAAO;AACtB;AAAA,QACF;AAAA,MAEF,SAAS,YAAY,UAAU;AAAA,IACjC;AAAA,EACF;AACA,MAAI,aAAa,OAAO,QAAQ;AAE9B,QAAI,WAAW,OAAO,aAAa,EAAE,GACnC,SAAS,YAAY,SAAS,GAC9B,SAAS,YAAY,SAAS;AAChC,QAAI,UAAU;AACZ,aAAO;AACP,cAAQ;AACR,eAAS;AACT,gBAAU;AACV,eAAS,MAAM;AACf,cAAQ,OAAO;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AASA,SAAS,eAAe,IAAI,QAAQ,YAAY;AAC9C,MAAI,SAAS,2BAA2B,IAAI,IAAI,GAC9C,YAAY,QAAQ,EAAE,EAAE,MAAM;AAGhC,SAAO,QAAQ;AACb,QAAI,gBAAgB,QAAQ,MAAM,EAAE,UAAU,GAC5C,UAAU;AACZ,QAAI,eAAe,SAAS,eAAe,QAAQ;AACjD,gBAAU,aAAa;AAAA,IACzB,OAAO;AACL,gBAAU,aAAa;AAAA,IACzB;AACA,QAAI,CAAC,QAAS,QAAO;AACrB,QAAI,WAAW,0BAA0B,EAAG;AAC5C,aAAS,2BAA2B,QAAQ,KAAK;AAAA,EACnD;AACA,SAAO;AACT;AAUA,SAAS,SAAS,IAAI,UAAU,SAAS,eAAe;AACtD,MAAI,eAAe,GACjB,IAAI,GACJ,WAAW,GAAG;AAChB,SAAO,IAAI,SAAS,QAAQ;AAC1B,QAAI,SAAS,CAAC,EAAE,MAAM,YAAY,UAAU,SAAS,CAAC,MAAM,SAAS,UAAU,iBAAiB,SAAS,CAAC,MAAM,SAAS,YAAY,QAAQ,SAAS,CAAC,GAAG,QAAQ,WAAW,IAAI,KAAK,GAAG;AACvL,UAAI,iBAAiB,UAAU;AAC7B,eAAO,SAAS,CAAC;AAAA,MACnB;AACA;AAAA,IACF;AACA;AAAA,EACF;AACA,SAAO;AACT;AAQA,SAAS,UAAU,IAAI,UAAU;AAC/B,MAAI,OAAO,GAAG;AACd,SAAO,SAAS,SAAS,SAAS,SAAS,IAAI,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,QAAQ,MAAM,QAAQ,IAAI;AACnH,WAAO,KAAK;AAAA,EACd;AACA,SAAO,QAAQ;AACjB;AASA,SAAS,MAAM,IAAI,UAAU;AAC3B,MAAIC,SAAQ;AACZ,MAAI,CAAC,MAAM,CAAC,GAAG,YAAY;AACzB,WAAO;AAAA,EACT;AAGA,SAAO,KAAK,GAAG,wBAAwB;AACrC,QAAI,GAAG,SAAS,YAAY,MAAM,cAAc,OAAO,SAAS,UAAU,CAAC,YAAY,QAAQ,IAAI,QAAQ,IAAI;AAC7G,MAAAA;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;AAQA,SAAS,wBAAwB,IAAI;AACnC,MAAI,aAAa,GACf,YAAY,GACZ,cAAc,0BAA0B;AAC1C,MAAI,IAAI;AACN,OAAG;AACD,UAAI,WAAW,OAAO,EAAE,GACtB,SAAS,SAAS,GAClB,SAAS,SAAS;AACpB,oBAAc,GAAG,aAAa;AAC9B,mBAAa,GAAG,YAAY;AAAA,IAC9B,SAAS,OAAO,gBAAgB,KAAK,GAAG;AAAA,EAC1C;AACA,SAAO,CAAC,YAAY,SAAS;AAC/B;AAQA,SAAS,cAAc,KAAK,KAAK;AAC/B,WAAS,KAAK,KAAK;AACjB,QAAI,CAAC,IAAI,eAAe,CAAC,EAAG;AAC5B,aAAS,OAAO,KAAK;AACnB,UAAI,IAAI,eAAe,GAAG,KAAK,IAAI,GAAG,MAAM,IAAI,CAAC,EAAE,GAAG,EAAG,QAAO,OAAO,CAAC;AAAA,IAC1E;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,2BAA2B,IAAI,aAAa;AAEnD,MAAI,CAAC,MAAM,CAAC,GAAG,sBAAuB,QAAO,0BAA0B;AACvE,MAAI,OAAO;AACX,MAAI,UAAU;AACd,KAAG;AAED,QAAI,KAAK,cAAc,KAAK,eAAe,KAAK,eAAe,KAAK,cAAc;AAChF,UAAI,UAAU,IAAI,IAAI;AACtB,UAAI,KAAK,cAAc,KAAK,gBAAgB,QAAQ,aAAa,UAAU,QAAQ,aAAa,aAAa,KAAK,eAAe,KAAK,iBAAiB,QAAQ,aAAa,UAAU,QAAQ,aAAa,WAAW;AACpN,YAAI,CAAC,KAAK,yBAAyB,SAAS,SAAS,KAAM,QAAO,0BAA0B;AAC5F,YAAI,WAAW,YAAa,QAAO;AACnC,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EAEF,SAAS,OAAO,KAAK;AACrB,SAAO,0BAA0B;AACnC;AACA,SAAS,OAAO,KAAK,KAAK;AACxB,MAAI,OAAO,KAAK;AACd,aAAS,OAAO,KAAK;AACnB,UAAI,IAAI,eAAe,GAAG,GAAG;AAC3B,YAAI,GAAG,IAAI,IAAI,GAAG;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,YAAY,OAAO,OAAO;AACjC,SAAO,KAAK,MAAM,MAAM,GAAG,MAAM,KAAK,MAAM,MAAM,GAAG,KAAK,KAAK,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM,IAAI,KAAK,KAAK,MAAM,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM,KAAK,MAAM,MAAM,KAAK;AAC5N;AACA,IAAI;AACJ,SAAS,SAAS,UAAU,IAAI;AAC9B,SAAO,WAAY;AACjB,QAAI,CAAC,kBAAkB;AACrB,UAAI,OAAO,WACT,QAAQ;AACV,UAAI,KAAK,WAAW,GAAG;AACrB,iBAAS,KAAK,OAAO,KAAK,CAAC,CAAC;AAAA,MAC9B,OAAO;AACL,iBAAS,MAAM,OAAO,IAAI;AAAA,MAC5B;AACA,yBAAmB,WAAW,WAAY;AACxC,2BAAmB;AAAA,MACrB,GAAG,EAAE;AAAA,IACP;AAAA,EACF;AACF;AACA,SAAS,iBAAiB;AACxB,eAAa,gBAAgB;AAC7B,qBAAmB;AACrB;AACA,SAAS,SAAS,IAAI,GAAG,GAAG;AAC1B,KAAG,cAAc;AACjB,KAAG,aAAa;AAClB;AACA,SAAS,MAAM,IAAI;AACjB,MAAI,UAAU,OAAO;AACrB,MAAI,IAAI,OAAO,UAAU,OAAO;AAChC,MAAI,WAAW,QAAQ,KAAK;AAC1B,WAAO,QAAQ,IAAI,EAAE,EAAE,UAAU,IAAI;AAAA,EACvC,WAAW,GAAG;AACZ,WAAO,EAAE,EAAE,EAAE,MAAM,IAAI,EAAE,CAAC;AAAA,EAC5B,OAAO;AACL,WAAO,GAAG,UAAU,IAAI;AAAA,EAC1B;AACF;AACA,SAAS,QAAQ,IAAI,MAAM;AACzB,MAAI,IAAI,YAAY,UAAU;AAC9B,MAAI,IAAI,OAAO,KAAK,GAAG;AACvB,MAAI,IAAI,QAAQ,KAAK,IAAI;AACzB,MAAI,IAAI,SAAS,KAAK,KAAK;AAC3B,MAAI,IAAI,UAAU,KAAK,MAAM;AAC/B;AACA,SAAS,UAAU,IAAI;AACrB,MAAI,IAAI,YAAY,EAAE;AACtB,MAAI,IAAI,OAAO,EAAE;AACjB,MAAI,IAAI,QAAQ,EAAE;AAClB,MAAI,IAAI,SAAS,EAAE;AACnB,MAAI,IAAI,UAAU,EAAE;AACtB;AACA,SAAS,kCAAkC,WAAW,SAASC,UAAS;AACtE,MAAI,OAAO,CAAC;AACZ,QAAM,KAAK,UAAU,QAAQ,EAAE,QAAQ,SAAU,OAAO;AACtD,QAAI,YAAY,WAAW,aAAa;AACxC,QAAI,CAAC,QAAQ,OAAO,QAAQ,WAAW,WAAW,KAAK,KAAK,MAAM,YAAY,UAAUA,SAAS;AACjG,QAAI,YAAY,QAAQ,KAAK;AAC7B,SAAK,OAAO,KAAK,KAAK,aAAa,KAAK,UAAU,QAAQ,eAAe,SAAS,aAAa,UAAU,UAAU,IAAI;AACvH,SAAK,MAAM,KAAK,KAAK,YAAY,KAAK,SAAS,QAAQ,cAAc,SAAS,YAAY,UAAU,UAAU,GAAG;AACjH,SAAK,QAAQ,KAAK,KAAK,cAAc,KAAK,WAAW,QAAQ,gBAAgB,SAAS,cAAc,WAAW,UAAU,KAAK;AAC9H,SAAK,SAAS,KAAK,KAAK,eAAe,KAAK,YAAY,QAAQ,iBAAiB,SAAS,eAAe,WAAW,UAAU,MAAM;AAAA,EACtI,CAAC;AACD,OAAK,QAAQ,KAAK,QAAQ,KAAK;AAC/B,OAAK,SAAS,KAAK,SAAS,KAAK;AACjC,OAAK,IAAI,KAAK;AACd,OAAK,IAAI,KAAK;AACd,SAAO;AACT;AACA,IAAI,UAAU,cAAa,oBAAI,KAAK,GAAE,QAAQ;AAE9C,SAAS,wBAAwB;AAC/B,MAAI,kBAAkB,CAAC,GACrB;AACF,SAAO;AAAA,IACL,uBAAuB,SAAS,wBAAwB;AACtD,wBAAkB,CAAC;AACnB,UAAI,CAAC,KAAK,QAAQ,UAAW;AAC7B,UAAI,WAAW,CAAC,EAAE,MAAM,KAAK,KAAK,GAAG,QAAQ;AAC7C,eAAS,QAAQ,SAAU,OAAO;AAChC,YAAI,IAAI,OAAO,SAAS,MAAM,UAAU,UAAU,SAAS,MAAO;AAClE,wBAAgB,KAAK;AAAA,UACnB,QAAQ;AAAA,UACR,MAAM,QAAQ,KAAK;AAAA,QACrB,CAAC;AACD,YAAI,WAAW,eAAe,CAAC,GAAG,gBAAgB,gBAAgB,SAAS,CAAC,EAAE,IAAI;AAGlF,YAAI,MAAM,uBAAuB;AAC/B,cAAI,cAAc,OAAO,OAAO,IAAI;AACpC,cAAI,aAAa;AACf,qBAAS,OAAO,YAAY;AAC5B,qBAAS,QAAQ,YAAY;AAAA,UAC/B;AAAA,QACF;AACA,cAAM,WAAW;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,IACA,mBAAmB,SAAS,kBAAkB,OAAO;AACnD,sBAAgB,KAAK,KAAK;AAAA,IAC5B;AAAA,IACA,sBAAsB,SAAS,qBAAqB,QAAQ;AAC1D,sBAAgB,OAAO,cAAc,iBAAiB;AAAA,QACpD;AAAA,MACF,CAAC,GAAG,CAAC;AAAA,IACP;AAAA,IACA,YAAY,SAAS,WAAW,UAAU;AACxC,UAAI,QAAQ;AACZ,UAAI,CAAC,KAAK,QAAQ,WAAW;AAC3B,qBAAa,mBAAmB;AAChC,YAAI,OAAO,aAAa,WAAY,UAAS;AAC7C;AAAA,MACF;AACA,UAAI,YAAY,OACd,gBAAgB;AAClB,sBAAgB,QAAQ,SAAU,OAAO;AACvC,YAAI,OAAO,GACT,SAAS,MAAM,QACf,WAAW,OAAO,UAClB,SAAS,QAAQ,MAAM,GACvB,eAAe,OAAO,cACtB,aAAa,OAAO,YACpB,gBAAgB,MAAM,MACtB,eAAe,OAAO,QAAQ,IAAI;AACpC,YAAI,cAAc;AAEhB,iBAAO,OAAO,aAAa;AAC3B,iBAAO,QAAQ,aAAa;AAAA,QAC9B;AACA,eAAO,SAAS;AAChB,YAAI,OAAO,uBAAuB;AAEhC,cAAI,YAAY,cAAc,MAAM,KAAK,CAAC,YAAY,UAAU,MAAM;AAAA,WAErE,cAAc,MAAM,OAAO,QAAQ,cAAc,OAAO,OAAO,WAAW,SAAS,MAAM,OAAO,QAAQ,SAAS,OAAO,OAAO,OAAO;AAErI,mBAAO,kBAAkB,eAAe,cAAc,YAAY,MAAM,OAAO;AAAA,UACjF;AAAA,QACF;AAGA,YAAI,CAAC,YAAY,QAAQ,QAAQ,GAAG;AAClC,iBAAO,eAAe;AACtB,iBAAO,aAAa;AACpB,cAAI,CAAC,MAAM;AACT,mBAAO,MAAM,QAAQ;AAAA,UACvB;AACA,gBAAM,QAAQ,QAAQ,eAAe,QAAQ,IAAI;AAAA,QACnD;AACA,YAAI,MAAM;AACR,sBAAY;AACZ,0BAAgB,KAAK,IAAI,eAAe,IAAI;AAC5C,uBAAa,OAAO,mBAAmB;AACvC,iBAAO,sBAAsB,WAAW,WAAY;AAClD,mBAAO,gBAAgB;AACvB,mBAAO,eAAe;AACtB,mBAAO,WAAW;AAClB,mBAAO,aAAa;AACpB,mBAAO,wBAAwB;AAAA,UACjC,GAAG,IAAI;AACP,iBAAO,wBAAwB;AAAA,QACjC;AAAA,MACF,CAAC;AACD,mBAAa,mBAAmB;AAChC,UAAI,CAAC,WAAW;AACd,YAAI,OAAO,aAAa,WAAY,UAAS;AAAA,MAC/C,OAAO;AACL,8BAAsB,WAAW,WAAY;AAC3C,cAAI,OAAO,aAAa,WAAY,UAAS;AAAA,QAC/C,GAAG,aAAa;AAAA,MAClB;AACA,wBAAkB,CAAC;AAAA,IACrB;AAAA,IACA,SAAS,SAAS,QAAQ,QAAQ,aAAa,QAAQ,UAAU;AAC/D,UAAI,UAAU;AACZ,YAAI,QAAQ,cAAc,EAAE;AAC5B,YAAI,QAAQ,aAAa,EAAE;AAC3B,YAAI,WAAW,OAAO,KAAK,EAAE,GAC3B,SAAS,YAAY,SAAS,GAC9B,SAAS,YAAY,SAAS,GAC9B,cAAc,YAAY,OAAO,OAAO,SAAS,UAAU,IAC3D,cAAc,YAAY,MAAM,OAAO,QAAQ,UAAU;AAC3D,eAAO,aAAa,CAAC,CAAC;AACtB,eAAO,aAAa,CAAC,CAAC;AACtB,YAAI,QAAQ,aAAa,iBAAiB,aAAa,QAAQ,aAAa,OAAO;AACnF,aAAK,kBAAkB,QAAQ,MAAM;AAErC,YAAI,QAAQ,cAAc,eAAe,WAAW,QAAQ,KAAK,QAAQ,SAAS,MAAM,KAAK,QAAQ,SAAS,GAAG;AACjH,YAAI,QAAQ,aAAa,oBAAoB;AAC7C,eAAO,OAAO,aAAa,YAAY,aAAa,OAAO,QAAQ;AACnE,eAAO,WAAW,WAAW,WAAY;AACvC,cAAI,QAAQ,cAAc,EAAE;AAC5B,cAAI,QAAQ,aAAa,EAAE;AAC3B,iBAAO,WAAW;AAClB,iBAAO,aAAa;AACpB,iBAAO,aAAa;AAAA,QACtB,GAAG,QAAQ;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,QAAQ,QAAQ;AACvB,SAAO,OAAO;AAChB;AACA,SAAS,kBAAkB,eAAe,UAAU,QAAQ,SAAS;AACnE,SAAO,KAAK,KAAK,KAAK,IAAI,SAAS,MAAM,cAAc,KAAK,CAAC,IAAI,KAAK,IAAI,SAAS,OAAO,cAAc,MAAM,CAAC,CAAC,IAAI,KAAK,KAAK,KAAK,IAAI,SAAS,MAAM,OAAO,KAAK,CAAC,IAAI,KAAK,IAAI,SAAS,OAAO,OAAO,MAAM,CAAC,CAAC,IAAI,QAAQ;AAC7N;AAEA,IAAI,UAAU,CAAC;AACf,IAAI,WAAW;AAAA,EACb,qBAAqB;AACvB;AACA,IAAI,gBAAgB;AAAA,EAClB,OAAO,SAAS,MAAM,QAAQ;AAE5B,aAASC,WAAU,UAAU;AAC3B,UAAI,SAAS,eAAeA,OAAM,KAAK,EAAEA,WAAU,SAAS;AAC1D,eAAOA,OAAM,IAAI,SAASA,OAAM;AAAA,MAClC;AAAA,IACF;AACA,YAAQ,QAAQ,SAAU,GAAG;AAC3B,UAAI,EAAE,eAAe,OAAO,YAAY;AACtC,cAAM,iCAAiC,OAAO,OAAO,YAAY,iBAAiB;AAAA,MACpF;AAAA,IACF,CAAC;AACD,YAAQ,KAAK,MAAM;AAAA,EACrB;AAAA,EACA,aAAa,SAAS,YAAY,WAAW,UAAU,KAAK;AAC1D,QAAI,QAAQ;AACZ,SAAK,gBAAgB;AACrB,QAAI,SAAS,WAAY;AACvB,YAAM,gBAAgB;AAAA,IACxB;AACA,QAAI,kBAAkB,YAAY;AAClC,YAAQ,QAAQ,SAAU,QAAQ;AAChC,UAAI,CAAC,SAAS,OAAO,UAAU,EAAG;AAElC,UAAI,SAAS,OAAO,UAAU,EAAE,eAAe,GAAG;AAChD,iBAAS,OAAO,UAAU,EAAE,eAAe,EAAE,eAAe;AAAA,UAC1D;AAAA,QACF,GAAG,GAAG,CAAC;AAAA,MACT;AAIA,UAAI,SAAS,QAAQ,OAAO,UAAU,KAAK,SAAS,OAAO,UAAU,EAAE,SAAS,GAAG;AACjF,iBAAS,OAAO,UAAU,EAAE,SAAS,EAAE,eAAe;AAAA,UACpD;AAAA,QACF,GAAG,GAAG,CAAC;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,SAAS,kBAAkB,UAAU,IAAIC,WAAU,SAAS;AAC7E,YAAQ,QAAQ,SAAU,QAAQ;AAChC,UAAI,aAAa,OAAO;AACxB,UAAI,CAAC,SAAS,QAAQ,UAAU,KAAK,CAAC,OAAO,oBAAqB;AAClE,UAAI,cAAc,IAAI,OAAO,UAAU,IAAI,SAAS,OAAO;AAC3D,kBAAY,WAAW;AACvB,kBAAY,UAAU,SAAS;AAC/B,eAAS,UAAU,IAAI;AAGvB,eAASA,WAAU,YAAY,QAAQ;AAAA,IACzC,CAAC;AACD,aAASD,WAAU,SAAS,SAAS;AACnC,UAAI,CAAC,SAAS,QAAQ,eAAeA,OAAM,EAAG;AAC9C,UAAI,WAAW,KAAK,aAAa,UAAUA,SAAQ,SAAS,QAAQA,OAAM,CAAC;AAC3E,UAAI,OAAO,aAAa,aAAa;AACnC,iBAAS,QAAQA,OAAM,IAAI;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,oBAAoB,SAAS,mBAAmB,MAAM,UAAU;AAC9D,QAAI,kBAAkB,CAAC;AACvB,YAAQ,QAAQ,SAAU,QAAQ;AAChC,UAAI,OAAO,OAAO,oBAAoB,WAAY;AAClD,eAAS,iBAAiB,OAAO,gBAAgB,KAAK,SAAS,OAAO,UAAU,GAAG,IAAI,CAAC;AAAA,IAC1F,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,cAAc,SAAS,aAAa,UAAU,MAAM,OAAO;AACzD,QAAI;AACJ,YAAQ,QAAQ,SAAU,QAAQ;AAEhC,UAAI,CAAC,SAAS,OAAO,UAAU,EAAG;AAGlC,UAAI,OAAO,mBAAmB,OAAO,OAAO,gBAAgB,IAAI,MAAM,YAAY;AAChF,wBAAgB,OAAO,gBAAgB,IAAI,EAAE,KAAK,SAAS,OAAO,UAAU,GAAG,KAAK;AAAA,MACtF;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAEA,SAAS,cAAc,MAAM;AAC3B,MAAI,WAAW,KAAK,UAClBE,UAAS,KAAK,QACd,OAAO,KAAK,MACZ,WAAW,KAAK,UAChBC,WAAU,KAAK,SACf,OAAO,KAAK,MACZ,SAAS,KAAK,QACdC,YAAW,KAAK,UAChBC,YAAW,KAAK,UAChBC,qBAAoB,KAAK,mBACzBC,qBAAoB,KAAK,mBACzB,gBAAgB,KAAK,eACrBC,eAAc,KAAK,aACnB,uBAAuB,KAAK;AAC9B,aAAW,YAAYN,WAAUA,QAAO,OAAO;AAC/C,MAAI,CAAC,SAAU;AACf,MAAI,KACF,UAAU,SAAS,SACnB,SAAS,OAAO,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,OAAO,CAAC;AAE9D,MAAI,OAAO,eAAe,CAAC,cAAc,CAAC,MAAM;AAC9C,UAAM,IAAI,YAAY,MAAM;AAAA,MAC1B,SAAS;AAAA,MACT,YAAY;AAAA,IACd,CAAC;AAAA,EACH,OAAO;AACL,UAAM,SAAS,YAAY,OAAO;AAClC,QAAI,UAAU,MAAM,MAAM,IAAI;AAAA,EAChC;AACA,MAAI,KAAK,QAAQA;AACjB,MAAI,OAAO,UAAUA;AACrB,MAAI,OAAO,YAAYA;AACvB,MAAI,QAAQC;AACZ,MAAI,WAAWC;AACf,MAAI,WAAWC;AACf,MAAI,oBAAoBC;AACxB,MAAI,oBAAoBC;AACxB,MAAI,gBAAgB;AACpB,MAAI,WAAWC,eAAcA,aAAY,cAAc;AACvD,MAAI,qBAAqB,eAAe,eAAe,CAAC,GAAG,oBAAoB,GAAG,cAAc,mBAAmB,MAAM,QAAQ,CAAC;AAClI,WAASR,WAAU,oBAAoB;AACrC,QAAIA,OAAM,IAAI,mBAAmBA,OAAM;AAAA,EACzC;AACA,MAAIE,SAAQ;AACV,IAAAA,QAAO,cAAc,GAAG;AAAA,EAC1B;AACA,MAAI,QAAQ,MAAM,GAAG;AACnB,YAAQ,MAAM,EAAE,KAAK,UAAU,GAAG;AAAA,EACpC;AACF;AAEA,IAAI,YAAY,CAAC,KAAK;AACtB,IAAIO,eAAc,SAASA,aAAY,WAAW,UAAU;AAC1D,MAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC,GAC9E,gBAAgB,KAAK,KACrB,OAAO,yBAAyB,MAAM,SAAS;AACjD,gBAAc,YAAY,KAAK,QAAQ,EAAE,WAAW,UAAU,eAAe;AAAA,IAC3E;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA,gBAAgB,SAAS;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,gBAAgB,SAAS,iBAAiB;AACxC,oBAAc;AAAA,IAChB;AAAA,IACA,eAAe,SAAS,gBAAgB;AACtC,oBAAc;AAAA,IAChB;AAAA,IACA,uBAAuB,SAAS,sBAAsB,MAAM;AAC1D,qBAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG,IAAI,CAAC;AACV;AACA,SAAS,eAAe,MAAM;AAC5B,gBAAc,eAAe;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,IAAI,CAAC;AACV;AACA,IAAI;AAAJ,IACE;AADF,IAEE;AAFF,IAGE;AAHF,IAIE;AAJF,IAKE;AALF,IAME;AANF,IAOE;AAPF,IAQE;AARF,IASE;AATF,IAUE;AAVF,IAWE;AAXF,IAYE;AAZF,IAaE;AAbF,IAcE,sBAAsB;AAdxB,IAeE,kBAAkB;AAfpB,IAgBE,YAAY,CAAC;AAhBf,IAiBE;AAjBF,IAkBE;AAlBF,IAmBE;AAnBF,IAoBE;AApBF,IAqBE;AArBF,IAsBE;AAtBF,IAuBE;AAvBF,IAwBE;AAxBF,IAyBE;AAzBF,IA0BE,wBAAwB;AA1B1B,IA2BE,yBAAyB;AA3B3B,IA4BE;AA5BF,IA8BE;AA9BF,IA+BE,mCAAmC,CAAC;AA/BtC,IAkCE,UAAU;AAlCZ,IAmCE,oBAAoB,CAAC;AAGvB,IAAI,iBAAiB,OAAO,aAAa;AAAzC,IACE,0BAA0B;AAD5B,IAEE,mBAAmB,QAAQ,aAAa,aAAa;AAFvD,IAIE,mBAAmB,kBAAkB,CAAC,oBAAoB,CAAC,OAAO,eAAe,SAAS,cAAc,KAAK;AAJ/G,IAKE,0BAA0B,WAAY;AACpC,MAAI,CAAC,eAAgB;AAErB,MAAI,YAAY;AACd,WAAO;AAAA,EACT;AACA,MAAI,KAAK,SAAS,cAAc,GAAG;AACnC,KAAG,MAAM,UAAU;AACnB,SAAO,GAAG,MAAM,kBAAkB;AACpC,EAAE;AAdJ,IAeE,mBAAmB,SAASC,kBAAiB,IAAI,SAAS;AACxD,MAAI,QAAQ,IAAI,EAAE,GAChB,UAAU,SAAS,MAAM,KAAK,IAAI,SAAS,MAAM,WAAW,IAAI,SAAS,MAAM,YAAY,IAAI,SAAS,MAAM,eAAe,IAAI,SAAS,MAAM,gBAAgB,GAChK,SAAS,SAAS,IAAI,GAAG,OAAO,GAChC,SAAS,SAAS,IAAI,GAAG,OAAO,GAChC,gBAAgB,UAAU,IAAI,MAAM,GACpC,iBAAiB,UAAU,IAAI,MAAM,GACrC,kBAAkB,iBAAiB,SAAS,cAAc,UAAU,IAAI,SAAS,cAAc,WAAW,IAAI,QAAQ,MAAM,EAAE,OAC9H,mBAAmB,kBAAkB,SAAS,eAAe,UAAU,IAAI,SAAS,eAAe,WAAW,IAAI,QAAQ,MAAM,EAAE;AACpI,MAAI,MAAM,YAAY,QAAQ;AAC5B,WAAO,MAAM,kBAAkB,YAAY,MAAM,kBAAkB,mBAAmB,aAAa;AAAA,EACrG;AACA,MAAI,MAAM,YAAY,QAAQ;AAC5B,WAAO,MAAM,oBAAoB,MAAM,GAAG,EAAE,UAAU,IAAI,aAAa;AAAA,EACzE;AACA,MAAI,UAAU,cAAc,OAAO,KAAK,cAAc,OAAO,MAAM,QAAQ;AACzE,QAAI,qBAAqB,cAAc,OAAO,MAAM,SAAS,SAAS;AACtE,WAAO,WAAW,eAAe,UAAU,UAAU,eAAe,UAAU,sBAAsB,aAAa;AAAA,EACnH;AACA,SAAO,WAAW,cAAc,YAAY,WAAW,cAAc,YAAY,UAAU,cAAc,YAAY,WAAW,cAAc,YAAY,UAAU,mBAAmB,WAAW,MAAM,gBAAgB,MAAM,UAAU,UAAU,MAAM,gBAAgB,MAAM,UAAU,kBAAkB,mBAAmB,WAAW,aAAa;AACvV;AAnCF,IAoCE,qBAAqB,SAASC,oBAAmB,UAAU,YAAY,UAAU;AAC/E,MAAI,cAAc,WAAW,SAAS,OAAO,SAAS,KACpD,cAAc,WAAW,SAAS,QAAQ,SAAS,QACnD,kBAAkB,WAAW,SAAS,QAAQ,SAAS,QACvD,cAAc,WAAW,WAAW,OAAO,WAAW,KACtD,cAAc,WAAW,WAAW,QAAQ,WAAW,QACvD,kBAAkB,WAAW,WAAW,QAAQ,WAAW;AAC7D,SAAO,gBAAgB,eAAe,gBAAgB,eAAe,cAAc,kBAAkB,MAAM,cAAc,kBAAkB;AAC7I;AA5CF,IAmDE,8BAA8B,SAASC,6BAA4B,GAAG,GAAG;AACvE,MAAI;AACJ,YAAU,KAAK,SAAU,UAAU;AACjC,QAAI,YAAY,SAAS,OAAO,EAAE,QAAQ;AAC1C,QAAI,CAAC,aAAa,UAAU,QAAQ,EAAG;AACvC,QAAI,OAAO,QAAQ,QAAQ,GACzB,qBAAqB,KAAK,KAAK,OAAO,aAAa,KAAK,KAAK,QAAQ,WACrE,mBAAmB,KAAK,KAAK,MAAM,aAAa,KAAK,KAAK,SAAS;AACrE,QAAI,sBAAsB,kBAAkB;AAC1C,aAAO,MAAM;AAAA,IACf;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAhEF,IAiEE,gBAAgB,SAASC,eAAc,SAAS;AAC9C,WAAS,KAAK,OAAO,MAAM;AACzB,WAAO,SAAU,IAAI,MAAMC,SAAQ,KAAK;AACtC,UAAI,YAAY,GAAG,QAAQ,MAAM,QAAQ,KAAK,QAAQ,MAAM,QAAQ,GAAG,QAAQ,MAAM,SAAS,KAAK,QAAQ,MAAM;AACjH,UAAI,SAAS,SAAS,QAAQ,YAAY;AAGxC,eAAO;AAAA,MACT,WAAW,SAAS,QAAQ,UAAU,OAAO;AAC3C,eAAO;AAAA,MACT,WAAW,QAAQ,UAAU,SAAS;AACpC,eAAO;AAAA,MACT,WAAW,OAAO,UAAU,YAAY;AACtC,eAAO,KAAK,MAAM,IAAI,MAAMA,SAAQ,GAAG,GAAG,IAAI,EAAE,IAAI,MAAMA,SAAQ,GAAG;AAAA,MACvE,OAAO;AACL,YAAI,cAAc,OAAO,KAAK,MAAM,QAAQ,MAAM;AAClD,eAAO,UAAU,QAAQ,OAAO,UAAU,YAAY,UAAU,cAAc,MAAM,QAAQ,MAAM,QAAQ,UAAU,IAAI;AAAA,MAC1H;AAAA,IACF;AAAA,EACF;AACA,MAAI,QAAQ,CAAC;AACb,MAAI,gBAAgB,QAAQ;AAC5B,MAAI,CAAC,iBAAiB,QAAQ,aAAa,KAAK,UAAU;AACxD,oBAAgB;AAAA,MACd,MAAM;AAAA,IACR;AAAA,EACF;AACA,QAAM,OAAO,cAAc;AAC3B,QAAM,YAAY,KAAK,cAAc,MAAM,IAAI;AAC/C,QAAM,WAAW,KAAK,cAAc,GAAG;AACvC,QAAM,cAAc,cAAc;AAClC,UAAQ,QAAQ;AAClB;AAjGF,IAkGE,sBAAsB,SAASC,uBAAsB;AACnD,MAAI,CAAC,2BAA2B,SAAS;AACvC,QAAI,SAAS,WAAW,MAAM;AAAA,EAChC;AACF;AAtGF,IAuGE,wBAAwB,SAASC,yBAAwB;AACvD,MAAI,CAAC,2BAA2B,SAAS;AACvC,QAAI,SAAS,WAAW,EAAE;AAAA,EAC5B;AACF;AAGF,IAAI,kBAAkB,CAAC,kBAAkB;AACvC,WAAS,iBAAiB,SAAS,SAAU,KAAK;AAChD,QAAI,iBAAiB;AACnB,UAAI,eAAe;AACnB,UAAI,mBAAmB,IAAI,gBAAgB;AAC3C,UAAI,4BAA4B,IAAI,yBAAyB;AAC7D,wBAAkB;AAClB,aAAO;AAAA,IACT;AAAA,EACF,GAAG,IAAI;AACT;AACA,IAAI,gCAAgC,SAASC,+BAA8B,KAAK;AAC9E,MAAI,QAAQ;AACV,UAAM,IAAI,UAAU,IAAI,QAAQ,CAAC,IAAI;AACrC,QAAI,UAAU,4BAA4B,IAAI,SAAS,IAAI,OAAO;AAClE,QAAI,SAAS;AAEX,UAAI,QAAQ,CAAC;AACb,eAAS,KAAK,KAAK;AACjB,YAAI,IAAI,eAAe,CAAC,GAAG;AACzB,gBAAM,CAAC,IAAI,IAAI,CAAC;AAAA,QAClB;AAAA,MACF;AACA,YAAM,SAAS,MAAM,SAAS;AAC9B,YAAM,iBAAiB;AACvB,YAAM,kBAAkB;AACxB,cAAQ,OAAO,EAAE,YAAY,KAAK;AAAA,IACpC;AAAA,EACF;AACF;AACA,IAAI,wBAAwB,SAASC,uBAAsB,KAAK;AAC9D,MAAI,QAAQ;AACV,WAAO,WAAW,OAAO,EAAE,iBAAiB,IAAI,MAAM;AAAA,EACxD;AACF;AAOA,SAAS,SAAS,IAAI,SAAS;AAC7B,MAAI,EAAE,MAAM,GAAG,YAAY,GAAG,aAAa,IAAI;AAC7C,UAAM,8CAA8C,OAAO,CAAC,EAAE,SAAS,KAAK,EAAE,CAAC;AAAA,EACjF;AACA,OAAK,KAAK;AACV,OAAK,UAAU,UAAU,SAAS,CAAC,GAAG,OAAO;AAG7C,KAAG,OAAO,IAAI;AACd,MAAIjB,YAAW;AAAA,IACb,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW,WAAW,KAAK,GAAG,QAAQ,IAAI,QAAQ;AAAA,IAClD,eAAe;AAAA;AAAA,IAEf,YAAY;AAAA;AAAA,IAEZ,uBAAuB;AAAA;AAAA,IAEvB,mBAAmB;AAAA,IACnB,WAAW,SAAS,YAAY;AAC9B,aAAO,iBAAiB,IAAI,KAAK,OAAO;AAAA,IAC1C;AAAA,IACA,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,SAAS,SAAS,QAAQ,cAAca,SAAQ;AAC9C,mBAAa,QAAQ,QAAQA,QAAO,WAAW;AAAA,IACjD;AAAA,IACA,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,kBAAkB;AAAA,IAClB,sBAAsB,OAAO,WAAW,SAAS,QAAQ,SAAS,OAAO,kBAAkB,EAAE,KAAK;AAAA,IAClG,eAAe;AAAA,IACf,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,MACd,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA,gBAAgB,SAAS,mBAAmB,SAAS,kBAAkB,UAAU,CAAC;AAAA,IAClF,sBAAsB;AAAA,EACxB;AACA,gBAAc,kBAAkB,MAAM,IAAIb,SAAQ;AAGlD,WAAS,QAAQA,WAAU;AACzB,MAAE,QAAQ,aAAa,QAAQ,IAAI,IAAIA,UAAS,IAAI;AAAA,EACtD;AACA,gBAAc,OAAO;AAGrB,WAAS,MAAM,MAAM;AACnB,QAAI,GAAG,OAAO,CAAC,MAAM,OAAO,OAAO,KAAK,EAAE,MAAM,YAAY;AAC1D,WAAK,EAAE,IAAI,KAAK,EAAE,EAAE,KAAK,IAAI;AAAA,IAC/B;AAAA,EACF;AAGA,OAAK,kBAAkB,QAAQ,gBAAgB,QAAQ;AACvD,MAAI,KAAK,iBAAiB;AAExB,SAAK,QAAQ,sBAAsB;AAAA,EACrC;AAGA,MAAI,QAAQ,gBAAgB;AAC1B,OAAG,IAAI,eAAe,KAAK,WAAW;AAAA,EACxC,OAAO;AACL,OAAG,IAAI,aAAa,KAAK,WAAW;AACpC,OAAG,IAAI,cAAc,KAAK,WAAW;AAAA,EACvC;AACA,MAAI,KAAK,iBAAiB;AACxB,OAAG,IAAI,YAAY,IAAI;AACvB,OAAG,IAAI,aAAa,IAAI;AAAA,EAC1B;AACA,YAAU,KAAK,KAAK,EAAE;AAGtB,UAAQ,SAAS,QAAQ,MAAM,OAAO,KAAK,KAAK,QAAQ,MAAM,IAAI,IAAI,KAAK,CAAC,CAAC;AAG7E,WAAS,MAAM,sBAAsB,CAAC;AACxC;AACA,SAAS;AAA4C;AAAA,EACnD,aAAa;AAAA,EACb,kBAAkB,SAAS,iBAAiB,QAAQ;AAClD,QAAI,CAAC,KAAK,GAAG,SAAS,MAAM,KAAK,WAAW,KAAK,IAAI;AACnD,mBAAa;AAAA,IACf;AAAA,EACF;AAAA,EACA,eAAe,SAAS,cAAc,KAAK,QAAQ;AACjD,WAAO,OAAO,KAAK,QAAQ,cAAc,aAAa,KAAK,QAAQ,UAAU,KAAK,MAAM,KAAK,QAAQ,MAAM,IAAI,KAAK,QAAQ;AAAA,EAC9H;AAAA,EACA,aAAa,SAAS,YAAoC,KAAK;AAC7D,QAAI,CAAC,IAAI,WAAY;AACrB,QAAI,QAAQ,MACV,KAAK,KAAK,IACV,UAAU,KAAK,SACf,kBAAkB,QAAQ,iBAC1B,OAAO,IAAI,MACX,QAAQ,IAAI,WAAW,IAAI,QAAQ,CAAC,KAAK,IAAI,eAAe,IAAI,gBAAgB,WAAW,KAC3F,UAAU,SAAS,KAAK,QACxB,iBAAiB,IAAI,OAAO,eAAe,IAAI,QAAQ,IAAI,KAAK,CAAC,KAAK,IAAI,gBAAgB,IAAI,aAAa,EAAE,CAAC,MAAM,QACpH,SAAS,QAAQ;AACnB,2BAAuB,EAAE;AAGzB,QAAI,QAAQ;AACV;AAAA,IACF;AACA,QAAI,wBAAwB,KAAK,IAAI,KAAK,IAAI,WAAW,KAAK,QAAQ,UAAU;AAC9E;AAAA,IACF;AAGA,QAAI,eAAe,mBAAmB;AACpC;AAAA,IACF;AAGA,QAAI,CAAC,KAAK,mBAAmB,UAAU,UAAU,OAAO,QAAQ,YAAY,MAAM,UAAU;AAC1F;AAAA,IACF;AACA,aAAS,QAAQ,QAAQ,QAAQ,WAAW,IAAI,KAAK;AACrD,QAAI,UAAU,OAAO,UAAU;AAC7B;AAAA,IACF;AACA,QAAI,eAAe,QAAQ;AAEzB;AAAA,IACF;AAGA,eAAW,MAAM,MAAM;AACvB,wBAAoB,MAAM,QAAQ,QAAQ,SAAS;AAGnD,QAAI,OAAO,WAAW,YAAY;AAChC,UAAI,OAAO,KAAK,MAAM,KAAK,QAAQ,IAAI,GAAG;AACxC,uBAAe;AAAA,UACb,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,UAAU;AAAA,UACV,MAAM;AAAA,UACN,QAAQ;AAAA,QACV,CAAC;AACD,QAAAQ,aAAY,UAAU,OAAO;AAAA,UAC3B;AAAA,QACF,CAAC;AACD,2BAAmB,IAAI,cAAc,IAAI,eAAe;AACxD;AAAA,MACF;AAAA,IACF,WAAW,QAAQ;AACjB,eAAS,OAAO,MAAM,GAAG,EAAE,KAAK,SAAU,UAAU;AAClD,mBAAW,QAAQ,gBAAgB,SAAS,KAAK,GAAG,IAAI,KAAK;AAC7D,YAAI,UAAU;AACZ,yBAAe;AAAA,YACb,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,MAAM;AAAA,UACR,CAAC;AACD,UAAAA,aAAY,UAAU,OAAO;AAAA,YAC3B;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,UAAI,QAAQ;AACV,2BAAmB,IAAI,cAAc,IAAI,eAAe;AACxD;AAAA,MACF;AAAA,IACF;AACA,QAAI,QAAQ,UAAU,CAAC,QAAQ,gBAAgB,QAAQ,QAAQ,IAAI,KAAK,GAAG;AACzE;AAAA,IACF;AAGA,SAAK,kBAAkB,KAAK,OAAO,MAAM;AAAA,EAC3C;AAAA,EACA,mBAAmB,SAAS,kBAA+B,KAAiB,OAAyB,QAAQ;AAC3G,QAAI,QAAQ,MACV,KAAK,MAAM,IACX,UAAU,MAAM,SAChB,gBAAgB,GAAG,eACnB;AACF,QAAI,UAAU,CAAC,UAAU,OAAO,eAAe,IAAI;AACjD,UAAI,WAAW,QAAQ,MAAM;AAC7B,eAAS;AACT,eAAS;AACT,iBAAW,OAAO;AAClB,eAAS,OAAO;AAChB,mBAAa;AACb,oBAAc,QAAQ;AACtB,eAAS,UAAU;AACnB,eAAS;AAAA,QACP,QAAQ;AAAA,QACR,UAAU,SAAS,KAAK;AAAA,QACxB,UAAU,SAAS,KAAK;AAAA,MAC1B;AACA,wBAAkB,OAAO,UAAU,SAAS;AAC5C,uBAAiB,OAAO,UAAU,SAAS;AAC3C,WAAK,UAAU,SAAS,KAAK;AAC7B,WAAK,UAAU,SAAS,KAAK;AAC7B,aAAO,MAAM,aAAa,IAAI;AAC9B,oBAAc,SAASU,eAAc;AACnC,QAAAV,aAAY,cAAc,OAAO;AAAA,UAC/B;AAAA,QACF,CAAC;AACD,YAAI,SAAS,eAAe;AAC1B,gBAAM,QAAQ;AACd;AAAA,QACF;AAGA,cAAM,0BAA0B;AAChC,YAAI,CAAC,WAAW,MAAM,iBAAiB;AACrC,iBAAO,YAAY;AAAA,QACrB;AAGA,cAAM,kBAAkB,KAAK,KAAK;AAGlC,uBAAe;AAAA,UACb,UAAU;AAAA,UACV,MAAM;AAAA,UACN,eAAe;AAAA,QACjB,CAAC;AAGD,oBAAY,QAAQ,QAAQ,aAAa,IAAI;AAAA,MAC/C;AAGA,cAAQ,OAAO,MAAM,GAAG,EAAE,QAAQ,SAAU,UAAU;AACpD,aAAK,QAAQ,SAAS,KAAK,GAAG,iBAAiB;AAAA,MACjD,CAAC;AACD,SAAG,eAAe,YAAY,6BAA6B;AAC3D,SAAG,eAAe,aAAa,6BAA6B;AAC5D,SAAG,eAAe,aAAa,6BAA6B;AAC5D,SAAG,eAAe,WAAW,MAAM,OAAO;AAC1C,SAAG,eAAe,YAAY,MAAM,OAAO;AAC3C,SAAG,eAAe,eAAe,MAAM,OAAO;AAG9C,UAAI,WAAW,KAAK,iBAAiB;AACnC,aAAK,QAAQ,sBAAsB;AACnC,eAAO,YAAY;AAAA,MACrB;AACA,MAAAA,aAAY,cAAc,MAAM;AAAA,QAC9B;AAAA,MACF,CAAC;AAGD,UAAI,QAAQ,UAAU,CAAC,QAAQ,oBAAoB,WAAW,CAAC,KAAK,mBAAmB,EAAE,QAAQ,cAAc;AAC7G,YAAI,SAAS,eAAe;AAC1B,eAAK,QAAQ;AACb;AAAA,QACF;AAIA,WAAG,eAAe,WAAW,MAAM,mBAAmB;AACtD,WAAG,eAAe,YAAY,MAAM,mBAAmB;AACvD,WAAG,eAAe,eAAe,MAAM,mBAAmB;AAC1D,WAAG,eAAe,aAAa,MAAM,4BAA4B;AACjE,WAAG,eAAe,aAAa,MAAM,4BAA4B;AACjE,gBAAQ,kBAAkB,GAAG,eAAe,eAAe,MAAM,4BAA4B;AAC7F,cAAM,kBAAkB,WAAW,aAAa,QAAQ,KAAK;AAAA,MAC/D,OAAO;AACL,oBAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EACA,8BAA8B,SAAS,6BAA6D,GAAG;AACrG,QAAI,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI;AACvC,QAAI,KAAK,IAAI,KAAK,IAAI,MAAM,UAAU,KAAK,MAAM,GAAG,KAAK,IAAI,MAAM,UAAU,KAAK,MAAM,CAAC,KAAK,KAAK,MAAM,KAAK,QAAQ,uBAAuB,KAAK,mBAAmB,OAAO,oBAAoB,EAAE,GAAG;AACnM,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,qBAAqB,SAAS,sBAAsB;AAClD,cAAU,kBAAkB,MAAM;AAClC,iBAAa,KAAK,eAAe;AACjC,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,2BAA2B,SAAS,4BAA4B;AAC9D,QAAI,gBAAgB,KAAK,GAAG;AAC5B,QAAI,eAAe,WAAW,KAAK,mBAAmB;AACtD,QAAI,eAAe,YAAY,KAAK,mBAAmB;AACvD,QAAI,eAAe,eAAe,KAAK,mBAAmB;AAC1D,QAAI,eAAe,aAAa,KAAK,4BAA4B;AACjE,QAAI,eAAe,aAAa,KAAK,4BAA4B;AACjE,QAAI,eAAe,eAAe,KAAK,4BAA4B;AAAA,EACrE;AAAA,EACA,mBAAmB,SAAS,kBAA+B,KAAiB,OAAO;AACjF,YAAQ,SAAS,IAAI,eAAe,WAAW;AAC/C,QAAI,CAAC,KAAK,mBAAmB,OAAO;AAClC,UAAI,KAAK,QAAQ,gBAAgB;AAC/B,WAAG,UAAU,eAAe,KAAK,YAAY;AAAA,MAC/C,WAAW,OAAO;AAChB,WAAG,UAAU,aAAa,KAAK,YAAY;AAAA,MAC7C,OAAO;AACL,WAAG,UAAU,aAAa,KAAK,YAAY;AAAA,MAC7C;AAAA,IACF,OAAO;AACL,SAAG,QAAQ,WAAW,IAAI;AAC1B,SAAG,QAAQ,aAAa,KAAK,YAAY;AAAA,IAC3C;AACA,QAAI;AACF,UAAI,SAAS,WAAW;AAEtB,kBAAU,WAAY;AACpB,mBAAS,UAAU,MAAM;AAAA,QAC3B,CAAC;AAAA,MACH,OAAO;AACL,eAAO,aAAa,EAAE,gBAAgB;AAAA,MACxC;AAAA,IACF,SAAS,KAAK;AAAA,IAAC;AAAA,EACjB;AAAA,EACA,cAAc,SAAS,aAAa,UAAU,KAAK;AACjD,0BAAsB;AACtB,QAAI,UAAU,QAAQ;AACpB,MAAAA,aAAY,eAAe,MAAM;AAAA,QAC/B;AAAA,MACF,CAAC;AACD,UAAI,KAAK,iBAAiB;AACxB,WAAG,UAAU,YAAY,qBAAqB;AAAA,MAChD;AACA,UAAI,UAAU,KAAK;AAGnB,OAAC,YAAY,YAAY,QAAQ,QAAQ,WAAW,KAAK;AACzD,kBAAY,QAAQ,QAAQ,YAAY,IAAI;AAC5C,eAAS,SAAS;AAClB,kBAAY,KAAK,aAAa;AAG9B,qBAAe;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,eAAe;AAAA,MACjB,CAAC;AAAA,IACH,OAAO;AACL,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,kBAAkB,SAAS,mBAAmB;AAC5C,QAAI,UAAU;AACZ,WAAK,SAAS,SAAS;AACvB,WAAK,SAAS,SAAS;AACvB,0BAAoB;AACpB,UAAI,SAAS,SAAS,iBAAiB,SAAS,SAAS,SAAS,OAAO;AACzE,UAAI,SAAS;AACb,aAAO,UAAU,OAAO,YAAY;AAClC,iBAAS,OAAO,WAAW,iBAAiB,SAAS,SAAS,SAAS,OAAO;AAC9E,YAAI,WAAW,OAAQ;AACvB,iBAAS;AAAA,MACX;AACA,aAAO,WAAW,OAAO,EAAE,iBAAiB,MAAM;AAClD,UAAI,QAAQ;AACV,WAAG;AACD,cAAI,OAAO,OAAO,GAAG;AACnB,gBAAI,WAAW;AACf,uBAAW,OAAO,OAAO,EAAE,YAAY;AAAA,cACrC,SAAS,SAAS;AAAA,cAClB,SAAS,SAAS;AAAA,cAClB;AAAA,cACA,QAAQ;AAAA,YACV,CAAC;AACD,gBAAI,YAAY,CAAC,KAAK,QAAQ,gBAAgB;AAC5C;AAAA,YACF;AAAA,UACF;AACA,mBAAS;AAAA,QACX,SAC8B,SAAS,gBAAgB,MAAM;AAAA,MAC/D;AACA,4BAAsB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,cAAc,SAAS,aAA6B,KAAK;AACvD,QAAI,QAAQ;AACV,UAAI,UAAU,KAAK,SACjB,oBAAoB,QAAQ,mBAC5B,iBAAiB,QAAQ,gBACzB,QAAQ,IAAI,UAAU,IAAI,QAAQ,CAAC,IAAI,KACvC,cAAc,WAAW,OAAO,SAAS,IAAI,GAC7C,SAAS,WAAW,eAAe,YAAY,GAC/C,SAAS,WAAW,eAAe,YAAY,GAC/C,uBAAuB,2BAA2B,uBAAuB,wBAAwB,mBAAmB,GACpH,MAAM,MAAM,UAAU,OAAO,UAAU,eAAe,MAAM,UAAU,MAAM,uBAAuB,qBAAqB,CAAC,IAAI,iCAAiC,CAAC,IAAI,MAAM,UAAU,IACnL,MAAM,MAAM,UAAU,OAAO,UAAU,eAAe,MAAM,UAAU,MAAM,uBAAuB,qBAAqB,CAAC,IAAI,iCAAiC,CAAC,IAAI,MAAM,UAAU;AAGrL,UAAI,CAAC,SAAS,UAAU,CAAC,qBAAqB;AAC5C,YAAI,qBAAqB,KAAK,IAAI,KAAK,IAAI,MAAM,UAAU,KAAK,MAAM,GAAG,KAAK,IAAI,MAAM,UAAU,KAAK,MAAM,CAAC,IAAI,mBAAmB;AACnI;AAAA,QACF;AACA,aAAK,aAAa,KAAK,IAAI;AAAA,MAC7B;AACA,UAAI,SAAS;AACX,YAAI,aAAa;AACf,sBAAY,KAAK,MAAM,UAAU;AACjC,sBAAY,KAAK,MAAM,UAAU;AAAA,QACnC,OAAO;AACL,wBAAc;AAAA,YACZ,GAAG;AAAA,YACH,GAAG;AAAA,YACH,GAAG;AAAA,YACH,GAAG;AAAA,YACH,GAAG;AAAA,YACH,GAAG;AAAA,UACL;AAAA,QACF;AACA,YAAI,YAAY,UAAU,OAAO,YAAY,GAAG,GAAG,EAAE,OAAO,YAAY,GAAG,GAAG,EAAE,OAAO,YAAY,GAAG,GAAG,EAAE,OAAO,YAAY,GAAG,GAAG,EAAE,OAAO,YAAY,GAAG,GAAG,EAAE,OAAO,YAAY,GAAG,GAAG;AAC1L,YAAI,SAAS,mBAAmB,SAAS;AACzC,YAAI,SAAS,gBAAgB,SAAS;AACtC,YAAI,SAAS,eAAe,SAAS;AACrC,YAAI,SAAS,aAAa,SAAS;AACnC,iBAAS;AACT,iBAAS;AACT,mBAAW;AAAA,MACb;AACA,UAAI,cAAc,IAAI,eAAe;AAAA,IACvC;AAAA,EACF;AAAA,EACA,cAAc,SAAS,eAAe;AAGpC,QAAI,CAAC,SAAS;AACZ,UAAI,YAAY,KAAK,QAAQ,iBAAiB,SAAS,OAAO,QAC5D,OAAO,QAAQ,QAAQ,MAAM,yBAAyB,MAAM,SAAS,GACrE,UAAU,KAAK;AAGjB,UAAI,yBAAyB;AAE3B,8BAAsB;AACtB,eAAO,IAAI,qBAAqB,UAAU,MAAM,YAAY,IAAI,qBAAqB,WAAW,MAAM,UAAU,wBAAwB,UAAU;AAChJ,gCAAsB,oBAAoB;AAAA,QAC5C;AACA,YAAI,wBAAwB,SAAS,QAAQ,wBAAwB,SAAS,iBAAiB;AAC7F,cAAI,wBAAwB,SAAU,uBAAsB,0BAA0B;AACtF,eAAK,OAAO,oBAAoB;AAChC,eAAK,QAAQ,oBAAoB;AAAA,QACnC,OAAO;AACL,gCAAsB,0BAA0B;AAAA,QAClD;AACA,2CAAmC,wBAAwB,mBAAmB;AAAA,MAChF;AACA,gBAAU,OAAO,UAAU,IAAI;AAC/B,kBAAY,SAAS,QAAQ,YAAY,KAAK;AAC9C,kBAAY,SAAS,QAAQ,eAAe,IAAI;AAChD,kBAAY,SAAS,QAAQ,WAAW,IAAI;AAC5C,UAAI,SAAS,cAAc,EAAE;AAC7B,UAAI,SAAS,aAAa,EAAE;AAC5B,UAAI,SAAS,cAAc,YAAY;AACvC,UAAI,SAAS,UAAU,CAAC;AACxB,UAAI,SAAS,OAAO,KAAK,GAAG;AAC5B,UAAI,SAAS,QAAQ,KAAK,IAAI;AAC9B,UAAI,SAAS,SAAS,KAAK,KAAK;AAChC,UAAI,SAAS,UAAU,KAAK,MAAM;AAClC,UAAI,SAAS,WAAW,KAAK;AAC7B,UAAI,SAAS,YAAY,0BAA0B,aAAa,OAAO;AACvE,UAAI,SAAS,UAAU,QAAQ;AAC/B,UAAI,SAAS,iBAAiB,MAAM;AACpC,eAAS,QAAQ;AACjB,gBAAU,YAAY,OAAO;AAG7B,UAAI,SAAS,oBAAoB,kBAAkB,SAAS,QAAQ,MAAM,KAAK,IAAI,MAAM,OAAO,iBAAiB,SAAS,QAAQ,MAAM,MAAM,IAAI,MAAM,GAAG;AAAA,IAC7J;AAAA,EACF;AAAA,EACA,cAAc,SAAS,aAAwB,KAAiB,UAAU;AACxE,QAAI,QAAQ;AACZ,QAAI,eAAe,IAAI;AACvB,QAAI,UAAU,MAAM;AACpB,IAAAA,aAAY,aAAa,MAAM;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,QAAI,SAAS,eAAe;AAC1B,WAAK,QAAQ;AACb;AAAA,IACF;AACA,IAAAA,aAAY,cAAc,IAAI;AAC9B,QAAI,CAAC,SAAS,eAAe;AAC3B,gBAAU,MAAM,MAAM;AACtB,cAAQ,gBAAgB,IAAI;AAC5B,cAAQ,YAAY;AACpB,cAAQ,MAAM,aAAa,IAAI;AAC/B,WAAK,WAAW;AAChB,kBAAY,SAAS,KAAK,QAAQ,aAAa,KAAK;AACpD,eAAS,QAAQ;AAAA,IACnB;AAGA,UAAM,UAAU,UAAU,WAAY;AACpC,MAAAA,aAAY,SAAS,KAAK;AAC1B,UAAI,SAAS,cAAe;AAC5B,UAAI,CAAC,MAAM,QAAQ,mBAAmB;AACpC,eAAO,aAAa,SAAS,MAAM;AAAA,MACrC;AACA,YAAM,WAAW;AACjB,qBAAe;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AACD,KAAC,YAAY,YAAY,QAAQ,QAAQ,WAAW,IAAI;AAGxD,QAAI,UAAU;AACZ,wBAAkB;AAClB,YAAM,UAAU,YAAY,MAAM,kBAAkB,EAAE;AAAA,IACxD,OAAO;AAEL,UAAI,UAAU,WAAW,MAAM,OAAO;AACtC,UAAI,UAAU,YAAY,MAAM,OAAO;AACvC,UAAI,UAAU,eAAe,MAAM,OAAO;AAC1C,UAAI,cAAc;AAChB,qBAAa,gBAAgB;AAC7B,gBAAQ,WAAW,QAAQ,QAAQ,KAAK,OAAO,cAAc,MAAM;AAAA,MACrE;AACA,SAAG,UAAU,QAAQ,KAAK;AAG1B,UAAI,QAAQ,aAAa,eAAe;AAAA,IAC1C;AACA,0BAAsB;AACtB,UAAM,eAAe,UAAU,MAAM,aAAa,KAAK,OAAO,UAAU,GAAG,CAAC;AAC5E,OAAG,UAAU,eAAe,KAAK;AACjC,YAAQ;AACR,QAAI,QAAQ;AACV,UAAI,SAAS,MAAM,eAAe,MAAM;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA,EAEA,aAAa,SAAS,YAAuB,KAAK;AAChD,QAAI,KAAK,KAAK,IACZ,SAAS,IAAI,QACb,UACA,YACA,QACA,UAAU,KAAK,SACf,QAAQ,QAAQ,OAChB,iBAAiB,SAAS,QAC1B,UAAU,gBAAgB,OAC1B,UAAU,QAAQ,MAClB,eAAe,eAAe,gBAC9B,UACA,QAAQ,MACR,iBAAiB;AACnB,QAAI,QAAS;AACb,aAAS,cAAc,MAAM,OAAO;AAClC,MAAAA,aAAY,MAAM,OAAO,eAAe;AAAA,QACtC;AAAA,QACA;AAAA,QACA,MAAM,WAAW,aAAa;AAAA,QAC9B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ,SAAS,OAAOW,SAAQC,QAAO;AACrC,iBAAO,QAAQ,QAAQ,IAAI,QAAQ,UAAUD,SAAQ,QAAQA,OAAM,GAAG,KAAKC,MAAK;AAAA,QAClF;AAAA,QACA;AAAA,MACF,GAAG,KAAK,CAAC;AAAA,IACX;AAGA,aAAS,UAAU;AACjB,oBAAc,0BAA0B;AACxC,YAAM,sBAAsB;AAC5B,UAAI,UAAU,cAAc;AAC1B,qBAAa,sBAAsB;AAAA,MACrC;AAAA,IACF;AAGA,aAAS,UAAU,WAAW;AAC5B,oBAAc,qBAAqB;AAAA,QACjC;AAAA,MACF,CAAC;AACD,UAAI,WAAW;AAEb,YAAI,SAAS;AACX,yBAAe,WAAW;AAAA,QAC5B,OAAO;AACL,yBAAe,WAAW,KAAK;AAAA,QACjC;AACA,YAAI,UAAU,cAAc;AAE1B,sBAAY,QAAQ,cAAc,YAAY,QAAQ,aAAa,eAAe,QAAQ,YAAY,KAAK;AAC3G,sBAAY,QAAQ,QAAQ,YAAY,IAAI;AAAA,QAC9C;AACA,YAAI,gBAAgB,SAAS,UAAU,SAAS,QAAQ;AACtD,wBAAc;AAAA,QAChB,WAAW,UAAU,SAAS,UAAU,aAAa;AACnD,wBAAc;AAAA,QAChB;AAGA,YAAI,iBAAiB,OAAO;AAC1B,gBAAM,wBAAwB;AAAA,QAChC;AACA,cAAM,WAAW,WAAY;AAC3B,wBAAc,2BAA2B;AACzC,gBAAM,wBAAwB;AAAA,QAChC,CAAC;AACD,YAAI,UAAU,cAAc;AAC1B,uBAAa,WAAW;AACxB,uBAAa,wBAAwB;AAAA,QACvC;AAAA,MACF;AAGA,UAAI,WAAW,UAAU,CAAC,OAAO,YAAY,WAAW,MAAM,CAAC,OAAO,UAAU;AAC9E,qBAAa;AAAA,MACf;AAGA,UAAI,CAAC,QAAQ,kBAAkB,CAAC,IAAI,UAAU,WAAW,UAAU;AACjE,eAAO,WAAW,OAAO,EAAE,iBAAiB,IAAI,MAAM;AAGtD,SAAC,aAAa,8BAA8B,GAAG;AAAA,MACjD;AACA,OAAC,QAAQ,kBAAkB,IAAI,mBAAmB,IAAI,gBAAgB;AACtE,aAAO,iBAAiB;AAAA,IAC1B;AAGA,aAAS,UAAU;AACjB,iBAAW,MAAM,MAAM;AACvB,0BAAoB,MAAM,QAAQ,QAAQ,SAAS;AACnD,qBAAe;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AACA,QAAI,IAAI,mBAAmB,QAAQ;AACjC,UAAI,cAAc,IAAI,eAAe;AAAA,IACvC;AACA,aAAS,QAAQ,QAAQ,QAAQ,WAAW,IAAI,IAAI;AACpD,kBAAc,UAAU;AACxB,QAAI,SAAS,cAAe,QAAO;AACnC,QAAI,OAAO,SAAS,IAAI,MAAM,KAAK,OAAO,YAAY,OAAO,cAAc,OAAO,cAAc,MAAM,0BAA0B,QAAQ;AACtI,aAAO,UAAU,KAAK;AAAA,IACxB;AACA,sBAAkB;AAClB,QAAI,kBAAkB,CAAC,QAAQ,aAAa,UAAU,YAAY,SAAS,aAAa,UACtF,gBAAgB,SAAS,KAAK,cAAc,YAAY,UAAU,MAAM,gBAAgB,QAAQ,GAAG,MAAM,MAAM,SAAS,MAAM,gBAAgB,QAAQ,GAAG,IAAI;AAC7J,iBAAW,KAAK,cAAc,KAAK,MAAM,MAAM;AAC/C,iBAAW,QAAQ,MAAM;AACzB,oBAAc,eAAe;AAC7B,UAAI,SAAS,cAAe,QAAO;AACnC,UAAI,QAAQ;AACV,mBAAW;AACX,gBAAQ;AACR,aAAK,WAAW;AAChB,sBAAc,QAAQ;AACtB,YAAI,CAAC,SAAS,eAAe;AAC3B,cAAI,QAAQ;AACV,mBAAO,aAAa,QAAQ,MAAM;AAAA,UACpC,OAAO;AACL,mBAAO,YAAY,MAAM;AAAA,UAC3B;AAAA,QACF;AACA,eAAO,UAAU,IAAI;AAAA,MACvB;AACA,UAAI,cAAc,UAAU,IAAI,QAAQ,SAAS;AACjD,UAAI,CAAC,eAAe,aAAa,KAAK,UAAU,IAAI,KAAK,CAAC,YAAY,UAAU;AAI9E,YAAI,gBAAgB,QAAQ;AAC1B,iBAAO,UAAU,KAAK;AAAA,QACxB;AAGA,YAAI,eAAe,OAAO,IAAI,QAAQ;AACpC,mBAAS;AAAA,QACX;AACA,YAAI,QAAQ;AACV,uBAAa,QAAQ,MAAM;AAAA,QAC7B;AACA,YAAI,QAAQ,QAAQ,IAAI,QAAQ,UAAU,QAAQ,YAAY,KAAK,CAAC,CAAC,MAAM,MAAM,OAAO;AACtF,kBAAQ;AACR,cAAI,eAAe,YAAY,aAAa;AAE1C,eAAG,aAAa,QAAQ,YAAY,WAAW;AAAA,UACjD,OAAO;AACL,eAAG,YAAY,MAAM;AAAA,UACvB;AACA,qBAAW;AAEX,kBAAQ;AACR,iBAAO,UAAU,IAAI;AAAA,QACvB;AAAA,MACF,WAAW,eAAe,cAAc,KAAK,UAAU,IAAI,GAAG;AAE5D,YAAI,aAAa,SAAS,IAAI,GAAG,SAAS,IAAI;AAC9C,YAAI,eAAe,QAAQ;AACzB,iBAAO,UAAU,KAAK;AAAA,QACxB;AACA,iBAAS;AACT,qBAAa,QAAQ,MAAM;AAC3B,YAAI,QAAQ,QAAQ,IAAI,QAAQ,UAAU,QAAQ,YAAY,KAAK,KAAK,MAAM,OAAO;AACnF,kBAAQ;AACR,aAAG,aAAa,QAAQ,UAAU;AAClC,qBAAW;AAEX,kBAAQ;AACR,iBAAO,UAAU,IAAI;AAAA,QACvB;AAAA,MACF,WAAW,OAAO,eAAe,IAAI;AACnC,qBAAa,QAAQ,MAAM;AAC3B,YAAI,YAAY,GACd,uBACA,iBAAiB,OAAO,eAAe,IACvC,kBAAkB,CAAC,mBAAmB,OAAO,YAAY,OAAO,UAAU,UAAU,OAAO,YAAY,OAAO,UAAU,YAAY,QAAQ,GAC5I,QAAQ,WAAW,QAAQ,QAC3B,kBAAkB,eAAe,QAAQ,OAAO,KAAK,KAAK,eAAe,QAAQ,OAAO,KAAK,GAC7F,eAAe,kBAAkB,gBAAgB,YAAY;AAC/D,YAAI,eAAe,QAAQ;AACzB,kCAAwB,WAAW,KAAK;AACxC,kCAAwB;AACxB,mCAAyB,CAAC,mBAAmB,QAAQ,cAAc;AAAA,QACrE;AACA,oBAAY,kBAAkB,KAAK,QAAQ,YAAY,UAAU,kBAAkB,IAAI,QAAQ,eAAe,QAAQ,yBAAyB,OAAO,QAAQ,gBAAgB,QAAQ,uBAAuB,wBAAwB,eAAe,MAAM;AAC1P,YAAI;AACJ,YAAI,cAAc,GAAG;AAEnB,cAAI,YAAY,MAAM,MAAM;AAC5B,aAAG;AACD,yBAAa;AACb,sBAAU,SAAS,SAAS,SAAS;AAAA,UACvC,SAAS,YAAY,IAAI,SAAS,SAAS,MAAM,UAAU,YAAY;AAAA,QACzE;AAEA,YAAI,cAAc,KAAK,YAAY,QAAQ;AACzC,iBAAO,UAAU,KAAK;AAAA,QACxB;AACA,qBAAa;AACb,wBAAgB;AAChB,YAAI,cAAc,OAAO,oBACvB,QAAQ;AACV,gBAAQ,cAAc;AACtB,YAAI,aAAa,QAAQ,QAAQ,IAAI,QAAQ,UAAU,QAAQ,YAAY,KAAK,KAAK;AACrF,YAAI,eAAe,OAAO;AACxB,cAAI,eAAe,KAAK,eAAe,IAAI;AACzC,oBAAQ,eAAe;AAAA,UACzB;AACA,oBAAU;AACV,qBAAW,WAAW,EAAE;AACxB,kBAAQ;AACR,cAAI,SAAS,CAAC,aAAa;AACzB,eAAG,YAAY,MAAM;AAAA,UACvB,OAAO;AACL,mBAAO,WAAW,aAAa,QAAQ,QAAQ,cAAc,MAAM;AAAA,UACrE;AAGA,cAAI,iBAAiB;AACnB,qBAAS,iBAAiB,GAAG,eAAe,gBAAgB,SAAS;AAAA,UACvE;AACA,qBAAW,OAAO;AAGlB,cAAI,0BAA0B,UAAa,CAAC,wBAAwB;AAClE,iCAAqB,KAAK,IAAI,wBAAwB,QAAQ,MAAM,EAAE,KAAK,CAAC;AAAA,UAC9E;AACA,kBAAQ;AACR,iBAAO,UAAU,IAAI;AAAA,QACvB;AAAA,MACF;AACA,UAAI,GAAG,SAAS,MAAM,GAAG;AACvB,eAAO,UAAU,KAAK;AAAA,MACxB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,uBAAuB;AAAA,EACvB,gBAAgB,SAAS,iBAAiB;AACxC,QAAI,UAAU,aAAa,KAAK,YAAY;AAC5C,QAAI,UAAU,aAAa,KAAK,YAAY;AAC5C,QAAI,UAAU,eAAe,KAAK,YAAY;AAC9C,QAAI,UAAU,YAAY,6BAA6B;AACvD,QAAI,UAAU,aAAa,6BAA6B;AACxD,QAAI,UAAU,aAAa,6BAA6B;AAAA,EAC1D;AAAA,EACA,cAAc,SAAS,eAAe;AACpC,QAAI,gBAAgB,KAAK,GAAG;AAC5B,QAAI,eAAe,WAAW,KAAK,OAAO;AAC1C,QAAI,eAAe,YAAY,KAAK,OAAO;AAC3C,QAAI,eAAe,aAAa,KAAK,OAAO;AAC5C,QAAI,eAAe,eAAe,KAAK,OAAO;AAC9C,QAAI,UAAU,eAAe,IAAI;AAAA,EACnC;AAAA,EACA,SAAS,SAAS,QAAmB,KAAK;AACxC,QAAI,KAAK,KAAK,IACZ,UAAU,KAAK;AAGjB,eAAW,MAAM,MAAM;AACvB,wBAAoB,MAAM,QAAQ,QAAQ,SAAS;AACnD,IAAAZ,aAAY,QAAQ,MAAM;AAAA,MACxB;AAAA,IACF,CAAC;AACD,eAAW,UAAU,OAAO;AAG5B,eAAW,MAAM,MAAM;AACvB,wBAAoB,MAAM,QAAQ,QAAQ,SAAS;AACnD,QAAI,SAAS,eAAe;AAC1B,WAAK,SAAS;AACd;AAAA,IACF;AACA,0BAAsB;AACtB,6BAAyB;AACzB,4BAAwB;AACxB,kBAAc,KAAK,OAAO;AAC1B,iBAAa,KAAK,eAAe;AACjC,oBAAgB,KAAK,OAAO;AAC5B,oBAAgB,KAAK,YAAY;AAGjC,QAAI,KAAK,iBAAiB;AACxB,UAAI,UAAU,QAAQ,IAAI;AAC1B,UAAI,IAAI,aAAa,KAAK,YAAY;AAAA,IACxC;AACA,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,QAAI,QAAQ;AACV,UAAI,SAAS,MAAM,eAAe,EAAE;AAAA,IACtC;AACA,QAAI,QAAQ,aAAa,EAAE;AAC3B,QAAI,KAAK;AACP,UAAI,OAAO;AACT,YAAI,cAAc,IAAI,eAAe;AACrC,SAAC,QAAQ,cAAc,IAAI,gBAAgB;AAAA,MAC7C;AACA,iBAAW,QAAQ,cAAc,QAAQ,WAAW,YAAY,OAAO;AACvE,UAAI,WAAW,YAAY,eAAe,YAAY,gBAAgB,SAAS;AAE7E,mBAAW,QAAQ,cAAc,QAAQ,WAAW,YAAY,OAAO;AAAA,MACzE;AACA,UAAI,QAAQ;AACV,YAAI,KAAK,iBAAiB;AACxB,cAAI,QAAQ,WAAW,IAAI;AAAA,QAC7B;AACA,0BAAkB,MAAM;AACxB,eAAO,MAAM,aAAa,IAAI;AAI9B,YAAI,SAAS,CAAC,qBAAqB;AACjC,sBAAY,QAAQ,cAAc,YAAY,QAAQ,aAAa,KAAK,QAAQ,YAAY,KAAK;AAAA,QACnG;AACA,oBAAY,QAAQ,KAAK,QAAQ,aAAa,KAAK;AAGnD,uBAAe;AAAA,UACb,UAAU;AAAA,UACV,MAAM;AAAA,UACN,MAAM;AAAA,UACN,UAAU;AAAA,UACV,mBAAmB;AAAA,UACnB,eAAe;AAAA,QACjB,CAAC;AACD,YAAI,WAAW,UAAU;AACvB,cAAI,YAAY,GAAG;AAEjB,2BAAe;AAAA,cACb,QAAQ;AAAA,cACR,MAAM;AAAA,cACN,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,eAAe;AAAA,YACjB,CAAC;AAGD,2BAAe;AAAA,cACb,UAAU;AAAA,cACV,MAAM;AAAA,cACN,MAAM;AAAA,cACN,eAAe;AAAA,YACjB,CAAC;AAGD,2BAAe;AAAA,cACb,QAAQ;AAAA,cACR,MAAM;AAAA,cACN,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,eAAe;AAAA,YACjB,CAAC;AACD,2BAAe;AAAA,cACb,UAAU;AAAA,cACV,MAAM;AAAA,cACN,MAAM;AAAA,cACN,eAAe;AAAA,YACjB,CAAC;AAAA,UACH;AACA,yBAAe,YAAY,KAAK;AAAA,QAClC,OAAO;AACL,cAAI,aAAa,UAAU;AACzB,gBAAI,YAAY,GAAG;AAEjB,6BAAe;AAAA,gBACb,UAAU;AAAA,gBACV,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,eAAe;AAAA,cACjB,CAAC;AACD,6BAAe;AAAA,gBACb,UAAU;AAAA,gBACV,MAAM;AAAA,gBACN,MAAM;AAAA,gBACN,eAAe;AAAA,cACjB,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AACA,YAAI,SAAS,QAAQ;AAEnB,cAAI,YAAY,QAAQ,aAAa,IAAI;AACvC,uBAAW;AACX,gCAAoB;AAAA,UACtB;AACA,yBAAe;AAAA,YACb,UAAU;AAAA,YACV,MAAM;AAAA,YACN,MAAM;AAAA,YACN,eAAe;AAAA,UACjB,CAAC;AAGD,eAAK,KAAK;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AACA,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,UAAU,SAAS,WAAW;AAC5B,IAAAA,aAAY,WAAW,IAAI;AAC3B,aAAS,SAAS,WAAW,UAAU,SAAS,UAAU,aAAa,cAAc,SAAS,WAAW,QAAQ,WAAW,oBAAoB,WAAW,oBAAoB,aAAa,gBAAgB,cAAc,cAAc,SAAS,UAAU,SAAS,QAAQ,SAAS,QAAQ,SAAS,SAAS;AAC/S,sBAAkB,QAAQ,SAAU,IAAI;AACtC,SAAG,UAAU;AAAA,IACf,CAAC;AACD,sBAAkB,SAAS,SAAS,SAAS;AAAA,EAC/C;AAAA,EACA,aAAa,SAAS,YAAuB,KAAK;AAChD,YAAQ,IAAI,MAAM;AAAA,MAChB,KAAK;AAAA,MACL,KAAK;AACH,aAAK,QAAQ,GAAG;AAChB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,YAAI,QAAQ;AACV,eAAK,YAAY,GAAG;AACpB,0BAAgB,GAAG;AAAA,QACrB;AACA;AAAA,MACF,KAAK;AACH,YAAI,eAAe;AACnB;AAAA,IACJ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,SAAS,UAAU;AAC1B,QAAI,QAAQ,CAAC,GACX,IACA,WAAW,KAAK,GAAG,UACnB,IAAI,GACJ,IAAI,SAAS,QACb,UAAU,KAAK;AACjB,WAAO,IAAI,GAAG,KAAK;AACjB,WAAK,SAAS,CAAC;AACf,UAAI,QAAQ,IAAI,QAAQ,WAAW,KAAK,IAAI,KAAK,GAAG;AAClD,cAAM,KAAK,GAAG,aAAa,QAAQ,UAAU,KAAK,YAAY,EAAE,CAAC;AAAA,MACnE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,SAAS,KAAK,OAAO,cAAc;AACvC,QAAI,QAAQ,CAAC,GACXP,UAAS,KAAK;AAChB,SAAK,QAAQ,EAAE,QAAQ,SAAU,IAAI,GAAG;AACtC,UAAI,KAAKA,QAAO,SAAS,CAAC;AAC1B,UAAI,QAAQ,IAAI,KAAK,QAAQ,WAAWA,SAAQ,KAAK,GAAG;AACtD,cAAM,EAAE,IAAI;AAAA,MACd;AAAA,IACF,GAAG,IAAI;AACP,oBAAgB,KAAK,sBAAsB;AAC3C,UAAM,QAAQ,SAAU,IAAI;AAC1B,UAAI,MAAM,EAAE,GAAG;AACb,QAAAA,QAAO,YAAY,MAAM,EAAE,CAAC;AAC5B,QAAAA,QAAO,YAAY,MAAM,EAAE,CAAC;AAAA,MAC9B;AAAA,IACF,CAAC;AACD,oBAAgB,KAAK,WAAW;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,SAAS,OAAO;AACpB,QAAI,QAAQ,KAAK,QAAQ;AACzB,aAAS,MAAM,OAAO,MAAM,IAAI,IAAI;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,SAAS,UAAU,IAAI,UAAU;AACxC,WAAO,QAAQ,IAAI,YAAY,KAAK,QAAQ,WAAW,KAAK,IAAI,KAAK;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,SAAS,OAAO,MAAM,OAAO;AACnC,QAAI,UAAU,KAAK;AACnB,QAAI,UAAU,QAAQ;AACpB,aAAO,QAAQ,IAAI;AAAA,IACrB,OAAO;AACL,UAAI,gBAAgB,cAAc,aAAa,MAAM,MAAM,KAAK;AAChE,UAAI,OAAO,kBAAkB,aAAa;AACxC,gBAAQ,IAAI,IAAI;AAAA,MAClB,OAAO;AACL,gBAAQ,IAAI,IAAI;AAAA,MAClB;AACA,UAAI,SAAS,SAAS;AACpB,sBAAc,OAAO;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,SAAS,UAAU;AAC1B,IAAAO,aAAY,WAAW,IAAI;AAC3B,QAAI,KAAK,KAAK;AACd,OAAG,OAAO,IAAI;AACd,QAAI,IAAI,aAAa,KAAK,WAAW;AACrC,QAAI,IAAI,cAAc,KAAK,WAAW;AACtC,QAAI,IAAI,eAAe,KAAK,WAAW;AACvC,QAAI,KAAK,iBAAiB;AACxB,UAAI,IAAI,YAAY,IAAI;AACxB,UAAI,IAAI,aAAa,IAAI;AAAA,IAC3B;AAEA,UAAM,UAAU,QAAQ,KAAK,GAAG,iBAAiB,aAAa,GAAG,SAAUa,KAAI;AAC7E,MAAAA,IAAG,gBAAgB,WAAW;AAAA,IAChC,CAAC;AACD,SAAK,QAAQ;AACb,SAAK,0BAA0B;AAC/B,cAAU,OAAO,UAAU,QAAQ,KAAK,EAAE,GAAG,CAAC;AAC9C,SAAK,KAAK,KAAK;AAAA,EACjB;AAAA,EACA,YAAY,SAAS,aAAa;AAChC,QAAI,CAAC,aAAa;AAChB,MAAAb,aAAY,aAAa,IAAI;AAC7B,UAAI,SAAS,cAAe;AAC5B,UAAI,SAAS,WAAW,MAAM;AAC9B,UAAI,KAAK,QAAQ,qBAAqB,QAAQ,YAAY;AACxD,gBAAQ,WAAW,YAAY,OAAO;AAAA,MACxC;AACA,oBAAc;AAAA,IAChB;AAAA,EACF;AAAA,EACA,YAAY,SAAS,WAAWD,cAAa;AAC3C,QAAIA,aAAY,gBAAgB,SAAS;AACvC,WAAK,WAAW;AAChB;AAAA,IACF;AACA,QAAI,aAAa;AACf,MAAAC,aAAY,aAAa,IAAI;AAC7B,UAAI,SAAS,cAAe;AAG5B,UAAI,OAAO,cAAc,UAAU,CAAC,KAAK,QAAQ,MAAM,aAAa;AAClE,eAAO,aAAa,SAAS,MAAM;AAAA,MACrC,WAAW,QAAQ;AACjB,eAAO,aAAa,SAAS,MAAM;AAAA,MACrC,OAAO;AACL,eAAO,YAAY,OAAO;AAAA,MAC5B;AACA,UAAI,KAAK,QAAQ,MAAM,aAAa;AAClC,aAAK,QAAQ,QAAQ,OAAO;AAAA,MAC9B;AACA,UAAI,SAAS,WAAW,EAAE;AAC1B,oBAAc;AAAA,IAChB;AAAA,EACF;AACF;AACA,SAAS,gBAA2B,KAAK;AACvC,MAAI,IAAI,cAAc;AACpB,QAAI,aAAa,aAAa;AAAA,EAChC;AACA,MAAI,cAAc,IAAI,eAAe;AACvC;AACA,SAAS,QAAQ,QAAQ,MAAMK,SAAQ,UAAU,UAAU,YAAY,eAAe,iBAAiB;AACrG,MAAI,KACF,WAAW,OAAO,OAAO,GACzB,WAAW,SAAS,QAAQ,QAC5B;AAEF,MAAI,OAAO,eAAe,CAAC,cAAc,CAAC,MAAM;AAC9C,UAAM,IAAI,YAAY,QAAQ;AAAA,MAC5B,SAAS;AAAA,MACT,YAAY;AAAA,IACd,CAAC;AAAA,EACH,OAAO;AACL,UAAM,SAAS,YAAY,OAAO;AAClC,QAAI,UAAU,QAAQ,MAAM,IAAI;AAAA,EAClC;AACA,MAAI,KAAK;AACT,MAAI,OAAO;AACX,MAAI,UAAUA;AACd,MAAI,cAAc;AAClB,MAAI,UAAU,YAAY;AAC1B,MAAI,cAAc,cAAc,QAAQ,IAAI;AAC5C,MAAI,kBAAkB;AACtB,MAAI,gBAAgB;AACpB,SAAO,cAAc,GAAG;AACxB,MAAI,UAAU;AACZ,aAAS,SAAS,KAAK,UAAU,KAAK,aAAa;AAAA,EACrD;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,IAAI;AAC7B,KAAG,YAAY;AACjB;AACA,SAAS,YAAY;AACnB,YAAU;AACZ;AACA,SAAS,cAAc,KAAK,UAAU,UAAU;AAC9C,MAAI,cAAc,QAAQ,SAAS,SAAS,IAAI,GAAG,SAAS,SAAS,IAAI,CAAC;AAC1E,MAAI,sBAAsB,kCAAkC,SAAS,IAAI,SAAS,SAAS,OAAO;AAClG,MAAI,SAAS;AACb,SAAO,WAAW,IAAI,UAAU,oBAAoB,OAAO,UAAU,IAAI,UAAU,YAAY,OAAO,IAAI,UAAU,YAAY,QAAQ,IAAI,UAAU,oBAAoB,MAAM,UAAU,IAAI,UAAU,YAAY,UAAU,IAAI,UAAU,YAAY;AAC1P;AACA,SAAS,aAAa,KAAK,UAAU,UAAU;AAC7C,MAAI,aAAa,QAAQ,UAAU,SAAS,IAAI,SAAS,QAAQ,SAAS,CAAC;AAC3E,MAAI,sBAAsB,kCAAkC,SAAS,IAAI,SAAS,SAAS,OAAO;AAClG,MAAI,SAAS;AACb,SAAO,WAAW,IAAI,UAAU,oBAAoB,QAAQ,UAAU,IAAI,UAAU,WAAW,UAAU,IAAI,UAAU,WAAW,OAAO,IAAI,UAAU,oBAAoB,SAAS,UAAU,IAAI,UAAU,WAAW,SAAS,IAAI,UAAU,WAAW;AAC3P;AACA,SAAS,kBAAkB,KAAK,QAAQ,YAAY,UAAU,eAAe,uBAAuB,YAAY,cAAc;AAC5H,MAAI,cAAc,WAAW,IAAI,UAAU,IAAI,SAC7C,eAAe,WAAW,WAAW,SAAS,WAAW,OACzD,WAAW,WAAW,WAAW,MAAM,WAAW,MAClD,WAAW,WAAW,WAAW,SAAS,WAAW,OACrD,SAAS;AACX,MAAI,CAAC,YAAY;AAEf,QAAI,gBAAgB,qBAAqB,eAAe,eAAe;AAGrE,UAAI,CAAC,0BAA0B,kBAAkB,IAAI,cAAc,WAAW,eAAe,wBAAwB,IAAI,cAAc,WAAW,eAAe,wBAAwB,IAAI;AAE3L,gCAAwB;AAAA,MAC1B;AACA,UAAI,CAAC,uBAAuB;AAE1B,YAAI,kBAAkB,IAAI,cAAc,WAAW,qBACjD,cAAc,WAAW,oBAAoB;AAC7C,iBAAO,CAAC;AAAA,QACV;AAAA,MACF,OAAO;AACL,iBAAS;AAAA,MACX;AAAA,IACF,OAAO;AAEL,UAAI,cAAc,WAAW,gBAAgB,IAAI,iBAAiB,KAAK,cAAc,WAAW,gBAAgB,IAAI,iBAAiB,GAAG;AACtI,eAAO,oBAAoB,MAAM;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACA,WAAS,UAAU;AACnB,MAAI,QAAQ;AAEV,QAAI,cAAc,WAAW,eAAe,wBAAwB,KAAK,cAAc,WAAW,eAAe,wBAAwB,GAAG;AAC1I,aAAO,cAAc,WAAW,eAAe,IAAI,IAAI;AAAA,IACzD;AAAA,EACF;AACA,SAAO;AACT;AAQA,SAAS,oBAAoB,QAAQ;AACnC,MAAI,MAAM,MAAM,IAAI,MAAM,MAAM,GAAG;AACjC,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAQA,SAAS,YAAY,IAAI;AACvB,MAAI,MAAM,GAAG,UAAU,GAAG,YAAY,GAAG,MAAM,GAAG,OAAO,GAAG,aAC1D,IAAI,IAAI,QACR,MAAM;AACR,SAAO,KAAK;AACV,WAAO,IAAI,WAAW,CAAC;AAAA,EACzB;AACA,SAAO,IAAI,SAAS,EAAE;AACxB;AACA,SAAS,uBAAuB,MAAM;AACpC,oBAAkB,SAAS;AAC3B,MAAI,SAAS,KAAK,qBAAqB,OAAO;AAC9C,MAAI,MAAM,OAAO;AACjB,SAAO,OAAO;AACZ,QAAI,KAAK,OAAO,GAAG;AACnB,OAAG,WAAW,kBAAkB,KAAK,EAAE;AAAA,EACzC;AACF;AACA,SAAS,UAAU,IAAI;AACrB,SAAO,WAAW,IAAI,CAAC;AACzB;AACA,SAAS,gBAAgB,IAAI;AAC3B,SAAO,aAAa,EAAE;AACxB;AAGA,IAAI,gBAAgB;AAClB,KAAG,UAAU,aAAa,SAAU,KAAK;AACvC,SAAK,SAAS,UAAU,wBAAwB,IAAI,YAAY;AAC9D,UAAI,eAAe;AAAA,IACrB;AAAA,EACF,CAAC;AACH;AAGA,SAAS,QAAQ;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,SAAS,GAAG,IAAI,UAAU;AAC5B,WAAO,CAAC,CAAC,QAAQ,IAAI,UAAU,IAAI,KAAK;AAAA,EAC1C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB;AAAA,EACA;AACF;AAOA,SAAS,MAAM,SAAU,SAAS;AAChC,SAAO,QAAQ,OAAO;AACxB;AAMA,SAAS,QAAQ,WAAY;AAC3B,WAAS,OAAO,UAAU,QAAQS,WAAU,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1F,IAAAA,SAAQ,IAAI,IAAI,UAAU,IAAI;AAAA,EAChC;AACA,MAAIA,SAAQ,CAAC,EAAE,gBAAgB,MAAO,CAAAA,WAAUA,SAAQ,CAAC;AACzD,EAAAA,SAAQ,QAAQ,SAAU,QAAQ;AAChC,QAAI,CAAC,OAAO,aAAa,CAAC,OAAO,UAAU,aAAa;AACtD,YAAM,gEAAgE,OAAO,CAAC,EAAE,SAAS,KAAK,MAAM,CAAC;AAAA,IACvG;AACA,QAAI,OAAO,MAAO,UAAS,QAAQ,eAAe,eAAe,CAAC,GAAG,SAAS,KAAK,GAAG,OAAO,KAAK;AAClG,kBAAc,MAAM,MAAM;AAAA,EAC5B,CAAC;AACH;AAOA,SAAS,SAAS,SAAU,IAAI,SAAS;AACvC,SAAO,IAAI,SAAS,IAAI,OAAO;AACjC;AAGA,SAAS,UAAU;AAEnB,IAAI,cAAc,CAAC;AAAnB,IACE;AADF,IAEE;AAFF,IAGE,YAAY;AAHd,IAIE;AAJF,IAKE;AALF,IAME;AANF,IAOE;AACF,SAAS,mBAAmB;AAC1B,WAAS,aAAa;AACpB,SAAK,WAAW;AAAA,MACd,QAAQ;AAAA,MACR,yBAAyB;AAAA,MACzB,mBAAmB;AAAA,MACnB,aAAa;AAAA,MACb,cAAc;AAAA,IAChB;AAGA,aAAS,MAAM,MAAM;AACnB,UAAI,GAAG,OAAO,CAAC,MAAM,OAAO,OAAO,KAAK,EAAE,MAAM,YAAY;AAC1D,aAAK,EAAE,IAAI,KAAK,EAAE,EAAE,KAAK,IAAI;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AACA,aAAW,YAAY;AAAA,IACrB,aAAa,SAASC,aAAY,MAAM;AACtC,UAAI,gBAAgB,KAAK;AACzB,UAAI,KAAK,SAAS,iBAAiB;AACjC,WAAG,UAAU,YAAY,KAAK,iBAAiB;AAAA,MACjD,OAAO;AACL,YAAI,KAAK,QAAQ,gBAAgB;AAC/B,aAAG,UAAU,eAAe,KAAK,yBAAyB;AAAA,QAC5D,WAAW,cAAc,SAAS;AAChC,aAAG,UAAU,aAAa,KAAK,yBAAyB;AAAA,QAC1D,OAAO;AACL,aAAG,UAAU,aAAa,KAAK,yBAAyB;AAAA,QAC1D;AAAA,MACF;AAAA,IACF;AAAA,IACA,mBAAmB,SAAS,kBAAkB,OAAO;AACnD,UAAI,gBAAgB,MAAM;AAE1B,UAAI,CAAC,KAAK,QAAQ,kBAAkB,CAAC,cAAc,QAAQ;AACzD,aAAK,kBAAkB,aAAa;AAAA,MACtC;AAAA,IACF;AAAA,IACA,MAAM,SAASC,QAAO;AACpB,UAAI,KAAK,SAAS,iBAAiB;AACjC,YAAI,UAAU,YAAY,KAAK,iBAAiB;AAAA,MAClD,OAAO;AACL,YAAI,UAAU,eAAe,KAAK,yBAAyB;AAC3D,YAAI,UAAU,aAAa,KAAK,yBAAyB;AACzD,YAAI,UAAU,aAAa,KAAK,yBAAyB;AAAA,MAC3D;AACA,sCAAgC;AAChC,uBAAiB;AACjB,qBAAe;AAAA,IACjB;AAAA,IACA,SAAS,SAAS,UAAU;AAC1B,mBAAa,eAAe,WAAW,YAAY,6BAA6B,kBAAkB,kBAAkB;AACpH,kBAAY,SAAS;AAAA,IACvB;AAAA,IACA,2BAA2B,SAAS,0BAA0B,KAAK;AACjE,WAAK,kBAAkB,KAAK,IAAI;AAAA,IAClC;AAAA,IACA,mBAAmB,SAAS,kBAAkB,KAAK,UAAU;AAC3D,UAAI,QAAQ;AACZ,UAAI,KAAK,IAAI,UAAU,IAAI,QAAQ,CAAC,IAAI,KAAK,SAC3C,KAAK,IAAI,UAAU,IAAI,QAAQ,CAAC,IAAI,KAAK,SACzC,OAAO,SAAS,iBAAiB,GAAG,CAAC;AACvC,mBAAa;AAMb,UAAI,YAAY,KAAK,QAAQ,2BAA2B,QAAQ,cAAc,QAAQ;AACpF,mBAAW,KAAK,KAAK,SAAS,MAAM,QAAQ;AAG5C,YAAI,iBAAiB,2BAA2B,MAAM,IAAI;AAC1D,YAAI,cAAc,CAAC,8BAA8B,MAAM,mBAAmB,MAAM,kBAAkB;AAChG,wCAA8B,gCAAgC;AAE9D,uCAA6B,YAAY,WAAY;AACnD,gBAAI,UAAU,2BAA2B,SAAS,iBAAiB,GAAG,CAAC,GAAG,IAAI;AAC9E,gBAAI,YAAY,gBAAgB;AAC9B,+BAAiB;AACjB,+BAAiB;AAAA,YACnB;AACA,uBAAW,KAAK,MAAM,SAAS,SAAS,QAAQ;AAAA,UAClD,GAAG,EAAE;AACL,4BAAkB;AAClB,4BAAkB;AAAA,QACpB;AAAA,MACF,OAAO;AAEL,YAAI,CAAC,KAAK,QAAQ,gBAAgB,2BAA2B,MAAM,IAAI,MAAM,0BAA0B,GAAG;AACxG,2BAAiB;AACjB;AAAA,QACF;AACA,mBAAW,KAAK,KAAK,SAAS,2BAA2B,MAAM,KAAK,GAAG,KAAK;AAAA,MAC9E;AAAA,IACF;AAAA,EACF;AACA,SAAO,SAAS,YAAY;AAAA,IAC1B,YAAY;AAAA,IACZ,qBAAqB;AAAA,EACvB,CAAC;AACH;AACA,SAAS,mBAAmB;AAC1B,cAAY,QAAQ,SAAUC,aAAY;AACxC,kBAAcA,YAAW,GAAG;AAAA,EAC9B,CAAC;AACD,gBAAc,CAAC;AACjB;AACA,SAAS,kCAAkC;AACzC,gBAAc,0BAA0B;AAC1C;AACA,IAAI,aAAa,SAAS,SAAU,KAAK,SAASxB,SAAQ,YAAY;AAEpE,MAAI,CAAC,QAAQ,OAAQ;AACrB,MAAI,KAAK,IAAI,UAAU,IAAI,QAAQ,CAAC,IAAI,KAAK,SAC3C,KAAK,IAAI,UAAU,IAAI,QAAQ,CAAC,IAAI,KAAK,SACzC,OAAO,QAAQ,mBACf,QAAQ,QAAQ,aAChB,cAAc,0BAA0B;AAC1C,MAAI,qBAAqB,OACvB;AAGF,MAAI,iBAAiBA,SAAQ;AAC3B,mBAAeA;AACf,qBAAiB;AACjB,eAAW,QAAQ;AACnB,qBAAiB,QAAQ;AACzB,QAAI,aAAa,MAAM;AACrB,iBAAW,2BAA2BA,SAAQ,IAAI;AAAA,IACpD;AAAA,EACF;AACA,MAAI,YAAY;AAChB,MAAI,gBAAgB;AACpB,KAAG;AACD,QAAI,KAAK,eACP,OAAO,QAAQ,EAAE,GACjB,MAAM,KAAK,KACX,SAAS,KAAK,QACd,OAAO,KAAK,MACZ,QAAQ,KAAK,OACb,QAAQ,KAAK,OACb,SAAS,KAAK,QACd,aAAa,QACb,aAAa,QACb,cAAc,GAAG,aACjB,eAAe,GAAG,cAClB,QAAQ,IAAI,EAAE,GACd,aAAa,GAAG,YAChB,aAAa,GAAG;AAClB,QAAI,OAAO,aAAa;AACtB,mBAAa,QAAQ,gBAAgB,MAAM,cAAc,UAAU,MAAM,cAAc,YAAY,MAAM,cAAc;AACvH,mBAAa,SAAS,iBAAiB,MAAM,cAAc,UAAU,MAAM,cAAc,YAAY,MAAM,cAAc;AAAA,IAC3H,OAAO;AACL,mBAAa,QAAQ,gBAAgB,MAAM,cAAc,UAAU,MAAM,cAAc;AACvF,mBAAa,SAAS,iBAAiB,MAAM,cAAc,UAAU,MAAM,cAAc;AAAA,IAC3F;AACA,QAAI,KAAK,eAAe,KAAK,IAAI,QAAQ,CAAC,KAAK,QAAQ,aAAa,QAAQ,gBAAgB,KAAK,IAAI,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC;AAC5H,QAAI,KAAK,eAAe,KAAK,IAAI,SAAS,CAAC,KAAK,QAAQ,aAAa,SAAS,iBAAiB,KAAK,IAAI,MAAM,CAAC,KAAK,QAAQ,CAAC,CAAC;AAC9H,QAAI,CAAC,YAAY,SAAS,GAAG;AAC3B,eAAS,IAAI,GAAG,KAAK,WAAW,KAAK;AACnC,YAAI,CAAC,YAAY,CAAC,GAAG;AACnB,sBAAY,CAAC,IAAI,CAAC;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AACA,QAAI,YAAY,SAAS,EAAE,MAAM,MAAM,YAAY,SAAS,EAAE,MAAM,MAAM,YAAY,SAAS,EAAE,OAAO,IAAI;AAC1G,kBAAY,SAAS,EAAE,KAAK;AAC5B,kBAAY,SAAS,EAAE,KAAK;AAC5B,kBAAY,SAAS,EAAE,KAAK;AAC5B,oBAAc,YAAY,SAAS,EAAE,GAAG;AACxC,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,6BAAqB;AAErB,oBAAY,SAAS,EAAE,MAAM,YAAY,WAAY;AAEnD,cAAI,cAAc,KAAK,UAAU,GAAG;AAClC,qBAAS,OAAO,aAAa,UAAU;AAAA,UACzC;AACA,cAAI,gBAAgB,YAAY,KAAK,KAAK,EAAE,KAAK,YAAY,KAAK,KAAK,EAAE,KAAK,QAAQ;AACtF,cAAI,gBAAgB,YAAY,KAAK,KAAK,EAAE,KAAK,YAAY,KAAK,KAAK,EAAE,KAAK,QAAQ;AACtF,cAAI,OAAO,mBAAmB,YAAY;AACxC,gBAAI,eAAe,KAAK,SAAS,QAAQ,WAAW,OAAO,GAAG,eAAe,eAAe,KAAK,YAAY,YAAY,KAAK,KAAK,EAAE,EAAE,MAAM,YAAY;AACvJ;AAAA,YACF;AAAA,UACF;AACA,mBAAS,YAAY,KAAK,KAAK,EAAE,IAAI,eAAe,aAAa;AAAA,QACnE,EAAE,KAAK;AAAA,UACL,OAAO;AAAA,QACT,CAAC,GAAG,EAAE;AAAA,MACR;AAAA,IACF;AACA;AAAA,EACF,SAAS,QAAQ,gBAAgB,kBAAkB,gBAAgB,gBAAgB,2BAA2B,eAAe,KAAK;AAClI,cAAY;AACd,GAAG,EAAE;AAEL,IAAI,OAAO,SAASuB,MAAK,MAAM;AAC7B,MAAI,gBAAgB,KAAK,eACvBjB,eAAc,KAAK,aACnBM,UAAS,KAAK,QACd,iBAAiB,KAAK,gBACtB,wBAAwB,KAAK,uBAC7B,qBAAqB,KAAK,oBAC1B,uBAAuB,KAAK;AAC9B,MAAI,CAAC,cAAe;AACpB,MAAI,aAAaN,gBAAe;AAChC,qBAAmB;AACnB,MAAI,QAAQ,cAAc,kBAAkB,cAAc,eAAe,SAAS,cAAc,eAAe,CAAC,IAAI;AACpH,MAAI,SAAS,SAAS,iBAAiB,MAAM,SAAS,MAAM,OAAO;AACnE,uBAAqB;AACrB,MAAI,cAAc,CAAC,WAAW,GAAG,SAAS,MAAM,GAAG;AACjD,0BAAsB,OAAO;AAC7B,SAAK,QAAQ;AAAA,MACX,QAAQM;AAAA,MACR,aAAaN;AAAA,IACf,CAAC;AAAA,EACH;AACF;AACA,SAAS,SAAS;AAAC;AACnB,OAAO,YAAY;AAAA,EACjB,YAAY;AAAA,EACZ,WAAW,SAAS,UAAU,OAAO;AACnC,QAAIF,qBAAoB,MAAM;AAC9B,SAAK,aAAaA;AAAA,EACpB;AAAA,EACA,SAAS,SAAS,QAAQ,OAAO;AAC/B,QAAIQ,UAAS,MAAM,QACjBN,eAAc,MAAM;AACtB,SAAK,SAAS,sBAAsB;AACpC,QAAIA,cAAa;AACf,MAAAA,aAAY,sBAAsB;AAAA,IACpC;AACA,QAAI,cAAc,SAAS,KAAK,SAAS,IAAI,KAAK,YAAY,KAAK,OAAO;AAC1E,QAAI,aAAa;AACf,WAAK,SAAS,GAAG,aAAaM,SAAQ,WAAW;AAAA,IACnD,OAAO;AACL,WAAK,SAAS,GAAG,YAAYA,OAAM;AAAA,IACrC;AACA,SAAK,SAAS,WAAW;AACzB,QAAIN,cAAa;AACf,MAAAA,aAAY,WAAW;AAAA,IACzB;AAAA,EACF;AAAA,EACA;AACF;AACA,SAAS,QAAQ;AAAA,EACf,YAAY;AACd,CAAC;AACD,SAAS,SAAS;AAAC;AACnB,OAAO,YAAY;AAAA,EACjB,SAAS,SAASmB,SAAQ,OAAO;AAC/B,QAAIb,UAAS,MAAM,QACjBN,eAAc,MAAM;AACtB,QAAI,iBAAiBA,gBAAe,KAAK;AACzC,mBAAe,sBAAsB;AACrC,IAAAM,QAAO,cAAcA,QAAO,WAAW,YAAYA,OAAM;AACzD,mBAAe,WAAW;AAAA,EAC5B;AAAA,EACA;AACF;AACA,SAAS,QAAQ;AAAA,EACf,YAAY;AACd,CAAC;AAED,IAAI;AACJ,SAAS,aAAa;AACpB,WAAS,OAAO;AACd,SAAK,WAAW;AAAA,MACd,WAAW;AAAA,IACb;AAAA,EACF;AACA,OAAK,YAAY;AAAA,IACf,WAAW,SAASc,WAAU,MAAM;AAClC,UAAId,UAAS,KAAK;AAClB,mBAAaA;AAAA,IACf;AAAA,IACA,eAAe,SAAS,cAAc,OAAO;AAC3C,UAAI,YAAY,MAAM,WACpB,SAAS,MAAM,QACf,SAAS,MAAM,QACf,iBAAiB,MAAM,gBACvB,UAAU,MAAM,SAChB,SAAS,MAAM;AACjB,UAAI,CAAC,eAAe,QAAQ,KAAM;AAClC,UAAI,KAAK,KAAK,SAAS,IACrB,UAAU,KAAK;AACjB,UAAI,UAAU,WAAW,IAAI;AAC3B,YAAI,aAAa;AACjB,YAAI,OAAO,MAAM,MAAM,OAAO;AAC5B,sBAAY,QAAQ,QAAQ,WAAW,IAAI;AAC3C,uBAAa;AAAA,QACf,OAAO;AACL,uBAAa;AAAA,QACf;AACA,YAAI,cAAc,eAAe,YAAY;AAC3C,sBAAY,YAAY,QAAQ,WAAW,KAAK;AAAA,QAClD;AAAA,MACF;AACA,cAAQ;AACR,gBAAU,IAAI;AACd,aAAO;AAAA,IACT;AAAA,IACA,MAAM,SAASW,MAAK,OAAO;AACzB,UAAI,iBAAiB,MAAM,gBACzBjB,eAAc,MAAM,aACpBM,UAAS,MAAM;AACjB,UAAI,aAAaN,gBAAe,KAAK;AACrC,UAAI,UAAU,KAAK;AACnB,oBAAc,YAAY,YAAY,QAAQ,WAAW,KAAK;AAC9D,UAAI,eAAe,QAAQ,QAAQA,gBAAeA,aAAY,QAAQ,OAAO;AAC3E,YAAIM,YAAW,YAAY;AACzB,qBAAW,sBAAsB;AACjC,cAAI,eAAe,eAAgB,gBAAe,sBAAsB;AACxE,oBAAUA,SAAQ,UAAU;AAC5B,qBAAW,WAAW;AACtB,cAAI,eAAe,eAAgB,gBAAe,WAAW;AAAA,QAC/D;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS,SAAS,UAAU;AAC1B,mBAAa;AAAA,IACf;AAAA,EACF;AACA,SAAO,SAAS,MAAM;AAAA,IACpB,YAAY;AAAA,IACZ,iBAAiB,SAAS,kBAAkB;AAC1C,aAAO;AAAA,QACL,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF,CAAC;AACH;AACA,SAAS,UAAU,IAAI,IAAI;AACzB,MAAI,KAAK,GAAG,YACV,KAAK,GAAG,YACR,IACA;AACF,MAAI,CAAC,MAAM,CAAC,MAAM,GAAG,YAAY,EAAE,KAAK,GAAG,YAAY,EAAE,EAAG;AAC5D,OAAK,MAAM,EAAE;AACb,OAAK,MAAM,EAAE;AACb,MAAI,GAAG,YAAY,EAAE,KAAK,KAAK,IAAI;AACjC;AAAA,EACF;AACA,KAAG,aAAa,IAAI,GAAG,SAAS,EAAE,CAAC;AACnC,KAAG,aAAa,IAAI,GAAG,SAAS,EAAE,CAAC;AACrC;AAEA,IAAI,oBAAoB,CAAC;AAAzB,IACE,kBAAkB,CAAC;AADrB,IAEE;AAFF,IAIE;AAJF,IAKE,iBAAiB;AALnB,IAOE,UAAU;AAPZ,IASE,cAAc;AAThB,IAUE;AAVF,IAWE;AAXF,IAYE;AACF,SAAS,kBAAkB;AACzB,WAAS,UAAU,UAAU;AAE3B,aAAS,MAAM,MAAM;AACnB,UAAI,GAAG,OAAO,CAAC,MAAM,OAAO,OAAO,KAAK,EAAE,MAAM,YAAY;AAC1D,aAAK,EAAE,IAAI,KAAK,EAAE,EAAE,KAAK,IAAI;AAAA,MAC/B;AAAA,IACF;AACA,QAAI,CAAC,SAAS,QAAQ,uBAAuB;AAC3C,UAAI,SAAS,QAAQ,gBAAgB;AACnC,WAAG,UAAU,aAAa,KAAK,kBAAkB;AAAA,MACnD,OAAO;AACL,WAAG,UAAU,WAAW,KAAK,kBAAkB;AAC/C,WAAG,UAAU,YAAY,KAAK,kBAAkB;AAAA,MAClD;AAAA,IACF;AACA,OAAG,UAAU,WAAW,KAAK,aAAa;AAC1C,OAAG,UAAU,SAAS,KAAK,WAAW;AACtC,SAAK,WAAW;AAAA,MACd,eAAe;AAAA,MACf,cAAc;AAAA,MACd,uBAAuB;AAAA,MACvB,SAAS,SAAS,QAAQ,cAAcA,SAAQ;AAC9C,YAAI,OAAO;AACX,YAAI,kBAAkB,UAAU,sBAAsB,UAAU;AAC9D,4BAAkB,QAAQ,SAAU,kBAAkB,GAAG;AACvD,qBAAS,CAAC,IAAI,KAAK,QAAQ,iBAAiB;AAAA,UAC9C,CAAC;AAAA,QACH,OAAO;AACL,iBAAOA,QAAO;AAAA,QAChB;AACA,qBAAa,QAAQ,QAAQ,IAAI;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACA,YAAU,YAAY;AAAA,IACpB,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,kBAAkB,SAAS,iBAAiB,MAAM;AAChD,UAAI,UAAU,KAAK;AACnB,iBAAW;AAAA,IACb;AAAA,IACA,YAAY,SAAS,aAAa;AAChC,WAAK,cAAc,CAAC,kBAAkB,QAAQ,QAAQ;AAAA,IACxD;AAAA,IACA,YAAY,SAAS,WAAW,OAAO;AACrC,UAAI,WAAW,MAAM,UACnB,SAAS,MAAM;AACjB,UAAI,CAAC,KAAK,YAAa;AACvB,eAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,wBAAgB,KAAK,MAAM,kBAAkB,CAAC,CAAC,CAAC;AAChD,wBAAgB,CAAC,EAAE,gBAAgB,kBAAkB,CAAC,EAAE;AACxD,wBAAgB,CAAC,EAAE,YAAY;AAC/B,wBAAgB,CAAC,EAAE,MAAM,aAAa,IAAI;AAC1C,oBAAY,gBAAgB,CAAC,GAAG,KAAK,QAAQ,eAAe,KAAK;AACjE,0BAAkB,CAAC,MAAM,YAAY,YAAY,gBAAgB,CAAC,GAAG,KAAK,QAAQ,aAAa,KAAK;AAAA,MACtG;AACA,eAAS,WAAW;AACpB,aAAO;AAAA,IACT;AAAA,IACA,OAAO,SAASe,OAAM,OAAO;AAC3B,UAAI,WAAW,MAAM,UACnB3B,UAAS,MAAM,QACf,wBAAwB,MAAM,uBAC9B,SAAS,MAAM;AACjB,UAAI,CAAC,KAAK,YAAa;AACvB,UAAI,CAAC,KAAK,QAAQ,mBAAmB;AACnC,YAAI,kBAAkB,UAAU,sBAAsB,UAAU;AAC9D,gCAAsB,MAAMA,OAAM;AAClC,gCAAsB,OAAO;AAC7B,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,IACA,WAAW,SAAS,UAAU,OAAO;AACnC,UAAI,gBAAgB,MAAM,eACxBA,UAAS,MAAM,QACf,SAAS,MAAM;AACjB,UAAI,CAAC,KAAK,YAAa;AACvB,4BAAsB,OAAOA,OAAM;AACnC,sBAAgB,QAAQ,SAAU2B,QAAO;AACvC,YAAIA,QAAO,WAAW,EAAE;AAAA,MAC1B,CAAC;AACD,oBAAc;AACd,qBAAe;AACf,aAAO;AAAA,IACT;AAAA,IACA,WAAW,SAAS,UAAU,OAAO;AACnC,UAAI,QAAQ;AACZ,UAAI,WAAW,MAAM,UACnB,iBAAiB,MAAM,gBACvB,SAAS,MAAM;AACjB,UAAI,CAAC,KAAK,YAAa;AACvB,sBAAgB,QAAQ,SAAUA,QAAO;AACvC,YAAIA,QAAO,WAAW,MAAM;AAC5B,YAAI,MAAM,QAAQ,qBAAqBA,OAAM,YAAY;AACvD,UAAAA,OAAM,WAAW,YAAYA,MAAK;AAAA,QACpC;AAAA,MACF,CAAC;AACD,qBAAe;AACf,qBAAe;AACf,aAAO;AAAA,IACT;AAAA,IACA,iBAAiB,SAAS,gBAAgB,OAAO;AAC/C,UAAI,WAAW,MAAM;AACrB,UAAI,CAAC,KAAK,eAAe,mBAAmB;AAC1C,0BAAkB,UAAU,mBAAmB;AAAA,MACjD;AACA,wBAAkB,QAAQ,SAAU,kBAAkB;AACpD,yBAAiB,gBAAgB,MAAM,gBAAgB;AAAA,MACzD,CAAC;AAGD,0BAAoB,kBAAkB,KAAK,SAAU,GAAG,GAAG;AACzD,eAAO,EAAE,gBAAgB,EAAE;AAAA,MAC7B,CAAC;AACD,oBAAc;AAAA,IAChB;AAAA,IACA,aAAa,SAASL,aAAY,OAAO;AACvC,UAAI,SAAS;AACb,UAAI,WAAW,MAAM;AACrB,UAAI,CAAC,KAAK,YAAa;AACvB,UAAI,KAAK,QAAQ,MAAM;AAQrB,iBAAS,sBAAsB;AAC/B,YAAI,KAAK,QAAQ,WAAW;AAC1B,4BAAkB,QAAQ,SAAU,kBAAkB;AACpD,gBAAI,qBAAqB,SAAU;AACnC,gBAAI,kBAAkB,YAAY,UAAU;AAAA,UAC9C,CAAC;AACD,cAAI,WAAW,QAAQ,UAAU,OAAO,MAAM,IAAI;AAClD,4BAAkB,QAAQ,SAAU,kBAAkB;AACpD,gBAAI,qBAAqB,SAAU;AACnC,oBAAQ,kBAAkB,QAAQ;AAAA,UACpC,CAAC;AACD,oBAAU;AACV,2BAAiB;AAAA,QACnB;AAAA,MACF;AACA,eAAS,WAAW,WAAY;AAC9B,kBAAU;AACV,yBAAiB;AACjB,YAAI,OAAO,QAAQ,WAAW;AAC5B,4BAAkB,QAAQ,SAAU,kBAAkB;AACpD,sBAAU,gBAAgB;AAAA,UAC5B,CAAC;AAAA,QACH;AAGA,YAAI,OAAO,QAAQ,MAAM;AACvB,kCAAwB;AAAA,QAC1B;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,UAAU,SAAS,SAAS,OAAO;AACjC,UAAI,SAAS,MAAM,QACjB,YAAY,MAAM,WAClB,SAAS,MAAM;AACjB,UAAI,WAAW,CAAC,kBAAkB,QAAQ,MAAM,GAAG;AACjD,kBAAU,KAAK;AACf,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,QAAQ,SAAS,OAAO,OAAO;AAC7B,UAAI,eAAe,MAAM,cACvBtB,UAAS,MAAM,QACf,WAAW,MAAM,UACjB,WAAW,MAAM;AACnB,UAAI,kBAAkB,SAAS,GAAG;AAEhC,0BAAkB,QAAQ,SAAU,kBAAkB;AACpD,mBAAS,kBAAkB;AAAA,YACzB,QAAQ;AAAA,YACR,MAAM,UAAU,QAAQ,gBAAgB,IAAI;AAAA,UAC9C,CAAC;AACD,oBAAU,gBAAgB;AAC1B,2BAAiB,WAAW;AAC5B,uBAAa,qBAAqB,gBAAgB;AAAA,QACpD,CAAC;AACD,kBAAU;AACV,gCAAwB,CAAC,KAAK,QAAQ,mBAAmBA,OAAM;AAAA,MACjE;AAAA,IACF;AAAA,IACA,mBAAmB,SAAS,kBAAkB,QAAQ;AACpD,UAAI,WAAW,OAAO,UACpB,UAAU,OAAO,SACjB,YAAY,OAAO,WACnB,iBAAiB,OAAO,gBACxB4B,YAAW,OAAO,UAClBtB,eAAc,OAAO;AACvB,UAAI,UAAU,KAAK;AACnB,UAAI,WAAW;AAEb,YAAI,SAAS;AACX,yBAAe,WAAW;AAAA,QAC5B;AACA,yBAAiB;AAEjB,YAAI,QAAQ,aAAa,kBAAkB,SAAS,MAAM,WAAW,CAAC,WAAW,CAAC,eAAe,QAAQ,QAAQ,CAACA,eAAc;AAE9H,cAAI,mBAAmB,QAAQ,UAAU,OAAO,MAAM,IAAI;AAC1D,4BAAkB,QAAQ,SAAU,kBAAkB;AACpD,gBAAI,qBAAqB,SAAU;AACnC,oBAAQ,kBAAkB,gBAAgB;AAI1C,YAAAsB,UAAS,YAAY,gBAAgB;AAAA,UACvC,CAAC;AACD,oBAAU;AAAA,QACZ;AAGA,YAAI,CAAC,SAAS;AAEZ,cAAI,CAAC,SAAS;AACZ,oCAAwB;AAAA,UAC1B;AACA,cAAI,kBAAkB,SAAS,GAAG;AAChC,gBAAI,qBAAqB;AACzB,2BAAe,WAAW,QAAQ;AAGlC,gBAAI,eAAe,QAAQ,aAAa,CAAC,gBAAgB,oBAAoB;AAC3E,8BAAgB,QAAQ,SAAUD,QAAO;AACvC,+BAAe,kBAAkB;AAAA,kBAC/B,QAAQA;AAAA,kBACR,MAAM;AAAA,gBACR,CAAC;AACD,gBAAAA,OAAM,WAAW;AACjB,gBAAAA,OAAM,wBAAwB;AAAA,cAChC,CAAC;AAAA,YACH;AAAA,UACF,OAAO;AACL,2BAAe,WAAW,QAAQ;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,0BAA0B,SAAS,yBAAyB,QAAQ;AAClE,UAAI,WAAW,OAAO,UACpB,UAAU,OAAO,SACjB,iBAAiB,OAAO;AAC1B,wBAAkB,QAAQ,SAAU,kBAAkB;AACpD,yBAAiB,wBAAwB;AAAA,MAC3C,CAAC;AACD,UAAI,eAAe,QAAQ,aAAa,CAAC,WAAW,eAAe,UAAU,aAAa;AACxF,yBAAiB,SAAS,CAAC,GAAG,QAAQ;AACtC,YAAI,aAAa,OAAO,UAAU,IAAI;AACtC,uBAAe,OAAO,WAAW;AACjC,uBAAe,QAAQ,WAAW;AAAA,MACpC;AAAA,IACF;AAAA,IACA,2BAA2B,SAAS,4BAA4B;AAC9D,UAAI,SAAS;AACX,kBAAU;AACV,gCAAwB;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,MAAM,SAASJ,MAAK,QAAQ;AAC1B,UAAI,MAAM,OAAO,eACfvB,UAAS,OAAO,QAChB4B,YAAW,OAAO,UAClB,WAAW,OAAO,UAClB,wBAAwB,OAAO,uBAC/B1B,YAAW,OAAO,UAClBI,eAAc,OAAO;AACvB,UAAI,aAAaA,gBAAe,KAAK;AACrC,UAAI,CAAC,IAAK;AACV,UAAI,UAAU,KAAK,SACjB,WAAWsB,UAAS;AAGtB,UAAI,CAAC,aAAa;AAChB,YAAI,QAAQ,gBAAgB,CAAC,KAAK,kBAAkB;AAClD,eAAK,mBAAmB;AAAA,QAC1B;AACA,oBAAY,UAAU,QAAQ,eAAe,CAAC,CAAC,kBAAkB,QAAQ,QAAQ,CAAC;AAClF,YAAI,CAAC,CAAC,kBAAkB,QAAQ,QAAQ,GAAG;AACzC,4BAAkB,KAAK,QAAQ;AAC/B,wBAAc;AAAA,YACZ;AAAA,YACA,QAAQ5B;AAAA,YACR,MAAM;AAAA,YACN,UAAU;AAAA,YACV,eAAe;AAAA,UACjB,CAAC;AAGD,cAAI,IAAI,YAAY,uBAAuB,SAAS,GAAG,SAAS,mBAAmB,GAAG;AACpF,gBAAI,YAAY,MAAM,mBAAmB,GACvC,eAAe,MAAM,QAAQ;AAC/B,gBAAI,CAAC,aAAa,CAAC,gBAAgB,cAAc,cAAc;AAG7D,kBAAI,GAAG;AACP,kBAAI,eAAe,WAAW;AAC5B,oBAAI;AACJ,oBAAI;AAAA,cACN,OAAO;AACL,oBAAI;AACJ,oBAAI,YAAY;AAAA,cAClB;AACA,qBAAO,IAAI,GAAG,KAAK;AACjB,oBAAI,CAAC,kBAAkB,QAAQ,SAAS,CAAC,CAAC,EAAG;AAC7C,4BAAY,SAAS,CAAC,GAAG,QAAQ,eAAe,IAAI;AACpD,kCAAkB,KAAK,SAAS,CAAC,CAAC;AAClC,8BAAc;AAAA,kBACZ;AAAA,kBACA,QAAQA;AAAA,kBACR,MAAM;AAAA,kBACN,UAAU,SAAS,CAAC;AAAA,kBACpB,eAAe;AAAA,gBACjB,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF,OAAO;AACL,kCAAsB;AAAA,UACxB;AACA,8BAAoB;AAAA,QACtB,OAAO;AACL,4BAAkB,OAAO,kBAAkB,QAAQ,QAAQ,GAAG,CAAC;AAC/D,gCAAsB;AACtB,wBAAc;AAAA,YACZ;AAAA,YACA,QAAQA;AAAA,YACR,MAAM;AAAA,YACN,UAAU;AAAA,YACV,eAAe;AAAA,UACjB,CAAC;AAAA,QACH;AAAA,MACF;AAGA,UAAI,eAAe,KAAK,aAAa;AACnC,kBAAU;AAEV,aAAK4B,UAAS,OAAO,EAAE,QAAQ,QAAQA,cAAa5B,YAAW,kBAAkB,SAAS,GAAG;AAC3F,cAAI,WAAW,QAAQ,QAAQ,GAC7B,iBAAiB,MAAM,UAAU,WAAW,KAAK,QAAQ,gBAAgB,GAAG;AAC9E,cAAI,CAAC,kBAAkB,QAAQ,UAAW,UAAS,wBAAwB;AAC3E,qBAAW,sBAAsB;AACjC,cAAI,CAAC,gBAAgB;AACnB,gBAAI,QAAQ,WAAW;AACrB,uBAAS,WAAW;AACpB,gCAAkB,QAAQ,SAAU,kBAAkB;AACpD,iCAAiB,wBAAwB;AACzC,oBAAI,qBAAqB,UAAU;AACjC,sBAAI,OAAO,UAAU,QAAQ,gBAAgB,IAAI;AACjD,mCAAiB,WAAW;AAG5B,6BAAW,kBAAkB;AAAA,oBAC3B,QAAQ;AAAA,oBACR;AAAA,kBACF,CAAC;AAAA,gBACH;AAAA,cACF,CAAC;AAAA,YACH;AAIA,oCAAwB;AACxB,8BAAkB,QAAQ,SAAU,kBAAkB;AACpD,kBAAI,SAAS,cAAc,GAAG;AAC5B,gBAAA4B,UAAS,aAAa,kBAAkB,SAAS,cAAc,CAAC;AAAA,cAClE,OAAO;AACL,gBAAAA,UAAS,YAAY,gBAAgB;AAAA,cACvC;AACA;AAAA,YACF,CAAC;AAKD,gBAAI1B,cAAa,MAAM,QAAQ,GAAG;AAChC,kBAAI,SAAS;AACb,gCAAkB,QAAQ,SAAU,kBAAkB;AACpD,oBAAI,iBAAiB,kBAAkB,MAAM,gBAAgB,GAAG;AAC9D,2BAAS;AACT;AAAA,gBACF;AAAA,cACF,CAAC;AACD,kBAAI,QAAQ;AACV,sCAAsB,QAAQ;AAC9B,sCAAsB,MAAM;AAAA,cAC9B;AAAA,YACF;AAAA,UACF;AAGA,4BAAkB,QAAQ,SAAU,kBAAkB;AACpD,sBAAU,gBAAgB;AAAA,UAC5B,CAAC;AACD,qBAAW,WAAW;AAAA,QACxB;AACA,4BAAoB;AAAA,MACtB;AAGA,UAAIF,YAAW4B,aAAYtB,gBAAeA,aAAY,gBAAgB,SAAS;AAC7E,wBAAgB,QAAQ,SAAUqB,QAAO;AACvC,UAAAA,OAAM,cAAcA,OAAM,WAAW,YAAYA,MAAK;AAAA,QACxD,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,eAAe,SAAS,gBAAgB;AACtC,WAAK,cAAc,cAAc;AACjC,sBAAgB,SAAS;AAAA,IAC3B;AAAA,IACA,eAAe,SAAS,gBAAgB;AACtC,WAAK,mBAAmB;AACxB,UAAI,UAAU,aAAa,KAAK,kBAAkB;AAClD,UAAI,UAAU,WAAW,KAAK,kBAAkB;AAChD,UAAI,UAAU,YAAY,KAAK,kBAAkB;AACjD,UAAI,UAAU,WAAW,KAAK,aAAa;AAC3C,UAAI,UAAU,SAAS,KAAK,WAAW;AAAA,IACzC;AAAA,IACA,oBAAoB,SAAS,mBAAmB,KAAK;AACnD,UAAI,OAAO,gBAAgB,eAAe,YAAa;AAGvD,UAAI,sBAAsB,KAAK,SAAU;AAGzC,UAAI,OAAO,QAAQ,IAAI,QAAQ,KAAK,QAAQ,WAAW,KAAK,SAAS,IAAI,KAAK,EAAG;AAGjF,UAAI,OAAO,IAAI,WAAW,EAAG;AAC7B,aAAO,kBAAkB,QAAQ;AAC/B,YAAI,KAAK,kBAAkB,CAAC;AAC5B,oBAAY,IAAI,KAAK,QAAQ,eAAe,KAAK;AACjD,0BAAkB,MAAM;AACxB,sBAAc;AAAA,UACZ,UAAU,KAAK;AAAA,UACf,QAAQ,KAAK,SAAS;AAAA,UACtB,MAAM;AAAA,UACN,UAAU;AAAA,UACV,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,eAAe,SAAS,cAAc,KAAK;AACzC,UAAI,IAAI,QAAQ,KAAK,QAAQ,cAAc;AACzC,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,aAAa,SAAS,YAAY,KAAK;AACrC,UAAI,IAAI,QAAQ,KAAK,QAAQ,cAAc;AACzC,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACA,SAAO,SAAS,WAAW;AAAA;AAAA,IAEzB,YAAY;AAAA,IACZ,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,MAKL,QAAQ,SAAS,OAAO,IAAI;AAC1B,YAAI,WAAW,GAAG,WAAW,OAAO;AACpC,YAAI,CAAC,YAAY,CAAC,SAAS,QAAQ,aAAa,CAAC,kBAAkB,QAAQ,EAAE,EAAG;AAChF,YAAI,qBAAqB,sBAAsB,UAAU;AACvD,4BAAkB,UAAU,mBAAmB;AAC/C,8BAAoB;AAAA,QACtB;AACA,oBAAY,IAAI,SAAS,QAAQ,eAAe,IAAI;AACpD,0BAAkB,KAAK,EAAE;AAAA,MAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,UAAU,SAAS,SAAS,IAAI;AAC9B,YAAI,WAAW,GAAG,WAAW,OAAO,GAClC/B,SAAQ,kBAAkB,QAAQ,EAAE;AACtC,YAAI,CAAC,YAAY,CAAC,SAAS,QAAQ,aAAa,CAAC,CAACA,OAAO;AACzD,oBAAY,IAAI,SAAS,QAAQ,eAAe,KAAK;AACrD,0BAAkB,OAAOA,QAAO,CAAC;AAAA,MACnC;AAAA,IACF;AAAA,IACA,iBAAiB,SAAS,kBAAkB;AAC1C,UAAI,SAAS;AACb,UAAI,cAAc,CAAC,GACjB,cAAc,CAAC;AACjB,wBAAkB,QAAQ,SAAU,kBAAkB;AACpD,oBAAY,KAAK;AAAA,UACf;AAAA,UACA,OAAO,iBAAiB;AAAA,QAC1B,CAAC;AAGD,YAAIO;AACJ,YAAI,WAAW,qBAAqB,UAAU;AAC5C,UAAAA,YAAW;AAAA,QACb,WAAW,SAAS;AAClB,UAAAA,YAAW,MAAM,kBAAkB,WAAW,OAAO,QAAQ,gBAAgB,GAAG;AAAA,QAClF,OAAO;AACL,UAAAA,YAAW,MAAM,gBAAgB;AAAA,QACnC;AACA,oBAAY,KAAK;AAAA,UACf;AAAA,UACA,OAAOA;AAAA,QACT,CAAC;AAAA,MACH,CAAC;AACD,aAAO;AAAA,QACL,OAAO,mBAAmB,iBAAiB;AAAA,QAC3C,QAAQ,CAAC,EAAE,OAAO,eAAe;AAAA,QACjC;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,cAAc,SAAS,aAAa,KAAK;AACvC,cAAM,IAAI,YAAY;AACtB,YAAI,QAAQ,QAAQ;AAClB,gBAAM;AAAA,QACR,WAAW,IAAI,SAAS,GAAG;AACzB,gBAAM,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,OAAO,CAAC;AAAA,QAClD;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,CAAC;AACH;AACA,SAAS,wBAAwB,gBAAgBH,SAAQ;AACvD,oBAAkB,QAAQ,SAAU,kBAAkB,GAAG;AACvD,QAAI,SAASA,QAAO,SAAS,iBAAiB,iBAAiB,iBAAiB,OAAO,CAAC,IAAI,EAAE;AAC9F,QAAI,QAAQ;AACV,MAAAA,QAAO,aAAa,kBAAkB,MAAM;AAAA,IAC9C,OAAO;AACL,MAAAA,QAAO,YAAY,gBAAgB;AAAA,IACrC;AAAA,EACF,CAAC;AACH;AAOA,SAAS,sBAAsB,kBAAkBA,SAAQ;AACvD,kBAAgB,QAAQ,SAAU2B,QAAO,GAAG;AAC1C,QAAI,SAAS3B,QAAO,SAAS2B,OAAM,iBAAiB,mBAAmB,OAAO,CAAC,IAAI,EAAE;AACrF,QAAI,QAAQ;AACV,MAAA3B,QAAO,aAAa2B,QAAO,MAAM;AAAA,IACnC,OAAO;AACL,MAAA3B,QAAO,YAAY2B,MAAK;AAAA,IAC1B;AAAA,EACF,CAAC;AACH;AACA,SAAS,0BAA0B;AACjC,oBAAkB,QAAQ,SAAU,kBAAkB;AACpD,QAAI,qBAAqB,SAAU;AACnC,qBAAiB,cAAc,iBAAiB,WAAW,YAAY,gBAAgB;AAAA,EACzF,CAAC;AACH;AAEA,SAAS,MAAM,IAAI,iBAAiB,CAAC;AACrC,SAAS,MAAM,QAAQ,MAAM;AAE7B,IAAO,uBAAQ;", "names": ["obj", "index", "ghostEl", "option", "defaults", "rootEl", "cloneEl", "oldIndex", "newIndex", "oldDraggableIndex", "newDraggableIndex", "putSortable", "pluginEvent", "_detectDirection", "_dragElInRowColumn", "_detectNearestEmptySortable", "_prepareGroup", "dragEl", "_hideGhostForTarget", "_unhideGhostForTarget", "nearestEmptyInsertDetectEvent", "_checkOutsideTargetEl", "dragStartFn", "target", "after", "el", "plugins", "dragStarted", "drop", "autoScroll", "onSpill", "dragStart", "clone", "parentEl"]}