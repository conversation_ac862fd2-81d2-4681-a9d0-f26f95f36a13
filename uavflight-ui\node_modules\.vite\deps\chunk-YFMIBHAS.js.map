{"version": 3, "sources": ["../../quickselect/index.js", "../../rbush/index.js"], "sourcesContent": ["\nexport default function quickselect(arr, k, left, right, compare) {\n    quickselectStep(arr, k, left || 0, right || (arr.length - 1), compare || defaultCompare);\n}\n\nfunction quickselectStep(arr, k, left, right, compare) {\n\n    while (right > left) {\n        if (right - left > 600) {\n            var n = right - left + 1;\n            var m = k - left + 1;\n            var z = Math.log(n);\n            var s = 0.5 * Math.exp(2 * z / 3);\n            var sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n            var newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n            var newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n            quickselectStep(arr, k, newLeft, newRight, compare);\n        }\n\n        var t = arr[k];\n        var i = left;\n        var j = right;\n\n        swap(arr, left, k);\n        if (compare(arr[right], t) > 0) swap(arr, left, right);\n\n        while (i < j) {\n            swap(arr, i, j);\n            i++;\n            j--;\n            while (compare(arr[i], t) < 0) i++;\n            while (compare(arr[j], t) > 0) j--;\n        }\n\n        if (compare(arr[left], t) === 0) swap(arr, left, j);\n        else {\n            j++;\n            swap(arr, j, right);\n        }\n\n        if (j <= k) left = j + 1;\n        if (k <= j) right = j - 1;\n    }\n}\n\nfunction swap(arr, i, j) {\n    var tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n}\n\nfunction defaultCompare(a, b) {\n    return a < b ? -1 : a > b ? 1 : 0;\n}\n", "import quickselect from 'quickselect';\n\nexport default class RBush {\n    constructor(maxEntries = 9) {\n        // max entries in a node is 9 by default; min node fill is 40% for best performance\n        this._maxEntries = Math.max(4, maxEntries);\n        this._minEntries = Math.max(2, Math.ceil(this._maxEntries * 0.4));\n        this.clear();\n    }\n\n    all() {\n        return this._all(this.data, []);\n    }\n\n    search(bbox) {\n        let node = this.data;\n        const result = [];\n\n        if (!intersects(bbox, node)) return result;\n\n        const toBBox = this.toBBox;\n        const nodesToSearch = [];\n\n        while (node) {\n            for (let i = 0; i < node.children.length; i++) {\n                const child = node.children[i];\n                const childBBox = node.leaf ? toBBox(child) : child;\n\n                if (intersects(bbox, childBBox)) {\n                    if (node.leaf) result.push(child);\n                    else if (contains(bbox, childBBox)) this._all(child, result);\n                    else nodesToSearch.push(child);\n                }\n            }\n            node = nodesToSearch.pop();\n        }\n\n        return result;\n    }\n\n    collides(bbox) {\n        let node = this.data;\n\n        if (!intersects(bbox, node)) return false;\n\n        const nodesToSearch = [];\n        while (node) {\n            for (let i = 0; i < node.children.length; i++) {\n                const child = node.children[i];\n                const childBBox = node.leaf ? this.toBBox(child) : child;\n\n                if (intersects(bbox, childBBox)) {\n                    if (node.leaf || contains(bbox, childBBox)) return true;\n                    nodesToSearch.push(child);\n                }\n            }\n            node = nodesToSearch.pop();\n        }\n\n        return false;\n    }\n\n    load(data) {\n        if (!(data && data.length)) return this;\n\n        if (data.length < this._minEntries) {\n            for (let i = 0; i < data.length; i++) {\n                this.insert(data[i]);\n            }\n            return this;\n        }\n\n        // recursively build the tree with the given data from scratch using OMT algorithm\n        let node = this._build(data.slice(), 0, data.length - 1, 0);\n\n        if (!this.data.children.length) {\n            // save as is if tree is empty\n            this.data = node;\n\n        } else if (this.data.height === node.height) {\n            // split root if trees have the same height\n            this._splitRoot(this.data, node);\n\n        } else {\n            if (this.data.height < node.height) {\n                // swap trees if inserted one is bigger\n                const tmpNode = this.data;\n                this.data = node;\n                node = tmpNode;\n            }\n\n            // insert the small tree into the large tree at appropriate level\n            this._insert(node, this.data.height - node.height - 1, true);\n        }\n\n        return this;\n    }\n\n    insert(item) {\n        if (item) this._insert(item, this.data.height - 1);\n        return this;\n    }\n\n    clear() {\n        this.data = createNode([]);\n        return this;\n    }\n\n    remove(item, equalsFn) {\n        if (!item) return this;\n\n        let node = this.data;\n        const bbox = this.toBBox(item);\n        const path = [];\n        const indexes = [];\n        let i, parent, goingUp;\n\n        // depth-first iterative tree traversal\n        while (node || path.length) {\n\n            if (!node) { // go up\n                node = path.pop();\n                parent = path[path.length - 1];\n                i = indexes.pop();\n                goingUp = true;\n            }\n\n            if (node.leaf) { // check current node\n                const index = findItem(item, node.children, equalsFn);\n\n                if (index !== -1) {\n                    // item found, remove the item and condense tree upwards\n                    node.children.splice(index, 1);\n                    path.push(node);\n                    this._condense(path);\n                    return this;\n                }\n            }\n\n            if (!goingUp && !node.leaf && contains(node, bbox)) { // go down\n                path.push(node);\n                indexes.push(i);\n                i = 0;\n                parent = node;\n                node = node.children[0];\n\n            } else if (parent) { // go right\n                i++;\n                node = parent.children[i];\n                goingUp = false;\n\n            } else node = null; // nothing found\n        }\n\n        return this;\n    }\n\n    toBBox(item) { return item; }\n\n    compareMinX(a, b) { return a.minX - b.minX; }\n    compareMinY(a, b) { return a.minY - b.minY; }\n\n    toJSON() { return this.data; }\n\n    fromJSON(data) {\n        this.data = data;\n        return this;\n    }\n\n    _all(node, result) {\n        const nodesToSearch = [];\n        while (node) {\n            if (node.leaf) result.push(...node.children);\n            else nodesToSearch.push(...node.children);\n\n            node = nodesToSearch.pop();\n        }\n        return result;\n    }\n\n    _build(items, left, right, height) {\n\n        const N = right - left + 1;\n        let M = this._maxEntries;\n        let node;\n\n        if (N <= M) {\n            // reached leaf level; return leaf\n            node = createNode(items.slice(left, right + 1));\n            calcBBox(node, this.toBBox);\n            return node;\n        }\n\n        if (!height) {\n            // target height of the bulk-loaded tree\n            height = Math.ceil(Math.log(N) / Math.log(M));\n\n            // target number of root entries to maximize storage utilization\n            M = Math.ceil(N / Math.pow(M, height - 1));\n        }\n\n        node = createNode([]);\n        node.leaf = false;\n        node.height = height;\n\n        // split the items into M mostly square tiles\n\n        const N2 = Math.ceil(N / M);\n        const N1 = N2 * Math.ceil(Math.sqrt(M));\n\n        multiSelect(items, left, right, N1, this.compareMinX);\n\n        for (let i = left; i <= right; i += N1) {\n\n            const right2 = Math.min(i + N1 - 1, right);\n\n            multiSelect(items, i, right2, N2, this.compareMinY);\n\n            for (let j = i; j <= right2; j += N2) {\n\n                const right3 = Math.min(j + N2 - 1, right2);\n\n                // pack each entry recursively\n                node.children.push(this._build(items, j, right3, height - 1));\n            }\n        }\n\n        calcBBox(node, this.toBBox);\n\n        return node;\n    }\n\n    _chooseSubtree(bbox, node, level, path) {\n        while (true) {\n            path.push(node);\n\n            if (node.leaf || path.length - 1 === level) break;\n\n            let minArea = Infinity;\n            let minEnlargement = Infinity;\n            let targetNode;\n\n            for (let i = 0; i < node.children.length; i++) {\n                const child = node.children[i];\n                const area = bboxArea(child);\n                const enlargement = enlargedArea(bbox, child) - area;\n\n                // choose entry with the least area enlargement\n                if (enlargement < minEnlargement) {\n                    minEnlargement = enlargement;\n                    minArea = area < minArea ? area : minArea;\n                    targetNode = child;\n\n                } else if (enlargement === minEnlargement) {\n                    // otherwise choose one with the smallest area\n                    if (area < minArea) {\n                        minArea = area;\n                        targetNode = child;\n                    }\n                }\n            }\n\n            node = targetNode || node.children[0];\n        }\n\n        return node;\n    }\n\n    _insert(item, level, isNode) {\n        const bbox = isNode ? item : this.toBBox(item);\n        const insertPath = [];\n\n        // find the best node for accommodating the item, saving all nodes along the path too\n        const node = this._chooseSubtree(bbox, this.data, level, insertPath);\n\n        // put the item into the node\n        node.children.push(item);\n        extend(node, bbox);\n\n        // split on node overflow; propagate upwards if necessary\n        while (level >= 0) {\n            if (insertPath[level].children.length > this._maxEntries) {\n                this._split(insertPath, level);\n                level--;\n            } else break;\n        }\n\n        // adjust bboxes along the insertion path\n        this._adjustParentBBoxes(bbox, insertPath, level);\n    }\n\n    // split overflowed node into two\n    _split(insertPath, level) {\n        const node = insertPath[level];\n        const M = node.children.length;\n        const m = this._minEntries;\n\n        this._chooseSplitAxis(node, m, M);\n\n        const splitIndex = this._chooseSplitIndex(node, m, M);\n\n        const newNode = createNode(node.children.splice(splitIndex, node.children.length - splitIndex));\n        newNode.height = node.height;\n        newNode.leaf = node.leaf;\n\n        calcBBox(node, this.toBBox);\n        calcBBox(newNode, this.toBBox);\n\n        if (level) insertPath[level - 1].children.push(newNode);\n        else this._splitRoot(node, newNode);\n    }\n\n    _splitRoot(node, newNode) {\n        // split root node\n        this.data = createNode([node, newNode]);\n        this.data.height = node.height + 1;\n        this.data.leaf = false;\n        calcBBox(this.data, this.toBBox);\n    }\n\n    _chooseSplitIndex(node, m, M) {\n        let index;\n        let minOverlap = Infinity;\n        let minArea = Infinity;\n\n        for (let i = m; i <= M - m; i++) {\n            const bbox1 = distBBox(node, 0, i, this.toBBox);\n            const bbox2 = distBBox(node, i, M, this.toBBox);\n\n            const overlap = intersectionArea(bbox1, bbox2);\n            const area = bboxArea(bbox1) + bboxArea(bbox2);\n\n            // choose distribution with minimum overlap\n            if (overlap < minOverlap) {\n                minOverlap = overlap;\n                index = i;\n\n                minArea = area < minArea ? area : minArea;\n\n            } else if (overlap === minOverlap) {\n                // otherwise choose distribution with minimum area\n                if (area < minArea) {\n                    minArea = area;\n                    index = i;\n                }\n            }\n        }\n\n        return index || M - m;\n    }\n\n    // sorts node children by the best axis for split\n    _chooseSplitAxis(node, m, M) {\n        const compareMinX = node.leaf ? this.compareMinX : compareNodeMinX;\n        const compareMinY = node.leaf ? this.compareMinY : compareNodeMinY;\n        const xMargin = this._allDistMargin(node, m, M, compareMinX);\n        const yMargin = this._allDistMargin(node, m, M, compareMinY);\n\n        // if total distributions margin value is minimal for x, sort by minX,\n        // otherwise it's already sorted by minY\n        if (xMargin < yMargin) node.children.sort(compareMinX);\n    }\n\n    // total margin of all possible split distributions where each node is at least m full\n    _allDistMargin(node, m, M, compare) {\n        node.children.sort(compare);\n\n        const toBBox = this.toBBox;\n        const leftBBox = distBBox(node, 0, m, toBBox);\n        const rightBBox = distBBox(node, M - m, M, toBBox);\n        let margin = bboxMargin(leftBBox) + bboxMargin(rightBBox);\n\n        for (let i = m; i < M - m; i++) {\n            const child = node.children[i];\n            extend(leftBBox, node.leaf ? toBBox(child) : child);\n            margin += bboxMargin(leftBBox);\n        }\n\n        for (let i = M - m - 1; i >= m; i--) {\n            const child = node.children[i];\n            extend(rightBBox, node.leaf ? toBBox(child) : child);\n            margin += bboxMargin(rightBBox);\n        }\n\n        return margin;\n    }\n\n    _adjustParentBBoxes(bbox, path, level) {\n        // adjust bboxes along the given tree path\n        for (let i = level; i >= 0; i--) {\n            extend(path[i], bbox);\n        }\n    }\n\n    _condense(path) {\n        // go through the path, removing empty nodes and updating bboxes\n        for (let i = path.length - 1, siblings; i >= 0; i--) {\n            if (path[i].children.length === 0) {\n                if (i > 0) {\n                    siblings = path[i - 1].children;\n                    siblings.splice(siblings.indexOf(path[i]), 1);\n\n                } else this.clear();\n\n            } else calcBBox(path[i], this.toBBox);\n        }\n    }\n}\n\nfunction findItem(item, items, equalsFn) {\n    if (!equalsFn) return items.indexOf(item);\n\n    for (let i = 0; i < items.length; i++) {\n        if (equalsFn(item, items[i])) return i;\n    }\n    return -1;\n}\n\n// calculate node's bbox from bboxes of its children\nfunction calcBBox(node, toBBox) {\n    distBBox(node, 0, node.children.length, toBBox, node);\n}\n\n// min bounding rectangle of node children from k to p-1\nfunction distBBox(node, k, p, toBBox, destNode) {\n    if (!destNode) destNode = createNode(null);\n    destNode.minX = Infinity;\n    destNode.minY = Infinity;\n    destNode.maxX = -Infinity;\n    destNode.maxY = -Infinity;\n\n    for (let i = k; i < p; i++) {\n        const child = node.children[i];\n        extend(destNode, node.leaf ? toBBox(child) : child);\n    }\n\n    return destNode;\n}\n\nfunction extend(a, b) {\n    a.minX = Math.min(a.minX, b.minX);\n    a.minY = Math.min(a.minY, b.minY);\n    a.maxX = Math.max(a.maxX, b.maxX);\n    a.maxY = Math.max(a.maxY, b.maxY);\n    return a;\n}\n\nfunction compareNodeMinX(a, b) { return a.minX - b.minX; }\nfunction compareNodeMinY(a, b) { return a.minY - b.minY; }\n\nfunction bboxArea(a)   { return (a.maxX - a.minX) * (a.maxY - a.minY); }\nfunction bboxMargin(a) { return (a.maxX - a.minX) + (a.maxY - a.minY); }\n\nfunction enlargedArea(a, b) {\n    return (Math.max(b.maxX, a.maxX) - Math.min(b.minX, a.minX)) *\n           (Math.max(b.maxY, a.maxY) - Math.min(b.minY, a.minY));\n}\n\nfunction intersectionArea(a, b) {\n    const minX = Math.max(a.minX, b.minX);\n    const minY = Math.max(a.minY, b.minY);\n    const maxX = Math.min(a.maxX, b.maxX);\n    const maxY = Math.min(a.maxY, b.maxY);\n\n    return Math.max(0, maxX - minX) *\n           Math.max(0, maxY - minY);\n}\n\nfunction contains(a, b) {\n    return a.minX <= b.minX &&\n           a.minY <= b.minY &&\n           b.maxX <= a.maxX &&\n           b.maxY <= a.maxY;\n}\n\nfunction intersects(a, b) {\n    return b.minX <= a.maxX &&\n           b.minY <= a.maxY &&\n           b.maxX >= a.minX &&\n           b.maxY >= a.minY;\n}\n\nfunction createNode(children) {\n    return {\n        children,\n        height: 1,\n        leaf: true,\n        minX: Infinity,\n        minY: Infinity,\n        maxX: -Infinity,\n        maxY: -Infinity\n    };\n}\n\n// sort an array so that items come in groups of n unsorted items, with groups sorted between each other;\n// combines selection algorithm with binary divide & conquer approach\n\nfunction multiSelect(arr, left, right, n, compare) {\n    const stack = [left, right];\n\n    while (stack.length) {\n        right = stack.pop();\n        left = stack.pop();\n\n        if (right - left <= n) continue;\n\n        const mid = left + Math.ceil((right - left) / n / 2) * n;\n        quickselect(arr, mid, left, right, compare);\n\n        stack.push(left, mid, mid, right);\n    }\n}\n"], "mappings": ";AACe,SAAR,YAA6B,KAAK,GAAG,MAAM,OAAO,SAAS;AAC9D,kBAAgB,KAAK,GAAG,QAAQ,GAAG,SAAU,IAAI,SAAS,GAAI,WAAW,cAAc;AAC3F;AAEA,SAAS,gBAAgB,KAAK,GAAG,MAAM,OAAO,SAAS;AAEnD,SAAO,QAAQ,MAAM;AACjB,QAAI,QAAQ,OAAO,KAAK;AACpB,UAAI,IAAI,QAAQ,OAAO;AACvB,UAAI,IAAI,IAAI,OAAO;AACnB,UAAI,IAAI,KAAK,IAAI,CAAC;AAClB,UAAI,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC;AAChC,UAAI,KAAK,MAAM,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AACtE,UAAI,UAAU,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AAC3D,UAAI,WAAW,KAAK,IAAI,OAAO,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;AACnE,sBAAgB,KAAK,GAAG,SAAS,UAAU,OAAO;AAAA,IACtD;AAEA,QAAI,IAAI,IAAI,CAAC;AACb,QAAI,IAAI;AACR,QAAI,IAAI;AAER,SAAK,KAAK,MAAM,CAAC;AACjB,QAAI,QAAQ,IAAI,KAAK,GAAG,CAAC,IAAI,EAAG,MAAK,KAAK,MAAM,KAAK;AAErD,WAAO,IAAI,GAAG;AACV,WAAK,KAAK,GAAG,CAAC;AACd;AACA;AACA,aAAO,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,EAAG;AAC/B,aAAO,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,EAAG;AAAA,IACnC;AAEA,QAAI,QAAQ,IAAI,IAAI,GAAG,CAAC,MAAM,EAAG,MAAK,KAAK,MAAM,CAAC;AAAA,SAC7C;AACD;AACA,WAAK,KAAK,GAAG,KAAK;AAAA,IACtB;AAEA,QAAI,KAAK,EAAG,QAAO,IAAI;AACvB,QAAI,KAAK,EAAG,SAAQ,IAAI;AAAA,EAC5B;AACJ;AAEA,SAAS,KAAK,KAAK,GAAG,GAAG;AACrB,MAAI,MAAM,IAAI,CAAC;AACf,MAAI,CAAC,IAAI,IAAI,CAAC;AACd,MAAI,CAAC,IAAI;AACb;AAEA,SAAS,eAAe,GAAG,GAAG;AAC1B,SAAO,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AACpC;;;ACnDA,IAAqB,QAArB,MAA2B;AAAA,EACvB,YAAY,aAAa,GAAG;AAExB,SAAK,cAAc,KAAK,IAAI,GAAG,UAAU;AACzC,SAAK,cAAc,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK,cAAc,GAAG,CAAC;AAChE,SAAK,MAAM;AAAA,EACf;AAAA,EAEA,MAAM;AACF,WAAO,KAAK,KAAK,KAAK,MAAM,CAAC,CAAC;AAAA,EAClC;AAAA,EAEA,OAAO,MAAM;AACT,QAAI,OAAO,KAAK;AAChB,UAAM,SAAS,CAAC;AAEhB,QAAI,CAAC,WAAW,MAAM,IAAI,EAAG,QAAO;AAEpC,UAAM,SAAS,KAAK;AACpB,UAAM,gBAAgB,CAAC;AAEvB,WAAO,MAAM;AACT,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,cAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,cAAM,YAAY,KAAK,OAAO,OAAO,KAAK,IAAI;AAE9C,YAAI,WAAW,MAAM,SAAS,GAAG;AAC7B,cAAI,KAAK,KAAM,QAAO,KAAK,KAAK;AAAA,mBACvB,SAAS,MAAM,SAAS,EAAG,MAAK,KAAK,OAAO,MAAM;AAAA,cACtD,eAAc,KAAK,KAAK;AAAA,QACjC;AAAA,MACJ;AACA,aAAO,cAAc,IAAI;AAAA,IAC7B;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,SAAS,MAAM;AACX,QAAI,OAAO,KAAK;AAEhB,QAAI,CAAC,WAAW,MAAM,IAAI,EAAG,QAAO;AAEpC,UAAM,gBAAgB,CAAC;AACvB,WAAO,MAAM;AACT,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,cAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,cAAM,YAAY,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI;AAEnD,YAAI,WAAW,MAAM,SAAS,GAAG;AAC7B,cAAI,KAAK,QAAQ,SAAS,MAAM,SAAS,EAAG,QAAO;AACnD,wBAAc,KAAK,KAAK;AAAA,QAC5B;AAAA,MACJ;AACA,aAAO,cAAc,IAAI;AAAA,IAC7B;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,KAAK,MAAM;AACP,QAAI,EAAE,QAAQ,KAAK,QAAS,QAAO;AAEnC,QAAI,KAAK,SAAS,KAAK,aAAa;AAChC,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,aAAK,OAAO,KAAK,CAAC,CAAC;AAAA,MACvB;AACA,aAAO;AAAA,IACX;AAGA,QAAI,OAAO,KAAK,OAAO,KAAK,MAAM,GAAG,GAAG,KAAK,SAAS,GAAG,CAAC;AAE1D,QAAI,CAAC,KAAK,KAAK,SAAS,QAAQ;AAE5B,WAAK,OAAO;AAAA,IAEhB,WAAW,KAAK,KAAK,WAAW,KAAK,QAAQ;AAEzC,WAAK,WAAW,KAAK,MAAM,IAAI;AAAA,IAEnC,OAAO;AACH,UAAI,KAAK,KAAK,SAAS,KAAK,QAAQ;AAEhC,cAAM,UAAU,KAAK;AACrB,aAAK,OAAO;AACZ,eAAO;AAAA,MACX;AAGA,WAAK,QAAQ,MAAM,KAAK,KAAK,SAAS,KAAK,SAAS,GAAG,IAAI;AAAA,IAC/D;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,OAAO,MAAM;AACT,QAAI,KAAM,MAAK,QAAQ,MAAM,KAAK,KAAK,SAAS,CAAC;AACjD,WAAO;AAAA,EACX;AAAA,EAEA,QAAQ;AACJ,SAAK,OAAO,WAAW,CAAC,CAAC;AACzB,WAAO;AAAA,EACX;AAAA,EAEA,OAAO,MAAM,UAAU;AACnB,QAAI,CAAC,KAAM,QAAO;AAElB,QAAI,OAAO,KAAK;AAChB,UAAM,OAAO,KAAK,OAAO,IAAI;AAC7B,UAAM,OAAO,CAAC;AACd,UAAM,UAAU,CAAC;AACjB,QAAI,GAAG,QAAQ;AAGf,WAAO,QAAQ,KAAK,QAAQ;AAExB,UAAI,CAAC,MAAM;AACP,eAAO,KAAK,IAAI;AAChB,iBAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,YAAI,QAAQ,IAAI;AAChB,kBAAU;AAAA,MACd;AAEA,UAAI,KAAK,MAAM;AACX,cAAM,QAAQ,SAAS,MAAM,KAAK,UAAU,QAAQ;AAEpD,YAAI,UAAU,IAAI;AAEd,eAAK,SAAS,OAAO,OAAO,CAAC;AAC7B,eAAK,KAAK,IAAI;AACd,eAAK,UAAU,IAAI;AACnB,iBAAO;AAAA,QACX;AAAA,MACJ;AAEA,UAAI,CAAC,WAAW,CAAC,KAAK,QAAQ,SAAS,MAAM,IAAI,GAAG;AAChD,aAAK,KAAK,IAAI;AACd,gBAAQ,KAAK,CAAC;AACd,YAAI;AACJ,iBAAS;AACT,eAAO,KAAK,SAAS,CAAC;AAAA,MAE1B,WAAW,QAAQ;AACf;AACA,eAAO,OAAO,SAAS,CAAC;AACxB,kBAAU;AAAA,MAEd,MAAO,QAAO;AAAA,IAClB;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,OAAO,MAAM;AAAE,WAAO;AAAA,EAAM;AAAA,EAE5B,YAAY,GAAG,GAAG;AAAE,WAAO,EAAE,OAAO,EAAE;AAAA,EAAM;AAAA,EAC5C,YAAY,GAAG,GAAG;AAAE,WAAO,EAAE,OAAO,EAAE;AAAA,EAAM;AAAA,EAE5C,SAAS;AAAE,WAAO,KAAK;AAAA,EAAM;AAAA,EAE7B,SAAS,MAAM;AACX,SAAK,OAAO;AACZ,WAAO;AAAA,EACX;AAAA,EAEA,KAAK,MAAM,QAAQ;AACf,UAAM,gBAAgB,CAAC;AACvB,WAAO,MAAM;AACT,UAAI,KAAK,KAAM,QAAO,KAAK,GAAG,KAAK,QAAQ;AAAA,UACtC,eAAc,KAAK,GAAG,KAAK,QAAQ;AAExC,aAAO,cAAc,IAAI;AAAA,IAC7B;AACA,WAAO;AAAA,EACX;AAAA,EAEA,OAAO,OAAO,MAAM,OAAO,QAAQ;AAE/B,UAAM,IAAI,QAAQ,OAAO;AACzB,QAAI,IAAI,KAAK;AACb,QAAI;AAEJ,QAAI,KAAK,GAAG;AAER,aAAO,WAAW,MAAM,MAAM,MAAM,QAAQ,CAAC,CAAC;AAC9C,eAAS,MAAM,KAAK,MAAM;AAC1B,aAAO;AAAA,IACX;AAEA,QAAI,CAAC,QAAQ;AAET,eAAS,KAAK,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AAG5C,UAAI,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC;AAAA,IAC7C;AAEA,WAAO,WAAW,CAAC,CAAC;AACpB,SAAK,OAAO;AACZ,SAAK,SAAS;AAId,UAAM,KAAK,KAAK,KAAK,IAAI,CAAC;AAC1B,UAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,CAAC;AAEtC,gBAAY,OAAO,MAAM,OAAO,IAAI,KAAK,WAAW;AAEpD,aAAS,IAAI,MAAM,KAAK,OAAO,KAAK,IAAI;AAEpC,YAAM,SAAS,KAAK,IAAI,IAAI,KAAK,GAAG,KAAK;AAEzC,kBAAY,OAAO,GAAG,QAAQ,IAAI,KAAK,WAAW;AAElD,eAAS,IAAI,GAAG,KAAK,QAAQ,KAAK,IAAI;AAElC,cAAM,SAAS,KAAK,IAAI,IAAI,KAAK,GAAG,MAAM;AAG1C,aAAK,SAAS,KAAK,KAAK,OAAO,OAAO,GAAG,QAAQ,SAAS,CAAC,CAAC;AAAA,MAChE;AAAA,IACJ;AAEA,aAAS,MAAM,KAAK,MAAM;AAE1B,WAAO;AAAA,EACX;AAAA,EAEA,eAAe,MAAM,MAAM,OAAO,MAAM;AACpC,WAAO,MAAM;AACT,WAAK,KAAK,IAAI;AAEd,UAAI,KAAK,QAAQ,KAAK,SAAS,MAAM,MAAO;AAE5C,UAAI,UAAU;AACd,UAAI,iBAAiB;AACrB,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,cAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,cAAM,OAAO,SAAS,KAAK;AAC3B,cAAM,cAAc,aAAa,MAAM,KAAK,IAAI;AAGhD,YAAI,cAAc,gBAAgB;AAC9B,2BAAiB;AACjB,oBAAU,OAAO,UAAU,OAAO;AAClC,uBAAa;AAAA,QAEjB,WAAW,gBAAgB,gBAAgB;AAEvC,cAAI,OAAO,SAAS;AAChB,sBAAU;AACV,yBAAa;AAAA,UACjB;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO,cAAc,KAAK,SAAS,CAAC;AAAA,IACxC;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,QAAQ,MAAM,OAAO,QAAQ;AACzB,UAAM,OAAO,SAAS,OAAO,KAAK,OAAO,IAAI;AAC7C,UAAM,aAAa,CAAC;AAGpB,UAAM,OAAO,KAAK,eAAe,MAAM,KAAK,MAAM,OAAO,UAAU;AAGnE,SAAK,SAAS,KAAK,IAAI;AACvB,WAAO,MAAM,IAAI;AAGjB,WAAO,SAAS,GAAG;AACf,UAAI,WAAW,KAAK,EAAE,SAAS,SAAS,KAAK,aAAa;AACtD,aAAK,OAAO,YAAY,KAAK;AAC7B;AAAA,MACJ,MAAO;AAAA,IACX;AAGA,SAAK,oBAAoB,MAAM,YAAY,KAAK;AAAA,EACpD;AAAA;AAAA,EAGA,OAAO,YAAY,OAAO;AACtB,UAAM,OAAO,WAAW,KAAK;AAC7B,UAAM,IAAI,KAAK,SAAS;AACxB,UAAM,IAAI,KAAK;AAEf,SAAK,iBAAiB,MAAM,GAAG,CAAC;AAEhC,UAAM,aAAa,KAAK,kBAAkB,MAAM,GAAG,CAAC;AAEpD,UAAM,UAAU,WAAW,KAAK,SAAS,OAAO,YAAY,KAAK,SAAS,SAAS,UAAU,CAAC;AAC9F,YAAQ,SAAS,KAAK;AACtB,YAAQ,OAAO,KAAK;AAEpB,aAAS,MAAM,KAAK,MAAM;AAC1B,aAAS,SAAS,KAAK,MAAM;AAE7B,QAAI,MAAO,YAAW,QAAQ,CAAC,EAAE,SAAS,KAAK,OAAO;AAAA,QACjD,MAAK,WAAW,MAAM,OAAO;AAAA,EACtC;AAAA,EAEA,WAAW,MAAM,SAAS;AAEtB,SAAK,OAAO,WAAW,CAAC,MAAM,OAAO,CAAC;AACtC,SAAK,KAAK,SAAS,KAAK,SAAS;AACjC,SAAK,KAAK,OAAO;AACjB,aAAS,KAAK,MAAM,KAAK,MAAM;AAAA,EACnC;AAAA,EAEA,kBAAkB,MAAM,GAAG,GAAG;AAC1B,QAAI;AACJ,QAAI,aAAa;AACjB,QAAI,UAAU;AAEd,aAAS,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK;AAC7B,YAAM,QAAQ,SAAS,MAAM,GAAG,GAAG,KAAK,MAAM;AAC9C,YAAM,QAAQ,SAAS,MAAM,GAAG,GAAG,KAAK,MAAM;AAE9C,YAAM,UAAU,iBAAiB,OAAO,KAAK;AAC7C,YAAM,OAAO,SAAS,KAAK,IAAI,SAAS,KAAK;AAG7C,UAAI,UAAU,YAAY;AACtB,qBAAa;AACb,gBAAQ;AAER,kBAAU,OAAO,UAAU,OAAO;AAAA,MAEtC,WAAW,YAAY,YAAY;AAE/B,YAAI,OAAO,SAAS;AAChB,oBAAU;AACV,kBAAQ;AAAA,QACZ;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO,SAAS,IAAI;AAAA,EACxB;AAAA;AAAA,EAGA,iBAAiB,MAAM,GAAG,GAAG;AACzB,UAAM,cAAc,KAAK,OAAO,KAAK,cAAc;AACnD,UAAM,cAAc,KAAK,OAAO,KAAK,cAAc;AACnD,UAAM,UAAU,KAAK,eAAe,MAAM,GAAG,GAAG,WAAW;AAC3D,UAAM,UAAU,KAAK,eAAe,MAAM,GAAG,GAAG,WAAW;AAI3D,QAAI,UAAU,QAAS,MAAK,SAAS,KAAK,WAAW;AAAA,EACzD;AAAA;AAAA,EAGA,eAAe,MAAM,GAAG,GAAG,SAAS;AAChC,SAAK,SAAS,KAAK,OAAO;AAE1B,UAAM,SAAS,KAAK;AACpB,UAAM,WAAW,SAAS,MAAM,GAAG,GAAG,MAAM;AAC5C,UAAM,YAAY,SAAS,MAAM,IAAI,GAAG,GAAG,MAAM;AACjD,QAAI,SAAS,WAAW,QAAQ,IAAI,WAAW,SAAS;AAExD,aAAS,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AAC5B,YAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,aAAO,UAAU,KAAK,OAAO,OAAO,KAAK,IAAI,KAAK;AAClD,gBAAU,WAAW,QAAQ;AAAA,IACjC;AAEA,aAAS,IAAI,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK;AACjC,YAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,aAAO,WAAW,KAAK,OAAO,OAAO,KAAK,IAAI,KAAK;AACnD,gBAAU,WAAW,SAAS;AAAA,IAClC;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,oBAAoB,MAAM,MAAM,OAAO;AAEnC,aAAS,IAAI,OAAO,KAAK,GAAG,KAAK;AAC7B,aAAO,KAAK,CAAC,GAAG,IAAI;AAAA,IACxB;AAAA,EACJ;AAAA,EAEA,UAAU,MAAM;AAEZ,aAAS,IAAI,KAAK,SAAS,GAAG,UAAU,KAAK,GAAG,KAAK;AACjD,UAAI,KAAK,CAAC,EAAE,SAAS,WAAW,GAAG;AAC/B,YAAI,IAAI,GAAG;AACP,qBAAW,KAAK,IAAI,CAAC,EAAE;AACvB,mBAAS,OAAO,SAAS,QAAQ,KAAK,CAAC,CAAC,GAAG,CAAC;AAAA,QAEhD,MAAO,MAAK,MAAM;AAAA,MAEtB,MAAO,UAAS,KAAK,CAAC,GAAG,KAAK,MAAM;AAAA,IACxC;AAAA,EACJ;AACJ;AAEA,SAAS,SAAS,MAAM,OAAO,UAAU;AACrC,MAAI,CAAC,SAAU,QAAO,MAAM,QAAQ,IAAI;AAExC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,QAAI,SAAS,MAAM,MAAM,CAAC,CAAC,EAAG,QAAO;AAAA,EACzC;AACA,SAAO;AACX;AAGA,SAAS,SAAS,MAAM,QAAQ;AAC5B,WAAS,MAAM,GAAG,KAAK,SAAS,QAAQ,QAAQ,IAAI;AACxD;AAGA,SAAS,SAAS,MAAM,GAAG,GAAG,QAAQ,UAAU;AAC5C,MAAI,CAAC,SAAU,YAAW,WAAW,IAAI;AACzC,WAAS,OAAO;AAChB,WAAS,OAAO;AAChB,WAAS,OAAO;AAChB,WAAS,OAAO;AAEhB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,UAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,WAAO,UAAU,KAAK,OAAO,OAAO,KAAK,IAAI,KAAK;AAAA,EACtD;AAEA,SAAO;AACX;AAEA,SAAS,OAAO,GAAG,GAAG;AAClB,IAAE,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AAChC,IAAE,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AAChC,IAAE,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AAChC,IAAE,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AAChC,SAAO;AACX;AAEA,SAAS,gBAAgB,GAAG,GAAG;AAAE,SAAO,EAAE,OAAO,EAAE;AAAM;AACzD,SAAS,gBAAgB,GAAG,GAAG;AAAE,SAAO,EAAE,OAAO,EAAE;AAAM;AAEzD,SAAS,SAAS,GAAK;AAAE,UAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE;AAAO;AACvE,SAAS,WAAW,GAAG;AAAE,SAAQ,EAAE,OAAO,EAAE,QAAS,EAAE,OAAO,EAAE;AAAO;AAEvE,SAAS,aAAa,GAAG,GAAG;AACxB,UAAQ,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI,IAAI,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI,MAClD,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI,IAAI,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AAC9D;AAEA,SAAS,iBAAiB,GAAG,GAAG;AAC5B,QAAM,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AACpC,QAAM,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AACpC,QAAM,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AACpC,QAAM,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AAEpC,SAAO,KAAK,IAAI,GAAG,OAAO,IAAI,IACvB,KAAK,IAAI,GAAG,OAAO,IAAI;AAClC;AAEA,SAAS,SAAS,GAAG,GAAG;AACpB,SAAO,EAAE,QAAQ,EAAE,QACZ,EAAE,QAAQ,EAAE,QACZ,EAAE,QAAQ,EAAE,QACZ,EAAE,QAAQ,EAAE;AACvB;AAEA,SAAS,WAAW,GAAG,GAAG;AACtB,SAAO,EAAE,QAAQ,EAAE,QACZ,EAAE,QAAQ,EAAE,QACZ,EAAE,QAAQ,EAAE,QACZ,EAAE,QAAQ,EAAE;AACvB;AAEA,SAAS,WAAW,UAAU;AAC1B,SAAO;AAAA,IACH;AAAA,IACA,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACV;AACJ;AAKA,SAAS,YAAY,KAAK,MAAM,OAAO,GAAG,SAAS;AAC/C,QAAM,QAAQ,CAAC,MAAM,KAAK;AAE1B,SAAO,MAAM,QAAQ;AACjB,YAAQ,MAAM,IAAI;AAClB,WAAO,MAAM,IAAI;AAEjB,QAAI,QAAQ,QAAQ,EAAG;AAEvB,UAAM,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,IAAI,CAAC,IAAI;AACvD,gBAAY,KAAK,KAAK,MAAM,OAAO,OAAO;AAE1C,UAAM,KAAK,MAAM,KAAK,KAAK,KAAK;AAAA,EACpC;AACJ;", "names": []}