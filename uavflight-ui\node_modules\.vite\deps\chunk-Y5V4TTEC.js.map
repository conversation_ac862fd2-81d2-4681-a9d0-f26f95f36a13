{"version": 3, "sources": ["../../ol/Overlay.js"], "sourcesContent": ["/**\n * @module ol/Overlay\n */\nimport BaseObject from './Object.js';\nimport MapEventType from './MapEventType.js';\nimport {CLASS_SELECTABLE} from './css.js';\nimport {containsExtent} from './extent.js';\nimport {listen, unlistenBy<PERSON>ey} from './events.js';\nimport {outerHeight, outerWidth, removeChildren, removeNode} from './dom.js';\n\n/**\n * @typedef {'bottom-left' | 'bottom-center' | 'bottom-right' | 'center-left' | 'center-center' | 'center-right' | 'top-left' | 'top-center' | 'top-right'} Positioning\n * The overlay position: `'bottom-left'`, `'bottom-center'`,  `'bottom-right'`,\n * `'center-left'`, `'center-center'`, `'center-right'`, `'top-left'`,\n * `'top-center'`, or `'top-right'`.\n */\n\n/**\n * @typedef {Object} Options\n * @property {number|string} [id] Set the overlay id. The overlay id can be used\n * with the {@link module:ol/Map~Map#getOverlayById} method.\n * @property {HTMLElement} [element] The overlay element.\n * @property {Array<number>} [offset=[0, 0]] Offsets in pixels used when positioning\n * the overlay. The first element in the\n * array is the horizontal offset. A positive value shifts the overlay right.\n * The second element in the array is the vertical offset. A positive value\n * shifts the overlay down.\n * @property {import(\"./coordinate.js\").Coordinate} [position] The overlay position\n * in map projection.\n * @property {Positioning} [positioning='top-left'] Defines how\n * the overlay is actually positioned with respect to its `position` property.\n * Possible values are `'bottom-left'`, `'bottom-center'`, `'bottom-right'`,\n * `'center-left'`, `'center-center'`, `'center-right'`, `'top-left'`,\n * `'top-center'`, and `'top-right'`.\n * @property {boolean} [stopEvent=true] Whether event propagation to the map\n * viewport should be stopped. If `true` the overlay is placed in the same\n * container as that of the controls (CSS class name\n * `ol-overlaycontainer-stopevent`); if `false` it is placed in the container\n * with CSS class name specified by the `className` property.\n * @property {boolean} [insertFirst=true] Whether the overlay is inserted first\n * in the overlay container, or appended. If the overlay is placed in the same\n * container as that of the controls (see the `stopEvent` option) you will\n * probably set `insertFirst` to `true` so the overlay is displayed below the\n * controls.\n * @property {PanIntoViewOptions|boolean} [autoPan=false] Pan the map when calling\n * `setPosition`, so that the overlay is entirely visible in the current viewport.\n * @property {string} [className='ol-overlay-container ol-selectable'] CSS class\n * name.\n */\n\n/**\n * @typedef {Object} PanOptions\n * @property {number} [duration=1000] The duration of the animation in\n * milliseconds.\n * @property {function(number):number} [easing] The easing function to use. Can\n * be one from {@link module:ol/easing} or a custom function.\n * Default is {@link module:ol/easing.inAndOut}.\n */\n\n/**\n * @typedef {Object} PanIntoViewOptions\n * @property {PanOptions} [animation={}] The animation parameters for the pan\n * @property {number} [margin=20] The margin (in pixels) between the\n * overlay and the borders of the map when panning into view.\n */\n\n/**\n * @enum {string}\n * @protected\n */\nconst Property = {\n  ELEMENT: 'element',\n  MAP: 'map',\n  OFFSET: 'offset',\n  POSITION: 'position',\n  POSITIONING: 'positioning',\n};\n\n/**\n * @typedef {import(\"./ObjectEventType\").Types|'change:element'|'change:map'|'change:offset'|'change:position'|\n *   'change:positioning'} OverlayObjectEventTypes\n */\n\n/***\n * @template Return\n * @typedef {import(\"./Observable\").OnSignature<import(\"./Observable\").EventTypes, import(\"./events/Event.js\").default, Return> &\n *   import(\"./Observable\").OnSignature<OverlayObjectEventTypes, import(\"./Object\").ObjectEvent, Return> &\n *   import(\"./Observable\").CombinedOnSignature<import(\"./Observable\").EventTypes|OverlayObjectEventTypes, Return>} OverlayOnSignature\n */\n\n/**\n * @classdesc\n * An element to be displayed over the map and attached to a single map\n * location.  Like {@link module:ol/control/Control~Control}, Overlays are\n * visible widgets. Unlike Controls, they are not in a fixed position on the\n * screen, but are tied to a geographical coordinate, so panning the map will\n * move an Overlay but not a Control.\n *\n * Example:\n *\n *     import Overlay from 'ol/Overlay.js';\n *\n *     // ...\n *     const popup = new Overlay({\n *       element: document.getElementById('popup'),\n *     });\n *     popup.setPosition(coordinate);\n *     map.addOverlay(popup);\n *\n * @api\n */\nclass Overlay extends BaseObject {\n  /**\n   * @param {Options} options Overlay options.\n   */\n  constructor(options) {\n    super();\n\n    /***\n     * @type {OverlayOnSignature<import(\"./events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {OverlayOnSignature<import(\"./events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {OverlayOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * @protected\n     * @type {Options}\n     */\n    this.options = options;\n\n    /**\n     * @protected\n     * @type {number|string|undefined}\n     */\n    this.id = options.id;\n\n    /**\n     * @protected\n     * @type {boolean}\n     */\n    this.insertFirst =\n      options.insertFirst !== undefined ? options.insertFirst : true;\n\n    /**\n     * @protected\n     * @type {boolean}\n     */\n    this.stopEvent = options.stopEvent !== undefined ? options.stopEvent : true;\n\n    /**\n     * @protected\n     * @type {HTMLElement}\n     */\n    this.element = document.createElement('div');\n    this.element.className =\n      options.className !== undefined\n        ? options.className\n        : 'ol-overlay-container ' + CLASS_SELECTABLE;\n    this.element.style.position = 'absolute';\n    this.element.style.pointerEvents = 'auto';\n\n    /**\n     * @protected\n     * @type {PanIntoViewOptions|undefined}\n     */\n    this.autoPan = options.autoPan === true ? {} : options.autoPan || undefined;\n\n    /**\n     * @protected\n     * @type {{transform_: string,\n     *         visible: boolean}}\n     */\n    this.rendered = {\n      transform_: '',\n      visible: true,\n    };\n\n    /**\n     * @protected\n     * @type {?import(\"./events.js\").EventsKey}\n     */\n    this.mapPostrenderListenerKey = null;\n\n    this.addChangeListener(Property.ELEMENT, this.handleElementChanged);\n    this.addChangeListener(Property.MAP, this.handleMapChanged);\n    this.addChangeListener(Property.OFFSET, this.handleOffsetChanged);\n    this.addChangeListener(Property.POSITION, this.handlePositionChanged);\n    this.addChangeListener(Property.POSITIONING, this.handlePositioningChanged);\n\n    if (options.element !== undefined) {\n      this.setElement(options.element);\n    }\n\n    this.setOffset(options.offset !== undefined ? options.offset : [0, 0]);\n\n    this.setPositioning(options.positioning || 'top-left');\n\n    if (options.position !== undefined) {\n      this.setPosition(options.position);\n    }\n  }\n\n  /**\n   * Get the DOM element of this overlay.\n   * @return {HTMLElement|undefined} The Element containing the overlay.\n   * @observable\n   * @api\n   */\n  getElement() {\n    return /** @type {HTMLElement|undefined} */ (this.get(Property.ELEMENT));\n  }\n\n  /**\n   * Get the overlay identifier which is set on constructor.\n   * @return {number|string|undefined} Id.\n   * @api\n   */\n  getId() {\n    return this.id;\n  }\n\n  /**\n   * Get the map associated with this overlay.\n   * @return {import(\"./Map.js\").default|null} The map that the\n   * overlay is part of.\n   * @observable\n   * @api\n   */\n  getMap() {\n    return /** @type {import(\"./Map.js\").default|null} */ (\n      this.get(Property.MAP) || null\n    );\n  }\n\n  /**\n   * Get the offset of this overlay.\n   * @return {Array<number>} The offset.\n   * @observable\n   * @api\n   */\n  getOffset() {\n    return /** @type {Array<number>} */ (this.get(Property.OFFSET));\n  }\n\n  /**\n   * Get the current position of this overlay.\n   * @return {import(\"./coordinate.js\").Coordinate|undefined} The spatial point that the overlay is\n   *     anchored at.\n   * @observable\n   * @api\n   */\n  getPosition() {\n    return /** @type {import(\"./coordinate.js\").Coordinate|undefined} */ (\n      this.get(Property.POSITION)\n    );\n  }\n\n  /**\n   * Get the current positioning of this overlay.\n   * @return {Positioning} How the overlay is positioned\n   *     relative to its point on the map.\n   * @observable\n   * @api\n   */\n  getPositioning() {\n    return /** @type {Positioning} */ (this.get(Property.POSITIONING));\n  }\n\n  /**\n   * @protected\n   */\n  handleElementChanged() {\n    removeChildren(this.element);\n    const element = this.getElement();\n    if (element) {\n      this.element.appendChild(element);\n    }\n  }\n\n  /**\n   * @protected\n   */\n  handleMapChanged() {\n    if (this.mapPostrenderListenerKey) {\n      removeNode(this.element);\n      unlistenByKey(this.mapPostrenderListenerKey);\n      this.mapPostrenderListenerKey = null;\n    }\n    const map = this.getMap();\n    if (map) {\n      this.mapPostrenderListenerKey = listen(\n        map,\n        MapEventType.POSTRENDER,\n        this.render,\n        this\n      );\n      this.updatePixelPosition();\n      const container = this.stopEvent\n        ? map.getOverlayContainerStopEvent()\n        : map.getOverlayContainer();\n      if (this.insertFirst) {\n        container.insertBefore(this.element, container.childNodes[0] || null);\n      } else {\n        container.appendChild(this.element);\n      }\n      this.performAutoPan();\n    }\n  }\n\n  /**\n   * @protected\n   */\n  render() {\n    this.updatePixelPosition();\n  }\n\n  /**\n   * @protected\n   */\n  handleOffsetChanged() {\n    this.updatePixelPosition();\n  }\n\n  /**\n   * @protected\n   */\n  handlePositionChanged() {\n    this.updatePixelPosition();\n    this.performAutoPan();\n  }\n\n  /**\n   * @protected\n   */\n  handlePositioningChanged() {\n    this.updatePixelPosition();\n  }\n\n  /**\n   * Set the DOM element to be associated with this overlay.\n   * @param {HTMLElement|undefined} element The Element containing the overlay.\n   * @observable\n   * @api\n   */\n  setElement(element) {\n    this.set(Property.ELEMENT, element);\n  }\n\n  /**\n   * Set the map to be associated with this overlay.\n   * @param {import(\"./Map.js\").default|null} map The map that the\n   * overlay is part of. Pass `null` to just remove the overlay from the current map.\n   * @observable\n   * @api\n   */\n  setMap(map) {\n    this.set(Property.MAP, map);\n  }\n\n  /**\n   * Set the offset for this overlay.\n   * @param {Array<number>} offset Offset.\n   * @observable\n   * @api\n   */\n  setOffset(offset) {\n    this.set(Property.OFFSET, offset);\n  }\n\n  /**\n   * Set the position for this overlay. If the position is `undefined` the\n   * overlay is hidden.\n   * @param {import(\"./coordinate.js\").Coordinate|undefined} position The spatial point that the overlay\n   *     is anchored at.\n   * @observable\n   * @api\n   */\n  setPosition(position) {\n    this.set(Property.POSITION, position);\n  }\n\n  /**\n   * Pan the map so that the overlay is entirely visible in the current viewport\n   * (if necessary) using the configured autoPan parameters\n   * @protected\n   */\n  performAutoPan() {\n    if (this.autoPan) {\n      this.panIntoView(this.autoPan);\n    }\n  }\n\n  /**\n   * Pan the map so that the overlay is entirely visible in the current viewport\n   * (if necessary).\n   * @param {PanIntoViewOptions} [panIntoViewOptions] Options for the pan action\n   * @api\n   */\n  panIntoView(panIntoViewOptions) {\n    const map = this.getMap();\n\n    if (!map || !map.getTargetElement() || !this.get(Property.POSITION)) {\n      return;\n    }\n\n    const mapRect = this.getRect(map.getTargetElement(), map.getSize());\n    const element = this.getElement();\n    const overlayRect = this.getRect(element, [\n      outerWidth(element),\n      outerHeight(element),\n    ]);\n\n    panIntoViewOptions = panIntoViewOptions || {};\n\n    const myMargin =\n      panIntoViewOptions.margin === undefined ? 20 : panIntoViewOptions.margin;\n    if (!containsExtent(mapRect, overlayRect)) {\n      // the overlay is not completely inside the viewport, so pan the map\n      const offsetLeft = overlayRect[0] - mapRect[0];\n      const offsetRight = mapRect[2] - overlayRect[2];\n      const offsetTop = overlayRect[1] - mapRect[1];\n      const offsetBottom = mapRect[3] - overlayRect[3];\n\n      const delta = [0, 0];\n      if (offsetLeft < 0) {\n        // move map to the left\n        delta[0] = offsetLeft - myMargin;\n      } else if (offsetRight < 0) {\n        // move map to the right\n        delta[0] = Math.abs(offsetRight) + myMargin;\n      }\n      if (offsetTop < 0) {\n        // move map up\n        delta[1] = offsetTop - myMargin;\n      } else if (offsetBottom < 0) {\n        // move map down\n        delta[1] = Math.abs(offsetBottom) + myMargin;\n      }\n\n      if (delta[0] !== 0 || delta[1] !== 0) {\n        const center = /** @type {import(\"./coordinate.js\").Coordinate} */ (\n          map.getView().getCenterInternal()\n        );\n        const centerPx = map.getPixelFromCoordinateInternal(center);\n        if (!centerPx) {\n          return;\n        }\n        const newCenterPx = [centerPx[0] + delta[0], centerPx[1] + delta[1]];\n\n        const panOptions = panIntoViewOptions.animation || {};\n        map.getView().animateInternal({\n          center: map.getCoordinateFromPixelInternal(newCenterPx),\n          duration: panOptions.duration,\n          easing: panOptions.easing,\n        });\n      }\n    }\n  }\n\n  /**\n   * Get the extent of an element relative to the document\n   * @param {HTMLElement} element The element.\n   * @param {import(\"./size.js\").Size} size The size of the element.\n   * @return {import(\"./extent.js\").Extent} The extent.\n   * @protected\n   */\n  getRect(element, size) {\n    const box = element.getBoundingClientRect();\n    const offsetX = box.left + window.pageXOffset;\n    const offsetY = box.top + window.pageYOffset;\n    return [offsetX, offsetY, offsetX + size[0], offsetY + size[1]];\n  }\n\n  /**\n   * Set the positioning for this overlay.\n   * @param {Positioning} positioning how the overlay is\n   *     positioned relative to its point on the map.\n   * @observable\n   * @api\n   */\n  setPositioning(positioning) {\n    this.set(Property.POSITIONING, positioning);\n  }\n\n  /**\n   * Modify the visibility of the element.\n   * @param {boolean} visible Element visibility.\n   * @protected\n   */\n  setVisible(visible) {\n    if (this.rendered.visible !== visible) {\n      this.element.style.display = visible ? '' : 'none';\n      this.rendered.visible = visible;\n    }\n  }\n\n  /**\n   * Update pixel position.\n   * @protected\n   */\n  updatePixelPosition() {\n    const map = this.getMap();\n    const position = this.getPosition();\n    if (!map || !map.isRendered() || !position) {\n      this.setVisible(false);\n      return;\n    }\n\n    const pixel = map.getPixelFromCoordinate(position);\n    const mapSize = map.getSize();\n    this.updateRenderedPosition(pixel, mapSize);\n  }\n\n  /**\n   * @param {import(\"./pixel.js\").Pixel} pixel The pixel location.\n   * @param {import(\"./size.js\").Size|undefined} mapSize The map size.\n   * @protected\n   */\n  updateRenderedPosition(pixel, mapSize) {\n    const style = this.element.style;\n    const offset = this.getOffset();\n\n    const positioning = this.getPositioning();\n\n    this.setVisible(true);\n\n    const x = Math.round(pixel[0] + offset[0]) + 'px';\n    const y = Math.round(pixel[1] + offset[1]) + 'px';\n    let posX = '0%';\n    let posY = '0%';\n    if (\n      positioning == 'bottom-right' ||\n      positioning == 'center-right' ||\n      positioning == 'top-right'\n    ) {\n      posX = '-100%';\n    } else if (\n      positioning == 'bottom-center' ||\n      positioning == 'center-center' ||\n      positioning == 'top-center'\n    ) {\n      posX = '-50%';\n    }\n    if (\n      positioning == 'bottom-left' ||\n      positioning == 'bottom-center' ||\n      positioning == 'bottom-right'\n    ) {\n      posY = '-100%';\n    } else if (\n      positioning == 'center-left' ||\n      positioning == 'center-center' ||\n      positioning == 'center-right'\n    ) {\n      posY = '-50%';\n    }\n    const transform = `translate(${posX}, ${posY}) translate(${x}, ${y})`;\n    if (this.rendered.transform_ != transform) {\n      this.rendered.transform_ = transform;\n      style.transform = transform;\n    }\n  }\n\n  /**\n   * returns the options this Overlay has been created with\n   * @return {Options} overlay options\n   */\n  getOptions() {\n    return this.options;\n  }\n}\n\nexport default Overlay;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAsEA,IAAM,WAAW;AAAA,EACf,SAAS;AAAA,EACT,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,aAAa;AACf;AAmCA,IAAM,UAAN,cAAsB,eAAW;AAAA;AAAA;AAAA;AAAA,EAI/B,YAAY,SAAS;AACnB,UAAM;AAKN,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,SAAK,UAAU;AAMf,SAAK,KAAK,QAAQ;AAMlB,SAAK,cACH,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAM5D,SAAK,YAAY,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAMvE,SAAK,UAAU,SAAS,cAAc,KAAK;AAC3C,SAAK,QAAQ,YACX,QAAQ,cAAc,SAClB,QAAQ,YACR,0BAA0B;AAChC,SAAK,QAAQ,MAAM,WAAW;AAC9B,SAAK,QAAQ,MAAM,gBAAgB;AAMnC,SAAK,UAAU,QAAQ,YAAY,OAAO,CAAC,IAAI,QAAQ,WAAW;AAOlE,SAAK,WAAW;AAAA,MACd,YAAY;AAAA,MACZ,SAAS;AAAA,IACX;AAMA,SAAK,2BAA2B;AAEhC,SAAK,kBAAkB,SAAS,SAAS,KAAK,oBAAoB;AAClE,SAAK,kBAAkB,SAAS,KAAK,KAAK,gBAAgB;AAC1D,SAAK,kBAAkB,SAAS,QAAQ,KAAK,mBAAmB;AAChE,SAAK,kBAAkB,SAAS,UAAU,KAAK,qBAAqB;AACpE,SAAK,kBAAkB,SAAS,aAAa,KAAK,wBAAwB;AAE1E,QAAI,QAAQ,YAAY,QAAW;AACjC,WAAK,WAAW,QAAQ,OAAO;AAAA,IACjC;AAEA,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC;AAErE,SAAK,eAAe,QAAQ,eAAe,UAAU;AAErD,QAAI,QAAQ,aAAa,QAAW;AAClC,WAAK,YAAY,QAAQ,QAAQ;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa;AACX;AAAA;AAAA,MAA6C,KAAK,IAAI,SAAS,OAAO;AAAA;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS;AACP;AAAA;AAAA,MACE,KAAK,IAAI,SAAS,GAAG,KAAK;AAAA;AAAA,EAE9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV;AAAA;AAAA,MAAqC,KAAK,IAAI,SAAS,MAAM;AAAA;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc;AACZ;AAAA;AAAA,MACE,KAAK,IAAI,SAAS,QAAQ;AAAA;AAAA,EAE9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB;AACf;AAAA;AAAA,MAAmC,KAAK,IAAI,SAAS,WAAW;AAAA;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,mBAAe,KAAK,OAAO;AAC3B,UAAM,UAAU,KAAK,WAAW;AAChC,QAAI,SAAS;AACX,WAAK,QAAQ,YAAY,OAAO;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,QAAI,KAAK,0BAA0B;AACjC,iBAAW,KAAK,OAAO;AACvB,oBAAc,KAAK,wBAAwB;AAC3C,WAAK,2BAA2B;AAAA,IAClC;AACA,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,KAAK;AACP,WAAK,2BAA2B;AAAA,QAC9B;AAAA,QACA,qBAAa;AAAA,QACb,KAAK;AAAA,QACL;AAAA,MACF;AACA,WAAK,oBAAoB;AACzB,YAAM,YAAY,KAAK,YACnB,IAAI,6BAA6B,IACjC,IAAI,oBAAoB;AAC5B,UAAI,KAAK,aAAa;AACpB,kBAAU,aAAa,KAAK,SAAS,UAAU,WAAW,CAAC,KAAK,IAAI;AAAA,MACtE,OAAO;AACL,kBAAU,YAAY,KAAK,OAAO;AAAA,MACpC;AACA,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB;AACpB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,SAAK,oBAAoB;AACzB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,2BAA2B;AACzB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAClB,SAAK,IAAI,SAAS,SAAS,OAAO;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,KAAK;AACV,SAAK,IAAI,SAAS,KAAK,GAAG;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,SAAK,IAAI,SAAS,QAAQ,MAAM;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,UAAU;AACpB,SAAK,IAAI,SAAS,UAAU,QAAQ;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,QAAI,KAAK,SAAS;AAChB,WAAK,YAAY,KAAK,OAAO;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,oBAAoB;AAC9B,UAAM,MAAM,KAAK,OAAO;AAExB,QAAI,CAAC,OAAO,CAAC,IAAI,iBAAiB,KAAK,CAAC,KAAK,IAAI,SAAS,QAAQ,GAAG;AACnE;AAAA,IACF;AAEA,UAAM,UAAU,KAAK,QAAQ,IAAI,iBAAiB,GAAG,IAAI,QAAQ,CAAC;AAClE,UAAM,UAAU,KAAK,WAAW;AAChC,UAAM,cAAc,KAAK,QAAQ,SAAS;AAAA,MACxC,WAAW,OAAO;AAAA,MAClB,YAAY,OAAO;AAAA,IACrB,CAAC;AAED,yBAAqB,sBAAsB,CAAC;AAE5C,UAAM,WACJ,mBAAmB,WAAW,SAAY,KAAK,mBAAmB;AACpE,QAAI,CAAC,eAAe,SAAS,WAAW,GAAG;AAEzC,YAAM,aAAa,YAAY,CAAC,IAAI,QAAQ,CAAC;AAC7C,YAAM,cAAc,QAAQ,CAAC,IAAI,YAAY,CAAC;AAC9C,YAAM,YAAY,YAAY,CAAC,IAAI,QAAQ,CAAC;AAC5C,YAAM,eAAe,QAAQ,CAAC,IAAI,YAAY,CAAC;AAE/C,YAAM,QAAQ,CAAC,GAAG,CAAC;AACnB,UAAI,aAAa,GAAG;AAElB,cAAM,CAAC,IAAI,aAAa;AAAA,MAC1B,WAAW,cAAc,GAAG;AAE1B,cAAM,CAAC,IAAI,KAAK,IAAI,WAAW,IAAI;AAAA,MACrC;AACA,UAAI,YAAY,GAAG;AAEjB,cAAM,CAAC,IAAI,YAAY;AAAA,MACzB,WAAW,eAAe,GAAG;AAE3B,cAAM,CAAC,IAAI,KAAK,IAAI,YAAY,IAAI;AAAA,MACtC;AAEA,UAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,GAAG;AACpC,cAAM;AAAA;AAAA,UACJ,IAAI,QAAQ,EAAE,kBAAkB;AAAA;AAElC,cAAM,WAAW,IAAI,+BAA+B,MAAM;AAC1D,YAAI,CAAC,UAAU;AACb;AAAA,QACF;AACA,cAAM,cAAc,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,GAAG,SAAS,CAAC,IAAI,MAAM,CAAC,CAAC;AAEnE,cAAM,aAAa,mBAAmB,aAAa,CAAC;AACpD,YAAI,QAAQ,EAAE,gBAAgB;AAAA,UAC5B,QAAQ,IAAI,+BAA+B,WAAW;AAAA,UACtD,UAAU,WAAW;AAAA,UACrB,QAAQ,WAAW;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,SAAS,MAAM;AACrB,UAAM,MAAM,QAAQ,sBAAsB;AAC1C,UAAM,UAAU,IAAI,OAAO,OAAO;AAClC,UAAM,UAAU,IAAI,MAAM,OAAO;AACjC,WAAO,CAAC,SAAS,SAAS,UAAU,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,CAAC;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,aAAa;AAC1B,SAAK,IAAI,SAAS,aAAa,WAAW;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,SAAS;AAClB,QAAI,KAAK,SAAS,YAAY,SAAS;AACrC,WAAK,QAAQ,MAAM,UAAU,UAAU,KAAK;AAC5C,WAAK,SAAS,UAAU;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB;AACpB,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,WAAW,KAAK,YAAY;AAClC,QAAI,CAAC,OAAO,CAAC,IAAI,WAAW,KAAK,CAAC,UAAU;AAC1C,WAAK,WAAW,KAAK;AACrB;AAAA,IACF;AAEA,UAAM,QAAQ,IAAI,uBAAuB,QAAQ;AACjD,UAAM,UAAU,IAAI,QAAQ;AAC5B,SAAK,uBAAuB,OAAO,OAAO;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,uBAAuB,OAAO,SAAS;AACrC,UAAM,QAAQ,KAAK,QAAQ;AAC3B,UAAM,SAAS,KAAK,UAAU;AAE9B,UAAM,cAAc,KAAK,eAAe;AAExC,SAAK,WAAW,IAAI;AAEpB,UAAM,IAAI,KAAK,MAAM,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI;AAC7C,UAAM,IAAI,KAAK,MAAM,MAAM,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI;AAC7C,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QACE,eAAe,kBACf,eAAe,kBACf,eAAe,aACf;AACA,aAAO;AAAA,IACT,WACE,eAAe,mBACf,eAAe,mBACf,eAAe,cACf;AACA,aAAO;AAAA,IACT;AACA,QACE,eAAe,iBACf,eAAe,mBACf,eAAe,gBACf;AACA,aAAO;AAAA,IACT,WACE,eAAe,iBACf,eAAe,mBACf,eAAe,gBACf;AACA,aAAO;AAAA,IACT;AACA,UAAM,YAAY,aAAa,IAAI,KAAK,IAAI,eAAe,CAAC,KAAK,CAAC;AAClE,QAAI,KAAK,SAAS,cAAc,WAAW;AACzC,WAAK,SAAS,aAAa;AAC3B,YAAM,YAAY;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAO,kBAAQ;", "names": []}