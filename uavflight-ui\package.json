{"name": "uavflight-ui", "version": "3.8.2", "description": "PIGCLOUD微服务开发平台", "author": "uavflight4cloud", "license": "Apache-2.0", "type": "module", "scripts": {"dev": "vite --force", "build": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --mode production", "build:docker": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --outDir ./docker/dist/", "lint:eslint": "eslint --fix --ext .js,.cjs,.mjs,.ts,.vue ./src", "prettier": "prettier --write ."}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "autoprefixer": "10.4.20", "axios": "1.7.7", "cesium-mvt-imagery-provider": "^1.4.1", "chardet": "^2.1.0", "china-area-data": "^5.0.1", "codemirror": "5.65.18", "crypto-js": "4.2.0", "driver.js": "1.3.1", "echarts": "5.5.1", "element-plus": "2.8.6", "hls.js": "^1.6.0", "iconv-lite": "^0.6.3", "js-cookie": "3.0.5", "less": "^4.2.2", "mitt": "3.0.1", "nprogress": "0.2.0", "ol": "^7.5.1", "pinia": "^2.1.7", "postcss": "8.4.47", "proj4": "^2.19.10", "qs": "6.13.0", "screenfull": "6.0.2", "shapefile": "^0.6.6", "sm-crypto": "0.3.13", "sortablejs": "1.15.3", "splitpanes": "3.1.5", "tailwindcss": "3.4.14", "text-encoding": "^0.7.0", "videojs-markers": "^1.0.1", "vue": "3.5.2", "vue-clipboard3": "2.0.0", "vue-echarts": "7.0.3", "vue-i18n": "9.14.1", "vue-router": "4.4.5", "vue-video-player": "^5.0.1", "vuedraggable": "4.1.0"}, "devDependencies": {"@stagewise/toolbar-vue": "^0.1.2", "@swc/core": "1.6.13", "@types/node": "^20.0.0", "@types/nprogress": "0.2.0", "@types/sortablejs": "1.15.0", "@typescript-eslint/eslint-plugin": "8.13.0", "@typescript-eslint/parser": "8.13.0", "@vitejs/plugin-vue": "5.0.5", "@vue/compiler-sfc": "3.4.3", "cesium": "^1.100.0", "consola": "3.0.0", "cross-env": "7.0.3", "eslint": "9.14.0", "eslint-plugin-vue": "9.30.0", "glob": "9.3.5", "pinia-plugin-persist": "1.0.0", "prettier": "3.3.3", "sass": "^1.86.0", "terser": "5.31.1", "typescript": "5.6.3", "unplugin-auto-import": "0.18.0", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "^5.4.14", "vite-plugin-cesium": "^1.2.23", "vite-plugin-compression": "0.5.1", "vite-plugin-top-level-await": "1.4.1", "vue-eslint-parser": "9.4.3"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": "https://uavflight4cloud.com"}, "engines": {"node": ">=18.0.0", "npm": ">= 8.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus"], "repository": {"type": "git", "url": "https://gitee.com/log4j/uavflight-ui"}, "resolutions": {"@swc/core": "~1.6.13"}}