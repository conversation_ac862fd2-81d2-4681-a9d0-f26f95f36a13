<template>
  <div class="layer-manager">
    <!-- 图层列表切换标签 -->
    <div class="layer-tabs">
      <div 
        class="layer-tab" 
        :class="{ 'active': activeTab === 'loaded' }"
        @click="activeTab = 'loaded'"
      >
        已加载图层
      </div>
      <div 
        class="layer-tab" 
        :class="{ 'active': activeTab === 'unloaded' }"
        @click="activeTab = 'unloaded'"
      >
        未加载图层
      </div>
    </div>
    
    <!-- 搜索框 - 仅在未加载图层标签页显示 -->
    <div v-show="activeTab === 'unloaded'" class="search-container">
      <el-input
        v-model="searchQuery"
        placeholder="搜索图层"
        clearable
        size="small"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>
    
    <!-- 已加载图层列表 -->
    <div v-show="activeTab === 'loaded'" class="layer-list-container">
      <draggable 
        v-model="displayLayers" 
        item-key="id" 
        ghost-class="ghost"
        handle=".drag-handle"
        @end="onDragEnd"
        :animation="200"
        class="layer-list"
      >
        <template #item="{ element }">
          <div class="layer-item">
            <div class="layer-item-header">
              <el-icon class="drag-handle"><Rank /></el-icon>
              <div class="layer-info">
                <el-tooltip :content="element.name" placement="top" :show-after="500">
                  <span class="layer-name">{{ formatLoadedLayerName(element.name) }}</span>
                </el-tooltip>
                <div class="layer-type-indicator" :class="getLayerTypeClass(element.type, element.geometryType)">
                  <span class="layer-type-text">{{ getLayerTypeText(element.type, element.geometryType) }}</span>
                </div>
              </div>
              <div class="layer-actions">
                <el-switch
                  v-model="element.visible"
                  @change="toggleLayerVisibility(element)"
                  size="small"
                />
                <el-button 
                  type="danger" 
                  size="small" 
                  circle
                  @click="removeLayer(element)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </template>
      </draggable>
      <div v-if="displayLayers.length === 0" class="empty-list">
        暂无已加载图层
      </div>
    </div>
    
    <!-- 未加载图层列表 -->
    <div v-show="activeTab === 'unloaded'" class="layer-list-container">
      <!-- 按主题分类的图层 -->
      <div v-for="theme in availableThemes" :key="theme" class="theme-group">
        <div class="theme-header" @click="toggleTheme(theme)">
          <el-icon :class="{ 'rotate-icon': expandedThemes.includes(theme) }">
            <ArrowRight />
          </el-icon>
          <span>{{ theme }}</span>
        </div>
        
        <div v-show="expandedThemes.includes(theme)" class="theme-content">
          <div 
            v-for="layer in getFilteredLayersByTheme(theme)" 
            :key="layer.id" 
            class="layer-item theme-layer-item"
          >
            <div class="layer-item-header">
              <div class="layer-info">
                <el-tooltip :content="layer.name" placement="top" :show-after="500">
                  <span class="layer-name">{{ formatUnloadedLayerName(layer.name) }}</span>
                </el-tooltip>
                <div class="layer-type-indicator" :class="getLayerTypeClass(layer.type, layer.geometryType)">
                  <span class="layer-type-text">{{ getLayerTypeText(layer.type, layer.geometryType) }}</span>
                </div>
              </div>
              <div class="layer-actions">
                <el-button 
                  type="success" 
                  size="small" 
                  circle
                  @click="addLayer(layer.id)"
                >
                  <el-icon><Plus /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 无主题的图层 -->
      <div v-for="layer in filteredLayersWithoutTheme" :key="layer.id" class="layer-item">
        <div class="layer-item-header">
          <div class="layer-info">
            <el-tooltip :content="layer.name" placement="top" :show-after="500">
              <span class="layer-name">{{ formatUnloadedLayerName(layer.name) }}</span>
            </el-tooltip>
            <div class="layer-type-indicator" :class="getLayerTypeClass(layer.type, layer.geometryType)">
              <span class="layer-type-text">{{ getLayerTypeText(layer.type, layer.geometryType) }}</span>
            </div>
          </div>
          <div class="layer-actions">
            <el-button 
              type="success" 
              size="small" 
              circle
              @click="addLayer(layer.id)"
            >
              <el-icon><Plus /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
      
      <div v-if="filteredAvailableLayers.length === 0" class="empty-list">
        暂无匹配的未加载图层
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="LayerList">
import { ref, computed, onMounted, watch, defineExpose, defineComponent } from 'vue';
import { useMapLayerManagerStore } from '/@/stores/mapLayer';
import { ElMessage } from 'element-plus';
import { Delete, Rank, Plus, Search, ArrowRight } from '@element-plus/icons-vue';
import draggable from 'vuedraggable';

// 获取图层管理器实例
const layerManager = useMapLayerManagerStore();

// 当前激活的标签页
const activeTab = ref('loaded');

// 用于显示的图层列表（包含额外的UI状态）
const displayLayers = ref<any[]>([]);

// 搜索查询
const searchQuery = ref('');

// 展开的主题列表
const expandedThemes = ref<string[]>([]);

// 格式化已加载图层名称，最多显示5个字符
const formatLoadedLayerName = (name: string) => {
  if (name.length <= 5) {
    return name;
  }
  return name.substring(0, 5) + '..';
};

// 格式化未加载图层名称，最多显示8个字符
const formatUnloadedLayerName = (name: string) => {
  if (name.length <= 8) {
    return name;
  }
  return name.substring(0, 8) + '..';
};

// 获取所有可用的主题
const availableThemes = computed(() => {
  return layerManager.getAvailableThemes;
});

// 获取所有可用但未加载的图层（排除DEM类型图层）
const availableLayers = computed(() => {
  if (!layerManager.mapConfig) return [];
  
  return layerManager.mapConfig.layers.filter(layer => 
    !layerManager.isLayerLoaded(layer.id) && 
    layer.type !== 'terrain' // 排除DEM图层
  );
});

// 根据搜索过滤未加载图层
const filteredAvailableLayers = computed(() => {
  if (!searchQuery.value) {
    return availableLayers.value;
  }
  
  const query = searchQuery.value.toLowerCase();
  return availableLayers.value.filter(layer => 
    layer.name.toLowerCase().includes(query) || 
    layer.id.toLowerCase().includes(query)
  );
});

// 获取没有主题的过滤后图层
const filteredLayersWithoutTheme = computed(() => {
  return filteredAvailableLayers.value.filter(layer => !layer.theme);
});

// 根据主题获取过滤后的图层
const getFilteredLayersByTheme = (theme: string) => {
  return filteredAvailableLayers.value.filter(layer => layer.theme === theme);
};

// 切换主题的展开/折叠状态
const toggleTheme = (theme: string) => {
  const index = expandedThemes.value.indexOf(theme);
  if (index === -1) {
    expandedThemes.value.push(theme);
  } else {
    expandedThemes.value.splice(index, 1);
  }
};

// 获取图层类型的CSS类名
const getLayerTypeClass = (type: string, geometryType?: string) => {
  switch (type) {
    case 'raster':
      return 'layer-type-raster';
    case 'vector':
      if (geometryType) {
        if (geometryType.includes('Point')) return 'layer-type-point';
        if (geometryType.includes('Line')) return 'layer-type-line';
        if (geometryType.includes('Polygon')) return 'layer-type-polygon';
      }
      return 'layer-type-vector';
    case 'CustomPoint':
      return 'layer-type-point';
    default:
      return 'layer-type-other';
  }
};

// 获取图层类型的文本描述
const getLayerTypeText = (type: string, geometryType?: string) => {
  switch (type) {
    case 'raster':
      return '栅格';
    case 'vector':
      if (geometryType) {
        if (geometryType.includes('Point')) return '点';
        if (geometryType.includes('Line')) return '线';
        if (geometryType.includes('Polygon')) return '面';
      }
      return '矢量';
    case 'CustomPoint':
      return '点';
    default:
      return '其他';
  }
};

// 初始化图层列表
const initLayerList = () => {
  if (!layerManager.mapConfig) return;
  
  // 获取已加载的图层和顺序
  const loadedLayerIds = layerManager.loadedLayers;
  const layerOrder = layerManager.getLayerOrder;
  
  // 创建显示用的图层列表（排除DEM类型图层）
  const layersList = loadedLayerIds
    .map(id => {
      const layer = layerManager.getLayerById(id);
      if (!layer || layer.type === 'terrain') return null; // 排除DEM图层
      
      // 使用图层在顺序中的位置作为zIndex
      const orderIndex = layerOrder.indexOf(id);
      const zIndex = orderIndex !== -1 ? layerOrder.length - orderIndex : 0;
      
      return {
        id: layer.id,
        name: layer.name,
        type: layer.type,
        geometryType: layer.geometryType,
        visible: layer.active !== false, // 默认为true
        zIndex: zIndex // 使用顺序作为zIndex
      };
    })
    .filter(Boolean);
  
  // 按zIndex倒序排列，使索引大的在上面
  displayLayers.value = layersList.sort((a, b) => {
    // 确保a和b不为null
    if (a && b) {
      return b.zIndex - a.zIndex;
    }
    return 0;
  });
};

// 刷新图层列表
const refreshLayers = () => {
  initLayerList();
  ElMessage.success('图层列表已刷新');
};

// 切换图层可见性
const toggleLayerVisibility = (layer: any) => {
  if (layer.visible) {
    // 显示图层
    const layerConfig = layerManager.getLayerById(layer.id);
    if (layerConfig && layerConfig.layerInstance) {
      // 根据图层类型设置可见性
      if (layerConfig.type === 'raster' && layerConfig.layerInstance) {
        layerConfig.layerInstance.show = true;
      } else if (layerConfig.type === 'vector' && layerConfig.layerInstance) {
        if (layerConfig.layerInstance instanceof Promise) {
          layerConfig.layerInstance.then((dataSource: any) => {
            if (dataSource) {
              dataSource.show = true;
            }
          });
        } else {
          layerConfig.layerInstance.show = true;
        }
      } else if (layerConfig.type === 'CustomPoint' && layerConfig.layerInstance) {
        // 调用CesiumPointManager的setVisibility方法设置可见性
        if (typeof layerConfig.layerInstance.setVisibility === 'function') {
          layerConfig.layerInstance.setVisibility(true);
        }
      }
      
      ElMessage.success(`已显示图层: ${layer.name}`);
    } else {
      // 如果图层实例不存在，则重新加载
      layerManager.addLayer(layer.id);
      ElMessage.success(`已显示图层: ${layer.name}`);
    }
  } else {
    // 隐藏图层（不从已加载列表中移除）
    const layerConfig = layerManager.getLayerById(layer.id);
    if (layerConfig && layerConfig.layerInstance) {
      // 根据图层类型设置可见性
      if (layerConfig.type === 'raster' && layerConfig.layerInstance) {
        layerConfig.layerInstance.show = false;
      } else if (layerConfig.type === 'vector' && layerConfig.layerInstance) {
        if (layerConfig.layerInstance instanceof Promise) {
          layerConfig.layerInstance.then((dataSource: any) => {
            if (dataSource) {
              dataSource.show = false;
            }
          });
        } else {
          layerConfig.layerInstance.show = false;
        }
      } else if (layerConfig.type === 'CustomPoint' && layerConfig.layerInstance) {
        // 调用CesiumPointManager的setVisibility方法设置可见性
        if (typeof layerConfig.layerInstance.setVisibility === 'function') {
          layerConfig.layerInstance.setVisibility(false);
        }
      }
      
      ElMessage.success(`已隐藏图层: ${layer.name}`);
    }
  }
};

// 移除图层（从已加载列表移除，添加到未加载列表）
const removeLayer = (layer: any) => {
  layerManager.removeLayerFromMap(layer.id);
  
  // 从显示列表中移除
  const index = displayLayers.value.findIndex(l => l.id === layer.id);
  if (index !== -1) {
    displayLayers.value.splice(index, 1);
  }
  
  ElMessage.success(`已移除图层: ${layer.name}`);
  
  // 如果当前在已加载标签页且已加载列表为空，自动切换到未加载标签页
  if (activeTab.value === 'loaded' && displayLayers.value.length === 0) {
    activeTab.value = 'unloaded';
  }
};

// 添加图层（从未加载列表添加到已加载列表）
const addLayer = (layerId: string) => {
  if (!layerId) return;
  
  const result = layerManager.addLayer(layerId);
  if (result.success) {
    const layer = layerManager.getLayerById(layerId);
    if (layer) {
      // 添加到显示列表的顶部
      displayLayers.value.unshift({
        id: layer.id,
        name: layer.name,
        type: layer.type,
        geometryType: layer.geometryType,
        visible: true,
        zIndex: displayLayers.value.length // 新添加的图层zIndex最大
      });
      
      ElMessage.success(result.message);
      
      // 如果当前在未加载标签页且未加载列表为空，自动切换到已加载标签页
      if (activeTab.value === 'unloaded' && availableLayers.value.length === 0) {
        activeTab.value = 'loaded';
      }
    }
  } else {
    ElMessage.warning(result.message || '添加图层失败');
  }
};

// 处理拖拽结束事件
const onDragEnd = () => {
  // 保存所有图层的ID和可见性状态，用于后续恢复
  const allLayersState = displayLayers.value.map(layer => ({ 
    id: layer.id, 
    visible: layer.visible,
    type: layer.type
  }));
  
  // 获取栅格图层
  const rasterLayers = allLayersState.filter(layer => layer.type === 'raster');
  
  // 更新每个图层的zIndex
  displayLayers.value.forEach((layer, index) => {
    layer.zIndex = displayLayers.value.length - index - 1;
  });
  
  // 应用新的图层顺序
  const newOrder = displayLayers.value.map(layer => layer.id);
  
  // 对每个图层应用新的顺序
  newOrder.forEach((layerId, index) => {
    layerManager.reorderLayer(layerId, index);
  });
  
  // 延时处理，确保顺序调整完成
  setTimeout(() => {
    // 重新加载栅格图层
    rasterLayers.forEach(layer => {
      // 先移除图层
      layerManager.removeLayerFromMap(layer.id);
    });
    
    // 再重新添加所有栅格图层
    rasterLayers.forEach(layer => {
      const result = layerManager.addLayer(layer.id, true); // 使用force参数强制添加
      
      if (result.success) {
        // 找到对应的图层配置
        const layerConfig = layerManager.getLayerById(layer.id);
        if (layerConfig && layerConfig.layerInstance) {
          // 恢复原来的可见性状态
          layerConfig.layerInstance.show = layer.visible;
          // 确保active状态与visible状态一致
          layerConfig.active = layer.visible;
        }
      }
    });
    
    // 保存原始的可见性状态
    const visibilityMap = new Map();
    allLayersState.forEach(layer => {
      visibilityMap.set(layer.id, layer.visible);
    });
    
    // 暂存当前图层列表
    const currentDisplayLayers = [...displayLayers.value];
    
    // 重新初始化图层列表
    initLayerList();
    
    // 手动恢复每个图层的可见性状态（UI状态）
    displayLayers.value = displayLayers.value.map(layer => {
      if (visibilityMap.has(layer.id)) {
        return {
          ...layer,
          visible: visibilityMap.get(layer.id)
        };
      }
      return layer;
    });
    
    // 同步更新实际图层状态
    displayLayers.value.forEach(layer => {
      const layerConfig = layerManager.getLayerById(layer.id);
      if (layerConfig && layerConfig.layerInstance) {
        if (layerConfig.type === 'raster') {
          layerConfig.layerInstance.show = layer.visible;
          layerConfig.active = layer.visible;
        } else if (layerConfig.type === 'vector') {
          if (layerConfig.layerInstance instanceof Promise) {
            layerConfig.layerInstance.then((dataSource: any) => {
              if (dataSource) {
                dataSource.show = layer.visible;
              }
            });
          } else {
            layerConfig.layerInstance.show = layer.visible;
          }
          layerConfig.active = layer.visible;
        } else if (layerConfig.type === 'CustomPoint') {
          if (typeof layerConfig.layerInstance.setVisibility === 'function') {
            layerConfig.layerInstance.setVisibility(layer.visible);
          }
          layerConfig.active = layer.visible;
        }
      }
    });
  }, 300);
  
  ElMessage.success('图层顺序已更新');
};

// 监听图层加载状态变化
watch(() => layerManager.loadedLayers, () => {
  initLayerList();
}, { deep: true });

// 组件挂载时初始化图层列表
onMounted(() => {
  // 如果mapConfig已加载，则初始化图层列表
  if (layerManager.mapConfig) {
    initLayerList();
  } else {
    // 否则等待mapConfig加载完成
    layerManager.loadMapConfig().then(() => {
      initLayerList();
    });
  }
});

// 暴露方法给父组件调用
defineExpose({
  refreshLayers
});

// 添加默认导出
defineComponent({
  name: 'LayerList'
});
</script>

<style scoped>
.layer-manager {
  background-color: rgba(2, 49, 52, 0.8);
  border-radius: 8px;
  padding: 12px;
  color: #c8dede;
  width: 100%;
  height: auto;
  max-height: 460px;
  display: flex;
  flex-direction: column;
}

.layer-tabs {
  display: flex;
  margin-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.layer-tab {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.layer-tab.active {
  color: #2a7729;
  border-bottom-color: #2a7729;
  font-weight: bold;
}

.search-container {
  margin-bottom: 12px;
  flex-shrink: 0;
}

.layer-list-container {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 10px;
  min-height: 100px;
  max-height: calc(460px - 120px); /* 减去tabs、搜索框和padding的高度 */
  scrollbar-width: thin;
  scrollbar-color: #2a7729 rgba(255, 255, 255, 0.1);
}

/* 自定义滚动条样式 - Webkit浏览器 */
.layer-list-container::-webkit-scrollbar {
  width: 6px;
}

.layer-list-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.layer-list-container::-webkit-scrollbar-thumb {
  background-color: #2a7729;
  border-radius: 3px;
}

.layer-list-container::-webkit-scrollbar-thumb:hover {
  background-color: #3a9739;
}

.layer-list {
  width: 100%;
}

.layer-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.layer-item-header {
  padding: 8px 12px;
  display: flex;
  align-items: center;
}

.drag-handle {
  cursor: move;
  margin-right: 8px;
  color: #c8dede;
}

.layer-info {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.layer-type-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  border-radius: 4px;
  padding: 2px 8px;
  width: fit-content;
  flex-shrink: 0;
  min-width: 40px;
  text-align: center;
}

.layer-type-raster {
  background-color: rgba(41, 121, 255, 0.2);
  color: #29b6f6;
  border-left: 3px solid #29b6f6;
}

.layer-type-vector {
  background-color: rgba(76, 175, 80, 0.2);
  color: #66bb6a;
  border-left: 3px solid #66bb6a;
}

.layer-type-point {
  background-color: rgba(255, 152, 0, 0.2);
  color: #ffa726;
  border-left: 3px solid #ffa726;
}

.layer-type-line {
  background-color: rgba(156, 39, 176, 0.2);
  color: #ba68c8;
  border-left: 3px solid #ba68c8;
}

.layer-type-polygon {
  background-color: rgba(76, 175, 80, 0.2);
  color: #66bb6a;
  border-left: 3px solid #66bb6a;
}

.layer-type-other {
  background-color: rgba(158, 158, 158, 0.2);
  color: #9e9e9e;
  border-left: 3px solid #9e9e9e;
}

.layer-type-text {
  font-size: 12px;
  font-weight: 500;
}

.layer-name {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.layer-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

.empty-list {
  text-align: center;
  padding: 20px;
  color: rgba(200, 222, 222, 0.5);
  font-style: italic;
}

.ghost {
  opacity: 0.5;
  background: #2a7729;
}

:deep(.el-select-dropdown__item) {
  color: #333;
}

:deep(.el-switch__core) {
  border-color: #2a7729;
}

:deep(.el-switch.is-checked .el-switch__core) {
  background-color: #2a7729;
  border-color: #2a7729;
}

.theme-group {
  margin-bottom: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  overflow: hidden;
}

.theme-header {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  background: rgba(42, 119, 41, 0.2);
  border-left: 3px solid #2a7729;
  transition: all 0.3s;
}

.theme-header:hover {
  background: rgba(42, 119, 41, 0.3);
}

.theme-header .el-icon {
  margin-right: 8px;
  transition: transform 0.3s;
}

.theme-header .rotate-icon {
  transform: rotate(90deg);
}

.theme-content {
  padding: 4px;
}

.theme-layer-item {
  margin-left: 12px;
  margin-right: 4px;
}
</style>