{"version": 3, "sources": ["../../ol/layer/TileProperty.js", "../../ol/renderer/canvas/TileLayer.js"], "sourcesContent": ["/**\n * @module ol/layer/TileProperty\n */\n\n/**\n * @enum {string}\n */\nexport default {\n  PRELOAD: 'preload',\n  USE_INTERIM_TILES_ON_ERROR: 'useInterimTilesOnError',\n};\n", "/**\n * @module ol/renderer/canvas/TileLayer\n */\nimport CanvasLayerRenderer from './Layer.js';\nimport ImageTile from '../../ImageTile.js';\nimport ReprojTile from '../../reproj/Tile.js';\nimport TileRange from '../../TileRange.js';\nimport TileState from '../../TileState.js';\nimport {\n  apply as applyTransform,\n  compose as composeTransform,\n  makeInverse,\n  toString as toTransformString,\n} from '../../transform.js';\nimport {ascending} from '../../array.js';\nimport {\n  containsCoordinate,\n  createEmpty,\n  equals,\n  getHeight,\n  getIntersection,\n  getRotatedViewport,\n  getTopLeft,\n  getWidth,\n  intersects,\n} from '../../extent.js';\nimport {fromUserExtent} from '../../proj.js';\nimport {getUid} from '../../util.js';\nimport {toSize} from '../../size.js';\n\n/**\n * @classdesc\n * Canvas renderer for tile layers.\n * @api\n * @template {import(\"../../layer/Tile.js\").default<import(\"../../source/Tile.js\").default>|import(\"../../layer/VectorTile.js\").default} [LayerType=import(\"../../layer/Tile.js\").default<import(\"../../source/Tile.js\").default>|import(\"../../layer/VectorTile.js\").default]\n * @extends {CanvasLayerRenderer<LayerType>}\n */\nclass CanvasTileLayerRenderer extends CanvasLayerRenderer {\n  /**\n   * @param {LayerType} tileLayer Tile layer.\n   */\n  constructor(tileLayer) {\n    super(tileLayer);\n\n    /**\n     * Rendered extent has changed since the previous `renderFrame()` call\n     * @type {boolean}\n     */\n    this.extentChanged = true;\n\n    /**\n     * @private\n     * @type {?import(\"../../extent.js\").Extent}\n     */\n    this.renderedExtent_ = null;\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.renderedPixelRatio;\n\n    /**\n     * @protected\n     * @type {import(\"../../proj/Projection.js\").default}\n     */\n    this.renderedProjection = null;\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.renderedRevision;\n\n    /**\n     * @protected\n     * @type {!Array<import(\"../../Tile.js\").default>}\n     */\n    this.renderedTiles = [];\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.newTiles_ = false;\n\n    /**\n     * @protected\n     * @type {import(\"../../extent.js\").Extent}\n     */\n    this.tmpExtent = createEmpty();\n\n    /**\n     * @private\n     * @type {import(\"../../TileRange.js\").default}\n     */\n    this.tmpTileRange_ = new TileRange(0, 0, 0, 0);\n  }\n\n  /**\n   * @protected\n   * @param {import(\"../../Tile.js\").default} tile Tile.\n   * @return {boolean} Tile is drawable.\n   */\n  isDrawableTile(tile) {\n    const tileLayer = this.getLayer();\n    const tileState = tile.getState();\n    const useInterimTilesOnError = tileLayer.getUseInterimTilesOnError();\n    return (\n      tileState == TileState.LOADED ||\n      tileState == TileState.EMPTY ||\n      (tileState == TileState.ERROR && !useInterimTilesOnError)\n    );\n  }\n\n  /**\n   * @param {number} z Tile coordinate z.\n   * @param {number} x Tile coordinate x.\n   * @param {number} y Tile coordinate y.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @return {!import(\"../../Tile.js\").default} Tile.\n   */\n  getTile(z, x, y, frameState) {\n    const pixelRatio = frameState.pixelRatio;\n    const projection = frameState.viewState.projection;\n    const tileLayer = this.getLayer();\n    const tileSource = tileLayer.getSource();\n    let tile = tileSource.getTile(z, x, y, pixelRatio, projection);\n    if (tile.getState() == TileState.ERROR) {\n      if (tileLayer.getUseInterimTilesOnError() && tileLayer.getPreload() > 0) {\n        // Preloaded tiles for lower resolutions might have finished loading.\n        this.newTiles_ = true;\n      }\n    }\n    if (!this.isDrawableTile(tile)) {\n      tile = tile.getInterimTile();\n    }\n    return tile;\n  }\n\n  /**\n   * @param {import(\"../../pixel.js\").Pixel} pixel Pixel.\n   * @return {Uint8ClampedArray} Data at the pixel location.\n   */\n  getData(pixel) {\n    const frameState = this.frameState;\n    if (!frameState) {\n      return null;\n    }\n\n    const layer = this.getLayer();\n    const coordinate = applyTransform(\n      frameState.pixelToCoordinateTransform,\n      pixel.slice()\n    );\n\n    const layerExtent = layer.getExtent();\n    if (layerExtent) {\n      if (!containsCoordinate(layerExtent, coordinate)) {\n        return null;\n      }\n    }\n\n    const pixelRatio = frameState.pixelRatio;\n    const projection = frameState.viewState.projection;\n    const viewState = frameState.viewState;\n    const source = layer.getRenderSource();\n    const tileGrid = source.getTileGridForProjection(viewState.projection);\n    const tilePixelRatio = source.getTilePixelRatio(frameState.pixelRatio);\n\n    for (\n      let z = tileGrid.getZForResolution(viewState.resolution);\n      z >= tileGrid.getMinZoom();\n      --z\n    ) {\n      const tileCoord = tileGrid.getTileCoordForCoordAndZ(coordinate, z);\n      const tile = source.getTile(\n        z,\n        tileCoord[1],\n        tileCoord[2],\n        pixelRatio,\n        projection\n      );\n      if (\n        !(tile instanceof ImageTile || tile instanceof ReprojTile) ||\n        (tile instanceof ReprojTile && tile.getState() === TileState.EMPTY)\n      ) {\n        return null;\n      }\n\n      if (tile.getState() !== TileState.LOADED) {\n        continue;\n      }\n\n      const tileOrigin = tileGrid.getOrigin(z);\n      const tileSize = toSize(tileGrid.getTileSize(z));\n      const tileResolution = tileGrid.getResolution(z);\n\n      const col = Math.floor(\n        tilePixelRatio *\n          ((coordinate[0] - tileOrigin[0]) / tileResolution -\n            tileCoord[1] * tileSize[0])\n      );\n\n      const row = Math.floor(\n        tilePixelRatio *\n          ((tileOrigin[1] - coordinate[1]) / tileResolution -\n            tileCoord[2] * tileSize[1])\n      );\n\n      const gutter = Math.round(\n        tilePixelRatio * source.getGutterForProjection(viewState.projection)\n      );\n\n      return this.getImageData(tile.getImage(), col + gutter, row + gutter);\n    }\n\n    return null;\n  }\n\n  /**\n   * @param {Object<number, Object<string, import(\"../../Tile.js\").default>>} tiles Lookup of loaded tiles by zoom level.\n   * @param {number} zoom Zoom level.\n   * @param {import(\"../../Tile.js\").default} tile Tile.\n   * @return {boolean|void} If `false`, the tile will not be considered loaded.\n   */\n  loadedTileCallback(tiles, zoom, tile) {\n    if (this.isDrawableTile(tile)) {\n      return super.loadedTileCallback(tiles, zoom, tile);\n    }\n    return false;\n  }\n\n  /**\n   * Determine whether render should be called.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @return {boolean} Layer is ready to be rendered.\n   */\n  prepareFrame(frameState) {\n    return !!this.getLayer().getSource();\n  }\n\n  /**\n   * Render the layer.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @param {HTMLElement} target Target that may be used to render content to.\n   * @return {HTMLElement} The rendered element.\n   */\n  renderFrame(frameState, target) {\n    const layerState = frameState.layerStatesArray[frameState.layerIndex];\n    const viewState = frameState.viewState;\n    const projection = viewState.projection;\n    const viewResolution = viewState.resolution;\n    const viewCenter = viewState.center;\n    const rotation = viewState.rotation;\n    const pixelRatio = frameState.pixelRatio;\n\n    const tileLayer = this.getLayer();\n    const tileSource = tileLayer.getSource();\n    const sourceRevision = tileSource.getRevision();\n    const tileGrid = tileSource.getTileGridForProjection(projection);\n    const z = tileGrid.getZForResolution(viewResolution, tileSource.zDirection);\n    const tileResolution = tileGrid.getResolution(z);\n\n    let extent = frameState.extent;\n    const resolution = frameState.viewState.resolution;\n    const tilePixelRatio = tileSource.getTilePixelRatio(pixelRatio);\n    // desired dimensions of the canvas in pixels\n    const width = Math.round((getWidth(extent) / resolution) * pixelRatio);\n    const height = Math.round((getHeight(extent) / resolution) * pixelRatio);\n\n    const layerExtent =\n      layerState.extent && fromUserExtent(layerState.extent, projection);\n    if (layerExtent) {\n      extent = getIntersection(\n        extent,\n        fromUserExtent(layerState.extent, projection)\n      );\n    }\n\n    const dx = (tileResolution * width) / 2 / tilePixelRatio;\n    const dy = (tileResolution * height) / 2 / tilePixelRatio;\n    const canvasExtent = [\n      viewCenter[0] - dx,\n      viewCenter[1] - dy,\n      viewCenter[0] + dx,\n      viewCenter[1] + dy,\n    ];\n\n    const tileRange = tileGrid.getTileRangeForExtentAndZ(extent, z);\n\n    /**\n     * @type {Object<number, Object<string, import(\"../../Tile.js\").default>>}\n     */\n    const tilesToDrawByZ = {};\n    tilesToDrawByZ[z] = {};\n\n    const findLoadedTiles = this.createLoadedTileFinder(\n      tileSource,\n      projection,\n      tilesToDrawByZ\n    );\n\n    const tmpExtent = this.tmpExtent;\n    const tmpTileRange = this.tmpTileRange_;\n    this.newTiles_ = false;\n    const viewport = rotation\n      ? getRotatedViewport(\n          viewState.center,\n          resolution,\n          rotation,\n          frameState.size\n        )\n      : undefined;\n    for (let x = tileRange.minX; x <= tileRange.maxX; ++x) {\n      for (let y = tileRange.minY; y <= tileRange.maxY; ++y) {\n        if (\n          rotation &&\n          !tileGrid.tileCoordIntersectsViewport([z, x, y], viewport)\n        ) {\n          continue;\n        }\n        const tile = this.getTile(z, x, y, frameState);\n        if (this.isDrawableTile(tile)) {\n          const uid = getUid(this);\n          if (tile.getState() == TileState.LOADED) {\n            tilesToDrawByZ[z][tile.tileCoord.toString()] = tile;\n            let inTransition = tile.inTransition(uid);\n            if (inTransition && layerState.opacity !== 1) {\n              // Skipping transition when layer is not fully opaque avoids visual artifacts.\n              tile.endTransition(uid);\n              inTransition = false;\n            }\n            if (\n              !this.newTiles_ &&\n              (inTransition || !this.renderedTiles.includes(tile))\n            ) {\n              this.newTiles_ = true;\n            }\n          }\n          if (tile.getAlpha(uid, frameState.time) === 1) {\n            // don't look for alt tiles if alpha is 1\n            continue;\n          }\n        }\n\n        const childTileRange = tileGrid.getTileCoordChildTileRange(\n          tile.tileCoord,\n          tmpTileRange,\n          tmpExtent\n        );\n\n        let covered = false;\n        if (childTileRange) {\n          covered = findLoadedTiles(z + 1, childTileRange);\n        }\n        if (!covered) {\n          tileGrid.forEachTileCoordParentTileRange(\n            tile.tileCoord,\n            findLoadedTiles,\n            tmpTileRange,\n            tmpExtent\n          );\n        }\n      }\n    }\n\n    const canvasScale =\n      ((tileResolution / viewResolution) * pixelRatio) / tilePixelRatio;\n\n    // set forward and inverse pixel transforms\n    composeTransform(\n      this.pixelTransform,\n      frameState.size[0] / 2,\n      frameState.size[1] / 2,\n      1 / pixelRatio,\n      1 / pixelRatio,\n      rotation,\n      -width / 2,\n      -height / 2\n    );\n\n    const canvasTransform = toTransformString(this.pixelTransform);\n\n    this.useContainer(target, canvasTransform, this.getBackground(frameState));\n    const context = this.context;\n    const canvas = context.canvas;\n\n    makeInverse(this.inversePixelTransform, this.pixelTransform);\n\n    // set scale transform for calculating tile positions on the canvas\n    composeTransform(\n      this.tempTransform,\n      width / 2,\n      height / 2,\n      canvasScale,\n      canvasScale,\n      0,\n      -width / 2,\n      -height / 2\n    );\n\n    if (canvas.width != width || canvas.height != height) {\n      canvas.width = width;\n      canvas.height = height;\n    } else if (!this.containerReused) {\n      context.clearRect(0, 0, width, height);\n    }\n\n    if (layerExtent) {\n      this.clipUnrotated(context, frameState, layerExtent);\n    }\n\n    if (!tileSource.getInterpolate()) {\n      context.imageSmoothingEnabled = false;\n    }\n\n    this.preRender(context, frameState);\n\n    this.renderedTiles.length = 0;\n    /** @type {Array<number>} */\n    let zs = Object.keys(tilesToDrawByZ).map(Number);\n    zs.sort(ascending);\n\n    let clips, clipZs, currentClip;\n    if (\n      layerState.opacity === 1 &&\n      (!this.containerReused ||\n        tileSource.getOpaque(frameState.viewState.projection))\n    ) {\n      zs = zs.reverse();\n    } else {\n      clips = [];\n      clipZs = [];\n    }\n    for (let i = zs.length - 1; i >= 0; --i) {\n      const currentZ = zs[i];\n      const currentTilePixelSize = tileSource.getTilePixelSize(\n        currentZ,\n        pixelRatio,\n        projection\n      );\n      const currentResolution = tileGrid.getResolution(currentZ);\n      const currentScale = currentResolution / tileResolution;\n      const dx = currentTilePixelSize[0] * currentScale * canvasScale;\n      const dy = currentTilePixelSize[1] * currentScale * canvasScale;\n      const originTileCoord = tileGrid.getTileCoordForCoordAndZ(\n        getTopLeft(canvasExtent),\n        currentZ\n      );\n      const originTileExtent = tileGrid.getTileCoordExtent(originTileCoord);\n      const origin = applyTransform(this.tempTransform, [\n        (tilePixelRatio * (originTileExtent[0] - canvasExtent[0])) /\n          tileResolution,\n        (tilePixelRatio * (canvasExtent[3] - originTileExtent[3])) /\n          tileResolution,\n      ]);\n      const tileGutter =\n        tilePixelRatio * tileSource.getGutterForProjection(projection);\n      const tilesToDraw = tilesToDrawByZ[currentZ];\n      for (const tileCoordKey in tilesToDraw) {\n        const tile = /** @type {import(\"../../ImageTile.js\").default} */ (\n          tilesToDraw[tileCoordKey]\n        );\n        const tileCoord = tile.tileCoord;\n\n        // Calculate integer positions and sizes so that tiles align\n        const xIndex = originTileCoord[1] - tileCoord[1];\n        const nextX = Math.round(origin[0] - (xIndex - 1) * dx);\n        const yIndex = originTileCoord[2] - tileCoord[2];\n        const nextY = Math.round(origin[1] - (yIndex - 1) * dy);\n        const x = Math.round(origin[0] - xIndex * dx);\n        const y = Math.round(origin[1] - yIndex * dy);\n        const w = nextX - x;\n        const h = nextY - y;\n        const transition = z === currentZ;\n\n        const inTransition =\n          transition && tile.getAlpha(getUid(this), frameState.time) !== 1;\n        let contextSaved = false;\n        if (!inTransition) {\n          if (clips) {\n            // Clip mask for regions in this tile that already filled by a higher z tile\n            currentClip = [x, y, x + w, y, x + w, y + h, x, y + h];\n            for (let i = 0, ii = clips.length; i < ii; ++i) {\n              if (z !== currentZ && currentZ < clipZs[i]) {\n                const clip = clips[i];\n                if (\n                  intersects(\n                    [x, y, x + w, y + h],\n                    [clip[0], clip[3], clip[4], clip[7]]\n                  )\n                ) {\n                  if (!contextSaved) {\n                    context.save();\n                    contextSaved = true;\n                  }\n                  context.beginPath();\n                  // counter-clockwise (outer ring) for current tile\n                  context.moveTo(currentClip[0], currentClip[1]);\n                  context.lineTo(currentClip[2], currentClip[3]);\n                  context.lineTo(currentClip[4], currentClip[5]);\n                  context.lineTo(currentClip[6], currentClip[7]);\n                  // clockwise (inner ring) for higher z tile\n                  context.moveTo(clip[6], clip[7]);\n                  context.lineTo(clip[4], clip[5]);\n                  context.lineTo(clip[2], clip[3]);\n                  context.lineTo(clip[0], clip[1]);\n                  context.clip();\n                }\n              }\n            }\n            clips.push(currentClip);\n            clipZs.push(currentZ);\n          } else {\n            context.clearRect(x, y, w, h);\n          }\n        }\n        this.drawTileImage(\n          tile,\n          frameState,\n          x,\n          y,\n          w,\n          h,\n          tileGutter,\n          transition\n        );\n        if (clips && !inTransition) {\n          if (contextSaved) {\n            context.restore();\n          }\n          this.renderedTiles.unshift(tile);\n        } else {\n          this.renderedTiles.push(tile);\n        }\n        this.updateUsedTiles(frameState.usedTiles, tileSource, tile);\n      }\n    }\n\n    this.renderedRevision = sourceRevision;\n    this.renderedResolution = tileResolution;\n    this.extentChanged =\n      !this.renderedExtent_ || !equals(this.renderedExtent_, canvasExtent);\n    this.renderedExtent_ = canvasExtent;\n    this.renderedPixelRatio = pixelRatio;\n    this.renderedProjection = projection;\n\n    this.manageTilePyramid(\n      frameState,\n      tileSource,\n      tileGrid,\n      pixelRatio,\n      projection,\n      extent,\n      z,\n      tileLayer.getPreload()\n    );\n    this.scheduleExpireCache(frameState, tileSource);\n\n    this.postRender(context, frameState);\n\n    if (layerState.extent) {\n      context.restore();\n    }\n    context.imageSmoothingEnabled = true;\n\n    if (canvasTransform !== canvas.style.transform) {\n      canvas.style.transform = canvasTransform;\n    }\n\n    return this.container;\n  }\n\n  /**\n   * @param {import(\"../../ImageTile.js\").default} tile Tile.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @param {number} x Left of the tile.\n   * @param {number} y Top of the tile.\n   * @param {number} w Width of the tile.\n   * @param {number} h Height of the tile.\n   * @param {number} gutter Tile gutter.\n   * @param {boolean} transition Apply an alpha transition.\n   */\n  drawTileImage(tile, frameState, x, y, w, h, gutter, transition) {\n    const image = this.getTileImage(tile);\n    if (!image) {\n      return;\n    }\n    const uid = getUid(this);\n    const layerState = frameState.layerStatesArray[frameState.layerIndex];\n    const alpha =\n      layerState.opacity *\n      (transition ? tile.getAlpha(uid, frameState.time) : 1);\n    const alphaChanged = alpha !== this.context.globalAlpha;\n    if (alphaChanged) {\n      this.context.save();\n      this.context.globalAlpha = alpha;\n    }\n    this.context.drawImage(\n      image,\n      gutter,\n      gutter,\n      image.width - 2 * gutter,\n      image.height - 2 * gutter,\n      x,\n      y,\n      w,\n      h\n    );\n\n    if (alphaChanged) {\n      this.context.restore();\n    }\n    if (alpha !== layerState.opacity) {\n      frameState.animate = true;\n    } else if (transition) {\n      tile.endTransition(uid);\n    }\n  }\n\n  /**\n   * @return {HTMLCanvasElement} Image\n   */\n  getImage() {\n    const context = this.context;\n    return context ? context.canvas : null;\n  }\n\n  /**\n   * Get the image from a tile.\n   * @param {import(\"../../ImageTile.js\").default} tile Tile.\n   * @return {HTMLCanvasElement|HTMLImageElement|HTMLVideoElement} Image.\n   * @protected\n   */\n  getTileImage(tile) {\n    return tile.getImage();\n  }\n\n  /**\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @param {import(\"../../source/Tile.js\").default} tileSource Tile source.\n   * @protected\n   */\n  scheduleExpireCache(frameState, tileSource) {\n    if (tileSource.canExpireCache()) {\n      /**\n       * @param {import(\"../../source/Tile.js\").default} tileSource Tile source.\n       * @param {import(\"../../Map.js\").default} map Map.\n       * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n       */\n      const postRenderFunction = function (tileSource, map, frameState) {\n        const tileSourceKey = getUid(tileSource);\n        if (tileSourceKey in frameState.usedTiles) {\n          tileSource.expireCache(\n            frameState.viewState.projection,\n            frameState.usedTiles[tileSourceKey]\n          );\n        }\n      }.bind(null, tileSource);\n\n      frameState.postRenderFunctions.push(\n        /** @type {import(\"../../Map.js\").PostRenderFunction} */ (\n          postRenderFunction\n        )\n      );\n    }\n  }\n\n  /**\n   * @param {!Object<string, !Object<string, boolean>>} usedTiles Used tiles.\n   * @param {import(\"../../source/Tile.js\").default} tileSource Tile source.\n   * @param {import('../../Tile.js').default} tile Tile.\n   * @protected\n   */\n  updateUsedTiles(usedTiles, tileSource, tile) {\n    // FIXME should we use tilesToDrawByZ instead?\n    const tileSourceKey = getUid(tileSource);\n    if (!(tileSourceKey in usedTiles)) {\n      usedTiles[tileSourceKey] = {};\n    }\n    usedTiles[tileSourceKey][tile.getKey()] = true;\n  }\n\n  /**\n   * Manage tile pyramid.\n   * This function performs a number of functions related to the tiles at the\n   * current zoom and lower zoom levels:\n   * - registers idle tiles in frameState.wantedTiles so that they are not\n   *   discarded by the tile queue\n   * - enqueues missing tiles\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @param {import(\"../../source/Tile.js\").default} tileSource Tile source.\n   * @param {import(\"../../tilegrid/TileGrid.js\").default} tileGrid Tile grid.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../../proj/Projection.js\").default} projection Projection.\n   * @param {import(\"../../extent.js\").Extent} extent Extent.\n   * @param {number} currentZ Current Z.\n   * @param {number} preload Load low resolution tiles up to `preload` levels.\n   * @param {function(import(\"../../Tile.js\").default):void} [tileCallback] Tile callback.\n   * @protected\n   */\n  manageTilePyramid(\n    frameState,\n    tileSource,\n    tileGrid,\n    pixelRatio,\n    projection,\n    extent,\n    currentZ,\n    preload,\n    tileCallback\n  ) {\n    const tileSourceKey = getUid(tileSource);\n    if (!(tileSourceKey in frameState.wantedTiles)) {\n      frameState.wantedTiles[tileSourceKey] = {};\n    }\n    const wantedTiles = frameState.wantedTiles[tileSourceKey];\n    const tileQueue = frameState.tileQueue;\n    const minZoom = tileGrid.getMinZoom();\n    const rotation = frameState.viewState.rotation;\n    const viewport = rotation\n      ? getRotatedViewport(\n          frameState.viewState.center,\n          frameState.viewState.resolution,\n          rotation,\n          frameState.size\n        )\n      : undefined;\n    let tileCount = 0;\n    let tile, tileRange, tileResolution, x, y, z;\n    for (z = minZoom; z <= currentZ; ++z) {\n      tileRange = tileGrid.getTileRangeForExtentAndZ(extent, z, tileRange);\n      tileResolution = tileGrid.getResolution(z);\n      for (x = tileRange.minX; x <= tileRange.maxX; ++x) {\n        for (y = tileRange.minY; y <= tileRange.maxY; ++y) {\n          if (\n            rotation &&\n            !tileGrid.tileCoordIntersectsViewport([z, x, y], viewport)\n          ) {\n            continue;\n          }\n          if (currentZ - z <= preload) {\n            ++tileCount;\n            tile = tileSource.getTile(z, x, y, pixelRatio, projection);\n            if (tile.getState() == TileState.IDLE) {\n              wantedTiles[tile.getKey()] = true;\n              if (!tileQueue.isKeyQueued(tile.getKey())) {\n                tileQueue.enqueue([\n                  tile,\n                  tileSourceKey,\n                  tileGrid.getTileCoordCenter(tile.tileCoord),\n                  tileResolution,\n                ]);\n              }\n            }\n            if (tileCallback !== undefined) {\n              tileCallback(tile);\n            }\n          } else {\n            tileSource.useTile(z, x, y, projection);\n          }\n        }\n      }\n    }\n    tileSource.updateCacheSize(tileCount, projection);\n  }\n}\n\nexport default CanvasTileLayerRenderer;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAO,uBAAQ;AAAA,EACb,SAAS;AAAA,EACT,4BAA4B;AAC9B;;;AC2BA,IAAM,0BAAN,cAAsC,cAAoB;AAAA;AAAA;AAAA;AAAA,EAIxD,YAAY,WAAW;AACrB,UAAM,SAAS;AAMf,SAAK,gBAAgB;AAMrB,SAAK,kBAAkB;AAMvB,SAAK;AAML,SAAK,qBAAqB;AAM1B,SAAK;AAML,SAAK,gBAAgB,CAAC;AAMtB,SAAK,YAAY;AAMjB,SAAK,YAAY,YAAY;AAM7B,SAAK,gBAAgB,IAAI,kBAAU,GAAG,GAAG,GAAG,CAAC;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,MAAM;AACnB,UAAM,YAAY,KAAK,SAAS;AAChC,UAAM,YAAY,KAAK,SAAS;AAChC,UAAM,yBAAyB,UAAU,0BAA0B;AACnE,WACE,aAAa,kBAAU,UACvB,aAAa,kBAAU,SACtB,aAAa,kBAAU,SAAS,CAAC;AAAA,EAEtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,GAAG,GAAG,GAAG,YAAY;AAC3B,UAAM,aAAa,WAAW;AAC9B,UAAM,aAAa,WAAW,UAAU;AACxC,UAAM,YAAY,KAAK,SAAS;AAChC,UAAM,aAAa,UAAU,UAAU;AACvC,QAAI,OAAO,WAAW,QAAQ,GAAG,GAAG,GAAG,YAAY,UAAU;AAC7D,QAAI,KAAK,SAAS,KAAK,kBAAU,OAAO;AACtC,UAAI,UAAU,0BAA0B,KAAK,UAAU,WAAW,IAAI,GAAG;AAEvE,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,eAAe,IAAI,GAAG;AAC9B,aAAO,KAAK,eAAe;AAAA,IAC7B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,OAAO;AACb,UAAM,aAAa,KAAK;AACxB,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AAEA,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,aAAa;AAAA,MACjB,WAAW;AAAA,MACX,MAAM,MAAM;AAAA,IACd;AAEA,UAAM,cAAc,MAAM,UAAU;AACpC,QAAI,aAAa;AACf,UAAI,CAAC,mBAAmB,aAAa,UAAU,GAAG;AAChD,eAAO;AAAA,MACT;AAAA,IACF;AAEA,UAAM,aAAa,WAAW;AAC9B,UAAM,aAAa,WAAW,UAAU;AACxC,UAAM,YAAY,WAAW;AAC7B,UAAM,SAAS,MAAM,gBAAgB;AACrC,UAAM,WAAW,OAAO,yBAAyB,UAAU,UAAU;AACrE,UAAM,iBAAiB,OAAO,kBAAkB,WAAW,UAAU;AAErE,aACM,IAAI,SAAS,kBAAkB,UAAU,UAAU,GACvD,KAAK,SAAS,WAAW,GACzB,EAAE,GACF;AACA,YAAM,YAAY,SAAS,yBAAyB,YAAY,CAAC;AACjE,YAAM,OAAO,OAAO;AAAA,QAClB;AAAA,QACA,UAAU,CAAC;AAAA,QACX,UAAU,CAAC;AAAA,QACX;AAAA,QACA;AAAA,MACF;AACA,UACE,EAAE,gBAAgB,qBAAa,gBAAgB,iBAC9C,gBAAgB,gBAAc,KAAK,SAAS,MAAM,kBAAU,OAC7D;AACA,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,SAAS,MAAM,kBAAU,QAAQ;AACxC;AAAA,MACF;AAEA,YAAM,aAAa,SAAS,UAAU,CAAC;AACvC,YAAM,WAAW,OAAO,SAAS,YAAY,CAAC,CAAC;AAC/C,YAAM,iBAAiB,SAAS,cAAc,CAAC;AAE/C,YAAM,MAAM,KAAK;AAAA,QACf,mBACI,WAAW,CAAC,IAAI,WAAW,CAAC,KAAK,iBACjC,UAAU,CAAC,IAAI,SAAS,CAAC;AAAA,MAC/B;AAEA,YAAM,MAAM,KAAK;AAAA,QACf,mBACI,WAAW,CAAC,IAAI,WAAW,CAAC,KAAK,iBACjC,UAAU,CAAC,IAAI,SAAS,CAAC;AAAA,MAC/B;AAEA,YAAM,SAAS,KAAK;AAAA,QAClB,iBAAiB,OAAO,uBAAuB,UAAU,UAAU;AAAA,MACrE;AAEA,aAAO,KAAK,aAAa,KAAK,SAAS,GAAG,MAAM,QAAQ,MAAM,MAAM;AAAA,IACtE;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB,OAAO,MAAM,MAAM;AACpC,QAAI,KAAK,eAAe,IAAI,GAAG;AAC7B,aAAO,MAAM,mBAAmB,OAAO,MAAM,IAAI;AAAA,IACnD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,YAAY;AACvB,WAAO,CAAC,CAAC,KAAK,SAAS,EAAE,UAAU;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,YAAY,QAAQ;AAC9B,UAAM,aAAa,WAAW,iBAAiB,WAAW,UAAU;AACpE,UAAM,YAAY,WAAW;AAC7B,UAAM,aAAa,UAAU;AAC7B,UAAM,iBAAiB,UAAU;AACjC,UAAM,aAAa,UAAU;AAC7B,UAAM,WAAW,UAAU;AAC3B,UAAM,aAAa,WAAW;AAE9B,UAAM,YAAY,KAAK,SAAS;AAChC,UAAM,aAAa,UAAU,UAAU;AACvC,UAAM,iBAAiB,WAAW,YAAY;AAC9C,UAAM,WAAW,WAAW,yBAAyB,UAAU;AAC/D,UAAM,IAAI,SAAS,kBAAkB,gBAAgB,WAAW,UAAU;AAC1E,UAAM,iBAAiB,SAAS,cAAc,CAAC;AAE/C,QAAI,SAAS,WAAW;AACxB,UAAM,aAAa,WAAW,UAAU;AACxC,UAAM,iBAAiB,WAAW,kBAAkB,UAAU;AAE9D,UAAM,QAAQ,KAAK,MAAO,SAAS,MAAM,IAAI,aAAc,UAAU;AACrE,UAAM,SAAS,KAAK,MAAO,UAAU,MAAM,IAAI,aAAc,UAAU;AAEvE,UAAM,cACJ,WAAW,UAAU,eAAe,WAAW,QAAQ,UAAU;AACnE,QAAI,aAAa;AACf,eAAS;AAAA,QACP;AAAA,QACA,eAAe,WAAW,QAAQ,UAAU;AAAA,MAC9C;AAAA,IACF;AAEA,UAAM,KAAM,iBAAiB,QAAS,IAAI;AAC1C,UAAM,KAAM,iBAAiB,SAAU,IAAI;AAC3C,UAAM,eAAe;AAAA,MACnB,WAAW,CAAC,IAAI;AAAA,MAChB,WAAW,CAAC,IAAI;AAAA,MAChB,WAAW,CAAC,IAAI;AAAA,MAChB,WAAW,CAAC,IAAI;AAAA,IAClB;AAEA,UAAM,YAAY,SAAS,0BAA0B,QAAQ,CAAC;AAK9D,UAAM,iBAAiB,CAAC;AACxB,mBAAe,CAAC,IAAI,CAAC;AAErB,UAAM,kBAAkB,KAAK;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,UAAM,YAAY,KAAK;AACvB,UAAM,eAAe,KAAK;AAC1B,SAAK,YAAY;AACjB,UAAM,WAAW,WACb;AAAA,MACE,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb,IACA;AACJ,aAAS,IAAI,UAAU,MAAM,KAAK,UAAU,MAAM,EAAE,GAAG;AACrD,eAAS,IAAI,UAAU,MAAM,KAAK,UAAU,MAAM,EAAE,GAAG;AACrD,YACE,YACA,CAAC,SAAS,4BAA4B,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ,GACzD;AACA;AAAA,QACF;AACA,cAAM,OAAO,KAAK,QAAQ,GAAG,GAAG,GAAG,UAAU;AAC7C,YAAI,KAAK,eAAe,IAAI,GAAG;AAC7B,gBAAM,MAAM,OAAO,IAAI;AACvB,cAAI,KAAK,SAAS,KAAK,kBAAU,QAAQ;AACvC,2BAAe,CAAC,EAAE,KAAK,UAAU,SAAS,CAAC,IAAI;AAC/C,gBAAI,eAAe,KAAK,aAAa,GAAG;AACxC,gBAAI,gBAAgB,WAAW,YAAY,GAAG;AAE5C,mBAAK,cAAc,GAAG;AACtB,6BAAe;AAAA,YACjB;AACA,gBACE,CAAC,KAAK,cACL,gBAAgB,CAAC,KAAK,cAAc,SAAS,IAAI,IAClD;AACA,mBAAK,YAAY;AAAA,YACnB;AAAA,UACF;AACA,cAAI,KAAK,SAAS,KAAK,WAAW,IAAI,MAAM,GAAG;AAE7C;AAAA,UACF;AAAA,QACF;AAEA,cAAM,iBAAiB,SAAS;AAAA,UAC9B,KAAK;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAEA,YAAI,UAAU;AACd,YAAI,gBAAgB;AAClB,oBAAU,gBAAgB,IAAI,GAAG,cAAc;AAAA,QACjD;AACA,YAAI,CAAC,SAAS;AACZ,mBAAS;AAAA,YACP,KAAK;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,UAAM,cACF,iBAAiB,iBAAkB,aAAc;AAGrD;AAAA,MACE,KAAK;AAAA,MACL,WAAW,KAAK,CAAC,IAAI;AAAA,MACrB,WAAW,KAAK,CAAC,IAAI;AAAA,MACrB,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ;AAAA,MACA,CAAC,QAAQ;AAAA,MACT,CAAC,SAAS;AAAA,IACZ;AAEA,UAAM,kBAAkB,SAAkB,KAAK,cAAc;AAE7D,SAAK,aAAa,QAAQ,iBAAiB,KAAK,cAAc,UAAU,CAAC;AACzE,UAAM,UAAU,KAAK;AACrB,UAAM,SAAS,QAAQ;AAEvB,gBAAY,KAAK,uBAAuB,KAAK,cAAc;AAG3D;AAAA,MACE,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC,QAAQ;AAAA,MACT,CAAC,SAAS;AAAA,IACZ;AAEA,QAAI,OAAO,SAAS,SAAS,OAAO,UAAU,QAAQ;AACpD,aAAO,QAAQ;AACf,aAAO,SAAS;AAAA,IAClB,WAAW,CAAC,KAAK,iBAAiB;AAChC,cAAQ,UAAU,GAAG,GAAG,OAAO,MAAM;AAAA,IACvC;AAEA,QAAI,aAAa;AACf,WAAK,cAAc,SAAS,YAAY,WAAW;AAAA,IACrD;AAEA,QAAI,CAAC,WAAW,eAAe,GAAG;AAChC,cAAQ,wBAAwB;AAAA,IAClC;AAEA,SAAK,UAAU,SAAS,UAAU;AAElC,SAAK,cAAc,SAAS;AAE5B,QAAI,KAAK,OAAO,KAAK,cAAc,EAAE,IAAI,MAAM;AAC/C,OAAG,KAAK,SAAS;AAEjB,QAAI,OAAO,QAAQ;AACnB,QACE,WAAW,YAAY,MACtB,CAAC,KAAK,mBACL,WAAW,UAAU,WAAW,UAAU,UAAU,IACtD;AACA,WAAK,GAAG,QAAQ;AAAA,IAClB,OAAO;AACL,cAAQ,CAAC;AACT,eAAS,CAAC;AAAA,IACZ;AACA,aAAS,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACvC,YAAM,WAAW,GAAG,CAAC;AACrB,YAAM,uBAAuB,WAAW;AAAA,QACtC;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,oBAAoB,SAAS,cAAc,QAAQ;AACzD,YAAM,eAAe,oBAAoB;AACzC,YAAMA,MAAK,qBAAqB,CAAC,IAAI,eAAe;AACpD,YAAMC,MAAK,qBAAqB,CAAC,IAAI,eAAe;AACpD,YAAM,kBAAkB,SAAS;AAAA,QAC/B,WAAW,YAAY;AAAA,QACvB;AAAA,MACF;AACA,YAAM,mBAAmB,SAAS,mBAAmB,eAAe;AACpE,YAAM,SAAS,MAAe,KAAK,eAAe;AAAA,QAC/C,kBAAkB,iBAAiB,CAAC,IAAI,aAAa,CAAC,KACrD;AAAA,QACD,kBAAkB,aAAa,CAAC,IAAI,iBAAiB,CAAC,KACrD;AAAA,MACJ,CAAC;AACD,YAAM,aACJ,iBAAiB,WAAW,uBAAuB,UAAU;AAC/D,YAAM,cAAc,eAAe,QAAQ;AAC3C,iBAAW,gBAAgB,aAAa;AACtC,cAAM;AAAA;AAAA,UACJ,YAAY,YAAY;AAAA;AAE1B,cAAM,YAAY,KAAK;AAGvB,cAAM,SAAS,gBAAgB,CAAC,IAAI,UAAU,CAAC;AAC/C,cAAM,QAAQ,KAAK,MAAM,OAAO,CAAC,KAAK,SAAS,KAAKD,GAAE;AACtD,cAAM,SAAS,gBAAgB,CAAC,IAAI,UAAU,CAAC;AAC/C,cAAM,QAAQ,KAAK,MAAM,OAAO,CAAC,KAAK,SAAS,KAAKC,GAAE;AACtD,cAAM,IAAI,KAAK,MAAM,OAAO,CAAC,IAAI,SAASD,GAAE;AAC5C,cAAM,IAAI,KAAK,MAAM,OAAO,CAAC,IAAI,SAASC,GAAE;AAC5C,cAAM,IAAI,QAAQ;AAClB,cAAM,IAAI,QAAQ;AAClB,cAAM,aAAa,MAAM;AAEzB,cAAM,eACJ,cAAc,KAAK,SAAS,OAAO,IAAI,GAAG,WAAW,IAAI,MAAM;AACjE,YAAI,eAAe;AACnB,YAAI,CAAC,cAAc;AACjB,cAAI,OAAO;AAET,0BAAc,CAAC,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,CAAC;AACrD,qBAASC,KAAI,GAAG,KAAK,MAAM,QAAQA,KAAI,IAAI,EAAEA,IAAG;AAC9C,kBAAI,MAAM,YAAY,WAAW,OAAOA,EAAC,GAAG;AAC1C,sBAAM,OAAO,MAAMA,EAAC;AACpB,oBACE;AAAA,kBACE,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC;AAAA,kBACnB,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,gBACrC,GACA;AACA,sBAAI,CAAC,cAAc;AACjB,4BAAQ,KAAK;AACb,mCAAe;AAAA,kBACjB;AACA,0BAAQ,UAAU;AAElB,0BAAQ,OAAO,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAC7C,0BAAQ,OAAO,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAC7C,0BAAQ,OAAO,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAC7C,0BAAQ,OAAO,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAE7C,0BAAQ,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAC/B,0BAAQ,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAC/B,0BAAQ,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAC/B,0BAAQ,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAC/B,0BAAQ,KAAK;AAAA,gBACf;AAAA,cACF;AAAA,YACF;AACA,kBAAM,KAAK,WAAW;AACtB,mBAAO,KAAK,QAAQ;AAAA,UACtB,OAAO;AACL,oBAAQ,UAAU,GAAG,GAAG,GAAG,CAAC;AAAA,UAC9B;AAAA,QACF;AACA,aAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,YAAI,SAAS,CAAC,cAAc;AAC1B,cAAI,cAAc;AAChB,oBAAQ,QAAQ;AAAA,UAClB;AACA,eAAK,cAAc,QAAQ,IAAI;AAAA,QACjC,OAAO;AACL,eAAK,cAAc,KAAK,IAAI;AAAA,QAC9B;AACA,aAAK,gBAAgB,WAAW,WAAW,YAAY,IAAI;AAAA,MAC7D;AAAA,IACF;AAEA,SAAK,mBAAmB;AACxB,SAAK,qBAAqB;AAC1B,SAAK,gBACH,CAAC,KAAK,mBAAmB,CAAC,OAAO,KAAK,iBAAiB,YAAY;AACrE,SAAK,kBAAkB;AACvB,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAE1B,SAAK;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU,WAAW;AAAA,IACvB;AACA,SAAK,oBAAoB,YAAY,UAAU;AAE/C,SAAK,WAAW,SAAS,UAAU;AAEnC,QAAI,WAAW,QAAQ;AACrB,cAAQ,QAAQ;AAAA,IAClB;AACA,YAAQ,wBAAwB;AAEhC,QAAI,oBAAoB,OAAO,MAAM,WAAW;AAC9C,aAAO,MAAM,YAAY;AAAA,IAC3B;AAEA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,cAAc,MAAM,YAAY,GAAG,GAAG,GAAG,GAAG,QAAQ,YAAY;AAC9D,UAAM,QAAQ,KAAK,aAAa,IAAI;AACpC,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AACA,UAAM,MAAM,OAAO,IAAI;AACvB,UAAM,aAAa,WAAW,iBAAiB,WAAW,UAAU;AACpE,UAAM,QACJ,WAAW,WACV,aAAa,KAAK,SAAS,KAAK,WAAW,IAAI,IAAI;AACtD,UAAM,eAAe,UAAU,KAAK,QAAQ;AAC5C,QAAI,cAAc;AAChB,WAAK,QAAQ,KAAK;AAClB,WAAK,QAAQ,cAAc;AAAA,IAC7B;AACA,SAAK,QAAQ;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,QAAQ,IAAI;AAAA,MAClB,MAAM,SAAS,IAAI;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAI,cAAc;AAChB,WAAK,QAAQ,QAAQ;AAAA,IACvB;AACA,QAAI,UAAU,WAAW,SAAS;AAChC,iBAAW,UAAU;AAAA,IACvB,WAAW,YAAY;AACrB,WAAK,cAAc,GAAG;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,UAAM,UAAU,KAAK;AACrB,WAAO,UAAU,QAAQ,SAAS;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,MAAM;AACjB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,YAAY,YAAY;AAC1C,QAAI,WAAW,eAAe,GAAG;AAM/B,YAAM,qBAAqB,SAAUC,aAAY,KAAKC,aAAY;AAChE,cAAM,gBAAgB,OAAOD,WAAU;AACvC,YAAI,iBAAiBC,YAAW,WAAW;AACzC,UAAAD,YAAW;AAAA,YACTC,YAAW,UAAU;AAAA,YACrBA,YAAW,UAAU,aAAa;AAAA,UACpC;AAAA,QACF;AAAA,MACF,EAAE,KAAK,MAAM,UAAU;AAEvB,iBAAW,oBAAoB;AAAA;AAAA,QAE3B;AAAA,MAEJ;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,WAAW,YAAY,MAAM;AAE3C,UAAM,gBAAgB,OAAO,UAAU;AACvC,QAAI,EAAE,iBAAiB,YAAY;AACjC,gBAAU,aAAa,IAAI,CAAC;AAAA,IAC9B;AACA,cAAU,aAAa,EAAE,KAAK,OAAO,CAAC,IAAI;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,kBACE,YACA,YACA,UACA,YACA,YACA,QACA,UACA,SACA,cACA;AACA,UAAM,gBAAgB,OAAO,UAAU;AACvC,QAAI,EAAE,iBAAiB,WAAW,cAAc;AAC9C,iBAAW,YAAY,aAAa,IAAI,CAAC;AAAA,IAC3C;AACA,UAAM,cAAc,WAAW,YAAY,aAAa;AACxD,UAAM,YAAY,WAAW;AAC7B,UAAM,UAAU,SAAS,WAAW;AACpC,UAAM,WAAW,WAAW,UAAU;AACtC,UAAM,WAAW,WACb;AAAA,MACE,WAAW,UAAU;AAAA,MACrB,WAAW,UAAU;AAAA,MACrB;AAAA,MACA,WAAW;AAAA,IACb,IACA;AACJ,QAAI,YAAY;AAChB,QAAI,MAAM,WAAW,gBAAgB,GAAG,GAAG;AAC3C,SAAK,IAAI,SAAS,KAAK,UAAU,EAAE,GAAG;AACpC,kBAAY,SAAS,0BAA0B,QAAQ,GAAG,SAAS;AACnE,uBAAiB,SAAS,cAAc,CAAC;AACzC,WAAK,IAAI,UAAU,MAAM,KAAK,UAAU,MAAM,EAAE,GAAG;AACjD,aAAK,IAAI,UAAU,MAAM,KAAK,UAAU,MAAM,EAAE,GAAG;AACjD,cACE,YACA,CAAC,SAAS,4BAA4B,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ,GACzD;AACA;AAAA,UACF;AACA,cAAI,WAAW,KAAK,SAAS;AAC3B,cAAE;AACF,mBAAO,WAAW,QAAQ,GAAG,GAAG,GAAG,YAAY,UAAU;AACzD,gBAAI,KAAK,SAAS,KAAK,kBAAU,MAAM;AACrC,0BAAY,KAAK,OAAO,CAAC,IAAI;AAC7B,kBAAI,CAAC,UAAU,YAAY,KAAK,OAAO,CAAC,GAAG;AACzC,0BAAU,QAAQ;AAAA,kBAChB;AAAA,kBACA;AAAA,kBACA,SAAS,mBAAmB,KAAK,SAAS;AAAA,kBAC1C;AAAA,gBACF,CAAC;AAAA,cACH;AAAA,YACF;AACA,gBAAI,iBAAiB,QAAW;AAC9B,2BAAa,IAAI;AAAA,YACnB;AAAA,UACF,OAAO;AACL,uBAAW,QAAQ,GAAG,GAAG,GAAG,UAAU;AAAA,UACxC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,eAAW,gBAAgB,WAAW,UAAU;AAAA,EAClD;AACF;AAEA,IAAO,oBAAQ;", "names": ["dx", "dy", "i", "tileSource", "frameState"]}