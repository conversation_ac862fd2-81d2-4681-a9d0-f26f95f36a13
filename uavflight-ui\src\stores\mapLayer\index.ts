/*
 * @Description: 地图图层管理 Store
 */
import { defineStore } from 'pinia';
import * as Cesium from 'cesium';
import { MapConfig, MapLayer } from './types';
import {
	createWmsLayer,
	createWmtsLayer,
	createXyzLayer,
	createGeoJsonLayer,
	createWfsLayer,
	createMvtLayer,
	createCustomPointJsonLayer,
	createDemLayer
} from './layerFactory';
import { getMapServerIp, getMapServerUrl, replaceIpInConfig } from '/@/utils/mapUrlReplacer';
import { loadMapConfigFile } from '/@/utils/mapConfig';

// 导出 Store 的类型定义，以便其他模块导入类型
export type MapLayerManagerStore = ReturnType<typeof useMapLayerManagerStore>;

export const useMapLayerManagerStore = defineStore({
	id: 'mapLayerManager',

	state: () => ({
		mapConfig: null as MapConfig | null,
		loadedLayers: [] as string[],
		isLoading: false,
		error: null as string | null,
		activeMap: null as Cesium.Viewer | null,
		// 事件到图层ID的映射
		eventLayers: new Map<string, string[]>(),
		// 图层顺序管理
		layerOrder: [] as string[],
		// 主题到图层ID的映射
		themeLayers: new Map<string, string[]>(),
	}),

	getters: {
		getLayerById: (state) => (id: string) => {
			return state.mapConfig?.layers.find((layer) => layer.id === id);
		},

		getInitialLayers: (state) => {
			return state.mapConfig?.layers.filter((layer) => layer.initialLoad) || [];
		},

		getLayersByEvent: (state) => (eventName: string) => {
			return state.mapConfig?.layers.filter((layer) => layer.event === eventName) || [];
		},

		isLayerLoaded: (state) => (layerId: string) => {
			return state.loadedLayers.includes(layerId);
		},

		// 获取当前图层顺序
		getLayerOrder: (state) => {
			return state.layerOrder;
		},

		// 获取所有可用的主题
		getAvailableThemes: (state) => {
			const themes = new Set<string>();
			if (state.mapConfig) {
				state.mapConfig.layers.forEach(layer => {
					if (layer.theme) {
						themes.add(layer.theme);
					}
				});
			}
			return Array.from(themes);
		},

		// 根据主题获取图层
		getLayersByTheme: (state) => (theme: string) => {
			return state.mapConfig?.layers.filter(layer => layer.theme === theme) || [];
		},
	},

	actions: {
		async loadMapConfig() {
			this.isLoading = true;
			this.error = null;

			try {
				console.log('正在加载Cesium地图配置...');
				
				// 使用新的工具函数加载配置
				const data = await loadMapConfigFile('baseMap');
				
				// 从环境变量获取地图服务IP地址
				const mapServerIp = getMapServerIp();
				
				// 替换配置中的IP地址
				const processedData = replaceIpInConfig(data, mapServerIp);
				
				this.mapConfig = processedData;
				console.log('地图配置加载成功:', this.mapConfig);
				
				// 初始化事件图层映射
				this.initEventLayersMap();
				
				return this.mapConfig;
			} catch (err: any) {
				this.error = err.message || '加载地图配置失败';
				console.error('加载地图配置失败:', err);
				return null;
			} finally {
				this.isLoading = false;
			}
		},

		/**
		 * 初始化事件图层映射
		 */
		initEventLayersMap() {
			this.eventLayers.clear();
			this.themeLayers.clear();
			
			if (this.mapConfig) {
				// 遍历所有图层，构建事件到图层ID的映射
				this.mapConfig.layers.forEach(layer => {
					// 处理事件映射
					if (layer.event) {
						if (!this.eventLayers.has(layer.event)) {
							this.eventLayers.set(layer.event, []);
						}
						this.eventLayers.get(layer.event)?.push(layer.id);
					}
					
					// 处理主题映射
					if (layer.theme) {
						if (!this.themeLayers.has(layer.theme)) {
							this.themeLayers.set(layer.theme, []);
						}
						this.themeLayers.get(layer.theme)?.push(layer.id);
					}
				});
			}
			
			console.log('事件图层映射初始化完成:', this.eventLayers);
			console.log('主题图层映射初始化完成:', this.themeLayers);
		},

		/**
		 * 加载地图配置并初始化地图
		 * @param viewer Cesium.Viewer实例
		 * @returns 是否成功加载并初始化
		 */
		async loadMapConfigAndInitialize(viewer: Cesium.Viewer): Promise<boolean> {
			const config = await this.loadMapConfig();
			if (config) {
				this.initializeMap(viewer);
				return true;
			}
			return false;
		},

		/**
		 * 初始化地图并加载标记为initialLoad的图层
		 * @param viewer Cesium.Viewer实例
		 */
		initializeMap(viewer: Cesium.Viewer) {
			this.activeMap = viewer;

			if (this.mapConfig) {
				// 加载所有initialLoad为true的图层
				const initialLayers = this.getInitialLayers;
				initialLayers.forEach((layer) => {
					const result = this.addLayer(layer.id);
					if (!result.success) {
						console.warn(result.message);
					}
				});
				
				// 初始化图层顺序
				this.initLayerOrder();
			}
		},

		/**
		 * 初始化图层顺序
		 */
		initLayerOrder() {
			// 清空当前顺序
			this.layerOrder = [];
			
			// 添加所有已加载的图层ID
			this.loadedLayers.forEach(id => {
				this.layerOrder.push(id);
			});
		},

		/**
		 * 添加图层到地图
		 * @param layerId 图层ID
		 * @param force 是否强制添加，即使已加载也重新添加
		 * @returns 返回值包含添加结果和消息
		 */
		addLayer(layerId: string, force: boolean = false): { success: boolean; message: string } {
			if (!this.activeMap) {
				return { success: false, message: '地图未初始化，无法添加图层' };
			}

			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig) {
				return { success: false, message: `图层 ${layerId} 不存在` };
			}

			// 如果图层已加载则根据force参数决定是否重新添加
			if (this.isLayerLoaded(layerId)) {
				if (!force) {
					return { success: false, message: `图层 ${layerConfig.name} 已加载，请先移除再添加` };
				} else {
					// 强制重新添加，先移除
					this.removeLayerFromMap(layerId);
				}
			}

			try {
				// 根据图层类型和协议创建对应的图层
				let cesiumLayer: any = null;

				if (layerConfig.type === 'raster') {
					if (layerConfig.protocol === 'WMS') {
						cesiumLayer = createWmsLayer(layerConfig, this.activeMap);
					} else if (layerConfig.protocol === 'WMTS') {
						cesiumLayer = createWmtsLayer(layerConfig, this.activeMap);
					} else if (layerConfig.protocol === 'XYZ') {
						cesiumLayer = createXyzLayer(layerConfig, this.activeMap);
					}
				} else if (layerConfig.type === 'vector') {
					if (layerConfig.protocol === 'GeoJSON') {
						cesiumLayer = createGeoJsonLayer(layerConfig, this.activeMap);
					} else if (layerConfig.protocol === 'WFS') {
						cesiumLayer = createWfsLayer(layerConfig, this.activeMap);
					} else if (layerConfig.protocol === 'MVT') {
						cesiumLayer = createMvtLayer(layerConfig, this.activeMap);
					}
				} else if (layerConfig.type === 'CustomPoint') {
					if (layerConfig.protocol === 'CustomPointJSON') {
						cesiumLayer = createCustomPointJsonLayer(layerConfig, this.activeMap);
					}
				} else if (layerConfig.type === 'terrain') {
					cesiumLayer = createDemLayer(layerConfig, this.activeMap);
				}

				if (cesiumLayer) {
					// 保存图层实例到配置中
					layerConfig.layerInstance = cesiumLayer;
					layerConfig.active = true;

					// 标记图层已加载
					this.markLayerAsLoaded(layerId);
					
					// 添加到图层顺序中（如果不存在）
					if (!this.layerOrder.includes(layerId)) {
						this.layerOrder.push(layerId);
					}

					return { success: true, message: `图层 ${layerConfig.name} 成功添加到地图` };
				} else {
					return { success: false, message: `无法为 ${layerConfig.name} 创建图层` };
				}
			} catch (error) {
				return { success: false, message: `添加图层 ${layerId} 时发生错误: ${error}` };
			}
		},

		/**
		 * 切换指定事件名称的所有图层
		 * @param eventName 事件名称
		 * @param show 是否显示，true为显示，false为隐藏
		 * @returns 操作是否成功
		 */
		toggleEventLayers(eventName: string, show: boolean): boolean {
			try {
				const layers = this.getLayersByEvent(eventName);
				
				if (layers.length === 0) {
					console.warn(`没有找到与事件 ${eventName} 关联的图层`);
					return false;
				}
				
				console.log(`${show ? '显示' : '隐藏'}与事件 ${eventName} 关联的图层，共 ${layers.length} 个图层`);
				
				if (show) {
					// 添加所有该事件相关的图层
					console.log(`添加与事件 ${eventName} 关联的图层`);
					let successCount = 0;
					for (const layer of layers) {
						const result = this.addLayer(layer.id);
						if (result.success) {
							successCount++;
						} else {
							console.warn(result.message);
						}
					}
					console.log(`成功添加 ${successCount}/${layers.length} 个图层`);
					return successCount > 0;
				} else {
					// 移除所有该事件相关的图层
					console.log(`移除与事件 ${eventName} 关联的图层`);
					let successCount = 0;
					for (const layer of layers) {
						if (this.removeLayerFromMap(layer.id)) {
							successCount++;
						}
					}
					console.log(`成功移除 ${successCount}/${layers.length} 个图层`);
					return successCount > 0;
				}
			} catch (error) {
				console.error(`切换事件 ${eventName} 图层时发生错误:`, error);
				return false;
			}
		},

		/**
		 * 从地图中移除图层
		 * @param layerId 图层ID
		 * @returns 是否成功移除图层
		 */
		removeLayerFromMap(layerId: string): boolean {
			if (!this.activeMap) {
				console.error('地图未初始化，无法移除图层');
				return false;
			}

			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig) {
				console.error(`图层 ${layerId} 不存在`);
				return false;
			}

			// 检查图层是否已加载
			if (!this.isLayerLoaded(layerId)) {
				console.log(`图层 ${layerId} 未加载，无需移除`);
				return true;
			}

			try {
				// 从地图中移除图层
				if (layerConfig.layerInstance) {
					if (layerConfig.type === 'raster') {
						// 移除栅格图层
						this.activeMap.imageryLayers.remove(layerConfig.layerInstance);
					} else if (layerConfig.type === 'vector') {
						// 对于Promise实例，需要等待其解析后再移除
						if (layerConfig.layerInstance instanceof Promise) {
							layerConfig.layerInstance.then((dataSource: any) => {
								if (this.activeMap) {
									this.activeMap.dataSources.remove(dataSource);
								}
							});
						} else {
							// 直接移除矢量图层
							this.activeMap.dataSources.remove(layerConfig.layerInstance);
						}
					} else if (layerConfig.type === 'CustomPoint' && layerConfig.protocol === 'CustomPointJSON') {
						// 移除自定义点数据图层
						if (layerConfig.layerInstance && typeof layerConfig.layerInstance.unloadLabels === 'function') {
							// 调用CesiumPointManager的unloadLabels方法移除点
							layerConfig.layerInstance.unloadLabels();
							// 移除点击事件处理器
							layerConfig.layerInstance.removeClickEventHandler();
						}
					}

					// 更新图层状态
					layerConfig.active = false;
					layerConfig.layerInstance = null;

					// 从加载列表中移除
					this.removeLayer(layerId);
					
					// 从图层顺序中移除
					const orderIndex = this.layerOrder.indexOf(layerId);
					if (orderIndex > -1) {
						this.layerOrder.splice(orderIndex, 1);
					}

					console.log(`图层 ${layerConfig.name} 已从地图移除`);
					return true;
				} else {
					console.warn(`图层 ${layerConfig.name} 没有实例，可能未正确加载`);
					// 从加载列表中移除
					this.removeLayer(layerId);
					return true;
				}
			} catch (error) {
				console.error(`移除图层 ${layerId} 时发生错误:`, error);
				return false;
			}
		},

		/**
		 * 标记图层为已加载
		 * @param layerId 图层ID
		 */
		markLayerAsLoaded(layerId: string) {
			if (!this.loadedLayers.includes(layerId)) {
				this.loadedLayers.push(layerId);
			}
		},

		/**
		 * 移除图层
		 * @param layerId 图层ID
		 */
		removeLayer(layerId: string) {
			const index = this.loadedLayers.indexOf(layerId);
			if (index > -1) {
				this.loadedLayers.splice(index, 1);
			}
		},

		/**
		 * 调整图层顺序
		 * @param layerId 图层ID
		 * @param newIndex 新的索引位置
		 * @returns 是否成功调整顺序
		 */
		reorderLayer(layerId: string, newIndex: number): boolean {
			if (!this.activeMap) {
				console.error('地图未初始化，无法调整图层顺序');
				return false;
			}

			const layerConfig = this.getLayerById(layerId);
			if (!layerConfig) {
				console.error(`图层 ${layerId} 不存在`);
				return false;
			}

			// 检查图层是否已加载
			if (!this.isLayerLoaded(layerId)) {
				console.log(`图层 ${layerId} 未加载，无需调整顺序`);
				return false;
			}

			try {
				// 从当前顺序中移除
				const currentIndex = this.layerOrder.indexOf(layerId);
				if (currentIndex > -1) {
					this.layerOrder.splice(currentIndex, 1);
				}

				// 插入到新位置
				if (newIndex >= this.layerOrder.length) {
					this.layerOrder.push(layerId);
				} else {
					this.layerOrder.splice(newIndex, 0, layerId);
				}

				// 根据图层类型调整实际顺序
				if (layerConfig.type === 'raster' && layerConfig.layerInstance) {
					// 对于栅格图层，使用Cesium的imageryLayers调整顺序
					const imageryLayerCollection = this.activeMap.imageryLayers;
					const layerIndex = imageryLayerCollection.indexOf(layerConfig.layerInstance);
					
					if (layerIndex !== -1) {
						// 计算目标索引
						const targetIndex = this.calculateTargetIndex(layerId, 'raster');
						
						// 调整顺序
						if (targetIndex > layerIndex) {
							// 向上移动
							for (let i = layerIndex; i < targetIndex; i++) {
								imageryLayerCollection.raise(layerConfig.layerInstance);
							}
						} else if (targetIndex < layerIndex) {
							// 向下移动
							for (let i = layerIndex; i > targetIndex; i--) {
								imageryLayerCollection.lower(layerConfig.layerInstance);
							}
						}
					}
				}
				// 其他类型的图层可以根据需要添加相应的处理逻辑

				console.log(`图层 ${layerConfig.name} 顺序已调整到 ${newIndex}`);
				return true;
			} catch (error) {
				console.error(`调整图层 ${layerId} 顺序时发生错误:`, error);
				return false;
			}
		},

		/**
		 * 计算图层在特定类型中的目标索引
		 * @param layerId 图层ID
		 * @param type 图层类型
		 * @returns 目标索引
		 */
		calculateTargetIndex(layerId: string, type: string): number {
			// 获取相同类型的图层
			const sameLayers = this.layerOrder
				.map(id => this.getLayerById(id))
				.filter(layer => layer && layer.type === type);
			
			// 找出目标图层在相同类型图层中的位置
			const targetLayer = this.getLayerById(layerId);
			if (!targetLayer) return 0;
			
			const targetIndex = sameLayers.findIndex(layer => layer && layer.id === layerId);
			return targetIndex !== -1 ? targetIndex : 0;
		},

		/**
		 * 重置所有图层
		 */
		resetLayers() {
			// 移除所有已加载的图层
			[...this.loadedLayers].forEach((layerId) => {
				this.removeLayerFromMap(layerId);
			});

			this.loadedLayers = [];
			this.layerOrder = [];
		}
	},
}); 