import { defineStore } from 'pinia';

/**
 * 修改配置时：
 * 1、需要每次都清理 `window.localStorage` 浏览器永久缓存
 * 2、或者点击布局配置最底部 `一键恢复默认` 按钮即可看到效果
 */
export const useThemeConfig = defineStore('themeConfig', {
	state: (): ThemeConfigState => ({
		themeConfig:
			// {
			// 	// 是否开启布局配置抽屉
			// 	isDrawer: false,

			// 	/**
			// 	 * 全局主题
			// 	 */
			// 	// 默认 primary 主题颜色
			// 	primary: '#2E5CF6',
			// 	// 是否开启深色模式
			// 	isIsDark: false,

			// 	/**
			// 	 * 顶栏设置
			// 	 */
			// 	// 默认顶栏导航背景颜色
			// 	topBar: '#ffffff',
			// 	// 默认顶栏导航字体颜色
			// 	topBarColor: '#606266',
			// 	// 是否开启顶栏背景颜色渐变
			// 	isTopBarColorGradual: false,

			// 	/**
			// 	 * 菜单设置
			// 	 */
			// 	// 默认菜单导航背景颜色
			// 	menuBar: '#FFFFFF',
			// 	// 默认菜单导航字体颜色
			// 	menuBarColor: '#505968',
			// 	// 默认菜单高亮背景色
			// 	menuBarActiveColor: 'rgba(242, 243, 245, 1)',
			// 	// 是否开启菜单背景颜色渐变
			// 	isMenuBarColorGradual: false,

			// 	/**
			// 	 * 分栏设置
			// 	 */
			// 	// 默认分栏菜单背景颜色
			// 	columnsMenuBar: '#545c64',
			// 	// 默认分栏菜单字体颜色
			// 	columnsMenuBarColor: '#e6e6e6',
			// 	// 是否开启分栏菜单背景颜色渐变
			// 	isColumnsMenuBarColorGradual: false,
			// 	// 是否开启分栏菜单鼠标悬停预加载(预览菜单)
			// 	isColumnsMenuHoverPreload: false,

			// 	/**
			// 	 * 界面设置
			// 	 */
			// 	// 是否开启菜单水平折叠效果
			// 	isCollapse: false,
			// 	// 是否开启菜单手风琴效果
			// 	isUniqueOpened: true,
			// 	// 是否开启固定 Header
			// 	isFixedHeader: false,
			// 	// 初始化变量，用于更新菜单 el-scrollbar 的高度，请勿删除
			// 	isFixedHeaderChange: false,
			// 	// 是否开启经典布局分割菜单（仅经典布局生效）
			// 	isClassicSplitMenu: false,
			// 	// 是否开启自动锁屏
			// 	isLockScreen: false,
			// 	// 开启自动锁屏倒计时(s/秒)
			// 	lockScreenTime: 30,

			// 	/**
			// 	 * 界面显示
			// 	 */
			// 	// 是否开启侧边栏 Logo
			// 	isShowLogo: true,
			// 	// 初始化变量，用于 el-scrollbar 的高度更新，请勿删除
			// 	isShowLogoChange: false,
			// 	// 是否开启 Breadcrumb，强制经典、横向布局不显示
			// 	isBreadcrumb: true,
			// 	// 是否开启 Tagsview
			// 	isTagsview: true,
			// 	// 是否开启 Breadcrumb 图标
			// 	isBreadcrumbIcon: false,
			// 	// 是否开启 Tagsview 图标
			// 	isTagsviewIcon: false,
			// 	// 是否开启 TagsView 缓存
			// 	isCacheTagsView: true,
			// 	// 是否开启 TagsView 拖拽
			// 	isSortableTagsView: true,
			// 	// 是否开启 TagsView 共用
			// 	isShareTagsView: false,
			// 	// 是否开启 Footer 底部版权信息
			// 	isFooter: true,
			// 	// 是否开启灰色模式
			// 	isGrayscale: false,
			// 	// 是否开启色弱模式
			// 	isInvert: false,
			// 	// 是否开启水印
			// 	isWartermark: false,
			// 	// 水印文案
			// 	wartermarkText: 'Uavflight',

			// 	/**
			// 	 * 其它设置
			// 	 */
			// 	// Tagsview 风格：可选值"<tags-style-one|tags-style-four|tags-style-five>"，默认 tags-style-five
			// 	// 定义的值与 `/src/layout/navBars/tagsView/tagsView.vue` 中的 class 同名
			// 	tagsStyle: 'tags-style-five',
			// 	// 主页面切换动画：可选值"<slide-right|slide-left|opacitys>"，默认 slide-right
			// 	animation: 'slide-right',
			// 	// 分栏高亮风格：可选值"<columns-round|columns-card>"，默认 columns-round
			// 	columnsAsideStyle: 'columns-round',
			// 	// 分栏布局风格：可选值"<columns-horizontal|columns-vertical>"，默认 columns-horizontal
			// 	columnsAsideLayout: 'columns-vertical',

			// 	/**
			// 	 * 布局切换
			// 	 * 注意：为了演示，切换布局时，颜色会被还原成默认，代码位置：/@/layout/navBars/breadcrumb/setings.vue
			// 	 * 中的 `initSetLayoutChange(设置布局切换，重置主题样式)` 方法
			// 	 */
			// 	// 布局切换：可选值"<defaults|classic|transverse|columns>"，默认 defaults
			// 	layout: 'defaults',

			// 	/**
			// 	 * 后端控制路由
			// 	 */
			// 	// 是否开启后端控制路由
			// 	isRequestRoutes: true,
			// 	/**
			// 	 * 全局网站标题 / 副标题
			// 	 */
			// 	// 网站主标题（菜单导航、浏览器当前网页标题、登录form顶部右侧）
			// 	globalTitle: '无人机智巡智检系统',
			// 	// 网站副标题（登录左侧底部页顶部文字）
			// 	globalViceTitle: '无人机智巡智检系统',
			// 	// 网站副标题（登录页顶部文字）
			// 	globalViceTitleMsg: '无人机智巡智检系统',
			// 	// 默认初始语言，可选值"<zh-cn|en|zh-tw>"，默认 zh-cn
			// 	globalI18n: 'zh-cn',
			// 	// 默认全局组件大小，可选值"<large|'default'|small>"，默认 'default'
			// 	globalComponentSize: 'default',
			// 	// footer 页面作者
			// 	footerAuthor: '©2025',
			// },
			{
				// 基础布局配置
				isDrawer: false, // 是否启用抽屉模式
				primary: '#0B440A', // 主题主色（绿色）
				isIsDark: false, // 是否启用暗黑模式
				topBar: '#FAFAFA', // 顶栏背景色（浅灰）
				topBarColor: '#000000', // 顶栏文字颜色（黑色）
				isTopBarColorGradual: true, // 顶栏是否启用渐变色

				// 菜单栏配置
				menuBar: '#FFFFFF', // 菜单栏背景色（白色）
				menuBarColor: '#000000', // 菜单栏文字颜色（黑色）
				menuBarActiveColor: 'rgba(16, 116, 41, 0.7)', // 菜单激活项颜色（半透明绿）
				isMenuBarColorGradual: true, // 菜单栏是否启用渐变色

				// 多栏目配置
				columnsMenuBar: '#131315', // 多栏目背景色（深灰）
				columnsMenuBarColor: '#FFFFFF', // 多栏目文字颜色（白色）
				isColumnsMenuBarColorGradual: false, // 多栏目是否启用渐变色
				isColumnsMenuHoverPreload: false, // 是否启用菜单悬停预加载

				// 界面行为配置
				isCollapse: false, // 是否默认折叠菜单
				isUniqueOpened: false, // 是否只保持一个子菜单展开
				isFixedHeader: false, // 是否固定顶栏
				isFixedHeaderChange: false, // 是否允许切换顶栏固定状态
				isClassicSplitMenu: false, // 是否启用经典分割菜单样式

				// 安全配置
				isLockScreen: false, // 是否启用锁屏功能
				lockScreenTime: 30, // 自动锁屏时间（分钟）

				// 界面元素可见性
				isShowLogo: true, // 是否显示 Logo
				isShowLogoChange: false, // 是否允许切换 Logo 显示
				isBreadcrumb: true, // 是否显示面包屑导航
				isTagsview: true, // 是否显示标签页
				isBreadcrumbIcon: true, // 面包屑是否显示图标
				isTagsviewIcon: true, // 标签页是否显示图标

				// 标签页功能
				isCacheTagsView: true, // 是否缓存标签页
				isSortableTagsView: true, // 标签页是否可拖拽排序
				isShareTagsView: false, // 是否启用标签页共享功能

				// 其他界面配置
				isFooter: false, // 是否显示页脚
				isGrayscale: false, // 是否启用灰度模式
				isInvert: false, // 是否启用反色模式
				isWartermark: false, // 是否启用水印
				wartermarkText: 'Uavflight', // 水印文字内容

				// 样式配置
				tagsStyle: 'tags-style-five', // 标签页样式名称
				animation: 'opacitys', // 全局动画效果
				columnsAsideStyle: 'columns-card', // 多栏目侧边样式
				columnsAsideLayout: 'columns-vertical', // 多栏目布局方式
				layout: 'classic', // 整体布局方案

				// 系统配置
				isRequestRoutes: true, // 是否启用路由请求拦截
				globalTitle: '扶绥县“AI+无人机场”监测监管平台（渠旧试点）', // 系统主标题
				globalViceTitle: '扶绥县“AI+无人机场”监测监管平台（渠旧试点）', // 系统副标题
				globalViceTitleMsg: '扶绥县“AI+无人机场”监测监管平台（渠旧试点）', // 系统欢迎语
				globalI18n: 'zh-cn', // 默认语言（简体中文）
				globalComponentSize: 'default', // 组件默认尺寸
				footerAuthor: '©2025', // 页脚版权信息
			},
	}),
	actions: {
		setThemeConfig(data: ThemeConfigState) {
			this.themeConfig = data.themeConfig;
		},
	},
});
