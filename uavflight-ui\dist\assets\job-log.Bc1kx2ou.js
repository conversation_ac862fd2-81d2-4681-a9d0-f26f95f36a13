import{u as X,__tla as Y}from"./table.BupIqdAe.js";import{s as k,v as Z,d as ee,c as D,__tla as le}from"./index.C0-0gsfl.js";import{u as oe,__tla as te}from"./dict.BtxeDvE4.js";import{d as B,k as c,A as ae,B as s,m as $,e as d,b as m,v as i,f as q,t as l,q as g,u as e,E as f,G as w,H as z,J as ne}from"./vue.CnN__PXn.js";import{__tla as re}from"./dict.B5-f6E3C.js";let H,se=Promise.all([(()=>{try{return Y}catch{}})(),(()=>{try{return le}catch{}})(),(()=>{try{return te}catch{}})(),(()=>{try{return re}catch{}})()]).then(async()=>{function N(C){return k({url:"/job/sys-job-log/page",method:"get",params:C})}let j,v,x;j={class:"layout-padding-auto layout-padding-view"},v={class:"mb8",style:{width:"100%"}},x=B({name:"job-log"}),H=B({...x,setup(C,{expose:V}){const{t:a}=Z.useI18n(),p=c(!1),{job_execute_status:F}=oe("job_type","job_execute_status"),M=c(),b=c(!0),S=c([]),I=c(!0),u=ae({queryForm:{jobId:""},pageList:N,createdIsNeed:!1}),{getDataList:_,currentChangeHandle:Q,sizeChangeHandle:U,tableStyle:L}=X(u),A=t=>{S.value=t.map(({jobLogId:o})=>o),I.value=!t.length},T=async t=>{try{await ee().confirm(a("common.delConfirmText"))}catch{return}try{await function(o){return k({url:"/job/sys-job-log",method:"delete",data:o})}(t),_(),D().success(a("common.delSuccessText"))}catch(o){D().error(o.msg)}};return V({openDialog:t=>{p.value=!0,u.queryForm.jobId=t,_()}}),(t,o)=>{const h=s("el-button"),E=s("right-toolbar"),G=s("el-row"),n=s("el-table-column"),J=s("dict-tag"),P=s("el-table"),R=s("pagination"),K=s("el-dialog"),y=$("auth"),O=$("loading");return m(),d(K,{modelValue:e(p),"onUpdate:modelValue":o[2]||(o[2]=r=>z(p)?p.value=r:null),"close-on-click-modal":!1,fullscreen:"",title:"\u8FD0\u884C\u65E5\u5FD7",draggable:""},{default:i(()=>[q("div",j,[l(G,null,{default:i(()=>[q("div",v,[g((m(),d(h,{formDialogRef:"",disabled:e(I),icon:"Delete",type:"primary",class:"ml10",onClick:o[0]||(o[0]=r=>T(e(S)))},{default:i(()=>[f(w(t.$t("common.delBtn")),1)]),_:1},8,["disabled"])),[[y,"sys_log_del"]]),l(E,{showSearch:e(b),"onUpdate:showSearch":o[1]||(o[1]=r=>z(b)?b.value=r:null),class:"ml10",style:{float:"right","margin-right":"20px"},onQueryTable:e(_)},null,8,["showSearch","onQueryTable"])])]),_:1}),g((m(),d(P,{data:e(u).dataList,style:{width:"100%"},onSelectionChange:A,border:"","cell-style":e(L).cellStyle,"header-cell-style":e(L).headerCellStyle},{default:i(()=>[l(n,{type:"selection",width:"40",align:"center"}),l(n,{type:"index",label:e(a)("log.index"),width:"80"},null,8,["label"]),l(n,{prop:"jobName",label:e(a)("log.jobName"),"show-overflow-tooltip":""},null,8,["label"]),l(n,{prop:"jobMessage",label:e(a)("log.jobMessage"),"show-overflow-tooltip":""},null,8,["label"]),l(n,{prop:"jobLogStatus",label:e(a)("log.jobLogStatus"),"show-overflow-tooltip":""},{default:i(r=>[l(J,{options:e(F),value:r.row.jobLogStatus},null,8,["options","value"])]),_:1},8,["label"]),l(n,{prop:"executeTime",label:e(a)("log.executeTime"),"show-overflow-tooltip":""},null,8,["label"]),l(n,{prop:"exceptionInfo",label:e(a)("log.exceptionInfo"),"show-overflow-tooltip":""},null,8,["label"]),l(n,{prop:"createTime",label:e(a)("log.createTime"),"show-overflow-tooltip":""},null,8,["label"]),l(n,{label:t.$t("common.action"),width:"150"},{default:i(r=>[g((m(),d(h,{text:"",type:"primary",onClick:W=>e(M).openDialog(r.row.jobLogId)},{default:i(()=>[f(w(t.$t("common.editBtn")),1)]),_:2},1032,["onClick"])),[[y,"pix_log_edit"]]),g((m(),d(h,{text:"",type:"primary",onClick:W=>T([r.row.jobLogId])},{default:i(()=>[f(w(t.$t("common.delBtn")),1)]),_:2},1032,["onClick"])),[[y,"sys_log_del"]])]),_:1},8,["label"])]),_:1},8,["data","cell-style","header-cell-style"])),[[O,e(u).loading]]),l(R,ne({onSizeChange:e(U),onCurrentChange:e(Q)},e(u).pagination),null,16,["onSizeChange","onCurrentChange"])])]),_:1},8,["modelValue"])}}})});export{se as __tla,H as default};
