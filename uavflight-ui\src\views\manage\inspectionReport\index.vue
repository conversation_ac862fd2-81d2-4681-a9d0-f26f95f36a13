<template>
  <div class="layout-padding">
    <splitpanes>
      <pane class="ml8">
        <el-card>
          <div class="top-title">巡检报告</div>
          <div class="detail-card">
            <div class="detail-container">
              <h4>航线执飞情况</h4>
              <div style="padding: 5px">
                <el-table :data="tableData" border show-summary :summary-method="getSummaries" :span-method="objectSpanMethod" style="width: 100%">
                  <el-table-column align="center" prop="region" label="区域" width="180" />
                  <el-table-column align="center" prop="regioTown" label="街道" width="180" />
                  <el-table-column align="center" prop="businessType" label="场景" />
                  <el-table-column align="center" prop="eventCount" label="事件数" />
                  <el-table-column align="center" prop="effectiveEventCount" label="有效事件数" />
                </el-table>
              </div>
            </div>
            <div class="detail-container">
              <h4>场景事件数量统计图</h4>
              <div class="echarts-div">
                <NestedPieChart :showLegend="false" :inner-data="innerData" :outer-data="outerData" />
              </div>
            </div>
            <div class="detail-container">
              <h4>社区事件数量TOP10</h4>
              <div class="echarts-div">
                <StackBarChart title="" :x-axis-data="top10DataName" :series-data="top10DataSeries" />
              </div>
            </div>
            <div class="detail-container event-detail">
              <h4>事件明细</h4>

              <div v-loading="tableLoad" element-loading-text="加载中..." v-for="(item, index) in tableEventData" :key="index">
                <div style="margin-bottom: 10px">
                  <table>
                    <tr>
                      <td rowspan="4">{{ (queryTableParams.current - 1) * queryTableParams.size + index + 1 }}</td>
                      <th>事件</th>
                      <td>{{ item.businessEventName }}</td>
                      <th>乡镇</th>
                      <td>
                        {{ item.lastCityName }}
                        <el-icon style="float: right; cursor: pointer" @click="handleEventIcon(item)" size="20" color="blue">
                          <Postcard />
                        </el-icon>
                      </td>
                      <!-- <th rowspan="2" class="icon-cell"><img src="calendar-icon-link" alt="calendar" class="calendar-icon" /></th> -->
                      <!-- 请将calendar-icon-link替换为实际图标链接 -->
                    </tr>
                    <tr>
                      <th>社区</th>
                      <td>{{ item.cityName }}</td>
                      <th>时间</th>
                      <td colspan="1">{{ item.time }}</td>
                    </tr>
                    <tr>
                      <th colspan="4">
                        <img :src="item.picturePath" width="800" />
                      </th>
                    </tr>
                  </table>
                </div>
              </div>

              <!-- <el-pagination
                background
                layout="prev, pager, next"
                :total="1000"
                style="float: left"
                @current-change="currentChangeHandle"
                @size-change="sizeChangeHandle"
              /> -->
              <div style="margin-right: 20px;">
                <el-pagination
                  v-model:current-page="queryTableParams.current"
                  v-model:page-size="queryTableParams.size"
                  :page-sizes="[10, 20, 30, 40]"
                  small
                  background
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="total"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-card>
        <VerifyForm ref="verifyDialogRef" :close-dialog="closeDialog" />
      </pane>
    </splitpanes>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { TableColumnCtx, TableSummaryMethod } from 'element-plus';
import { auditRegionTree } from '/@/api/manage/auditDepartment';
import { getInspectReportData, getBusinessTypeAndEventCountData, getEventTop10Data } from '/@/api/manage/inspectionReport';
import { getBusinessTypeList, getBusinessEventListByType } from '/@/api/manage/businessType';
import NestedPieChart from '/@/components/Echarts/NestedPieChart/index.vue';
import StackBarChart from '/@/components/Echarts/StackBarChart/index.vue';
import { pageList } from '/@/api/manage/verify';

import VerifyForm from '/@/views/manage/verify/form.vue';

// import * as echarts from 'echarts';

const currentChangeHandle = () => {};
const sizeChangeHandle = () => {};
const tableEventData = ref([]);

const queryTableParams = ref({
	current: 1,
	size: 10,
});

const total = ref(1);
const tableLoad = ref(false);

const loadTable = () => {
	tableLoad.value = true;
	pageList(queryTableParams.value).then((res) => {
		tableEventData.value = res.data.records;
		total.value = res.data.total;
		tableLoad.value = false;
	});
};

const handleSizeChange = (val: number) => {
	queryTableParams.size = val;
	loadTable();
};

const handleCurrentChange = (val: number) => {
	queryTableParams.current = val;
	loadTable();
};

const verifyDialogRef = ref<InstanceType<typeof VerifyForm> | null>(null);
const closeDialog = () => {
	if (verifyDialogRef.value) {
		verifyDialogRef.value.closeDialog();
	}
};

// 点击事件表格图标
const handleEventIcon = (rowData: any) => {
	verifyDialogRef.value.showDialog(rowData, 1);
};

const top10DataName = ref([]);
const top10DataSeries = ref([]);

// 社区事件数量TOP10
const getEventTop10DataFun = async () => {
	getEventTop10Data().then((res) => {
		top10DataName.value = res.data.regions;
		top10DataSeries.value = res.data.seriesData;
	});
};

const queryForm = ref({
	cityCode: '',
	businessType: '',
	businessEvent: '',
});

const businessTypeData = ref<any[]>([]);
// 初始化业务场景数据
const getBusinessTypeDataFun = () => {
	getBusinessTypeList().then((res) => {
		businessTypeData.value = res.data;
	});
};

// 业务事件
const businessEventData = ref<any[]>([]);
const changeBusinessType = async (val) => {
	state.queryForm.businessEvent = '';
	const res = await getBusinessEventListByType({ businessType: val });
	businessEventData.value = res.data;
};

const auditRegionData = ref<any[]>([]);
// 初始化行政区数据
const getAuditRegionDataFun = () => {
	// 获取行政区数据
	auditRegionTree().then((res) => {
		auditRegionData.value = res.data;
	});
};

const loadData = async () => {
	try {
		loadTable();
		getAuditRegionDataFun();
		getBusinessTypeDataFun();
		getEventTop10DataFun();
		getInspectReportDataFun();
		getBusinessTypeAndEventCountFun();
	} catch (error) {
		console.error('数据加载失败:', error);
	}
};

onMounted(() => {
	loadData();
});

interface TableData {
	region: string;
	regioTown: string;
	businessType: string;
	eventCount: number;
	effectiveEventCount: number;
}

const innerData = ref([]);

const outerData = ref([]);

const getBusinessTypeAndEventCountFun = async () => {
	const res = await getBusinessTypeAndEventCountData({});
	innerData.value = res.data.businessTypeCount;
	outerData.value = res.data.eventCount;
};

const getInspectReportDataFun = async () => {
	const res = await getInspectReportData({});
	tableData.value = res.data;
};

// 示例数据
const tableData = ref<TableItem[]>([]);

// 合并逻辑（确保不处理汇总行）
const objectSpanMethod = ({ rowIndex, columnIndex }: { rowIndex: number; columnIndex: number }) => {
	// 关键点：跳过汇总行
	if (rowIndex === tableData.value.length) return [1, 1];

	// 区域列合并逻辑
	if (columnIndex === 0) {
		if (rowIndex === 0 || tableData.value[rowIndex].region !== tableData.value[rowIndex - 1].region) {
			let count = 1;
			for (let i = rowIndex + 1; i < tableData.value.length; i++) {
				if (tableData.value[i].region === tableData.value[rowIndex].region) count++;
			}
			return [count, 1];
		}
		return [0, 0];
	}

	// 街道列合并逻辑
	if (columnIndex === 1) {
		if (rowIndex === 0 || tableData.value[rowIndex].regioTown !== tableData.value[rowIndex - 1].regioTown) {
			let count = 1;
			for (let i = rowIndex + 1; i < tableData.value.length; i++) {
				if (tableData.value[i].regioTown === tableData.value[rowIndex].regioTown) count++;
			}
			return [count, 1];
		}
		return [0, 0];
	}

	return [1, 1];
};

// 关键修改点：生成5列的汇总数据
const getSummaries: TableSummaryMethod<TableData> = ({ columns }) => {
	const sums = columns.map((column, index) => {
		// 第一列显示"总计"
		if (index === 0) return '总计';
		// 保留场景列显示"-"
		// if (column.property === 'businessType') return '-';
		// 数值列计算总和
		if (['eventCount', 'effectiveEventCount'].includes(column.property)) {
			const total = tableData.value.reduce((sum, row) => {
				if (row.businessType !== '合计') {
					return sum + parseInt(row[column.property as keyof TableData]);
				} else {
					return sum;
				}
			}, 0);
			return `${total}`;
		}

		return '-'; // 其他非数值列留空
	});
	return sums; // 返回包含5个元素的数组
};
</script>

<style scoped>
.top-title {
	text-align: center;
	font-size: 14px;
}

.detail-card {
	overflow-y: auto;
	scrollbar-width: auto; /* 可选：auto | thin | none */
}

.event-detail {
	height: auto;
}

.detail-container {
	margin-bottom: 0px;
}
.detail-container h4 {
	margin-bottom: 10px;
	position: relative;
	padding-left: 15px;
	font-size: 15px;
}

.detail-container h4::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 5px;
	height: 15px;
	background-color: #2a7729; /* 标题前竖线颜色 */
}

.detail-container h4 {
	margin-bottom: 10px;
	position: relative;
	padding-left: 15px;
	font-size: 15px;
}

.query-condition-container {
	background-color: #ffffff;
	padding: 0px;
	/* border-radius: 5px;
	box-shadow: 0 0 5px rgba(0, 0, 0, 0.1); */
}
.title-container {
	display: flex;
	align-items: center;
	margin-bottom: 20px;
}
.title-before {
	height: 20px;
	width: 6px;
	background: green;
	margin-right: 8px;
}
.title {
	font-size: 18px;
	font-weight: bold;
	margin-left: 0px;
}
.form-item {
	margin-bottom: 20px;
}
.form-item label {
	display: block;
	margin-bottom: 5px;
}

.button-container {
	display: flex;
	justify-content: flex-end;
	margin-top: 20px;
}

.echarts-div {
	height: 500px;
	width: 100%;
	margin: 0 auto;
	text-align: center;
	/* margin-bottom: 50px;
	margin-top: 50px;
	margin-left: 100px; */
}
table {
	width: 98%;
	border-collapse: collapse;
}
th,
td {
	border: 1px solid black;
	padding: 8px;
	text-align: center;
}
th {
}
.layout-padding {
  height: auto !important;
  overflow-y: auto !important;
}
</style>
