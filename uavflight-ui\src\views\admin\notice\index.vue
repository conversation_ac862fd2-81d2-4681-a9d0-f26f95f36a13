<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-form :model="state.queryForm" ref="queryRef" :inline="true">
				<el-form-item label="标题" prop="title">
					<el-input v-model="state.queryForm.title" placeholder="请输入标题" clearable />
				</el-form-item>
				<el-form-item label="等级" prop="level">
					<el-select v-model="state.queryForm.level" placeholder="请选择等级" clearable>
						<el-option label="通知" value="1" />
						<el-option label="重要" value="2" />
					</el-select>
				</el-form-item>
				<el-form-item label="内容" prop="context">
					<el-input v-model="state.queryForm.context" placeholder="请输入内容" clearable />
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="search" @click="getDataList">搜索</el-button>
					<el-button icon="refresh" @click="resetQuery">重置</el-button>
				</el-form-item>
			</el-form>
			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()" v-auth="'admin.per'"> 新 增 </el-button>
					<el-button plain :disabled="multiple" icon="Delete" type="primary" v-auth="'admin.per'" @click="handleDelete(selectObjs)">
						删 除
					</el-button>
				</div>
			</el-row>
			<el-table
				:data="state.dataList"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				@selection-change="selectionChangHandle"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column type="index" label="#" width="40" />
				<el-table-column prop="type" label="类型" width="120" show-overflow-tooltip>
					<template #default="scope">
						<el-tag :type="scope.row.type === '1' ? 'danger' : 'primary'">
							{{ scope.row.type === '1' ? '上级通知' : '系统通知' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="title" label="标题" width="200" show-overflow-tooltip>
					<template #default="scope">
						<span>{{ scope.row.title }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="level" label="等级" width="100" show-overflow-tooltip>
					<template #default="scope">
						<el-tag :type="scope.row.level === '1' ? 'success' : 'danger'">
							{{ scope.row.level === '1' ? '通知' : '重要' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="context" label="内容" :show-overflow-tooltip="false">
					<template #default="scope">
						<span>{{ scope.row.context.length > 50 ? scope.row.context.slice(0, 50) + '...' : scope.row.context }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="createTime" label="发布时间" width="180" show-overflow-tooltip>
					<template #default="scope">
						<span>{{ scope.row.createTime }}</span>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="220">
					<template #default="scope">
						<el-button icon="view" text type="primary" @click="handleView(scope.row)">查看</el-button>
						<el-button
							v-if="scope.row.type !== '1'"
							icon="edit-pen"
							text
							type="primary"
							v-auth="'admin.per'"
							@click="formDialogRef.openDialog(scope.row.id)"
						>
							编辑
						</el-button>
						<el-button v-if="scope.row.type !== '1'" icon="delete" text type="primary" v-auth="'admin.per'" @click="handleDelete([scope.row.id])">
							删除
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />

		<!-- 查看详情 -->
		<el-dialog title="查看通知详情" v-model="viewDialogVisible" width="600px" :close-on-click-modal="false" draggable>
			<div v-if="viewData">
				<div class="mb20">
					<span class="font-bold mr10">标题:</span>
					<span>{{ viewData.title }}</span>
				</div>
				<div class="mb20">
					<span class="font-bold mr10">等级:</span>
					<el-tag :type="viewData.level === '1' ? 'success' : 'danger'">
						{{ viewData.level === '1' ? '通知' : '重要' }}
					</el-tag>
				</div>
				<div>
					<span class="font-bold">内容:</span>
					<div class="mt10 p10 bg-white rounded" style="white-space: pre-wrap; line-height: 1.6">{{ viewData.context }}</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemSysNotice">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchList, delObjs } from '../../../api/admin/notice';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// 定义查询字典

// 定义变量内容
const formDialogRef = ref();
const excelUploadRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);
// 查看对话框
const viewDialogVisible = ref(false);
const viewData = ref<any>(null);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields();
	// 清空多选
	selectObjs.value = [];
	getDataList();
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/admin/sysNotice/export', Object.assign(state.queryForm, { ids: selectObjs }), 'sysNotice.xlsx');
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 查看详情
const handleView = (row: any) => {
	viewData.value = row;
	viewDialogVisible.value = true;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm('此操作将永久删除');
	} catch {
		return;
	}

	try {
		await delObjs(ids);
		getDataList();
		useMessage().success('删除成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
</script>
