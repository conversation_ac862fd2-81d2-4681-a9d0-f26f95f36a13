{"version": 3, "sources": ["../../geotiff/dist-module/compression/deflate.js"], "sourcesContent": ["import { inflate } from 'pako';\nimport BaseDecoder from './basedecoder.js';\n\nexport default class DeflateDecoder extends BaseDecoder {\n  decodeBlock(buffer) {\n    return inflate(new Uint8Array(buffer)).buffer;\n  }\n}\n"], "mappings": ";;;;;;;;;AAGA,IAAqB,iBAArB,cAA4C,YAAY;AAAA,EACtD,YAAY,QAAQ;AAClB,WAAO,UAAQ,IAAI,WAAW,MAAM,CAAC,EAAE;AAAA,EACzC;AACF;", "names": []}