const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/header.HoakDa7u.js","assets/index.C0-0gsfl.js","assets/vue.CnN__PXn.js","assets/index.CB8Sa4Ps.css","assets/main.Fs6WOWAg.js"])))=>i.map(i=>d[i]);
import{u as h,a as r,__tla as T}from"./index.C0-0gsfl.js";import{d as o,k as b,s as v,l as E,o as k,w as n,B as w,e as M,b as P,v as j,t as _,u,j as c,y as x}from"./vue.CnN__PXn.js";let i,A=Promise.all([(()=>{try{return T}catch{}})()]).then(async()=>{let e;e=o({name:"layoutTransverse"}),i=o({...e,setup(D){const f=c(()=>r(()=>import("./header.HoakDa7u.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([0,1,2,3]))),p=c(()=>r(()=>import("./main.Fs6WOWAg.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([4,1,2,3]))),t=b(),y=h(),{themeConfig:m}=v(y),R=E(),s=()=>{t.value.layoutMainScrollbarRef.update()},l=()=>{x(()=>{setTimeout(()=>{s(),t.value.layoutMainScrollbarRef.wrapRef.scrollTop=0},500)})};return k(()=>{l()}),n(()=>R.path,()=>{l()}),n(m,()=>{s()},{deep:!0}),(a,I)=>{const d=w("el-container");return P(),M(d,{class:"layout-container flex-center layout-backtop"},{default:j(()=>[_(u(f)),_(u(p),{ref_key:"layoutMainRef",ref:t},null,512)]),_:1})}}})});export{A as __tla,i as default};
