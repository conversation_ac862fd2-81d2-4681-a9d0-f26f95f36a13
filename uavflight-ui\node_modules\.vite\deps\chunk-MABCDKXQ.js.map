{"version": 3, "sources": ["../../ol/reproj.js", "../../ol/ImageTile.js", "../../ol/reproj/common.js", "../../ol/reproj/Triangulation.js", "../../ol/reproj/Tile.js"], "sourcesContent": ["/**\n * @module ol/reproj\n */\nimport {\n  containsCoordinate,\n  createEmpty,\n  extend,\n  forEach<PERSON>orner,\n  getCenter,\n  getHeight,\n  getTopLeft,\n  getWidth,\n} from './extent.js';\nimport {createCanvasContext2D, releaseCanvas} from './dom.js';\nimport {getPointResolution, transform} from './proj.js';\nimport {solveLinearSystem} from './math.js';\n\nlet brokenDiagonalRendering_;\n\n/**\n * @type {Array<HTMLCanvasElement>}\n */\nexport const canvasPool = [];\n\n/**\n * This draws a small triangle into a canvas by setting the triangle as the clip region\n * and then drawing a (too large) rectangle\n *\n * @param {CanvasRenderingContext2D} ctx The context in which to draw the triangle\n * @param {number} u1 The x-coordinate of the second point. The first point is 0,0.\n * @param {number} v1 The y-coordinate of the second point.\n * @param {number} u2 The x-coordinate of the third point.\n * @param {number} v2 The y-coordinate of the third point.\n */\nfunction drawTestTriangle(ctx, u1, v1, u2, v2) {\n  ctx.beginPath();\n  ctx.moveTo(0, 0);\n  ctx.lineTo(u1, v1);\n  ctx.lineTo(u2, v2);\n  ctx.closePath();\n  ctx.save();\n  ctx.clip();\n  ctx.fillRect(0, 0, Math.max(u1, u2) + 1, Math.max(v1, v2));\n  ctx.restore();\n}\n\n/**\n * Given the data from getImageData, see if the right values appear at the provided offset.\n * Returns true if either the color or transparency is off\n *\n * @param {Uint8ClampedArray} data The data returned from getImageData\n * @param {number} offset The pixel offset from the start of data.\n * @return {boolean} true if the diagonal rendering is broken\n */\nfunction verifyBrokenDiagonalRendering(data, offset) {\n  // the values ought to be close to the rgba(210, 0, 0, 0.75)\n  return (\n    Math.abs(data[offset * 4] - 210) > 2 ||\n    Math.abs(data[offset * 4 + 3] - 0.75 * 255) > 2\n  );\n}\n\n/**\n * Determines if the current browser configuration can render triangular clip regions correctly.\n * This value is cached so the function is only expensive the first time called.\n * Firefox on Windows (as of now) does not if HWA is enabled. See https://bugzilla.mozilla.org/show_bug.cgi?id=1606976\n * Chrome works, and everything seems to work on OSX and Android. This function caches the\n * result. I suppose that it is conceivably possible that a browser might flip modes while the app is\n * running, but lets hope not.\n *\n * @return {boolean} true if the Diagonal Rendering is broken.\n */\nfunction isBrokenDiagonalRendering() {\n  if (brokenDiagonalRendering_ === undefined) {\n    const ctx = createCanvasContext2D(6, 6, canvasPool);\n    ctx.globalCompositeOperation = 'lighter';\n    ctx.fillStyle = 'rgba(210, 0, 0, 0.75)';\n    drawTestTriangle(ctx, 4, 5, 4, 0);\n    drawTestTriangle(ctx, 4, 5, 0, 5);\n    const data = ctx.getImageData(0, 0, 3, 3).data;\n    brokenDiagonalRendering_ =\n      verifyBrokenDiagonalRendering(data, 0) ||\n      verifyBrokenDiagonalRendering(data, 4) ||\n      verifyBrokenDiagonalRendering(data, 8);\n    releaseCanvas(ctx);\n    canvasPool.push(ctx.canvas);\n  }\n\n  return brokenDiagonalRendering_;\n}\n\n/**\n * Calculates ideal resolution to use from the source in order to achieve\n * pixel mapping as close as possible to 1:1 during reprojection.\n * The resolution is calculated regardless of what resolutions\n * are actually available in the dataset (TileGrid, Image, ...).\n *\n * @param {import(\"./proj/Projection.js\").default} sourceProj Source projection.\n * @param {import(\"./proj/Projection.js\").default} targetProj Target projection.\n * @param {import(\"./coordinate.js\").Coordinate} targetCenter Target center.\n * @param {number} targetResolution Target resolution.\n * @return {number} The best resolution to use. Can be +-Infinity, NaN or 0.\n */\nexport function calculateSourceResolution(\n  sourceProj,\n  targetProj,\n  targetCenter,\n  targetResolution\n) {\n  const sourceCenter = transform(targetCenter, targetProj, sourceProj);\n\n  // calculate the ideal resolution of the source data\n  let sourceResolution = getPointResolution(\n    targetProj,\n    targetResolution,\n    targetCenter\n  );\n\n  const targetMetersPerUnit = targetProj.getMetersPerUnit();\n  if (targetMetersPerUnit !== undefined) {\n    sourceResolution *= targetMetersPerUnit;\n  }\n  const sourceMetersPerUnit = sourceProj.getMetersPerUnit();\n  if (sourceMetersPerUnit !== undefined) {\n    sourceResolution /= sourceMetersPerUnit;\n  }\n\n  // Based on the projection properties, the point resolution at the specified\n  // coordinates may be slightly different. We need to reverse-compensate this\n  // in order to achieve optimal results.\n\n  const sourceExtent = sourceProj.getExtent();\n  if (!sourceExtent || containsCoordinate(sourceExtent, sourceCenter)) {\n    const compensationFactor =\n      getPointResolution(sourceProj, sourceResolution, sourceCenter) /\n      sourceResolution;\n    if (isFinite(compensationFactor) && compensationFactor > 0) {\n      sourceResolution /= compensationFactor;\n    }\n  }\n\n  return sourceResolution;\n}\n\n/**\n * Calculates ideal resolution to use from the source in order to achieve\n * pixel mapping as close as possible to 1:1 during reprojection.\n * The resolution is calculated regardless of what resolutions\n * are actually available in the dataset (TileGrid, Image, ...).\n *\n * @param {import(\"./proj/Projection.js\").default} sourceProj Source projection.\n * @param {import(\"./proj/Projection.js\").default} targetProj Target projection.\n * @param {import(\"./extent.js\").Extent} targetExtent Target extent\n * @param {number} targetResolution Target resolution.\n * @return {number} The best resolution to use. Can be +-Infinity, NaN or 0.\n */\nexport function calculateSourceExtentResolution(\n  sourceProj,\n  targetProj,\n  targetExtent,\n  targetResolution\n) {\n  const targetCenter = getCenter(targetExtent);\n  let sourceResolution = calculateSourceResolution(\n    sourceProj,\n    targetProj,\n    targetCenter,\n    targetResolution\n  );\n\n  if (!isFinite(sourceResolution) || sourceResolution <= 0) {\n    forEachCorner(targetExtent, function (corner) {\n      sourceResolution = calculateSourceResolution(\n        sourceProj,\n        targetProj,\n        corner,\n        targetResolution\n      );\n      return isFinite(sourceResolution) && sourceResolution > 0;\n    });\n  }\n\n  return sourceResolution;\n}\n\n/**\n * @typedef {Object} ImageExtent\n * @property {import(\"./extent.js\").Extent} extent Extent.\n * @property {HTMLCanvasElement|HTMLImageElement|HTMLVideoElement} image Image.\n */\n\n/**\n * Renders the source data into new canvas based on the triangulation.\n *\n * @param {number} width Width of the canvas.\n * @param {number} height Height of the canvas.\n * @param {number} pixelRatio Pixel ratio.\n * @param {number} sourceResolution Source resolution.\n * @param {import(\"./extent.js\").Extent} sourceExtent Extent of the data source.\n * @param {number} targetResolution Target resolution.\n * @param {import(\"./extent.js\").Extent} targetExtent Target extent.\n * @param {import(\"./reproj/Triangulation.js\").default} triangulation Calculated triangulation.\n * @param {Array<ImageExtent>} sources Array of sources.\n * @param {number} gutter Gutter of the sources.\n * @param {boolean} [renderEdges] Render reprojection edges.\n * @param {boolean} [interpolate] Use linear interpolation when resampling.\n * @return {HTMLCanvasElement} Canvas with reprojected data.\n */\nexport function render(\n  width,\n  height,\n  pixelRatio,\n  sourceResolution,\n  sourceExtent,\n  targetResolution,\n  targetExtent,\n  triangulation,\n  sources,\n  gutter,\n  renderEdges,\n  interpolate\n) {\n  const context = createCanvasContext2D(\n    Math.round(pixelRatio * width),\n    Math.round(pixelRatio * height),\n    canvasPool\n  );\n\n  if (!interpolate) {\n    context.imageSmoothingEnabled = false;\n  }\n\n  if (sources.length === 0) {\n    return context.canvas;\n  }\n\n  context.scale(pixelRatio, pixelRatio);\n\n  function pixelRound(value) {\n    return Math.round(value * pixelRatio) / pixelRatio;\n  }\n\n  context.globalCompositeOperation = 'lighter';\n\n  const sourceDataExtent = createEmpty();\n  sources.forEach(function (src, i, arr) {\n    extend(sourceDataExtent, src.extent);\n  });\n\n  const canvasWidthInUnits = getWidth(sourceDataExtent);\n  const canvasHeightInUnits = getHeight(sourceDataExtent);\n  const stitchContext = createCanvasContext2D(\n    Math.round((pixelRatio * canvasWidthInUnits) / sourceResolution),\n    Math.round((pixelRatio * canvasHeightInUnits) / sourceResolution),\n    canvasPool\n  );\n\n  if (!interpolate) {\n    stitchContext.imageSmoothingEnabled = false;\n  }\n\n  const stitchScale = pixelRatio / sourceResolution;\n\n  sources.forEach(function (src, i, arr) {\n    const xPos = src.extent[0] - sourceDataExtent[0];\n    const yPos = -(src.extent[3] - sourceDataExtent[3]);\n    const srcWidth = getWidth(src.extent);\n    const srcHeight = getHeight(src.extent);\n\n    // This test should never fail -- but it does. Need to find a fix the upstream condition\n    if (src.image.width > 0 && src.image.height > 0) {\n      stitchContext.drawImage(\n        src.image,\n        gutter,\n        gutter,\n        src.image.width - 2 * gutter,\n        src.image.height - 2 * gutter,\n        xPos * stitchScale,\n        yPos * stitchScale,\n        srcWidth * stitchScale,\n        srcHeight * stitchScale\n      );\n    }\n  });\n\n  const targetTopLeft = getTopLeft(targetExtent);\n\n  triangulation.getTriangles().forEach(function (triangle, i, arr) {\n    /* Calculate affine transform (src -> dst)\n     * Resulting matrix can be used to transform coordinate\n     * from `sourceProjection` to destination pixels.\n     *\n     * To optimize number of context calls and increase numerical stability,\n     * we also do the following operations:\n     * trans(-topLeftExtentCorner), scale(1 / targetResolution), scale(1, -1)\n     * here before solving the linear system so [ui, vi] are pixel coordinates.\n     *\n     * Src points: xi, yi\n     * Dst points: ui, vi\n     * Affine coefficients: aij\n     *\n     * | x0 y0 1  0  0 0 |   |a00|   |u0|\n     * | x1 y1 1  0  0 0 |   |a01|   |u1|\n     * | x2 y2 1  0  0 0 | x |a02| = |u2|\n     * |  0  0 0 x0 y0 1 |   |a10|   |v0|\n     * |  0  0 0 x1 y1 1 |   |a11|   |v1|\n     * |  0  0 0 x2 y2 1 |   |a12|   |v2|\n     */\n    const source = triangle.source;\n    const target = triangle.target;\n    let x0 = source[0][0],\n      y0 = source[0][1];\n    let x1 = source[1][0],\n      y1 = source[1][1];\n    let x2 = source[2][0],\n      y2 = source[2][1];\n    // Make sure that everything is on pixel boundaries\n    const u0 = pixelRound((target[0][0] - targetTopLeft[0]) / targetResolution);\n    const v0 = pixelRound(\n      -(target[0][1] - targetTopLeft[1]) / targetResolution\n    );\n    const u1 = pixelRound((target[1][0] - targetTopLeft[0]) / targetResolution);\n    const v1 = pixelRound(\n      -(target[1][1] - targetTopLeft[1]) / targetResolution\n    );\n    const u2 = pixelRound((target[2][0] - targetTopLeft[0]) / targetResolution);\n    const v2 = pixelRound(\n      -(target[2][1] - targetTopLeft[1]) / targetResolution\n    );\n\n    // Shift all the source points to improve numerical stability\n    // of all the subsequent calculations. The [x0, y0] is used here.\n    // This is also used to simplify the linear system.\n    const sourceNumericalShiftX = x0;\n    const sourceNumericalShiftY = y0;\n    x0 = 0;\n    y0 = 0;\n    x1 -= sourceNumericalShiftX;\n    y1 -= sourceNumericalShiftY;\n    x2 -= sourceNumericalShiftX;\n    y2 -= sourceNumericalShiftY;\n\n    const augmentedMatrix = [\n      [x1, y1, 0, 0, u1 - u0],\n      [x2, y2, 0, 0, u2 - u0],\n      [0, 0, x1, y1, v1 - v0],\n      [0, 0, x2, y2, v2 - v0],\n    ];\n    const affineCoefs = solveLinearSystem(augmentedMatrix);\n    if (!affineCoefs) {\n      return;\n    }\n\n    context.save();\n    context.beginPath();\n\n    if (isBrokenDiagonalRendering() || !interpolate) {\n      // Make sure that all lines are horizontal or vertical\n      context.moveTo(u1, v1);\n      // This is the diagonal line. Do it in 4 steps\n      const steps = 4;\n      const ud = u0 - u1;\n      const vd = v0 - v1;\n      for (let step = 0; step < steps; step++) {\n        // Go horizontally\n        context.lineTo(\n          u1 + pixelRound(((step + 1) * ud) / steps),\n          v1 + pixelRound((step * vd) / (steps - 1))\n        );\n        // Go vertically\n        if (step != steps - 1) {\n          context.lineTo(\n            u1 + pixelRound(((step + 1) * ud) / steps),\n            v1 + pixelRound(((step + 1) * vd) / (steps - 1))\n          );\n        }\n      }\n      // We are almost at u0r, v0r\n      context.lineTo(u2, v2);\n    } else {\n      context.moveTo(u1, v1);\n      context.lineTo(u0, v0);\n      context.lineTo(u2, v2);\n    }\n\n    context.clip();\n\n    context.transform(\n      affineCoefs[0],\n      affineCoefs[2],\n      affineCoefs[1],\n      affineCoefs[3],\n      u0,\n      v0\n    );\n\n    context.translate(\n      sourceDataExtent[0] - sourceNumericalShiftX,\n      sourceDataExtent[3] - sourceNumericalShiftY\n    );\n\n    context.scale(\n      sourceResolution / pixelRatio,\n      -sourceResolution / pixelRatio\n    );\n\n    context.drawImage(stitchContext.canvas, 0, 0);\n    context.restore();\n  });\n\n  releaseCanvas(stitchContext);\n  canvasPool.push(stitchContext.canvas);\n\n  if (renderEdges) {\n    context.save();\n\n    context.globalCompositeOperation = 'source-over';\n    context.strokeStyle = 'black';\n    context.lineWidth = 1;\n\n    triangulation.getTriangles().forEach(function (triangle, i, arr) {\n      const target = triangle.target;\n      const u0 = (target[0][0] - targetTopLeft[0]) / targetResolution;\n      const v0 = -(target[0][1] - targetTopLeft[1]) / targetResolution;\n      const u1 = (target[1][0] - targetTopLeft[0]) / targetResolution;\n      const v1 = -(target[1][1] - targetTopLeft[1]) / targetResolution;\n      const u2 = (target[2][0] - targetTopLeft[0]) / targetResolution;\n      const v2 = -(target[2][1] - targetTopLeft[1]) / targetResolution;\n\n      context.beginPath();\n      context.moveTo(u1, v1);\n      context.lineTo(u0, v0);\n      context.lineTo(u2, v2);\n      context.closePath();\n      context.stroke();\n    });\n\n    context.restore();\n  }\n  return context.canvas;\n}\n", "/**\n * @module ol/ImageTile\n */\nimport Tile from './Tile.js';\nimport TileState from './TileState.js';\nimport {createCanvasContext2D} from './dom.js';\nimport {listenImage} from './Image.js';\n\nclass ImageTile extends Tile {\n  /**\n   * @param {import(\"./tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n   * @param {import(\"./TileState.js\").default} state State.\n   * @param {string} src Image source URI.\n   * @param {?string} crossOrigin Cross origin.\n   * @param {import(\"./Tile.js\").LoadFunction} tileLoadFunction Tile load function.\n   * @param {import(\"./Tile.js\").Options} [options] Tile options.\n   */\n  constructor(tileCoord, state, src, crossOrigin, tileLoadFunction, options) {\n    super(tileCoord, state, options);\n\n    /**\n     * @private\n     * @type {?string}\n     */\n    this.crossOrigin_ = crossOrigin;\n\n    /**\n     * Image URI\n     *\n     * @private\n     * @type {string}\n     */\n    this.src_ = src;\n\n    this.key = src;\n\n    /**\n     * @private\n     * @type {HTMLImageElement|HTMLCanvasElement}\n     */\n    this.image_ = new Image();\n    if (crossOrigin !== null) {\n      this.image_.crossOrigin = crossOrigin;\n    }\n\n    /**\n     * @private\n     * @type {?function():void}\n     */\n    this.unlisten_ = null;\n\n    /**\n     * @private\n     * @type {import(\"./Tile.js\").LoadFunction}\n     */\n    this.tileLoadFunction_ = tileLoadFunction;\n  }\n\n  /**\n   * Get the HTML image element for this tile (may be a Canvas, Image, or Video).\n   * @return {HTMLCanvasElement|HTMLImageElement|HTMLVideoElement} Image.\n   * @api\n   */\n  getImage() {\n    return this.image_;\n  }\n\n  /**\n   * Sets an HTML image element for this tile (may be a Canvas or preloaded Image).\n   * @param {HTMLCanvasElement|HTMLImageElement} element Element.\n   */\n  setImage(element) {\n    this.image_ = element;\n    this.state = TileState.LOADED;\n    this.unlistenImage_();\n    this.changed();\n  }\n\n  /**\n   * Tracks loading or read errors.\n   *\n   * @private\n   */\n  handleImageError_() {\n    this.state = TileState.ERROR;\n    this.unlistenImage_();\n    this.image_ = getBlankImage();\n    this.changed();\n  }\n\n  /**\n   * Tracks successful image load.\n   *\n   * @private\n   */\n  handleImageLoad_() {\n    const image = /** @type {HTMLImageElement} */ (this.image_);\n    if (image.naturalWidth && image.naturalHeight) {\n      this.state = TileState.LOADED;\n    } else {\n      this.state = TileState.EMPTY;\n    }\n    this.unlistenImage_();\n    this.changed();\n  }\n\n  /**\n   * Load the image or retry if loading previously failed.\n   * Loading is taken care of by the tile queue, and calling this method is\n   * only needed for preloading or for reloading in case of an error.\n   *\n   * To retry loading tiles on failed requests, use a custom `tileLoadFunction`\n   * that checks for error status codes and reloads only when the status code is\n   * 408, 429, 500, 502, 503 and 504, and only when not too many retries have been\n   * made already:\n   *\n   * ```js\n   * const retryCodes = [408, 429, 500, 502, 503, 504];\n   * const retries = {};\n   * source.setTileLoadFunction((tile, src) => {\n   *   const image = tile.getImage();\n   *   fetch(src)\n   *     .then((response) => {\n   *       if (retryCodes.includes(response.status)) {\n   *         retries[src] = (retries[src] || 0) + 1;\n   *         if (retries[src] <= 3) {\n   *           setTimeout(() => tile.load(), retries[src] * 1000);\n   *         }\n   *         return Promise.reject();\n   *       }\n   *       return response.blob();\n   *     })\n   *     .then((blob) => {\n   *       const imageUrl = URL.createObjectURL(blob);\n   *       image.src = imageUrl;\n   *       setTimeout(() => URL.revokeObjectURL(imageUrl), 5000);\n   *     })\n   *     .catch(() => tile.setState(3)); // error\n   * });\n   * ```\n   *\n   * @api\n   */\n  load() {\n    if (this.state == TileState.ERROR) {\n      this.state = TileState.IDLE;\n      this.image_ = new Image();\n      if (this.crossOrigin_ !== null) {\n        this.image_.crossOrigin = this.crossOrigin_;\n      }\n    }\n    if (this.state == TileState.IDLE) {\n      this.state = TileState.LOADING;\n      this.changed();\n      this.tileLoadFunction_(this, this.src_);\n      this.unlisten_ = listenImage(\n        this.image_,\n        this.handleImageLoad_.bind(this),\n        this.handleImageError_.bind(this)\n      );\n    }\n  }\n\n  /**\n   * Discards event handlers which listen for load completion or errors.\n   *\n   * @private\n   */\n  unlistenImage_() {\n    if (this.unlisten_) {\n      this.unlisten_();\n      this.unlisten_ = null;\n    }\n  }\n}\n\n/**\n * Get a 1-pixel blank image.\n * @return {HTMLCanvasElement} Blank image.\n */\nfunction getBlankImage() {\n  const ctx = createCanvasContext2D(1, 1);\n  ctx.fillStyle = 'rgba(0,0,0,0)';\n  ctx.fillRect(0, 0, 1, 1);\n  return ctx.canvas;\n}\n\nexport default ImageTile;\n", "/**\n * @module ol/reproj/common\n */\n\n/**\n * Default maximum allowed threshold  (in pixels) for reprojection\n * triangulation.\n * @type {number}\n */\nexport const ERROR_THRESHOLD = 0.5;\n", "/**\n * @module ol/reproj/Triangulation\n */\nimport {\n  boundingExtent,\n  createEmpty,\n  extendCoordinate,\n  getArea,\n  getBottomLeft,\n  getBottomRight,\n  getTopLeft,\n  getTopRight,\n  getWidth,\n  intersects,\n} from '../extent.js';\nimport {getTransform} from '../proj.js';\nimport {modulo} from '../math.js';\n\n/**\n * Single triangle; consists of 3 source points and 3 target points.\n * @typedef {Object} Triangle\n * @property {Array<import(\"../coordinate.js\").Coordinate>} source Source.\n * @property {Array<import(\"../coordinate.js\").Coordinate>} target Target.\n */\n\n/**\n * Maximum number of subdivision steps during raster reprojection triangulation.\n * Prevents high memory usage and large number of proj4 calls (for certain\n * transformations and areas). At most `2*(2^this)` triangles are created for\n * each triangulated extent (tile/image).\n * @type {number}\n */\nconst MAX_SUBDIVISION = 10;\n\n/**\n * Maximum allowed size of triangle relative to world width. When transforming\n * corners of world extent between certain projections, the resulting\n * triangulation seems to have zero error and no subdivision is performed. If\n * the triangle width is more than this (relative to world width; 0-1),\n * subdivison is forced (up to `MAX_SUBDIVISION`). Default is `0.25`.\n * @type {number}\n */\nconst MAX_TRIANGLE_WIDTH = 0.25;\n\n/**\n * @classdesc\n * Class containing triangulation of the given target extent.\n * Used for determining source data and the reprojection itself.\n */\nclass Triangulation {\n  /**\n   * @param {import(\"../proj/Projection.js\").default} sourceProj Source projection.\n   * @param {import(\"../proj/Projection.js\").default} targetProj Target projection.\n   * @param {import(\"../extent.js\").Extent} targetExtent Target extent to triangulate.\n   * @param {import(\"../extent.js\").Extent} maxSourceExtent Maximal source extent that can be used.\n   * @param {number} errorThreshold Acceptable error (in source units).\n   * @param {?number} destinationResolution The (optional) resolution of the destination.\n   */\n  constructor(\n    sourceProj,\n    targetProj,\n    targetExtent,\n    maxSourceExtent,\n    errorThreshold,\n    destinationResolution\n  ) {\n    /**\n     * @type {import(\"../proj/Projection.js\").default}\n     * @private\n     */\n    this.sourceProj_ = sourceProj;\n\n    /**\n     * @type {import(\"../proj/Projection.js\").default}\n     * @private\n     */\n    this.targetProj_ = targetProj;\n\n    /** @type {!Object<string, import(\"../coordinate.js\").Coordinate>} */\n    let transformInvCache = {};\n    const transformInv = getTransform(this.targetProj_, this.sourceProj_);\n\n    /**\n     * @param {import(\"../coordinate.js\").Coordinate} c A coordinate.\n     * @return {import(\"../coordinate.js\").Coordinate} Transformed coordinate.\n     * @private\n     */\n    this.transformInv_ = function (c) {\n      const key = c[0] + '/' + c[1];\n      if (!transformInvCache[key]) {\n        transformInvCache[key] = transformInv(c);\n      }\n      return transformInvCache[key];\n    };\n\n    /**\n     * @type {import(\"../extent.js\").Extent}\n     * @private\n     */\n    this.maxSourceExtent_ = maxSourceExtent;\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.errorThresholdSquared_ = errorThreshold * errorThreshold;\n\n    /**\n     * @type {Array<Triangle>}\n     * @private\n     */\n    this.triangles_ = [];\n\n    /**\n     * Indicates that the triangulation crosses edge of the source projection.\n     * @type {boolean}\n     * @private\n     */\n    this.wrapsXInSource_ = false;\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.canWrapXInSource_ =\n      this.sourceProj_.canWrapX() &&\n      !!maxSourceExtent &&\n      !!this.sourceProj_.getExtent() &&\n      getWidth(maxSourceExtent) >= getWidth(this.sourceProj_.getExtent());\n\n    /**\n     * @type {?number}\n     * @private\n     */\n    this.sourceWorldWidth_ = this.sourceProj_.getExtent()\n      ? getWidth(this.sourceProj_.getExtent())\n      : null;\n\n    /**\n     * @type {?number}\n     * @private\n     */\n    this.targetWorldWidth_ = this.targetProj_.getExtent()\n      ? getWidth(this.targetProj_.getExtent())\n      : null;\n\n    const destinationTopLeft = getTopLeft(targetExtent);\n    const destinationTopRight = getTopRight(targetExtent);\n    const destinationBottomRight = getBottomRight(targetExtent);\n    const destinationBottomLeft = getBottomLeft(targetExtent);\n    const sourceTopLeft = this.transformInv_(destinationTopLeft);\n    const sourceTopRight = this.transformInv_(destinationTopRight);\n    const sourceBottomRight = this.transformInv_(destinationBottomRight);\n    const sourceBottomLeft = this.transformInv_(destinationBottomLeft);\n\n    /*\n     * The maxSubdivision controls how many splittings of the target area can\n     * be done. The idea here is to do a linear mapping of the target areas\n     * but the actual overall reprojection (can be) extremely non-linear. The\n     * default value of MAX_SUBDIVISION was chosen based on mapping a 256x256\n     * tile size. However this function is also called to remap canvas rendered\n     * layers which can be much larger. This calculation increases the maxSubdivision\n     * value by the right factor so that each 256x256 pixel area has\n     * MAX_SUBDIVISION divisions.\n     */\n    const maxSubdivision =\n      MAX_SUBDIVISION +\n      (destinationResolution\n        ? Math.max(\n            0,\n            Math.ceil(\n              Math.log2(\n                getArea(targetExtent) /\n                  (destinationResolution * destinationResolution * 256 * 256)\n              )\n            )\n          )\n        : 0);\n\n    this.addQuad_(\n      destinationTopLeft,\n      destinationTopRight,\n      destinationBottomRight,\n      destinationBottomLeft,\n      sourceTopLeft,\n      sourceTopRight,\n      sourceBottomRight,\n      sourceBottomLeft,\n      maxSubdivision\n    );\n\n    if (this.wrapsXInSource_) {\n      let leftBound = Infinity;\n      this.triangles_.forEach(function (triangle, i, arr) {\n        leftBound = Math.min(\n          leftBound,\n          triangle.source[0][0],\n          triangle.source[1][0],\n          triangle.source[2][0]\n        );\n      });\n\n      // Shift triangles to be as close to `leftBound` as possible\n      // (if the distance is more than `worldWidth / 2` it can be closer.\n      this.triangles_.forEach((triangle) => {\n        if (\n          Math.max(\n            triangle.source[0][0],\n            triangle.source[1][0],\n            triangle.source[2][0]\n          ) -\n            leftBound >\n          this.sourceWorldWidth_ / 2\n        ) {\n          const newTriangle = [\n            [triangle.source[0][0], triangle.source[0][1]],\n            [triangle.source[1][0], triangle.source[1][1]],\n            [triangle.source[2][0], triangle.source[2][1]],\n          ];\n          if (newTriangle[0][0] - leftBound > this.sourceWorldWidth_ / 2) {\n            newTriangle[0][0] -= this.sourceWorldWidth_;\n          }\n          if (newTriangle[1][0] - leftBound > this.sourceWorldWidth_ / 2) {\n            newTriangle[1][0] -= this.sourceWorldWidth_;\n          }\n          if (newTriangle[2][0] - leftBound > this.sourceWorldWidth_ / 2) {\n            newTriangle[2][0] -= this.sourceWorldWidth_;\n          }\n\n          // Rarely (if the extent contains both the dateline and prime meridian)\n          // the shift can in turn break some triangles.\n          // Detect this here and don't shift in such cases.\n          const minX = Math.min(\n            newTriangle[0][0],\n            newTriangle[1][0],\n            newTriangle[2][0]\n          );\n          const maxX = Math.max(\n            newTriangle[0][0],\n            newTriangle[1][0],\n            newTriangle[2][0]\n          );\n          if (maxX - minX < this.sourceWorldWidth_ / 2) {\n            triangle.source = newTriangle;\n          }\n        }\n      });\n    }\n\n    transformInvCache = {};\n  }\n\n  /**\n   * Adds triangle to the triangulation.\n   * @param {import(\"../coordinate.js\").Coordinate} a The target a coordinate.\n   * @param {import(\"../coordinate.js\").Coordinate} b The target b coordinate.\n   * @param {import(\"../coordinate.js\").Coordinate} c The target c coordinate.\n   * @param {import(\"../coordinate.js\").Coordinate} aSrc The source a coordinate.\n   * @param {import(\"../coordinate.js\").Coordinate} bSrc The source b coordinate.\n   * @param {import(\"../coordinate.js\").Coordinate} cSrc The source c coordinate.\n   * @private\n   */\n  addTriangle_(a, b, c, aSrc, bSrc, cSrc) {\n    this.triangles_.push({\n      source: [aSrc, bSrc, cSrc],\n      target: [a, b, c],\n    });\n  }\n\n  /**\n   * Adds quad (points in clock-wise order) to the triangulation\n   * (and reprojects the vertices) if valid.\n   * Performs quad subdivision if needed to increase precision.\n   *\n   * @param {import(\"../coordinate.js\").Coordinate} a The target a coordinate.\n   * @param {import(\"../coordinate.js\").Coordinate} b The target b coordinate.\n   * @param {import(\"../coordinate.js\").Coordinate} c The target c coordinate.\n   * @param {import(\"../coordinate.js\").Coordinate} d The target d coordinate.\n   * @param {import(\"../coordinate.js\").Coordinate} aSrc The source a coordinate.\n   * @param {import(\"../coordinate.js\").Coordinate} bSrc The source b coordinate.\n   * @param {import(\"../coordinate.js\").Coordinate} cSrc The source c coordinate.\n   * @param {import(\"../coordinate.js\").Coordinate} dSrc The source d coordinate.\n   * @param {number} maxSubdivision Maximal allowed subdivision of the quad.\n   * @private\n   */\n  addQuad_(a, b, c, d, aSrc, bSrc, cSrc, dSrc, maxSubdivision) {\n    const sourceQuadExtent = boundingExtent([aSrc, bSrc, cSrc, dSrc]);\n    const sourceCoverageX = this.sourceWorldWidth_\n      ? getWidth(sourceQuadExtent) / this.sourceWorldWidth_\n      : null;\n    const sourceWorldWidth = /** @type {number} */ (this.sourceWorldWidth_);\n\n    // when the quad is wrapped in the source projection\n    // it covers most of the projection extent, but not fully\n    const wrapsX =\n      this.sourceProj_.canWrapX() &&\n      sourceCoverageX > 0.5 &&\n      sourceCoverageX < 1;\n\n    let needsSubdivision = false;\n\n    if (maxSubdivision > 0) {\n      if (this.targetProj_.isGlobal() && this.targetWorldWidth_) {\n        const targetQuadExtent = boundingExtent([a, b, c, d]);\n        const targetCoverageX =\n          getWidth(targetQuadExtent) / this.targetWorldWidth_;\n        needsSubdivision =\n          targetCoverageX > MAX_TRIANGLE_WIDTH || needsSubdivision;\n      }\n      if (!wrapsX && this.sourceProj_.isGlobal() && sourceCoverageX) {\n        needsSubdivision =\n          sourceCoverageX > MAX_TRIANGLE_WIDTH || needsSubdivision;\n      }\n    }\n\n    if (!needsSubdivision && this.maxSourceExtent_) {\n      if (\n        isFinite(sourceQuadExtent[0]) &&\n        isFinite(sourceQuadExtent[1]) &&\n        isFinite(sourceQuadExtent[2]) &&\n        isFinite(sourceQuadExtent[3])\n      ) {\n        if (!intersects(sourceQuadExtent, this.maxSourceExtent_)) {\n          // whole quad outside source projection extent -> ignore\n          return;\n        }\n      }\n    }\n\n    let isNotFinite = 0;\n\n    if (!needsSubdivision) {\n      if (\n        !isFinite(aSrc[0]) ||\n        !isFinite(aSrc[1]) ||\n        !isFinite(bSrc[0]) ||\n        !isFinite(bSrc[1]) ||\n        !isFinite(cSrc[0]) ||\n        !isFinite(cSrc[1]) ||\n        !isFinite(dSrc[0]) ||\n        !isFinite(dSrc[1])\n      ) {\n        if (maxSubdivision > 0) {\n          needsSubdivision = true;\n        } else {\n          // It might be the case that only 1 of the points is infinite. In this case\n          // we can draw a single triangle with the other three points\n          isNotFinite =\n            (!isFinite(aSrc[0]) || !isFinite(aSrc[1]) ? 8 : 0) +\n            (!isFinite(bSrc[0]) || !isFinite(bSrc[1]) ? 4 : 0) +\n            (!isFinite(cSrc[0]) || !isFinite(cSrc[1]) ? 2 : 0) +\n            (!isFinite(dSrc[0]) || !isFinite(dSrc[1]) ? 1 : 0);\n          if (\n            isNotFinite != 1 &&\n            isNotFinite != 2 &&\n            isNotFinite != 4 &&\n            isNotFinite != 8\n          ) {\n            return;\n          }\n        }\n      }\n    }\n\n    if (maxSubdivision > 0) {\n      if (!needsSubdivision) {\n        const center = [(a[0] + c[0]) / 2, (a[1] + c[1]) / 2];\n        const centerSrc = this.transformInv_(center);\n\n        let dx;\n        if (wrapsX) {\n          const centerSrcEstimX =\n            (modulo(aSrc[0], sourceWorldWidth) +\n              modulo(cSrc[0], sourceWorldWidth)) /\n            2;\n          dx = centerSrcEstimX - modulo(centerSrc[0], sourceWorldWidth);\n        } else {\n          dx = (aSrc[0] + cSrc[0]) / 2 - centerSrc[0];\n        }\n        const dy = (aSrc[1] + cSrc[1]) / 2 - centerSrc[1];\n        const centerSrcErrorSquared = dx * dx + dy * dy;\n        needsSubdivision = centerSrcErrorSquared > this.errorThresholdSquared_;\n      }\n      if (needsSubdivision) {\n        if (Math.abs(a[0] - c[0]) <= Math.abs(a[1] - c[1])) {\n          // split horizontally (top & bottom)\n          const bc = [(b[0] + c[0]) / 2, (b[1] + c[1]) / 2];\n          const bcSrc = this.transformInv_(bc);\n          const da = [(d[0] + a[0]) / 2, (d[1] + a[1]) / 2];\n          const daSrc = this.transformInv_(da);\n\n          this.addQuad_(\n            a,\n            b,\n            bc,\n            da,\n            aSrc,\n            bSrc,\n            bcSrc,\n            daSrc,\n            maxSubdivision - 1\n          );\n          this.addQuad_(\n            da,\n            bc,\n            c,\n            d,\n            daSrc,\n            bcSrc,\n            cSrc,\n            dSrc,\n            maxSubdivision - 1\n          );\n        } else {\n          // split vertically (left & right)\n          const ab = [(a[0] + b[0]) / 2, (a[1] + b[1]) / 2];\n          const abSrc = this.transformInv_(ab);\n          const cd = [(c[0] + d[0]) / 2, (c[1] + d[1]) / 2];\n          const cdSrc = this.transformInv_(cd);\n\n          this.addQuad_(\n            a,\n            ab,\n            cd,\n            d,\n            aSrc,\n            abSrc,\n            cdSrc,\n            dSrc,\n            maxSubdivision - 1\n          );\n          this.addQuad_(\n            ab,\n            b,\n            c,\n            cd,\n            abSrc,\n            bSrc,\n            cSrc,\n            cdSrc,\n            maxSubdivision - 1\n          );\n        }\n        return;\n      }\n    }\n\n    if (wrapsX) {\n      if (!this.canWrapXInSource_) {\n        return;\n      }\n      this.wrapsXInSource_ = true;\n    }\n\n    // Exactly zero or one of *Src is not finite\n    // The triangles must have the diagonal line as the first side\n    // This is to allow easy code in reproj.s to make it straight for broken\n    // browsers that can't handle diagonal clipping\n    if ((isNotFinite & 0xb) == 0) {\n      this.addTriangle_(a, c, d, aSrc, cSrc, dSrc);\n    }\n    if ((isNotFinite & 0xe) == 0) {\n      this.addTriangle_(a, c, b, aSrc, cSrc, bSrc);\n    }\n    if (isNotFinite) {\n      // Try the other two triangles\n      if ((isNotFinite & 0xd) == 0) {\n        this.addTriangle_(b, d, a, bSrc, dSrc, aSrc);\n      }\n      if ((isNotFinite & 0x7) == 0) {\n        this.addTriangle_(b, d, c, bSrc, dSrc, cSrc);\n      }\n    }\n  }\n\n  /**\n   * Calculates extent of the `source` coordinates from all the triangles.\n   *\n   * @return {import(\"../extent.js\").Extent} Calculated extent.\n   */\n  calculateSourceExtent() {\n    const extent = createEmpty();\n\n    this.triangles_.forEach(function (triangle, i, arr) {\n      const src = triangle.source;\n      extendCoordinate(extent, src[0]);\n      extendCoordinate(extent, src[1]);\n      extendCoordinate(extent, src[2]);\n    });\n\n    return extent;\n  }\n\n  /**\n   * @return {Array<Triangle>} Array of the calculated triangles.\n   */\n  getTriangles() {\n    return this.triangles_;\n  }\n}\n\nexport default Triangulation;\n", "/**\n * @module ol/reproj/Tile\n */\nimport {ERROR_THRESHOLD} from './common.js';\n\nimport EventType from '../events/EventType.js';\nimport Tile from '../Tile.js';\nimport TileState from '../TileState.js';\nimport Triangulation from './Triangulation.js';\nimport {\n  calculateSourceExtentResolution,\n  canvasPool,\n  render as renderReprojected,\n} from '../reproj.js';\nimport {clamp} from '../math.js';\nimport {getArea, getIntersection} from '../extent.js';\nimport {listen, unlistenByKey} from '../events.js';\nimport {releaseCanvas} from '../dom.js';\n\n/**\n * @typedef {function(number, number, number, number) : (import(\"../ImageTile.js\").default)} FunctionType\n */\n\n/**\n * @classdesc\n * Class encapsulating single reprojected tile.\n * See {@link module:ol/source/TileImage~TileImage}.\n *\n */\nclass ReprojTile extends Tile {\n  /**\n   * @param {import(\"../proj/Projection.js\").default} sourceProj Source projection.\n   * @param {import(\"../tilegrid/TileGrid.js\").default} sourceTileGrid Source tile grid.\n   * @param {import(\"../proj/Projection.js\").default} targetProj Target projection.\n   * @param {import(\"../tilegrid/TileGrid.js\").default} targetTileGrid Target tile grid.\n   * @param {import(\"../tilecoord.js\").TileCoord} tileCoord Coordinate of the tile.\n   * @param {import(\"../tilecoord.js\").TileCoord} wrappedTileCoord Coordinate of the tile wrapped in X.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {number} gutter Gutter of the source tiles.\n   * @param {FunctionType} getTileFunction\n   *     Function returning source tiles (z, x, y, pixelRatio).\n   * @param {number} [errorThreshold] Acceptable reprojection error (in px).\n   * @param {boolean} [renderEdges] Render reprojection edges.\n   * @param {boolean} [interpolate] Use linear interpolation when resampling.\n   */\n  constructor(\n    sourceProj,\n    sourceTileGrid,\n    targetProj,\n    targetTileGrid,\n    tileCoord,\n    wrappedTileCoord,\n    pixelRatio,\n    gutter,\n    getTileFunction,\n    errorThreshold,\n    renderEdges,\n    interpolate\n  ) {\n    super(tileCoord, TileState.IDLE, {interpolate: !!interpolate});\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.renderEdges_ = renderEdges !== undefined ? renderEdges : false;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.pixelRatio_ = pixelRatio;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.gutter_ = gutter;\n\n    /**\n     * @private\n     * @type {HTMLCanvasElement}\n     */\n    this.canvas_ = null;\n\n    /**\n     * @private\n     * @type {import(\"../tilegrid/TileGrid.js\").default}\n     */\n    this.sourceTileGrid_ = sourceTileGrid;\n\n    /**\n     * @private\n     * @type {import(\"../tilegrid/TileGrid.js\").default}\n     */\n    this.targetTileGrid_ = targetTileGrid;\n\n    /**\n     * @private\n     * @type {import(\"../tilecoord.js\").TileCoord}\n     */\n    this.wrappedTileCoord_ = wrappedTileCoord ? wrappedTileCoord : tileCoord;\n\n    /**\n     * @private\n     * @type {!Array<import(\"../ImageTile.js\").default>}\n     */\n    this.sourceTiles_ = [];\n\n    /**\n     * @private\n     * @type {?Array<import(\"../events.js\").EventsKey>}\n     */\n    this.sourcesListenerKeys_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.sourceZ_ = 0;\n\n    const targetExtent = targetTileGrid.getTileCoordExtent(\n      this.wrappedTileCoord_\n    );\n    const maxTargetExtent = this.targetTileGrid_.getExtent();\n    let maxSourceExtent = this.sourceTileGrid_.getExtent();\n\n    const limitedTargetExtent = maxTargetExtent\n      ? getIntersection(targetExtent, maxTargetExtent)\n      : targetExtent;\n\n    if (getArea(limitedTargetExtent) === 0) {\n      // Tile is completely outside range -> EMPTY\n      // TODO: is it actually correct that the source even creates the tile ?\n      this.state = TileState.EMPTY;\n      return;\n    }\n\n    const sourceProjExtent = sourceProj.getExtent();\n    if (sourceProjExtent) {\n      if (!maxSourceExtent) {\n        maxSourceExtent = sourceProjExtent;\n      } else {\n        maxSourceExtent = getIntersection(maxSourceExtent, sourceProjExtent);\n      }\n    }\n\n    const targetResolution = targetTileGrid.getResolution(\n      this.wrappedTileCoord_[0]\n    );\n\n    const sourceResolution = calculateSourceExtentResolution(\n      sourceProj,\n      targetProj,\n      limitedTargetExtent,\n      targetResolution\n    );\n\n    if (!isFinite(sourceResolution) || sourceResolution <= 0) {\n      // invalid sourceResolution -> EMPTY\n      // probably edges of the projections when no extent is defined\n      this.state = TileState.EMPTY;\n      return;\n    }\n\n    const errorThresholdInPixels =\n      errorThreshold !== undefined ? errorThreshold : ERROR_THRESHOLD;\n\n    /**\n     * @private\n     * @type {!import(\"./Triangulation.js\").default}\n     */\n    this.triangulation_ = new Triangulation(\n      sourceProj,\n      targetProj,\n      limitedTargetExtent,\n      maxSourceExtent,\n      sourceResolution * errorThresholdInPixels,\n      targetResolution\n    );\n\n    if (this.triangulation_.getTriangles().length === 0) {\n      // no valid triangles -> EMPTY\n      this.state = TileState.EMPTY;\n      return;\n    }\n\n    this.sourceZ_ = sourceTileGrid.getZForResolution(sourceResolution);\n    let sourceExtent = this.triangulation_.calculateSourceExtent();\n\n    if (maxSourceExtent) {\n      if (sourceProj.canWrapX()) {\n        sourceExtent[1] = clamp(\n          sourceExtent[1],\n          maxSourceExtent[1],\n          maxSourceExtent[3]\n        );\n        sourceExtent[3] = clamp(\n          sourceExtent[3],\n          maxSourceExtent[1],\n          maxSourceExtent[3]\n        );\n      } else {\n        sourceExtent = getIntersection(sourceExtent, maxSourceExtent);\n      }\n    }\n\n    if (!getArea(sourceExtent)) {\n      this.state = TileState.EMPTY;\n    } else {\n      const sourceRange = sourceTileGrid.getTileRangeForExtentAndZ(\n        sourceExtent,\n        this.sourceZ_\n      );\n\n      for (let srcX = sourceRange.minX; srcX <= sourceRange.maxX; srcX++) {\n        for (let srcY = sourceRange.minY; srcY <= sourceRange.maxY; srcY++) {\n          const tile = getTileFunction(this.sourceZ_, srcX, srcY, pixelRatio);\n          if (tile) {\n            this.sourceTiles_.push(tile);\n          }\n        }\n      }\n\n      if (this.sourceTiles_.length === 0) {\n        this.state = TileState.EMPTY;\n      }\n    }\n  }\n\n  /**\n   * Get the HTML Canvas element for this tile.\n   * @return {HTMLCanvasElement} Canvas.\n   */\n  getImage() {\n    return this.canvas_;\n  }\n\n  /**\n   * @private\n   */\n  reproject_() {\n    const sources = [];\n    this.sourceTiles_.forEach((tile) => {\n      if (tile && tile.getState() == TileState.LOADED) {\n        sources.push({\n          extent: this.sourceTileGrid_.getTileCoordExtent(tile.tileCoord),\n          image: tile.getImage(),\n        });\n      }\n    });\n    this.sourceTiles_.length = 0;\n\n    if (sources.length === 0) {\n      this.state = TileState.ERROR;\n    } else {\n      const z = this.wrappedTileCoord_[0];\n      const size = this.targetTileGrid_.getTileSize(z);\n      const width = typeof size === 'number' ? size : size[0];\n      const height = typeof size === 'number' ? size : size[1];\n      const targetResolution = this.targetTileGrid_.getResolution(z);\n      const sourceResolution = this.sourceTileGrid_.getResolution(\n        this.sourceZ_\n      );\n\n      const targetExtent = this.targetTileGrid_.getTileCoordExtent(\n        this.wrappedTileCoord_\n      );\n\n      this.canvas_ = renderReprojected(\n        width,\n        height,\n        this.pixelRatio_,\n        sourceResolution,\n        this.sourceTileGrid_.getExtent(),\n        targetResolution,\n        targetExtent,\n        this.triangulation_,\n        sources,\n        this.gutter_,\n        this.renderEdges_,\n        this.interpolate\n      );\n\n      this.state = TileState.LOADED;\n    }\n    this.changed();\n  }\n\n  /**\n   * Load not yet loaded URI.\n   */\n  load() {\n    if (this.state == TileState.IDLE) {\n      this.state = TileState.LOADING;\n      this.changed();\n\n      let leftToLoad = 0;\n\n      this.sourcesListenerKeys_ = [];\n      this.sourceTiles_.forEach((tile) => {\n        const state = tile.getState();\n        if (state == TileState.IDLE || state == TileState.LOADING) {\n          leftToLoad++;\n\n          const sourceListenKey = listen(\n            tile,\n            EventType.CHANGE,\n            function (e) {\n              const state = tile.getState();\n              if (\n                state == TileState.LOADED ||\n                state == TileState.ERROR ||\n                state == TileState.EMPTY\n              ) {\n                unlistenByKey(sourceListenKey);\n                leftToLoad--;\n                if (leftToLoad === 0) {\n                  this.unlistenSources_();\n                  this.reproject_();\n                }\n              }\n            },\n            this\n          );\n          this.sourcesListenerKeys_.push(sourceListenKey);\n        }\n      });\n\n      if (leftToLoad === 0) {\n        setTimeout(this.reproject_.bind(this), 0);\n      } else {\n        this.sourceTiles_.forEach(function (tile, i, arr) {\n          const state = tile.getState();\n          if (state == TileState.IDLE) {\n            tile.load();\n          }\n        });\n      }\n    }\n  }\n\n  /**\n   * @private\n   */\n  unlistenSources_() {\n    this.sourcesListenerKeys_.forEach(unlistenByKey);\n    this.sourcesListenerKeys_ = null;\n  }\n\n  /**\n   * Remove from the cache due to expiry\n   */\n  release() {\n    if (this.canvas_) {\n      releaseCanvas(this.canvas_.getContext('2d'));\n      canvasPool.push(this.canvas_);\n      this.canvas_ = null;\n    }\n    super.release();\n  }\n}\n\nexport default ReprojTile;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,IAAI;AAKG,IAAM,aAAa,CAAC;AAY3B,SAAS,iBAAiB,KAAK,IAAI,IAAI,IAAI,IAAI;AAC7C,MAAI,UAAU;AACd,MAAI,OAAO,GAAG,CAAC;AACf,MAAI,OAAO,IAAI,EAAE;AACjB,MAAI,OAAO,IAAI,EAAE;AACjB,MAAI,UAAU;AACd,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,SAAS,GAAG,GAAG,KAAK,IAAI,IAAI,EAAE,IAAI,GAAG,KAAK,IAAI,IAAI,EAAE,CAAC;AACzD,MAAI,QAAQ;AACd;AAUA,SAAS,8BAA8B,MAAM,QAAQ;AAEnD,SACE,KAAK,IAAI,KAAK,SAAS,CAAC,IAAI,GAAG,IAAI,KACnC,KAAK,IAAI,KAAK,SAAS,IAAI,CAAC,IAAI,OAAO,GAAG,IAAI;AAElD;AAYA,SAAS,4BAA4B;AACnC,MAAI,6BAA6B,QAAW;AAC1C,UAAM,MAAM,sBAAsB,GAAG,GAAG,UAAU;AAClD,QAAI,2BAA2B;AAC/B,QAAI,YAAY;AAChB,qBAAiB,KAAK,GAAG,GAAG,GAAG,CAAC;AAChC,qBAAiB,KAAK,GAAG,GAAG,GAAG,CAAC;AAChC,UAAM,OAAO,IAAI,aAAa,GAAG,GAAG,GAAG,CAAC,EAAE;AAC1C,+BACE,8BAA8B,MAAM,CAAC,KACrC,8BAA8B,MAAM,CAAC,KACrC,8BAA8B,MAAM,CAAC;AACvC,kBAAc,GAAG;AACjB,eAAW,KAAK,IAAI,MAAM;AAAA,EAC5B;AAEA,SAAO;AACT;AAcO,SAAS,0BACd,YACA,YACA,cACA,kBACA;AACA,QAAM,eAAe,UAAU,cAAc,YAAY,UAAU;AAGnE,MAAI,mBAAmB;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,sBAAsB,WAAW,iBAAiB;AACxD,MAAI,wBAAwB,QAAW;AACrC,wBAAoB;AAAA,EACtB;AACA,QAAM,sBAAsB,WAAW,iBAAiB;AACxD,MAAI,wBAAwB,QAAW;AACrC,wBAAoB;AAAA,EACtB;AAMA,QAAM,eAAe,WAAW,UAAU;AAC1C,MAAI,CAAC,gBAAgB,mBAAmB,cAAc,YAAY,GAAG;AACnE,UAAM,qBACJ,mBAAmB,YAAY,kBAAkB,YAAY,IAC7D;AACF,QAAI,SAAS,kBAAkB,KAAK,qBAAqB,GAAG;AAC1D,0BAAoB;AAAA,IACtB;AAAA,EACF;AAEA,SAAO;AACT;AAcO,SAAS,gCACd,YACA,YACA,cACA,kBACA;AACA,QAAM,eAAe,UAAU,YAAY;AAC3C,MAAI,mBAAmB;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,MAAI,CAAC,SAAS,gBAAgB,KAAK,oBAAoB,GAAG;AACxD,kBAAc,cAAc,SAAU,QAAQ;AAC5C,yBAAmB;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO,SAAS,gBAAgB,KAAK,mBAAmB;AAAA,IAC1D,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAyBO,SAAS,OACd,OACA,QACA,YACA,kBACA,cACA,kBACA,cACA,eACA,SACA,QACA,aACA,aACA;AACA,QAAM,UAAU;AAAA,IACd,KAAK,MAAM,aAAa,KAAK;AAAA,IAC7B,KAAK,MAAM,aAAa,MAAM;AAAA,IAC9B;AAAA,EACF;AAEA,MAAI,CAAC,aAAa;AAChB,YAAQ,wBAAwB;AAAA,EAClC;AAEA,MAAI,QAAQ,WAAW,GAAG;AACxB,WAAO,QAAQ;AAAA,EACjB;AAEA,UAAQ,MAAM,YAAY,UAAU;AAEpC,WAAS,WAAW,OAAO;AACzB,WAAO,KAAK,MAAM,QAAQ,UAAU,IAAI;AAAA,EAC1C;AAEA,UAAQ,2BAA2B;AAEnC,QAAM,mBAAmB,YAAY;AACrC,UAAQ,QAAQ,SAAU,KAAK,GAAG,KAAK;AACrC,WAAO,kBAAkB,IAAI,MAAM;AAAA,EACrC,CAAC;AAED,QAAM,qBAAqB,SAAS,gBAAgB;AACpD,QAAM,sBAAsB,UAAU,gBAAgB;AACtD,QAAM,gBAAgB;AAAA,IACpB,KAAK,MAAO,aAAa,qBAAsB,gBAAgB;AAAA,IAC/D,KAAK,MAAO,aAAa,sBAAuB,gBAAgB;AAAA,IAChE;AAAA,EACF;AAEA,MAAI,CAAC,aAAa;AAChB,kBAAc,wBAAwB;AAAA,EACxC;AAEA,QAAM,cAAc,aAAa;AAEjC,UAAQ,QAAQ,SAAU,KAAK,GAAG,KAAK;AACrC,UAAM,OAAO,IAAI,OAAO,CAAC,IAAI,iBAAiB,CAAC;AAC/C,UAAM,OAAO,EAAE,IAAI,OAAO,CAAC,IAAI,iBAAiB,CAAC;AACjD,UAAM,WAAW,SAAS,IAAI,MAAM;AACpC,UAAM,YAAY,UAAU,IAAI,MAAM;AAGtC,QAAI,IAAI,MAAM,QAAQ,KAAK,IAAI,MAAM,SAAS,GAAG;AAC/C,oBAAc;AAAA,QACZ,IAAI;AAAA,QACJ;AAAA,QACA;AAAA,QACA,IAAI,MAAM,QAAQ,IAAI;AAAA,QACtB,IAAI,MAAM,SAAS,IAAI;AAAA,QACvB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,WAAW;AAAA,QACX,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF,CAAC;AAED,QAAM,gBAAgB,WAAW,YAAY;AAE7C,gBAAc,aAAa,EAAE,QAAQ,SAAU,UAAU,GAAG,KAAK;AAqB/D,UAAM,SAAS,SAAS;AACxB,UAAM,SAAS,SAAS;AACxB,QAAI,KAAK,OAAO,CAAC,EAAE,CAAC,GAClB,KAAK,OAAO,CAAC,EAAE,CAAC;AAClB,QAAI,KAAK,OAAO,CAAC,EAAE,CAAC,GAClB,KAAK,OAAO,CAAC,EAAE,CAAC;AAClB,QAAI,KAAK,OAAO,CAAC,EAAE,CAAC,GAClB,KAAK,OAAO,CAAC,EAAE,CAAC;AAElB,UAAM,KAAK,YAAY,OAAO,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,KAAK,gBAAgB;AAC1E,UAAM,KAAK;AAAA,MACT,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,KAAK;AAAA,IACvC;AACA,UAAM,KAAK,YAAY,OAAO,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,KAAK,gBAAgB;AAC1E,UAAM,KAAK;AAAA,MACT,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,KAAK;AAAA,IACvC;AACA,UAAM,KAAK,YAAY,OAAO,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,KAAK,gBAAgB;AAC1E,UAAM,KAAK;AAAA,MACT,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,KAAK;AAAA,IACvC;AAKA,UAAM,wBAAwB;AAC9B,UAAM,wBAAwB;AAC9B,SAAK;AACL,SAAK;AACL,UAAM;AACN,UAAM;AACN,UAAM;AACN,UAAM;AAEN,UAAM,kBAAkB;AAAA,MACtB,CAAC,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE;AAAA,MACtB,CAAC,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE;AAAA,MACtB,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE;AAAA,MACtB,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE;AAAA,IACxB;AACA,UAAM,cAAc,kBAAkB,eAAe;AACrD,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AAEA,YAAQ,KAAK;AACb,YAAQ,UAAU;AAElB,QAAI,0BAA0B,KAAK,CAAC,aAAa;AAE/C,cAAQ,OAAO,IAAI,EAAE;AAErB,YAAM,QAAQ;AACd,YAAM,KAAK,KAAK;AAChB,YAAM,KAAK,KAAK;AAChB,eAAS,OAAO,GAAG,OAAO,OAAO,QAAQ;AAEvC,gBAAQ;AAAA,UACN,KAAK,YAAa,OAAO,KAAK,KAAM,KAAK;AAAA,UACzC,KAAK,WAAY,OAAO,MAAO,QAAQ,EAAE;AAAA,QAC3C;AAEA,YAAI,QAAQ,QAAQ,GAAG;AACrB,kBAAQ;AAAA,YACN,KAAK,YAAa,OAAO,KAAK,KAAM,KAAK;AAAA,YACzC,KAAK,YAAa,OAAO,KAAK,MAAO,QAAQ,EAAE;AAAA,UACjD;AAAA,QACF;AAAA,MACF;AAEA,cAAQ,OAAO,IAAI,EAAE;AAAA,IACvB,OAAO;AACL,cAAQ,OAAO,IAAI,EAAE;AACrB,cAAQ,OAAO,IAAI,EAAE;AACrB,cAAQ,OAAO,IAAI,EAAE;AAAA,IACvB;AAEA,YAAQ,KAAK;AAEb,YAAQ;AAAA,MACN,YAAY,CAAC;AAAA,MACb,YAAY,CAAC;AAAA,MACb,YAAY,CAAC;AAAA,MACb,YAAY,CAAC;AAAA,MACb;AAAA,MACA;AAAA,IACF;AAEA,YAAQ;AAAA,MACN,iBAAiB,CAAC,IAAI;AAAA,MACtB,iBAAiB,CAAC,IAAI;AAAA,IACxB;AAEA,YAAQ;AAAA,MACN,mBAAmB;AAAA,MACnB,CAAC,mBAAmB;AAAA,IACtB;AAEA,YAAQ,UAAU,cAAc,QAAQ,GAAG,CAAC;AAC5C,YAAQ,QAAQ;AAAA,EAClB,CAAC;AAED,gBAAc,aAAa;AAC3B,aAAW,KAAK,cAAc,MAAM;AAEpC,MAAI,aAAa;AACf,YAAQ,KAAK;AAEb,YAAQ,2BAA2B;AACnC,YAAQ,cAAc;AACtB,YAAQ,YAAY;AAEpB,kBAAc,aAAa,EAAE,QAAQ,SAAU,UAAU,GAAG,KAAK;AAC/D,YAAM,SAAS,SAAS;AACxB,YAAM,MAAM,OAAO,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,KAAK;AAC/C,YAAM,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,KAAK;AAChD,YAAM,MAAM,OAAO,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,KAAK;AAC/C,YAAM,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,KAAK;AAChD,YAAM,MAAM,OAAO,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,KAAK;AAC/C,YAAM,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,KAAK;AAEhD,cAAQ,UAAU;AAClB,cAAQ,OAAO,IAAI,EAAE;AACrB,cAAQ,OAAO,IAAI,EAAE;AACrB,cAAQ,OAAO,IAAI,EAAE;AACrB,cAAQ,UAAU;AAClB,cAAQ,OAAO;AAAA,IACjB,CAAC;AAED,YAAQ,QAAQ;AAAA,EAClB;AACA,SAAO,QAAQ;AACjB;;;AChbA,IAAM,YAAN,cAAwB,aAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS3B,YAAY,WAAW,OAAO,KAAK,aAAa,kBAAkB,SAAS;AACzE,UAAM,WAAW,OAAO,OAAO;AAM/B,SAAK,eAAe;AAQpB,SAAK,OAAO;AAEZ,SAAK,MAAM;AAMX,SAAK,SAAS,IAAI,MAAM;AACxB,QAAI,gBAAgB,MAAM;AACxB,WAAK,OAAO,cAAc;AAAA,IAC5B;AAMA,SAAK,YAAY;AAMjB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,SAAS;AAChB,SAAK,SAAS;AACd,SAAK,QAAQ,kBAAU;AACvB,SAAK,eAAe;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB;AAClB,SAAK,QAAQ,kBAAU;AACvB,SAAK,eAAe;AACpB,SAAK,SAAS,cAAc;AAC5B,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB;AACjB,UAAM;AAAA;AAAA,MAAyC,KAAK;AAAA;AACpD,QAAI,MAAM,gBAAgB,MAAM,eAAe;AAC7C,WAAK,QAAQ,kBAAU;AAAA,IACzB,OAAO;AACL,WAAK,QAAQ,kBAAU;AAAA,IACzB;AACA,SAAK,eAAe;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuCA,OAAO;AACL,QAAI,KAAK,SAAS,kBAAU,OAAO;AACjC,WAAK,QAAQ,kBAAU;AACvB,WAAK,SAAS,IAAI,MAAM;AACxB,UAAI,KAAK,iBAAiB,MAAM;AAC9B,aAAK,OAAO,cAAc,KAAK;AAAA,MACjC;AAAA,IACF;AACA,QAAI,KAAK,SAAS,kBAAU,MAAM;AAChC,WAAK,QAAQ,kBAAU;AACvB,WAAK,QAAQ;AACb,WAAK,kBAAkB,MAAM,KAAK,IAAI;AACtC,WAAK,YAAY;AAAA,QACf,KAAK;AAAA,QACL,KAAK,iBAAiB,KAAK,IAAI;AAAA,QAC/B,KAAK,kBAAkB,KAAK,IAAI;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU;AACf,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AACF;AAMA,SAAS,gBAAgB;AACvB,QAAM,MAAM,sBAAsB,GAAG,CAAC;AACtC,MAAI,YAAY;AAChB,MAAI,SAAS,GAAG,GAAG,GAAG,CAAC;AACvB,SAAO,IAAI;AACb;AAEA,IAAO,oBAAQ;;;AClLR,IAAM,kBAAkB;;;ACuB/B,IAAM,kBAAkB;AAUxB,IAAM,qBAAqB;AAO3B,IAAM,gBAAN,MAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASlB,YACE,YACA,YACA,cACA,iBACA,gBACA,uBACA;AAKA,SAAK,cAAc;AAMnB,SAAK,cAAc;AAGnB,QAAI,oBAAoB,CAAC;AACzB,UAAM,eAAe,aAAa,KAAK,aAAa,KAAK,WAAW;AAOpE,SAAK,gBAAgB,SAAU,GAAG;AAChC,YAAM,MAAM,EAAE,CAAC,IAAI,MAAM,EAAE,CAAC;AAC5B,UAAI,CAAC,kBAAkB,GAAG,GAAG;AAC3B,0BAAkB,GAAG,IAAI,aAAa,CAAC;AAAA,MACzC;AACA,aAAO,kBAAkB,GAAG;AAAA,IAC9B;AAMA,SAAK,mBAAmB;AAMxB,SAAK,yBAAyB,iBAAiB;AAM/C,SAAK,aAAa,CAAC;AAOnB,SAAK,kBAAkB;AAMvB,SAAK,oBACH,KAAK,YAAY,SAAS,KAC1B,CAAC,CAAC,mBACF,CAAC,CAAC,KAAK,YAAY,UAAU,KAC7B,SAAS,eAAe,KAAK,SAAS,KAAK,YAAY,UAAU,CAAC;AAMpE,SAAK,oBAAoB,KAAK,YAAY,UAAU,IAChD,SAAS,KAAK,YAAY,UAAU,CAAC,IACrC;AAMJ,SAAK,oBAAoB,KAAK,YAAY,UAAU,IAChD,SAAS,KAAK,YAAY,UAAU,CAAC,IACrC;AAEJ,UAAM,qBAAqB,WAAW,YAAY;AAClD,UAAM,sBAAsB,YAAY,YAAY;AACpD,UAAM,yBAAyB,eAAe,YAAY;AAC1D,UAAM,wBAAwB,cAAc,YAAY;AACxD,UAAM,gBAAgB,KAAK,cAAc,kBAAkB;AAC3D,UAAM,iBAAiB,KAAK,cAAc,mBAAmB;AAC7D,UAAM,oBAAoB,KAAK,cAAc,sBAAsB;AACnE,UAAM,mBAAmB,KAAK,cAAc,qBAAqB;AAYjE,UAAM,iBACJ,mBACC,wBACG,KAAK;AAAA,MACH;AAAA,MACA,KAAK;AAAA,QACH,KAAK;AAAA,UACH,QAAQ,YAAY,KACjB,wBAAwB,wBAAwB,MAAM;AAAA,QAC3D;AAAA,MACF;AAAA,IACF,IACA;AAEN,SAAK;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAI,KAAK,iBAAiB;AACxB,UAAI,YAAY;AAChB,WAAK,WAAW,QAAQ,SAAU,UAAU,GAAG,KAAK;AAClD,oBAAY,KAAK;AAAA,UACf;AAAA,UACA,SAAS,OAAO,CAAC,EAAE,CAAC;AAAA,UACpB,SAAS,OAAO,CAAC,EAAE,CAAC;AAAA,UACpB,SAAS,OAAO,CAAC,EAAE,CAAC;AAAA,QACtB;AAAA,MACF,CAAC;AAID,WAAK,WAAW,QAAQ,CAAC,aAAa;AACpC,YACE,KAAK;AAAA,UACH,SAAS,OAAO,CAAC,EAAE,CAAC;AAAA,UACpB,SAAS,OAAO,CAAC,EAAE,CAAC;AAAA,UACpB,SAAS,OAAO,CAAC,EAAE,CAAC;AAAA,QACtB,IACE,YACF,KAAK,oBAAoB,GACzB;AACA,gBAAM,cAAc;AAAA,YAClB,CAAC,SAAS,OAAO,CAAC,EAAE,CAAC,GAAG,SAAS,OAAO,CAAC,EAAE,CAAC,CAAC;AAAA,YAC7C,CAAC,SAAS,OAAO,CAAC,EAAE,CAAC,GAAG,SAAS,OAAO,CAAC,EAAE,CAAC,CAAC;AAAA,YAC7C,CAAC,SAAS,OAAO,CAAC,EAAE,CAAC,GAAG,SAAS,OAAO,CAAC,EAAE,CAAC,CAAC;AAAA,UAC/C;AACA,cAAI,YAAY,CAAC,EAAE,CAAC,IAAI,YAAY,KAAK,oBAAoB,GAAG;AAC9D,wBAAY,CAAC,EAAE,CAAC,KAAK,KAAK;AAAA,UAC5B;AACA,cAAI,YAAY,CAAC,EAAE,CAAC,IAAI,YAAY,KAAK,oBAAoB,GAAG;AAC9D,wBAAY,CAAC,EAAE,CAAC,KAAK,KAAK;AAAA,UAC5B;AACA,cAAI,YAAY,CAAC,EAAE,CAAC,IAAI,YAAY,KAAK,oBAAoB,GAAG;AAC9D,wBAAY,CAAC,EAAE,CAAC,KAAK,KAAK;AAAA,UAC5B;AAKA,gBAAM,OAAO,KAAK;AAAA,YAChB,YAAY,CAAC,EAAE,CAAC;AAAA,YAChB,YAAY,CAAC,EAAE,CAAC;AAAA,YAChB,YAAY,CAAC,EAAE,CAAC;AAAA,UAClB;AACA,gBAAM,OAAO,KAAK;AAAA,YAChB,YAAY,CAAC,EAAE,CAAC;AAAA,YAChB,YAAY,CAAC,EAAE,CAAC;AAAA,YAChB,YAAY,CAAC,EAAE,CAAC;AAAA,UAClB;AACA,cAAI,OAAO,OAAO,KAAK,oBAAoB,GAAG;AAC5C,qBAAS,SAAS;AAAA,UACpB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,wBAAoB,CAAC;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,aAAa,GAAG,GAAG,GAAG,MAAM,MAAM,MAAM;AACtC,SAAK,WAAW,KAAK;AAAA,MACnB,QAAQ,CAAC,MAAM,MAAM,IAAI;AAAA,MACzB,QAAQ,CAAC,GAAG,GAAG,CAAC;AAAA,IAClB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,SAAS,GAAG,GAAG,GAAG,GAAG,MAAM,MAAM,MAAM,MAAM,gBAAgB;AAC3D,UAAM,mBAAmB,eAAe,CAAC,MAAM,MAAM,MAAM,IAAI,CAAC;AAChE,UAAM,kBAAkB,KAAK,oBACzB,SAAS,gBAAgB,IAAI,KAAK,oBAClC;AACJ,UAAM;AAAA;AAAA,MAA0C,KAAK;AAAA;AAIrD,UAAM,SACJ,KAAK,YAAY,SAAS,KAC1B,kBAAkB,OAClB,kBAAkB;AAEpB,QAAI,mBAAmB;AAEvB,QAAI,iBAAiB,GAAG;AACtB,UAAI,KAAK,YAAY,SAAS,KAAK,KAAK,mBAAmB;AACzD,cAAM,mBAAmB,eAAe,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACpD,cAAM,kBACJ,SAAS,gBAAgB,IAAI,KAAK;AACpC,2BACE,kBAAkB,sBAAsB;AAAA,MAC5C;AACA,UAAI,CAAC,UAAU,KAAK,YAAY,SAAS,KAAK,iBAAiB;AAC7D,2BACE,kBAAkB,sBAAsB;AAAA,MAC5C;AAAA,IACF;AAEA,QAAI,CAAC,oBAAoB,KAAK,kBAAkB;AAC9C,UACE,SAAS,iBAAiB,CAAC,CAAC,KAC5B,SAAS,iBAAiB,CAAC,CAAC,KAC5B,SAAS,iBAAiB,CAAC,CAAC,KAC5B,SAAS,iBAAiB,CAAC,CAAC,GAC5B;AACA,YAAI,CAAC,WAAW,kBAAkB,KAAK,gBAAgB,GAAG;AAExD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,cAAc;AAElB,QAAI,CAAC,kBAAkB;AACrB,UACE,CAAC,SAAS,KAAK,CAAC,CAAC,KACjB,CAAC,SAAS,KAAK,CAAC,CAAC,KACjB,CAAC,SAAS,KAAK,CAAC,CAAC,KACjB,CAAC,SAAS,KAAK,CAAC,CAAC,KACjB,CAAC,SAAS,KAAK,CAAC,CAAC,KACjB,CAAC,SAAS,KAAK,CAAC,CAAC,KACjB,CAAC,SAAS,KAAK,CAAC,CAAC,KACjB,CAAC,SAAS,KAAK,CAAC,CAAC,GACjB;AACA,YAAI,iBAAiB,GAAG;AACtB,6BAAmB;AAAA,QACrB,OAAO;AAGL,yBACG,CAAC,SAAS,KAAK,CAAC,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,MAC/C,CAAC,SAAS,KAAK,CAAC,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,MAC/C,CAAC,SAAS,KAAK,CAAC,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,MAC/C,CAAC,SAAS,KAAK,CAAC,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI;AAClD,cACE,eAAe,KACf,eAAe,KACf,eAAe,KACf,eAAe,GACf;AACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,iBAAiB,GAAG;AACtB,UAAI,CAAC,kBAAkB;AACrB,cAAM,SAAS,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;AACpD,cAAM,YAAY,KAAK,cAAc,MAAM;AAE3C,YAAI;AACJ,YAAI,QAAQ;AACV,gBAAM,mBACH,OAAO,KAAK,CAAC,GAAG,gBAAgB,IAC/B,OAAO,KAAK,CAAC,GAAG,gBAAgB,KAClC;AACF,eAAK,kBAAkB,OAAO,UAAU,CAAC,GAAG,gBAAgB;AAAA,QAC9D,OAAO;AACL,gBAAM,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,IAAI,UAAU,CAAC;AAAA,QAC5C;AACA,cAAM,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,IAAI,UAAU,CAAC;AAChD,cAAM,wBAAwB,KAAK,KAAK,KAAK;AAC7C,2BAAmB,wBAAwB,KAAK;AAAA,MAClD;AACA,UAAI,kBAAkB;AACpB,YAAI,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG;AAElD,gBAAM,KAAK,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;AAChD,gBAAM,QAAQ,KAAK,cAAc,EAAE;AACnC,gBAAM,KAAK,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;AAChD,gBAAM,QAAQ,KAAK,cAAc,EAAE;AAEnC,eAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,iBAAiB;AAAA,UACnB;AACA,eAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,iBAAiB;AAAA,UACnB;AAAA,QACF,OAAO;AAEL,gBAAM,KAAK,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;AAChD,gBAAM,QAAQ,KAAK,cAAc,EAAE;AACnC,gBAAM,KAAK,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;AAChD,gBAAM,QAAQ,KAAK,cAAc,EAAE;AAEnC,eAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,iBAAiB;AAAA,UACnB;AACA,eAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,iBAAiB;AAAA,UACnB;AAAA,QACF;AACA;AAAA,MACF;AAAA,IACF;AAEA,QAAI,QAAQ;AACV,UAAI,CAAC,KAAK,mBAAmB;AAC3B;AAAA,MACF;AACA,WAAK,kBAAkB;AAAA,IACzB;AAMA,SAAK,cAAc,OAAQ,GAAG;AAC5B,WAAK,aAAa,GAAG,GAAG,GAAG,MAAM,MAAM,IAAI;AAAA,IAC7C;AACA,SAAK,cAAc,OAAQ,GAAG;AAC5B,WAAK,aAAa,GAAG,GAAG,GAAG,MAAM,MAAM,IAAI;AAAA,IAC7C;AACA,QAAI,aAAa;AAEf,WAAK,cAAc,OAAQ,GAAG;AAC5B,aAAK,aAAa,GAAG,GAAG,GAAG,MAAM,MAAM,IAAI;AAAA,MAC7C;AACA,WAAK,cAAc,MAAQ,GAAG;AAC5B,aAAK,aAAa,GAAG,GAAG,GAAG,MAAM,MAAM,IAAI;AAAA,MAC7C;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB;AACtB,UAAM,SAAS,YAAY;AAE3B,SAAK,WAAW,QAAQ,SAAU,UAAU,GAAG,KAAK;AAClD,YAAM,MAAM,SAAS;AACrB,uBAAiB,QAAQ,IAAI,CAAC,CAAC;AAC/B,uBAAiB,QAAQ,IAAI,CAAC,CAAC;AAC/B,uBAAiB,QAAQ,IAAI,CAAC,CAAC;AAAA,IACjC,CAAC;AAED,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAO,wBAAQ;;;ACxdf,IAAM,aAAN,cAAyB,aAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgB5B,YACE,YACA,gBACA,YACA,gBACA,WACA,kBACA,YACA,QACA,iBACA,gBACA,aACA,aACA;AACA,UAAM,WAAW,kBAAU,MAAM,EAAC,aAAa,CAAC,CAAC,YAAW,CAAC;AAM7D,SAAK,eAAe,gBAAgB,SAAY,cAAc;AAM9D,SAAK,cAAc;AAMnB,SAAK,UAAU;AAMf,SAAK,UAAU;AAMf,SAAK,kBAAkB;AAMvB,SAAK,kBAAkB;AAMvB,SAAK,oBAAoB,mBAAmB,mBAAmB;AAM/D,SAAK,eAAe,CAAC;AAMrB,SAAK,uBAAuB;AAM5B,SAAK,WAAW;AAEhB,UAAM,eAAe,eAAe;AAAA,MAClC,KAAK;AAAA,IACP;AACA,UAAM,kBAAkB,KAAK,gBAAgB,UAAU;AACvD,QAAI,kBAAkB,KAAK,gBAAgB,UAAU;AAErD,UAAM,sBAAsB,kBACxB,gBAAgB,cAAc,eAAe,IAC7C;AAEJ,QAAI,QAAQ,mBAAmB,MAAM,GAAG;AAGtC,WAAK,QAAQ,kBAAU;AACvB;AAAA,IACF;AAEA,UAAM,mBAAmB,WAAW,UAAU;AAC9C,QAAI,kBAAkB;AACpB,UAAI,CAAC,iBAAiB;AACpB,0BAAkB;AAAA,MACpB,OAAO;AACL,0BAAkB,gBAAgB,iBAAiB,gBAAgB;AAAA,MACrE;AAAA,IACF;AAEA,UAAM,mBAAmB,eAAe;AAAA,MACtC,KAAK,kBAAkB,CAAC;AAAA,IAC1B;AAEA,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAI,CAAC,SAAS,gBAAgB,KAAK,oBAAoB,GAAG;AAGxD,WAAK,QAAQ,kBAAU;AACvB;AAAA,IACF;AAEA,UAAM,yBACJ,mBAAmB,SAAY,iBAAiB;AAMlD,SAAK,iBAAiB,IAAI;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,mBAAmB;AAAA,MACnB;AAAA,IACF;AAEA,QAAI,KAAK,eAAe,aAAa,EAAE,WAAW,GAAG;AAEnD,WAAK,QAAQ,kBAAU;AACvB;AAAA,IACF;AAEA,SAAK,WAAW,eAAe,kBAAkB,gBAAgB;AACjE,QAAI,eAAe,KAAK,eAAe,sBAAsB;AAE7D,QAAI,iBAAiB;AACnB,UAAI,WAAW,SAAS,GAAG;AACzB,qBAAa,CAAC,IAAI;AAAA,UAChB,aAAa,CAAC;AAAA,UACd,gBAAgB,CAAC;AAAA,UACjB,gBAAgB,CAAC;AAAA,QACnB;AACA,qBAAa,CAAC,IAAI;AAAA,UAChB,aAAa,CAAC;AAAA,UACd,gBAAgB,CAAC;AAAA,UACjB,gBAAgB,CAAC;AAAA,QACnB;AAAA,MACF,OAAO;AACL,uBAAe,gBAAgB,cAAc,eAAe;AAAA,MAC9D;AAAA,IACF;AAEA,QAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,WAAK,QAAQ,kBAAU;AAAA,IACzB,OAAO;AACL,YAAM,cAAc,eAAe;AAAA,QACjC;AAAA,QACA,KAAK;AAAA,MACP;AAEA,eAAS,OAAO,YAAY,MAAM,QAAQ,YAAY,MAAM,QAAQ;AAClE,iBAAS,OAAO,YAAY,MAAM,QAAQ,YAAY,MAAM,QAAQ;AAClE,gBAAM,OAAO,gBAAgB,KAAK,UAAU,MAAM,MAAM,UAAU;AAClE,cAAI,MAAM;AACR,iBAAK,aAAa,KAAK,IAAI;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAEA,UAAI,KAAK,aAAa,WAAW,GAAG;AAClC,aAAK,QAAQ,kBAAU;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,UAAM,UAAU,CAAC;AACjB,SAAK,aAAa,QAAQ,CAAC,SAAS;AAClC,UAAI,QAAQ,KAAK,SAAS,KAAK,kBAAU,QAAQ;AAC/C,gBAAQ,KAAK;AAAA,UACX,QAAQ,KAAK,gBAAgB,mBAAmB,KAAK,SAAS;AAAA,UAC9D,OAAO,KAAK,SAAS;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,SAAK,aAAa,SAAS;AAE3B,QAAI,QAAQ,WAAW,GAAG;AACxB,WAAK,QAAQ,kBAAU;AAAA,IACzB,OAAO;AACL,YAAM,IAAI,KAAK,kBAAkB,CAAC;AAClC,YAAM,OAAO,KAAK,gBAAgB,YAAY,CAAC;AAC/C,YAAM,QAAQ,OAAO,SAAS,WAAW,OAAO,KAAK,CAAC;AACtD,YAAM,SAAS,OAAO,SAAS,WAAW,OAAO,KAAK,CAAC;AACvD,YAAM,mBAAmB,KAAK,gBAAgB,cAAc,CAAC;AAC7D,YAAM,mBAAmB,KAAK,gBAAgB;AAAA,QAC5C,KAAK;AAAA,MACP;AAEA,YAAM,eAAe,KAAK,gBAAgB;AAAA,QACxC,KAAK;AAAA,MACP;AAEA,WAAK,UAAU;AAAA,QACb;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA,KAAK,gBAAgB,UAAU;AAAA,QAC/B;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAEA,WAAK,QAAQ,kBAAU;AAAA,IACzB;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,QAAI,KAAK,SAAS,kBAAU,MAAM;AAChC,WAAK,QAAQ,kBAAU;AACvB,WAAK,QAAQ;AAEb,UAAI,aAAa;AAEjB,WAAK,uBAAuB,CAAC;AAC7B,WAAK,aAAa,QAAQ,CAAC,SAAS;AAClC,cAAM,QAAQ,KAAK,SAAS;AAC5B,YAAI,SAAS,kBAAU,QAAQ,SAAS,kBAAU,SAAS;AACzD;AAEA,gBAAM,kBAAkB;AAAA,YACtB;AAAA,YACA,kBAAU;AAAA,YACV,SAAU,GAAG;AACX,oBAAMA,SAAQ,KAAK,SAAS;AAC5B,kBACEA,UAAS,kBAAU,UACnBA,UAAS,kBAAU,SACnBA,UAAS,kBAAU,OACnB;AACA,8BAAc,eAAe;AAC7B;AACA,oBAAI,eAAe,GAAG;AACpB,uBAAK,iBAAiB;AACtB,uBAAK,WAAW;AAAA,gBAClB;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,UACF;AACA,eAAK,qBAAqB,KAAK,eAAe;AAAA,QAChD;AAAA,MACF,CAAC;AAED,UAAI,eAAe,GAAG;AACpB,mBAAW,KAAK,WAAW,KAAK,IAAI,GAAG,CAAC;AAAA,MAC1C,OAAO;AACL,aAAK,aAAa,QAAQ,SAAU,MAAM,GAAG,KAAK;AAChD,gBAAM,QAAQ,KAAK,SAAS;AAC5B,cAAI,SAAS,kBAAU,MAAM;AAC3B,iBAAK,KAAK;AAAA,UACZ;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,SAAK,qBAAqB,QAAQ,aAAa;AAC/C,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,QAAI,KAAK,SAAS;AAChB,oBAAc,KAAK,QAAQ,WAAW,IAAI,CAAC;AAC3C,iBAAW,KAAK,KAAK,OAAO;AAC5B,WAAK,UAAU;AAAA,IACjB;AACA,UAAM,QAAQ;AAAA,EAChB;AACF;AAEA,IAAOC,gBAAQ;", "names": ["state", "Tile_default"]}