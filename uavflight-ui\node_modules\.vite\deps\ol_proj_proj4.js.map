{"version": 3, "sources": ["../../ol/proj/proj4.js"], "sourcesContent": ["/**\n * @module ol/proj/proj4\n */\nimport Projection from './Projection.js';\nimport {\n  addCoordinateTransforms,\n  addEquivalentProjections,\n  addProjection,\n  createSafeCoordinateTransform,\n  get,\n} from '../proj.js';\nimport {get as getTransform} from './transforms.js';\n\n/**\n * @type {import(\"proj4\")|null}\n */\nlet registered = null;\n\n/**\n * @return {boolean} Proj4 has been registered.\n */\nexport function isRegistered() {\n  return !!registered;\n}\n\n/**\n * Unsets the shared proj4 previously set with register.\n */\nexport function unregister() {\n  registered = null;\n}\n\n/**\n * Make projections defined in proj4 (with `proj4.defs()`) available in\n * OpenLayers. Requires proj4 >= 2.8.0.\n *\n * This function should be called whenever changes are made to the proj4\n * registry, e.g. after calling `proj4.defs()`. Existing transforms will not be\n * modified by this function.\n *\n * @param {import(\"proj4\")} proj4 Proj4.\n * @api\n */\nexport function register(proj4) {\n  registered = proj4;\n\n  const projCodes = Object.keys(proj4.defs);\n  const len = projCodes.length;\n  let i, j;\n  for (i = 0; i < len; ++i) {\n    const code = projCodes[i];\n    if (!get(code)) {\n      const def = proj4.defs(code);\n      let units = /** @type {import(\"./Units.js\").Units} */ (def.units);\n      if (!units && def.projName === 'longlat') {\n        units = 'degrees';\n      }\n      addProjection(\n        new Projection({\n          code: code,\n          axisOrientation: def.axis,\n          metersPerUnit: def.to_meter,\n          units,\n        })\n      );\n    }\n  }\n  for (i = 0; i < len; ++i) {\n    const code1 = projCodes[i];\n    const proj1 = get(code1);\n    for (j = 0; j < len; ++j) {\n      const code2 = projCodes[j];\n      const proj2 = get(code2);\n      if (!getTransform(code1, code2)) {\n        if (proj4.defs[code1] === proj4.defs[code2]) {\n          addEquivalentProjections([proj1, proj2]);\n        } else {\n          const transform = proj4(code1, code2);\n          addCoordinateTransforms(\n            proj1,\n            proj2,\n            createSafeCoordinateTransform(proj1, proj2, transform.forward),\n            createSafeCoordinateTransform(proj2, proj1, transform.inverse)\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * @param {number} code The EPSG code.\n * @return {Promise<string>} The proj4 definition.\n */\nlet epsgLookup = async function (code) {\n  const response = await fetch(`https://epsg.io/${code}.proj4`);\n  if (!response.ok) {\n    throw new Error(`Unexpected response from epsg.io: ${response.status}`);\n  }\n  return response.text();\n};\n\n/**\n * Set the lookup function for getting proj4 definitions given an EPSG code.\n * By default, the {@link module:ol/proj/proj4.fromEPSGCode} function uses the\n * epsg.io website for proj4 definitions.  This can be changed by providing a\n * different lookup function.\n *\n * @param {function(number):Promise<string>} func The lookup function.\n * @api\n */\nexport function setEPSGLookup(func) {\n  epsgLookup = func;\n}\n\n/**\n * Get the current EPSG lookup function.\n *\n * @return {function(number):Promise<string>} The EPSG lookup function.\n */\nexport function getEPSGLookup() {\n  return epsgLookup;\n}\n\n/**\n * Get a projection from an EPSG code.  This function fetches the projection\n * definition from the epsg.io website, registers this definition for use with\n * proj4, and returns a configured projection.  You must call import proj4 and\n * call {@link module:ol/proj/proj4.register} before using this function.\n *\n * If the projection definition is already registered with proj4, it will not\n * be fetched again (so it is ok to call this function multiple times with the\n * same code).\n *\n * @param {number|string} code The EPSG code (e.g. 4326 or 'EPSG:4326').\n * @return {Promise<Projection>} The projection.\n * @api\n */\nexport async function fromEPSGCode(code) {\n  if (typeof code === 'string') {\n    code = parseInt(code.split(':').pop(), 10);\n  }\n\n  const proj4 = registered;\n  if (!proj4) {\n    throw new Error('Proj4 must be registered first with register(proj4)');\n  }\n\n  const epsgCode = 'EPSG:' + code;\n  if (proj4.defs(epsgCode)) {\n    return get(epsgCode);\n  }\n\n  proj4.defs(epsgCode, await epsgLookup(code));\n  register(proj4);\n\n  return get(epsgCode);\n}\n\n/**\n * Generate an EPSG lookup function which uses the MapTiler Coordinates API to find projection\n * definitions which do not require proj4 to be configured to handle `+nadgrids` parameters.\n * Call {@link module:ol/proj/proj4.setEPSGLookup} use the function for lookups\n * `setEPSGLookup(epsgLookupMapTiler('{YOUR_MAPTILER_API_KEY_HERE}'))`.\n *\n * @param {string} key MapTiler API key.  Get your own API key at https://www.maptiler.com/cloud/.\n * @return {function(number):Promise<string>} The EPSG lookup function.\n * @api\n */\nexport function epsgLookupMapTiler(key) {\n  return async function (code) {\n    const response = await fetch(\n      `https://api.maptiler.com/coordinates/search/code:${code}.json?transformations=true&exports=true&key=${key}`\n    );\n    if (!response.ok) {\n      throw new Error(\n        `Unexpected response from maptiler.com: ${response.status}`\n      );\n    }\n    return response.json().then((json) => {\n      const results = json['results'];\n      if (results?.length > 0) {\n        const result = results.filter(\n          (r) => r['id']?.['authority'] === 'EPSG' && r['id']?.['code'] === code\n        )[0];\n        if (result) {\n          const transforms = result['transformations'];\n          if (transforms?.length > 0) {\n            // use default transform if it does not require grids\n            const defaultTransform = result['default_transformation'];\n            if (\n              transforms.filter(\n                (t) =>\n                  t['id']?.['authority'] === defaultTransform?.['authority'] &&\n                  t['id']?.['code'] === defaultTransform?.['code'] &&\n                  t['grids']?.length === 0\n              ).length > 0\n            ) {\n              return result['exports']?.['proj4'];\n            }\n            // otherwise use most accurate alternative without grids\n            const transform = transforms\n              .filter(\n                (t) =>\n                  t['grids']?.length === 0 &&\n                  t['target_crs']?.['authority'] === 'EPSG' &&\n                  t['target_crs']?.['code'] === 4326 &&\n                  t['deprecated'] === false &&\n                  t['usable'] === true\n              )\n              .sort((t1, t2) => t1['accuracy'] - t2['accuracy'])[0]?.[\n              'exports'\n            ]?.['proj4'];\n            if (transform) {\n              return transform;\n            }\n          }\n          // fallback to default\n          return result['exports']?.['proj4'];\n        }\n      }\n    });\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAgBA,IAAI,aAAa;AAKV,SAAS,eAAe;AAC7B,SAAO,CAAC,CAAC;AACX;AAKO,SAAS,aAAa;AAC3B,eAAa;AACf;AAaO,SAAS,SAAS,OAAO;AAC9B,eAAa;AAEb,QAAM,YAAY,OAAO,KAAK,MAAM,IAAI;AACxC,QAAM,MAAM,UAAU;AACtB,MAAI,GAAG;AACP,OAAK,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AACxB,UAAM,OAAO,UAAU,CAAC;AACxB,QAAI,CAACA,KAAI,IAAI,GAAG;AACd,YAAM,MAAM,MAAM,KAAK,IAAI;AAC3B,UAAI;AAAA;AAAA,QAAmD,IAAI;AAAA;AAC3D,UAAI,CAAC,SAAS,IAAI,aAAa,WAAW;AACxC,gBAAQ;AAAA,MACV;AACA;AAAA,QACE,IAAI,mBAAW;AAAA,UACb;AAAA,UACA,iBAAiB,IAAI;AAAA,UACrB,eAAe,IAAI;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,OAAK,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AACxB,UAAM,QAAQ,UAAU,CAAC;AACzB,UAAM,QAAQA,KAAI,KAAK;AACvB,SAAK,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AACxB,YAAM,QAAQ,UAAU,CAAC;AACzB,YAAM,QAAQA,KAAI,KAAK;AACvB,UAAI,CAAC,IAAa,OAAO,KAAK,GAAG;AAC/B,YAAI,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG;AAC3C,mCAAyB,CAAC,OAAO,KAAK,CAAC;AAAA,QACzC,OAAO;AACL,gBAAM,YAAY,MAAM,OAAO,KAAK;AACpC;AAAA,YACE;AAAA,YACA;AAAA,YACA,8BAA8B,OAAO,OAAO,UAAU,OAAO;AAAA,YAC7D,8BAA8B,OAAO,OAAO,UAAU,OAAO;AAAA,UAC/D;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAMA,IAAI,aAAa,eAAgB,MAAM;AACrC,QAAM,WAAW,MAAM,MAAM,mBAAmB,IAAI,QAAQ;AAC5D,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MAAM,qCAAqC,SAAS,MAAM,EAAE;AAAA,EACxE;AACA,SAAO,SAAS,KAAK;AACvB;AAWO,SAAS,cAAc,MAAM;AAClC,eAAa;AACf;AAOO,SAAS,gBAAgB;AAC9B,SAAO;AACT;AAgBA,eAAsB,aAAa,MAAM;AACvC,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,SAAS,KAAK,MAAM,GAAG,EAAE,IAAI,GAAG,EAAE;AAAA,EAC3C;AAEA,QAAM,QAAQ;AACd,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,qDAAqD;AAAA,EACvE;AAEA,QAAM,WAAW,UAAU;AAC3B,MAAI,MAAM,KAAK,QAAQ,GAAG;AACxB,WAAOA,KAAI,QAAQ;AAAA,EACrB;AAEA,QAAM,KAAK,UAAU,MAAM,WAAW,IAAI,CAAC;AAC3C,WAAS,KAAK;AAEd,SAAOA,KAAI,QAAQ;AACrB;AAYO,SAAS,mBAAmB,KAAK;AACtC,SAAO,eAAgB,MAAM;AAC3B,UAAM,WAAW,MAAM;AAAA,MACrB,oDAAoD,IAAI,+CAA+C,GAAG;AAAA,IAC5G;AACA,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,IAAI;AAAA,QACR,0CAA0C,SAAS,MAAM;AAAA,MAC3D;AAAA,IACF;AACA,WAAO,SAAS,KAAK,EAAE,KAAK,CAAC,SAAS;AACpC,YAAM,UAAU,KAAK,SAAS;AAC9B,UAAI,SAAS,SAAS,GAAG;AACvB,cAAM,SAAS,QAAQ;AAAA,UACrB,CAAC,MAAM,EAAE,IAAI,IAAI,WAAW,MAAM,UAAU,EAAE,IAAI,IAAI,MAAM,MAAM;AAAA,QACpE,EAAE,CAAC;AACH,YAAI,QAAQ;AACV,gBAAM,aAAa,OAAO,iBAAiB;AAC3C,cAAI,YAAY,SAAS,GAAG;AAE1B,kBAAM,mBAAmB,OAAO,wBAAwB;AACxD,gBACE,WAAW;AAAA,cACT,CAAC,MACC,EAAE,IAAI,IAAI,WAAW,MAAM,mBAAmB,WAAW,KACzD,EAAE,IAAI,IAAI,MAAM,MAAM,mBAAmB,MAAM,KAC/C,EAAE,OAAO,GAAG,WAAW;AAAA,YAC3B,EAAE,SAAS,GACX;AACA,qBAAO,OAAO,SAAS,IAAI,OAAO;AAAA,YACpC;AAEA,kBAAM,YAAY,WACf;AAAA,cACC,CAAC,MACC,EAAE,OAAO,GAAG,WAAW,KACvB,EAAE,YAAY,IAAI,WAAW,MAAM,UACnC,EAAE,YAAY,IAAI,MAAM,MAAM,QAC9B,EAAE,YAAY,MAAM,SACpB,EAAE,QAAQ,MAAM;AAAA,YACpB,EACC,KAAK,CAAC,IAAI,OAAO,GAAG,UAAU,IAAI,GAAG,UAAU,CAAC,EAAE,CAAC,IACpD,SACF,IAAI,OAAO;AACX,gBAAI,WAAW;AACb,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,iBAAO,OAAO,SAAS,IAAI,OAAO;AAAA,QACpC;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;", "names": ["get"]}