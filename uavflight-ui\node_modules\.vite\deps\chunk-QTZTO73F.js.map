{"version": 3, "sources": ["../../ol/transform.js", "../../ol/geom/flat/transform.js", "../../ol/geom/Geometry.js", "../../ol/geom/SimpleGeometry.js", "../../ol/geom/flat/deflate.js", "../../ol/geom/Point.js", "../../ol/geom/flat/closest.js", "../../ol/geom/flat/simplify.js", "../../ol/geom/flat/inflate.js", "../../ol/geom/flat/area.js", "../../ol/geom/LinearRing.js", "../../ol/geom/flat/reverse.js", "../../ol/geom/flat/orient.js", "../../ol/geom/flat/interiorpoint.js", "../../ol/geom/Polygon.js"], "sourcesContent": ["/**\n * @module ol/transform\n */\nimport {WORKER_OFFSCREEN_CANVAS} from './has.js';\nimport {assert} from './asserts.js';\n\n/**\n * An array representing an affine 2d transformation for use with\n * {@link module:ol/transform} functions. The array has 6 elements.\n * @typedef {!Array<number>} Transform\n * @api\n */\n\n/**\n * Collection of affine 2d transformation functions. The functions work on an\n * array of 6 elements. The element order is compatible with the [SVGMatrix\n * interface](https://developer.mozilla.org/en-US/docs/Web/API/SVGMatrix) and is\n * a subset (elements a to f) of a 3×3 matrix:\n * ```\n * [ a c e ]\n * [ b d f ]\n * [ 0 0 1 ]\n * ```\n */\n\n/**\n * @private\n * @type {Transform}\n */\nconst tmp_ = new Array(6);\n\n/**\n * Create an identity transform.\n * @return {!Transform} Identity transform.\n */\nexport function create() {\n  return [1, 0, 0, 1, 0, 0];\n}\n\n/**\n * Resets the given transform to an identity transform.\n * @param {!Transform} transform Transform.\n * @return {!Transform} Transform.\n */\nexport function reset(transform) {\n  return set(transform, 1, 0, 0, 1, 0, 0);\n}\n\n/**\n * Multiply the underlying matrices of two transforms and return the result in\n * the first transform.\n * @param {!Transform} transform1 Transform parameters of matrix 1.\n * @param {!Transform} transform2 Transform parameters of matrix 2.\n * @return {!Transform} transform1 multiplied with transform2.\n */\nexport function multiply(transform1, transform2) {\n  const a1 = transform1[0];\n  const b1 = transform1[1];\n  const c1 = transform1[2];\n  const d1 = transform1[3];\n  const e1 = transform1[4];\n  const f1 = transform1[5];\n  const a2 = transform2[0];\n  const b2 = transform2[1];\n  const c2 = transform2[2];\n  const d2 = transform2[3];\n  const e2 = transform2[4];\n  const f2 = transform2[5];\n\n  transform1[0] = a1 * a2 + c1 * b2;\n  transform1[1] = b1 * a2 + d1 * b2;\n  transform1[2] = a1 * c2 + c1 * d2;\n  transform1[3] = b1 * c2 + d1 * d2;\n  transform1[4] = a1 * e2 + c1 * f2 + e1;\n  transform1[5] = b1 * e2 + d1 * f2 + f1;\n\n  return transform1;\n}\n\n/**\n * Set the transform components a-f on a given transform.\n * @param {!Transform} transform Transform.\n * @param {number} a The a component of the transform.\n * @param {number} b The b component of the transform.\n * @param {number} c The c component of the transform.\n * @param {number} d The d component of the transform.\n * @param {number} e The e component of the transform.\n * @param {number} f The f component of the transform.\n * @return {!Transform} Matrix with transform applied.\n */\nexport function set(transform, a, b, c, d, e, f) {\n  transform[0] = a;\n  transform[1] = b;\n  transform[2] = c;\n  transform[3] = d;\n  transform[4] = e;\n  transform[5] = f;\n  return transform;\n}\n\n/**\n * Set transform on one matrix from another matrix.\n * @param {!Transform} transform1 Matrix to set transform to.\n * @param {!Transform} transform2 Matrix to set transform from.\n * @return {!Transform} transform1 with transform from transform2 applied.\n */\nexport function setFromArray(transform1, transform2) {\n  transform1[0] = transform2[0];\n  transform1[1] = transform2[1];\n  transform1[2] = transform2[2];\n  transform1[3] = transform2[3];\n  transform1[4] = transform2[4];\n  transform1[5] = transform2[5];\n  return transform1;\n}\n\n/**\n * Transforms the given coordinate with the given transform returning the\n * resulting, transformed coordinate. The coordinate will be modified in-place.\n *\n * @param {Transform} transform The transformation.\n * @param {import(\"./coordinate.js\").Coordinate|import(\"./pixel.js\").Pixel} coordinate The coordinate to transform.\n * @return {import(\"./coordinate.js\").Coordinate|import(\"./pixel.js\").Pixel} return coordinate so that operations can be\n *     chained together.\n */\nexport function apply(transform, coordinate) {\n  const x = coordinate[0];\n  const y = coordinate[1];\n  coordinate[0] = transform[0] * x + transform[2] * y + transform[4];\n  coordinate[1] = transform[1] * x + transform[3] * y + transform[5];\n  return coordinate;\n}\n\n/**\n * Applies rotation to the given transform.\n * @param {!Transform} transform Transform.\n * @param {number} angle Angle in radians.\n * @return {!Transform} The rotated transform.\n */\nexport function rotate(transform, angle) {\n  const cos = Math.cos(angle);\n  const sin = Math.sin(angle);\n  return multiply(transform, set(tmp_, cos, sin, -sin, cos, 0, 0));\n}\n\n/**\n * Applies scale to a given transform.\n * @param {!Transform} transform Transform.\n * @param {number} x Scale factor x.\n * @param {number} y Scale factor y.\n * @return {!Transform} The scaled transform.\n */\nexport function scale(transform, x, y) {\n  return multiply(transform, set(tmp_, x, 0, 0, y, 0, 0));\n}\n\n/**\n * Creates a scale transform.\n * @param {!Transform} target Transform to overwrite.\n * @param {number} x Scale factor x.\n * @param {number} y Scale factor y.\n * @return {!Transform} The scale transform.\n */\nexport function makeScale(target, x, y) {\n  return set(target, x, 0, 0, y, 0, 0);\n}\n\n/**\n * Applies translation to the given transform.\n * @param {!Transform} transform Transform.\n * @param {number} dx Translation x.\n * @param {number} dy Translation y.\n * @return {!Transform} The translated transform.\n */\nexport function translate(transform, dx, dy) {\n  return multiply(transform, set(tmp_, 1, 0, 0, 1, dx, dy));\n}\n\n/**\n * Creates a composite transform given an initial translation, scale, rotation, and\n * final translation (in that order only, not commutative).\n * @param {!Transform} transform The transform (will be modified in place).\n * @param {number} dx1 Initial translation x.\n * @param {number} dy1 Initial translation y.\n * @param {number} sx Scale factor x.\n * @param {number} sy Scale factor y.\n * @param {number} angle Rotation (in counter-clockwise radians).\n * @param {number} dx2 Final translation x.\n * @param {number} dy2 Final translation y.\n * @return {!Transform} The composite transform.\n */\nexport function compose(transform, dx1, dy1, sx, sy, angle, dx2, dy2) {\n  const sin = Math.sin(angle);\n  const cos = Math.cos(angle);\n  transform[0] = sx * cos;\n  transform[1] = sy * sin;\n  transform[2] = -sx * sin;\n  transform[3] = sy * cos;\n  transform[4] = dx2 * sx * cos - dy2 * sx * sin + dx1;\n  transform[5] = dx2 * sy * sin + dy2 * sy * cos + dy1;\n  return transform;\n}\n\n/**\n * Creates a composite transform given an initial translation, scale, rotation, and\n * final translation (in that order only, not commutative). The resulting transform\n * string can be applied as `transform` property of an HTMLElement's style.\n * @param {number} dx1 Initial translation x.\n * @param {number} dy1 Initial translation y.\n * @param {number} sx Scale factor x.\n * @param {number} sy Scale factor y.\n * @param {number} angle Rotation (in counter-clockwise radians).\n * @param {number} dx2 Final translation x.\n * @param {number} dy2 Final translation y.\n * @return {string} The composite css transform.\n * @api\n */\nexport function composeCssTransform(dx1, dy1, sx, sy, angle, dx2, dy2) {\n  return toString(compose(create(), dx1, dy1, sx, sy, angle, dx2, dy2));\n}\n\n/**\n * Invert the given transform.\n * @param {!Transform} source The source transform to invert.\n * @return {!Transform} The inverted (source) transform.\n */\nexport function invert(source) {\n  return makeInverse(source, source);\n}\n\n/**\n * Invert the given transform.\n * @param {!Transform} target Transform to be set as the inverse of\n *     the source transform.\n * @param {!Transform} source The source transform to invert.\n * @return {!Transform} The inverted (target) transform.\n */\nexport function makeInverse(target, source) {\n  const det = determinant(source);\n  assert(det !== 0, 32); // Transformation matrix cannot be inverted\n\n  const a = source[0];\n  const b = source[1];\n  const c = source[2];\n  const d = source[3];\n  const e = source[4];\n  const f = source[5];\n\n  target[0] = d / det;\n  target[1] = -b / det;\n  target[2] = -c / det;\n  target[3] = a / det;\n  target[4] = (c * f - d * e) / det;\n  target[5] = -(a * f - b * e) / det;\n\n  return target;\n}\n\n/**\n * Returns the determinant of the given matrix.\n * @param {!Transform} mat Matrix.\n * @return {number} Determinant.\n */\nexport function determinant(mat) {\n  return mat[0] * mat[3] - mat[1] * mat[2];\n}\n\n/**\n * @type {HTMLElement}\n * @private\n */\nlet transformStringDiv;\n\n/**\n * A rounded string version of the transform.  This can be used\n * for CSS transforms.\n * @param {!Transform} mat Matrix.\n * @return {string} The transform as a string.\n */\nexport function toString(mat) {\n  const transformString = 'matrix(' + mat.join(', ') + ')';\n  if (WORKER_OFFSCREEN_CANVAS) {\n    return transformString;\n  }\n  const node =\n    transformStringDiv || (transformStringDiv = document.createElement('div'));\n  node.style.transform = transformString;\n  return node.style.transform;\n}\n", "/**\n * @module ol/geom/flat/transform\n */\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {import(\"../../transform.js\").Transform} transform Transform.\n * @param {Array<number>} [dest] Destination.\n * @return {Array<number>} Transformed coordinates.\n */\nexport function transform2D(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  transform,\n  dest\n) {\n  dest = dest ? dest : [];\n  let i = 0;\n  for (let j = offset; j < end; j += stride) {\n    const x = flatCoordinates[j];\n    const y = flatCoordinates[j + 1];\n    dest[i++] = transform[0] * x + transform[2] * y + transform[4];\n    dest[i++] = transform[1] * x + transform[3] * y + transform[5];\n  }\n  if (dest && dest.length != i) {\n    dest.length = i;\n  }\n  return dest;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} angle Angle.\n * @param {Array<number>} anchor Rotation anchor point.\n * @param {Array<number>} [dest] Destination.\n * @return {Array<number>} Transformed coordinates.\n */\nexport function rotate(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  angle,\n  anchor,\n  dest\n) {\n  dest = dest ? dest : [];\n  const cos = Math.cos(angle);\n  const sin = Math.sin(angle);\n  const anchorX = anchor[0];\n  const anchorY = anchor[1];\n  let i = 0;\n  for (let j = offset; j < end; j += stride) {\n    const deltaX = flatCoordinates[j] - anchorX;\n    const deltaY = flatCoordinates[j + 1] - anchorY;\n    dest[i++] = anchorX + deltaX * cos - deltaY * sin;\n    dest[i++] = anchorY + deltaX * sin + deltaY * cos;\n    for (let k = j + 2; k < j + stride; ++k) {\n      dest[i++] = flatCoordinates[k];\n    }\n  }\n  if (dest && dest.length != i) {\n    dest.length = i;\n  }\n  return dest;\n}\n\n/**\n * Scale the coordinates.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} sx Scale factor in the x-direction.\n * @param {number} sy Scale factor in the y-direction.\n * @param {Array<number>} anchor Scale anchor point.\n * @param {Array<number>} [dest] Destination.\n * @return {Array<number>} Transformed coordinates.\n */\nexport function scale(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  sx,\n  sy,\n  anchor,\n  dest\n) {\n  dest = dest ? dest : [];\n  const anchorX = anchor[0];\n  const anchorY = anchor[1];\n  let i = 0;\n  for (let j = offset; j < end; j += stride) {\n    const deltaX = flatCoordinates[j] - anchorX;\n    const deltaY = flatCoordinates[j + 1] - anchorY;\n    dest[i++] = anchorX + sx * deltaX;\n    dest[i++] = anchorY + sy * deltaY;\n    for (let k = j + 2; k < j + stride; ++k) {\n      dest[i++] = flatCoordinates[k];\n    }\n  }\n  if (dest && dest.length != i) {\n    dest.length = i;\n  }\n  return dest;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} deltaX Delta X.\n * @param {number} deltaY Delta Y.\n * @param {Array<number>} [dest] Destination.\n * @return {Array<number>} Transformed coordinates.\n */\nexport function translate(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  deltaX,\n  deltaY,\n  dest\n) {\n  dest = dest ? dest : [];\n  let i = 0;\n  for (let j = offset; j < end; j += stride) {\n    dest[i++] = flatCoordinates[j] + deltaX;\n    dest[i++] = flatCoordinates[j + 1] + deltaY;\n    for (let k = j + 2; k < j + stride; ++k) {\n      dest[i++] = flatCoordinates[k];\n    }\n  }\n  if (dest && dest.length != i) {\n    dest.length = i;\n  }\n  return dest;\n}\n", "/**\n * @module ol/geom/Geometry\n */\nimport BaseObject from '../Object.js';\nimport {abstract} from '../util.js';\nimport {\n  compose as composeTransform,\n  create as createTransform,\n} from '../transform.js';\nimport {\n  createEmpty,\n  createOrUpdateEmpty,\n  getHeight,\n  returnOrUpdate,\n} from '../extent.js';\nimport {get as getProjection, getTransform} from '../proj.js';\nimport {memoizeOne} from '../functions.js';\nimport {transform2D} from './flat/transform.js';\n\n/**\n * @typedef {'XY' | 'XYZ' | 'XYM' | 'XYZM'} GeometryLayout\n * The coordinate layout for geometries, indicating whether a 3rd or 4th z ('Z')\n * or measure ('M') coordinate is available.\n */\n\n/**\n * @typedef {'Point' | 'LineString' | 'LinearRing' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon' | 'GeometryCollection' | 'Circle'} Type\n * The geometry type.  One of `'Point'`, `'LineString'`, `'LinearRing'`,\n * `'Polygon'`, `'MultiPoint'`, `'MultiLineString'`, `'MultiPolygon'`,\n * `'GeometryCollection'`, or `'Circle'`.\n */\n\n/**\n * @type {import(\"../transform.js\").Transform}\n */\nconst tmpTransform = createTransform();\n\n/**\n * @classdesc\n * Abstract base class; normally only used for creating subclasses and not\n * instantiated in apps.\n * Base class for vector geometries.\n *\n * To get notified of changes to the geometry, register a listener for the\n * generic `change` event on your geometry instance.\n *\n * @abstract\n * @api\n */\nclass Geometry extends BaseObject {\n  constructor() {\n    super();\n\n    /**\n     * @private\n     * @type {import(\"../extent.js\").Extent}\n     */\n    this.extent_ = createEmpty();\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.extentRevision_ = -1;\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.simplifiedGeometryMaxMinSquaredTolerance = 0;\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.simplifiedGeometryRevision = 0;\n\n    /**\n     * Get a transformed and simplified version of the geometry.\n     * @abstract\n     * @param {number} revision The geometry revision.\n     * @param {number} squaredTolerance Squared tolerance.\n     * @param {import(\"../proj.js\").TransformFunction} [transform] Optional transform function.\n     * @return {Geometry} Simplified geometry.\n     */\n    this.simplifyTransformedInternal = memoizeOne(function (\n      revision,\n      squaredTolerance,\n      transform\n    ) {\n      if (!transform) {\n        return this.getSimplifiedGeometry(squaredTolerance);\n      }\n      const clone = this.clone();\n      clone.applyTransform(transform);\n      return clone.getSimplifiedGeometry(squaredTolerance);\n    });\n  }\n\n  /**\n   * Get a transformed and simplified version of the geometry.\n   * @abstract\n   * @param {number} squaredTolerance Squared tolerance.\n   * @param {import(\"../proj.js\").TransformFunction} [transform] Optional transform function.\n   * @return {Geometry} Simplified geometry.\n   */\n  simplifyTransformed(squaredTolerance, transform) {\n    return this.simplifyTransformedInternal(\n      this.getRevision(),\n      squaredTolerance,\n      transform\n    );\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @abstract\n   * @return {!Geometry} Clone.\n   */\n  clone() {\n    return abstract();\n  }\n\n  /**\n   * @abstract\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    return abstract();\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @return {boolean} Contains (x, y).\n   */\n  containsXY(x, y) {\n    const coord = this.getClosestPoint([x, y]);\n    return coord[0] === x && coord[1] === y;\n  }\n\n  /**\n   * Return the closest point of the geometry to the passed point as\n   * {@link module:ol/coordinate~Coordinate coordinate}.\n   * @param {import(\"../coordinate.js\").Coordinate} point Point.\n   * @param {import(\"../coordinate.js\").Coordinate} [closestPoint] Closest point.\n   * @return {import(\"../coordinate.js\").Coordinate} Closest point.\n   * @api\n   */\n  getClosestPoint(point, closestPoint) {\n    closestPoint = closestPoint ? closestPoint : [NaN, NaN];\n    this.closestPointXY(point[0], point[1], closestPoint, Infinity);\n    return closestPoint;\n  }\n\n  /**\n   * Returns true if this geometry includes the specified coordinate. If the\n   * coordinate is on the boundary of the geometry, returns false.\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @return {boolean} Contains coordinate.\n   * @api\n   */\n  intersectsCoordinate(coordinate) {\n    return this.containsXY(coordinate[0], coordinate[1]);\n  }\n\n  /**\n   * @abstract\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @protected\n   * @return {import(\"../extent.js\").Extent} extent Extent.\n   */\n  computeExtent(extent) {\n    return abstract();\n  }\n\n  /**\n   * Get the extent of the geometry.\n   * @param {import(\"../extent.js\").Extent} [extent] Extent.\n   * @return {import(\"../extent.js\").Extent} extent Extent.\n   * @api\n   */\n  getExtent(extent) {\n    if (this.extentRevision_ != this.getRevision()) {\n      const extent = this.computeExtent(this.extent_);\n      if (isNaN(extent[0]) || isNaN(extent[1])) {\n        createOrUpdateEmpty(extent);\n      }\n      this.extentRevision_ = this.getRevision();\n    }\n    return returnOrUpdate(this.extent_, extent);\n  }\n\n  /**\n   * Rotate the geometry around a given coordinate. This modifies the geometry\n   * coordinates in place.\n   * @abstract\n   * @param {number} angle Rotation angle in radians.\n   * @param {import(\"../coordinate.js\").Coordinate} anchor The rotation center.\n   * @api\n   */\n  rotate(angle, anchor) {\n    abstract();\n  }\n\n  /**\n   * Scale the geometry (with an optional origin).  This modifies the geometry\n   * coordinates in place.\n   * @abstract\n   * @param {number} sx The scaling factor in the x-direction.\n   * @param {number} [sy] The scaling factor in the y-direction (defaults to sx).\n   * @param {import(\"../coordinate.js\").Coordinate} [anchor] The scale origin (defaults to the center\n   *     of the geometry extent).\n   * @api\n   */\n  scale(sx, sy, anchor) {\n    abstract();\n  }\n\n  /**\n   * Create a simplified version of this geometry.  For linestrings, this uses\n   * the [Douglas Peucker](https://en.wikipedia.org/wiki/Ramer-Douglas-Peucker_algorithm)\n   * algorithm.  For polygons, a quantization-based\n   * simplification is used to preserve topology.\n   * @param {number} tolerance The tolerance distance for simplification.\n   * @return {Geometry} A new, simplified version of the original geometry.\n   * @api\n   */\n  simplify(tolerance) {\n    return this.getSimplifiedGeometry(tolerance * tolerance);\n  }\n\n  /**\n   * Create a simplified version of this geometry using the Douglas Peucker\n   * algorithm.\n   * See https://en.wikipedia.org/wiki/Ramer-Douglas-Peucker_algorithm.\n   * @abstract\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {Geometry} Simplified geometry.\n   */\n  getSimplifiedGeometry(squaredTolerance) {\n    return abstract();\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @abstract\n   * @return {Type} Geometry type.\n   */\n  getType() {\n    return abstract();\n  }\n\n  /**\n   * Apply a transform function to the coordinates of the geometry.\n   * The geometry is modified in place.\n   * If you do not want the geometry modified in place, first `clone()` it and\n   * then use this function on the clone.\n   * @abstract\n   * @param {import(\"../proj.js\").TransformFunction} transformFn Transform function.\n   * Called with a flat array of geometry coordinates.\n   */\n  applyTransform(transformFn) {\n    abstract();\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @abstract\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   */\n  intersectsExtent(extent) {\n    return abstract();\n  }\n\n  /**\n   * Translate the geometry.  This modifies the geometry coordinates in place.  If\n   * instead you want a new geometry, first `clone()` this geometry.\n   * @abstract\n   * @param {number} deltaX Delta X.\n   * @param {number} deltaY Delta Y.\n   * @api\n   */\n  translate(deltaX, deltaY) {\n    abstract();\n  }\n\n  /**\n   * Transform each coordinate of the geometry from one coordinate reference\n   * system to another. The geometry is modified in place.\n   * For example, a line will be transformed to a line and a circle to a circle.\n   * If you do not want the geometry modified in place, first `clone()` it and\n   * then use this function on the clone.\n   *\n   * @param {import(\"../proj.js\").ProjectionLike} source The current projection.  Can be a\n   *     string identifier or a {@link module:ol/proj/Projection~Projection} object.\n   * @param {import(\"../proj.js\").ProjectionLike} destination The desired projection.  Can be a\n   *     string identifier or a {@link module:ol/proj/Projection~Projection} object.\n   * @return {Geometry} This geometry.  Note that original geometry is\n   *     modified in place.\n   * @api\n   */\n  transform(source, destination) {\n    /** @type {import(\"../proj/Projection.js\").default} */\n    const sourceProj = getProjection(source);\n    const transformFn =\n      sourceProj.getUnits() == 'tile-pixels'\n        ? function (inCoordinates, outCoordinates, stride) {\n            const pixelExtent = sourceProj.getExtent();\n            const projectedExtent = sourceProj.getWorldExtent();\n            const scale = getHeight(projectedExtent) / getHeight(pixelExtent);\n            composeTransform(\n              tmpTransform,\n              projectedExtent[0],\n              projectedExtent[3],\n              scale,\n              -scale,\n              0,\n              0,\n              0\n            );\n            transform2D(\n              inCoordinates,\n              0,\n              inCoordinates.length,\n              stride,\n              tmpTransform,\n              outCoordinates\n            );\n            return getTransform(sourceProj, destination)(\n              inCoordinates,\n              outCoordinates,\n              stride\n            );\n          }\n        : getTransform(sourceProj, destination);\n    this.applyTransform(transformFn);\n    return this;\n  }\n}\n\nexport default Geometry;\n", "/**\n * @module ol/geom/SimpleGeometry\n */\nimport Geometry from './Geometry.js';\nimport {abstract} from '../util.js';\nimport {createOrUpdateFromFlatCoordinates, getCenter} from '../extent.js';\nimport {rotate, scale, transform2D, translate} from './flat/transform.js';\n\n/**\n * @classdesc\n * Abstract base class; only used for creating subclasses; do not instantiate\n * in apps, as cannot be rendered.\n *\n * @abstract\n * @api\n */\nclass SimpleGeometry extends Geometry {\n  constructor() {\n    super();\n\n    /**\n     * @protected\n     * @type {import(\"./Geometry.js\").GeometryLayout}\n     */\n    this.layout = 'XY';\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.stride = 2;\n\n    /**\n     * @protected\n     * @type {Array<number>}\n     */\n    this.flatCoordinates = null;\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @protected\n   * @return {import(\"../extent.js\").Extent} extent Extent.\n   */\n  computeExtent(extent) {\n    return createOrUpdateFromFlatCoordinates(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      extent\n    );\n  }\n\n  /**\n   * @abstract\n   * @return {Array<*> | null} Coordinates.\n   */\n  getCoordinates() {\n    return abstract();\n  }\n\n  /**\n   * Return the first coordinate of the geometry.\n   * @return {import(\"../coordinate.js\").Coordinate} First coordinate.\n   * @api\n   */\n  getFirstCoordinate() {\n    return this.flatCoordinates.slice(0, this.stride);\n  }\n\n  /**\n   * @return {Array<number>} Flat coordinates.\n   */\n  getFlatCoordinates() {\n    return this.flatCoordinates;\n  }\n\n  /**\n   * Return the last coordinate of the geometry.\n   * @return {import(\"../coordinate.js\").Coordinate} Last point.\n   * @api\n   */\n  getLastCoordinate() {\n    return this.flatCoordinates.slice(\n      this.flatCoordinates.length - this.stride\n    );\n  }\n\n  /**\n   * Return the {@link import(\"./Geometry.js\").GeometryLayout layout} of the geometry.\n   * @return {import(\"./Geometry.js\").GeometryLayout} Layout.\n   * @api\n   */\n  getLayout() {\n    return this.layout;\n  }\n\n  /**\n   * Create a simplified version of this geometry using the Douglas Peucker algorithm.\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {SimpleGeometry} Simplified geometry.\n   */\n  getSimplifiedGeometry(squaredTolerance) {\n    if (this.simplifiedGeometryRevision !== this.getRevision()) {\n      this.simplifiedGeometryMaxMinSquaredTolerance = 0;\n      this.simplifiedGeometryRevision = this.getRevision();\n    }\n    // If squaredTolerance is negative or if we know that simplification will not\n    // have any effect then just return this.\n    if (\n      squaredTolerance < 0 ||\n      (this.simplifiedGeometryMaxMinSquaredTolerance !== 0 &&\n        squaredTolerance <= this.simplifiedGeometryMaxMinSquaredTolerance)\n    ) {\n      return this;\n    }\n\n    const simplifiedGeometry =\n      this.getSimplifiedGeometryInternal(squaredTolerance);\n    const simplifiedFlatCoordinates = simplifiedGeometry.getFlatCoordinates();\n    if (simplifiedFlatCoordinates.length < this.flatCoordinates.length) {\n      return simplifiedGeometry;\n    }\n    // Simplification did not actually remove any coordinates.  We now know\n    // that any calls to getSimplifiedGeometry with a squaredTolerance less\n    // than or equal to the current squaredTolerance will also not have any\n    // effect.  This allows us to short circuit simplification (saving CPU\n    // cycles) and prevents the cache of simplified geometries from filling\n    // up with useless identical copies of this geometry (saving memory).\n    this.simplifiedGeometryMaxMinSquaredTolerance = squaredTolerance;\n    return this;\n  }\n\n  /**\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {SimpleGeometry} Simplified geometry.\n   * @protected\n   */\n  getSimplifiedGeometryInternal(squaredTolerance) {\n    return this;\n  }\n\n  /**\n   * @return {number} Stride.\n   */\n  getStride() {\n    return this.stride;\n  }\n\n  /**\n   * @param {import(\"./Geometry.js\").GeometryLayout} layout Layout.\n   * @param {Array<number>} flatCoordinates Flat coordinates.\n   */\n  setFlatCoordinates(layout, flatCoordinates) {\n    this.stride = getStrideForLayout(layout);\n    this.layout = layout;\n    this.flatCoordinates = flatCoordinates;\n  }\n\n  /**\n   * @abstract\n   * @param {!Array<*>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   */\n  setCoordinates(coordinates, layout) {\n    abstract();\n  }\n\n  /**\n   * @param {import(\"./Geometry.js\").GeometryLayout|undefined} layout Layout.\n   * @param {Array<*>} coordinates Coordinates.\n   * @param {number} nesting Nesting.\n   * @protected\n   */\n  setLayout(layout, coordinates, nesting) {\n    /** @type {number} */\n    let stride;\n    if (layout) {\n      stride = getStrideForLayout(layout);\n    } else {\n      for (let i = 0; i < nesting; ++i) {\n        if (coordinates.length === 0) {\n          this.layout = 'XY';\n          this.stride = 2;\n          return;\n        }\n        coordinates = /** @type {Array} */ (coordinates[0]);\n      }\n      stride = coordinates.length;\n      layout = getLayoutForStride(stride);\n    }\n    this.layout = layout;\n    this.stride = stride;\n  }\n\n  /**\n   * Apply a transform function to the coordinates of the geometry.\n   * The geometry is modified in place.\n   * If you do not want the geometry modified in place, first `clone()` it and\n   * then use this function on the clone.\n   * @param {import(\"../proj.js\").TransformFunction} transformFn Transform function.\n   * Called with a flat array of geometry coordinates.\n   * @api\n   */\n  applyTransform(transformFn) {\n    if (this.flatCoordinates) {\n      transformFn(this.flatCoordinates, this.flatCoordinates, this.stride);\n      this.changed();\n    }\n  }\n\n  /**\n   * Rotate the geometry around a given coordinate. This modifies the geometry\n   * coordinates in place.\n   * @param {number} angle Rotation angle in counter-clockwise radians.\n   * @param {import(\"../coordinate.js\").Coordinate} anchor The rotation center.\n   * @api\n   */\n  rotate(angle, anchor) {\n    const flatCoordinates = this.getFlatCoordinates();\n    if (flatCoordinates) {\n      const stride = this.getStride();\n      rotate(\n        flatCoordinates,\n        0,\n        flatCoordinates.length,\n        stride,\n        angle,\n        anchor,\n        flatCoordinates\n      );\n      this.changed();\n    }\n  }\n\n  /**\n   * Scale the geometry (with an optional origin).  This modifies the geometry\n   * coordinates in place.\n   * @param {number} sx The scaling factor in the x-direction.\n   * @param {number} [sy] The scaling factor in the y-direction (defaults to sx).\n   * @param {import(\"../coordinate.js\").Coordinate} [anchor] The scale origin (defaults to the center\n   *     of the geometry extent).\n   * @api\n   */\n  scale(sx, sy, anchor) {\n    if (sy === undefined) {\n      sy = sx;\n    }\n    if (!anchor) {\n      anchor = getCenter(this.getExtent());\n    }\n    const flatCoordinates = this.getFlatCoordinates();\n    if (flatCoordinates) {\n      const stride = this.getStride();\n      scale(\n        flatCoordinates,\n        0,\n        flatCoordinates.length,\n        stride,\n        sx,\n        sy,\n        anchor,\n        flatCoordinates\n      );\n      this.changed();\n    }\n  }\n\n  /**\n   * Translate the geometry.  This modifies the geometry coordinates in place.  If\n   * instead you want a new geometry, first `clone()` this geometry.\n   * @param {number} deltaX Delta X.\n   * @param {number} deltaY Delta Y.\n   * @api\n   */\n  translate(deltaX, deltaY) {\n    const flatCoordinates = this.getFlatCoordinates();\n    if (flatCoordinates) {\n      const stride = this.getStride();\n      translate(\n        flatCoordinates,\n        0,\n        flatCoordinates.length,\n        stride,\n        deltaX,\n        deltaY,\n        flatCoordinates\n      );\n      this.changed();\n    }\n  }\n}\n\n/**\n * @param {number} stride Stride.\n * @return {import(\"./Geometry.js\").GeometryLayout} layout Layout.\n */\nfunction getLayoutForStride(stride) {\n  let layout;\n  if (stride == 2) {\n    layout = 'XY';\n  } else if (stride == 3) {\n    layout = 'XYZ';\n  } else if (stride == 4) {\n    layout = 'XYZM';\n  }\n  return /** @type {import(\"./Geometry.js\").GeometryLayout} */ (layout);\n}\n\n/**\n * @param {import(\"./Geometry.js\").GeometryLayout} layout Layout.\n * @return {number} Stride.\n */\nexport function getStrideForLayout(layout) {\n  let stride;\n  if (layout == 'XY') {\n    stride = 2;\n  } else if (layout == 'XYZ' || layout == 'XYM') {\n    stride = 3;\n  } else if (layout == 'XYZM') {\n    stride = 4;\n  }\n  return /** @type {number} */ (stride);\n}\n\n/**\n * @param {SimpleGeometry} simpleGeometry Simple geometry.\n * @param {import(\"../transform.js\").Transform} transform Transform.\n * @param {Array<number>} [dest] Destination.\n * @return {Array<number>} Transformed flat coordinates.\n */\nexport function transformGeom2D(simpleGeometry, transform, dest) {\n  const flatCoordinates = simpleGeometry.getFlatCoordinates();\n  if (!flatCoordinates) {\n    return null;\n  }\n  const stride = simpleGeometry.getStride();\n  return transform2D(\n    flatCoordinates,\n    0,\n    flatCoordinates.length,\n    stride,\n    transform,\n    dest\n  );\n}\n\nexport default SimpleGeometry;\n", "/**\n * @module ol/geom/flat/deflate\n */\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {import(\"../../coordinate.js\").Coordinate} coordinate Coordinate.\n * @param {number} stride Stride.\n * @return {number} offset Offset.\n */\nexport function deflateCoordinate(flatCoordinates, offset, coordinate, stride) {\n  for (let i = 0, ii = coordinate.length; i < ii; ++i) {\n    flatCoordinates[offset++] = coordinate[i];\n  }\n  return offset;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<import(\"../../coordinate.js\").Coordinate>} coordinates Coordinates.\n * @param {number} stride Stride.\n * @return {number} offset Offset.\n */\nexport function deflateCoordinates(\n  flatCoordinates,\n  offset,\n  coordinates,\n  stride\n) {\n  for (let i = 0, ii = coordinates.length; i < ii; ++i) {\n    const coordinate = coordinates[i];\n    for (let j = 0; j < stride; ++j) {\n      flatCoordinates[offset++] = coordinate[j];\n    }\n  }\n  return offset;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<import(\"../../coordinate.js\").Coordinate>>} coordinatess Coordinatess.\n * @param {number} stride Stride.\n * @param {Array<number>} [ends] Ends.\n * @return {Array<number>} Ends.\n */\nexport function deflateCoordinatesArray(\n  flatCoordinates,\n  offset,\n  coordinatess,\n  stride,\n  ends\n) {\n  ends = ends ? ends : [];\n  let i = 0;\n  for (let j = 0, jj = coordinatess.length; j < jj; ++j) {\n    const end = deflateCoordinates(\n      flatCoordinates,\n      offset,\n      coordinatess[j],\n      stride\n    );\n    ends[i++] = end;\n    offset = end;\n  }\n  ends.length = i;\n  return ends;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<Array<import(\"../../coordinate.js\").Coordinate>>>} coordinatesss Coordinatesss.\n * @param {number} stride Stride.\n * @param {Array<Array<number>>} [endss] Endss.\n * @return {Array<Array<number>>} Endss.\n */\nexport function deflateMultiCoordinatesArray(\n  flatCoordinates,\n  offset,\n  coordinatesss,\n  stride,\n  endss\n) {\n  endss = endss ? endss : [];\n  let i = 0;\n  for (let j = 0, jj = coordinatesss.length; j < jj; ++j) {\n    const ends = deflateCoordinatesArray(\n      flatCoordinates,\n      offset,\n      coordinatesss[j],\n      stride,\n      endss[i]\n    );\n    if (ends.length === 0) {\n      ends[0] = offset;\n    }\n    endss[i++] = ends;\n    offset = ends[ends.length - 1];\n  }\n  endss.length = i;\n  return endss;\n}\n", "/**\n * @module ol/geom/Point\n */\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {containsXY, createOrUpdateFromCoordinate} from '../extent.js';\nimport {deflateCoordinate} from './flat/deflate.js';\nimport {squaredDistance as squaredDx} from '../math.js';\n\n/**\n * @classdesc\n * Point geometry.\n *\n * @api\n */\nclass Point extends SimpleGeometry {\n  /**\n   * @param {import(\"../coordinate.js\").Coordinate} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   */\n  constructor(coordinates, layout) {\n    super();\n    this.setCoordinates(coordinates, layout);\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!Point} Clone.\n   * @api\n   */\n  clone() {\n    const point = new Point(this.flatCoordinates.slice(), this.layout);\n    point.applyProperties(this);\n    return point;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    const flatCoordinates = this.flatCoordinates;\n    const squaredDistance = squaredDx(\n      x,\n      y,\n      flatCoordinates[0],\n      flatCoordinates[1]\n    );\n    if (squaredDistance < minSquaredDistance) {\n      const stride = this.stride;\n      for (let i = 0; i < stride; ++i) {\n        closestPoint[i] = flatCoordinates[i];\n      }\n      closestPoint.length = stride;\n      return squaredDistance;\n    }\n    return minSquaredDistance;\n  }\n\n  /**\n   * Return the coordinate of the point.\n   * @return {import(\"../coordinate.js\").Coordinate} Coordinates.\n   * @api\n   */\n  getCoordinates() {\n    return !this.flatCoordinates ? [] : this.flatCoordinates.slice();\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @protected\n   * @return {import(\"../extent.js\").Extent} extent Extent.\n   */\n  computeExtent(extent) {\n    return createOrUpdateFromCoordinate(this.flatCoordinates, extent);\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   */\n  getType() {\n    return 'Point';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   */\n  intersectsExtent(extent) {\n    return containsXY(extent, this.flatCoordinates[0], this.flatCoordinates[1]);\n  }\n\n  /**\n   * @param {!Array<*>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   */\n  setCoordinates(coordinates, layout) {\n    this.setLayout(layout, coordinates, 0);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    this.flatCoordinates.length = deflateCoordinate(\n      this.flatCoordinates,\n      0,\n      coordinates,\n      this.stride\n    );\n    this.changed();\n  }\n}\n\nexport default Point;\n", "/**\n * @module ol/geom/flat/closest\n */\nimport {lerp, squaredDistance as squaredDx} from '../../math.js';\n\n/**\n * Returns the point on the 2D line segment flatCoordinates[offset1] to\n * flatCoordinates[offset2] that is closest to the point (x, y).  Extra\n * dimensions are linearly interpolated.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset1 Offset 1.\n * @param {number} offset2 Offset 2.\n * @param {number} stride Stride.\n * @param {number} x X.\n * @param {number} y Y.\n * @param {Array<number>} closestPoint Closest point.\n */\nfunction assignClosest(\n  flatCoordinates,\n  offset1,\n  offset2,\n  stride,\n  x,\n  y,\n  closestPoint\n) {\n  const x1 = flatCoordinates[offset1];\n  const y1 = flatCoordinates[offset1 + 1];\n  const dx = flatCoordinates[offset2] - x1;\n  const dy = flatCoordinates[offset2 + 1] - y1;\n  let offset;\n  if (dx === 0 && dy === 0) {\n    offset = offset1;\n  } else {\n    const t = ((x - x1) * dx + (y - y1) * dy) / (dx * dx + dy * dy);\n    if (t > 1) {\n      offset = offset2;\n    } else if (t > 0) {\n      for (let i = 0; i < stride; ++i) {\n        closestPoint[i] = lerp(\n          flatCoordinates[offset1 + i],\n          flatCoordinates[offset2 + i],\n          t\n        );\n      }\n      closestPoint.length = stride;\n      return;\n    } else {\n      offset = offset1;\n    }\n  }\n  for (let i = 0; i < stride; ++i) {\n    closestPoint[i] = flatCoordinates[offset + i];\n  }\n  closestPoint.length = stride;\n}\n\n/**\n * Return the squared of the largest distance between any pair of consecutive\n * coordinates.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} max Max squared delta.\n * @return {number} Max squared delta.\n */\nexport function maxSquaredDelta(flatCoordinates, offset, end, stride, max) {\n  let x1 = flatCoordinates[offset];\n  let y1 = flatCoordinates[offset + 1];\n  for (offset += stride; offset < end; offset += stride) {\n    const x2 = flatCoordinates[offset];\n    const y2 = flatCoordinates[offset + 1];\n    const squaredDelta = squaredDx(x1, y1, x2, y2);\n    if (squaredDelta > max) {\n      max = squaredDelta;\n    }\n    x1 = x2;\n    y1 = y2;\n  }\n  return max;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {number} max Max squared delta.\n * @return {number} Max squared delta.\n */\nexport function arrayMaxSquaredDelta(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  max\n) {\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    max = maxSquaredDelta(flatCoordinates, offset, end, stride, max);\n    offset = end;\n  }\n  return max;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {number} max Max squared delta.\n * @return {number} Max squared delta.\n */\nexport function multiArrayMaxSquaredDelta(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  max\n) {\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    max = arrayMaxSquaredDelta(flatCoordinates, offset, ends, stride, max);\n    offset = ends[ends.length - 1];\n  }\n  return max;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} maxDelta Max delta.\n * @param {boolean} isRing Is ring.\n * @param {number} x X.\n * @param {number} y Y.\n * @param {Array<number>} closestPoint Closest point.\n * @param {number} minSquaredDistance Minimum squared distance.\n * @param {Array<number>} [tmpPoint] Temporary point object.\n * @return {number} Minimum squared distance.\n */\nexport function assignClosestPoint(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  maxDelta,\n  isRing,\n  x,\n  y,\n  closestPoint,\n  minSquaredDistance,\n  tmpPoint\n) {\n  if (offset == end) {\n    return minSquaredDistance;\n  }\n  let i, squaredDistance;\n  if (maxDelta === 0) {\n    // All points are identical, so just test the first point.\n    squaredDistance = squaredDx(\n      x,\n      y,\n      flatCoordinates[offset],\n      flatCoordinates[offset + 1]\n    );\n    if (squaredDistance < minSquaredDistance) {\n      for (i = 0; i < stride; ++i) {\n        closestPoint[i] = flatCoordinates[offset + i];\n      }\n      closestPoint.length = stride;\n      return squaredDistance;\n    }\n    return minSquaredDistance;\n  }\n  tmpPoint = tmpPoint ? tmpPoint : [NaN, NaN];\n  let index = offset + stride;\n  while (index < end) {\n    assignClosest(\n      flatCoordinates,\n      index - stride,\n      index,\n      stride,\n      x,\n      y,\n      tmpPoint\n    );\n    squaredDistance = squaredDx(x, y, tmpPoint[0], tmpPoint[1]);\n    if (squaredDistance < minSquaredDistance) {\n      minSquaredDistance = squaredDistance;\n      for (i = 0; i < stride; ++i) {\n        closestPoint[i] = tmpPoint[i];\n      }\n      closestPoint.length = stride;\n      index += stride;\n    } else {\n      // Skip ahead multiple points, because we know that all the skipped\n      // points cannot be any closer than the closest point we have found so\n      // far.  We know this because we know how close the current point is, how\n      // close the closest point we have found so far is, and the maximum\n      // distance between consecutive points.  For example, if we're currently\n      // at distance 10, the best we've found so far is 3, and that the maximum\n      // distance between consecutive points is 2, then we'll need to skip at\n      // least (10 - 3) / 2 == 3 (rounded down) points to have any chance of\n      // finding a closer point.  We use Math.max(..., 1) to ensure that we\n      // always advance at least one point, to avoid an infinite loop.\n      index +=\n        stride *\n        Math.max(\n          ((Math.sqrt(squaredDistance) - Math.sqrt(minSquaredDistance)) /\n            maxDelta) |\n            0,\n          1\n        );\n    }\n  }\n  if (isRing) {\n    // Check the closing segment.\n    assignClosest(\n      flatCoordinates,\n      end - stride,\n      offset,\n      stride,\n      x,\n      y,\n      tmpPoint\n    );\n    squaredDistance = squaredDx(x, y, tmpPoint[0], tmpPoint[1]);\n    if (squaredDistance < minSquaredDistance) {\n      minSquaredDistance = squaredDistance;\n      for (i = 0; i < stride; ++i) {\n        closestPoint[i] = tmpPoint[i];\n      }\n      closestPoint.length = stride;\n    }\n  }\n  return minSquaredDistance;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {number} maxDelta Max delta.\n * @param {boolean} isRing Is ring.\n * @param {number} x X.\n * @param {number} y Y.\n * @param {Array<number>} closestPoint Closest point.\n * @param {number} minSquaredDistance Minimum squared distance.\n * @param {Array<number>} [tmpPoint] Temporary point object.\n * @return {number} Minimum squared distance.\n */\nexport function assignClosestArrayPoint(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  maxDelta,\n  isRing,\n  x,\n  y,\n  closestPoint,\n  minSquaredDistance,\n  tmpPoint\n) {\n  tmpPoint = tmpPoint ? tmpPoint : [NaN, NaN];\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    minSquaredDistance = assignClosestPoint(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      maxDelta,\n      isRing,\n      x,\n      y,\n      closestPoint,\n      minSquaredDistance,\n      tmpPoint\n    );\n    offset = end;\n  }\n  return minSquaredDistance;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {number} maxDelta Max delta.\n * @param {boolean} isRing Is ring.\n * @param {number} x X.\n * @param {number} y Y.\n * @param {Array<number>} closestPoint Closest point.\n * @param {number} minSquaredDistance Minimum squared distance.\n * @param {Array<number>} [tmpPoint] Temporary point object.\n * @return {number} Minimum squared distance.\n */\nexport function assignClosestMultiArrayPoint(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  maxDelta,\n  isRing,\n  x,\n  y,\n  closestPoint,\n  minSquaredDistance,\n  tmpPoint\n) {\n  tmpPoint = tmpPoint ? tmpPoint : [NaN, NaN];\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    minSquaredDistance = assignClosestArrayPoint(\n      flatCoordinates,\n      offset,\n      ends,\n      stride,\n      maxDelta,\n      isRing,\n      x,\n      y,\n      closestPoint,\n      minSquaredDistance,\n      tmpPoint\n    );\n    offset = ends[ends.length - 1];\n  }\n  return minSquaredDistance;\n}\n", "/**\n * @module ol/geom/flat/simplify\n */\n// Based on simplify-js https://github.com/mourner/simplify-js\n// Copyright (c) 2012, <PERSON>in\n// All rights reserved.\n//\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are met:\n//\n//    1. Redistributions of source code must retain the above copyright notice,\n//       this list of conditions and the following disclaimer.\n//\n//    2. Redistributions in binary form must reproduce the above copyright\n//       notice, this list of conditions and the following disclaimer in the\n//       documentation and/or other materials provided with the distribution.\n//\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n// POSSIBILITY OF SUCH DAMAGE.\n\nimport {squaredDistance, squaredSegmentDistance} from '../../math.js';\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} squaredTolerance Squared tolerance.\n * @param {boolean} highQuality Highest quality.\n * @param {Array<number>} [simplifiedFlatCoordinates] Simplified flat\n *     coordinates.\n * @return {Array<number>} Simplified line string.\n */\nexport function simplifyLineString(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  squaredTolerance,\n  highQuality,\n  simplifiedFlatCoordinates\n) {\n  simplifiedFlatCoordinates =\n    simplifiedFlatCoordinates !== undefined ? simplifiedFlatCoordinates : [];\n  if (!highQuality) {\n    end = radialDistance(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      squaredTolerance,\n      simplifiedFlatCoordinates,\n      0\n    );\n    flatCoordinates = simplifiedFlatCoordinates;\n    offset = 0;\n    stride = 2;\n  }\n  simplifiedFlatCoordinates.length = douglasPeucker(\n    flatCoordinates,\n    offset,\n    end,\n    stride,\n    squaredTolerance,\n    simplifiedFlatCoordinates,\n    0\n  );\n  return simplifiedFlatCoordinates;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} squaredTolerance Squared tolerance.\n * @param {Array<number>} simplifiedFlatCoordinates Simplified flat\n *     coordinates.\n * @param {number} simplifiedOffset Simplified offset.\n * @return {number} Simplified offset.\n */\nexport function douglasPeucker(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  squaredTolerance,\n  simplifiedFlatCoordinates,\n  simplifiedOffset\n) {\n  const n = (end - offset) / stride;\n  if (n < 3) {\n    for (; offset < end; offset += stride) {\n      simplifiedFlatCoordinates[simplifiedOffset++] = flatCoordinates[offset];\n      simplifiedFlatCoordinates[simplifiedOffset++] =\n        flatCoordinates[offset + 1];\n    }\n    return simplifiedOffset;\n  }\n  /** @type {Array<number>} */\n  const markers = new Array(n);\n  markers[0] = 1;\n  markers[n - 1] = 1;\n  /** @type {Array<number>} */\n  const stack = [offset, end - stride];\n  let index = 0;\n  while (stack.length > 0) {\n    const last = stack.pop();\n    const first = stack.pop();\n    let maxSquaredDistance = 0;\n    const x1 = flatCoordinates[first];\n    const y1 = flatCoordinates[first + 1];\n    const x2 = flatCoordinates[last];\n    const y2 = flatCoordinates[last + 1];\n    for (let i = first + stride; i < last; i += stride) {\n      const x = flatCoordinates[i];\n      const y = flatCoordinates[i + 1];\n      const squaredDistance = squaredSegmentDistance(x, y, x1, y1, x2, y2);\n      if (squaredDistance > maxSquaredDistance) {\n        index = i;\n        maxSquaredDistance = squaredDistance;\n      }\n    }\n    if (maxSquaredDistance > squaredTolerance) {\n      markers[(index - offset) / stride] = 1;\n      if (first + stride < index) {\n        stack.push(first, index);\n      }\n      if (index + stride < last) {\n        stack.push(index, last);\n      }\n    }\n  }\n  for (let i = 0; i < n; ++i) {\n    if (markers[i]) {\n      simplifiedFlatCoordinates[simplifiedOffset++] =\n        flatCoordinates[offset + i * stride];\n      simplifiedFlatCoordinates[simplifiedOffset++] =\n        flatCoordinates[offset + i * stride + 1];\n    }\n  }\n  return simplifiedOffset;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {number} squaredTolerance Squared tolerance.\n * @param {Array<number>} simplifiedFlatCoordinates Simplified flat\n *     coordinates.\n * @param {number} simplifiedOffset Simplified offset.\n * @param {Array<number>} simplifiedEnds Simplified ends.\n * @return {number} Simplified offset.\n */\nexport function douglasPeuckerArray(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  squaredTolerance,\n  simplifiedFlatCoordinates,\n  simplifiedOffset,\n  simplifiedEnds\n) {\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    simplifiedOffset = douglasPeucker(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      squaredTolerance,\n      simplifiedFlatCoordinates,\n      simplifiedOffset\n    );\n    simplifiedEnds.push(simplifiedOffset);\n    offset = end;\n  }\n  return simplifiedOffset;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {number} squaredTolerance Squared tolerance.\n * @param {Array<number>} simplifiedFlatCoordinates Simplified flat\n *     coordinates.\n * @param {number} simplifiedOffset Simplified offset.\n * @param {Array<Array<number>>} simplifiedEndss Simplified endss.\n * @return {number} Simplified offset.\n */\nexport function douglasPeuckerMultiArray(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  squaredTolerance,\n  simplifiedFlatCoordinates,\n  simplifiedOffset,\n  simplifiedEndss\n) {\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    const simplifiedEnds = [];\n    simplifiedOffset = douglasPeuckerArray(\n      flatCoordinates,\n      offset,\n      ends,\n      stride,\n      squaredTolerance,\n      simplifiedFlatCoordinates,\n      simplifiedOffset,\n      simplifiedEnds\n    );\n    simplifiedEndss.push(simplifiedEnds);\n    offset = ends[ends.length - 1];\n  }\n  return simplifiedOffset;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} squaredTolerance Squared tolerance.\n * @param {Array<number>} simplifiedFlatCoordinates Simplified flat\n *     coordinates.\n * @param {number} simplifiedOffset Simplified offset.\n * @return {number} Simplified offset.\n */\nexport function radialDistance(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  squaredTolerance,\n  simplifiedFlatCoordinates,\n  simplifiedOffset\n) {\n  if (end <= offset + stride) {\n    // zero or one point, no simplification possible, so copy and return\n    for (; offset < end; offset += stride) {\n      simplifiedFlatCoordinates[simplifiedOffset++] = flatCoordinates[offset];\n      simplifiedFlatCoordinates[simplifiedOffset++] =\n        flatCoordinates[offset + 1];\n    }\n    return simplifiedOffset;\n  }\n  let x1 = flatCoordinates[offset];\n  let y1 = flatCoordinates[offset + 1];\n  // copy first point\n  simplifiedFlatCoordinates[simplifiedOffset++] = x1;\n  simplifiedFlatCoordinates[simplifiedOffset++] = y1;\n  let x2 = x1;\n  let y2 = y1;\n  for (offset += stride; offset < end; offset += stride) {\n    x2 = flatCoordinates[offset];\n    y2 = flatCoordinates[offset + 1];\n    if (squaredDistance(x1, y1, x2, y2) > squaredTolerance) {\n      // copy point at offset\n      simplifiedFlatCoordinates[simplifiedOffset++] = x2;\n      simplifiedFlatCoordinates[simplifiedOffset++] = y2;\n      x1 = x2;\n      y1 = y2;\n    }\n  }\n  if (x2 != x1 || y2 != y1) {\n    // copy last point\n    simplifiedFlatCoordinates[simplifiedOffset++] = x2;\n    simplifiedFlatCoordinates[simplifiedOffset++] = y2;\n  }\n  return simplifiedOffset;\n}\n\n/**\n * @param {number} value Value.\n * @param {number} tolerance Tolerance.\n * @return {number} Rounded value.\n */\nexport function snap(value, tolerance) {\n  return tolerance * Math.round(value / tolerance);\n}\n\n/**\n * Simplifies a line string using an algorithm designed by Tim Schaub.\n * Coordinates are snapped to the nearest value in a virtual grid and\n * consecutive duplicate coordinates are discarded.  This effectively preserves\n * topology as the simplification of any subsection of a line string is\n * independent of the rest of the line string.  This means that, for examples,\n * the common edge between two polygons will be simplified to the same line\n * string independently in both polygons.  This implementation uses a single\n * pass over the coordinates and eliminates intermediate collinear points.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} tolerance Tolerance.\n * @param {Array<number>} simplifiedFlatCoordinates Simplified flat\n *     coordinates.\n * @param {number} simplifiedOffset Simplified offset.\n * @return {number} Simplified offset.\n */\nexport function quantize(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  tolerance,\n  simplifiedFlatCoordinates,\n  simplifiedOffset\n) {\n  // do nothing if the line is empty\n  if (offset == end) {\n    return simplifiedOffset;\n  }\n  // snap the first coordinate (P1)\n  let x1 = snap(flatCoordinates[offset], tolerance);\n  let y1 = snap(flatCoordinates[offset + 1], tolerance);\n  offset += stride;\n  // add the first coordinate to the output\n  simplifiedFlatCoordinates[simplifiedOffset++] = x1;\n  simplifiedFlatCoordinates[simplifiedOffset++] = y1;\n  // find the next coordinate that does not snap to the same value as the first\n  // coordinate (P2)\n  let x2, y2;\n  do {\n    x2 = snap(flatCoordinates[offset], tolerance);\n    y2 = snap(flatCoordinates[offset + 1], tolerance);\n    offset += stride;\n    if (offset == end) {\n      // all coordinates snap to the same value, the line collapses to a point\n      // push the last snapped value anyway to ensure that the output contains\n      // at least two points\n      // FIXME should we really return at least two points anyway?\n      simplifiedFlatCoordinates[simplifiedOffset++] = x2;\n      simplifiedFlatCoordinates[simplifiedOffset++] = y2;\n      return simplifiedOffset;\n    }\n  } while (x2 == x1 && y2 == y1);\n  while (offset < end) {\n    // snap the next coordinate (P3)\n    const x3 = snap(flatCoordinates[offset], tolerance);\n    const y3 = snap(flatCoordinates[offset + 1], tolerance);\n    offset += stride;\n    // skip P3 if it is equal to P2\n    if (x3 == x2 && y3 == y2) {\n      continue;\n    }\n    // calculate the delta between P1 and P2\n    const dx1 = x2 - x1;\n    const dy1 = y2 - y1;\n    // calculate the delta between P3 and P1\n    const dx2 = x3 - x1;\n    const dy2 = y3 - y1;\n    // if P1, P2, and P3 are colinear and P3 is further from P1 than P2 is from\n    // P1 in the same direction then P2 is on the straight line between P1 and\n    // P3\n    if (\n      dx1 * dy2 == dy1 * dx2 &&\n      ((dx1 < 0 && dx2 < dx1) || dx1 == dx2 || (dx1 > 0 && dx2 > dx1)) &&\n      ((dy1 < 0 && dy2 < dy1) || dy1 == dy2 || (dy1 > 0 && dy2 > dy1))\n    ) {\n      // discard P2 and set P2 = P3\n      x2 = x3;\n      y2 = y3;\n      continue;\n    }\n    // either P1, P2, and P3 are not colinear, or they are colinear but P3 is\n    // between P3 and P1 or on the opposite half of the line to P2.  add P2,\n    // and continue with P1 = P2 and P2 = P3\n    simplifiedFlatCoordinates[simplifiedOffset++] = x2;\n    simplifiedFlatCoordinates[simplifiedOffset++] = y2;\n    x1 = x2;\n    y1 = y2;\n    x2 = x3;\n    y2 = y3;\n  }\n  // add the last point (P2)\n  simplifiedFlatCoordinates[simplifiedOffset++] = x2;\n  simplifiedFlatCoordinates[simplifiedOffset++] = y2;\n  return simplifiedOffset;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {number} tolerance Tolerance.\n * @param {Array<number>} simplifiedFlatCoordinates Simplified flat\n *     coordinates.\n * @param {number} simplifiedOffset Simplified offset.\n * @param {Array<number>} simplifiedEnds Simplified ends.\n * @return {number} Simplified offset.\n */\nexport function quantizeArray(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  tolerance,\n  simplifiedFlatCoordinates,\n  simplifiedOffset,\n  simplifiedEnds\n) {\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    simplifiedOffset = quantize(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      tolerance,\n      simplifiedFlatCoordinates,\n      simplifiedOffset\n    );\n    simplifiedEnds.push(simplifiedOffset);\n    offset = end;\n  }\n  return simplifiedOffset;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {number} tolerance Tolerance.\n * @param {Array<number>} simplifiedFlatCoordinates Simplified flat\n *     coordinates.\n * @param {number} simplifiedOffset Simplified offset.\n * @param {Array<Array<number>>} simplifiedEndss Simplified endss.\n * @return {number} Simplified offset.\n */\nexport function quantizeMultiArray(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  tolerance,\n  simplifiedFlatCoordinates,\n  simplifiedOffset,\n  simplifiedEndss\n) {\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    const simplifiedEnds = [];\n    simplifiedOffset = quantizeArray(\n      flatCoordinates,\n      offset,\n      ends,\n      stride,\n      tolerance,\n      simplifiedFlatCoordinates,\n      simplifiedOffset,\n      simplifiedEnds\n    );\n    simplifiedEndss.push(simplifiedEnds);\n    offset = ends[ends.length - 1];\n  }\n  return simplifiedOffset;\n}\n", "/**\n * @module ol/geom/flat/inflate\n */\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {Array<import(\"../../coordinate.js\").Coordinate>} [coordinates] Coordinates.\n * @return {Array<import(\"../../coordinate.js\").Coordinate>} Coordinates.\n */\nexport function inflateCoordinates(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  coordinates\n) {\n  coordinates = coordinates !== undefined ? coordinates : [];\n  let i = 0;\n  for (let j = offset; j < end; j += stride) {\n    coordinates[i++] = flatCoordinates.slice(j, j + stride);\n  }\n  coordinates.length = i;\n  return coordinates;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {Array<Array<import(\"../../coordinate.js\").Coordinate>>} [coordinatess] Coordinatess.\n * @return {Array<Array<import(\"../../coordinate.js\").Coordinate>>} Coordinatess.\n */\nexport function inflateCoordinatesArray(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  coordinatess\n) {\n  coordinatess = coordinatess !== undefined ? coordinatess : [];\n  let i = 0;\n  for (let j = 0, jj = ends.length; j < jj; ++j) {\n    const end = ends[j];\n    coordinatess[i++] = inflateCoordinates(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      coordinatess[i]\n    );\n    offset = end;\n  }\n  coordinatess.length = i;\n  return coordinatess;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {Array<Array<Array<import(\"../../coordinate.js\").Coordinate>>>} [coordinatesss]\n *     Coordinatesss.\n * @return {Array<Array<Array<import(\"../../coordinate.js\").Coordinate>>>} Coordinatesss.\n */\nexport function inflateMultiCoordinatesArray(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  coordinatesss\n) {\n  coordinatesss = coordinatesss !== undefined ? coordinatesss : [];\n  let i = 0;\n  for (let j = 0, jj = endss.length; j < jj; ++j) {\n    const ends = endss[j];\n    coordinatesss[i++] =\n      ends.length === 1 && ends[0] === offset\n        ? []\n        : inflateCoordinatesArray(\n            flatCoordinates,\n            offset,\n            ends,\n            stride,\n            coordinatesss[i]\n          );\n    offset = ends[ends.length - 1];\n  }\n  coordinatesss.length = i;\n  return coordinatesss;\n}\n", "/**\n * @module ol/geom/flat/area\n */\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @return {number} Area.\n */\nexport function linearRing(flatCoordinates, offset, end, stride) {\n  let twiceArea = 0;\n  let x1 = flatCoordinates[end - stride];\n  let y1 = flatCoordinates[end - stride + 1];\n  for (; offset < end; offset += stride) {\n    const x2 = flatCoordinates[offset];\n    const y2 = flatCoordinates[offset + 1];\n    twiceArea += y1 * x2 - x1 * y2;\n    x1 = x2;\n    y1 = y2;\n  }\n  return twiceArea / 2;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @return {number} Area.\n */\nexport function linearRings(flatCoordinates, offset, ends, stride) {\n  let area = 0;\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    area += linearRing(flatCoordinates, offset, end, stride);\n    offset = end;\n  }\n  return area;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @return {number} Area.\n */\nexport function linearRingss(flatCoordinates, offset, endss, stride) {\n  let area = 0;\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    area += linearRings(flatCoordinates, offset, ends, stride);\n    offset = ends[ends.length - 1];\n  }\n  return area;\n}\n", "/**\n * @module ol/geom/LinearRing\n */\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {assignClosestPoint, maxSquaredDelta} from './flat/closest.js';\nimport {closestSquaredDistanceXY} from '../extent.js';\nimport {deflateCoordinates} from './flat/deflate.js';\nimport {doug<PERSON><PERSON>eu<PERSON>} from './flat/simplify.js';\nimport {inflateCoordinates} from './flat/inflate.js';\nimport {linearRing as linearRingArea} from './flat/area.js';\n\n/**\n * @classdesc\n * Linear ring geometry. Only used as part of polygon; cannot be rendered\n * on its own.\n *\n * @api\n */\nclass LinearRing extends SimpleGeometry {\n  /**\n   * @param {Array<import(\"../coordinate.js\").Coordinate>|Array<number>} coordinates Coordinates.\n   *     For internal use, flat coordinates in combination with `layout` are also accepted.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   */\n  constructor(coordinates, layout) {\n    super();\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDelta_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDeltaRevision_ = -1;\n\n    if (layout !== undefined && !Array.isArray(coordinates[0])) {\n      this.setFlatCoordinates(\n        layout,\n        /** @type {Array<number>} */ (coordinates)\n      );\n    } else {\n      this.setCoordinates(\n        /** @type {Array<import(\"../coordinate.js\").Coordinate>} */ (\n          coordinates\n        ),\n        layout\n      );\n    }\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!LinearRing} Clone.\n   * @api\n   */\n  clone() {\n    return new LinearRing(this.flatCoordinates.slice(), this.layout);\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    if (minSquaredDistance < closestSquaredDistanceXY(this.getExtent(), x, y)) {\n      return minSquaredDistance;\n    }\n    if (this.maxDeltaRevision_ != this.getRevision()) {\n      this.maxDelta_ = Math.sqrt(\n        maxSquaredDelta(\n          this.flatCoordinates,\n          0,\n          this.flatCoordinates.length,\n          this.stride,\n          0\n        )\n      );\n      this.maxDeltaRevision_ = this.getRevision();\n    }\n    return assignClosestPoint(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      this.maxDelta_,\n      true,\n      x,\n      y,\n      closestPoint,\n      minSquaredDistance\n    );\n  }\n\n  /**\n   * Return the area of the linear ring on projected plane.\n   * @return {number} Area (on projected plane).\n   * @api\n   */\n  getArea() {\n    return linearRingArea(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride\n    );\n  }\n\n  /**\n   * Return the coordinates of the linear ring.\n   * @return {Array<import(\"../coordinate.js\").Coordinate>} Coordinates.\n   * @api\n   */\n  getCoordinates() {\n    return inflateCoordinates(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride\n    );\n  }\n\n  /**\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {LinearRing} Simplified LinearRing.\n   * @protected\n   */\n  getSimplifiedGeometryInternal(squaredTolerance) {\n    const simplifiedFlatCoordinates = [];\n    simplifiedFlatCoordinates.length = douglasPeucker(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      squaredTolerance,\n      simplifiedFlatCoordinates,\n      0\n    );\n    return new LinearRing(simplifiedFlatCoordinates, 'XY');\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   */\n  getType() {\n    return 'LinearRing';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   */\n  intersectsExtent(extent) {\n    return false;\n  }\n\n  /**\n   * Set the coordinates of the linear ring.\n   * @param {!Array<import(\"../coordinate.js\").Coordinate>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   */\n  setCoordinates(coordinates, layout) {\n    this.setLayout(layout, coordinates, 1);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    this.flatCoordinates.length = deflateCoordinates(\n      this.flatCoordinates,\n      0,\n      coordinates,\n      this.stride\n    );\n    this.changed();\n  }\n}\n\nexport default LinearRing;\n", "/**\n * @module ol/geom/flat/reverse\n */\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n */\nexport function coordinates(flatCoordinates, offset, end, stride) {\n  while (offset < end - stride) {\n    for (let i = 0; i < stride; ++i) {\n      const tmp = flatCoordinates[offset + i];\n      flatCoordinates[offset + i] = flatCoordinates[end - stride + i];\n      flatCoordinates[end - stride + i] = tmp;\n    }\n    offset += stride;\n    end -= stride;\n  }\n}\n", "/**\n * @module ol/geom/flat/orient\n */\nimport {coordinates as reverseCoordinates} from './reverse.js';\n\n/**\n * Is the linear ring oriented clockwise in a coordinate system with a bottom-left\n * coordinate origin? For a coordinate system with a top-left coordinate origin,\n * the ring's orientation is clockwise when this function returns false.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @return {boolean} Is clockwise.\n */\nexport function linearRingIsClockwise(flatCoordinates, offset, end, stride) {\n  // https://stackoverflow.com/q/1165647/clockwise-method#1165943\n  // https://github.com/OSGeo/gdal/blob/master/gdal/ogr/ogrlinearring.cpp\n  let edge = 0;\n  let x1 = flatCoordinates[end - stride];\n  let y1 = flatCoordinates[end - stride + 1];\n  for (; offset < end; offset += stride) {\n    const x2 = flatCoordinates[offset];\n    const y2 = flatCoordinates[offset + 1];\n    edge += (x2 - x1) * (y2 + y1);\n    x1 = x2;\n    y1 = y2;\n  }\n  return edge === 0 ? undefined : edge > 0;\n}\n\n/**\n * Determines if linear rings are oriented.  By default, left-hand orientation\n * is tested (first ring must be clockwise, remaining rings counter-clockwise).\n * To test for right-hand orientation, use the `right` argument.\n *\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Array of end indexes.\n * @param {number} stride Stride.\n * @param {boolean} [right] Test for right-hand orientation\n *     (counter-clockwise exterior ring and clockwise interior rings).\n * @return {boolean} Rings are correctly oriented.\n */\nexport function linearRingsAreOriented(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  right\n) {\n  right = right !== undefined ? right : false;\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    const isClockwise = linearRingIsClockwise(\n      flatCoordinates,\n      offset,\n      end,\n      stride\n    );\n    if (i === 0) {\n      if ((right && isClockwise) || (!right && !isClockwise)) {\n        return false;\n      }\n    } else {\n      if ((right && !isClockwise) || (!right && isClockwise)) {\n        return false;\n      }\n    }\n    offset = end;\n  }\n  return true;\n}\n\n/**\n * Determines if linear rings are oriented.  By default, left-hand orientation\n * is tested (first ring must be clockwise, remaining rings counter-clockwise).\n * To test for right-hand orientation, use the `right` argument.\n *\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Array of array of end indexes.\n * @param {number} stride Stride.\n * @param {boolean} [right] Test for right-hand orientation\n *     (counter-clockwise exterior ring and clockwise interior rings).\n * @return {boolean} Rings are correctly oriented.\n */\nexport function linearRingssAreOriented(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  right\n) {\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    if (!linearRingsAreOriented(flatCoordinates, offset, ends, stride, right)) {\n      return false;\n    }\n    if (ends.length) {\n      offset = ends[ends.length - 1];\n    }\n  }\n  return true;\n}\n\n/**\n * Orient coordinates in a flat array of linear rings.  By default, rings\n * are oriented following the left-hand rule (clockwise for exterior and\n * counter-clockwise for interior rings).  To orient according to the\n * right-hand rule, use the `right` argument.\n *\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {boolean} [right] Follow the right-hand rule for orientation.\n * @return {number} End.\n */\nexport function orientLinearRings(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  right\n) {\n  right = right !== undefined ? right : false;\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    const isClockwise = linearRingIsClockwise(\n      flatCoordinates,\n      offset,\n      end,\n      stride\n    );\n    const reverse =\n      i === 0\n        ? (right && isClockwise) || (!right && !isClockwise)\n        : (right && !isClockwise) || (!right && isClockwise);\n    if (reverse) {\n      reverseCoordinates(flatCoordinates, offset, end, stride);\n    }\n    offset = end;\n  }\n  return offset;\n}\n\n/**\n * Orient coordinates in a flat array of linear rings.  By default, rings\n * are oriented following the left-hand rule (clockwise for exterior and\n * counter-clockwise for interior rings).  To orient according to the\n * right-hand rule, use the `right` argument.\n *\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Array of array of end indexes.\n * @param {number} stride Stride.\n * @param {boolean} [right] Follow the right-hand rule for orientation.\n * @return {number} End.\n */\nexport function orientLinearRingsArray(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  right\n) {\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    offset = orientLinearRings(\n      flatCoordinates,\n      offset,\n      endss[i],\n      stride,\n      right\n    );\n  }\n  return offset;\n}\n\n/**\n * Return a two-dimensional endss\n * @param {Array<number>} flatCoordinates Flat coordinates\n * @param {Array<number>} ends Linear ring end indexes\n * @return {Array<Array<number>>} Two dimensional endss array that can\n * be used to construct a MultiPolygon\n */\nexport function inflateEnds(flatCoordinates, ends) {\n  const endss = [];\n  let offset = 0;\n  let prevEndIndex = 0;\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    // classifies an array of rings into polygons with outer rings and holes\n    if (!linearRingIsClockwise(flatCoordinates, offset, end, 2)) {\n      endss.push(ends.slice(prevEndIndex, i + 1));\n    } else {\n      if (endss.length === 0) {\n        continue;\n      }\n      endss[endss.length - 1].push(ends[prevEndIndex]);\n    }\n    prevEndIndex = i + 1;\n    offset = end;\n  }\n  return endss;\n}\n", "/**\n * @module ol/geom/flat/interiorpoint\n */\nimport {ascending} from '../../array.js';\nimport {linearRingsContainsXY} from './contains.js';\n\n/**\n * Calculates a point that is likely to lie in the interior of the linear rings.\n * Inspired by JTS's com.vividsolutions.jts.geom.Geometry#getInteriorPoint.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {Array<number>} flatCenters Flat centers.\n * @param {number} flatCentersOffset Flat center offset.\n * @param {Array<number>} [dest] Destination.\n * @return {Array<number>} Destination point as XYM coordinate, where M is the\n * length of the horizontal intersection that the point belongs to.\n */\nexport function getInteriorPointOfArray(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  flatCenters,\n  flatCentersOffset,\n  dest\n) {\n  let i, ii, x, x1, x2, y1, y2;\n  const y = flatCenters[flatCentersOffset + 1];\n  /** @type {Array<number>} */\n  const intersections = [];\n  // Calculate intersections with the horizontal line\n  for (let r = 0, rr = ends.length; r < rr; ++r) {\n    const end = ends[r];\n    x1 = flatCoordinates[end - stride];\n    y1 = flatCoordinates[end - stride + 1];\n    for (i = offset; i < end; i += stride) {\n      x2 = flatCoordinates[i];\n      y2 = flatCoordinates[i + 1];\n      if ((y <= y1 && y2 <= y) || (y1 <= y && y <= y2)) {\n        x = ((y - y1) / (y2 - y1)) * (x2 - x1) + x1;\n        intersections.push(x);\n      }\n      x1 = x2;\n      y1 = y2;\n    }\n  }\n  // Find the longest segment of the horizontal line that has its center point\n  // inside the linear ring.\n  let pointX = NaN;\n  let maxSegmentLength = -Infinity;\n  intersections.sort(ascending);\n  x1 = intersections[0];\n  for (i = 1, ii = intersections.length; i < ii; ++i) {\n    x2 = intersections[i];\n    const segmentLength = Math.abs(x2 - x1);\n    if (segmentLength > maxSegmentLength) {\n      x = (x1 + x2) / 2;\n      if (linearRingsContainsXY(flatCoordinates, offset, ends, stride, x, y)) {\n        pointX = x;\n        maxSegmentLength = segmentLength;\n      }\n    }\n    x1 = x2;\n  }\n  if (isNaN(pointX)) {\n    // There is no horizontal line that has its center point inside the linear\n    // ring.  Use the center of the the linear ring's extent.\n    pointX = flatCenters[flatCentersOffset];\n  }\n  if (dest) {\n    dest.push(pointX, y, maxSegmentLength);\n    return dest;\n  }\n  return [pointX, y, maxSegmentLength];\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {Array<number>} flatCenters Flat centers.\n * @return {Array<number>} Interior points as XYM coordinates, where M is the\n * length of the horizontal intersection that the point belongs to.\n */\nexport function getInteriorPointsOfMultiArray(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  flatCenters\n) {\n  let interiorPoints = [];\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    interiorPoints = getInteriorPointOfArray(\n      flatCoordinates,\n      offset,\n      ends,\n      stride,\n      flatCenters,\n      2 * i,\n      interiorPoints\n    );\n    offset = ends[ends.length - 1];\n  }\n  return interiorPoints;\n}\n", "/**\n * @module ol/geom/Polygon\n */\nimport LinearRing from './LinearRing.js';\nimport Point from './Point.js';\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {arrayMaxSquaredDelta, assignClosestArrayPoint} from './flat/closest.js';\nimport {closestSquaredDistanceXY, getCenter, isEmpty} from '../extent.js';\nimport {deflateCoordinatesArray} from './flat/deflate.js';\nimport {extend} from '../array.js';\nimport {getInteriorPointOfArray} from './flat/interiorpoint.js';\nimport {inflateCoordinatesArray} from './flat/inflate.js';\nimport {intersectsLinearRingArray} from './flat/intersectsextent.js';\nimport {linearRingsAreOriented, orientLinearRings} from './flat/orient.js';\nimport {linearRings as linearRingsArea} from './flat/area.js';\nimport {linearRingsContainsXY} from './flat/contains.js';\nimport {modulo} from '../math.js';\nimport {quantizeArray} from './flat/simplify.js';\nimport {offset as sphereOffset} from '../sphere.js';\n\n/**\n * @classdesc\n * Polygon geometry.\n *\n * @api\n */\nclass Polygon extends SimpleGeometry {\n  /**\n   * @param {!Array<Array<import(\"../coordinate.js\").Coordinate>>|!Array<number>} coordinates\n   *     Array of linear rings that define the polygon. The first linear ring of the\n   *     array defines the outer-boundary or surface of the polygon. Each subsequent\n   *     linear ring defines a hole in the surface of the polygon. A linear ring is\n   *     an array of vertices' coordinates where the first coordinate and the last are\n   *     equivalent. (For internal use, flat coordinates in combination with\n   *     `layout` and `ends` are also accepted.)\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @param {Array<number>} [ends] Ends (for internal use with flat coordinates).\n   */\n  constructor(coordinates, layout, ends) {\n    super();\n\n    /**\n     * @type {Array<number>}\n     * @private\n     */\n    this.ends_ = [];\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.flatInteriorPointRevision_ = -1;\n\n    /**\n     * @private\n     * @type {import(\"../coordinate.js\").Coordinate}\n     */\n    this.flatInteriorPoint_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDelta_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDeltaRevision_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.orientedRevision_ = -1;\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.orientedFlatCoordinates_ = null;\n\n    if (layout !== undefined && ends) {\n      this.setFlatCoordinates(\n        layout,\n        /** @type {Array<number>} */ (coordinates)\n      );\n      this.ends_ = ends;\n    } else {\n      this.setCoordinates(\n        /** @type {Array<Array<import(\"../coordinate.js\").Coordinate>>} */ (\n          coordinates\n        ),\n        layout\n      );\n    }\n  }\n\n  /**\n   * Append the passed linear ring to this polygon.\n   * @param {LinearRing} linearRing Linear ring.\n   * @api\n   */\n  appendLinearRing(linearRing) {\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = linearRing.getFlatCoordinates().slice();\n    } else {\n      extend(this.flatCoordinates, linearRing.getFlatCoordinates());\n    }\n    this.ends_.push(this.flatCoordinates.length);\n    this.changed();\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!Polygon} Clone.\n   * @api\n   */\n  clone() {\n    const polygon = new Polygon(\n      this.flatCoordinates.slice(),\n      this.layout,\n      this.ends_.slice()\n    );\n    polygon.applyProperties(this);\n    return polygon;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    if (minSquaredDistance < closestSquaredDistanceXY(this.getExtent(), x, y)) {\n      return minSquaredDistance;\n    }\n    if (this.maxDeltaRevision_ != this.getRevision()) {\n      this.maxDelta_ = Math.sqrt(\n        arrayMaxSquaredDelta(\n          this.flatCoordinates,\n          0,\n          this.ends_,\n          this.stride,\n          0\n        )\n      );\n      this.maxDeltaRevision_ = this.getRevision();\n    }\n    return assignClosestArrayPoint(\n      this.flatCoordinates,\n      0,\n      this.ends_,\n      this.stride,\n      this.maxDelta_,\n      true,\n      x,\n      y,\n      closestPoint,\n      minSquaredDistance\n    );\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @return {boolean} Contains (x, y).\n   */\n  containsXY(x, y) {\n    return linearRingsContainsXY(\n      this.getOrientedFlatCoordinates(),\n      0,\n      this.ends_,\n      this.stride,\n      x,\n      y\n    );\n  }\n\n  /**\n   * Return the area of the polygon on projected plane.\n   * @return {number} Area (on projected plane).\n   * @api\n   */\n  getArea() {\n    return linearRingsArea(\n      this.getOrientedFlatCoordinates(),\n      0,\n      this.ends_,\n      this.stride\n    );\n  }\n\n  /**\n   * Get the coordinate array for this geometry.  This array has the structure\n   * of a GeoJSON coordinate array for polygons.\n   *\n   * @param {boolean} [right] Orient coordinates according to the right-hand\n   *     rule (counter-clockwise for exterior and clockwise for interior rings).\n   *     If `false`, coordinates will be oriented according to the left-hand rule\n   *     (clockwise for exterior and counter-clockwise for interior rings).\n   *     By default, coordinate orientation will depend on how the geometry was\n   *     constructed.\n   * @return {Array<Array<import(\"../coordinate.js\").Coordinate>>} Coordinates.\n   * @api\n   */\n  getCoordinates(right) {\n    let flatCoordinates;\n    if (right !== undefined) {\n      flatCoordinates = this.getOrientedFlatCoordinates().slice();\n      orientLinearRings(flatCoordinates, 0, this.ends_, this.stride, right);\n    } else {\n      flatCoordinates = this.flatCoordinates;\n    }\n\n    return inflateCoordinatesArray(flatCoordinates, 0, this.ends_, this.stride);\n  }\n\n  /**\n   * @return {Array<number>} Ends.\n   */\n  getEnds() {\n    return this.ends_;\n  }\n\n  /**\n   * @return {Array<number>} Interior point.\n   */\n  getFlatInteriorPoint() {\n    if (this.flatInteriorPointRevision_ != this.getRevision()) {\n      const flatCenter = getCenter(this.getExtent());\n      this.flatInteriorPoint_ = getInteriorPointOfArray(\n        this.getOrientedFlatCoordinates(),\n        0,\n        this.ends_,\n        this.stride,\n        flatCenter,\n        0\n      );\n      this.flatInteriorPointRevision_ = this.getRevision();\n    }\n    return this.flatInteriorPoint_;\n  }\n\n  /**\n   * Return an interior point of the polygon.\n   * @return {Point} Interior point as XYM coordinate, where M is the\n   * length of the horizontal intersection that the point belongs to.\n   * @api\n   */\n  getInteriorPoint() {\n    return new Point(this.getFlatInteriorPoint(), 'XYM');\n  }\n\n  /**\n   * Return the number of rings of the polygon,  this includes the exterior\n   * ring and any interior rings.\n   *\n   * @return {number} Number of rings.\n   * @api\n   */\n  getLinearRingCount() {\n    return this.ends_.length;\n  }\n\n  /**\n   * Return the Nth linear ring of the polygon geometry. Return `null` if the\n   * given index is out of range.\n   * The exterior linear ring is available at index `0` and the interior rings\n   * at index `1` and beyond.\n   *\n   * @param {number} index Index.\n   * @return {LinearRing|null} Linear ring.\n   * @api\n   */\n  getLinearRing(index) {\n    if (index < 0 || this.ends_.length <= index) {\n      return null;\n    }\n    return new LinearRing(\n      this.flatCoordinates.slice(\n        index === 0 ? 0 : this.ends_[index - 1],\n        this.ends_[index]\n      ),\n      this.layout\n    );\n  }\n\n  /**\n   * Return the linear rings of the polygon.\n   * @return {Array<LinearRing>} Linear rings.\n   * @api\n   */\n  getLinearRings() {\n    const layout = this.layout;\n    const flatCoordinates = this.flatCoordinates;\n    const ends = this.ends_;\n    const linearRings = [];\n    let offset = 0;\n    for (let i = 0, ii = ends.length; i < ii; ++i) {\n      const end = ends[i];\n      const linearRing = new LinearRing(\n        flatCoordinates.slice(offset, end),\n        layout\n      );\n      linearRings.push(linearRing);\n      offset = end;\n    }\n    return linearRings;\n  }\n\n  /**\n   * @return {Array<number>} Oriented flat coordinates.\n   */\n  getOrientedFlatCoordinates() {\n    if (this.orientedRevision_ != this.getRevision()) {\n      const flatCoordinates = this.flatCoordinates;\n      if (linearRingsAreOriented(flatCoordinates, 0, this.ends_, this.stride)) {\n        this.orientedFlatCoordinates_ = flatCoordinates;\n      } else {\n        this.orientedFlatCoordinates_ = flatCoordinates.slice();\n        this.orientedFlatCoordinates_.length = orientLinearRings(\n          this.orientedFlatCoordinates_,\n          0,\n          this.ends_,\n          this.stride\n        );\n      }\n      this.orientedRevision_ = this.getRevision();\n    }\n    return this.orientedFlatCoordinates_;\n  }\n\n  /**\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {Polygon} Simplified Polygon.\n   * @protected\n   */\n  getSimplifiedGeometryInternal(squaredTolerance) {\n    const simplifiedFlatCoordinates = [];\n    const simplifiedEnds = [];\n    simplifiedFlatCoordinates.length = quantizeArray(\n      this.flatCoordinates,\n      0,\n      this.ends_,\n      this.stride,\n      Math.sqrt(squaredTolerance),\n      simplifiedFlatCoordinates,\n      0,\n      simplifiedEnds\n    );\n    return new Polygon(simplifiedFlatCoordinates, 'XY', simplifiedEnds);\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   */\n  getType() {\n    return 'Polygon';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   */\n  intersectsExtent(extent) {\n    return intersectsLinearRingArray(\n      this.getOrientedFlatCoordinates(),\n      0,\n      this.ends_,\n      this.stride,\n      extent\n    );\n  }\n\n  /**\n   * Set the coordinates of the polygon.\n   * @param {!Array<Array<import(\"../coordinate.js\").Coordinate>>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   */\n  setCoordinates(coordinates, layout) {\n    this.setLayout(layout, coordinates, 2);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    const ends = deflateCoordinatesArray(\n      this.flatCoordinates,\n      0,\n      coordinates,\n      this.stride,\n      this.ends_\n    );\n    this.flatCoordinates.length = ends.length === 0 ? 0 : ends[ends.length - 1];\n    this.changed();\n  }\n}\n\nexport default Polygon;\n\n/**\n * Create an approximation of a circle on the surface of a sphere.\n * @param {import(\"../coordinate.js\").Coordinate} center Center (`[lon, lat]` in degrees).\n * @param {number} radius The great-circle distance from the center to\n *     the polygon vertices in meters.\n * @param {number} [n] Optional number of vertices for the resulting\n *     polygon. Default is `32`.\n * @param {number} [sphereRadius] Optional radius for the sphere (defaults to\n *     the Earth's mean radius using the WGS84 ellipsoid).\n * @return {Polygon} The \"circular\" polygon.\n * @api\n */\nexport function circular(center, radius, n, sphereRadius) {\n  n = n ? n : 32;\n  /** @type {Array<number>} */\n  const flatCoordinates = [];\n  for (let i = 0; i < n; ++i) {\n    extend(\n      flatCoordinates,\n      sphereOffset(center, radius, (2 * Math.PI * i) / n, sphereRadius)\n    );\n  }\n  flatCoordinates.push(flatCoordinates[0], flatCoordinates[1]);\n  return new Polygon(flatCoordinates, 'XY', [flatCoordinates.length]);\n}\n\n/**\n * Create a polygon from an extent. The layout used is `XY`.\n * @param {import(\"../extent.js\").Extent} extent The extent.\n * @return {Polygon} The polygon.\n * @api\n */\nexport function fromExtent(extent) {\n  if (isEmpty(extent)) {\n    throw new Error('Cannot create polygon from empty extent');\n  }\n  const minX = extent[0];\n  const minY = extent[1];\n  const maxX = extent[2];\n  const maxY = extent[3];\n  const flatCoordinates = [\n    minX,\n    minY,\n    minX,\n    maxY,\n    maxX,\n    maxY,\n    maxX,\n    minY,\n    minX,\n    minY,\n  ];\n  return new Polygon(flatCoordinates, 'XY', [flatCoordinates.length]);\n}\n\n/**\n * Create a regular polygon from a circle.\n * @param {import(\"./Circle.js\").default} circle Circle geometry.\n * @param {number} [sides] Number of sides of the polygon. Default is 32.\n * @param {number} [angle] Start angle for the first vertex of the polygon in\n *     counter-clockwise radians. 0 means East. Default is 0.\n * @return {Polygon} Polygon geometry.\n * @api\n */\nexport function fromCircle(circle, sides, angle) {\n  sides = sides ? sides : 32;\n  const stride = circle.getStride();\n  const layout = circle.getLayout();\n  const center = circle.getCenter();\n  const arrayLength = stride * (sides + 1);\n  const flatCoordinates = new Array(arrayLength);\n  for (let i = 0; i < arrayLength; i += stride) {\n    flatCoordinates[i] = 0;\n    flatCoordinates[i + 1] = 0;\n    for (let j = 2; j < stride; j++) {\n      flatCoordinates[i + j] = center[j];\n    }\n  }\n  const ends = [flatCoordinates.length];\n  const polygon = new Polygon(flatCoordinates, layout, ends);\n  makeRegular(polygon, center, circle.getRadius(), angle);\n  return polygon;\n}\n\n/**\n * Modify the coordinates of a polygon to make it a regular polygon.\n * @param {Polygon} polygon Polygon geometry.\n * @param {import(\"../coordinate.js\").Coordinate} center Center of the regular polygon.\n * @param {number} radius Radius of the regular polygon.\n * @param {number} [angle] Start angle for the first vertex of the polygon in\n *     counter-clockwise radians. 0 means East. Default is 0.\n */\nexport function makeRegular(polygon, center, radius, angle) {\n  const flatCoordinates = polygon.getFlatCoordinates();\n  const stride = polygon.getStride();\n  const sides = flatCoordinates.length / stride - 1;\n  const startAngle = angle ? angle : 0;\n  for (let i = 0; i <= sides; ++i) {\n    const offset = i * stride;\n    const angle = startAngle + (modulo(i, sides) * 2 * Math.PI) / sides;\n    flatCoordinates[offset] = center[0] + radius * Math.cos(angle);\n    flatCoordinates[offset + 1] = center[1] + radius * Math.sin(angle);\n  }\n  polygon.changed();\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,IAAM,OAAO,IAAI,MAAM,CAAC;AAMjB,SAAS,SAAS;AACvB,SAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC1B;AAOO,SAAS,MAAM,WAAW;AAC/B,SAAO,IAAI,WAAW,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACxC;AASO,SAAS,SAAS,YAAY,YAAY;AAC/C,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AAEvB,aAAW,CAAC,IAAI,KAAK,KAAK,KAAK;AAC/B,aAAW,CAAC,IAAI,KAAK,KAAK,KAAK;AAC/B,aAAW,CAAC,IAAI,KAAK,KAAK,KAAK;AAC/B,aAAW,CAAC,IAAI,KAAK,KAAK,KAAK;AAC/B,aAAW,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK;AACpC,aAAW,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK;AAEpC,SAAO;AACT;AAaO,SAAS,IAAI,WAAW,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC/C,YAAU,CAAC,IAAI;AACf,YAAU,CAAC,IAAI;AACf,YAAU,CAAC,IAAI;AACf,YAAU,CAAC,IAAI;AACf,YAAU,CAAC,IAAI;AACf,YAAU,CAAC,IAAI;AACf,SAAO;AACT;AAQO,SAAS,aAAa,YAAY,YAAY;AACnD,aAAW,CAAC,IAAI,WAAW,CAAC;AAC5B,aAAW,CAAC,IAAI,WAAW,CAAC;AAC5B,aAAW,CAAC,IAAI,WAAW,CAAC;AAC5B,aAAW,CAAC,IAAI,WAAW,CAAC;AAC5B,aAAW,CAAC,IAAI,WAAW,CAAC;AAC5B,aAAW,CAAC,IAAI,WAAW,CAAC;AAC5B,SAAO;AACT;AAWO,SAAS,MAAM,WAAW,YAAY;AAC3C,QAAM,IAAI,WAAW,CAAC;AACtB,QAAM,IAAI,WAAW,CAAC;AACtB,aAAW,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC;AACjE,aAAW,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC;AACjE,SAAO;AACT;AAQO,SAAS,OAAO,WAAW,OAAO;AACvC,QAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,QAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,SAAO,SAAS,WAAW,IAAI,MAAM,KAAK,KAAK,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC;AACjE;AASO,SAAS,MAAM,WAAW,GAAG,GAAG;AACrC,SAAO,SAAS,WAAW,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACxD;AASO,SAAS,UAAU,QAAQ,GAAG,GAAG;AACtC,SAAO,IAAI,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACrC;AASO,SAAS,UAAU,WAAW,IAAI,IAAI;AAC3C,SAAO,SAAS,WAAW,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;AAC1D;AAeO,SAAS,QAAQ,WAAW,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK;AACpE,QAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,QAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,YAAU,CAAC,IAAI,KAAK;AACpB,YAAU,CAAC,IAAI,KAAK;AACpB,YAAU,CAAC,IAAI,CAAC,KAAK;AACrB,YAAU,CAAC,IAAI,KAAK;AACpB,YAAU,CAAC,IAAI,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM;AACjD,YAAU,CAAC,IAAI,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM;AACjD,SAAO;AACT;AAoCO,SAAS,YAAY,QAAQ,QAAQ;AAC1C,QAAM,MAAM,YAAY,MAAM;AAC9B,SAAO,QAAQ,GAAG,EAAE;AAEpB,QAAM,IAAI,OAAO,CAAC;AAClB,QAAM,IAAI,OAAO,CAAC;AAClB,QAAM,IAAI,OAAO,CAAC;AAClB,QAAM,IAAI,OAAO,CAAC;AAClB,QAAM,IAAI,OAAO,CAAC;AAClB,QAAM,IAAI,OAAO,CAAC;AAElB,SAAO,CAAC,IAAI,IAAI;AAChB,SAAO,CAAC,IAAI,CAAC,IAAI;AACjB,SAAO,CAAC,IAAI,CAAC,IAAI;AACjB,SAAO,CAAC,IAAI,IAAI;AAChB,SAAO,CAAC,KAAK,IAAI,IAAI,IAAI,KAAK;AAC9B,SAAO,CAAC,IAAI,EAAE,IAAI,IAAI,IAAI,KAAK;AAE/B,SAAO;AACT;AAOO,SAAS,YAAY,KAAK;AAC/B,SAAO,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;AACzC;AAMA,IAAI;AAQG,SAAS,SAAS,KAAK;AAC5B,QAAM,kBAAkB,YAAY,IAAI,KAAK,IAAI,IAAI;AACrD,MAAI,yBAAyB;AAC3B,WAAO;AAAA,EACT;AACA,QAAM,OACJ,uBAAuB,qBAAqB,SAAS,cAAc,KAAK;AAC1E,OAAK,MAAM,YAAY;AACvB,SAAO,KAAK,MAAM;AACpB;;;ACnRO,SAAS,YACd,iBACAA,SACA,KACA,QACA,WACA,MACA;AACA,SAAO,OAAO,OAAO,CAAC;AACtB,MAAI,IAAI;AACR,WAAS,IAAIA,SAAQ,IAAI,KAAK,KAAK,QAAQ;AACzC,UAAM,IAAI,gBAAgB,CAAC;AAC3B,UAAM,IAAI,gBAAgB,IAAI,CAAC;AAC/B,SAAK,GAAG,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC;AAC7D,SAAK,GAAG,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC;AAAA,EAC/D;AACA,MAAI,QAAQ,KAAK,UAAU,GAAG;AAC5B,SAAK,SAAS;AAAA,EAChB;AACA,SAAO;AACT;AAYO,SAASC,QACd,iBACAD,SACA,KACA,QACA,OACA,QACA,MACA;AACA,SAAO,OAAO,OAAO,CAAC;AACtB,QAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,QAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,QAAM,UAAU,OAAO,CAAC;AACxB,QAAM,UAAU,OAAO,CAAC;AACxB,MAAI,IAAI;AACR,WAAS,IAAIA,SAAQ,IAAI,KAAK,KAAK,QAAQ;AACzC,UAAM,SAAS,gBAAgB,CAAC,IAAI;AACpC,UAAM,SAAS,gBAAgB,IAAI,CAAC,IAAI;AACxC,SAAK,GAAG,IAAI,UAAU,SAAS,MAAM,SAAS;AAC9C,SAAK,GAAG,IAAI,UAAU,SAAS,MAAM,SAAS;AAC9C,aAAS,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACvC,WAAK,GAAG,IAAI,gBAAgB,CAAC;AAAA,IAC/B;AAAA,EACF;AACA,MAAI,QAAQ,KAAK,UAAU,GAAG;AAC5B,SAAK,SAAS;AAAA,EAChB;AACA,SAAO;AACT;AAcO,SAASE,OACd,iBACAF,SACA,KACA,QACA,IACA,IACA,QACA,MACA;AACA,SAAO,OAAO,OAAO,CAAC;AACtB,QAAM,UAAU,OAAO,CAAC;AACxB,QAAM,UAAU,OAAO,CAAC;AACxB,MAAI,IAAI;AACR,WAAS,IAAIA,SAAQ,IAAI,KAAK,KAAK,QAAQ;AACzC,UAAM,SAAS,gBAAgB,CAAC,IAAI;AACpC,UAAM,SAAS,gBAAgB,IAAI,CAAC,IAAI;AACxC,SAAK,GAAG,IAAI,UAAU,KAAK;AAC3B,SAAK,GAAG,IAAI,UAAU,KAAK;AAC3B,aAAS,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACvC,WAAK,GAAG,IAAI,gBAAgB,CAAC;AAAA,IAC/B;AAAA,EACF;AACA,MAAI,QAAQ,KAAK,UAAU,GAAG;AAC5B,SAAK,SAAS;AAAA,EAChB;AACA,SAAO;AACT;AAYO,SAASG,WACd,iBACAH,SACA,KACA,QACA,QACA,QACA,MACA;AACA,SAAO,OAAO,OAAO,CAAC;AACtB,MAAI,IAAI;AACR,WAAS,IAAIA,SAAQ,IAAI,KAAK,KAAK,QAAQ;AACzC,SAAK,GAAG,IAAI,gBAAgB,CAAC,IAAI;AACjC,SAAK,GAAG,IAAI,gBAAgB,IAAI,CAAC,IAAI;AACrC,aAAS,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACvC,WAAK,GAAG,IAAI,gBAAgB,CAAC;AAAA,IAC/B;AAAA,EACF;AACA,MAAI,QAAQ,KAAK,UAAU,GAAG;AAC5B,SAAK,SAAS;AAAA,EAChB;AACA,SAAO;AACT;;;ACjHA,IAAM,eAAe,OAAgB;AAcrC,IAAM,WAAN,cAAuB,eAAW;AAAA,EAChC,cAAc;AACZ,UAAM;AAMN,SAAK,UAAU,YAAY;AAM3B,SAAK,kBAAkB;AAMvB,SAAK,2CAA2C;AAMhD,SAAK,6BAA6B;AAUlC,SAAK,8BAA8B,WAAW,SAC5C,UACA,kBACA,WACA;AACA,UAAI,CAAC,WAAW;AACd,eAAO,KAAK,sBAAsB,gBAAgB;AAAA,MACpD;AACA,YAAM,QAAQ,KAAK,MAAM;AACzB,YAAM,eAAe,SAAS;AAC9B,aAAO,MAAM,sBAAsB,gBAAgB;AAAA,IACrD,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,oBAAoB,kBAAkB,WAAW;AAC/C,WAAO,KAAK;AAAA,MACV,KAAK,YAAY;AAAA,MACjB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,GAAG,GAAG;AACf,UAAM,QAAQ,KAAK,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACzC,WAAO,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,gBAAgB,OAAO,cAAc;AACnC,mBAAe,eAAe,eAAe,CAAC,KAAK,GAAG;AACtD,SAAK,eAAe,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,cAAc,QAAQ;AAC9D,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,qBAAqB,YAAY;AAC/B,WAAO,KAAK,WAAW,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,QAAQ;AACpB,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,QAAI,KAAK,mBAAmB,KAAK,YAAY,GAAG;AAC9C,YAAMI,UAAS,KAAK,cAAc,KAAK,OAAO;AAC9C,UAAI,MAAMA,QAAO,CAAC,CAAC,KAAK,MAAMA,QAAO,CAAC,CAAC,GAAG;AACxC,4BAAoBA,OAAM;AAAA,MAC5B;AACA,WAAK,kBAAkB,KAAK,YAAY;AAAA,IAC1C;AACA,WAAO,eAAe,KAAK,SAAS,MAAM;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,OAAO,QAAQ;AACpB,aAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,IAAI,IAAI,QAAQ;AACpB,aAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,SAAS,WAAW;AAClB,WAAO,KAAK,sBAAsB,YAAY,SAAS;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,sBAAsB,kBAAkB;AACtC,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,eAAe,aAAa;AAC1B,aAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,QAAQ;AACvB,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,UAAU,QAAQ,QAAQ;AACxB,aAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,UAAU,QAAQ,aAAa;AAE7B,UAAM,aAAa,IAAc,MAAM;AACvC,UAAM,cACJ,WAAW,SAAS,KAAK,gBACrB,SAAU,eAAe,gBAAgB,QAAQ;AAC/C,YAAM,cAAc,WAAW,UAAU;AACzC,YAAM,kBAAkB,WAAW,eAAe;AAClD,YAAMC,SAAQ,UAAU,eAAe,IAAI,UAAU,WAAW;AAChE;AAAA,QACE;AAAA,QACA,gBAAgB,CAAC;AAAA,QACjB,gBAAgB,CAAC;AAAA,QACjBA;AAAA,QACA,CAACA;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,cAAc;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO,aAAa,YAAY,WAAW;AAAA,QACzC;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,IACA,aAAa,YAAY,WAAW;AAC1C,SAAK,eAAe,WAAW;AAC/B,WAAO;AAAA,EACT;AACF;AAEA,IAAO,mBAAQ;;;AC1Uf,IAAM,iBAAN,cAA6B,iBAAS;AAAA,EACpC,cAAc;AACZ,UAAM;AAMN,SAAK,SAAS;AAMd,SAAK,SAAS;AAMd,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,QAAQ;AACpB,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB;AACf,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,qBAAqB;AACnB,WAAO,KAAK,gBAAgB,MAAM,GAAG,KAAK,MAAM;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB;AAClB,WAAO,KAAK,gBAAgB;AAAA,MAC1B,KAAK,gBAAgB,SAAS,KAAK;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,kBAAkB;AACtC,QAAI,KAAK,+BAA+B,KAAK,YAAY,GAAG;AAC1D,WAAK,2CAA2C;AAChD,WAAK,6BAA6B,KAAK,YAAY;AAAA,IACrD;AAGA,QACE,mBAAmB,KAClB,KAAK,6CAA6C,KACjD,oBAAoB,KAAK,0CAC3B;AACA,aAAO;AAAA,IACT;AAEA,UAAM,qBACJ,KAAK,8BAA8B,gBAAgB;AACrD,UAAM,4BAA4B,mBAAmB,mBAAmB;AACxE,QAAI,0BAA0B,SAAS,KAAK,gBAAgB,QAAQ;AAClE,aAAO;AAAA,IACT;AAOA,SAAK,2CAA2C;AAChD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,8BAA8B,kBAAkB;AAC9C,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,QAAQ,iBAAiB;AAC1C,SAAK,SAAS,mBAAmB,MAAM;AACvC,SAAK,SAAS;AACd,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAeC,cAAa,QAAQ;AAClC,aAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQA,cAAa,SAAS;AAEtC,QAAI;AACJ,QAAI,QAAQ;AACV,eAAS,mBAAmB,MAAM;AAAA,IACpC,OAAO;AACL,eAAS,IAAI,GAAG,IAAI,SAAS,EAAE,GAAG;AAChC,YAAIA,aAAY,WAAW,GAAG;AAC5B,eAAK,SAAS;AACd,eAAK,SAAS;AACd;AAAA,QACF;AACA,QAAAA;AAAA,QAAoCA,aAAY,CAAC;AAAA,MACnD;AACA,eAASA,aAAY;AACrB,eAAS,mBAAmB,MAAM;AAAA,IACpC;AACA,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,eAAe,aAAa;AAC1B,QAAI,KAAK,iBAAiB;AACxB,kBAAY,KAAK,iBAAiB,KAAK,iBAAiB,KAAK,MAAM;AACnE,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,OAAO,QAAQ;AACpB,UAAM,kBAAkB,KAAK,mBAAmB;AAChD,QAAI,iBAAiB;AACnB,YAAM,SAAS,KAAK,UAAU;AAC9B,MAAAC;AAAA,QACE;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,IAAI,IAAI,QAAQ;AACpB,QAAI,OAAO,QAAW;AACpB,WAAK;AAAA,IACP;AACA,QAAI,CAAC,QAAQ;AACX,eAAS,UAAU,KAAK,UAAU,CAAC;AAAA,IACrC;AACA,UAAM,kBAAkB,KAAK,mBAAmB;AAChD,QAAI,iBAAiB;AACnB,YAAM,SAAS,KAAK,UAAU;AAC9B,MAAAC;AAAA,QACE;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,QAAQ,QAAQ;AACxB,UAAM,kBAAkB,KAAK,mBAAmB;AAChD,QAAI,iBAAiB;AACnB,YAAM,SAAS,KAAK,UAAU;AAC9B,MAAAC;AAAA,QACE;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AACF;AAMA,SAAS,mBAAmB,QAAQ;AAClC,MAAI;AACJ,MAAI,UAAU,GAAG;AACf,aAAS;AAAA,EACX,WAAW,UAAU,GAAG;AACtB,aAAS;AAAA,EACX,WAAW,UAAU,GAAG;AACtB,aAAS;AAAA,EACX;AACA;AAAA;AAAA,IAA8D;AAAA;AAChE;AAMO,SAAS,mBAAmB,QAAQ;AACzC,MAAI;AACJ,MAAI,UAAU,MAAM;AAClB,aAAS;AAAA,EACX,WAAW,UAAU,SAAS,UAAU,OAAO;AAC7C,aAAS;AAAA,EACX,WAAW,UAAU,QAAQ;AAC3B,aAAS;AAAA,EACX;AACA;AAAA;AAAA,IAA8B;AAAA;AAChC;AAQO,SAAS,gBAAgB,gBAAgB,WAAW,MAAM;AAC/D,QAAM,kBAAkB,eAAe,mBAAmB;AAC1D,MAAI,CAAC,iBAAiB;AACpB,WAAO;AAAA,EACT;AACA,QAAM,SAAS,eAAe,UAAU;AACxC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAO,yBAAQ;;;ACjVR,SAAS,kBAAkB,iBAAiBC,SAAQ,YAAY,QAAQ;AAC7E,WAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,oBAAgBA,SAAQ,IAAI,WAAW,CAAC;AAAA,EAC1C;AACA,SAAOA;AACT;AASO,SAAS,mBACd,iBACAA,SACAC,cACA,QACA;AACA,WAAS,IAAI,GAAG,KAAKA,aAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,UAAM,aAAaA,aAAY,CAAC;AAChC,aAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,sBAAgBD,SAAQ,IAAI,WAAW,CAAC;AAAA,IAC1C;AAAA,EACF;AACA,SAAOA;AACT;AAUO,SAAS,wBACd,iBACAA,SACA,cACA,QACA,MACA;AACA,SAAO,OAAO,OAAO,CAAC;AACtB,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,KAAK,aAAa,QAAQ,IAAI,IAAI,EAAE,GAAG;AACrD,UAAM,MAAM;AAAA,MACV;AAAA,MACAA;AAAA,MACA,aAAa,CAAC;AAAA,MACd;AAAA,IACF;AACA,SAAK,GAAG,IAAI;AACZ,IAAAA,UAAS;AAAA,EACX;AACA,OAAK,SAAS;AACd,SAAO;AACT;AAUO,SAAS,6BACd,iBACAA,SACA,eACA,QACA,OACA;AACA,UAAQ,QAAQ,QAAQ,CAAC;AACzB,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,KAAK,cAAc,QAAQ,IAAI,IAAI,EAAE,GAAG;AACtD,UAAM,OAAO;AAAA,MACX;AAAA,MACAA;AAAA,MACA,cAAc,CAAC;AAAA,MACf;AAAA,MACA,MAAM,CAAC;AAAA,IACT;AACA,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,CAAC,IAAIA;AAAA,IACZ;AACA,UAAM,GAAG,IAAI;AACb,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,QAAM,SAAS;AACf,SAAO;AACT;;;AC1FA,IAAM,QAAN,MAAM,eAAc,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjC,YAAYE,cAAa,QAAQ;AAC/B,UAAM;AACN,SAAK,eAAeA,cAAa,MAAM;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,UAAM,QAAQ,IAAI,OAAM,KAAK,gBAAgB,MAAM,GAAG,KAAK,MAAM;AACjE,UAAM,gBAAgB,IAAI;AAC1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,UAAM,kBAAkB,KAAK;AAC7B,UAAMC,mBAAkB;AAAA,MACtB;AAAA,MACA;AAAA,MACA,gBAAgB,CAAC;AAAA,MACjB,gBAAgB,CAAC;AAAA,IACnB;AACA,QAAIA,mBAAkB,oBAAoB;AACxC,YAAM,SAAS,KAAK;AACpB,eAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,qBAAa,CAAC,IAAI,gBAAgB,CAAC;AAAA,MACrC;AACA,mBAAa,SAAS;AACtB,aAAOA;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,WAAO,CAAC,KAAK,kBAAkB,CAAC,IAAI,KAAK,gBAAgB,MAAM;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,QAAQ;AACpB,WAAO,6BAA6B,KAAK,iBAAiB,MAAM;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,QAAQ;AACvB,WAAO,WAAW,QAAQ,KAAK,gBAAgB,CAAC,GAAG,KAAK,gBAAgB,CAAC,CAAC;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAeD,cAAa,QAAQ;AAClC,SAAK,UAAU,QAAQA,cAAa,CAAC;AACrC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AACA,SAAK,gBAAgB,SAAS;AAAA,MAC5B,KAAK;AAAA,MACL;AAAA,MACAA;AAAA,MACA,KAAK;AAAA,IACP;AACA,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,gBAAQ;;;ACrGf,SAAS,cACP,iBACA,SACA,SACA,QACA,GACA,GACA,cACA;AACA,QAAM,KAAK,gBAAgB,OAAO;AAClC,QAAM,KAAK,gBAAgB,UAAU,CAAC;AACtC,QAAM,KAAK,gBAAgB,OAAO,IAAI;AACtC,QAAM,KAAK,gBAAgB,UAAU,CAAC,IAAI;AAC1C,MAAIE;AACJ,MAAI,OAAO,KAAK,OAAO,GAAG;AACxB,IAAAA,UAAS;AAAA,EACX,OAAO;AACL,UAAM,MAAM,IAAI,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK,KAAK,KAAK;AAC5D,QAAI,IAAI,GAAG;AACT,MAAAA,UAAS;AAAA,IACX,WAAW,IAAI,GAAG;AAChB,eAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,qBAAa,CAAC,IAAI;AAAA,UAChB,gBAAgB,UAAU,CAAC;AAAA,UAC3B,gBAAgB,UAAU,CAAC;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AACA,mBAAa,SAAS;AACtB;AAAA,IACF,OAAO;AACL,MAAAA,UAAS;AAAA,IACX;AAAA,EACF;AACA,WAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,iBAAa,CAAC,IAAI,gBAAgBA,UAAS,CAAC;AAAA,EAC9C;AACA,eAAa,SAAS;AACxB;AAYO,SAAS,gBAAgB,iBAAiBA,SAAQ,KAAK,QAAQ,KAAK;AACzE,MAAI,KAAK,gBAAgBA,OAAM;AAC/B,MAAI,KAAK,gBAAgBA,UAAS,CAAC;AACnC,OAAKA,WAAU,QAAQA,UAAS,KAAKA,WAAU,QAAQ;AACrD,UAAM,KAAK,gBAAgBA,OAAM;AACjC,UAAM,KAAK,gBAAgBA,UAAS,CAAC;AACrC,UAAM,eAAe,gBAAU,IAAI,IAAI,IAAI,EAAE;AAC7C,QAAI,eAAe,KAAK;AACtB,YAAM;AAAA,IACR;AACA,SAAK;AACL,SAAK;AAAA,EACP;AACA,SAAO;AACT;AAUO,SAAS,qBACd,iBACAA,SACA,MACA,QACA,KACA;AACA,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,UAAM,gBAAgB,iBAAiBA,SAAQ,KAAK,QAAQ,GAAG;AAC/D,IAAAA,UAAS;AAAA,EACX;AACA,SAAO;AACT;AAUO,SAAS,0BACd,iBACAA,SACA,OACA,QACA,KACA;AACA,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,qBAAqB,iBAAiBA,SAAQ,MAAM,QAAQ,GAAG;AACrE,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;AAgBO,SAAS,mBACd,iBACAA,SACA,KACA,QACA,UACA,QACA,GACA,GACA,cACA,oBACA,UACA;AACA,MAAIA,WAAU,KAAK;AACjB,WAAO;AAAA,EACT;AACA,MAAI,GAAGC;AACP,MAAI,aAAa,GAAG;AAElB,IAAAA,mBAAkB;AAAA,MAChB;AAAA,MACA;AAAA,MACA,gBAAgBD,OAAM;AAAA,MACtB,gBAAgBA,UAAS,CAAC;AAAA,IAC5B;AACA,QAAIC,mBAAkB,oBAAoB;AACxC,WAAK,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC3B,qBAAa,CAAC,IAAI,gBAAgBD,UAAS,CAAC;AAAA,MAC9C;AACA,mBAAa,SAAS;AACtB,aAAOC;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,aAAW,WAAW,WAAW,CAAC,KAAK,GAAG;AAC1C,MAAI,QAAQD,UAAS;AACrB,SAAO,QAAQ,KAAK;AAClB;AAAA,MACE;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,IAAAC,mBAAkB,gBAAU,GAAG,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAC1D,QAAIA,mBAAkB,oBAAoB;AACxC,2BAAqBA;AACrB,WAAK,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC3B,qBAAa,CAAC,IAAI,SAAS,CAAC;AAAA,MAC9B;AACA,mBAAa,SAAS;AACtB,eAAS;AAAA,IACX,OAAO;AAWL,eACE,SACA,KAAK;AAAA,SACD,KAAK,KAAKA,gBAAe,IAAI,KAAK,KAAK,kBAAkB,KACzD,WACA;AAAA,QACF;AAAA,MACF;AAAA,IACJ;AAAA,EACF;AACA,MAAI,QAAQ;AAEV;AAAA,MACE;AAAA,MACA,MAAM;AAAA,MACND;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,IAAAC,mBAAkB,gBAAU,GAAG,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAC1D,QAAIA,mBAAkB,oBAAoB;AACxC,2BAAqBA;AACrB,WAAK,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC3B,qBAAa,CAAC,IAAI,SAAS,CAAC;AAAA,MAC9B;AACA,mBAAa,SAAS;AAAA,IACxB;AAAA,EACF;AACA,SAAO;AACT;AAgBO,SAAS,wBACd,iBACAD,SACA,MACA,QACA,UACA,QACA,GACA,GACA,cACA,oBACA,UACA;AACA,aAAW,WAAW,WAAW,CAAC,KAAK,GAAG;AAC1C,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,yBAAqB;AAAA,MACnB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,IAAAA,UAAS;AAAA,EACX;AACA,SAAO;AACT;AAgBO,SAAS,6BACd,iBACAA,SACA,OACA,QACA,UACA,QACA,GACA,GACA,cACA,oBACA,UACA;AACA,aAAW,WAAW,WAAW,CAAC,KAAK,GAAG;AAC1C,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,yBAAqB;AAAA,MACnB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;;;ACrPO,SAAS,eACd,iBACAE,SACA,KACA,QACA,kBACA,2BACA,kBACA;AACA,QAAM,KAAK,MAAMA,WAAU;AAC3B,MAAI,IAAI,GAAG;AACT,WAAOA,UAAS,KAAKA,WAAU,QAAQ;AACrC,gCAA0B,kBAAkB,IAAI,gBAAgBA,OAAM;AACtE,gCAA0B,kBAAkB,IAC1C,gBAAgBA,UAAS,CAAC;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAEA,QAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,UAAQ,CAAC,IAAI;AACb,UAAQ,IAAI,CAAC,IAAI;AAEjB,QAAM,QAAQ,CAACA,SAAQ,MAAM,MAAM;AACnC,MAAI,QAAQ;AACZ,SAAO,MAAM,SAAS,GAAG;AACvB,UAAM,OAAO,MAAM,IAAI;AACvB,UAAM,QAAQ,MAAM,IAAI;AACxB,QAAI,qBAAqB;AACzB,UAAM,KAAK,gBAAgB,KAAK;AAChC,UAAM,KAAK,gBAAgB,QAAQ,CAAC;AACpC,UAAM,KAAK,gBAAgB,IAAI;AAC/B,UAAM,KAAK,gBAAgB,OAAO,CAAC;AACnC,aAAS,IAAI,QAAQ,QAAQ,IAAI,MAAM,KAAK,QAAQ;AAClD,YAAM,IAAI,gBAAgB,CAAC;AAC3B,YAAM,IAAI,gBAAgB,IAAI,CAAC;AAC/B,YAAMC,mBAAkB,uBAAuB,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE;AACnE,UAAIA,mBAAkB,oBAAoB;AACxC,gBAAQ;AACR,6BAAqBA;AAAA,MACvB;AAAA,IACF;AACA,QAAI,qBAAqB,kBAAkB;AACzC,eAAS,QAAQD,WAAU,MAAM,IAAI;AACrC,UAAI,QAAQ,SAAS,OAAO;AAC1B,cAAM,KAAK,OAAO,KAAK;AAAA,MACzB;AACA,UAAI,QAAQ,SAAS,MAAM;AACzB,cAAM,KAAK,OAAO,IAAI;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACA,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,QAAI,QAAQ,CAAC,GAAG;AACd,gCAA0B,kBAAkB,IAC1C,gBAAgBA,UAAS,IAAI,MAAM;AACrC,gCAA0B,kBAAkB,IAC1C,gBAAgBA,UAAS,IAAI,SAAS,CAAC;AAAA,IAC3C;AAAA,EACF;AACA,SAAO;AACT;AAcO,SAAS,oBACd,iBACAA,SACA,MACA,QACA,kBACA,2BACA,kBACA,gBACA;AACA,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,uBAAmB;AAAA,MACjB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,mBAAe,KAAK,gBAAgB;AACpC,IAAAA,UAAS;AAAA,EACX;AACA,SAAO;AACT;AAuGO,SAAS,KAAK,OAAO,WAAW;AACrC,SAAO,YAAY,KAAK,MAAM,QAAQ,SAAS;AACjD;AAqBO,SAAS,SACd,iBACAE,SACA,KACA,QACA,WACA,2BACA,kBACA;AAEA,MAAIA,WAAU,KAAK;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,KAAK,gBAAgBA,OAAM,GAAG,SAAS;AAChD,MAAI,KAAK,KAAK,gBAAgBA,UAAS,CAAC,GAAG,SAAS;AACpD,EAAAA,WAAU;AAEV,4BAA0B,kBAAkB,IAAI;AAChD,4BAA0B,kBAAkB,IAAI;AAGhD,MAAI,IAAI;AACR,KAAG;AACD,SAAK,KAAK,gBAAgBA,OAAM,GAAG,SAAS;AAC5C,SAAK,KAAK,gBAAgBA,UAAS,CAAC,GAAG,SAAS;AAChD,IAAAA,WAAU;AACV,QAAIA,WAAU,KAAK;AAKjB,gCAA0B,kBAAkB,IAAI;AAChD,gCAA0B,kBAAkB,IAAI;AAChD,aAAO;AAAA,IACT;AAAA,EACF,SAAS,MAAM,MAAM,MAAM;AAC3B,SAAOA,UAAS,KAAK;AAEnB,UAAM,KAAK,KAAK,gBAAgBA,OAAM,GAAG,SAAS;AAClD,UAAM,KAAK,KAAK,gBAAgBA,UAAS,CAAC,GAAG,SAAS;AACtD,IAAAA,WAAU;AAEV,QAAI,MAAM,MAAM,MAAM,IAAI;AACxB;AAAA,IACF;AAEA,UAAM,MAAM,KAAK;AACjB,UAAM,MAAM,KAAK;AAEjB,UAAM,MAAM,KAAK;AACjB,UAAM,MAAM,KAAK;AAIjB,QACE,MAAM,OAAO,MAAM,QACjB,MAAM,KAAK,MAAM,OAAQ,OAAO,OAAQ,MAAM,KAAK,MAAM,SACzD,MAAM,KAAK,MAAM,OAAQ,OAAO,OAAQ,MAAM,KAAK,MAAM,MAC3D;AAEA,WAAK;AACL,WAAK;AACL;AAAA,IACF;AAIA,8BAA0B,kBAAkB,IAAI;AAChD,8BAA0B,kBAAkB,IAAI;AAChD,SAAK;AACL,SAAK;AACL,SAAK;AACL,SAAK;AAAA,EACP;AAEA,4BAA0B,kBAAkB,IAAI;AAChD,4BAA0B,kBAAkB,IAAI;AAChD,SAAO;AACT;AAcO,SAAS,cACd,iBACAA,SACA,MACA,QACA,WACA,2BACA,kBACA,gBACA;AACA,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,uBAAmB;AAAA,MACjB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,mBAAe,KAAK,gBAAgB;AACpC,IAAAA,UAAS;AAAA,EACX;AACA,SAAO;AACT;AAcO,SAAS,mBACd,iBACAA,SACA,OACA,QACA,WACA,2BACA,kBACA,iBACA;AACA,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,iBAAiB,CAAC;AACxB,uBAAmB;AAAA,MACjB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,oBAAgB,KAAK,cAAc;AACnC,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;;;AC/cO,SAAS,mBACd,iBACAC,SACA,KACA,QACAC,cACA;AACA,EAAAA,eAAcA,iBAAgB,SAAYA,eAAc,CAAC;AACzD,MAAI,IAAI;AACR,WAAS,IAAID,SAAQ,IAAI,KAAK,KAAK,QAAQ;AACzC,IAAAC,aAAY,GAAG,IAAI,gBAAgB,MAAM,GAAG,IAAI,MAAM;AAAA,EACxD;AACA,EAAAA,aAAY,SAAS;AACrB,SAAOA;AACT;AAUO,SAAS,wBACd,iBACAD,SACA,MACA,QACA,cACA;AACA,iBAAe,iBAAiB,SAAY,eAAe,CAAC;AAC5D,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,iBAAa,GAAG,IAAI;AAAA,MAClB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,CAAC;AAAA,IAChB;AACA,IAAAA,UAAS;AAAA,EACX;AACA,eAAa,SAAS;AACtB,SAAO;AACT;AAWO,SAAS,6BACd,iBACAA,SACA,OACA,QACA,eACA;AACA,kBAAgB,kBAAkB,SAAY,gBAAgB,CAAC;AAC/D,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,kBAAc,GAAG,IACf,KAAK,WAAW,KAAK,KAAK,CAAC,MAAMA,UAC7B,CAAC,IACD;AAAA,MACE;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,CAAC;AAAA,IACjB;AACN,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,gBAAc,SAAS;AACvB,SAAO;AACT;;;ACnFO,SAAS,WAAW,iBAAiBE,SAAQ,KAAK,QAAQ;AAC/D,MAAI,YAAY;AAChB,MAAI,KAAK,gBAAgB,MAAM,MAAM;AACrC,MAAI,KAAK,gBAAgB,MAAM,SAAS,CAAC;AACzC,SAAOA,UAAS,KAAKA,WAAU,QAAQ;AACrC,UAAM,KAAK,gBAAgBA,OAAM;AACjC,UAAM,KAAK,gBAAgBA,UAAS,CAAC;AACrC,iBAAa,KAAK,KAAK,KAAK;AAC5B,SAAK;AACL,SAAK;AAAA,EACP;AACA,SAAO,YAAY;AACrB;AASO,SAAS,YAAY,iBAAiBA,SAAQ,MAAM,QAAQ;AACjE,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,YAAQ,WAAW,iBAAiBA,SAAQ,KAAK,MAAM;AACvD,IAAAA,UAAS;AAAA,EACX;AACA,SAAO;AACT;AASO,SAAS,aAAa,iBAAiBA,SAAQ,OAAO,QAAQ;AACnE,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,YAAQ,YAAY,iBAAiBA,SAAQ,MAAM,MAAM;AACzD,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;;;ACvCA,IAAM,aAAN,MAAM,oBAAmB,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtC,YAAYC,cAAa,QAAQ;AAC/B,UAAM;AAMN,SAAK,YAAY;AAMjB,SAAK,oBAAoB;AAEzB,QAAI,WAAW,UAAa,CAAC,MAAM,QAAQA,aAAY,CAAC,CAAC,GAAG;AAC1D,WAAK;AAAA,QACH;AAAA;AAAA,QAC8BA;AAAA,MAChC;AAAA,IACF,OAAO;AACL,WAAK;AAAA;AAAA,QAEDA;AAAA,QAEF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,WAAO,IAAI,YAAW,KAAK,gBAAgB,MAAM,GAAG,KAAK,MAAM;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,QAAI,qBAAqB,yBAAyB,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG;AACzE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,qBAAqB,KAAK,YAAY,GAAG;AAChD,WAAK,YAAY,KAAK;AAAA,QACpB;AAAA,UACE,KAAK;AAAA,UACL;AAAA,UACA,KAAK,gBAAgB;AAAA,UACrB,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,WAAK,oBAAoB,KAAK,YAAY;AAAA,IAC5C;AACA,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,8BAA8B,kBAAkB;AAC9C,UAAM,4BAA4B,CAAC;AACnC,8BAA0B,SAAS;AAAA,MACjC,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,IAAI,YAAW,2BAA2B,IAAI;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,QAAQ;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAeA,cAAa,QAAQ;AAClC,SAAK,UAAU,QAAQA,cAAa,CAAC;AACrC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AACA,SAAK,gBAAgB,SAAS;AAAA,MAC5B,KAAK;AAAA,MACL;AAAA,MACAA;AAAA,MACA,KAAK;AAAA,IACP;AACA,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,qBAAQ;;;ACjLR,SAAS,YAAY,iBAAiBC,SAAQ,KAAK,QAAQ;AAChE,SAAOA,UAAS,MAAM,QAAQ;AAC5B,aAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,YAAM,MAAM,gBAAgBA,UAAS,CAAC;AACtC,sBAAgBA,UAAS,CAAC,IAAI,gBAAgB,MAAM,SAAS,CAAC;AAC9D,sBAAgB,MAAM,SAAS,CAAC,IAAI;AAAA,IACtC;AACA,IAAAA,WAAU;AACV,WAAO;AAAA,EACT;AACF;;;ACLO,SAAS,sBAAsB,iBAAiBC,SAAQ,KAAK,QAAQ;AAG1E,MAAI,OAAO;AACX,MAAI,KAAK,gBAAgB,MAAM,MAAM;AACrC,MAAI,KAAK,gBAAgB,MAAM,SAAS,CAAC;AACzC,SAAOA,UAAS,KAAKA,WAAU,QAAQ;AACrC,UAAM,KAAK,gBAAgBA,OAAM;AACjC,UAAM,KAAK,gBAAgBA,UAAS,CAAC;AACrC,aAAS,KAAK,OAAO,KAAK;AAC1B,SAAK;AACL,SAAK;AAAA,EACP;AACA,SAAO,SAAS,IAAI,SAAY,OAAO;AACzC;AAeO,SAAS,uBACd,iBACAA,SACA,MACA,QACA,OACA;AACA,UAAQ,UAAU,SAAY,QAAQ;AACtC,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,UAAM,cAAc;AAAA,MAClB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAI,MAAM,GAAG;AACX,UAAK,SAAS,eAAiB,CAAC,SAAS,CAAC,aAAc;AACtD,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,UAAK,SAAS,CAAC,eAAiB,CAAC,SAAS,aAAc;AACtD,eAAO;AAAA,MACT;AAAA,IACF;AACA,IAAAA,UAAS;AAAA,EACX;AACA,SAAO;AACT;AAeO,SAAS,wBACd,iBACAA,SACA,OACA,QACA,OACA;AACA,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,CAAC,uBAAuB,iBAAiBA,SAAQ,MAAM,QAAQ,KAAK,GAAG;AACzE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,QAAQ;AACf,MAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AACT;AAeO,SAAS,kBACd,iBACAA,SACA,MACA,QACA,OACA;AACA,UAAQ,UAAU,SAAY,QAAQ;AACtC,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,UAAM,cAAc;AAAA,MAClB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,UACJ,MAAM,IACD,SAAS,eAAiB,CAAC,SAAS,CAAC,cACrC,SAAS,CAAC,eAAiB,CAAC,SAAS;AAC5C,QAAI,SAAS;AACX,kBAAmB,iBAAiBA,SAAQ,KAAK,MAAM;AAAA,IACzD;AACA,IAAAA,UAAS;AAAA,EACX;AACA,SAAOA;AACT;AAeO,SAAS,uBACd,iBACAA,SACA,OACA,QACA,OACA;AACA,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,IAAAA,UAAS;AAAA,MACP;AAAA,MACAA;AAAA,MACA,MAAM,CAAC;AAAA,MACP;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;AASO,SAAS,YAAY,iBAAiB,MAAM;AACjD,QAAM,QAAQ,CAAC;AACf,MAAIA,UAAS;AACb,MAAI,eAAe;AACnB,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAElB,QAAI,CAAC,sBAAsB,iBAAiBA,SAAQ,KAAK,CAAC,GAAG;AAC3D,YAAM,KAAK,KAAK,MAAM,cAAc,IAAI,CAAC,CAAC;AAAA,IAC5C,OAAO;AACL,UAAI,MAAM,WAAW,GAAG;AACtB;AAAA,MACF;AACA,YAAM,MAAM,SAAS,CAAC,EAAE,KAAK,KAAK,YAAY,CAAC;AAAA,IACjD;AACA,mBAAe,IAAI;AACnB,IAAAA,UAAS;AAAA,EACX;AACA,SAAO;AACT;;;AC1LO,SAAS,wBACd,iBACAC,SACA,MACA,QACA,aACA,mBACA,MACA;AACA,MAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI;AAC1B,QAAM,IAAI,YAAY,oBAAoB,CAAC;AAE3C,QAAM,gBAAgB,CAAC;AAEvB,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,SAAK,gBAAgB,MAAM,MAAM;AACjC,SAAK,gBAAgB,MAAM,SAAS,CAAC;AACrC,SAAK,IAAIA,SAAQ,IAAI,KAAK,KAAK,QAAQ;AACrC,WAAK,gBAAgB,CAAC;AACtB,WAAK,gBAAgB,IAAI,CAAC;AAC1B,UAAK,KAAK,MAAM,MAAM,KAAO,MAAM,KAAK,KAAK,IAAK;AAChD,aAAM,IAAI,OAAO,KAAK,OAAQ,KAAK,MAAM;AACzC,sBAAc,KAAK,CAAC;AAAA,MACtB;AACA,WAAK;AACL,WAAK;AAAA,IACP;AAAA,EACF;AAGA,MAAI,SAAS;AACb,MAAI,mBAAmB;AACvB,gBAAc,KAAK,SAAS;AAC5B,OAAK,cAAc,CAAC;AACpB,OAAK,IAAI,GAAG,KAAK,cAAc,QAAQ,IAAI,IAAI,EAAE,GAAG;AAClD,SAAK,cAAc,CAAC;AACpB,UAAM,gBAAgB,KAAK,IAAI,KAAK,EAAE;AACtC,QAAI,gBAAgB,kBAAkB;AACpC,WAAK,KAAK,MAAM;AAChB,UAAI,sBAAsB,iBAAiBA,SAAQ,MAAM,QAAQ,GAAG,CAAC,GAAG;AACtE,iBAAS;AACT,2BAAmB;AAAA,MACrB;AAAA,IACF;AACA,SAAK;AAAA,EACP;AACA,MAAI,MAAM,MAAM,GAAG;AAGjB,aAAS,YAAY,iBAAiB;AAAA,EACxC;AACA,MAAI,MAAM;AACR,SAAK,KAAK,QAAQ,GAAG,gBAAgB;AACrC,WAAO;AAAA,EACT;AACA,SAAO,CAAC,QAAQ,GAAG,gBAAgB;AACrC;AAWO,SAAS,8BACd,iBACAA,SACA,OACA,QACA,aACA;AACA,MAAI,iBAAiB,CAAC;AACtB,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,qBAAiB;AAAA,MACf;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI;AAAA,MACJ;AAAA,IACF;AACA,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;;;ACnFA,IAAM,UAAN,MAAM,iBAAgB,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYnC,YAAYC,cAAa,QAAQ,MAAM;AACrC,UAAM;AAMN,SAAK,QAAQ,CAAC;AAMd,SAAK,6BAA6B;AAMlC,SAAK,qBAAqB;AAM1B,SAAK,YAAY;AAMjB,SAAK,oBAAoB;AAMzB,SAAK,oBAAoB;AAMzB,SAAK,2BAA2B;AAEhC,QAAI,WAAW,UAAa,MAAM;AAChC,WAAK;AAAA,QACH;AAAA;AAAA,QAC8BA;AAAA,MAChC;AACA,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,WAAK;AAAA;AAAA,QAEDA;AAAA,QAEF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiBC,aAAY;AAC3B,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkBA,YAAW,mBAAmB,EAAE,MAAM;AAAA,IAC/D,OAAO;AACL,aAAO,KAAK,iBAAiBA,YAAW,mBAAmB,CAAC;AAAA,IAC9D;AACA,SAAK,MAAM,KAAK,KAAK,gBAAgB,MAAM;AAC3C,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,UAAM,UAAU,IAAI;AAAA,MAClB,KAAK,gBAAgB,MAAM;AAAA,MAC3B,KAAK;AAAA,MACL,KAAK,MAAM,MAAM;AAAA,IACnB;AACA,YAAQ,gBAAgB,IAAI;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,QAAI,qBAAqB,yBAAyB,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG;AACzE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,qBAAqB,KAAK,YAAY,GAAG;AAChD,WAAK,YAAY,KAAK;AAAA,QACpB;AAAA,UACE,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,WAAK,oBAAoB,KAAK,YAAY;AAAA,IAC5C;AACA,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,GAAG,GAAG;AACf,WAAO;AAAA,MACL,KAAK,2BAA2B;AAAA,MAChC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO;AAAA,MACL,KAAK,2BAA2B;AAAA,MAChC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,eAAe,OAAO;AACpB,QAAI;AACJ,QAAI,UAAU,QAAW;AACvB,wBAAkB,KAAK,2BAA2B,EAAE,MAAM;AAC1D,wBAAkB,iBAAiB,GAAG,KAAK,OAAO,KAAK,QAAQ,KAAK;AAAA,IACtE,OAAO;AACL,wBAAkB,KAAK;AAAA,IACzB;AAEA,WAAO,wBAAwB,iBAAiB,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,QAAI,KAAK,8BAA8B,KAAK,YAAY,GAAG;AACzD,YAAM,aAAa,UAAU,KAAK,UAAU,CAAC;AAC7C,WAAK,qBAAqB;AAAA,QACxB,KAAK,2BAA2B;AAAA,QAChC;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AACA,WAAK,6BAA6B,KAAK,YAAY;AAAA,IACrD;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB;AACjB,WAAO,IAAI,cAAM,KAAK,qBAAqB,GAAG,KAAK;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,qBAAqB;AACnB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,cAAc,OAAO;AACnB,QAAI,QAAQ,KAAK,KAAK,MAAM,UAAU,OAAO;AAC3C,aAAO;AAAA,IACT;AACA,WAAO,IAAI;AAAA,MACT,KAAK,gBAAgB;AAAA,QACnB,UAAU,IAAI,IAAI,KAAK,MAAM,QAAQ,CAAC;AAAA,QACtC,KAAK,MAAM,KAAK;AAAA,MAClB;AAAA,MACA,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,UAAM,SAAS,KAAK;AACpB,UAAM,kBAAkB,KAAK;AAC7B,UAAM,OAAO,KAAK;AAClB,UAAMC,eAAc,CAAC;AACrB,QAAIC,UAAS;AACb,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,YAAM,MAAM,KAAK,CAAC;AAClB,YAAMF,cAAa,IAAI;AAAA,QACrB,gBAAgB,MAAME,SAAQ,GAAG;AAAA,QACjC;AAAA,MACF;AACA,MAAAD,aAAY,KAAKD,WAAU;AAC3B,MAAAE,UAAS;AAAA,IACX;AACA,WAAOD;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,6BAA6B;AAC3B,QAAI,KAAK,qBAAqB,KAAK,YAAY,GAAG;AAChD,YAAM,kBAAkB,KAAK;AAC7B,UAAI,uBAAuB,iBAAiB,GAAG,KAAK,OAAO,KAAK,MAAM,GAAG;AACvE,aAAK,2BAA2B;AAAA,MAClC,OAAO;AACL,aAAK,2BAA2B,gBAAgB,MAAM;AACtD,aAAK,yBAAyB,SAAS;AAAA,UACrC,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AACA,WAAK,oBAAoB,KAAK,YAAY;AAAA,IAC5C;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,8BAA8B,kBAAkB;AAC9C,UAAM,4BAA4B,CAAC;AACnC,UAAM,iBAAiB,CAAC;AACxB,8BAA0B,SAAS;AAAA,MACjC,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,KAAK,gBAAgB;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,IAAI,SAAQ,2BAA2B,MAAM,cAAc;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,QAAQ;AACvB,WAAO;AAAA,MACL,KAAK,2BAA2B;AAAA,MAChC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAeF,cAAa,QAAQ;AAClC,SAAK,UAAU,QAAQA,cAAa,CAAC;AACrC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AACA,UAAM,OAAO;AAAA,MACX,KAAK;AAAA,MACL;AAAA,MACAA;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,SAAK,gBAAgB,SAAS,KAAK,WAAW,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC1E,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,kBAAQ;AAkCR,SAAS,WAAW,QAAQ;AACjC,MAAI,QAAQ,MAAM,GAAG;AACnB,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC3D;AACA,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,kBAAkB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO,IAAI,QAAQ,iBAAiB,MAAM,CAAC,gBAAgB,MAAM,CAAC;AACpE;AAWO,SAAS,WAAW,QAAQ,OAAO,OAAO;AAC/C,UAAQ,QAAQ,QAAQ;AACxB,QAAM,SAAS,OAAO,UAAU;AAChC,QAAM,SAAS,OAAO,UAAU;AAChC,QAAM,SAAS,OAAO,UAAU;AAChC,QAAM,cAAc,UAAU,QAAQ;AACtC,QAAM,kBAAkB,IAAI,MAAM,WAAW;AAC7C,WAAS,IAAI,GAAG,IAAI,aAAa,KAAK,QAAQ;AAC5C,oBAAgB,CAAC,IAAI;AACrB,oBAAgB,IAAI,CAAC,IAAI;AACzB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,sBAAgB,IAAI,CAAC,IAAI,OAAO,CAAC;AAAA,IACnC;AAAA,EACF;AACA,QAAM,OAAO,CAAC,gBAAgB,MAAM;AACpC,QAAM,UAAU,IAAI,QAAQ,iBAAiB,QAAQ,IAAI;AACzD,cAAY,SAAS,QAAQ,OAAO,UAAU,GAAG,KAAK;AACtD,SAAO;AACT;AAUO,SAAS,YAAY,SAAS,QAAQ,QAAQ,OAAO;AAC1D,QAAM,kBAAkB,QAAQ,mBAAmB;AACnD,QAAM,SAAS,QAAQ,UAAU;AACjC,QAAM,QAAQ,gBAAgB,SAAS,SAAS;AAChD,QAAM,aAAa,QAAQ,QAAQ;AACnC,WAAS,IAAI,GAAG,KAAK,OAAO,EAAE,GAAG;AAC/B,UAAMI,UAAS,IAAI;AACnB,UAAMC,SAAQ,aAAc,OAAO,GAAG,KAAK,IAAI,IAAI,KAAK,KAAM;AAC9D,oBAAgBD,OAAM,IAAI,OAAO,CAAC,IAAI,SAAS,KAAK,IAAIC,MAAK;AAC7D,oBAAgBD,UAAS,CAAC,IAAI,OAAO,CAAC,IAAI,SAAS,KAAK,IAAIC,MAAK;AAAA,EACnE;AACA,UAAQ,QAAQ;AAClB;", "names": ["offset", "rotate", "scale", "translate", "extent", "scale", "coordinates", "rotate", "scale", "translate", "offset", "coordinates", "coordinates", "squaredDistance", "offset", "squaredDistance", "offset", "squaredDistance", "offset", "offset", "coordinates", "offset", "coordinates", "offset", "offset", "offset", "coordinates", "linearRing", "linearRings", "offset", "offset", "angle"]}