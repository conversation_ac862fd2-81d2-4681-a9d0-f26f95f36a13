<!--
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-05-27 09:49:35
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-06-05 16:52:17
 * @FilePath: \uavflight-ui\src\views\manage\verify\form.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
	<el-dialog
		destroy-on-close
		:title="operateValue === 1 ? '查看' : '审核'"
		width="80%"
		:close-on-click-modal="false"
		v-model="visable"
		@close="closeDialog"
	>
		<div class="audit-detail-container">
			<div class="audit-event">
				<div class="event-detail-section" style="flex: 1">
					<h3>事件详情</h3>
					<div class="info-item" style="margin-top: 20px">
						<span>业务场景</span>
						<span v-if="auditData.businessTypeName">{{ auditData.businessTypeName }}</span>
						<span v-else>-</span>
					</div>
					<div class="info-item">
						<span>事件</span>
						<span v-if="auditData.businessEventName">{{ auditData.businessEventName }}</span>
						<span v-else>-</span>
					</div>
					<div class="info-item">
						<span>发生时间</span>
						<span v-if="auditData.time">{{ auditData.time }}</span>
						<span v-else>-</span>
					</div>
					<div class="info-item">
						<span>街道</span>
						<span v-if="auditData.cityName">{{ auditData.cityName }}</span>
						<span v-else>-</span>
					</div>
					<div class="info-item">
						<span>航线</span>
						<span v-if="auditData.airLineName">{{ auditData.airLineName }}</span>
						<span v-else>-</span>
					</div>
					<div class="info-item">
						<span>飞行作业起止时间</span>
						<span v-if="auditData.airLineStartTime && auditData.airLineEndTime"
							>{{ auditData.airLineStartTime }} 至 {{ auditData.airLineEndTime.split(' ')[1] }}</span
						>
						<span v-else>-</span>
					</div>
					<!-- <div class="info-item">
						<span>网格</span>
						<span v-if="auditData.grid">{{ auditData.grid }}</span>
						<span v-else>-</span>
					</div> -->
					<div class="info-item">
						<span>状态</span>
						<el-tag :type="getTagType(auditData.status, auditData.auditResult)">
							<span v-if="auditData.status === '0'">待审核</span>
							<span v-else-if="auditData.status === '1' && auditData.auditResult === '1'">属实</span>
							<span v-else-if="auditData.status === '1' && auditData.auditResult === '2'">不属实</span>
						</el-tag>
					</div>
					<!-- <div class="info-item">
						<span>检测类型</span>
						<span v-if="auditData.detectionType">{{ auditData.detectionType }}</span>
						<span v-else>-</span>
					</div>
					<div class="info-item">
						<span>是否为效果图事件</span>
						<span>{{ auditData.isEffectDiagram ? '是' : '否' }}</span>
					</div>
					<div class="info-item">
						<span>描述</span>
						<span v-if="auditData.description">{{ auditData.description }}</span>
						<span v-else>-</span>
					</div> -->
				</div>
				<div class="event-location" style="flex: 1">
					<h3>事件位置</h3>
					<div style="width: 100%; height: 300px" ref="mapContainer">
						<!-- OpenLayers 地图容器 -->
					</div>
				</div>
			</div>
			<div class="event-video">
				<div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px">
					<h3>事件图像视频</h3>
					<div class="media-buttons">
						<el-button :type="viewValue === 1 ? 'success' : 'default'" @click="handleClick(1)">标记图像</el-button>
						<el-button :type="viewValue === 2 ? 'success' : 'default'" @click="handleClick(2)">预警视频</el-button>
						<el-button :type="viewValue === 3 ? 'success' : 'default'" @click="handleClick(3)">原始视频</el-button>
					</div>
				</div>
				<div style="display: flex; justify-content: center; background-color: #000000">
					<div style="width: 80%">
						<div v-if="viewValue === 1">
							<img :src="auditData.picturePath" style="width: 100%; height: 100%; object-fit: contain" />
						</div>
						<div v-else-if="viewValue === 2">
							<VideoPlayer ref="orgPlayerRef" :options="orgVideoOptions" :markers="orgMarkers" />
						</div>
						<div v-else>
							<div id="aaa"></div>
							<VideoPlayer ref="proPlayerRef" :options="proVideoOptions" :markers="orgMarkers" />
						</div>
					</div>
				</div>
			</div>

			<div class="audit-section">
				<h3>审核</h3>
				<div class="form-item">
					<span>是否属实:</span>
					<el-radio-group v-model="auditData.auditResult" :disabled="operateValue === 1">
						<el-radio label="属实" value="1">属实</el-radio>
						<el-radio label="不属实" value="2">不属实</el-radio>
					</el-radio-group>
				</div>
				<div class="form-item">
					<span>审核补充说明:</span>
					<el-input
						:disabled="operateValue === 1"
						type="textarea"
						:rows="5"
						show-word-limit
						maxlength="200"
						v-model="auditData.auditRecord"
						placeholder="请输入审核补充说明"
					></el-input>
				</div>
				<div class="form-item">
					<span>附件:</span>
					<upload :disabledUpload="operateValue === 1" @update:modelValue="uploadSuccess" :modelValue="auditData.filePaths" />
				</div>
			</div>
		</div>
		<template #footer v-if="operateValue === 2">
			<span class="dialog-footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="handleSaveAudit"> 确 定 </el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
// Vue 核心功能导入
import { ref, defineExpose, onMounted, nextTick, defineAsyncComponent } from 'vue';
// Element Plus 图标
import { Plus } from '@element-plus/icons-vue';
// 视频播放器组件及其类型
import { VideoPlayer } from '/@/components/VideoPlayer';
import type { VideoPlayerOptions } from '/@/components/VideoPlayer';
// API 和工具函数
import { audit } from '/@/api/manage/verify';
import { useMessage } from '/@/hooks/message';
import { getMapServerNginxUrl, replaceIpInConfig } from '/@/utils/mapUrlReplacer';
// OpenLayers 地图相关导入
import { Map, View } from 'ol';
import TileLayer from 'ol/layer/Tile';
import { TileWMS } from 'ol/source';
import { fromLonLat } from 'ol/proj';
import 'ol/ol.css';
import Feature from 'ol/Feature';
import Point from 'ol/geom/Point';
import { Vector as VectorLayer } from 'ol/layer';
import { Vector as VectorSource } from 'ol/source';
import { Style, Text, Icon, Fill, Stroke } from 'ol/style';
import WMTS from 'ol/source/WMTS';
import WMTSTileGrid from 'ol/tilegrid/WMTS';
import { get as getProjection } from 'ol/proj';
import { getTopLeft, getWidth } from 'ol/extent';
import currentLocationIcon from '/@/assets/icon/current_location.png'; // 导入图标

// 异步导入上传组件
const Upload = defineAsyncComponent(() => import('/@/components/Upload/uavUploadFile.vue'));

// 视频播放器引用
const orgPlayerRef = ref<InstanceType<typeof VideoPlayer> | null>(null); // 原始视频播放器
const proPlayerRef = ref<InstanceType<typeof VideoPlayer> | null>(null); // 处理后视频播放器

// 视频播放器基础配置
const orgVideoOptions: VideoPlayerOptions = {
	controls: true, // 显示播放控件
	poster: '', // 视频封面
	sources: [], // 视频源
};

const proVideoOptions: VideoPlayerOptions = {
	controls: true, // 显示播放控件
	poster: '', // 视频封面
	sources: [], // 视频源
};

// 视频标记点数组
const orgMarkers = ref<any[]>([]);

// 定义事件发射器
const emit = defineEmits(['refresh']);

// 组件属性定义
const props = defineProps({
	eventDetail: {
		type: Object,
		default: () => ({}),
	},
});

// 视图状态变量
const viewValue = ref(1); // 当前查看的内容类型（1:图片, 2:预警视频, 3:原始视频）
const visable = ref(false); // 对话框显示状态
const operateValue = ref(1); // 操作类型（1:查看, 2:审核）
const auditData = ref<any>({}); // 审核数据
const localEventDetail = ref(props.eventDetail); // 本地事件详情

// OpenLayers 地图相关
const mapContainer = ref(null); // 地图容器引用
let map: Map | null = null; // 地图实例
const mapConfig = ref<any>(null); // 地图配置

/**
 * 加载地图配置
 */
const loadMapConfig = async () => {
	try {
		const mapServerUrl = getMapServerNginxUrl();
		const configUrl = `${mapServerUrl}/olMap.json`;
		console.log('正在加载OpenLayers地图配置，URL:', configUrl);
		
		const response = await fetch(configUrl);
		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}
		
		// 解析配置
		const data = await response.json();
		
		// 替换配置中的IP地址
		mapConfig.value = replaceIpInConfig(data, import.meta.env.VITE_MAP_SERVER_IP);
		console.log('OpenLayers地图配置加载成功:', mapConfig.value);
		
		return mapConfig.value;
	} catch (err: any) {
		console.error('加载OpenLayers地图配置失败:', err);
		return null;
	}
};

/**
 * 创建WMS图层
 * @param layerConfig 图层配置
 * @returns OpenLayers WMS图层
 */
const createWmsLayer = (layerConfig: any) => {
	// 解析URL中的参数
	const url = new URL(layerConfig.url);
	const baseUrl = url.origin + url.pathname;
	const params: any = {};
	
	// 提取URL中的查询参数
	url.searchParams.forEach((value, key) => {
		params[key] = value;
	});
	
	// 确保必要的WMS参数存在
	if (!params.LAYERS && params.layers) {
		params.LAYERS = params.layers;
		delete params.layers;
	}
	
	// 删除OpenLayers自动添加的参数
	delete params.REQUEST;
	delete params.request;
	delete params.WIDTH;
	delete params.width;
	delete params.HEIGHT;
	delete params.height;
	delete params.BBOX;
	delete params.bbox;
	delete params.FORMAT;
	delete params.format;
	
	console.log('WMS图层基础URL:', baseUrl);
	console.log('WMS图层参数:', params);
	
	// 解码URL编码的参数
	if (params.LAYERS) {
		params.LAYERS = decodeURIComponent(params.LAYERS);
	}
	
	return new TileLayer({
		source: new TileWMS({
			url: baseUrl,
			params: {
				'LAYERS': params.LAYERS || layerConfig.layers || '',
				'FORMAT': 'image/png',
				'TRANSPARENT': true,
				'VERSION': params.VERSION || params.version || '1.1.1',
				...params
			},
			serverType: 'geoserver',
			crossOrigin: 'anonymous'
		}),
		visible: true,
		opacity: 1,
	});
};

/**
 * 创建WMTS图层
 * @param layerConfig 图层配置
 * @returns OpenLayers WMTS图层
 */
const createWmtsLayer = (layerConfig: any) => {
	try {
		// 解析URL中的参数
		const url = new URL(layerConfig.url);
		
		// 从URL路径中提取图层名称
		const pathParts = url.pathname.split('/');
		const demoIndex = pathParts.indexOf('demo');
		let layerName = '';
		
		if (demoIndex !== -1 && demoIndex < pathParts.length - 1) {
			layerName = pathParts[demoIndex + 1].split('?')[0];
		} else if (url.pathname.includes('demo/')) {
			layerName = url.pathname.split('demo/')[1].split('?')[0];
		}
		
		console.log('提取的图层名称:', layerName);
		
		// 如果图层名称包含冒号，说明是命名空间:图层格式
		if (layerName.includes(':')) {
			const [workspace, layer] = layerName.split(':');
			console.log('工作空间:', workspace, '图层:', layer);
		}
		
		// 尝试直接使用WMS方式加载WMTS图层
		console.log('尝试使用WMS方式加载WMTS图层');
		
		// 构建WMS URL
		const wmsUrl = `${url.origin}/geoserver/${layerName.split(':')[0]}/wms`;
		console.log('WMS URL:', wmsUrl);
		
		return new TileLayer({
			source: new TileWMS({
				url: wmsUrl,
				params: {
					'LAYERS': layerName,
					'FORMAT': 'image/png',
					'TRANSPARENT': true,
					'VERSION': '1.1.1',
					'SRS': 'EPSG:4326'
				},
				serverType: 'geoserver',
				crossOrigin: 'anonymous'
			}),
			visible: true,
			opacity: 1,
		});
	} catch (error) {
		console.error('创建WMTS图层时出错:', error);
		return null;
	}
};

/**
 * 初始化地图
 * 使用 OpenLayers 创建地图实例并设置中心点
 */
const initMap = async () => {
	if (mapContainer.value && auditData.value) {
		// 获取经纬度，如果没有则使用默认值
		const longitude = auditData.value.longitude || 104.195397;
		const latitude = auditData.value.latitude || 22.627899;
		console.log('事件位置经度:', longitude);
		console.log('事件位置纬度:', latitude);

		// 如果地图已存在，清除它
		if (map) {
			map.setTarget(undefined);
			map = null;
		}

		// 加载地图配置
		if (!mapConfig.value) {
			await loadMapConfig();
		}

		// 创建标记点
		const point = new Feature({
			geometry: new Point(fromLonLat([longitude, latitude])),
		});

		// 设置标记点样式
		point.setStyle(
			new Style({
				image: new Icon({
					anchor: [0.5, 1],
					anchorXUnits: 'fraction',
					anchorYUnits: 'fraction',
					src: currentLocationIcon,
					scale: 0.5,
					opacity: 1,
				}),
				text: new Text({
					text: '事件位置',
					offsetY: 15,
					font: 'bold 14px Arial',
					fill: new Fill({
						color: '#ff0000',
					}),
					stroke: new Stroke({
						color: '#fff',
						width: 3,
					}),
				}),
			})
		);

		// 创建矢量图层
		const vectorLayer = new VectorLayer({
			source: new VectorSource({
				features: [point],
			}),
		});

		// 从配置中创建图层
		const mapLayers = [];
		
		// 添加从配置加载的图层
		if (mapConfig.value && mapConfig.value.layers) {
			console.log('开始加载配置的图层，总数:', mapConfig.value.layers.length);
			
			mapConfig.value.layers.forEach((layerConfig: any) => {
				console.log('处理图层:', layerConfig.id, layerConfig.name, layerConfig.protocol);
				
				if (layerConfig.type === 'raster') {
					let layer;
					if (layerConfig.protocol === 'WMS') {
						layer = createWmsLayer(layerConfig);
					} else if (layerConfig.protocol === 'WMTS') {
						layer = createWmtsLayer(layerConfig);
					}
					
					if (layer) {
						console.log('成功创建图层:', layerConfig.name);
						mapLayers.push(layer);
					} else {
						console.error('创建图层失败:', layerConfig.name);
					}
				}
			});
		}
		
		console.log('创建的图层数量:', mapLayers.length);
		
		
		
		// 添加标记点图层
		mapLayers.push(vectorLayer);

		// 创建新的地图实例
		map = new Map({
			target: mapContainer.value,
			layers: mapLayers,
			view: new View({
				center: fromLonLat([longitude, latitude]),
				zoom: 15,
				maxZoom: 18,
			}),
		});
		
		// 添加地图加载完成事件监听
		map.once('rendercomplete', () => {
			console.log('地图渲染完成');
		});
		
		// 添加错误处理
		map.getLayers().forEach((layer) => {
			if (layer instanceof TileLayer) {
				const source = layer.getSource();
				source.on('tileloaderror', (event: any) => {
					console.error('瓦片加载错误:', event);
				});
				
				// 添加瓦片加载成功事件监听
				source.on('tileloadend', (event: any) => {
					console.log('瓦片加载成功:', event);
				});
			}
		});
	}
};

// 组件挂载后初始化地图
onMounted(() => {
	nextTick(() => {
		initMap();
	});
});

/**
 * 获取状态标签的类型
 * @param status 状态值
 * @param auditResult 审核结果
 * @returns 标签类型
 */
const getTagType = (status: string, auditResult: string) => {
	if (status === '0') return 'danger'; // 待审核
	if (status === '1' && auditResult === '1') return 'success'; // 已审核且属实
	return 'default'; // 其他情况
};

/**
 * 关闭对话框
 * 清理地图实例和表单数据
 */
const closeDialog = () => {
	if (map) {
		map.setTarget(undefined);
		map = null;
	}
	clearFormAndData();
	visable.value = false;
};

/**
 * 上传成功回调
 * @param val 上传的文件路径
 */
const uploadSuccess = (val: any) => {
	auditData.value.filePaths = val;
};

/**
 * 清空表单和相关数据
 */
const clearFormAndData = () => {
	viewValue.value = 1;
	orgMarkers.value = [];
};

/**
 * 保存审核结果
 */
const handleSaveAudit = async () => {
	if (!auditData.value.auditResult) {
		useMessage().wraning('请选择是否属实');
		return;
	}
	if (!auditData.value.auditRecord || auditData.value.auditRecord.trim() === '') {
		useMessage().wraning('请先填写补充说明');
		return;
	}
	const res = await audit(auditData.value);
	visable.value = false;
	if (res.data === 1) {
		useMessage().success('成功');
	} else {
		useMessage().success(res.data);
	}
	emit('refresh');
};

/**
 * 生成视频封面
 * @param time 视频时间点
 */
const proVideoPoster = (time: number) => {
	const player = proPlayerRef.value?.player;
	if (!player) return;

	player.currentTime(time);

	const canvas = document.createElement('canvas');
	const video = player.el().querySelector('video');

	if (!video) return;

	canvas.width = video.videoWidth;
	canvas.height = video.videoHeight;
	const ctx = canvas.getContext('2d');

	if (ctx) {
		ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
		proVideoOptions.poster = canvas.toDataURL('image/jpg');
	}
};

/**
 * 加载视频数据
 * @param data 包含视频路径的数据对象
 */
const loadVideo = (data: any) => {
	orgVideoOptions.poster = data.picturePath;
	orgVideoOptions.sources = [
		{
			src: data.videoFilePath,
			type: 'video/mp4' as const,
		},
	];

	proVideoOptions.sources = [
		{
			src: data.processedVideoPath,
			type: 'video/mp4' as const,
		},
	];
};

// 暴露组件方法
defineExpose({
	/**
	 * 显示对话框
	 * @param data 审核数据
	 * @param operate 操作类型
	 */
	showDialog: (data: any, operate: string) => {
		visable.value = true;
		auditData.value = data;
		operateValue.value = Number(operate);
		loadVideo(data);

		nextTick(() => {
			initMap();
		});
	},
	closeDialog: () => {
		clearFormAndData();
		visable.value = false;
	},
});

/**
 * 处理视图切换
 * @param val 视图类型（1:图片, 2:预警视频, 3:原始视频）
 */
const handleClick = (val: number) => {
	viewValue.value = val;
	const ms = Math.floor((parseInt(auditData.value.frame || '0') - 1) / 30);

	const marker = {
		time: ms,
		text: '监测位置',
		class: 'red',
	};
	orgMarkers.value.push(marker);

	setTimeout(() => {
		if (val === 2) {
			const player = orgPlayerRef.value?.player;
			if (player) player.currentTime(ms);
		} else if (val === 3) {
			const player = proPlayerRef.value?.player;
			if (player) player.currentTime(ms);
		}
	}, 500);
};
</script>

<style scoped>
/* .audit-detail-container {
	width: 80%;
	max-height: 80%; 
	padding: 20px;
	border: 1px solid #ccc;
	background-color: #fff;
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 1000;
	overflow-y: auto;
} */

.audit-event {
	display: flex;
	flex-wrap: nowrap;
	overflow-y: auto;
}

.audit-title {
	margin-bottom: 10px;
	font-size: 18px;
	border-bottom: 1px solid #eee;
}

.close-button {
	background: none;
	border: none;
	cursor: pointer;
	margin-bottom: 10px;
}

.event-detail-section {
	width: 35%;
	padding-right: 10px;
	/* border: 1px solid #4405f1; */
}
.event-media-section {
	width: 65%;
	/* border: 1px solid #f50707; */
}

.info-item {
	margin-bottom: 18px;
	display: flex;
	justify-content: space-between;
}

.event-location {
	margin-left: 15px;
	height: 300px;
	/* border: 1px solid #e0e0e0; */
	border-radius: 4px;
	overflow: hidden;
}

.audit-section {
	margin-top: 20px;
}

.form-item {
	margin-bottom: 15px;
	display: flex;
	align-items: start; /* 让表单元素垂直顶部对齐 */
}

.form-item span {
	min-width: 120px; /* 让表单标签有固定最小宽度 */
}

.audit-detail-container h3 {
	margin-bottom: 10px;
	position: relative;
	padding-left: 15px;
	font-size: 15px;
}

.audit-detail-container h3::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 5px;
	height: 15px;
	background-color: #e74c3c; /* 标题前竖线颜色 */
}

.audit-detail-container h3 {
	margin-bottom: 10px;
	position: relative;
	padding-left: 15px;
	font-size: 15px;
}

.event-detail-section h3::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 5px;
	height: 15px;
	background-color: #e74c3c; /* 标题前竖线颜色 */
}

.event-media-section h3 {
	position: relative;
	padding-left: 15px;
	font-size: 15px;
}

.event-media-section h3::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 5px;
	height: 15px;
	background-color: #e74c3c; /* 标题前竖线颜色 */
}

.event-image-video {
	display: flex;
	flex-direction: column;
}

.image-video-buttons {
	margin-bottom: 10px;
	text-align: right;
}

.image-video-display {
	width: 100%;
	height: 400px; /* 根据需要调整高度 */
	overflow: hidden;
}

.button-group {
	text-align: right;
	margin-top: 20px;
}

.el-upload {
	width: 100%; /* 让附件上传区域占满表单行剩余空间 */
}
.media-buttons {
	display: flex;
	align-items: center;
	font-size: 14px;
	line-height: 22px;
	color: #00000080;
	border-radius: 4px;
}

.event-video {
	margin-top: 20px;
}

/* OpenLayers 相关样式 */
:deep(.ol-control) {
	background-color: rgba(255, 255, 255, 0.4);
	border-radius: 4px;
	padding: 2px;
}

:deep(.ol-zoom) {
	top: 0.5em;
	left: 0.5em;
}

:deep(.ol-zoom-in),
:deep(.ol-zoom-out) {
	background-color: rgba(0, 60, 136, 0.5);
	color: white;
}

:deep(.ol-zoom-in:hover),
:deep(.ol-zoom-out:hover) {
	background-color: rgba(0, 60, 136, 0.7);
}
</style>
