{"version": 3, "sources": ["../../ol/ImageState.js", "../../ol/ImageBase.js", "../../ol/Image.js"], "sourcesContent": ["/**\n * @module ol/ImageState\n */\n\n/**\n * @enum {number}\n */\nexport default {\n  IDLE: 0,\n  LOADING: 1,\n  LOADED: 2,\n  ERROR: 3,\n  EMPTY: 4,\n};\n", "/**\n * @module ol/ImageBase\n */\nimport EventTarget from './events/Target.js';\nimport EventType from './events/EventType.js';\nimport {abstract} from './util.js';\n\n/**\n * @abstract\n */\nclass ImageBase extends EventTarget {\n  /**\n   * @param {import(\"./extent.js\").Extent} extent Extent.\n   * @param {number|undefined} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"./ImageState.js\").default} state State.\n   */\n  constructor(extent, resolution, pixelRatio, state) {\n    super();\n\n    /**\n     * @protected\n     * @type {import(\"./extent.js\").Extent}\n     */\n    this.extent = extent;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.pixelRatio_ = pixelRatio;\n\n    /**\n     * @protected\n     * @type {number|undefined}\n     */\n    this.resolution = resolution;\n\n    /**\n     * @protected\n     * @type {import(\"./ImageState.js\").default}\n     */\n    this.state = state;\n  }\n\n  /**\n   * @protected\n   */\n  changed() {\n    this.dispatchEvent(EventType.CHANGE);\n  }\n\n  /**\n   * @return {import(\"./extent.js\").Extent} Extent.\n   */\n  getExtent() {\n    return this.extent;\n  }\n\n  /**\n   * @abstract\n   * @return {HTMLCanvasElement|HTMLImageElement|HTMLVideoElement} Image.\n   */\n  getImage() {\n    return abstract();\n  }\n\n  /**\n   * @return {number} PixelRatio.\n   */\n  getPixelRatio() {\n    return this.pixelRatio_;\n  }\n\n  /**\n   * @return {number} Resolution.\n   */\n  getResolution() {\n    return /** @type {number} */ (this.resolution);\n  }\n\n  /**\n   * @return {import(\"./ImageState.js\").default} State.\n   */\n  getState() {\n    return this.state;\n  }\n\n  /**\n   * Load not yet loaded URI.\n   * @abstract\n   */\n  load() {\n    abstract();\n  }\n}\n\nexport default ImageBase;\n", "/**\n * @module ol/Image\n */\nimport EventType from './events/EventType.js';\nimport ImageBase from './ImageBase.js';\nimport ImageState from './ImageState.js';\nimport {IMAGE_DECODE} from './has.js';\nimport {getHeight} from './extent.js';\nimport {listenOnce, unlistenBy<PERSON>ey} from './events.js';\n\n/**\n * A function that takes an {@link module:ol/Image~ImageWrapper} for the image and a\n * `{string}` for the src as arguments. It is supposed to make it so the\n * underlying image {@link module:ol/Image~ImageWrapper#getImage} is assigned the\n * content specified by the src. If not specified, the default is\n *\n *     function(image, src) {\n *       image.getImage().src = src;\n *     }\n *\n * Providing a custom `imageLoadFunction` can be useful to load images with\n * post requests or - in general - through XHR requests, where the src of the\n * image element would be set to a data URI when the content is loaded.\n *\n * @typedef {function(ImageWrapper, string): void} LoadFunction\n * @api\n */\n\nclass ImageWrapper extends ImageBase {\n  /**\n   * @param {import(\"./extent.js\").Extent} extent Extent.\n   * @param {number|undefined} resolution Resolution.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {string} src Image source URI.\n   * @param {?string} crossOrigin Cross origin.\n   * @param {LoadFunction} imageLoadFunction Image load function.\n   * @param {CanvasRenderingContext2D} [context] Canvas context. When provided, the image will be\n   *    drawn into the context's canvas, and `getImage()` will return the canvas once the image\n   *    has finished loading.\n   */\n  constructor(\n    extent,\n    resolution,\n    pixelRatio,\n    src,\n    crossOrigin,\n    imageLoadFunction,\n    context\n  ) {\n    super(extent, resolution, pixelRatio, ImageState.IDLE);\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.src_ = src;\n\n    /**\n     * @private\n     * @type {HTMLCanvasElement|HTMLImageElement|HTMLVideoElement}\n     */\n    this.image_ = new Image();\n    if (crossOrigin !== null) {\n      this.image_.crossOrigin = crossOrigin;\n    }\n\n    /**\n     * @private\n     * @type {CanvasRenderingContext2D}\n     */\n    this.context_ = context;\n\n    /**\n     * @private\n     * @type {?function():void}\n     */\n    this.unlisten_ = null;\n\n    /**\n     * @protected\n     * @type {import(\"./ImageState.js\").default}\n     */\n    this.state = ImageState.IDLE;\n\n    /**\n     * @private\n     * @type {LoadFunction}\n     */\n    this.imageLoadFunction_ = imageLoadFunction;\n  }\n\n  /**\n   * @return {HTMLCanvasElement|HTMLImageElement|HTMLVideoElement} Image.\n   * @api\n   */\n  getImage() {\n    if (\n      this.state == ImageState.LOADED &&\n      this.context_ &&\n      !(this.image_ instanceof HTMLCanvasElement)\n    ) {\n      const canvas = this.context_.canvas;\n      canvas.width = this.image_.width;\n      canvas.height = this.image_.height;\n      this.context_.drawImage(this.image_, 0, 0);\n      this.image_ = this.context_.canvas;\n    }\n    return this.image_;\n  }\n\n  /**\n   * Tracks loading or read errors.\n   *\n   * @private\n   */\n  handleImageError_() {\n    this.state = ImageState.ERROR;\n    this.unlistenImage_();\n    this.changed();\n  }\n\n  /**\n   * Tracks successful image load.\n   *\n   * @private\n   */\n  handleImageLoad_() {\n    if (this.resolution === undefined) {\n      this.resolution = getHeight(this.extent) / this.image_.height;\n    }\n    this.state = ImageState.LOADED;\n    this.unlistenImage_();\n    this.changed();\n  }\n\n  /**\n   * Load the image or retry if loading previously failed.\n   * Loading is taken care of by the tile queue, and calling this method is\n   * only needed for preloading or for reloading in case of an error.\n   * @api\n   */\n  load() {\n    if (this.state == ImageState.IDLE || this.state == ImageState.ERROR) {\n      this.state = ImageState.LOADING;\n      this.changed();\n      this.imageLoadFunction_(this, this.src_);\n      this.unlisten_ = listenImage(\n        this.image_,\n        this.handleImageLoad_.bind(this),\n        this.handleImageError_.bind(this)\n      );\n    }\n  }\n\n  /**\n   * @param {HTMLCanvasElement|HTMLImageElement|HTMLVideoElement} image Image.\n   */\n  setImage(image) {\n    this.image_ = image;\n    this.resolution = getHeight(this.extent) / this.image_.height;\n  }\n\n  /**\n   * Discards event handlers which listen for load completion or errors.\n   *\n   * @private\n   */\n  unlistenImage_() {\n    if (this.unlisten_) {\n      this.unlisten_();\n      this.unlisten_ = null;\n    }\n  }\n}\n\n/**\n * @param {HTMLCanvasElement|HTMLImageElement|HTMLVideoElement} image Image element.\n * @param {function():any} loadHandler Load callback function.\n * @param {function():any} errorHandler Error callback function.\n * @return {function():void} Callback to stop listening.\n */\nexport function listenImage(image, loadHandler, errorHandler) {\n  const img = /** @type {HTMLImageElement} */ (image);\n  let listening = true;\n  let decoding = false;\n  let loaded = false;\n\n  const listenerKeys = [\n    listenOnce(img, EventType.LOAD, function () {\n      loaded = true;\n      if (!decoding) {\n        loadHandler();\n      }\n    }),\n  ];\n\n  if (img.src && IMAGE_DECODE) {\n    decoding = true;\n    img\n      .decode()\n      .then(function () {\n        if (listening) {\n          loadHandler();\n        }\n      })\n      .catch(function (error) {\n        if (listening) {\n          if (loaded) {\n            loadHandler();\n          } else {\n            errorHandler();\n          }\n        }\n      });\n  } else {\n    listenerKeys.push(listenOnce(img, EventType.ERROR, errorHandler));\n  }\n\n  return function unlisten() {\n    listening = false;\n    listenerKeys.forEach(unlistenByKey);\n  };\n}\n\nexport default ImageWrapper;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAOA,IAAO,qBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AACT;;;ACHA,IAAM,YAAN,cAAwB,eAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlC,YAAY,QAAQ,YAAY,YAAY,OAAO;AACjD,UAAM;AAMN,SAAK,SAAS;AAMd,SAAK,cAAc;AAMnB,SAAK,aAAa;AAMlB,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,cAAc,kBAAU,MAAM;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd;AAAA;AAAA,MAA8B,KAAK;AAAA;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACL,aAAS;AAAA,EACX;AACF;AAEA,IAAO,oBAAQ;;;ACrEf,IAAM,eAAN,cAA2B,kBAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYnC,YACE,QACA,YACA,YACA,KACA,aACA,mBACA,SACA;AACA,UAAM,QAAQ,YAAY,YAAY,mBAAW,IAAI;AAMrD,SAAK,OAAO;AAMZ,SAAK,SAAS,IAAI,MAAM;AACxB,QAAI,gBAAgB,MAAM;AACxB,WAAK,OAAO,cAAc;AAAA,IAC5B;AAMA,SAAK,WAAW;AAMhB,SAAK,YAAY;AAMjB,SAAK,QAAQ,mBAAW;AAMxB,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,QACE,KAAK,SAAS,mBAAW,UACzB,KAAK,YACL,EAAE,KAAK,kBAAkB,oBACzB;AACA,YAAM,SAAS,KAAK,SAAS;AAC7B,aAAO,QAAQ,KAAK,OAAO;AAC3B,aAAO,SAAS,KAAK,OAAO;AAC5B,WAAK,SAAS,UAAU,KAAK,QAAQ,GAAG,CAAC;AACzC,WAAK,SAAS,KAAK,SAAS;AAAA,IAC9B;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB;AAClB,SAAK,QAAQ,mBAAW;AACxB,SAAK,eAAe;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB;AACjB,QAAI,KAAK,eAAe,QAAW;AACjC,WAAK,aAAa,UAAU,KAAK,MAAM,IAAI,KAAK,OAAO;AAAA,IACzD;AACA,SAAK,QAAQ,mBAAW;AACxB,SAAK,eAAe;AACpB,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO;AACL,QAAI,KAAK,SAAS,mBAAW,QAAQ,KAAK,SAAS,mBAAW,OAAO;AACnE,WAAK,QAAQ,mBAAW;AACxB,WAAK,QAAQ;AACb,WAAK,mBAAmB,MAAM,KAAK,IAAI;AACvC,WAAK,YAAY;AAAA,QACf,KAAK;AAAA,QACL,KAAK,iBAAiB,KAAK,IAAI;AAAA,QAC/B,KAAK,kBAAkB,KAAK,IAAI;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,OAAO;AACd,SAAK,SAAS;AACd,SAAK,aAAa,UAAU,KAAK,MAAM,IAAI,KAAK,OAAO;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU;AACf,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AACF;AAQO,SAAS,YAAY,OAAO,aAAa,cAAc;AAC5D,QAAM;AAAA;AAAA,IAAuC;AAAA;AAC7C,MAAI,YAAY;AAChB,MAAI,WAAW;AACf,MAAI,SAAS;AAEb,QAAM,eAAe;AAAA,IACnB,WAAW,KAAK,kBAAU,MAAM,WAAY;AAC1C,eAAS;AACT,UAAI,CAAC,UAAU;AACb,oBAAY;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,IAAI,OAAO,cAAc;AAC3B,eAAW;AACX,QACG,OAAO,EACP,KAAK,WAAY;AAChB,UAAI,WAAW;AACb,oBAAY;AAAA,MACd;AAAA,IACF,CAAC,EACA,MAAM,SAAU,OAAO;AACtB,UAAI,WAAW;AACb,YAAI,QAAQ;AACV,sBAAY;AAAA,QACd,OAAO;AACL,uBAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACL,OAAO;AACL,iBAAa,KAAK,WAAW,KAAK,kBAAU,OAAO,YAAY,CAAC;AAAA,EAClE;AAEA,SAAO,SAAS,WAAW;AACzB,gBAAY;AACZ,iBAAa,QAAQ,aAAa;AAAA,EACpC;AACF;AAEA,IAAO,gBAAQ;", "names": []}