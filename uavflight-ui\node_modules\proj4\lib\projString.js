import { D2R } from './constants/values';
import PrimeMeridian from './constants/PrimeMeridian';
import units from './constants/units';
import match from './match';

/**
 * @param {string} defData
 * @returns {import('./defs').ProjectionDefinition}
 */
export default function (defData) {
  /** @type {import('./defs').ProjectionDefinition} */
  var self = {};
  var paramObj = defData.split('+').map(function (v) {
    return v.trim();
  }).filter(function (a) {
    return a;
  }).reduce(function (p, a) {
    /** @type {Array<?>} */
    var split = a.split('=');
    split.push(true);
    p[split[0].toLowerCase()] = split[1];
    return p;
  }, {});
  var paramName, paramVal, paramOutname;
  var params = {
    proj: 'projName',
    datum: 'datumCode',
    rf: function (v) {
      self.rf = parseFloat(v);
    },
    lat_0: function (v) {
      self.lat0 = v * D2R;
    },
    lat_1: function (v) {
      self.lat1 = v * D2R;
    },
    lat_2: function (v) {
      self.lat2 = v * D2R;
    },
    lat_ts: function (v) {
      self.lat_ts = v * D2R;
    },
    lon_0: function (v) {
      self.long0 = v * D2R;
    },
    lon_1: function (v) {
      self.long1 = v * D2R;
    },
    lon_2: function (v) {
      self.long2 = v * D2R;
    },
    alpha: function (v) {
      self.alpha = parseFloat(v) * D2R;
    },
    gamma: function (v) {
      self.rectified_grid_angle = parseFloat(v) * D2R;
    },
    lonc: function (v) {
      self.longc = v * D2R;
    },
    x_0: function (v) {
      self.x0 = parseFloat(v);
    },
    y_0: function (v) {
      self.y0 = parseFloat(v);
    },
    k_0: function (v) {
      self.k0 = parseFloat(v);
    },
    k: function (v) {
      self.k0 = parseFloat(v);
    },
    a: function (v) {
      self.a = parseFloat(v);
    },
    b: function (v) {
      self.b = parseFloat(v);
    },
    r: function (v) {
      self.a = self.b = parseFloat(v);
    },
    r_a: function () {
      self.R_A = true;
    },
    zone: function (v) {
      self.zone = parseInt(v, 10);
    },
    south: function () {
      self.utmSouth = true;
    },
    towgs84: function (v) {
      self.datum_params = v.split(',').map(function (a) {
        return parseFloat(a);
      });
    },
    to_meter: function (v) {
      self.to_meter = parseFloat(v);
    },
    units: function (v) {
      self.units = v;
      var unit = match(units, v);
      if (unit) {
        self.to_meter = unit.to_meter;
      }
    },
    from_greenwich: function (v) {
      self.from_greenwich = v * D2R;
    },
    pm: function (v) {
      var pm = match(PrimeMeridian, v);
      self.from_greenwich = (pm ? pm : parseFloat(v)) * D2R;
    },
    nadgrids: function (v) {
      if (v === '@null') {
        self.datumCode = 'none';
      } else {
        self.nadgrids = v;
      }
    },
    axis: function (v) {
      var legalAxis = 'ewnsud';
      if (v.length === 3 && legalAxis.indexOf(v.substr(0, 1)) !== -1 && legalAxis.indexOf(v.substr(1, 1)) !== -1 && legalAxis.indexOf(v.substr(2, 1)) !== -1) {
        self.axis = v;
      }
    },
    approx: function () {
      self.approx = true;
    }
  };
  for (paramName in paramObj) {
    paramVal = paramObj[paramName];
    if (paramName in params) {
      paramOutname = params[paramName];
      if (typeof paramOutname === 'function') {
        paramOutname(paramVal);
      } else {
        self[paramOutname] = paramVal;
      }
    } else {
      self[paramName] = paramVal;
    }
  }
  if (typeof self.datumCode === 'string' && self.datumCode !== 'WGS84') {
    self.datumCode = self.datumCode.toLowerCase();
  }
  return self;
}
