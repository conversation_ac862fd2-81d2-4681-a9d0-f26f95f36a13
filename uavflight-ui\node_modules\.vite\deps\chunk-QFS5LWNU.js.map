{"version": 3, "sources": ["../../ol/renderer/canvas/VectorTileLayer.js", "../../ol/layer/VectorTile.js"], "sourcesContent": ["/**\n * @module ol/renderer/canvas/VectorTileLayer\n */\nimport CanvasBuilderGroup from '../../render/canvas/BuilderGroup.js';\nimport CanvasExecutorGroup from '../../render/canvas/ExecutorGroup.js';\nimport CanvasTileLayerRenderer from './TileLayer.js';\nimport TileState from '../../TileState.js';\nimport ViewHint from '../../ViewHint.js';\nimport {\n  HIT_DETECT_RESOLUTION,\n  createHitDetectionImageData,\n  hitDetect,\n} from '../../render/canvas/hitdetect.js';\nimport {\n  apply as applyTransform,\n  create as createTransform,\n  multiply,\n  reset as resetTransform,\n  scale,\n  scale as scaleTransform,\n  translate as translateTransform,\n} from '../../transform.js';\nimport {\n  boundingExtent,\n  buffer,\n  containsExtent,\n  equals,\n  getIntersection,\n  getTopLeft,\n  intersects,\n} from '../../extent.js';\nimport {\n  getSquaredTolerance as getSquaredRenderTolerance,\n  renderFeature,\n} from '../vector.js';\nimport {getUid} from '../../util.js';\nimport {toSize} from '../../size.js';\nimport {wrapX} from '../../coordinate.js';\n\n/**\n * @type {!Object<string, Array<import(\"../../render/canvas.js\").BuilderType>>}\n */\nconst IMAGE_REPLAYS = {\n  'image': ['Polygon', 'Circle', 'LineString', 'Image', 'Text'],\n  'hybrid': ['Polygon', 'LineString'],\n  'vector': [],\n};\n\n/**\n * @type {!Object<string, Array<import(\"../../render/canvas.js\").BuilderType>>}\n */\nconst VECTOR_REPLAYS = {\n  'hybrid': ['Image', 'Text', 'Default'],\n  'vector': ['Polygon', 'Circle', 'LineString', 'Image', 'Text', 'Default'],\n};\n\n/**\n * @classdesc\n * Canvas renderer for vector tile layers.\n * @api\n * @extends {CanvasTileLayerRenderer<import(\"../../layer/VectorTile.js\").default>}\n */\nclass CanvasVectorTileLayerRenderer extends CanvasTileLayerRenderer {\n  /**\n   * @param {import(\"../../layer/VectorTile.js\").default} layer VectorTile layer.\n   */\n  constructor(layer) {\n    super(layer);\n\n    /** @private */\n    this.boundHandleStyleImageChange_ = this.handleStyleImageChange_.bind(this);\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.renderedLayerRevision_;\n\n    /**\n     * @private\n     * @type {import(\"../../transform\").Transform}\n     */\n    this.renderedPixelToCoordinateTransform_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.renderedRotation_;\n\n    /**\n     * @private\n     * @type {import(\"../../transform.js\").Transform}\n     */\n    this.tmpTransform_ = createTransform();\n  }\n\n  /**\n   * @param {import(\"../../VectorRenderTile.js\").default} tile Tile.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../../proj/Projection\").default} projection Projection.\n   * @return {boolean|undefined} Tile needs to be rendered.\n   */\n  prepareTile(tile, pixelRatio, projection) {\n    let render;\n    const state = tile.getState();\n    if (state === TileState.LOADED || state === TileState.ERROR) {\n      this.updateExecutorGroup_(tile, pixelRatio, projection);\n      if (this.tileImageNeedsRender_(tile)) {\n        render = true;\n      }\n    }\n    return render;\n  }\n\n  /**\n   * @param {number} z Tile coordinate z.\n   * @param {number} x Tile coordinate x.\n   * @param {number} y Tile coordinate y.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @return {!import(\"../../Tile.js\").default} Tile.\n   */\n  getTile(z, x, y, frameState) {\n    const pixelRatio = frameState.pixelRatio;\n    const viewState = frameState.viewState;\n    const resolution = viewState.resolution;\n    const projection = viewState.projection;\n    const layer = this.getLayer();\n    const tile = layer.getSource().getTile(z, x, y, pixelRatio, projection);\n    const viewHints = frameState.viewHints;\n    const hifi = !(\n      viewHints[ViewHint.ANIMATING] || viewHints[ViewHint.INTERACTING]\n    );\n    if (hifi || !tile.wantedResolution) {\n      tile.wantedResolution = resolution;\n    }\n    const render = this.prepareTile(tile, pixelRatio, projection);\n    if (\n      render &&\n      (hifi || Date.now() - frameState.time < 8) &&\n      layer.getRenderMode() !== 'vector'\n    ) {\n      this.renderTileImage_(tile, frameState);\n    }\n    return super.getTile(z, x, y, frameState);\n  }\n\n  /**\n   * @param {import(\"../../VectorRenderTile.js\").default} tile Tile.\n   * @return {boolean} Tile is drawable.\n   */\n  isDrawableTile(tile) {\n    const layer = this.getLayer();\n    return (\n      super.isDrawableTile(tile) &&\n      (layer.getRenderMode() === 'vector'\n        ? getUid(layer) in tile.executorGroups\n        : tile.hasContext(layer))\n    );\n  }\n\n  /**\n   * @inheritDoc\n   */\n  getTileImage(tile) {\n    return tile.getImage(this.getLayer());\n  }\n\n  /**\n   * Determine whether render should be called.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @return {boolean} Layer is ready to be rendered.\n   */\n  prepareFrame(frameState) {\n    const layerRevision = this.getLayer().getRevision();\n    if (this.renderedLayerRevision_ !== layerRevision) {\n      this.renderedLayerRevision_ = layerRevision;\n      this.renderedTiles.length = 0;\n    }\n    return super.prepareFrame(frameState);\n  }\n\n  /**\n   * @param {import(\"../../VectorRenderTile.js\").default} tile Tile.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../../proj/Projection.js\").default} projection Projection.\n   * @private\n   */\n  updateExecutorGroup_(tile, pixelRatio, projection) {\n    const layer = /** @type {import(\"../../layer/VectorTile.js\").default} */ (\n      this.getLayer()\n    );\n    const revision = layer.getRevision();\n    const renderOrder = layer.getRenderOrder() || null;\n\n    const resolution = tile.wantedResolution;\n    const builderState = tile.getReplayState(layer);\n    if (\n      !builderState.dirty &&\n      builderState.renderedResolution === resolution &&\n      builderState.renderedRevision == revision &&\n      builderState.renderedRenderOrder == renderOrder\n    ) {\n      return;\n    }\n\n    const source = layer.getSource();\n    const declutter = layer.getDeclutter();\n    const sourceTileGrid = source.getTileGrid();\n    const tileGrid = source.getTileGridForProjection(projection);\n    const tileExtent = tileGrid.getTileCoordExtent(tile.wrappedTileCoord);\n\n    const sourceTiles = source.getSourceTiles(pixelRatio, projection, tile);\n    const layerUid = getUid(layer);\n    delete tile.hitDetectionImageData[layerUid];\n    tile.executorGroups[layerUid] = [];\n    if (declutter) {\n      tile.declutterExecutorGroups[layerUid] = [];\n    }\n    builderState.dirty = false;\n    for (let t = 0, tt = sourceTiles.length; t < tt; ++t) {\n      const sourceTile = sourceTiles[t];\n      if (sourceTile.getState() != TileState.LOADED) {\n        continue;\n      }\n      const sourceTileCoord = sourceTile.tileCoord;\n      const sourceTileExtent =\n        sourceTileGrid.getTileCoordExtent(sourceTileCoord);\n      const sharedExtent = getIntersection(tileExtent, sourceTileExtent);\n      const builderExtent = buffer(\n        sharedExtent,\n        layer.getRenderBuffer() * resolution,\n        this.tmpExtent\n      );\n      const bufferedExtent = equals(sourceTileExtent, sharedExtent)\n        ? null\n        : builderExtent;\n      const builderGroup = new CanvasBuilderGroup(\n        0,\n        builderExtent,\n        resolution,\n        pixelRatio\n      );\n      const declutterBuilderGroup = declutter\n        ? new CanvasBuilderGroup(0, sharedExtent, resolution, pixelRatio)\n        : undefined;\n      const squaredTolerance = getSquaredRenderTolerance(\n        resolution,\n        pixelRatio\n      );\n\n      /**\n       * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n       * @this {CanvasVectorTileLayerRenderer}\n       */\n      const render = function (feature) {\n        let styles;\n        const styleFunction =\n          feature.getStyleFunction() || layer.getStyleFunction();\n        if (styleFunction) {\n          styles = styleFunction(feature, resolution);\n        }\n        if (styles) {\n          const dirty = this.renderFeature(\n            feature,\n            squaredTolerance,\n            styles,\n            builderGroup,\n            declutterBuilderGroup\n          );\n          builderState.dirty = builderState.dirty || dirty;\n        }\n      };\n\n      const features = sourceTile.getFeatures();\n      if (renderOrder && renderOrder !== builderState.renderedRenderOrder) {\n        features.sort(renderOrder);\n      }\n      for (let i = 0, ii = features.length; i < ii; ++i) {\n        const feature = features[i];\n        if (\n          !bufferedExtent ||\n          intersects(bufferedExtent, feature.getGeometry().getExtent())\n        ) {\n          render.call(this, feature);\n        }\n      }\n      const executorGroupInstructions = builderGroup.finish();\n      // no need to clip when the render tile is covered by a single source tile\n      const replayExtent =\n        layer.getRenderMode() !== 'vector' &&\n        declutter &&\n        sourceTiles.length === 1\n          ? null\n          : sharedExtent;\n      const renderingReplayGroup = new CanvasExecutorGroup(\n        replayExtent,\n        resolution,\n        pixelRatio,\n        source.getOverlaps(),\n        executorGroupInstructions,\n        layer.getRenderBuffer()\n      );\n      tile.executorGroups[layerUid].push(renderingReplayGroup);\n      if (declutterBuilderGroup) {\n        const declutterExecutorGroup = new CanvasExecutorGroup(\n          null,\n          resolution,\n          pixelRatio,\n          source.getOverlaps(),\n          declutterBuilderGroup.finish(),\n          layer.getRenderBuffer()\n        );\n        tile.declutterExecutorGroups[layerUid].push(declutterExecutorGroup);\n      }\n    }\n    builderState.renderedRevision = revision;\n    builderState.renderedRenderOrder = renderOrder;\n    builderState.renderedResolution = resolution;\n  }\n\n  /**\n   * @param {import(\"../../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @param {number} hitTolerance Hit tolerance in pixels.\n   * @param {import(\"../vector.js\").FeatureCallback<T>} callback Feature callback.\n   * @param {Array<import(\"../Map.js\").HitMatch<T>>} matches The hit detected matches with tolerance.\n   * @return {T|undefined} Callback result.\n   * @template T\n   */\n  forEachFeatureAtCoordinate(\n    coordinate,\n    frameState,\n    hitTolerance,\n    callback,\n    matches\n  ) {\n    const resolution = frameState.viewState.resolution;\n    const rotation = frameState.viewState.rotation;\n    hitTolerance = hitTolerance == undefined ? 0 : hitTolerance;\n    const layer = this.getLayer();\n    const source = layer.getSource();\n    const tileGrid = source.getTileGridForProjection(\n      frameState.viewState.projection\n    );\n\n    const hitExtent = boundingExtent([coordinate]);\n    buffer(hitExtent, resolution * hitTolerance, hitExtent);\n\n    /** @type {!Object<string, import(\"../Map.js\").HitMatch<T>|true>} */\n    const features = {};\n\n    /**\n     * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n     * @param {import(\"../../geom/SimpleGeometry.js\").default} geometry Geometry.\n     * @param {number} distanceSq The squared distance to the click position.\n     * @return {T|undefined} Callback result.\n     */\n    const featureCallback = function (feature, geometry, distanceSq) {\n      let key = feature.getId();\n      if (key === undefined) {\n        key = getUid(feature);\n      }\n      const match = features[key];\n      if (!match) {\n        if (distanceSq === 0) {\n          features[key] = true;\n          return callback(feature, layer, geometry);\n        }\n        matches.push(\n          (features[key] = {\n            feature: feature,\n            layer: layer,\n            geometry: geometry,\n            distanceSq: distanceSq,\n            callback: callback,\n          })\n        );\n      } else if (match !== true && distanceSq < match.distanceSq) {\n        if (distanceSq === 0) {\n          features[key] = true;\n          matches.splice(matches.lastIndexOf(match), 1);\n          return callback(feature, layer, geometry);\n        }\n        match.geometry = geometry;\n        match.distanceSq = distanceSq;\n      }\n      return undefined;\n    };\n\n    const renderedTiles =\n      /** @type {Array<import(\"../../VectorRenderTile.js\").default>} */ (\n        this.renderedTiles\n      );\n\n    let found;\n    for (let i = 0, ii = renderedTiles.length; !found && i < ii; ++i) {\n      const tile = renderedTiles[i];\n      const tileExtent = tileGrid.getTileCoordExtent(tile.wrappedTileCoord);\n      if (!intersects(tileExtent, hitExtent)) {\n        continue;\n      }\n\n      const layerUid = getUid(layer);\n      const executorGroups = [tile.executorGroups[layerUid]];\n      const declutterExecutorGroups = tile.declutterExecutorGroups[layerUid];\n      if (declutterExecutorGroups) {\n        executorGroups.push(declutterExecutorGroups);\n      }\n      executorGroups.some((executorGroups) => {\n        const declutteredFeatures =\n          executorGroups === declutterExecutorGroups\n            ? frameState.declutterTree.all().map((item) => item.value)\n            : null;\n        for (let t = 0, tt = executorGroups.length; t < tt; ++t) {\n          const executorGroup = executorGroups[t];\n          found = executorGroup.forEachFeatureAtCoordinate(\n            coordinate,\n            resolution,\n            rotation,\n            hitTolerance,\n            featureCallback,\n            declutteredFeatures\n          );\n          if (found) {\n            return true;\n          }\n        }\n      });\n    }\n    return found;\n  }\n\n  /**\n   * Asynchronous layer level hit detection.\n   * @param {import(\"../../pixel.js\").Pixel} pixel Pixel.\n   * @return {Promise<Array<import(\"../../Feature.js\").FeatureLike>>} Promise that resolves with an array of features.\n   */\n  getFeatures(pixel) {\n    return new Promise((resolve, reject) => {\n      const layer = this.getLayer();\n      const layerUid = getUid(layer);\n      const source = layer.getSource();\n      const projection = this.renderedProjection;\n      const projectionExtent = projection.getExtent();\n      const resolution = this.renderedResolution;\n      const tileGrid = source.getTileGridForProjection(projection);\n      const coordinate = applyTransform(\n        this.renderedPixelToCoordinateTransform_,\n        pixel.slice()\n      );\n      const tileCoord = tileGrid.getTileCoordForCoordAndResolution(\n        coordinate,\n        resolution\n      );\n      /** @type {import(\"../../VectorRenderTile.js\").default|undefined} */\n      let tile;\n      for (let i = 0, ii = this.renderedTiles.length; i < ii; ++i) {\n        if (\n          tileCoord.toString() === this.renderedTiles[i].tileCoord.toString()\n        ) {\n          tile = /** @type {import(\"../../VectorRenderTile.js\").default} */ (\n            this.renderedTiles[i]\n          );\n          if (tile.getState() === TileState.LOADED) {\n            const extent = tileGrid.getTileCoordExtent(tile.tileCoord);\n            if (\n              source.getWrapX() &&\n              projection.canWrapX() &&\n              !containsExtent(projectionExtent, extent)\n            ) {\n              wrapX(coordinate, projection);\n            }\n            break;\n          }\n          tile = undefined;\n        }\n      }\n      if (!tile || tile.loadingSourceTiles > 0) {\n        resolve([]);\n        return;\n      }\n      const extent = tileGrid.getTileCoordExtent(tile.wrappedTileCoord);\n      const corner = getTopLeft(extent);\n      const tilePixel = [\n        (coordinate[0] - corner[0]) / resolution,\n        (corner[1] - coordinate[1]) / resolution,\n      ];\n      /** @type {Array<import(\"../../Feature.js\").FeatureLike>} */\n      const features = tile\n        .getSourceTiles()\n        .reduce(function (accumulator, sourceTile) {\n          return accumulator.concat(sourceTile.getFeatures());\n        }, []);\n      /** @type {ImageData|undefined} */\n      let hitDetectionImageData = tile.hitDetectionImageData[layerUid];\n      if (!hitDetectionImageData) {\n        const tileSize = toSize(\n          tileGrid.getTileSize(\n            tileGrid.getZForResolution(resolution, source.zDirection)\n          )\n        );\n        const rotation = this.renderedRotation_;\n        const transforms = [\n          this.getRenderTransform(\n            tileGrid.getTileCoordCenter(tile.wrappedTileCoord),\n            resolution,\n            0,\n            HIT_DETECT_RESOLUTION,\n            tileSize[0] * HIT_DETECT_RESOLUTION,\n            tileSize[1] * HIT_DETECT_RESOLUTION,\n            0\n          ),\n        ];\n        hitDetectionImageData = createHitDetectionImageData(\n          tileSize,\n          transforms,\n          features,\n          layer.getStyleFunction(),\n          tileGrid.getTileCoordExtent(tile.wrappedTileCoord),\n          tile.getReplayState(layer).renderedResolution,\n          rotation\n        );\n        tile.hitDetectionImageData[layerUid] = hitDetectionImageData;\n      }\n      resolve(hitDetect(tilePixel, features, hitDetectionImageData));\n    });\n  }\n\n  /**\n   * Perform action necessary to get the layer rendered after new fonts have loaded\n   */\n  handleFontsChanged() {\n    const layer = this.getLayer();\n    if (layer.getVisible() && this.renderedLayerRevision_ !== undefined) {\n      layer.changed();\n    }\n  }\n\n  /**\n   * Handle changes in image style state.\n   * @param {import(\"../../events/Event.js\").default} event Image style change event.\n   * @private\n   */\n  handleStyleImageChange_(event) {\n    this.renderIfReadyAndVisible();\n  }\n\n  /**\n   * Render declutter items for this layer\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   */\n  renderDeclutter(frameState) {\n    const context = this.context;\n    const alpha = context.globalAlpha;\n    context.globalAlpha = this.getLayer().getOpacity();\n    const viewHints = frameState.viewHints;\n    const hifi = !(\n      viewHints[ViewHint.ANIMATING] || viewHints[ViewHint.INTERACTING]\n    );\n    const tiles =\n      /** @type {Array<import(\"../../VectorRenderTile.js\").default>} */ (\n        this.renderedTiles\n      );\n    for (let i = 0, ii = tiles.length; i < ii; ++i) {\n      const tile = tiles[i];\n      const declutterExecutorGroups =\n        tile.declutterExecutorGroups[getUid(this.getLayer())];\n      if (declutterExecutorGroups) {\n        for (let j = declutterExecutorGroups.length - 1; j >= 0; --j) {\n          declutterExecutorGroups[j].execute(\n            this.context,\n            1,\n            this.getTileRenderTransform(tile, frameState),\n            frameState.viewState.rotation,\n            hifi,\n            undefined,\n            frameState.declutterTree\n          );\n        }\n      }\n    }\n    context.globalAlpha = alpha;\n  }\n\n  getTileRenderTransform(tile, frameState) {\n    const pixelRatio = frameState.pixelRatio;\n    const viewState = frameState.viewState;\n    const center = viewState.center;\n    const resolution = viewState.resolution;\n    const rotation = viewState.rotation;\n    const size = frameState.size;\n    const width = Math.round(size[0] * pixelRatio);\n    const height = Math.round(size[1] * pixelRatio);\n\n    const source = this.getLayer().getSource();\n    const tileGrid = source.getTileGridForProjection(\n      frameState.viewState.projection\n    );\n    const tileCoord = tile.tileCoord;\n    const tileExtent = tileGrid.getTileCoordExtent(tile.wrappedTileCoord);\n    const worldOffset =\n      tileGrid.getTileCoordExtent(tileCoord, this.tmpExtent)[0] - tileExtent[0];\n    const transform = multiply(\n      scale(this.inversePixelTransform.slice(), 1 / pixelRatio, 1 / pixelRatio),\n      this.getRenderTransform(\n        center,\n        resolution,\n        rotation,\n        pixelRatio,\n        width,\n        height,\n        worldOffset\n      )\n    );\n    return transform;\n  }\n\n  /**\n   * Render the vectors for this layer.\n   * @param {CanvasRenderingContext2D} context Target context.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   */\n  postRender(context, frameState) {\n    const viewHints = frameState.viewHints;\n    const hifi = !(\n      viewHints[ViewHint.ANIMATING] || viewHints[ViewHint.INTERACTING]\n    );\n\n    this.renderedPixelToCoordinateTransform_ =\n      frameState.pixelToCoordinateTransform.slice();\n    this.renderedRotation_ = frameState.viewState.rotation;\n\n    const layer = /** @type {import(\"../../layer/VectorTile.js\").default} */ (\n      this.getLayer()\n    );\n    const renderMode = layer.getRenderMode();\n    const alpha = context.globalAlpha;\n    context.globalAlpha = layer.getOpacity();\n    const replayTypes = VECTOR_REPLAYS[renderMode];\n    const viewState = frameState.viewState;\n    const rotation = viewState.rotation;\n    const tileSource = layer.getSource();\n    const tileGrid = tileSource.getTileGridForProjection(viewState.projection);\n    const z = tileGrid.getZForResolution(\n      viewState.resolution,\n      tileSource.zDirection\n    );\n\n    const tiles = this.renderedTiles;\n    const clips = [];\n    const clipZs = [];\n    let ready = true;\n    for (let i = tiles.length - 1; i >= 0; --i) {\n      const tile = /** @type {import(\"../../VectorRenderTile.js\").default} */ (\n        tiles[i]\n      );\n      ready = ready && !tile.getReplayState(layer).dirty;\n      const executorGroups = tile.executorGroups[getUid(layer)].filter(\n        (group) => group.hasExecutors(replayTypes)\n      );\n      if (executorGroups.length === 0) {\n        continue;\n      }\n      const transform = this.getTileRenderTransform(tile, frameState);\n      const currentZ = tile.tileCoord[0];\n      let contextSaved = false;\n      // Clip mask for regions in this tile that already filled by a higher z tile\n      const currentClip = executorGroups[0].getClipCoords(transform);\n      if (currentClip) {\n        for (let j = 0, jj = clips.length; j < jj; ++j) {\n          if (z !== currentZ && currentZ < clipZs[j]) {\n            const clip = clips[j];\n            if (\n              intersects(\n                [\n                  currentClip[0],\n                  currentClip[3],\n                  currentClip[4],\n                  currentClip[7],\n                ],\n                [clip[0], clip[3], clip[4], clip[7]]\n              )\n            ) {\n              if (!contextSaved) {\n                context.save();\n                contextSaved = true;\n              }\n              context.beginPath();\n              // counter-clockwise (outer ring) for current tile\n              context.moveTo(currentClip[0], currentClip[1]);\n              context.lineTo(currentClip[2], currentClip[3]);\n              context.lineTo(currentClip[4], currentClip[5]);\n              context.lineTo(currentClip[6], currentClip[7]);\n              // clockwise (inner ring) for higher z tile\n              context.moveTo(clip[6], clip[7]);\n              context.lineTo(clip[4], clip[5]);\n              context.lineTo(clip[2], clip[3]);\n              context.lineTo(clip[0], clip[1]);\n              context.clip();\n            }\n          }\n        }\n        clips.push(currentClip);\n        clipZs.push(currentZ);\n      }\n      for (let t = 0, tt = executorGroups.length; t < tt; ++t) {\n        const executorGroup = executorGroups[t];\n        executorGroup.execute(\n          context,\n          1,\n          transform,\n          rotation,\n          hifi,\n          replayTypes\n        );\n      }\n      if (contextSaved) {\n        context.restore();\n      }\n    }\n    context.globalAlpha = alpha;\n    this.ready = ready;\n\n    super.postRender(context, frameState);\n  }\n\n  /**\n   * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n   * @param {number} squaredTolerance Squared tolerance.\n   * @param {import(\"../../style/Style.js\").default|Array<import(\"../../style/Style.js\").default>} styles The style or array of styles.\n   * @param {import(\"../../render/canvas/BuilderGroup.js\").default} builderGroup Replay group.\n   * @param {import(\"../../render/canvas/BuilderGroup.js\").default} [declutterBuilderGroup] Builder group for decluttering.\n   * @return {boolean} `true` if an image is loading.\n   */\n  renderFeature(\n    feature,\n    squaredTolerance,\n    styles,\n    builderGroup,\n    declutterBuilderGroup\n  ) {\n    if (!styles) {\n      return false;\n    }\n    let loading = false;\n    if (Array.isArray(styles)) {\n      for (let i = 0, ii = styles.length; i < ii; ++i) {\n        loading =\n          renderFeature(\n            builderGroup,\n            feature,\n            styles[i],\n            squaredTolerance,\n            this.boundHandleStyleImageChange_,\n            undefined,\n            declutterBuilderGroup\n          ) || loading;\n      }\n    } else {\n      loading = renderFeature(\n        builderGroup,\n        feature,\n        styles,\n        squaredTolerance,\n        this.boundHandleStyleImageChange_,\n        undefined,\n        declutterBuilderGroup\n      );\n    }\n    return loading;\n  }\n\n  /**\n   * @param {import(\"../../VectorRenderTile.js\").default} tile Tile.\n   * @return {boolean} A new tile image was rendered.\n   * @private\n   */\n  tileImageNeedsRender_(tile) {\n    const layer = /** @type {import(\"../../layer/VectorTile.js\").default} */ (\n      this.getLayer()\n    );\n    if (layer.getRenderMode() === 'vector') {\n      return false;\n    }\n    const replayState = tile.getReplayState(layer);\n    const revision = layer.getRevision();\n    const resolution = tile.wantedResolution;\n    return (\n      replayState.renderedTileResolution !== resolution ||\n      replayState.renderedTileRevision !== revision\n    );\n  }\n\n  /**\n   * @param {import(\"../../VectorRenderTile.js\").default} tile Tile.\n   * @param {import(\"../../Map\").FrameState} frameState Frame state.\n   * @private\n   */\n  renderTileImage_(tile, frameState) {\n    const layer = /** @type {import(\"../../layer/VectorTile.js\").default} */ (\n      this.getLayer()\n    );\n    const replayState = tile.getReplayState(layer);\n    const revision = layer.getRevision();\n    const executorGroups = tile.executorGroups[getUid(layer)];\n    replayState.renderedTileRevision = revision;\n\n    const tileCoord = tile.wrappedTileCoord;\n    const z = tileCoord[0];\n    const source = layer.getSource();\n    let pixelRatio = frameState.pixelRatio;\n    const viewState = frameState.viewState;\n    const projection = viewState.projection;\n    const tileGrid = source.getTileGridForProjection(projection);\n    const tileResolution = tileGrid.getResolution(tile.tileCoord[0]);\n    const renderPixelRatio =\n      (frameState.pixelRatio / tile.wantedResolution) * tileResolution;\n    const resolution = tileGrid.getResolution(z);\n    const context = tile.getContext(layer);\n\n    // Increase tile size when overzooming for low pixel ratio, to avoid blurry tiles\n    pixelRatio = Math.round(\n      Math.max(pixelRatio, renderPixelRatio / pixelRatio)\n    );\n    const size = source.getTilePixelSize(z, pixelRatio, projection);\n    context.canvas.width = size[0];\n    context.canvas.height = size[1];\n    const renderScale = pixelRatio / renderPixelRatio;\n    if (renderScale !== 1) {\n      const canvasTransform = resetTransform(this.tmpTransform_);\n      scaleTransform(canvasTransform, renderScale, renderScale);\n      context.setTransform.apply(context, canvasTransform);\n    }\n    const tileExtent = tileGrid.getTileCoordExtent(tileCoord, this.tmpExtent);\n    const pixelScale = renderPixelRatio / resolution;\n    const transform = resetTransform(this.tmpTransform_);\n    scaleTransform(transform, pixelScale, -pixelScale);\n    translateTransform(transform, -tileExtent[0], -tileExtent[3]);\n    for (let i = 0, ii = executorGroups.length; i < ii; ++i) {\n      const executorGroup = executorGroups[i];\n      executorGroup.execute(\n        context,\n        renderScale,\n        transform,\n        0,\n        true,\n        IMAGE_REPLAYS[layer.getRenderMode()]\n      );\n    }\n    replayState.renderedTileResolution = tile.wantedResolution;\n  }\n}\n\nexport default CanvasVectorTileLayerRenderer;\n", "/**\n * @module ol/layer/VectorTile\n */\nimport BaseVectorLayer from './BaseVector.js';\nimport CanvasVectorTileLayerRenderer from '../renderer/canvas/VectorTileLayer.js';\nimport TileProperty from './TileProperty.js';\nimport {assert} from '../asserts.js';\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"./Base\").BaseLayerObjectEventTypes|\n *     import(\"./Layer.js\").LayerEventType|'change:preload'|'change:useInterimTilesOnError', import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../render/EventType\").LayerRenderEventTypes, import(\"../render/Event\").default, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"./Base\").BaseLayerObjectEventTypes|\n *     import(\"./Layer.js\").LayerEventType|'change:preload'|'change:useInterimTilesOnError'|import(\"../render/EventType\").LayerRenderEventTypes, Return>} VectorTileLayerOnSignature\n */\n\n/**\n * @typedef {'hybrid' | 'vector'} VectorTileRenderType\n */\n\n/**\n * @typedef {Object} Options\n * @property {string} [className='ol-layer'] A CSS class name to set to the layer element.\n * @property {number} [opacity=1] Opacity (0, 1).\n * @property {boolean} [visible=true] Visibility.\n * @property {import(\"../extent.js\").Extent} [extent] The bounding extent for layer rendering.  The layer will not be\n * rendered outside of this extent.\n * @property {number} [zIndex] The z-index for layer rendering.  At rendering time, the layers\n * will be ordered, first by Z-index and then by position. When `undefined`, a `zIndex` of 0 is assumed\n * for layers that are added to the map's `layers` collection, or `Infinity` when the layer's `setMap()`\n * method was used.\n * @property {number} [minResolution] The minimum resolution (inclusive) at which this layer will be\n * visible.\n * @property {number} [maxResolution] The maximum resolution (exclusive) below which this layer will\n * be visible.\n * @property {number} [minZoom] The minimum view zoom level (exclusive) above which this layer will be\n * visible.\n * @property {number} [maxZoom] The maximum view zoom level (inclusive) at which this layer will\n * be visible.\n * @property {import(\"../render.js\").OrderFunction} [renderOrder] Render order. Function to be used when sorting\n * features before rendering. By default features are drawn in the order that they are created. Use\n * `null` to avoid the sort, but get an undefined draw order.\n * @property {number} [renderBuffer=100] The buffer in pixels around the tile extent used by the\n * renderer when getting features from the vector tile for the rendering or hit-detection.\n * Recommended value: Vector tiles are usually generated with a buffer, so this value should match\n * the largest possible buffer of the used tiles. It should be at least the size of the largest\n * point symbol or line width.\n * @property {VectorTileRenderType} [renderMode='hybrid'] Render mode for vector tiles:\n *  * `'hybrid'`: Polygon and line elements are rendered as images, so pixels are scaled during zoom\n *    animations. Point symbols and texts are accurately rendered as vectors and can stay upright on\n *    rotated views.\n *  * `'vector'`: Everything is rendered as vectors. Use this mode for improved performance on vector\n *    tile layers with only a few rendered features (e.g. for highlighting a subset of features of\n *    another layer with the same source).\n * @property {import(\"../source/VectorTile.js\").default} [source] Source.\n * @property {import(\"../Map.js\").default} [map] Sets the layer as overlay on a map. The map will not manage\n * this layer in its layers collection, and the layer will be rendered on top. This is useful for\n * temporary layers. The standard way to add a layer to a map and have it managed by the map is to\n * use [map.addLayer()]{@link import(\"../Map.js\").default#addLayer}.\n * @property {boolean} [declutter=false] Declutter images and text. Decluttering is applied to all\n * image and text styles of all Vector and VectorTile layers that have set this to `true`. The priority\n * is defined by the z-index of the layer, the `zIndex` of the style and the render order of features.\n * Higher z-index means higher priority. Within the same z-index, a feature rendered before another has\n * higher priority.\n *\n * As an optimization decluttered features from layers with the same `className` are rendered above\n * the fill and stroke styles of all of those layers regardless of z-index.  To opt out of this\n * behavior and place declutterd features with their own layer configure the layer with a `className`\n * other than `ol-layer`.\n * @property {import(\"../style/Style.js\").StyleLike|null} [style] Layer style. When set to `null`, only\n * features that have their own style will be rendered. See {@link module:ol/style/Style~Style} for the default style\n * which will be used if this is not set.\n * @property {import(\"./Base.js\").BackgroundColor|false} [background] Background color for the layer. If not specified, no\n * background will be rendered.\n * @property {boolean} [updateWhileAnimating=false] When set to `true`, feature batches will be\n * recreated during animations. This means that no vectors will be shown clipped, but the setting\n * will have a performance impact for large amounts of vector data. When set to `false`, batches\n * will be recreated when no animation is active.\n * @property {boolean} [updateWhileInteracting=false] When set to `true`, feature batches will be\n * recreated during interactions. See also `updateWhileAnimating`.\n * @property {number} [preload=0] Preload. Load low-resolution tiles up to `preload` levels. `0`\n * means no preloading.\n * @property {boolean} [useInterimTilesOnError=true] Use interim tiles on error.\n * @property {Object<string, *>} [properties] Arbitrary observable properties. Can be accessed with `#get()` and `#set()`.\n */\n\n/**\n * @classdesc\n * Layer for vector tile data that is rendered client-side.\n * Note that any property set in the options is set as a {@link module:ol/Object~BaseObject}\n * property on the layer object; for example, setting `title: 'My Title'` in the\n * options means that `title` is observable, and has get/set accessors.\n *\n * @param {Options} [options] Options.\n * @extends {BaseVectorLayer<import(\"../source/VectorTile.js\").default, CanvasVectorTileLayerRenderer>}\n * @api\n */\nclass VectorTileLayer extends BaseVectorLayer {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    const baseOptions = /** @type {Object} */ (Object.assign({}, options));\n    delete baseOptions.preload;\n    delete baseOptions.useInterimTilesOnError;\n\n    super(\n      /** @type {import(\"./BaseVector.js\").Options<import(\"../source/VectorTile.js\").default>} */ (\n        baseOptions\n      )\n    );\n\n    /***\n     * @type {VectorTileLayerOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {VectorTileLayerOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {VectorTileLayerOnSignature<void>}\n     */\n    this.un;\n\n    const renderMode = options.renderMode || 'hybrid';\n    // `renderMode` must be `'hybrid'` or `'vector'`.\n    assert(renderMode == 'hybrid' || renderMode == 'vector', 28);\n\n    /**\n     * @private\n     * @type {VectorTileRenderType}\n     */\n    this.renderMode_ = renderMode;\n\n    this.setPreload(options.preload ? options.preload : 0);\n    this.setUseInterimTilesOnError(\n      options.useInterimTilesOnError !== undefined\n        ? options.useInterimTilesOnError\n        : true\n    );\n\n    /**\n     * @return {import(\"./Base.js\").BackgroundColor} Background color.\n     * @function\n     * @api\n     */\n    this.getBackground;\n\n    /**\n     * @param {import(\"./Base.js\").BackgroundColor} background Background color.\n     * @function\n     * @api\n     */\n    this.setBackground;\n  }\n\n  createRenderer() {\n    return new CanvasVectorTileLayerRenderer(this);\n  }\n\n  /**\n   * Get the topmost feature that intersects the given pixel on the viewport. Returns a promise\n   * that resolves with an array of features. The array will either contain the topmost feature\n   * when a hit was detected, or it will be empty.\n   *\n   * The hit detection algorithm used for this method is optimized for performance, but is less\n   * accurate than the one used in [map.getFeaturesAtPixel()]{@link import(\"../Map.js\").default#getFeaturesAtPixel}.\n   * Text is not considered, and icons are only represented by their bounding box instead of the exact\n   * image.\n   *\n   * @param {import(\"../pixel.js\").Pixel} pixel Pixel.\n   * @return {Promise<Array<import(\"../Feature\").FeatureLike>>} Promise that resolves with an array of features.\n   * @api\n   */\n  getFeatures(pixel) {\n    return super.getFeatures(pixel);\n  }\n\n  /**\n   * @return {VectorTileRenderType} The render mode.\n   */\n  getRenderMode() {\n    return this.renderMode_;\n  }\n\n  /**\n   * Return the level as number to which we will preload tiles up to.\n   * @return {number} The level to preload tiles up to.\n   * @observable\n   * @api\n   */\n  getPreload() {\n    return /** @type {number} */ (this.get(TileProperty.PRELOAD));\n  }\n\n  /**\n   * Whether we use interim tiles on error.\n   * @return {boolean} Use interim tiles on error.\n   * @observable\n   * @api\n   */\n  getUseInterimTilesOnError() {\n    return /** @type {boolean} */ (\n      this.get(TileProperty.USE_INTERIM_TILES_ON_ERROR)\n    );\n  }\n\n  /**\n   * Set the level as number to which we will preload tiles up to.\n   * @param {number} preload The level to preload tiles up to.\n   * @observable\n   * @api\n   */\n  setPreload(preload) {\n    this.set(TileProperty.PRELOAD, preload);\n  }\n\n  /**\n   * Set whether we use interim tiles on error.\n   * @param {boolean} useInterimTilesOnError Use interim tiles on error.\n   * @observable\n   * @api\n   */\n  setUseInterimTilesOnError(useInterimTilesOnError) {\n    this.set(TileProperty.USE_INTERIM_TILES_ON_ERROR, useInterimTilesOnError);\n  }\n}\n\nexport default VectorTileLayer;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,IAAM,gBAAgB;AAAA,EACpB,SAAS,CAAC,WAAW,UAAU,cAAc,SAAS,MAAM;AAAA,EAC5D,UAAU,CAAC,WAAW,YAAY;AAAA,EAClC,UAAU,CAAC;AACb;AAKA,IAAM,iBAAiB;AAAA,EACrB,UAAU,CAAC,SAAS,QAAQ,SAAS;AAAA,EACrC,UAAU,CAAC,WAAW,UAAU,cAAc,SAAS,QAAQ,SAAS;AAC1E;AAQA,IAAM,gCAAN,cAA4C,kBAAwB;AAAA;AAAA;AAAA;AAAA,EAIlE,YAAY,OAAO;AACjB,UAAM,KAAK;AAGX,SAAK,+BAA+B,KAAK,wBAAwB,KAAK,IAAI;AAM1E,SAAK;AAML,SAAK,sCAAsC;AAM3C,SAAK;AAML,SAAK,gBAAgB,OAAgB;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,MAAM,YAAY,YAAY;AACxC,QAAI;AACJ,UAAM,QAAQ,KAAK,SAAS;AAC5B,QAAI,UAAU,kBAAU,UAAU,UAAU,kBAAU,OAAO;AAC3D,WAAK,qBAAqB,MAAM,YAAY,UAAU;AACtD,UAAI,KAAK,sBAAsB,IAAI,GAAG;AACpC,iBAAS;AAAA,MACX;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,GAAG,GAAG,GAAG,YAAY;AAC3B,UAAM,aAAa,WAAW;AAC9B,UAAM,YAAY,WAAW;AAC7B,UAAM,aAAa,UAAU;AAC7B,UAAM,aAAa,UAAU;AAC7B,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,OAAO,MAAM,UAAU,EAAE,QAAQ,GAAG,GAAG,GAAG,YAAY,UAAU;AACtE,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO,EACX,UAAU,iBAAS,SAAS,KAAK,UAAU,iBAAS,WAAW;AAEjE,QAAI,QAAQ,CAAC,KAAK,kBAAkB;AAClC,WAAK,mBAAmB;AAAA,IAC1B;AACA,UAAM,SAAS,KAAK,YAAY,MAAM,YAAY,UAAU;AAC5D,QACE,WACC,QAAQ,KAAK,IAAI,IAAI,WAAW,OAAO,MACxC,MAAM,cAAc,MAAM,UAC1B;AACA,WAAK,iBAAiB,MAAM,UAAU;AAAA,IACxC;AACA,WAAO,MAAM,QAAQ,GAAG,GAAG,GAAG,UAAU;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,MAAM;AACnB,UAAM,QAAQ,KAAK,SAAS;AAC5B,WACE,MAAM,eAAe,IAAI,MACxB,MAAM,cAAc,MAAM,WACvB,OAAO,KAAK,KAAK,KAAK,iBACtB,KAAK,WAAW,KAAK;AAAA,EAE7B;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,MAAM;AACjB,WAAO,KAAK,SAAS,KAAK,SAAS,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,YAAY;AACvB,UAAM,gBAAgB,KAAK,SAAS,EAAE,YAAY;AAClD,QAAI,KAAK,2BAA2B,eAAe;AACjD,WAAK,yBAAyB;AAC9B,WAAK,cAAc,SAAS;AAAA,IAC9B;AACA,WAAO,MAAM,aAAa,UAAU;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,MAAM,YAAY,YAAY;AACjD,UAAM;AAAA;AAAA,MACJ,KAAK,SAAS;AAAA;AAEhB,UAAM,WAAW,MAAM,YAAY;AACnC,UAAM,cAAc,MAAM,eAAe,KAAK;AAE9C,UAAM,aAAa,KAAK;AACxB,UAAM,eAAe,KAAK,eAAe,KAAK;AAC9C,QACE,CAAC,aAAa,SACd,aAAa,uBAAuB,cACpC,aAAa,oBAAoB,YACjC,aAAa,uBAAuB,aACpC;AACA;AAAA,IACF;AAEA,UAAM,SAAS,MAAM,UAAU;AAC/B,UAAM,YAAY,MAAM,aAAa;AACrC,UAAM,iBAAiB,OAAO,YAAY;AAC1C,UAAM,WAAW,OAAO,yBAAyB,UAAU;AAC3D,UAAM,aAAa,SAAS,mBAAmB,KAAK,gBAAgB;AAEpE,UAAM,cAAc,OAAO,eAAe,YAAY,YAAY,IAAI;AACtE,UAAM,WAAW,OAAO,KAAK;AAC7B,WAAO,KAAK,sBAAsB,QAAQ;AAC1C,SAAK,eAAe,QAAQ,IAAI,CAAC;AACjC,QAAI,WAAW;AACb,WAAK,wBAAwB,QAAQ,IAAI,CAAC;AAAA,IAC5C;AACA,iBAAa,QAAQ;AACrB,aAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,YAAM,aAAa,YAAY,CAAC;AAChC,UAAI,WAAW,SAAS,KAAK,kBAAU,QAAQ;AAC7C;AAAA,MACF;AACA,YAAM,kBAAkB,WAAW;AACnC,YAAM,mBACJ,eAAe,mBAAmB,eAAe;AACnD,YAAM,eAAe,gBAAgB,YAAY,gBAAgB;AACjE,YAAM,gBAAgB;AAAA,QACpB;AAAA,QACA,MAAM,gBAAgB,IAAI;AAAA,QAC1B,KAAK;AAAA,MACP;AACA,YAAM,iBAAiB,OAAO,kBAAkB,YAAY,IACxD,OACA;AACJ,YAAM,eAAe,IAAI;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,wBAAwB,YAC1B,IAAI,qBAAmB,GAAG,cAAc,YAAY,UAAU,IAC9D;AACJ,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,MACF;AAMA,YAAM,SAAS,SAAU,SAAS;AAChC,YAAI;AACJ,cAAM,gBACJ,QAAQ,iBAAiB,KAAK,MAAM,iBAAiB;AACvD,YAAI,eAAe;AACjB,mBAAS,cAAc,SAAS,UAAU;AAAA,QAC5C;AACA,YAAI,QAAQ;AACV,gBAAM,QAAQ,KAAK;AAAA,YACjB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,uBAAa,QAAQ,aAAa,SAAS;AAAA,QAC7C;AAAA,MACF;AAEA,YAAM,WAAW,WAAW,YAAY;AACxC,UAAI,eAAe,gBAAgB,aAAa,qBAAqB;AACnE,iBAAS,KAAK,WAAW;AAAA,MAC3B;AACA,eAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,EAAE,GAAG;AACjD,cAAM,UAAU,SAAS,CAAC;AAC1B,YACE,CAAC,kBACD,WAAW,gBAAgB,QAAQ,YAAY,EAAE,UAAU,CAAC,GAC5D;AACA,iBAAO,KAAK,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AACA,YAAM,4BAA4B,aAAa,OAAO;AAEtD,YAAM,eACJ,MAAM,cAAc,MAAM,YAC1B,aACA,YAAY,WAAW,IACnB,OACA;AACN,YAAM,uBAAuB,IAAI;AAAA,QAC/B;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAO,YAAY;AAAA,QACnB;AAAA,QACA,MAAM,gBAAgB;AAAA,MACxB;AACA,WAAK,eAAe,QAAQ,EAAE,KAAK,oBAAoB;AACvD,UAAI,uBAAuB;AACzB,cAAM,yBAAyB,IAAI;AAAA,UACjC;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO,YAAY;AAAA,UACnB,sBAAsB,OAAO;AAAA,UAC7B,MAAM,gBAAgB;AAAA,QACxB;AACA,aAAK,wBAAwB,QAAQ,EAAE,KAAK,sBAAsB;AAAA,MACpE;AAAA,IACF;AACA,iBAAa,mBAAmB;AAChC,iBAAa,sBAAsB;AACnC,iBAAa,qBAAqB;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,2BACE,YACA,YACA,cACA,UACA,SACA;AACA,UAAM,aAAa,WAAW,UAAU;AACxC,UAAM,WAAW,WAAW,UAAU;AACtC,mBAAe,gBAAgB,SAAY,IAAI;AAC/C,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,SAAS,MAAM,UAAU;AAC/B,UAAM,WAAW,OAAO;AAAA,MACtB,WAAW,UAAU;AAAA,IACvB;AAEA,UAAM,YAAY,eAAe,CAAC,UAAU,CAAC;AAC7C,WAAO,WAAW,aAAa,cAAc,SAAS;AAGtD,UAAM,WAAW,CAAC;AAQlB,UAAM,kBAAkB,SAAU,SAAS,UAAU,YAAY;AAC/D,UAAI,MAAM,QAAQ,MAAM;AACxB,UAAI,QAAQ,QAAW;AACrB,cAAM,OAAO,OAAO;AAAA,MACtB;AACA,YAAM,QAAQ,SAAS,GAAG;AAC1B,UAAI,CAAC,OAAO;AACV,YAAI,eAAe,GAAG;AACpB,mBAAS,GAAG,IAAI;AAChB,iBAAO,SAAS,SAAS,OAAO,QAAQ;AAAA,QAC1C;AACA,gBAAQ;AAAA,UACL,SAAS,GAAG,IAAI;AAAA,YACf;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF,WAAW,UAAU,QAAQ,aAAa,MAAM,YAAY;AAC1D,YAAI,eAAe,GAAG;AACpB,mBAAS,GAAG,IAAI;AAChB,kBAAQ,OAAO,QAAQ,YAAY,KAAK,GAAG,CAAC;AAC5C,iBAAO,SAAS,SAAS,OAAO,QAAQ;AAAA,QAC1C;AACA,cAAM,WAAW;AACjB,cAAM,aAAa;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AAEA,UAAM;AAAA;AAAA,MAEF,KAAK;AAAA;AAGT,QAAI;AACJ,aAAS,IAAI,GAAG,KAAK,cAAc,QAAQ,CAAC,SAAS,IAAI,IAAI,EAAE,GAAG;AAChE,YAAM,OAAO,cAAc,CAAC;AAC5B,YAAM,aAAa,SAAS,mBAAmB,KAAK,gBAAgB;AACpE,UAAI,CAAC,WAAW,YAAY,SAAS,GAAG;AACtC;AAAA,MACF;AAEA,YAAM,WAAW,OAAO,KAAK;AAC7B,YAAM,iBAAiB,CAAC,KAAK,eAAe,QAAQ,CAAC;AACrD,YAAM,0BAA0B,KAAK,wBAAwB,QAAQ;AACrE,UAAI,yBAAyB;AAC3B,uBAAe,KAAK,uBAAuB;AAAA,MAC7C;AACA,qBAAe,KAAK,CAACA,oBAAmB;AACtC,cAAM,sBACJA,oBAAmB,0BACf,WAAW,cAAc,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,KAAK,IACvD;AACN,iBAAS,IAAI,GAAG,KAAKA,gBAAe,QAAQ,IAAI,IAAI,EAAE,GAAG;AACvD,gBAAM,gBAAgBA,gBAAe,CAAC;AACtC,kBAAQ,cAAc;AAAA,YACpB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,cAAI,OAAO;AACT,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,OAAO;AACjB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,QAAQ,KAAK,SAAS;AAC5B,YAAM,WAAW,OAAO,KAAK;AAC7B,YAAM,SAAS,MAAM,UAAU;AAC/B,YAAM,aAAa,KAAK;AACxB,YAAM,mBAAmB,WAAW,UAAU;AAC9C,YAAM,aAAa,KAAK;AACxB,YAAM,WAAW,OAAO,yBAAyB,UAAU;AAC3D,YAAM,aAAa;AAAA,QACjB,KAAK;AAAA,QACL,MAAM,MAAM;AAAA,MACd;AACA,YAAM,YAAY,SAAS;AAAA,QACzB;AAAA,QACA;AAAA,MACF;AAEA,UAAI;AACJ,eAAS,IAAI,GAAG,KAAK,KAAK,cAAc,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC3D,YACE,UAAU,SAAS,MAAM,KAAK,cAAc,CAAC,EAAE,UAAU,SAAS,GAClE;AACA;AAAA,UACE,KAAK,cAAc,CAAC;AAEtB,cAAI,KAAK,SAAS,MAAM,kBAAU,QAAQ;AACxC,kBAAMC,UAAS,SAAS,mBAAmB,KAAK,SAAS;AACzD,gBACE,OAAO,SAAS,KAChB,WAAW,SAAS,KACpB,CAAC,eAAe,kBAAkBA,OAAM,GACxC;AACA,oBAAM,YAAY,UAAU;AAAA,YAC9B;AACA;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,CAAC,QAAQ,KAAK,qBAAqB,GAAG;AACxC,gBAAQ,CAAC,CAAC;AACV;AAAA,MACF;AACA,YAAM,SAAS,SAAS,mBAAmB,KAAK,gBAAgB;AAChE,YAAM,SAAS,WAAW,MAAM;AAChC,YAAM,YAAY;AAAA,SACf,WAAW,CAAC,IAAI,OAAO,CAAC,KAAK;AAAA,SAC7B,OAAO,CAAC,IAAI,WAAW,CAAC,KAAK;AAAA,MAChC;AAEA,YAAM,WAAW,KACd,eAAe,EACf,OAAO,SAAU,aAAa,YAAY;AACzC,eAAO,YAAY,OAAO,WAAW,YAAY,CAAC;AAAA,MACpD,GAAG,CAAC,CAAC;AAEP,UAAI,wBAAwB,KAAK,sBAAsB,QAAQ;AAC/D,UAAI,CAAC,uBAAuB;AAC1B,cAAM,WAAW;AAAA,UACf,SAAS;AAAA,YACP,SAAS,kBAAkB,YAAY,OAAO,UAAU;AAAA,UAC1D;AAAA,QACF;AACA,cAAM,WAAW,KAAK;AACtB,cAAM,aAAa;AAAA,UACjB,KAAK;AAAA,YACH,SAAS,mBAAmB,KAAK,gBAAgB;AAAA,YACjD;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAAS,CAAC,IAAI;AAAA,YACd,SAAS,CAAC,IAAI;AAAA,YACd;AAAA,UACF;AAAA,QACF;AACA,gCAAwB;AAAA,UACtB;AAAA,UACA;AAAA,UACA;AAAA,UACA,MAAM,iBAAiB;AAAA,UACvB,SAAS,mBAAmB,KAAK,gBAAgB;AAAA,UACjD,KAAK,eAAe,KAAK,EAAE;AAAA,UAC3B;AAAA,QACF;AACA,aAAK,sBAAsB,QAAQ,IAAI;AAAA,MACzC;AACA,cAAQ,UAAU,WAAW,UAAU,qBAAqB,CAAC;AAAA,IAC/D,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,UAAM,QAAQ,KAAK,SAAS;AAC5B,QAAI,MAAM,WAAW,KAAK,KAAK,2BAA2B,QAAW;AACnE,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB,OAAO;AAC7B,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,YAAY;AAC1B,UAAM,UAAU,KAAK;AACrB,UAAM,QAAQ,QAAQ;AACtB,YAAQ,cAAc,KAAK,SAAS,EAAE,WAAW;AACjD,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO,EACX,UAAU,iBAAS,SAAS,KAAK,UAAU,iBAAS,WAAW;AAEjE,UAAM;AAAA;AAAA,MAEF,KAAK;AAAA;AAET,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,YAAM,OAAO,MAAM,CAAC;AACpB,YAAM,0BACJ,KAAK,wBAAwB,OAAO,KAAK,SAAS,CAAC,CAAC;AACtD,UAAI,yBAAyB;AAC3B,iBAAS,IAAI,wBAAwB,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAC5D,kCAAwB,CAAC,EAAE;AAAA,YACzB,KAAK;AAAA,YACL;AAAA,YACA,KAAK,uBAAuB,MAAM,UAAU;AAAA,YAC5C,WAAW,UAAU;AAAA,YACrB;AAAA,YACA;AAAA,YACA,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,YAAQ,cAAc;AAAA,EACxB;AAAA,EAEA,uBAAuB,MAAM,YAAY;AACvC,UAAM,aAAa,WAAW;AAC9B,UAAM,YAAY,WAAW;AAC7B,UAAM,SAAS,UAAU;AACzB,UAAM,aAAa,UAAU;AAC7B,UAAM,WAAW,UAAU;AAC3B,UAAM,OAAO,WAAW;AACxB,UAAM,QAAQ,KAAK,MAAM,KAAK,CAAC,IAAI,UAAU;AAC7C,UAAM,SAAS,KAAK,MAAM,KAAK,CAAC,IAAI,UAAU;AAE9C,UAAM,SAAS,KAAK,SAAS,EAAE,UAAU;AACzC,UAAM,WAAW,OAAO;AAAA,MACtB,WAAW,UAAU;AAAA,IACvB;AACA,UAAM,YAAY,KAAK;AACvB,UAAM,aAAa,SAAS,mBAAmB,KAAK,gBAAgB;AACpE,UAAM,cACJ,SAAS,mBAAmB,WAAW,KAAK,SAAS,EAAE,CAAC,IAAI,WAAW,CAAC;AAC1E,UAAM,YAAY;AAAA,MAChB,MAAM,KAAK,sBAAsB,MAAM,GAAG,IAAI,YAAY,IAAI,UAAU;AAAA,MACxE,KAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,SAAS,YAAY;AAC9B,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO,EACX,UAAU,iBAAS,SAAS,KAAK,UAAU,iBAAS,WAAW;AAGjE,SAAK,sCACH,WAAW,2BAA2B,MAAM;AAC9C,SAAK,oBAAoB,WAAW,UAAU;AAE9C,UAAM;AAAA;AAAA,MACJ,KAAK,SAAS;AAAA;AAEhB,UAAM,aAAa,MAAM,cAAc;AACvC,UAAM,QAAQ,QAAQ;AACtB,YAAQ,cAAc,MAAM,WAAW;AACvC,UAAM,cAAc,eAAe,UAAU;AAC7C,UAAM,YAAY,WAAW;AAC7B,UAAM,WAAW,UAAU;AAC3B,UAAM,aAAa,MAAM,UAAU;AACnC,UAAM,WAAW,WAAW,yBAAyB,UAAU,UAAU;AACzE,UAAM,IAAI,SAAS;AAAA,MACjB,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAEA,UAAM,QAAQ,KAAK;AACnB,UAAM,QAAQ,CAAC;AACf,UAAM,SAAS,CAAC;AAChB,QAAI,QAAQ;AACZ,aAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAC1C,YAAM;AAAA;AAAA,QACJ,MAAM,CAAC;AAAA;AAET,cAAQ,SAAS,CAAC,KAAK,eAAe,KAAK,EAAE;AAC7C,YAAM,iBAAiB,KAAK,eAAe,OAAO,KAAK,CAAC,EAAE;AAAA,QACxD,CAAC,UAAU,MAAM,aAAa,WAAW;AAAA,MAC3C;AACA,UAAI,eAAe,WAAW,GAAG;AAC/B;AAAA,MACF;AACA,YAAM,YAAY,KAAK,uBAAuB,MAAM,UAAU;AAC9D,YAAM,WAAW,KAAK,UAAU,CAAC;AACjC,UAAI,eAAe;AAEnB,YAAM,cAAc,eAAe,CAAC,EAAE,cAAc,SAAS;AAC7D,UAAI,aAAa;AACf,iBAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,cAAI,MAAM,YAAY,WAAW,OAAO,CAAC,GAAG;AAC1C,kBAAM,OAAO,MAAM,CAAC;AACpB,gBACE;AAAA,cACE;AAAA,gBACE,YAAY,CAAC;AAAA,gBACb,YAAY,CAAC;AAAA,gBACb,YAAY,CAAC;AAAA,gBACb,YAAY,CAAC;AAAA,cACf;AAAA,cACA,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,YACrC,GACA;AACA,kBAAI,CAAC,cAAc;AACjB,wBAAQ,KAAK;AACb,+BAAe;AAAA,cACjB;AACA,sBAAQ,UAAU;AAElB,sBAAQ,OAAO,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAC7C,sBAAQ,OAAO,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAC7C,sBAAQ,OAAO,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAC7C,sBAAQ,OAAO,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAE7C,sBAAQ,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAC/B,sBAAQ,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAC/B,sBAAQ,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAC/B,sBAAQ,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAC/B,sBAAQ,KAAK;AAAA,YACf;AAAA,UACF;AAAA,QACF;AACA,cAAM,KAAK,WAAW;AACtB,eAAO,KAAK,QAAQ;AAAA,MACtB;AACA,eAAS,IAAI,GAAG,KAAK,eAAe,QAAQ,IAAI,IAAI,EAAE,GAAG;AACvD,cAAM,gBAAgB,eAAe,CAAC;AACtC,sBAAc;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,UAAI,cAAc;AAChB,gBAAQ,QAAQ;AAAA,MAClB;AAAA,IACF;AACA,YAAQ,cAAc;AACtB,SAAK,QAAQ;AAEb,UAAM,WAAW,SAAS,UAAU;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,cACE,SACA,kBACA,QACA,cACA,uBACA;AACA,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,QAAI,UAAU;AACd,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,kBACE;AAAA,UACE;AAAA,UACA;AAAA,UACA,OAAO,CAAC;AAAA,UACR;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,QACF,KAAK;AAAA,MACT;AAAA,IACF,OAAO;AACL,gBAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,MAAM;AAC1B,UAAM;AAAA;AAAA,MACJ,KAAK,SAAS;AAAA;AAEhB,QAAI,MAAM,cAAc,MAAM,UAAU;AACtC,aAAO;AAAA,IACT;AACA,UAAM,cAAc,KAAK,eAAe,KAAK;AAC7C,UAAM,WAAW,MAAM,YAAY;AACnC,UAAM,aAAa,KAAK;AACxB,WACE,YAAY,2BAA2B,cACvC,YAAY,yBAAyB;AAAA,EAEzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,MAAM,YAAY;AACjC,UAAM;AAAA;AAAA,MACJ,KAAK,SAAS;AAAA;AAEhB,UAAM,cAAc,KAAK,eAAe,KAAK;AAC7C,UAAM,WAAW,MAAM,YAAY;AACnC,UAAM,iBAAiB,KAAK,eAAe,OAAO,KAAK,CAAC;AACxD,gBAAY,uBAAuB;AAEnC,UAAM,YAAY,KAAK;AACvB,UAAM,IAAI,UAAU,CAAC;AACrB,UAAM,SAAS,MAAM,UAAU;AAC/B,QAAI,aAAa,WAAW;AAC5B,UAAM,YAAY,WAAW;AAC7B,UAAM,aAAa,UAAU;AAC7B,UAAM,WAAW,OAAO,yBAAyB,UAAU;AAC3D,UAAM,iBAAiB,SAAS,cAAc,KAAK,UAAU,CAAC,CAAC;AAC/D,UAAM,mBACH,WAAW,aAAa,KAAK,mBAAoB;AACpD,UAAM,aAAa,SAAS,cAAc,CAAC;AAC3C,UAAM,UAAU,KAAK,WAAW,KAAK;AAGrC,iBAAa,KAAK;AAAA,MAChB,KAAK,IAAI,YAAY,mBAAmB,UAAU;AAAA,IACpD;AACA,UAAM,OAAO,OAAO,iBAAiB,GAAG,YAAY,UAAU;AAC9D,YAAQ,OAAO,QAAQ,KAAK,CAAC;AAC7B,YAAQ,OAAO,SAAS,KAAK,CAAC;AAC9B,UAAM,cAAc,aAAa;AACjC,QAAI,gBAAgB,GAAG;AACrB,YAAM,kBAAkB,MAAe,KAAK,aAAa;AACzD,YAAe,iBAAiB,aAAa,WAAW;AACxD,cAAQ,aAAa,MAAM,SAAS,eAAe;AAAA,IACrD;AACA,UAAM,aAAa,SAAS,mBAAmB,WAAW,KAAK,SAAS;AACxE,UAAM,aAAa,mBAAmB;AACtC,UAAM,YAAY,MAAe,KAAK,aAAa;AACnD,UAAe,WAAW,YAAY,CAAC,UAAU;AACjD,cAAmB,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AAC5D,aAAS,IAAI,GAAG,KAAK,eAAe,QAAQ,IAAI,IAAI,EAAE,GAAG;AACvD,YAAM,gBAAgB,eAAe,CAAC;AACtC,oBAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,MAAM,cAAc,CAAC;AAAA,MACrC;AAAA,IACF;AACA,gBAAY,yBAAyB,KAAK;AAAA,EAC5C;AACF;AAEA,IAAO,0BAAQ;;;ACnvBf,IAAM,kBAAN,cAA8B,mBAAgB;AAAA;AAAA;AAAA;AAAA,EAI5C,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM;AAAA;AAAA,MAAqC,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA;AACpE,WAAO,YAAY;AACnB,WAAO,YAAY;AAEnB;AAAA;AAAA,MAEI;AAAA,IAEJ;AAKA,SAAK;AAKL,SAAK;AAKL,SAAK;AAEL,UAAM,aAAa,QAAQ,cAAc;AAEzC,WAAO,cAAc,YAAY,cAAc,UAAU,EAAE;AAM3D,SAAK,cAAc;AAEnB,SAAK,WAAW,QAAQ,UAAU,QAAQ,UAAU,CAAC;AACrD,SAAK;AAAA,MACH,QAAQ,2BAA2B,SAC/B,QAAQ,yBACR;AAAA,IACN;AAOA,SAAK;AAOL,SAAK;AAAA,EACP;AAAA,EAEA,iBAAiB;AACf,WAAO,IAAI,wBAA8B,IAAI;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,YAAY,OAAO;AACjB,WAAO,MAAM,YAAY,KAAK;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa;AACX;AAAA;AAAA,MAA8B,KAAK,IAAI,qBAAa,OAAO;AAAA;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,4BAA4B;AAC1B;AAAA;AAAA,MACE,KAAK,IAAI,qBAAa,0BAA0B;AAAA;AAAA,EAEpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAClB,SAAK,IAAI,qBAAa,SAAS,OAAO;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,0BAA0B,wBAAwB;AAChD,SAAK,IAAI,qBAAa,4BAA4B,sBAAsB;AAAA,EAC1E;AACF;AAEA,IAAO,qBAAQ;", "names": ["executorGroups", "extent"]}