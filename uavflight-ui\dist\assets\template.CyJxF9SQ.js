import{s as e,__tla as g}from"./index.C0-0gsfl.js";let n,a,r,l,u,o,m,s,p=Promise.all([(()=>{try{return g}catch{}})()]).then(async()=>{l=function(t){return e({url:"/gen/template/page",method:"get",params:t})},o=function(){return e({url:"/gen/template/list",method:"get"})},m=function(){return e({url:"/gen/template/online",method:"get"})},a=function(){return e({url:"/gen/template/checkVersion",method:"get"})},n=function(t){return e({url:"/gen/template",method:"post",data:t})},u=function(t){return e({url:"/gen/template/"+t,method:"get"})},r=function(t){return e({url:"/gen/template",method:"delete",data:t})},s=function(t){return e({url:"/gen/template",method:"put",data:t})}});export{p as __tla,n as a,a as c,r as d,l as f,u as g,o as l,m as o,s as p};
