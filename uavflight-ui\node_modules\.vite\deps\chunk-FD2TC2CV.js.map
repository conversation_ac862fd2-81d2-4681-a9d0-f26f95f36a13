{"version": 3, "sources": ["../../ol/events/EventType.js", "../../ol/functions.js"], "sourcesContent": ["/**\n * @module ol/events/EventType\n */\n\n/**\n * @enum {string}\n * @const\n */\nexport default {\n  /**\n   * Generic change event. Triggered when the revision counter is increased.\n   * @event module:ol/events/Event~BaseEvent#change\n   * @api\n   */\n  CHANGE: 'change',\n\n  /**\n   * Generic error event. Triggered when an error occurs.\n   * @event module:ol/events/Event~BaseEvent#error\n   * @api\n   */\n  ERROR: 'error',\n\n  BLUR: 'blur',\n  CLEAR: 'clear',\n  CONTEXTMENU: 'contextmenu',\n  CLICK: 'click',\n  DBLCLICK: 'dblclick',\n  DRAGENTER: 'dragenter',\n  DRAGOVER: 'dragover',\n  DROP: 'drop',\n  FOCUS: 'focus',\n  KEYDOWN: 'keydown',\n  KEYPRESS: 'keypress',\n  LOAD: 'load',\n  RESIZE: 'resize',\n  TOUCHMOVE: 'touchmove',\n  WHEEL: 'wheel',\n};\n", "/**\n * @module ol/functions\n */\n\nimport {equals as arrayEquals} from './array.js';\n\n/**\n * Always returns true.\n * @return {boolean} true.\n */\nexport function TRUE() {\n  return true;\n}\n\n/**\n * Always returns false.\n * @return {boolean} false.\n */\nexport function FALSE() {\n  return false;\n}\n\n/**\n * A reusable function, used e.g. as a default for callbacks.\n *\n * @return {void} Nothing.\n */\nexport function VOID() {}\n\n/**\n * Wrap a function in another function that remembers the last return.  If the\n * returned function is called twice in a row with the same arguments and the same\n * this object, it will return the value from the first call in the second call.\n *\n * @param {function(...any): ReturnType} fn The function to memoize.\n * @return {function(...any): ReturnType} The memoized function.\n * @template ReturnType\n */\nexport function memoizeOne(fn) {\n  let called = false;\n\n  /** @type {ReturnType} */\n  let lastResult;\n\n  /** @type {Array<any>} */\n  let lastArgs;\n\n  let lastThis;\n\n  return function () {\n    const nextArgs = Array.prototype.slice.call(arguments);\n    if (!called || this !== lastThis || !arrayEquals(nextArgs, lastArgs)) {\n      called = true;\n      lastThis = this;\n      lastArgs = nextArgs;\n      lastResult = fn.apply(this, arguments);\n    }\n    return lastResult;\n  };\n}\n\n/**\n * @template T\n * @param {function(): (T | Promise<T>)} getter A function that returns a value or a promise for a value.\n * @return {Promise<T>} A promise for the value.\n */\nexport function toPromise(getter) {\n  function promiseGetter() {\n    let value;\n    try {\n      value = getter();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n    if (value instanceof Promise) {\n      return value;\n    }\n    return Promise.resolve(value);\n  }\n  return promiseGetter();\n}\n"], "mappings": ";;;;;AAQA,IAAO,oBAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOR,OAAO;AAAA,EAEP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,MAAM;AAAA,EACN,OAAO;AAAA,EACP,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,OAAO;AACT;;;AC5BO,SAAS,OAAO;AACrB,SAAO;AACT;AAMO,SAAS,QAAQ;AACtB,SAAO;AACT;AAOO,SAAS,OAAO;AAAC;AAWjB,SAAS,WAAW,IAAI;AAC7B,MAAI,SAAS;AAGb,MAAI;AAGJ,MAAI;AAEJ,MAAI;AAEJ,SAAO,WAAY;AACjB,UAAM,WAAW,MAAM,UAAU,MAAM,KAAK,SAAS;AACrD,QAAI,CAAC,UAAU,SAAS,YAAY,CAAC,OAAY,UAAU,QAAQ,GAAG;AACpE,eAAS;AACT,iBAAW;AACX,iBAAW;AACX,mBAAa,GAAG,MAAM,MAAM,SAAS;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AACF;AAOO,SAAS,UAAU,QAAQ;AAChC,WAAS,gBAAgB;AACvB,QAAI;AACJ,QAAI;AACF,cAAQ,OAAO;AAAA,IACjB,SAAS,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;AAAA,IAC3B;AACA,QAAI,iBAAiB,SAAS;AAC5B,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,QAAQ,KAAK;AAAA,EAC9B;AACA,SAAO,cAAc;AACvB;", "names": []}