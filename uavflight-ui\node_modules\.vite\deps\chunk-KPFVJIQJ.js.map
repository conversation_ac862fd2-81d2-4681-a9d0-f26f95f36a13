{"version": 3, "sources": ["../../ol/math.js"], "sourcesContent": ["/**\n * @module ol/math\n */\n\n/**\n * Takes a number and clamps it to within the provided bounds.\n * @param {number} value The input number.\n * @param {number} min The minimum value to return.\n * @param {number} max The maximum value to return.\n * @return {number} The input number if it is within bounds, or the nearest\n *     number within the bounds.\n */\nexport function clamp(value, min, max) {\n  return Math.min(Math.max(value, min), max);\n}\n\n/**\n * Returns the square of the closest distance between the point (x, y) and the\n * line segment (x1, y1) to (x2, y2).\n * @param {number} x X.\n * @param {number} y Y.\n * @param {number} x1 X1.\n * @param {number} y1 Y1.\n * @param {number} x2 X2.\n * @param {number} y2 Y2.\n * @return {number} Squared distance.\n */\nexport function squaredSegmentDistance(x, y, x1, y1, x2, y2) {\n  const dx = x2 - x1;\n  const dy = y2 - y1;\n  if (dx !== 0 || dy !== 0) {\n    const t = ((x - x1) * dx + (y - y1) * dy) / (dx * dx + dy * dy);\n    if (t > 1) {\n      x1 = x2;\n      y1 = y2;\n    } else if (t > 0) {\n      x1 += dx * t;\n      y1 += dy * t;\n    }\n  }\n  return squaredDistance(x, y, x1, y1);\n}\n\n/**\n * Returns the square of the distance between the points (x1, y1) and (x2, y2).\n * @param {number} x1 X1.\n * @param {number} y1 Y1.\n * @param {number} x2 X2.\n * @param {number} y2 Y2.\n * @return {number} Squared distance.\n */\nexport function squaredDistance(x1, y1, x2, y2) {\n  const dx = x2 - x1;\n  const dy = y2 - y1;\n  return dx * dx + dy * dy;\n}\n\n/**\n * Solves system of linear equations using Gaussian elimination method.\n *\n * @param {Array<Array<number>>} mat Augmented matrix (n x n + 1 column)\n *                                     in row-major order.\n * @return {Array<number>} The resulting vector.\n */\nexport function solveLinearSystem(mat) {\n  const n = mat.length;\n\n  for (let i = 0; i < n; i++) {\n    // Find max in the i-th column (ignoring i - 1 first rows)\n    let maxRow = i;\n    let maxEl = Math.abs(mat[i][i]);\n    for (let r = i + 1; r < n; r++) {\n      const absValue = Math.abs(mat[r][i]);\n      if (absValue > maxEl) {\n        maxEl = absValue;\n        maxRow = r;\n      }\n    }\n\n    if (maxEl === 0) {\n      return null; // matrix is singular\n    }\n\n    // Swap max row with i-th (current) row\n    const tmp = mat[maxRow];\n    mat[maxRow] = mat[i];\n    mat[i] = tmp;\n\n    // Subtract the i-th row to make all the remaining rows 0 in the i-th column\n    for (let j = i + 1; j < n; j++) {\n      const coef = -mat[j][i] / mat[i][i];\n      for (let k = i; k < n + 1; k++) {\n        if (i == k) {\n          mat[j][k] = 0;\n        } else {\n          mat[j][k] += coef * mat[i][k];\n        }\n      }\n    }\n  }\n\n  // Solve Ax=b for upper triangular matrix A (mat)\n  const x = new Array(n);\n  for (let l = n - 1; l >= 0; l--) {\n    x[l] = mat[l][n] / mat[l][l];\n    for (let m = l - 1; m >= 0; m--) {\n      mat[m][n] -= mat[m][l] * x[l];\n    }\n  }\n  return x;\n}\n\n/**\n * Converts radians to to degrees.\n *\n * @param {number} angleInRadians Angle in radians.\n * @return {number} Angle in degrees.\n */\nexport function toDegrees(angleInRadians) {\n  return (angleInRadians * 180) / Math.PI;\n}\n\n/**\n * Converts degrees to radians.\n *\n * @param {number} angleInDegrees Angle in degrees.\n * @return {number} Angle in radians.\n */\nexport function toRadians(angleInDegrees) {\n  return (angleInDegrees * Math.PI) / 180;\n}\n\n/**\n * Returns the modulo of a / b, depending on the sign of b.\n *\n * @param {number} a Dividend.\n * @param {number} b Divisor.\n * @return {number} Modulo.\n */\nexport function modulo(a, b) {\n  const r = a % b;\n  return r * b < 0 ? r + b : r;\n}\n\n/**\n * Calculates the linearly interpolated value of x between a and b.\n *\n * @param {number} a Number\n * @param {number} b Number\n * @param {number} x Value to be interpolated.\n * @return {number} Interpolated value.\n */\nexport function lerp(a, b, x) {\n  return a + x * (b - a);\n}\n\n/**\n * Returns a number with a limited number of decimal digits.\n * @param {number} n The input number.\n * @param {number} decimals The maximum number of decimal digits.\n * @return {number} The input number with a limited number of decimal digits.\n */\nexport function toFixed(n, decimals) {\n  const factor = Math.pow(10, decimals);\n  return Math.round(n * factor) / factor;\n}\n\n/**\n * Rounds a number to the nearest integer value considering only the given number\n * of decimal digits (with rounding on the final digit).\n * @param {number} n The input number.\n * @param {number} decimals The maximum number of decimal digits.\n * @return {number} The nearest integer.\n */\nexport function round(n, decimals) {\n  return Math.round(toFixed(n, decimals));\n}\n\n/**\n * Rounds a number to the next smaller integer considering only the given number\n * of decimal digits (with rounding on the final digit).\n * @param {number} n The input number.\n * @param {number} decimals The maximum number of decimal digits.\n * @return {number} The next smaller integer.\n */\nexport function floor(n, decimals) {\n  return Math.floor(toFixed(n, decimals));\n}\n\n/**\n * Rounds a number to the next bigger integer considering only the given number\n * of decimal digits (with rounding on the final digit).\n * @param {number} n The input number.\n * @param {number} decimals The maximum number of decimal digits.\n * @return {number} The next bigger integer.\n */\nexport function ceil(n, decimals) {\n  return Math.ceil(toFixed(n, decimals));\n}\n"], "mappings": ";AAYO,SAAS,MAAM,OAAO,KAAK,KAAK;AACrC,SAAO,KAAK,IAAI,KAAK,IAAI,OAAO,GAAG,GAAG,GAAG;AAC3C;AAaO,SAAS,uBAAuB,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI;AAC3D,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,MAAI,OAAO,KAAK,OAAO,GAAG;AACxB,UAAM,MAAM,IAAI,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK,KAAK,KAAK;AAC5D,QAAI,IAAI,GAAG;AACT,WAAK;AACL,WAAK;AAAA,IACP,WAAW,IAAI,GAAG;AAChB,YAAM,KAAK;AACX,YAAM,KAAK;AAAA,IACb;AAAA,EACF;AACA,SAAO,gBAAgB,GAAG,GAAG,IAAI,EAAE;AACrC;AAUO,SAAS,gBAAgB,IAAI,IAAI,IAAI,IAAI;AAC9C,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,SAAO,KAAK,KAAK,KAAK;AACxB;AASO,SAAS,kBAAkB,KAAK;AACrC,QAAM,IAAI,IAAI;AAEd,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAE1B,QAAI,SAAS;AACb,QAAI,QAAQ,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AAC9B,aAAS,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK;AAC9B,YAAM,WAAW,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AACnC,UAAI,WAAW,OAAO;AACpB,gBAAQ;AACR,iBAAS;AAAA,MACX;AAAA,IACF;AAEA,QAAI,UAAU,GAAG;AACf,aAAO;AAAA,IACT;AAGA,UAAM,MAAM,IAAI,MAAM;AACtB,QAAI,MAAM,IAAI,IAAI,CAAC;AACnB,QAAI,CAAC,IAAI;AAGT,aAAS,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK;AAC9B,YAAM,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC;AAClC,eAAS,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AAC9B,YAAI,KAAK,GAAG;AACV,cAAI,CAAC,EAAE,CAAC,IAAI;AAAA,QACd,OAAO;AACL,cAAI,CAAC,EAAE,CAAC,KAAK,OAAO,IAAI,CAAC,EAAE,CAAC;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,QAAM,IAAI,IAAI,MAAM,CAAC;AACrB,WAAS,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK;AAC/B,MAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC;AAC3B,aAAS,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK;AAC/B,UAAI,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC9B;AAAA,EACF;AACA,SAAO;AACT;AAQO,SAAS,UAAU,gBAAgB;AACxC,SAAQ,iBAAiB,MAAO,KAAK;AACvC;AAQO,SAAS,UAAU,gBAAgB;AACxC,SAAQ,iBAAiB,KAAK,KAAM;AACtC;AASO,SAAS,OAAO,GAAG,GAAG;AAC3B,QAAM,IAAI,IAAI;AACd,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAC7B;AAUO,SAAS,KAAK,GAAG,GAAG,GAAG;AAC5B,SAAO,IAAI,KAAK,IAAI;AACtB;AAQO,SAAS,QAAQ,GAAG,UAAU;AACnC,QAAM,SAAS,KAAK,IAAI,IAAI,QAAQ;AACpC,SAAO,KAAK,MAAM,IAAI,MAAM,IAAI;AAClC;AASO,SAAS,MAAM,GAAG,UAAU;AACjC,SAAO,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC;AACxC;AASO,SAAS,MAAM,GAAG,UAAU;AACjC,SAAO,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC;AACxC;AASO,SAAS,KAAK,GAAG,UAAU;AAChC,SAAO,KAAK,KAAK,QAAQ,GAAG,QAAQ,CAAC;AACvC;", "names": []}