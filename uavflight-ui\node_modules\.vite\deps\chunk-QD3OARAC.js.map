{"version": 3, "sources": ["../../ol/css.js"], "sourcesContent": ["/**\n * @module ol/css\n */\n\n/**\n * @typedef {Object} FontParameters\n * @property {string} style Style.\n * @property {string} variant Variant.\n * @property {string} weight Weight.\n * @property {string} size Size.\n * @property {string} lineHeight LineHeight.\n * @property {string} family Family.\n * @property {Array<string>} families Families.\n */\n\n/**\n * The CSS class for hidden feature.\n *\n * @const\n * @type {string}\n */\nexport const CLASS_HIDDEN = 'ol-hidden';\n\n/**\n * The CSS class that we'll give the DOM elements to have them selectable.\n *\n * @const\n * @type {string}\n */\nexport const CLASS_SELECTABLE = 'ol-selectable';\n\n/**\n * The CSS class that we'll give the DOM elements to have them unselectable.\n *\n * @const\n * @type {string}\n */\nexport const CLASS_UNSELECTABLE = 'ol-unselectable';\n\n/**\n * The CSS class for unsupported feature.\n *\n * @const\n * @type {string}\n */\nexport const CLASS_UNSUPPORTED = 'ol-unsupported';\n\n/**\n * The CSS class for controls.\n *\n * @const\n * @type {string}\n */\nexport const CLASS_CONTROL = 'ol-control';\n\n/**\n * The CSS class that we'll give the DOM elements that are collapsed, i.e.\n * to those elements which usually can be expanded.\n *\n * @const\n * @type {string}\n */\nexport const CLASS_COLLAPSED = 'ol-collapsed';\n\n/**\n * From https://stackoverflow.com/questions/10135697/regex-to-parse-any-css-font\n * @type {RegExp}\n */\nconst fontRegEx = new RegExp(\n  [\n    '^\\\\s*(?=(?:(?:[-a-z]+\\\\s*){0,2}(italic|oblique))?)',\n    '(?=(?:(?:[-a-z]+\\\\s*){0,2}(small-caps))?)',\n    '(?=(?:(?:[-a-z]+\\\\s*){0,2}(bold(?:er)?|lighter|[1-9]00 ))?)',\n    '(?:(?:normal|\\\\1|\\\\2|\\\\3)\\\\s*){0,3}((?:xx?-)?',\n    '(?:small|large)|medium|smaller|larger|[\\\\.\\\\d]+(?:\\\\%|in|[cem]m|ex|p[ctx]))',\n    '(?:\\\\s*\\\\/\\\\s*(normal|[\\\\.\\\\d]+(?:\\\\%|in|[cem]m|ex|p[ctx])?))',\n    '?\\\\s*([-,\\\\\"\\\\\\'\\\\sa-z]+?)\\\\s*$',\n  ].join(''),\n  'i'\n);\nconst fontRegExMatchIndex = [\n  'style',\n  'variant',\n  'weight',\n  'size',\n  'lineHeight',\n  'family',\n];\n\n/**\n * Get the list of font families from a font spec.  Note that this doesn't work\n * for font families that have commas in them.\n * @param {string} fontSpec The CSS font property.\n * @return {FontParameters|null} The font parameters (or null if the input spec is invalid).\n */\nexport const getFontParameters = function (fontSpec) {\n  const match = fontSpec.match(fontRegEx);\n  if (!match) {\n    return null;\n  }\n  const style = /** @type {FontParameters} */ ({\n    lineHeight: 'normal',\n    size: '1.2em',\n    style: 'normal',\n    weight: 'normal',\n    variant: 'normal',\n  });\n  for (let i = 0, ii = fontRegExMatchIndex.length; i < ii; ++i) {\n    const value = match[i + 1];\n    if (value !== undefined) {\n      style[fontRegExMatchIndex[i]] = value;\n    }\n  }\n  style.families = style.family.split(/,\\s?/);\n  return style;\n};\n"], "mappings": ";AAqBO,IAAM,eAAe;AAQrB,IAAM,mBAAmB;AAQzB,IAAM,qBAAqB;AAQ3B,IAAM,oBAAoB;AAQ1B,IAAM,gBAAgB;AAStB,IAAM,kBAAkB;AAM/B,IAAM,YAAY,IAAI;AAAA,EACpB;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,EAAE,KAAK,EAAE;AAAA,EACT;AACF;AACA,IAAM,sBAAsB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAQO,IAAM,oBAAoB,SAAU,UAAU;AACnD,QAAM,QAAQ,SAAS,MAAM,SAAS;AACtC,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,QAAM;AAAA;AAAA,IAAuC;AAAA,MAC3C,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,IACX;AAAA;AACA,WAAS,IAAI,GAAG,KAAK,oBAAoB,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC5D,UAAM,QAAQ,MAAM,IAAI,CAAC;AACzB,QAAI,UAAU,QAAW;AACvB,YAAM,oBAAoB,CAAC,CAAC,IAAI;AAAA,IAClC;AAAA,EACF;AACA,QAAM,WAAW,MAAM,OAAO,MAAM,MAAM;AAC1C,SAAO;AACT;", "names": []}