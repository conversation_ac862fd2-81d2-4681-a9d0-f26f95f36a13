import * as Cesium from 'cesium';
import { VideoControllerData } from './types';
import TerrainSampler from '../../utils/cesium/TerrainSampler';

export default class VideoControler {
	private static _instance: VideoControler | null = null;
	private degreePath: VideoControllerData[] = []; // 存储每一帧的无人机数据
	private _currentFrameIdx: number = 0; // 当前帧索引
	private _frameRate: number = 30; // 视频帧率，每秒30帧
	private player: any = null; // 视频播放组件
	private UAVEntity: any = null; // 无人机实体
	private pathPointEntities: any = null; // 关键点实体
	private totalFrames: number = 0; // 总帧数
	private heading: number = 0;
	private interval: number | null = null;
	private duration: number = 0;
	private viewer: any = null; // Cesium viewer属性
	private animationFrameId: number | null = null; // 存储requestAnimationFrame的ID
	private isPlaying: boolean = false; // 标记是否正在播放
	private boundVideoTimeUpdateHandler: any = null; // 保存绑定后的事件处理函数

	// 地形高度采样器
	private terrainSampler: TerrainSampler = new TerrainSampler();

	constructor(degreePath: VideoControllerData[]) {
		if (!VideoControler._instance) {
			if (degreePath) {
				this.degreePath = degreePath;
				this.totalFrames = degreePath.length;
			}
			// 创建绑定的事件处理程序
			this.boundVideoTimeUpdateHandler = this.handleVideoTimeUpdate.bind(this);
			VideoControler._instance = this;
		}
		return VideoControler._instance;
	}

	/**
	 * 初始化三维对象
	 * @param {Object} obj 包含UAV实体和关键点实体的对象
	 */
	initObject(obj: { UAVEntity: any; pathPointEntities: any; viewer?: any }) {
		const { UAVEntity, pathPointEntities, viewer } = obj;
		if (!UAVEntity || !pathPointEntities) throw new Error('缺少属性：UAVEntity或pathPointEntities');
		this.UAVEntity = UAVEntity;
		this.pathPointEntities = pathPointEntities;
		if (viewer) {
			this.viewer = viewer;
			this.terrainSampler.setViewer(viewer); // 设置地形采样器的viewer
		}
		this.moveTo(0);
	}

	/**
	 * 初始化视频播放组件
	 * @param {HTMLVideoElement} player 视频播放组件
	 */
	initVideoPlayer(player: HTMLVideoElement) {
		this.player = player;
		this.duration = this.player.duration;
		
		// 为视频添加timeupdate事件监听器，实现视频和无人机位置的同步
		this.player.addEventListener('timeupdate', this.boundVideoTimeUpdateHandler);
	}

	/**
	 * 处理视频时间更新事件
	 */
	private handleVideoTimeUpdate() {
		// 由于使用requestAnimationFrame，这个方法不再需要更新无人机位置
		// 保留此方法用于视频时间同步或UI更新
	}

	/**
	 * 设置当前帧索引
	 */
	set currentFrameIdx(val: number) {
		if (this.UAVEntity) this.moveTo(val);
	}

	/**
	 * 开始播放无人机轨迹与视频同步
	 */
	start() {
		this.pause();
		this.isPlaying = true;
		
		// 使用视频事件驱动无人机位置更新，不再需要setInterval
		if (this.player) {
			this.player.play();
			// 使用requestAnimationFrame启动动画循环
			this.animationLoop();
		}
	}

	/**
	 * 动画循环，每帧更新无人机位置
	 */
	private animationLoop() {
		if (!this.isPlaying) return;
		
		// 更新无人机位置
		if (this.player) {
			const currentTime = this.player.currentTime;
			const frameIdx = Math.floor(currentTime * this._frameRate);
			
			if (frameIdx < this.totalFrames) {
				// 只有在帧索引变化时才更新当前帧索引
				if (this._currentFrameIdx !== frameIdx) {
					this._currentFrameIdx = frameIdx;
					this.updateUAV(frameIdx);
				}
			}
		}
		
		// 请求下一帧动画
		this.animationFrameId = requestAnimationFrame(() => this.animationLoop());
	}

	/**
	 * 暂停播放
	 */
	pause() {
		this.isPlaying = false;
		
		// 取消任何进行中的动画帧
		if (this.animationFrameId !== null) {
			cancelAnimationFrame(this.animationFrameId);
			this.animationFrameId = null;
		}
		
		// 取消任何进行中的间隔
		if (this.interval) {
			window.clearInterval(this.interval);
			this.interval = null;
		}
		
		// 暂停视频
		if (this.player) {
			this.player.pause();
		}
	}

	/**
	 * 移动到指定帧
	 * @param {number} idx 目标帧索引
	 */
	moveTo(idx: number) {
		// 添加调试信息
		console.log('VideoControler.moveTo 被调用:', { 
			idx: idx,
			totalFrames: this.totalFrames,
			degreePath长度: this.degreePath.length
		});
		
		// 确保索引在有效范围内
		if (idx < 0) {
			console.error('帧索引小于0:', idx);
			idx = 0;
		} else if (idx >= this.totalFrames) {
			console.error('帧索引超出总帧数:', idx, '总帧数:', this.totalFrames);
			idx = this.totalFrames - 1;
		}
		
		this._currentFrameIdx = idx;
		
		// 打印当前帧数据
		const frameData = this.degreePath[idx];
		if (frameData) {
			console.log('当前帧数据:', {
				lng: frameData.lng,
				lat: frameData.lat,
				gimbalYaw: frameData.gimbalYaw
			});
		} else {
			console.error('找不到对应帧数据:', idx);
		}
		
		this.updateUAV(idx);

		// 计算对应的视频时间
		if (this.player) {
			// 临时移除timeupdate事件监听器，防止循环触发
			this.player.removeEventListener('timeupdate', this.boundVideoTimeUpdateHandler);
			
			// 计算视频时间并添加调试信息
			const videoTime = idx / this._frameRate;
			console.log('设置视频时间:', {
				idx: idx,
				videoTime: videoTime,
				frameRate: this._frameRate,
				videoDuration: this.player.duration
			});
			
			// 将视频跳转到对应的时间
			this.player.currentTime = videoTime;
			
			// 300毫秒后重新添加事件监听器，给足够时间完成跳转
			setTimeout(() => {
				this.player.addEventListener('timeupdate', this.boundVideoTimeUpdateHandler);
			}, 300);
		}
	}

	moveToByTime(time: number) {
		let idx = Math.floor(time * this._frameRate);
		if (idx < 0 || idx >= this.totalFrames) return;
		this._currentFrameIdx = idx;
		this.updateUAV(idx);

		// 需要设置视频时间，因为该方法是从UI控件调用的
		if (this.player) {
			// 临时移除timeupdate事件监听器，防止循环触发
			this.player.removeEventListener('timeupdate', this.boundVideoTimeUpdateHandler);
			
			// 设置视频时间
			this.player.currentTime = time;
			
			// 100毫秒后重新添加事件监听器
			setTimeout(() => {
				this.player.addEventListener('timeupdate', this.boundVideoTimeUpdateHandler);
			}, 100);
		}
	}

	/**
	 * 设置Cesium viewer实例
	 * @param viewer Cesium.Viewer实例
	 */
	setViewer(viewer: any) {
		this.viewer = viewer;
		this.terrainSampler.setViewer(viewer); // 同时设置地形采样器的viewer
	}

	/**
	 * 更新无人机位置和姿态
	 * @param {number} idx 当前帧索引
	 */
	private updateUAV(idx: number) {
		const frameData = this.degreePath[idx];
		if (!frameData) return;

		// 直接使用保存的viewer实例
		const viewer = this.viewer;
		if (!viewer || !viewer.terrainProvider) {
			// 如果没有viewer或terrainProvider，则使用固定高度
			this.updateUAVWithFixedHeight(frameData, idx);
			return;
		}

		// 异步更新无人机位置
		(async () => {
			try {
				// 使用地形采样器获取地形高度
				const rawTerrainHeight = await this.terrainSampler.getTerrainHeightWithCache(idx, frameData.lng, frameData.lat);

				if (this.UAVEntity) {
					// 计算无人机位置
					let position;
					if (idx == 0) {
						position = Cesium.Cartesian3.fromDegrees(frameData.lng, frameData.lat, Number(rawTerrainHeight) + 100);
						this.UAVEntity.position = position;
					} else {
						// 应用平滑过渡
						const smoothedTerrainHeight = this.terrainSampler.smoothHeight(rawTerrainHeight);
						
						// 设置位置（平滑过渡后的地形高度 + 100米）
						position = Cesium.Cartesian3.fromDegrees(frameData.lng, frameData.lat, Number(smoothedTerrainHeight) + 100);
						this.UAVEntity.position = position;
					}

					// 设置朝向
					let heading = Cesium.Math.toRadians(frameData.gimbalYaw + 90);
					let hpr = new Cesium.HeadingPitchRoll(heading, 0, 0);
					let orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr);
					this.UAVEntity.orientation = orientation;
				}
			} catch (error) {
				// 如果获取地形高度出错，则使用固定高度
				this.updateUAVWithFixedHeight(frameData, idx);
			}
		})();
	}

	/**
	 * 使用固定高度更新无人机位置（作为备选方案）
	 * @param frameData 当前帧数据
	 * @param idx 当前帧索引
	 */
	private updateUAVWithFixedHeight(frameData: VideoControllerData, idx: number) {
		// 设置位置（固定高度100米）
		const position = Cesium.Cartesian3.fromDegrees(frameData.lng, frameData.lat, 200);
		if (this.UAVEntity) {
			this.UAVEntity.position = position;
			let nextPoistion = null;
			if (idx + 1 != this.degreePath.length) {
				nextPoistion = Cesium.Cartesian3.fromDegrees(this.degreePath[idx + 1].lng, this.degreePath[idx + 1].lat, 200);
			} else {
				nextPoistion = position;
			}

			let heading = Cesium.Math.toRadians(frameData.gimbalYaw + 90);
			let hpr = new Cesium.HeadingPitchRoll(heading, 0, 0);
			let orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr);
			this.UAVEntity.orientation = orientation;
		}
	}

	/**
	 * 根据给定的时间跳转到相应的帧
	 * @param {string} timeStr 时间字符串，格式为 "HH:mm:ss,SSS"
	 */
	jumpToTime(timeStr: string) {
		const timeParts = timeStr.split(',');
		const time = timeParts[0].split(':');
		const milliseconds = parseInt(timeParts[1]);

		const hours = parseInt(time[0]);
		const minutes = parseInt(time[1]);
		const seconds = parseInt(time[2]);

		// 计算总秒数
		const totalSeconds = hours * 3600 + minutes * 60 + seconds;
		const totalMilliseconds = totalSeconds * 1000 + milliseconds;

		// 计算目标帧索引
		const idx = Math.round((totalMilliseconds / 1000) * this._frameRate);

		// 限制索引范围
		if (idx < 0) return;
		const targetFrameIdx = idx >= this.totalFrames ? this.totalFrames - 1 : idx;

		// 跳转到对应的帧
		this.moveTo(targetFrameIdx);
	}

	/**
	 * 跳转到关键点位置
	 * @param {number} idx 关键点对应的帧索引
	 */
	jumpToKeyPoint(idx: number) {
		console.log('jumpToKeyPoint 被调用:', { idx });
		
		// 不暂停视频，只跳转到指定帧
		// this.pause();
		
		// 使用moveTo方法跳转到指定帧
		this.moveTo(idx);
		
		// 如果视频正在播放，则继续播放
		if (this.isPlaying && this.player) {
			// 确保视频继续播放
			this.player.play();
		}
	}

	/**
	 * 清理实例并允许创建新实例
	 */
	cleanup() {
		// 停止所有正在进行的动画或定时器
		this.pause();
		
		// 移除视频事件监听器
		if (this.player) {
			this.player.removeEventListener('timeupdate', this.boundVideoTimeUpdateHandler);
		}

		// 重置所有实例变量到初始状态
		this.degreePath = [];
		this._currentFrameIdx = 0;
		this.totalFrames = 0;
		this.heading = 0;
		this.duration = 0;
		this.isPlaying = false;
		this.animationFrameId = null;
		this.boundVideoTimeUpdateHandler = null;

		// 清理视频相关
		this.player = null;

		// 清理实体引用
		this.UAVEntity = null;
		this.pathPointEntities = null;

		// 清理地形采样器
		this.terrainSampler.cleanup();

		// 清理单例引用，允许创建新实例
		VideoControler._instance = null;
	}
}
