{"version": 3, "sources": ["../../ol/source/TileImage.js"], "sourcesContent": ["/**\n * @module ol/source/TileImage\n */\nimport EventType from '../events/EventType.js';\nimport ImageTile from '../ImageTile.js';\nimport ReprojTile from '../reproj/Tile.js';\nimport TileCache from '../TileCache.js';\nimport TileState from '../TileState.js';\nimport UrlTile from './UrlTile.js';\nimport {equivalent, get as getProjection} from '../proj.js';\nimport {getKey, getKeyZXY} from '../tilecoord.js';\nimport {getForProjection as getTileGridForProjection} from '../tilegrid.js';\nimport {getUid} from '../util.js';\n\n/**\n * @typedef {Object} Options\n * @property {import(\"./Source.js\").AttributionLike} [attributions] Attributions.\n * @property {boolean} [attributionsCollapsible=true] Attributions are collapsible.\n * @property {number} [cacheSize] Initial tile cache size. Will auto-grow to hold at least the number of tiles in the viewport.\n * @property {null|string} [crossOrigin] The `crossOrigin` attribute for loaded images.  Note that\n * you must provide a `crossOrigin` value if you want to access pixel data with the Canvas renderer.\n * See https://developer.mozilla.org/en-US/docs/Web/HTML/CORS_enabled_image for more detail.\n * @property {boolean} [interpolate=true] Use interpolated values when resampling.  By default,\n * linear interpolation is used when resampling.  Set to false to use the nearest neighbor instead.\n * @property {boolean} [opaque=false] Whether the layer is opaque.\n * @property {import(\"../proj.js\").ProjectionLike} [projection] Projection. Default is the view projection.\n * @property {number} [reprojectionErrorThreshold=0.5] Maximum allowed reprojection error (in pixels).\n * Higher values can increase reprojection performance, but decrease precision.\n * @property {import(\"./Source.js\").State} [state] Source state.\n * @property {typeof import(\"../ImageTile.js\").default} [tileClass] Class used to instantiate image tiles.\n * Default is {@link module:ol/ImageTile~ImageTile}.\n * @property {import(\"../tilegrid/TileGrid.js\").default} [tileGrid] Tile grid.\n * @property {import(\"../Tile.js\").LoadFunction} [tileLoadFunction] Optional function to load a tile given a URL. The default is\n * ```js\n * function(imageTile, src) {\n *   imageTile.getImage().src = src;\n * };\n * ```\n * @property {number} [tilePixelRatio=1] The pixel ratio used by the tile service. For example, if the tile\n * service advertizes 256px by 256px tiles but actually sends 512px\n * by 512px images (for retina/hidpi devices) then `tilePixelRatio`\n * should be set to `2`.\n * @property {import(\"../Tile.js\").UrlFunction} [tileUrlFunction] Optional function to get tile URL given a tile coordinate and the projection.\n * @property {string} [url] URL template. Must include `{x}`, `{y}` or `{-y}`, and `{z}` placeholders.\n * A `{?-?}` template pattern, for example `subdomain{a-f}.domain.com`, may be\n * used instead of defining each one separately in the `urls` option.\n * @property {Array<string>} [urls] An array of URL templates.\n * @property {boolean} [wrapX] Whether to wrap the world horizontally. The default, is to\n * request out-of-bounds tiles from the server. When set to `false`, only one\n * world will be rendered. When set to `true`, tiles will be requested for one\n * world only, but they will be wrapped horizontally to render multiple worlds.\n * @property {number} [transition] Duration of the opacity transition for rendering.\n * To disable the opacity transition, pass `transition: 0`.\n * @property {string} [key] Optional tile key for proper cache fetching\n * @property {number|import(\"../array.js\").NearestDirectionFunction} [zDirection=0]\n * Choose whether to use tiles with a higher or lower zoom level when between integer\n * zoom levels. See {@link module:ol/tilegrid/TileGrid~TileGrid#getZForResolution}.\n */\n\n/**\n * @classdesc\n * Base class for sources providing images divided into a tile grid.\n *\n * @fires import(\"./Tile.js\").TileSourceEvent\n * @api\n */\nclass TileImage extends UrlTile {\n  /**\n   * @param {!Options} options Image tile options.\n   */\n  constructor(options) {\n    super({\n      attributions: options.attributions,\n      cacheSize: options.cacheSize,\n      opaque: options.opaque,\n      projection: options.projection,\n      state: options.state,\n      tileGrid: options.tileGrid,\n      tileLoadFunction: options.tileLoadFunction\n        ? options.tileLoadFunction\n        : defaultTileLoadFunction,\n      tilePixelRatio: options.tilePixelRatio,\n      tileUrlFunction: options.tileUrlFunction,\n      url: options.url,\n      urls: options.urls,\n      wrapX: options.wrapX,\n      transition: options.transition,\n      interpolate:\n        options.interpolate !== undefined ? options.interpolate : true,\n      key: options.key,\n      attributionsCollapsible: options.attributionsCollapsible,\n      zDirection: options.zDirection,\n    });\n\n    /**\n     * @protected\n     * @type {?string}\n     */\n    this.crossOrigin =\n      options.crossOrigin !== undefined ? options.crossOrigin : null;\n\n    /**\n     * @protected\n     * @type {typeof ImageTile}\n     */\n    this.tileClass =\n      options.tileClass !== undefined ? options.tileClass : ImageTile;\n\n    /**\n     * @protected\n     * @type {!Object<string, TileCache>}\n     */\n    this.tileCacheForProjection = {};\n\n    /**\n     * @protected\n     * @type {!Object<string, import(\"../tilegrid/TileGrid.js\").default>}\n     */\n    this.tileGridForProjection = {};\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.reprojectionErrorThreshold_ = options.reprojectionErrorThreshold;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.renderReprojectionEdges_ = false;\n  }\n\n  /**\n   * @return {boolean} Can expire cache.\n   */\n  canExpireCache() {\n    if (this.tileCache.canExpireCache()) {\n      return true;\n    }\n    for (const key in this.tileCacheForProjection) {\n      if (this.tileCacheForProjection[key].canExpireCache()) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  /**\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @param {!Object<string, boolean>} usedTiles Used tiles.\n   */\n  expireCache(projection, usedTiles) {\n    const usedTileCache = this.getTileCacheForProjection(projection);\n\n    this.tileCache.expireCache(\n      this.tileCache == usedTileCache ? usedTiles : {}\n    );\n    for (const id in this.tileCacheForProjection) {\n      const tileCache = this.tileCacheForProjection[id];\n      tileCache.expireCache(tileCache == usedTileCache ? usedTiles : {});\n    }\n  }\n\n  /**\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {number} Gutter.\n   */\n  getGutterForProjection(projection) {\n    if (\n      this.getProjection() &&\n      projection &&\n      !equivalent(this.getProjection(), projection)\n    ) {\n      return 0;\n    }\n    return this.getGutter();\n  }\n\n  /**\n   * @return {number} Gutter.\n   */\n  getGutter() {\n    return 0;\n  }\n\n  /**\n   * Return the key to be used for all tiles in the source.\n   * @return {string} The key for all tiles.\n   */\n  getKey() {\n    let key = super.getKey();\n    if (!this.getInterpolate()) {\n      key += ':disable-interpolation';\n    }\n    return key;\n  }\n\n  /**\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {boolean} Opaque.\n   */\n  getOpaque(projection) {\n    if (\n      this.getProjection() &&\n      projection &&\n      !equivalent(this.getProjection(), projection)\n    ) {\n      return false;\n    }\n    return super.getOpaque(projection);\n  }\n\n  /**\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {!import(\"../tilegrid/TileGrid.js\").default} Tile grid.\n   */\n  getTileGridForProjection(projection) {\n    const thisProj = this.getProjection();\n    if (this.tileGrid && (!thisProj || equivalent(thisProj, projection))) {\n      return this.tileGrid;\n    }\n    const projKey = getUid(projection);\n    if (!(projKey in this.tileGridForProjection)) {\n      this.tileGridForProjection[projKey] =\n        getTileGridForProjection(projection);\n    }\n    return this.tileGridForProjection[projKey];\n  }\n\n  /**\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {import(\"../TileCache.js\").default} Tile cache.\n   */\n  getTileCacheForProjection(projection) {\n    const thisProj = this.getProjection();\n    if (!thisProj || equivalent(thisProj, projection)) {\n      return this.tileCache;\n    }\n    const projKey = getUid(projection);\n    if (!(projKey in this.tileCacheForProjection)) {\n      this.tileCacheForProjection[projKey] = new TileCache(\n        this.tileCache.highWaterMark\n      );\n    }\n    return this.tileCacheForProjection[projKey];\n  }\n\n  /**\n   * @param {number} z Tile coordinate z.\n   * @param {number} x Tile coordinate x.\n   * @param {number} y Tile coordinate y.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @param {string} key The key set on the tile.\n   * @return {!ImageTile} Tile.\n   * @private\n   */\n  createTile_(z, x, y, pixelRatio, projection, key) {\n    const tileCoord = [z, x, y];\n    const urlTileCoord = this.getTileCoordForTileUrlFunction(\n      tileCoord,\n      projection\n    );\n    const tileUrl = urlTileCoord\n      ? this.tileUrlFunction(urlTileCoord, pixelRatio, projection)\n      : undefined;\n    const tile = new this.tileClass(\n      tileCoord,\n      tileUrl !== undefined ? TileState.IDLE : TileState.EMPTY,\n      tileUrl !== undefined ? tileUrl : '',\n      this.crossOrigin,\n      this.tileLoadFunction,\n      this.tileOptions\n    );\n    tile.key = key;\n    tile.addEventListener(EventType.CHANGE, this.handleTileChange.bind(this));\n    return tile;\n  }\n\n  /**\n   * @param {number} z Tile coordinate z.\n   * @param {number} x Tile coordinate x.\n   * @param {number} y Tile coordinate y.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {!(ImageTile|ReprojTile)} Tile.\n   */\n  getTile(z, x, y, pixelRatio, projection) {\n    const sourceProjection = this.getProjection();\n    if (\n      !sourceProjection ||\n      !projection ||\n      equivalent(sourceProjection, projection)\n    ) {\n      return this.getTileInternal(\n        z,\n        x,\n        y,\n        pixelRatio,\n        sourceProjection || projection\n      );\n    }\n    const cache = this.getTileCacheForProjection(projection);\n    const tileCoord = [z, x, y];\n    let tile;\n    const tileCoordKey = getKey(tileCoord);\n    if (cache.containsKey(tileCoordKey)) {\n      tile = cache.get(tileCoordKey);\n    }\n    const key = this.getKey();\n    if (tile && tile.key == key) {\n      return tile;\n    }\n    const sourceTileGrid = this.getTileGridForProjection(sourceProjection);\n    const targetTileGrid = this.getTileGridForProjection(projection);\n    const wrappedTileCoord = this.getTileCoordForTileUrlFunction(\n      tileCoord,\n      projection\n    );\n    const newTile = new ReprojTile(\n      sourceProjection,\n      sourceTileGrid,\n      projection,\n      targetTileGrid,\n      tileCoord,\n      wrappedTileCoord,\n      this.getTilePixelRatio(pixelRatio),\n      this.getGutter(),\n      (z, x, y, pixelRatio) =>\n        this.getTileInternal(z, x, y, pixelRatio, sourceProjection),\n      this.reprojectionErrorThreshold_,\n      this.renderReprojectionEdges_,\n      this.getInterpolate()\n    );\n    newTile.key = key;\n\n    if (tile) {\n      newTile.interimTile = tile;\n      newTile.refreshInterimChain();\n      cache.replace(tileCoordKey, newTile);\n    } else {\n      cache.set(tileCoordKey, newTile);\n    }\n    return newTile;\n  }\n\n  /**\n   * @param {number} z Tile coordinate z.\n   * @param {number} x Tile coordinate x.\n   * @param {number} y Tile coordinate y.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {!import(\"../proj/Projection.js\").default} projection Projection.\n   * @return {!ImageTile} Tile.\n   * @protected\n   */\n  getTileInternal(z, x, y, pixelRatio, projection) {\n    let tile = null;\n    const tileCoordKey = getKeyZXY(z, x, y);\n    const key = this.getKey();\n    if (!this.tileCache.containsKey(tileCoordKey)) {\n      tile = this.createTile_(z, x, y, pixelRatio, projection, key);\n      this.tileCache.set(tileCoordKey, tile);\n    } else {\n      tile = this.tileCache.get(tileCoordKey);\n      if (tile.key != key) {\n        // The source's params changed. If the tile has an interim tile and if we\n        // can use it then we use it. Otherwise we create a new tile.  In both\n        // cases we attempt to assign an interim tile to the new tile.\n        const interimTile = tile;\n        tile = this.createTile_(z, x, y, pixelRatio, projection, key);\n\n        //make the new tile the head of the list,\n        if (interimTile.getState() == TileState.IDLE) {\n          //the old tile hasn't begun loading yet, and is now outdated, so we can simply discard it\n          tile.interimTile = interimTile.interimTile;\n        } else {\n          tile.interimTile = interimTile;\n        }\n        tile.refreshInterimChain();\n        this.tileCache.replace(tileCoordKey, tile);\n      }\n    }\n    return tile;\n  }\n\n  /**\n   * Sets whether to render reprojection edges or not (usually for debugging).\n   * @param {boolean} render Render the edges.\n   * @api\n   */\n  setRenderReprojectionEdges(render) {\n    if (this.renderReprojectionEdges_ == render) {\n      return;\n    }\n    this.renderReprojectionEdges_ = render;\n    for (const id in this.tileCacheForProjection) {\n      this.tileCacheForProjection[id].clear();\n    }\n    this.changed();\n  }\n\n  /**\n   * Sets the tile grid to use when reprojecting the tiles to the given\n   * projection instead of the default tile grid for the projection.\n   *\n   * This can be useful when the default tile grid cannot be created\n   * (e.g. projection has no extent defined) or\n   * for optimization reasons (custom tile size, resolutions, ...).\n   *\n   * @param {import(\"../proj.js\").ProjectionLike} projection Projection.\n   * @param {import(\"../tilegrid/TileGrid.js\").default} tilegrid Tile grid to use for the projection.\n   * @api\n   */\n  setTileGridForProjection(projection, tilegrid) {\n    const proj = getProjection(projection);\n    if (proj) {\n      const projKey = getUid(proj);\n      if (!(projKey in this.tileGridForProjection)) {\n        this.tileGridForProjection[projKey] = tilegrid;\n      }\n    }\n  }\n\n  clear() {\n    super.clear();\n    for (const id in this.tileCacheForProjection) {\n      this.tileCacheForProjection[id].clear();\n    }\n  }\n}\n\n/**\n * @param {ImageTile} imageTile Image tile.\n * @param {string} src Source.\n */\nfunction defaultTileLoadFunction(imageTile, src) {\n  /** @type {HTMLImageElement|HTMLVideoElement} */ (imageTile.getImage()).src =\n    src;\n}\n\nexport default TileImage;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEA,IAAM,YAAN,cAAwB,gBAAQ;AAAA;AAAA;AAAA;AAAA,EAI9B,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ,cAAc,QAAQ;AAAA,MACtB,WAAW,QAAQ;AAAA,MACnB,QAAQ,QAAQ;AAAA,MAChB,YAAY,QAAQ;AAAA,MACpB,OAAO,QAAQ;AAAA,MACf,UAAU,QAAQ;AAAA,MAClB,kBAAkB,QAAQ,mBACtB,QAAQ,mBACR;AAAA,MACJ,gBAAgB,QAAQ;AAAA,MACxB,iBAAiB,QAAQ;AAAA,MACzB,KAAK,QAAQ;AAAA,MACb,MAAM,QAAQ;AAAA,MACd,OAAO,QAAQ;AAAA,MACf,YAAY,QAAQ;AAAA,MACpB,aACE,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAAA,MAC5D,KAAK,QAAQ;AAAA,MACb,yBAAyB,QAAQ;AAAA,MACjC,YAAY,QAAQ;AAAA,IACtB,CAAC;AAMD,SAAK,cACH,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAM5D,SAAK,YACH,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAMxD,SAAK,yBAAyB,CAAC;AAM/B,SAAK,wBAAwB,CAAC;AAM9B,SAAK,8BAA8B,QAAQ;AAM3C,SAAK,2BAA2B;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,QAAI,KAAK,UAAU,eAAe,GAAG;AACnC,aAAO;AAAA,IACT;AACA,eAAW,OAAO,KAAK,wBAAwB;AAC7C,UAAI,KAAK,uBAAuB,GAAG,EAAE,eAAe,GAAG;AACrD,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,YAAY,WAAW;AACjC,UAAM,gBAAgB,KAAK,0BAA0B,UAAU;AAE/D,SAAK,UAAU;AAAA,MACb,KAAK,aAAa,gBAAgB,YAAY,CAAC;AAAA,IACjD;AACA,eAAW,MAAM,KAAK,wBAAwB;AAC5C,YAAM,YAAY,KAAK,uBAAuB,EAAE;AAChD,gBAAU,YAAY,aAAa,gBAAgB,YAAY,CAAC,CAAC;AAAA,IACnE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,YAAY;AACjC,QACE,KAAK,cAAc,KACnB,cACA,CAAC,WAAW,KAAK,cAAc,GAAG,UAAU,GAC5C;AACA,aAAO;AAAA,IACT;AACA,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,QAAI,MAAM,MAAM,OAAO;AACvB,QAAI,CAAC,KAAK,eAAe,GAAG;AAC1B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,YAAY;AACpB,QACE,KAAK,cAAc,KACnB,cACA,CAAC,WAAW,KAAK,cAAc,GAAG,UAAU,GAC5C;AACA,aAAO;AAAA,IACT;AACA,WAAO,MAAM,UAAU,UAAU;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,YAAY;AACnC,UAAM,WAAW,KAAK,cAAc;AACpC,QAAI,KAAK,aAAa,CAAC,YAAY,WAAW,UAAU,UAAU,IAAI;AACpE,aAAO,KAAK;AAAA,IACd;AACA,UAAM,UAAU,OAAO,UAAU;AACjC,QAAI,EAAE,WAAW,KAAK,wBAAwB;AAC5C,WAAK,sBAAsB,OAAO,IAChC,iBAAyB,UAAU;AAAA,IACvC;AACA,WAAO,KAAK,sBAAsB,OAAO;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,0BAA0B,YAAY;AACpC,UAAM,WAAW,KAAK,cAAc;AACpC,QAAI,CAAC,YAAY,WAAW,UAAU,UAAU,GAAG;AACjD,aAAO,KAAK;AAAA,IACd;AACA,UAAM,UAAU,OAAO,UAAU;AACjC,QAAI,EAAE,WAAW,KAAK,yBAAyB;AAC7C,WAAK,uBAAuB,OAAO,IAAI,IAAI;AAAA,QACzC,KAAK,UAAU;AAAA,MACjB;AAAA,IACF;AACA,WAAO,KAAK,uBAAuB,OAAO;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,YAAY,GAAG,GAAG,GAAG,YAAY,YAAY,KAAK;AAChD,UAAM,YAAY,CAAC,GAAG,GAAG,CAAC;AAC1B,UAAM,eAAe,KAAK;AAAA,MACxB;AAAA,MACA;AAAA,IACF;AACA,UAAM,UAAU,eACZ,KAAK,gBAAgB,cAAc,YAAY,UAAU,IACzD;AACJ,UAAM,OAAO,IAAI,KAAK;AAAA,MACpB;AAAA,MACA,YAAY,SAAY,kBAAU,OAAO,kBAAU;AAAA,MACnD,YAAY,SAAY,UAAU;AAAA,MAClC,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,SAAK,MAAM;AACX,SAAK,iBAAiB,kBAAU,QAAQ,KAAK,iBAAiB,KAAK,IAAI,CAAC;AACxE,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QAAQ,GAAG,GAAG,GAAG,YAAY,YAAY;AACvC,UAAM,mBAAmB,KAAK,cAAc;AAC5C,QACE,CAAC,oBACD,CAAC,cACD,WAAW,kBAAkB,UAAU,GACvC;AACA,aAAO,KAAK;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,oBAAoB;AAAA,MACtB;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,0BAA0B,UAAU;AACvD,UAAM,YAAY,CAAC,GAAG,GAAG,CAAC;AAC1B,QAAI;AACJ,UAAM,eAAe,OAAO,SAAS;AACrC,QAAI,MAAM,YAAY,YAAY,GAAG;AACnC,aAAO,MAAM,IAAI,YAAY;AAAA,IAC/B;AACA,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,QAAQ,KAAK,OAAO,KAAK;AAC3B,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,KAAK,yBAAyB,gBAAgB;AACrE,UAAM,iBAAiB,KAAK,yBAAyB,UAAU;AAC/D,UAAM,mBAAmB,KAAK;AAAA,MAC5B;AAAA,MACA;AAAA,IACF;AACA,UAAM,UAAU,IAAI;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK,kBAAkB,UAAU;AAAA,MACjC,KAAK,UAAU;AAAA,MACf,CAACA,IAAGC,IAAGC,IAAGC,gBACR,KAAK,gBAAgBH,IAAGC,IAAGC,IAAGC,aAAY,gBAAgB;AAAA,MAC5D,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,eAAe;AAAA,IACtB;AACA,YAAQ,MAAM;AAEd,QAAI,MAAM;AACR,cAAQ,cAAc;AACtB,cAAQ,oBAAoB;AAC5B,YAAM,QAAQ,cAAc,OAAO;AAAA,IACrC,OAAO;AACL,YAAM,IAAI,cAAc,OAAO;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,gBAAgB,GAAG,GAAG,GAAG,YAAY,YAAY;AAC/C,QAAI,OAAO;AACX,UAAM,eAAe,UAAU,GAAG,GAAG,CAAC;AACtC,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,CAAC,KAAK,UAAU,YAAY,YAAY,GAAG;AAC7C,aAAO,KAAK,YAAY,GAAG,GAAG,GAAG,YAAY,YAAY,GAAG;AAC5D,WAAK,UAAU,IAAI,cAAc,IAAI;AAAA,IACvC,OAAO;AACL,aAAO,KAAK,UAAU,IAAI,YAAY;AACtC,UAAI,KAAK,OAAO,KAAK;AAInB,cAAM,cAAc;AACpB,eAAO,KAAK,YAAY,GAAG,GAAG,GAAG,YAAY,YAAY,GAAG;AAG5D,YAAI,YAAY,SAAS,KAAK,kBAAU,MAAM;AAE5C,eAAK,cAAc,YAAY;AAAA,QACjC,OAAO;AACL,eAAK,cAAc;AAAA,QACrB;AACA,aAAK,oBAAoB;AACzB,aAAK,UAAU,QAAQ,cAAc,IAAI;AAAA,MAC3C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,2BAA2B,QAAQ;AACjC,QAAI,KAAK,4BAA4B,QAAQ;AAC3C;AAAA,IACF;AACA,SAAK,2BAA2B;AAChC,eAAW,MAAM,KAAK,wBAAwB;AAC5C,WAAK,uBAAuB,EAAE,EAAE,MAAM;AAAA,IACxC;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,yBAAyB,YAAY,UAAU;AAC7C,UAAM,OAAO,IAAc,UAAU;AACrC,QAAI,MAAM;AACR,YAAM,UAAU,OAAO,IAAI;AAC3B,UAAI,EAAE,WAAW,KAAK,wBAAwB;AAC5C,aAAK,sBAAsB,OAAO,IAAI;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,QAAQ;AACN,UAAM,MAAM;AACZ,eAAW,MAAM,KAAK,wBAAwB;AAC5C,WAAK,uBAAuB,EAAE,EAAE,MAAM;AAAA,IACxC;AAAA,EACF;AACF;AAMA,SAAS,wBAAwB,WAAW,KAAK;AACE,EAAC,UAAU,SAAS,EAAG,MACtE;AACJ;AAEA,IAAO,oBAAQ;", "names": ["z", "x", "y", "pixelRatio"]}