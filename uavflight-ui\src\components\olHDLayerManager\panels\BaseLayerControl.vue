<!--
 * @Author: AI Assistant 
 * @Date: 2025-07-15 
 * @Description: 快拼图层控制面板组件，加载和管理快拼图层
 *  
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved. 
-->
<template>
  <div class="hd-base-layer-control">
    <div class="panel-title">
      <span>图层控制</span>
    </div>
    <div class="panel-content">
      <!-- 分组控制工具栏 -->
      <div class="control-toolbar">
        <el-input
          v-model="searchText"
          placeholder="搜索图层"
          clearable
          prefix-icon="Search"
          size="small"
          @input="filterLayers"
        />
        <div class="theme-filter">
          <el-button 
            size="small" 
            :type="isQueryModeActive ? 'danger' : 'success'" 
            class="location-btn" 
            @click="selectLocation"
            :class="{ 'active-tool': isQueryModeActive }"
          >
            {{ isQueryModeActive ? '关闭点击位置查询' : '点击位置查询' }}
          </el-button>
        </div>
      </div>

      <!-- 图层组列表 -->
      <div class="layer-list-container">
        <el-collapse v-model="activeCollapseGroups" class="layer-group-list">
          <el-collapse-item
            v-for="(group, index) in visibleGroups"
            :key="index"
            :name="index.toString()"
          >
            <template #title>
              <div class="group-title">
                <el-checkbox
                  :modelValue="getGroupVisibility(group)"
                  :indeterminate="isGroupIndeterminate(group)"
                  @change="toggleGroupVisibility(group, $event)"
                  @click.stop
                >
                  {{ group.name }}
                </el-checkbox>
              </div>
            </template>

            <!-- 图层列表 -->
            <div class="layer-items">
              <div
                v-for="(layer, layerIndex) in getVisibleLayers(group)"
                :key="layerIndex"
                class="layer-item"
              >
                <div class="layer-header">
                  <el-checkbox
                    v-model="layer.visible"
                    @change="toggleLayerVisibility(layer, $event)"
                  >
                    {{ layer.name }}
                  </el-checkbox>
                  <el-dropdown trigger="click" @command="handleLayerCommand($event, layer)">
                    <el-icon class="layer-actions-btn"><MoreFilled /></el-icon>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="'metadata'">图层信息</el-dropdown-item>
                        <el-dropdown-item :command="'style'" v-if="layer.type === 'vector'">样式设置</el-dropdown-item>
                        <el-dropdown-item :command="'zoom'">定位图层</el-dropdown-item>
                        <el-dropdown-item :command="'label'" v-if="layer.type === 'vector' && hasLabelField(layer)">
                          {{ layer.showLabels ? '隐藏标注' : '显示标注' }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>

                <!-- 透明度控制 -->
                <div class="layer-opacity-control" v-if="layer.showOpacityControl">
                  <span class="opacity-label">透明度:</span>
                  <el-slider
                    v-model="layer.opacity"
                    :min="0"
                    :max="100"
                    :step="5"
                    @change="changeLayerOpacity(layer, $event)"
                  />
                </div>

                <!-- 图层样式设置 -->
                <div class="layer-style-control" v-if="layer.showStyleControl && layer.type === 'vector'">
                  <div class="style-title">样式设置</div>
                  <!-- 线条颜色 -->
                  <div class="style-item">
                    <span>线条颜色:</span>
                    <el-color-picker
                      v-model="layer.style.color"
                      size="small"
                      show-alpha
                      @change="updateLayerStyle(layer)"
                    />
                  </div>
                  <!-- 线条宽度 -->
                  <div class="style-item">
                    <span>线条宽度:</span>
                    <el-slider
                      v-model="layer.style.weight"
                      :min="0.5"
                      :max="5"
                      :step="0.5"
                      @change="updateLayerStyle(layer)"
                    />
                  </div>
                  <!-- 填充颜色 -->
                  <div class="style-item">
                    <span>填充颜色:</span>
                    <el-color-picker
                      v-model="layer.style.fillColor"
                      size="small"
                      show-alpha
                      @change="updateLayerStyle(layer)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <!-- 图层信息对话框 -->
    <el-dialog
      v-model="metadataDialogVisible"
      title="图层信息"
      width="500px"
      destroy-on-close
      center
      append-to-body
    >
      <div v-if="selectedLayer" class="metadata-content">
        <div class="metadata-item">
          <span class="metadata-label">图层名称:</span>
          <span class="metadata-value">{{ selectedLayer.name }}</span>
        </div>
        <div class="metadata-item">
          <span class="metadata-label">图层ID:</span>
          <span class="metadata-value">{{ selectedLayer.id }}</span>
        </div>
        <div class="metadata-item">
          <span class="metadata-label">图层类型:</span>
          <span class="metadata-value">{{ selectedLayer.type === 'vector' ? '矢量图层' : '栅格图层' }}</span>
        </div>
        <div class="metadata-item" v-if="selectedLayer.workspace">
          <span class="metadata-label">工作空间:</span>
          <span class="metadata-value">{{ selectedLayer.workspace }}</span>
        </div>
        <div class="metadata-item" v-if="selectedLayer.layerName">
          <span class="metadata-label">服务图层名:</span>
          <span class="metadata-value">{{ selectedLayer.layerName }}</span>
        </div>
        <div class="metadata-item" v-if="selectedLayer.protocol">
          <span class="metadata-label">加载协议:</span>
          <span class="metadata-value">{{ selectedLayer.protocol }}</span>
        </div>
        <div class="metadata-item" v-if="selectedLayer.theme">
          <span class="metadata-label">主题分类:</span>
          <span class="metadata-value">{{ selectedLayer.theme }}</span>
        </div>
        <div class="metadata-item" v-if="selectedLayer.geometryType">
          <span class="metadata-label">几何类型:</span>
          <span class="metadata-value">{{ selectedLayer.geometryType }}</span>
        </div>
        <div class="metadata-item" v-if="layerExtent">
          <span class="metadata-label">边界信息:</span>
          <span class="metadata-value">{{ layerExtent }}</span>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="metadataDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive, onBeforeUnmount, inject } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, MoreFilled } from '@element-plus/icons-vue';
import { useHDMapLayerStore } from '/@/stores/hdMapLayer'; // 快拼图层Store
import { useOLMapStore } from '/@/stores/olMapStore'; // 地图Store
import { transform } from 'ol/proj';
import axios from 'axios';

// 图层搜索
const searchText = ref('');
const currentTheme = ref('');

// 弹窗控制
const metadataDialogVisible = ref(false);
const selectedLayer = ref(null);
const layerExtent = ref('');

// 获取快拼图层Store和地图Store
const hdMapLayerStore = useHDMapLayerStore();
const mapStore = useOLMapStore();

// 展开的分组
const activeCollapseGroups = ref([]);

// 从父组件注入的图层查询功能
const layerQuery = inject('layerQuery', {
  toggleQueryMode: () => {},
  isQueryModeActive: ref(false)
});

// 获取查询模式状态
const isQueryModeActive = computed(() => layerQuery.isQueryModeActive.value);

// 获取图层组列表（按主题分组）
const layerGroups = computed(() => {
  return hdMapLayerStore.layerGroups;
});

// 主题列表
const themesList = computed(() => {
  return hdMapLayerStore.themesList;
});

// 可见的分组（根据搜索和主题过滤）
const visibleGroups = computed(() => {
  return layerGroups.value.filter(group => {
    // 如果选择了主题且组名不匹配，则过滤掉
    if (currentTheme.value && group.name !== currentTheme.value) {
      return false;
    }

    // 如果有搜索文本，检查是否有匹配的图层
    if (searchText.value) {
      const hasMatchingLayer = group.layers.some(layer => 
        layer.name.toLowerCase().includes(searchText.value.toLowerCase()));
      return hasMatchingLayer;
    }

    return true;
  }).sort((a, b) => {
    // 按主题文字升序排序（使用中文本地化排序规则）
    return a.name.localeCompare(b.name, 'zh-CN');
  });
});

// 根据搜索文本获取可见图层
const getVisibleLayers = (group) => {
  if (!searchText.value) {
    return group.layers;
  }
  
  return group.layers.filter(layer => 
    layer.name.toLowerCase().includes(searchText.value.toLowerCase())
  );
};

// 获取分组的可见性状态
const getGroupVisibility = (group) => {
  const visibleLayers = getVisibleLayers(group);
  if (visibleLayers.length === 0) return false;
  return visibleLayers.every(layer => layer.visible);
};

// 判断分组是否处于半选中状态
const isGroupIndeterminate = (group) => {
  const visibleLayers = getVisibleLayers(group);
  if (visibleLayers.length === 0) return false;
  
  const hasVisibleLayer = visibleLayers.some(layer => layer.visible);
  const hasInvisibleLayer = visibleLayers.some(layer => !layer.visible);
  
  return hasVisibleLayer && hasInvisibleLayer;
};

// 切换图层组可见性
const toggleGroupVisibility = (group, visible) => {
  hdMapLayerStore.toggleGroupVisibility(group, visible);
};

// 切换单个图层可见性
const toggleLayerVisibility = (layer, visible) => {
  hdMapLayerStore.toggleLayerVisibility(layer, visible);
};

// 处理图层操作命令
const handleLayerCommand = (command, layer) => {
  switch (command) {
    case 'metadata':
      showLayerMetadata(layer);
      break;
    case 'style':
      toggleLayerStyleControl(layer);
      break;
    case 'zoom':
      zoomToLayer(layer);
      break;
    case 'label':
      toggleLabels(layer);
      break;
  }
};

// 显示图层元数据信息
const showLayerMetadata = async (layer) => {
  selectedLayer.value = layer;
  layerExtent.value = '正在获取...';
  metadataDialogVisible.value = true;
  
  try {
    // 尝试从API获取边界信息
    if (layer.workspace && layer.layerName) {
      // 使用图层管理器获取边界信息
      if (hdMapLayerStore.layerManager) {
        const workspace = layer.workspace;
        const layerName = layer.layerName;
        
        console.log(`尝试获取图层 ${workspace}:${layerName} 的边界信息`);
        
        try {
          // 导入getLayerBbox函数
          const { getLayerBbox } = await import('/@/utils/geoserver');
          const bboxData = await getLayerBbox(workspace, layerName);
          
          if (bboxData && bboxData.status === 'success' && bboxData.bbox) {
            const { latLon } = bboxData.bbox;
            if (latLon) {
              layerExtent.value = `经度: ${latLon.minx.toFixed(6)} ~ ${latLon.maxx.toFixed(6)}, 纬度: ${latLon.miny.toFixed(6)} ~ ${latLon.maxy.toFixed(6)}`;
              return;
            }
          }
        } catch (error) {
          console.error(`获取图层边界信息失败:`, error);
        }
      }
    }
    
    // 如果API获取失败，尝试从图层源获取
    if (hdMapLayerStore.layerManager) {
      const layerInstance = hdMapLayerStore.layerManager.getLayer(layer.id);
      if (layerInstance) {
        const source = layerInstance.getSource();
        if (source && source.getExtent) {
          try {
            const extent = source.getExtent();
            if (extent && extent.length === 4 && !extent.some(v => isNaN(v))) {
              layerExtent.value = `[${extent[0].toFixed(2)}, ${extent[1].toFixed(2)}, ${extent[2].toFixed(2)}, ${extent[3].toFixed(2)}]`;
              return;
            }
          } catch (error) {
            console.warn(`从图层源获取边界失败:`, error);
          }
        }
      }
    }
    
    // 如果都失败，显示未知
    layerExtent.value = '未知';
  } catch (error) {
    console.error('获取图层边界信息失败:', error);
    layerExtent.value = '获取失败';
  }
};

// 切换图层样式控制面板
const toggleLayerStyleControl = (layer) => {
  layer.showStyleControl = !layer.showStyleControl;
};

// 更改图层透明度
const changeLayerOpacity = (layer, opacity) => {
  hdMapLayerStore.setLayerOpacity(layer, opacity);
};

// 更新图层样式
const updateLayerStyle = (layer) => {
  hdMapLayerStore.updateLayerStyle(layer);
};

// 缩放到图层
const zoomToLayer = (layer) => {
  try {
    console.log('正在缩放到图层:', layer.id, layer.name);
    console.log('图层详细信息:', JSON.stringify(layer));
    
    // 确保图层已加载
    if (!layer.visible) {
      console.log('图层未加载，先加载图层');
      layer.visible = true;
      hdMapLayerStore.toggleLayerVisibility(layer, true);
    }
    
    // 使用setTimeout确保图层加载完成
    setTimeout(() => {
      if (hdMapLayerStore.layerManager) {
        // 直接调用图层管理器的zoomToLayer方法
        // 注意：这里需要确保传递的是正确的图层ID
        const layerId = layer.id;
        console.log(`调用zoomToLayer，使用图层ID: ${layerId}`);
        hdMapLayerStore.layerManager.zoomToLayer(layerId);
        ElMessage.success(`已定位到图层: ${layer.name}`);
      } else {
        console.error('图层管理器实例不存在');
        ElMessage.error('无法定位图层，图层管理器实例不存在');
      }
    }, 1000); // 增加延时，确保图层完全加载
  } catch (error) {
    console.error('缩放到图层失败:', error);
    ElMessage.error(`缩放到图层失败: ${error.message || '未知错误'}`);
  }
};

// 切换标签显示
const toggleLabels = (layer) => {
  hdMapLayerStore.toggleLayerLabels(layer);
};

// 判断图层是否支持标签
const hasLabelField = (layer) => {
  return layer.labelField != null && layer.labelField !== '';
};

// 过滤图层
const filterLayers = () => {
  // 自动展开所有组
  if (searchText.value) {
    activeCollapseGroups.value = visibleGroups.value.map((_, index) => index.toString());
  }
};

// 选择位置查询功能 - 使用父组件提供的查询功能
const selectLocation = () => {
  layerQuery.toggleQueryMode();
};

// 组件初始化
onMounted(async () => {
  try {
    // 加载高精度地图配置
    // 使用相同文件名，hdMapLayerStore.loadMapConfig内部已经修改为从API接口加载
    await hdMapLayerStore.loadMapConfig('baseMap3', 'baseStyle3');
    
    // 默认不展开任何组
    activeCollapseGroups.value = [];
  } catch (error) {
    console.error('初始化快拼图层控制面板失败:', error);
    ElMessage.error('加载图层配置失败，请检查网络连接');
  }
});

// 组件卸载前清理
onBeforeUnmount(() => {
  // 如果组件卸载时查询模式仍然激活，确保关闭查询模式
  if (isQueryModeActive.value) {
    layerQuery.toggleQueryMode();
  }
});
</script>

<style lang="scss" scoped>
.hd-base-layer-control {
  height: 100%;
  display: flex;
  flex-direction: column;
  color: #fff;
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
  padding: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.control-toolbar {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
  
  .el-input {
    flex: 1;
    min-width: 120px;
  }
  
  .theme-filter {
    width: 120px;
    
    .location-btn {
      width: 100%;
      font-weight: bold;
      
      &.active-tool {
        background-color: #F56C6C !important;
        border-color: #F56C6C !important;
      }
    }
  }
}

.layer-list-container {
  flex: 1;
  overflow-y: auto;
}

.layer-group-list {
  border: none;
  background: transparent;
}

:deep(.el-collapse-item__header) {
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
  border: none;
  color: white;
  height: 40px;
  font-weight: bold;
}

:deep(.el-collapse-item__content) {
  background-color: rgba(11, 18, 32, 0.5);
  padding: 5px;
  border-radius: 0 0 4px 4px;
}

:deep(.el-collapse-item__arrow) {
  color: white;
}

:deep(.el-checkbox__label) {
  color: white;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #409EFF;
  border-color: #409EFF;
}

:deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner) {
  background-color: #409EFF;
}

.group-title {
  display: flex;
  align-items: center;
  width: 100%;
}

.layer-items {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.layer-item {
  background-color: rgba(11, 18, 32, 0.8);
  border-radius: 4px;
  padding: 6px 8px;
  margin-bottom: 2px;
  
  // 确保所有文本为白色
  color: white !important;
  
  // 强制所有子元素中的文本为白色
  :deep(*) {
    color: white !important;
  }
}

.layer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.layer-actions-btn {
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: rgba(255, 255, 255, 0.7);
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
  }
}

.layer-opacity-control, .layer-style-control {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed rgba(255, 255, 255, 0.1);
}

.opacity-label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.style-title {
  font-size: 13px;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: bold;
}

.style-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  
  span {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
  }
  
  .el-slider {
    width: 150px;
  }
}

.metadata-content {
  max-height: 400px;
  overflow-y: auto;
}

.metadata-item {
  display: flex;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px dashed #eee;
}

.metadata-label {
  font-weight: bold;
  width: 100px;
}

.metadata-value {
  flex: 1;
  word-break: break-all;
}

:deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.2);
}

:deep(.el-slider__bar) {
  background-color: #409EFF;
}

:deep(.el-slider__button) {
  border: 2px solid #409EFF;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-select-dropdown__item) {
  font-size: 13px;
}

:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: none;
}

:deep(.el-input__inner) {
  color: white;
}

:deep(.el-input__prefix) {
  color: rgba(255, 255, 255, 0.7);
}

/* 添加查询相关样式 */
.active-tool {
  background-color: #25802a !important;
  border-color: #25802a !important;
}

.query-loading, .query-error, .no-layers-found {
  text-align: center;
  padding: 20px 0;
  
  .el-icon {
    font-size: 32px;
    margin-bottom: 10px;
  }
}

.query-error {
  .el-icon {
    color: #f56c6c;
  }
}

.result-summary {
  font-weight: bold;
  margin-bottom: 10px;
}

.loaded-tag {
  margin: 0 auto;
  display: block;
  text-align: center;
}

.dialog-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 