{"version": 3, "sources": ["../../ol/renderer/Layer.js", "../../ol/renderer/canvas/Layer.js"], "sourcesContent": ["/**\n * @module ol/renderer/Layer\n */\nimport EventType from '../events/EventType.js';\nimport ImageState from '../ImageState.js';\nimport Observable from '../Observable.js';\nimport {abstract} from '../util.js';\n\n/**\n * @template {import(\"../layer/Layer.js\").default} LayerType\n */\nclass LayerRenderer extends Observable {\n  /**\n   * @param {LayerType} layer Layer.\n   */\n  constructor(layer) {\n    super();\n\n    /**\n     * The renderer is initialized and ready to render.\n     * @type {boolean}\n     */\n    this.ready = true;\n\n    /** @private */\n    this.boundHandleImageChange_ = this.handleImageChange_.bind(this);\n\n    /**\n     * @protected\n     * @type {LayerType}\n     */\n    this.layer_ = layer;\n\n    /**\n     * @type {import(\"../render/canvas/ExecutorGroup\").default}\n     */\n    this.declutterExecutorGroup = null;\n  }\n\n  /**\n   * Asynchronous layer level hit detection.\n   * @param {import(\"../pixel.js\").Pixel} pixel Pixel.\n   * @return {Promise<Array<import(\"../Feature\").FeatureLike>>} Promise that resolves with\n   * an array of features.\n   */\n  getFeatures(pixel) {\n    return abstract();\n  }\n\n  /**\n   * @param {import(\"../pixel.js\").Pixel} pixel Pixel.\n   * @return {Uint8ClampedArray|Uint8Array|Float32Array|DataView|null} Pixel data.\n   */\n  getData(pixel) {\n    return null;\n  }\n\n  /**\n   * Determine whether render should be called.\n   * @abstract\n   * @param {import(\"../Map.js\").FrameState} frameState Frame state.\n   * @return {boolean} Layer is ready to be rendered.\n   */\n  prepareFrame(frameState) {\n    return abstract();\n  }\n\n  /**\n   * Render the layer.\n   * @abstract\n   * @param {import(\"../Map.js\").FrameState} frameState Frame state.\n   * @param {HTMLElement|null} target Target that may be used to render content to.\n   * @return {HTMLElement|null} The rendered element.\n   */\n  renderFrame(frameState, target) {\n    return abstract();\n  }\n\n  /**\n   * @param {Object<number, Object<string, import(\"../Tile.js\").default>>} tiles Lookup of loaded tiles by zoom level.\n   * @param {number} zoom Zoom level.\n   * @param {import(\"../Tile.js\").default} tile Tile.\n   * @return {boolean|void} If `false`, the tile will not be considered loaded.\n   */\n  loadedTileCallback(tiles, zoom, tile) {\n    if (!tiles[zoom]) {\n      tiles[zoom] = {};\n    }\n    tiles[zoom][tile.tileCoord.toString()] = tile;\n    return undefined;\n  }\n\n  /**\n   * Create a function that adds loaded tiles to the tile lookup.\n   * @param {import(\"../source/Tile.js\").default} source Tile source.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection of the tiles.\n   * @param {Object<number, Object<string, import(\"../Tile.js\").default>>} tiles Lookup of loaded tiles by zoom level.\n   * @return {function(number, import(\"../TileRange.js\").default):boolean} A function that can be\n   *     called with a zoom level and a tile range to add loaded tiles to the lookup.\n   * @protected\n   */\n  createLoadedTileFinder(source, projection, tiles) {\n    return (\n      /**\n       * @param {number} zoom Zoom level.\n       * @param {import(\"../TileRange.js\").default} tileRange Tile range.\n       * @return {boolean} The tile range is fully loaded.\n       */\n      (zoom, tileRange) => {\n        const callback = this.loadedTileCallback.bind(this, tiles, zoom);\n        return source.forEachLoadedTile(projection, zoom, tileRange, callback);\n      }\n    );\n  }\n  /**\n   * @abstract\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @param {import(\"../Map.js\").FrameState} frameState Frame state.\n   * @param {number} hitTolerance Hit tolerance in pixels.\n   * @param {import(\"./vector.js\").FeatureCallback<T>} callback Feature callback.\n   * @param {Array<import(\"./Map.js\").HitMatch<T>>} matches The hit detected matches with tolerance.\n   * @return {T|undefined} Callback result.\n   * @template T\n   */\n  forEachFeatureAtCoordinate(\n    coordinate,\n    frameState,\n    hitTolerance,\n    callback,\n    matches\n  ) {\n    return undefined;\n  }\n\n  /**\n   * @return {LayerType} Layer.\n   */\n  getLayer() {\n    return this.layer_;\n  }\n\n  /**\n   * Perform action necessary to get the layer rendered after new fonts have loaded\n   * @abstract\n   */\n  handleFontsChanged() {}\n\n  /**\n   * Handle changes in image state.\n   * @param {import(\"../events/Event.js\").default} event Image change event.\n   * @private\n   */\n  handleImageChange_(event) {\n    const image = /** @type {import(\"../Image.js\").default} */ (event.target);\n    if (\n      image.getState() === ImageState.LOADED ||\n      image.getState() === ImageState.ERROR\n    ) {\n      this.renderIfReadyAndVisible();\n    }\n  }\n\n  /**\n   * Load the image if not already loaded, and register the image change\n   * listener if needed.\n   * @param {import(\"../ImageBase.js\").default} image Image.\n   * @return {boolean} `true` if the image is already loaded, `false` otherwise.\n   * @protected\n   */\n  loadImage(image) {\n    let imageState = image.getState();\n    if (imageState != ImageState.LOADED && imageState != ImageState.ERROR) {\n      image.addEventListener(EventType.CHANGE, this.boundHandleImageChange_);\n    }\n    if (imageState == ImageState.IDLE) {\n      image.load();\n      imageState = image.getState();\n    }\n    return imageState == ImageState.LOADED;\n  }\n\n  /**\n   * @protected\n   */\n  renderIfReadyAndVisible() {\n    const layer = this.getLayer();\n    if (layer && layer.getVisible() && layer.getSourceState() === 'ready') {\n      layer.changed();\n    }\n  }\n\n  /**\n   * Clean up.\n   */\n  disposeInternal() {\n    delete this.layer_;\n    super.disposeInternal();\n  }\n}\n\nexport default LayerRenderer;\n", "/**\n * @module ol/renderer/canvas/Layer\n */\nimport LayerRenderer from '../Layer.js';\nimport RenderEvent from '../../render/Event.js';\nimport RenderEventType from '../../render/EventType.js';\nimport {\n  apply as applyTransform,\n  compose as composeTransform,\n  create as createTransform,\n} from '../../transform.js';\nimport {asArray} from '../../color.js';\nimport {createCanvasContext2D} from '../../dom.js';\nimport {equals} from '../../array.js';\nimport {\n  getBottomLeft,\n  getBottomRight,\n  getTopLeft,\n  getTopRight,\n} from '../../extent.js';\n\n/**\n * @type {Array<HTMLCanvasElement>}\n */\nexport const canvasPool = [];\n\n/**\n * @type {CanvasRenderingContext2D}\n */\nlet pixelContext = null;\n\nfunction createPixelContext() {\n  pixelContext = createCanvasContext2D(1, 1, undefined, {\n    willReadFrequently: true,\n  });\n}\n\n/**\n * @abstract\n * @template {import(\"../../layer/Layer.js\").default} LayerType\n * @extends {LayerRenderer<LayerType>}\n */\nclass CanvasLayerRenderer extends LayerRenderer {\n  /**\n   * @param {LayerType} layer Layer.\n   */\n  constructor(layer) {\n    super(layer);\n\n    /**\n     * @protected\n     * @type {HTMLElement}\n     */\n    this.container = null;\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.renderedResolution;\n\n    /**\n     * A temporary transform.  The values in this transform should only be used in a\n     * function that sets the values.\n     * @protected\n     * @type {import(\"../../transform.js\").Transform}\n     */\n    this.tempTransform = createTransform();\n\n    /**\n     * The transform for rendered pixels to viewport CSS pixels.  This transform must\n     * be set when rendering a frame and may be used by other functions after rendering.\n     * @protected\n     * @type {import(\"../../transform.js\").Transform}\n     */\n    this.pixelTransform = createTransform();\n\n    /**\n     * The transform for viewport CSS pixels to rendered pixels.  This transform must\n     * be set when rendering a frame and may be used by other functions after rendering.\n     * @protected\n     * @type {import(\"../../transform.js\").Transform}\n     */\n    this.inversePixelTransform = createTransform();\n\n    /**\n     * @type {CanvasRenderingContext2D}\n     */\n    this.context = null;\n\n    /**\n     * @type {boolean}\n     */\n    this.containerReused = false;\n\n    /**\n     * @private\n     * @type {CanvasRenderingContext2D}\n     */\n    this.pixelContext_ = null;\n\n    /**\n     * @protected\n     * @type {import(\"../../Map.js\").FrameState|null}\n     */\n    this.frameState = null;\n  }\n\n  /**\n   * @param {HTMLCanvasElement|HTMLImageElement|HTMLVideoElement} image Image.\n   * @param {number} col The column index.\n   * @param {number} row The row index.\n   * @return {Uint8ClampedArray|null} The image data.\n   */\n  getImageData(image, col, row) {\n    if (!pixelContext) {\n      createPixelContext();\n    }\n    pixelContext.clearRect(0, 0, 1, 1);\n\n    let data;\n    try {\n      pixelContext.drawImage(image, col, row, 1, 1, 0, 0, 1, 1);\n      data = pixelContext.getImageData(0, 0, 1, 1).data;\n    } catch (err) {\n      pixelContext = null;\n      return null;\n    }\n    return data;\n  }\n\n  /**\n   * @param {import('../../Map.js').FrameState} frameState Frame state.\n   * @return {string} Background color.\n   */\n  getBackground(frameState) {\n    const layer = this.getLayer();\n    let background = layer.getBackground();\n    if (typeof background === 'function') {\n      background = background(frameState.viewState.resolution);\n    }\n    return background || undefined;\n  }\n\n  /**\n   * Get a rendering container from an existing target, if compatible.\n   * @param {HTMLElement} target Potential render target.\n   * @param {string} transform CSS Transform.\n   * @param {string} [backgroundColor] Background color.\n   */\n  useContainer(target, transform, backgroundColor) {\n    const layerClassName = this.getLayer().getClassName();\n    let container, context;\n    if (\n      target &&\n      target.className === layerClassName &&\n      (!backgroundColor ||\n        (target &&\n          target.style.backgroundColor &&\n          equals(\n            asArray(target.style.backgroundColor),\n            asArray(backgroundColor)\n          )))\n    ) {\n      const canvas = target.firstElementChild;\n      if (canvas instanceof HTMLCanvasElement) {\n        context = canvas.getContext('2d');\n      }\n    }\n    if (context && context.canvas.style.transform === transform) {\n      // Container of the previous layer renderer can be used.\n      this.container = target;\n      this.context = context;\n      this.containerReused = true;\n    } else if (this.containerReused) {\n      // Previously reused container cannot be used any more.\n      this.container = null;\n      this.context = null;\n      this.containerReused = false;\n    } else if (this.container) {\n      this.container.style.backgroundColor = null;\n    }\n    if (!this.container) {\n      container = document.createElement('div');\n      container.className = layerClassName;\n      let style = container.style;\n      style.position = 'absolute';\n      style.width = '100%';\n      style.height = '100%';\n      context = createCanvasContext2D();\n      const canvas = context.canvas;\n      container.appendChild(canvas);\n      style = canvas.style;\n      style.position = 'absolute';\n      style.left = '0';\n      style.transformOrigin = 'top left';\n      this.container = container;\n      this.context = context;\n    }\n    if (\n      !this.containerReused &&\n      backgroundColor &&\n      !this.container.style.backgroundColor\n    ) {\n      this.container.style.backgroundColor = backgroundColor;\n    }\n  }\n\n  /**\n   * @param {CanvasRenderingContext2D} context Context.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @param {import(\"../../extent.js\").Extent} extent Clip extent.\n   * @protected\n   */\n  clipUnrotated(context, frameState, extent) {\n    const topLeft = getTopLeft(extent);\n    const topRight = getTopRight(extent);\n    const bottomRight = getBottomRight(extent);\n    const bottomLeft = getBottomLeft(extent);\n\n    applyTransform(frameState.coordinateToPixelTransform, topLeft);\n    applyTransform(frameState.coordinateToPixelTransform, topRight);\n    applyTransform(frameState.coordinateToPixelTransform, bottomRight);\n    applyTransform(frameState.coordinateToPixelTransform, bottomLeft);\n\n    const inverted = this.inversePixelTransform;\n    applyTransform(inverted, topLeft);\n    applyTransform(inverted, topRight);\n    applyTransform(inverted, bottomRight);\n    applyTransform(inverted, bottomLeft);\n\n    context.save();\n    context.beginPath();\n    context.moveTo(Math.round(topLeft[0]), Math.round(topLeft[1]));\n    context.lineTo(Math.round(topRight[0]), Math.round(topRight[1]));\n    context.lineTo(Math.round(bottomRight[0]), Math.round(bottomRight[1]));\n    context.lineTo(Math.round(bottomLeft[0]), Math.round(bottomLeft[1]));\n    context.clip();\n  }\n\n  /**\n   * @param {import(\"../../render/EventType.js\").default} type Event type.\n   * @param {CanvasRenderingContext2D} context Context.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @private\n   */\n  dispatchRenderEvent_(type, context, frameState) {\n    const layer = this.getLayer();\n    if (layer.hasListener(type)) {\n      const event = new RenderEvent(\n        type,\n        this.inversePixelTransform,\n        frameState,\n        context\n      );\n      layer.dispatchEvent(event);\n    }\n  }\n\n  /**\n   * @param {CanvasRenderingContext2D} context Context.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @protected\n   */\n  preRender(context, frameState) {\n    this.frameState = frameState;\n    this.dispatchRenderEvent_(RenderEventType.PRERENDER, context, frameState);\n  }\n\n  /**\n   * @param {CanvasRenderingContext2D} context Context.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @protected\n   */\n  postRender(context, frameState) {\n    this.dispatchRenderEvent_(RenderEventType.POSTRENDER, context, frameState);\n  }\n\n  /**\n   * Creates a transform for rendering to an element that will be rotated after rendering.\n   * @param {import(\"../../coordinate.js\").Coordinate} center Center.\n   * @param {number} resolution Resolution.\n   * @param {number} rotation Rotation.\n   * @param {number} pixelRatio Pixel ratio.\n   * @param {number} width Width of the rendered element (in pixels).\n   * @param {number} height Height of the rendered element (in pixels).\n   * @param {number} offsetX Offset on the x-axis in view coordinates.\n   * @protected\n   * @return {!import(\"../../transform.js\").Transform} Transform.\n   */\n  getRenderTransform(\n    center,\n    resolution,\n    rotation,\n    pixelRatio,\n    width,\n    height,\n    offsetX\n  ) {\n    const dx1 = width / 2;\n    const dy1 = height / 2;\n    const sx = pixelRatio / resolution;\n    const sy = -sx;\n    const dx2 = -center[0] + offsetX;\n    const dy2 = -center[1];\n    return composeTransform(\n      this.tempTransform,\n      dx1,\n      dy1,\n      sx,\n      sy,\n      -rotation,\n      dx2,\n      dy2\n    );\n  }\n\n  /**\n   * Clean up.\n   */\n  disposeInternal() {\n    delete this.frameState;\n    super.disposeInternal();\n  }\n}\n\nexport default CanvasLayerRenderer;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,IAAM,gBAAN,cAA4B,mBAAW;AAAA;AAAA;AAAA;AAAA,EAIrC,YAAY,OAAO;AACjB,UAAM;AAMN,SAAK,QAAQ;AAGb,SAAK,0BAA0B,KAAK,mBAAmB,KAAK,IAAI;AAMhE,SAAK,SAAS;AAKd,SAAK,yBAAyB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,OAAO;AACjB,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,OAAO;AACb,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,YAAY;AACvB,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,YAAY,QAAQ;AAC9B,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB,OAAO,MAAM,MAAM;AACpC,QAAI,CAAC,MAAM,IAAI,GAAG;AAChB,YAAM,IAAI,IAAI,CAAC;AAAA,IACjB;AACA,UAAM,IAAI,EAAE,KAAK,UAAU,SAAS,CAAC,IAAI;AACzC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,uBAAuB,QAAQ,YAAY,OAAO;AAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAME,CAAC,MAAM,cAAc;AACnB,cAAM,WAAW,KAAK,mBAAmB,KAAK,MAAM,OAAO,IAAI;AAC/D,eAAO,OAAO,kBAAkB,YAAY,MAAM,WAAW,QAAQ;AAAA,MACvE;AAAA;AAAA,EAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,2BACE,YACA,YACA,cACA,UACA,SACA;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOtB,mBAAmB,OAAO;AACxB,UAAM;AAAA;AAAA,MAAsD,MAAM;AAAA;AAClE,QACE,MAAM,SAAS,MAAM,mBAAW,UAChC,MAAM,SAAS,MAAM,mBAAW,OAChC;AACA,WAAK,wBAAwB;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,OAAO;AACf,QAAI,aAAa,MAAM,SAAS;AAChC,QAAI,cAAc,mBAAW,UAAU,cAAc,mBAAW,OAAO;AACrE,YAAM,iBAAiB,kBAAU,QAAQ,KAAK,uBAAuB;AAAA,IACvE;AACA,QAAI,cAAc,mBAAW,MAAM;AACjC,YAAM,KAAK;AACX,mBAAa,MAAM,SAAS;AAAA,IAC9B;AACA,WAAO,cAAc,mBAAW;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B;AACxB,UAAM,QAAQ,KAAK,SAAS;AAC5B,QAAI,SAAS,MAAM,WAAW,KAAK,MAAM,eAAe,MAAM,SAAS;AACrE,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,WAAO,KAAK;AACZ,UAAM,gBAAgB;AAAA,EACxB;AACF;AAEA,IAAO,gBAAQ;;;AChLR,IAAM,aAAa,CAAC;AAK3B,IAAI,eAAe;AAEnB,SAAS,qBAAqB;AAC5B,iBAAe,sBAAsB,GAAG,GAAG,QAAW;AAAA,IACpD,oBAAoB;AAAA,EACtB,CAAC;AACH;AAOA,IAAM,sBAAN,cAAkC,cAAc;AAAA;AAAA;AAAA;AAAA,EAI9C,YAAY,OAAO;AACjB,UAAM,KAAK;AAMX,SAAK,YAAY;AAMjB,SAAK;AAQL,SAAK,gBAAgB,OAAgB;AAQrC,SAAK,iBAAiB,OAAgB;AAQtC,SAAK,wBAAwB,OAAgB;AAK7C,SAAK,UAAU;AAKf,SAAK,kBAAkB;AAMvB,SAAK,gBAAgB;AAMrB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,OAAO,KAAK,KAAK;AAC5B,QAAI,CAAC,cAAc;AACjB,yBAAmB;AAAA,IACrB;AACA,iBAAa,UAAU,GAAG,GAAG,GAAG,CAAC;AAEjC,QAAI;AACJ,QAAI;AACF,mBAAa,UAAU,OAAO,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACxD,aAAO,aAAa,aAAa,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,IAC/C,SAAS,KAAK;AACZ,qBAAe;AACf,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,YAAY;AACxB,UAAM,QAAQ,KAAK,SAAS;AAC5B,QAAI,aAAa,MAAM,cAAc;AACrC,QAAI,OAAO,eAAe,YAAY;AACpC,mBAAa,WAAW,WAAW,UAAU,UAAU;AAAA,IACzD;AACA,WAAO,cAAc;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,QAAQ,WAAW,iBAAiB;AAC/C,UAAM,iBAAiB,KAAK,SAAS,EAAE,aAAa;AACpD,QAAI,WAAW;AACf,QACE,UACA,OAAO,cAAc,mBACpB,CAAC,mBACC,UACC,OAAO,MAAM,mBACb;AAAA,MACE,QAAQ,OAAO,MAAM,eAAe;AAAA,MACpC,QAAQ,eAAe;AAAA,IACzB,IACJ;AACA,YAAM,SAAS,OAAO;AACtB,UAAI,kBAAkB,mBAAmB;AACvC,kBAAU,OAAO,WAAW,IAAI;AAAA,MAClC;AAAA,IACF;AACA,QAAI,WAAW,QAAQ,OAAO,MAAM,cAAc,WAAW;AAE3D,WAAK,YAAY;AACjB,WAAK,UAAU;AACf,WAAK,kBAAkB;AAAA,IACzB,WAAW,KAAK,iBAAiB;AAE/B,WAAK,YAAY;AACjB,WAAK,UAAU;AACf,WAAK,kBAAkB;AAAA,IACzB,WAAW,KAAK,WAAW;AACzB,WAAK,UAAU,MAAM,kBAAkB;AAAA,IACzC;AACA,QAAI,CAAC,KAAK,WAAW;AACnB,kBAAY,SAAS,cAAc,KAAK;AACxC,gBAAU,YAAY;AACtB,UAAI,QAAQ,UAAU;AACtB,YAAM,WAAW;AACjB,YAAM,QAAQ;AACd,YAAM,SAAS;AACf,gBAAU,sBAAsB;AAChC,YAAM,SAAS,QAAQ;AACvB,gBAAU,YAAY,MAAM;AAC5B,cAAQ,OAAO;AACf,YAAM,WAAW;AACjB,YAAM,OAAO;AACb,YAAM,kBAAkB;AACxB,WAAK,YAAY;AACjB,WAAK,UAAU;AAAA,IACjB;AACA,QACE,CAAC,KAAK,mBACN,mBACA,CAAC,KAAK,UAAU,MAAM,iBACtB;AACA,WAAK,UAAU,MAAM,kBAAkB;AAAA,IACzC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,SAAS,YAAY,QAAQ;AACzC,UAAM,UAAU,WAAW,MAAM;AACjC,UAAM,WAAW,YAAY,MAAM;AACnC,UAAM,cAAc,eAAe,MAAM;AACzC,UAAM,aAAa,cAAc,MAAM;AAEvC,UAAe,WAAW,4BAA4B,OAAO;AAC7D,UAAe,WAAW,4BAA4B,QAAQ;AAC9D,UAAe,WAAW,4BAA4B,WAAW;AACjE,UAAe,WAAW,4BAA4B,UAAU;AAEhE,UAAM,WAAW,KAAK;AACtB,UAAe,UAAU,OAAO;AAChC,UAAe,UAAU,QAAQ;AACjC,UAAe,UAAU,WAAW;AACpC,UAAe,UAAU,UAAU;AAEnC,YAAQ,KAAK;AACb,YAAQ,UAAU;AAClB,YAAQ,OAAO,KAAK,MAAM,QAAQ,CAAC,CAAC,GAAG,KAAK,MAAM,QAAQ,CAAC,CAAC,CAAC;AAC7D,YAAQ,OAAO,KAAK,MAAM,SAAS,CAAC,CAAC,GAAG,KAAK,MAAM,SAAS,CAAC,CAAC,CAAC;AAC/D,YAAQ,OAAO,KAAK,MAAM,YAAY,CAAC,CAAC,GAAG,KAAK,MAAM,YAAY,CAAC,CAAC,CAAC;AACrE,YAAQ,OAAO,KAAK,MAAM,WAAW,CAAC,CAAC,GAAG,KAAK,MAAM,WAAW,CAAC,CAAC,CAAC;AACnE,YAAQ,KAAK;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,MAAM,SAAS,YAAY;AAC9C,UAAM,QAAQ,KAAK,SAAS;AAC5B,QAAI,MAAM,YAAY,IAAI,GAAG;AAC3B,YAAM,QAAQ,IAAI;AAAA,QAChB;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AACA,YAAM,cAAc,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,SAAS,YAAY;AAC7B,SAAK,aAAa;AAClB,SAAK,qBAAqBA,mBAAgB,WAAW,SAAS,UAAU;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,SAAS,YAAY;AAC9B,SAAK,qBAAqBA,mBAAgB,YAAY,SAAS,UAAU;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,mBACE,QACA,YACA,UACA,YACA,OACA,QACA,SACA;AACA,UAAM,MAAM,QAAQ;AACpB,UAAM,MAAM,SAAS;AACrB,UAAM,KAAK,aAAa;AACxB,UAAM,KAAK,CAAC;AACZ,UAAM,MAAM,CAAC,OAAO,CAAC,IAAI;AACzB,UAAM,MAAM,CAAC,OAAO,CAAC;AACrB,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,CAAC;AAAA,MACD;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,WAAO,KAAK;AACZ,UAAM,gBAAgB;AAAA,EACxB;AACF;AAEA,IAAOC,iBAAQ;", "names": ["EventType_default", "Layer_default"]}