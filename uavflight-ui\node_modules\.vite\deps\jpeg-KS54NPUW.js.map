{"version": 3, "sources": ["../../geotiff/dist-module/compression/jpeg.js"], "sourcesContent": ["import BaseDecoder from './basedecoder.js';\n\n/* -*- tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- /\n/* vim: set shiftwidth=2 tabstop=2 autoindent cindent expandtab: */\n/*\n   Copyright 2011 notmasteryet\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n       http://www.apache.org/licenses/LICENSE-2.0\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\n*/\n\n// - The JPEG specification can be found in the ITU CCITT Recommendation T.81\n//   (www.w3.org/Graphics/JPEG/itu-t81.pdf)\n// - The JFIF specification can be found in the JPEG File Interchange Format\n//   (www.w3.org/Graphics/JPEG/jfif3.pdf)\n// - The Adobe Application-Specific JPEG markers in the Supporting the DCT Filters\n//   in PostScript Level 2, Technical Note #5116\n//   (partners.adobe.com/public/developer/en/ps/sdk/5116.DCT_Filter.pdf)\n\nconst dctZigZag = new Int32Array([\n  0,\n  1, 8,\n  16, 9, 2,\n  3, 10, 17, 24,\n  32, 25, 18, 11, 4,\n  5, 12, 19, 26, 33, 40,\n  48, 41, 34, 27, 20, 13, 6,\n  7, 14, 21, 28, 35, 42, 49, 56,\n  57, 50, 43, 36, 29, 22, 15,\n  23, 30, 37, 44, 51, 58,\n  59, 52, 45, 38, 31,\n  39, 46, 53, 60,\n  61, 54, 47,\n  55, 62,\n  63,\n]);\n\nconst dctCos1 = 4017; // cos(pi/16)\nconst dctSin1 = 799; // sin(pi/16)\nconst dctCos3 = 3406; // cos(3*pi/16)\nconst dctSin3 = 2276; // sin(3*pi/16)\nconst dctCos6 = 1567; // cos(6*pi/16)\nconst dctSin6 = 3784; // sin(6*pi/16)\nconst dctSqrt2 = 5793; // sqrt(2)\nconst dctSqrt1d2 = 2896;// sqrt(2) / 2\n\nfunction buildHuffmanTable(codeLengths, values) {\n  let k = 0;\n  const code = [];\n  let length = 16;\n  while (length > 0 && !codeLengths[length - 1]) {\n    --length;\n  }\n  code.push({ children: [], index: 0 });\n\n  let p = code[0];\n  let q;\n  for (let i = 0; i < length; i++) {\n    for (let j = 0; j < codeLengths[i]; j++) {\n      p = code.pop();\n      p.children[p.index] = values[k];\n      while (p.index > 0) {\n        p = code.pop();\n      }\n      p.index++;\n      code.push(p);\n      while (code.length <= i) {\n        code.push(q = { children: [], index: 0 });\n        p.children[p.index] = q.children;\n        p = q;\n      }\n      k++;\n    }\n    if (i + 1 < length) {\n      // p here points to last code\n      code.push(q = { children: [], index: 0 });\n      p.children[p.index] = q.children;\n      p = q;\n    }\n  }\n  return code[0].children;\n}\n\nfunction decodeScan(data, initialOffset,\n  frame, components, resetInterval,\n  spectralStart, spectralEnd,\n  successivePrev, successive) {\n  const { mcusPerLine, progressive } = frame;\n\n  const startOffset = initialOffset;\n  let offset = initialOffset;\n  let bitsData = 0;\n  let bitsCount = 0;\n  function readBit() {\n    if (bitsCount > 0) {\n      bitsCount--;\n      return (bitsData >> bitsCount) & 1;\n    }\n    bitsData = data[offset++];\n    if (bitsData === 0xFF) {\n      const nextByte = data[offset++];\n      if (nextByte) {\n        throw new Error(`unexpected marker: ${((bitsData << 8) | nextByte).toString(16)}`);\n      }\n      // unstuff 0\n    }\n    bitsCount = 7;\n    return bitsData >>> 7;\n  }\n  function decodeHuffman(tree) {\n    let node = tree;\n    let bit;\n    while ((bit = readBit()) !== null) { // eslint-disable-line no-cond-assign\n      node = node[bit];\n      if (typeof node === 'number') {\n        return node;\n      }\n      if (typeof node !== 'object') {\n        throw new Error('invalid huffman sequence');\n      }\n    }\n    return null;\n  }\n  function receive(initialLength) {\n    let length = initialLength;\n    let n = 0;\n    while (length > 0) {\n      const bit = readBit();\n      if (bit === null) {\n        return undefined;\n      }\n      n = (n << 1) | bit;\n      --length;\n    }\n    return n;\n  }\n  function receiveAndExtend(length) {\n    const n = receive(length);\n    if (n >= 1 << (length - 1)) {\n      return n;\n    }\n    return n + (-1 << length) + 1;\n  }\n  function decodeBaseline(component, zz) {\n    const t = decodeHuffman(component.huffmanTableDC);\n    const diff = t === 0 ? 0 : receiveAndExtend(t);\n    component.pred += diff;\n    zz[0] = component.pred;\n    let k = 1;\n    while (k < 64) {\n      const rs = decodeHuffman(component.huffmanTableAC);\n      const s = rs & 15;\n      const r = rs >> 4;\n      if (s === 0) {\n        if (r < 15) {\n          break;\n        }\n        k += 16;\n      } else {\n        k += r;\n        const z = dctZigZag[k];\n        zz[z] = receiveAndExtend(s);\n        k++;\n      }\n    }\n  }\n  function decodeDCFirst(component, zz) {\n    const t = decodeHuffman(component.huffmanTableDC);\n    const diff = t === 0 ? 0 : (receiveAndExtend(t) << successive);\n    component.pred += diff;\n    zz[0] = component.pred;\n  }\n  function decodeDCSuccessive(component, zz) {\n    zz[0] |= readBit() << successive;\n  }\n  let eobrun = 0;\n  function decodeACFirst(component, zz) {\n    if (eobrun > 0) {\n      eobrun--;\n      return;\n    }\n    let k = spectralStart;\n    const e = spectralEnd;\n    while (k <= e) {\n      const rs = decodeHuffman(component.huffmanTableAC);\n      const s = rs & 15;\n      const r = rs >> 4;\n      if (s === 0) {\n        if (r < 15) {\n          eobrun = receive(r) + (1 << r) - 1;\n          break;\n        }\n        k += 16;\n      } else {\n        k += r;\n        const z = dctZigZag[k];\n        zz[z] = receiveAndExtend(s) * (1 << successive);\n        k++;\n      }\n    }\n  }\n  let successiveACState = 0;\n  let successiveACNextValue;\n  function decodeACSuccessive(component, zz) {\n    let k = spectralStart;\n    const e = spectralEnd;\n    let r = 0;\n    while (k <= e) {\n      const z = dctZigZag[k];\n      const direction = zz[z] < 0 ? -1 : 1;\n      switch (successiveACState) {\n        case 0: { // initial state\n          const rs = decodeHuffman(component.huffmanTableAC);\n          const s = rs & 15;\n          r = rs >> 4;\n          if (s === 0) {\n            if (r < 15) {\n              eobrun = receive(r) + (1 << r);\n              successiveACState = 4;\n            } else {\n              r = 16;\n              successiveACState = 1;\n            }\n          } else {\n            if (s !== 1) {\n              throw new Error('invalid ACn encoding');\n            }\n            successiveACNextValue = receiveAndExtend(s);\n            successiveACState = r ? 2 : 3;\n          }\n          continue; // eslint-disable-line no-continue\n        }\n        case 1: // skipping r zero items\n        case 2:\n          if (zz[z]) {\n            zz[z] += (readBit() << successive) * direction;\n          } else {\n            r--;\n            if (r === 0) {\n              successiveACState = successiveACState === 2 ? 3 : 0;\n            }\n          }\n          break;\n        case 3: // set value for a zero item\n          if (zz[z]) {\n            zz[z] += (readBit() << successive) * direction;\n          } else {\n            zz[z] = successiveACNextValue << successive;\n            successiveACState = 0;\n          }\n          break;\n        case 4: // eob\n          if (zz[z]) {\n            zz[z] += (readBit() << successive) * direction;\n          }\n          break;\n        default:\n          break;\n      }\n      k++;\n    }\n    if (successiveACState === 4) {\n      eobrun--;\n      if (eobrun === 0) {\n        successiveACState = 0;\n      }\n    }\n  }\n  function decodeMcu(component, decodeFunction, mcu, row, col) {\n    const mcuRow = (mcu / mcusPerLine) | 0;\n    const mcuCol = mcu % mcusPerLine;\n    const blockRow = (mcuRow * component.v) + row;\n    const blockCol = (mcuCol * component.h) + col;\n    decodeFunction(component, component.blocks[blockRow][blockCol]);\n  }\n  function decodeBlock(component, decodeFunction, mcu) {\n    const blockRow = (mcu / component.blocksPerLine) | 0;\n    const blockCol = mcu % component.blocksPerLine;\n    decodeFunction(component, component.blocks[blockRow][blockCol]);\n  }\n\n  const componentsLength = components.length;\n  let component;\n  let i;\n  let j;\n  let k;\n  let n;\n  let decodeFn;\n  if (progressive) {\n    if (spectralStart === 0) {\n      decodeFn = successivePrev === 0 ? decodeDCFirst : decodeDCSuccessive;\n    } else {\n      decodeFn = successivePrev === 0 ? decodeACFirst : decodeACSuccessive;\n    }\n  } else {\n    decodeFn = decodeBaseline;\n  }\n\n  let mcu = 0;\n  let marker;\n  let mcuExpected;\n  if (componentsLength === 1) {\n    mcuExpected = components[0].blocksPerLine * components[0].blocksPerColumn;\n  } else {\n    mcuExpected = mcusPerLine * frame.mcusPerColumn;\n  }\n\n  const usedResetInterval = resetInterval || mcuExpected;\n\n  while (mcu < mcuExpected) {\n    // reset interval stuff\n    for (i = 0; i < componentsLength; i++) {\n      components[i].pred = 0;\n    }\n    eobrun = 0;\n\n    if (componentsLength === 1) {\n      component = components[0];\n      for (n = 0; n < usedResetInterval; n++) {\n        decodeBlock(component, decodeFn, mcu);\n        mcu++;\n      }\n    } else {\n      for (n = 0; n < usedResetInterval; n++) {\n        for (i = 0; i < componentsLength; i++) {\n          component = components[i];\n          const { h, v } = component;\n          for (j = 0; j < v; j++) {\n            for (k = 0; k < h; k++) {\n              decodeMcu(component, decodeFn, mcu, j, k);\n            }\n          }\n        }\n        mcu++;\n\n        // If we've reached our expected MCU's, stop decoding\n        if (mcu === mcuExpected) {\n          break;\n        }\n      }\n    }\n\n    // find marker\n    bitsCount = 0;\n    marker = (data[offset] << 8) | data[offset + 1];\n    if (marker < 0xFF00) {\n      throw new Error('marker was not found');\n    }\n\n    if (marker >= 0xFFD0 && marker <= 0xFFD7) { // RSTx\n      offset += 2;\n    } else {\n      break;\n    }\n  }\n\n  return offset - startOffset;\n}\n\nfunction buildComponentData(frame, component) {\n  const lines = [];\n  const { blocksPerLine, blocksPerColumn } = component;\n  const samplesPerLine = blocksPerLine << 3;\n  const R = new Int32Array(64);\n  const r = new Uint8Array(64);\n\n  // A port of poppler's IDCT method which in turn is taken from:\n  //   Christoph Loeffler, Adriaan Ligtenberg, George S. Moschytz,\n  //   \"Practical Fast 1-D DCT Algorithms with 11 Multiplications\",\n  //   IEEE Intl. Conf. on Acoustics, Speech & Signal Processing, 1989,\n  //   988-991.\n  function quantizeAndInverse(zz, dataOut, dataIn) {\n    const qt = component.quantizationTable;\n    let v0;\n    let v1;\n    let v2;\n    let v3;\n    let v4;\n    let v5;\n    let v6;\n    let v7;\n    let t;\n    const p = dataIn;\n    let i;\n\n    // dequant\n    for (i = 0; i < 64; i++) {\n      p[i] = zz[i] * qt[i];\n    }\n\n    // inverse DCT on rows\n    for (i = 0; i < 8; ++i) {\n      const row = 8 * i;\n\n      // check for all-zero AC coefficients\n      if (p[1 + row] === 0 && p[2 + row] === 0 && p[3 + row] === 0\n        && p[4 + row] === 0 && p[5 + row] === 0 && p[6 + row] === 0\n        && p[7 + row] === 0) {\n        t = ((dctSqrt2 * p[0 + row]) + 512) >> 10;\n        p[0 + row] = t;\n        p[1 + row] = t;\n        p[2 + row] = t;\n        p[3 + row] = t;\n        p[4 + row] = t;\n        p[5 + row] = t;\n        p[6 + row] = t;\n        p[7 + row] = t;\n        continue; // eslint-disable-line no-continue\n      }\n\n      // stage 4\n      v0 = ((dctSqrt2 * p[0 + row]) + 128) >> 8;\n      v1 = ((dctSqrt2 * p[4 + row]) + 128) >> 8;\n      v2 = p[2 + row];\n      v3 = p[6 + row];\n      v4 = ((dctSqrt1d2 * (p[1 + row] - p[7 + row])) + 128) >> 8;\n      v7 = ((dctSqrt1d2 * (p[1 + row] + p[7 + row])) + 128) >> 8;\n      v5 = p[3 + row] << 4;\n      v6 = p[5 + row] << 4;\n\n      // stage 3\n      t = (v0 - v1 + 1) >> 1;\n      v0 = (v0 + v1 + 1) >> 1;\n      v1 = t;\n      t = ((v2 * dctSin6) + (v3 * dctCos6) + 128) >> 8;\n      v2 = ((v2 * dctCos6) - (v3 * dctSin6) + 128) >> 8;\n      v3 = t;\n      t = (v4 - v6 + 1) >> 1;\n      v4 = (v4 + v6 + 1) >> 1;\n      v6 = t;\n      t = (v7 + v5 + 1) >> 1;\n      v5 = (v7 - v5 + 1) >> 1;\n      v7 = t;\n\n      // stage 2\n      t = (v0 - v3 + 1) >> 1;\n      v0 = (v0 + v3 + 1) >> 1;\n      v3 = t;\n      t = (v1 - v2 + 1) >> 1;\n      v1 = (v1 + v2 + 1) >> 1;\n      v2 = t;\n      t = ((v4 * dctSin3) + (v7 * dctCos3) + 2048) >> 12;\n      v4 = ((v4 * dctCos3) - (v7 * dctSin3) + 2048) >> 12;\n      v7 = t;\n      t = ((v5 * dctSin1) + (v6 * dctCos1) + 2048) >> 12;\n      v5 = ((v5 * dctCos1) - (v6 * dctSin1) + 2048) >> 12;\n      v6 = t;\n\n      // stage 1\n      p[0 + row] = v0 + v7;\n      p[7 + row] = v0 - v7;\n      p[1 + row] = v1 + v6;\n      p[6 + row] = v1 - v6;\n      p[2 + row] = v2 + v5;\n      p[5 + row] = v2 - v5;\n      p[3 + row] = v3 + v4;\n      p[4 + row] = v3 - v4;\n    }\n\n    // inverse DCT on columns\n    for (i = 0; i < 8; ++i) {\n      const col = i;\n\n      // check for all-zero AC coefficients\n      if (p[(1 * 8) + col] === 0 && p[(2 * 8) + col] === 0 && p[(3 * 8) + col] === 0\n        && p[(4 * 8) + col] === 0 && p[(5 * 8) + col] === 0 && p[(6 * 8) + col] === 0\n        && p[(7 * 8) + col] === 0) {\n        t = ((dctSqrt2 * dataIn[i + 0]) + 8192) >> 14;\n        p[(0 * 8) + col] = t;\n        p[(1 * 8) + col] = t;\n        p[(2 * 8) + col] = t;\n        p[(3 * 8) + col] = t;\n        p[(4 * 8) + col] = t;\n        p[(5 * 8) + col] = t;\n        p[(6 * 8) + col] = t;\n        p[(7 * 8) + col] = t;\n        continue; // eslint-disable-line no-continue\n      }\n\n      // stage 4\n      v0 = ((dctSqrt2 * p[(0 * 8) + col]) + 2048) >> 12;\n      v1 = ((dctSqrt2 * p[(4 * 8) + col]) + 2048) >> 12;\n      v2 = p[(2 * 8) + col];\n      v3 = p[(6 * 8) + col];\n      v4 = ((dctSqrt1d2 * (p[(1 * 8) + col] - p[(7 * 8) + col])) + 2048) >> 12;\n      v7 = ((dctSqrt1d2 * (p[(1 * 8) + col] + p[(7 * 8) + col])) + 2048) >> 12;\n      v5 = p[(3 * 8) + col];\n      v6 = p[(5 * 8) + col];\n\n      // stage 3\n      t = (v0 - v1 + 1) >> 1;\n      v0 = (v0 + v1 + 1) >> 1;\n      v1 = t;\n      t = ((v2 * dctSin6) + (v3 * dctCos6) + 2048) >> 12;\n      v2 = ((v2 * dctCos6) - (v3 * dctSin6) + 2048) >> 12;\n      v3 = t;\n      t = (v4 - v6 + 1) >> 1;\n      v4 = (v4 + v6 + 1) >> 1;\n      v6 = t;\n      t = (v7 + v5 + 1) >> 1;\n      v5 = (v7 - v5 + 1) >> 1;\n      v7 = t;\n\n      // stage 2\n      t = (v0 - v3 + 1) >> 1;\n      v0 = (v0 + v3 + 1) >> 1;\n      v3 = t;\n      t = (v1 - v2 + 1) >> 1;\n      v1 = (v1 + v2 + 1) >> 1;\n      v2 = t;\n      t = ((v4 * dctSin3) + (v7 * dctCos3) + 2048) >> 12;\n      v4 = ((v4 * dctCos3) - (v7 * dctSin3) + 2048) >> 12;\n      v7 = t;\n      t = ((v5 * dctSin1) + (v6 * dctCos1) + 2048) >> 12;\n      v5 = ((v5 * dctCos1) - (v6 * dctSin1) + 2048) >> 12;\n      v6 = t;\n\n      // stage 1\n      p[(0 * 8) + col] = v0 + v7;\n      p[(7 * 8) + col] = v0 - v7;\n      p[(1 * 8) + col] = v1 + v6;\n      p[(6 * 8) + col] = v1 - v6;\n      p[(2 * 8) + col] = v2 + v5;\n      p[(5 * 8) + col] = v2 - v5;\n      p[(3 * 8) + col] = v3 + v4;\n      p[(4 * 8) + col] = v3 - v4;\n    }\n\n    // convert to 8-bit integers\n    for (i = 0; i < 64; ++i) {\n      const sample = 128 + ((p[i] + 8) >> 4);\n      if (sample < 0) {\n        dataOut[i] = 0;\n      } else if (sample > 0XFF) {\n        dataOut[i] = 0xFF;\n      } else {\n        dataOut[i] = sample;\n      }\n    }\n  }\n\n  for (let blockRow = 0; blockRow < blocksPerColumn; blockRow++) {\n    const scanLine = blockRow << 3;\n    for (let i = 0; i < 8; i++) {\n      lines.push(new Uint8Array(samplesPerLine));\n    }\n    for (let blockCol = 0; blockCol < blocksPerLine; blockCol++) {\n      quantizeAndInverse(component.blocks[blockRow][blockCol], r, R);\n\n      let offset = 0;\n      const sample = blockCol << 3;\n      for (let j = 0; j < 8; j++) {\n        const line = lines[scanLine + j];\n        for (let i = 0; i < 8; i++) {\n          line[sample + i] = r[offset++];\n        }\n      }\n    }\n  }\n  return lines;\n}\n\nclass JpegStreamReader {\n  constructor() {\n    this.jfif = null;\n    this.adobe = null;\n\n    this.quantizationTables = [];\n    this.huffmanTablesAC = [];\n    this.huffmanTablesDC = [];\n    this.resetFrames();\n  }\n\n  resetFrames() {\n    this.frames = [];\n  }\n\n  parse(data) {\n    let offset = 0;\n    // const { length } = data;\n    function readUint16() {\n      const value = (data[offset] << 8) | data[offset + 1];\n      offset += 2;\n      return value;\n    }\n    function readDataBlock() {\n      const length = readUint16();\n      const array = data.subarray(offset, offset + length - 2);\n      offset += array.length;\n      return array;\n    }\n    function prepareComponents(frame) {\n      let maxH = 0;\n      let maxV = 0;\n      let component;\n      let componentId;\n      for (componentId in frame.components) {\n        if (frame.components.hasOwnProperty(componentId)) {\n          component = frame.components[componentId];\n          if (maxH < component.h) {\n            maxH = component.h;\n          }\n          if (maxV < component.v) {\n            maxV = component.v;\n          }\n        }\n      }\n      const mcusPerLine = Math.ceil(frame.samplesPerLine / 8 / maxH);\n      const mcusPerColumn = Math.ceil(frame.scanLines / 8 / maxV);\n      for (componentId in frame.components) {\n        if (frame.components.hasOwnProperty(componentId)) {\n          component = frame.components[componentId];\n          const blocksPerLine = Math.ceil(Math.ceil(frame.samplesPerLine / 8) * component.h / maxH);\n          const blocksPerColumn = Math.ceil(Math.ceil(frame.scanLines / 8) * component.v / maxV);\n          const blocksPerLineForMcu = mcusPerLine * component.h;\n          const blocksPerColumnForMcu = mcusPerColumn * component.v;\n          const blocks = [];\n          for (let i = 0; i < blocksPerColumnForMcu; i++) {\n            const row = [];\n            for (let j = 0; j < blocksPerLineForMcu; j++) {\n              row.push(new Int32Array(64));\n            }\n            blocks.push(row);\n          }\n          component.blocksPerLine = blocksPerLine;\n          component.blocksPerColumn = blocksPerColumn;\n          component.blocks = blocks;\n        }\n      }\n      frame.maxH = maxH;\n      frame.maxV = maxV;\n      frame.mcusPerLine = mcusPerLine;\n      frame.mcusPerColumn = mcusPerColumn;\n    }\n\n    let fileMarker = readUint16();\n    if (fileMarker !== 0xFFD8) { // SOI (Start of Image)\n      throw new Error('SOI not found');\n    }\n\n    fileMarker = readUint16();\n    while (fileMarker !== 0xFFD9) { // EOI (End of image)\n      switch (fileMarker) {\n        case 0xFF00: break;\n        case 0xFFE0: // APP0 (Application Specific)\n        case 0xFFE1: // APP1\n        case 0xFFE2: // APP2\n        case 0xFFE3: // APP3\n        case 0xFFE4: // APP4\n        case 0xFFE5: // APP5\n        case 0xFFE6: // APP6\n        case 0xFFE7: // APP7\n        case 0xFFE8: // APP8\n        case 0xFFE9: // APP9\n        case 0xFFEA: // APP10\n        case 0xFFEB: // APP11\n        case 0xFFEC: // APP12\n        case 0xFFED: // APP13\n        case 0xFFEE: // APP14\n        case 0xFFEF: // APP15\n        case 0xFFFE: { // COM (Comment)\n          const appData = readDataBlock();\n\n          if (fileMarker === 0xFFE0) {\n            if (appData[0] === 0x4A && appData[1] === 0x46 && appData[2] === 0x49\n              && appData[3] === 0x46 && appData[4] === 0) { // 'JFIF\\x00'\n              this.jfif = {\n                version: { major: appData[5], minor: appData[6] },\n                densityUnits: appData[7],\n                xDensity: (appData[8] << 8) | appData[9],\n                yDensity: (appData[10] << 8) | appData[11],\n                thumbWidth: appData[12],\n                thumbHeight: appData[13],\n                thumbData: appData.subarray(14, 14 + (3 * appData[12] * appData[13])),\n              };\n            }\n          }\n          // TODO APP1 - Exif\n          if (fileMarker === 0xFFEE) {\n            if (appData[0] === 0x41 && appData[1] === 0x64 && appData[2] === 0x6F\n              && appData[3] === 0x62 && appData[4] === 0x65 && appData[5] === 0) { // 'Adobe\\x00'\n              this.adobe = {\n                version: appData[6],\n                flags0: (appData[7] << 8) | appData[8],\n                flags1: (appData[9] << 8) | appData[10],\n                transformCode: appData[11],\n              };\n            }\n          }\n          break;\n        }\n\n        case 0xFFDB: { // DQT (Define Quantization Tables)\n          const quantizationTablesLength = readUint16();\n          const quantizationTablesEnd = quantizationTablesLength + offset - 2;\n          while (offset < quantizationTablesEnd) {\n            const quantizationTableSpec = data[offset++];\n            const tableData = new Int32Array(64);\n            if ((quantizationTableSpec >> 4) === 0) { // 8 bit values\n              for (let j = 0; j < 64; j++) {\n                const z = dctZigZag[j];\n                tableData[z] = data[offset++];\n              }\n            } else if ((quantizationTableSpec >> 4) === 1) { // 16 bit\n              for (let j = 0; j < 64; j++) {\n                const z = dctZigZag[j];\n                tableData[z] = readUint16();\n              }\n            } else {\n              throw new Error('DQT: invalid table spec');\n            }\n            this.quantizationTables[quantizationTableSpec & 15] = tableData;\n          }\n          break;\n        }\n\n        case 0xFFC0: // SOF0 (Start of Frame, Baseline DCT)\n        case 0xFFC1: // SOF1 (Start of Frame, Extended DCT)\n        case 0xFFC2: { // SOF2 (Start of Frame, Progressive DCT)\n          readUint16(); // skip data length\n          const frame = {\n            extended: (fileMarker === 0xFFC1),\n            progressive: (fileMarker === 0xFFC2),\n            precision: data[offset++],\n            scanLines: readUint16(),\n            samplesPerLine: readUint16(),\n            components: {},\n            componentsOrder: [],\n          };\n\n          const componentsCount = data[offset++];\n          let componentId;\n          // let maxH = 0;\n          // let maxV = 0;\n          for (let i = 0; i < componentsCount; i++) {\n            componentId = data[offset];\n            const h = data[offset + 1] >> 4;\n            const v = data[offset + 1] & 15;\n            const qId = data[offset + 2];\n            frame.componentsOrder.push(componentId);\n            frame.components[componentId] = {\n              h,\n              v,\n              quantizationIdx: qId,\n            };\n            offset += 3;\n          }\n          prepareComponents(frame);\n          this.frames.push(frame);\n          break;\n        }\n\n        case 0xFFC4: { // DHT (Define Huffman Tables)\n          const huffmanLength = readUint16();\n          for (let i = 2; i < huffmanLength;) {\n            const huffmanTableSpec = data[offset++];\n            const codeLengths = new Uint8Array(16);\n            let codeLengthSum = 0;\n            for (let j = 0; j < 16; j++, offset++) {\n              codeLengths[j] = data[offset];\n              codeLengthSum += codeLengths[j];\n            }\n            const huffmanValues = new Uint8Array(codeLengthSum);\n            for (let j = 0; j < codeLengthSum; j++, offset++) {\n              huffmanValues[j] = data[offset];\n            }\n            i += 17 + codeLengthSum;\n\n            if ((huffmanTableSpec >> 4) === 0) {\n              this.huffmanTablesDC[huffmanTableSpec & 15] = buildHuffmanTable(\n                codeLengths, huffmanValues,\n              );\n            } else {\n              this.huffmanTablesAC[huffmanTableSpec & 15] = buildHuffmanTable(\n                codeLengths, huffmanValues,\n              );\n            }\n          }\n          break;\n        }\n\n        case 0xFFDD: // DRI (Define Restart Interval)\n          readUint16(); // skip data length\n          this.resetInterval = readUint16();\n          break;\n\n        case 0xFFDA: { // SOS (Start of Scan)\n          readUint16(); // skip length\n          const selectorsCount = data[offset++];\n          const components = [];\n          const frame = this.frames[0];\n          for (let i = 0; i < selectorsCount; i++) {\n            const component = frame.components[data[offset++]];\n            const tableSpec = data[offset++];\n            component.huffmanTableDC = this.huffmanTablesDC[tableSpec >> 4];\n            component.huffmanTableAC = this.huffmanTablesAC[tableSpec & 15];\n            components.push(component);\n          }\n          const spectralStart = data[offset++];\n          const spectralEnd = data[offset++];\n          const successiveApproximation = data[offset++];\n          const processed = decodeScan(data, offset,\n            frame, components, this.resetInterval,\n            spectralStart, spectralEnd,\n            successiveApproximation >> 4, successiveApproximation & 15);\n          offset += processed;\n          break;\n        }\n\n        case 0xFFFF: // Fill bytes\n          if (data[offset] !== 0xFF) { // Avoid skipping a valid marker.\n            offset--;\n          }\n          break;\n\n        default:\n          if (data[offset - 3] === 0xFF\n            && data[offset - 2] >= 0xC0 && data[offset - 2] <= 0xFE) {\n            // could be incorrect encoding -- last 0xFF byte of the previous\n            // block was eaten by the encoder\n            offset -= 3;\n            break;\n          }\n          throw new Error(`unknown JPEG marker ${fileMarker.toString(16)}`);\n      }\n      fileMarker = readUint16();\n    }\n  }\n\n  getResult() {\n    const { frames } = this;\n    if (this.frames.length === 0) {\n      throw new Error('no frames were decoded');\n    } else if (this.frames.length > 1) {\n      console.warn('more than one frame is not supported');\n    }\n\n    // set each frame's components quantization table\n    for (let i = 0; i < this.frames.length; i++) {\n      const cp = this.frames[i].components;\n      for (const j of Object.keys(cp)) {\n        cp[j].quantizationTable = this.quantizationTables[cp[j].quantizationIdx];\n        delete cp[j].quantizationIdx;\n      }\n    }\n\n    const frame = frames[0];\n    const { components, componentsOrder } = frame;\n    const outComponents = [];\n    const width = frame.samplesPerLine;\n    const height = frame.scanLines;\n\n    for (let i = 0; i < componentsOrder.length; i++) {\n      const component = components[componentsOrder[i]];\n      outComponents.push({\n        lines: buildComponentData(frame, component),\n        scaleX: component.h / frame.maxH,\n        scaleY: component.v / frame.maxV,\n      });\n    }\n\n    const out = new Uint8Array(width * height * outComponents.length);\n    let oi = 0;\n    for (let y = 0; y < height; ++y) {\n      for (let x = 0; x < width; ++x) {\n        for (let i = 0; i < outComponents.length; ++i) {\n          const component = outComponents[i];\n          out[oi] = component.lines[0 | y * component.scaleY][0 | x * component.scaleX];\n          ++oi;\n        }\n      }\n    }\n    return out;\n  }\n}\n\nexport default class JpegDecoder extends BaseDecoder {\n  constructor(fileDirectory) {\n    super();\n    this.reader = new JpegStreamReader();\n    if (fileDirectory.JPEGTables) {\n      this.reader.parse(fileDirectory.JPEGTables);\n    }\n  }\n\n  decodeBlock(buffer) {\n    this.reader.resetFrames();\n    this.reader.parse(new Uint8Array(buffer));\n    return this.reader.getResult().buffer;\n  }\n}\n"], "mappings": ";;;;;;AAyBA,IAAM,YAAY,IAAI,WAAW;AAAA,EAC/B;AAAA,EACA;AAAA,EAAG;AAAA,EACH;AAAA,EAAI;AAAA,EAAG;AAAA,EACP;AAAA,EAAG;AAAA,EAAI;AAAA,EAAI;AAAA,EACX;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAChB;AAAA,EAAG;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EACnB;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EACxB;AAAA,EAAG;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAC3B;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EACxB;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EACpB;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAChB;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EACZ;AAAA,EAAI;AAAA,EAAI;AAAA,EACR;AAAA,EAAI;AAAA,EACJ;AACF,CAAC;AAED,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,IAAM,aAAa;AAEnB,SAAS,kBAAkB,aAAa,QAAQ;AAC9C,MAAI,IAAI;AACR,QAAM,OAAO,CAAC;AACd,MAAI,SAAS;AACb,SAAO,SAAS,KAAK,CAAC,YAAY,SAAS,CAAC,GAAG;AAC7C,MAAE;AAAA,EACJ;AACA,OAAK,KAAK,EAAE,UAAU,CAAC,GAAG,OAAO,EAAE,CAAC;AAEpC,MAAI,IAAI,KAAK,CAAC;AACd,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,aAAS,IAAI,GAAG,IAAI,YAAY,CAAC,GAAG,KAAK;AACvC,UAAI,KAAK,IAAI;AACb,QAAE,SAAS,EAAE,KAAK,IAAI,OAAO,CAAC;AAC9B,aAAO,EAAE,QAAQ,GAAG;AAClB,YAAI,KAAK,IAAI;AAAA,MACf;AACA,QAAE;AACF,WAAK,KAAK,CAAC;AACX,aAAO,KAAK,UAAU,GAAG;AACvB,aAAK,KAAK,IAAI,EAAE,UAAU,CAAC,GAAG,OAAO,EAAE,CAAC;AACxC,UAAE,SAAS,EAAE,KAAK,IAAI,EAAE;AACxB,YAAI;AAAA,MACN;AACA;AAAA,IACF;AACA,QAAI,IAAI,IAAI,QAAQ;AAElB,WAAK,KAAK,IAAI,EAAE,UAAU,CAAC,GAAG,OAAO,EAAE,CAAC;AACxC,QAAE,SAAS,EAAE,KAAK,IAAI,EAAE;AACxB,UAAI;AAAA,IACN;AAAA,EACF;AACA,SAAO,KAAK,CAAC,EAAE;AACjB;AAEA,SAAS,WAAW,MAAM,eACxB,OAAO,YAAY,eACnB,eAAe,aACf,gBAAgB,YAAY;AAC5B,QAAM,EAAE,aAAa,YAAY,IAAI;AAErC,QAAM,cAAc;AACpB,MAAI,SAAS;AACb,MAAI,WAAW;AACf,MAAI,YAAY;AAChB,WAAS,UAAU;AACjB,QAAI,YAAY,GAAG;AACjB;AACA,aAAQ,YAAY,YAAa;AAAA,IACnC;AACA,eAAW,KAAK,QAAQ;AACxB,QAAI,aAAa,KAAM;AACrB,YAAM,WAAW,KAAK,QAAQ;AAC9B,UAAI,UAAU;AACZ,cAAM,IAAI,MAAM,uBAAwB,YAAY,IAAK,UAAU,SAAS,EAAE,CAAC,EAAE;AAAA,MACnF;AAAA,IAEF;AACA,gBAAY;AACZ,WAAO,aAAa;AAAA,EACtB;AACA,WAAS,cAAc,MAAM;AAC3B,QAAI,OAAO;AACX,QAAI;AACJ,YAAQ,MAAM,QAAQ,OAAO,MAAM;AACjC,aAAO,KAAK,GAAG;AACf,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO;AAAA,MACT;AACA,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC5C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,WAAS,QAAQ,eAAe;AAC9B,QAAI,SAAS;AACb,QAAIA,KAAI;AACR,WAAO,SAAS,GAAG;AACjB,YAAM,MAAM,QAAQ;AACpB,UAAI,QAAQ,MAAM;AAChB,eAAO;AAAA,MACT;AACA,MAAAA,KAAKA,MAAK,IAAK;AACf,QAAE;AAAA,IACJ;AACA,WAAOA;AAAA,EACT;AACA,WAAS,iBAAiB,QAAQ;AAChC,UAAMA,KAAI,QAAQ,MAAM;AACxB,QAAIA,MAAK,KAAM,SAAS,GAAI;AAC1B,aAAOA;AAAA,IACT;AACA,WAAOA,MAAK,MAAM,UAAU;AAAA,EAC9B;AACA,WAAS,eAAeC,YAAW,IAAI;AACrC,UAAM,IAAI,cAAcA,WAAU,cAAc;AAChD,UAAM,OAAO,MAAM,IAAI,IAAI,iBAAiB,CAAC;AAC7C,IAAAA,WAAU,QAAQ;AAClB,OAAG,CAAC,IAAIA,WAAU;AAClB,QAAIC,KAAI;AACR,WAAOA,KAAI,IAAI;AACb,YAAM,KAAK,cAAcD,WAAU,cAAc;AACjD,YAAM,IAAI,KAAK;AACf,YAAM,IAAI,MAAM;AAChB,UAAI,MAAM,GAAG;AACX,YAAI,IAAI,IAAI;AACV;AAAA,QACF;AACA,QAAAC,MAAK;AAAA,MACP,OAAO;AACL,QAAAA,MAAK;AACL,cAAM,IAAI,UAAUA,EAAC;AACrB,WAAG,CAAC,IAAI,iBAAiB,CAAC;AAC1B,QAAAA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,WAAS,cAAcD,YAAW,IAAI;AACpC,UAAM,IAAI,cAAcA,WAAU,cAAc;AAChD,UAAM,OAAO,MAAM,IAAI,IAAK,iBAAiB,CAAC,KAAK;AACnD,IAAAA,WAAU,QAAQ;AAClB,OAAG,CAAC,IAAIA,WAAU;AAAA,EACpB;AACA,WAAS,mBAAmBA,YAAW,IAAI;AACzC,OAAG,CAAC,KAAK,QAAQ,KAAK;AAAA,EACxB;AACA,MAAI,SAAS;AACb,WAAS,cAAcA,YAAW,IAAI;AACpC,QAAI,SAAS,GAAG;AACd;AACA;AAAA,IACF;AACA,QAAIC,KAAI;AACR,UAAM,IAAI;AACV,WAAOA,MAAK,GAAG;AACb,YAAM,KAAK,cAAcD,WAAU,cAAc;AACjD,YAAM,IAAI,KAAK;AACf,YAAM,IAAI,MAAM;AAChB,UAAI,MAAM,GAAG;AACX,YAAI,IAAI,IAAI;AACV,mBAAS,QAAQ,CAAC,KAAK,KAAK,KAAK;AACjC;AAAA,QACF;AACA,QAAAC,MAAK;AAAA,MACP,OAAO;AACL,QAAAA,MAAK;AACL,cAAM,IAAI,UAAUA,EAAC;AACrB,WAAG,CAAC,IAAI,iBAAiB,CAAC,KAAK,KAAK;AACpC,QAAAA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,oBAAoB;AACxB,MAAI;AACJ,WAAS,mBAAmBD,YAAW,IAAI;AACzC,QAAIC,KAAI;AACR,UAAM,IAAI;AACV,QAAI,IAAI;AACR,WAAOA,MAAK,GAAG;AACb,YAAM,IAAI,UAAUA,EAAC;AACrB,YAAM,YAAY,GAAG,CAAC,IAAI,IAAI,KAAK;AACnC,cAAQ,mBAAmB;AAAA,QACzB,KAAK,GAAG;AACN,gBAAM,KAAK,cAAcD,WAAU,cAAc;AACjD,gBAAM,IAAI,KAAK;AACf,cAAI,MAAM;AACV,cAAI,MAAM,GAAG;AACX,gBAAI,IAAI,IAAI;AACV,uBAAS,QAAQ,CAAC,KAAK,KAAK;AAC5B,kCAAoB;AAAA,YACtB,OAAO;AACL,kBAAI;AACJ,kCAAoB;AAAA,YACtB;AAAA,UACF,OAAO;AACL,gBAAI,MAAM,GAAG;AACX,oBAAM,IAAI,MAAM,sBAAsB;AAAA,YACxC;AACA,oCAAwB,iBAAiB,CAAC;AAC1C,gCAAoB,IAAI,IAAI;AAAA,UAC9B;AACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AACH,cAAI,GAAG,CAAC,GAAG;AACT,eAAG,CAAC,MAAM,QAAQ,KAAK,cAAc;AAAA,UACvC,OAAO;AACL;AACA,gBAAI,MAAM,GAAG;AACX,kCAAoB,sBAAsB,IAAI,IAAI;AAAA,YACpD;AAAA,UACF;AACA;AAAA,QACF,KAAK;AACH,cAAI,GAAG,CAAC,GAAG;AACT,eAAG,CAAC,MAAM,QAAQ,KAAK,cAAc;AAAA,UACvC,OAAO;AACL,eAAG,CAAC,IAAI,yBAAyB;AACjC,gCAAoB;AAAA,UACtB;AACA;AAAA,QACF,KAAK;AACH,cAAI,GAAG,CAAC,GAAG;AACT,eAAG,CAAC,MAAM,QAAQ,KAAK,cAAc;AAAA,UACvC;AACA;AAAA,QACF;AACE;AAAA,MACJ;AACA,MAAAC;AAAA,IACF;AACA,QAAI,sBAAsB,GAAG;AAC3B;AACA,UAAI,WAAW,GAAG;AAChB,4BAAoB;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACA,WAAS,UAAUD,YAAW,gBAAgBE,MAAK,KAAK,KAAK;AAC3D,UAAM,SAAUA,OAAM,cAAe;AACrC,UAAM,SAASA,OAAM;AACrB,UAAM,WAAY,SAASF,WAAU,IAAK;AAC1C,UAAM,WAAY,SAASA,WAAU,IAAK;AAC1C,mBAAeA,YAAWA,WAAU,OAAO,QAAQ,EAAE,QAAQ,CAAC;AAAA,EAChE;AACA,WAAS,YAAYA,YAAW,gBAAgBE,MAAK;AACnD,UAAM,WAAYA,OAAMF,WAAU,gBAAiB;AACnD,UAAM,WAAWE,OAAMF,WAAU;AACjC,mBAAeA,YAAWA,WAAU,OAAO,QAAQ,EAAE,QAAQ,CAAC;AAAA,EAChE;AAEA,QAAM,mBAAmB,WAAW;AACpC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,aAAa;AACf,QAAI,kBAAkB,GAAG;AACvB,iBAAW,mBAAmB,IAAI,gBAAgB;AAAA,IACpD,OAAO;AACL,iBAAW,mBAAmB,IAAI,gBAAgB;AAAA,IACpD;AAAA,EACF,OAAO;AACL,eAAW;AAAA,EACb;AAEA,MAAI,MAAM;AACV,MAAI;AACJ,MAAI;AACJ,MAAI,qBAAqB,GAAG;AAC1B,kBAAc,WAAW,CAAC,EAAE,gBAAgB,WAAW,CAAC,EAAE;AAAA,EAC5D,OAAO;AACL,kBAAc,cAAc,MAAM;AAAA,EACpC;AAEA,QAAM,oBAAoB,iBAAiB;AAE3C,SAAO,MAAM,aAAa;AAExB,SAAK,IAAI,GAAG,IAAI,kBAAkB,KAAK;AACrC,iBAAW,CAAC,EAAE,OAAO;AAAA,IACvB;AACA,aAAS;AAET,QAAI,qBAAqB,GAAG;AAC1B,kBAAY,WAAW,CAAC;AACxB,WAAK,IAAI,GAAG,IAAI,mBAAmB,KAAK;AACtC,oBAAY,WAAW,UAAU,GAAG;AACpC;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,IAAI,GAAG,IAAI,mBAAmB,KAAK;AACtC,aAAK,IAAI,GAAG,IAAI,kBAAkB,KAAK;AACrC,sBAAY,WAAW,CAAC;AACxB,gBAAM,EAAE,GAAG,EAAE,IAAI;AACjB,eAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,iBAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,wBAAU,WAAW,UAAU,KAAK,GAAG,CAAC;AAAA,YAC1C;AAAA,UACF;AAAA,QACF;AACA;AAGA,YAAI,QAAQ,aAAa;AACvB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,gBAAY;AACZ,aAAU,KAAK,MAAM,KAAK,IAAK,KAAK,SAAS,CAAC;AAC9C,QAAI,SAAS,OAAQ;AACnB,YAAM,IAAI,MAAM,sBAAsB;AAAA,IACxC;AAEA,QAAI,UAAU,SAAU,UAAU,OAAQ;AACxC,gBAAU;AAAA,IACZ,OAAO;AACL;AAAA,IACF;AAAA,EACF;AAEA,SAAO,SAAS;AAClB;AAEA,SAAS,mBAAmB,OAAO,WAAW;AAC5C,QAAM,QAAQ,CAAC;AACf,QAAM,EAAE,eAAe,gBAAgB,IAAI;AAC3C,QAAM,iBAAiB,iBAAiB;AACxC,QAAM,IAAI,IAAI,WAAW,EAAE;AAC3B,QAAM,IAAI,IAAI,WAAW,EAAE;AAO3B,WAAS,mBAAmB,IAAI,SAAS,QAAQ;AAC/C,UAAM,KAAK,UAAU;AACrB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,UAAM,IAAI;AACV,QAAI;AAGJ,SAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,QAAE,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC;AAAA,IACrB;AAGA,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,YAAM,MAAM,IAAI;AAGhB,UAAI,EAAE,IAAI,GAAG,MAAM,KAAK,EAAE,IAAI,GAAG,MAAM,KAAK,EAAE,IAAI,GAAG,MAAM,KACtD,EAAE,IAAI,GAAG,MAAM,KAAK,EAAE,IAAI,GAAG,MAAM,KAAK,EAAE,IAAI,GAAG,MAAM,KACvD,EAAE,IAAI,GAAG,MAAM,GAAG;AACrB,YAAM,WAAW,EAAE,IAAI,GAAG,IAAK,OAAQ;AACvC,UAAE,IAAI,GAAG,IAAI;AACb,UAAE,IAAI,GAAG,IAAI;AACb,UAAE,IAAI,GAAG,IAAI;AACb,UAAE,IAAI,GAAG,IAAI;AACb,UAAE,IAAI,GAAG,IAAI;AACb,UAAE,IAAI,GAAG,IAAI;AACb,UAAE,IAAI,GAAG,IAAI;AACb,UAAE,IAAI,GAAG,IAAI;AACb;AAAA,MACF;AAGA,WAAO,WAAW,EAAE,IAAI,GAAG,IAAK,OAAQ;AACxC,WAAO,WAAW,EAAE,IAAI,GAAG,IAAK,OAAQ;AACxC,WAAK,EAAE,IAAI,GAAG;AACd,WAAK,EAAE,IAAI,GAAG;AACd,WAAO,cAAc,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,KAAM,OAAQ;AACzD,WAAO,cAAc,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,KAAM,OAAQ;AACzD,WAAK,EAAE,IAAI,GAAG,KAAK;AACnB,WAAK,EAAE,IAAI,GAAG,KAAK;AAGnB,UAAK,KAAK,KAAK,KAAM;AACrB,WAAM,KAAK,KAAK,KAAM;AACtB,WAAK;AACL,UAAM,KAAK,UAAY,KAAK,UAAW,OAAQ;AAC/C,WAAO,KAAK,UAAY,KAAK,UAAW,OAAQ;AAChD,WAAK;AACL,UAAK,KAAK,KAAK,KAAM;AACrB,WAAM,KAAK,KAAK,KAAM;AACtB,WAAK;AACL,UAAK,KAAK,KAAK,KAAM;AACrB,WAAM,KAAK,KAAK,KAAM;AACtB,WAAK;AAGL,UAAK,KAAK,KAAK,KAAM;AACrB,WAAM,KAAK,KAAK,KAAM;AACtB,WAAK;AACL,UAAK,KAAK,KAAK,KAAM;AACrB,WAAM,KAAK,KAAK,KAAM;AACtB,WAAK;AACL,UAAM,KAAK,UAAY,KAAK,UAAW,QAAS;AAChD,WAAO,KAAK,UAAY,KAAK,UAAW,QAAS;AACjD,WAAK;AACL,UAAM,KAAK,UAAY,KAAK,UAAW,QAAS;AAChD,WAAO,KAAK,UAAY,KAAK,UAAW,QAAS;AACjD,WAAK;AAGL,QAAE,IAAI,GAAG,IAAI,KAAK;AAClB,QAAE,IAAI,GAAG,IAAI,KAAK;AAClB,QAAE,IAAI,GAAG,IAAI,KAAK;AAClB,QAAE,IAAI,GAAG,IAAI,KAAK;AAClB,QAAE,IAAI,GAAG,IAAI,KAAK;AAClB,QAAE,IAAI,GAAG,IAAI,KAAK;AAClB,QAAE,IAAI,GAAG,IAAI,KAAK;AAClB,QAAE,IAAI,GAAG,IAAI,KAAK;AAAA,IACpB;AAGA,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,YAAM,MAAM;AAGZ,UAAI,EAAG,IAAI,IAAK,GAAG,MAAM,KAAK,EAAG,IAAI,IAAK,GAAG,MAAM,KAAK,EAAG,IAAI,IAAK,GAAG,MAAM,KACxE,EAAG,IAAI,IAAK,GAAG,MAAM,KAAK,EAAG,IAAI,IAAK,GAAG,MAAM,KAAK,EAAG,IAAI,IAAK,GAAG,MAAM,KACzE,EAAG,IAAI,IAAK,GAAG,MAAM,GAAG;AAC3B,YAAM,WAAW,OAAO,IAAI,CAAC,IAAK,QAAS;AAC3C,UAAG,IAAI,IAAK,GAAG,IAAI;AACnB,UAAG,IAAI,IAAK,GAAG,IAAI;AACnB,UAAG,IAAI,IAAK,GAAG,IAAI;AACnB,UAAG,IAAI,IAAK,GAAG,IAAI;AACnB,UAAG,IAAI,IAAK,GAAG,IAAI;AACnB,UAAG,IAAI,IAAK,GAAG,IAAI;AACnB,UAAG,IAAI,IAAK,GAAG,IAAI;AACnB,UAAG,IAAI,IAAK,GAAG,IAAI;AACnB;AAAA,MACF;AAGA,WAAO,WAAW,EAAG,IAAI,IAAK,GAAG,IAAK,QAAS;AAC/C,WAAO,WAAW,EAAG,IAAI,IAAK,GAAG,IAAK,QAAS;AAC/C,WAAK,EAAG,IAAI,IAAK,GAAG;AACpB,WAAK,EAAG,IAAI,IAAK,GAAG;AACpB,WAAO,cAAc,EAAG,IAAI,IAAK,GAAG,IAAI,EAAG,IAAI,IAAK,GAAG,KAAM,QAAS;AACtE,WAAO,cAAc,EAAG,IAAI,IAAK,GAAG,IAAI,EAAG,IAAI,IAAK,GAAG,KAAM,QAAS;AACtE,WAAK,EAAG,IAAI,IAAK,GAAG;AACpB,WAAK,EAAG,IAAI,IAAK,GAAG;AAGpB,UAAK,KAAK,KAAK,KAAM;AACrB,WAAM,KAAK,KAAK,KAAM;AACtB,WAAK;AACL,UAAM,KAAK,UAAY,KAAK,UAAW,QAAS;AAChD,WAAO,KAAK,UAAY,KAAK,UAAW,QAAS;AACjD,WAAK;AACL,UAAK,KAAK,KAAK,KAAM;AACrB,WAAM,KAAK,KAAK,KAAM;AACtB,WAAK;AACL,UAAK,KAAK,KAAK,KAAM;AACrB,WAAM,KAAK,KAAK,KAAM;AACtB,WAAK;AAGL,UAAK,KAAK,KAAK,KAAM;AACrB,WAAM,KAAK,KAAK,KAAM;AACtB,WAAK;AACL,UAAK,KAAK,KAAK,KAAM;AACrB,WAAM,KAAK,KAAK,KAAM;AACtB,WAAK;AACL,UAAM,KAAK,UAAY,KAAK,UAAW,QAAS;AAChD,WAAO,KAAK,UAAY,KAAK,UAAW,QAAS;AACjD,WAAK;AACL,UAAM,KAAK,UAAY,KAAK,UAAW,QAAS;AAChD,WAAO,KAAK,UAAY,KAAK,UAAW,QAAS;AACjD,WAAK;AAGL,QAAG,IAAI,IAAK,GAAG,IAAI,KAAK;AACxB,QAAG,IAAI,IAAK,GAAG,IAAI,KAAK;AACxB,QAAG,IAAI,IAAK,GAAG,IAAI,KAAK;AACxB,QAAG,IAAI,IAAK,GAAG,IAAI,KAAK;AACxB,QAAG,IAAI,IAAK,GAAG,IAAI,KAAK;AACxB,QAAG,IAAI,IAAK,GAAG,IAAI,KAAK;AACxB,QAAG,IAAI,IAAK,GAAG,IAAI,KAAK;AACxB,QAAG,IAAI,IAAK,GAAG,IAAI,KAAK;AAAA,IAC1B;AAGA,SAAK,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACvB,YAAM,SAAS,OAAQ,EAAE,CAAC,IAAI,KAAM;AACpC,UAAI,SAAS,GAAG;AACd,gBAAQ,CAAC,IAAI;AAAA,MACf,WAAW,SAAS,KAAM;AACxB,gBAAQ,CAAC,IAAI;AAAA,MACf,OAAO;AACL,gBAAQ,CAAC,IAAI;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAEA,WAAS,WAAW,GAAG,WAAW,iBAAiB,YAAY;AAC7D,UAAM,WAAW,YAAY;AAC7B,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,KAAK,IAAI,WAAW,cAAc,CAAC;AAAA,IAC3C;AACA,aAAS,WAAW,GAAG,WAAW,eAAe,YAAY;AAC3D,yBAAmB,UAAU,OAAO,QAAQ,EAAE,QAAQ,GAAG,GAAG,CAAC;AAE7D,UAAI,SAAS;AACb,YAAM,SAAS,YAAY;AAC3B,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAM,OAAO,MAAM,WAAW,CAAC;AAC/B,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,eAAK,SAAS,CAAC,IAAI,EAAE,QAAQ;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,mBAAN,MAAuB;AAAA,EACrB,cAAc;AACZ,SAAK,OAAO;AACZ,SAAK,QAAQ;AAEb,SAAK,qBAAqB,CAAC;AAC3B,SAAK,kBAAkB,CAAC;AACxB,SAAK,kBAAkB,CAAC;AACxB,SAAK,YAAY;AAAA,EACnB;AAAA,EAEA,cAAc;AACZ,SAAK,SAAS,CAAC;AAAA,EACjB;AAAA,EAEA,MAAM,MAAM;AACV,QAAI,SAAS;AAEb,aAAS,aAAa;AACpB,YAAM,QAAS,KAAK,MAAM,KAAK,IAAK,KAAK,SAAS,CAAC;AACnD,gBAAU;AACV,aAAO;AAAA,IACT;AACA,aAAS,gBAAgB;AACvB,YAAM,SAAS,WAAW;AAC1B,YAAM,QAAQ,KAAK,SAAS,QAAQ,SAAS,SAAS,CAAC;AACvD,gBAAU,MAAM;AAChB,aAAO;AAAA,IACT;AACA,aAAS,kBAAkB,OAAO;AAChC,UAAI,OAAO;AACX,UAAI,OAAO;AACX,UAAI;AACJ,UAAI;AACJ,WAAK,eAAe,MAAM,YAAY;AACpC,YAAI,MAAM,WAAW,eAAe,WAAW,GAAG;AAChD,sBAAY,MAAM,WAAW,WAAW;AACxC,cAAI,OAAO,UAAU,GAAG;AACtB,mBAAO,UAAU;AAAA,UACnB;AACA,cAAI,OAAO,UAAU,GAAG;AACtB,mBAAO,UAAU;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AACA,YAAM,cAAc,KAAK,KAAK,MAAM,iBAAiB,IAAI,IAAI;AAC7D,YAAM,gBAAgB,KAAK,KAAK,MAAM,YAAY,IAAI,IAAI;AAC1D,WAAK,eAAe,MAAM,YAAY;AACpC,YAAI,MAAM,WAAW,eAAe,WAAW,GAAG;AAChD,sBAAY,MAAM,WAAW,WAAW;AACxC,gBAAM,gBAAgB,KAAK,KAAK,KAAK,KAAK,MAAM,iBAAiB,CAAC,IAAI,UAAU,IAAI,IAAI;AACxF,gBAAM,kBAAkB,KAAK,KAAK,KAAK,KAAK,MAAM,YAAY,CAAC,IAAI,UAAU,IAAI,IAAI;AACrF,gBAAM,sBAAsB,cAAc,UAAU;AACpD,gBAAM,wBAAwB,gBAAgB,UAAU;AACxD,gBAAM,SAAS,CAAC;AAChB,mBAAS,IAAI,GAAG,IAAI,uBAAuB,KAAK;AAC9C,kBAAM,MAAM,CAAC;AACb,qBAAS,IAAI,GAAG,IAAI,qBAAqB,KAAK;AAC5C,kBAAI,KAAK,IAAI,WAAW,EAAE,CAAC;AAAA,YAC7B;AACA,mBAAO,KAAK,GAAG;AAAA,UACjB;AACA,oBAAU,gBAAgB;AAC1B,oBAAU,kBAAkB;AAC5B,oBAAU,SAAS;AAAA,QACrB;AAAA,MACF;AACA,YAAM,OAAO;AACb,YAAM,OAAO;AACb,YAAM,cAAc;AACpB,YAAM,gBAAgB;AAAA,IACxB;AAEA,QAAI,aAAa,WAAW;AAC5B,QAAI,eAAe,OAAQ;AACzB,YAAM,IAAI,MAAM,eAAe;AAAA,IACjC;AAEA,iBAAa,WAAW;AACxB,WAAO,eAAe,OAAQ;AAC5B,cAAQ,YAAY;AAAA,QAClB,KAAK;AAAQ;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,OAAQ;AACX,gBAAM,UAAU,cAAc;AAE9B,cAAI,eAAe,OAAQ;AACzB,gBAAI,QAAQ,CAAC,MAAM,MAAQ,QAAQ,CAAC,MAAM,MAAQ,QAAQ,CAAC,MAAM,MAC5D,QAAQ,CAAC,MAAM,MAAQ,QAAQ,CAAC,MAAM,GAAG;AAC5C,mBAAK,OAAO;AAAA,gBACV,SAAS,EAAE,OAAO,QAAQ,CAAC,GAAG,OAAO,QAAQ,CAAC,EAAE;AAAA,gBAChD,cAAc,QAAQ,CAAC;AAAA,gBACvB,UAAW,QAAQ,CAAC,KAAK,IAAK,QAAQ,CAAC;AAAA,gBACvC,UAAW,QAAQ,EAAE,KAAK,IAAK,QAAQ,EAAE;AAAA,gBACzC,YAAY,QAAQ,EAAE;AAAA,gBACtB,aAAa,QAAQ,EAAE;AAAA,gBACvB,WAAW,QAAQ,SAAS,IAAI,KAAM,IAAI,QAAQ,EAAE,IAAI,QAAQ,EAAE,CAAE;AAAA,cACtE;AAAA,YACF;AAAA,UACF;AAEA,cAAI,eAAe,OAAQ;AACzB,gBAAI,QAAQ,CAAC,MAAM,MAAQ,QAAQ,CAAC,MAAM,OAAQ,QAAQ,CAAC,MAAM,OAC5D,QAAQ,CAAC,MAAM,MAAQ,QAAQ,CAAC,MAAM,OAAQ,QAAQ,CAAC,MAAM,GAAG;AACnE,mBAAK,QAAQ;AAAA,gBACX,SAAS,QAAQ,CAAC;AAAA,gBAClB,QAAS,QAAQ,CAAC,KAAK,IAAK,QAAQ,CAAC;AAAA,gBACrC,QAAS,QAAQ,CAAC,KAAK,IAAK,QAAQ,EAAE;AAAA,gBACtC,eAAe,QAAQ,EAAE;AAAA,cAC3B;AAAA,YACF;AAAA,UACF;AACA;AAAA,QACF;AAAA,QAEA,KAAK,OAAQ;AACX,gBAAM,2BAA2B,WAAW;AAC5C,gBAAM,wBAAwB,2BAA2B,SAAS;AAClE,iBAAO,SAAS,uBAAuB;AACrC,kBAAM,wBAAwB,KAAK,QAAQ;AAC3C,kBAAM,YAAY,IAAI,WAAW,EAAE;AACnC,gBAAK,yBAAyB,MAAO,GAAG;AACtC,uBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,sBAAM,IAAI,UAAU,CAAC;AACrB,0BAAU,CAAC,IAAI,KAAK,QAAQ;AAAA,cAC9B;AAAA,YACF,WAAY,yBAAyB,MAAO,GAAG;AAC7C,uBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,sBAAM,IAAI,UAAU,CAAC;AACrB,0BAAU,CAAC,IAAI,WAAW;AAAA,cAC5B;AAAA,YACF,OAAO;AACL,oBAAM,IAAI,MAAM,yBAAyB;AAAA,YAC3C;AACA,iBAAK,mBAAmB,wBAAwB,EAAE,IAAI;AAAA,UACxD;AACA;AAAA,QACF;AAAA,QAEA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,OAAQ;AACX,qBAAW;AACX,gBAAM,QAAQ;AAAA,YACZ,UAAW,eAAe;AAAA,YAC1B,aAAc,eAAe;AAAA,YAC7B,WAAW,KAAK,QAAQ;AAAA,YACxB,WAAW,WAAW;AAAA,YACtB,gBAAgB,WAAW;AAAA,YAC3B,YAAY,CAAC;AAAA,YACb,iBAAiB,CAAC;AAAA,UACpB;AAEA,gBAAM,kBAAkB,KAAK,QAAQ;AACrC,cAAI;AAGJ,mBAAS,IAAI,GAAG,IAAI,iBAAiB,KAAK;AACxC,0BAAc,KAAK,MAAM;AACzB,kBAAM,IAAI,KAAK,SAAS,CAAC,KAAK;AAC9B,kBAAM,IAAI,KAAK,SAAS,CAAC,IAAI;AAC7B,kBAAM,MAAM,KAAK,SAAS,CAAC;AAC3B,kBAAM,gBAAgB,KAAK,WAAW;AACtC,kBAAM,WAAW,WAAW,IAAI;AAAA,cAC9B;AAAA,cACA;AAAA,cACA,iBAAiB;AAAA,YACnB;AACA,sBAAU;AAAA,UACZ;AACA,4BAAkB,KAAK;AACvB,eAAK,OAAO,KAAK,KAAK;AACtB;AAAA,QACF;AAAA,QAEA,KAAK,OAAQ;AACX,gBAAM,gBAAgB,WAAW;AACjC,mBAAS,IAAI,GAAG,IAAI,iBAAgB;AAClC,kBAAM,mBAAmB,KAAK,QAAQ;AACtC,kBAAM,cAAc,IAAI,WAAW,EAAE;AACrC,gBAAI,gBAAgB;AACpB,qBAAS,IAAI,GAAG,IAAI,IAAI,KAAK,UAAU;AACrC,0BAAY,CAAC,IAAI,KAAK,MAAM;AAC5B,+BAAiB,YAAY,CAAC;AAAA,YAChC;AACA,kBAAM,gBAAgB,IAAI,WAAW,aAAa;AAClD,qBAAS,IAAI,GAAG,IAAI,eAAe,KAAK,UAAU;AAChD,4BAAc,CAAC,IAAI,KAAK,MAAM;AAAA,YAChC;AACA,iBAAK,KAAK;AAEV,gBAAK,oBAAoB,MAAO,GAAG;AACjC,mBAAK,gBAAgB,mBAAmB,EAAE,IAAI;AAAA,gBAC5C;AAAA,gBAAa;AAAA,cACf;AAAA,YACF,OAAO;AACL,mBAAK,gBAAgB,mBAAmB,EAAE,IAAI;AAAA,gBAC5C;AAAA,gBAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AACA;AAAA,QACF;AAAA,QAEA,KAAK;AACH,qBAAW;AACX,eAAK,gBAAgB,WAAW;AAChC;AAAA,QAEF,KAAK,OAAQ;AACX,qBAAW;AACX,gBAAM,iBAAiB,KAAK,QAAQ;AACpC,gBAAM,aAAa,CAAC;AACpB,gBAAM,QAAQ,KAAK,OAAO,CAAC;AAC3B,mBAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,kBAAM,YAAY,MAAM,WAAW,KAAK,QAAQ,CAAC;AACjD,kBAAM,YAAY,KAAK,QAAQ;AAC/B,sBAAU,iBAAiB,KAAK,gBAAgB,aAAa,CAAC;AAC9D,sBAAU,iBAAiB,KAAK,gBAAgB,YAAY,EAAE;AAC9D,uBAAW,KAAK,SAAS;AAAA,UAC3B;AACA,gBAAM,gBAAgB,KAAK,QAAQ;AACnC,gBAAM,cAAc,KAAK,QAAQ;AACjC,gBAAM,0BAA0B,KAAK,QAAQ;AAC7C,gBAAM,YAAY;AAAA,YAAW;AAAA,YAAM;AAAA,YACjC;AAAA,YAAO;AAAA,YAAY,KAAK;AAAA,YACxB;AAAA,YAAe;AAAA,YACf,2BAA2B;AAAA,YAAG,0BAA0B;AAAA,UAAE;AAC5D,oBAAU;AACV;AAAA,QACF;AAAA,QAEA,KAAK;AACH,cAAI,KAAK,MAAM,MAAM,KAAM;AACzB;AAAA,UACF;AACA;AAAA,QAEF;AACE,cAAI,KAAK,SAAS,CAAC,MAAM,OACpB,KAAK,SAAS,CAAC,KAAK,OAAQ,KAAK,SAAS,CAAC,KAAK,KAAM;AAGzD,sBAAU;AACV;AAAA,UACF;AACA,gBAAM,IAAI,MAAM,uBAAuB,WAAW,SAAS,EAAE,CAAC,EAAE;AAAA,MACpE;AACA,mBAAa,WAAW;AAAA,IAC1B;AAAA,EACF;AAAA,EAEA,YAAY;AACV,UAAM,EAAE,OAAO,IAAI;AACnB,QAAI,KAAK,OAAO,WAAW,GAAG;AAC5B,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC1C,WAAW,KAAK,OAAO,SAAS,GAAG;AACjC,cAAQ,KAAK,sCAAsC;AAAA,IACrD;AAGA,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,YAAM,KAAK,KAAK,OAAO,CAAC,EAAE;AAC1B,iBAAW,KAAK,OAAO,KAAK,EAAE,GAAG;AAC/B,WAAG,CAAC,EAAE,oBAAoB,KAAK,mBAAmB,GAAG,CAAC,EAAE,eAAe;AACvE,eAAO,GAAG,CAAC,EAAE;AAAA,MACf;AAAA,IACF;AAEA,UAAM,QAAQ,OAAO,CAAC;AACtB,UAAM,EAAE,YAAY,gBAAgB,IAAI;AACxC,UAAM,gBAAgB,CAAC;AACvB,UAAM,QAAQ,MAAM;AACpB,UAAM,SAAS,MAAM;AAErB,aAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,YAAM,YAAY,WAAW,gBAAgB,CAAC,CAAC;AAC/C,oBAAc,KAAK;AAAA,QACjB,OAAO,mBAAmB,OAAO,SAAS;AAAA,QAC1C,QAAQ,UAAU,IAAI,MAAM;AAAA,QAC5B,QAAQ,UAAU,IAAI,MAAM;AAAA,MAC9B,CAAC;AAAA,IACH;AAEA,UAAM,MAAM,IAAI,WAAW,QAAQ,SAAS,cAAc,MAAM;AAChE,QAAI,KAAK;AACT,aAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,eAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAC9B,iBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,EAAE,GAAG;AAC7C,gBAAM,YAAY,cAAc,CAAC;AACjC,cAAI,EAAE,IAAI,UAAU,MAAM,IAAI,IAAI,UAAU,MAAM,EAAE,IAAI,IAAI,UAAU,MAAM;AAC5E,YAAE;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAqB,cAArB,cAAyC,YAAY;AAAA,EACnD,YAAY,eAAe;AACzB,UAAM;AACN,SAAK,SAAS,IAAI,iBAAiB;AACnC,QAAI,cAAc,YAAY;AAC5B,WAAK,OAAO,MAAM,cAAc,UAAU;AAAA,IAC5C;AAAA,EACF;AAAA,EAEA,YAAY,QAAQ;AAClB,SAAK,OAAO,YAAY;AACxB,SAAK,OAAO,MAAM,IAAI,WAAW,MAAM,CAAC;AACxC,WAAO,KAAK,OAAO,UAAU,EAAE;AAAA,EACjC;AACF;", "names": ["n", "component", "k", "mcu"]}