/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */
export function init(this: import("../defs.js").ProjectionDefinition & LocalThis): void;
export class init {
    temp: number;
    es: number;
    e: number;
    e0: number;
    e1: number;
    e2: number;
    e3: number;
    ml0: number;
}
export function forward(p: any): any;
export function inverse(p: any): any;
export const names: string[];
declare namespace _default {
    export { init };
    export { forward };
    export { inverse };
    export { names };
}
export default _default;
export type LocalThis = {
    temp: number;
    es: number;
    e: number;
    e0: number;
    e1: number;
    e2: number;
    e3: number;
    ml0: number;
};
