<template>
	<div class="home-container">
		<!-- 最新通知 -->
		<div class="notice-section">
			<div class="notice-header">
				<div class="tabs">
					<span class="tab-item active"> 通知 </span>
				</div>
				<el-link type="primary" class="more" @click="$router.push('/admin/notice/index')">更多</el-link>
			</div>
			<div class="notice-list">
				<template v-if="noticeList.length > 0">
					<div v-for="notice in noticeList" :key="notice.id" class="notice-item">
						<div class="notice-left">
							<div class="notice-tag" :class="notice.noticeTypeClass">{{ notice.noticeType }}</div>
							<div class="notice-tag" :class="notice.levelType">{{ notice.tag }}</div>
							<div class="notice-content" @click="showNoticeDetail(notice)">{{ notice.title || notice.content }}</div>
						</div>
						<div class="notice-right">
							<div class="notice-time">{{ formatRelativeTime(notice.createTime) }}</div>
							<div class="notice-date">{{ notice.createTime }}</div>
						</div>
					</div>
				</template>
				<div v-else class="empty-notice">
					<el-empty description="暂无通知" />
				</div>
			</div>
		</div>

		<!-- 数据卡片区域 -->
		<div class="data-cards">
			<div class="data-card">
				<div class="icon-box blue">
					<el-icon><DataLine /></el-icon>
				</div>
				<div class="data-info">
					<div class="value">2022</div>
					<div class="label">总浏览量</div>
				</div>
			</div>
			<div class="data-card">
				<div class="icon-box cyan">
					<el-icon><User /></el-icon>
				</div>
				<div class="data-info">
					<div class="value">19526</div>
					<div class="label">用户数</div>
				</div>
			</div>
			<div class="data-card">
				<div class="icon-box red">
					<el-icon><Document /></el-icon>
				</div>
				<div class="data-info">
					<div class="value">325</div>
					<div class="label">事件数量</div>
				</div>
			</div>
			<div class="data-card">
				<div class="icon-box green">
					<el-icon><Timer /></el-icon>
				</div>
				<div class="data-info">
					<div class="value">520</div>
					<div class="label">系统运行</div>
				</div>
			</div>
			<div class="data-card">
				<div class="icon-box orange">
					<el-icon><Reading /></el-icon>
				</div>
				<div class="data-info">
					<div class="value">89</div>
					<div class="label">在线培训</div>
				</div>
			</div>
			<div class="data-card">
				<div class="icon-box purple">
					<el-icon><Files /></el-icon>
				</div>
				<div class="data-info">
					<div class="value">170</div>
					<div class="label">附件</div>
				</div>
			</div>
		</div>

		<!-- 图表区域 -->
		<div class="charts-section">
			<div class="chart-card">
				<div class="card-header">
					<span class="title">事件类型</span>
				</div>
				<div
					class="chart-content"
					ref="trendChartRef"
					v-loading="eventTypeLoading"
					element-loading-text="加载中..."
					element-loading-background="rgba(255, 255, 255, 0.5)"
				></div>
			</div>
			<div class="chart-card">
				<div class="card-header">
					<span class="title">事件数量</span>
				</div>
				<div
					class="chart-content"
					ref="barChartRef"
					v-loading="top10Loading"
					element-loading-text="加载中..."
					element-loading-background="rgba(255, 255, 255, 0.5)"
				></div>
			</div>
		</div>

		<!-- 底部区域 -->
		<div class="bottom-section">
			<div class="visit-trend">
				<div class="card-header">
					<span class="title">每天事件数量</span>
				</div>
				<div class="chart-content" ref="visitTrendChartRef"></div>
			</div>
		</div>

		<!-- 通知详情弹窗 -->
		<el-dialog v-model="noticeDetailVisible" title="通知详情" width="600px" :close-on-click-modal="true" :show-close="true">
			<template v-if="currentNotice">
				<div class="notice-detail-header">
					<div class="notice-detail-title">{{ currentNotice.title || '通知' }}</div>
					<div class="notice-meta">
						<span class="notice-type">{{ currentNotice.noticeType }}</span>
						<span class="notice-time">{{ currentNotice.createTime }}</span>
					</div>
				</div>
				<div class="notice-detail-content">
					{{ currentNotice.content }}
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, reactive } from 'vue';
import * as echarts from 'echarts';
import { DataLine, User, Document, Timer, Reading, Files } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import { getNoticeList } from '/@/api/admin/notice';
import { getEventDataHx, getEventTop10 } from '/@/api/admin/bigScreen';
import TransparentSelect from '/@/components/TransparentSelect/index.vue';

const currentDate = ref(new Date());
const router = useRouter();

// 格式化日期
const formatDate = (date: string) => {
	const d = new Date(date);
	return `${d.getFullYear()} 年 ${d.getMonth() + 1} 月`;
};

// 格式化相对时间
const formatRelativeTime = (dateString: string): string => {
	console.log('格式化时间输入:', dateString);
	if (!dateString) return '';

	try {
		const now = new Date();
		const date = new Date(dateString);
		console.log('当前时间:', now);
		console.log('通知时间:', date);

		// 检查日期是否有效
		if (isNaN(date.getTime())) {
			console.error('无效的日期格式:', dateString);
			return dateString;
		}

		const diffSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
		console.log('时间差(秒):', diffSeconds);

		// 一天内显示相对时间
		if (diffSeconds < 60) {
			return `${diffSeconds}秒前`;
		} else if (diffSeconds < 3600) {
			return `${Math.floor(diffSeconds / 60)}分钟前`;
		} else if (diffSeconds < 86400) {
			return `${Math.floor(diffSeconds / 3600)}小时前`;
		} else {
			return `${Math.floor(diffSeconds / (3600 * 24))}天前`;
		}
	} catch (error) {
		console.error('格式化时间错误:', error);
		return dateString;
	}
};

// 图表引用
const trendChartRef = ref();
const barChartRef = ref();
const visitTrendChartRef = ref();

// 通知相关
const noticeList = ref<any[]>([]);
const loading = ref(false);

// 详情弹窗相关
const noticeDetailVisible = ref(false);
const currentNotice = ref<any>(null);

// 事件类型图表相关
const eventTypeSelect = ref('本年');
const eventTypeData = ref([]);
const eventTypeLoading = ref(true);

// 事件数Top7相关
const eventTop10Select = ref('本年');
const top10Loading = ref(true);
interface EventItem {
	name: string;
	data: number[];
}
interface EventTop10Data {
	resList: EventItem[];
	cityNames: string[];
}
const eventTop10Data = ref<EventTop10Data>({ resList: [], cityNames: [] });

// 获取通知数据
const fetchNoticeData = () => {
	loading.value = true;

	getNoticeList({})
		.then((res) => {
			if (res && res.code === 0 && Array.isArray(res.data)) {
				noticeList.value = res.data
					.map((item) => {
						// 设置通知类型和样式
						let levelType = '';
						let noticeType = '';
						let tag = '';
						let noticeTypeClass = '';

						// 设置系统/上级类型
						if (item.type === '1' || item.type === 1) {
							noticeTypeClass = 'important';
							noticeType = '上级通知';
						} else {
							noticeTypeClass = 'info';
							noticeType = '系统通知';
						}

						// 设置重要/通知标签
						if (item.level === '1' || item.level === 1) {
							levelType = 'info';
							tag = '通知';
						} else if (item.level === '2' || item.level === 2) {
							levelType = 'important';
							tag = '重要';
						}

						// 确保时间格式正确
						const createTime = item.createTime || new Date().toISOString();
						console.log('原始时间:', createTime);

						return {
							id: item.id || Math.random().toString(36).substring(2),
							levelType,
							noticeType,
							noticeTypeClass,
							tag,
							title: item.title || '',
							content: item.context || '',
							time: item.time || '',
							createTime,
						};
					})
					.slice(0, 5); // 只取前5条

				console.log('处理后的通知数据:', noticeList.value); // 调试日志
			} else {
				noticeList.value = [];
			}
			loading.value = false;
		})
		.catch((error) => {
			console.error('获取通知数据失败:', error);
			noticeList.value = [];
			loading.value = false;
		});
};

// 获取事件类型数据
const getEventTypeData = async () => {
	eventTypeLoading.value = true;
	let dateStr = 'year';
	if (eventTypeSelect.value === '本日') {
		dateStr = 'today';
	} else if (eventTypeSelect.value === '本月') {
		dateStr = 'month';
	}
	try {
		const res = await getEventDataHx({ date: dateStr });
		eventTypeData.value = res.data;
		updateTrendChart();
	} catch (error) {
		console.error('获取事件类型数据失败:', error);
		eventTypeData.value = [];
	} finally {
		eventTypeLoading.value = false;
	}
};

// 获取事件Top10数据
const getEventTop10Data = async () => {
	top10Loading.value = true;
	let dateStr = 'year';
	if (eventTop10Select.value === '本日') {
		dateStr = 'today';
	} else if (eventTop10Select.value === '本月') {
		dateStr = 'month';
	}

	try {
		const res = await getEventTop10({ date: dateStr });
		eventTop10Data.value = res.data;
		updateBarChart();
	} catch (error) {
		console.error('获取事件Top10数据失败:', error);
		eventTop10Data.value = { resList: [], cityNames: [] };
	} finally {
		top10Loading.value = false;
	}
};

onMounted(() => {
	fetchNoticeData();
	getEventTypeData(); // 获取事件类型数据
	getEventTop10Data(); // 获取事件数Top7数据
	initVisitTrendChart();
});

// 更新趋势图
const updateTrendChart = () => {
	const chart = echarts.init(trendChartRef.value);
	chart.setOption({
		tooltip: {
			trigger: 'item',
			formatter: '{a} <br/>{b}: {c} ({d}%)',
		},
		legend: {
			orient: 'horizontal',
			bottom: 'bottom',
			textStyle: {
				color: '#303133',
			},
			formatter: function (name: string) {
				return name; // 确保完整显示名称
			},
			tooltip: {
				show: true,
				formatter: function (params: { name: string }) {
					return params.name;
				},
			},
		},
		series: [
			{
				name: '事件类型',
				type: 'pie',
				radius: ['30%', '60%'],
				center: ['50%', '40%'],
				avoidLabelOverlap: true,
				itemStyle: {
					borderRadius: 10,
					color: function (params: { dataIndex: number }) {
						// 使用绿色系列颜色
						const greenColors = [
							'rgba(16, 116, 41, 1)', // 主绿色
							'rgba(16, 116, 41, 0.9)',
							'rgba(16, 116, 41, 0.8)',
							'rgba(16, 116, 41, 0.7)',
							'rgba(16, 116, 41, 0.6)',
							'rgba(67, 160, 71, 1)', // 深绿
							'rgba(102, 187, 106, 1)', // 中绿
							'rgba(129, 199, 132, 1)', // 浅绿
							'rgba(165, 214, 167, 1)', // 更浅的绿
							'rgba(200, 230, 201, 1)', // 最浅的绿
						];
						return greenColors[params.dataIndex % greenColors.length];
					},
				},
				label: {
					show: true,
					position: 'outside',
					formatter: '{b}: {c} ({d}%)',
					fontSize: 12,
					lineHeight: 15,
					rich: {
						b: {
							fontSize: 12,
							lineHeight: 15,
							fontWeight: 'bold',
						},
					},
					padding: [0, 0, 0, 0],
					align: 'center',
				},
				labelLine: {
					show: true,
					length: 15,
					length2: 10,
				},
				emphasis: {
					itemStyle: {
						shadowBlur: 10,
						shadowOffsetX: 0,
						shadowColor: 'rgba(0, 0, 0, 0.5)',
					},
					label: {
						show: true,
					},
				},
				data: eventTypeData.value,
			},
		],
	});
};

// 更新柱状图
const updateBarChart = () => {
	const chart = echarts.init(barChartRef.value);

	// 统一的颜色定义
	const colors = [
		'#ff5757', // 红色
		'#37A2FF', // 蓝色
		'#67C23A', // 绿色
		'#E6A23C', // 橙色
		'#8B5CF6', // 紫色
		'#19D3DB', // 青色
		'#F56C6C', // 粉红色
		'#909399', // 灰色
		'#00CC99', // 薄荷绿
		'#FF9900', // 黄色
	];

	// 处理数据，最多取7条
	interface ChartDataItem {
		name: string;
		type: string;
		data: number[];
		itemStyle: {
			color: string;
		};
	}

	let displayData: ChartDataItem[] = [];
	let categories: string[] = [];

	if (eventTop10Data.value && eventTop10Data.value.cityNames && eventTop10Data.value.cityNames.length > 0) {
		// 最多显示7条数据，并反转顺序
		const originalCategories = eventTop10Data.value.cityNames.slice(-7).reverse();

		// 处理标签，去掉数字前缀
		categories = originalCategories.map((cat) => {
			// 匹配形如"数字+空格"的模式并去除
			const match = cat.match(/^\d+\s+(.+)/);
			return match ? match[1] : cat;
		});

		// 准备系列数据
		if (eventTop10Data.value.resList && eventTop10Data.value.resList.length > 0) {
			displayData = eventTop10Data.value.resList.map((item, index) => {
				return {
					name: item.name,
					type: 'bar',
					// 反转数据顺序以匹配反转后的类别
					data: item.data.slice(-7).reverse(),
					stack: 'total', // 添加stack属性使柱状图叠加
					itemStyle: {
						color: colors[index % colors.length],
					},
					emphasis: {
						focus: 'series', // 高亮显示单个系列
					},
					barWidth: '40%', // 调整柱子宽度，使叠加的柱状图更美观
				};
			});
		}
	}

	chart.setOption({
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow',
			},
		},
		legend: {
			data: displayData.map((item) => item.name),
			textStyle: {
				color: '#303133',
			},
			orient: 'horizontal', // 横向布局
			bottom: 0, // 位于底部
			itemGap: 10, // 项目之间的间距
			itemWidth: 15, // 图例标记的宽度
		},
		grid: {
			left: '1%',
			right: '1%',
			top: '5%',
			bottom: '15%', // 保持足够的底部空间显示横向图例
			containLabel: true,
		},
		xAxis: {
			type: 'category',
			data: categories,
			axisLabel: {
				rotate: 0, // 铺平标签
				interval: 0, // 强制显示所有标签
				formatter: function (value: string) {
					return value; // 确保完整显示
				},
				textStyle: {
					fontSize: 12,
				},
				rich: {
					value: {
						lineHeight: 20,
					},
				},
			},
		},
		yAxis: {
			type: 'value',
		},
		series:
			displayData.length > 0
				? displayData
				: [
						{
							name: '暂无数据',
							type: 'bar',
							data: [],
							itemStyle: {
								color: 'rgba(16, 116, 41, 0.5)',
							},
						},
					],
	});

	// 确保图表填满容器
	setTimeout(() => {
		chart.resize();
	}, 100);
};

// 初始化访问量趋势图
const initVisitTrendChart = () => {
	const chart = echarts.init(visitTrendChartRef.value);
	chart.setOption({
		tooltip: {
			trigger: 'axis',
		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: '3%',
			containLabel: true,
		},
		xAxis: {
			type: 'category',
			boundaryGap: false,
			data: [
				'2025-06-03',
				'2025-06-02',
				'2025-06-01',
				'2025-05-31',
				'2025-05-30',
				'2025-05-29',
				'2025-05-28',
				'2025-05-27',
				'2025-05-26',
				'2025-05-25',
				'2025-05-24',
				'2025-05-23',
				'2025-05-22',
				'2025-05-21',
				'2025-05-20',
			],
			axisLabel: {
				rotate: 45,
			},
		},
		yAxis: {
			type: 'value',
			splitLine: {
				lineStyle: {
					type: 'dashed',
				},
			},
		},
		series: [
			{
				name: '访问量',
				type: 'line',
				smooth: true,
				data: [10, 15, 12, 14, 16, 18, 20, 450, 380, 220, 70, 80, 85, 50, 45],
				areaStyle: {
					color: {
						type: 'linear',
						x: 0,
						y: 0,
						x2: 0,
						y2: 1,
						colorStops: [
							{
								offset: 0,
								color: 'rgba(16, 116, 41, 0.2)',
							},
							{
								offset: 1,
								color: 'rgba(16, 116, 41, 0)',
							},
						],
					},
				},
				itemStyle: {
					color: 'rgba(16, 116, 41, 1)',
				},
				lineStyle: {
					width: 2,
					color: 'rgba(16, 116, 41, 1)',
				},
				symbol: 'circle',
				symbolSize: 6,
			},
		],
	});
};

// 显示通知详情
const showNoticeDetail = (notice: any) => {
	currentNotice.value = notice;
	noticeDetailVisible.value = true;
};
</script>

<style scoped lang="scss">
:deep(.el-backtop) {
	display: none !important;
}

.home-container {
	padding: 20px;
	background: #f5f7fa;

	.notice-section {
		background: #fff;
		border-radius: 8px;
		padding: 24px;
		margin-bottom: 20px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
		height: 260px; /* 调整高度以适应4条通知 */

		.notice-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20px;
			padding-bottom: 16px;
			border-bottom: 1px solid #f0f0f0;

			.tabs {
				display: flex;
				gap: 24px;

				.tab-item {
					font-size: 16px;
					font-weight: bold;
					color: #909399;
					cursor: pointer;
					padding: 0 4px 8px 4px;
					position: relative;
					transition: all 0.3s;

					&.active {
						color: rgba(16, 116, 41, 1);

						&:after {
							content: '';
							position: absolute;
							bottom: -1px;
							left: 0;
							width: 100%;
							height: 3px;
							background-color: rgba(16, 116, 41, 1);
							border-radius: 1.5px;
						}
					}

					&:hover:not(.active) {
						color: rgba(16, 116, 41, 0.7);
					}
				}
			}

			.more {
				font-weight: 500;
			}
		}

		.notice-list {
			height: calc(100% - 60px);
			overflow-y: auto;
			scrollbar-width: none; /* Firefox */
			&::-webkit-scrollbar {
				display: none; /* Chrome, Safari, Edge */
			}

			.notice-item {
				height: 50px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 12px;
				border-bottom: 1px dashed #ebeef5;

				&:hover {
					background-color: #f9f9f9;
				}

				&:last-child {
					border-bottom: none;
				}

				.notice-left {
					display: flex;
					align-items: center;
					flex: 1;
					overflow: hidden;
				}

				.notice-right {
					display: flex;
					flex-direction: column;
					align-items: flex-end;
					margin-left: 15px;
				}

				.notice-tag {
					padding: 2px 8px;
					border-radius: 4px;
					font-size: 12px;
					margin-right: 12px;
					color: #fff;
					min-width: 40px;
					text-align: center;

					&.important {
						background-color: #f56c6c; /* 红色 - level=2 重要 */
					}
					&.info {
						background-color: rgba(16, 116, 41, 1); /* 绿色 - level=1 通知 */
					}
					&.warning {
						background-color: #e6a23c;
					}
				}

				.notice-content {
					flex: 1;
					color: #303133;
					font-size: 14px;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					cursor: pointer;

					&:hover {
						color: rgba(16, 116, 41, 1);
						text-decoration: underline;
					}
				}

				.notice-time {
					color: #909399;
					font-size: 12px;
				}

				.notice-date {
					color: #909399;
					font-size: 12px;
					margin-top: 2px;
				}
			}

			.empty-notice {
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				padding-top: 20px;

				:deep(.el-empty) {
					padding: 0;

					.el-empty__image {
						width: 80px;
						height: 80px;
					}

					.el-empty__description {
						margin-top: 10px;
					}
				}
			}
		}
	}

	.data-cards {
		display: grid;
		grid-template-columns: repeat(6, 1fr);
		gap: 20px;
		margin-bottom: 20px;

		.data-card {
			background: #fff;
			border-radius: 8px;
			padding: 20px;
			display: flex;
			align-items: center;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

			.icon-box {
				width: 48px;
				height: 48px;
				border-radius: 8px;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 16px;

				i {
					font-size: 24px;
					color: #fff;
				}

				&.blue {
					background: rgba(16, 116, 41, 1);
				}
				&.cyan {
					background: rgba(16, 116, 41, 0.8);
				}
				&.red {
					background: #ff5757;
				}
				&.green {
					background: rgba(16, 116, 41, 0.6);
				}
				&.orange {
					background: #e6a23c;
				}
				&.purple {
					background: #8b5cf6;
				}
			}

			.data-info {
				.value {
					font-size: 24px;
					font-weight: bold;
					color: #303133;
					line-height: 1;
					margin-bottom: 8px;
				}

				.label {
					font-size: 14px;
					color: #909399;
				}
			}
		}
	}

	.charts-section {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20px;
		margin-bottom: 20px;

		.chart-card {
			background: #fff;
			border-radius: 8px;
			padding: 20px;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

			.card-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20px;

				.title {
					font-size: 16px;
					font-weight: bold;
					color: #303133;
				}
			}

			.chart-content {
				height: 260px; /* 调整高度以适应4条通知 */
			}
		}
	}

	.bottom-section {
		margin-top: 20px;

		.visit-trend {
			background: #fff;
			border-radius: 8px;
			padding: 20px;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
			margin-bottom: 20px;

			.card-header {
				margin-bottom: 20px;

				.title {
					font-size: 16px;
					font-weight: bold;
					color: #303133;
				}
			}

			.chart-content {
				height: 400px;
			}
		}
	}

	.notice-detail-header {
		margin-bottom: 20px;
		padding-bottom: 15px;
		border-bottom: 1px solid #ebeef5;

		.notice-detail-title {
			font-size: 18px;
			font-weight: bold;
			color: #303133;
			margin-bottom: 10px;
		}

		.notice-meta {
			display: flex;
			justify-content: space-between;
			color: #909399;
			font-size: 14px;
		}
	}

	.notice-detail-content {
		line-height: 1.8;
		font-size: 14px;
		color: #606266;
		white-space: pre-wrap;
		word-break: break-all;
	}
}
</style>
