<!--
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-07-14 10:00:00
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-07-30 10:21:41
 * @FilePath: \uavflight-ui\src\components\GisTools\index.vue
 * @Description: GIS工具面板组件，包含绘制和测量功能
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <!-- 右上角GIS工具面板 -->
  <div class="gis-tools-panel" :class="{ 'collapsed': isGISToolsPanelCollapsed }">
    <!-- 收缩按钮 (位于长条最左侧) -->
    <div class="gis-tools-collapse-btn panel-button-horizontal" @click="toggleGISToolsPanel">
      <div class="icon-container">
        <el-icon><ArrowRight v-if="!isGISToolsPanelCollapsed" /><ArrowLeft v-else /></el-icon>
      </div>
    </div>
    
    <!-- 工具按钮区域 -->
    <div class="gis-tools-content">
      <!-- 绘制点 -->
      <div class="gis-tool-item">
        <el-tooltip content="绘制点" placement="bottom">
          <el-button type="primary" size="small" @click="handleDrawPoint" :class="{ 'active': activeDrawTool === 'Point' }">
            <el-icon><Location /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
      
      <!-- 绘制线 -->
      <div class="gis-tool-item">
        <el-tooltip content="绘制线" placement="bottom">
          <el-button type="primary" size="small" @click="handleDrawLine" :class="{ 'active': activeDrawTool === 'LineString' }">
            <el-icon><Connection /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
      
      <!-- 绘制面 -->
      <div class="gis-tool-item">
        <el-tooltip content="绘制面" placement="bottom">
          <el-button type="primary" size="small" @click="handleDrawPolygon" :class="{ 'active': activeDrawTool === 'Polygon' }">
            <el-icon><CopyDocument /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
      
      <!-- 绘制矩形 -->
      <div class="gis-tool-item">
        <el-tooltip content="绘制矩形" placement="bottom">
          <el-button type="primary" size="small" @click="handleDrawRectangle" :class="{ 'active': activeDrawTool === 'Rectangle' }">
            <el-icon><PictureFilled /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
      
      <!-- 绘制圆 -->
      <div class="gis-tool-item">
        <el-tooltip content="绘制圆" placement="bottom">
          <el-button type="primary" size="small" @click="handleDrawCircle" :class="{ 'active': activeDrawTool === 'Circle' }">
            <el-icon><Odometer /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
      
      <!-- 分隔线 -->
      <div class="gis-tool-divider"></div>
      
      <!-- 测量距离 -->
      <div class="gis-tool-item">
        <el-tooltip content="测量距离" placement="bottom">
          <el-button type="success" size="small" @click="handleMeasureDistance" :class="{ 'active': activeDrawTool === 'MeasureDistance' }">
            <el-icon><ScaleToOriginal /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
      
      <!-- 测量面积 -->
      <div class="gis-tool-item">
        <el-tooltip content="测量面积" placement="bottom">
          <el-button type="success" size="small" @click="handleMeasureArea" :class="{ 'active': activeDrawTool === 'MeasureArea' }">
            <el-icon><Grid /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
      
      <!-- 分隔线 -->
      <div class="gis-tool-divider"></div>

      <!-- 属性查看 -->
      <div class="gis-tool-item">
        <el-tooltip content="属性查看" placement="bottom">
          <el-button type="warning" size="small" @click="handleAttributeInfo" :class="{ 'active': activeDrawTool === 'AttributeInfo' }">
            <el-icon><InfoFilled /></el-icon>
          </el-button>
        </el-tooltip>
      </div>

      <!-- 分隔线 -->
      <div class="gis-tool-divider"></div>
      
      <!-- 清除所有绘制 -->
      <div class="gis-tool-item">
        <el-tooltip content="清除所有绘制" placement="bottom">
          <el-button type="danger" size="small" @click="handleClearDrawings">
            <el-icon><DeleteFilled /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>
  </div>

  
</template>

<script setup lang="ts">
// Vue相关导入
import { onMounted, onBeforeUnmount, ref, defineProps, defineEmits } from 'vue';

// 第三方库
import { 
  ArrowRight, 
  ArrowLeft, 
  Location, 
  Connection, 
  CopyDocument, 
  PictureFilled, 
  Odometer, 
  DeleteFilled, 
  ScaleToOriginal, 
  Grid,
  InfoFilled
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import Draw, { Options as DrawOptions, createBox as olCreateBox } from 'ol/interaction/Draw';
import { Vector as VectorSource } from 'ol/source';
import { Vector as VectorLayer } from 'ol/layer';
import { Style, Fill, Stroke, Circle as CircleStyle } from 'ol/style';
import { LineString, Polygon } from 'ol/geom';
import { getArea, getLength } from 'ol/sphere';
import { unByKey } from 'ol/Observable';
import Overlay from 'ol/Overlay';
import axios from 'axios';

// Props和Emits定义
const props = defineProps({
  mapStore: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['drawStart', 'drawEnd', 'attributeInfo']);

// 面板状态
const isGISToolsPanelCollapsed = ref(false);
const showGISExpandButton = ref(false);
const activeDrawTool = ref('');

// 绘制相关
const drawSource = ref(new VectorSource());
const drawLayer = ref(new VectorLayer({
  source: drawSource.value,
  style: new Style({
    fill: new Fill({
      color: 'rgba(64, 158, 255, 0.4)'
    }),
    stroke: new Stroke({
      color: '#409eff',
      width: 2
    }),
    image: new CircleStyle({
      radius: 7,
      fill: new Fill({
        color: '#409eff'
      })
    })
  })
}));
const draw = ref(null);
const measureTooltipElement = ref(null);
const measureTooltip = ref(null);
const sketch = ref(null);
const listener = ref(null);

/**
 * 切换GIS工具面板收缩状态
 */
function toggleGISToolsPanel() {
  isGISToolsPanelCollapsed.value = !isGISToolsPanelCollapsed.value;
  
  if (isGISToolsPanelCollapsed.value) {
    // 延迟显示展开按钮，等待过渡动画完成
    setTimeout(() => {
      showGISExpandButton.value = true;
    }, 300);
  } else {
    // 如果面板被展开，立即隐藏展开按钮
    showGISExpandButton.value = false;
  }
  
  console.log(`GIS工具面板${isGISToolsPanelCollapsed.value ? '已收缩' : '已展开'}`);
}

/**
 * 绘制点
 */
function handleDrawPoint() {
  if (activeDrawTool.value === 'Point') {
    // 如果当前工具已激活，则取消激活
    activeDrawTool.value = '';
    removeDrawInteraction();
    ElMessage.info('已取消绘制点');
  } else {
    // 取消其他激活的工具
    removeDrawInteraction();
    activeDrawTool.value = 'Point';
    addDrawInteraction('Point');
    ElMessage.success('已激活绘制点工具');
  }
}

/**
 * 绘制线
 */
function handleDrawLine() {
  if (activeDrawTool.value === 'LineString') {
    activeDrawTool.value = '';
    removeDrawInteraction();
    ElMessage.info('已取消绘制线');
  } else {
    removeDrawInteraction();
    activeDrawTool.value = 'LineString';
    addDrawInteraction('LineString');
    ElMessage.success('已激活绘制线工具');
  }
}

/**
 * 绘制面
 */
function handleDrawPolygon() {
  if (activeDrawTool.value === 'Polygon') {
    activeDrawTool.value = '';
    removeDrawInteraction();
    ElMessage.info('已取消绘制面');
  } else {
    removeDrawInteraction();
    activeDrawTool.value = 'Polygon';
    addDrawInteraction('Polygon');
    ElMessage.success('已激活绘制面工具');
  }
}

/**
 * 绘制矩形
 */
function handleDrawRectangle() {
  if (activeDrawTool.value === 'Rectangle') {
    activeDrawTool.value = '';
    removeDrawInteraction();
    ElMessage.info('已取消绘制矩形');
  } else {
    removeDrawInteraction();
    activeDrawTool.value = 'Rectangle';
    addDrawInteraction('LineString', 'Rectangle');
    ElMessage.success('已激活绘制矩形工具');
  }
}

/**
 * 绘制圆
 */
function handleDrawCircle() {
  if (activeDrawTool.value === 'Circle') {
    activeDrawTool.value = '';
    removeDrawInteraction();
    ElMessage.info('已取消绘制圆');
  } else {
    removeDrawInteraction();
    activeDrawTool.value = 'Circle';
    addDrawInteraction('Circle');
    ElMessage.success('已激活绘制圆工具');
  }
}

/**
 * 添加绘制交互
 * @param {string} type - 绘制类型
 * @param {string | null} geometryType - 几何函数类型
 */
function addDrawInteraction(type: string, geometryType: string | null = null) {
  const map = props.mapStore.map?.map;
  if (!map) return;
  
  // 确保绘制图层已添加到地图
  if (!map.getLayers().getArray().includes(drawLayer.value)) {
    map.addLayer(drawLayer.value);
  }
  
  // 创建绘制交互
  const drawOptions: DrawOptions = {
    source: drawSource.value as VectorSource,
    type: type as any,
    // 双击结束绘制
    condition: function(event) {
      return true;
    },
    // 设置样式
    style: new Style({
      fill: new Fill({
        color: 'rgba(64, 158, 255, 0.4)'
      }),
      stroke: new Stroke({
        color: '#409eff',
        width: 2
      }),
      image: new CircleStyle({
        radius: 7,
        fill: new Fill({
          color: '#409eff'
        })
      })
    })
  };
  
  // 处理几何类型
  if (geometryType === 'Rectangle') {
    drawOptions.type = 'Circle' as any;
    drawOptions.geometryFunction = olCreateBox();
  }
  
  // 创建绘制交互
  draw.value = new Draw(drawOptions);
  
  // 添加绘制交互到地图
  map.addInteraction(draw.value);
  
  // 监听绘制开始事件
  draw.value.on('drawstart', (evt: any) => {
    sketch.value = evt.feature;
    
    // 添加右键取消上一个点的功能
    const mapElement = map.getTargetElement();
    mapElement.addEventListener('contextmenu', handleRightClick);
    
    // 触发绘制开始事件
    emit('drawStart', { tool: activeDrawTool.value });
    
    if (activeDrawTool.value === 'MeasureDistance' || activeDrawTool.value === 'MeasureArea') {
      createMeasureTooltip();
      
      // 监听几何变化事件
      listener.value = sketch.value.getGeometry().on('change', (e: any) => {
        const geom = e.target;
        let output;
        
        if (geom instanceof Polygon) {
          output = formatArea(geom);
          measureTooltipElement.value.innerHTML = output;
          measureTooltip.value.setPosition(geom.getInteriorPoint().getCoordinates());
        } else if (geom instanceof LineString) {
          output = formatLength(geom);
          measureTooltipElement.value.innerHTML = output;
          measureTooltip.value.setPosition(geom.getLastCoordinate());
        }
      });
    }
  });
  
  // 监听绘制结束事件
  draw.value.on('drawend', (evt: any) => {
    // 移除右键事件监听
    const mapElement = map.getTargetElement();
    mapElement.removeEventListener('contextmenu', handleRightClick);
    
    // 触发绘制结束事件
    emit('drawEnd', { 
      tool: activeDrawTool.value,
      feature: evt.feature
    });
    
    if (activeDrawTool.value === 'MeasureDistance' || activeDrawTool.value === 'MeasureArea') {
      if (measureTooltipElement.value) {
        measureTooltipElement.value.className = 'ol-tooltip ol-tooltip-static';
      }
      if (measureTooltip.value) {
        measureTooltip.value.setOffset([0, -7]);
      }
      sketch.value = null;
      measureTooltipElement.value = null;
      createMeasureTooltip();
      if (listener.value) {
        unByKey(listener.value);
      }
    }
  });
}

/**
 * 移除绘制交互
 */
function removeDrawInteraction() {
  const map = props.mapStore.map?.map;
  if (!map || !draw.value) return;
  
  map.removeInteraction(draw.value);
  draw.value = null;
}

/**
 * 处理右键点击事件
 * @param {Event} event - 鼠标事件
 */
function handleRightClick(event: Event) {
  event.preventDefault();
  
  // 如果正在绘制，移除最后一个点
  if (sketch.value && draw.value) {
    draw.value.removeLastPoint();
  } else if (activeDrawTool.value) {
    // 如果没有正在绘制但已激活绘制工具，取消当前绘制工具
    const toolName = getDrawToolName(activeDrawTool.value);
    activeDrawTool.value = '';
    removeDrawInteraction();
    ElMessage.info(`已取消${toolName}`);
  }
}

/**
 * 处理全局右键点击事件
 * @param {Event} event - 鼠标事件
 */
function handleGlobalRightClick(event: Event) {
  event.preventDefault();
  
  // 如果正在绘制，移除最后一个点
  if (sketch.value && draw.value) {
    draw.value.removeLastPoint();
  } else if (activeDrawTool.value) {
    // 如果没有正在绘制但已激活绘制工具，取消当前绘制工具
    const toolName = getDrawToolName(activeDrawTool.value);
    activeDrawTool.value = '';
    removeDrawInteraction();
    ElMessage.info(`已取消${toolName}`);
  }
}

/**
 * 清除所有绘制
 */
function handleClearDrawings() {
  if (drawSource.value) {
    drawSource.value.clear();
    ElMessage.success('已清除所有绘制');
    
    // 清除测量提示
    const tooltips = document.querySelectorAll('.ol-tooltip');
    tooltips.forEach(tooltip => {
      if (tooltip.parentNode) {
        tooltip.parentNode.removeChild(tooltip);
      }
    });
  }
}

/**
 * 测量距离
 */
function handleMeasureDistance() {
  if (activeDrawTool.value === 'MeasureDistance') {
    activeDrawTool.value = '';
    removeDrawInteraction();
    ElMessage.info('已取消测量距离');
  } else {
    removeDrawInteraction();
    activeDrawTool.value = 'MeasureDistance';
    addDrawInteraction('LineString');
    ElMessage.success('已激活测量距离工具');
  }
}

/**
 * 测量面积
 */
function handleMeasureArea() {
  if (activeDrawTool.value === 'MeasureArea') {
    activeDrawTool.value = '';
    removeDrawInteraction();
    ElMessage.info('已取消测量面积');
  } else {
    removeDrawInteraction();
    activeDrawTool.value = 'MeasureArea';
    addDrawInteraction('Polygon');
    ElMessage.success('已激活测量面积工具');
  }
}

/**
 * 创建测量提示
 */
function createMeasureTooltip() {
  const map = props.mapStore.map?.map;
  if (!map) return;

  if (measureTooltipElement.value && measureTooltipElement.value.parentNode) {
    measureTooltipElement.value.parentNode.removeChild(measureTooltipElement.value);
  }
  
  measureTooltipElement.value = document.createElement('div');
  measureTooltipElement.value.className = 'ol-tooltip ol-tooltip-measure';
  
  measureTooltip.value = new Overlay({
    element: measureTooltipElement.value,
    offset: [0, -15],
    positioning: 'bottom-center',
    stopEvent: false,
    insertFirst: false,
  });
  
  map.addOverlay(measureTooltip.value);
}

/**
 * 格式化长度
 * @param {LineString} line - 线几何
 * @returns {string} - 格式化后的长度
 */
function formatLength(line: LineString) {
  const length = getLength(line);
  let output;
  
  if (length > 1000) {
    output = (Math.round(length / 1000 * 100) / 100) + ' km';
  } else {
    output = (Math.round(length * 100) / 100) + ' m';
  }
  
  return output;
}

/**
 * 格式化面积
 * @param {Polygon} polygon - 面几何
 * @returns {string} - 格式化后的面积
 */
function formatArea(polygon: Polygon) {
  const area = getArea(polygon);
  let output;
  
  if (area > 10000) {
    output = (Math.round(area / 1000000 * 100) / 100) + ' km²';
  } else {
    output = (Math.round(area * 100) / 100) + ' m²';
  }
  
  return output;
}

/**
 * 获取绘制工具的中文名称
 * @param {string} toolType - 绘制工具类型
 * @returns {string} - 工具中文名称
 */
function getDrawToolName(toolType: string): string {
  const toolNames: Record<string, string> = {
    'Point': '绘制点',
    'LineString': '绘制线',
    'Polygon': '绘制面',
    'Rectangle': '绘制矩形',
    'Circle': '绘制圆',
    'MeasureDistance': '测量距离',
    'MeasureArea': '测量面积',
    'AttributeInfo': '属性查看',
    'Edit': '编辑',
    '': '绘制'
  };
  return toolNames[toolType] || '绘制';
}

/**
 * 属性查看功能
 */
function handleAttributeInfo() {
  console.log('激活/停用属性查看工具');
  if (activeDrawTool.value === 'AttributeInfo') {
    activeDrawTool.value = '';
    removeAttributeInfoClick();
    ElMessage.info('已取消属性查看');
  } else {
    // 清除其他工具
    removeDrawInteraction();
    activeDrawTool.value = 'AttributeInfo';
    addAttributeInfoClick();
    ElMessage.success('已激活属性查看工具');
  }
}

/**
 * 添加属性查看点击事件
 */
let clickEventKey = null;

function addAttributeInfoClick() {
  const map = props.mapStore.map?.map;
  if (!map) return;
  
  clickEventKey = map.on('singleclick', handleMapClick);
}

/**
 * 移除属性查看点击事件
 */
function removeAttributeInfoClick() {
  if (clickEventKey) {
    unByKey(clickEventKey);
    clickEventKey = null;
  }
}

/**
 * 处理地图点击事件
 * @param {any} evt - 点击事件对象
 */
function handleMapClick(evt) {
  const map = props.mapStore.map?.map;
  if (!map || activeDrawTool.value !== 'AttributeInfo') return;
  
  // 获取WMS图层
  const wmsLayers = map.getLayers().getArray().filter(layer => {
    // 检查是否是WMS图层
    const source = layer.getSource?.();
    return source && source.getFeatureInfoUrl;
  });

  if (wmsLayers.length === 0) {
    ElMessage.warning('当前地图上没有可查询的WMS图层');
    return;
  }

  // 对每个WMS图层进行查询
  const promises = wmsLayers.map(layer => {
    const source = layer.getSource();
    const view = map.getView();
    const viewResolution = view.getResolution();
    
    // 获取图层ID和名称
    let layerId = source.params_?.LAYERS || '';
    // 尝试获取图层标题（如果有设置）
    const layerTitle = layer.get('title') || layer.get('name') || '';
    // 使用更友好的显示名称
    const displayName = layerTitle || formatLayerName(layerId);
    
    // 构建GetFeatureInfo请求URL
    const url = source.getFeatureInfoUrl(
      evt.coordinate, 
      viewResolution, 
      view.getProjection().getCode(),
      {'INFO_FORMAT': 'application/json'}
    );

    if (url) {
      // 返回带layerId和displayName的Promise
      return axios.get(url)
        .then(response => {
          return {
            layerId: layerId,
            layerName: displayName,
            data: response.data,
            coordinate: evt.coordinate
          };
        })
        .catch(() => null);
    }
    return Promise.resolve(null);
  });

  // 处理所有查询结果
  Promise.all(promises)
    .then(results => {
      const validResults = results.filter(result => 
        result && 
        result.data && 
        result.data.features && 
        result.data.features.length > 0
      );

      if (validResults.length > 0) {
        // 处理第一个有效结果
        const result = validResults[0];
        const feature = result.data.features[0];
        
        // 触发属性信息事件
        emit('attributeInfo', {
          layerId: result.layerId,
          layerName: result.layerName,
          attributes: feature.properties,
          coordinate: result.coordinate
        });
      } else {
        ElMessage.info('点击位置没有找到要素');
      }
    })
    .catch(error => {
      console.error('获取要素信息失败', error);
      ElMessage.error('获取要素信息失败');
    });
}

/**
 * 格式化图层名称，使其更易读
 * @param {string} layerId - 原始图层ID
 * @returns {string} - 格式化后的图层名称
 */
function formatLayerName(layerId) {
  if (!layerId) return '未知图层';
  
  // 移除命名空间前缀（如 workspace:layername）
  const parts = layerId.split(':');
  const name = parts.length > 1 ? parts[1] : parts[0];
  
  // 将下划线和连字符替换为空格，并将每个单词的首字母大写
  return name
    .replace(/[_-]/g, ' ')
    .replace(/\w\S*/g, txt => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
}

// 生命周期钩子
onMounted(() => {
  // 确保初始状态下面板是展开的
  isGISToolsPanelCollapsed.value = false;
  showGISExpandButton.value = false;
  
  // 初始化时添加绘制图层到地图
  const map = props.mapStore.map?.map;
  if (map) {
    map.addLayer(drawLayer.value);
    
    // 添加全局右键点击监听
    const mapElement = map.getTargetElement();
    if (mapElement) {
      mapElement.addEventListener('contextmenu', handleGlobalRightClick);
    }
  }
});

onBeforeUnmount(() => {
  // 移除交互和图层
  const map = props.mapStore.map?.map;
  if (map) {
    // 移除全局右键点击监听
    const mapElement = map.getTargetElement();
    if (mapElement) {
      mapElement.removeEventListener('contextmenu', handleGlobalRightClick);
    }
    
    // 移除绘制图层
    if (map.getLayers().getArray().includes(drawLayer.value)) {
      map.removeLayer(drawLayer.value);
    }
    
    // 移除绘制交互
    if (draw.value) {
      map.removeInteraction(draw.value);
    }
    
    // 移除测量提示
    if (measureTooltip.value) {
      map.removeOverlay(measureTooltip.value);
    }

    // 移除属性查看点击事件
    removeAttributeInfoClick();
  }
});

// 导出需要被父组件调用的方法
defineExpose({
  clearDrawings: handleClearDrawings,
  getActiveDrawTool: () => activeDrawTool.value,
  removeDrawInteraction,
  removeAttributeInfoClick,
  activateAttributeInfo: handleAttributeInfo
});
</script>

<style lang="scss">
@import "../../theme/common/panel-buttons.scss";
/* ===== GIS工具面板样式 ===== */
.gis-tools-panel {
  position: absolute;
  top: 11%;
  right: 0px;
  height: 60px;
  width: auto; /* 自适应宽度 */
  max-width: 600px;
  background-color: rgba(15, 21, 32, 0.9);
  backdrop-filter: blur(10px);
  z-index: 99;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: row;
  color: white;
  overflow: hidden;
  transition: all 0.3s ease;
  border-radius: 4px;
  border: 1px solid rgba(64, 158, 255, 0.5);
  transform-origin: right center;
}

.gis-tools-panel.collapsed {
  transform: translateX(calc(100% - 24px)); /* Only show the collapse button */
  overflow: hidden;
}

.gis-tools-panel.collapsed .gis-tools-collapse-btn {
  border-right: none;
  border-radius: 0 4px 4px 0;
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.3);
}

/* GIS工具面板按钮样式已移至通用按钮样式 */

/* 工具内容区域 */
.gis-tools-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 10px;
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(64, 158, 255, 0.5) rgba(15, 21, 32, 0.3);
}

.gis-tools-content::-webkit-scrollbar {
  height: 4px;
}

.gis-tools-content::-webkit-scrollbar-track {
  background: rgba(15, 21, 32, 0.3);
}

.gis-tools-content::-webkit-scrollbar-thumb {
  background-color: rgba(64, 158, 255, 0.5);
  border-radius: 2px;
}

/* 工具项样式 */
.gis-tool-item {
  margin: 0 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 分隔线样式 */
.gis-tool-divider {
  width: 1px;
  height: 30px;
  background-color: rgba(255, 255, 255, 0.2);
  margin: 0 10px;
}

/* 按钮样式 */
.gis-tool-item .el-button {
  border-radius: 4px;
  padding: 6px;
  font-size: 12px;
  border: none;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  transition: all 0.3s ease;
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gis-tool-item .el-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.gis-tool-item .el-button.active {
  background-color: #409eff;
  color: white;
  box-shadow: 0 0 5px rgba(64, 158, 255, 0.5);
}

.gis-tool-item .el-button.active:hover {
  background-color: #66b1ff;
}

.gis-tool-item .el-button[type="primary"] {
  background-color: rgba(64, 158, 255, 0.2);
}

.gis-tool-item .el-button[type="success"] {
  background-color: rgba(103, 194, 58, 0.2);
}

.gis-tool-item .el-button[type="warning"] {
  background-color: rgba(230, 162, 60, 0.2);
}

.gis-tool-item .el-button[type="danger"] {
  background-color: rgba(245, 108, 108, 0.2);
}

.gis-tool-item .el-button[type="primary"].active {
  background-color: #409eff;
}

.gis-tool-item .el-button[type="success"].active {
  background-color: #67c23a;
}

.gis-tool-item .el-button[type="warning"].active {
  background-color: #e6a23c;
}

.gis-tool-item .el-button[type="danger"].active {
  background-color: #f56c6c;
}

.gis-tool-item .el-button .el-icon {
  font-size: 18px;
}

/* 定义过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from {
  opacity: 0;
}

.fade-leave-to {
  opacity: 0;
}

/* ===== 测量工具样式 ===== */
.ol-tooltip {
  position: relative;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  color: white;
  padding: 4px 8px;
  white-space: nowrap;
  font-size: 12px;
  pointer-events: none;
}

.ol-tooltip-measure {
  opacity: 1;
  font-weight: bold;
}

.ol-tooltip-static {
  background-color: rgba(64, 158, 255, 0.7);
  color: white;
  border: 1px solid white;
}

.ol-tooltip-measure:before,
.ol-tooltip-static:before {
  border-top: 6px solid rgba(0, 0, 0, 0.7);
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
  content: "";
  position: absolute;
  bottom: -6px;
  margin-left: -7px;
  left: 50%;
}

.ol-tooltip-static:before {
  border-top-color: rgba(64, 158, 255, 0.7);
}

/* 按钮基础样式 - 水平方向 */
.panel-button-horizontal {
  background-color: rgba(16, 64, 70, 0.95);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.panel-button-horizontal:hover {
  background-color: rgba(22, 80, 85, 1);
}

.panel-button-horizontal .icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  width: 100%;
  height: 100%;
}

.panel-button-horizontal .el-icon {
  font-size: 16px;
  background-color: rgba(59, 179, 59, 0.9);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
}

/* 收缩按钮样式 */
.gis-tools-collapse-btn {
  width: 24px;
  height: 60px;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 101; /* Ensure it's above other elements */
  background-color: rgba(16, 64, 70, 0.95);
}

/* GIS工具面板展开按钮 */
.gis-tools-expand-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 60px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 100;
}
</style> 