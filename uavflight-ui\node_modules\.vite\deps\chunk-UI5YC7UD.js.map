{"version": 3, "sources": ["../../ol/geom/flat/interpolate.js", "../../ol/geom/LineString.js", "../../ol/geom/MultiLineString.js", "../../ol/geom/MultiPoint.js", "../../ol/geom/flat/center.js", "../../ol/geom/MultiPolygon.js", "../../ol/geom/GeometryCollection.js"], "sourcesContent": ["/**\n * @module ol/geom/flat/interpolate\n */\nimport {binarySearch} from '../../array.js';\nimport {lerp} from '../../math.js';\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} fraction Fraction.\n * @param {Array<number>} [dest] Destination.\n * @param {number} [dimension] Destination dimension (default is `2`)\n * @return {Array<number>} Destination.\n */\nexport function interpolatePoint(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  fraction,\n  dest,\n  dimension\n) {\n  let o, t;\n  const n = (end - offset) / stride;\n  if (n === 1) {\n    o = offset;\n  } else if (n === 2) {\n    o = offset;\n    t = fraction;\n  } else if (n !== 0) {\n    let x1 = flatCoordinates[offset];\n    let y1 = flatCoordinates[offset + 1];\n    let length = 0;\n    const cumulativeLengths = [0];\n    for (let i = offset + stride; i < end; i += stride) {\n      const x2 = flatCoordinates[i];\n      const y2 = flatCoordinates[i + 1];\n      length += Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));\n      cumulativeLengths.push(length);\n      x1 = x2;\n      y1 = y2;\n    }\n    const target = fraction * length;\n    const index = binarySearch(cumulativeLengths, target);\n    if (index < 0) {\n      t =\n        (target - cumulativeLengths[-index - 2]) /\n        (cumulativeLengths[-index - 1] - cumulativeLengths[-index - 2]);\n      o = offset + (-index - 2) * stride;\n    } else {\n      o = offset + index * stride;\n    }\n  }\n  dimension = dimension > 1 ? dimension : 2;\n  dest = dest ? dest : new Array(dimension);\n  for (let i = 0; i < dimension; ++i) {\n    dest[i] =\n      o === undefined\n        ? NaN\n        : t === undefined\n        ? flatCoordinates[o + i]\n        : lerp(flatCoordinates[o + i], flatCoordinates[o + stride + i], t);\n  }\n  return dest;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} m M.\n * @param {boolean} extrapolate Extrapolate.\n * @return {import(\"../../coordinate.js\").Coordinate|null} Coordinate.\n */\nexport function lineStringCoordinateAtM(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  m,\n  extrapolate\n) {\n  if (end == offset) {\n    return null;\n  }\n  let coordinate;\n  if (m < flatCoordinates[offset + stride - 1]) {\n    if (extrapolate) {\n      coordinate = flatCoordinates.slice(offset, offset + stride);\n      coordinate[stride - 1] = m;\n      return coordinate;\n    }\n    return null;\n  }\n  if (flatCoordinates[end - 1] < m) {\n    if (extrapolate) {\n      coordinate = flatCoordinates.slice(end - stride, end);\n      coordinate[stride - 1] = m;\n      return coordinate;\n    }\n    return null;\n  }\n  // FIXME use O(1) search\n  if (m == flatCoordinates[offset + stride - 1]) {\n    return flatCoordinates.slice(offset, offset + stride);\n  }\n  let lo = offset / stride;\n  let hi = end / stride;\n  while (lo < hi) {\n    const mid = (lo + hi) >> 1;\n    if (m < flatCoordinates[(mid + 1) * stride - 1]) {\n      hi = mid;\n    } else {\n      lo = mid + 1;\n    }\n  }\n  const m0 = flatCoordinates[lo * stride - 1];\n  if (m == m0) {\n    return flatCoordinates.slice((lo - 1) * stride, (lo - 1) * stride + stride);\n  }\n  const m1 = flatCoordinates[(lo + 1) * stride - 1];\n  const t = (m - m0) / (m1 - m0);\n  coordinate = [];\n  for (let i = 0; i < stride - 1; ++i) {\n    coordinate.push(\n      lerp(\n        flatCoordinates[(lo - 1) * stride + i],\n        flatCoordinates[lo * stride + i],\n        t\n      )\n    );\n  }\n  coordinate.push(m);\n  return coordinate;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {number} m M.\n * @param {boolean} extrapolate Extrapolate.\n * @param {boolean} interpolate Interpolate.\n * @return {import(\"../../coordinate.js\").Coordinate|null} Coordinate.\n */\nexport function lineStringsCoordinateAtM(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  m,\n  extrapolate,\n  interpolate\n) {\n  if (interpolate) {\n    return lineStringCoordinateAtM(\n      flatCoordinates,\n      offset,\n      ends[ends.length - 1],\n      stride,\n      m,\n      extrapolate\n    );\n  }\n  let coordinate;\n  if (m < flatCoordinates[stride - 1]) {\n    if (extrapolate) {\n      coordinate = flatCoordinates.slice(0, stride);\n      coordinate[stride - 1] = m;\n      return coordinate;\n    }\n    return null;\n  }\n  if (flatCoordinates[flatCoordinates.length - 1] < m) {\n    if (extrapolate) {\n      coordinate = flatCoordinates.slice(flatCoordinates.length - stride);\n      coordinate[stride - 1] = m;\n      return coordinate;\n    }\n    return null;\n  }\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    if (offset == end) {\n      continue;\n    }\n    if (m < flatCoordinates[offset + stride - 1]) {\n      return null;\n    }\n    if (m <= flatCoordinates[end - 1]) {\n      return lineStringCoordinateAtM(\n        flatCoordinates,\n        offset,\n        end,\n        stride,\n        m,\n        false\n      );\n    }\n    offset = end;\n  }\n  return null;\n}\n", "/**\n * @module ol/geom/LineString\n */\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {assignClosestPoint, maxSquaredDelta} from './flat/closest.js';\nimport {closestSquaredDistanceXY} from '../extent.js';\nimport {deflateCoordinates} from './flat/deflate.js';\nimport {do<PERSON><PERSON><PERSON><PERSON>} from './flat/simplify.js';\nimport {extend} from '../array.js';\nimport {forEach as forEachSegment} from './flat/segments.js';\nimport {inflateCoordinates} from './flat/inflate.js';\nimport {interpolatePoint, lineStringCoordinateAtM} from './flat/interpolate.js';\nimport {intersectsLineString} from './flat/intersectsextent.js';\nimport {lineStringLength} from './flat/length.js';\n\n/**\n * @classdesc\n * Linestring geometry.\n *\n * @api\n */\nclass LineString extends SimpleGeometry {\n  /**\n   * @param {Array<import(\"../coordinate.js\").Coordinate>|Array<number>} coordinates Coordinates.\n   *     For internal use, flat coordinates in combination with `layout` are also accepted.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   */\n  constructor(coordinates, layout) {\n    super();\n\n    /**\n     * @private\n     * @type {import(\"../coordinate.js\").Coordinate}\n     */\n    this.flatMidpoint_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.flatMidpointRevision_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDelta_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDeltaRevision_ = -1;\n\n    if (layout !== undefined && !Array.isArray(coordinates[0])) {\n      this.setFlatCoordinates(\n        layout,\n        /** @type {Array<number>} */ (coordinates)\n      );\n    } else {\n      this.setCoordinates(\n        /** @type {Array<import(\"../coordinate.js\").Coordinate>} */ (\n          coordinates\n        ),\n        layout\n      );\n    }\n  }\n\n  /**\n   * Append the passed coordinate to the coordinates of the linestring.\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @api\n   */\n  appendCoordinate(coordinate) {\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = coordinate.slice();\n    } else {\n      extend(this.flatCoordinates, coordinate);\n    }\n    this.changed();\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!LineString} Clone.\n   * @api\n   */\n  clone() {\n    const lineString = new LineString(\n      this.flatCoordinates.slice(),\n      this.layout\n    );\n    lineString.applyProperties(this);\n    return lineString;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    if (minSquaredDistance < closestSquaredDistanceXY(this.getExtent(), x, y)) {\n      return minSquaredDistance;\n    }\n    if (this.maxDeltaRevision_ != this.getRevision()) {\n      this.maxDelta_ = Math.sqrt(\n        maxSquaredDelta(\n          this.flatCoordinates,\n          0,\n          this.flatCoordinates.length,\n          this.stride,\n          0\n        )\n      );\n      this.maxDeltaRevision_ = this.getRevision();\n    }\n    return assignClosestPoint(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      this.maxDelta_,\n      false,\n      x,\n      y,\n      closestPoint,\n      minSquaredDistance\n    );\n  }\n\n  /**\n   * Iterate over each segment, calling the provided callback.\n   * If the callback returns a truthy value the function returns that\n   * value immediately. Otherwise the function returns `false`.\n   *\n   * @param {function(this: S, import(\"../coordinate.js\").Coordinate, import(\"../coordinate.js\").Coordinate): T} callback Function\n   *     called for each segment. The function will receive two arguments, the start and end coordinates of the segment.\n   * @return {T|boolean} Value.\n   * @template T,S\n   * @api\n   */\n  forEachSegment(callback) {\n    return forEachSegment(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      callback\n    );\n  }\n\n  /**\n   * Returns the coordinate at `m` using linear interpolation, or `null` if no\n   * such coordinate exists.\n   *\n   * `extrapolate` controls extrapolation beyond the range of Ms in the\n   * MultiLineString. If `extrapolate` is `true` then Ms less than the first\n   * M will return the first coordinate and Ms greater than the last M will\n   * return the last coordinate.\n   *\n   * @param {number} m M.\n   * @param {boolean} [extrapolate] Extrapolate. Default is `false`.\n   * @return {import(\"../coordinate.js\").Coordinate|null} Coordinate.\n   * @api\n   */\n  getCoordinateAtM(m, extrapolate) {\n    if (this.layout != 'XYM' && this.layout != 'XYZM') {\n      return null;\n    }\n    extrapolate = extrapolate !== undefined ? extrapolate : false;\n    return lineStringCoordinateAtM(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      m,\n      extrapolate\n    );\n  }\n\n  /**\n   * Return the coordinates of the linestring.\n   * @return {Array<import(\"../coordinate.js\").Coordinate>} Coordinates.\n   * @api\n   */\n  getCoordinates() {\n    return inflateCoordinates(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride\n    );\n  }\n\n  /**\n   * Return the coordinate at the provided fraction along the linestring.\n   * The `fraction` is a number between 0 and 1, where 0 is the start of the\n   * linestring and 1 is the end.\n   * @param {number} fraction Fraction.\n   * @param {import(\"../coordinate.js\").Coordinate} [dest] Optional coordinate whose values will\n   *     be modified. If not provided, a new coordinate will be returned.\n   * @return {import(\"../coordinate.js\").Coordinate} Coordinate of the interpolated point.\n   * @api\n   */\n  getCoordinateAt(fraction, dest) {\n    return interpolatePoint(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      fraction,\n      dest,\n      this.stride\n    );\n  }\n\n  /**\n   * Return the length of the linestring on projected plane.\n   * @return {number} Length (on projected plane).\n   * @api\n   */\n  getLength() {\n    return lineStringLength(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride\n    );\n  }\n\n  /**\n   * @return {Array<number>} Flat midpoint.\n   */\n  getFlatMidpoint() {\n    if (this.flatMidpointRevision_ != this.getRevision()) {\n      this.flatMidpoint_ = this.getCoordinateAt(0.5, this.flatMidpoint_);\n      this.flatMidpointRevision_ = this.getRevision();\n    }\n    return this.flatMidpoint_;\n  }\n\n  /**\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {LineString} Simplified LineString.\n   * @protected\n   */\n  getSimplifiedGeometryInternal(squaredTolerance) {\n    const simplifiedFlatCoordinates = [];\n    simplifiedFlatCoordinates.length = douglasPeucker(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      squaredTolerance,\n      simplifiedFlatCoordinates,\n      0\n    );\n    return new LineString(simplifiedFlatCoordinates, 'XY');\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   */\n  getType() {\n    return 'LineString';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   */\n  intersectsExtent(extent) {\n    return intersectsLineString(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      extent\n    );\n  }\n\n  /**\n   * Set the coordinates of the linestring.\n   * @param {!Array<import(\"../coordinate.js\").Coordinate>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   */\n  setCoordinates(coordinates, layout) {\n    this.setLayout(layout, coordinates, 1);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    this.flatCoordinates.length = deflateCoordinates(\n      this.flatCoordinates,\n      0,\n      coordinates,\n      this.stride\n    );\n    this.changed();\n  }\n}\n\nexport default LineString;\n", "/**\n * @module ol/geom/MultiLineString\n */\nimport LineString from './LineString.js';\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {arrayMaxSquaredDelta, assignClosestArrayPoint} from './flat/closest.js';\nimport {closestSquaredDistanceXY} from '../extent.js';\nimport {deflateCoordinatesArray} from './flat/deflate.js';\nimport {douglasPeuckerArray} from './flat/simplify.js';\nimport {extend} from '../array.js';\nimport {inflateCoordinatesArray} from './flat/inflate.js';\nimport {\n  interpolatePoint,\n  lineStringsCoordinateAtM,\n} from './flat/interpolate.js';\nimport {intersectsLineStringArray} from './flat/intersectsextent.js';\n\n/**\n * @classdesc\n * Multi-linestring geometry.\n *\n * @api\n */\nclass MultiLineString extends SimpleGeometry {\n  /**\n   * @param {Array<Array<import(\"../coordinate.js\").Coordinate>|LineString>|Array<number>} coordinates\n   *     Coordinates or LineString geometries. (For internal use, flat coordinates in\n   *     combination with `layout` and `ends` are also accepted.)\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @param {Array<number>} [ends] Flat coordinate ends for internal use.\n   */\n  constructor(coordinates, layout, ends) {\n    super();\n\n    /**\n     * @type {Array<number>}\n     * @private\n     */\n    this.ends_ = [];\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDelta_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDeltaRevision_ = -1;\n\n    if (Array.isArray(coordinates[0])) {\n      this.setCoordinates(\n        /** @type {Array<Array<import(\"../coordinate.js\").Coordinate>>} */ (\n          coordinates\n        ),\n        layout\n      );\n    } else if (layout !== undefined && ends) {\n      this.setFlatCoordinates(\n        layout,\n        /** @type {Array<number>} */ (coordinates)\n      );\n      this.ends_ = ends;\n    } else {\n      let layout = this.getLayout();\n      const lineStrings = /** @type {Array<LineString>} */ (coordinates);\n      const flatCoordinates = [];\n      const ends = [];\n      for (let i = 0, ii = lineStrings.length; i < ii; ++i) {\n        const lineString = lineStrings[i];\n        if (i === 0) {\n          layout = lineString.getLayout();\n        }\n        extend(flatCoordinates, lineString.getFlatCoordinates());\n        ends.push(flatCoordinates.length);\n      }\n      this.setFlatCoordinates(layout, flatCoordinates);\n      this.ends_ = ends;\n    }\n  }\n\n  /**\n   * Append the passed linestring to the multilinestring.\n   * @param {LineString} lineString LineString.\n   * @api\n   */\n  appendLineString(lineString) {\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = lineString.getFlatCoordinates().slice();\n    } else {\n      extend(this.flatCoordinates, lineString.getFlatCoordinates().slice());\n    }\n    this.ends_.push(this.flatCoordinates.length);\n    this.changed();\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!MultiLineString} Clone.\n   * @api\n   */\n  clone() {\n    const multiLineString = new MultiLineString(\n      this.flatCoordinates.slice(),\n      this.layout,\n      this.ends_.slice()\n    );\n    multiLineString.applyProperties(this);\n    return multiLineString;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    if (minSquaredDistance < closestSquaredDistanceXY(this.getExtent(), x, y)) {\n      return minSquaredDistance;\n    }\n    if (this.maxDeltaRevision_ != this.getRevision()) {\n      this.maxDelta_ = Math.sqrt(\n        arrayMaxSquaredDelta(\n          this.flatCoordinates,\n          0,\n          this.ends_,\n          this.stride,\n          0\n        )\n      );\n      this.maxDeltaRevision_ = this.getRevision();\n    }\n    return assignClosestArrayPoint(\n      this.flatCoordinates,\n      0,\n      this.ends_,\n      this.stride,\n      this.maxDelta_,\n      false,\n      x,\n      y,\n      closestPoint,\n      minSquaredDistance\n    );\n  }\n\n  /**\n   * Returns the coordinate at `m` using linear interpolation, or `null` if no\n   * such coordinate exists.\n   *\n   * `extrapolate` controls extrapolation beyond the range of Ms in the\n   * MultiLineString. If `extrapolate` is `true` then Ms less than the first\n   * M will return the first coordinate and Ms greater than the last M will\n   * return the last coordinate.\n   *\n   * `interpolate` controls interpolation between consecutive LineStrings\n   * within the MultiLineString. If `interpolate` is `true` the coordinates\n   * will be linearly interpolated between the last coordinate of one LineString\n   * and the first coordinate of the next LineString.  If `interpolate` is\n   * `false` then the function will return `null` for Ms falling between\n   * LineStrings.\n   *\n   * @param {number} m M.\n   * @param {boolean} [extrapolate] Extrapolate. Default is `false`.\n   * @param {boolean} [interpolate] Interpolate. Default is `false`.\n   * @return {import(\"../coordinate.js\").Coordinate|null} Coordinate.\n   * @api\n   */\n  getCoordinateAtM(m, extrapolate, interpolate) {\n    if (\n      (this.layout != 'XYM' && this.layout != 'XYZM') ||\n      this.flatCoordinates.length === 0\n    ) {\n      return null;\n    }\n    extrapolate = extrapolate !== undefined ? extrapolate : false;\n    interpolate = interpolate !== undefined ? interpolate : false;\n    return lineStringsCoordinateAtM(\n      this.flatCoordinates,\n      0,\n      this.ends_,\n      this.stride,\n      m,\n      extrapolate,\n      interpolate\n    );\n  }\n\n  /**\n   * Return the coordinates of the multilinestring.\n   * @return {Array<Array<import(\"../coordinate.js\").Coordinate>>} Coordinates.\n   * @api\n   */\n  getCoordinates() {\n    return inflateCoordinatesArray(\n      this.flatCoordinates,\n      0,\n      this.ends_,\n      this.stride\n    );\n  }\n\n  /**\n   * @return {Array<number>} Ends.\n   */\n  getEnds() {\n    return this.ends_;\n  }\n\n  /**\n   * Return the linestring at the specified index.\n   * @param {number} index Index.\n   * @return {LineString} LineString.\n   * @api\n   */\n  getLineString(index) {\n    if (index < 0 || this.ends_.length <= index) {\n      return null;\n    }\n    return new LineString(\n      this.flatCoordinates.slice(\n        index === 0 ? 0 : this.ends_[index - 1],\n        this.ends_[index]\n      ),\n      this.layout\n    );\n  }\n\n  /**\n   * Return the linestrings of this multilinestring.\n   * @return {Array<LineString>} LineStrings.\n   * @api\n   */\n  getLineStrings() {\n    const flatCoordinates = this.flatCoordinates;\n    const ends = this.ends_;\n    const layout = this.layout;\n    /** @type {Array<LineString>} */\n    const lineStrings = [];\n    let offset = 0;\n    for (let i = 0, ii = ends.length; i < ii; ++i) {\n      const end = ends[i];\n      const lineString = new LineString(\n        flatCoordinates.slice(offset, end),\n        layout\n      );\n      lineStrings.push(lineString);\n      offset = end;\n    }\n    return lineStrings;\n  }\n\n  /**\n   * @return {Array<number>} Flat midpoints.\n   */\n  getFlatMidpoints() {\n    const midpoints = [];\n    const flatCoordinates = this.flatCoordinates;\n    let offset = 0;\n    const ends = this.ends_;\n    const stride = this.stride;\n    for (let i = 0, ii = ends.length; i < ii; ++i) {\n      const end = ends[i];\n      const midpoint = interpolatePoint(\n        flatCoordinates,\n        offset,\n        end,\n        stride,\n        0.5\n      );\n      extend(midpoints, midpoint);\n      offset = end;\n    }\n    return midpoints;\n  }\n\n  /**\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {MultiLineString} Simplified MultiLineString.\n   * @protected\n   */\n  getSimplifiedGeometryInternal(squaredTolerance) {\n    const simplifiedFlatCoordinates = [];\n    const simplifiedEnds = [];\n    simplifiedFlatCoordinates.length = douglasPeuckerArray(\n      this.flatCoordinates,\n      0,\n      this.ends_,\n      this.stride,\n      squaredTolerance,\n      simplifiedFlatCoordinates,\n      0,\n      simplifiedEnds\n    );\n    return new MultiLineString(simplifiedFlatCoordinates, 'XY', simplifiedEnds);\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   */\n  getType() {\n    return 'MultiLineString';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   */\n  intersectsExtent(extent) {\n    return intersectsLineStringArray(\n      this.flatCoordinates,\n      0,\n      this.ends_,\n      this.stride,\n      extent\n    );\n  }\n\n  /**\n   * Set the coordinates of the multilinestring.\n   * @param {!Array<Array<import(\"../coordinate.js\").Coordinate>>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   */\n  setCoordinates(coordinates, layout) {\n    this.setLayout(layout, coordinates, 2);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    const ends = deflateCoordinatesArray(\n      this.flatCoordinates,\n      0,\n      coordinates,\n      this.stride,\n      this.ends_\n    );\n    this.flatCoordinates.length = ends.length === 0 ? 0 : ends[ends.length - 1];\n    this.changed();\n  }\n}\n\nexport default MultiLineString;\n", "/**\n * @module ol/geom/MultiPoint\n */\nimport Point from './Point.js';\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {closestSquaredDistanceXY, containsXY} from '../extent.js';\nimport {deflateCoordinates} from './flat/deflate.js';\nimport {extend} from '../array.js';\nimport {inflateCoordinates} from './flat/inflate.js';\nimport {squaredDistance as squaredDx} from '../math.js';\n\n/**\n * @classdesc\n * Multi-point geometry.\n *\n * @api\n */\nclass MultiPoint extends SimpleGeometry {\n  /**\n   * @param {Array<import(\"../coordinate.js\").Coordinate>|Array<number>} coordinates Coordinates.\n   *     For internal use, flat coordinates in combination with `layout` are also accepted.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   */\n  constructor(coordinates, layout) {\n    super();\n    if (layout && !Array.isArray(coordinates[0])) {\n      this.setFlatCoordinates(\n        layout,\n        /** @type {Array<number>} */ (coordinates)\n      );\n    } else {\n      this.setCoordinates(\n        /** @type {Array<import(\"../coordinate.js\").Coordinate>} */ (\n          coordinates\n        ),\n        layout\n      );\n    }\n  }\n\n  /**\n   * Append the passed point to this multipoint.\n   * @param {Point} point Point.\n   * @api\n   */\n  appendPoint(point) {\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = point.getFlatCoordinates().slice();\n    } else {\n      extend(this.flatCoordinates, point.getFlatCoordinates());\n    }\n    this.changed();\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!MultiPoint} Clone.\n   * @api\n   */\n  clone() {\n    const multiPoint = new MultiPoint(\n      this.flatCoordinates.slice(),\n      this.layout\n    );\n    multiPoint.applyProperties(this);\n    return multiPoint;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    if (minSquaredDistance < closestSquaredDistanceXY(this.getExtent(), x, y)) {\n      return minSquaredDistance;\n    }\n    const flatCoordinates = this.flatCoordinates;\n    const stride = this.stride;\n    for (let i = 0, ii = flatCoordinates.length; i < ii; i += stride) {\n      const squaredDistance = squaredDx(\n        x,\n        y,\n        flatCoordinates[i],\n        flatCoordinates[i + 1]\n      );\n      if (squaredDistance < minSquaredDistance) {\n        minSquaredDistance = squaredDistance;\n        for (let j = 0; j < stride; ++j) {\n          closestPoint[j] = flatCoordinates[i + j];\n        }\n        closestPoint.length = stride;\n      }\n    }\n    return minSquaredDistance;\n  }\n\n  /**\n   * Return the coordinates of the multipoint.\n   * @return {Array<import(\"../coordinate.js\").Coordinate>} Coordinates.\n   * @api\n   */\n  getCoordinates() {\n    return inflateCoordinates(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride\n    );\n  }\n\n  /**\n   * Return the point at the specified index.\n   * @param {number} index Index.\n   * @return {Point} Point.\n   * @api\n   */\n  getPoint(index) {\n    const n = !this.flatCoordinates\n      ? 0\n      : this.flatCoordinates.length / this.stride;\n    if (index < 0 || n <= index) {\n      return null;\n    }\n    return new Point(\n      this.flatCoordinates.slice(\n        index * this.stride,\n        (index + 1) * this.stride\n      ),\n      this.layout\n    );\n  }\n\n  /**\n   * Return the points of this multipoint.\n   * @return {Array<Point>} Points.\n   * @api\n   */\n  getPoints() {\n    const flatCoordinates = this.flatCoordinates;\n    const layout = this.layout;\n    const stride = this.stride;\n    /** @type {Array<Point>} */\n    const points = [];\n    for (let i = 0, ii = flatCoordinates.length; i < ii; i += stride) {\n      const point = new Point(flatCoordinates.slice(i, i + stride), layout);\n      points.push(point);\n    }\n    return points;\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   */\n  getType() {\n    return 'MultiPoint';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   */\n  intersectsExtent(extent) {\n    const flatCoordinates = this.flatCoordinates;\n    const stride = this.stride;\n    for (let i = 0, ii = flatCoordinates.length; i < ii; i += stride) {\n      const x = flatCoordinates[i];\n      const y = flatCoordinates[i + 1];\n      if (containsXY(extent, x, y)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * Set the coordinates of the multipoint.\n   * @param {!Array<import(\"../coordinate.js\").Coordinate>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   */\n  setCoordinates(coordinates, layout) {\n    this.setLayout(layout, coordinates, 1);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    this.flatCoordinates.length = deflateCoordinates(\n      this.flatCoordinates,\n      0,\n      coordinates,\n      this.stride\n    );\n    this.changed();\n  }\n}\n\nexport default MultiPoint;\n", "/**\n * @module ol/geom/flat/center\n */\nimport {createEmpty, createOrUpdateFromFlatCoordinates} from '../../extent.js';\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @return {Array<number>} Flat centers.\n */\nexport function linearRingss(flatCoordinates, offset, endss, stride) {\n  const flatCenters = [];\n  let extent = createEmpty();\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    extent = createOrUpdateFromFlatCoordinates(\n      flatCoordinates,\n      offset,\n      ends[0],\n      stride\n    );\n    flatCenters.push((extent[0] + extent[2]) / 2, (extent[1] + extent[3]) / 2);\n    offset = ends[ends.length - 1];\n  }\n  return flatCenters;\n}\n", "/**\n * @module ol/geom/MultiPolygon\n */\nimport MultiPoint from './MultiPoint.js';\nimport Polygon from './Polygon.js';\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {\n  assignClosestMultiArrayPoint,\n  multiArrayMaxSquaredDelta,\n} from './flat/closest.js';\nimport {closestSquaredDistanceXY} from '../extent.js';\nimport {deflateMultiCoordinatesArray} from './flat/deflate.js';\nimport {extend} from '../array.js';\nimport {getInteriorPointsOfMultiArray} from './flat/interiorpoint.js';\nimport {inflateMultiCoordinatesArray} from './flat/inflate.js';\nimport {intersectsLinearRingMultiArray} from './flat/intersectsextent.js';\nimport {\n  linearRingssAreOriented,\n  orientLinearRingsArray,\n} from './flat/orient.js';\nimport {linearRingss as linearRingssArea} from './flat/area.js';\nimport {linearRingss as linearRingssCenter} from './flat/center.js';\nimport {linearRingssContainsXY} from './flat/contains.js';\nimport {quantizeMultiArray} from './flat/simplify.js';\n\n/**\n * @classdesc\n * Multi-polygon geometry.\n *\n * @api\n */\nclass MultiPolygon extends SimpleGeometry {\n  /**\n   * @param {Array<Array<Array<import(\"../coordinate.js\").Coordinate>>|Polygon>|Array<number>} coordinates Coordinates.\n   *     For internal use, flat coordinates in combination with `layout` and `endss` are also accepted.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @param {Array<Array<number>>} [endss] Array of ends for internal use with flat coordinates.\n   */\n  constructor(coordinates, layout, endss) {\n    super();\n\n    /**\n     * @type {Array<Array<number>>}\n     * @private\n     */\n    this.endss_ = [];\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.flatInteriorPointsRevision_ = -1;\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.flatInteriorPoints_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDelta_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDeltaRevision_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.orientedRevision_ = -1;\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.orientedFlatCoordinates_ = null;\n\n    if (!endss && !Array.isArray(coordinates[0])) {\n      let thisLayout = this.getLayout();\n      const polygons = /** @type {Array<Polygon>} */ (coordinates);\n      const flatCoordinates = [];\n      const thisEndss = [];\n      for (let i = 0, ii = polygons.length; i < ii; ++i) {\n        const polygon = polygons[i];\n        if (i === 0) {\n          thisLayout = polygon.getLayout();\n        }\n        const offset = flatCoordinates.length;\n        const ends = polygon.getEnds();\n        for (let j = 0, jj = ends.length; j < jj; ++j) {\n          ends[j] += offset;\n        }\n        extend(flatCoordinates, polygon.getFlatCoordinates());\n        thisEndss.push(ends);\n      }\n      layout = thisLayout;\n      coordinates = flatCoordinates;\n      endss = thisEndss;\n    }\n    if (layout !== undefined && endss) {\n      this.setFlatCoordinates(\n        layout,\n        /** @type {Array<number>} */ (coordinates)\n      );\n      this.endss_ = endss;\n    } else {\n      this.setCoordinates(\n        /** @type {Array<Array<Array<import(\"../coordinate.js\").Coordinate>>>} */ (\n          coordinates\n        ),\n        layout\n      );\n    }\n  }\n\n  /**\n   * Append the passed polygon to this multipolygon.\n   * @param {Polygon} polygon Polygon.\n   * @api\n   */\n  appendPolygon(polygon) {\n    /** @type {Array<number>} */\n    let ends;\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = polygon.getFlatCoordinates().slice();\n      ends = polygon.getEnds().slice();\n      this.endss_.push();\n    } else {\n      const offset = this.flatCoordinates.length;\n      extend(this.flatCoordinates, polygon.getFlatCoordinates());\n      ends = polygon.getEnds().slice();\n      for (let i = 0, ii = ends.length; i < ii; ++i) {\n        ends[i] += offset;\n      }\n    }\n    this.endss_.push(ends);\n    this.changed();\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!MultiPolygon} Clone.\n   * @api\n   */\n  clone() {\n    const len = this.endss_.length;\n    const newEndss = new Array(len);\n    for (let i = 0; i < len; ++i) {\n      newEndss[i] = this.endss_[i].slice();\n    }\n\n    const multiPolygon = new MultiPolygon(\n      this.flatCoordinates.slice(),\n      this.layout,\n      newEndss\n    );\n    multiPolygon.applyProperties(this);\n\n    return multiPolygon;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    if (minSquaredDistance < closestSquaredDistanceXY(this.getExtent(), x, y)) {\n      return minSquaredDistance;\n    }\n    if (this.maxDeltaRevision_ != this.getRevision()) {\n      this.maxDelta_ = Math.sqrt(\n        multiArrayMaxSquaredDelta(\n          this.flatCoordinates,\n          0,\n          this.endss_,\n          this.stride,\n          0\n        )\n      );\n      this.maxDeltaRevision_ = this.getRevision();\n    }\n    return assignClosestMultiArrayPoint(\n      this.getOrientedFlatCoordinates(),\n      0,\n      this.endss_,\n      this.stride,\n      this.maxDelta_,\n      true,\n      x,\n      y,\n      closestPoint,\n      minSquaredDistance\n    );\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @return {boolean} Contains (x, y).\n   */\n  containsXY(x, y) {\n    return linearRingssContainsXY(\n      this.getOrientedFlatCoordinates(),\n      0,\n      this.endss_,\n      this.stride,\n      x,\n      y\n    );\n  }\n\n  /**\n   * Return the area of the multipolygon on projected plane.\n   * @return {number} Area (on projected plane).\n   * @api\n   */\n  getArea() {\n    return linearRingssArea(\n      this.getOrientedFlatCoordinates(),\n      0,\n      this.endss_,\n      this.stride\n    );\n  }\n\n  /**\n   * Get the coordinate array for this geometry.  This array has the structure\n   * of a GeoJSON coordinate array for multi-polygons.\n   *\n   * @param {boolean} [right] Orient coordinates according to the right-hand\n   *     rule (counter-clockwise for exterior and clockwise for interior rings).\n   *     If `false`, coordinates will be oriented according to the left-hand rule\n   *     (clockwise for exterior and counter-clockwise for interior rings).\n   *     By default, coordinate orientation will depend on how the geometry was\n   *     constructed.\n   * @return {Array<Array<Array<import(\"../coordinate.js\").Coordinate>>>} Coordinates.\n   * @api\n   */\n  getCoordinates(right) {\n    let flatCoordinates;\n    if (right !== undefined) {\n      flatCoordinates = this.getOrientedFlatCoordinates().slice();\n      orientLinearRingsArray(\n        flatCoordinates,\n        0,\n        this.endss_,\n        this.stride,\n        right\n      );\n    } else {\n      flatCoordinates = this.flatCoordinates;\n    }\n\n    return inflateMultiCoordinatesArray(\n      flatCoordinates,\n      0,\n      this.endss_,\n      this.stride\n    );\n  }\n\n  /**\n   * @return {Array<Array<number>>} Endss.\n   */\n  getEndss() {\n    return this.endss_;\n  }\n\n  /**\n   * @return {Array<number>} Flat interior points.\n   */\n  getFlatInteriorPoints() {\n    if (this.flatInteriorPointsRevision_ != this.getRevision()) {\n      const flatCenters = linearRingssCenter(\n        this.flatCoordinates,\n        0,\n        this.endss_,\n        this.stride\n      );\n      this.flatInteriorPoints_ = getInteriorPointsOfMultiArray(\n        this.getOrientedFlatCoordinates(),\n        0,\n        this.endss_,\n        this.stride,\n        flatCenters\n      );\n      this.flatInteriorPointsRevision_ = this.getRevision();\n    }\n    return this.flatInteriorPoints_;\n  }\n\n  /**\n   * Return the interior points as {@link module:ol/geom/MultiPoint~MultiPoint multipoint}.\n   * @return {MultiPoint} Interior points as XYM coordinates, where M is\n   * the length of the horizontal intersection that the point belongs to.\n   * @api\n   */\n  getInteriorPoints() {\n    return new MultiPoint(this.getFlatInteriorPoints().slice(), 'XYM');\n  }\n\n  /**\n   * @return {Array<number>} Oriented flat coordinates.\n   */\n  getOrientedFlatCoordinates() {\n    if (this.orientedRevision_ != this.getRevision()) {\n      const flatCoordinates = this.flatCoordinates;\n      if (\n        linearRingssAreOriented(flatCoordinates, 0, this.endss_, this.stride)\n      ) {\n        this.orientedFlatCoordinates_ = flatCoordinates;\n      } else {\n        this.orientedFlatCoordinates_ = flatCoordinates.slice();\n        this.orientedFlatCoordinates_.length = orientLinearRingsArray(\n          this.orientedFlatCoordinates_,\n          0,\n          this.endss_,\n          this.stride\n        );\n      }\n      this.orientedRevision_ = this.getRevision();\n    }\n    return this.orientedFlatCoordinates_;\n  }\n\n  /**\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {MultiPolygon} Simplified MultiPolygon.\n   * @protected\n   */\n  getSimplifiedGeometryInternal(squaredTolerance) {\n    const simplifiedFlatCoordinates = [];\n    const simplifiedEndss = [];\n    simplifiedFlatCoordinates.length = quantizeMultiArray(\n      this.flatCoordinates,\n      0,\n      this.endss_,\n      this.stride,\n      Math.sqrt(squaredTolerance),\n      simplifiedFlatCoordinates,\n      0,\n      simplifiedEndss\n    );\n    return new MultiPolygon(simplifiedFlatCoordinates, 'XY', simplifiedEndss);\n  }\n\n  /**\n   * Return the polygon at the specified index.\n   * @param {number} index Index.\n   * @return {Polygon} Polygon.\n   * @api\n   */\n  getPolygon(index) {\n    if (index < 0 || this.endss_.length <= index) {\n      return null;\n    }\n    let offset;\n    if (index === 0) {\n      offset = 0;\n    } else {\n      const prevEnds = this.endss_[index - 1];\n      offset = prevEnds[prevEnds.length - 1];\n    }\n    const ends = this.endss_[index].slice();\n    const end = ends[ends.length - 1];\n    if (offset !== 0) {\n      for (let i = 0, ii = ends.length; i < ii; ++i) {\n        ends[i] -= offset;\n      }\n    }\n    return new Polygon(\n      this.flatCoordinates.slice(offset, end),\n      this.layout,\n      ends\n    );\n  }\n\n  /**\n   * Return the polygons of this multipolygon.\n   * @return {Array<Polygon>} Polygons.\n   * @api\n   */\n  getPolygons() {\n    const layout = this.layout;\n    const flatCoordinates = this.flatCoordinates;\n    const endss = this.endss_;\n    const polygons = [];\n    let offset = 0;\n    for (let i = 0, ii = endss.length; i < ii; ++i) {\n      const ends = endss[i].slice();\n      const end = ends[ends.length - 1];\n      if (offset !== 0) {\n        for (let j = 0, jj = ends.length; j < jj; ++j) {\n          ends[j] -= offset;\n        }\n      }\n      const polygon = new Polygon(\n        flatCoordinates.slice(offset, end),\n        layout,\n        ends\n      );\n      polygons.push(polygon);\n      offset = end;\n    }\n    return polygons;\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   */\n  getType() {\n    return 'MultiPolygon';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   */\n  intersectsExtent(extent) {\n    return intersectsLinearRingMultiArray(\n      this.getOrientedFlatCoordinates(),\n      0,\n      this.endss_,\n      this.stride,\n      extent\n    );\n  }\n\n  /**\n   * Set the coordinates of the multipolygon.\n   * @param {!Array<Array<Array<import(\"../coordinate.js\").Coordinate>>>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   */\n  setCoordinates(coordinates, layout) {\n    this.setLayout(layout, coordinates, 3);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    const endss = deflateMultiCoordinatesArray(\n      this.flatCoordinates,\n      0,\n      coordinates,\n      this.stride,\n      this.endss_\n    );\n    if (endss.length === 0) {\n      this.flatCoordinates.length = 0;\n    } else {\n      const lastEnds = endss[endss.length - 1];\n      this.flatCoordinates.length =\n        lastEnds.length === 0 ? 0 : lastEnds[lastEnds.length - 1];\n    }\n    this.changed();\n  }\n}\n\nexport default MultiPolygon;\n", "/**\n * @module ol/geom/GeometryCollection\n */\nimport EventType from '../events/EventType.js';\nimport Geometry from './Geometry.js';\nimport {\n  closestSquaredDistanceXY,\n  createOrUpdateEmpty,\n  extend,\n  getCenter,\n} from '../extent.js';\nimport {listen, unlistenBy<PERSON>ey} from '../events.js';\n\n/**\n * @classdesc\n * An array of {@link module:ol/geom/Geometry~Geometry} objects.\n *\n * @api\n */\nclass GeometryCollection extends Geometry {\n  /**\n   * @param {Array<Geometry>} [geometries] Geometries.\n   */\n  constructor(geometries) {\n    super();\n\n    /**\n     * @private\n     * @type {Array<Geometry>}\n     */\n    this.geometries_ = geometries ? geometries : null;\n\n    /**\n     * @type {Array<import(\"../events.js\").EventsKey>}\n     */\n    this.changeEventsKeys_ = [];\n\n    this.listenGeometriesChange_();\n  }\n\n  /**\n   * @private\n   */\n  unlistenGeometriesChange_() {\n    this.changeEventsKeys_.forEach(unlistenByKey);\n    this.changeEventsKeys_.length = 0;\n  }\n\n  /**\n   * @private\n   */\n  listenGeometriesChange_() {\n    if (!this.geometries_) {\n      return;\n    }\n    for (let i = 0, ii = this.geometries_.length; i < ii; ++i) {\n      this.changeEventsKeys_.push(\n        listen(this.geometries_[i], EventType.CHANGE, this.changed, this)\n      );\n    }\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!GeometryCollection} Clone.\n   * @api\n   */\n  clone() {\n    const geometryCollection = new GeometryCollection(null);\n    geometryCollection.setGeometries(this.geometries_);\n    geometryCollection.applyProperties(this);\n    return geometryCollection;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    if (minSquaredDistance < closestSquaredDistanceXY(this.getExtent(), x, y)) {\n      return minSquaredDistance;\n    }\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      minSquaredDistance = geometries[i].closestPointXY(\n        x,\n        y,\n        closestPoint,\n        minSquaredDistance\n      );\n    }\n    return minSquaredDistance;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @return {boolean} Contains (x, y).\n   */\n  containsXY(x, y) {\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      if (geometries[i].containsXY(x, y)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @protected\n   * @return {import(\"../extent.js\").Extent} extent Extent.\n   */\n  computeExtent(extent) {\n    createOrUpdateEmpty(extent);\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      extend(extent, geometries[i].getExtent());\n    }\n    return extent;\n  }\n\n  /**\n   * Return the geometries that make up this geometry collection.\n   * @return {Array<Geometry>} Geometries.\n   * @api\n   */\n  getGeometries() {\n    return cloneGeometries(this.geometries_);\n  }\n\n  /**\n   * @return {Array<Geometry>} Geometries.\n   */\n  getGeometriesArray() {\n    return this.geometries_;\n  }\n\n  /**\n   * @return {Array<Geometry>} Geometries.\n   */\n  getGeometriesArrayRecursive() {\n    /** @type {Array<Geometry>} */\n    let geometriesArray = [];\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      if (geometries[i].getType() === this.getType()) {\n        geometriesArray = geometriesArray.concat(\n          /** @type {GeometryCollection} */ (\n            geometries[i]\n          ).getGeometriesArrayRecursive()\n        );\n      } else {\n        geometriesArray.push(geometries[i]);\n      }\n    }\n    return geometriesArray;\n  }\n\n  /**\n   * Create a simplified version of this geometry using the Douglas Peucker algorithm.\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {GeometryCollection} Simplified GeometryCollection.\n   */\n  getSimplifiedGeometry(squaredTolerance) {\n    if (this.simplifiedGeometryRevision !== this.getRevision()) {\n      this.simplifiedGeometryMaxMinSquaredTolerance = 0;\n      this.simplifiedGeometryRevision = this.getRevision();\n    }\n    if (\n      squaredTolerance < 0 ||\n      (this.simplifiedGeometryMaxMinSquaredTolerance !== 0 &&\n        squaredTolerance < this.simplifiedGeometryMaxMinSquaredTolerance)\n    ) {\n      return this;\n    }\n\n    const simplifiedGeometries = [];\n    const geometries = this.geometries_;\n    let simplified = false;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      const geometry = geometries[i];\n      const simplifiedGeometry =\n        geometry.getSimplifiedGeometry(squaredTolerance);\n      simplifiedGeometries.push(simplifiedGeometry);\n      if (simplifiedGeometry !== geometry) {\n        simplified = true;\n      }\n    }\n    if (simplified) {\n      const simplifiedGeometryCollection = new GeometryCollection(null);\n      simplifiedGeometryCollection.setGeometriesArray(simplifiedGeometries);\n      return simplifiedGeometryCollection;\n    }\n    this.simplifiedGeometryMaxMinSquaredTolerance = squaredTolerance;\n    return this;\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   */\n  getType() {\n    return 'GeometryCollection';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   */\n  intersectsExtent(extent) {\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      if (geometries[i].intersectsExtent(extent)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * @return {boolean} Is empty.\n   */\n  isEmpty() {\n    return this.geometries_.length === 0;\n  }\n\n  /**\n   * Rotate the geometry around a given coordinate. This modifies the geometry\n   * coordinates in place.\n   * @param {number} angle Rotation angle in radians.\n   * @param {import(\"../coordinate.js\").Coordinate} anchor The rotation center.\n   * @api\n   */\n  rotate(angle, anchor) {\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      geometries[i].rotate(angle, anchor);\n    }\n    this.changed();\n  }\n\n  /**\n   * Scale the geometry (with an optional origin).  This modifies the geometry\n   * coordinates in place.\n   * @abstract\n   * @param {number} sx The scaling factor in the x-direction.\n   * @param {number} [sy] The scaling factor in the y-direction (defaults to sx).\n   * @param {import(\"../coordinate.js\").Coordinate} [anchor] The scale origin (defaults to the center\n   *     of the geometry extent).\n   * @api\n   */\n  scale(sx, sy, anchor) {\n    if (!anchor) {\n      anchor = getCenter(this.getExtent());\n    }\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      geometries[i].scale(sx, sy, anchor);\n    }\n    this.changed();\n  }\n\n  /**\n   * Set the geometries that make up this geometry collection.\n   * @param {Array<Geometry>} geometries Geometries.\n   * @api\n   */\n  setGeometries(geometries) {\n    this.setGeometriesArray(cloneGeometries(geometries));\n  }\n\n  /**\n   * @param {Array<Geometry>} geometries Geometries.\n   */\n  setGeometriesArray(geometries) {\n    this.unlistenGeometriesChange_();\n    this.geometries_ = geometries;\n    this.listenGeometriesChange_();\n    this.changed();\n  }\n\n  /**\n   * Apply a transform function to the coordinates of the geometry.\n   * The geometry is modified in place.\n   * If you do not want the geometry modified in place, first `clone()` it and\n   * then use this function on the clone.\n   * @param {import(\"../proj.js\").TransformFunction} transformFn Transform function.\n   * Called with a flat array of geometry coordinates.\n   * @api\n   */\n  applyTransform(transformFn) {\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      geometries[i].applyTransform(transformFn);\n    }\n    this.changed();\n  }\n\n  /**\n   * Translate the geometry.  This modifies the geometry coordinates in place.  If\n   * instead you want a new geometry, first `clone()` this geometry.\n   * @param {number} deltaX Delta X.\n   * @param {number} deltaY Delta Y.\n   * @api\n   */\n  translate(deltaX, deltaY) {\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      geometries[i].translate(deltaX, deltaY);\n    }\n    this.changed();\n  }\n\n  /**\n   * Clean up.\n   */\n  disposeInternal() {\n    this.unlistenGeometriesChange_();\n    super.disposeInternal();\n  }\n}\n\n/**\n * @param {Array<Geometry>} geometries Geometries.\n * @return {Array<Geometry>} Cloned geometries.\n */\nfunction cloneGeometries(geometries) {\n  const clonedGeometries = [];\n  for (let i = 0, ii = geometries.length; i < ii; ++i) {\n    clonedGeometries.push(geometries[i].clone());\n  }\n  return clonedGeometries;\n}\n\nexport default GeometryCollection;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBO,SAAS,iBACd,iBACA,QACA,KACA,QACA,UACA,MACA,WACA;AACA,MAAI,GAAG;AACP,QAAM,KAAK,MAAM,UAAU;AAC3B,MAAI,MAAM,GAAG;AACX,QAAI;AAAA,EACN,WAAW,MAAM,GAAG;AAClB,QAAI;AACJ,QAAI;AAAA,EACN,WAAW,MAAM,GAAG;AAClB,QAAI,KAAK,gBAAgB,MAAM;AAC/B,QAAI,KAAK,gBAAgB,SAAS,CAAC;AACnC,QAAI,SAAS;AACb,UAAM,oBAAoB,CAAC,CAAC;AAC5B,aAAS,IAAI,SAAS,QAAQ,IAAI,KAAK,KAAK,QAAQ;AAClD,YAAM,KAAK,gBAAgB,CAAC;AAC5B,YAAM,KAAK,gBAAgB,IAAI,CAAC;AAChC,gBAAU,KAAK,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,GAAG;AACjE,wBAAkB,KAAK,MAAM;AAC7B,WAAK;AACL,WAAK;AAAA,IACP;AACA,UAAM,SAAS,WAAW;AAC1B,UAAM,QAAQ,aAAa,mBAAmB,MAAM;AACpD,QAAI,QAAQ,GAAG;AACb,WACG,SAAS,kBAAkB,CAAC,QAAQ,CAAC,MACrC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,kBAAkB,CAAC,QAAQ,CAAC;AAC/D,UAAI,UAAU,CAAC,QAAQ,KAAK;AAAA,IAC9B,OAAO;AACL,UAAI,SAAS,QAAQ;AAAA,IACvB;AAAA,EACF;AACA,cAAY,YAAY,IAAI,YAAY;AACxC,SAAO,OAAO,OAAO,IAAI,MAAM,SAAS;AACxC,WAAS,IAAI,GAAG,IAAI,WAAW,EAAE,GAAG;AAClC,SAAK,CAAC,IACJ,MAAM,SACF,MACA,MAAM,SACN,gBAAgB,IAAI,CAAC,IACrB,KAAK,gBAAgB,IAAI,CAAC,GAAG,gBAAgB,IAAI,SAAS,CAAC,GAAG,CAAC;AAAA,EACvE;AACA,SAAO;AACT;AAWO,SAAS,wBACd,iBACA,QACA,KACA,QACA,GACA,aACA;AACA,MAAI,OAAO,QAAQ;AACjB,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI,IAAI,gBAAgB,SAAS,SAAS,CAAC,GAAG;AAC5C,QAAI,aAAa;AACf,mBAAa,gBAAgB,MAAM,QAAQ,SAAS,MAAM;AAC1D,iBAAW,SAAS,CAAC,IAAI;AACzB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,MAAM,CAAC,IAAI,GAAG;AAChC,QAAI,aAAa;AACf,mBAAa,gBAAgB,MAAM,MAAM,QAAQ,GAAG;AACpD,iBAAW,SAAS,CAAC,IAAI;AACzB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,gBAAgB,SAAS,SAAS,CAAC,GAAG;AAC7C,WAAO,gBAAgB,MAAM,QAAQ,SAAS,MAAM;AAAA,EACtD;AACA,MAAI,KAAK,SAAS;AAClB,MAAI,KAAK,MAAM;AACf,SAAO,KAAK,IAAI;AACd,UAAM,MAAO,KAAK,MAAO;AACzB,QAAI,IAAI,iBAAiB,MAAM,KAAK,SAAS,CAAC,GAAG;AAC/C,WAAK;AAAA,IACP,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AACA,QAAM,KAAK,gBAAgB,KAAK,SAAS,CAAC;AAC1C,MAAI,KAAK,IAAI;AACX,WAAO,gBAAgB,OAAO,KAAK,KAAK,SAAS,KAAK,KAAK,SAAS,MAAM;AAAA,EAC5E;AACA,QAAM,KAAK,iBAAiB,KAAK,KAAK,SAAS,CAAC;AAChD,QAAM,KAAK,IAAI,OAAO,KAAK;AAC3B,eAAa,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,SAAS,GAAG,EAAE,GAAG;AACnC,eAAW;AAAA,MACT;AAAA,QACE,iBAAiB,KAAK,KAAK,SAAS,CAAC;AAAA,QACrC,gBAAgB,KAAK,SAAS,CAAC;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,aAAW,KAAK,CAAC;AACjB,SAAO;AACT;AAYO,SAAS,yBACd,iBACA,QACA,MACA,QACA,GACA,aACA,aACA;AACA,MAAI,aAAa;AACf,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAK,KAAK,SAAS,CAAC;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,MAAI;AACJ,MAAI,IAAI,gBAAgB,SAAS,CAAC,GAAG;AACnC,QAAI,aAAa;AACf,mBAAa,gBAAgB,MAAM,GAAG,MAAM;AAC5C,iBAAW,SAAS,CAAC,IAAI;AACzB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,gBAAgB,SAAS,CAAC,IAAI,GAAG;AACnD,QAAI,aAAa;AACf,mBAAa,gBAAgB,MAAM,gBAAgB,SAAS,MAAM;AAClE,iBAAW,SAAS,CAAC,IAAI;AACzB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,QAAI,UAAU,KAAK;AACjB;AAAA,IACF;AACA,QAAI,IAAI,gBAAgB,SAAS,SAAS,CAAC,GAAG;AAC5C,aAAO;AAAA,IACT;AACA,QAAI,KAAK,gBAAgB,MAAM,CAAC,GAAG;AACjC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,aAAS;AAAA,EACX;AACA,SAAO;AACT;;;AC1LA,IAAM,aAAN,MAAM,oBAAmB,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtC,YAAY,aAAa,QAAQ;AAC/B,UAAM;AAMN,SAAK,gBAAgB;AAMrB,SAAK,wBAAwB;AAM7B,SAAK,YAAY;AAMjB,SAAK,oBAAoB;AAEzB,QAAI,WAAW,UAAa,CAAC,MAAM,QAAQ,YAAY,CAAC,CAAC,GAAG;AAC1D,WAAK;AAAA,QACH;AAAA;AAAA,QAC8B;AAAA,MAChC;AAAA,IACF,OAAO;AACL,WAAK;AAAA;AAAA,QAED;AAAA,QAEF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,YAAY;AAC3B,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,WAAW,MAAM;AAAA,IAC1C,OAAO;AACL,aAAO,KAAK,iBAAiB,UAAU;AAAA,IACzC;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,UAAM,aAAa,IAAI;AAAA,MACrB,KAAK,gBAAgB,MAAM;AAAA,MAC3B,KAAK;AAAA,IACP;AACA,eAAW,gBAAgB,IAAI;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,QAAI,qBAAqB,yBAAyB,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG;AACzE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,qBAAqB,KAAK,YAAY,GAAG;AAChD,WAAK,YAAY,KAAK;AAAA,QACpB;AAAA,UACE,KAAK;AAAA,UACL;AAAA,UACA,KAAK,gBAAgB;AAAA,UACrB,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,WAAK,oBAAoB,KAAK,YAAY;AAAA,IAC5C;AACA,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,eAAe,UAAU;AACvB,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,iBAAiB,GAAG,aAAa;AAC/B,QAAI,KAAK,UAAU,SAAS,KAAK,UAAU,QAAQ;AACjD,aAAO;AAAA,IACT;AACA,kBAAc,gBAAgB,SAAY,cAAc;AACxD,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,gBAAgB,UAAU,MAAM;AAC9B,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,QAAI,KAAK,yBAAyB,KAAK,YAAY,GAAG;AACpD,WAAK,gBAAgB,KAAK,gBAAgB,KAAK,KAAK,aAAa;AACjE,WAAK,wBAAwB,KAAK,YAAY;AAAA,IAChD;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,8BAA8B,kBAAkB;AAC9C,UAAM,4BAA4B,CAAC;AACnC,8BAA0B,SAAS;AAAA,MACjC,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,IAAI,YAAW,2BAA2B,IAAI;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,QAAQ;AACvB,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,aAAa,QAAQ;AAClC,SAAK,UAAU,QAAQ,aAAa,CAAC;AACrC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AACA,SAAK,gBAAgB,SAAS;AAAA,MAC5B,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACP;AACA,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,qBAAQ;;;AC/Rf,IAAM,kBAAN,MAAM,yBAAwB,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ3C,YAAY,aAAa,QAAQ,MAAM;AACrC,UAAM;AAMN,SAAK,QAAQ,CAAC;AAMd,SAAK,YAAY;AAMjB,SAAK,oBAAoB;AAEzB,QAAI,MAAM,QAAQ,YAAY,CAAC,CAAC,GAAG;AACjC,WAAK;AAAA;AAAA,QAED;AAAA,QAEF;AAAA,MACF;AAAA,IACF,WAAW,WAAW,UAAa,MAAM;AACvC,WAAK;AAAA,QACH;AAAA;AAAA,QAC8B;AAAA,MAChC;AACA,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,UAAIA,UAAS,KAAK,UAAU;AAC5B,YAAM;AAAA;AAAA,QAAgD;AAAA;AACtD,YAAM,kBAAkB,CAAC;AACzB,YAAMC,QAAO,CAAC;AACd,eAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,cAAM,aAAa,YAAY,CAAC;AAChC,YAAI,MAAM,GAAG;AACX,UAAAD,UAAS,WAAW,UAAU;AAAA,QAChC;AACA,eAAO,iBAAiB,WAAW,mBAAmB,CAAC;AACvD,QAAAC,MAAK,KAAK,gBAAgB,MAAM;AAAA,MAClC;AACA,WAAK,mBAAmBD,SAAQ,eAAe;AAC/C,WAAK,QAAQC;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,YAAY;AAC3B,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,WAAW,mBAAmB,EAAE,MAAM;AAAA,IAC/D,OAAO;AACL,aAAO,KAAK,iBAAiB,WAAW,mBAAmB,EAAE,MAAM,CAAC;AAAA,IACtE;AACA,SAAK,MAAM,KAAK,KAAK,gBAAgB,MAAM;AAC3C,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,UAAM,kBAAkB,IAAI;AAAA,MAC1B,KAAK,gBAAgB,MAAM;AAAA,MAC3B,KAAK;AAAA,MACL,KAAK,MAAM,MAAM;AAAA,IACnB;AACA,oBAAgB,gBAAgB,IAAI;AACpC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,QAAI,qBAAqB,yBAAyB,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG;AACzE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,qBAAqB,KAAK,YAAY,GAAG;AAChD,WAAK,YAAY,KAAK;AAAA,QACpB;AAAA,UACE,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,WAAK,oBAAoB,KAAK,YAAY;AAAA,IAC5C;AACA,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBA,iBAAiB,GAAG,aAAa,aAAa;AAC5C,QACG,KAAK,UAAU,SAAS,KAAK,UAAU,UACxC,KAAK,gBAAgB,WAAW,GAChC;AACA,aAAO;AAAA,IACT;AACA,kBAAc,gBAAgB,SAAY,cAAc;AACxD,kBAAc,gBAAgB,SAAY,cAAc;AACxD,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,OAAO;AACnB,QAAI,QAAQ,KAAK,KAAK,MAAM,UAAU,OAAO;AAC3C,aAAO;AAAA,IACT;AACA,WAAO,IAAI;AAAA,MACT,KAAK,gBAAgB;AAAA,QACnB,UAAU,IAAI,IAAI,KAAK,MAAM,QAAQ,CAAC;AAAA,QACtC,KAAK,MAAM,KAAK;AAAA,MAClB;AAAA,MACA,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,UAAM,kBAAkB,KAAK;AAC7B,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS,KAAK;AAEpB,UAAM,cAAc,CAAC;AACrB,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,YAAM,MAAM,KAAK,CAAC;AAClB,YAAM,aAAa,IAAI;AAAA,QACrB,gBAAgB,MAAM,QAAQ,GAAG;AAAA,QACjC;AAAA,MACF;AACA,kBAAY,KAAK,UAAU;AAC3B,eAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,UAAM,YAAY,CAAC;AACnB,UAAM,kBAAkB,KAAK;AAC7B,QAAI,SAAS;AACb,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS,KAAK;AACpB,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,YAAM,MAAM,KAAK,CAAC;AAClB,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO,WAAW,QAAQ;AAC1B,eAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,8BAA8B,kBAAkB;AAC9C,UAAM,4BAA4B,CAAC;AACnC,UAAM,iBAAiB,CAAC;AACxB,8BAA0B,SAAS;AAAA,MACjC,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,IAAI,iBAAgB,2BAA2B,MAAM,cAAc;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,QAAQ;AACvB,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,aAAa,QAAQ;AAClC,SAAK,UAAU,QAAQ,aAAa,CAAC;AACrC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AACA,UAAM,OAAO;AAAA,MACX,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,SAAK,gBAAgB,SAAS,KAAK,WAAW,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC1E,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,0BAAQ;;;AC5Uf,IAAM,aAAN,MAAM,oBAAmB,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtC,YAAY,aAAa,QAAQ;AAC/B,UAAM;AACN,QAAI,UAAU,CAAC,MAAM,QAAQ,YAAY,CAAC,CAAC,GAAG;AAC5C,WAAK;AAAA,QACH;AAAA;AAAA,QAC8B;AAAA,MAChC;AAAA,IACF,OAAO;AACL,WAAK;AAAA;AAAA,QAED;AAAA,QAEF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,OAAO;AACjB,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,MAAM,mBAAmB,EAAE,MAAM;AAAA,IAC1D,OAAO;AACL,aAAO,KAAK,iBAAiB,MAAM,mBAAmB,CAAC;AAAA,IACzD;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,UAAM,aAAa,IAAI;AAAA,MACrB,KAAK,gBAAgB,MAAM;AAAA,MAC3B,KAAK;AAAA,IACP;AACA,eAAW,gBAAgB,IAAI;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,QAAI,qBAAqB,yBAAyB,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG;AACzE,aAAO;AAAA,IACT;AACA,UAAM,kBAAkB,KAAK;AAC7B,UAAM,SAAS,KAAK;AACpB,aAAS,IAAI,GAAG,KAAK,gBAAgB,QAAQ,IAAI,IAAI,KAAK,QAAQ;AAChE,YAAMC,mBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,QACA,gBAAgB,CAAC;AAAA,QACjB,gBAAgB,IAAI,CAAC;AAAA,MACvB;AACA,UAAIA,mBAAkB,oBAAoB;AACxC,6BAAqBA;AACrB,iBAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,uBAAa,CAAC,IAAI,gBAAgB,IAAI,CAAC;AAAA,QACzC;AACA,qBAAa,SAAS;AAAA,MACxB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO;AACd,UAAM,IAAI,CAAC,KAAK,kBACZ,IACA,KAAK,gBAAgB,SAAS,KAAK;AACvC,QAAI,QAAQ,KAAK,KAAK,OAAO;AAC3B,aAAO;AAAA,IACT;AACA,WAAO,IAAI;AAAA,MACT,KAAK,gBAAgB;AAAA,QACnB,QAAQ,KAAK;AAAA,SACZ,QAAQ,KAAK,KAAK;AAAA,MACrB;AAAA,MACA,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,UAAM,kBAAkB,KAAK;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,SAAS,KAAK;AAEpB,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,GAAG,KAAK,gBAAgB,QAAQ,IAAI,IAAI,KAAK,QAAQ;AAChE,YAAM,QAAQ,IAAI,cAAM,gBAAgB,MAAM,GAAG,IAAI,MAAM,GAAG,MAAM;AACpE,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,QAAQ;AACvB,UAAM,kBAAkB,KAAK;AAC7B,UAAM,SAAS,KAAK;AACpB,aAAS,IAAI,GAAG,KAAK,gBAAgB,QAAQ,IAAI,IAAI,KAAK,QAAQ;AAChE,YAAM,IAAI,gBAAgB,CAAC;AAC3B,YAAM,IAAI,gBAAgB,IAAI,CAAC;AAC/B,UAAI,WAAW,QAAQ,GAAG,CAAC,GAAG;AAC5B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,aAAa,QAAQ;AAClC,SAAK,UAAU,QAAQ,aAAa,CAAC;AACrC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AACA,SAAK,gBAAgB,SAAS;AAAA,MAC5B,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACP;AACA,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,qBAAQ;;;AC9LR,SAASC,cAAa,iBAAiB,QAAQ,OAAO,QAAQ;AACnE,QAAM,cAAc,CAAC;AACrB,MAAI,SAAS,YAAY;AACzB,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,aAAS;AAAA,MACP;AAAA,MACA;AAAA,MACA,KAAK,CAAC;AAAA,MACN;AAAA,IACF;AACA,gBAAY,MAAM,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC;AACzE,aAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;;;ACIA,IAAM,eAAN,MAAM,sBAAqB,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxC,YAAY,aAAa,QAAQ,OAAO;AACtC,UAAM;AAMN,SAAK,SAAS,CAAC;AAMf,SAAK,8BAA8B;AAMnC,SAAK,sBAAsB;AAM3B,SAAK,YAAY;AAMjB,SAAK,oBAAoB;AAMzB,SAAK,oBAAoB;AAMzB,SAAK,2BAA2B;AAEhC,QAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,YAAY,CAAC,CAAC,GAAG;AAC5C,UAAI,aAAa,KAAK,UAAU;AAChC,YAAM;AAAA;AAAA,QAA0C;AAAA;AAChD,YAAM,kBAAkB,CAAC;AACzB,YAAM,YAAY,CAAC;AACnB,eAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,EAAE,GAAG;AACjD,cAAM,UAAU,SAAS,CAAC;AAC1B,YAAI,MAAM,GAAG;AACX,uBAAa,QAAQ,UAAU;AAAA,QACjC;AACA,cAAM,SAAS,gBAAgB;AAC/B,cAAM,OAAO,QAAQ,QAAQ;AAC7B,iBAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,eAAK,CAAC,KAAK;AAAA,QACb;AACA,eAAO,iBAAiB,QAAQ,mBAAmB,CAAC;AACpD,kBAAU,KAAK,IAAI;AAAA,MACrB;AACA,eAAS;AACT,oBAAc;AACd,cAAQ;AAAA,IACV;AACA,QAAI,WAAW,UAAa,OAAO;AACjC,WAAK;AAAA,QACH;AAAA;AAAA,QAC8B;AAAA,MAChC;AACA,WAAK,SAAS;AAAA,IAChB,OAAO;AACL,WAAK;AAAA;AAAA,QAED;AAAA,QAEF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,SAAS;AAErB,QAAI;AACJ,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,QAAQ,mBAAmB,EAAE,MAAM;AAC1D,aAAO,QAAQ,QAAQ,EAAE,MAAM;AAC/B,WAAK,OAAO,KAAK;AAAA,IACnB,OAAO;AACL,YAAM,SAAS,KAAK,gBAAgB;AACpC,aAAO,KAAK,iBAAiB,QAAQ,mBAAmB,CAAC;AACzD,aAAO,QAAQ,QAAQ,EAAE,MAAM;AAC/B,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,aAAK,CAAC,KAAK;AAAA,MACb;AAAA,IACF;AACA,SAAK,OAAO,KAAK,IAAI;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,WAAW,IAAI,MAAM,GAAG;AAC9B,aAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,eAAS,CAAC,IAAI,KAAK,OAAO,CAAC,EAAE,MAAM;AAAA,IACrC;AAEA,UAAM,eAAe,IAAI;AAAA,MACvB,KAAK,gBAAgB,MAAM;AAAA,MAC3B,KAAK;AAAA,MACL;AAAA,IACF;AACA,iBAAa,gBAAgB,IAAI;AAEjC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,QAAI,qBAAqB,yBAAyB,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG;AACzE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,qBAAqB,KAAK,YAAY,GAAG;AAChD,WAAK,YAAY,KAAK;AAAA,QACpB;AAAA,UACE,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,WAAK,oBAAoB,KAAK,YAAY;AAAA,IAC5C;AACA,WAAO;AAAA,MACL,KAAK,2BAA2B;AAAA,MAChC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,GAAG,GAAG;AACf,WAAO;AAAA,MACL,KAAK,2BAA2B;AAAA,MAChC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO;AAAA,MACL,KAAK,2BAA2B;AAAA,MAChC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,eAAe,OAAO;AACpB,QAAI;AACJ,QAAI,UAAU,QAAW;AACvB,wBAAkB,KAAK,2BAA2B,EAAE,MAAM;AAC1D;AAAA,QACE;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,MACF;AAAA,IACF,OAAO;AACL,wBAAkB,KAAK;AAAA,IACzB;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,QAAI,KAAK,+BAA+B,KAAK,YAAY,GAAG;AAC1D,YAAM,cAAcC;AAAA,QAClB,KAAK;AAAA,QACL;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AACA,WAAK,sBAAsB;AAAA,QACzB,KAAK,2BAA2B;AAAA,QAChC;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,MACF;AACA,WAAK,8BAA8B,KAAK,YAAY;AAAA,IACtD;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB;AAClB,WAAO,IAAI,mBAAW,KAAK,sBAAsB,EAAE,MAAM,GAAG,KAAK;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA,EAKA,6BAA6B;AAC3B,QAAI,KAAK,qBAAqB,KAAK,YAAY,GAAG;AAChD,YAAM,kBAAkB,KAAK;AAC7B,UACE,wBAAwB,iBAAiB,GAAG,KAAK,QAAQ,KAAK,MAAM,GACpE;AACA,aAAK,2BAA2B;AAAA,MAClC,OAAO;AACL,aAAK,2BAA2B,gBAAgB,MAAM;AACtD,aAAK,yBAAyB,SAAS;AAAA,UACrC,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AACA,WAAK,oBAAoB,KAAK,YAAY;AAAA,IAC5C;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,8BAA8B,kBAAkB;AAC9C,UAAM,4BAA4B,CAAC;AACnC,UAAM,kBAAkB,CAAC;AACzB,8BAA0B,SAAS;AAAA,MACjC,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,KAAK,gBAAgB;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,IAAI,cAAa,2BAA2B,MAAM,eAAe;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,OAAO;AAChB,QAAI,QAAQ,KAAK,KAAK,OAAO,UAAU,OAAO;AAC5C,aAAO;AAAA,IACT;AACA,QAAI;AACJ,QAAI,UAAU,GAAG;AACf,eAAS;AAAA,IACX,OAAO;AACL,YAAM,WAAW,KAAK,OAAO,QAAQ,CAAC;AACtC,eAAS,SAAS,SAAS,SAAS,CAAC;AAAA,IACvC;AACA,UAAM,OAAO,KAAK,OAAO,KAAK,EAAE,MAAM;AACtC,UAAM,MAAM,KAAK,KAAK,SAAS,CAAC;AAChC,QAAI,WAAW,GAAG;AAChB,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,aAAK,CAAC,KAAK;AAAA,MACb;AAAA,IACF;AACA,WAAO,IAAI;AAAA,MACT,KAAK,gBAAgB,MAAM,QAAQ,GAAG;AAAA,MACtC,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,UAAM,SAAS,KAAK;AACpB,UAAM,kBAAkB,KAAK;AAC7B,UAAM,QAAQ,KAAK;AACnB,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,YAAM,OAAO,MAAM,CAAC,EAAE,MAAM;AAC5B,YAAM,MAAM,KAAK,KAAK,SAAS,CAAC;AAChC,UAAI,WAAW,GAAG;AAChB,iBAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,eAAK,CAAC,KAAK;AAAA,QACb;AAAA,MACF;AACA,YAAM,UAAU,IAAI;AAAA,QAClB,gBAAgB,MAAM,QAAQ,GAAG;AAAA,QACjC;AAAA,QACA;AAAA,MACF;AACA,eAAS,KAAK,OAAO;AACrB,eAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,QAAQ;AACvB,WAAO;AAAA,MACL,KAAK,2BAA2B;AAAA,MAChC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,aAAa,QAAQ;AAClC,SAAK,UAAU,QAAQ,aAAa,CAAC;AACrC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AACA,UAAM,QAAQ;AAAA,MACZ,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,QAAI,MAAM,WAAW,GAAG;AACtB,WAAK,gBAAgB,SAAS;AAAA,IAChC,OAAO;AACL,YAAM,WAAW,MAAM,MAAM,SAAS,CAAC;AACvC,WAAK,gBAAgB,SACnB,SAAS,WAAW,IAAI,IAAI,SAAS,SAAS,SAAS,CAAC;AAAA,IAC5D;AACA,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,uBAAQ;;;ACncf,IAAM,qBAAN,MAAM,4BAA2B,iBAAS;AAAA;AAAA;AAAA;AAAA,EAIxC,YAAY,YAAY;AACtB,UAAM;AAMN,SAAK,cAAc,aAAa,aAAa;AAK7C,SAAK,oBAAoB,CAAC;AAE1B,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKA,4BAA4B;AAC1B,SAAK,kBAAkB,QAAQ,aAAa;AAC5C,SAAK,kBAAkB,SAAS;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B;AACxB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,aAAS,IAAI,GAAG,KAAK,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACzD,WAAK,kBAAkB;AAAA,QACrB,OAAO,KAAK,YAAY,CAAC,GAAG,kBAAU,QAAQ,KAAK,SAAS,IAAI;AAAA,MAClE;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,UAAM,qBAAqB,IAAI,oBAAmB,IAAI;AACtD,uBAAmB,cAAc,KAAK,WAAW;AACjD,uBAAmB,gBAAgB,IAAI;AACvC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,QAAI,qBAAqB,yBAAyB,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG;AACzE,aAAO;AAAA,IACT;AACA,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,2BAAqB,WAAW,CAAC,EAAE;AAAA,QACjC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,GAAG,GAAG;AACf,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,UAAI,WAAW,CAAC,EAAE,WAAW,GAAG,CAAC,GAAG;AAClC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,QAAQ;AACpB,wBAAoB,MAAM;AAC1B,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,MAAAC,QAAO,QAAQ,WAAW,CAAC,EAAE,UAAU,CAAC;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACd,WAAO,gBAAgB,KAAK,WAAW;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,8BAA8B;AAE5B,QAAI,kBAAkB,CAAC;AACvB,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,UAAI,WAAW,CAAC,EAAE,QAAQ,MAAM,KAAK,QAAQ,GAAG;AAC9C,0BAAkB,gBAAgB;AAAA;AAAA,UAE9B,WAAW,CAAC,EACZ,4BAA4B;AAAA,QAChC;AAAA,MACF,OAAO;AACL,wBAAgB,KAAK,WAAW,CAAC,CAAC;AAAA,MACpC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,kBAAkB;AACtC,QAAI,KAAK,+BAA+B,KAAK,YAAY,GAAG;AAC1D,WAAK,2CAA2C;AAChD,WAAK,6BAA6B,KAAK,YAAY;AAAA,IACrD;AACA,QACE,mBAAmB,KAClB,KAAK,6CAA6C,KACjD,mBAAmB,KAAK,0CAC1B;AACA,aAAO;AAAA,IACT;AAEA,UAAM,uBAAuB,CAAC;AAC9B,UAAM,aAAa,KAAK;AACxB,QAAI,aAAa;AACjB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,YAAM,WAAW,WAAW,CAAC;AAC7B,YAAM,qBACJ,SAAS,sBAAsB,gBAAgB;AACjD,2BAAqB,KAAK,kBAAkB;AAC5C,UAAI,uBAAuB,UAAU;AACnC,qBAAa;AAAA,MACf;AAAA,IACF;AACA,QAAI,YAAY;AACd,YAAM,+BAA+B,IAAI,oBAAmB,IAAI;AAChE,mCAA6B,mBAAmB,oBAAoB;AACpE,aAAO;AAAA,IACT;AACA,SAAK,2CAA2C;AAChD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,QAAQ;AACvB,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,UAAI,WAAW,CAAC,EAAE,iBAAiB,MAAM,GAAG;AAC1C,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO,KAAK,YAAY,WAAW;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,OAAO,QAAQ;AACpB,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,iBAAW,CAAC,EAAE,OAAO,OAAO,MAAM;AAAA,IACpC;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,IAAI,IAAI,QAAQ;AACpB,QAAI,CAAC,QAAQ;AACX,eAAS,UAAU,KAAK,UAAU,CAAC;AAAA,IACrC;AACA,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,iBAAW,CAAC,EAAE,MAAM,IAAI,IAAI,MAAM;AAAA,IACpC;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,YAAY;AACxB,SAAK,mBAAmB,gBAAgB,UAAU,CAAC;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,YAAY;AAC7B,SAAK,0BAA0B;AAC/B,SAAK,cAAc;AACnB,SAAK,wBAAwB;AAC7B,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,eAAe,aAAa;AAC1B,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,iBAAW,CAAC,EAAE,eAAe,WAAW;AAAA,IAC1C;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,QAAQ,QAAQ;AACxB,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,iBAAW,CAAC,EAAE,UAAU,QAAQ,MAAM;AAAA,IACxC;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,SAAK,0BAA0B;AAC/B,UAAM,gBAAgB;AAAA,EACxB;AACF;AAMA,SAAS,gBAAgB,YAAY;AACnC,QAAM,mBAAmB,CAAC;AAC1B,WAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,qBAAiB,KAAK,WAAW,CAAC,EAAE,MAAM,CAAC;AAAA,EAC7C;AACA,SAAO;AACT;AAEA,IAAO,6BAAQ;", "names": ["layout", "ends", "squaredDistance", "linearRingss", "linearRingss", "extend"]}