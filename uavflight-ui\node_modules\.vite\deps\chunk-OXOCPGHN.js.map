{"version": 3, "sources": ["../../ol/util.js", "../../ol/ObjectEventType.js", "../../ol/Object.js"], "sourcesContent": ["/**\n * @module ol/util\n */\n\n/**\n * @return {never} Any return.\n */\nexport function abstract() {\n  throw new Error('Unimplemented abstract method.');\n}\n\n/**\n * Counter for getUid.\n * @type {number}\n * @private\n */\nlet uidCounter_ = 0;\n\n/**\n * Gets a unique ID for an object. This mutates the object so that further calls\n * with the same object as a parameter returns the same value. Unique IDs are generated\n * as a strictly increasing sequence. Adapted from goog.getUid.\n *\n * @param {Object} obj The object to get the unique ID for.\n * @return {string} The unique ID for the object.\n * @api\n */\nexport function getUid(obj) {\n  return obj.ol_uid || (obj.ol_uid = String(++uidCounter_));\n}\n\n/**\n * OpenLayers version.\n * @type {string}\n */\nexport const VERSION = '7.5.1';\n", "/**\n * @module ol/ObjectEventType\n */\n\n/**\n * @enum {string}\n */\nexport default {\n  /**\n   * Triggered when a property is changed.\n   * @event module:ol/Object.ObjectEvent#propertychange\n   * @api\n   */\n  PROPERTYCHANGE: 'propertychange',\n};\n\n/**\n * @typedef {'propertychange'} Types\n */\n", "/**\n * @module ol/Object\n */\nimport Event from './events/Event.js';\nimport ObjectEventType from './ObjectEventType.js';\nimport Observable from './Observable.js';\nimport {getUid} from './util.js';\nimport {isEmpty} from './obj.js';\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/Object~BaseObject} instances are instances of this type.\n */\nexport class ObjectEvent extends Event {\n  /**\n   * @param {string} type The event type.\n   * @param {string} key The property name.\n   * @param {*} oldValue The old value for `key`.\n   */\n  constructor(type, key, oldValue) {\n    super(type);\n\n    /**\n     * The name of the property whose value is changing.\n     * @type {string}\n     * @api\n     */\n    this.key = key;\n\n    /**\n     * The old value. To get the new value use `e.target.get(e.key)` where\n     * `e` is the event object.\n     * @type {*}\n     * @api\n     */\n    this.oldValue = oldValue;\n  }\n}\n\n/***\n * @template Return\n * @typedef {import(\"./Observable\").OnSignature<import(\"./Observable\").EventTypes, import(\"./events/Event.js\").default, Return> &\n *    import(\"./Observable\").OnSignature<import(\"./ObjectEventType\").Types, ObjectEvent, Return> &\n *    import(\"./Observable\").CombinedOnSignature<import(\"./Observable\").EventTypes|import(\"./ObjectEventType\").Types, Return>} ObjectOnSignature\n */\n\n/**\n * @classdesc\n * Abstract base class; normally only used for creating subclasses and not\n * instantiated in apps.\n * Most non-trivial classes inherit from this.\n *\n * This extends {@link module:ol/Observable~Observable} with observable\n * properties, where each property is observable as well as the object as a\n * whole.\n *\n * Classes that inherit from this have pre-defined properties, to which you can\n * add your owns. The pre-defined properties are listed in this documentation as\n * 'Observable Properties', and have their own accessors; for example,\n * {@link module:ol/Map~Map} has a `target` property, accessed with\n * `getTarget()` and changed with `setTarget()`. Not all properties are however\n * settable. There are also general-purpose accessors `get()` and `set()`. For\n * example, `get('target')` is equivalent to `getTarget()`.\n *\n * The `set` accessors trigger a change event, and you can monitor this by\n * registering a listener. For example, {@link module:ol/View~View} has a\n * `center` property, so `view.on('change:center', function(evt) {...});` would\n * call the function whenever the value of the center property changes. Within\n * the function, `evt.target` would be the view, so `evt.target.getCenter()`\n * would return the new center.\n *\n * You can add your own observable properties with\n * `object.set('prop', 'value')`, and retrieve that with `object.get('prop')`.\n * You can listen for changes on that property value with\n * `object.on('change:prop', listener)`. You can get a list of all\n * properties with {@link module:ol/Object~BaseObject#getProperties}.\n *\n * Note that the observable properties are separate from standard JS properties.\n * You can, for example, give your map object a title with\n * `map.title='New title'` and with `map.set('title', 'Another title')`. The\n * first will be a `hasOwnProperty`; the second will appear in\n * `getProperties()`. Only the second is observable.\n *\n * Properties can be deleted by using the unset method. E.g.\n * object.unset('foo').\n *\n * @fires ObjectEvent\n * @api\n */\nclass BaseObject extends Observable {\n  /**\n   * @param {Object<string, *>} [values] An object with key-value pairs.\n   */\n  constructor(values) {\n    super();\n\n    /***\n     * @type {ObjectOnSignature<import(\"./events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {ObjectOnSignature<import(\"./events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {ObjectOnSignature<void>}\n     */\n    this.un;\n\n    // Call {@link module:ol/util.getUid} to ensure that the order of objects' ids is\n    // the same as the order in which they were created.  This also helps to\n    // ensure that object properties are always added in the same order, which\n    // helps many JavaScript engines generate faster code.\n    getUid(this);\n\n    /**\n     * @private\n     * @type {Object<string, *>}\n     */\n    this.values_ = null;\n\n    if (values !== undefined) {\n      this.setProperties(values);\n    }\n  }\n\n  /**\n   * Gets a value.\n   * @param {string} key Key name.\n   * @return {*} Value.\n   * @api\n   */\n  get(key) {\n    let value;\n    if (this.values_ && this.values_.hasOwnProperty(key)) {\n      value = this.values_[key];\n    }\n    return value;\n  }\n\n  /**\n   * Get a list of object property names.\n   * @return {Array<string>} List of property names.\n   * @api\n   */\n  getKeys() {\n    return (this.values_ && Object.keys(this.values_)) || [];\n  }\n\n  /**\n   * Get an object of all property names and values.\n   * @return {Object<string, *>} Object.\n   * @api\n   */\n  getProperties() {\n    return (this.values_ && Object.assign({}, this.values_)) || {};\n  }\n\n  /**\n   * @return {boolean} The object has properties.\n   */\n  hasProperties() {\n    return !!this.values_;\n  }\n\n  /**\n   * @param {string} key Key name.\n   * @param {*} oldValue Old value.\n   */\n  notify(key, oldValue) {\n    let eventType;\n    eventType = `change:${key}`;\n    if (this.hasListener(eventType)) {\n      this.dispatchEvent(new ObjectEvent(eventType, key, oldValue));\n    }\n    eventType = ObjectEventType.PROPERTYCHANGE;\n    if (this.hasListener(eventType)) {\n      this.dispatchEvent(new ObjectEvent(eventType, key, oldValue));\n    }\n  }\n\n  /**\n   * @param {string} key Key name.\n   * @param {import(\"./events.js\").Listener} listener Listener.\n   */\n  addChangeListener(key, listener) {\n    this.addEventListener(`change:${key}`, listener);\n  }\n\n  /**\n   * @param {string} key Key name.\n   * @param {import(\"./events.js\").Listener} listener Listener.\n   */\n  removeChangeListener(key, listener) {\n    this.removeEventListener(`change:${key}`, listener);\n  }\n\n  /**\n   * Sets a value.\n   * @param {string} key Key name.\n   * @param {*} value Value.\n   * @param {boolean} [silent] Update without triggering an event.\n   * @api\n   */\n  set(key, value, silent) {\n    const values = this.values_ || (this.values_ = {});\n    if (silent) {\n      values[key] = value;\n    } else {\n      const oldValue = values[key];\n      values[key] = value;\n      if (oldValue !== value) {\n        this.notify(key, oldValue);\n      }\n    }\n  }\n\n  /**\n   * Sets a collection of key-value pairs.  Note that this changes any existing\n   * properties and adds new ones (it does not remove any existing properties).\n   * @param {Object<string, *>} values Values.\n   * @param {boolean} [silent] Update without triggering an event.\n   * @api\n   */\n  setProperties(values, silent) {\n    for (const key in values) {\n      this.set(key, values[key], silent);\n    }\n  }\n\n  /**\n   * Apply any properties from another object without triggering events.\n   * @param {BaseObject} source The source object.\n   * @protected\n   */\n  applyProperties(source) {\n    if (!source.values_) {\n      return;\n    }\n    Object.assign(this.values_ || (this.values_ = {}), source.values_);\n  }\n\n  /**\n   * Unsets a property.\n   * @param {string} key Key name.\n   * @param {boolean} [silent] Unset without triggering an event.\n   * @api\n   */\n  unset(key, silent) {\n    if (this.values_ && key in this.values_) {\n      const oldValue = this.values_[key];\n      delete this.values_[key];\n      if (isEmpty(this.values_)) {\n        this.values_ = null;\n      }\n      if (!silent) {\n        this.notify(key, oldValue);\n      }\n    }\n  }\n}\n\nexport default BaseObject;\n"], "mappings": ";;;;;;;;;AAOO,SAAS,WAAW;AACzB,QAAM,IAAI,MAAM,gCAAgC;AAClD;AAOA,IAAI,cAAc;AAWX,SAAS,OAAO,KAAK;AAC1B,SAAO,IAAI,WAAW,IAAI,SAAS,OAAO,EAAE,WAAW;AACzD;;;ACtBA,IAAO,0BAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,gBAAgB;AAClB;;;ACDO,IAAM,cAAN,cAA0B,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrC,YAAY,MAAM,KAAK,UAAU;AAC/B,UAAM,IAAI;AAOV,SAAK,MAAM;AAQX,SAAK,WAAW;AAAA,EAClB;AACF;AAoDA,IAAM,aAAN,cAAyB,mBAAW;AAAA;AAAA;AAAA;AAAA,EAIlC,YAAY,QAAQ;AAClB,UAAM;AAKN,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,WAAO,IAAI;AAMX,SAAK,UAAU;AAEf,QAAI,WAAW,QAAW;AACxB,WAAK,cAAc,MAAM;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,KAAK;AACP,QAAI;AACJ,QAAI,KAAK,WAAW,KAAK,QAAQ,eAAe,GAAG,GAAG;AACpD,cAAQ,KAAK,QAAQ,GAAG;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAQ,KAAK,WAAW,OAAO,KAAK,KAAK,OAAO,KAAM,CAAC;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACd,WAAQ,KAAK,WAAW,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO,KAAM,CAAC;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,KAAK,UAAU;AACpB,QAAI;AACJ,gBAAY,UAAU,GAAG;AACzB,QAAI,KAAK,YAAY,SAAS,GAAG;AAC/B,WAAK,cAAc,IAAI,YAAY,WAAW,KAAK,QAAQ,CAAC;AAAA,IAC9D;AACA,gBAAY,wBAAgB;AAC5B,QAAI,KAAK,YAAY,SAAS,GAAG;AAC/B,WAAK,cAAc,IAAI,YAAY,WAAW,KAAK,QAAQ,CAAC;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,KAAK,UAAU;AAC/B,SAAK,iBAAiB,UAAU,GAAG,IAAI,QAAQ;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,KAAK,UAAU;AAClC,SAAK,oBAAoB,UAAU,GAAG,IAAI,QAAQ;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,KAAK,OAAO,QAAQ;AACtB,UAAM,SAAS,KAAK,YAAY,KAAK,UAAU,CAAC;AAChD,QAAI,QAAQ;AACV,aAAO,GAAG,IAAI;AAAA,IAChB,OAAO;AACL,YAAM,WAAW,OAAO,GAAG;AAC3B,aAAO,GAAG,IAAI;AACd,UAAI,aAAa,OAAO;AACtB,aAAK,OAAO,KAAK,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,QAAQ,QAAQ;AAC5B,eAAW,OAAO,QAAQ;AACxB,WAAK,IAAI,KAAK,OAAO,GAAG,GAAG,MAAM;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,QAAQ;AACtB,QAAI,CAAC,OAAO,SAAS;AACnB;AAAA,IACF;AACA,WAAO,OAAO,KAAK,YAAY,KAAK,UAAU,CAAC,IAAI,OAAO,OAAO;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,KAAK,QAAQ;AACjB,QAAI,KAAK,WAAW,OAAO,KAAK,SAAS;AACvC,YAAM,WAAW,KAAK,QAAQ,GAAG;AACjC,aAAO,KAAK,QAAQ,GAAG;AACvB,UAAI,QAAQ,KAAK,OAAO,GAAG;AACzB,aAAK,UAAU;AAAA,MACjB;AACA,UAAI,CAAC,QAAQ;AACX,aAAK,OAAO,KAAK,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,iBAAQ;", "names": []}