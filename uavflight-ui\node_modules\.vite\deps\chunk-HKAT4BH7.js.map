{"version": 3, "sources": ["../../ol/format/IIIFInfo.js"], "sourcesContent": ["/**\n * @module ol/format/IIIFInfo\n */\n\nimport {assert} from '../asserts.js';\n\n/**\n * @typedef {Object} PreferredOptions\n * @property {string} [format] Preferred image format. Will be used if the image information\n * indicates support for that format.\n * @property {string} [quality] IIIF image qualitiy.  Will be used if the image information\n * indicates support for that quality.\n */\n\n/**\n * @typedef {Object} SupportedFeatures\n * @property {Array<string>} [supports] Supported IIIF image size and region\n * calculation features.\n * @property {Array<string>} [formats] Supported image formats.\n * @property {Array<string>} [qualities] Supported IIIF image qualities.\n */\n\n/**\n * @typedef {Object} TileInfo\n * @property {Array<number>} scaleFactors Supported resolution scaling factors.\n * @property {number} width Tile width in pixels.\n * @property {number} [height] Tile height in pixels. Same as tile width if height is\n * not given.\n */\n\n/**\n * @typedef {Object} IiifProfile\n * @property {Array<string>} [formats] Supported image formats for the image service.\n * @property {Array<string>} [qualities] Supported IIIF image qualities.\n * @property {Array<string>} [supports] Supported features.\n * @property {number} [maxArea] Maximum area (pixels) available for this image service.\n * @property {number} [maxHeight] Maximum height.\n * @property {number} [maxWidth] Maximum width.\n */\n\n/**\n * @typedef {Object<string,string|number|Array<number|string|IiifProfile|Object<string, number>|TileInfo>>}\n *    ImageInformationResponse\n */\n\n/**\n * Enum representing the major IIIF Image API versions\n * @enum {string}\n */\nexport const Versions = {\n  VERSION1: 'version1',\n  VERSION2: 'version2',\n  VERSION3: 'version3',\n};\n\n/**\n * Supported image formats, qualities and supported region / size calculation features\n * for different image API versions and compliance levels\n * @const\n * @type {Object<string, Object<string, SupportedFeatures>>}\n */\nconst IIIF_PROFILE_VALUES = {};\nIIIF_PROFILE_VALUES[Versions.VERSION1] = {\n  'level0': {\n    supports: [],\n    formats: [],\n    qualities: ['native'],\n  },\n  'level1': {\n    supports: ['regionByPx', 'sizeByW', 'sizeByH', 'sizeByPct'],\n    formats: ['jpg'],\n    qualities: ['native'],\n  },\n  'level2': {\n    supports: [\n      'regionByPx',\n      'regionByPct',\n      'sizeByW',\n      'sizeByH',\n      'sizeByPct',\n      'sizeByConfinedWh',\n      'sizeByWh',\n    ],\n    formats: ['jpg', 'png'],\n    qualities: ['native', 'color', 'grey', 'bitonal'],\n  },\n};\nIIIF_PROFILE_VALUES[Versions.VERSION2] = {\n  'level0': {\n    supports: [],\n    formats: ['jpg'],\n    qualities: ['default'],\n  },\n  'level1': {\n    supports: ['regionByPx', 'sizeByW', 'sizeByH', 'sizeByPct'],\n    formats: ['jpg'],\n    qualities: ['default'],\n  },\n  'level2': {\n    supports: [\n      'regionByPx',\n      'regionByPct',\n      'sizeByW',\n      'sizeByH',\n      'sizeByPct',\n      'sizeByConfinedWh',\n      'sizeByDistortedWh',\n      'sizeByWh',\n    ],\n    formats: ['jpg', 'png'],\n    qualities: ['default', 'bitonal'],\n  },\n};\nIIIF_PROFILE_VALUES[Versions.VERSION3] = {\n  'level0': {\n    supports: [],\n    formats: ['jpg'],\n    qualities: ['default'],\n  },\n  'level1': {\n    supports: ['regionByPx', 'regionSquare', 'sizeByW', 'sizeByH', 'sizeByWh'],\n    formats: ['jpg'],\n    qualities: ['default'],\n  },\n  'level2': {\n    supports: [\n      'regionByPx',\n      'regionSquare',\n      'regionByPct',\n      'sizeByW',\n      'sizeByH',\n      'sizeByPct',\n      'sizeByConfinedWh',\n      'sizeByWh',\n    ],\n    formats: ['jpg', 'png'],\n    qualities: ['default'],\n  },\n};\nIIIF_PROFILE_VALUES['none'] = {\n  'none': {\n    supports: [],\n    formats: [],\n    qualities: [],\n  },\n};\n\nconst COMPLIANCE_VERSION1 =\n  /^https?:\\/\\/library\\.stanford\\.edu\\/iiif\\/image-api\\/(?:1\\.1\\/)?compliance\\.html#level[0-2]$/;\nconst COMPLIANCE_VERSION2 =\n  /^https?:\\/\\/iiif\\.io\\/api\\/image\\/2\\/level[0-2](?:\\.json)?$/;\nconst COMPLIANCE_VERSION3 =\n  /(^https?:\\/\\/iiif\\.io\\/api\\/image\\/3\\/level[0-2](?:\\.json)?$)|(^level[0-2]$)/;\n\nfunction generateVersion1Options(iiifInfo) {\n  let levelProfile = iiifInfo.getComplianceLevelSupportedFeatures();\n  // Version 1.0 and 1.1 do not require a profile.\n  if (levelProfile === undefined) {\n    levelProfile = IIIF_PROFILE_VALUES[Versions.VERSION1]['level0'];\n  }\n  return {\n    url:\n      iiifInfo.imageInfo['@id'] === undefined\n        ? undefined\n        : iiifInfo.imageInfo['@id'].replace(/\\/?(?:info\\.json)?$/g, ''),\n    supports: levelProfile.supports,\n    formats: [\n      ...levelProfile.formats,\n      iiifInfo.imageInfo.formats === undefined\n        ? []\n        : iiifInfo.imageInfo.formats,\n    ],\n    qualities: [\n      ...levelProfile.qualities,\n      iiifInfo.imageInfo.qualities === undefined\n        ? []\n        : iiifInfo.imageInfo.qualities,\n    ],\n    resolutions: iiifInfo.imageInfo.scale_factors,\n    tileSize:\n      iiifInfo.imageInfo.tile_width !== undefined\n        ? iiifInfo.imageInfo.tile_height !== undefined\n          ? [iiifInfo.imageInfo.tile_width, iiifInfo.imageInfo.tile_height]\n          : [iiifInfo.imageInfo.tile_width, iiifInfo.imageInfo.tile_width]\n        : iiifInfo.imageInfo.tile_height != undefined\n        ? [iiifInfo.imageInfo.tile_height, iiifInfo.imageInfo.tile_height]\n        : undefined,\n  };\n}\n\nfunction generateVersion2Options(iiifInfo) {\n  const levelProfile = iiifInfo.getComplianceLevelSupportedFeatures(),\n    additionalProfile =\n      Array.isArray(iiifInfo.imageInfo.profile) &&\n      iiifInfo.imageInfo.profile.length > 1,\n    profileSupports =\n      additionalProfile && iiifInfo.imageInfo.profile[1].supports\n        ? iiifInfo.imageInfo.profile[1].supports\n        : [],\n    profileFormats =\n      additionalProfile && iiifInfo.imageInfo.profile[1].formats\n        ? iiifInfo.imageInfo.profile[1].formats\n        : [],\n    profileQualities =\n      additionalProfile && iiifInfo.imageInfo.profile[1].qualities\n        ? iiifInfo.imageInfo.profile[1].qualities\n        : [];\n  return {\n    url: iiifInfo.imageInfo['@id'].replace(/\\/?(?:info\\.json)?$/g, ''),\n    sizes:\n      iiifInfo.imageInfo.sizes === undefined\n        ? undefined\n        : iiifInfo.imageInfo.sizes.map(function (size) {\n            return [size.width, size.height];\n          }),\n    tileSize:\n      iiifInfo.imageInfo.tiles === undefined\n        ? undefined\n        : [\n            iiifInfo.imageInfo.tiles.map(function (tile) {\n              return tile.width;\n            })[0],\n            iiifInfo.imageInfo.tiles.map(function (tile) {\n              return tile.height === undefined ? tile.width : tile.height;\n            })[0],\n          ],\n    resolutions:\n      iiifInfo.imageInfo.tiles === undefined\n        ? undefined\n        : iiifInfo.imageInfo.tiles.map(function (tile) {\n            return tile.scaleFactors;\n          })[0],\n    supports: [...levelProfile.supports, ...profileSupports],\n    formats: [...levelProfile.formats, ...profileFormats],\n    qualities: [...levelProfile.qualities, ...profileQualities],\n  };\n}\n\nfunction generateVersion3Options(iiifInfo) {\n  const levelProfile = iiifInfo.getComplianceLevelSupportedFeatures(),\n    formats =\n      iiifInfo.imageInfo.extraFormats === undefined\n        ? levelProfile.formats\n        : [...levelProfile.formats, ...iiifInfo.imageInfo.extraFormats],\n    preferredFormat =\n      iiifInfo.imageInfo.preferredFormats !== undefined &&\n      Array.isArray(iiifInfo.imageInfo.preferredFormats) &&\n      iiifInfo.imageInfo.preferredFormats.length > 0\n        ? iiifInfo.imageInfo.preferredFormats\n            .filter(function (format) {\n              return ['jpg', 'png', 'gif'].includes(format);\n            })\n            .reduce(function (acc, format) {\n              return acc === undefined && formats.includes(format)\n                ? format\n                : acc;\n            }, undefined)\n        : undefined;\n  return {\n    url: iiifInfo.imageInfo['id'],\n    sizes:\n      iiifInfo.imageInfo.sizes === undefined\n        ? undefined\n        : iiifInfo.imageInfo.sizes.map(function (size) {\n            return [size.width, size.height];\n          }),\n    tileSize:\n      iiifInfo.imageInfo.tiles === undefined\n        ? undefined\n        : [\n            iiifInfo.imageInfo.tiles.map(function (tile) {\n              return tile.width;\n            })[0],\n            iiifInfo.imageInfo.tiles.map(function (tile) {\n              return tile.height;\n            })[0],\n          ],\n    resolutions:\n      iiifInfo.imageInfo.tiles === undefined\n        ? undefined\n        : iiifInfo.imageInfo.tiles.map(function (tile) {\n            return tile.scaleFactors;\n          })[0],\n    supports:\n      iiifInfo.imageInfo.extraFeatures === undefined\n        ? levelProfile.supports\n        : [...levelProfile.supports, ...iiifInfo.imageInfo.extraFeatures],\n    formats: formats,\n    qualities:\n      iiifInfo.imageInfo.extraQualities === undefined\n        ? levelProfile.qualities\n        : [...levelProfile.qualities, ...iiifInfo.imageInfo.extraQualities],\n    preferredFormat: preferredFormat,\n  };\n}\n\nconst versionFunctions = {};\nversionFunctions[Versions.VERSION1] = generateVersion1Options;\nversionFunctions[Versions.VERSION2] = generateVersion2Options;\nversionFunctions[Versions.VERSION3] = generateVersion3Options;\n\n/**\n * @classdesc\n * Format for transforming IIIF Image API image information responses into\n * IIIF tile source ready options\n *\n * @api\n */\nclass IIIFInfo {\n  /**\n   * @param {string|ImageInformationResponse} imageInfo\n   * Deserialized image information JSON response object or JSON response as string\n   */\n  constructor(imageInfo) {\n    this.setImageInfo(imageInfo);\n  }\n\n  /**\n   * @param {string|ImageInformationResponse} imageInfo\n   * Deserialized image information JSON response object or JSON response as string\n   * @api\n   */\n  setImageInfo(imageInfo) {\n    if (typeof imageInfo == 'string') {\n      this.imageInfo = JSON.parse(imageInfo);\n    } else {\n      this.imageInfo = imageInfo;\n    }\n  }\n\n  /**\n   * @return {Versions|undefined} Major IIIF version.\n   * @api\n   */\n  getImageApiVersion() {\n    if (this.imageInfo === undefined) {\n      return undefined;\n    }\n    let context = this.imageInfo['@context'] || 'ol-no-context';\n    if (typeof context == 'string') {\n      context = [context];\n    }\n    for (let i = 0; i < context.length; i++) {\n      switch (context[i]) {\n        case 'http://library.stanford.edu/iiif/image-api/1.1/context.json':\n        case 'http://iiif.io/api/image/1/context.json':\n          return Versions.VERSION1;\n        case 'http://iiif.io/api/image/2/context.json':\n          return Versions.VERSION2;\n        case 'http://iiif.io/api/image/3/context.json':\n          return Versions.VERSION3;\n        case 'ol-no-context':\n          // Image API 1.0 has no '@context'\n          if (\n            this.getComplianceLevelEntryFromProfile(Versions.VERSION1) &&\n            this.imageInfo.identifier\n          ) {\n            return Versions.VERSION1;\n          }\n          break;\n        default:\n      }\n    }\n    assert(false, 61);\n  }\n\n  /**\n   * @param {Versions} version Optional IIIF image API version\n   * @return {string|undefined} Compliance level as it appears in the IIIF image information\n   * response.\n   */\n  getComplianceLevelEntryFromProfile(version) {\n    if (this.imageInfo === undefined || this.imageInfo.profile === undefined) {\n      return undefined;\n    }\n    if (version === undefined) {\n      version = this.getImageApiVersion();\n    }\n    switch (version) {\n      case Versions.VERSION1:\n        if (COMPLIANCE_VERSION1.test(this.imageInfo.profile)) {\n          return this.imageInfo.profile;\n        }\n        break;\n      case Versions.VERSION3:\n        if (COMPLIANCE_VERSION3.test(this.imageInfo.profile)) {\n          return this.imageInfo.profile;\n        }\n        break;\n      case Versions.VERSION2:\n        if (\n          typeof this.imageInfo.profile === 'string' &&\n          COMPLIANCE_VERSION2.test(this.imageInfo.profile)\n        ) {\n          return this.imageInfo.profile;\n        }\n        if (\n          Array.isArray(this.imageInfo.profile) &&\n          this.imageInfo.profile.length > 0 &&\n          typeof this.imageInfo.profile[0] === 'string' &&\n          COMPLIANCE_VERSION2.test(this.imageInfo.profile[0])\n        ) {\n          return this.imageInfo.profile[0];\n        }\n        break;\n      default:\n    }\n    return undefined;\n  }\n\n  /**\n   * @param {Versions} version Optional IIIF image API version\n   * @return {string} Compliance level, on of 'level0', 'level1' or 'level2' or undefined\n   */\n  getComplianceLevelFromProfile(version) {\n    const complianceLevel = this.getComplianceLevelEntryFromProfile(version);\n    if (complianceLevel === undefined) {\n      return undefined;\n    }\n    const level = complianceLevel.match(/level[0-2](?:\\.json)?$/g);\n    return Array.isArray(level) ? level[0].replace('.json', '') : undefined;\n  }\n\n  /**\n   * @return {SupportedFeatures|undefined} Image formats, qualities and region / size calculation\n   * methods that are supported by the IIIF service.\n   */\n  getComplianceLevelSupportedFeatures() {\n    if (this.imageInfo === undefined) {\n      return undefined;\n    }\n    const version = this.getImageApiVersion();\n    const level = this.getComplianceLevelFromProfile(version);\n    if (level === undefined) {\n      return IIIF_PROFILE_VALUES['none']['none'];\n    }\n    return IIIF_PROFILE_VALUES[version][level];\n  }\n\n  /**\n   * @param {PreferredOptions} [preferredOptions] Optional options for preferred format and quality.\n   * @return {import(\"../source/IIIF.js\").Options|undefined} IIIF tile source ready constructor options.\n   * @api\n   */\n  getTileSourceOptions(preferredOptions) {\n    const options = preferredOptions || {},\n      version = this.getImageApiVersion();\n    if (version === undefined) {\n      return undefined;\n    }\n    const imageOptions =\n      version === undefined ? undefined : versionFunctions[version](this);\n    if (imageOptions === undefined) {\n      return undefined;\n    }\n    return {\n      url: imageOptions.url,\n      version: version,\n      size: [this.imageInfo.width, this.imageInfo.height],\n      sizes: imageOptions.sizes,\n      format:\n        options.format !== undefined &&\n        imageOptions.formats.includes(options.format)\n          ? options.format\n          : imageOptions.preferredFormat !== undefined\n          ? imageOptions.preferredFormat\n          : 'jpg',\n      supports: imageOptions.supports,\n      quality:\n        options.quality && imageOptions.qualities.includes(options.quality)\n          ? options.quality\n          : imageOptions.qualities.includes('native')\n          ? 'native'\n          : 'default',\n      resolutions: Array.isArray(imageOptions.resolutions)\n        ? imageOptions.resolutions.sort(function (a, b) {\n            return b - a;\n          })\n        : undefined,\n      tileSize: imageOptions.tileSize,\n    };\n  }\n}\n\nexport default IIIFInfo;\n"], "mappings": ";;;;;AAiDO,IAAM,WAAW;AAAA,EACtB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AACZ;AAQA,IAAM,sBAAsB,CAAC;AAC7B,oBAAoB,SAAS,QAAQ,IAAI;AAAA,EACvC,UAAU;AAAA,IACR,UAAU,CAAC;AAAA,IACX,SAAS,CAAC;AAAA,IACV,WAAW,CAAC,QAAQ;AAAA,EACtB;AAAA,EACA,UAAU;AAAA,IACR,UAAU,CAAC,cAAc,WAAW,WAAW,WAAW;AAAA,IAC1D,SAAS,CAAC,KAAK;AAAA,IACf,WAAW,CAAC,QAAQ;AAAA,EACtB;AAAA,EACA,UAAU;AAAA,IACR,UAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,SAAS,CAAC,OAAO,KAAK;AAAA,IACtB,WAAW,CAAC,UAAU,SAAS,QAAQ,SAAS;AAAA,EAClD;AACF;AACA,oBAAoB,SAAS,QAAQ,IAAI;AAAA,EACvC,UAAU;AAAA,IACR,UAAU,CAAC;AAAA,IACX,SAAS,CAAC,KAAK;AAAA,IACf,WAAW,CAAC,SAAS;AAAA,EACvB;AAAA,EACA,UAAU;AAAA,IACR,UAAU,CAAC,cAAc,WAAW,WAAW,WAAW;AAAA,IAC1D,SAAS,CAAC,KAAK;AAAA,IACf,WAAW,CAAC,SAAS;AAAA,EACvB;AAAA,EACA,UAAU;AAAA,IACR,UAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,SAAS,CAAC,OAAO,KAAK;AAAA,IACtB,WAAW,CAAC,WAAW,SAAS;AAAA,EAClC;AACF;AACA,oBAAoB,SAAS,QAAQ,IAAI;AAAA,EACvC,UAAU;AAAA,IACR,UAAU,CAAC;AAAA,IACX,SAAS,CAAC,KAAK;AAAA,IACf,WAAW,CAAC,SAAS;AAAA,EACvB;AAAA,EACA,UAAU;AAAA,IACR,UAAU,CAAC,cAAc,gBAAgB,WAAW,WAAW,UAAU;AAAA,IACzE,SAAS,CAAC,KAAK;AAAA,IACf,WAAW,CAAC,SAAS;AAAA,EACvB;AAAA,EACA,UAAU;AAAA,IACR,UAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,SAAS,CAAC,OAAO,KAAK;AAAA,IACtB,WAAW,CAAC,SAAS;AAAA,EACvB;AACF;AACA,oBAAoB,MAAM,IAAI;AAAA,EAC5B,QAAQ;AAAA,IACN,UAAU,CAAC;AAAA,IACX,SAAS,CAAC;AAAA,IACV,WAAW,CAAC;AAAA,EACd;AACF;AAEA,IAAM,sBACJ;AACF,IAAM,sBACJ;AACF,IAAM,sBACJ;AAEF,SAAS,wBAAwB,UAAU;AACzC,MAAI,eAAe,SAAS,oCAAoC;AAEhE,MAAI,iBAAiB,QAAW;AAC9B,mBAAe,oBAAoB,SAAS,QAAQ,EAAE,QAAQ;AAAA,EAChE;AACA,SAAO;AAAA,IACL,KACE,SAAS,UAAU,KAAK,MAAM,SAC1B,SACA,SAAS,UAAU,KAAK,EAAE,QAAQ,wBAAwB,EAAE;AAAA,IAClE,UAAU,aAAa;AAAA,IACvB,SAAS;AAAA,MACP,GAAG,aAAa;AAAA,MAChB,SAAS,UAAU,YAAY,SAC3B,CAAC,IACD,SAAS,UAAU;AAAA,IACzB;AAAA,IACA,WAAW;AAAA,MACT,GAAG,aAAa;AAAA,MAChB,SAAS,UAAU,cAAc,SAC7B,CAAC,IACD,SAAS,UAAU;AAAA,IACzB;AAAA,IACA,aAAa,SAAS,UAAU;AAAA,IAChC,UACE,SAAS,UAAU,eAAe,SAC9B,SAAS,UAAU,gBAAgB,SACjC,CAAC,SAAS,UAAU,YAAY,SAAS,UAAU,WAAW,IAC9D,CAAC,SAAS,UAAU,YAAY,SAAS,UAAU,UAAU,IAC/D,SAAS,UAAU,eAAe,SAClC,CAAC,SAAS,UAAU,aAAa,SAAS,UAAU,WAAW,IAC/D;AAAA,EACR;AACF;AAEA,SAAS,wBAAwB,UAAU;AACzC,QAAM,eAAe,SAAS,oCAAoC,GAChE,oBACE,MAAM,QAAQ,SAAS,UAAU,OAAO,KACxC,SAAS,UAAU,QAAQ,SAAS,GACtC,kBACE,qBAAqB,SAAS,UAAU,QAAQ,CAAC,EAAE,WAC/C,SAAS,UAAU,QAAQ,CAAC,EAAE,WAC9B,CAAC,GACP,iBACE,qBAAqB,SAAS,UAAU,QAAQ,CAAC,EAAE,UAC/C,SAAS,UAAU,QAAQ,CAAC,EAAE,UAC9B,CAAC,GACP,mBACE,qBAAqB,SAAS,UAAU,QAAQ,CAAC,EAAE,YAC/C,SAAS,UAAU,QAAQ,CAAC,EAAE,YAC9B,CAAC;AACT,SAAO;AAAA,IACL,KAAK,SAAS,UAAU,KAAK,EAAE,QAAQ,wBAAwB,EAAE;AAAA,IACjE,OACE,SAAS,UAAU,UAAU,SACzB,SACA,SAAS,UAAU,MAAM,IAAI,SAAU,MAAM;AAC3C,aAAO,CAAC,KAAK,OAAO,KAAK,MAAM;AAAA,IACjC,CAAC;AAAA,IACP,UACE,SAAS,UAAU,UAAU,SACzB,SACA;AAAA,MACE,SAAS,UAAU,MAAM,IAAI,SAAU,MAAM;AAC3C,eAAO,KAAK;AAAA,MACd,CAAC,EAAE,CAAC;AAAA,MACJ,SAAS,UAAU,MAAM,IAAI,SAAU,MAAM;AAC3C,eAAO,KAAK,WAAW,SAAY,KAAK,QAAQ,KAAK;AAAA,MACvD,CAAC,EAAE,CAAC;AAAA,IACN;AAAA,IACN,aACE,SAAS,UAAU,UAAU,SACzB,SACA,SAAS,UAAU,MAAM,IAAI,SAAU,MAAM;AAC3C,aAAO,KAAK;AAAA,IACd,CAAC,EAAE,CAAC;AAAA,IACV,UAAU,CAAC,GAAG,aAAa,UAAU,GAAG,eAAe;AAAA,IACvD,SAAS,CAAC,GAAG,aAAa,SAAS,GAAG,cAAc;AAAA,IACpD,WAAW,CAAC,GAAG,aAAa,WAAW,GAAG,gBAAgB;AAAA,EAC5D;AACF;AAEA,SAAS,wBAAwB,UAAU;AACzC,QAAM,eAAe,SAAS,oCAAoC,GAChE,UACE,SAAS,UAAU,iBAAiB,SAChC,aAAa,UACb,CAAC,GAAG,aAAa,SAAS,GAAG,SAAS,UAAU,YAAY,GAClE,kBACE,SAAS,UAAU,qBAAqB,UACxC,MAAM,QAAQ,SAAS,UAAU,gBAAgB,KACjD,SAAS,UAAU,iBAAiB,SAAS,IACzC,SAAS,UAAU,iBAChB,OAAO,SAAU,QAAQ;AACxB,WAAO,CAAC,OAAO,OAAO,KAAK,EAAE,SAAS,MAAM;AAAA,EAC9C,CAAC,EACA,OAAO,SAAU,KAAK,QAAQ;AAC7B,WAAO,QAAQ,UAAa,QAAQ,SAAS,MAAM,IAC/C,SACA;AAAA,EACN,GAAG,MAAS,IACd;AACR,SAAO;AAAA,IACL,KAAK,SAAS,UAAU,IAAI;AAAA,IAC5B,OACE,SAAS,UAAU,UAAU,SACzB,SACA,SAAS,UAAU,MAAM,IAAI,SAAU,MAAM;AAC3C,aAAO,CAAC,KAAK,OAAO,KAAK,MAAM;AAAA,IACjC,CAAC;AAAA,IACP,UACE,SAAS,UAAU,UAAU,SACzB,SACA;AAAA,MACE,SAAS,UAAU,MAAM,IAAI,SAAU,MAAM;AAC3C,eAAO,KAAK;AAAA,MACd,CAAC,EAAE,CAAC;AAAA,MACJ,SAAS,UAAU,MAAM,IAAI,SAAU,MAAM;AAC3C,eAAO,KAAK;AAAA,MACd,CAAC,EAAE,CAAC;AAAA,IACN;AAAA,IACN,aACE,SAAS,UAAU,UAAU,SACzB,SACA,SAAS,UAAU,MAAM,IAAI,SAAU,MAAM;AAC3C,aAAO,KAAK;AAAA,IACd,CAAC,EAAE,CAAC;AAAA,IACV,UACE,SAAS,UAAU,kBAAkB,SACjC,aAAa,WACb,CAAC,GAAG,aAAa,UAAU,GAAG,SAAS,UAAU,aAAa;AAAA,IACpE;AAAA,IACA,WACE,SAAS,UAAU,mBAAmB,SAClC,aAAa,YACb,CAAC,GAAG,aAAa,WAAW,GAAG,SAAS,UAAU,cAAc;AAAA,IACtE;AAAA,EACF;AACF;AAEA,IAAM,mBAAmB,CAAC;AAC1B,iBAAiB,SAAS,QAAQ,IAAI;AACtC,iBAAiB,SAAS,QAAQ,IAAI;AACtC,iBAAiB,SAAS,QAAQ,IAAI;AAStC,IAAM,WAAN,MAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,YAAY,WAAW;AACrB,SAAK,aAAa,SAAS;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,WAAW;AACtB,QAAI,OAAO,aAAa,UAAU;AAChC,WAAK,YAAY,KAAK,MAAM,SAAS;AAAA,IACvC,OAAO;AACL,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB;AACnB,QAAI,KAAK,cAAc,QAAW;AAChC,aAAO;AAAA,IACT;AACA,QAAI,UAAU,KAAK,UAAU,UAAU,KAAK;AAC5C,QAAI,OAAO,WAAW,UAAU;AAC9B,gBAAU,CAAC,OAAO;AAAA,IACpB;AACA,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAQ,QAAQ,CAAC,GAAG;AAAA,QAClB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,SAAS;AAAA,QAClB,KAAK;AACH,iBAAO,SAAS;AAAA,QAClB,KAAK;AACH,iBAAO,SAAS;AAAA,QAClB,KAAK;AAEH,cACE,KAAK,mCAAmC,SAAS,QAAQ,KACzD,KAAK,UAAU,YACf;AACA,mBAAO,SAAS;AAAA,UAClB;AACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO,OAAO,EAAE;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mCAAmC,SAAS;AAC1C,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,YAAY,QAAW;AACxE,aAAO;AAAA,IACT;AACA,QAAI,YAAY,QAAW;AACzB,gBAAU,KAAK,mBAAmB;AAAA,IACpC;AACA,YAAQ,SAAS;AAAA,MACf,KAAK,SAAS;AACZ,YAAI,oBAAoB,KAAK,KAAK,UAAU,OAAO,GAAG;AACpD,iBAAO,KAAK,UAAU;AAAA,QACxB;AACA;AAAA,MACF,KAAK,SAAS;AACZ,YAAI,oBAAoB,KAAK,KAAK,UAAU,OAAO,GAAG;AACpD,iBAAO,KAAK,UAAU;AAAA,QACxB;AACA;AAAA,MACF,KAAK,SAAS;AACZ,YACE,OAAO,KAAK,UAAU,YAAY,YAClC,oBAAoB,KAAK,KAAK,UAAU,OAAO,GAC/C;AACA,iBAAO,KAAK,UAAU;AAAA,QACxB;AACA,YACE,MAAM,QAAQ,KAAK,UAAU,OAAO,KACpC,KAAK,UAAU,QAAQ,SAAS,KAChC,OAAO,KAAK,UAAU,QAAQ,CAAC,MAAM,YACrC,oBAAoB,KAAK,KAAK,UAAU,QAAQ,CAAC,CAAC,GAClD;AACA,iBAAO,KAAK,UAAU,QAAQ,CAAC;AAAA,QACjC;AACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,8BAA8B,SAAS;AACrC,UAAM,kBAAkB,KAAK,mCAAmC,OAAO;AACvE,QAAI,oBAAoB,QAAW;AACjC,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,gBAAgB,MAAM,yBAAyB;AAC7D,WAAO,MAAM,QAAQ,KAAK,IAAI,MAAM,CAAC,EAAE,QAAQ,SAAS,EAAE,IAAI;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sCAAsC;AACpC,QAAI,KAAK,cAAc,QAAW;AAChC,aAAO;AAAA,IACT;AACA,UAAM,UAAU,KAAK,mBAAmB;AACxC,UAAM,QAAQ,KAAK,8BAA8B,OAAO;AACxD,QAAI,UAAU,QAAW;AACvB,aAAO,oBAAoB,MAAM,EAAE,MAAM;AAAA,IAC3C;AACA,WAAO,oBAAoB,OAAO,EAAE,KAAK;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,qBAAqB,kBAAkB;AACrC,UAAM,UAAU,oBAAoB,CAAC,GACnC,UAAU,KAAK,mBAAmB;AACpC,QAAI,YAAY,QAAW;AACzB,aAAO;AAAA,IACT;AACA,UAAM,eACJ,YAAY,SAAY,SAAY,iBAAiB,OAAO,EAAE,IAAI;AACpE,QAAI,iBAAiB,QAAW;AAC9B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,MACL,KAAK,aAAa;AAAA,MAClB;AAAA,MACA,MAAM,CAAC,KAAK,UAAU,OAAO,KAAK,UAAU,MAAM;AAAA,MAClD,OAAO,aAAa;AAAA,MACpB,QACE,QAAQ,WAAW,UACnB,aAAa,QAAQ,SAAS,QAAQ,MAAM,IACxC,QAAQ,SACR,aAAa,oBAAoB,SACjC,aAAa,kBACb;AAAA,MACN,UAAU,aAAa;AAAA,MACvB,SACE,QAAQ,WAAW,aAAa,UAAU,SAAS,QAAQ,OAAO,IAC9D,QAAQ,UACR,aAAa,UAAU,SAAS,QAAQ,IACxC,WACA;AAAA,MACN,aAAa,MAAM,QAAQ,aAAa,WAAW,IAC/C,aAAa,YAAY,KAAK,SAAU,GAAG,GAAG;AAC5C,eAAO,IAAI;AAAA,MACb,CAAC,IACD;AAAA,MACJ,UAAU,aAAa;AAAA,IACzB;AAAA,EACF;AACF;AAEA,IAAO,mBAAQ;", "names": []}