import * as Cesium from 'cesium';

/**
 * 地形高度采样器，负责地形高度的获取、缓存和平滑过渡
 */
export default class TerrainSampler {
  // 高度缓存系统
  private terrainHeightCache: Map<number, number> = new Map(); // 缓存帧索引到地形高度的映射
  private CACHE_SAMPLE_INTERVAL: number = 150; // 采样间隔（帧数），降低到20帧提高更新频率
  
  // 高度平滑过渡系统
  private currentTerrainHeight: number = 0; // 当前使用的地形高度
  private targetTerrainHeight: number = 0; // 目标地形高度
  private heightTransitionFrames: number = 20; // 高度过渡的帧数，降低到10帧加快过渡
  private heightTransitionCounter: number = 0; // 高度过渡计数器
  private heightTransitionStep: number = 0; // 每帧高度变化量
  
  // Cesium查看器
  private viewer: Cesium.Viewer | null = null;
  
  /**
   * 构造函数
   * @param viewer Cesium查看器实例
   */
  constructor(viewer?: Cesium.Viewer) {
    if (viewer) {
      this.viewer = viewer;
    }
  }
  
  /**
   * 设置Cesium查看器
   * @param viewer Cesium查看器实例
   */
  setViewer(viewer: Cesium.Viewer): void {
    this.viewer = viewer;
  }
  
  /**
   * 获取指定帧的地形高度，使用缓存机制
   * @param frameIdx 当前帧索引
   * @param longitude 经度
   * @param latitude 纬度
   * @returns 地形高度
   */
  async getTerrainHeightWithCache(frameIdx: number, longitude: number, latitude: number): Promise<number> {
    // 首先检查当前帧是否已缓存
    if (this.terrainHeightCache.has(frameIdx)) {
      return this.terrainHeightCache.get(frameIdx) || 0;
    }
    
    // 查找缓存中是否有接近的帧索引
    const nearestCachedIdx = this.findNearestCachedFrame(frameIdx);
    
    if (nearestCachedIdx !== null && Math.abs(nearestCachedIdx - frameIdx) < this.CACHE_SAMPLE_INTERVAL) {
      // 使用最近的缓存帧的高度
      const cachedHeight = this.terrainHeightCache.get(nearestCachedIdx) || 0;
      return cachedHeight;
    }
    
    // 没有缓存或缓存太旧，重新获取地形高度
    const height = await this.fetchTerrainHeight(longitude, latitude);
    
    // 更新缓存
    this.terrainHeightCache.set(frameIdx, height);
    
    return height;
  }
  
  /**
   * 查找最接近当前帧的已缓存帧
   * @param frameIdx 当前帧索引
   * @returns 最接近的缓存帧索引，如果没有找到返回null
   */
  private findNearestCachedFrame(frameIdx: number): number | null {
    if (this.terrainHeightCache.size === 0) return null;
    
    let nearestIdx: number | null = null;
    let minDistance = Infinity;
    
    // 遍历所有缓存帧，找出距离当前帧最近的
    for (const cachedIdx of this.terrainHeightCache.keys()) {
      const distance = Math.abs(cachedIdx - frameIdx);
      if (distance < minDistance) {
        minDistance = distance;
        nearestIdx = cachedIdx;
      }
    }
    
    return nearestIdx;
  }
  
  /**
   * 从地形提供者获取高度
   * @param longitude 经度
   * @param latitude 纬度
   * @returns 地形高度
   */
  private fetchTerrainHeight(longitude: number, latitude: number): Promise<number> {
    return new Promise((resolve) => {
      if (!this.viewer || !this.viewer.terrainProvider) {
        resolve(0);
        return;
      }
      
      // 创建采样位置数组
      const positions = [Cesium.Cartographic.fromDegrees(longitude, latitude)];
      
      // 使用sampleTerrainMostDetailed获取最精确的地形高度
      Cesium.sampleTerrainMostDetailed(this.viewer.terrainProvider, positions)
        .then((updatedPositions) => {
          // 返回海拔高度（单位：米）
          resolve(updatedPositions[0].height || 0);
        })
        .catch(() => {
          // 如果获取地形高度失败，返回0
          resolve(0);
        });
    });
  }
  
  /**
   * 平滑过渡到新的高度
   * @param newHeight 新的目标高度
   * @returns 当前应该使用的平滑高度
   */
  smoothHeight(newHeight: number): number {
    // 检查是否需要开始新的高度过渡
    if (Math.abs(this.targetTerrainHeight - newHeight) > 0.1) {
      // 目标高度发生了变化，开始新的过渡
      this.targetTerrainHeight = newHeight;
      this.heightTransitionCounter = this.heightTransitionFrames;
      this.heightTransitionStep = (this.targetTerrainHeight - this.currentTerrainHeight) / this.heightTransitionFrames;
    }
    
    // 如果正在过渡中
    if (this.heightTransitionCounter > 0) {
      // 更新当前高度
      this.currentTerrainHeight += this.heightTransitionStep;
      this.heightTransitionCounter--;
      
      // 如果是最后一帧，确保精确到达目标高度
      if (this.heightTransitionCounter === 0) {
        this.currentTerrainHeight = this.targetTerrainHeight;
      }
    } else if (Math.abs(this.currentTerrainHeight - this.targetTerrainHeight) > 0.1) {
      // 过渡应该结束但高度不匹配，强制设置
      this.currentTerrainHeight = this.targetTerrainHeight;
    }
    
    return this.currentTerrainHeight;
  }
  
  /**
   * 直接获取地形高度（不使用缓存）
   * @param longitude 经度
   * @param latitude 纬度
   * @returns 地形高度
   */
  getTerrainHeight(longitude: number, latitude: number): Promise<number> {
    return this.fetchTerrainHeight(longitude, latitude);
  }
  
  /**
   * 设置采样间隔
   * @param interval 采样间隔（帧数）
   */
  setCacheSampleInterval(interval: number): void {
    this.CACHE_SAMPLE_INTERVAL = interval;
  }
  
  /**
   * 设置高度过渡帧数
   * @param frames 过渡帧数
   */
  setHeightTransitionFrames(frames: number): void {
    this.heightTransitionFrames = frames;
  }
  
  /**
   * 清理缓存和状态
   */
  cleanup(): void {
    this.terrainHeightCache.clear();
    this.currentTerrainHeight = 0;
    this.targetTerrainHeight = 0;
    this.heightTransitionCounter = 0;
    this.heightTransitionStep = 0;
  }
} 