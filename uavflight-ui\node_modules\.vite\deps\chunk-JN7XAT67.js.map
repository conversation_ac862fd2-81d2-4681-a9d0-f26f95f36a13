{"version": 3, "sources": ["../../ol/featureloader.js"], "sourcesContent": ["/**\n * @module ol/featureloader\n */\nimport {VOID} from './functions.js';\n\n/**\n *\n * @type {boolean}\n * @private\n */\nlet withCredentials = false;\n\n/**\n * {@link module:ol/source/Vector~VectorSource} sources use a function of this type to\n * load features.\n *\n * This function takes up to 5 arguments. These are an {@link module:ol/extent~Extent} representing\n * the area to be loaded, a `{number}` representing the resolution (map units per pixel), an\n * {@link module:ol/proj/Projection~Projection} for the projection, an optional success callback that should get\n * the loaded features passed as an argument and an optional failure callback with no arguments. If\n * the callbacks are not used, the corresponding vector source will not fire `'featuresloadend'` and\n * `'featuresloaderror'` events. `this` within the function is bound to the\n * {@link module:ol/source/Vector~VectorSource} it's called from.\n *\n * The function is responsible for loading the features and adding them to the\n * source.\n * @typedef {function(this:(import(\"./source/Vector\").default|import(\"./VectorTile.js\").default),\n *           import(\"./extent.js\").Extent,\n *           number,\n *           import(\"./proj/Projection.js\").default,\n *           function(Array<import(\"./Feature.js\").default>): void=,\n *           function(): void=): void} FeatureLoader\n * @api\n */\n\n/**\n * {@link module:ol/source/Vector~VectorSource} sources use a function of this type to\n * get the url to load features from.\n *\n * This function takes an {@link module:ol/extent~Extent} representing the area\n * to be loaded, a `{number}` representing the resolution (map units per pixel)\n * and an {@link module:ol/proj/Projection~Projection} for the projection  as\n * arguments and returns a `{string}` representing the URL.\n * @typedef {function(import(\"./extent.js\").Extent, number, import(\"./proj/Projection.js\").default): string} FeatureUrlFunction\n * @api\n */\n\n/**\n * @param {string|FeatureUrlFunction} url Feature URL service.\n * @param {import(\"./format/Feature.js\").default} format Feature format.\n * @param {import(\"./extent.js\").Extent} extent Extent.\n * @param {number} resolution Resolution.\n * @param {import(\"./proj/Projection.js\").default} projection Projection.\n * @param {function(Array<import(\"./Feature.js\").default>, import(\"./proj/Projection.js\").default): void} success Success\n *      Function called with the loaded features and optionally with the data projection.\n * @param {function(): void} failure Failure\n *      Function called when loading failed.\n */\nexport function loadFeaturesXhr(\n  url,\n  format,\n  extent,\n  resolution,\n  projection,\n  success,\n  failure\n) {\n  const xhr = new XMLHttpRequest();\n  xhr.open(\n    'GET',\n    typeof url === 'function' ? url(extent, resolution, projection) : url,\n    true\n  );\n  if (format.getType() == 'arraybuffer') {\n    xhr.responseType = 'arraybuffer';\n  }\n  xhr.withCredentials = withCredentials;\n  /**\n   * @param {Event} event Event.\n   * @private\n   */\n  xhr.onload = function (event) {\n    // status will be 0 for file:// urls\n    if (!xhr.status || (xhr.status >= 200 && xhr.status < 300)) {\n      const type = format.getType();\n      /** @type {Document|Node|Object|string|undefined} */\n      let source;\n      if (type == 'json' || type == 'text') {\n        source = xhr.responseText;\n      } else if (type == 'xml') {\n        source = xhr.responseXML;\n        if (!source) {\n          source = new DOMParser().parseFromString(\n            xhr.responseText,\n            'application/xml'\n          );\n        }\n      } else if (type == 'arraybuffer') {\n        source = /** @type {ArrayBuffer} */ (xhr.response);\n      }\n      if (source) {\n        success(\n          /** @type {Array<import(\"./Feature.js\").default>} */\n          (\n            format.readFeatures(source, {\n              extent: extent,\n              featureProjection: projection,\n            })\n          ),\n          format.readProjection(source)\n        );\n      } else {\n        failure();\n      }\n    } else {\n      failure();\n    }\n  };\n  /**\n   * @private\n   */\n  xhr.onerror = failure;\n  xhr.send();\n}\n\n/**\n * Create an XHR feature loader for a `url` and `format`. The feature loader\n * loads features (with XHR), parses the features, and adds them to the\n * vector source.\n * @param {string|FeatureUrlFunction} url Feature URL service.\n * @param {import(\"./format/Feature.js\").default} format Feature format.\n * @return {FeatureLoader} The feature loader.\n * @api\n */\nexport function xhr(url, format) {\n  /**\n   * @param {import(\"./extent.js\").Extent} extent Extent.\n   * @param {number} resolution Resolution.\n   * @param {import(\"./proj/Projection.js\").default} projection Projection.\n   * @param {function(Array<import(\"./Feature.js\").default>): void} [success] Success\n   *      Function called when loading succeeded.\n   * @param {function(): void} [failure] Failure\n   *      Function called when loading failed.\n   */\n  return function (extent, resolution, projection, success, failure) {\n    const source = /** @type {import(\"./source/Vector\").default} */ (this);\n    loadFeaturesXhr(\n      url,\n      format,\n      extent,\n      resolution,\n      projection,\n      /**\n       * @param {Array<import(\"./Feature.js\").default>} features The loaded features.\n       * @param {import(\"./proj/Projection.js\").default} dataProjection Data\n       * projection.\n       */\n      function (features, dataProjection) {\n        source.addFeatures(features);\n        if (success !== undefined) {\n          success(features);\n        }\n      },\n      /* FIXME handle error */ failure ? failure : VOID\n    );\n  };\n}\n\n/**\n * Setter for the withCredentials configuration for the XHR.\n *\n * @param {boolean} xhrWithCredentials The value of withCredentials to set.\n * Compare https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/\n * @api\n */\nexport function setWithCredentials(xhrWithCredentials) {\n  withCredentials = xhrWithCredentials;\n}\n"], "mappings": ";;;;;AAUA,IAAI,kBAAkB;AAgDf,SAAS,gBACd,KACA,QACA,QACA,YACA,YACA,SACA,SACA;AACA,QAAMA,OAAM,IAAI,eAAe;AAC/B,EAAAA,KAAI;AAAA,IACF;AAAA,IACA,OAAO,QAAQ,aAAa,IAAI,QAAQ,YAAY,UAAU,IAAI;AAAA,IAClE;AAAA,EACF;AACA,MAAI,OAAO,QAAQ,KAAK,eAAe;AACrC,IAAAA,KAAI,eAAe;AAAA,EACrB;AACA,EAAAA,KAAI,kBAAkB;AAKtB,EAAAA,KAAI,SAAS,SAAU,OAAO;AAE5B,QAAI,CAACA,KAAI,UAAWA,KAAI,UAAU,OAAOA,KAAI,SAAS,KAAM;AAC1D,YAAM,OAAO,OAAO,QAAQ;AAE5B,UAAI;AACJ,UAAI,QAAQ,UAAU,QAAQ,QAAQ;AACpC,iBAASA,KAAI;AAAA,MACf,WAAW,QAAQ,OAAO;AACxB,iBAASA,KAAI;AACb,YAAI,CAAC,QAAQ;AACX,mBAAS,IAAI,UAAU,EAAE;AAAA,YACvBA,KAAI;AAAA,YACJ;AAAA,UACF;AAAA,QACF;AAAA,MACF,WAAW,QAAQ,eAAe;AAChC;AAAA,QAAqCA,KAAI;AAAA,MAC3C;AACA,UAAI,QAAQ;AACV;AAAA;AAAA,UAGI,OAAO,aAAa,QAAQ;AAAA,YAC1B;AAAA,YACA,mBAAmB;AAAA,UACrB,CAAC;AAAA,UAEH,OAAO,eAAe,MAAM;AAAA,QAC9B;AAAA,MACF,OAAO;AACL,gBAAQ;AAAA,MACV;AAAA,IACF,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF;AAIA,EAAAA,KAAI,UAAU;AACd,EAAAA,KAAI,KAAK;AACX;AAWO,SAAS,IAAI,KAAK,QAAQ;AAU/B,SAAO,SAAU,QAAQ,YAAY,YAAY,SAAS,SAAS;AACjE,UAAM;AAAA;AAAA,MAA2D;AAAA;AACjE;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,SAAU,UAAU,gBAAgB;AAClC,eAAO,YAAY,QAAQ;AAC3B,YAAI,YAAY,QAAW;AACzB,kBAAQ,QAAQ;AAAA,QAClB;AAAA,MACF;AAAA;AAAA,MACyB,UAAU,UAAU;AAAA,IAC/C;AAAA,EACF;AACF;", "names": ["xhr"]}