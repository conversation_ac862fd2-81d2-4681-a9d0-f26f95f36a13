{"version": 3, "sources": ["../../ol/color.js"], "sourcesContent": ["/**\n * @module ol/color\n */\nimport {assert} from './asserts.js';\nimport {clamp} from './math.js';\n\n/**\n * A color represented as a short array [red, green, blue, alpha].\n * red, green, and blue should be integers in the range 0..255 inclusive.\n * alpha should be a float in the range 0..1 inclusive. If no alpha value is\n * given then `1` will be used.\n * @typedef {Array<number>} Color\n * @api\n */\n\n/**\n * This RegExp matches # followed by 3, 4, 6, or 8 hex digits.\n * @const\n * @type {RegExp}\n * @private\n */\nconst HEX_COLOR_RE_ = /^#([a-f0-9]{3}|[a-f0-9]{4}(?:[a-f0-9]{2}){0,2})$/i;\n\n/**\n * Regular expression for matching potential named color style strings.\n * @const\n * @type {RegExp}\n * @private\n */\nconst NAMED_COLOR_RE_ = /^([a-z]*)$|^hsla?\\(.*\\)$/i;\n\n/**\n * Return the color as an rgba string.\n * @param {Color|string} color Color.\n * @return {string} Rgba string.\n * @api\n */\nexport function asString(color) {\n  if (typeof color === 'string') {\n    return color;\n  }\n  return toString(color);\n}\n\n/**\n * Return named color as an rgba string.\n * @param {string} color Named color.\n * @return {string} Rgb string.\n */\nfunction fromNamed(color) {\n  const el = document.createElement('div');\n  el.style.color = color;\n  if (el.style.color !== '') {\n    document.body.appendChild(el);\n    const rgb = getComputedStyle(el).color;\n    document.body.removeChild(el);\n    return rgb;\n  }\n  return '';\n}\n\n/**\n * @param {string} s String.\n * @return {Color} Color.\n */\nexport const fromString = (function () {\n  // We maintain a small cache of parsed strings.  To provide cheap LRU-like\n  // semantics, whenever the cache grows too large we simply delete an\n  // arbitrary 25% of the entries.\n\n  /**\n   * @const\n   * @type {number}\n   */\n  const MAX_CACHE_SIZE = 1024;\n\n  /**\n   * @type {Object<string, Color>}\n   */\n  const cache = {};\n\n  /**\n   * @type {number}\n   */\n  let cacheSize = 0;\n\n  return (\n    /**\n     * @param {string} s String.\n     * @return {Color} Color.\n     */\n    function (s) {\n      let color;\n      if (cache.hasOwnProperty(s)) {\n        color = cache[s];\n      } else {\n        if (cacheSize >= MAX_CACHE_SIZE) {\n          let i = 0;\n          for (const key in cache) {\n            if ((i++ & 3) === 0) {\n              delete cache[key];\n              --cacheSize;\n            }\n          }\n        }\n        color = fromStringInternal_(s);\n        cache[s] = color;\n        ++cacheSize;\n      }\n      return color;\n    }\n  );\n})();\n\n/**\n * Return the color as an array. This function maintains a cache of calculated\n * arrays which means the result should not be modified.\n * @param {Color|string} color Color.\n * @return {Color} Color.\n * @api\n */\nexport function asArray(color) {\n  if (Array.isArray(color)) {\n    return color;\n  }\n  return fromString(color);\n}\n\n/**\n * @param {string} s String.\n * @private\n * @return {Color} Color.\n */\nfunction fromStringInternal_(s) {\n  let r, g, b, a, color;\n\n  if (NAMED_COLOR_RE_.exec(s)) {\n    s = fromNamed(s);\n  }\n\n  if (HEX_COLOR_RE_.exec(s)) {\n    // hex\n    const n = s.length - 1; // number of hex digits\n    let d; // number of digits per channel\n    if (n <= 4) {\n      d = 1;\n    } else {\n      d = 2;\n    }\n    const hasAlpha = n === 4 || n === 8;\n    r = parseInt(s.substr(1 + 0 * d, d), 16);\n    g = parseInt(s.substr(1 + 1 * d, d), 16);\n    b = parseInt(s.substr(1 + 2 * d, d), 16);\n    if (hasAlpha) {\n      a = parseInt(s.substr(1 + 3 * d, d), 16);\n    } else {\n      a = 255;\n    }\n    if (d == 1) {\n      r = (r << 4) + r;\n      g = (g << 4) + g;\n      b = (b << 4) + b;\n      if (hasAlpha) {\n        a = (a << 4) + a;\n      }\n    }\n    color = [r, g, b, a / 255];\n  } else if (s.startsWith('rgba(')) {\n    // rgba()\n    color = s.slice(5, -1).split(',').map(Number);\n    normalize(color);\n  } else if (s.startsWith('rgb(')) {\n    // rgb()\n    color = s.slice(4, -1).split(',').map(Number);\n    color.push(1);\n    normalize(color);\n  } else {\n    assert(false, 14); // Invalid color\n  }\n  return color;\n}\n\n/**\n * TODO this function is only used in the test, we probably shouldn't export it\n * @param {Color} color Color.\n * @return {Color} Clamped color.\n */\nexport function normalize(color) {\n  color[0] = clamp((color[0] + 0.5) | 0, 0, 255);\n  color[1] = clamp((color[1] + 0.5) | 0, 0, 255);\n  color[2] = clamp((color[2] + 0.5) | 0, 0, 255);\n  color[3] = clamp(color[3], 0, 1);\n  return color;\n}\n\n/**\n * @param {Color} color Color.\n * @return {string} String.\n */\nexport function toString(color) {\n  let r = color[0];\n  if (r != (r | 0)) {\n    r = (r + 0.5) | 0;\n  }\n  let g = color[1];\n  if (g != (g | 0)) {\n    g = (g + 0.5) | 0;\n  }\n  let b = color[2];\n  if (b != (b | 0)) {\n    b = (b + 0.5) | 0;\n  }\n  const a = color[3] === undefined ? 1 : Math.round(color[3] * 100) / 100;\n  return 'rgba(' + r + ',' + g + ',' + b + ',' + a + ')';\n}\n\n/**\n * @param {string} s String.\n * @return {boolean} Whether the string is actually a valid color\n */\nexport function isStringColor(s) {\n  if (NAMED_COLOR_RE_.test(s)) {\n    s = fromNamed(s);\n  }\n  return HEX_COLOR_RE_.test(s) || s.startsWith('rgba(') || s.startsWith('rgb(');\n}\n"], "mappings": ";;;;;;;;AAqBA,IAAM,gBAAgB;AAQtB,IAAM,kBAAkB;AAQjB,SAAS,SAAS,OAAO;AAC9B,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AACA,SAAO,SAAS,KAAK;AACvB;AAOA,SAAS,UAAU,OAAO;AACxB,QAAM,KAAK,SAAS,cAAc,KAAK;AACvC,KAAG,MAAM,QAAQ;AACjB,MAAI,GAAG,MAAM,UAAU,IAAI;AACzB,aAAS,KAAK,YAAY,EAAE;AAC5B,UAAM,MAAM,iBAAiB,EAAE,EAAE;AACjC,aAAS,KAAK,YAAY,EAAE;AAC5B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAMO,IAAM,aAAc,2BAAY;AASrC,QAAM,iBAAiB;AAKvB,QAAM,QAAQ,CAAC;AAKf,MAAI,YAAY;AAEhB;AAAA;AAAA;AAAA;AAAA;AAAA,IAKE,SAAU,GAAG;AACX,UAAI;AACJ,UAAI,MAAM,eAAe,CAAC,GAAG;AAC3B,gBAAQ,MAAM,CAAC;AAAA,MACjB,OAAO;AACL,YAAI,aAAa,gBAAgB;AAC/B,cAAI,IAAI;AACR,qBAAW,OAAO,OAAO;AACvB,iBAAK,MAAM,OAAO,GAAG;AACnB,qBAAO,MAAM,GAAG;AAChB,gBAAE;AAAA,YACJ;AAAA,UACF;AAAA,QACF;AACA,gBAAQ,oBAAoB,CAAC;AAC7B,cAAM,CAAC,IAAI;AACX,UAAE;AAAA,MACJ;AACA,aAAO;AAAA,IACT;AAAA;AAEJ,EAAG;AASI,SAAS,QAAQ,OAAO;AAC7B,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO;AAAA,EACT;AACA,SAAO,WAAW,KAAK;AACzB;AAOA,SAAS,oBAAoB,GAAG;AAC9B,MAAI,GAAG,GAAG,GAAG,GAAG;AAEhB,MAAI,gBAAgB,KAAK,CAAC,GAAG;AAC3B,QAAI,UAAU,CAAC;AAAA,EACjB;AAEA,MAAI,cAAc,KAAK,CAAC,GAAG;AAEzB,UAAM,IAAI,EAAE,SAAS;AACrB,QAAI;AACJ,QAAI,KAAK,GAAG;AACV,UAAI;AAAA,IACN,OAAO;AACL,UAAI;AAAA,IACN;AACA,UAAM,WAAW,MAAM,KAAK,MAAM;AAClC,QAAI,SAAS,EAAE,OAAO,IAAI,IAAI,GAAG,CAAC,GAAG,EAAE;AACvC,QAAI,SAAS,EAAE,OAAO,IAAI,IAAI,GAAG,CAAC,GAAG,EAAE;AACvC,QAAI,SAAS,EAAE,OAAO,IAAI,IAAI,GAAG,CAAC,GAAG,EAAE;AACvC,QAAI,UAAU;AACZ,UAAI,SAAS,EAAE,OAAO,IAAI,IAAI,GAAG,CAAC,GAAG,EAAE;AAAA,IACzC,OAAO;AACL,UAAI;AAAA,IACN;AACA,QAAI,KAAK,GAAG;AACV,WAAK,KAAK,KAAK;AACf,WAAK,KAAK,KAAK;AACf,WAAK,KAAK,KAAK;AACf,UAAI,UAAU;AACZ,aAAK,KAAK,KAAK;AAAA,MACjB;AAAA,IACF;AACA,YAAQ,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG;AAAA,EAC3B,WAAW,EAAE,WAAW,OAAO,GAAG;AAEhC,YAAQ,EAAE,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG,EAAE,IAAI,MAAM;AAC5C,cAAU,KAAK;AAAA,EACjB,WAAW,EAAE,WAAW,MAAM,GAAG;AAE/B,YAAQ,EAAE,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG,EAAE,IAAI,MAAM;AAC5C,UAAM,KAAK,CAAC;AACZ,cAAU,KAAK;AAAA,EACjB,OAAO;AACL,WAAO,OAAO,EAAE;AAAA,EAClB;AACA,SAAO;AACT;AAOO,SAAS,UAAU,OAAO;AAC/B,QAAM,CAAC,IAAI,MAAO,MAAM,CAAC,IAAI,MAAO,GAAG,GAAG,GAAG;AAC7C,QAAM,CAAC,IAAI,MAAO,MAAM,CAAC,IAAI,MAAO,GAAG,GAAG,GAAG;AAC7C,QAAM,CAAC,IAAI,MAAO,MAAM,CAAC,IAAI,MAAO,GAAG,GAAG,GAAG;AAC7C,QAAM,CAAC,IAAI,MAAM,MAAM,CAAC,GAAG,GAAG,CAAC;AAC/B,SAAO;AACT;AAMO,SAAS,SAAS,OAAO;AAC9B,MAAI,IAAI,MAAM,CAAC;AACf,MAAI,MAAM,IAAI,IAAI;AAChB,QAAK,IAAI,MAAO;AAAA,EAClB;AACA,MAAI,IAAI,MAAM,CAAC;AACf,MAAI,MAAM,IAAI,IAAI;AAChB,QAAK,IAAI,MAAO;AAAA,EAClB;AACA,MAAI,IAAI,MAAM,CAAC;AACf,MAAI,MAAM,IAAI,IAAI;AAChB,QAAK,IAAI,MAAO;AAAA,EAClB;AACA,QAAM,IAAI,MAAM,CAAC,MAAM,SAAY,IAAI,KAAK,MAAM,MAAM,CAAC,IAAI,GAAG,IAAI;AACpE,SAAO,UAAU,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI;AACrD;AAMO,SAAS,cAAc,GAAG;AAC/B,MAAI,gBAAgB,KAAK,CAAC,GAAG;AAC3B,QAAI,UAAU,CAAC;AAAA,EACjB;AACA,SAAO,cAAc,KAAK,CAAC,KAAK,EAAE,WAAW,OAAO,KAAK,EAAE,WAAW,MAAM;AAC9E;", "names": []}