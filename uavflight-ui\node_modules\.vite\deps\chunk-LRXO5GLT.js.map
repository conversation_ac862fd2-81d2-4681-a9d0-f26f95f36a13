{"version": 3, "sources": ["../../ol/TileRange.js"], "sourcesContent": ["/**\n * @module ol/TileRange\n */\n\n/**\n * A representation of a contiguous block of tiles.  A tile range is specified\n * by its min/max tile coordinates and is inclusive of coordinates.\n */\nclass TileRange {\n  /**\n   * @param {number} minX Minimum X.\n   * @param {number} maxX Maximum X.\n   * @param {number} minY Minimum Y.\n   * @param {number} maxY Maximum Y.\n   */\n  constructor(minX, maxX, minY, maxY) {\n    /**\n     * @type {number}\n     */\n    this.minX = minX;\n\n    /**\n     * @type {number}\n     */\n    this.maxX = maxX;\n\n    /**\n     * @type {number}\n     */\n    this.minY = minY;\n\n    /**\n     * @type {number}\n     */\n    this.maxY = maxY;\n  }\n\n  /**\n   * @param {import(\"./tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n   * @return {boolean} Contains tile coordinate.\n   */\n  contains(tileCoord) {\n    return this.containsXY(tileCoord[1], tileCoord[2]);\n  }\n\n  /**\n   * @param {TileRange} tileRange Tile range.\n   * @return {boolean} Contains.\n   */\n  containsTileRange(tileRange) {\n    return (\n      this.minX <= tileRange.minX &&\n      tileRange.maxX <= this.maxX &&\n      this.minY <= tileRange.minY &&\n      tileRange.maxY <= this.maxY\n    );\n  }\n\n  /**\n   * @param {number} x Tile coordinate x.\n   * @param {number} y Tile coordinate y.\n   * @return {boolean} Contains coordinate.\n   */\n  containsXY(x, y) {\n    return this.minX <= x && x <= this.maxX && this.minY <= y && y <= this.maxY;\n  }\n\n  /**\n   * @param {TileRange} tileRange Tile range.\n   * @return {boolean} Equals.\n   */\n  equals(tileRange) {\n    return (\n      this.minX == tileRange.minX &&\n      this.minY == tileRange.minY &&\n      this.maxX == tileRange.maxX &&\n      this.maxY == tileRange.maxY\n    );\n  }\n\n  /**\n   * @param {TileRange} tileRange Tile range.\n   */\n  extend(tileRange) {\n    if (tileRange.minX < this.minX) {\n      this.minX = tileRange.minX;\n    }\n    if (tileRange.maxX > this.maxX) {\n      this.maxX = tileRange.maxX;\n    }\n    if (tileRange.minY < this.minY) {\n      this.minY = tileRange.minY;\n    }\n    if (tileRange.maxY > this.maxY) {\n      this.maxY = tileRange.maxY;\n    }\n  }\n\n  /**\n   * @return {number} Height.\n   */\n  getHeight() {\n    return this.maxY - this.minY + 1;\n  }\n\n  /**\n   * @return {import(\"./size.js\").Size} Size.\n   */\n  getSize() {\n    return [this.getWidth(), this.getHeight()];\n  }\n\n  /**\n   * @return {number} Width.\n   */\n  getWidth() {\n    return this.maxX - this.minX + 1;\n  }\n\n  /**\n   * @param {TileRange} tileRange Tile range.\n   * @return {boolean} Intersects.\n   */\n  intersects(tileRange) {\n    return (\n      this.minX <= tileRange.maxX &&\n      this.maxX >= tileRange.minX &&\n      this.minY <= tileRange.maxY &&\n      this.maxY >= tileRange.minY\n    );\n  }\n}\n\n/**\n * @param {number} minX Minimum X.\n * @param {number} maxX Maximum X.\n * @param {number} minY Minimum Y.\n * @param {number} maxY Maximum Y.\n * @param {TileRange} [tileRange] TileRange.\n * @return {TileRange} Tile range.\n */\nexport function createOrUpdate(minX, maxX, minY, maxY, tileRange) {\n  if (tileRange !== undefined) {\n    tileRange.minX = minX;\n    tileRange.maxX = maxX;\n    tileRange.minY = minY;\n    tileRange.maxY = maxY;\n    return tileRange;\n  }\n  return new TileRange(minX, maxX, minY, maxY);\n}\n\nexport default TileRange;\n"], "mappings": ";AAQA,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOd,YAAY,MAAM,MAAM,MAAM,MAAM;AAIlC,SAAK,OAAO;AAKZ,SAAK,OAAO;AAKZ,SAAK,OAAO;AAKZ,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,WAAW;AAClB,WAAO,KAAK,WAAW,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,WAAW;AAC3B,WACE,KAAK,QAAQ,UAAU,QACvB,UAAU,QAAQ,KAAK,QACvB,KAAK,QAAQ,UAAU,QACvB,UAAU,QAAQ,KAAK;AAAA,EAE3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,GAAG,GAAG;AACf,WAAO,KAAK,QAAQ,KAAK,KAAK,KAAK,QAAQ,KAAK,QAAQ,KAAK,KAAK,KAAK;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,WAAW;AAChB,WACE,KAAK,QAAQ,UAAU,QACvB,KAAK,QAAQ,UAAU,QACvB,KAAK,QAAQ,UAAU,QACvB,KAAK,QAAQ,UAAU;AAAA,EAE3B;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,WAAW;AAChB,QAAI,UAAU,OAAO,KAAK,MAAM;AAC9B,WAAK,OAAO,UAAU;AAAA,IACxB;AACA,QAAI,UAAU,OAAO,KAAK,MAAM;AAC9B,WAAK,OAAO,UAAU;AAAA,IACxB;AACA,QAAI,UAAU,OAAO,KAAK,MAAM;AAC9B,WAAK,OAAO,UAAU;AAAA,IACxB;AACA,QAAI,UAAU,OAAO,KAAK,MAAM;AAC9B,WAAK,OAAO,UAAU;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,WAAO,KAAK,OAAO,KAAK,OAAO;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO,CAAC,KAAK,SAAS,GAAG,KAAK,UAAU,CAAC;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK,OAAO,KAAK,OAAO;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,WAAW;AACpB,WACE,KAAK,QAAQ,UAAU,QACvB,KAAK,QAAQ,UAAU,QACvB,KAAK,QAAQ,UAAU,QACvB,KAAK,QAAQ,UAAU;AAAA,EAE3B;AACF;AAUO,SAAS,eAAe,MAAM,MAAM,MAAM,MAAM,WAAW;AAChE,MAAI,cAAc,QAAW;AAC3B,cAAU,OAAO;AACjB,cAAU,OAAO;AACjB,cAAU,OAAO;AACjB,cAAU,OAAO;AACjB,WAAO;AAAA,EACT;AACA,SAAO,IAAI,UAAU,MAAM,MAAM,MAAM,IAAI;AAC7C;AAEA,IAAO,oBAAQ;", "names": []}