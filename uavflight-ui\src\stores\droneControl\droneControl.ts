// user模块，类似于 vuex 中的moduel
import { defineStore } from 'pinia';
import * as Cesium from 'cesium';
import VideoControler from './VideoControler';
import { markRaw } from 'vue';
import { list } from 'postcss';
import { time } from 'echarts';
import PolylineTrailMaterialProperty from '../../utils/cesium/polylineTrail.js';
import TerrainSampler from '../../utils/cesium/TerrainSampler';

export const useDroneControlStore = defineStore({
	// 模块ID
	id: 'DroneControlStore',
	state: () => {
		return {
			defaultVideoPath: [],
			videoPath: [], //无人机的关键点路径（Cartesian3 格式）
			clampVideoPath: [], //绘制无人机飞行路径的信息
			videoControler: {},
			videoHandle: {}, // 无人机视频部分鼠标事件
			viewer: markRaw({}),
			videoEntityCollection: markRaw({}),
			DronePanelShow: false,
			taskId: '',
			// 添加地形采样器
			terrainSampler: null as TerrainSampler | null,
		};
	},
	actions: {
		init(viewer: any) {
			this.viewer = viewer;
			// 无人机视频的实体集合
			this.videoEntityCollection = new Cesium.CustomDataSource('videoEntityCollection');
			this.viewer.dataSources.add(this.videoEntityCollection);
			// 初始化地形采样器
			this.terrainSampler = new TerrainSampler(viewer);
		},
		setData(data) {
			this.defaultVideoPath = [];
			data.map((item) => {
				this.defaultVideoPath.push({
					lng: parseFloat(item.longitude),
					lat: parseFloat(item.latitude),
					startTime: item.startTime,
					gimbalPitch: parseFloat(item.gimbalPitch),
					gimbalRoll: parseFloat(item.gimbalRoll),
					gimbalYaw: parseFloat(item.gimbalYaw),
				});
			});
			console.log(this.defaultVideoPath);
			// console.log(1);

			this.videoPath = [];
			this.videoPath = this.defaultVideoPath.flatMap((item, index, array) => {
				const point = Cesium.Cartesian3.fromDegrees(item.lng, item.lat, 60);
				const result = [];

				// 每隔 100 个点额外存储当前点（保证不在最后一个点重复存）
				if (index > 0 && index % 100 === 0 && index !== array.length - 1) {
					// 计算实际帧索引 - 使用实际索引
					const frameIndex = index;
					console.log(`添加中间关键点，原始索引: ${index}, 帧索引: ${frameIndex}`);
					result.push({ 
						point: point, 
						time: item.startTime,
						frameIndex: frameIndex // 存储实际帧索引
					});
				}

				// 确保第一个点被添加
				if (index === 0) {
					console.log(`添加第一个点，原始索引: ${index}`);
					result.push({ 
						point: point, 
						time: item.startTime,
						frameIndex: 0 // 第一个点的帧索引为0
					});
				}

				// 确保最后一个点被添加
				if (index === array.length - 1) {
					console.log(`添加最后一个点，原始索引: ${index}`);
					result.push({ 
						point: point, 
						time: item.startTime,
						frameIndex: index // 存储实际帧索引
					});
				}

				return result;
			});

			console.log('生成的关键点路径:', this.videoPath);
			// console.log(2);
			// console.log(this.videoPath);
			// console.log(this.videoPath.map((item) => item.point));
			this.clampVideoPath = [];
			this.defaultVideoPath.forEach((item) => {
				this.clampVideoPath.push(item.lng);
				this.clampVideoPath.push(item.lat);
			});

			this.videoControler = new VideoControler(this.defaultVideoPath);
			// 设置viewer实例
			this.videoControler.setViewer(this.viewer);
			this.doUAVVideoShow();
		},
		doUAVVideoShow() {
			let UAVEntity = {};
			let	pathPointEntities = [];
			// 添加无人机

			const position = this.videoPath[0].point;
			const heading = Cesium.Math.toRadians(135);
			const pitch = 0;
			const roll = 0;
			const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
			
			// 从position中获取经纬度和高度
			const cartographic = Cesium.Cartographic.fromCartesian(position);
			const longitude = Cesium.Math.toDegrees(cartographic.longitude);
			const latitude = Cesium.Math.toDegrees(cartographic.latitude);
			
			// 异步函数，添加无人机和路径点
			const addEntities = async () => {
				// 使用地形采样器获取无人机位置的地形高度
				const terrainHeight = await this.terrainSampler?.getTerrainHeight(longitude, latitude) || 0;
				console.log('terrainHeight', terrainHeight);

				// 创建在地面上方100米的无人机位置
				const uavPosition = Cesium.Cartesian3.fromDegrees(
					longitude,
					latitude,
					terrainHeight + 100  // 地形高度 + 100米
				);
				
			
				
				// 计算新位置的方向
				const orientation = Cesium.Transforms.headingPitchRollQuaternion(uavPosition, hpr);
				
				// 添加无人机实体
				UAVEntity = this.videoEntityCollection.entities.add({
					name: 'UAV',
					position: uavPosition,
					orientation: orientation,
					model: {
						uri: '/endData/model/CesiumDrone.glb',
						minimumPixelSize: 64,
						// maximumScale: 20000,
					},
				});
				
				// 相机跳转到无人机位置
				this.viewer.camera.flyTo({
					destination: Cesium.Cartesian3.fromDegrees(
						longitude,
						latitude,
						terrainHeight + 550  // 无人机高度 + 50米
					),
					orientation: {
						heading: Cesium.Math.toRadians(0),  // 偏航角
						pitch: Cesium.Math.toRadians(-90),  // 俯仰角，负值表示向下看
						roll: 0  // 翻滚角
					},
					duration: 1.5  // 动画持续时间（秒）
				});
				
				// 创建流线动态效果的材质
				const flowLineMaterial = new PolylineTrailMaterialProperty({
					color: Cesium.Color.CYAN,
					trailLength: 0.6,  // 尾迹长度，取值范围0-1
					period: 4.0  // 周期，单位秒
				});

				// 添加流线动态路径
				this.videoEntityCollection.entities.add({
					polyline: {
						positions: Cesium.Cartesian3.fromDegreesArray(this.clampVideoPath),
						width: 5,
						clampToGround: true,
						material: flowLineMaterial,
					},
				});

				// 添加静态路径线（颜色更浅）
				this.videoEntityCollection.entities.add({
					polyline: {
						positions: Cesium.Cartesian3.fromDegreesArray(this.clampVideoPath),
						width: 3,
						clampToGround: true,
						material: Cesium.Color.CYAN.withAlpha(0.3),
						zIndex: 0, // 确保静态线在动态线下方
					},
				});
				
				// 添加路径关键点
				console.log('this.clampVideoPath', this.clampVideoPath);
				
				// 异步处理所有路径点
				for (let index = 0; index < this.videoPath.length; index++) {
					const item = this.videoPath[index];
					// 获取当前点的经纬度
					const pointCartographic = Cesium.Cartographic.fromCartesian(item.point);
					const pointLon = Cesium.Math.toDegrees(pointCartographic.longitude);
					const pointLat = Cesium.Math.toDegrees(pointCartographic.latitude);
					
					// 使用地形采样器获取该点的地形高度
					const pointTerrainHeight = await this.terrainSampler?.getTerrainHeight(pointLon, pointLat) || 0;
					
					// 创建新的位置点（地形高度 + 50米）
					const newPosition = Cesium.Cartesian3.fromDegrees(
						pointLon,
						pointLat,
						pointTerrainHeight + 50
					);
					
					// 添加路径点实体
					const redSphere = this.videoEntityCollection.entities.add({
						name: 'UAV Path Point',
						position: newPosition,
						ellipsoid: {
							radii: new Cesium.Cartesian3(5.0, 5.0, 5.0),
							material: Cesium.Color.RED.withAlpha(0.5),
						},
					});
					
					// 计算实际帧索引 - 直接使用原始索引作为帧索引
					// 修复：之前使用 index * 100 导致跳转到上一个点
					const frameIndex = item.frameIndex || 0;
					const pointIndex = frameIndex;
					
					redSphere.type = 'pathPoint';
					redSphere.idx = pointIndex;
					redSphere.videoTime = item.time;
					
					// 添加调试信息
					console.log(`添加关键点 #${index}:`, {
						pointIndex: pointIndex,
						frameIndex: frameIndex,
						videoTime: item.time,
						position: [pointLon, pointLat]
					});
					
					pathPointEntities.push(redSphere);
				}
				
				// 开始鼠标事件处理
				this.startMouseEvents(pathPointEntities);
				
				// 初始化视频控制器
				this.videoControler.initObject({
					UAVEntity,
					pathPointEntities,
					viewer: this.viewer // 传递viewer实例
				});
			};
			
			// 调用异步函数添加实体
			addEntities();
		},
		
		// 鼠标事件处理方法提取为单独的函数
		startMouseEvents(pathPointEntities) {
			// 自定义基于requestAnimationFrame的节流函数
			function throttleByRAF(func) {
				let isWaiting = false;
				let lastArgs = null;

				return function (...args) {
					lastArgs = args;
					if (!isWaiting) {
						isWaiting = true;
						requestAnimationFrame(() => {
							func.apply(this, lastArgs);
							isWaiting = false;
						});
					}
				};
			}

			// 修改后的鼠标移动事件处理
			this.videoHandle = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
			let lastedHighLight = null;

			// 使用自定义节流
			const throttledHandler = throttleByRAF((movement) => {
				if (!this.viewer || this.viewer.isDestroyed()) return;

				try {
					const pickedObject = this.viewer.scene.pick(movement.endPosition);

					// 高亮逻辑
					if (Cesium.defined(pickedObject) && pickedObject.id?.type === 'pathPoint') {
						const entity = pickedObject.id;
						if (entity === lastedHighLight) return;

						// 恢复之前的高亮
						if (lastedHighLight?.ellipsoid) {
							lastedHighLight.ellipsoid.material = Cesium.Color.RED.withAlpha(0.5);
						}

						// 设置新高亮
						if (entity.ellipsoid) {
							entity.ellipsoid.material = Cesium.Color.YELLOW.withAlpha(0.5);
							lastedHighLight = entity;
						}
					} else if (lastedHighLight) {
						// 取消高亮
						if (lastedHighLight.ellipsoid) {
							lastedHighLight.ellipsoid.material = Cesium.Color.RED.withAlpha(0.5);
						}
						lastedHighLight = null;
					}
				} catch (e) {
					console.error('Highlight error:', e);
				}
			});

			// 绑定事件
			this.videoHandle.setInputAction((movement) => throttledHandler(movement), Cesium.ScreenSpaceEventType.MOUSE_MOVE);

			// 鼠标点击事件
			this.videoHandle.setInputAction((event) => {
				let pickedObject = this.viewer.scene.pick(event.position);

				if (Cesium.defined(pickedObject) && pickedObject.id && pickedObject.id.type == 'pathPoint') {
					const entity = pickedObject.id;
					// 添加详细调试信息
					console.log('点击关键点详细信息:', {
						idx: entity.idx,
						videoTime: entity.videoTime,
						type: entity.type,
						position: entity.position && entity.position.getValue 
							? entity.position.getValue(Cesium.JulianDate.now()) 
							: '无法获取位置'
					});
					
					// 确保idx是有效的数字
					if (typeof entity.idx === 'number' && !isNaN(entity.idx)) {
						// 使用jumpToKeyPoint方法，确保无人机和视频同步
						console.log('准备跳转到帧索引:', entity.idx);
						this.videoControler.jumpToKeyPoint(entity.idx);
					} else {
						console.error('无效的关键点索引:', entity.idx);
					}
				}
			}, Cesium.ScreenSpaceEventType.LEFT_CLICK);
		},

		closeUAVVideoShow() {
			// videoEntityCollection.show = false;

			this.viewer.dataSources.getByName('videoEntityCollection')[0].entities.removeAll();
			this.videoHandle.destroy();

			this.defaultVideoPath = [];
			this.videoPath = []; //无人机的关键点路径（Cartesian3 格式）
			this.clampVideoPath = []; //绘制无人机飞行路径的信息
			this.videoControler.cleanup();
			this.videoControler = {};
			
		},
		moveTo(idx){
			this.videoControler.moveTo(idx)
		}
	},
});
