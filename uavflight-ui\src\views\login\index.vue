<template>
	<div class="outer-container">
		<div class="container1">
			<img src="../../assets/img/login/login_log.png" class="logo-sty" />
			<p class="index-top-font">扶绥县"AI+无人机场"监测监管平台（渠旧试点）</p>
		</div>
		<div class="parent">
			<div class="child">
				<el-card>
					<!-- <el-tabs v-model="tabsActiveName"> -->
					<!-- <el-tab-pane label="登录" name="account"> -->
					<div class="login-name">
						<span>登录</span>
					</div>
					<Password @signInSuccess="signInSuccess" />
					<!-- </el-tab-pane> -->
					<!-- </el-tabs> -->
				</el-card>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts" name="loginIndex">
import { useThemeConfig } from '/@/stores/themeConfig';
import { NextLoading } from '/@/utils/loading';
import logoPng from '/@/assets/login/logo.png';
import { useI18n } from 'vue-i18n';
import { formatAxis } from '/@/utils/formatTime';
import { useMessage } from '/@/hooks/message';
import { Session } from '/@/utils/storage';
import { initBackEndControlRoutes } from '/@/router/backEnd';

// 引入组件
const Password = defineAsyncComponent(() => import('./component/password.vue'));

// 定义变量内容
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// 是否开启注册
const registerEnable = ref(import.meta.env.VITE_REGISTER_ENABLE === 'true');

// 默认选择账号密码登录方式
const tabsActiveName = ref('account');

// 获取布局配置信息
const getThemeConfig = computed(() => {
	return themeConfig.value;
});

// 登录成功后的跳转处理事件
const signInSuccess = async () => {
	const isNoPower = await initBackEndControlRoutes();
	if (isNoPower) {
		useMessage().wraning('抱歉，您没有登录权限');
		Session.clear();
	} else {
		// 初始化登录成功时间问候语
		let currentTimeInfo = formatAxis(new Date());
		if (route.query?.redirect) {
			router.push({
				path: route.query.redirect as string,
				query: Object.keys(route.query?.params || {}).length > 0 ? JSON.parse(route.query?.params as string) : '',
			});
		} else {
			router.push('/');
		}
		// 登录成功提示
		const signInText = t('signInText');
		useMessage().success(`${currentTimeInfo},${signInText}`);
		// 添加 loading，防止第一次进入界面时出现短暂空白
		NextLoading.start();
	}
};

// 页面加载时
onMounted(() => {
	NextLoading.done();
});
</script>

<style scoped>
@font-face {
	font-family: 'DingTalk';
	src: url('../../assets/fonts/DingTalk.ttf') format('truetype');
}

.outer-container {
	width: 100vw; /* 使用视口宽度单位 */
	height: 100vh; /* 使用视口高度单位 */
	background-image: url('../../assets/login/扶绥1.jpg');
	background-size: cover;
	background-position: center;
	margin-top: -5vh; /* 使用视口高度单位 */
	font-family: 'DingTalk';
}

.parent {
	height: 56vh; /* 使用视口高度单位 */
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 10vh; /* 使用视口高度单位 */
}

.child {
	margin-left: 60vw; /* 使用视口宽度单位 */
}

.container1 {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 5vh;
	background-color: rgba(150, 150, 150, 0.3);
	height: clamp(60px, 12vh, 100px);
	padding: 0 2vw;
	gap: 2vw;
}

.logo-sty {
	height: clamp(40px, 8vh, 80px);
	width: auto;
	object-fit: contain;
}

.index-top-font {
	font-family: 'DingTalk';
	color: #000000;
	line-height: 7vh;
	font-size: 2vw;
	font-weight: 600;
	letter-spacing: 0.2vw;
	margin: 0;
	white-space: nowrap;
}

.container1 {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 5vh;
	background-color: rgba(150, 150, 150, 0.3);
	height: clamp(60px, 12vh, 100px);
	padding: 0 2vw;
	gap: 2vw;
}

:deep(.el-tabs__nav-scroll) {
	display: flex;
	justify-content: center;
	font-size: 1.6vw; /* 使用视口宽度单位 */
}

.login-name {
	font-family: 'DingTalk';
	font-size: clamp(16px, 1.3vw, 24px);
	font-weight: 600;
	color: #2a7729;
	letter-spacing: 0.1vw;
	display: flex;
	justify-content: center;
	margin-bottom: 20px;
}
.login-content-form {
	margin-top: 15px;
}

:deep(.el-card.is-always-shadow) {
	height: 100%;
	border-radius: 8px;
	background-color: rgba(255, 255, 255, 0.7);
	backdrop-filter: blur(8px);
}

:deep(.el-card__body) {
	background-color: transparent;
}

:deep(.el-input__prefix-icon),
:deep(.el-input__icon),
:deep(.el-icon) {
	color: #2a7729 !important;
}

:deep(.el-input__wrapper) {
	border-radius: 20px;
	background-color: rgba(255, 255, 255, 0.5);
	box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1) inset;
}

/* 验证码输入框样式 */
:deep(.login-animation2 .el-col-15 .el-input__wrapper) {
	width: 140px !important;
	flex: none !important;
}

:deep(.login-animation2 .el-col-8 img) {
	height: 40px;
	cursor: pointer;
}

:deep(.el-input__wrapper:hover) {
	background-color: rgba(255, 255, 255, 0.7);
}

:deep(.el-input__wrapper.is-focus) {
	background-color: rgba(255, 255, 255, 0.8);
	box-shadow: 0 0 0 1px #2a7729 inset;
}

:deep(.el-button) {
	border-radius: 6px;
}

:deep(.mt30) {
	margin-top: 8vh !important; /* 使用视口高度单位 */
}

:deep(.el-form-item--large .el-form-item__content) {
	line-height: 4vh; /* 使用视口高度单位 */
	display: flex;
	justify-content: center;
	margin-bottom: 0.5vh;
}

:deep(.login-content-form .login-content-submit) {
	width: 65%;
	height: 80%;
	letter-spacing: 0.2vw;
	font-weight: 300;
	margin-top: calc(3vh - 15px);
	border-radius: 0.8vw;
	background-color: #2a7729;
	border: 0px;
}

:deep(.el-tabs__active-bar) {
	background-color: #2a7729;
}

/* 媒体查询，小屏幕样式调整 */
@media (max-width: 768px) {
	.child {
		width: 80vw;
		margin-left: 10vw;
	}

	.container1 {
		height: auto;
		padding: 10px;
		flex-direction: column;
	}

	.logo-sty {
		height: clamp(30px, 6vh, 50px);
	}

	.index-top-font {
		font-size: clamp(14px, 4vw, 24px);
		text-align: center;
		white-space: normal;
	}

	:deep(.el-tabs__nav-scroll) {
		font-size: 3vw;
	}

	:deep(.el-tabs__item) {
		font-size: 3.5vw;
	}
}
</style>
