/**
 * @file test-wms-style.ts
 * @description WMS图层样式设置测试文件
 * 用于测试新的WMS图层样式配置逻辑
 */

import { useOlMapLayerStore } from './index';

/**
 * 测试WMS图层样式设置
 */
export async function testWmsStyleConfiguration() {
  console.log('=== 开始测试WMS图层样式配置 ===');
  
  const mapLayerStore = useOlMapLayerStore();
  
  // 测试用例1: 使用配置文件中的defaultStyle
  const testLayer1 = {
    id: 'test_layer_1',
    name: '测试图层1',
    type: 'raster' as const,
    protocol: 'WMS' as const,
    workspace: 'test_myworkspace',
    layerName: 'test_layer',
    theme: '临时文件',
    defaultStyle: 'Blue',
    initialLoad: false
  };
  
  console.log('测试用例1: 使用配置的defaultStyle');
  console.log('图层配置:', testLayer1);
  
  // 测试用例2: 使用Random样式
  const testLayer2 = {
    id: 'test_layer_2',
    name: '测试图层2',
    type: 'raster' as const,
    protocol: 'WMS' as const,
    workspace: 'test_myworkspace',
    layerName: 'test_layer',
    theme: '临时文件',
    defaultStyle: 'Random',
    initialLoad: false
  };
  
  console.log('测试用例2: 使用Random样式');
  console.log('图层配置:', testLayer2);
  
  // 测试用例3: 没有defaultStyle配置
  const testLayer3 = {
    id: 'test_layer_3',
    name: '测试图层3',
    type: 'raster' as const,
    protocol: 'WMS' as const,
    workspace: 'test_myworkspace',
    layerName: 'test_layer',
    theme: '临时文件',
    initialLoad: false
  };
  
  console.log('测试用例3: 没有defaultStyle配置');
  console.log('图层配置:', testLayer3);
  
  // 测试样式设置方法
  try {
    console.log('\n--- 测试样式设置方法 ---');
    
    // 测试API随机样式获取
    console.log('测试API随机样式获取...');
    const randomStyleFromApi = await mapLayerStore.getRandomStyleFromApi();
    console.log('API返回的随机样式:', randomStyleFromApi);
    
    // 测试本地随机样式获取
    console.log('测试本地随机样式获取...');
    const localRandomStyle = mapLayerStore.getLocalRandomStyle();
    console.log('本地随机样式:', localRandomStyle);
    
    // 测试样式设置
    console.log('\n--- 测试样式设置 ---');
    
    const testOptions1 = { params: {} };
    await mapLayerStore.setWmsLayerStyle(testLayer1, testOptions1);
    console.log('测试用例1样式设置结果:', testOptions1.params.STYLES);
    
    const testOptions2 = { params: {} };
    await mapLayerStore.setWmsLayerStyle(testLayer2, testOptions2);
    console.log('测试用例2样式设置结果:', testOptions2.params.STYLES);
    
    const testOptions3 = { params: {} };
    await mapLayerStore.setWmsLayerStyle(testLayer3, testOptions3);
    console.log('测试用例3样式设置结果:', testOptions3.params.STYLES);
    
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
  
  console.log('=== WMS图层样式配置测试完成 ===');
}

/**
 * 测试随机样式API
 */
export async function testRandomStyleApi() {
  console.log('=== 测试随机样式API ===');

  try {
    // 使用与baseMap2相同的方式构建API URL
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8080';
    const apiUrl = `http://${geoserverIp}:${geoserverPort}/api/map/random-style`;

    console.log('测试API URL:', apiUrl);

    const response = await fetch(apiUrl);
    console.log('API响应状态:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('API响应数据:', data);

      if (data.status === 'success' && data.data && data.data.randomStyle) {
        console.log('随机样式信息:');
        console.log('- 文件名:', data.data.randomStyle.filename);
        console.log('- 样式名:', data.data.randomStyle.styleName);
        console.log('- 路径:', data.data.randomStyle.path);
        console.log('- 总样式数:', data.data.totalStyles);
      } else {
        console.warn('API返回的数据格式不正确');
      }
    } else {
      console.error('API请求失败:', response.statusText);
    }
  } catch (error) {
    console.error('调用随机样式API时出错:', error);
  }

  console.log('=== 随机样式API测试完成 ===');
}

// 在浏览器控制台中可以调用的测试函数
if (typeof window !== 'undefined') {
  (window as any).testWmsStyle = testWmsStyleConfiguration;
  (window as any).testRandomStyleApi = testRandomStyleApi;
  console.log('测试函数已添加到window对象:');
  console.log('- window.testWmsStyle() - 测试WMS样式配置');
  console.log('- window.testRandomStyleApi() - 测试随机样式API');
}
