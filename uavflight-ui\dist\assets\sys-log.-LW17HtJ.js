import{u as k,__tla as v}from"./table.BupIqdAe.js";import{p as T,__tla as z}from"./log.B1rhHpmr.js";import{d as n,z as A,A as D,B as a,e as i,b as e,v as t,D as $,u as d,a as q,F as B,p as C,E as c,G as s,f as u,t as E}from"./vue.CnN__PXn.js";import{q as F,__tla as G}from"./index.C0-0gsfl.js";let p,I=Promise.all([(()=>{try{return v}catch{}})(),(()=>{try{return z}catch{}})(),(()=>{try{return G}catch{}})()]).then(async()=>{let o,_;o={class:"card-header"},_=n({name:"SysLogDashboard"}),p=F(n({..._,setup(P){const h=A(),l=D({pageList:T,descs:["create_time"],pagination:{size:3}});k(l);const f=()=>{h.push("/admin/log/index")};return(m,S)=>{const y=a("el-button"),g=a("el-timeline-item"),b=a("el-timeline"),L=a("el-card");return e(),i(L,{class:"box-card",style:{height:"100%"}},{header:t(()=>[u("div",o,[u("span",null,s(m.$t("home.systemLogsTip")),1),E(y,{link:"",class:"button",text:"",onClick:f},{default:t(()=>[c(s(m.$t("home.moreTip")),1)]),_:1})])]),default:t(()=>[d(l).dataList.length>0?(e(),i(b,{key:0},{default:t(()=>[(e(!0),q(B,null,C(d(l).dataList,(r,x)=>(e(),i(g,{key:x,timestamp:r.createTime},{default:t(()=>[c(s(r.title)+" - "+s(r.remoteAddr),1)]),_:2},1032,["timestamp"]))),128))]),_:1})):$("",!0)]),_:1})}}}),[["__scopeId","data-v-7a20b33d"]])});export{I as __tla,p as default};
