import {
  init_runtime_dom_esm_bundler,
  runtime_dom_esm_bundler_exports
} from "./chunk-SBQ2JIIQ.js";
import {
  __commonJS,
  __esm,
  __export,
  __toCommonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/@intlify/shared/dist/shared.esm-browser.js
var shared_esm_browser_exports = {};
__export(shared_esm_browser_exports, {
  assign: () => assign,
  createEmitter: () => createEmitter,
  deepCopy: () => deepCopy,
  escapeHtml: () => escapeHtml,
  format: () => format,
  friendlyJSONstringify: () => friendlyJSONstringify,
  generateCodeFrame: () => generateCodeFrame,
  generateFormatCacheKey: () => generateFormatCacheKey,
  getGlobalThis: () => getGlobalThis,
  hasOwn: () => hasOwn,
  inBrowser: () => inBrowser,
  incrementer: () => incrementer,
  isArray: () => isArray,
  isBoolean: () => isBoolean,
  isDate: () => isDate,
  isEmptyObject: () => isEmptyObject,
  isFunction: () => isFunction,
  isNumber: () => isNumber,
  isObject: () => isObject,
  isPlainObject: () => isPlainObject,
  isPromise: () => isPromise,
  isRegExp: () => isRegExp,
  isString: () => isString,
  isSymbol: () => isSymbol,
  join: () => join,
  makeSymbol: () => makeSymbol,
  mark: () => mark,
  measure: () => measure,
  objectToString: () => objectToString,
  toDisplayString: () => toDisplayString,
  toTypeString: () => toTypeString,
  warn: () => warn,
  warnOnce: () => warnOnce
});
function format(message, ...args) {
  if (args.length === 1 && isObject(args[0])) {
    args = args[0];
  }
  if (!args || !args.hasOwnProperty) {
    args = {};
  }
  return message.replace(RE_ARGS, (match, identifier) => {
    return args.hasOwnProperty(identifier) ? args[identifier] : "";
  });
}
function escapeHtml(rawText) {
  return rawText.replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&apos;");
}
function hasOwn(obj, key) {
  return hasOwnProperty.call(obj, key);
}
function join(items, separator = "") {
  return items.reduce((str, item, index) => index === 0 ? str + item : str + separator + item, "");
}
function generateCodeFrame(source, start = 0, end = source.length) {
  const lines = source.split(/\r?\n/);
  let count = 0;
  const res = [];
  for (let i = 0; i < lines.length; i++) {
    count += lines[i].length + 1;
    if (count >= start) {
      for (let j = i - RANGE; j <= i + RANGE || end > count; j++) {
        if (j < 0 || j >= lines.length)
          continue;
        const line = j + 1;
        res.push(`${line}${" ".repeat(3 - String(line).length)}|  ${lines[j]}`);
        const lineLength = lines[j].length;
        if (j === i) {
          const pad = start - (count - lineLength) + 1;
          const length = Math.max(1, end > count ? lineLength - pad : end - start);
          res.push(`   |  ` + " ".repeat(pad) + "^".repeat(length));
        } else if (j > i) {
          if (end > count) {
            const length = Math.max(Math.min(end - count, lineLength), 1);
            res.push(`   |  ` + "^".repeat(length));
          }
          count += lineLength + 1;
        }
      }
      break;
    }
  }
  return res.join("\n");
}
function incrementer(code2) {
  let current = code2;
  return () => ++current;
}
function warn(msg, err) {
  if (typeof console !== "undefined") {
    console.warn(`[intlify] ` + msg);
    if (err) {
      console.warn(err.stack);
    }
  }
}
function warnOnce(msg) {
  if (!hasWarned[msg]) {
    hasWarned[msg] = true;
    warn(msg);
  }
}
function createEmitter() {
  const events = /* @__PURE__ */ new Map();
  const emitter = {
    events,
    on(event, handler) {
      const handlers = events.get(event);
      const added = handlers && handlers.push(handler);
      if (!added) {
        events.set(event, [handler]);
      }
    },
    off(event, handler) {
      const handlers = events.get(event);
      if (handlers) {
        handlers.splice(handlers.indexOf(handler) >>> 0, 1);
      }
    },
    emit(event, payload) {
      (events.get(event) || []).slice().map((handler) => handler(payload));
      (events.get("*") || []).slice().map((handler) => handler(event, payload));
    }
  };
  return emitter;
}
function deepCopy(src, des) {
  if (isNotObjectOrIsArray(src) || isNotObjectOrIsArray(des)) {
    throw new Error("Invalid value");
  }
  const stack2 = [{ src, des }];
  while (stack2.length) {
    const { src: src2, des: des2 } = stack2.pop();
    Object.keys(src2).forEach((key) => {
      if (isObject(src2[key]) && !isObject(des2[key])) {
        des2[key] = Array.isArray(src2[key]) ? [] : {};
      }
      if (isNotObjectOrIsArray(des2[key]) || isNotObjectOrIsArray(src2[key])) {
        des2[key] = src2[key];
      } else {
        stack2.push({ src: src2[key], des: des2[key] });
      }
    });
  }
}
var inBrowser, mark, measure, RE_ARGS, makeSymbol, generateFormatCacheKey, friendlyJSONstringify, isNumber, isDate, isRegExp, isEmptyObject, assign, _globalThis, getGlobalThis, hasOwnProperty, isArray, isFunction, isString, isBoolean, isSymbol, isObject, isPromise, objectToString, toTypeString, isPlainObject, toDisplayString, RANGE, hasWarned, isNotObjectOrIsArray;
var init_shared_esm_browser = __esm({
  "node_modules/@intlify/shared/dist/shared.esm-browser.js"() {
    inBrowser = typeof window !== "undefined";
    {
      const perf = inBrowser && window.performance;
      if (perf && perf.mark && perf.measure && perf.clearMarks && // @ts-ignore browser compat
      perf.clearMeasures) {
        mark = (tag) => {
          perf.mark(tag);
        };
        measure = (name, startTag, endTag) => {
          perf.measure(name, startTag, endTag);
          perf.clearMarks(startTag);
          perf.clearMarks(endTag);
        };
      }
    }
    RE_ARGS = /\{([0-9a-zA-Z]+)\}/g;
    makeSymbol = (name, shareable = false) => !shareable ? Symbol(name) : Symbol.for(name);
    generateFormatCacheKey = (locale, key, source) => friendlyJSONstringify({ l: locale, k: key, s: source });
    friendlyJSONstringify = (json) => JSON.stringify(json).replace(/\u2028/g, "\\u2028").replace(/\u2029/g, "\\u2029").replace(/\u0027/g, "\\u0027");
    isNumber = (val) => typeof val === "number" && isFinite(val);
    isDate = (val) => toTypeString(val) === "[object Date]";
    isRegExp = (val) => toTypeString(val) === "[object RegExp]";
    isEmptyObject = (val) => isPlainObject(val) && Object.keys(val).length === 0;
    assign = Object.assign;
    getGlobalThis = () => {
      return _globalThis || (_globalThis = typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : {});
    };
    hasOwnProperty = Object.prototype.hasOwnProperty;
    isArray = Array.isArray;
    isFunction = (val) => typeof val === "function";
    isString = (val) => typeof val === "string";
    isBoolean = (val) => typeof val === "boolean";
    isSymbol = (val) => typeof val === "symbol";
    isObject = (val) => val !== null && typeof val === "object";
    isPromise = (val) => {
      return isObject(val) && isFunction(val.then) && isFunction(val.catch);
    };
    objectToString = Object.prototype.toString;
    toTypeString = (value) => objectToString.call(value);
    isPlainObject = (val) => {
      if (!isObject(val))
        return false;
      const proto = Object.getPrototypeOf(val);
      return proto === null || proto.constructor === Object;
    };
    toDisplayString = (val) => {
      return val == null ? "" : isArray(val) || isPlainObject(val) && val.toString === objectToString ? JSON.stringify(val, null, 2) : String(val);
    };
    RANGE = 2;
    hasWarned = {};
    isNotObjectOrIsArray = (val) => !isObject(val) || isArray(val);
  }
});

// node_modules/@intlify/core-base/dist/core-base.esm-browser.js
var core_base_esm_browser_exports = {};
__export(core_base_esm_browser_exports, {
  CompileErrorCodes: () => CompileErrorCodes,
  CoreErrorCodes: () => CoreErrorCodes,
  CoreWarnCodes: () => CoreWarnCodes,
  DATETIME_FORMAT_OPTIONS_KEYS: () => DATETIME_FORMAT_OPTIONS_KEYS,
  DEFAULT_LOCALE: () => DEFAULT_LOCALE,
  DEFAULT_MESSAGE_DATA_TYPE: () => DEFAULT_MESSAGE_DATA_TYPE,
  MISSING_RESOLVE_VALUE: () => MISSING_RESOLVE_VALUE,
  NOT_REOSLVED: () => NOT_REOSLVED,
  NUMBER_FORMAT_OPTIONS_KEYS: () => NUMBER_FORMAT_OPTIONS_KEYS,
  VERSION: () => VERSION,
  clearCompileCache: () => clearCompileCache,
  clearDateTimeFormat: () => clearDateTimeFormat,
  clearNumberFormat: () => clearNumberFormat,
  compile: () => compile,
  compileToFunction: () => compileToFunction,
  createCompileError: () => createCompileError,
  createCoreContext: () => createCoreContext,
  createCoreError: () => createCoreError,
  createMessageContext: () => createMessageContext,
  datetime: () => datetime,
  fallbackWithLocaleChain: () => fallbackWithLocaleChain,
  fallbackWithSimple: () => fallbackWithSimple,
  getAdditionalMeta: () => getAdditionalMeta,
  getDevToolsHook: () => getDevToolsHook,
  getFallbackContext: () => getFallbackContext,
  getLocale: () => getLocale,
  getWarnMessage: () => getWarnMessage,
  handleMissing: () => handleMissing,
  initI18nDevTools: () => initI18nDevTools,
  isAlmostSameLocale: () => isAlmostSameLocale,
  isImplicitFallback: () => isImplicitFallback,
  isMessageAST: () => isMessageAST,
  isMessageFunction: () => isMessageFunction,
  isTranslateFallbackWarn: () => isTranslateFallbackWarn,
  isTranslateMissingWarn: () => isTranslateMissingWarn,
  number: () => number,
  parse: () => parse,
  parseDateTimeArgs: () => parseDateTimeArgs,
  parseNumberArgs: () => parseNumberArgs,
  parseTranslateArgs: () => parseTranslateArgs,
  registerLocaleFallbacker: () => registerLocaleFallbacker,
  registerMessageCompiler: () => registerMessageCompiler,
  registerMessageResolver: () => registerMessageResolver,
  resolveLocale: () => resolveLocale,
  resolveValue: () => resolveValue,
  resolveWithKeyValue: () => resolveWithKeyValue,
  setAdditionalMeta: () => setAdditionalMeta,
  setDevToolsHook: () => setDevToolsHook,
  setFallbackContext: () => setFallbackContext,
  translate: () => translate,
  translateDevTools: () => translateDevTools,
  updateFallbackLocale: () => updateFallbackLocale
});
function format$1(message, ...args) {
  if (args.length === 1 && isObject2(args[0])) {
    args = args[0];
  }
  if (!args || !args.hasOwnProperty) {
    args = {};
  }
  return message.replace(RE_ARGS2, (match, identifier) => {
    return args.hasOwnProperty(identifier) ? args[identifier] : "";
  });
}
function escapeHtml2(rawText) {
  return rawText.replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&apos;");
}
function join2(items, separator = "") {
  return items.reduce((str, item, index) => index === 0 ? str + item : str + separator + item, "");
}
function generateCodeFrame2(source, start = 0, end = source.length) {
  const lines = source.split(/\r?\n/);
  let count = 0;
  const res = [];
  for (let i = 0; i < lines.length; i++) {
    count += lines[i].length + 1;
    if (count >= start) {
      for (let j = i - RANGE2; j <= i + RANGE2 || end > count; j++) {
        if (j < 0 || j >= lines.length)
          continue;
        const line = j + 1;
        res.push(`${line}${" ".repeat(3 - String(line).length)}|  ${lines[j]}`);
        const lineLength = lines[j].length;
        if (j === i) {
          const pad = start - (count - lineLength) + 1;
          const length = Math.max(1, end > count ? lineLength - pad : end - start);
          res.push(`   |  ` + " ".repeat(pad) + "^".repeat(length));
        } else if (j > i) {
          if (end > count) {
            const length = Math.max(Math.min(end - count, lineLength), 1);
            res.push(`   |  ` + "^".repeat(length));
          }
          count += lineLength + 1;
        }
      }
      break;
    }
  }
  return res.join("\n");
}
function incrementer2(code2) {
  let current = code2;
  return () => ++current;
}
function warn2(msg, err) {
  if (typeof console !== "undefined") {
    console.warn(`[intlify] ` + msg);
    if (err) {
      console.warn(err.stack);
    }
  }
}
function warnOnce2(msg) {
  if (!hasWarned2[msg]) {
    hasWarned2[msg] = true;
    warn2(msg);
  }
}
function createPosition(line, column, offset) {
  return { line, column, offset };
}
function createLocation(start, end, source) {
  const loc = { start, end };
  if (source != null) {
    loc.source = source;
  }
  return loc;
}
function createCompileWarn(code2, loc, ...args) {
  const msg = format$1(warnMessages$1[code2] || "", ...args || []);
  const message = { message: String(msg), code: code2 };
  if (loc) {
    message.location = loc;
  }
  return message;
}
function createCompileError(code2, loc, options = {}) {
  const { domain, messages, args } = options;
  const msg = format$1((messages || errorMessages$1)[code2] || "", ...args || []);
  const error = new SyntaxError(String(msg));
  error.code = code2;
  if (loc) {
    error.location = loc;
  }
  error.domain = domain;
  return error;
}
function defaultOnError(error) {
  throw error;
}
function createScanner(str) {
  const _buf = str;
  let _index = 0;
  let _line = 1;
  let _column = 1;
  let _peekOffset = 0;
  const isCRLF = (index2) => _buf[index2] === CHAR_CR && _buf[index2 + 1] === CHAR_LF;
  const isLF = (index2) => _buf[index2] === CHAR_LF;
  const isPS = (index2) => _buf[index2] === CHAR_PS;
  const isLS = (index2) => _buf[index2] === CHAR_LS;
  const isLineEnd = (index2) => isCRLF(index2) || isLF(index2) || isPS(index2) || isLS(index2);
  const index = () => _index;
  const line = () => _line;
  const column = () => _column;
  const peekOffset = () => _peekOffset;
  const charAt = (offset) => isCRLF(offset) || isPS(offset) || isLS(offset) ? CHAR_LF : _buf[offset];
  const currentChar = () => charAt(_index);
  const currentPeek = () => charAt(_index + _peekOffset);
  function next() {
    _peekOffset = 0;
    if (isLineEnd(_index)) {
      _line++;
      _column = 0;
    }
    if (isCRLF(_index)) {
      _index++;
    }
    _index++;
    _column++;
    return _buf[_index];
  }
  function peek() {
    if (isCRLF(_index + _peekOffset)) {
      _peekOffset++;
    }
    _peekOffset++;
    return _buf[_index + _peekOffset];
  }
  function reset2() {
    _index = 0;
    _line = 1;
    _column = 1;
    _peekOffset = 0;
  }
  function resetPeek(offset = 0) {
    _peekOffset = offset;
  }
  function skipToPeek() {
    const target = _index + _peekOffset;
    while (target !== _index) {
      next();
    }
    _peekOffset = 0;
  }
  return {
    index,
    line,
    column,
    peekOffset,
    charAt,
    currentChar,
    currentPeek,
    next,
    peek,
    reset: reset2,
    resetPeek,
    skipToPeek
  };
}
function createTokenizer(source, options = {}) {
  const location = options.location !== false;
  const _scnr = createScanner(source);
  const currentOffset = () => _scnr.index();
  const currentPosition = () => createPosition(_scnr.line(), _scnr.column(), _scnr.index());
  const _initLoc = currentPosition();
  const _initOffset = currentOffset();
  const _context = {
    currentType: 14,
    offset: _initOffset,
    startLoc: _initLoc,
    endLoc: _initLoc,
    lastType: 14,
    lastOffset: _initOffset,
    lastStartLoc: _initLoc,
    lastEndLoc: _initLoc,
    braceNest: 0,
    inLinked: false,
    text: ""
  };
  const context = () => _context;
  const { onError } = options;
  function emitError2(code2, pos, offset, ...args) {
    const ctx = context();
    pos.column += offset;
    pos.offset += offset;
    if (onError) {
      const loc = location ? createLocation(ctx.startLoc, pos) : null;
      const err = createCompileError(code2, loc, {
        domain: ERROR_DOMAIN$3,
        args
      });
      onError(err);
    }
  }
  function getToken(context2, type, value) {
    context2.endLoc = currentPosition();
    context2.currentType = type;
    const token = { type };
    if (location) {
      token.loc = createLocation(context2.startLoc, context2.endLoc);
    }
    if (value != null) {
      token.value = value;
    }
    return token;
  }
  const getEndToken = (context2) => getToken(
    context2,
    14
    /* TokenTypes.EOF */
  );
  function eat(scnr, ch) {
    if (scnr.currentChar() === ch) {
      scnr.next();
      return ch;
    } else {
      emitError2(CompileErrorCodes.EXPECTED_TOKEN, currentPosition(), 0, ch);
      return "";
    }
  }
  function peekSpaces(scnr) {
    let buf = "";
    while (scnr.currentPeek() === CHAR_SP || scnr.currentPeek() === CHAR_LF) {
      buf += scnr.currentPeek();
      scnr.peek();
    }
    return buf;
  }
  function skipSpaces(scnr) {
    const buf = peekSpaces(scnr);
    scnr.skipToPeek();
    return buf;
  }
  function isIdentifierStart(ch) {
    if (ch === EOF) {
      return false;
    }
    const cc = ch.charCodeAt(0);
    return cc >= 97 && cc <= 122 || // a-z
    cc >= 65 && cc <= 90 || // A-Z
    cc === 95;
  }
  function isNumberStart(ch) {
    if (ch === EOF) {
      return false;
    }
    const cc = ch.charCodeAt(0);
    return cc >= 48 && cc <= 57;
  }
  function isNamedIdentifierStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 2) {
      return false;
    }
    peekSpaces(scnr);
    const ret = isIdentifierStart(scnr.currentPeek());
    scnr.resetPeek();
    return ret;
  }
  function isListIdentifierStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 2) {
      return false;
    }
    peekSpaces(scnr);
    const ch = scnr.currentPeek() === "-" ? scnr.peek() : scnr.currentPeek();
    const ret = isNumberStart(ch);
    scnr.resetPeek();
    return ret;
  }
  function isLiteralStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 2) {
      return false;
    }
    peekSpaces(scnr);
    const ret = scnr.currentPeek() === LITERAL_DELIMITER;
    scnr.resetPeek();
    return ret;
  }
  function isLinkedDotStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 8) {
      return false;
    }
    peekSpaces(scnr);
    const ret = scnr.currentPeek() === ".";
    scnr.resetPeek();
    return ret;
  }
  function isLinkedModifierStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 9) {
      return false;
    }
    peekSpaces(scnr);
    const ret = isIdentifierStart(scnr.currentPeek());
    scnr.resetPeek();
    return ret;
  }
  function isLinkedDelimiterStart(scnr, context2) {
    const { currentType } = context2;
    if (!(currentType === 8 || currentType === 12)) {
      return false;
    }
    peekSpaces(scnr);
    const ret = scnr.currentPeek() === ":";
    scnr.resetPeek();
    return ret;
  }
  function isLinkedReferStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 10) {
      return false;
    }
    const fn = () => {
      const ch = scnr.currentPeek();
      if (ch === "{") {
        return isIdentifierStart(scnr.peek());
      } else if (ch === "@" || ch === "%" || ch === "|" || ch === ":" || ch === "." || ch === CHAR_SP || !ch) {
        return false;
      } else if (ch === CHAR_LF) {
        scnr.peek();
        return fn();
      } else {
        return isTextStart(scnr, false);
      }
    };
    const ret = fn();
    scnr.resetPeek();
    return ret;
  }
  function isPluralStart(scnr) {
    peekSpaces(scnr);
    const ret = scnr.currentPeek() === "|";
    scnr.resetPeek();
    return ret;
  }
  function detectModuloStart(scnr) {
    const spaces = peekSpaces(scnr);
    const ret = scnr.currentPeek() === "%" && scnr.peek() === "{";
    scnr.resetPeek();
    return {
      isModulo: ret,
      hasSpace: spaces.length > 0
    };
  }
  function isTextStart(scnr, reset2 = true) {
    const fn = (hasSpace = false, prev = "", detectModulo = false) => {
      const ch = scnr.currentPeek();
      if (ch === "{") {
        return prev === "%" ? false : hasSpace;
      } else if (ch === "@" || !ch) {
        return prev === "%" ? true : hasSpace;
      } else if (ch === "%") {
        scnr.peek();
        return fn(hasSpace, "%", true);
      } else if (ch === "|") {
        return prev === "%" || detectModulo ? true : !(prev === CHAR_SP || prev === CHAR_LF);
      } else if (ch === CHAR_SP) {
        scnr.peek();
        return fn(true, CHAR_SP, detectModulo);
      } else if (ch === CHAR_LF) {
        scnr.peek();
        return fn(true, CHAR_LF, detectModulo);
      } else {
        return true;
      }
    };
    const ret = fn();
    reset2 && scnr.resetPeek();
    return ret;
  }
  function takeChar(scnr, fn) {
    const ch = scnr.currentChar();
    if (ch === EOF) {
      return EOF;
    }
    if (fn(ch)) {
      scnr.next();
      return ch;
    }
    return null;
  }
  function isIdentifier(ch) {
    const cc = ch.charCodeAt(0);
    return cc >= 97 && cc <= 122 || // a-z
    cc >= 65 && cc <= 90 || // A-Z
    cc >= 48 && cc <= 57 || // 0-9
    cc === 95 || // _
    cc === 36;
  }
  function takeIdentifierChar(scnr) {
    return takeChar(scnr, isIdentifier);
  }
  function isNamedIdentifier(ch) {
    const cc = ch.charCodeAt(0);
    return cc >= 97 && cc <= 122 || // a-z
    cc >= 65 && cc <= 90 || // A-Z
    cc >= 48 && cc <= 57 || // 0-9
    cc === 95 || // _
    cc === 36 || // $
    cc === 45;
  }
  function takeNamedIdentifierChar(scnr) {
    return takeChar(scnr, isNamedIdentifier);
  }
  function isDigit(ch) {
    const cc = ch.charCodeAt(0);
    return cc >= 48 && cc <= 57;
  }
  function takeDigit(scnr) {
    return takeChar(scnr, isDigit);
  }
  function isHexDigit(ch) {
    const cc = ch.charCodeAt(0);
    return cc >= 48 && cc <= 57 || // 0-9
    cc >= 65 && cc <= 70 || // A-F
    cc >= 97 && cc <= 102;
  }
  function takeHexDigit(scnr) {
    return takeChar(scnr, isHexDigit);
  }
  function getDigits(scnr) {
    let ch = "";
    let num = "";
    while (ch = takeDigit(scnr)) {
      num += ch;
    }
    return num;
  }
  function readModulo(scnr) {
    skipSpaces(scnr);
    const ch = scnr.currentChar();
    if (ch !== "%") {
      emitError2(CompileErrorCodes.EXPECTED_TOKEN, currentPosition(), 0, ch);
    }
    scnr.next();
    return "%";
  }
  function readText(scnr) {
    let buf = "";
    while (true) {
      const ch = scnr.currentChar();
      if (ch === "{" || ch === "}" || ch === "@" || ch === "|" || !ch) {
        break;
      } else if (ch === "%") {
        if (isTextStart(scnr)) {
          buf += ch;
          scnr.next();
        } else {
          break;
        }
      } else if (ch === CHAR_SP || ch === CHAR_LF) {
        if (isTextStart(scnr)) {
          buf += ch;
          scnr.next();
        } else if (isPluralStart(scnr)) {
          break;
        } else {
          buf += ch;
          scnr.next();
        }
      } else {
        buf += ch;
        scnr.next();
      }
    }
    return buf;
  }
  function readNamedIdentifier(scnr) {
    skipSpaces(scnr);
    let ch = "";
    let name = "";
    while (ch = takeNamedIdentifierChar(scnr)) {
      name += ch;
    }
    if (scnr.currentChar() === EOF) {
      emitError2(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);
    }
    return name;
  }
  function readListIdentifier(scnr) {
    skipSpaces(scnr);
    let value = "";
    if (scnr.currentChar() === "-") {
      scnr.next();
      value += `-${getDigits(scnr)}`;
    } else {
      value += getDigits(scnr);
    }
    if (scnr.currentChar() === EOF) {
      emitError2(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);
    }
    return value;
  }
  function isLiteral2(ch) {
    return ch !== LITERAL_DELIMITER && ch !== CHAR_LF;
  }
  function readLiteral(scnr) {
    skipSpaces(scnr);
    eat(scnr, `'`);
    let ch = "";
    let literal = "";
    while (ch = takeChar(scnr, isLiteral2)) {
      if (ch === "\\") {
        literal += readEscapeSequence(scnr);
      } else {
        literal += ch;
      }
    }
    const current = scnr.currentChar();
    if (current === CHAR_LF || current === EOF) {
      emitError2(CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER, currentPosition(), 0);
      if (current === CHAR_LF) {
        scnr.next();
        eat(scnr, `'`);
      }
      return literal;
    }
    eat(scnr, `'`);
    return literal;
  }
  function readEscapeSequence(scnr) {
    const ch = scnr.currentChar();
    switch (ch) {
      case "\\":
      case `'`:
        scnr.next();
        return `\\${ch}`;
      case "u":
        return readUnicodeEscapeSequence(scnr, ch, 4);
      case "U":
        return readUnicodeEscapeSequence(scnr, ch, 6);
      default:
        emitError2(CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE, currentPosition(), 0, ch);
        return "";
    }
  }
  function readUnicodeEscapeSequence(scnr, unicode, digits) {
    eat(scnr, unicode);
    let sequence = "";
    for (let i = 0; i < digits; i++) {
      const ch = takeHexDigit(scnr);
      if (!ch) {
        emitError2(CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE, currentPosition(), 0, `\\${unicode}${sequence}${scnr.currentChar()}`);
        break;
      }
      sequence += ch;
    }
    return `\\${unicode}${sequence}`;
  }
  function isInvalidIdentifier(ch) {
    return ch !== "{" && ch !== "}" && ch !== CHAR_SP && ch !== CHAR_LF;
  }
  function readInvalidIdentifier(scnr) {
    skipSpaces(scnr);
    let ch = "";
    let identifiers = "";
    while (ch = takeChar(scnr, isInvalidIdentifier)) {
      identifiers += ch;
    }
    return identifiers;
  }
  function readLinkedModifier(scnr) {
    let ch = "";
    let name = "";
    while (ch = takeIdentifierChar(scnr)) {
      name += ch;
    }
    return name;
  }
  function readLinkedRefer(scnr) {
    const fn = (buf) => {
      const ch = scnr.currentChar();
      if (ch === "{" || ch === "%" || ch === "@" || ch === "|" || ch === "(" || ch === ")" || !ch) {
        return buf;
      } else if (ch === CHAR_SP) {
        return buf;
      } else if (ch === CHAR_LF || ch === DOT) {
        buf += ch;
        scnr.next();
        return fn(buf);
      } else {
        buf += ch;
        scnr.next();
        return fn(buf);
      }
    };
    return fn("");
  }
  function readPlural(scnr) {
    skipSpaces(scnr);
    const plural = eat(
      scnr,
      "|"
      /* TokenChars.Pipe */
    );
    skipSpaces(scnr);
    return plural;
  }
  function readTokenInPlaceholder(scnr, context2) {
    let token = null;
    const ch = scnr.currentChar();
    switch (ch) {
      case "{":
        if (context2.braceNest >= 1) {
          emitError2(CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER, currentPosition(), 0);
        }
        scnr.next();
        token = getToken(
          context2,
          2,
          "{"
          /* TokenChars.BraceLeft */
        );
        skipSpaces(scnr);
        context2.braceNest++;
        return token;
      case "}":
        if (context2.braceNest > 0 && context2.currentType === 2) {
          emitError2(CompileErrorCodes.EMPTY_PLACEHOLDER, currentPosition(), 0);
        }
        scnr.next();
        token = getToken(
          context2,
          3,
          "}"
          /* TokenChars.BraceRight */
        );
        context2.braceNest--;
        context2.braceNest > 0 && skipSpaces(scnr);
        if (context2.inLinked && context2.braceNest === 0) {
          context2.inLinked = false;
        }
        return token;
      case "@":
        if (context2.braceNest > 0) {
          emitError2(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);
        }
        token = readTokenInLinked(scnr, context2) || getEndToken(context2);
        context2.braceNest = 0;
        return token;
      default: {
        let validNamedIdentifier = true;
        let validListIdentifier = true;
        let validLiteral = true;
        if (isPluralStart(scnr)) {
          if (context2.braceNest > 0) {
            emitError2(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);
          }
          token = getToken(context2, 1, readPlural(scnr));
          context2.braceNest = 0;
          context2.inLinked = false;
          return token;
        }
        if (context2.braceNest > 0 && (context2.currentType === 5 || context2.currentType === 6 || context2.currentType === 7)) {
          emitError2(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);
          context2.braceNest = 0;
          return readToken(scnr, context2);
        }
        if (validNamedIdentifier = isNamedIdentifierStart(scnr, context2)) {
          token = getToken(context2, 5, readNamedIdentifier(scnr));
          skipSpaces(scnr);
          return token;
        }
        if (validListIdentifier = isListIdentifierStart(scnr, context2)) {
          token = getToken(context2, 6, readListIdentifier(scnr));
          skipSpaces(scnr);
          return token;
        }
        if (validLiteral = isLiteralStart(scnr, context2)) {
          token = getToken(context2, 7, readLiteral(scnr));
          skipSpaces(scnr);
          return token;
        }
        if (!validNamedIdentifier && !validListIdentifier && !validLiteral) {
          token = getToken(context2, 13, readInvalidIdentifier(scnr));
          emitError2(CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER, currentPosition(), 0, token.value);
          skipSpaces(scnr);
          return token;
        }
        break;
      }
    }
    return token;
  }
  function readTokenInLinked(scnr, context2) {
    const { currentType } = context2;
    let token = null;
    const ch = scnr.currentChar();
    if ((currentType === 8 || currentType === 9 || currentType === 12 || currentType === 10) && (ch === CHAR_LF || ch === CHAR_SP)) {
      emitError2(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);
    }
    switch (ch) {
      case "@":
        scnr.next();
        token = getToken(
          context2,
          8,
          "@"
          /* TokenChars.LinkedAlias */
        );
        context2.inLinked = true;
        return token;
      case ".":
        skipSpaces(scnr);
        scnr.next();
        return getToken(
          context2,
          9,
          "."
          /* TokenChars.LinkedDot */
        );
      case ":":
        skipSpaces(scnr);
        scnr.next();
        return getToken(
          context2,
          10,
          ":"
          /* TokenChars.LinkedDelimiter */
        );
      default:
        if (isPluralStart(scnr)) {
          token = getToken(context2, 1, readPlural(scnr));
          context2.braceNest = 0;
          context2.inLinked = false;
          return token;
        }
        if (isLinkedDotStart(scnr, context2) || isLinkedDelimiterStart(scnr, context2)) {
          skipSpaces(scnr);
          return readTokenInLinked(scnr, context2);
        }
        if (isLinkedModifierStart(scnr, context2)) {
          skipSpaces(scnr);
          return getToken(context2, 12, readLinkedModifier(scnr));
        }
        if (isLinkedReferStart(scnr, context2)) {
          skipSpaces(scnr);
          if (ch === "{") {
            return readTokenInPlaceholder(scnr, context2) || token;
          } else {
            return getToken(context2, 11, readLinkedRefer(scnr));
          }
        }
        if (currentType === 8) {
          emitError2(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);
        }
        context2.braceNest = 0;
        context2.inLinked = false;
        return readToken(scnr, context2);
    }
  }
  function readToken(scnr, context2) {
    let token = {
      type: 14
      /* TokenTypes.EOF */
    };
    if (context2.braceNest > 0) {
      return readTokenInPlaceholder(scnr, context2) || getEndToken(context2);
    }
    if (context2.inLinked) {
      return readTokenInLinked(scnr, context2) || getEndToken(context2);
    }
    const ch = scnr.currentChar();
    switch (ch) {
      case "{":
        return readTokenInPlaceholder(scnr, context2) || getEndToken(context2);
      case "}":
        emitError2(CompileErrorCodes.UNBALANCED_CLOSING_BRACE, currentPosition(), 0);
        scnr.next();
        return getToken(
          context2,
          3,
          "}"
          /* TokenChars.BraceRight */
        );
      case "@":
        return readTokenInLinked(scnr, context2) || getEndToken(context2);
      default: {
        if (isPluralStart(scnr)) {
          token = getToken(context2, 1, readPlural(scnr));
          context2.braceNest = 0;
          context2.inLinked = false;
          return token;
        }
        const { isModulo, hasSpace } = detectModuloStart(scnr);
        if (isModulo) {
          return hasSpace ? getToken(context2, 0, readText(scnr)) : getToken(context2, 4, readModulo(scnr));
        }
        if (isTextStart(scnr)) {
          return getToken(context2, 0, readText(scnr));
        }
        break;
      }
    }
    return token;
  }
  function nextToken() {
    const { currentType, offset, startLoc, endLoc } = _context;
    _context.lastType = currentType;
    _context.lastOffset = offset;
    _context.lastStartLoc = startLoc;
    _context.lastEndLoc = endLoc;
    _context.offset = currentOffset();
    _context.startLoc = currentPosition();
    if (_scnr.currentChar() === EOF) {
      return getToken(
        _context,
        14
        /* TokenTypes.EOF */
      );
    }
    return readToken(_scnr, _context);
  }
  return {
    nextToken,
    currentOffset,
    currentPosition,
    context
  };
}
function fromEscapeSequence(match, codePoint4, codePoint6) {
  switch (match) {
    case `\\\\`:
      return `\\`;
    case `\\'`:
      return `'`;
    default: {
      const codePoint = parseInt(codePoint4 || codePoint6, 16);
      if (codePoint <= 55295 || codePoint >= 57344) {
        return String.fromCodePoint(codePoint);
      }
      return "�";
    }
  }
}
function createParser(options = {}) {
  const location = options.location !== false;
  const { onError, onWarn } = options;
  function emitError2(tokenzer, code2, start, offset, ...args) {
    const end = tokenzer.currentPosition();
    end.offset += offset;
    end.column += offset;
    if (onError) {
      const loc = location ? createLocation(start, end) : null;
      const err = createCompileError(code2, loc, {
        domain: ERROR_DOMAIN$2,
        args
      });
      onError(err);
    }
  }
  function emitWarn(tokenzer, code2, start, offset, ...args) {
    const end = tokenzer.currentPosition();
    end.offset += offset;
    end.column += offset;
    if (onWarn) {
      const loc = location ? createLocation(start, end) : null;
      onWarn(createCompileWarn(code2, loc, args));
    }
  }
  function startNode(type, offset, loc) {
    const node = { type };
    if (location) {
      node.start = offset;
      node.end = offset;
      node.loc = { start: loc, end: loc };
    }
    return node;
  }
  function endNode(node, offset, pos, type) {
    if (type) {
      node.type = type;
    }
    if (location) {
      node.end = offset;
      if (node.loc) {
        node.loc.end = pos;
      }
    }
  }
  function parseText(tokenizer2, value) {
    const context = tokenizer2.context();
    const node = startNode(3, context.offset, context.startLoc);
    node.value = value;
    endNode(node, tokenizer2.currentOffset(), tokenizer2.currentPosition());
    return node;
  }
  function parseList(tokenizer2, index) {
    const context = tokenizer2.context();
    const { lastOffset: offset, lastStartLoc: loc } = context;
    const node = startNode(5, offset, loc);
    node.index = parseInt(index, 10);
    tokenizer2.nextToken();
    endNode(node, tokenizer2.currentOffset(), tokenizer2.currentPosition());
    return node;
  }
  function parseNamed(tokenizer2, key, modulo) {
    const context = tokenizer2.context();
    const { lastOffset: offset, lastStartLoc: loc } = context;
    const node = startNode(4, offset, loc);
    node.key = key;
    if (modulo === true) {
      node.modulo = true;
    }
    tokenizer2.nextToken();
    endNode(node, tokenizer2.currentOffset(), tokenizer2.currentPosition());
    return node;
  }
  function parseLiteral(tokenizer2, value) {
    const context = tokenizer2.context();
    const { lastOffset: offset, lastStartLoc: loc } = context;
    const node = startNode(9, offset, loc);
    node.value = value.replace(KNOWN_ESCAPES, fromEscapeSequence);
    tokenizer2.nextToken();
    endNode(node, tokenizer2.currentOffset(), tokenizer2.currentPosition());
    return node;
  }
  function parseLinkedModifier(tokenizer2) {
    const token = tokenizer2.nextToken();
    const context = tokenizer2.context();
    const { lastOffset: offset, lastStartLoc: loc } = context;
    const node = startNode(8, offset, loc);
    if (token.type !== 12) {
      emitError2(tokenizer2, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER, context.lastStartLoc, 0);
      node.value = "";
      endNode(node, offset, loc);
      return {
        nextConsumeToken: token,
        node
      };
    }
    if (token.value == null) {
      emitError2(tokenizer2, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
    }
    node.value = token.value || "";
    endNode(node, tokenizer2.currentOffset(), tokenizer2.currentPosition());
    return {
      node
    };
  }
  function parseLinkedKey(tokenizer2, value) {
    const context = tokenizer2.context();
    const node = startNode(7, context.offset, context.startLoc);
    node.value = value;
    endNode(node, tokenizer2.currentOffset(), tokenizer2.currentPosition());
    return node;
  }
  function parseLinked(tokenizer2) {
    const context = tokenizer2.context();
    const linkedNode = startNode(6, context.offset, context.startLoc);
    let token = tokenizer2.nextToken();
    if (token.type === 9) {
      const parsed = parseLinkedModifier(tokenizer2);
      linkedNode.modifier = parsed.node;
      token = parsed.nextConsumeToken || tokenizer2.nextToken();
    }
    if (token.type !== 10) {
      emitError2(tokenizer2, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
    }
    token = tokenizer2.nextToken();
    if (token.type === 2) {
      token = tokenizer2.nextToken();
    }
    switch (token.type) {
      case 11:
        if (token.value == null) {
          emitError2(tokenizer2, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
        }
        linkedNode.key = parseLinkedKey(tokenizer2, token.value || "");
        break;
      case 5:
        if (token.value == null) {
          emitError2(tokenizer2, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
        }
        linkedNode.key = parseNamed(tokenizer2, token.value || "");
        break;
      case 6:
        if (token.value == null) {
          emitError2(tokenizer2, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
        }
        linkedNode.key = parseList(tokenizer2, token.value || "");
        break;
      case 7:
        if (token.value == null) {
          emitError2(tokenizer2, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
        }
        linkedNode.key = parseLiteral(tokenizer2, token.value || "");
        break;
      default: {
        emitError2(tokenizer2, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY, context.lastStartLoc, 0);
        const nextContext = tokenizer2.context();
        const emptyLinkedKeyNode = startNode(7, nextContext.offset, nextContext.startLoc);
        emptyLinkedKeyNode.value = "";
        endNode(emptyLinkedKeyNode, nextContext.offset, nextContext.startLoc);
        linkedNode.key = emptyLinkedKeyNode;
        endNode(linkedNode, nextContext.offset, nextContext.startLoc);
        return {
          nextConsumeToken: token,
          node: linkedNode
        };
      }
    }
    endNode(linkedNode, tokenizer2.currentOffset(), tokenizer2.currentPosition());
    return {
      node: linkedNode
    };
  }
  function parseMessage(tokenizer2) {
    const context = tokenizer2.context();
    const startOffset = context.currentType === 1 ? tokenizer2.currentOffset() : context.offset;
    const startLoc = context.currentType === 1 ? context.endLoc : context.startLoc;
    const node = startNode(2, startOffset, startLoc);
    node.items = [];
    let nextToken = null;
    let modulo = null;
    do {
      const token = nextToken || tokenizer2.nextToken();
      nextToken = null;
      switch (token.type) {
        case 0:
          if (token.value == null) {
            emitError2(tokenizer2, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
          }
          node.items.push(parseText(tokenizer2, token.value || ""));
          break;
        case 6:
          if (token.value == null) {
            emitError2(tokenizer2, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
          }
          node.items.push(parseList(tokenizer2, token.value || ""));
          break;
        case 4:
          modulo = true;
          break;
        case 5:
          if (token.value == null) {
            emitError2(tokenizer2, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
          }
          node.items.push(parseNamed(tokenizer2, token.value || "", !!modulo));
          if (modulo) {
            emitWarn(tokenizer2, CompileWarnCodes.USE_MODULO_SYNTAX, context.lastStartLoc, 0, getTokenCaption(token));
            modulo = null;
          }
          break;
        case 7:
          if (token.value == null) {
            emitError2(tokenizer2, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
          }
          node.items.push(parseLiteral(tokenizer2, token.value || ""));
          break;
        case 8: {
          const parsed = parseLinked(tokenizer2);
          node.items.push(parsed.node);
          nextToken = parsed.nextConsumeToken || null;
          break;
        }
      }
    } while (context.currentType !== 14 && context.currentType !== 1);
    const endOffset = context.currentType === 1 ? context.lastOffset : tokenizer2.currentOffset();
    const endLoc = context.currentType === 1 ? context.lastEndLoc : tokenizer2.currentPosition();
    endNode(node, endOffset, endLoc);
    return node;
  }
  function parsePlural(tokenizer2, offset, loc, msgNode) {
    const context = tokenizer2.context();
    let hasEmptyMessage = msgNode.items.length === 0;
    const node = startNode(1, offset, loc);
    node.cases = [];
    node.cases.push(msgNode);
    do {
      const msg = parseMessage(tokenizer2);
      if (!hasEmptyMessage) {
        hasEmptyMessage = msg.items.length === 0;
      }
      node.cases.push(msg);
    } while (context.currentType !== 14);
    if (hasEmptyMessage) {
      emitError2(tokenizer2, CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL, loc, 0);
    }
    endNode(node, tokenizer2.currentOffset(), tokenizer2.currentPosition());
    return node;
  }
  function parseResource(tokenizer2) {
    const context = tokenizer2.context();
    const { offset, startLoc } = context;
    const msgNode = parseMessage(tokenizer2);
    if (context.currentType === 14) {
      return msgNode;
    } else {
      return parsePlural(tokenizer2, offset, startLoc, msgNode);
    }
  }
  function parse3(source) {
    const tokenizer2 = createTokenizer(source, assign2({}, options));
    const context = tokenizer2.context();
    const node = startNode(0, context.offset, context.startLoc);
    if (location && node.loc) {
      node.loc.source = source;
    }
    node.body = parseResource(tokenizer2);
    if (options.onCacheKey) {
      node.cacheKey = options.onCacheKey(source);
    }
    if (context.currentType !== 14) {
      emitError2(tokenizer2, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, source[context.offset] || "");
    }
    endNode(node, tokenizer2.currentOffset(), tokenizer2.currentPosition());
    return node;
  }
  return { parse: parse3 };
}
function getTokenCaption(token) {
  if (token.type === 14) {
    return "EOF";
  }
  const name = (token.value || "").replace(/\r?\n/gu, "\\n");
  return name.length > 10 ? name.slice(0, 9) + "…" : name;
}
function createTransformer(ast, options = {}) {
  const _context = {
    ast,
    helpers: /* @__PURE__ */ new Set()
  };
  const context = () => _context;
  const helper = (name) => {
    _context.helpers.add(name);
    return name;
  };
  return { context, helper };
}
function traverseNodes(nodes, transformer) {
  for (let i = 0; i < nodes.length; i++) {
    traverseNode(nodes[i], transformer);
  }
}
function traverseNode(node, transformer) {
  switch (node.type) {
    case 1:
      traverseNodes(node.cases, transformer);
      transformer.helper(
        "plural"
        /* HelperNameMap.PLURAL */
      );
      break;
    case 2:
      traverseNodes(node.items, transformer);
      break;
    case 6: {
      const linked = node;
      traverseNode(linked.key, transformer);
      transformer.helper(
        "linked"
        /* HelperNameMap.LINKED */
      );
      transformer.helper(
        "type"
        /* HelperNameMap.TYPE */
      );
      break;
    }
    case 5:
      transformer.helper(
        "interpolate"
        /* HelperNameMap.INTERPOLATE */
      );
      transformer.helper(
        "list"
        /* HelperNameMap.LIST */
      );
      break;
    case 4:
      transformer.helper(
        "interpolate"
        /* HelperNameMap.INTERPOLATE */
      );
      transformer.helper(
        "named"
        /* HelperNameMap.NAMED */
      );
      break;
  }
}
function transform(ast, options = {}) {
  const transformer = createTransformer(ast);
  transformer.helper(
    "normalize"
    /* HelperNameMap.NORMALIZE */
  );
  ast.body && traverseNode(ast.body, transformer);
  const context = transformer.context();
  ast.helpers = Array.from(context.helpers);
}
function optimize(ast) {
  const body = ast.body;
  if (body.type === 2) {
    optimizeMessageNode(body);
  } else {
    body.cases.forEach((c) => optimizeMessageNode(c));
  }
  return ast;
}
function optimizeMessageNode(message) {
  if (message.items.length === 1) {
    const item = message.items[0];
    if (item.type === 3 || item.type === 9) {
      message.static = item.value;
      delete item.value;
    }
  } else {
    const values = [];
    for (let i = 0; i < message.items.length; i++) {
      const item = message.items[i];
      if (!(item.type === 3 || item.type === 9)) {
        break;
      }
      if (item.value == null) {
        break;
      }
      values.push(item.value);
    }
    if (values.length === message.items.length) {
      message.static = join2(values);
      for (let i = 0; i < message.items.length; i++) {
        const item = message.items[i];
        if (item.type === 3 || item.type === 9) {
          delete item.value;
        }
      }
    }
  }
}
function minify(node) {
  node.t = node.type;
  switch (node.type) {
    case 0: {
      const resource = node;
      minify(resource.body);
      resource.b = resource.body;
      delete resource.body;
      break;
    }
    case 1: {
      const plural = node;
      const cases = plural.cases;
      for (let i = 0; i < cases.length; i++) {
        minify(cases[i]);
      }
      plural.c = cases;
      delete plural.cases;
      break;
    }
    case 2: {
      const message = node;
      const items = message.items;
      for (let i = 0; i < items.length; i++) {
        minify(items[i]);
      }
      message.i = items;
      delete message.items;
      if (message.static) {
        message.s = message.static;
        delete message.static;
      }
      break;
    }
    case 3:
    case 9:
    case 8:
    case 7: {
      const valueNode = node;
      if (valueNode.value) {
        valueNode.v = valueNode.value;
        delete valueNode.value;
      }
      break;
    }
    case 6: {
      const linked = node;
      minify(linked.key);
      linked.k = linked.key;
      delete linked.key;
      if (linked.modifier) {
        minify(linked.modifier);
        linked.m = linked.modifier;
        delete linked.modifier;
      }
      break;
    }
    case 5: {
      const list = node;
      list.i = list.index;
      delete list.index;
      break;
    }
    case 4: {
      const named = node;
      named.k = named.key;
      delete named.key;
      break;
    }
    default: {
      throw createCompileError(CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE, null, {
        domain: ERROR_DOMAIN$1,
        args: [node.type]
      });
    }
  }
  delete node.type;
}
function createCodeGenerator(ast, options) {
  const { sourceMap, filename, breakLineCode, needIndent: _needIndent } = options;
  const location = options.location !== false;
  const _context = {
    filename,
    code: "",
    column: 1,
    line: 1,
    offset: 0,
    map: void 0,
    breakLineCode,
    needIndent: _needIndent,
    indentLevel: 0
  };
  if (location && ast.loc) {
    _context.source = ast.loc.source;
  }
  const context = () => _context;
  function push(code2, node) {
    _context.code += code2;
  }
  function _newline(n, withBreakLine = true) {
    const _breakLineCode = withBreakLine ? breakLineCode : "";
    push(_needIndent ? _breakLineCode + `  `.repeat(n) : _breakLineCode);
  }
  function indent(withNewLine = true) {
    const level = ++_context.indentLevel;
    withNewLine && _newline(level);
  }
  function deindent(withNewLine = true) {
    const level = --_context.indentLevel;
    withNewLine && _newline(level);
  }
  function newline() {
    _newline(_context.indentLevel);
  }
  const helper = (key) => `_${key}`;
  const needIndent = () => _context.needIndent;
  return {
    context,
    push,
    indent,
    deindent,
    newline,
    helper,
    needIndent
  };
}
function generateLinkedNode(generator, node) {
  const { helper } = generator;
  generator.push(`${helper(
    "linked"
    /* HelperNameMap.LINKED */
  )}(`);
  generateNode(generator, node.key);
  if (node.modifier) {
    generator.push(`, `);
    generateNode(generator, node.modifier);
    generator.push(`, _type`);
  } else {
    generator.push(`, undefined, _type`);
  }
  generator.push(`)`);
}
function generateMessageNode(generator, node) {
  const { helper, needIndent } = generator;
  generator.push(`${helper(
    "normalize"
    /* HelperNameMap.NORMALIZE */
  )}([`);
  generator.indent(needIndent());
  const length = node.items.length;
  for (let i = 0; i < length; i++) {
    generateNode(generator, node.items[i]);
    if (i === length - 1) {
      break;
    }
    generator.push(", ");
  }
  generator.deindent(needIndent());
  generator.push("])");
}
function generatePluralNode(generator, node) {
  const { helper, needIndent } = generator;
  if (node.cases.length > 1) {
    generator.push(`${helper(
      "plural"
      /* HelperNameMap.PLURAL */
    )}([`);
    generator.indent(needIndent());
    const length = node.cases.length;
    for (let i = 0; i < length; i++) {
      generateNode(generator, node.cases[i]);
      if (i === length - 1) {
        break;
      }
      generator.push(", ");
    }
    generator.deindent(needIndent());
    generator.push(`])`);
  }
}
function generateResource(generator, node) {
  if (node.body) {
    generateNode(generator, node.body);
  } else {
    generator.push("null");
  }
}
function generateNode(generator, node) {
  const { helper } = generator;
  switch (node.type) {
    case 0:
      generateResource(generator, node);
      break;
    case 1:
      generatePluralNode(generator, node);
      break;
    case 2:
      generateMessageNode(generator, node);
      break;
    case 6:
      generateLinkedNode(generator, node);
      break;
    case 8:
      generator.push(JSON.stringify(node.value), node);
      break;
    case 7:
      generator.push(JSON.stringify(node.value), node);
      break;
    case 5:
      generator.push(`${helper(
        "interpolate"
        /* HelperNameMap.INTERPOLATE */
      )}(${helper(
        "list"
        /* HelperNameMap.LIST */
      )}(${node.index}))`, node);
      break;
    case 4:
      generator.push(`${helper(
        "interpolate"
        /* HelperNameMap.INTERPOLATE */
      )}(${helper(
        "named"
        /* HelperNameMap.NAMED */
      )}(${JSON.stringify(node.key)}))`, node);
      break;
    case 9:
      generator.push(JSON.stringify(node.value), node);
      break;
    case 3:
      generator.push(JSON.stringify(node.value), node);
      break;
    default: {
      throw createCompileError(CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE, null, {
        domain: ERROR_DOMAIN,
        args: [node.type]
      });
    }
  }
}
function baseCompile$1(source, options = {}) {
  const assignedOptions = assign2({}, options);
  const jit = !!assignedOptions.jit;
  const enalbeMinify = !!assignedOptions.minify;
  const enambeOptimize = assignedOptions.optimize == null ? true : assignedOptions.optimize;
  const parser = createParser(assignedOptions);
  const ast = parser.parse(source);
  if (!jit) {
    transform(ast, assignedOptions);
    return generate(ast, assignedOptions);
  } else {
    enambeOptimize && optimize(ast);
    enalbeMinify && minify(ast);
    return { ast, code: "" };
  }
}
function isLiteral(exp) {
  return literalValueRE.test(exp);
}
function stripQuotes(str) {
  const a = str.charCodeAt(0);
  const b = str.charCodeAt(str.length - 1);
  return a === b && (a === 34 || a === 39) ? str.slice(1, -1) : str;
}
function getPathCharType(ch) {
  if (ch === void 0 || ch === null) {
    return "o";
  }
  const code2 = ch.charCodeAt(0);
  switch (code2) {
    case 91:
    case 93:
    case 46:
    case 34:
    case 39:
      return ch;
    case 95:
    case 36:
    case 45:
      return "i";
    case 9:
    case 10:
    case 13:
    case 160:
    case 65279:
    case 8232:
    case 8233:
      return "w";
  }
  return "i";
}
function formatSubPath(path) {
  const trimmed = path.trim();
  if (path.charAt(0) === "0" && isNaN(parseInt(path))) {
    return false;
  }
  return isLiteral(trimmed) ? stripQuotes(trimmed) : "*" + trimmed;
}
function parse(path) {
  const keys = [];
  let index = -1;
  let mode = 0;
  let subPathDepth = 0;
  let c;
  let key;
  let newChar;
  let type;
  let transition;
  let action;
  let typeMap;
  const actions = [];
  actions[
    0
    /* Actions.APPEND */
  ] = () => {
    if (key === void 0) {
      key = newChar;
    } else {
      key += newChar;
    }
  };
  actions[
    1
    /* Actions.PUSH */
  ] = () => {
    if (key !== void 0) {
      keys.push(key);
      key = void 0;
    }
  };
  actions[
    2
    /* Actions.INC_SUB_PATH_DEPTH */
  ] = () => {
    actions[
      0
      /* Actions.APPEND */
    ]();
    subPathDepth++;
  };
  actions[
    3
    /* Actions.PUSH_SUB_PATH */
  ] = () => {
    if (subPathDepth > 0) {
      subPathDepth--;
      mode = 4;
      actions[
        0
        /* Actions.APPEND */
      ]();
    } else {
      subPathDepth = 0;
      if (key === void 0) {
        return false;
      }
      key = formatSubPath(key);
      if (key === false) {
        return false;
      } else {
        actions[
          1
          /* Actions.PUSH */
        ]();
      }
    }
  };
  function maybeUnescapeQuote() {
    const nextChar = path[index + 1];
    if (mode === 5 && nextChar === "'" || mode === 6 && nextChar === '"') {
      index++;
      newChar = "\\" + nextChar;
      actions[
        0
        /* Actions.APPEND */
      ]();
      return true;
    }
  }
  while (mode !== null) {
    index++;
    c = path[index];
    if (c === "\\" && maybeUnescapeQuote()) {
      continue;
    }
    type = getPathCharType(c);
    typeMap = pathStateMachine[mode];
    transition = typeMap[type] || typeMap[
      "l"
      /* PathCharTypes.ELSE */
    ] || 8;
    if (transition === 8) {
      return;
    }
    mode = transition[0];
    if (transition[1] !== void 0) {
      action = actions[transition[1]];
      if (action) {
        newChar = c;
        if (action() === false) {
          return;
        }
      }
    }
    if (mode === 7) {
      return keys;
    }
  }
}
function resolveWithKeyValue(obj, path) {
  return isObject2(obj) ? obj[path] : null;
}
function resolveValue(obj, path) {
  if (!isObject2(obj)) {
    return null;
  }
  let hit = cache.get(path);
  if (!hit) {
    hit = parse(path);
    if (hit) {
      cache.set(path, hit);
    }
  }
  if (!hit) {
    return null;
  }
  const len = hit.length;
  let last = obj;
  let i = 0;
  while (i < len) {
    const val = last[hit[i]];
    if (val === void 0) {
      return null;
    }
    if (isFunction2(last)) {
      return null;
    }
    last = val;
    i++;
  }
  return last;
}
function pluralDefault(choice, choicesLength) {
  choice = Math.abs(choice);
  if (choicesLength === 2) {
    return choice ? choice > 1 ? 1 : 0 : 1;
  }
  return choice ? Math.min(choice, 2) : 0;
}
function getPluralIndex(options) {
  const index = isNumber2(options.pluralIndex) ? options.pluralIndex : -1;
  return options.named && (isNumber2(options.named.count) || isNumber2(options.named.n)) ? isNumber2(options.named.count) ? options.named.count : isNumber2(options.named.n) ? options.named.n : index : index;
}
function normalizeNamed(pluralIndex, props) {
  if (!props.count) {
    props.count = pluralIndex;
  }
  if (!props.n) {
    props.n = pluralIndex;
  }
}
function createMessageContext(options = {}) {
  const locale = options.locale;
  const pluralIndex = getPluralIndex(options);
  const pluralRule = isObject2(options.pluralRules) && isString2(locale) && isFunction2(options.pluralRules[locale]) ? options.pluralRules[locale] : pluralDefault;
  const orgPluralRule = isObject2(options.pluralRules) && isString2(locale) && isFunction2(options.pluralRules[locale]) ? pluralDefault : void 0;
  const plural = (messages) => {
    return messages[pluralRule(pluralIndex, messages.length, orgPluralRule)];
  };
  const _list = options.list || [];
  const list = (index) => _list[index];
  const _named = options.named || {};
  isNumber2(options.pluralIndex) && normalizeNamed(pluralIndex, _named);
  const named = (key) => _named[key];
  function message(key) {
    const msg = isFunction2(options.messages) ? options.messages(key) : isObject2(options.messages) ? options.messages[key] : false;
    return !msg ? options.parent ? options.parent.message(key) : DEFAULT_MESSAGE : msg;
  }
  const _modifier = (name) => options.modifiers ? options.modifiers[name] : DEFAULT_MODIFIER;
  const normalize = isPlainObject2(options.processor) && isFunction2(options.processor.normalize) ? options.processor.normalize : DEFAULT_NORMALIZE;
  const interpolate = isPlainObject2(options.processor) && isFunction2(options.processor.interpolate) ? options.processor.interpolate : DEFAULT_INTERPOLATE;
  const type = isPlainObject2(options.processor) && isString2(options.processor.type) ? options.processor.type : DEFAULT_MESSAGE_DATA_TYPE;
  const linked = (key, ...args) => {
    const [arg1, arg2] = args;
    let type2 = "text";
    let modifier = "";
    if (args.length === 1) {
      if (isObject2(arg1)) {
        modifier = arg1.modifier || modifier;
        type2 = arg1.type || type2;
      } else if (isString2(arg1)) {
        modifier = arg1 || modifier;
      }
    } else if (args.length === 2) {
      if (isString2(arg1)) {
        modifier = arg1 || modifier;
      }
      if (isString2(arg2)) {
        type2 = arg2 || type2;
      }
    }
    const ret = message(key)(ctx);
    const msg = (
      // The message in vnode resolved with linked are returned as an array by processor.nomalize
      type2 === "vnode" && isArray2(ret) && modifier ? ret[0] : ret
    );
    return modifier ? _modifier(modifier)(msg, type2) : msg;
  };
  const ctx = {
    [
      "list"
      /* HelperNameMap.LIST */
    ]: list,
    [
      "named"
      /* HelperNameMap.NAMED */
    ]: named,
    [
      "plural"
      /* HelperNameMap.PLURAL */
    ]: plural,
    [
      "linked"
      /* HelperNameMap.LINKED */
    ]: linked,
    [
      "message"
      /* HelperNameMap.MESSAGE */
    ]: message,
    [
      "type"
      /* HelperNameMap.TYPE */
    ]: type,
    [
      "interpolate"
      /* HelperNameMap.INTERPOLATE */
    ]: interpolate,
    [
      "normalize"
      /* HelperNameMap.NORMALIZE */
    ]: normalize,
    [
      "values"
      /* HelperNameMap.VALUES */
    ]: assign2({}, _list, _named)
  };
  return ctx;
}
function setDevToolsHook(hook) {
  devtools = hook;
}
function getDevToolsHook() {
  return devtools;
}
function initI18nDevTools(i18n, version, meta) {
  devtools && devtools.emit("i18n:init", {
    timestamp: Date.now(),
    i18n,
    version,
    meta
  });
}
function createDevToolsHook(hook) {
  return (payloads) => devtools && devtools.emit(hook, payloads);
}
function getWarnMessage(code2, ...args) {
  return format$1(warnMessages[code2], ...args);
}
function createCoreError(code2) {
  return createCompileError(code2, null, { messages: errorMessages });
}
function getLocale(context, options) {
  return options.locale != null ? resolveLocale(options.locale) : resolveLocale(context.locale);
}
function resolveLocale(locale) {
  if (isString2(locale)) {
    return locale;
  } else {
    if (isFunction2(locale)) {
      if (locale.resolvedOnce && _resolveLocale != null) {
        return _resolveLocale;
      } else if (locale.constructor.name === "Function") {
        const resolve = locale();
        if (isPromise2(resolve)) {
          throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE);
        }
        return _resolveLocale = resolve;
      } else {
        throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION);
      }
    } else {
      throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE);
    }
  }
}
function fallbackWithSimple(ctx, fallback, start) {
  return [.../* @__PURE__ */ new Set([
    start,
    ...isArray2(fallback) ? fallback : isObject2(fallback) ? Object.keys(fallback) : isString2(fallback) ? [fallback] : [start]
  ])];
}
function fallbackWithLocaleChain(ctx, fallback, start) {
  const startLocale = isString2(start) ? start : DEFAULT_LOCALE;
  const context = ctx;
  if (!context.__localeChainCache) {
    context.__localeChainCache = /* @__PURE__ */ new Map();
  }
  let chain = context.__localeChainCache.get(startLocale);
  if (!chain) {
    chain = [];
    let block = [start];
    while (isArray2(block)) {
      block = appendBlockToChain(chain, block, fallback);
    }
    const defaults = isArray2(fallback) || !isPlainObject2(fallback) ? fallback : fallback["default"] ? fallback["default"] : null;
    block = isString2(defaults) ? [defaults] : defaults;
    if (isArray2(block)) {
      appendBlockToChain(chain, block, false);
    }
    context.__localeChainCache.set(startLocale, chain);
  }
  return chain;
}
function appendBlockToChain(chain, block, blocks) {
  let follow = true;
  for (let i = 0; i < block.length && isBoolean2(follow); i++) {
    const locale = block[i];
    if (isString2(locale)) {
      follow = appendLocaleToChain(chain, block[i], blocks);
    }
  }
  return follow;
}
function appendLocaleToChain(chain, locale, blocks) {
  let follow;
  const tokens = locale.split("-");
  do {
    const target = tokens.join("-");
    follow = appendItemToChain(chain, target, blocks);
    tokens.splice(-1, 1);
  } while (tokens.length && follow === true);
  return follow;
}
function appendItemToChain(chain, target, blocks) {
  let follow = false;
  if (!chain.includes(target)) {
    follow = true;
    if (target) {
      follow = target[target.length - 1] !== "!";
      const locale = target.replace(/!/g, "");
      chain.push(locale);
      if ((isArray2(blocks) || isPlainObject2(blocks)) && blocks[locale]) {
        follow = blocks[locale];
      }
    }
  }
  return follow;
}
function getDefaultLinkedModifiers() {
  return {
    upper: (val, type) => {
      return type === "text" && isString2(val) ? val.toUpperCase() : type === "vnode" && isObject2(val) && "__v_isVNode" in val ? val.children.toUpperCase() : val;
    },
    lower: (val, type) => {
      return type === "text" && isString2(val) ? val.toLowerCase() : type === "vnode" && isObject2(val) && "__v_isVNode" in val ? val.children.toLowerCase() : val;
    },
    capitalize: (val, type) => {
      return type === "text" && isString2(val) ? capitalize(val) : type === "vnode" && isObject2(val) && "__v_isVNode" in val ? capitalize(val.children) : val;
    }
  };
}
function registerMessageCompiler(compiler) {
  _compiler = compiler;
}
function registerMessageResolver(resolver) {
  _resolver = resolver;
}
function registerLocaleFallbacker(fallbacker) {
  _fallbacker = fallbacker;
}
function createCoreContext(options = {}) {
  const onWarn = isFunction2(options.onWarn) ? options.onWarn : warn2;
  const version = isString2(options.version) ? options.version : VERSION;
  const locale = isString2(options.locale) || isFunction2(options.locale) ? options.locale : DEFAULT_LOCALE;
  const _locale = isFunction2(locale) ? DEFAULT_LOCALE : locale;
  const fallbackLocale = isArray2(options.fallbackLocale) || isPlainObject2(options.fallbackLocale) || isString2(options.fallbackLocale) || options.fallbackLocale === false ? options.fallbackLocale : _locale;
  const messages = isPlainObject2(options.messages) ? options.messages : { [_locale]: {} };
  const datetimeFormats = isPlainObject2(options.datetimeFormats) ? options.datetimeFormats : { [_locale]: {} };
  const numberFormats = isPlainObject2(options.numberFormats) ? options.numberFormats : { [_locale]: {} };
  const modifiers = assign2({}, options.modifiers || {}, getDefaultLinkedModifiers());
  const pluralRules = options.pluralRules || {};
  const missing = isFunction2(options.missing) ? options.missing : null;
  const missingWarn = isBoolean2(options.missingWarn) || isRegExp2(options.missingWarn) ? options.missingWarn : true;
  const fallbackWarn = isBoolean2(options.fallbackWarn) || isRegExp2(options.fallbackWarn) ? options.fallbackWarn : true;
  const fallbackFormat = !!options.fallbackFormat;
  const unresolving = !!options.unresolving;
  const postTranslation = isFunction2(options.postTranslation) ? options.postTranslation : null;
  const processor = isPlainObject2(options.processor) ? options.processor : null;
  const warnHtmlMessage = isBoolean2(options.warnHtmlMessage) ? options.warnHtmlMessage : true;
  const escapeParameter = !!options.escapeParameter;
  const messageCompiler = isFunction2(options.messageCompiler) ? options.messageCompiler : _compiler;
  if (isFunction2(options.messageCompiler)) {
    warnOnce2(getWarnMessage(CoreWarnCodes.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER));
  }
  const messageResolver = isFunction2(options.messageResolver) ? options.messageResolver : _resolver || resolveWithKeyValue;
  const localeFallbacker = isFunction2(options.localeFallbacker) ? options.localeFallbacker : _fallbacker || fallbackWithSimple;
  const fallbackContext = isObject2(options.fallbackContext) ? options.fallbackContext : void 0;
  const internalOptions = options;
  const __datetimeFormatters = isObject2(internalOptions.__datetimeFormatters) ? internalOptions.__datetimeFormatters : /* @__PURE__ */ new Map();
  const __numberFormatters = isObject2(internalOptions.__numberFormatters) ? internalOptions.__numberFormatters : /* @__PURE__ */ new Map();
  const __meta = isObject2(internalOptions.__meta) ? internalOptions.__meta : {};
  _cid++;
  const context = {
    version,
    cid: _cid,
    locale,
    fallbackLocale,
    messages,
    modifiers,
    pluralRules,
    missing,
    missingWarn,
    fallbackWarn,
    fallbackFormat,
    unresolving,
    postTranslation,
    processor,
    warnHtmlMessage,
    escapeParameter,
    messageCompiler,
    messageResolver,
    localeFallbacker,
    fallbackContext,
    onWarn,
    __meta
  };
  {
    context.datetimeFormats = datetimeFormats;
    context.numberFormats = numberFormats;
    context.__datetimeFormatters = __datetimeFormatters;
    context.__numberFormatters = __numberFormatters;
  }
  {
    context.__v_emitter = internalOptions.__v_emitter != null ? internalOptions.__v_emitter : void 0;
  }
  {
    initI18nDevTools(context, version, __meta);
  }
  return context;
}
function isTranslateFallbackWarn(fallback, key) {
  return fallback instanceof RegExp ? fallback.test(key) : fallback;
}
function isTranslateMissingWarn(missing, key) {
  return missing instanceof RegExp ? missing.test(key) : missing;
}
function handleMissing(context, key, locale, missingWarn, type) {
  const { missing, onWarn } = context;
  {
    const emitter = context.__v_emitter;
    if (emitter) {
      emitter.emit("missing", {
        locale,
        key,
        type,
        groupId: `${type}:${key}`
      });
    }
  }
  if (missing !== null) {
    const ret = missing(context, locale, key, type);
    return isString2(ret) ? ret : key;
  } else {
    if (isTranslateMissingWarn(missingWarn, key)) {
      onWarn(getWarnMessage(CoreWarnCodes.NOT_FOUND_KEY, { key, locale }));
    }
    return key;
  }
}
function updateFallbackLocale(ctx, locale, fallback) {
  const context = ctx;
  context.__localeChainCache = /* @__PURE__ */ new Map();
  ctx.localeFallbacker(ctx, fallback, locale);
}
function isAlmostSameLocale(locale, compareLocale) {
  if (locale === compareLocale)
    return false;
  return locale.split("-")[0] === compareLocale.split("-")[0];
}
function isImplicitFallback(targetLocale, locales) {
  const index = locales.indexOf(targetLocale);
  if (index === -1) {
    return false;
  }
  for (let i = index + 1; i < locales.length; i++) {
    if (isAlmostSameLocale(targetLocale, locales[i])) {
      return true;
    }
  }
  return false;
}
function format2(ast) {
  const msg = (ctx) => formatParts(ctx, ast);
  return msg;
}
function formatParts(ctx, ast) {
  const body = ast.b || ast.body;
  if ((body.t || body.type) === 1) {
    const plural = body;
    const cases = plural.c || plural.cases;
    return ctx.plural(cases.reduce((messages, c) => [
      ...messages,
      formatMessageParts(ctx, c)
    ], []));
  } else {
    return formatMessageParts(ctx, body);
  }
}
function formatMessageParts(ctx, node) {
  const _static = node.s || node.static;
  if (_static) {
    return ctx.type === "text" ? _static : ctx.normalize([_static]);
  } else {
    const messages = (node.i || node.items).reduce((acm, c) => [...acm, formatMessagePart(ctx, c)], []);
    return ctx.normalize(messages);
  }
}
function formatMessagePart(ctx, node) {
  const type = node.t || node.type;
  switch (type) {
    case 3: {
      const text = node;
      return text.v || text.value;
    }
    case 9: {
      const literal = node;
      return literal.v || literal.value;
    }
    case 4: {
      const named = node;
      return ctx.interpolate(ctx.named(named.k || named.key));
    }
    case 5: {
      const list = node;
      return ctx.interpolate(ctx.list(list.i != null ? list.i : list.index));
    }
    case 6: {
      const linked = node;
      const modifier = linked.m || linked.modifier;
      return ctx.linked(formatMessagePart(ctx, linked.k || linked.key), modifier ? formatMessagePart(ctx, modifier) : void 0, ctx.type);
    }
    case 7: {
      const linkedKey = node;
      return linkedKey.v || linkedKey.value;
    }
    case 8: {
      const linkedModifier = node;
      return linkedModifier.v || linkedModifier.value;
    }
    default:
      throw new Error(`unhandled node type on format message part: ${type}`);
  }
}
function checkHtmlMessage(source, warnHtmlMessage) {
  if (warnHtmlMessage && detectHtmlTag(source)) {
    warn2(format$1(WARN_MESSAGE, { source }));
  }
}
function onCompileWarn(_warn) {
  if (_warn.code === CompileWarnCodes.USE_MODULO_SYNTAX) {
    warn2(`The use of named interpolation with modulo syntax is deprecated. It will be removed in v10.
reference: https://vue-i18n.intlify.dev/guide/essentials/syntax#rails-i18n-format 
(message compiler warning message: ${_warn.message})`);
  }
}
function clearCompileCache() {
  compileCache = /* @__PURE__ */ Object.create(null);
}
function baseCompile(message, options = {}) {
  let detectError = false;
  const onError = options.onError || defaultOnError;
  options.onError = (err) => {
    detectError = true;
    onError(err);
  };
  return { ...baseCompile$1(message, options), detectError };
}
function compile(message, context) {
  {
    context.onWarn = onCompileWarn;
  }
  if (isString2(message)) {
    const warnHtmlMessage = isBoolean2(context.warnHtmlMessage) ? context.warnHtmlMessage : true;
    checkHtmlMessage(message, warnHtmlMessage);
    const onCacheKey = context.onCacheKey || defaultOnCacheKey;
    const cacheKey = onCacheKey(message);
    const cached = compileCache[cacheKey];
    if (cached) {
      return cached;
    }
    const { ast, detectError } = baseCompile(message, {
      ...context,
      location: true,
      jit: true
    });
    const msg = format2(ast);
    return !detectError ? compileCache[cacheKey] = msg : msg;
  } else {
    if (!isMessageAST(message)) {
      warn2(`the message that is resolve with key '${context.key}' is not supported for jit compilation`);
      return () => message;
    }
    const cacheKey = message.cacheKey;
    if (cacheKey) {
      const cached = compileCache[cacheKey];
      if (cached) {
        return cached;
      }
      return compileCache[cacheKey] = format2(message);
    } else {
      return format2(message);
    }
  }
}
function translate(context, ...args) {
  const { fallbackFormat, postTranslation, unresolving, messageCompiler, fallbackLocale, messages } = context;
  const [key, options] = parseTranslateArgs(...args);
  const missingWarn = isBoolean2(options.missingWarn) ? options.missingWarn : context.missingWarn;
  const fallbackWarn = isBoolean2(options.fallbackWarn) ? options.fallbackWarn : context.fallbackWarn;
  const escapeParameter = isBoolean2(options.escapeParameter) ? options.escapeParameter : context.escapeParameter;
  const resolvedMessage = !!options.resolvedMessage;
  const defaultMsgOrKey = isString2(options.default) || isBoolean2(options.default) ? !isBoolean2(options.default) ? options.default : !messageCompiler ? () => key : key : fallbackFormat ? !messageCompiler ? () => key : key : "";
  const enableDefaultMsg = fallbackFormat || defaultMsgOrKey !== "";
  const locale = getLocale(context, options);
  escapeParameter && escapeParams(options);
  let [formatScope, targetLocale, message] = !resolvedMessage ? resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn) : [
    key,
    locale,
    messages[locale] || {}
  ];
  let format3 = formatScope;
  let cacheBaseKey = key;
  if (!resolvedMessage && !(isString2(format3) || isMessageAST(format3) || isMessageFunction(format3))) {
    if (enableDefaultMsg) {
      format3 = defaultMsgOrKey;
      cacheBaseKey = format3;
    }
  }
  if (!resolvedMessage && (!(isString2(format3) || isMessageAST(format3) || isMessageFunction(format3)) || !isString2(targetLocale))) {
    return unresolving ? NOT_REOSLVED : key;
  }
  if (isString2(format3) && context.messageCompiler == null) {
    warn2(`The message format compilation is not supported in this build. Because message compiler isn't included. You need to pre-compilation all message format. So translate function return '${key}'.`);
    return key;
  }
  let occurred = false;
  const onError = () => {
    occurred = true;
  };
  const msg = !isMessageFunction(format3) ? compileMessageFormat(context, key, targetLocale, format3, cacheBaseKey, onError) : format3;
  if (occurred) {
    return format3;
  }
  const ctxOptions = getMessageContextOptions(context, targetLocale, message, options);
  const msgContext = createMessageContext(ctxOptions);
  const messaged = evaluateMessage(context, msg, msgContext);
  const ret = postTranslation ? postTranslation(messaged, key) : messaged;
  {
    const payloads = {
      timestamp: Date.now(),
      key: isString2(key) ? key : isMessageFunction(format3) ? format3.key : "",
      locale: targetLocale || (isMessageFunction(format3) ? format3.locale : ""),
      format: isString2(format3) ? format3 : isMessageFunction(format3) ? format3.source : "",
      message: ret
    };
    payloads.meta = assign2({}, context.__meta, getAdditionalMeta() || {});
    translateDevTools(payloads);
  }
  return ret;
}
function escapeParams(options) {
  if (isArray2(options.list)) {
    options.list = options.list.map((item) => isString2(item) ? escapeHtml2(item) : item);
  } else if (isObject2(options.named)) {
    Object.keys(options.named).forEach((key) => {
      if (isString2(options.named[key])) {
        options.named[key] = escapeHtml2(options.named[key]);
      }
    });
  }
}
function resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn) {
  const { messages, onWarn, messageResolver: resolveValue2, localeFallbacker } = context;
  const locales = localeFallbacker(context, fallbackLocale, locale);
  let message = {};
  let targetLocale;
  let format3 = null;
  let from = locale;
  let to = null;
  const type = "translate";
  for (let i = 0; i < locales.length; i++) {
    targetLocale = to = locales[i];
    if (locale !== targetLocale && !isAlmostSameLocale(locale, targetLocale) && isTranslateFallbackWarn(fallbackWarn, key)) {
      onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_TRANSLATE, {
        key,
        target: targetLocale
      }));
    }
    if (locale !== targetLocale) {
      const emitter = context.__v_emitter;
      if (emitter) {
        emitter.emit("fallback", {
          type,
          key,
          from,
          to,
          groupId: `${type}:${key}`
        });
      }
    }
    message = messages[targetLocale] || {};
    let start = null;
    let startTag;
    let endTag;
    if (inBrowser2) {
      start = window.performance.now();
      startTag = "intlify-message-resolve-start";
      endTag = "intlify-message-resolve-end";
      mark2 && mark2(startTag);
    }
    if ((format3 = resolveValue2(message, key)) === null) {
      format3 = message[key];
    }
    if (inBrowser2) {
      const end = window.performance.now();
      const emitter = context.__v_emitter;
      if (emitter && start && format3) {
        emitter.emit("message-resolve", {
          type: "message-resolve",
          key,
          message: format3,
          time: end - start,
          groupId: `${type}:${key}`
        });
      }
      if (startTag && endTag && mark2 && measure2) {
        mark2(endTag);
        measure2("intlify message resolve", startTag, endTag);
      }
    }
    if (isString2(format3) || isMessageAST(format3) || isMessageFunction(format3)) {
      break;
    }
    if (!isImplicitFallback(targetLocale, locales)) {
      const missingRet = handleMissing(
        context,
        // eslint-disable-line @typescript-eslint/no-explicit-any
        key,
        targetLocale,
        missingWarn,
        type
      );
      if (missingRet !== key) {
        format3 = missingRet;
      }
    }
    from = to;
  }
  return [format3, targetLocale, message];
}
function compileMessageFormat(context, key, targetLocale, format3, cacheBaseKey, onError) {
  const { messageCompiler, warnHtmlMessage } = context;
  if (isMessageFunction(format3)) {
    const msg2 = format3;
    msg2.locale = msg2.locale || targetLocale;
    msg2.key = msg2.key || key;
    return msg2;
  }
  if (messageCompiler == null) {
    const msg2 = () => format3;
    msg2.locale = targetLocale;
    msg2.key = key;
    return msg2;
  }
  let start = null;
  let startTag;
  let endTag;
  if (inBrowser2) {
    start = window.performance.now();
    startTag = "intlify-message-compilation-start";
    endTag = "intlify-message-compilation-end";
    mark2 && mark2(startTag);
  }
  const msg = messageCompiler(format3, getCompileContext(context, targetLocale, cacheBaseKey, format3, warnHtmlMessage, onError));
  if (inBrowser2) {
    const end = window.performance.now();
    const emitter = context.__v_emitter;
    if (emitter && start) {
      emitter.emit("message-compilation", {
        type: "message-compilation",
        message: format3,
        time: end - start,
        groupId: `${"translate"}:${key}`
      });
    }
    if (startTag && endTag && mark2 && measure2) {
      mark2(endTag);
      measure2("intlify message compilation", startTag, endTag);
    }
  }
  msg.locale = targetLocale;
  msg.key = key;
  msg.source = format3;
  return msg;
}
function evaluateMessage(context, msg, msgCtx) {
  let start = null;
  let startTag;
  let endTag;
  if (inBrowser2) {
    start = window.performance.now();
    startTag = "intlify-message-evaluation-start";
    endTag = "intlify-message-evaluation-end";
    mark2 && mark2(startTag);
  }
  const messaged = msg(msgCtx);
  if (inBrowser2) {
    const end = window.performance.now();
    const emitter = context.__v_emitter;
    if (emitter && start) {
      emitter.emit("message-evaluation", {
        type: "message-evaluation",
        value: messaged,
        time: end - start,
        groupId: `${"translate"}:${msg.key}`
      });
    }
    if (startTag && endTag && mark2 && measure2) {
      mark2(endTag);
      measure2("intlify message evaluation", startTag, endTag);
    }
  }
  return messaged;
}
function parseTranslateArgs(...args) {
  const [arg1, arg2, arg3] = args;
  const options = {};
  if (!isString2(arg1) && !isNumber2(arg1) && !isMessageFunction(arg1) && !isMessageAST(arg1)) {
    throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);
  }
  const key = isNumber2(arg1) ? String(arg1) : isMessageFunction(arg1) ? arg1 : arg1;
  if (isNumber2(arg2)) {
    options.plural = arg2;
  } else if (isString2(arg2)) {
    options.default = arg2;
  } else if (isPlainObject2(arg2) && !isEmptyObject2(arg2)) {
    options.named = arg2;
  } else if (isArray2(arg2)) {
    options.list = arg2;
  }
  if (isNumber2(arg3)) {
    options.plural = arg3;
  } else if (isString2(arg3)) {
    options.default = arg3;
  } else if (isPlainObject2(arg3)) {
    assign2(options, arg3);
  }
  return [key, options];
}
function getCompileContext(context, locale, key, source, warnHtmlMessage, onError) {
  return {
    locale,
    key,
    warnHtmlMessage,
    onError: (err) => {
      onError && onError(err);
      {
        const _source = getSourceForCodeFrame(source);
        const message = `Message compilation error: ${err.message}`;
        const codeFrame = err.location && _source && generateCodeFrame2(_source, err.location.start.offset, err.location.end.offset);
        const emitter = context.__v_emitter;
        if (emitter && _source) {
          emitter.emit("compile-error", {
            message: _source,
            error: err.message,
            start: err.location && err.location.start.offset,
            end: err.location && err.location.end.offset,
            groupId: `${"translate"}:${key}`
          });
        }
        console.error(codeFrame ? `${message}
${codeFrame}` : message);
      }
    },
    onCacheKey: (source2) => generateFormatCacheKey2(locale, key, source2)
  };
}
function getSourceForCodeFrame(source) {
  if (isString2(source)) {
    return source;
  } else {
    if (source.loc && source.loc.source) {
      return source.loc.source;
    }
  }
}
function getMessageContextOptions(context, locale, message, options) {
  const { modifiers, pluralRules, messageResolver: resolveValue2, fallbackLocale, fallbackWarn, missingWarn, fallbackContext } = context;
  const resolveMessage = (key) => {
    let val = resolveValue2(message, key);
    if (val == null && fallbackContext) {
      const [, , message2] = resolveMessageFormat(fallbackContext, key, locale, fallbackLocale, fallbackWarn, missingWarn);
      val = resolveValue2(message2, key);
    }
    if (isString2(val) || isMessageAST(val)) {
      let occurred = false;
      const onError = () => {
        occurred = true;
      };
      const msg = compileMessageFormat(context, key, locale, val, key, onError);
      return !occurred ? msg : NOOP_MESSAGE_FUNCTION;
    } else if (isMessageFunction(val)) {
      return val;
    } else {
      return NOOP_MESSAGE_FUNCTION;
    }
  };
  const ctxOptions = {
    locale,
    modifiers,
    pluralRules,
    messages: resolveMessage
  };
  if (context.processor) {
    ctxOptions.processor = context.processor;
  }
  if (options.list) {
    ctxOptions.list = options.list;
  }
  if (options.named) {
    ctxOptions.named = options.named;
  }
  if (isNumber2(options.plural)) {
    ctxOptions.pluralIndex = options.plural;
  }
  return ctxOptions;
}
function datetime(context, ...args) {
  const { datetimeFormats, unresolving, fallbackLocale, onWarn, localeFallbacker } = context;
  const { __datetimeFormatters } = context;
  if (!Availabilities.dateTimeFormat) {
    onWarn(getWarnMessage(CoreWarnCodes.CANNOT_FORMAT_DATE));
    return MISSING_RESOLVE_VALUE;
  }
  const [key, value, options, overrides] = parseDateTimeArgs(...args);
  const missingWarn = isBoolean2(options.missingWarn) ? options.missingWarn : context.missingWarn;
  const fallbackWarn = isBoolean2(options.fallbackWarn) ? options.fallbackWarn : context.fallbackWarn;
  const part = !!options.part;
  const locale = getLocale(context, options);
  const locales = localeFallbacker(
    context,
    // eslint-disable-line @typescript-eslint/no-explicit-any
    fallbackLocale,
    locale
  );
  if (!isString2(key) || key === "") {
    return new Intl.DateTimeFormat(locale, overrides).format(value);
  }
  let datetimeFormat = {};
  let targetLocale;
  let format3 = null;
  let from = locale;
  let to = null;
  const type = "datetime format";
  for (let i = 0; i < locales.length; i++) {
    targetLocale = to = locales[i];
    if (locale !== targetLocale && isTranslateFallbackWarn(fallbackWarn, key)) {
      onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_DATE_FORMAT, {
        key,
        target: targetLocale
      }));
    }
    if (locale !== targetLocale) {
      const emitter = context.__v_emitter;
      if (emitter) {
        emitter.emit("fallback", {
          type,
          key,
          from,
          to,
          groupId: `${type}:${key}`
        });
      }
    }
    datetimeFormat = datetimeFormats[targetLocale] || {};
    format3 = datetimeFormat[key];
    if (isPlainObject2(format3))
      break;
    handleMissing(context, key, targetLocale, missingWarn, type);
    from = to;
  }
  if (!isPlainObject2(format3) || !isString2(targetLocale)) {
    return unresolving ? NOT_REOSLVED : key;
  }
  let id = `${targetLocale}__${key}`;
  if (!isEmptyObject2(overrides)) {
    id = `${id}__${JSON.stringify(overrides)}`;
  }
  let formatter = __datetimeFormatters.get(id);
  if (!formatter) {
    formatter = new Intl.DateTimeFormat(targetLocale, assign2({}, format3, overrides));
    __datetimeFormatters.set(id, formatter);
  }
  return !part ? formatter.format(value) : formatter.formatToParts(value);
}
function parseDateTimeArgs(...args) {
  const [arg1, arg2, arg3, arg4] = args;
  const options = {};
  let overrides = {};
  let value;
  if (isString2(arg1)) {
    const matches = arg1.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);
    if (!matches) {
      throw createCoreError(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);
    }
    const dateTime = matches[3] ? matches[3].trim().startsWith("T") ? `${matches[1].trim()}${matches[3].trim()}` : `${matches[1].trim()}T${matches[3].trim()}` : matches[1].trim();
    value = new Date(dateTime);
    try {
      value.toISOString();
    } catch (e) {
      throw createCoreError(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);
    }
  } else if (isDate2(arg1)) {
    if (isNaN(arg1.getTime())) {
      throw createCoreError(CoreErrorCodes.INVALID_DATE_ARGUMENT);
    }
    value = arg1;
  } else if (isNumber2(arg1)) {
    value = arg1;
  } else {
    throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);
  }
  if (isString2(arg2)) {
    options.key = arg2;
  } else if (isPlainObject2(arg2)) {
    Object.keys(arg2).forEach((key) => {
      if (DATETIME_FORMAT_OPTIONS_KEYS.includes(key)) {
        overrides[key] = arg2[key];
      } else {
        options[key] = arg2[key];
      }
    });
  }
  if (isString2(arg3)) {
    options.locale = arg3;
  } else if (isPlainObject2(arg3)) {
    overrides = arg3;
  }
  if (isPlainObject2(arg4)) {
    overrides = arg4;
  }
  return [options.key || "", value, options, overrides];
}
function clearDateTimeFormat(ctx, locale, format3) {
  const context = ctx;
  for (const key in format3) {
    const id = `${locale}__${key}`;
    if (!context.__datetimeFormatters.has(id)) {
      continue;
    }
    context.__datetimeFormatters.delete(id);
  }
}
function number(context, ...args) {
  const { numberFormats, unresolving, fallbackLocale, onWarn, localeFallbacker } = context;
  const { __numberFormatters } = context;
  if (!Availabilities.numberFormat) {
    onWarn(getWarnMessage(CoreWarnCodes.CANNOT_FORMAT_NUMBER));
    return MISSING_RESOLVE_VALUE;
  }
  const [key, value, options, overrides] = parseNumberArgs(...args);
  const missingWarn = isBoolean2(options.missingWarn) ? options.missingWarn : context.missingWarn;
  const fallbackWarn = isBoolean2(options.fallbackWarn) ? options.fallbackWarn : context.fallbackWarn;
  const part = !!options.part;
  const locale = getLocale(context, options);
  const locales = localeFallbacker(
    context,
    // eslint-disable-line @typescript-eslint/no-explicit-any
    fallbackLocale,
    locale
  );
  if (!isString2(key) || key === "") {
    return new Intl.NumberFormat(locale, overrides).format(value);
  }
  let numberFormat = {};
  let targetLocale;
  let format3 = null;
  let from = locale;
  let to = null;
  const type = "number format";
  for (let i = 0; i < locales.length; i++) {
    targetLocale = to = locales[i];
    if (locale !== targetLocale && isTranslateFallbackWarn(fallbackWarn, key)) {
      onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT, {
        key,
        target: targetLocale
      }));
    }
    if (locale !== targetLocale) {
      const emitter = context.__v_emitter;
      if (emitter) {
        emitter.emit("fallback", {
          type,
          key,
          from,
          to,
          groupId: `${type}:${key}`
        });
      }
    }
    numberFormat = numberFormats[targetLocale] || {};
    format3 = numberFormat[key];
    if (isPlainObject2(format3))
      break;
    handleMissing(context, key, targetLocale, missingWarn, type);
    from = to;
  }
  if (!isPlainObject2(format3) || !isString2(targetLocale)) {
    return unresolving ? NOT_REOSLVED : key;
  }
  let id = `${targetLocale}__${key}`;
  if (!isEmptyObject2(overrides)) {
    id = `${id}__${JSON.stringify(overrides)}`;
  }
  let formatter = __numberFormatters.get(id);
  if (!formatter) {
    formatter = new Intl.NumberFormat(targetLocale, assign2({}, format3, overrides));
    __numberFormatters.set(id, formatter);
  }
  return !part ? formatter.format(value) : formatter.formatToParts(value);
}
function parseNumberArgs(...args) {
  const [arg1, arg2, arg3, arg4] = args;
  const options = {};
  let overrides = {};
  if (!isNumber2(arg1)) {
    throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);
  }
  const value = arg1;
  if (isString2(arg2)) {
    options.key = arg2;
  } else if (isPlainObject2(arg2)) {
    Object.keys(arg2).forEach((key) => {
      if (NUMBER_FORMAT_OPTIONS_KEYS.includes(key)) {
        overrides[key] = arg2[key];
      } else {
        options[key] = arg2[key];
      }
    });
  }
  if (isString2(arg3)) {
    options.locale = arg3;
  } else if (isPlainObject2(arg3)) {
    overrides = arg3;
  }
  if (isPlainObject2(arg4)) {
    overrides = arg4;
  }
  return [options.key || "", value, options, overrides];
}
function clearNumberFormat(ctx, locale, format3) {
  const context = ctx;
  for (const key in format3) {
    const id = `${locale}__${key}`;
    if (!context.__numberFormatters.has(id)) {
      continue;
    }
    context.__numberFormatters.delete(id);
  }
}
var inBrowser2, mark2, measure2, RE_ARGS2, generateFormatCacheKey2, friendlyJSONstringify2, isNumber2, isDate2, isRegExp2, isEmptyObject2, assign2, isArray2, isFunction2, isString2, isBoolean2, isObject2, isPromise2, objectToString2, toTypeString2, isPlainObject2, toDisplayString2, RANGE2, hasWarned2, CompileWarnCodes, warnMessages$1, CompileErrorCodes, errorMessages$1, RE_HTML_TAG, detectHtmlTag, CHAR_SP, CHAR_CR, CHAR_LF, CHAR_LS, CHAR_PS, EOF, DOT, LITERAL_DELIMITER, ERROR_DOMAIN$3, ERROR_DOMAIN$2, KNOWN_ESCAPES, ERROR_DOMAIN$1, ERROR_DOMAIN, generate, pathStateMachine, literalValueRE, cache, DEFAULT_MODIFIER, DEFAULT_MESSAGE, DEFAULT_MESSAGE_DATA_TYPE, DEFAULT_NORMALIZE, DEFAULT_INTERPOLATE, devtools, translateDevTools, code$1, inc$1, CoreWarnCodes, warnMessages, code, inc, CoreErrorCodes, errorMessages, _resolveLocale, VERSION, NOT_REOSLVED, DEFAULT_LOCALE, MISSING_RESOLVE_VALUE, capitalize, _compiler, _resolver, _fallbacker, _additionalMeta, setAdditionalMeta, getAdditionalMeta, _fallbackContext, setFallbackContext, getFallbackContext, _cid, WARN_MESSAGE, defaultOnCacheKey, compileCache, isMessageAST, compileToFunction, NOOP_MESSAGE_FUNCTION, isMessageFunction, intlDefined, Availabilities, DATETIME_FORMAT_OPTIONS_KEYS, NUMBER_FORMAT_OPTIONS_KEYS;
var init_core_base_esm_browser = __esm({
  "node_modules/@intlify/core-base/dist/core-base.esm-browser.js"() {
    inBrowser2 = typeof window !== "undefined";
    {
      const perf = inBrowser2 && window.performance;
      if (perf && perf.mark && perf.measure && perf.clearMarks && // @ts-ignore browser compat
      perf.clearMeasures) {
        mark2 = (tag) => {
          perf.mark(tag);
        };
        measure2 = (name, startTag, endTag) => {
          perf.measure(name, startTag, endTag);
          perf.clearMarks(startTag);
          perf.clearMarks(endTag);
        };
      }
    }
    RE_ARGS2 = /\{([0-9a-zA-Z]+)\}/g;
    generateFormatCacheKey2 = (locale, key, source) => friendlyJSONstringify2({ l: locale, k: key, s: source });
    friendlyJSONstringify2 = (json) => JSON.stringify(json).replace(/\u2028/g, "\\u2028").replace(/\u2029/g, "\\u2029").replace(/\u0027/g, "\\u0027");
    isNumber2 = (val) => typeof val === "number" && isFinite(val);
    isDate2 = (val) => toTypeString2(val) === "[object Date]";
    isRegExp2 = (val) => toTypeString2(val) === "[object RegExp]";
    isEmptyObject2 = (val) => isPlainObject2(val) && Object.keys(val).length === 0;
    assign2 = Object.assign;
    isArray2 = Array.isArray;
    isFunction2 = (val) => typeof val === "function";
    isString2 = (val) => typeof val === "string";
    isBoolean2 = (val) => typeof val === "boolean";
    isObject2 = (val) => val !== null && typeof val === "object";
    isPromise2 = (val) => {
      return isObject2(val) && isFunction2(val.then) && isFunction2(val.catch);
    };
    objectToString2 = Object.prototype.toString;
    toTypeString2 = (value) => objectToString2.call(value);
    isPlainObject2 = (val) => {
      if (!isObject2(val))
        return false;
      const proto = Object.getPrototypeOf(val);
      return proto === null || proto.constructor === Object;
    };
    toDisplayString2 = (val) => {
      return val == null ? "" : isArray2(val) || isPlainObject2(val) && val.toString === objectToString2 ? JSON.stringify(val, null, 2) : String(val);
    };
    RANGE2 = 2;
    hasWarned2 = {};
    CompileWarnCodes = {
      USE_MODULO_SYNTAX: 1,
      __EXTEND_POINT__: 2
    };
    warnMessages$1 = {
      [CompileWarnCodes.USE_MODULO_SYNTAX]: `Use modulo before '{{0}}'.`
    };
    CompileErrorCodes = {
      // tokenizer error codes
      EXPECTED_TOKEN: 1,
      INVALID_TOKEN_IN_PLACEHOLDER: 2,
      UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER: 3,
      UNKNOWN_ESCAPE_SEQUENCE: 4,
      INVALID_UNICODE_ESCAPE_SEQUENCE: 5,
      UNBALANCED_CLOSING_BRACE: 6,
      UNTERMINATED_CLOSING_BRACE: 7,
      EMPTY_PLACEHOLDER: 8,
      NOT_ALLOW_NEST_PLACEHOLDER: 9,
      INVALID_LINKED_FORMAT: 10,
      // parser error codes
      MUST_HAVE_MESSAGES_IN_PLURAL: 11,
      UNEXPECTED_EMPTY_LINKED_MODIFIER: 12,
      UNEXPECTED_EMPTY_LINKED_KEY: 13,
      UNEXPECTED_LEXICAL_ANALYSIS: 14,
      // generator error codes
      UNHANDLED_CODEGEN_NODE_TYPE: 15,
      // minifier error codes
      UNHANDLED_MINIFIER_NODE_TYPE: 16,
      // Special value for higher-order compilers to pick up the last code
      // to avoid collision of error codes. This should always be kept as the last
      // item.
      __EXTEND_POINT__: 17
    };
    errorMessages$1 = {
      // tokenizer error messages
      [CompileErrorCodes.EXPECTED_TOKEN]: `Expected token: '{0}'`,
      [CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER]: `Invalid token in placeholder: '{0}'`,
      [CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]: `Unterminated single quote in placeholder`,
      [CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE]: `Unknown escape sequence: \\{0}`,
      [CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE]: `Invalid unicode escape sequence: {0}`,
      [CompileErrorCodes.UNBALANCED_CLOSING_BRACE]: `Unbalanced closing brace`,
      [CompileErrorCodes.UNTERMINATED_CLOSING_BRACE]: `Unterminated closing brace`,
      [CompileErrorCodes.EMPTY_PLACEHOLDER]: `Empty placeholder`,
      [CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER]: `Not allowed nest placeholder`,
      [CompileErrorCodes.INVALID_LINKED_FORMAT]: `Invalid linked format`,
      // parser error messages
      [CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL]: `Plural must have messages`,
      [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER]: `Unexpected empty linked modifier`,
      [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY]: `Unexpected empty linked key`,
      [CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS]: `Unexpected lexical analysis in token: '{0}'`,
      // generator error messages
      [CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE]: `unhandled codegen node type: '{0}'`,
      // minimizer error messages
      [CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE]: `unhandled mimifier node type: '{0}'`
    };
    RE_HTML_TAG = /<\/?[\w\s="/.':;#-\/]+>/;
    detectHtmlTag = (source) => RE_HTML_TAG.test(source);
    CHAR_SP = " ";
    CHAR_CR = "\r";
    CHAR_LF = "\n";
    CHAR_LS = String.fromCharCode(8232);
    CHAR_PS = String.fromCharCode(8233);
    EOF = void 0;
    DOT = ".";
    LITERAL_DELIMITER = "'";
    ERROR_DOMAIN$3 = "tokenizer";
    ERROR_DOMAIN$2 = "parser";
    KNOWN_ESCAPES = /(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;
    ERROR_DOMAIN$1 = "minifier";
    ERROR_DOMAIN = "parser";
    generate = (ast, options = {}) => {
      const mode = isString2(options.mode) ? options.mode : "normal";
      const filename = isString2(options.filename) ? options.filename : "message.intl";
      const sourceMap = !!options.sourceMap;
      const breakLineCode = options.breakLineCode != null ? options.breakLineCode : mode === "arrow" ? ";" : "\n";
      const needIndent = options.needIndent ? options.needIndent : mode !== "arrow";
      const helpers = ast.helpers || [];
      const generator = createCodeGenerator(ast, {
        mode,
        filename,
        sourceMap,
        breakLineCode,
        needIndent
      });
      generator.push(mode === "normal" ? `function __msg__ (ctx) {` : `(ctx) => {`);
      generator.indent(needIndent);
      if (helpers.length > 0) {
        generator.push(`const { ${join2(helpers.map((s) => `${s}: _${s}`), ", ")} } = ctx`);
        generator.newline();
      }
      generator.push(`return `);
      generateNode(generator, ast);
      generator.deindent(needIndent);
      generator.push(`}`);
      delete ast.helpers;
      const { code: code2, map } = generator.context();
      return {
        ast,
        code: code2,
        map: map ? map.toJSON() : void 0
        // eslint-disable-line @typescript-eslint/no-explicit-any
      };
    };
    pathStateMachine = [];
    pathStateMachine[
      0
      /* States.BEFORE_PATH */
    ] = {
      [
        "w"
        /* PathCharTypes.WORKSPACE */
      ]: [
        0
        /* States.BEFORE_PATH */
      ],
      [
        "i"
        /* PathCharTypes.IDENT */
      ]: [
        3,
        0
        /* Actions.APPEND */
      ],
      [
        "["
        /* PathCharTypes.LEFT_BRACKET */
      ]: [
        4
        /* States.IN_SUB_PATH */
      ],
      [
        "o"
        /* PathCharTypes.END_OF_FAIL */
      ]: [
        7
        /* States.AFTER_PATH */
      ]
    };
    pathStateMachine[
      1
      /* States.IN_PATH */
    ] = {
      [
        "w"
        /* PathCharTypes.WORKSPACE */
      ]: [
        1
        /* States.IN_PATH */
      ],
      [
        "."
        /* PathCharTypes.DOT */
      ]: [
        2
        /* States.BEFORE_IDENT */
      ],
      [
        "["
        /* PathCharTypes.LEFT_BRACKET */
      ]: [
        4
        /* States.IN_SUB_PATH */
      ],
      [
        "o"
        /* PathCharTypes.END_OF_FAIL */
      ]: [
        7
        /* States.AFTER_PATH */
      ]
    };
    pathStateMachine[
      2
      /* States.BEFORE_IDENT */
    ] = {
      [
        "w"
        /* PathCharTypes.WORKSPACE */
      ]: [
        2
        /* States.BEFORE_IDENT */
      ],
      [
        "i"
        /* PathCharTypes.IDENT */
      ]: [
        3,
        0
        /* Actions.APPEND */
      ],
      [
        "0"
        /* PathCharTypes.ZERO */
      ]: [
        3,
        0
        /* Actions.APPEND */
      ]
    };
    pathStateMachine[
      3
      /* States.IN_IDENT */
    ] = {
      [
        "i"
        /* PathCharTypes.IDENT */
      ]: [
        3,
        0
        /* Actions.APPEND */
      ],
      [
        "0"
        /* PathCharTypes.ZERO */
      ]: [
        3,
        0
        /* Actions.APPEND */
      ],
      [
        "w"
        /* PathCharTypes.WORKSPACE */
      ]: [
        1,
        1
        /* Actions.PUSH */
      ],
      [
        "."
        /* PathCharTypes.DOT */
      ]: [
        2,
        1
        /* Actions.PUSH */
      ],
      [
        "["
        /* PathCharTypes.LEFT_BRACKET */
      ]: [
        4,
        1
        /* Actions.PUSH */
      ],
      [
        "o"
        /* PathCharTypes.END_OF_FAIL */
      ]: [
        7,
        1
        /* Actions.PUSH */
      ]
    };
    pathStateMachine[
      4
      /* States.IN_SUB_PATH */
    ] = {
      [
        "'"
        /* PathCharTypes.SINGLE_QUOTE */
      ]: [
        5,
        0
        /* Actions.APPEND */
      ],
      [
        '"'
        /* PathCharTypes.DOUBLE_QUOTE */
      ]: [
        6,
        0
        /* Actions.APPEND */
      ],
      [
        "["
        /* PathCharTypes.LEFT_BRACKET */
      ]: [
        4,
        2
        /* Actions.INC_SUB_PATH_DEPTH */
      ],
      [
        "]"
        /* PathCharTypes.RIGHT_BRACKET */
      ]: [
        1,
        3
        /* Actions.PUSH_SUB_PATH */
      ],
      [
        "o"
        /* PathCharTypes.END_OF_FAIL */
      ]: 8,
      [
        "l"
        /* PathCharTypes.ELSE */
      ]: [
        4,
        0
        /* Actions.APPEND */
      ]
    };
    pathStateMachine[
      5
      /* States.IN_SINGLE_QUOTE */
    ] = {
      [
        "'"
        /* PathCharTypes.SINGLE_QUOTE */
      ]: [
        4,
        0
        /* Actions.APPEND */
      ],
      [
        "o"
        /* PathCharTypes.END_OF_FAIL */
      ]: 8,
      [
        "l"
        /* PathCharTypes.ELSE */
      ]: [
        5,
        0
        /* Actions.APPEND */
      ]
    };
    pathStateMachine[
      6
      /* States.IN_DOUBLE_QUOTE */
    ] = {
      [
        '"'
        /* PathCharTypes.DOUBLE_QUOTE */
      ]: [
        4,
        0
        /* Actions.APPEND */
      ],
      [
        "o"
        /* PathCharTypes.END_OF_FAIL */
      ]: 8,
      [
        "l"
        /* PathCharTypes.ELSE */
      ]: [
        6,
        0
        /* Actions.APPEND */
      ]
    };
    literalValueRE = /^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;
    cache = /* @__PURE__ */ new Map();
    DEFAULT_MODIFIER = (str) => str;
    DEFAULT_MESSAGE = (ctx) => "";
    DEFAULT_MESSAGE_DATA_TYPE = "text";
    DEFAULT_NORMALIZE = (values) => values.length === 0 ? "" : join2(values);
    DEFAULT_INTERPOLATE = toDisplayString2;
    devtools = null;
    translateDevTools = createDevToolsHook(
      "function:translate"
      /* IntlifyDevToolsHooks.FunctionTranslate */
    );
    code$1 = CompileWarnCodes.__EXTEND_POINT__;
    inc$1 = incrementer2(code$1);
    CoreWarnCodes = {
      NOT_FOUND_KEY: code$1,
      // 2
      FALLBACK_TO_TRANSLATE: inc$1(),
      // 3
      CANNOT_FORMAT_NUMBER: inc$1(),
      // 4
      FALLBACK_TO_NUMBER_FORMAT: inc$1(),
      // 5
      CANNOT_FORMAT_DATE: inc$1(),
      // 6
      FALLBACK_TO_DATE_FORMAT: inc$1(),
      // 7
      EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER: inc$1(),
      // 8
      __EXTEND_POINT__: inc$1()
      // 9
    };
    warnMessages = {
      [CoreWarnCodes.NOT_FOUND_KEY]: `Not found '{key}' key in '{locale}' locale messages.`,
      [CoreWarnCodes.FALLBACK_TO_TRANSLATE]: `Fall back to translate '{key}' key with '{target}' locale.`,
      [CoreWarnCodes.CANNOT_FORMAT_NUMBER]: `Cannot format a number value due to not supported Intl.NumberFormat.`,
      [CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT]: `Fall back to number format '{key}' key with '{target}' locale.`,
      [CoreWarnCodes.CANNOT_FORMAT_DATE]: `Cannot format a date value due to not supported Intl.DateTimeFormat.`,
      [CoreWarnCodes.FALLBACK_TO_DATE_FORMAT]: `Fall back to datetime format '{key}' key with '{target}' locale.`,
      [CoreWarnCodes.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER]: `This project is using Custom Message Compiler, which is an experimental feature. It may receive breaking changes or be removed in the future.`
    };
    code = CompileErrorCodes.__EXTEND_POINT__;
    inc = incrementer2(code);
    CoreErrorCodes = {
      INVALID_ARGUMENT: code,
      // 17
      INVALID_DATE_ARGUMENT: inc(),
      // 18
      INVALID_ISO_DATE_ARGUMENT: inc(),
      // 19
      NOT_SUPPORT_NON_STRING_MESSAGE: inc(),
      // 20
      NOT_SUPPORT_LOCALE_PROMISE_VALUE: inc(),
      // 21
      NOT_SUPPORT_LOCALE_ASYNC_FUNCTION: inc(),
      // 22
      NOT_SUPPORT_LOCALE_TYPE: inc(),
      // 23
      __EXTEND_POINT__: inc()
      // 24
    };
    errorMessages = {
      [CoreErrorCodes.INVALID_ARGUMENT]: "Invalid arguments",
      [CoreErrorCodes.INVALID_DATE_ARGUMENT]: "The date provided is an invalid Date object.Make sure your Date represents a valid date.",
      [CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT]: "The argument provided is not a valid ISO date string",
      [CoreErrorCodes.NOT_SUPPORT_NON_STRING_MESSAGE]: "Not support non-string message",
      [CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE]: "cannot support promise value",
      [CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION]: "cannot support async function",
      [CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE]: "cannot support locale type"
    };
    VERSION = "9.14.1";
    NOT_REOSLVED = -1;
    DEFAULT_LOCALE = "en-US";
    MISSING_RESOLVE_VALUE = "";
    capitalize = (str) => `${str.charAt(0).toLocaleUpperCase()}${str.substr(1)}`;
    _additionalMeta = null;
    setAdditionalMeta = (meta) => {
      _additionalMeta = meta;
    };
    getAdditionalMeta = () => _additionalMeta;
    _fallbackContext = null;
    setFallbackContext = (context) => {
      _fallbackContext = context;
    };
    getFallbackContext = () => _fallbackContext;
    _cid = 0;
    WARN_MESSAGE = `Detected HTML in '{source}' message. Recommend not using HTML messages to avoid XSS.`;
    defaultOnCacheKey = (message) => message;
    compileCache = /* @__PURE__ */ Object.create(null);
    isMessageAST = (val) => isObject2(val) && (val.t === 0 || val.type === 0) && ("b" in val || "body" in val);
    compileToFunction = (message, context) => {
      if (!isString2(message)) {
        throw createCoreError(CoreErrorCodes.NOT_SUPPORT_NON_STRING_MESSAGE);
      }
      {
        context.onWarn = onCompileWarn;
      }
      {
        const warnHtmlMessage = isBoolean2(context.warnHtmlMessage) ? context.warnHtmlMessage : true;
        checkHtmlMessage(message, warnHtmlMessage);
        const onCacheKey = context.onCacheKey || defaultOnCacheKey;
        const cacheKey = onCacheKey(message);
        const cached = compileCache[cacheKey];
        if (cached) {
          return cached;
        }
        const { code: code2, detectError } = baseCompile(message, context);
        const msg = new Function(`return ${code2}`)();
        return !detectError ? compileCache[cacheKey] = msg : msg;
      }
    };
    NOOP_MESSAGE_FUNCTION = () => "";
    isMessageFunction = (val) => isFunction2(val);
    intlDefined = typeof Intl !== "undefined";
    Availabilities = {
      dateTimeFormat: intlDefined && typeof Intl.DateTimeFormat !== "undefined",
      numberFormat: intlDefined && typeof Intl.NumberFormat !== "undefined"
    };
    DATETIME_FORMAT_OPTIONS_KEYS = [
      "localeMatcher",
      "weekday",
      "era",
      "year",
      "month",
      "day",
      "hour",
      "minute",
      "second",
      "timeZoneName",
      "formatMatcher",
      "hour12",
      "timeZone",
      "dateStyle",
      "timeStyle",
      "calendar",
      "dayPeriod",
      "numberingSystem",
      "hourCycle",
      "fractionalSecondDigits"
    ];
    NUMBER_FORMAT_OPTIONS_KEYS = [
      "localeMatcher",
      "style",
      "currency",
      "currencyDisplay",
      "currencySign",
      "useGrouping",
      "minimumIntegerDigits",
      "minimumFractionDigits",
      "maximumFractionDigits",
      "minimumSignificantDigits",
      "maximumSignificantDigits",
      "compactDisplay",
      "notation",
      "signDisplay",
      "unit",
      "unitDisplay",
      "roundingMode",
      "roundingPriority",
      "roundingIncrement",
      "trailingZeroDisplay"
    ];
  }
});

// node_modules/vue/node_modules/@vue/shared/dist/shared.esm-bundler.js
var shared_esm_bundler_exports = {};
__export(shared_esm_bundler_exports, {
  EMPTY_ARR: () => EMPTY_ARR,
  EMPTY_OBJ: () => EMPTY_OBJ,
  NO: () => NO,
  NOOP: () => NOOP,
  PatchFlagNames: () => PatchFlagNames,
  PatchFlags: () => PatchFlags,
  ShapeFlags: () => ShapeFlags,
  SlotFlags: () => SlotFlags,
  camelize: () => camelize,
  capitalize: () => capitalize2,
  cssVarNameEscapeSymbolsRE: () => cssVarNameEscapeSymbolsRE,
  def: () => def,
  escapeHtml: () => escapeHtml3,
  escapeHtmlComment: () => escapeHtmlComment,
  extend: () => extend,
  genPropsAccessExp: () => genPropsAccessExp,
  generateCodeFrame: () => generateCodeFrame3,
  getEscapedCssVarName: () => getEscapedCssVarName,
  getGlobalThis: () => getGlobalThis2,
  hasChanged: () => hasChanged,
  hasOwn: () => hasOwn2,
  hyphenate: () => hyphenate,
  includeBooleanAttr: () => includeBooleanAttr,
  invokeArrayFns: () => invokeArrayFns,
  isArray: () => isArray3,
  isBooleanAttr: () => isBooleanAttr,
  isBuiltInDirective: () => isBuiltInDirective,
  isDate: () => isDate3,
  isFunction: () => isFunction3,
  isGloballyAllowed: () => isGloballyAllowed,
  isGloballyWhitelisted: () => isGloballyWhitelisted,
  isHTMLTag: () => isHTMLTag,
  isIntegerKey: () => isIntegerKey,
  isKnownHtmlAttr: () => isKnownHtmlAttr,
  isKnownSvgAttr: () => isKnownSvgAttr,
  isMap: () => isMap,
  isMathMLTag: () => isMathMLTag,
  isModelListener: () => isModelListener,
  isObject: () => isObject3,
  isOn: () => isOn,
  isPlainObject: () => isPlainObject3,
  isPromise: () => isPromise3,
  isRegExp: () => isRegExp3,
  isRenderableAttrValue: () => isRenderableAttrValue,
  isReservedProp: () => isReservedProp,
  isSSRSafeAttrName: () => isSSRSafeAttrName,
  isSVGTag: () => isSVGTag,
  isSet: () => isSet,
  isSpecialBooleanAttr: () => isSpecialBooleanAttr,
  isString: () => isString3,
  isSymbol: () => isSymbol2,
  isVoidTag: () => isVoidTag,
  looseEqual: () => looseEqual,
  looseIndexOf: () => looseIndexOf,
  looseToNumber: () => looseToNumber,
  makeMap: () => makeMap,
  normalizeClass: () => normalizeClass,
  normalizeProps: () => normalizeProps,
  normalizeStyle: () => normalizeStyle,
  objectToString: () => objectToString3,
  parseStringStyle: () => parseStringStyle,
  propsToAttrMap: () => propsToAttrMap,
  remove: () => remove,
  slotFlagsText: () => slotFlagsText,
  stringifyStyle: () => stringifyStyle,
  toDisplayString: () => toDisplayString3,
  toHandlerKey: () => toHandlerKey,
  toNumber: () => toNumber,
  toRawType: () => toRawType,
  toTypeString: () => toTypeString3
});
function makeMap(str, expectsLowerCase) {
  const set = new Set(str.split(","));
  return expectsLowerCase ? (val) => set.has(val.toLowerCase()) : (val) => set.has(val);
}
function genPropsAccessExp(name) {
  return identRE.test(name) ? `__props.${name}` : `__props[${JSON.stringify(name)}]`;
}
function generateCodeFrame3(source, start = 0, end = source.length) {
  start = Math.max(0, Math.min(start, source.length));
  end = Math.max(0, Math.min(end, source.length));
  if (start > end) return "";
  let lines = source.split(/(\r?\n)/);
  const newlineSequences = lines.filter((_, idx) => idx % 2 === 1);
  lines = lines.filter((_, idx) => idx % 2 === 0);
  let count = 0;
  const res = [];
  for (let i = 0; i < lines.length; i++) {
    count += lines[i].length + (newlineSequences[i] && newlineSequences[i].length || 0);
    if (count >= start) {
      for (let j = i - range; j <= i + range || end > count; j++) {
        if (j < 0 || j >= lines.length) continue;
        const line = j + 1;
        res.push(
          `${line}${" ".repeat(Math.max(3 - String(line).length, 0))}|  ${lines[j]}`
        );
        const lineLength = lines[j].length;
        const newLineSeqLength = newlineSequences[j] && newlineSequences[j].length || 0;
        if (j === i) {
          const pad = start - (count - (lineLength + newLineSeqLength));
          const length = Math.max(
            1,
            end > count ? lineLength - pad : end - start
          );
          res.push(`   |  ` + " ".repeat(pad) + "^".repeat(length));
        } else if (j > i) {
          if (end > count) {
            const length = Math.max(Math.min(end - count, lineLength), 1);
            res.push(`   |  ` + "^".repeat(length));
          }
          count += lineLength + newLineSeqLength;
        }
      }
      break;
    }
  }
  return res.join("\n");
}
function normalizeStyle(value) {
  if (isArray3(value)) {
    const res = {};
    for (let i = 0; i < value.length; i++) {
      const item = value[i];
      const normalized = isString3(item) ? parseStringStyle(item) : normalizeStyle(item);
      if (normalized) {
        for (const key in normalized) {
          res[key] = normalized[key];
        }
      }
    }
    return res;
  } else if (isString3(value) || isObject3(value)) {
    return value;
  }
}
function parseStringStyle(cssText) {
  const ret = {};
  cssText.replace(styleCommentRE, "").split(listDelimiterRE).forEach((item) => {
    if (item) {
      const tmp = item.split(propertyDelimiterRE);
      tmp.length > 1 && (ret[tmp[0].trim()] = tmp[1].trim());
    }
  });
  return ret;
}
function stringifyStyle(styles) {
  let ret = "";
  if (!styles || isString3(styles)) {
    return ret;
  }
  for (const key in styles) {
    const value = styles[key];
    if (isString3(value) || typeof value === "number") {
      const normalizedKey = key.startsWith(`--`) ? key : hyphenate(key);
      ret += `${normalizedKey}:${value};`;
    }
  }
  return ret;
}
function normalizeClass(value) {
  let res = "";
  if (isString3(value)) {
    res = value;
  } else if (isArray3(value)) {
    for (let i = 0; i < value.length; i++) {
      const normalized = normalizeClass(value[i]);
      if (normalized) {
        res += normalized + " ";
      }
    }
  } else if (isObject3(value)) {
    for (const name in value) {
      if (value[name]) {
        res += name + " ";
      }
    }
  }
  return res.trim();
}
function normalizeProps(props) {
  if (!props) return null;
  let { class: klass, style } = props;
  if (klass && !isString3(klass)) {
    props.class = normalizeClass(klass);
  }
  if (style) {
    props.style = normalizeStyle(style);
  }
  return props;
}
function includeBooleanAttr(value) {
  return !!value || value === "";
}
function isSSRSafeAttrName(name) {
  if (attrValidationCache.hasOwnProperty(name)) {
    return attrValidationCache[name];
  }
  const isUnsafe = unsafeAttrCharRE.test(name);
  if (isUnsafe) {
    console.error(`unsafe attribute name: ${name}`);
  }
  return attrValidationCache[name] = !isUnsafe;
}
function isRenderableAttrValue(value) {
  if (value == null) {
    return false;
  }
  const type = typeof value;
  return type === "string" || type === "number" || type === "boolean";
}
function escapeHtml3(string) {
  const str = "" + string;
  const match = escapeRE.exec(str);
  if (!match) {
    return str;
  }
  let html = "";
  let escaped;
  let index;
  let lastIndex = 0;
  for (index = match.index; index < str.length; index++) {
    switch (str.charCodeAt(index)) {
      case 34:
        escaped = "&quot;";
        break;
      case 38:
        escaped = "&amp;";
        break;
      case 39:
        escaped = "&#39;";
        break;
      case 60:
        escaped = "&lt;";
        break;
      case 62:
        escaped = "&gt;";
        break;
      default:
        continue;
    }
    if (lastIndex !== index) {
      html += str.slice(lastIndex, index);
    }
    lastIndex = index + 1;
    html += escaped;
  }
  return lastIndex !== index ? html + str.slice(lastIndex, index) : html;
}
function escapeHtmlComment(src) {
  return src.replace(commentStripRE, "");
}
function getEscapedCssVarName(key, doubleEscape) {
  return key.replace(
    cssVarNameEscapeSymbolsRE,
    (s) => doubleEscape ? s === '"' ? '\\\\\\"' : `\\\\${s}` : `\\${s}`
  );
}
function looseCompareArrays(a, b) {
  if (a.length !== b.length) return false;
  let equal = true;
  for (let i = 0; equal && i < a.length; i++) {
    equal = looseEqual(a[i], b[i]);
  }
  return equal;
}
function looseEqual(a, b) {
  if (a === b) return true;
  let aValidType = isDate3(a);
  let bValidType = isDate3(b);
  if (aValidType || bValidType) {
    return aValidType && bValidType ? a.getTime() === b.getTime() : false;
  }
  aValidType = isSymbol2(a);
  bValidType = isSymbol2(b);
  if (aValidType || bValidType) {
    return a === b;
  }
  aValidType = isArray3(a);
  bValidType = isArray3(b);
  if (aValidType || bValidType) {
    return aValidType && bValidType ? looseCompareArrays(a, b) : false;
  }
  aValidType = isObject3(a);
  bValidType = isObject3(b);
  if (aValidType || bValidType) {
    if (!aValidType || !bValidType) {
      return false;
    }
    const aKeysCount = Object.keys(a).length;
    const bKeysCount = Object.keys(b).length;
    if (aKeysCount !== bKeysCount) {
      return false;
    }
    for (const key in a) {
      const aHasKey = a.hasOwnProperty(key);
      const bHasKey = b.hasOwnProperty(key);
      if (aHasKey && !bHasKey || !aHasKey && bHasKey || !looseEqual(a[key], b[key])) {
        return false;
      }
    }
  }
  return String(a) === String(b);
}
function looseIndexOf(arr, val) {
  return arr.findIndex((item) => looseEqual(item, val));
}
var EMPTY_OBJ, EMPTY_ARR, NOOP, NO, isOn, isModelListener, extend, remove, hasOwnProperty2, hasOwn2, isArray3, isMap, isSet, isDate3, isRegExp3, isFunction3, isString3, isSymbol2, isObject3, isPromise3, objectToString3, toTypeString3, toRawType, isPlainObject3, isIntegerKey, isReservedProp, isBuiltInDirective, cacheStringFunction, camelizeRE, camelize, hyphenateRE, hyphenate, capitalize2, toHandlerKey, hasChanged, invokeArrayFns, def, looseToNumber, toNumber, _globalThis2, getGlobalThis2, identRE, PatchFlags, PatchFlagNames, ShapeFlags, SlotFlags, slotFlagsText, GLOBALS_ALLOWED, isGloballyAllowed, isGloballyWhitelisted, range, listDelimiterRE, propertyDelimiterRE, styleCommentRE, HTML_TAGS, SVG_TAGS, MATH_TAGS, VOID_TAGS, isHTMLTag, isSVGTag, isMathMLTag, isVoidTag, specialBooleanAttrs, isSpecialBooleanAttr, isBooleanAttr, unsafeAttrCharRE, attrValidationCache, propsToAttrMap, isKnownHtmlAttr, isKnownSvgAttr, escapeRE, commentStripRE, cssVarNameEscapeSymbolsRE, isRef, toDisplayString3, replacer, stringifySymbol;
var init_shared_esm_bundler = __esm({
  "node_modules/vue/node_modules/@vue/shared/dist/shared.esm-bundler.js"() {
    EMPTY_OBJ = true ? Object.freeze({}) : {};
    EMPTY_ARR = true ? Object.freeze([]) : [];
    NOOP = () => {
    };
    NO = () => false;
    isOn = (key) => key.charCodeAt(0) === 111 && key.charCodeAt(1) === 110 && // uppercase letter
    (key.charCodeAt(2) > 122 || key.charCodeAt(2) < 97);
    isModelListener = (key) => key.startsWith("onUpdate:");
    extend = Object.assign;
    remove = (arr, el) => {
      const i = arr.indexOf(el);
      if (i > -1) {
        arr.splice(i, 1);
      }
    };
    hasOwnProperty2 = Object.prototype.hasOwnProperty;
    hasOwn2 = (val, key) => hasOwnProperty2.call(val, key);
    isArray3 = Array.isArray;
    isMap = (val) => toTypeString3(val) === "[object Map]";
    isSet = (val) => toTypeString3(val) === "[object Set]";
    isDate3 = (val) => toTypeString3(val) === "[object Date]";
    isRegExp3 = (val) => toTypeString3(val) === "[object RegExp]";
    isFunction3 = (val) => typeof val === "function";
    isString3 = (val) => typeof val === "string";
    isSymbol2 = (val) => typeof val === "symbol";
    isObject3 = (val) => val !== null && typeof val === "object";
    isPromise3 = (val) => {
      return (isObject3(val) || isFunction3(val)) && isFunction3(val.then) && isFunction3(val.catch);
    };
    objectToString3 = Object.prototype.toString;
    toTypeString3 = (value) => objectToString3.call(value);
    toRawType = (value) => {
      return toTypeString3(value).slice(8, -1);
    };
    isPlainObject3 = (val) => toTypeString3(val) === "[object Object]";
    isIntegerKey = (key) => isString3(key) && key !== "NaN" && key[0] !== "-" && "" + parseInt(key, 10) === key;
    isReservedProp = makeMap(
      // the leading comma is intentional so empty string "" is also included
      ",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"
    );
    isBuiltInDirective = makeMap(
      "bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"
    );
    cacheStringFunction = (fn) => {
      const cache2 = /* @__PURE__ */ Object.create(null);
      return (str) => {
        const hit = cache2[str];
        return hit || (cache2[str] = fn(str));
      };
    };
    camelizeRE = /-(\w)/g;
    camelize = cacheStringFunction(
      (str) => {
        return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : "");
      }
    );
    hyphenateRE = /\B([A-Z])/g;
    hyphenate = cacheStringFunction(
      (str) => str.replace(hyphenateRE, "-$1").toLowerCase()
    );
    capitalize2 = cacheStringFunction((str) => {
      return str.charAt(0).toUpperCase() + str.slice(1);
    });
    toHandlerKey = cacheStringFunction(
      (str) => {
        const s = str ? `on${capitalize2(str)}` : ``;
        return s;
      }
    );
    hasChanged = (value, oldValue) => !Object.is(value, oldValue);
    invokeArrayFns = (fns, ...arg) => {
      for (let i = 0; i < fns.length; i++) {
        fns[i](...arg);
      }
    };
    def = (obj, key, value, writable = false) => {
      Object.defineProperty(obj, key, {
        configurable: true,
        enumerable: false,
        writable,
        value
      });
    };
    looseToNumber = (val) => {
      const n = parseFloat(val);
      return isNaN(n) ? val : n;
    };
    toNumber = (val) => {
      const n = isString3(val) ? Number(val) : NaN;
      return isNaN(n) ? val : n;
    };
    getGlobalThis2 = () => {
      return _globalThis2 || (_globalThis2 = typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : {});
    };
    identRE = /^[_$a-zA-Z\xA0-\uFFFF][_$a-zA-Z0-9\xA0-\uFFFF]*$/;
    PatchFlags = {
      "TEXT": 1,
      "1": "TEXT",
      "CLASS": 2,
      "2": "CLASS",
      "STYLE": 4,
      "4": "STYLE",
      "PROPS": 8,
      "8": "PROPS",
      "FULL_PROPS": 16,
      "16": "FULL_PROPS",
      "NEED_HYDRATION": 32,
      "32": "NEED_HYDRATION",
      "STABLE_FRAGMENT": 64,
      "64": "STABLE_FRAGMENT",
      "KEYED_FRAGMENT": 128,
      "128": "KEYED_FRAGMENT",
      "UNKEYED_FRAGMENT": 256,
      "256": "UNKEYED_FRAGMENT",
      "NEED_PATCH": 512,
      "512": "NEED_PATCH",
      "DYNAMIC_SLOTS": 1024,
      "1024": "DYNAMIC_SLOTS",
      "DEV_ROOT_FRAGMENT": 2048,
      "2048": "DEV_ROOT_FRAGMENT",
      "CACHED": -1,
      "-1": "CACHED",
      "BAIL": -2,
      "-2": "BAIL"
    };
    PatchFlagNames = {
      [1]: `TEXT`,
      [2]: `CLASS`,
      [4]: `STYLE`,
      [8]: `PROPS`,
      [16]: `FULL_PROPS`,
      [32]: `NEED_HYDRATION`,
      [64]: `STABLE_FRAGMENT`,
      [128]: `KEYED_FRAGMENT`,
      [256]: `UNKEYED_FRAGMENT`,
      [512]: `NEED_PATCH`,
      [1024]: `DYNAMIC_SLOTS`,
      [2048]: `DEV_ROOT_FRAGMENT`,
      [-1]: `HOISTED`,
      [-2]: `BAIL`
    };
    ShapeFlags = {
      "ELEMENT": 1,
      "1": "ELEMENT",
      "FUNCTIONAL_COMPONENT": 2,
      "2": "FUNCTIONAL_COMPONENT",
      "STATEFUL_COMPONENT": 4,
      "4": "STATEFUL_COMPONENT",
      "TEXT_CHILDREN": 8,
      "8": "TEXT_CHILDREN",
      "ARRAY_CHILDREN": 16,
      "16": "ARRAY_CHILDREN",
      "SLOTS_CHILDREN": 32,
      "32": "SLOTS_CHILDREN",
      "TELEPORT": 64,
      "64": "TELEPORT",
      "SUSPENSE": 128,
      "128": "SUSPENSE",
      "COMPONENT_SHOULD_KEEP_ALIVE": 256,
      "256": "COMPONENT_SHOULD_KEEP_ALIVE",
      "COMPONENT_KEPT_ALIVE": 512,
      "512": "COMPONENT_KEPT_ALIVE",
      "COMPONENT": 6,
      "6": "COMPONENT"
    };
    SlotFlags = {
      "STABLE": 1,
      "1": "STABLE",
      "DYNAMIC": 2,
      "2": "DYNAMIC",
      "FORWARDED": 3,
      "3": "FORWARDED"
    };
    slotFlagsText = {
      [1]: "STABLE",
      [2]: "DYNAMIC",
      [3]: "FORWARDED"
    };
    GLOBALS_ALLOWED = "Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,console,Error,Symbol";
    isGloballyAllowed = makeMap(GLOBALS_ALLOWED);
    isGloballyWhitelisted = isGloballyAllowed;
    range = 2;
    listDelimiterRE = /;(?![^(]*\))/g;
    propertyDelimiterRE = /:([^]+)/;
    styleCommentRE = /\/\*[^]*?\*\//g;
    HTML_TAGS = "html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot";
    SVG_TAGS = "svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view";
    MATH_TAGS = "annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics";
    VOID_TAGS = "area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr";
    isHTMLTag = makeMap(HTML_TAGS);
    isSVGTag = makeMap(SVG_TAGS);
    isMathMLTag = makeMap(MATH_TAGS);
    isVoidTag = makeMap(VOID_TAGS);
    specialBooleanAttrs = `itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly`;
    isSpecialBooleanAttr = makeMap(specialBooleanAttrs);
    isBooleanAttr = makeMap(
      specialBooleanAttrs + `,async,autofocus,autoplay,controls,default,defer,disabled,hidden,inert,loop,open,required,reversed,scoped,seamless,checked,muted,multiple,selected`
    );
    unsafeAttrCharRE = /[>/="'\u0009\u000a\u000c\u0020]/;
    attrValidationCache = {};
    propsToAttrMap = {
      acceptCharset: "accept-charset",
      className: "class",
      htmlFor: "for",
      httpEquiv: "http-equiv"
    };
    isKnownHtmlAttr = makeMap(
      `accept,accept-charset,accesskey,action,align,allow,alt,async,autocapitalize,autocomplete,autofocus,autoplay,background,bgcolor,border,buffered,capture,challenge,charset,checked,cite,class,code,codebase,color,cols,colspan,content,contenteditable,contextmenu,controls,coords,crossorigin,csp,data,datetime,decoding,default,defer,dir,dirname,disabled,download,draggable,dropzone,enctype,enterkeyhint,for,form,formaction,formenctype,formmethod,formnovalidate,formtarget,headers,height,hidden,high,href,hreflang,http-equiv,icon,id,importance,inert,integrity,ismap,itemprop,keytype,kind,label,lang,language,loading,list,loop,low,manifest,max,maxlength,minlength,media,min,multiple,muted,name,novalidate,open,optimum,pattern,ping,placeholder,poster,preload,radiogroup,readonly,referrerpolicy,rel,required,reversed,rows,rowspan,sandbox,scope,scoped,selected,shape,size,sizes,slot,span,spellcheck,src,srcdoc,srclang,srcset,start,step,style,summary,tabindex,target,title,translate,type,usemap,value,width,wrap`
    );
    isKnownSvgAttr = makeMap(
      `xmlns,accent-height,accumulate,additive,alignment-baseline,alphabetic,amplitude,arabic-form,ascent,attributeName,attributeType,azimuth,baseFrequency,baseline-shift,baseProfile,bbox,begin,bias,by,calcMode,cap-height,class,clip,clipPathUnits,clip-path,clip-rule,color,color-interpolation,color-interpolation-filters,color-profile,color-rendering,contentScriptType,contentStyleType,crossorigin,cursor,cx,cy,d,decelerate,descent,diffuseConstant,direction,display,divisor,dominant-baseline,dur,dx,dy,edgeMode,elevation,enable-background,end,exponent,fill,fill-opacity,fill-rule,filter,filterRes,filterUnits,flood-color,flood-opacity,font-family,font-size,font-size-adjust,font-stretch,font-style,font-variant,font-weight,format,from,fr,fx,fy,g1,g2,glyph-name,glyph-orientation-horizontal,glyph-orientation-vertical,glyphRef,gradientTransform,gradientUnits,hanging,height,href,hreflang,horiz-adv-x,horiz-origin-x,id,ideographic,image-rendering,in,in2,intercept,k,k1,k2,k3,k4,kernelMatrix,kernelUnitLength,kerning,keyPoints,keySplines,keyTimes,lang,lengthAdjust,letter-spacing,lighting-color,limitingConeAngle,local,marker-end,marker-mid,marker-start,markerHeight,markerUnits,markerWidth,mask,maskContentUnits,maskUnits,mathematical,max,media,method,min,mode,name,numOctaves,offset,opacity,operator,order,orient,orientation,origin,overflow,overline-position,overline-thickness,panose-1,paint-order,path,pathLength,patternContentUnits,patternTransform,patternUnits,ping,pointer-events,points,pointsAtX,pointsAtY,pointsAtZ,preserveAlpha,preserveAspectRatio,primitiveUnits,r,radius,referrerPolicy,refX,refY,rel,rendering-intent,repeatCount,repeatDur,requiredExtensions,requiredFeatures,restart,result,rotate,rx,ry,scale,seed,shape-rendering,slope,spacing,specularConstant,specularExponent,speed,spreadMethod,startOffset,stdDeviation,stemh,stemv,stitchTiles,stop-color,stop-opacity,strikethrough-position,strikethrough-thickness,string,stroke,stroke-dasharray,stroke-dashoffset,stroke-linecap,stroke-linejoin,stroke-miterlimit,stroke-opacity,stroke-width,style,surfaceScale,systemLanguage,tabindex,tableValues,target,targetX,targetY,text-anchor,text-decoration,text-rendering,textLength,to,transform,transform-origin,type,u1,u2,underline-position,underline-thickness,unicode,unicode-bidi,unicode-range,units-per-em,v-alphabetic,v-hanging,v-ideographic,v-mathematical,values,vector-effect,version,vert-adv-y,vert-origin-x,vert-origin-y,viewBox,viewTarget,visibility,width,widths,word-spacing,writing-mode,x,x-height,x1,x2,xChannelSelector,xlink:actuate,xlink:arcrole,xlink:href,xlink:role,xlink:show,xlink:title,xlink:type,xmlns:xlink,xml:base,xml:lang,xml:space,y,y1,y2,yChannelSelector,z,zoomAndPan`
    );
    escapeRE = /["'&<>]/;
    commentStripRE = /^-?>|<!--|-->|--!>|<!-$/g;
    cssVarNameEscapeSymbolsRE = /[ !"#$%&'()*+,./:;<=>?@[\\\]^`{|}~]/g;
    isRef = (val) => {
      return !!(val && val["__v_isRef"] === true);
    };
    toDisplayString3 = (val) => {
      return isString3(val) ? val : val == null ? "" : isArray3(val) || isObject3(val) && (val.toString === objectToString3 || !isFunction3(val.toString)) ? isRef(val) ? toDisplayString3(val.value) : JSON.stringify(val, replacer, 2) : String(val);
    };
    replacer = (_key, val) => {
      if (isRef(val)) {
        return replacer(_key, val.value);
      } else if (isMap(val)) {
        return {
          [`Map(${val.size})`]: [...val.entries()].reduce(
            (entries, [key, val2], i) => {
              entries[stringifySymbol(key, i) + " =>"] = val2;
              return entries;
            },
            {}
          )
        };
      } else if (isSet(val)) {
        return {
          [`Set(${val.size})`]: [...val.values()].map((v) => stringifySymbol(v))
        };
      } else if (isSymbol2(val)) {
        return stringifySymbol(val);
      } else if (isObject3(val) && !isArray3(val) && !isPlainObject3(val)) {
        return String(val);
      }
      return val;
    };
    stringifySymbol = (v, i = "") => {
      var _a;
      return (
        // Symbol.description in es2019+ so we need to cast here to pass
        // the lib: es2016 check
        isSymbol2(v) ? `Symbol(${(_a = v.description) != null ? _a : i})` : v
      );
    };
  }
});

// node_modules/vue/node_modules/@vue/compiler-core/dist/compiler-core.esm-bundler.js
function registerRuntimeHelpers(helpers) {
  Object.getOwnPropertySymbols(helpers).forEach((s) => {
    helperNameMap[s] = helpers[s];
  });
}
function createRoot(children, source = "") {
  return {
    type: 0,
    source,
    children,
    helpers: /* @__PURE__ */ new Set(),
    components: [],
    directives: [],
    hoists: [],
    imports: [],
    cached: [],
    temps: 0,
    codegenNode: void 0,
    loc: locStub
  };
}
function createVNodeCall(context, tag, props, children, patchFlag, dynamicProps, directives, isBlock = false, disableTracking = false, isComponent2 = false, loc = locStub) {
  if (context) {
    if (isBlock) {
      context.helper(OPEN_BLOCK);
      context.helper(getVNodeBlockHelper(context.inSSR, isComponent2));
    } else {
      context.helper(getVNodeHelper(context.inSSR, isComponent2));
    }
    if (directives) {
      context.helper(WITH_DIRECTIVES);
    }
  }
  return {
    type: 13,
    tag,
    props,
    children,
    patchFlag,
    dynamicProps,
    directives,
    isBlock,
    disableTracking,
    isComponent: isComponent2,
    loc
  };
}
function createArrayExpression(elements, loc = locStub) {
  return {
    type: 17,
    loc,
    elements
  };
}
function createObjectExpression(properties, loc = locStub) {
  return {
    type: 15,
    loc,
    properties
  };
}
function createObjectProperty(key, value) {
  return {
    type: 16,
    loc: locStub,
    key: isString3(key) ? createSimpleExpression(key, true) : key,
    value
  };
}
function createSimpleExpression(content, isStatic = false, loc = locStub, constType = 0) {
  return {
    type: 4,
    loc,
    content,
    isStatic,
    constType: isStatic ? 3 : constType
  };
}
function createInterpolation(content, loc) {
  return {
    type: 5,
    loc,
    content: isString3(content) ? createSimpleExpression(content, false, loc) : content
  };
}
function createCompoundExpression(children, loc = locStub) {
  return {
    type: 8,
    loc,
    children
  };
}
function createCallExpression(callee, args = [], loc = locStub) {
  return {
    type: 14,
    loc,
    callee,
    arguments: args
  };
}
function createFunctionExpression(params, returns = void 0, newline = false, isSlot = false, loc = locStub) {
  return {
    type: 18,
    params,
    returns,
    newline,
    isSlot,
    loc
  };
}
function createConditionalExpression(test, consequent, alternate, newline = true) {
  return {
    type: 19,
    test,
    consequent,
    alternate,
    newline,
    loc: locStub
  };
}
function createCacheExpression(index, value, needPauseTracking = false) {
  return {
    type: 20,
    index,
    value,
    needPauseTracking,
    needArraySpread: false,
    loc: locStub
  };
}
function createBlockStatement(body) {
  return {
    type: 21,
    body,
    loc: locStub
  };
}
function createTemplateLiteral(elements) {
  return {
    type: 22,
    elements,
    loc: locStub
  };
}
function createIfStatement(test, consequent, alternate) {
  return {
    type: 23,
    test,
    consequent,
    alternate,
    loc: locStub
  };
}
function createAssignmentExpression(left, right) {
  return {
    type: 24,
    left,
    right,
    loc: locStub
  };
}
function createSequenceExpression(expressions) {
  return {
    type: 25,
    expressions,
    loc: locStub
  };
}
function createReturnStatement(returns) {
  return {
    type: 26,
    returns,
    loc: locStub
  };
}
function getVNodeHelper(ssr, isComponent2) {
  return ssr || isComponent2 ? CREATE_VNODE : CREATE_ELEMENT_VNODE;
}
function getVNodeBlockHelper(ssr, isComponent2) {
  return ssr || isComponent2 ? CREATE_BLOCK : CREATE_ELEMENT_BLOCK;
}
function convertToBlock(node, { helper, removeHelper, inSSR }) {
  if (!node.isBlock) {
    node.isBlock = true;
    removeHelper(getVNodeHelper(inSSR, node.isComponent));
    helper(OPEN_BLOCK);
    helper(getVNodeBlockHelper(inSSR, node.isComponent));
  }
}
function isTagStartChar(c) {
  return c >= 97 && c <= 122 || c >= 65 && c <= 90;
}
function isWhitespace(c) {
  return c === 32 || c === 10 || c === 9 || c === 12 || c === 13;
}
function isEndOfTagSection(c) {
  return c === 47 || c === 62 || isWhitespace(c);
}
function toCharCodes(str) {
  const ret = new Uint8Array(str.length);
  for (let i = 0; i < str.length; i++) {
    ret[i] = str.charCodeAt(i);
  }
  return ret;
}
function getCompatValue(key, { compatConfig }) {
  const value = compatConfig && compatConfig[key];
  if (key === "MODE") {
    return value || 3;
  } else {
    return value;
  }
}
function isCompatEnabled(key, context) {
  const mode = getCompatValue("MODE", context);
  const value = getCompatValue(key, context);
  return mode === 3 ? value === true : value !== false;
}
function checkCompatEnabled(key, context, loc, ...args) {
  const enabled = isCompatEnabled(key, context);
  if (enabled) {
    warnDeprecation(key, context, loc, ...args);
  }
  return enabled;
}
function warnDeprecation(key, context, loc, ...args) {
  const val = getCompatValue(key, context);
  if (val === "suppress-warning") {
    return;
  }
  const { message, link } = deprecationData[key];
  const msg = `(deprecation ${key}) ${typeof message === "function" ? message(...args) : message}${link ? `
  Details: ${link}` : ``}`;
  const err = new SyntaxError(msg);
  err.code = key;
  if (loc) err.loc = loc;
  context.onWarn(err);
}
function defaultOnError2(error) {
  throw error;
}
function defaultOnWarn(msg) {
  console.warn(`[Vue warn] ${msg.message}`);
}
function createCompilerError(code2, loc, messages, additionalMessage) {
  const msg = true ? (messages || errorMessages2)[code2] + (additionalMessage || ``) : `https://vuejs.org/error-reference/#compiler-${code2}`;
  const error = new SyntaxError(String(msg));
  error.code = code2;
  error.loc = loc;
  return error;
}
function walkIdentifiers(root, onIdentifier, includeAll = false, parentStack = [], knownIds = /* @__PURE__ */ Object.create(null)) {
  {
    return;
  }
}
function isReferencedIdentifier(id, parent, parentStack) {
  {
    return false;
  }
}
function isInDestructureAssignment(parent, parentStack) {
  if (parent && (parent.type === "ObjectProperty" || parent.type === "ArrayPattern")) {
    let i = parentStack.length;
    while (i--) {
      const p = parentStack[i];
      if (p.type === "AssignmentExpression") {
        return true;
      } else if (p.type !== "ObjectProperty" && !p.type.endsWith("Pattern")) {
        break;
      }
    }
  }
  return false;
}
function isInNewExpression(parentStack) {
  let i = parentStack.length;
  while (i--) {
    const p = parentStack[i];
    if (p.type === "NewExpression") {
      return true;
    } else if (p.type !== "MemberExpression") {
      break;
    }
  }
  return false;
}
function walkFunctionParams(node, onIdent) {
  for (const p of node.params) {
    for (const id of extractIdentifiers(p)) {
      onIdent(id);
    }
  }
}
function walkBlockDeclarations(block, onIdent) {
  for (const stmt of block.body) {
    if (stmt.type === "VariableDeclaration") {
      if (stmt.declare) continue;
      for (const decl of stmt.declarations) {
        for (const id of extractIdentifiers(decl.id)) {
          onIdent(id);
        }
      }
    } else if (stmt.type === "FunctionDeclaration" || stmt.type === "ClassDeclaration") {
      if (stmt.declare || !stmt.id) continue;
      onIdent(stmt.id);
    } else if (isForStatement(stmt)) {
      walkForStatement(stmt, true, onIdent);
    }
  }
}
function isForStatement(stmt) {
  return stmt.type === "ForOfStatement" || stmt.type === "ForInStatement" || stmt.type === "ForStatement";
}
function walkForStatement(stmt, isVar, onIdent) {
  const variable = stmt.type === "ForStatement" ? stmt.init : stmt.left;
  if (variable && variable.type === "VariableDeclaration" && (variable.kind === "var" ? isVar : !isVar)) {
    for (const decl of variable.declarations) {
      for (const id of extractIdentifiers(decl.id)) {
        onIdent(id);
      }
    }
  }
}
function extractIdentifiers(param, nodes = []) {
  switch (param.type) {
    case "Identifier":
      nodes.push(param);
      break;
    case "MemberExpression":
      let object = param;
      while (object.type === "MemberExpression") {
        object = object.object;
      }
      nodes.push(object);
      break;
    case "ObjectPattern":
      for (const prop of param.properties) {
        if (prop.type === "RestElement") {
          extractIdentifiers(prop.argument, nodes);
        } else {
          extractIdentifiers(prop.value, nodes);
        }
      }
      break;
    case "ArrayPattern":
      param.elements.forEach((element) => {
        if (element) extractIdentifiers(element, nodes);
      });
      break;
    case "RestElement":
      extractIdentifiers(param.argument, nodes);
      break;
    case "AssignmentPattern":
      extractIdentifiers(param.left, nodes);
      break;
  }
  return nodes;
}
function unwrapTSNode(node) {
  if (TS_NODE_TYPES.includes(node.type)) {
    return unwrapTSNode(node.expression);
  } else {
    return node;
  }
}
function isCoreComponent(tag) {
  switch (tag) {
    case "Teleport":
    case "teleport":
      return TELEPORT;
    case "Suspense":
    case "suspense":
      return SUSPENSE;
    case "KeepAlive":
    case "keep-alive":
      return KEEP_ALIVE;
    case "BaseTransition":
    case "base-transition":
      return BASE_TRANSITION;
  }
}
function advancePositionWithClone(pos, source, numberOfCharacters = source.length) {
  return advancePositionWithMutation(
    {
      offset: pos.offset,
      line: pos.line,
      column: pos.column
    },
    source,
    numberOfCharacters
  );
}
function advancePositionWithMutation(pos, source, numberOfCharacters = source.length) {
  let linesCount = 0;
  let lastNewLinePos = -1;
  for (let i = 0; i < numberOfCharacters; i++) {
    if (source.charCodeAt(i) === 10) {
      linesCount++;
      lastNewLinePos = i;
    }
  }
  pos.offset += numberOfCharacters;
  pos.line += linesCount;
  pos.column = lastNewLinePos === -1 ? pos.column + numberOfCharacters : numberOfCharacters - lastNewLinePos;
  return pos;
}
function assert(condition, msg) {
  if (!condition) {
    throw new Error(msg || `unexpected compiler condition`);
  }
}
function findDir(node, name, allowEmpty = false) {
  for (let i = 0; i < node.props.length; i++) {
    const p = node.props[i];
    if (p.type === 7 && (allowEmpty || p.exp) && (isString3(name) ? p.name === name : name.test(p.name))) {
      return p;
    }
  }
}
function findProp(node, name, dynamicOnly = false, allowEmpty = false) {
  for (let i = 0; i < node.props.length; i++) {
    const p = node.props[i];
    if (p.type === 6) {
      if (dynamicOnly) continue;
      if (p.name === name && (p.value || allowEmpty)) {
        return p;
      }
    } else if (p.name === "bind" && (p.exp || allowEmpty) && isStaticArgOf(p.arg, name)) {
      return p;
    }
  }
}
function isStaticArgOf(arg, name) {
  return !!(arg && isStaticExp(arg) && arg.content === name);
}
function hasDynamicKeyVBind(node) {
  return node.props.some(
    (p) => p.type === 7 && p.name === "bind" && (!p.arg || // v-bind="obj"
    p.arg.type !== 4 || // v-bind:[_ctx.foo]
    !p.arg.isStatic)
    // v-bind:[foo]
  );
}
function isText$1(node) {
  return node.type === 5 || node.type === 2;
}
function isVSlot(p) {
  return p.type === 7 && p.name === "slot";
}
function isTemplateNode(node) {
  return node.type === 1 && node.tagType === 3;
}
function isSlotOutlet(node) {
  return node.type === 1 && node.tagType === 2;
}
function getUnnormalizedProps(props, callPath = []) {
  if (props && !isString3(props) && props.type === 14) {
    const callee = props.callee;
    if (!isString3(callee) && propsHelperSet.has(callee)) {
      return getUnnormalizedProps(
        props.arguments[0],
        callPath.concat(props)
      );
    }
  }
  return [props, callPath];
}
function injectProp(node, prop, context) {
  let propsWithInjection;
  let props = node.type === 13 ? node.props : node.arguments[2];
  let callPath = [];
  let parentCall;
  if (props && !isString3(props) && props.type === 14) {
    const ret = getUnnormalizedProps(props);
    props = ret[0];
    callPath = ret[1];
    parentCall = callPath[callPath.length - 1];
  }
  if (props == null || isString3(props)) {
    propsWithInjection = createObjectExpression([prop]);
  } else if (props.type === 14) {
    const first = props.arguments[0];
    if (!isString3(first) && first.type === 15) {
      if (!hasProp(prop, first)) {
        first.properties.unshift(prop);
      }
    } else {
      if (props.callee === TO_HANDLERS) {
        propsWithInjection = createCallExpression(context.helper(MERGE_PROPS), [
          createObjectExpression([prop]),
          props
        ]);
      } else {
        props.arguments.unshift(createObjectExpression([prop]));
      }
    }
    !propsWithInjection && (propsWithInjection = props);
  } else if (props.type === 15) {
    if (!hasProp(prop, props)) {
      props.properties.unshift(prop);
    }
    propsWithInjection = props;
  } else {
    propsWithInjection = createCallExpression(context.helper(MERGE_PROPS), [
      createObjectExpression([prop]),
      props
    ]);
    if (parentCall && parentCall.callee === GUARD_REACTIVE_PROPS) {
      parentCall = callPath[callPath.length - 2];
    }
  }
  if (node.type === 13) {
    if (parentCall) {
      parentCall.arguments[0] = propsWithInjection;
    } else {
      node.props = propsWithInjection;
    }
  } else {
    if (parentCall) {
      parentCall.arguments[0] = propsWithInjection;
    } else {
      node.arguments[2] = propsWithInjection;
    }
  }
}
function hasProp(prop, props) {
  let result = false;
  if (prop.key.type === 4) {
    const propKeyName = prop.key.content;
    result = props.properties.some(
      (p) => p.key.type === 4 && p.key.content === propKeyName
    );
  }
  return result;
}
function toValidAssetId(name, type) {
  return `_${type}_${name.replace(/[^\w]/g, (searchValue, replaceValue) => {
    return searchValue === "-" ? "_" : name.charCodeAt(replaceValue).toString();
  })}`;
}
function hasScopeRef(node, ids) {
  if (!node || Object.keys(ids).length === 0) {
    return false;
  }
  switch (node.type) {
    case 1:
      for (let i = 0; i < node.props.length; i++) {
        const p = node.props[i];
        if (p.type === 7 && (hasScopeRef(p.arg, ids) || hasScopeRef(p.exp, ids))) {
          return true;
        }
      }
      return node.children.some((c) => hasScopeRef(c, ids));
    case 11:
      if (hasScopeRef(node.source, ids)) {
        return true;
      }
      return node.children.some((c) => hasScopeRef(c, ids));
    case 9:
      return node.branches.some((b) => hasScopeRef(b, ids));
    case 10:
      if (hasScopeRef(node.condition, ids)) {
        return true;
      }
      return node.children.some((c) => hasScopeRef(c, ids));
    case 4:
      return !node.isStatic && isSimpleIdentifier(node.content) && !!ids[node.content];
    case 8:
      return node.children.some((c) => isObject3(c) && hasScopeRef(c, ids));
    case 5:
    case 12:
      return hasScopeRef(node.content, ids);
    case 2:
    case 3:
    case 20:
      return false;
    default:
      if (true) ;
      return false;
  }
}
function getMemoedVNodeCall(node) {
  if (node.type === 14 && node.callee === WITH_MEMO) {
    return node.arguments[1].returns;
  } else {
    return node;
  }
}
function parseForExpression(input) {
  const loc = input.loc;
  const exp = input.content;
  const inMatch = exp.match(forAliasRE);
  if (!inMatch) return;
  const [, LHS, RHS] = inMatch;
  const createAliasExpression = (content, offset, asParam = false) => {
    const start = loc.start.offset + offset;
    const end = start + content.length;
    return createExp(
      content,
      false,
      getLoc(start, end),
      0,
      asParam ? 1 : 0
      /* Normal */
    );
  };
  const result = {
    source: createAliasExpression(RHS.trim(), exp.indexOf(RHS, LHS.length)),
    value: void 0,
    key: void 0,
    index: void 0,
    finalized: false
  };
  let valueContent = LHS.trim().replace(stripParensRE, "").trim();
  const trimmedOffset = LHS.indexOf(valueContent);
  const iteratorMatch = valueContent.match(forIteratorRE);
  if (iteratorMatch) {
    valueContent = valueContent.replace(forIteratorRE, "").trim();
    const keyContent = iteratorMatch[1].trim();
    let keyOffset;
    if (keyContent) {
      keyOffset = exp.indexOf(keyContent, trimmedOffset + valueContent.length);
      result.key = createAliasExpression(keyContent, keyOffset, true);
    }
    if (iteratorMatch[2]) {
      const indexContent = iteratorMatch[2].trim();
      if (indexContent) {
        result.index = createAliasExpression(
          indexContent,
          exp.indexOf(
            indexContent,
            result.key ? keyOffset + keyContent.length : trimmedOffset + valueContent.length
          ),
          true
        );
      }
    }
  }
  if (valueContent) {
    result.value = createAliasExpression(valueContent, trimmedOffset, true);
  }
  return result;
}
function getSlice(start, end) {
  return currentInput.slice(start, end);
}
function endOpenTag(end) {
  if (tokenizer.inSFCRoot) {
    currentOpenTag.innerLoc = getLoc(end + 1, end + 1);
  }
  addNode(currentOpenTag);
  const { tag, ns } = currentOpenTag;
  if (ns === 0 && currentOptions.isPreTag(tag)) {
    inPre++;
  }
  if (currentOptions.isVoidTag(tag)) {
    onCloseTag(currentOpenTag, end);
  } else {
    stack.unshift(currentOpenTag);
    if (ns === 1 || ns === 2) {
      tokenizer.inXML = true;
    }
  }
  currentOpenTag = null;
}
function onText(content, start, end) {
  {
    const tag = stack[0] && stack[0].tag;
    if (tag !== "script" && tag !== "style" && content.includes("&")) {
      content = currentOptions.decodeEntities(content, false);
    }
  }
  const parent = stack[0] || currentRoot;
  const lastNode = parent.children[parent.children.length - 1];
  if (lastNode && lastNode.type === 2) {
    lastNode.content += content;
    setLocEnd(lastNode.loc, end);
  } else {
    parent.children.push({
      type: 2,
      content,
      loc: getLoc(start, end)
    });
  }
}
function onCloseTag(el, end, isImplied = false) {
  if (isImplied) {
    setLocEnd(el.loc, backTrack(end, 60));
  } else {
    setLocEnd(el.loc, lookAhead(end, 62) + 1);
  }
  if (tokenizer.inSFCRoot) {
    if (el.children.length) {
      el.innerLoc.end = extend({}, el.children[el.children.length - 1].loc.end);
    } else {
      el.innerLoc.end = extend({}, el.innerLoc.start);
    }
    el.innerLoc.source = getSlice(
      el.innerLoc.start.offset,
      el.innerLoc.end.offset
    );
  }
  const { tag, ns } = el;
  if (!inVPre) {
    if (tag === "slot") {
      el.tagType = 2;
    } else if (isFragmentTemplate(el)) {
      el.tagType = 3;
    } else if (isComponent(el)) {
      el.tagType = 1;
    }
  }
  if (!tokenizer.inRCDATA) {
    el.children = condenseWhitespace(el.children, el.tag);
  }
  if (ns === 0 && currentOptions.isPreTag(tag)) {
    inPre--;
  }
  if (currentVPreBoundary === el) {
    inVPre = tokenizer.inVPre = false;
    currentVPreBoundary = null;
  }
  if (tokenizer.inXML && (stack[0] ? stack[0].ns : currentOptions.ns) === 0) {
    tokenizer.inXML = false;
  }
  {
    const props = el.props;
    if (isCompatEnabled(
      "COMPILER_V_IF_V_FOR_PRECEDENCE",
      currentOptions
    )) {
      let hasIf = false;
      let hasFor = false;
      for (let i = 0; i < props.length; i++) {
        const p = props[i];
        if (p.type === 7) {
          if (p.name === "if") {
            hasIf = true;
          } else if (p.name === "for") {
            hasFor = true;
          }
        }
        if (hasIf && hasFor) {
          warnDeprecation(
            "COMPILER_V_IF_V_FOR_PRECEDENCE",
            currentOptions,
            el.loc
          );
          break;
        }
      }
    }
    if (!tokenizer.inSFCRoot && isCompatEnabled(
      "COMPILER_NATIVE_TEMPLATE",
      currentOptions
    ) && el.tag === "template" && !isFragmentTemplate(el)) {
      warnDeprecation(
        "COMPILER_NATIVE_TEMPLATE",
        currentOptions,
        el.loc
      );
      const parent = stack[0] || currentRoot;
      const index = parent.children.indexOf(el);
      parent.children.splice(index, 1, ...el.children);
    }
    const inlineTemplateProp = props.find(
      (p) => p.type === 6 && p.name === "inline-template"
    );
    if (inlineTemplateProp && checkCompatEnabled(
      "COMPILER_INLINE_TEMPLATE",
      currentOptions,
      inlineTemplateProp.loc
    ) && el.children.length) {
      inlineTemplateProp.value = {
        type: 2,
        content: getSlice(
          el.children[0].loc.start.offset,
          el.children[el.children.length - 1].loc.end.offset
        ),
        loc: inlineTemplateProp.loc
      };
    }
  }
}
function lookAhead(index, c) {
  let i = index;
  while (currentInput.charCodeAt(i) !== c && i < currentInput.length - 1) i++;
  return i;
}
function backTrack(index, c) {
  let i = index;
  while (currentInput.charCodeAt(i) !== c && i >= 0) i--;
  return i;
}
function isFragmentTemplate({ tag, props }) {
  if (tag === "template") {
    for (let i = 0; i < props.length; i++) {
      if (props[i].type === 7 && specialTemplateDir.has(props[i].name)) {
        return true;
      }
    }
  }
  return false;
}
function isComponent({ tag, props }) {
  if (currentOptions.isCustomElement(tag)) {
    return false;
  }
  if (tag === "component" || isUpperCase(tag.charCodeAt(0)) || isCoreComponent(tag) || currentOptions.isBuiltInComponent && currentOptions.isBuiltInComponent(tag) || currentOptions.isNativeTag && !currentOptions.isNativeTag(tag)) {
    return true;
  }
  for (let i = 0; i < props.length; i++) {
    const p = props[i];
    if (p.type === 6) {
      if (p.name === "is" && p.value) {
        if (p.value.content.startsWith("vue:")) {
          return true;
        } else if (checkCompatEnabled(
          "COMPILER_IS_ON_ELEMENT",
          currentOptions,
          p.loc
        )) {
          return true;
        }
      }
    } else if (
      // :is on plain element - only treat as component in compat mode
      p.name === "bind" && isStaticArgOf(p.arg, "is") && checkCompatEnabled(
        "COMPILER_IS_ON_ELEMENT",
        currentOptions,
        p.loc
      )
    ) {
      return true;
    }
  }
  return false;
}
function isUpperCase(c) {
  return c > 64 && c < 91;
}
function condenseWhitespace(nodes, tag) {
  const shouldCondense = currentOptions.whitespace !== "preserve";
  let removedWhitespace = false;
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];
    if (node.type === 2) {
      if (!inPre) {
        if (isAllWhitespace(node.content)) {
          const prev = nodes[i - 1] && nodes[i - 1].type;
          const next = nodes[i + 1] && nodes[i + 1].type;
          if (!prev || !next || shouldCondense && (prev === 3 && (next === 3 || next === 1) || prev === 1 && (next === 3 || next === 1 && hasNewlineChar(node.content)))) {
            removedWhitespace = true;
            nodes[i] = null;
          } else {
            node.content = " ";
          }
        } else if (shouldCondense) {
          node.content = condense(node.content);
        }
      } else {
        node.content = node.content.replace(windowsNewlineRE, "\n");
      }
    }
  }
  if (inPre && tag && currentOptions.isPreTag(tag)) {
    const first = nodes[0];
    if (first && first.type === 2) {
      first.content = first.content.replace(/^\r?\n/, "");
    }
  }
  return removedWhitespace ? nodes.filter(Boolean) : nodes;
}
function isAllWhitespace(str) {
  for (let i = 0; i < str.length; i++) {
    if (!isWhitespace(str.charCodeAt(i))) {
      return false;
    }
  }
  return true;
}
function hasNewlineChar(str) {
  for (let i = 0; i < str.length; i++) {
    const c = str.charCodeAt(i);
    if (c === 10 || c === 13) {
      return true;
    }
  }
  return false;
}
function condense(str) {
  let ret = "";
  let prevCharIsWhitespace = false;
  for (let i = 0; i < str.length; i++) {
    if (isWhitespace(str.charCodeAt(i))) {
      if (!prevCharIsWhitespace) {
        ret += " ";
        prevCharIsWhitespace = true;
      }
    } else {
      ret += str[i];
      prevCharIsWhitespace = false;
    }
  }
  return ret;
}
function addNode(node) {
  (stack[0] || currentRoot).children.push(node);
}
function getLoc(start, end) {
  return {
    start: tokenizer.getPos(start),
    // @ts-expect-error allow late attachment
    end: end == null ? end : tokenizer.getPos(end),
    // @ts-expect-error allow late attachment
    source: end == null ? end : getSlice(start, end)
  };
}
function setLocEnd(loc, end) {
  loc.end = tokenizer.getPos(end);
  loc.source = getSlice(loc.start.offset, end);
}
function dirToAttr(dir) {
  const attr = {
    type: 6,
    name: dir.rawName,
    nameLoc: getLoc(
      dir.loc.start.offset,
      dir.loc.start.offset + dir.rawName.length
    ),
    value: void 0,
    loc: dir.loc
  };
  if (dir.exp) {
    const loc = dir.exp.loc;
    if (loc.end.offset < dir.loc.end.offset) {
      loc.start.offset--;
      loc.start.column--;
      loc.end.offset++;
      loc.end.column++;
    }
    attr.value = {
      type: 2,
      content: dir.exp.content,
      loc
    };
  }
  return attr;
}
function createExp(content, isStatic = false, loc, constType = 0, parseMode = 0) {
  const exp = createSimpleExpression(content, isStatic, loc, constType);
  return exp;
}
function emitError(code2, index, message) {
  currentOptions.onError(
    createCompilerError(code2, getLoc(index, index), void 0, message)
  );
}
function reset() {
  tokenizer.reset();
  currentOpenTag = null;
  currentProp = null;
  currentAttrValue = "";
  currentAttrStartIndex = -1;
  currentAttrEndIndex = -1;
  stack.length = 0;
}
function baseParse(input, options) {
  reset();
  currentInput = input;
  currentOptions = extend({}, defaultParserOptions);
  if (options) {
    let key;
    for (key in options) {
      if (options[key] != null) {
        currentOptions[key] = options[key];
      }
    }
  }
  if (true) {
    if (!currentOptions.decodeEntities) {
      throw new Error(
        `[@vue/compiler-core] decodeEntities option is required in browser builds.`
      );
    }
  }
  tokenizer.mode = currentOptions.parseMode === "html" ? 1 : currentOptions.parseMode === "sfc" ? 2 : 0;
  tokenizer.inXML = currentOptions.ns === 1 || currentOptions.ns === 2;
  const delimiters = options && options.delimiters;
  if (delimiters) {
    tokenizer.delimiterOpen = toCharCodes(delimiters[0]);
    tokenizer.delimiterClose = toCharCodes(delimiters[1]);
  }
  const root = currentRoot = createRoot([], input);
  tokenizer.parse(currentInput);
  root.loc = getLoc(0, input.length);
  root.children = condenseWhitespace(root.children);
  currentRoot = null;
  return root;
}
function cacheStatic(root, context) {
  walk(
    root,
    void 0,
    context,
    // Root node is unfortunately non-hoistable due to potential parent
    // fallthrough attributes.
    isSingleElementRoot(root, root.children[0])
  );
}
function isSingleElementRoot(root, child) {
  const { children } = root;
  return children.length === 1 && child.type === 1 && !isSlotOutlet(child);
}
function walk(node, parent, context, doNotHoistNode = false, inFor = false) {
  const { children } = node;
  const toCache = [];
  for (let i = 0; i < children.length; i++) {
    const child = children[i];
    if (child.type === 1 && child.tagType === 0) {
      const constantType = doNotHoistNode ? 0 : getConstantType(child, context);
      if (constantType > 0) {
        if (constantType >= 2) {
          child.codegenNode.patchFlag = -1;
          toCache.push(child);
          continue;
        }
      } else {
        const codegenNode = child.codegenNode;
        if (codegenNode.type === 13) {
          const flag = codegenNode.patchFlag;
          if ((flag === void 0 || flag === 512 || flag === 1) && getGeneratedPropsConstantType(child, context) >= 2) {
            const props = getNodeProps(child);
            if (props) {
              codegenNode.props = context.hoist(props);
            }
          }
          if (codegenNode.dynamicProps) {
            codegenNode.dynamicProps = context.hoist(codegenNode.dynamicProps);
          }
        }
      }
    } else if (child.type === 12) {
      const constantType = doNotHoistNode ? 0 : getConstantType(child, context);
      if (constantType >= 2) {
        toCache.push(child);
        continue;
      }
    }
    if (child.type === 1) {
      const isComponent2 = child.tagType === 1;
      if (isComponent2) {
        context.scopes.vSlot++;
      }
      walk(child, node, context, false, inFor);
      if (isComponent2) {
        context.scopes.vSlot--;
      }
    } else if (child.type === 11) {
      walk(child, node, context, child.children.length === 1, true);
    } else if (child.type === 9) {
      for (let i2 = 0; i2 < child.branches.length; i2++) {
        walk(
          child.branches[i2],
          node,
          context,
          child.branches[i2].children.length === 1,
          inFor
        );
      }
    }
  }
  let cachedAsArray = false;
  if (toCache.length === children.length && node.type === 1) {
    if (node.tagType === 0 && node.codegenNode && node.codegenNode.type === 13 && isArray3(node.codegenNode.children)) {
      node.codegenNode.children = getCacheExpression(
        createArrayExpression(node.codegenNode.children)
      );
      cachedAsArray = true;
    } else if (node.tagType === 1 && node.codegenNode && node.codegenNode.type === 13 && node.codegenNode.children && !isArray3(node.codegenNode.children) && node.codegenNode.children.type === 15) {
      const slot = getSlotNode(node.codegenNode, "default");
      if (slot) {
        slot.returns = getCacheExpression(
          createArrayExpression(slot.returns)
        );
        cachedAsArray = true;
      }
    } else if (node.tagType === 3 && parent && parent.type === 1 && parent.tagType === 1 && parent.codegenNode && parent.codegenNode.type === 13 && parent.codegenNode.children && !isArray3(parent.codegenNode.children) && parent.codegenNode.children.type === 15) {
      const slotName = findDir(node, "slot", true);
      const slot = slotName && slotName.arg && getSlotNode(parent.codegenNode, slotName.arg);
      if (slot) {
        slot.returns = getCacheExpression(
          createArrayExpression(slot.returns)
        );
        cachedAsArray = true;
      }
    }
  }
  if (!cachedAsArray) {
    for (const child of toCache) {
      child.codegenNode = context.cache(child.codegenNode);
    }
  }
  function getCacheExpression(value) {
    const exp = context.cache(value);
    if (inFor && context.hmr) {
      exp.needArraySpread = true;
    }
    return exp;
  }
  function getSlotNode(node2, name) {
    if (node2.children && !isArray3(node2.children) && node2.children.type === 15) {
      const slot = node2.children.properties.find(
        (p) => p.key === name || p.key.content === name
      );
      return slot && slot.value;
    }
  }
  if (toCache.length && context.transformHoist) {
    context.transformHoist(children, context, node);
  }
}
function getConstantType(node, context) {
  const { constantCache } = context;
  switch (node.type) {
    case 1:
      if (node.tagType !== 0) {
        return 0;
      }
      const cached = constantCache.get(node);
      if (cached !== void 0) {
        return cached;
      }
      const codegenNode = node.codegenNode;
      if (codegenNode.type !== 13) {
        return 0;
      }
      if (codegenNode.isBlock && node.tag !== "svg" && node.tag !== "foreignObject" && node.tag !== "math") {
        return 0;
      }
      if (codegenNode.patchFlag === void 0) {
        let returnType2 = 3;
        const generatedPropsType = getGeneratedPropsConstantType(node, context);
        if (generatedPropsType === 0) {
          constantCache.set(node, 0);
          return 0;
        }
        if (generatedPropsType < returnType2) {
          returnType2 = generatedPropsType;
        }
        for (let i = 0; i < node.children.length; i++) {
          const childType = getConstantType(node.children[i], context);
          if (childType === 0) {
            constantCache.set(node, 0);
            return 0;
          }
          if (childType < returnType2) {
            returnType2 = childType;
          }
        }
        if (returnType2 > 1) {
          for (let i = 0; i < node.props.length; i++) {
            const p = node.props[i];
            if (p.type === 7 && p.name === "bind" && p.exp) {
              const expType = getConstantType(p.exp, context);
              if (expType === 0) {
                constantCache.set(node, 0);
                return 0;
              }
              if (expType < returnType2) {
                returnType2 = expType;
              }
            }
          }
        }
        if (codegenNode.isBlock) {
          for (let i = 0; i < node.props.length; i++) {
            const p = node.props[i];
            if (p.type === 7) {
              constantCache.set(node, 0);
              return 0;
            }
          }
          context.removeHelper(OPEN_BLOCK);
          context.removeHelper(
            getVNodeBlockHelper(context.inSSR, codegenNode.isComponent)
          );
          codegenNode.isBlock = false;
          context.helper(getVNodeHelper(context.inSSR, codegenNode.isComponent));
        }
        constantCache.set(node, returnType2);
        return returnType2;
      } else {
        constantCache.set(node, 0);
        return 0;
      }
    case 2:
    case 3:
      return 3;
    case 9:
    case 11:
    case 10:
      return 0;
    case 5:
    case 12:
      return getConstantType(node.content, context);
    case 4:
      return node.constType;
    case 8:
      let returnType = 3;
      for (let i = 0; i < node.children.length; i++) {
        const child = node.children[i];
        if (isString3(child) || isSymbol2(child)) {
          continue;
        }
        const childType = getConstantType(child, context);
        if (childType === 0) {
          return 0;
        } else if (childType < returnType) {
          returnType = childType;
        }
      }
      return returnType;
    case 20:
      return 2;
    default:
      if (true) ;
      return 0;
  }
}
function getConstantTypeOfHelperCall(value, context) {
  if (value.type === 14 && !isString3(value.callee) && allowHoistedHelperSet.has(value.callee)) {
    const arg = value.arguments[0];
    if (arg.type === 4) {
      return getConstantType(arg, context);
    } else if (arg.type === 14) {
      return getConstantTypeOfHelperCall(arg, context);
    }
  }
  return 0;
}
function getGeneratedPropsConstantType(node, context) {
  let returnType = 3;
  const props = getNodeProps(node);
  if (props && props.type === 15) {
    const { properties } = props;
    for (let i = 0; i < properties.length; i++) {
      const { key, value } = properties[i];
      const keyType = getConstantType(key, context);
      if (keyType === 0) {
        return keyType;
      }
      if (keyType < returnType) {
        returnType = keyType;
      }
      let valueType;
      if (value.type === 4) {
        valueType = getConstantType(value, context);
      } else if (value.type === 14) {
        valueType = getConstantTypeOfHelperCall(value, context);
      } else {
        valueType = 0;
      }
      if (valueType === 0) {
        return valueType;
      }
      if (valueType < returnType) {
        returnType = valueType;
      }
    }
  }
  return returnType;
}
function getNodeProps(node) {
  const codegenNode = node.codegenNode;
  if (codegenNode.type === 13) {
    return codegenNode.props;
  }
}
function createTransformContext(root, {
  filename = "",
  prefixIdentifiers = false,
  hoistStatic = false,
  hmr = false,
  cacheHandlers = false,
  nodeTransforms = [],
  directiveTransforms = {},
  transformHoist = null,
  isBuiltInComponent = NOOP,
  isCustomElement = NOOP,
  expressionPlugins = [],
  scopeId = null,
  slotted = true,
  ssr = false,
  inSSR = false,
  ssrCssVars = ``,
  bindingMetadata = EMPTY_OBJ,
  inline = false,
  isTS = false,
  onError = defaultOnError2,
  onWarn = defaultOnWarn,
  compatConfig
}) {
  const nameMatch = filename.replace(/\?.*$/, "").match(/([^/\\]+)\.\w+$/);
  const context = {
    // options
    filename,
    selfName: nameMatch && capitalize2(camelize(nameMatch[1])),
    prefixIdentifiers,
    hoistStatic,
    hmr,
    cacheHandlers,
    nodeTransforms,
    directiveTransforms,
    transformHoist,
    isBuiltInComponent,
    isCustomElement,
    expressionPlugins,
    scopeId,
    slotted,
    ssr,
    inSSR,
    ssrCssVars,
    bindingMetadata,
    inline,
    isTS,
    onError,
    onWarn,
    compatConfig,
    // state
    root,
    helpers: /* @__PURE__ */ new Map(),
    components: /* @__PURE__ */ new Set(),
    directives: /* @__PURE__ */ new Set(),
    hoists: [],
    imports: [],
    cached: [],
    constantCache: /* @__PURE__ */ new WeakMap(),
    temps: 0,
    identifiers: /* @__PURE__ */ Object.create(null),
    scopes: {
      vFor: 0,
      vSlot: 0,
      vPre: 0,
      vOnce: 0
    },
    parent: null,
    grandParent: null,
    currentNode: root,
    childIndex: 0,
    inVOnce: false,
    // methods
    helper(name) {
      const count = context.helpers.get(name) || 0;
      context.helpers.set(name, count + 1);
      return name;
    },
    removeHelper(name) {
      const count = context.helpers.get(name);
      if (count) {
        const currentCount = count - 1;
        if (!currentCount) {
          context.helpers.delete(name);
        } else {
          context.helpers.set(name, currentCount);
        }
      }
    },
    helperString(name) {
      return `_${helperNameMap[context.helper(name)]}`;
    },
    replaceNode(node) {
      if (true) {
        if (!context.currentNode) {
          throw new Error(`Node being replaced is already removed.`);
        }
        if (!context.parent) {
          throw new Error(`Cannot replace root node.`);
        }
      }
      context.parent.children[context.childIndex] = context.currentNode = node;
    },
    removeNode(node) {
      if (!context.parent) {
        throw new Error(`Cannot remove root node.`);
      }
      const list = context.parent.children;
      const removalIndex = node ? list.indexOf(node) : context.currentNode ? context.childIndex : -1;
      if (removalIndex < 0) {
        throw new Error(`node being removed is not a child of current parent`);
      }
      if (!node || node === context.currentNode) {
        context.currentNode = null;
        context.onNodeRemoved();
      } else {
        if (context.childIndex > removalIndex) {
          context.childIndex--;
          context.onNodeRemoved();
        }
      }
      context.parent.children.splice(removalIndex, 1);
    },
    onNodeRemoved: NOOP,
    addIdentifiers(exp) {
    },
    removeIdentifiers(exp) {
    },
    hoist(exp) {
      if (isString3(exp)) exp = createSimpleExpression(exp);
      context.hoists.push(exp);
      const identifier = createSimpleExpression(
        `_hoisted_${context.hoists.length}`,
        false,
        exp.loc,
        2
      );
      identifier.hoisted = exp;
      return identifier;
    },
    cache(exp, isVNode = false) {
      const cacheExp = createCacheExpression(
        context.cached.length,
        exp,
        isVNode
      );
      context.cached.push(cacheExp);
      return cacheExp;
    }
  };
  {
    context.filters = /* @__PURE__ */ new Set();
  }
  return context;
}
function transform2(root, options) {
  const context = createTransformContext(root, options);
  traverseNode2(root, context);
  if (options.hoistStatic) {
    cacheStatic(root, context);
  }
  if (!options.ssr) {
    createRootCodegen(root, context);
  }
  root.helpers = /* @__PURE__ */ new Set([...context.helpers.keys()]);
  root.components = [...context.components];
  root.directives = [...context.directives];
  root.imports = context.imports;
  root.hoists = context.hoists;
  root.temps = context.temps;
  root.cached = context.cached;
  root.transformed = true;
  {
    root.filters = [...context.filters];
  }
}
function createRootCodegen(root, context) {
  const { helper } = context;
  const { children } = root;
  if (children.length === 1) {
    const child = children[0];
    if (isSingleElementRoot(root, child) && child.codegenNode) {
      const codegenNode = child.codegenNode;
      if (codegenNode.type === 13) {
        convertToBlock(codegenNode, context);
      }
      root.codegenNode = codegenNode;
    } else {
      root.codegenNode = child;
    }
  } else if (children.length > 1) {
    let patchFlag = 64;
    let patchFlagText = PatchFlagNames[64];
    if (children.filter((c) => c.type !== 3).length === 1) {
      patchFlag |= 2048;
      patchFlagText += `, ${PatchFlagNames[2048]}`;
    }
    root.codegenNode = createVNodeCall(
      context,
      helper(FRAGMENT),
      void 0,
      root.children,
      patchFlag,
      void 0,
      void 0,
      true,
      void 0,
      false
    );
  } else ;
}
function traverseChildren(parent, context) {
  let i = 0;
  const nodeRemoved = () => {
    i--;
  };
  for (; i < parent.children.length; i++) {
    const child = parent.children[i];
    if (isString3(child)) continue;
    context.grandParent = context.parent;
    context.parent = parent;
    context.childIndex = i;
    context.onNodeRemoved = nodeRemoved;
    traverseNode2(child, context);
  }
}
function traverseNode2(node, context) {
  context.currentNode = node;
  const { nodeTransforms } = context;
  const exitFns = [];
  for (let i2 = 0; i2 < nodeTransforms.length; i2++) {
    const onExit = nodeTransforms[i2](node, context);
    if (onExit) {
      if (isArray3(onExit)) {
        exitFns.push(...onExit);
      } else {
        exitFns.push(onExit);
      }
    }
    if (!context.currentNode) {
      return;
    } else {
      node = context.currentNode;
    }
  }
  switch (node.type) {
    case 3:
      if (!context.ssr) {
        context.helper(CREATE_COMMENT);
      }
      break;
    case 5:
      if (!context.ssr) {
        context.helper(TO_DISPLAY_STRING);
      }
      break;
    case 9:
      for (let i2 = 0; i2 < node.branches.length; i2++) {
        traverseNode2(node.branches[i2], context);
      }
      break;
    case 10:
    case 11:
    case 1:
    case 0:
      traverseChildren(node, context);
      break;
  }
  context.currentNode = node;
  let i = exitFns.length;
  while (i--) {
    exitFns[i]();
  }
}
function createStructuralDirectiveTransform(name, fn) {
  const matches = isString3(name) ? (n) => n === name : (n) => name.test(n);
  return (node, context) => {
    if (node.type === 1) {
      const { props } = node;
      if (node.tagType === 3 && props.some(isVSlot)) {
        return;
      }
      const exitFns = [];
      for (let i = 0; i < props.length; i++) {
        const prop = props[i];
        if (prop.type === 7 && matches(prop.name)) {
          props.splice(i, 1);
          i--;
          const onExit = fn(node, prop, context);
          if (onExit) exitFns.push(onExit);
        }
      }
      return exitFns;
    }
  };
}
function createCodegenContext(ast, {
  mode = "function",
  prefixIdentifiers = mode === "module",
  sourceMap = false,
  filename = `template.vue.html`,
  scopeId = null,
  optimizeImports = false,
  runtimeGlobalName = `Vue`,
  runtimeModuleName = `vue`,
  ssrRuntimeModuleName = "vue/server-renderer",
  ssr = false,
  isTS = false,
  inSSR = false
}) {
  const context = {
    mode,
    prefixIdentifiers,
    sourceMap,
    filename,
    scopeId,
    optimizeImports,
    runtimeGlobalName,
    runtimeModuleName,
    ssrRuntimeModuleName,
    ssr,
    isTS,
    inSSR,
    source: ast.source,
    code: ``,
    column: 1,
    line: 1,
    offset: 0,
    indentLevel: 0,
    pure: false,
    map: void 0,
    helper(key) {
      return `_${helperNameMap[key]}`;
    },
    push(code2, newlineIndex = -2, node) {
      context.code += code2;
    },
    indent() {
      newline(++context.indentLevel);
    },
    deindent(withoutNewLine = false) {
      if (withoutNewLine) {
        --context.indentLevel;
      } else {
        newline(--context.indentLevel);
      }
    },
    newline() {
      newline(context.indentLevel);
    }
  };
  function newline(n) {
    context.push(
      "\n" + `  `.repeat(n),
      0
      /* Start */
    );
  }
  return context;
}
function generate2(ast, options = {}) {
  const context = createCodegenContext(ast, options);
  if (options.onContextCreated) options.onContextCreated(context);
  const {
    mode,
    push,
    prefixIdentifiers,
    indent,
    deindent,
    newline,
    scopeId,
    ssr
  } = context;
  const helpers = Array.from(ast.helpers);
  const hasHelpers = helpers.length > 0;
  const useWithBlock = !prefixIdentifiers && mode !== "module";
  const preambleContext = context;
  {
    genFunctionPreamble(ast, preambleContext);
  }
  const functionName = ssr ? `ssrRender` : `render`;
  const args = ssr ? ["_ctx", "_push", "_parent", "_attrs"] : ["_ctx", "_cache"];
  const signature = args.join(", ");
  {
    push(`function ${functionName}(${signature}) {`);
  }
  indent();
  if (useWithBlock) {
    push(`with (_ctx) {`);
    indent();
    if (hasHelpers) {
      push(
        `const { ${helpers.map(aliasHelper).join(", ")} } = _Vue
`,
        -1
        /* End */
      );
      newline();
    }
  }
  if (ast.components.length) {
    genAssets(ast.components, "component", context);
    if (ast.directives.length || ast.temps > 0) {
      newline();
    }
  }
  if (ast.directives.length) {
    genAssets(ast.directives, "directive", context);
    if (ast.temps > 0) {
      newline();
    }
  }
  if (ast.filters && ast.filters.length) {
    newline();
    genAssets(ast.filters, "filter", context);
    newline();
  }
  if (ast.temps > 0) {
    push(`let `);
    for (let i = 0; i < ast.temps; i++) {
      push(`${i > 0 ? `, ` : ``}_temp${i}`);
    }
  }
  if (ast.components.length || ast.directives.length || ast.temps) {
    push(
      `
`,
      0
      /* Start */
    );
    newline();
  }
  if (!ssr) {
    push(`return `);
  }
  if (ast.codegenNode) {
    genNode(ast.codegenNode, context);
  } else {
    push(`null`);
  }
  if (useWithBlock) {
    deindent();
    push(`}`);
  }
  deindent();
  push(`}`);
  return {
    ast,
    code: context.code,
    preamble: ``,
    map: context.map ? context.map.toJSON() : void 0
  };
}
function genFunctionPreamble(ast, context) {
  const {
    ssr,
    prefixIdentifiers,
    push,
    newline,
    runtimeModuleName,
    runtimeGlobalName,
    ssrRuntimeModuleName
  } = context;
  const VueBinding = runtimeGlobalName;
  const helpers = Array.from(ast.helpers);
  if (helpers.length > 0) {
    {
      push(
        `const _Vue = ${VueBinding}
`,
        -1
        /* End */
      );
      if (ast.hoists.length) {
        const staticHelpers = [
          CREATE_VNODE,
          CREATE_ELEMENT_VNODE,
          CREATE_COMMENT,
          CREATE_TEXT,
          CREATE_STATIC
        ].filter((helper) => helpers.includes(helper)).map(aliasHelper).join(", ");
        push(
          `const { ${staticHelpers} } = _Vue
`,
          -1
          /* End */
        );
      }
    }
  }
  genHoists(ast.hoists, context);
  newline();
  push(`return `);
}
function genAssets(assets, type, { helper, push, newline, isTS }) {
  const resolver = helper(
    type === "filter" ? RESOLVE_FILTER : type === "component" ? RESOLVE_COMPONENT : RESOLVE_DIRECTIVE
  );
  for (let i = 0; i < assets.length; i++) {
    let id = assets[i];
    const maybeSelfReference = id.endsWith("__self");
    if (maybeSelfReference) {
      id = id.slice(0, -6);
    }
    push(
      `const ${toValidAssetId(id, type)} = ${resolver}(${JSON.stringify(id)}${maybeSelfReference ? `, true` : ``})${isTS ? `!` : ``}`
    );
    if (i < assets.length - 1) {
      newline();
    }
  }
}
function genHoists(hoists, context) {
  if (!hoists.length) {
    return;
  }
  context.pure = true;
  const { push, newline } = context;
  newline();
  for (let i = 0; i < hoists.length; i++) {
    const exp = hoists[i];
    if (exp) {
      push(`const _hoisted_${i + 1} = `);
      genNode(exp, context);
      newline();
    }
  }
  context.pure = false;
}
function isText(n) {
  return isString3(n) || n.type === 4 || n.type === 2 || n.type === 5 || n.type === 8;
}
function genNodeListAsArray(nodes, context) {
  const multilines = nodes.length > 3 || nodes.some((n) => isArray3(n) || !isText(n));
  context.push(`[`);
  multilines && context.indent();
  genNodeList(nodes, context, multilines);
  multilines && context.deindent();
  context.push(`]`);
}
function genNodeList(nodes, context, multilines = false, comma = true) {
  const { push, newline } = context;
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];
    if (isString3(node)) {
      push(
        node,
        -3
        /* Unknown */
      );
    } else if (isArray3(node)) {
      genNodeListAsArray(node, context);
    } else {
      genNode(node, context);
    }
    if (i < nodes.length - 1) {
      if (multilines) {
        comma && push(",");
        newline();
      } else {
        comma && push(", ");
      }
    }
  }
}
function genNode(node, context) {
  if (isString3(node)) {
    context.push(
      node,
      -3
      /* Unknown */
    );
    return;
  }
  if (isSymbol2(node)) {
    context.push(context.helper(node));
    return;
  }
  switch (node.type) {
    case 1:
    case 9:
    case 11:
      assert(
        node.codegenNode != null,
        `Codegen node is missing for element/if/for node. Apply appropriate transforms first.`
      );
      genNode(node.codegenNode, context);
      break;
    case 2:
      genText(node, context);
      break;
    case 4:
      genExpression(node, context);
      break;
    case 5:
      genInterpolation(node, context);
      break;
    case 12:
      genNode(node.codegenNode, context);
      break;
    case 8:
      genCompoundExpression(node, context);
      break;
    case 3:
      genComment(node, context);
      break;
    case 13:
      genVNodeCall(node, context);
      break;
    case 14:
      genCallExpression(node, context);
      break;
    case 15:
      genObjectExpression(node, context);
      break;
    case 17:
      genArrayExpression(node, context);
      break;
    case 18:
      genFunctionExpression(node, context);
      break;
    case 19:
      genConditionalExpression(node, context);
      break;
    case 20:
      genCacheExpression(node, context);
      break;
    case 21:
      genNodeList(node.body, context, true, false);
      break;
    case 22:
      break;
    case 23:
      break;
    case 24:
      break;
    case 25:
      break;
    case 26:
      break;
    case 10:
      break;
    default:
      if (true) {
        assert(false, `unhandled codegen node type: ${node.type}`);
        const exhaustiveCheck = node;
        return exhaustiveCheck;
      }
  }
}
function genText(node, context) {
  context.push(JSON.stringify(node.content), -3, node);
}
function genExpression(node, context) {
  const { content, isStatic } = node;
  context.push(
    isStatic ? JSON.stringify(content) : content,
    -3,
    node
  );
}
function genInterpolation(node, context) {
  const { push, helper, pure } = context;
  if (pure) push(PURE_ANNOTATION);
  push(`${helper(TO_DISPLAY_STRING)}(`);
  genNode(node.content, context);
  push(`)`);
}
function genCompoundExpression(node, context) {
  for (let i = 0; i < node.children.length; i++) {
    const child = node.children[i];
    if (isString3(child)) {
      context.push(
        child,
        -3
        /* Unknown */
      );
    } else {
      genNode(child, context);
    }
  }
}
function genExpressionAsPropertyKey(node, context) {
  const { push } = context;
  if (node.type === 8) {
    push(`[`);
    genCompoundExpression(node, context);
    push(`]`);
  } else if (node.isStatic) {
    const text = isSimpleIdentifier(node.content) ? node.content : JSON.stringify(node.content);
    push(text, -2, node);
  } else {
    push(`[${node.content}]`, -3, node);
  }
}
function genComment(node, context) {
  const { push, helper, pure } = context;
  if (pure) {
    push(PURE_ANNOTATION);
  }
  push(
    `${helper(CREATE_COMMENT)}(${JSON.stringify(node.content)})`,
    -3,
    node
  );
}
function genVNodeCall(node, context) {
  const { push, helper, pure } = context;
  const {
    tag,
    props,
    children,
    patchFlag,
    dynamicProps,
    directives,
    isBlock,
    disableTracking,
    isComponent: isComponent2
  } = node;
  let patchFlagString;
  if (patchFlag) {
    if (true) {
      if (patchFlag < 0) {
        patchFlagString = patchFlag + ` /* ${PatchFlagNames[patchFlag]} */`;
      } else {
        const flagNames = Object.keys(PatchFlagNames).map(Number).filter((n) => n > 0 && patchFlag & n).map((n) => PatchFlagNames[n]).join(`, `);
        patchFlagString = patchFlag + ` /* ${flagNames} */`;
      }
    } else {
      patchFlagString = String(patchFlag);
    }
  }
  if (directives) {
    push(helper(WITH_DIRECTIVES) + `(`);
  }
  if (isBlock) {
    push(`(${helper(OPEN_BLOCK)}(${disableTracking ? `true` : ``}), `);
  }
  if (pure) {
    push(PURE_ANNOTATION);
  }
  const callHelper = isBlock ? getVNodeBlockHelper(context.inSSR, isComponent2) : getVNodeHelper(context.inSSR, isComponent2);
  push(helper(callHelper) + `(`, -2, node);
  genNodeList(
    genNullableArgs([tag, props, children, patchFlagString, dynamicProps]),
    context
  );
  push(`)`);
  if (isBlock) {
    push(`)`);
  }
  if (directives) {
    push(`, `);
    genNode(directives, context);
    push(`)`);
  }
}
function genNullableArgs(args) {
  let i = args.length;
  while (i--) {
    if (args[i] != null) break;
  }
  return args.slice(0, i + 1).map((arg) => arg || `null`);
}
function genCallExpression(node, context) {
  const { push, helper, pure } = context;
  const callee = isString3(node.callee) ? node.callee : helper(node.callee);
  if (pure) {
    push(PURE_ANNOTATION);
  }
  push(callee + `(`, -2, node);
  genNodeList(node.arguments, context);
  push(`)`);
}
function genObjectExpression(node, context) {
  const { push, indent, deindent, newline } = context;
  const { properties } = node;
  if (!properties.length) {
    push(`{}`, -2, node);
    return;
  }
  const multilines = properties.length > 1 || properties.some((p) => p.value.type !== 4);
  push(multilines ? `{` : `{ `);
  multilines && indent();
  for (let i = 0; i < properties.length; i++) {
    const { key, value } = properties[i];
    genExpressionAsPropertyKey(key, context);
    push(`: `);
    genNode(value, context);
    if (i < properties.length - 1) {
      push(`,`);
      newline();
    }
  }
  multilines && deindent();
  push(multilines ? `}` : ` }`);
}
function genArrayExpression(node, context) {
  genNodeListAsArray(node.elements, context);
}
function genFunctionExpression(node, context) {
  const { push, indent, deindent } = context;
  const { params, returns, body, newline, isSlot } = node;
  if (isSlot) {
    push(`_${helperNameMap[WITH_CTX]}(`);
  }
  push(`(`, -2, node);
  if (isArray3(params)) {
    genNodeList(params, context);
  } else if (params) {
    genNode(params, context);
  }
  push(`) => `);
  if (newline || body) {
    push(`{`);
    indent();
  }
  if (returns) {
    if (newline) {
      push(`return `);
    }
    if (isArray3(returns)) {
      genNodeListAsArray(returns, context);
    } else {
      genNode(returns, context);
    }
  } else if (body) {
    genNode(body, context);
  }
  if (newline || body) {
    deindent();
    push(`}`);
  }
  if (isSlot) {
    if (node.isNonScopedSlot) {
      push(`, undefined, true`);
    }
    push(`)`);
  }
}
function genConditionalExpression(node, context) {
  const { test, consequent, alternate, newline: needNewline } = node;
  const { push, indent, deindent, newline } = context;
  if (test.type === 4) {
    const needsParens = !isSimpleIdentifier(test.content);
    needsParens && push(`(`);
    genExpression(test, context);
    needsParens && push(`)`);
  } else {
    push(`(`);
    genNode(test, context);
    push(`)`);
  }
  needNewline && indent();
  context.indentLevel++;
  needNewline || push(` `);
  push(`? `);
  genNode(consequent, context);
  context.indentLevel--;
  needNewline && newline();
  needNewline || push(` `);
  push(`: `);
  const isNested = alternate.type === 19;
  if (!isNested) {
    context.indentLevel++;
  }
  genNode(alternate, context);
  if (!isNested) {
    context.indentLevel--;
  }
  needNewline && deindent(
    true
    /* without newline */
  );
}
function genCacheExpression(node, context) {
  const { push, helper, indent, deindent, newline } = context;
  const { needPauseTracking, needArraySpread } = node;
  if (needArraySpread) {
    push(`[...(`);
  }
  push(`_cache[${node.index}] || (`);
  if (needPauseTracking) {
    indent();
    push(`${helper(SET_BLOCK_TRACKING)}(-1),`);
    newline();
    push(`(`);
  }
  push(`_cache[${node.index}] = `);
  genNode(node.value, context);
  if (needPauseTracking) {
    push(`).cacheIndex = ${node.index},`);
    newline();
    push(`${helper(SET_BLOCK_TRACKING)}(1),`);
    newline();
    push(`_cache[${node.index}]`);
    deindent();
  }
  push(`)`);
  if (needArraySpread) {
    push(`)]`);
  }
}
function validateBrowserExpression(node, context, asParams = false, asRawStatements = false) {
  const exp = node.content;
  if (!exp.trim()) {
    return;
  }
  try {
    new Function(
      asRawStatements ? ` ${exp} ` : `return ${asParams ? `(${exp}) => {}` : `(${exp})`}`
    );
  } catch (e) {
    let message = e.message;
    const keywordMatch = exp.replace(stripStringRE, "").match(prohibitedKeywordRE);
    if (keywordMatch) {
      message = `avoid using JavaScript keyword as property name: "${keywordMatch[0]}"`;
    }
    context.onError(
      createCompilerError(
        45,
        node.loc,
        void 0,
        message
      )
    );
  }
}
function processExpression(node, context, asParams = false, asRawStatements = false, localVars = Object.create(context.identifiers)) {
  {
    if (true) {
      validateBrowserExpression(node, context, asParams, asRawStatements);
    }
    return node;
  }
}
function stringifyExpression(exp) {
  if (isString3(exp)) {
    return exp;
  } else if (exp.type === 4) {
    return exp.content;
  } else {
    return exp.children.map(stringifyExpression).join("");
  }
}
function processIf(node, dir, context, processCodegen) {
  if (dir.name !== "else" && (!dir.exp || !dir.exp.content.trim())) {
    const loc = dir.exp ? dir.exp.loc : node.loc;
    context.onError(
      createCompilerError(28, dir.loc)
    );
    dir.exp = createSimpleExpression(`true`, false, loc);
  }
  if (dir.exp) {
    validateBrowserExpression(dir.exp, context);
  }
  if (dir.name === "if") {
    const branch = createIfBranch(node, dir);
    const ifNode = {
      type: 9,
      loc: node.loc,
      branches: [branch]
    };
    context.replaceNode(ifNode);
    if (processCodegen) {
      return processCodegen(ifNode, branch, true);
    }
  } else {
    const siblings = context.parent.children;
    const comments = [];
    let i = siblings.indexOf(node);
    while (i-- >= -1) {
      const sibling = siblings[i];
      if (sibling && sibling.type === 3) {
        context.removeNode(sibling);
        comments.unshift(sibling);
        continue;
      }
      if (sibling && sibling.type === 2 && !sibling.content.trim().length) {
        context.removeNode(sibling);
        continue;
      }
      if (sibling && sibling.type === 9) {
        if (dir.name === "else-if" && sibling.branches[sibling.branches.length - 1].condition === void 0) {
          context.onError(
            createCompilerError(30, node.loc)
          );
        }
        context.removeNode();
        const branch = createIfBranch(node, dir);
        if (comments.length && // #3619 ignore comments if the v-if is direct child of <transition>
        !(context.parent && context.parent.type === 1 && (context.parent.tag === "transition" || context.parent.tag === "Transition"))) {
          branch.children = [...comments, ...branch.children];
        }
        if (true) {
          const key = branch.userKey;
          if (key) {
            sibling.branches.forEach(({ userKey }) => {
              if (isSameKey(userKey, key)) {
                context.onError(
                  createCompilerError(
                    29,
                    branch.userKey.loc
                  )
                );
              }
            });
          }
        }
        sibling.branches.push(branch);
        const onExit = processCodegen && processCodegen(sibling, branch, false);
        traverseNode2(branch, context);
        if (onExit) onExit();
        context.currentNode = null;
      } else {
        context.onError(
          createCompilerError(30, node.loc)
        );
      }
      break;
    }
  }
}
function createIfBranch(node, dir) {
  const isTemplateIf = node.tagType === 3;
  return {
    type: 10,
    loc: node.loc,
    condition: dir.name === "else" ? void 0 : dir.exp,
    children: isTemplateIf && !findDir(node, "for") ? node.children : [node],
    userKey: findProp(node, `key`),
    isTemplateIf
  };
}
function createCodegenNodeForBranch(branch, keyIndex, context) {
  if (branch.condition) {
    return createConditionalExpression(
      branch.condition,
      createChildrenCodegenNode(branch, keyIndex, context),
      // make sure to pass in asBlock: true so that the comment node call
      // closes the current block.
      createCallExpression(context.helper(CREATE_COMMENT), [
        true ? '"v-if"' : '""',
        "true"
      ])
    );
  } else {
    return createChildrenCodegenNode(branch, keyIndex, context);
  }
}
function createChildrenCodegenNode(branch, keyIndex, context) {
  const { helper } = context;
  const keyProperty = createObjectProperty(
    `key`,
    createSimpleExpression(
      `${keyIndex}`,
      false,
      locStub,
      2
    )
  );
  const { children } = branch;
  const firstChild = children[0];
  const needFragmentWrapper = children.length !== 1 || firstChild.type !== 1;
  if (needFragmentWrapper) {
    if (children.length === 1 && firstChild.type === 11) {
      const vnodeCall = firstChild.codegenNode;
      injectProp(vnodeCall, keyProperty, context);
      return vnodeCall;
    } else {
      let patchFlag = 64;
      let patchFlagText = PatchFlagNames[64];
      if (!branch.isTemplateIf && children.filter((c) => c.type !== 3).length === 1) {
        patchFlag |= 2048;
        patchFlagText += `, ${PatchFlagNames[2048]}`;
      }
      return createVNodeCall(
        context,
        helper(FRAGMENT),
        createObjectExpression([keyProperty]),
        children,
        patchFlag,
        void 0,
        void 0,
        true,
        false,
        false,
        branch.loc
      );
    }
  } else {
    const ret = firstChild.codegenNode;
    const vnodeCall = getMemoedVNodeCall(ret);
    if (vnodeCall.type === 13) {
      convertToBlock(vnodeCall, context);
    }
    injectProp(vnodeCall, keyProperty, context);
    return ret;
  }
}
function isSameKey(a, b) {
  if (!a || a.type !== b.type) {
    return false;
  }
  if (a.type === 6) {
    if (a.value.content !== b.value.content) {
      return false;
    }
  } else {
    const exp = a.exp;
    const branchExp = b.exp;
    if (exp.type !== branchExp.type) {
      return false;
    }
    if (exp.type !== 4 || exp.isStatic !== branchExp.isStatic || exp.content !== branchExp.content) {
      return false;
    }
  }
  return true;
}
function getParentCondition(node) {
  while (true) {
    if (node.type === 19) {
      if (node.alternate.type === 19) {
        node = node.alternate;
      } else {
        return node;
      }
    } else if (node.type === 20) {
      node = node.value;
    }
  }
}
function processFor(node, dir, context, processCodegen) {
  if (!dir.exp) {
    context.onError(
      createCompilerError(31, dir.loc)
    );
    return;
  }
  const parseResult = dir.forParseResult;
  if (!parseResult) {
    context.onError(
      createCompilerError(32, dir.loc)
    );
    return;
  }
  finalizeForParseResult(parseResult, context);
  const { addIdentifiers, removeIdentifiers, scopes } = context;
  const { source, value, key, index } = parseResult;
  const forNode = {
    type: 11,
    loc: dir.loc,
    source,
    valueAlias: value,
    keyAlias: key,
    objectIndexAlias: index,
    parseResult,
    children: isTemplateNode(node) ? node.children : [node]
  };
  context.replaceNode(forNode);
  scopes.vFor++;
  const onExit = processCodegen && processCodegen(forNode);
  return () => {
    scopes.vFor--;
    if (onExit) onExit();
  };
}
function finalizeForParseResult(result, context) {
  if (result.finalized) return;
  if (true) {
    validateBrowserExpression(result.source, context);
    if (result.key) {
      validateBrowserExpression(
        result.key,
        context,
        true
      );
    }
    if (result.index) {
      validateBrowserExpression(
        result.index,
        context,
        true
      );
    }
    if (result.value) {
      validateBrowserExpression(
        result.value,
        context,
        true
      );
    }
  }
  result.finalized = true;
}
function createForLoopParams({ value, key, index }, memoArgs = []) {
  return createParamsList([value, key, index, ...memoArgs]);
}
function createParamsList(args) {
  let i = args.length;
  while (i--) {
    if (args[i]) break;
  }
  return args.slice(0, i + 1).map((arg, i2) => arg || createSimpleExpression(`_`.repeat(i2 + 1), false));
}
function buildSlots(node, context, buildSlotFn = buildClientSlotFn) {
  context.helper(WITH_CTX);
  const { children, loc } = node;
  const slotsProperties = [];
  const dynamicSlots = [];
  let hasDynamicSlots = context.scopes.vSlot > 0 || context.scopes.vFor > 0;
  const onComponentSlot = findDir(node, "slot", true);
  if (onComponentSlot) {
    const { arg, exp } = onComponentSlot;
    if (arg && !isStaticExp(arg)) {
      hasDynamicSlots = true;
    }
    slotsProperties.push(
      createObjectProperty(
        arg || createSimpleExpression("default", true),
        buildSlotFn(exp, void 0, children, loc)
      )
    );
  }
  let hasTemplateSlots = false;
  let hasNamedDefaultSlot = false;
  const implicitDefaultChildren = [];
  const seenSlotNames = /* @__PURE__ */ new Set();
  let conditionalBranchIndex = 0;
  for (let i = 0; i < children.length; i++) {
    const slotElement = children[i];
    let slotDir;
    if (!isTemplateNode(slotElement) || !(slotDir = findDir(slotElement, "slot", true))) {
      if (slotElement.type !== 3) {
        implicitDefaultChildren.push(slotElement);
      }
      continue;
    }
    if (onComponentSlot) {
      context.onError(
        createCompilerError(37, slotDir.loc)
      );
      break;
    }
    hasTemplateSlots = true;
    const { children: slotChildren, loc: slotLoc } = slotElement;
    const {
      arg: slotName = createSimpleExpression(`default`, true),
      exp: slotProps,
      loc: dirLoc
    } = slotDir;
    let staticSlotName;
    if (isStaticExp(slotName)) {
      staticSlotName = slotName ? slotName.content : `default`;
    } else {
      hasDynamicSlots = true;
    }
    const vFor = findDir(slotElement, "for");
    const slotFunction = buildSlotFn(slotProps, vFor, slotChildren, slotLoc);
    let vIf;
    let vElse;
    if (vIf = findDir(slotElement, "if")) {
      hasDynamicSlots = true;
      dynamicSlots.push(
        createConditionalExpression(
          vIf.exp,
          buildDynamicSlot(slotName, slotFunction, conditionalBranchIndex++),
          defaultFallback
        )
      );
    } else if (vElse = findDir(
      slotElement,
      /^else(-if)?$/,
      true
      /* allowEmpty */
    )) {
      let j = i;
      let prev;
      while (j--) {
        prev = children[j];
        if (prev.type !== 3) {
          break;
        }
      }
      if (prev && isTemplateNode(prev) && findDir(prev, /^(else-)?if$/)) {
        let conditional = dynamicSlots[dynamicSlots.length - 1];
        while (conditional.alternate.type === 19) {
          conditional = conditional.alternate;
        }
        conditional.alternate = vElse.exp ? createConditionalExpression(
          vElse.exp,
          buildDynamicSlot(
            slotName,
            slotFunction,
            conditionalBranchIndex++
          ),
          defaultFallback
        ) : buildDynamicSlot(slotName, slotFunction, conditionalBranchIndex++);
      } else {
        context.onError(
          createCompilerError(30, vElse.loc)
        );
      }
    } else if (vFor) {
      hasDynamicSlots = true;
      const parseResult = vFor.forParseResult;
      if (parseResult) {
        finalizeForParseResult(parseResult, context);
        dynamicSlots.push(
          createCallExpression(context.helper(RENDER_LIST), [
            parseResult.source,
            createFunctionExpression(
              createForLoopParams(parseResult),
              buildDynamicSlot(slotName, slotFunction),
              true
            )
          ])
        );
      } else {
        context.onError(
          createCompilerError(
            32,
            vFor.loc
          )
        );
      }
    } else {
      if (staticSlotName) {
        if (seenSlotNames.has(staticSlotName)) {
          context.onError(
            createCompilerError(
              38,
              dirLoc
            )
          );
          continue;
        }
        seenSlotNames.add(staticSlotName);
        if (staticSlotName === "default") {
          hasNamedDefaultSlot = true;
        }
      }
      slotsProperties.push(createObjectProperty(slotName, slotFunction));
    }
  }
  if (!onComponentSlot) {
    const buildDefaultSlotProperty = (props, children2) => {
      const fn = buildSlotFn(props, void 0, children2, loc);
      if (context.compatConfig) {
        fn.isNonScopedSlot = true;
      }
      return createObjectProperty(`default`, fn);
    };
    if (!hasTemplateSlots) {
      slotsProperties.push(buildDefaultSlotProperty(void 0, children));
    } else if (implicitDefaultChildren.length && // #3766
    // with whitespace: 'preserve', whitespaces between slots will end up in
    // implicitDefaultChildren. Ignore if all implicit children are whitespaces.
    implicitDefaultChildren.some((node2) => isNonWhitespaceContent(node2))) {
      if (hasNamedDefaultSlot) {
        context.onError(
          createCompilerError(
            39,
            implicitDefaultChildren[0].loc
          )
        );
      } else {
        slotsProperties.push(
          buildDefaultSlotProperty(void 0, implicitDefaultChildren)
        );
      }
    }
  }
  const slotFlag = hasDynamicSlots ? 2 : hasForwardedSlots(node.children) ? 3 : 1;
  let slots = createObjectExpression(
    slotsProperties.concat(
      createObjectProperty(
        `_`,
        // 2 = compiled but dynamic = can skip normalization, but must run diff
        // 1 = compiled and static = can skip normalization AND diff as optimized
        createSimpleExpression(
          slotFlag + (true ? ` /* ${slotFlagsText[slotFlag]} */` : ``),
          false
        )
      )
    ),
    loc
  );
  if (dynamicSlots.length) {
    slots = createCallExpression(context.helper(CREATE_SLOTS), [
      slots,
      createArrayExpression(dynamicSlots)
    ]);
  }
  return {
    slots,
    hasDynamicSlots
  };
}
function buildDynamicSlot(name, fn, index) {
  const props = [
    createObjectProperty(`name`, name),
    createObjectProperty(`fn`, fn)
  ];
  if (index != null) {
    props.push(
      createObjectProperty(`key`, createSimpleExpression(String(index), true))
    );
  }
  return createObjectExpression(props);
}
function hasForwardedSlots(children) {
  for (let i = 0; i < children.length; i++) {
    const child = children[i];
    switch (child.type) {
      case 1:
        if (child.tagType === 2 || hasForwardedSlots(child.children)) {
          return true;
        }
        break;
      case 9:
        if (hasForwardedSlots(child.branches)) return true;
        break;
      case 10:
      case 11:
        if (hasForwardedSlots(child.children)) return true;
        break;
    }
  }
  return false;
}
function isNonWhitespaceContent(node) {
  if (node.type !== 2 && node.type !== 12)
    return true;
  return node.type === 2 ? !!node.content.trim() : isNonWhitespaceContent(node.content);
}
function resolveComponentType(node, context, ssr = false) {
  let { tag } = node;
  const isExplicitDynamic = isComponentTag(tag);
  const isProp = findProp(
    node,
    "is",
    false,
    true
    /* allow empty */
  );
  if (isProp) {
    if (isExplicitDynamic || isCompatEnabled(
      "COMPILER_IS_ON_ELEMENT",
      context
    )) {
      let exp;
      if (isProp.type === 6) {
        exp = isProp.value && createSimpleExpression(isProp.value.content, true);
      } else {
        exp = isProp.exp;
        if (!exp) {
          exp = createSimpleExpression(`is`, false, isProp.arg.loc);
        }
      }
      if (exp) {
        return createCallExpression(context.helper(RESOLVE_DYNAMIC_COMPONENT), [
          exp
        ]);
      }
    } else if (isProp.type === 6 && isProp.value.content.startsWith("vue:")) {
      tag = isProp.value.content.slice(4);
    }
  }
  const builtIn = isCoreComponent(tag) || context.isBuiltInComponent(tag);
  if (builtIn) {
    if (!ssr) context.helper(builtIn);
    return builtIn;
  }
  context.helper(RESOLVE_COMPONENT);
  context.components.add(tag);
  return toValidAssetId(tag, `component`);
}
function buildProps(node, context, props = node.props, isComponent2, isDynamicComponent, ssr = false) {
  const { tag, loc: elementLoc, children } = node;
  let properties = [];
  const mergeArgs = [];
  const runtimeDirectives = [];
  const hasChildren = children.length > 0;
  let shouldUseBlock = false;
  let patchFlag = 0;
  let hasRef = false;
  let hasClassBinding = false;
  let hasStyleBinding = false;
  let hasHydrationEventBinding = false;
  let hasDynamicKeys = false;
  let hasVnodeHook = false;
  const dynamicPropNames = [];
  const pushMergeArg = (arg) => {
    if (properties.length) {
      mergeArgs.push(
        createObjectExpression(dedupeProperties(properties), elementLoc)
      );
      properties = [];
    }
    if (arg) mergeArgs.push(arg);
  };
  const pushRefVForMarker = () => {
    if (context.scopes.vFor > 0) {
      properties.push(
        createObjectProperty(
          createSimpleExpression("ref_for", true),
          createSimpleExpression("true")
        )
      );
    }
  };
  const analyzePatchFlag = ({ key, value }) => {
    if (isStaticExp(key)) {
      const name = key.content;
      const isEventHandler = isOn(name);
      if (isEventHandler && (!isComponent2 || isDynamicComponent) && // omit the flag for click handlers because hydration gives click
      // dedicated fast path.
      name.toLowerCase() !== "onclick" && // omit v-model handlers
      name !== "onUpdate:modelValue" && // omit onVnodeXXX hooks
      !isReservedProp(name)) {
        hasHydrationEventBinding = true;
      }
      if (isEventHandler && isReservedProp(name)) {
        hasVnodeHook = true;
      }
      if (isEventHandler && value.type === 14) {
        value = value.arguments[0];
      }
      if (value.type === 20 || (value.type === 4 || value.type === 8) && getConstantType(value, context) > 0) {
        return;
      }
      if (name === "ref") {
        hasRef = true;
      } else if (name === "class") {
        hasClassBinding = true;
      } else if (name === "style") {
        hasStyleBinding = true;
      } else if (name !== "key" && !dynamicPropNames.includes(name)) {
        dynamicPropNames.push(name);
      }
      if (isComponent2 && (name === "class" || name === "style") && !dynamicPropNames.includes(name)) {
        dynamicPropNames.push(name);
      }
    } else {
      hasDynamicKeys = true;
    }
  };
  for (let i = 0; i < props.length; i++) {
    const prop = props[i];
    if (prop.type === 6) {
      const { loc, name, nameLoc, value } = prop;
      let isStatic = true;
      if (name === "ref") {
        hasRef = true;
        pushRefVForMarker();
      }
      if (name === "is" && (isComponentTag(tag) || value && value.content.startsWith("vue:") || isCompatEnabled(
        "COMPILER_IS_ON_ELEMENT",
        context
      ))) {
        continue;
      }
      properties.push(
        createObjectProperty(
          createSimpleExpression(name, true, nameLoc),
          createSimpleExpression(
            value ? value.content : "",
            isStatic,
            value ? value.loc : loc
          )
        )
      );
    } else {
      const { name, arg, exp, loc, modifiers } = prop;
      const isVBind = name === "bind";
      const isVOn = name === "on";
      if (name === "slot") {
        if (!isComponent2) {
          context.onError(
            createCompilerError(40, loc)
          );
        }
        continue;
      }
      if (name === "once" || name === "memo") {
        continue;
      }
      if (name === "is" || isVBind && isStaticArgOf(arg, "is") && (isComponentTag(tag) || isCompatEnabled(
        "COMPILER_IS_ON_ELEMENT",
        context
      ))) {
        continue;
      }
      if (isVOn && ssr) {
        continue;
      }
      if (
        // #938: elements with dynamic keys should be forced into blocks
        isVBind && isStaticArgOf(arg, "key") || // inline before-update hooks need to force block so that it is invoked
        // before children
        isVOn && hasChildren && isStaticArgOf(arg, "vue:before-update")
      ) {
        shouldUseBlock = true;
      }
      if (isVBind && isStaticArgOf(arg, "ref")) {
        pushRefVForMarker();
      }
      if (!arg && (isVBind || isVOn)) {
        hasDynamicKeys = true;
        if (exp) {
          if (isVBind) {
            pushRefVForMarker();
            pushMergeArg();
            {
              if (true) {
                const hasOverridableKeys = mergeArgs.some((arg2) => {
                  if (arg2.type === 15) {
                    return arg2.properties.some(({ key }) => {
                      if (key.type !== 4 || !key.isStatic) {
                        return true;
                      }
                      return key.content !== "class" && key.content !== "style" && !isOn(key.content);
                    });
                  } else {
                    return true;
                  }
                });
                if (hasOverridableKeys) {
                  checkCompatEnabled(
                    "COMPILER_V_BIND_OBJECT_ORDER",
                    context,
                    loc
                  );
                }
              }
              if (isCompatEnabled(
                "COMPILER_V_BIND_OBJECT_ORDER",
                context
              )) {
                mergeArgs.unshift(exp);
                continue;
              }
            }
            mergeArgs.push(exp);
          } else {
            pushMergeArg({
              type: 14,
              loc,
              callee: context.helper(TO_HANDLERS),
              arguments: isComponent2 ? [exp] : [exp, `true`]
            });
          }
        } else {
          context.onError(
            createCompilerError(
              isVBind ? 34 : 35,
              loc
            )
          );
        }
        continue;
      }
      if (isVBind && modifiers.some((mod) => mod.content === "prop")) {
        patchFlag |= 32;
      }
      const directiveTransform = context.directiveTransforms[name];
      if (directiveTransform) {
        const { props: props2, needRuntime } = directiveTransform(prop, node, context);
        !ssr && props2.forEach(analyzePatchFlag);
        if (isVOn && arg && !isStaticExp(arg)) {
          pushMergeArg(createObjectExpression(props2, elementLoc));
        } else {
          properties.push(...props2);
        }
        if (needRuntime) {
          runtimeDirectives.push(prop);
          if (isSymbol2(needRuntime)) {
            directiveImportMap.set(prop, needRuntime);
          }
        }
      } else if (!isBuiltInDirective(name)) {
        runtimeDirectives.push(prop);
        if (hasChildren) {
          shouldUseBlock = true;
        }
      }
    }
  }
  let propsExpression = void 0;
  if (mergeArgs.length) {
    pushMergeArg();
    if (mergeArgs.length > 1) {
      propsExpression = createCallExpression(
        context.helper(MERGE_PROPS),
        mergeArgs,
        elementLoc
      );
    } else {
      propsExpression = mergeArgs[0];
    }
  } else if (properties.length) {
    propsExpression = createObjectExpression(
      dedupeProperties(properties),
      elementLoc
    );
  }
  if (hasDynamicKeys) {
    patchFlag |= 16;
  } else {
    if (hasClassBinding && !isComponent2) {
      patchFlag |= 2;
    }
    if (hasStyleBinding && !isComponent2) {
      patchFlag |= 4;
    }
    if (dynamicPropNames.length) {
      patchFlag |= 8;
    }
    if (hasHydrationEventBinding) {
      patchFlag |= 32;
    }
  }
  if (!shouldUseBlock && (patchFlag === 0 || patchFlag === 32) && (hasRef || hasVnodeHook || runtimeDirectives.length > 0)) {
    patchFlag |= 512;
  }
  if (!context.inSSR && propsExpression) {
    switch (propsExpression.type) {
      case 15:
        let classKeyIndex = -1;
        let styleKeyIndex = -1;
        let hasDynamicKey = false;
        for (let i = 0; i < propsExpression.properties.length; i++) {
          const key = propsExpression.properties[i].key;
          if (isStaticExp(key)) {
            if (key.content === "class") {
              classKeyIndex = i;
            } else if (key.content === "style") {
              styleKeyIndex = i;
            }
          } else if (!key.isHandlerKey) {
            hasDynamicKey = true;
          }
        }
        const classProp = propsExpression.properties[classKeyIndex];
        const styleProp = propsExpression.properties[styleKeyIndex];
        if (!hasDynamicKey) {
          if (classProp && !isStaticExp(classProp.value)) {
            classProp.value = createCallExpression(
              context.helper(NORMALIZE_CLASS),
              [classProp.value]
            );
          }
          if (styleProp && // the static style is compiled into an object,
          // so use `hasStyleBinding` to ensure that it is a dynamic style binding
          (hasStyleBinding || styleProp.value.type === 4 && styleProp.value.content.trim()[0] === `[` || // v-bind:style and style both exist,
          // v-bind:style with static literal object
          styleProp.value.type === 17)) {
            styleProp.value = createCallExpression(
              context.helper(NORMALIZE_STYLE),
              [styleProp.value]
            );
          }
        } else {
          propsExpression = createCallExpression(
            context.helper(NORMALIZE_PROPS),
            [propsExpression]
          );
        }
        break;
      case 14:
        break;
      default:
        propsExpression = createCallExpression(
          context.helper(NORMALIZE_PROPS),
          [
            createCallExpression(context.helper(GUARD_REACTIVE_PROPS), [
              propsExpression
            ])
          ]
        );
        break;
    }
  }
  return {
    props: propsExpression,
    directives: runtimeDirectives,
    patchFlag,
    dynamicPropNames,
    shouldUseBlock
  };
}
function dedupeProperties(properties) {
  const knownProps = /* @__PURE__ */ new Map();
  const deduped = [];
  for (let i = 0; i < properties.length; i++) {
    const prop = properties[i];
    if (prop.key.type === 8 || !prop.key.isStatic) {
      deduped.push(prop);
      continue;
    }
    const name = prop.key.content;
    const existing = knownProps.get(name);
    if (existing) {
      if (name === "style" || name === "class" || isOn(name)) {
        mergeAsArray(existing, prop);
      }
    } else {
      knownProps.set(name, prop);
      deduped.push(prop);
    }
  }
  return deduped;
}
function mergeAsArray(existing, incoming) {
  if (existing.value.type === 17) {
    existing.value.elements.push(incoming.value);
  } else {
    existing.value = createArrayExpression(
      [existing.value, incoming.value],
      existing.loc
    );
  }
}
function buildDirectiveArgs(dir, context) {
  const dirArgs = [];
  const runtime = directiveImportMap.get(dir);
  if (runtime) {
    dirArgs.push(context.helperString(runtime));
  } else {
    {
      context.helper(RESOLVE_DIRECTIVE);
      context.directives.add(dir.name);
      dirArgs.push(toValidAssetId(dir.name, `directive`));
    }
  }
  const { loc } = dir;
  if (dir.exp) dirArgs.push(dir.exp);
  if (dir.arg) {
    if (!dir.exp) {
      dirArgs.push(`void 0`);
    }
    dirArgs.push(dir.arg);
  }
  if (Object.keys(dir.modifiers).length) {
    if (!dir.arg) {
      if (!dir.exp) {
        dirArgs.push(`void 0`);
      }
      dirArgs.push(`void 0`);
    }
    const trueExpression = createSimpleExpression(`true`, false, loc);
    dirArgs.push(
      createObjectExpression(
        dir.modifiers.map(
          (modifier) => createObjectProperty(modifier, trueExpression)
        ),
        loc
      )
    );
  }
  return createArrayExpression(dirArgs, dir.loc);
}
function stringifyDynamicPropNames(props) {
  let propsNamesString = `[`;
  for (let i = 0, l = props.length; i < l; i++) {
    propsNamesString += JSON.stringify(props[i]);
    if (i < l - 1) propsNamesString += ", ";
  }
  return propsNamesString + `]`;
}
function isComponentTag(tag) {
  return tag === "component" || tag === "Component";
}
function processSlotOutlet(node, context) {
  let slotName = `"default"`;
  let slotProps = void 0;
  const nonNameProps = [];
  for (let i = 0; i < node.props.length; i++) {
    const p = node.props[i];
    if (p.type === 6) {
      if (p.value) {
        if (p.name === "name") {
          slotName = JSON.stringify(p.value.content);
        } else {
          p.name = camelize(p.name);
          nonNameProps.push(p);
        }
      }
    } else {
      if (p.name === "bind" && isStaticArgOf(p.arg, "name")) {
        if (p.exp) {
          slotName = p.exp;
        } else if (p.arg && p.arg.type === 4) {
          const name = camelize(p.arg.content);
          slotName = p.exp = createSimpleExpression(name, false, p.arg.loc);
        }
      } else {
        if (p.name === "bind" && p.arg && isStaticExp(p.arg)) {
          p.arg.content = camelize(p.arg.content);
        }
        nonNameProps.push(p);
      }
    }
  }
  if (nonNameProps.length > 0) {
    const { props, directives } = buildProps(
      node,
      context,
      nonNameProps,
      false,
      false
    );
    slotProps = props;
    if (directives.length) {
      context.onError(
        createCompilerError(
          36,
          directives[0].loc
        )
      );
    }
  }
  return {
    slotName,
    slotProps
  };
}
function createTransformProps(props = []) {
  return { props };
}
function rewriteFilter(node, context) {
  if (node.type === 4) {
    parseFilter(node, context);
  } else {
    for (let i = 0; i < node.children.length; i++) {
      const child = node.children[i];
      if (typeof child !== "object") continue;
      if (child.type === 4) {
        parseFilter(child, context);
      } else if (child.type === 8) {
        rewriteFilter(node, context);
      } else if (child.type === 5) {
        rewriteFilter(child.content, context);
      }
    }
  }
}
function parseFilter(node, context) {
  const exp = node.content;
  let inSingle = false;
  let inDouble = false;
  let inTemplateString = false;
  let inRegex = false;
  let curly = 0;
  let square = 0;
  let paren = 0;
  let lastFilterIndex = 0;
  let c, prev, i, expression, filters = [];
  for (i = 0; i < exp.length; i++) {
    prev = c;
    c = exp.charCodeAt(i);
    if (inSingle) {
      if (c === 39 && prev !== 92) inSingle = false;
    } else if (inDouble) {
      if (c === 34 && prev !== 92) inDouble = false;
    } else if (inTemplateString) {
      if (c === 96 && prev !== 92) inTemplateString = false;
    } else if (inRegex) {
      if (c === 47 && prev !== 92) inRegex = false;
    } else if (c === 124 && // pipe
    exp.charCodeAt(i + 1) !== 124 && exp.charCodeAt(i - 1) !== 124 && !curly && !square && !paren) {
      if (expression === void 0) {
        lastFilterIndex = i + 1;
        expression = exp.slice(0, i).trim();
      } else {
        pushFilter();
      }
    } else {
      switch (c) {
        case 34:
          inDouble = true;
          break;
        case 39:
          inSingle = true;
          break;
        case 96:
          inTemplateString = true;
          break;
        case 40:
          paren++;
          break;
        case 41:
          paren--;
          break;
        case 91:
          square++;
          break;
        case 93:
          square--;
          break;
        case 123:
          curly++;
          break;
        case 125:
          curly--;
          break;
      }
      if (c === 47) {
        let j = i - 1;
        let p;
        for (; j >= 0; j--) {
          p = exp.charAt(j);
          if (p !== " ") break;
        }
        if (!p || !validDivisionCharRE.test(p)) {
          inRegex = true;
        }
      }
    }
  }
  if (expression === void 0) {
    expression = exp.slice(0, i).trim();
  } else if (lastFilterIndex !== 0) {
    pushFilter();
  }
  function pushFilter() {
    filters.push(exp.slice(lastFilterIndex, i).trim());
    lastFilterIndex = i + 1;
  }
  if (filters.length) {
    warnDeprecation(
      "COMPILER_FILTERS",
      context,
      node.loc
    );
    for (i = 0; i < filters.length; i++) {
      expression = wrapFilter(expression, filters[i], context);
    }
    node.content = expression;
    node.ast = void 0;
  }
}
function wrapFilter(exp, filter, context) {
  context.helper(RESOLVE_FILTER);
  const i = filter.indexOf("(");
  if (i < 0) {
    context.filters.add(filter);
    return `${toValidAssetId(filter, "filter")}(${exp})`;
  } else {
    const name = filter.slice(0, i);
    const args = filter.slice(i + 1);
    context.filters.add(name);
    return `${toValidAssetId(name, "filter")}(${exp}${args !== ")" ? "," + args : args}`;
  }
}
function getBaseTransformPreset(prefixIdentifiers) {
  return [
    [
      transformOnce,
      transformIf,
      transformMemo,
      transformFor,
      ...[transformFilter],
      ...true ? [transformExpression] : [],
      transformSlotOutlet,
      transformElement,
      trackSlotScopes,
      transformText
    ],
    {
      on: transformOn,
      bind: transformBind,
      model: transformModel
    }
  ];
}
function baseCompile2(source, options = {}) {
  const onError = options.onError || defaultOnError2;
  const isModuleMode = options.mode === "module";
  {
    if (options.prefixIdentifiers === true) {
      onError(createCompilerError(47));
    } else if (isModuleMode) {
      onError(createCompilerError(48));
    }
  }
  const prefixIdentifiers = false;
  if (options.cacheHandlers) {
    onError(createCompilerError(49));
  }
  if (options.scopeId && !isModuleMode) {
    onError(createCompilerError(50));
  }
  const resolvedOptions = extend({}, options, {
    prefixIdentifiers
  });
  const ast = isString3(source) ? baseParse(source, resolvedOptions) : source;
  const [nodeTransforms, directiveTransforms] = getBaseTransformPreset();
  transform2(
    ast,
    extend({}, resolvedOptions, {
      nodeTransforms: [
        ...nodeTransforms,
        ...options.nodeTransforms || []
        // user transforms
      ],
      directiveTransforms: extend(
        {},
        directiveTransforms,
        options.directiveTransforms || {}
        // user transforms
      )
    })
  );
  return generate2(ast, resolvedOptions);
}
var FRAGMENT, TELEPORT, SUSPENSE, KEEP_ALIVE, BASE_TRANSITION, OPEN_BLOCK, CREATE_BLOCK, CREATE_ELEMENT_BLOCK, CREATE_VNODE, CREATE_ELEMENT_VNODE, CREATE_COMMENT, CREATE_TEXT, CREATE_STATIC, RESOLVE_COMPONENT, RESOLVE_DYNAMIC_COMPONENT, RESOLVE_DIRECTIVE, RESOLVE_FILTER, WITH_DIRECTIVES, RENDER_LIST, RENDER_SLOT, CREATE_SLOTS, TO_DISPLAY_STRING, MERGE_PROPS, NORMALIZE_CLASS, NORMALIZE_STYLE, NORMALIZE_PROPS, GUARD_REACTIVE_PROPS, TO_HANDLERS, CAMELIZE, CAPITALIZE, TO_HANDLER_KEY, SET_BLOCK_TRACKING, PUSH_SCOPE_ID, POP_SCOPE_ID, WITH_CTX, UNREF, IS_REF, WITH_MEMO, IS_MEMO_SAME, helperNameMap, Namespaces, NodeTypes, ElementTypes, ConstantTypes, locStub, defaultDelimitersOpen, defaultDelimitersClose, Sequences, Tokenizer, CompilerDeprecationTypes, deprecationData, ErrorCodes, errorMessages2, isFunctionType, isStaticProperty, isStaticPropertyKey, TS_NODE_TYPES, isStaticExp, nonIdentifierRE, isSimpleIdentifier, validFirstIdentCharRE, validIdentCharRE, whitespaceRE, getExpSource, isMemberExpressionBrowser, isMemberExpressionNode, isMemberExpression, fnExpRE, isFnExpressionBrowser, isFnExpressionNode, isFnExpression, propsHelperSet, forAliasRE, defaultParserOptions, currentOptions, currentRoot, currentInput, currentOpenTag, currentProp, currentAttrValue, currentAttrStartIndex, currentAttrEndIndex, inPre, inVPre, currentVPreBoundary, stack, tokenizer, forIteratorRE, stripParensRE, specialTemplateDir, windowsNewlineRE, allowHoistedHelperSet, PURE_ANNOTATION, aliasHelper, prohibitedKeywordRE, stripStringRE, transformExpression, transformIf, transformBind, transformBindShorthand, injectPrefix, transformFor, defaultFallback, trackSlotScopes, trackVForSlotScopes, buildClientSlotFn, directiveImportMap, transformElement, transformSlotOutlet, transformOn, transformText, seen$1, transformOnce, transformModel, validDivisionCharRE, transformFilter, seen, transformMemo, BindingTypes, noopDirectiveTransform;
var init_compiler_core_esm_bundler = __esm({
  "node_modules/vue/node_modules/@vue/compiler-core/dist/compiler-core.esm-bundler.js"() {
    init_shared_esm_bundler();
    init_shared_esm_bundler();
    FRAGMENT = Symbol(true ? `Fragment` : ``);
    TELEPORT = Symbol(true ? `Teleport` : ``);
    SUSPENSE = Symbol(true ? `Suspense` : ``);
    KEEP_ALIVE = Symbol(true ? `KeepAlive` : ``);
    BASE_TRANSITION = Symbol(
      true ? `BaseTransition` : ``
    );
    OPEN_BLOCK = Symbol(true ? `openBlock` : ``);
    CREATE_BLOCK = Symbol(true ? `createBlock` : ``);
    CREATE_ELEMENT_BLOCK = Symbol(
      true ? `createElementBlock` : ``
    );
    CREATE_VNODE = Symbol(true ? `createVNode` : ``);
    CREATE_ELEMENT_VNODE = Symbol(
      true ? `createElementVNode` : ``
    );
    CREATE_COMMENT = Symbol(
      true ? `createCommentVNode` : ``
    );
    CREATE_TEXT = Symbol(
      true ? `createTextVNode` : ``
    );
    CREATE_STATIC = Symbol(
      true ? `createStaticVNode` : ``
    );
    RESOLVE_COMPONENT = Symbol(
      true ? `resolveComponent` : ``
    );
    RESOLVE_DYNAMIC_COMPONENT = Symbol(
      true ? `resolveDynamicComponent` : ``
    );
    RESOLVE_DIRECTIVE = Symbol(
      true ? `resolveDirective` : ``
    );
    RESOLVE_FILTER = Symbol(
      true ? `resolveFilter` : ``
    );
    WITH_DIRECTIVES = Symbol(
      true ? `withDirectives` : ``
    );
    RENDER_LIST = Symbol(true ? `renderList` : ``);
    RENDER_SLOT = Symbol(true ? `renderSlot` : ``);
    CREATE_SLOTS = Symbol(true ? `createSlots` : ``);
    TO_DISPLAY_STRING = Symbol(
      true ? `toDisplayString` : ``
    );
    MERGE_PROPS = Symbol(true ? `mergeProps` : ``);
    NORMALIZE_CLASS = Symbol(
      true ? `normalizeClass` : ``
    );
    NORMALIZE_STYLE = Symbol(
      true ? `normalizeStyle` : ``
    );
    NORMALIZE_PROPS = Symbol(
      true ? `normalizeProps` : ``
    );
    GUARD_REACTIVE_PROPS = Symbol(
      true ? `guardReactiveProps` : ``
    );
    TO_HANDLERS = Symbol(true ? `toHandlers` : ``);
    CAMELIZE = Symbol(true ? `camelize` : ``);
    CAPITALIZE = Symbol(true ? `capitalize` : ``);
    TO_HANDLER_KEY = Symbol(
      true ? `toHandlerKey` : ``
    );
    SET_BLOCK_TRACKING = Symbol(
      true ? `setBlockTracking` : ``
    );
    PUSH_SCOPE_ID = Symbol(true ? `pushScopeId` : ``);
    POP_SCOPE_ID = Symbol(true ? `popScopeId` : ``);
    WITH_CTX = Symbol(true ? `withCtx` : ``);
    UNREF = Symbol(true ? `unref` : ``);
    IS_REF = Symbol(true ? `isRef` : ``);
    WITH_MEMO = Symbol(true ? `withMemo` : ``);
    IS_MEMO_SAME = Symbol(true ? `isMemoSame` : ``);
    helperNameMap = {
      [FRAGMENT]: `Fragment`,
      [TELEPORT]: `Teleport`,
      [SUSPENSE]: `Suspense`,
      [KEEP_ALIVE]: `KeepAlive`,
      [BASE_TRANSITION]: `BaseTransition`,
      [OPEN_BLOCK]: `openBlock`,
      [CREATE_BLOCK]: `createBlock`,
      [CREATE_ELEMENT_BLOCK]: `createElementBlock`,
      [CREATE_VNODE]: `createVNode`,
      [CREATE_ELEMENT_VNODE]: `createElementVNode`,
      [CREATE_COMMENT]: `createCommentVNode`,
      [CREATE_TEXT]: `createTextVNode`,
      [CREATE_STATIC]: `createStaticVNode`,
      [RESOLVE_COMPONENT]: `resolveComponent`,
      [RESOLVE_DYNAMIC_COMPONENT]: `resolveDynamicComponent`,
      [RESOLVE_DIRECTIVE]: `resolveDirective`,
      [RESOLVE_FILTER]: `resolveFilter`,
      [WITH_DIRECTIVES]: `withDirectives`,
      [RENDER_LIST]: `renderList`,
      [RENDER_SLOT]: `renderSlot`,
      [CREATE_SLOTS]: `createSlots`,
      [TO_DISPLAY_STRING]: `toDisplayString`,
      [MERGE_PROPS]: `mergeProps`,
      [NORMALIZE_CLASS]: `normalizeClass`,
      [NORMALIZE_STYLE]: `normalizeStyle`,
      [NORMALIZE_PROPS]: `normalizeProps`,
      [GUARD_REACTIVE_PROPS]: `guardReactiveProps`,
      [TO_HANDLERS]: `toHandlers`,
      [CAMELIZE]: `camelize`,
      [CAPITALIZE]: `capitalize`,
      [TO_HANDLER_KEY]: `toHandlerKey`,
      [SET_BLOCK_TRACKING]: `setBlockTracking`,
      [PUSH_SCOPE_ID]: `pushScopeId`,
      [POP_SCOPE_ID]: `popScopeId`,
      [WITH_CTX]: `withCtx`,
      [UNREF]: `unref`,
      [IS_REF]: `isRef`,
      [WITH_MEMO]: `withMemo`,
      [IS_MEMO_SAME]: `isMemoSame`
    };
    Namespaces = {
      "HTML": 0,
      "0": "HTML",
      "SVG": 1,
      "1": "SVG",
      "MATH_ML": 2,
      "2": "MATH_ML"
    };
    NodeTypes = {
      "ROOT": 0,
      "0": "ROOT",
      "ELEMENT": 1,
      "1": "ELEMENT",
      "TEXT": 2,
      "2": "TEXT",
      "COMMENT": 3,
      "3": "COMMENT",
      "SIMPLE_EXPRESSION": 4,
      "4": "SIMPLE_EXPRESSION",
      "INTERPOLATION": 5,
      "5": "INTERPOLATION",
      "ATTRIBUTE": 6,
      "6": "ATTRIBUTE",
      "DIRECTIVE": 7,
      "7": "DIRECTIVE",
      "COMPOUND_EXPRESSION": 8,
      "8": "COMPOUND_EXPRESSION",
      "IF": 9,
      "9": "IF",
      "IF_BRANCH": 10,
      "10": "IF_BRANCH",
      "FOR": 11,
      "11": "FOR",
      "TEXT_CALL": 12,
      "12": "TEXT_CALL",
      "VNODE_CALL": 13,
      "13": "VNODE_CALL",
      "JS_CALL_EXPRESSION": 14,
      "14": "JS_CALL_EXPRESSION",
      "JS_OBJECT_EXPRESSION": 15,
      "15": "JS_OBJECT_EXPRESSION",
      "JS_PROPERTY": 16,
      "16": "JS_PROPERTY",
      "JS_ARRAY_EXPRESSION": 17,
      "17": "JS_ARRAY_EXPRESSION",
      "JS_FUNCTION_EXPRESSION": 18,
      "18": "JS_FUNCTION_EXPRESSION",
      "JS_CONDITIONAL_EXPRESSION": 19,
      "19": "JS_CONDITIONAL_EXPRESSION",
      "JS_CACHE_EXPRESSION": 20,
      "20": "JS_CACHE_EXPRESSION",
      "JS_BLOCK_STATEMENT": 21,
      "21": "JS_BLOCK_STATEMENT",
      "JS_TEMPLATE_LITERAL": 22,
      "22": "JS_TEMPLATE_LITERAL",
      "JS_IF_STATEMENT": 23,
      "23": "JS_IF_STATEMENT",
      "JS_ASSIGNMENT_EXPRESSION": 24,
      "24": "JS_ASSIGNMENT_EXPRESSION",
      "JS_SEQUENCE_EXPRESSION": 25,
      "25": "JS_SEQUENCE_EXPRESSION",
      "JS_RETURN_STATEMENT": 26,
      "26": "JS_RETURN_STATEMENT"
    };
    ElementTypes = {
      "ELEMENT": 0,
      "0": "ELEMENT",
      "COMPONENT": 1,
      "1": "COMPONENT",
      "SLOT": 2,
      "2": "SLOT",
      "TEMPLATE": 3,
      "3": "TEMPLATE"
    };
    ConstantTypes = {
      "NOT_CONSTANT": 0,
      "0": "NOT_CONSTANT",
      "CAN_SKIP_PATCH": 1,
      "1": "CAN_SKIP_PATCH",
      "CAN_CACHE": 2,
      "2": "CAN_CACHE",
      "CAN_STRINGIFY": 3,
      "3": "CAN_STRINGIFY"
    };
    locStub = {
      start: { line: 1, column: 1, offset: 0 },
      end: { line: 1, column: 1, offset: 0 },
      source: ""
    };
    defaultDelimitersOpen = new Uint8Array([123, 123]);
    defaultDelimitersClose = new Uint8Array([125, 125]);
    Sequences = {
      Cdata: new Uint8Array([67, 68, 65, 84, 65, 91]),
      // CDATA[
      CdataEnd: new Uint8Array([93, 93, 62]),
      // ]]>
      CommentEnd: new Uint8Array([45, 45, 62]),
      // `-->`
      ScriptEnd: new Uint8Array([60, 47, 115, 99, 114, 105, 112, 116]),
      // `<\/script`
      StyleEnd: new Uint8Array([60, 47, 115, 116, 121, 108, 101]),
      // `</style`
      TitleEnd: new Uint8Array([60, 47, 116, 105, 116, 108, 101]),
      // `</title`
      TextareaEnd: new Uint8Array([
        60,
        47,
        116,
        101,
        120,
        116,
        97,
        114,
        101,
        97
      ])
      // `</textarea
    };
    Tokenizer = class {
      constructor(stack2, cbs) {
        this.stack = stack2;
        this.cbs = cbs;
        this.state = 1;
        this.buffer = "";
        this.sectionStart = 0;
        this.index = 0;
        this.entityStart = 0;
        this.baseState = 1;
        this.inRCDATA = false;
        this.inXML = false;
        this.inVPre = false;
        this.newlines = [];
        this.mode = 0;
        this.delimiterOpen = defaultDelimitersOpen;
        this.delimiterClose = defaultDelimitersClose;
        this.delimiterIndex = -1;
        this.currentSequence = void 0;
        this.sequenceIndex = 0;
      }
      get inSFCRoot() {
        return this.mode === 2 && this.stack.length === 0;
      }
      reset() {
        this.state = 1;
        this.mode = 0;
        this.buffer = "";
        this.sectionStart = 0;
        this.index = 0;
        this.baseState = 1;
        this.inRCDATA = false;
        this.currentSequence = void 0;
        this.newlines.length = 0;
        this.delimiterOpen = defaultDelimitersOpen;
        this.delimiterClose = defaultDelimitersClose;
      }
      /**
       * Generate Position object with line / column information using recorded
       * newline positions. We know the index is always going to be an already
       * processed index, so all the newlines up to this index should have been
       * recorded.
       */
      getPos(index) {
        let line = 1;
        let column = index + 1;
        for (let i = this.newlines.length - 1; i >= 0; i--) {
          const newlineIndex = this.newlines[i];
          if (index > newlineIndex) {
            line = i + 2;
            column = index - newlineIndex;
            break;
          }
        }
        return {
          column,
          line,
          offset: index
        };
      }
      peek() {
        return this.buffer.charCodeAt(this.index + 1);
      }
      stateText(c) {
        if (c === 60) {
          if (this.index > this.sectionStart) {
            this.cbs.ontext(this.sectionStart, this.index);
          }
          this.state = 5;
          this.sectionStart = this.index;
        } else if (!this.inVPre && c === this.delimiterOpen[0]) {
          this.state = 2;
          this.delimiterIndex = 0;
          this.stateInterpolationOpen(c);
        }
      }
      stateInterpolationOpen(c) {
        if (c === this.delimiterOpen[this.delimiterIndex]) {
          if (this.delimiterIndex === this.delimiterOpen.length - 1) {
            const start = this.index + 1 - this.delimiterOpen.length;
            if (start > this.sectionStart) {
              this.cbs.ontext(this.sectionStart, start);
            }
            this.state = 3;
            this.sectionStart = start;
          } else {
            this.delimiterIndex++;
          }
        } else if (this.inRCDATA) {
          this.state = 32;
          this.stateInRCDATA(c);
        } else {
          this.state = 1;
          this.stateText(c);
        }
      }
      stateInterpolation(c) {
        if (c === this.delimiterClose[0]) {
          this.state = 4;
          this.delimiterIndex = 0;
          this.stateInterpolationClose(c);
        }
      }
      stateInterpolationClose(c) {
        if (c === this.delimiterClose[this.delimiterIndex]) {
          if (this.delimiterIndex === this.delimiterClose.length - 1) {
            this.cbs.oninterpolation(this.sectionStart, this.index + 1);
            if (this.inRCDATA) {
              this.state = 32;
            } else {
              this.state = 1;
            }
            this.sectionStart = this.index + 1;
          } else {
            this.delimiterIndex++;
          }
        } else {
          this.state = 3;
          this.stateInterpolation(c);
        }
      }
      stateSpecialStartSequence(c) {
        const isEnd = this.sequenceIndex === this.currentSequence.length;
        const isMatch = isEnd ? (
          // If we are at the end of the sequence, make sure the tag name has ended
          isEndOfTagSection(c)
        ) : (
          // Otherwise, do a case-insensitive comparison
          (c | 32) === this.currentSequence[this.sequenceIndex]
        );
        if (!isMatch) {
          this.inRCDATA = false;
        } else if (!isEnd) {
          this.sequenceIndex++;
          return;
        }
        this.sequenceIndex = 0;
        this.state = 6;
        this.stateInTagName(c);
      }
      /** Look for an end tag. For <title> and <textarea>, also decode entities. */
      stateInRCDATA(c) {
        if (this.sequenceIndex === this.currentSequence.length) {
          if (c === 62 || isWhitespace(c)) {
            const endOfText = this.index - this.currentSequence.length;
            if (this.sectionStart < endOfText) {
              const actualIndex = this.index;
              this.index = endOfText;
              this.cbs.ontext(this.sectionStart, endOfText);
              this.index = actualIndex;
            }
            this.sectionStart = endOfText + 2;
            this.stateInClosingTagName(c);
            this.inRCDATA = false;
            return;
          }
          this.sequenceIndex = 0;
        }
        if ((c | 32) === this.currentSequence[this.sequenceIndex]) {
          this.sequenceIndex += 1;
        } else if (this.sequenceIndex === 0) {
          if (this.currentSequence === Sequences.TitleEnd || this.currentSequence === Sequences.TextareaEnd && !this.inSFCRoot) {
            if (c === this.delimiterOpen[0]) {
              this.state = 2;
              this.delimiterIndex = 0;
              this.stateInterpolationOpen(c);
            }
          } else if (this.fastForwardTo(60)) {
            this.sequenceIndex = 1;
          }
        } else {
          this.sequenceIndex = Number(c === 60);
        }
      }
      stateCDATASequence(c) {
        if (c === Sequences.Cdata[this.sequenceIndex]) {
          if (++this.sequenceIndex === Sequences.Cdata.length) {
            this.state = 28;
            this.currentSequence = Sequences.CdataEnd;
            this.sequenceIndex = 0;
            this.sectionStart = this.index + 1;
          }
        } else {
          this.sequenceIndex = 0;
          this.state = 23;
          this.stateInDeclaration(c);
        }
      }
      /**
       * When we wait for one specific character, we can speed things up
       * by skipping through the buffer until we find it.
       *
       * @returns Whether the character was found.
       */
      fastForwardTo(c) {
        while (++this.index < this.buffer.length) {
          const cc = this.buffer.charCodeAt(this.index);
          if (cc === 10) {
            this.newlines.push(this.index);
          }
          if (cc === c) {
            return true;
          }
        }
        this.index = this.buffer.length - 1;
        return false;
      }
      /**
       * Comments and CDATA end with `-->` and `]]>`.
       *
       * Their common qualities are:
       * - Their end sequences have a distinct character they start with.
       * - That character is then repeated, so we have to check multiple repeats.
       * - All characters but the start character of the sequence can be skipped.
       */
      stateInCommentLike(c) {
        if (c === this.currentSequence[this.sequenceIndex]) {
          if (++this.sequenceIndex === this.currentSequence.length) {
            if (this.currentSequence === Sequences.CdataEnd) {
              this.cbs.oncdata(this.sectionStart, this.index - 2);
            } else {
              this.cbs.oncomment(this.sectionStart, this.index - 2);
            }
            this.sequenceIndex = 0;
            this.sectionStart = this.index + 1;
            this.state = 1;
          }
        } else if (this.sequenceIndex === 0) {
          if (this.fastForwardTo(this.currentSequence[0])) {
            this.sequenceIndex = 1;
          }
        } else if (c !== this.currentSequence[this.sequenceIndex - 1]) {
          this.sequenceIndex = 0;
        }
      }
      startSpecial(sequence, offset) {
        this.enterRCDATA(sequence, offset);
        this.state = 31;
      }
      enterRCDATA(sequence, offset) {
        this.inRCDATA = true;
        this.currentSequence = sequence;
        this.sequenceIndex = offset;
      }
      stateBeforeTagName(c) {
        if (c === 33) {
          this.state = 22;
          this.sectionStart = this.index + 1;
        } else if (c === 63) {
          this.state = 24;
          this.sectionStart = this.index + 1;
        } else if (isTagStartChar(c)) {
          this.sectionStart = this.index;
          if (this.mode === 0) {
            this.state = 6;
          } else if (this.inSFCRoot) {
            this.state = 34;
          } else if (!this.inXML) {
            if (c === 116) {
              this.state = 30;
            } else {
              this.state = c === 115 ? 29 : 6;
            }
          } else {
            this.state = 6;
          }
        } else if (c === 47) {
          this.state = 8;
        } else {
          this.state = 1;
          this.stateText(c);
        }
      }
      stateInTagName(c) {
        if (isEndOfTagSection(c)) {
          this.handleTagName(c);
        }
      }
      stateInSFCRootTagName(c) {
        if (isEndOfTagSection(c)) {
          const tag = this.buffer.slice(this.sectionStart, this.index);
          if (tag !== "template") {
            this.enterRCDATA(toCharCodes(`</` + tag), 0);
          }
          this.handleTagName(c);
        }
      }
      handleTagName(c) {
        this.cbs.onopentagname(this.sectionStart, this.index);
        this.sectionStart = -1;
        this.state = 11;
        this.stateBeforeAttrName(c);
      }
      stateBeforeClosingTagName(c) {
        if (isWhitespace(c)) ;
        else if (c === 62) {
          if (true) {
            this.cbs.onerr(14, this.index);
          }
          this.state = 1;
          this.sectionStart = this.index + 1;
        } else {
          this.state = isTagStartChar(c) ? 9 : 27;
          this.sectionStart = this.index;
        }
      }
      stateInClosingTagName(c) {
        if (c === 62 || isWhitespace(c)) {
          this.cbs.onclosetag(this.sectionStart, this.index);
          this.sectionStart = -1;
          this.state = 10;
          this.stateAfterClosingTagName(c);
        }
      }
      stateAfterClosingTagName(c) {
        if (c === 62) {
          this.state = 1;
          this.sectionStart = this.index + 1;
        }
      }
      stateBeforeAttrName(c) {
        if (c === 62) {
          this.cbs.onopentagend(this.index);
          if (this.inRCDATA) {
            this.state = 32;
          } else {
            this.state = 1;
          }
          this.sectionStart = this.index + 1;
        } else if (c === 47) {
          this.state = 7;
          if (this.peek() !== 62) {
            this.cbs.onerr(22, this.index);
          }
        } else if (c === 60 && this.peek() === 47) {
          this.cbs.onopentagend(this.index);
          this.state = 5;
          this.sectionStart = this.index;
        } else if (!isWhitespace(c)) {
          if (c === 61) {
            this.cbs.onerr(
              19,
              this.index
            );
          }
          this.handleAttrStart(c);
        }
      }
      handleAttrStart(c) {
        if (c === 118 && this.peek() === 45) {
          this.state = 13;
          this.sectionStart = this.index;
        } else if (c === 46 || c === 58 || c === 64 || c === 35) {
          this.cbs.ondirname(this.index, this.index + 1);
          this.state = 14;
          this.sectionStart = this.index + 1;
        } else {
          this.state = 12;
          this.sectionStart = this.index;
        }
      }
      stateInSelfClosingTag(c) {
        if (c === 62) {
          this.cbs.onselfclosingtag(this.index);
          this.state = 1;
          this.sectionStart = this.index + 1;
          this.inRCDATA = false;
        } else if (!isWhitespace(c)) {
          this.state = 11;
          this.stateBeforeAttrName(c);
        }
      }
      stateInAttrName(c) {
        if (c === 61 || isEndOfTagSection(c)) {
          this.cbs.onattribname(this.sectionStart, this.index);
          this.handleAttrNameEnd(c);
        } else if (c === 34 || c === 39 || c === 60) {
          this.cbs.onerr(
            17,
            this.index
          );
        }
      }
      stateInDirName(c) {
        if (c === 61 || isEndOfTagSection(c)) {
          this.cbs.ondirname(this.sectionStart, this.index);
          this.handleAttrNameEnd(c);
        } else if (c === 58) {
          this.cbs.ondirname(this.sectionStart, this.index);
          this.state = 14;
          this.sectionStart = this.index + 1;
        } else if (c === 46) {
          this.cbs.ondirname(this.sectionStart, this.index);
          this.state = 16;
          this.sectionStart = this.index + 1;
        }
      }
      stateInDirArg(c) {
        if (c === 61 || isEndOfTagSection(c)) {
          this.cbs.ondirarg(this.sectionStart, this.index);
          this.handleAttrNameEnd(c);
        } else if (c === 91) {
          this.state = 15;
        } else if (c === 46) {
          this.cbs.ondirarg(this.sectionStart, this.index);
          this.state = 16;
          this.sectionStart = this.index + 1;
        }
      }
      stateInDynamicDirArg(c) {
        if (c === 93) {
          this.state = 14;
        } else if (c === 61 || isEndOfTagSection(c)) {
          this.cbs.ondirarg(this.sectionStart, this.index + 1);
          this.handleAttrNameEnd(c);
          if (true) {
            this.cbs.onerr(
              27,
              this.index
            );
          }
        }
      }
      stateInDirModifier(c) {
        if (c === 61 || isEndOfTagSection(c)) {
          this.cbs.ondirmodifier(this.sectionStart, this.index);
          this.handleAttrNameEnd(c);
        } else if (c === 46) {
          this.cbs.ondirmodifier(this.sectionStart, this.index);
          this.sectionStart = this.index + 1;
        }
      }
      handleAttrNameEnd(c) {
        this.sectionStart = this.index;
        this.state = 17;
        this.cbs.onattribnameend(this.index);
        this.stateAfterAttrName(c);
      }
      stateAfterAttrName(c) {
        if (c === 61) {
          this.state = 18;
        } else if (c === 47 || c === 62) {
          this.cbs.onattribend(0, this.sectionStart);
          this.sectionStart = -1;
          this.state = 11;
          this.stateBeforeAttrName(c);
        } else if (!isWhitespace(c)) {
          this.cbs.onattribend(0, this.sectionStart);
          this.handleAttrStart(c);
        }
      }
      stateBeforeAttrValue(c) {
        if (c === 34) {
          this.state = 19;
          this.sectionStart = this.index + 1;
        } else if (c === 39) {
          this.state = 20;
          this.sectionStart = this.index + 1;
        } else if (!isWhitespace(c)) {
          this.sectionStart = this.index;
          this.state = 21;
          this.stateInAttrValueNoQuotes(c);
        }
      }
      handleInAttrValue(c, quote) {
        if (c === quote || this.fastForwardTo(quote)) {
          this.cbs.onattribdata(this.sectionStart, this.index);
          this.sectionStart = -1;
          this.cbs.onattribend(
            quote === 34 ? 3 : 2,
            this.index + 1
          );
          this.state = 11;
        }
      }
      stateInAttrValueDoubleQuotes(c) {
        this.handleInAttrValue(c, 34);
      }
      stateInAttrValueSingleQuotes(c) {
        this.handleInAttrValue(c, 39);
      }
      stateInAttrValueNoQuotes(c) {
        if (isWhitespace(c) || c === 62) {
          this.cbs.onattribdata(this.sectionStart, this.index);
          this.sectionStart = -1;
          this.cbs.onattribend(1, this.index);
          this.state = 11;
          this.stateBeforeAttrName(c);
        } else if (c === 34 || c === 39 || c === 60 || c === 61 || c === 96) {
          this.cbs.onerr(
            18,
            this.index
          );
        } else ;
      }
      stateBeforeDeclaration(c) {
        if (c === 91) {
          this.state = 26;
          this.sequenceIndex = 0;
        } else {
          this.state = c === 45 ? 25 : 23;
        }
      }
      stateInDeclaration(c) {
        if (c === 62 || this.fastForwardTo(62)) {
          this.state = 1;
          this.sectionStart = this.index + 1;
        }
      }
      stateInProcessingInstruction(c) {
        if (c === 62 || this.fastForwardTo(62)) {
          this.cbs.onprocessinginstruction(this.sectionStart, this.index);
          this.state = 1;
          this.sectionStart = this.index + 1;
        }
      }
      stateBeforeComment(c) {
        if (c === 45) {
          this.state = 28;
          this.currentSequence = Sequences.CommentEnd;
          this.sequenceIndex = 2;
          this.sectionStart = this.index + 1;
        } else {
          this.state = 23;
        }
      }
      stateInSpecialComment(c) {
        if (c === 62 || this.fastForwardTo(62)) {
          this.cbs.oncomment(this.sectionStart, this.index);
          this.state = 1;
          this.sectionStart = this.index + 1;
        }
      }
      stateBeforeSpecialS(c) {
        if (c === Sequences.ScriptEnd[3]) {
          this.startSpecial(Sequences.ScriptEnd, 4);
        } else if (c === Sequences.StyleEnd[3]) {
          this.startSpecial(Sequences.StyleEnd, 4);
        } else {
          this.state = 6;
          this.stateInTagName(c);
        }
      }
      stateBeforeSpecialT(c) {
        if (c === Sequences.TitleEnd[3]) {
          this.startSpecial(Sequences.TitleEnd, 4);
        } else if (c === Sequences.TextareaEnd[3]) {
          this.startSpecial(Sequences.TextareaEnd, 4);
        } else {
          this.state = 6;
          this.stateInTagName(c);
        }
      }
      startEntity() {
      }
      stateInEntity() {
      }
      /**
       * Iterates through the buffer, calling the function corresponding to the current state.
       *
       * States that are more likely to be hit are higher up, as a performance improvement.
       */
      parse(input) {
        this.buffer = input;
        while (this.index < this.buffer.length) {
          const c = this.buffer.charCodeAt(this.index);
          if (c === 10) {
            this.newlines.push(this.index);
          }
          switch (this.state) {
            case 1: {
              this.stateText(c);
              break;
            }
            case 2: {
              this.stateInterpolationOpen(c);
              break;
            }
            case 3: {
              this.stateInterpolation(c);
              break;
            }
            case 4: {
              this.stateInterpolationClose(c);
              break;
            }
            case 31: {
              this.stateSpecialStartSequence(c);
              break;
            }
            case 32: {
              this.stateInRCDATA(c);
              break;
            }
            case 26: {
              this.stateCDATASequence(c);
              break;
            }
            case 19: {
              this.stateInAttrValueDoubleQuotes(c);
              break;
            }
            case 12: {
              this.stateInAttrName(c);
              break;
            }
            case 13: {
              this.stateInDirName(c);
              break;
            }
            case 14: {
              this.stateInDirArg(c);
              break;
            }
            case 15: {
              this.stateInDynamicDirArg(c);
              break;
            }
            case 16: {
              this.stateInDirModifier(c);
              break;
            }
            case 28: {
              this.stateInCommentLike(c);
              break;
            }
            case 27: {
              this.stateInSpecialComment(c);
              break;
            }
            case 11: {
              this.stateBeforeAttrName(c);
              break;
            }
            case 6: {
              this.stateInTagName(c);
              break;
            }
            case 34: {
              this.stateInSFCRootTagName(c);
              break;
            }
            case 9: {
              this.stateInClosingTagName(c);
              break;
            }
            case 5: {
              this.stateBeforeTagName(c);
              break;
            }
            case 17: {
              this.stateAfterAttrName(c);
              break;
            }
            case 20: {
              this.stateInAttrValueSingleQuotes(c);
              break;
            }
            case 18: {
              this.stateBeforeAttrValue(c);
              break;
            }
            case 8: {
              this.stateBeforeClosingTagName(c);
              break;
            }
            case 10: {
              this.stateAfterClosingTagName(c);
              break;
            }
            case 29: {
              this.stateBeforeSpecialS(c);
              break;
            }
            case 30: {
              this.stateBeforeSpecialT(c);
              break;
            }
            case 21: {
              this.stateInAttrValueNoQuotes(c);
              break;
            }
            case 7: {
              this.stateInSelfClosingTag(c);
              break;
            }
            case 23: {
              this.stateInDeclaration(c);
              break;
            }
            case 22: {
              this.stateBeforeDeclaration(c);
              break;
            }
            case 25: {
              this.stateBeforeComment(c);
              break;
            }
            case 24: {
              this.stateInProcessingInstruction(c);
              break;
            }
            case 33: {
              this.stateInEntity();
              break;
            }
          }
          this.index++;
        }
        this.cleanup();
        this.finish();
      }
      /**
       * Remove data that has already been consumed from the buffer.
       */
      cleanup() {
        if (this.sectionStart !== this.index) {
          if (this.state === 1 || this.state === 32 && this.sequenceIndex === 0) {
            this.cbs.ontext(this.sectionStart, this.index);
            this.sectionStart = this.index;
          } else if (this.state === 19 || this.state === 20 || this.state === 21) {
            this.cbs.onattribdata(this.sectionStart, this.index);
            this.sectionStart = this.index;
          }
        }
      }
      finish() {
        this.handleTrailingData();
        this.cbs.onend();
      }
      /** Handle any trailing data. */
      handleTrailingData() {
        const endIndex = this.buffer.length;
        if (this.sectionStart >= endIndex) {
          return;
        }
        if (this.state === 28) {
          if (this.currentSequence === Sequences.CdataEnd) {
            this.cbs.oncdata(this.sectionStart, endIndex);
          } else {
            this.cbs.oncomment(this.sectionStart, endIndex);
          }
        } else if (this.state === 6 || this.state === 11 || this.state === 18 || this.state === 17 || this.state === 12 || this.state === 13 || this.state === 14 || this.state === 15 || this.state === 16 || this.state === 20 || this.state === 19 || this.state === 21 || this.state === 9) ;
        else {
          this.cbs.ontext(this.sectionStart, endIndex);
        }
      }
      emitCodePoint(cp, consumed) {
      }
    };
    CompilerDeprecationTypes = {
      "COMPILER_IS_ON_ELEMENT": "COMPILER_IS_ON_ELEMENT",
      "COMPILER_V_BIND_SYNC": "COMPILER_V_BIND_SYNC",
      "COMPILER_V_BIND_OBJECT_ORDER": "COMPILER_V_BIND_OBJECT_ORDER",
      "COMPILER_V_ON_NATIVE": "COMPILER_V_ON_NATIVE",
      "COMPILER_V_IF_V_FOR_PRECEDENCE": "COMPILER_V_IF_V_FOR_PRECEDENCE",
      "COMPILER_NATIVE_TEMPLATE": "COMPILER_NATIVE_TEMPLATE",
      "COMPILER_INLINE_TEMPLATE": "COMPILER_INLINE_TEMPLATE",
      "COMPILER_FILTERS": "COMPILER_FILTERS"
    };
    deprecationData = {
      ["COMPILER_IS_ON_ELEMENT"]: {
        message: `Platform-native elements with "is" prop will no longer be treated as components in Vue 3 unless the "is" value is explicitly prefixed with "vue:".`,
        link: `https://v3-migration.vuejs.org/breaking-changes/custom-elements-interop.html`
      },
      ["COMPILER_V_BIND_SYNC"]: {
        message: (key) => `.sync modifier for v-bind has been removed. Use v-model with argument instead. \`v-bind:${key}.sync\` should be changed to \`v-model:${key}\`.`,
        link: `https://v3-migration.vuejs.org/breaking-changes/v-model.html`
      },
      ["COMPILER_V_BIND_OBJECT_ORDER"]: {
        message: `v-bind="obj" usage is now order sensitive and behaves like JavaScript object spread: it will now overwrite an existing non-mergeable attribute that appears before v-bind in the case of conflict. To retain 2.x behavior, move v-bind to make it the first attribute. You can also suppress this warning if the usage is intended.`,
        link: `https://v3-migration.vuejs.org/breaking-changes/v-bind.html`
      },
      ["COMPILER_V_ON_NATIVE"]: {
        message: `.native modifier for v-on has been removed as is no longer necessary.`,
        link: `https://v3-migration.vuejs.org/breaking-changes/v-on-native-modifier-removed.html`
      },
      ["COMPILER_V_IF_V_FOR_PRECEDENCE"]: {
        message: `v-if / v-for precedence when used on the same element has changed in Vue 3: v-if now takes higher precedence and will no longer have access to v-for scope variables. It is best to avoid the ambiguity with <template> tags or use a computed property that filters v-for data source.`,
        link: `https://v3-migration.vuejs.org/breaking-changes/v-if-v-for.html`
      },
      ["COMPILER_NATIVE_TEMPLATE"]: {
        message: `<template> with no special directives will render as a native template element instead of its inner content in Vue 3.`
      },
      ["COMPILER_INLINE_TEMPLATE"]: {
        message: `"inline-template" has been removed in Vue 3.`,
        link: `https://v3-migration.vuejs.org/breaking-changes/inline-template-attribute.html`
      },
      ["COMPILER_FILTERS"]: {
        message: `filters have been removed in Vue 3. The "|" symbol will be treated as native JavaScript bitwise OR operator. Use method calls or computed properties instead.`,
        link: `https://v3-migration.vuejs.org/breaking-changes/filters.html`
      }
    };
    ErrorCodes = {
      "ABRUPT_CLOSING_OF_EMPTY_COMMENT": 0,
      "0": "ABRUPT_CLOSING_OF_EMPTY_COMMENT",
      "CDATA_IN_HTML_CONTENT": 1,
      "1": "CDATA_IN_HTML_CONTENT",
      "DUPLICATE_ATTRIBUTE": 2,
      "2": "DUPLICATE_ATTRIBUTE",
      "END_TAG_WITH_ATTRIBUTES": 3,
      "3": "END_TAG_WITH_ATTRIBUTES",
      "END_TAG_WITH_TRAILING_SOLIDUS": 4,
      "4": "END_TAG_WITH_TRAILING_SOLIDUS",
      "EOF_BEFORE_TAG_NAME": 5,
      "5": "EOF_BEFORE_TAG_NAME",
      "EOF_IN_CDATA": 6,
      "6": "EOF_IN_CDATA",
      "EOF_IN_COMMENT": 7,
      "7": "EOF_IN_COMMENT",
      "EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT": 8,
      "8": "EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT",
      "EOF_IN_TAG": 9,
      "9": "EOF_IN_TAG",
      "INCORRECTLY_CLOSED_COMMENT": 10,
      "10": "INCORRECTLY_CLOSED_COMMENT",
      "INCORRECTLY_OPENED_COMMENT": 11,
      "11": "INCORRECTLY_OPENED_COMMENT",
      "INVALID_FIRST_CHARACTER_OF_TAG_NAME": 12,
      "12": "INVALID_FIRST_CHARACTER_OF_TAG_NAME",
      "MISSING_ATTRIBUTE_VALUE": 13,
      "13": "MISSING_ATTRIBUTE_VALUE",
      "MISSING_END_TAG_NAME": 14,
      "14": "MISSING_END_TAG_NAME",
      "MISSING_WHITESPACE_BETWEEN_ATTRIBUTES": 15,
      "15": "MISSING_WHITESPACE_BETWEEN_ATTRIBUTES",
      "NESTED_COMMENT": 16,
      "16": "NESTED_COMMENT",
      "UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME": 17,
      "17": "UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME",
      "UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE": 18,
      "18": "UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE",
      "UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME": 19,
      "19": "UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME",
      "UNEXPECTED_NULL_CHARACTER": 20,
      "20": "UNEXPECTED_NULL_CHARACTER",
      "UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME": 21,
      "21": "UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME",
      "UNEXPECTED_SOLIDUS_IN_TAG": 22,
      "22": "UNEXPECTED_SOLIDUS_IN_TAG",
      "X_INVALID_END_TAG": 23,
      "23": "X_INVALID_END_TAG",
      "X_MISSING_END_TAG": 24,
      "24": "X_MISSING_END_TAG",
      "X_MISSING_INTERPOLATION_END": 25,
      "25": "X_MISSING_INTERPOLATION_END",
      "X_MISSING_DIRECTIVE_NAME": 26,
      "26": "X_MISSING_DIRECTIVE_NAME",
      "X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END": 27,
      "27": "X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END",
      "X_V_IF_NO_EXPRESSION": 28,
      "28": "X_V_IF_NO_EXPRESSION",
      "X_V_IF_SAME_KEY": 29,
      "29": "X_V_IF_SAME_KEY",
      "X_V_ELSE_NO_ADJACENT_IF": 30,
      "30": "X_V_ELSE_NO_ADJACENT_IF",
      "X_V_FOR_NO_EXPRESSION": 31,
      "31": "X_V_FOR_NO_EXPRESSION",
      "X_V_FOR_MALFORMED_EXPRESSION": 32,
      "32": "X_V_FOR_MALFORMED_EXPRESSION",
      "X_V_FOR_TEMPLATE_KEY_PLACEMENT": 33,
      "33": "X_V_FOR_TEMPLATE_KEY_PLACEMENT",
      "X_V_BIND_NO_EXPRESSION": 34,
      "34": "X_V_BIND_NO_EXPRESSION",
      "X_V_ON_NO_EXPRESSION": 35,
      "35": "X_V_ON_NO_EXPRESSION",
      "X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET": 36,
      "36": "X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET",
      "X_V_SLOT_MIXED_SLOT_USAGE": 37,
      "37": "X_V_SLOT_MIXED_SLOT_USAGE",
      "X_V_SLOT_DUPLICATE_SLOT_NAMES": 38,
      "38": "X_V_SLOT_DUPLICATE_SLOT_NAMES",
      "X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN": 39,
      "39": "X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN",
      "X_V_SLOT_MISPLACED": 40,
      "40": "X_V_SLOT_MISPLACED",
      "X_V_MODEL_NO_EXPRESSION": 41,
      "41": "X_V_MODEL_NO_EXPRESSION",
      "X_V_MODEL_MALFORMED_EXPRESSION": 42,
      "42": "X_V_MODEL_MALFORMED_EXPRESSION",
      "X_V_MODEL_ON_SCOPE_VARIABLE": 43,
      "43": "X_V_MODEL_ON_SCOPE_VARIABLE",
      "X_V_MODEL_ON_PROPS": 44,
      "44": "X_V_MODEL_ON_PROPS",
      "X_INVALID_EXPRESSION": 45,
      "45": "X_INVALID_EXPRESSION",
      "X_KEEP_ALIVE_INVALID_CHILDREN": 46,
      "46": "X_KEEP_ALIVE_INVALID_CHILDREN",
      "X_PREFIX_ID_NOT_SUPPORTED": 47,
      "47": "X_PREFIX_ID_NOT_SUPPORTED",
      "X_MODULE_MODE_NOT_SUPPORTED": 48,
      "48": "X_MODULE_MODE_NOT_SUPPORTED",
      "X_CACHE_HANDLER_NOT_SUPPORTED": 49,
      "49": "X_CACHE_HANDLER_NOT_SUPPORTED",
      "X_SCOPE_ID_NOT_SUPPORTED": 50,
      "50": "X_SCOPE_ID_NOT_SUPPORTED",
      "X_VNODE_HOOKS": 51,
      "51": "X_VNODE_HOOKS",
      "X_V_BIND_INVALID_SAME_NAME_ARGUMENT": 52,
      "52": "X_V_BIND_INVALID_SAME_NAME_ARGUMENT",
      "__EXTEND_POINT__": 53,
      "53": "__EXTEND_POINT__"
    };
    errorMessages2 = {
      // parse errors
      [0]: "Illegal comment.",
      [1]: "CDATA section is allowed only in XML context.",
      [2]: "Duplicate attribute.",
      [3]: "End tag cannot have attributes.",
      [4]: "Illegal '/' in tags.",
      [5]: "Unexpected EOF in tag.",
      [6]: "Unexpected EOF in CDATA section.",
      [7]: "Unexpected EOF in comment.",
      [8]: "Unexpected EOF in script.",
      [9]: "Unexpected EOF in tag.",
      [10]: "Incorrectly closed comment.",
      [11]: "Incorrectly opened comment.",
      [12]: "Illegal tag name. Use '&lt;' to print '<'.",
      [13]: "Attribute value was expected.",
      [14]: "End tag name was expected.",
      [15]: "Whitespace was expected.",
      [16]: "Unexpected '<!--' in comment.",
      [17]: `Attribute name cannot contain U+0022 ("), U+0027 ('), and U+003C (<).`,
      [18]: "Unquoted attribute value cannot contain U+0022 (\"), U+0027 ('), U+003C (<), U+003D (=), and U+0060 (`).",
      [19]: "Attribute name cannot start with '='.",
      [21]: "'<?' is allowed only in XML context.",
      [20]: `Unexpected null character.`,
      [22]: "Illegal '/' in tags.",
      // Vue-specific parse errors
      [23]: "Invalid end tag.",
      [24]: "Element is missing end tag.",
      [25]: "Interpolation end sign was not found.",
      [27]: "End bracket for dynamic directive argument was not found. Note that dynamic directive argument cannot contain spaces.",
      [26]: "Legal directive name was expected.",
      // transform errors
      [28]: `v-if/v-else-if is missing expression.`,
      [29]: `v-if/else branches must use unique keys.`,
      [30]: `v-else/v-else-if has no adjacent v-if or v-else-if.`,
      [31]: `v-for is missing expression.`,
      [32]: `v-for has invalid expression.`,
      [33]: `<template v-for> key should be placed on the <template> tag.`,
      [34]: `v-bind is missing expression.`,
      [52]: `v-bind with same-name shorthand only allows static argument.`,
      [35]: `v-on is missing expression.`,
      [36]: `Unexpected custom directive on <slot> outlet.`,
      [37]: `Mixed v-slot usage on both the component and nested <template>. When there are multiple named slots, all slots should use <template> syntax to avoid scope ambiguity.`,
      [38]: `Duplicate slot names found. `,
      [39]: `Extraneous children found when component already has explicitly named default slot. These children will be ignored.`,
      [40]: `v-slot can only be used on components or <template> tags.`,
      [41]: `v-model is missing expression.`,
      [42]: `v-model value must be a valid JavaScript member expression.`,
      [43]: `v-model cannot be used on v-for or v-slot scope variables because they are not writable.`,
      [44]: `v-model cannot be used on a prop, because local prop bindings are not writable.
Use a v-bind binding combined with a v-on listener that emits update:x event instead.`,
      [45]: `Error parsing JavaScript expression: `,
      [46]: `<KeepAlive> expects exactly one child component.`,
      [51]: `@vnode-* hooks in templates are no longer supported. Use the vue: prefix instead. For example, @vnode-mounted should be changed to @vue:mounted. @vnode-* hooks support has been removed in 3.4.`,
      // generic errors
      [47]: `"prefixIdentifiers" option is not supported in this build of compiler.`,
      [48]: `ES module mode is not supported in this build of compiler.`,
      [49]: `"cacheHandlers" option is only supported when the "prefixIdentifiers" option is enabled.`,
      [50]: `"scopeId" option is only supported in module mode.`,
      // just to fulfill types
      [53]: ``
    };
    isFunctionType = (node) => {
      return /Function(?:Expression|Declaration)$|Method$/.test(node.type);
    };
    isStaticProperty = (node) => node && (node.type === "ObjectProperty" || node.type === "ObjectMethod") && !node.computed;
    isStaticPropertyKey = (node, parent) => isStaticProperty(parent) && parent.key === node;
    TS_NODE_TYPES = [
      "TSAsExpression",
      // foo as number
      "TSTypeAssertion",
      // (<number>foo)
      "TSNonNullExpression",
      // foo!
      "TSInstantiationExpression",
      // foo<string>
      "TSSatisfiesExpression"
      // foo satisfies T
    ];
    isStaticExp = (p) => p.type === 4 && p.isStatic;
    nonIdentifierRE = /^\d|[^\$\w\xA0-\uFFFF]/;
    isSimpleIdentifier = (name) => !nonIdentifierRE.test(name);
    validFirstIdentCharRE = /[A-Za-z_$\xA0-\uFFFF]/;
    validIdentCharRE = /[\.\?\w$\xA0-\uFFFF]/;
    whitespaceRE = /\s+[.[]\s*|\s*[.[]\s+/g;
    getExpSource = (exp) => exp.type === 4 ? exp.content : exp.loc.source;
    isMemberExpressionBrowser = (exp) => {
      const path = getExpSource(exp).trim().replace(whitespaceRE, (s) => s.trim());
      let state = 0;
      let stateStack = [];
      let currentOpenBracketCount = 0;
      let currentOpenParensCount = 0;
      let currentStringType = null;
      for (let i = 0; i < path.length; i++) {
        const char = path.charAt(i);
        switch (state) {
          case 0:
            if (char === "[") {
              stateStack.push(state);
              state = 1;
              currentOpenBracketCount++;
            } else if (char === "(") {
              stateStack.push(state);
              state = 2;
              currentOpenParensCount++;
            } else if (!(i === 0 ? validFirstIdentCharRE : validIdentCharRE).test(char)) {
              return false;
            }
            break;
          case 1:
            if (char === `'` || char === `"` || char === "`") {
              stateStack.push(state);
              state = 3;
              currentStringType = char;
            } else if (char === `[`) {
              currentOpenBracketCount++;
            } else if (char === `]`) {
              if (!--currentOpenBracketCount) {
                state = stateStack.pop();
              }
            }
            break;
          case 2:
            if (char === `'` || char === `"` || char === "`") {
              stateStack.push(state);
              state = 3;
              currentStringType = char;
            } else if (char === `(`) {
              currentOpenParensCount++;
            } else if (char === `)`) {
              if (i === path.length - 1) {
                return false;
              }
              if (!--currentOpenParensCount) {
                state = stateStack.pop();
              }
            }
            break;
          case 3:
            if (char === currentStringType) {
              state = stateStack.pop();
              currentStringType = null;
            }
            break;
        }
      }
      return !currentOpenBracketCount && !currentOpenParensCount;
    };
    isMemberExpressionNode = NOOP;
    isMemberExpression = isMemberExpressionBrowser;
    fnExpRE = /^\s*(async\s*)?(\([^)]*?\)|[\w$_]+)\s*(:[^=]+)?=>|^\s*(async\s+)?function(?:\s+[\w$]+)?\s*\(/;
    isFnExpressionBrowser = (exp) => fnExpRE.test(getExpSource(exp));
    isFnExpressionNode = NOOP;
    isFnExpression = isFnExpressionBrowser;
    propsHelperSet = /* @__PURE__ */ new Set([NORMALIZE_PROPS, GUARD_REACTIVE_PROPS]);
    forAliasRE = /([\s\S]*?)\s+(?:in|of)\s+(\S[\s\S]*)/;
    defaultParserOptions = {
      parseMode: "base",
      ns: 0,
      delimiters: [`{{`, `}}`],
      getNamespace: () => 0,
      isVoidTag: NO,
      isPreTag: NO,
      isCustomElement: NO,
      onError: defaultOnError2,
      onWarn: defaultOnWarn,
      comments: true,
      prefixIdentifiers: false
    };
    currentOptions = defaultParserOptions;
    currentRoot = null;
    currentInput = "";
    currentOpenTag = null;
    currentProp = null;
    currentAttrValue = "";
    currentAttrStartIndex = -1;
    currentAttrEndIndex = -1;
    inPre = 0;
    inVPre = false;
    currentVPreBoundary = null;
    stack = [];
    tokenizer = new Tokenizer(stack, {
      onerr: emitError,
      ontext(start, end) {
        onText(getSlice(start, end), start, end);
      },
      ontextentity(char, start, end) {
        onText(char, start, end);
      },
      oninterpolation(start, end) {
        if (inVPre) {
          return onText(getSlice(start, end), start, end);
        }
        let innerStart = start + tokenizer.delimiterOpen.length;
        let innerEnd = end - tokenizer.delimiterClose.length;
        while (isWhitespace(currentInput.charCodeAt(innerStart))) {
          innerStart++;
        }
        while (isWhitespace(currentInput.charCodeAt(innerEnd - 1))) {
          innerEnd--;
        }
        let exp = getSlice(innerStart, innerEnd);
        if (exp.includes("&")) {
          {
            exp = currentOptions.decodeEntities(exp, false);
          }
        }
        addNode({
          type: 5,
          content: createExp(exp, false, getLoc(innerStart, innerEnd)),
          loc: getLoc(start, end)
        });
      },
      onopentagname(start, end) {
        const name = getSlice(start, end);
        currentOpenTag = {
          type: 1,
          tag: name,
          ns: currentOptions.getNamespace(name, stack[0], currentOptions.ns),
          tagType: 0,
          // will be refined on tag close
          props: [],
          children: [],
          loc: getLoc(start - 1, end),
          codegenNode: void 0
        };
      },
      onopentagend(end) {
        endOpenTag(end);
      },
      onclosetag(start, end) {
        const name = getSlice(start, end);
        if (!currentOptions.isVoidTag(name)) {
          let found = false;
          for (let i = 0; i < stack.length; i++) {
            const e = stack[i];
            if (e.tag.toLowerCase() === name.toLowerCase()) {
              found = true;
              if (i > 0) {
                emitError(24, stack[0].loc.start.offset);
              }
              for (let j = 0; j <= i; j++) {
                const el = stack.shift();
                onCloseTag(el, end, j < i);
              }
              break;
            }
          }
          if (!found) {
            emitError(23, backTrack(start, 60));
          }
        }
      },
      onselfclosingtag(end) {
        const name = currentOpenTag.tag;
        currentOpenTag.isSelfClosing = true;
        endOpenTag(end);
        if (stack[0] && stack[0].tag === name) {
          onCloseTag(stack.shift(), end);
        }
      },
      onattribname(start, end) {
        currentProp = {
          type: 6,
          name: getSlice(start, end),
          nameLoc: getLoc(start, end),
          value: void 0,
          loc: getLoc(start)
        };
      },
      ondirname(start, end) {
        const raw = getSlice(start, end);
        const name = raw === "." || raw === ":" ? "bind" : raw === "@" ? "on" : raw === "#" ? "slot" : raw.slice(2);
        if (!inVPre && name === "") {
          emitError(26, start);
        }
        if (inVPre || name === "") {
          currentProp = {
            type: 6,
            name: raw,
            nameLoc: getLoc(start, end),
            value: void 0,
            loc: getLoc(start)
          };
        } else {
          currentProp = {
            type: 7,
            name,
            rawName: raw,
            exp: void 0,
            arg: void 0,
            modifiers: raw === "." ? [createSimpleExpression("prop")] : [],
            loc: getLoc(start)
          };
          if (name === "pre") {
            inVPre = tokenizer.inVPre = true;
            currentVPreBoundary = currentOpenTag;
            const props = currentOpenTag.props;
            for (let i = 0; i < props.length; i++) {
              if (props[i].type === 7) {
                props[i] = dirToAttr(props[i]);
              }
            }
          }
        }
      },
      ondirarg(start, end) {
        if (start === end) return;
        const arg = getSlice(start, end);
        if (inVPre) {
          currentProp.name += arg;
          setLocEnd(currentProp.nameLoc, end);
        } else {
          const isStatic = arg[0] !== `[`;
          currentProp.arg = createExp(
            isStatic ? arg : arg.slice(1, -1),
            isStatic,
            getLoc(start, end),
            isStatic ? 3 : 0
          );
        }
      },
      ondirmodifier(start, end) {
        const mod = getSlice(start, end);
        if (inVPre) {
          currentProp.name += "." + mod;
          setLocEnd(currentProp.nameLoc, end);
        } else if (currentProp.name === "slot") {
          const arg = currentProp.arg;
          if (arg) {
            arg.content += "." + mod;
            setLocEnd(arg.loc, end);
          }
        } else {
          const exp = createSimpleExpression(mod, true, getLoc(start, end));
          currentProp.modifiers.push(exp);
        }
      },
      onattribdata(start, end) {
        currentAttrValue += getSlice(start, end);
        if (currentAttrStartIndex < 0) currentAttrStartIndex = start;
        currentAttrEndIndex = end;
      },
      onattribentity(char, start, end) {
        currentAttrValue += char;
        if (currentAttrStartIndex < 0) currentAttrStartIndex = start;
        currentAttrEndIndex = end;
      },
      onattribnameend(end) {
        const start = currentProp.loc.start.offset;
        const name = getSlice(start, end);
        if (currentProp.type === 7) {
          currentProp.rawName = name;
        }
        if (currentOpenTag.props.some(
          (p) => (p.type === 7 ? p.rawName : p.name) === name
        )) {
          emitError(2, start);
        }
      },
      onattribend(quote, end) {
        if (currentOpenTag && currentProp) {
          setLocEnd(currentProp.loc, end);
          if (quote !== 0) {
            if (currentAttrValue.includes("&")) {
              currentAttrValue = currentOptions.decodeEntities(
                currentAttrValue,
                true
              );
            }
            if (currentProp.type === 6) {
              if (currentProp.name === "class") {
                currentAttrValue = condense(currentAttrValue).trim();
              }
              if (quote === 1 && !currentAttrValue) {
                emitError(13, end);
              }
              currentProp.value = {
                type: 2,
                content: currentAttrValue,
                loc: quote === 1 ? getLoc(currentAttrStartIndex, currentAttrEndIndex) : getLoc(currentAttrStartIndex - 1, currentAttrEndIndex + 1)
              };
              if (tokenizer.inSFCRoot && currentOpenTag.tag === "template" && currentProp.name === "lang" && currentAttrValue && currentAttrValue !== "html") {
                tokenizer.enterRCDATA(toCharCodes(`</template`), 0);
              }
            } else {
              let expParseMode = 0;
              currentProp.exp = createExp(
                currentAttrValue,
                false,
                getLoc(currentAttrStartIndex, currentAttrEndIndex),
                0,
                expParseMode
              );
              if (currentProp.name === "for") {
                currentProp.forParseResult = parseForExpression(currentProp.exp);
              }
              let syncIndex = -1;
              if (currentProp.name === "bind" && (syncIndex = currentProp.modifiers.findIndex(
                (mod) => mod.content === "sync"
              )) > -1 && checkCompatEnabled(
                "COMPILER_V_BIND_SYNC",
                currentOptions,
                currentProp.loc,
                currentProp.rawName
              )) {
                currentProp.name = "model";
                currentProp.modifiers.splice(syncIndex, 1);
              }
            }
          }
          if (currentProp.type !== 7 || currentProp.name !== "pre") {
            currentOpenTag.props.push(currentProp);
          }
        }
        currentAttrValue = "";
        currentAttrStartIndex = currentAttrEndIndex = -1;
      },
      oncomment(start, end) {
        if (currentOptions.comments) {
          addNode({
            type: 3,
            content: getSlice(start, end),
            loc: getLoc(start - 4, end + 3)
          });
        }
      },
      onend() {
        const end = currentInput.length;
        if (tokenizer.state !== 1) {
          switch (tokenizer.state) {
            case 5:
            case 8:
              emitError(5, end);
              break;
            case 3:
            case 4:
              emitError(
                25,
                tokenizer.sectionStart
              );
              break;
            case 28:
              if (tokenizer.currentSequence === Sequences.CdataEnd) {
                emitError(6, end);
              } else {
                emitError(7, end);
              }
              break;
            case 6:
            case 7:
            case 9:
            case 11:
            case 12:
            case 13:
            case 14:
            case 15:
            case 16:
            case 17:
            case 18:
            case 19:
            case 20:
            case 21:
              emitError(9, end);
              break;
          }
        }
        for (let index = 0; index < stack.length; index++) {
          onCloseTag(stack[index], end - 1);
          emitError(24, stack[index].loc.start.offset);
        }
      },
      oncdata(start, end) {
        if (stack[0].ns !== 0) {
          onText(getSlice(start, end), start, end);
        } else {
          emitError(1, start - 9);
        }
      },
      onprocessinginstruction(start) {
        if ((stack[0] ? stack[0].ns : currentOptions.ns) === 0) {
          emitError(
            21,
            start - 1
          );
        }
      }
    });
    forIteratorRE = /,([^,\}\]]*)(?:,([^,\}\]]*))?$/;
    stripParensRE = /^\(|\)$/g;
    specialTemplateDir = /* @__PURE__ */ new Set(["if", "else", "else-if", "for", "slot"]);
    windowsNewlineRE = /\r\n/g;
    allowHoistedHelperSet = /* @__PURE__ */ new Set([
      NORMALIZE_CLASS,
      NORMALIZE_STYLE,
      NORMALIZE_PROPS,
      GUARD_REACTIVE_PROPS
    ]);
    PURE_ANNOTATION = `/*@__PURE__*/`;
    aliasHelper = (s) => `${helperNameMap[s]}: _${helperNameMap[s]}`;
    prohibitedKeywordRE = new RegExp(
      "\\b" + "arguments,await,break,case,catch,class,const,continue,debugger,default,delete,do,else,export,extends,finally,for,function,if,import,let,new,return,super,switch,throw,try,var,void,while,with,yield".split(",").join("\\b|\\b") + "\\b"
    );
    stripStringRE = /'(?:[^'\\]|\\.)*'|"(?:[^"\\]|\\.)*"|`(?:[^`\\]|\\.)*\$\{|\}(?:[^`\\]|\\.)*`|`(?:[^`\\]|\\.)*`/g;
    transformExpression = (node, context) => {
      if (node.type === 5) {
        node.content = processExpression(
          node.content,
          context
        );
      } else if (node.type === 1) {
        for (let i = 0; i < node.props.length; i++) {
          const dir = node.props[i];
          if (dir.type === 7 && dir.name !== "for") {
            const exp = dir.exp;
            const arg = dir.arg;
            if (exp && exp.type === 4 && !(dir.name === "on" && arg)) {
              dir.exp = processExpression(
                exp,
                context,
                // slot args must be processed as function params
                dir.name === "slot"
              );
            }
            if (arg && arg.type === 4 && !arg.isStatic) {
              dir.arg = processExpression(arg, context);
            }
          }
        }
      }
    };
    transformIf = createStructuralDirectiveTransform(
      /^(if|else|else-if)$/,
      (node, dir, context) => {
        return processIf(node, dir, context, (ifNode, branch, isRoot) => {
          const siblings = context.parent.children;
          let i = siblings.indexOf(ifNode);
          let key = 0;
          while (i-- >= 0) {
            const sibling = siblings[i];
            if (sibling && sibling.type === 9) {
              key += sibling.branches.length;
            }
          }
          return () => {
            if (isRoot) {
              ifNode.codegenNode = createCodegenNodeForBranch(
                branch,
                key,
                context
              );
            } else {
              const parentCondition = getParentCondition(ifNode.codegenNode);
              parentCondition.alternate = createCodegenNodeForBranch(
                branch,
                key + ifNode.branches.length - 1,
                context
              );
            }
          };
        });
      }
    );
    transformBind = (dir, _node, context) => {
      const { modifiers, loc } = dir;
      const arg = dir.arg;
      let { exp } = dir;
      if (exp && exp.type === 4 && !exp.content.trim()) {
        {
          exp = void 0;
        }
      }
      if (!exp) {
        if (arg.type !== 4 || !arg.isStatic) {
          context.onError(
            createCompilerError(
              52,
              arg.loc
            )
          );
          return {
            props: [
              createObjectProperty(arg, createSimpleExpression("", true, loc))
            ]
          };
        }
        transformBindShorthand(dir);
        exp = dir.exp;
      }
      if (arg.type !== 4) {
        arg.children.unshift(`(`);
        arg.children.push(`) || ""`);
      } else if (!arg.isStatic) {
        arg.content = `${arg.content} || ""`;
      }
      if (modifiers.some((mod) => mod.content === "camel")) {
        if (arg.type === 4) {
          if (arg.isStatic) {
            arg.content = camelize(arg.content);
          } else {
            arg.content = `${context.helperString(CAMELIZE)}(${arg.content})`;
          }
        } else {
          arg.children.unshift(`${context.helperString(CAMELIZE)}(`);
          arg.children.push(`)`);
        }
      }
      if (!context.inSSR) {
        if (modifiers.some((mod) => mod.content === "prop")) {
          injectPrefix(arg, ".");
        }
        if (modifiers.some((mod) => mod.content === "attr")) {
          injectPrefix(arg, "^");
        }
      }
      return {
        props: [createObjectProperty(arg, exp)]
      };
    };
    transformBindShorthand = (dir, context) => {
      const arg = dir.arg;
      const propName = camelize(arg.content);
      dir.exp = createSimpleExpression(propName, false, arg.loc);
    };
    injectPrefix = (arg, prefix) => {
      if (arg.type === 4) {
        if (arg.isStatic) {
          arg.content = prefix + arg.content;
        } else {
          arg.content = `\`${prefix}\${${arg.content}}\``;
        }
      } else {
        arg.children.unshift(`'${prefix}' + (`);
        arg.children.push(`)`);
      }
    };
    transformFor = createStructuralDirectiveTransform(
      "for",
      (node, dir, context) => {
        const { helper, removeHelper } = context;
        return processFor(node, dir, context, (forNode) => {
          const renderExp = createCallExpression(helper(RENDER_LIST), [
            forNode.source
          ]);
          const isTemplate = isTemplateNode(node);
          const memo = findDir(node, "memo");
          const keyProp = findProp(node, `key`, false, true);
          if (keyProp && keyProp.type === 7 && !keyProp.exp) {
            transformBindShorthand(keyProp);
          }
          const keyExp = keyProp && (keyProp.type === 6 ? keyProp.value ? createSimpleExpression(keyProp.value.content, true) : void 0 : keyProp.exp);
          const keyProperty = keyProp && keyExp ? createObjectProperty(`key`, keyExp) : null;
          const isStableFragment = forNode.source.type === 4 && forNode.source.constType > 0;
          const fragmentFlag = isStableFragment ? 64 : keyProp ? 128 : 256;
          forNode.codegenNode = createVNodeCall(
            context,
            helper(FRAGMENT),
            void 0,
            renderExp,
            fragmentFlag,
            void 0,
            void 0,
            true,
            !isStableFragment,
            false,
            node.loc
          );
          return () => {
            let childBlock;
            const { children } = forNode;
            if (isTemplate) {
              node.children.some((c) => {
                if (c.type === 1) {
                  const key = findProp(c, "key");
                  if (key) {
                    context.onError(
                      createCompilerError(
                        33,
                        key.loc
                      )
                    );
                    return true;
                  }
                }
              });
            }
            const needFragmentWrapper = children.length !== 1 || children[0].type !== 1;
            const slotOutlet = isSlotOutlet(node) ? node : isTemplate && node.children.length === 1 && isSlotOutlet(node.children[0]) ? node.children[0] : null;
            if (slotOutlet) {
              childBlock = slotOutlet.codegenNode;
              if (isTemplate && keyProperty) {
                injectProp(childBlock, keyProperty, context);
              }
            } else if (needFragmentWrapper) {
              childBlock = createVNodeCall(
                context,
                helper(FRAGMENT),
                keyProperty ? createObjectExpression([keyProperty]) : void 0,
                node.children,
                64,
                void 0,
                void 0,
                true,
                void 0,
                false
              );
            } else {
              childBlock = children[0].codegenNode;
              if (isTemplate && keyProperty) {
                injectProp(childBlock, keyProperty, context);
              }
              if (childBlock.isBlock !== !isStableFragment) {
                if (childBlock.isBlock) {
                  removeHelper(OPEN_BLOCK);
                  removeHelper(
                    getVNodeBlockHelper(context.inSSR, childBlock.isComponent)
                  );
                } else {
                  removeHelper(
                    getVNodeHelper(context.inSSR, childBlock.isComponent)
                  );
                }
              }
              childBlock.isBlock = !isStableFragment;
              if (childBlock.isBlock) {
                helper(OPEN_BLOCK);
                helper(getVNodeBlockHelper(context.inSSR, childBlock.isComponent));
              } else {
                helper(getVNodeHelper(context.inSSR, childBlock.isComponent));
              }
            }
            if (memo) {
              const loop = createFunctionExpression(
                createForLoopParams(forNode.parseResult, [
                  createSimpleExpression(`_cached`)
                ])
              );
              loop.body = createBlockStatement([
                createCompoundExpression([`const _memo = (`, memo.exp, `)`]),
                createCompoundExpression([
                  `if (_cached`,
                  ...keyExp ? [` && _cached.key === `, keyExp] : [],
                  ` && ${context.helperString(
                    IS_MEMO_SAME
                  )}(_cached, _memo)) return _cached`
                ]),
                createCompoundExpression([`const _item = `, childBlock]),
                createSimpleExpression(`_item.memo = _memo`),
                createSimpleExpression(`return _item`)
              ]);
              renderExp.arguments.push(
                loop,
                createSimpleExpression(`_cache`),
                createSimpleExpression(String(context.cached.length))
              );
              context.cached.push(null);
            } else {
              renderExp.arguments.push(
                createFunctionExpression(
                  createForLoopParams(forNode.parseResult),
                  childBlock,
                  true
                )
              );
            }
          };
        });
      }
    );
    defaultFallback = createSimpleExpression(`undefined`, false);
    trackSlotScopes = (node, context) => {
      if (node.type === 1 && (node.tagType === 1 || node.tagType === 3)) {
        const vSlot = findDir(node, "slot");
        if (vSlot) {
          vSlot.exp;
          context.scopes.vSlot++;
          return () => {
            context.scopes.vSlot--;
          };
        }
      }
    };
    trackVForSlotScopes = (node, context) => {
      let vFor;
      if (isTemplateNode(node) && node.props.some(isVSlot) && (vFor = findDir(node, "for"))) {
        const result = vFor.forParseResult;
        if (result) {
          finalizeForParseResult(result, context);
          const { value, key, index } = result;
          const { addIdentifiers, removeIdentifiers } = context;
          value && addIdentifiers(value);
          key && addIdentifiers(key);
          index && addIdentifiers(index);
          return () => {
            value && removeIdentifiers(value);
            key && removeIdentifiers(key);
            index && removeIdentifiers(index);
          };
        }
      }
    };
    buildClientSlotFn = (props, _vForExp, children, loc) => createFunctionExpression(
      props,
      children,
      false,
      true,
      children.length ? children[0].loc : loc
    );
    directiveImportMap = /* @__PURE__ */ new WeakMap();
    transformElement = (node, context) => {
      return function postTransformElement() {
        node = context.currentNode;
        if (!(node.type === 1 && (node.tagType === 0 || node.tagType === 1))) {
          return;
        }
        const { tag, props } = node;
        const isComponent2 = node.tagType === 1;
        let vnodeTag = isComponent2 ? resolveComponentType(node, context) : `"${tag}"`;
        const isDynamicComponent = isObject3(vnodeTag) && vnodeTag.callee === RESOLVE_DYNAMIC_COMPONENT;
        let vnodeProps;
        let vnodeChildren;
        let patchFlag = 0;
        let vnodeDynamicProps;
        let dynamicPropNames;
        let vnodeDirectives;
        let shouldUseBlock = (
          // dynamic component may resolve to plain elements
          isDynamicComponent || vnodeTag === TELEPORT || vnodeTag === SUSPENSE || !isComponent2 && // <svg> and <foreignObject> must be forced into blocks so that block
          // updates inside get proper isSVG flag at runtime. (#639, #643)
          // This is technically web-specific, but splitting the logic out of core
          // leads to too much unnecessary complexity.
          (tag === "svg" || tag === "foreignObject" || tag === "math")
        );
        if (props.length > 0) {
          const propsBuildResult = buildProps(
            node,
            context,
            void 0,
            isComponent2,
            isDynamicComponent
          );
          vnodeProps = propsBuildResult.props;
          patchFlag = propsBuildResult.patchFlag;
          dynamicPropNames = propsBuildResult.dynamicPropNames;
          const directives = propsBuildResult.directives;
          vnodeDirectives = directives && directives.length ? createArrayExpression(
            directives.map((dir) => buildDirectiveArgs(dir, context))
          ) : void 0;
          if (propsBuildResult.shouldUseBlock) {
            shouldUseBlock = true;
          }
        }
        if (node.children.length > 0) {
          if (vnodeTag === KEEP_ALIVE) {
            shouldUseBlock = true;
            patchFlag |= 1024;
            if (node.children.length > 1) {
              context.onError(
                createCompilerError(46, {
                  start: node.children[0].loc.start,
                  end: node.children[node.children.length - 1].loc.end,
                  source: ""
                })
              );
            }
          }
          const shouldBuildAsSlots = isComponent2 && // Teleport is not a real component and has dedicated runtime handling
          vnodeTag !== TELEPORT && // explained above.
          vnodeTag !== KEEP_ALIVE;
          if (shouldBuildAsSlots) {
            const { slots, hasDynamicSlots } = buildSlots(node, context);
            vnodeChildren = slots;
            if (hasDynamicSlots) {
              patchFlag |= 1024;
            }
          } else if (node.children.length === 1 && vnodeTag !== TELEPORT) {
            const child = node.children[0];
            const type = child.type;
            const hasDynamicTextChild = type === 5 || type === 8;
            if (hasDynamicTextChild && getConstantType(child, context) === 0) {
              patchFlag |= 1;
            }
            if (hasDynamicTextChild || type === 2) {
              vnodeChildren = child;
            } else {
              vnodeChildren = node.children;
            }
          } else {
            vnodeChildren = node.children;
          }
        }
        if (dynamicPropNames && dynamicPropNames.length) {
          vnodeDynamicProps = stringifyDynamicPropNames(dynamicPropNames);
        }
        node.codegenNode = createVNodeCall(
          context,
          vnodeTag,
          vnodeProps,
          vnodeChildren,
          patchFlag === 0 ? void 0 : patchFlag,
          vnodeDynamicProps,
          vnodeDirectives,
          !!shouldUseBlock,
          false,
          isComponent2,
          node.loc
        );
      };
    };
    transformSlotOutlet = (node, context) => {
      if (isSlotOutlet(node)) {
        const { children, loc } = node;
        const { slotName, slotProps } = processSlotOutlet(node, context);
        const slotArgs = [
          context.prefixIdentifiers ? `_ctx.$slots` : `$slots`,
          slotName,
          "{}",
          "undefined",
          "true"
        ];
        let expectedLen = 2;
        if (slotProps) {
          slotArgs[2] = slotProps;
          expectedLen = 3;
        }
        if (children.length) {
          slotArgs[3] = createFunctionExpression([], children, false, false, loc);
          expectedLen = 4;
        }
        if (context.scopeId && !context.slotted) {
          expectedLen = 5;
        }
        slotArgs.splice(expectedLen);
        node.codegenNode = createCallExpression(
          context.helper(RENDER_SLOT),
          slotArgs,
          loc
        );
      }
    };
    transformOn = (dir, node, context, augmentor) => {
      const { loc, modifiers, arg } = dir;
      if (!dir.exp && !modifiers.length) {
        context.onError(createCompilerError(35, loc));
      }
      let eventName;
      if (arg.type === 4) {
        if (arg.isStatic) {
          let rawName = arg.content;
          if (rawName.startsWith("vnode")) {
            context.onError(createCompilerError(51, arg.loc));
          }
          if (rawName.startsWith("vue:")) {
            rawName = `vnode-${rawName.slice(4)}`;
          }
          const eventString = node.tagType !== 0 || rawName.startsWith("vnode") || !/[A-Z]/.test(rawName) ? (
            // for non-element and vnode lifecycle event listeners, auto convert
            // it to camelCase. See issue #2249
            toHandlerKey(camelize(rawName))
          ) : (
            // preserve case for plain element listeners that have uppercase
            // letters, as these may be custom elements' custom events
            `on:${rawName}`
          );
          eventName = createSimpleExpression(eventString, true, arg.loc);
        } else {
          eventName = createCompoundExpression([
            `${context.helperString(TO_HANDLER_KEY)}(`,
            arg,
            `)`
          ]);
        }
      } else {
        eventName = arg;
        eventName.children.unshift(`${context.helperString(TO_HANDLER_KEY)}(`);
        eventName.children.push(`)`);
      }
      let exp = dir.exp;
      if (exp && !exp.content.trim()) {
        exp = void 0;
      }
      let shouldCache = context.cacheHandlers && !exp && !context.inVOnce;
      if (exp) {
        const isMemberExp = isMemberExpression(exp);
        const isInlineStatement = !(isMemberExp || isFnExpression(exp));
        const hasMultipleStatements = exp.content.includes(`;`);
        if (true) {
          validateBrowserExpression(
            exp,
            context,
            false,
            hasMultipleStatements
          );
        }
        if (isInlineStatement || shouldCache && isMemberExp) {
          exp = createCompoundExpression([
            `${isInlineStatement ? `$event` : `${``}(...args)`} => ${hasMultipleStatements ? `{` : `(`}`,
            exp,
            hasMultipleStatements ? `}` : `)`
          ]);
        }
      }
      let ret = {
        props: [
          createObjectProperty(
            eventName,
            exp || createSimpleExpression(`() => {}`, false, loc)
          )
        ]
      };
      if (augmentor) {
        ret = augmentor(ret);
      }
      if (shouldCache) {
        ret.props[0].value = context.cache(ret.props[0].value);
      }
      ret.props.forEach((p) => p.key.isHandlerKey = true);
      return ret;
    };
    transformText = (node, context) => {
      if (node.type === 0 || node.type === 1 || node.type === 11 || node.type === 10) {
        return () => {
          const children = node.children;
          let currentContainer = void 0;
          let hasText = false;
          for (let i = 0; i < children.length; i++) {
            const child = children[i];
            if (isText$1(child)) {
              hasText = true;
              for (let j = i + 1; j < children.length; j++) {
                const next = children[j];
                if (isText$1(next)) {
                  if (!currentContainer) {
                    currentContainer = children[i] = createCompoundExpression(
                      [child],
                      child.loc
                    );
                  }
                  currentContainer.children.push(` + `, next);
                  children.splice(j, 1);
                  j--;
                } else {
                  currentContainer = void 0;
                  break;
                }
              }
            }
          }
          if (!hasText || // if this is a plain element with a single text child, leave it
          // as-is since the runtime has dedicated fast path for this by directly
          // setting textContent of the element.
          // for component root it's always normalized anyway.
          children.length === 1 && (node.type === 0 || node.type === 1 && node.tagType === 0 && // #3756
          // custom directives can potentially add DOM elements arbitrarily,
          // we need to avoid setting textContent of the element at runtime
          // to avoid accidentally overwriting the DOM elements added
          // by the user through custom directives.
          !node.props.find(
            (p) => p.type === 7 && !context.directiveTransforms[p.name]
          ) && // in compat mode, <template> tags with no special directives
          // will be rendered as a fragment so its children must be
          // converted into vnodes.
          !(node.tag === "template"))) {
            return;
          }
          for (let i = 0; i < children.length; i++) {
            const child = children[i];
            if (isText$1(child) || child.type === 8) {
              const callArgs = [];
              if (child.type !== 2 || child.content !== " ") {
                callArgs.push(child);
              }
              if (!context.ssr && getConstantType(child, context) === 0) {
                callArgs.push(
                  1 + (true ? ` /* ${PatchFlagNames[1]} */` : ``)
                );
              }
              children[i] = {
                type: 12,
                content: child,
                loc: child.loc,
                codegenNode: createCallExpression(
                  context.helper(CREATE_TEXT),
                  callArgs
                )
              };
            }
          }
        };
      }
    };
    seen$1 = /* @__PURE__ */ new WeakSet();
    transformOnce = (node, context) => {
      if (node.type === 1 && findDir(node, "once", true)) {
        if (seen$1.has(node) || context.inVOnce || context.inSSR) {
          return;
        }
        seen$1.add(node);
        context.inVOnce = true;
        context.helper(SET_BLOCK_TRACKING);
        return () => {
          context.inVOnce = false;
          const cur = context.currentNode;
          if (cur.codegenNode) {
            cur.codegenNode = context.cache(
              cur.codegenNode,
              true
              /* isVNode */
            );
          }
        };
      }
    };
    transformModel = (dir, node, context) => {
      const { exp, arg } = dir;
      if (!exp) {
        context.onError(
          createCompilerError(41, dir.loc)
        );
        return createTransformProps();
      }
      const rawExp = exp.loc.source;
      const expString = exp.type === 4 ? exp.content : rawExp;
      const bindingType = context.bindingMetadata[rawExp];
      if (bindingType === "props" || bindingType === "props-aliased") {
        context.onError(createCompilerError(44, exp.loc));
        return createTransformProps();
      }
      const maybeRef = false;
      if (!expString.trim() || !isMemberExpression(exp) && !maybeRef) {
        context.onError(
          createCompilerError(42, exp.loc)
        );
        return createTransformProps();
      }
      const propName = arg ? arg : createSimpleExpression("modelValue", true);
      const eventName = arg ? isStaticExp(arg) ? `onUpdate:${camelize(arg.content)}` : createCompoundExpression(['"onUpdate:" + ', arg]) : `onUpdate:modelValue`;
      let assignmentExp;
      const eventArg = context.isTS ? `($event: any)` : `$event`;
      {
        assignmentExp = createCompoundExpression([
          `${eventArg} => ((`,
          exp,
          `) = $event)`
        ]);
      }
      const props = [
        // modelValue: foo
        createObjectProperty(propName, dir.exp),
        // "onUpdate:modelValue": $event => (foo = $event)
        createObjectProperty(eventName, assignmentExp)
      ];
      if (dir.modifiers.length && node.tagType === 1) {
        const modifiers = dir.modifiers.map((m) => m.content).map((m) => (isSimpleIdentifier(m) ? m : JSON.stringify(m)) + `: true`).join(`, `);
        const modifiersKey = arg ? isStaticExp(arg) ? `${arg.content}Modifiers` : createCompoundExpression([arg, ' + "Modifiers"']) : `modelModifiers`;
        props.push(
          createObjectProperty(
            modifiersKey,
            createSimpleExpression(
              `{ ${modifiers} }`,
              false,
              dir.loc,
              2
            )
          )
        );
      }
      return createTransformProps(props);
    };
    validDivisionCharRE = /[\w).+\-_$\]]/;
    transformFilter = (node, context) => {
      if (!isCompatEnabled("COMPILER_FILTERS", context)) {
        return;
      }
      if (node.type === 5) {
        rewriteFilter(node.content, context);
      } else if (node.type === 1) {
        node.props.forEach((prop) => {
          if (prop.type === 7 && prop.name !== "for" && prop.exp) {
            rewriteFilter(prop.exp, context);
          }
        });
      }
    };
    seen = /* @__PURE__ */ new WeakSet();
    transformMemo = (node, context) => {
      if (node.type === 1) {
        const dir = findDir(node, "memo");
        if (!dir || seen.has(node)) {
          return;
        }
        seen.add(node);
        return () => {
          const codegenNode = node.codegenNode || context.currentNode.codegenNode;
          if (codegenNode && codegenNode.type === 13) {
            if (node.tagType !== 1) {
              convertToBlock(codegenNode, context);
            }
            node.codegenNode = createCallExpression(context.helper(WITH_MEMO), [
              dir.exp,
              createFunctionExpression(void 0, codegenNode),
              `_cache`,
              String(context.cached.length)
            ]);
            context.cached.push(null);
          }
        };
      }
    };
    BindingTypes = {
      "DATA": "data",
      "PROPS": "props",
      "PROPS_ALIASED": "props-aliased",
      "SETUP_LET": "setup-let",
      "SETUP_CONST": "setup-const",
      "SETUP_REACTIVE_CONST": "setup-reactive-const",
      "SETUP_MAYBE_REF": "setup-maybe-ref",
      "SETUP_REF": "setup-ref",
      "OPTIONS": "options",
      "LITERAL_CONST": "literal-const"
    };
    noopDirectiveTransform = () => ({ props: [] });
  }
});

// node_modules/vue/node_modules/@vue/compiler-dom/dist/compiler-dom.esm-bundler.js
var compiler_dom_esm_bundler_exports = {};
__export(compiler_dom_esm_bundler_exports, {
  BASE_TRANSITION: () => BASE_TRANSITION,
  BindingTypes: () => BindingTypes,
  CAMELIZE: () => CAMELIZE,
  CAPITALIZE: () => CAPITALIZE,
  CREATE_BLOCK: () => CREATE_BLOCK,
  CREATE_COMMENT: () => CREATE_COMMENT,
  CREATE_ELEMENT_BLOCK: () => CREATE_ELEMENT_BLOCK,
  CREATE_ELEMENT_VNODE: () => CREATE_ELEMENT_VNODE,
  CREATE_SLOTS: () => CREATE_SLOTS,
  CREATE_STATIC: () => CREATE_STATIC,
  CREATE_TEXT: () => CREATE_TEXT,
  CREATE_VNODE: () => CREATE_VNODE,
  CompilerDeprecationTypes: () => CompilerDeprecationTypes,
  ConstantTypes: () => ConstantTypes,
  DOMDirectiveTransforms: () => DOMDirectiveTransforms,
  DOMErrorCodes: () => DOMErrorCodes,
  DOMErrorMessages: () => DOMErrorMessages,
  DOMNodeTransforms: () => DOMNodeTransforms,
  ElementTypes: () => ElementTypes,
  ErrorCodes: () => ErrorCodes,
  FRAGMENT: () => FRAGMENT,
  GUARD_REACTIVE_PROPS: () => GUARD_REACTIVE_PROPS,
  IS_MEMO_SAME: () => IS_MEMO_SAME,
  IS_REF: () => IS_REF,
  KEEP_ALIVE: () => KEEP_ALIVE,
  MERGE_PROPS: () => MERGE_PROPS,
  NORMALIZE_CLASS: () => NORMALIZE_CLASS,
  NORMALIZE_PROPS: () => NORMALIZE_PROPS,
  NORMALIZE_STYLE: () => NORMALIZE_STYLE,
  Namespaces: () => Namespaces,
  NodeTypes: () => NodeTypes,
  OPEN_BLOCK: () => OPEN_BLOCK,
  POP_SCOPE_ID: () => POP_SCOPE_ID,
  PUSH_SCOPE_ID: () => PUSH_SCOPE_ID,
  RENDER_LIST: () => RENDER_LIST,
  RENDER_SLOT: () => RENDER_SLOT,
  RESOLVE_COMPONENT: () => RESOLVE_COMPONENT,
  RESOLVE_DIRECTIVE: () => RESOLVE_DIRECTIVE,
  RESOLVE_DYNAMIC_COMPONENT: () => RESOLVE_DYNAMIC_COMPONENT,
  RESOLVE_FILTER: () => RESOLVE_FILTER,
  SET_BLOCK_TRACKING: () => SET_BLOCK_TRACKING,
  SUSPENSE: () => SUSPENSE,
  TELEPORT: () => TELEPORT,
  TO_DISPLAY_STRING: () => TO_DISPLAY_STRING,
  TO_HANDLERS: () => TO_HANDLERS,
  TO_HANDLER_KEY: () => TO_HANDLER_KEY,
  TRANSITION: () => TRANSITION,
  TRANSITION_GROUP: () => TRANSITION_GROUP,
  TS_NODE_TYPES: () => TS_NODE_TYPES,
  UNREF: () => UNREF,
  V_MODEL_CHECKBOX: () => V_MODEL_CHECKBOX,
  V_MODEL_DYNAMIC: () => V_MODEL_DYNAMIC,
  V_MODEL_RADIO: () => V_MODEL_RADIO,
  V_MODEL_SELECT: () => V_MODEL_SELECT,
  V_MODEL_TEXT: () => V_MODEL_TEXT,
  V_ON_WITH_KEYS: () => V_ON_WITH_KEYS,
  V_ON_WITH_MODIFIERS: () => V_ON_WITH_MODIFIERS,
  V_SHOW: () => V_SHOW,
  WITH_CTX: () => WITH_CTX,
  WITH_DIRECTIVES: () => WITH_DIRECTIVES,
  WITH_MEMO: () => WITH_MEMO,
  advancePositionWithClone: () => advancePositionWithClone,
  advancePositionWithMutation: () => advancePositionWithMutation,
  assert: () => assert,
  baseCompile: () => baseCompile2,
  baseParse: () => baseParse,
  buildDirectiveArgs: () => buildDirectiveArgs,
  buildProps: () => buildProps,
  buildSlots: () => buildSlots,
  checkCompatEnabled: () => checkCompatEnabled,
  compile: () => compile2,
  convertToBlock: () => convertToBlock,
  createArrayExpression: () => createArrayExpression,
  createAssignmentExpression: () => createAssignmentExpression,
  createBlockStatement: () => createBlockStatement,
  createCacheExpression: () => createCacheExpression,
  createCallExpression: () => createCallExpression,
  createCompilerError: () => createCompilerError,
  createCompoundExpression: () => createCompoundExpression,
  createConditionalExpression: () => createConditionalExpression,
  createDOMCompilerError: () => createDOMCompilerError,
  createForLoopParams: () => createForLoopParams,
  createFunctionExpression: () => createFunctionExpression,
  createIfStatement: () => createIfStatement,
  createInterpolation: () => createInterpolation,
  createObjectExpression: () => createObjectExpression,
  createObjectProperty: () => createObjectProperty,
  createReturnStatement: () => createReturnStatement,
  createRoot: () => createRoot,
  createSequenceExpression: () => createSequenceExpression,
  createSimpleExpression: () => createSimpleExpression,
  createStructuralDirectiveTransform: () => createStructuralDirectiveTransform,
  createTemplateLiteral: () => createTemplateLiteral,
  createTransformContext: () => createTransformContext,
  createVNodeCall: () => createVNodeCall,
  errorMessages: () => errorMessages2,
  extractIdentifiers: () => extractIdentifiers,
  findDir: () => findDir,
  findProp: () => findProp,
  forAliasRE: () => forAliasRE,
  generate: () => generate2,
  generateCodeFrame: () => generateCodeFrame3,
  getBaseTransformPreset: () => getBaseTransformPreset,
  getConstantType: () => getConstantType,
  getMemoedVNodeCall: () => getMemoedVNodeCall,
  getVNodeBlockHelper: () => getVNodeBlockHelper,
  getVNodeHelper: () => getVNodeHelper,
  hasDynamicKeyVBind: () => hasDynamicKeyVBind,
  hasScopeRef: () => hasScopeRef,
  helperNameMap: () => helperNameMap,
  injectProp: () => injectProp,
  isCoreComponent: () => isCoreComponent,
  isFnExpression: () => isFnExpression,
  isFnExpressionBrowser: () => isFnExpressionBrowser,
  isFnExpressionNode: () => isFnExpressionNode,
  isFunctionType: () => isFunctionType,
  isInDestructureAssignment: () => isInDestructureAssignment,
  isInNewExpression: () => isInNewExpression,
  isMemberExpression: () => isMemberExpression,
  isMemberExpressionBrowser: () => isMemberExpressionBrowser,
  isMemberExpressionNode: () => isMemberExpressionNode,
  isReferencedIdentifier: () => isReferencedIdentifier,
  isSimpleIdentifier: () => isSimpleIdentifier,
  isSlotOutlet: () => isSlotOutlet,
  isStaticArgOf: () => isStaticArgOf,
  isStaticExp: () => isStaticExp,
  isStaticProperty: () => isStaticProperty,
  isStaticPropertyKey: () => isStaticPropertyKey,
  isTemplateNode: () => isTemplateNode,
  isText: () => isText$1,
  isVSlot: () => isVSlot,
  locStub: () => locStub,
  noopDirectiveTransform: () => noopDirectiveTransform,
  parse: () => parse2,
  parserOptions: () => parserOptions,
  processExpression: () => processExpression,
  processFor: () => processFor,
  processIf: () => processIf,
  processSlotOutlet: () => processSlotOutlet,
  registerRuntimeHelpers: () => registerRuntimeHelpers,
  resolveComponentType: () => resolveComponentType,
  stringifyExpression: () => stringifyExpression,
  toValidAssetId: () => toValidAssetId,
  trackSlotScopes: () => trackSlotScopes,
  trackVForSlotScopes: () => trackVForSlotScopes,
  transform: () => transform2,
  transformBind: () => transformBind,
  transformElement: () => transformElement,
  transformExpression: () => transformExpression,
  transformModel: () => transformModel,
  transformOn: () => transformOn,
  transformStyle: () => transformStyle,
  traverseNode: () => traverseNode2,
  unwrapTSNode: () => unwrapTSNode,
  walkBlockDeclarations: () => walkBlockDeclarations,
  walkFunctionParams: () => walkFunctionParams,
  walkIdentifiers: () => walkIdentifiers,
  warnDeprecation: () => warnDeprecation
});
function decodeHtmlBrowser(raw, asAttr = false) {
  if (!decoder) {
    decoder = document.createElement("div");
  }
  if (asAttr) {
    decoder.innerHTML = `<div foo="${raw.replace(/"/g, "&quot;")}">`;
    return decoder.children[0].getAttribute("foo");
  } else {
    decoder.innerHTML = raw;
    return decoder.textContent;
  }
}
function createDOMCompilerError(code2, loc) {
  return createCompilerError(
    code2,
    loc,
    true ? DOMErrorMessages : void 0
  );
}
function hasMultipleChildren(node) {
  const children = node.children = node.children.filter(
    (c) => c.type !== 3 && !(c.type === 2 && !c.content.trim())
  );
  const child = children[0];
  return children.length !== 1 || child.type === 11 || child.type === 9 && child.branches.some(hasMultipleChildren);
}
function isValidHTMLNesting(parent, child) {
  if (parent in onlyValidChildren) {
    return onlyValidChildren[parent].has(child);
  }
  if (child in onlyValidParents) {
    return onlyValidParents[child].has(parent);
  }
  if (parent in knownInvalidChildren) {
    if (knownInvalidChildren[parent].has(child)) return false;
  }
  if (child in knownInvalidParents) {
    if (knownInvalidParents[child].has(parent)) return false;
  }
  return true;
}
function compile2(src, options = {}) {
  return baseCompile2(
    src,
    extend({}, parserOptions, options, {
      nodeTransforms: [
        // ignore <script> and <tag>
        // this is not put inside DOMNodeTransforms because that list is used
        // by compiler-ssr to generate vnode fallback branches
        ignoreSideEffectTags,
        ...DOMNodeTransforms,
        ...options.nodeTransforms || []
      ],
      directiveTransforms: extend(
        {},
        DOMDirectiveTransforms,
        options.directiveTransforms || {}
      ),
      transformHoist: null
    })
  );
}
function parse2(template, options = {}) {
  return baseParse(template, extend({}, parserOptions, options));
}
var V_MODEL_RADIO, V_MODEL_CHECKBOX, V_MODEL_TEXT, V_MODEL_SELECT, V_MODEL_DYNAMIC, V_ON_WITH_MODIFIERS, V_ON_WITH_KEYS, V_SHOW, TRANSITION, TRANSITION_GROUP, decoder, parserOptions, transformStyle, parseInlineCSS, DOMErrorCodes, DOMErrorMessages, transformVHtml, transformVText, transformModel2, isEventOptionModifier, isNonKeyModifier, maybeKeyModifier, isKeyboardEvent, resolveModifiers, transformClick, transformOn2, transformShow, transformTransition, ignoreSideEffectTags, headings, emptySet, onlyValidChildren, onlyValidParents, knownInvalidChildren, knownInvalidParents, validateHtmlNesting, DOMNodeTransforms, DOMDirectiveTransforms;
var init_compiler_dom_esm_bundler = __esm({
  "node_modules/vue/node_modules/@vue/compiler-dom/dist/compiler-dom.esm-bundler.js"() {
    init_compiler_core_esm_bundler();
    init_compiler_core_esm_bundler();
    init_shared_esm_bundler();
    V_MODEL_RADIO = Symbol(true ? `vModelRadio` : ``);
    V_MODEL_CHECKBOX = Symbol(
      true ? `vModelCheckbox` : ``
    );
    V_MODEL_TEXT = Symbol(true ? `vModelText` : ``);
    V_MODEL_SELECT = Symbol(
      true ? `vModelSelect` : ``
    );
    V_MODEL_DYNAMIC = Symbol(
      true ? `vModelDynamic` : ``
    );
    V_ON_WITH_MODIFIERS = Symbol(
      true ? `vOnModifiersGuard` : ``
    );
    V_ON_WITH_KEYS = Symbol(
      true ? `vOnKeysGuard` : ``
    );
    V_SHOW = Symbol(true ? `vShow` : ``);
    TRANSITION = Symbol(true ? `Transition` : ``);
    TRANSITION_GROUP = Symbol(
      true ? `TransitionGroup` : ``
    );
    registerRuntimeHelpers({
      [V_MODEL_RADIO]: `vModelRadio`,
      [V_MODEL_CHECKBOX]: `vModelCheckbox`,
      [V_MODEL_TEXT]: `vModelText`,
      [V_MODEL_SELECT]: `vModelSelect`,
      [V_MODEL_DYNAMIC]: `vModelDynamic`,
      [V_ON_WITH_MODIFIERS]: `withModifiers`,
      [V_ON_WITH_KEYS]: `withKeys`,
      [V_SHOW]: `vShow`,
      [TRANSITION]: `Transition`,
      [TRANSITION_GROUP]: `TransitionGroup`
    });
    parserOptions = {
      parseMode: "html",
      isVoidTag,
      isNativeTag: (tag) => isHTMLTag(tag) || isSVGTag(tag) || isMathMLTag(tag),
      isPreTag: (tag) => tag === "pre",
      decodeEntities: decodeHtmlBrowser,
      isBuiltInComponent: (tag) => {
        if (tag === "Transition" || tag === "transition") {
          return TRANSITION;
        } else if (tag === "TransitionGroup" || tag === "transition-group") {
          return TRANSITION_GROUP;
        }
      },
      // https://html.spec.whatwg.org/multipage/parsing.html#tree-construction-dispatcher
      getNamespace(tag, parent, rootNamespace) {
        let ns = parent ? parent.ns : rootNamespace;
        if (parent && ns === 2) {
          if (parent.tag === "annotation-xml") {
            if (tag === "svg") {
              return 1;
            }
            if (parent.props.some(
              (a) => a.type === 6 && a.name === "encoding" && a.value != null && (a.value.content === "text/html" || a.value.content === "application/xhtml+xml")
            )) {
              ns = 0;
            }
          } else if (/^m(?:[ions]|text)$/.test(parent.tag) && tag !== "mglyph" && tag !== "malignmark") {
            ns = 0;
          }
        } else if (parent && ns === 1) {
          if (parent.tag === "foreignObject" || parent.tag === "desc" || parent.tag === "title") {
            ns = 0;
          }
        }
        if (ns === 0) {
          if (tag === "svg") {
            return 1;
          }
          if (tag === "math") {
            return 2;
          }
        }
        return ns;
      }
    };
    transformStyle = (node) => {
      if (node.type === 1) {
        node.props.forEach((p, i) => {
          if (p.type === 6 && p.name === "style" && p.value) {
            node.props[i] = {
              type: 7,
              name: `bind`,
              arg: createSimpleExpression(`style`, true, p.loc),
              exp: parseInlineCSS(p.value.content, p.loc),
              modifiers: [],
              loc: p.loc
            };
          }
        });
      }
    };
    parseInlineCSS = (cssText, loc) => {
      const normalized = parseStringStyle(cssText);
      return createSimpleExpression(
        JSON.stringify(normalized),
        false,
        loc,
        3
      );
    };
    DOMErrorCodes = {
      "X_V_HTML_NO_EXPRESSION": 53,
      "53": "X_V_HTML_NO_EXPRESSION",
      "X_V_HTML_WITH_CHILDREN": 54,
      "54": "X_V_HTML_WITH_CHILDREN",
      "X_V_TEXT_NO_EXPRESSION": 55,
      "55": "X_V_TEXT_NO_EXPRESSION",
      "X_V_TEXT_WITH_CHILDREN": 56,
      "56": "X_V_TEXT_WITH_CHILDREN",
      "X_V_MODEL_ON_INVALID_ELEMENT": 57,
      "57": "X_V_MODEL_ON_INVALID_ELEMENT",
      "X_V_MODEL_ARG_ON_ELEMENT": 58,
      "58": "X_V_MODEL_ARG_ON_ELEMENT",
      "X_V_MODEL_ON_FILE_INPUT_ELEMENT": 59,
      "59": "X_V_MODEL_ON_FILE_INPUT_ELEMENT",
      "X_V_MODEL_UNNECESSARY_VALUE": 60,
      "60": "X_V_MODEL_UNNECESSARY_VALUE",
      "X_V_SHOW_NO_EXPRESSION": 61,
      "61": "X_V_SHOW_NO_EXPRESSION",
      "X_TRANSITION_INVALID_CHILDREN": 62,
      "62": "X_TRANSITION_INVALID_CHILDREN",
      "X_IGNORED_SIDE_EFFECT_TAG": 63,
      "63": "X_IGNORED_SIDE_EFFECT_TAG",
      "__EXTEND_POINT__": 64,
      "64": "__EXTEND_POINT__"
    };
    DOMErrorMessages = {
      [53]: `v-html is missing expression.`,
      [54]: `v-html will override element children.`,
      [55]: `v-text is missing expression.`,
      [56]: `v-text will override element children.`,
      [57]: `v-model can only be used on <input>, <textarea> and <select> elements.`,
      [58]: `v-model argument is not supported on plain elements.`,
      [59]: `v-model cannot be used on file inputs since they are read-only. Use a v-on:change listener instead.`,
      [60]: `Unnecessary value binding used alongside v-model. It will interfere with v-model's behavior.`,
      [61]: `v-show is missing expression.`,
      [62]: `<Transition> expects exactly one child element or component.`,
      [63]: `Tags with side effect (<script> and <style>) are ignored in client component templates.`
    };
    transformVHtml = (dir, node, context) => {
      const { exp, loc } = dir;
      if (!exp) {
        context.onError(
          createDOMCompilerError(53, loc)
        );
      }
      if (node.children.length) {
        context.onError(
          createDOMCompilerError(54, loc)
        );
        node.children.length = 0;
      }
      return {
        props: [
          createObjectProperty(
            createSimpleExpression(`innerHTML`, true, loc),
            exp || createSimpleExpression("", true)
          )
        ]
      };
    };
    transformVText = (dir, node, context) => {
      const { exp, loc } = dir;
      if (!exp) {
        context.onError(
          createDOMCompilerError(55, loc)
        );
      }
      if (node.children.length) {
        context.onError(
          createDOMCompilerError(56, loc)
        );
        node.children.length = 0;
      }
      return {
        props: [
          createObjectProperty(
            createSimpleExpression(`textContent`, true),
            exp ? getConstantType(exp, context) > 0 ? exp : createCallExpression(
              context.helperString(TO_DISPLAY_STRING),
              [exp],
              loc
            ) : createSimpleExpression("", true)
          )
        ]
      };
    };
    transformModel2 = (dir, node, context) => {
      const baseResult = transformModel(dir, node, context);
      if (!baseResult.props.length || node.tagType === 1) {
        return baseResult;
      }
      if (dir.arg) {
        context.onError(
          createDOMCompilerError(
            58,
            dir.arg.loc
          )
        );
      }
      function checkDuplicatedValue() {
        const value = findDir(node, "bind");
        if (value && isStaticArgOf(value.arg, "value")) {
          context.onError(
            createDOMCompilerError(
              60,
              value.loc
            )
          );
        }
      }
      const { tag } = node;
      const isCustomElement = context.isCustomElement(tag);
      if (tag === "input" || tag === "textarea" || tag === "select" || isCustomElement) {
        let directiveToUse = V_MODEL_TEXT;
        let isInvalidType = false;
        if (tag === "input" || isCustomElement) {
          const type = findProp(node, `type`);
          if (type) {
            if (type.type === 7) {
              directiveToUse = V_MODEL_DYNAMIC;
            } else if (type.value) {
              switch (type.value.content) {
                case "radio":
                  directiveToUse = V_MODEL_RADIO;
                  break;
                case "checkbox":
                  directiveToUse = V_MODEL_CHECKBOX;
                  break;
                case "file":
                  isInvalidType = true;
                  context.onError(
                    createDOMCompilerError(
                      59,
                      dir.loc
                    )
                  );
                  break;
                default:
                  checkDuplicatedValue();
                  break;
              }
            }
          } else if (hasDynamicKeyVBind(node)) {
            directiveToUse = V_MODEL_DYNAMIC;
          } else {
            checkDuplicatedValue();
          }
        } else if (tag === "select") {
          directiveToUse = V_MODEL_SELECT;
        } else {
          checkDuplicatedValue();
        }
        if (!isInvalidType) {
          baseResult.needRuntime = context.helper(directiveToUse);
        }
      } else {
        context.onError(
          createDOMCompilerError(
            57,
            dir.loc
          )
        );
      }
      baseResult.props = baseResult.props.filter(
        (p) => !(p.key.type === 4 && p.key.content === "modelValue")
      );
      return baseResult;
    };
    isEventOptionModifier = makeMap(`passive,once,capture`);
    isNonKeyModifier = makeMap(
      // event propagation management
      `stop,prevent,self,ctrl,shift,alt,meta,exact,middle`
    );
    maybeKeyModifier = makeMap("left,right");
    isKeyboardEvent = makeMap(
      `onkeyup,onkeydown,onkeypress`,
      true
    );
    resolveModifiers = (key, modifiers, context, loc) => {
      const keyModifiers = [];
      const nonKeyModifiers = [];
      const eventOptionModifiers = [];
      for (let i = 0; i < modifiers.length; i++) {
        const modifier = modifiers[i].content;
        if (modifier === "native" && checkCompatEnabled(
          "COMPILER_V_ON_NATIVE",
          context,
          loc
        )) {
          eventOptionModifiers.push(modifier);
        } else if (isEventOptionModifier(modifier)) {
          eventOptionModifiers.push(modifier);
        } else {
          if (maybeKeyModifier(modifier)) {
            if (isStaticExp(key)) {
              if (isKeyboardEvent(key.content)) {
                keyModifiers.push(modifier);
              } else {
                nonKeyModifiers.push(modifier);
              }
            } else {
              keyModifiers.push(modifier);
              nonKeyModifiers.push(modifier);
            }
          } else {
            if (isNonKeyModifier(modifier)) {
              nonKeyModifiers.push(modifier);
            } else {
              keyModifiers.push(modifier);
            }
          }
        }
      }
      return {
        keyModifiers,
        nonKeyModifiers,
        eventOptionModifiers
      };
    };
    transformClick = (key, event) => {
      const isStaticClick = isStaticExp(key) && key.content.toLowerCase() === "onclick";
      return isStaticClick ? createSimpleExpression(event, true) : key.type !== 4 ? createCompoundExpression([
        `(`,
        key,
        `) === "onClick" ? "${event}" : (`,
        key,
        `)`
      ]) : key;
    };
    transformOn2 = (dir, node, context) => {
      return transformOn(dir, node, context, (baseResult) => {
        const { modifiers } = dir;
        if (!modifiers.length) return baseResult;
        let { key, value: handlerExp } = baseResult.props[0];
        const { keyModifiers, nonKeyModifiers, eventOptionModifiers } = resolveModifiers(key, modifiers, context, dir.loc);
        if (nonKeyModifiers.includes("right")) {
          key = transformClick(key, `onContextmenu`);
        }
        if (nonKeyModifiers.includes("middle")) {
          key = transformClick(key, `onMouseup`);
        }
        if (nonKeyModifiers.length) {
          handlerExp = createCallExpression(context.helper(V_ON_WITH_MODIFIERS), [
            handlerExp,
            JSON.stringify(nonKeyModifiers)
          ]);
        }
        if (keyModifiers.length && // if event name is dynamic, always wrap with keys guard
        (!isStaticExp(key) || isKeyboardEvent(key.content))) {
          handlerExp = createCallExpression(context.helper(V_ON_WITH_KEYS), [
            handlerExp,
            JSON.stringify(keyModifiers)
          ]);
        }
        if (eventOptionModifiers.length) {
          const modifierPostfix = eventOptionModifiers.map(capitalize2).join("");
          key = isStaticExp(key) ? createSimpleExpression(`${key.content}${modifierPostfix}`, true) : createCompoundExpression([`(`, key, `) + "${modifierPostfix}"`]);
        }
        return {
          props: [createObjectProperty(key, handlerExp)]
        };
      });
    };
    transformShow = (dir, node, context) => {
      const { exp, loc } = dir;
      if (!exp) {
        context.onError(
          createDOMCompilerError(61, loc)
        );
      }
      return {
        props: [],
        needRuntime: context.helper(V_SHOW)
      };
    };
    transformTransition = (node, context) => {
      if (node.type === 1 && node.tagType === 1) {
        const component = context.isBuiltInComponent(node.tag);
        if (component === TRANSITION) {
          return () => {
            if (!node.children.length) {
              return;
            }
            if (hasMultipleChildren(node)) {
              context.onError(
                createDOMCompilerError(
                  62,
                  {
                    start: node.children[0].loc.start,
                    end: node.children[node.children.length - 1].loc.end,
                    source: ""
                  }
                )
              );
            }
            const child = node.children[0];
            if (child.type === 1) {
              for (const p of child.props) {
                if (p.type === 7 && p.name === "show") {
                  node.props.push({
                    type: 6,
                    name: "persisted",
                    nameLoc: node.loc,
                    value: void 0,
                    loc: node.loc
                  });
                }
              }
            }
          };
        }
      }
    };
    ignoreSideEffectTags = (node, context) => {
      if (node.type === 1 && node.tagType === 0 && (node.tag === "script" || node.tag === "style")) {
        context.onError(
          createDOMCompilerError(
            63,
            node.loc
          )
        );
        context.removeNode();
      }
    };
    headings = /* @__PURE__ */ new Set(["h1", "h2", "h3", "h4", "h5", "h6"]);
    emptySet = /* @__PURE__ */ new Set([]);
    onlyValidChildren = {
      head: /* @__PURE__ */ new Set([
        "base",
        "basefront",
        "bgsound",
        "link",
        "meta",
        "title",
        "noscript",
        "noframes",
        "style",
        "script",
        "template"
      ]),
      optgroup: /* @__PURE__ */ new Set(["option"]),
      select: /* @__PURE__ */ new Set(["optgroup", "option", "hr"]),
      // table
      table: /* @__PURE__ */ new Set(["caption", "colgroup", "tbody", "tfoot", "thead"]),
      tr: /* @__PURE__ */ new Set(["td", "th"]),
      colgroup: /* @__PURE__ */ new Set(["col"]),
      tbody: /* @__PURE__ */ new Set(["tr"]),
      thead: /* @__PURE__ */ new Set(["tr"]),
      tfoot: /* @__PURE__ */ new Set(["tr"]),
      // these elements can not have any children elements
      script: emptySet,
      iframe: emptySet,
      option: emptySet,
      textarea: emptySet,
      style: emptySet,
      title: emptySet
    };
    onlyValidParents = {
      // sections
      html: emptySet,
      body: /* @__PURE__ */ new Set(["html"]),
      head: /* @__PURE__ */ new Set(["html"]),
      // table
      td: /* @__PURE__ */ new Set(["tr"]),
      colgroup: /* @__PURE__ */ new Set(["table"]),
      caption: /* @__PURE__ */ new Set(["table"]),
      tbody: /* @__PURE__ */ new Set(["table"]),
      tfoot: /* @__PURE__ */ new Set(["table"]),
      col: /* @__PURE__ */ new Set(["colgroup"]),
      th: /* @__PURE__ */ new Set(["tr"]),
      thead: /* @__PURE__ */ new Set(["table"]),
      tr: /* @__PURE__ */ new Set(["tbody", "thead", "tfoot"]),
      // data list
      dd: /* @__PURE__ */ new Set(["dl", "div"]),
      dt: /* @__PURE__ */ new Set(["dl", "div"]),
      // other
      figcaption: /* @__PURE__ */ new Set(["figure"]),
      // li: new Set(["ul", "ol"]),
      summary: /* @__PURE__ */ new Set(["details"]),
      area: /* @__PURE__ */ new Set(["map"])
    };
    knownInvalidChildren = {
      p: /* @__PURE__ */ new Set([
        "address",
        "article",
        "aside",
        "blockquote",
        "center",
        "details",
        "dialog",
        "dir",
        "div",
        "dl",
        "fieldset",
        "figure",
        "footer",
        "form",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "header",
        "hgroup",
        "hr",
        "li",
        "main",
        "nav",
        "menu",
        "ol",
        "p",
        "pre",
        "section",
        "table",
        "ul"
      ]),
      svg: /* @__PURE__ */ new Set([
        "b",
        "blockquote",
        "br",
        "code",
        "dd",
        "div",
        "dl",
        "dt",
        "em",
        "embed",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "hr",
        "i",
        "img",
        "li",
        "menu",
        "meta",
        "ol",
        "p",
        "pre",
        "ruby",
        "s",
        "small",
        "span",
        "strong",
        "sub",
        "sup",
        "table",
        "u",
        "ul",
        "var"
      ])
    };
    knownInvalidParents = {
      a: /* @__PURE__ */ new Set(["a"]),
      button: /* @__PURE__ */ new Set(["button"]),
      dd: /* @__PURE__ */ new Set(["dd", "dt"]),
      dt: /* @__PURE__ */ new Set(["dd", "dt"]),
      form: /* @__PURE__ */ new Set(["form"]),
      li: /* @__PURE__ */ new Set(["li"]),
      h1: headings,
      h2: headings,
      h3: headings,
      h4: headings,
      h5: headings,
      h6: headings
    };
    validateHtmlNesting = (node, context) => {
      if (node.type === 1 && node.tagType === 0 && context.parent && context.parent.type === 1 && context.parent.tagType === 0 && !isValidHTMLNesting(context.parent.tag, node.tag)) {
        const error = new SyntaxError(
          `<${node.tag}> cannot be child of <${context.parent.tag}>, according to HTML specifications. This can cause hydration errors or potentially disrupt future functionality.`
        );
        error.loc = node.loc;
        context.onWarn(error);
      }
    };
    DOMNodeTransforms = [
      transformStyle,
      ...true ? [transformTransition, validateHtmlNesting] : []
    ];
    DOMDirectiveTransforms = {
      cloak: noopDirectiveTransform,
      html: transformVHtml,
      text: transformVText,
      model: transformModel2,
      // override compiler-core
      on: transformOn2,
      // override compiler-core
      show: transformShow
    };
  }
});

// node_modules/vue/dist/vue.cjs.js
var require_vue_cjs = __commonJS({
  "node_modules/vue/dist/vue.cjs.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var compilerDom = (init_compiler_dom_esm_bundler(), __toCommonJS(compiler_dom_esm_bundler_exports));
    var runtimeDom = (init_runtime_dom_esm_bundler(), __toCommonJS(runtime_dom_esm_bundler_exports));
    var shared = (init_shared_esm_bundler(), __toCommonJS(shared_esm_bundler_exports));
    function _interopNamespaceDefault(e) {
      var n = /* @__PURE__ */ Object.create(null);
      if (e) {
        for (var k in e) {
          n[k] = e[k];
        }
      }
      n.default = e;
      return Object.freeze(n);
    }
    var runtimeDom__namespace = _interopNamespaceDefault(runtimeDom);
    var compileCache2 = /* @__PURE__ */ new WeakMap();
    function getCache(options) {
      let c = compileCache2.get(options != null ? options : shared.EMPTY_OBJ);
      if (!c) {
        c = /* @__PURE__ */ Object.create(null);
        compileCache2.set(options != null ? options : shared.EMPTY_OBJ, c);
      }
      return c;
    }
    function compileToFunction2(template, options) {
      if (!shared.isString(template)) {
        if (template.nodeType) {
          template = template.innerHTML;
        } else {
          runtimeDom.warn(`invalid template option: `, template);
          return shared.NOOP;
        }
      }
      const key = template;
      const cache2 = getCache(options);
      const cached = cache2[key];
      if (cached) {
        return cached;
      }
      if (template[0] === "#") {
        const el = document.querySelector(template);
        if (!el) {
          runtimeDom.warn(`Template element not found or is empty: ${template}`);
        }
        template = el ? el.innerHTML : ``;
      }
      const opts = shared.extend(
        {
          hoistStatic: true,
          onError,
          onWarn: (e) => onError(e, true)
        },
        options
      );
      if (!opts.isCustomElement && typeof customElements !== "undefined") {
        opts.isCustomElement = (tag) => !!customElements.get(tag);
      }
      const { code: code2 } = compilerDom.compile(template, opts);
      function onError(err, asWarning = false) {
        const message = asWarning ? err.message : `Template compilation error: ${err.message}`;
        const codeFrame = err.loc && shared.generateCodeFrame(
          template,
          err.loc.start.offset,
          err.loc.end.offset
        );
        runtimeDom.warn(codeFrame ? `${message}
${codeFrame}` : message);
      }
      const render = new Function("Vue", code2)(runtimeDom__namespace);
      render._rc = true;
      return cache2[key] = render;
    }
    runtimeDom.registerRuntimeCompiler(compileToFunction2);
    exports.compile = compileToFunction2;
    Object.keys(runtimeDom).forEach(function(k) {
      if (k !== "default" && !Object.prototype.hasOwnProperty.call(exports, k)) exports[k] = runtimeDom[k];
    });
  }
});

// node_modules/vue/index.js
var require_vue = __commonJS({
  "node_modules/vue/index.js"(exports, module) {
    "use strict";
    if (false) {
      module.exports = null;
    } else {
      module.exports = require_vue_cjs();
    }
  }
});

// node_modules/vue-i18n/dist/vue-i18n.cjs
var require_vue_i18n = __commonJS({
  "node_modules/vue-i18n/dist/vue-i18n.cjs"(exports) {
    "use strict";
    var shared = (init_shared_esm_browser(), __toCommonJS(shared_esm_browser_exports));
    var coreBase = (init_core_base_esm_browser(), __toCommonJS(core_base_esm_browser_exports));
    var vue = require_vue();
    var VERSION2 = "9.14.1";
    var code$12 = coreBase.CoreWarnCodes.__EXTEND_POINT__;
    var inc$12 = shared.incrementer(code$12);
    var I18nWarnCodes = {
      FALLBACK_TO_ROOT: code$12,
      // 9
      NOT_SUPPORTED_PRESERVE: inc$12(),
      // 10
      NOT_SUPPORTED_FORMATTER: inc$12(),
      // 11
      NOT_SUPPORTED_PRESERVE_DIRECTIVE: inc$12(),
      // 12
      NOT_SUPPORTED_GET_CHOICE_INDEX: inc$12(),
      // 13
      COMPONENT_NAME_LEGACY_COMPATIBLE: inc$12(),
      // 14
      NOT_FOUND_PARENT_SCOPE: inc$12(),
      // 15
      IGNORE_OBJ_FLATTEN: inc$12(),
      // 16
      NOTICE_DROP_ALLOW_COMPOSITION: inc$12(),
      // 17
      NOTICE_DROP_TRANSLATE_EXIST_COMPATIBLE_FLAG: inc$12()
      // 18
    };
    var warnMessages2 = {
      [I18nWarnCodes.FALLBACK_TO_ROOT]: `Fall back to {type} '{key}' with root locale.`,
      [I18nWarnCodes.NOT_SUPPORTED_PRESERVE]: `Not supported 'preserve'.`,
      [I18nWarnCodes.NOT_SUPPORTED_FORMATTER]: `Not supported 'formatter'.`,
      [I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE]: `Not supported 'preserveDirectiveContent'.`,
      [I18nWarnCodes.NOT_SUPPORTED_GET_CHOICE_INDEX]: `Not supported 'getChoiceIndex'.`,
      [I18nWarnCodes.COMPONENT_NAME_LEGACY_COMPATIBLE]: `Component name legacy compatible: '{name}' -> 'i18n'`,
      [I18nWarnCodes.NOT_FOUND_PARENT_SCOPE]: `Not found parent scope. use the global scope.`,
      [I18nWarnCodes.IGNORE_OBJ_FLATTEN]: `Ignore object flatten: '{key}' key has an string value`,
      [I18nWarnCodes.NOTICE_DROP_ALLOW_COMPOSITION]: `'allowComposition' option will be dropped in the next major version. For more information, please see 👉 https://tinyurl.com/2p97mcze`,
      [I18nWarnCodes.NOTICE_DROP_TRANSLATE_EXIST_COMPATIBLE_FLAG]: `'translateExistCompatible' option will be dropped in the next major version.`
    };
    function getWarnMessage2(code3, ...args) {
      return shared.format(warnMessages2[code3], ...args);
    }
    var code2 = coreBase.CoreErrorCodes.__EXTEND_POINT__;
    var inc2 = shared.incrementer(code2);
    var I18nErrorCodes = {
      // composer module errors
      UNEXPECTED_RETURN_TYPE: code2,
      // 24
      // legacy module errors
      INVALID_ARGUMENT: inc2(),
      // 25
      // i18n module errors
      MUST_BE_CALL_SETUP_TOP: inc2(),
      // 26
      NOT_INSTALLED: inc2(),
      // 27
      NOT_AVAILABLE_IN_LEGACY_MODE: inc2(),
      // 28
      // directive module errors
      REQUIRED_VALUE: inc2(),
      // 29
      INVALID_VALUE: inc2(),
      // 30
      // vue-devtools errors
      CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN: inc2(),
      // 31
      NOT_INSTALLED_WITH_PROVIDE: inc2(),
      // 32
      // unexpected error
      UNEXPECTED_ERROR: inc2(),
      // 33
      // not compatible legacy vue-i18n constructor
      NOT_COMPATIBLE_LEGACY_VUE_I18N: inc2(),
      // 34
      // bridge support vue 2.x only
      BRIDGE_SUPPORT_VUE_2_ONLY: inc2(),
      // 35
      // need to define `i18n` option in `allowComposition: true` and `useScope: 'local' at `useI18n``
      MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION: inc2(),
      // 36
      // Not available Compostion API in Legacy API mode. Please make sure that the legacy API mode is working properly
      NOT_AVAILABLE_COMPOSITION_IN_LEGACY: inc2(),
      // 37
      // for enhancement
      __EXTEND_POINT__: inc2()
      // 38
    };
    function createI18nError(code3, ...args) {
      return coreBase.createCompileError(code3, null, { messages: errorMessages3, args });
    }
    var errorMessages3 = {
      [I18nErrorCodes.UNEXPECTED_RETURN_TYPE]: "Unexpected return type in composer",
      [I18nErrorCodes.INVALID_ARGUMENT]: "Invalid argument",
      [I18nErrorCodes.MUST_BE_CALL_SETUP_TOP]: "Must be called at the top of a `setup` function",
      [I18nErrorCodes.NOT_INSTALLED]: "Need to install with `app.use` function",
      [I18nErrorCodes.UNEXPECTED_ERROR]: "Unexpected error",
      [I18nErrorCodes.NOT_AVAILABLE_IN_LEGACY_MODE]: "Not available in legacy mode",
      [I18nErrorCodes.REQUIRED_VALUE]: `Required in value: {0}`,
      [I18nErrorCodes.INVALID_VALUE]: `Invalid value`,
      [I18nErrorCodes.CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN]: `Cannot setup vue-devtools plugin`,
      [I18nErrorCodes.NOT_INSTALLED_WITH_PROVIDE]: "Need to install with `provide` function",
      [I18nErrorCodes.NOT_COMPATIBLE_LEGACY_VUE_I18N]: "Not compatible legacy VueI18n.",
      [I18nErrorCodes.BRIDGE_SUPPORT_VUE_2_ONLY]: "vue-i18n-bridge support Vue 2.x only",
      [I18nErrorCodes.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION]: "Must define ‘i18n’ option or custom block in Composition API with using local scope in Legacy API mode",
      [I18nErrorCodes.NOT_AVAILABLE_COMPOSITION_IN_LEGACY]: "Not available Compostion API in Legacy API mode. Please make sure that the legacy API mode is working properly"
    };
    var TranslateVNodeSymbol = shared.makeSymbol("__translateVNode");
    var DatetimePartsSymbol = shared.makeSymbol("__datetimeParts");
    var NumberPartsSymbol = shared.makeSymbol("__numberParts");
    var EnableEmitter = shared.makeSymbol("__enableEmitter");
    var DisableEmitter = shared.makeSymbol("__disableEmitter");
    var SetPluralRulesSymbol = shared.makeSymbol("__setPluralRules");
    shared.makeSymbol("__intlifyMeta");
    var InejctWithOptionSymbol = shared.makeSymbol("__injectWithOption");
    var DisposeSymbol = shared.makeSymbol("__dispose");
    var __VUE_I18N_BRIDGE__ = "__VUE_I18N_BRIDGE__";
    function handleFlatJson(obj) {
      if (!shared.isObject(obj)) {
        return obj;
      }
      for (const key in obj) {
        if (!shared.hasOwn(obj, key)) {
          continue;
        }
        if (!key.includes(".")) {
          if (shared.isObject(obj[key])) {
            handleFlatJson(obj[key]);
          }
        } else {
          const subKeys = key.split(".");
          const lastIndex = subKeys.length - 1;
          let currentObj = obj;
          let hasStringValue = false;
          for (let i = 0; i < lastIndex; i++) {
            if (!(subKeys[i] in currentObj)) {
              currentObj[subKeys[i]] = {};
            }
            if (!shared.isObject(currentObj[subKeys[i]])) {
              shared.warn(getWarnMessage2(I18nWarnCodes.IGNORE_OBJ_FLATTEN, {
                key: subKeys[i]
              }));
              hasStringValue = true;
              break;
            }
            currentObj = currentObj[subKeys[i]];
          }
          if (!hasStringValue) {
            currentObj[subKeys[lastIndex]] = obj[key];
            delete obj[key];
          }
          if (shared.isObject(currentObj[subKeys[lastIndex]])) {
            handleFlatJson(currentObj[subKeys[lastIndex]]);
          }
        }
      }
      return obj;
    }
    function getLocaleMessages(locale, options) {
      const { messages, __i18n, messageResolver, flatJson } = options;
      const ret = shared.isPlainObject(messages) ? messages : shared.isArray(__i18n) ? {} : { [locale]: {} };
      if (shared.isArray(__i18n)) {
        __i18n.forEach((custom) => {
          if ("locale" in custom && "resource" in custom) {
            const { locale: locale2, resource } = custom;
            if (locale2) {
              ret[locale2] = ret[locale2] || {};
              shared.deepCopy(resource, ret[locale2]);
            } else {
              shared.deepCopy(resource, ret);
            }
          } else {
            shared.isString(custom) && shared.deepCopy(JSON.parse(custom), ret);
          }
        });
      }
      if (messageResolver == null && flatJson) {
        for (const key in ret) {
          if (shared.hasOwn(ret, key)) {
            handleFlatJson(ret[key]);
          }
        }
      }
      return ret;
    }
    function getComponentOptions(instance) {
      return instance.type;
    }
    function adjustI18nResources(gl, options, componentOptions) {
      let messages = shared.isObject(options.messages) ? options.messages : {};
      if ("__i18nGlobal" in componentOptions) {
        messages = getLocaleMessages(gl.locale.value, {
          messages,
          __i18n: componentOptions.__i18nGlobal
        });
      }
      const locales = Object.keys(messages);
      if (locales.length) {
        locales.forEach((locale) => {
          gl.mergeLocaleMessage(locale, messages[locale]);
        });
      }
      {
        if (shared.isObject(options.datetimeFormats)) {
          const locales2 = Object.keys(options.datetimeFormats);
          if (locales2.length) {
            locales2.forEach((locale) => {
              gl.mergeDateTimeFormat(locale, options.datetimeFormats[locale]);
            });
          }
        }
        if (shared.isObject(options.numberFormats)) {
          const locales2 = Object.keys(options.numberFormats);
          if (locales2.length) {
            locales2.forEach((locale) => {
              gl.mergeNumberFormat(locale, options.numberFormats[locale]);
            });
          }
        }
      }
    }
    function createTextNode(key) {
      return vue.createVNode(vue.Text, null, key, 0);
    }
    var DEVTOOLS_META = "__INTLIFY_META__";
    var NOOP_RETURN_ARRAY = () => [];
    var NOOP_RETURN_FALSE = () => false;
    var composerID = 0;
    function defineCoreMissingHandler(missing) {
      return (ctx, locale, key, type) => {
        return missing(locale, key, vue.getCurrentInstance() || void 0, type);
      };
    }
    var getMetaInfo = () => {
      const instance = vue.getCurrentInstance();
      let meta = null;
      return instance && (meta = getComponentOptions(instance)[DEVTOOLS_META]) ? { [DEVTOOLS_META]: meta } : null;
    };
    function createComposer(options = {}, VueI18nLegacy) {
      const { __root, __injectWithOption } = options;
      const _isGlobal = __root === void 0;
      const flatJson = options.flatJson;
      const _ref = shared.inBrowser ? vue.ref : vue.shallowRef;
      const translateExistCompatible = !!options.translateExistCompatible;
      {
        if (translateExistCompatible && true) {
          shared.warnOnce(getWarnMessage2(I18nWarnCodes.NOTICE_DROP_TRANSLATE_EXIST_COMPATIBLE_FLAG));
        }
      }
      let _inheritLocale = shared.isBoolean(options.inheritLocale) ? options.inheritLocale : true;
      const _locale = _ref(
        // prettier-ignore
        __root && _inheritLocale ? __root.locale.value : shared.isString(options.locale) ? options.locale : coreBase.DEFAULT_LOCALE
      );
      const _fallbackLocale = _ref(
        // prettier-ignore
        __root && _inheritLocale ? __root.fallbackLocale.value : shared.isString(options.fallbackLocale) || shared.isArray(options.fallbackLocale) || shared.isPlainObject(options.fallbackLocale) || options.fallbackLocale === false ? options.fallbackLocale : _locale.value
      );
      const _messages = _ref(getLocaleMessages(_locale.value, options));
      const _datetimeFormats = _ref(shared.isPlainObject(options.datetimeFormats) ? options.datetimeFormats : { [_locale.value]: {} });
      const _numberFormats = _ref(shared.isPlainObject(options.numberFormats) ? options.numberFormats : { [_locale.value]: {} });
      let _missingWarn = __root ? __root.missingWarn : shared.isBoolean(options.missingWarn) || shared.isRegExp(options.missingWarn) ? options.missingWarn : true;
      let _fallbackWarn = __root ? __root.fallbackWarn : shared.isBoolean(options.fallbackWarn) || shared.isRegExp(options.fallbackWarn) ? options.fallbackWarn : true;
      let _fallbackRoot = __root ? __root.fallbackRoot : shared.isBoolean(options.fallbackRoot) ? options.fallbackRoot : true;
      let _fallbackFormat = !!options.fallbackFormat;
      let _missing = shared.isFunction(options.missing) ? options.missing : null;
      let _runtimeMissing = shared.isFunction(options.missing) ? defineCoreMissingHandler(options.missing) : null;
      let _postTranslation = shared.isFunction(options.postTranslation) ? options.postTranslation : null;
      let _warnHtmlMessage = __root ? __root.warnHtmlMessage : shared.isBoolean(options.warnHtmlMessage) ? options.warnHtmlMessage : true;
      let _escapeParameter = !!options.escapeParameter;
      const _modifiers = __root ? __root.modifiers : shared.isPlainObject(options.modifiers) ? options.modifiers : {};
      let _pluralRules = options.pluralRules || __root && __root.pluralRules;
      let _context;
      const getCoreContext = () => {
        _isGlobal && coreBase.setFallbackContext(null);
        const ctxOptions = {
          version: VERSION2,
          locale: _locale.value,
          fallbackLocale: _fallbackLocale.value,
          messages: _messages.value,
          modifiers: _modifiers,
          pluralRules: _pluralRules,
          missing: _runtimeMissing === null ? void 0 : _runtimeMissing,
          missingWarn: _missingWarn,
          fallbackWarn: _fallbackWarn,
          fallbackFormat: _fallbackFormat,
          unresolving: true,
          postTranslation: _postTranslation === null ? void 0 : _postTranslation,
          warnHtmlMessage: _warnHtmlMessage,
          escapeParameter: _escapeParameter,
          messageResolver: options.messageResolver,
          messageCompiler: options.messageCompiler,
          __meta: { framework: "vue" }
        };
        {
          ctxOptions.datetimeFormats = _datetimeFormats.value;
          ctxOptions.numberFormats = _numberFormats.value;
          ctxOptions.__datetimeFormatters = shared.isPlainObject(_context) ? _context.__datetimeFormatters : void 0;
          ctxOptions.__numberFormatters = shared.isPlainObject(_context) ? _context.__numberFormatters : void 0;
        }
        {
          ctxOptions.__v_emitter = shared.isPlainObject(_context) ? _context.__v_emitter : void 0;
        }
        const ctx = coreBase.createCoreContext(ctxOptions);
        _isGlobal && coreBase.setFallbackContext(ctx);
        return ctx;
      };
      _context = getCoreContext();
      coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);
      function trackReactivityValues() {
        return [
          _locale.value,
          _fallbackLocale.value,
          _messages.value,
          _datetimeFormats.value,
          _numberFormats.value
        ];
      }
      const locale = vue.computed({
        get: () => _locale.value,
        set: (val) => {
          _locale.value = val;
          _context.locale = _locale.value;
        }
      });
      const fallbackLocale = vue.computed({
        get: () => _fallbackLocale.value,
        set: (val) => {
          _fallbackLocale.value = val;
          _context.fallbackLocale = _fallbackLocale.value;
          coreBase.updateFallbackLocale(_context, _locale.value, val);
        }
      });
      const messages = vue.computed(() => _messages.value);
      const datetimeFormats = vue.computed(() => _datetimeFormats.value);
      const numberFormats = vue.computed(() => _numberFormats.value);
      function getPostTranslationHandler() {
        return shared.isFunction(_postTranslation) ? _postTranslation : null;
      }
      function setPostTranslationHandler(handler) {
        _postTranslation = handler;
        _context.postTranslation = handler;
      }
      function getMissingHandler() {
        return _missing;
      }
      function setMissingHandler(handler) {
        if (handler !== null) {
          _runtimeMissing = defineCoreMissingHandler(handler);
        }
        _missing = handler;
        _context.missing = _runtimeMissing;
      }
      function isResolvedTranslateMessage(type, arg) {
        return type !== "translate" || !arg.resolvedMessage;
      }
      const wrapWithDeps = (fn, argumentParser, warnType, fallbackSuccess, fallbackFail, successCondition) => {
        trackReactivityValues();
        let ret;
        try {
          if (true) {
            coreBase.setAdditionalMeta(getMetaInfo());
          }
          if (!_isGlobal) {
            _context.fallbackContext = __root ? coreBase.getFallbackContext() : void 0;
          }
          ret = fn(_context);
        } finally {
          {
            coreBase.setAdditionalMeta(null);
          }
          if (!_isGlobal) {
            _context.fallbackContext = void 0;
          }
        }
        if (warnType !== "translate exists" && // for not `te` (e.g `t`)
        shared.isNumber(ret) && ret === coreBase.NOT_REOSLVED || warnType === "translate exists" && !ret) {
          const [key, arg2] = argumentParser();
          if (__root && shared.isString(key) && isResolvedTranslateMessage(warnType, arg2)) {
            if (_fallbackRoot && (coreBase.isTranslateFallbackWarn(_fallbackWarn, key) || coreBase.isTranslateMissingWarn(_missingWarn, key))) {
              shared.warn(getWarnMessage2(I18nWarnCodes.FALLBACK_TO_ROOT, {
                key,
                type: warnType
              }));
            }
            {
              const { __v_emitter: emitter } = _context;
              if (emitter && _fallbackRoot) {
                emitter.emit("fallback", {
                  type: warnType,
                  key,
                  to: "global",
                  groupId: `${warnType}:${key}`
                });
              }
            }
          }
          return __root && _fallbackRoot ? fallbackSuccess(__root) : fallbackFail(key);
        } else if (successCondition(ret)) {
          return ret;
        } else {
          throw createI18nError(I18nErrorCodes.UNEXPECTED_RETURN_TYPE);
        }
      };
      function t(...args) {
        return wrapWithDeps((context) => Reflect.apply(coreBase.translate, null, [context, ...args]), () => coreBase.parseTranslateArgs(...args), "translate", (root) => Reflect.apply(root.t, root, [...args]), (key) => key, (val) => shared.isString(val));
      }
      function rt(...args) {
        const [arg1, arg2, arg3] = args;
        if (arg3 && !shared.isObject(arg3)) {
          throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);
        }
        return t(...[arg1, arg2, shared.assign({ resolvedMessage: true }, arg3 || {})]);
      }
      function d(...args) {
        return wrapWithDeps((context) => Reflect.apply(coreBase.datetime, null, [context, ...args]), () => coreBase.parseDateTimeArgs(...args), "datetime format", (root) => Reflect.apply(root.d, root, [...args]), () => coreBase.MISSING_RESOLVE_VALUE, (val) => shared.isString(val));
      }
      function n(...args) {
        return wrapWithDeps((context) => Reflect.apply(coreBase.number, null, [context, ...args]), () => coreBase.parseNumberArgs(...args), "number format", (root) => Reflect.apply(root.n, root, [...args]), () => coreBase.MISSING_RESOLVE_VALUE, (val) => shared.isString(val));
      }
      function normalize(values) {
        return values.map((val) => shared.isString(val) || shared.isNumber(val) || shared.isBoolean(val) ? createTextNode(String(val)) : val);
      }
      const interpolate = (val) => val;
      const processor = {
        normalize,
        interpolate,
        type: "vnode"
      };
      function translateVNode(...args) {
        return wrapWithDeps(
          (context) => {
            let ret;
            const _context2 = context;
            try {
              _context2.processor = processor;
              ret = Reflect.apply(coreBase.translate, null, [_context2, ...args]);
            } finally {
              _context2.processor = null;
            }
            return ret;
          },
          () => coreBase.parseTranslateArgs(...args),
          "translate",
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (root) => root[TranslateVNodeSymbol](...args),
          (key) => [createTextNode(key)],
          (val) => shared.isArray(val)
        );
      }
      function numberParts(...args) {
        return wrapWithDeps(
          (context) => Reflect.apply(coreBase.number, null, [context, ...args]),
          () => coreBase.parseNumberArgs(...args),
          "number format",
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (root) => root[NumberPartsSymbol](...args),
          NOOP_RETURN_ARRAY,
          (val) => shared.isString(val) || shared.isArray(val)
        );
      }
      function datetimeParts(...args) {
        return wrapWithDeps(
          (context) => Reflect.apply(coreBase.datetime, null, [context, ...args]),
          () => coreBase.parseDateTimeArgs(...args),
          "datetime format",
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (root) => root[DatetimePartsSymbol](...args),
          NOOP_RETURN_ARRAY,
          (val) => shared.isString(val) || shared.isArray(val)
        );
      }
      function setPluralRules(rules) {
        _pluralRules = rules;
        _context.pluralRules = _pluralRules;
      }
      function te(key, locale2) {
        return wrapWithDeps(() => {
          if (!key) {
            return false;
          }
          const targetLocale = shared.isString(locale2) ? locale2 : _locale.value;
          const message = getLocaleMessage(targetLocale);
          const resolved = _context.messageResolver(message, key);
          return !translateExistCompatible ? coreBase.isMessageAST(resolved) || coreBase.isMessageFunction(resolved) || shared.isString(resolved) : resolved != null;
        }, () => [key], "translate exists", (root) => {
          return Reflect.apply(root.te, root, [key, locale2]);
        }, NOOP_RETURN_FALSE, (val) => shared.isBoolean(val));
      }
      function resolveMessages(key) {
        let messages2 = null;
        const locales = coreBase.fallbackWithLocaleChain(_context, _fallbackLocale.value, _locale.value);
        for (let i = 0; i < locales.length; i++) {
          const targetLocaleMessages = _messages.value[locales[i]] || {};
          const messageValue = _context.messageResolver(targetLocaleMessages, key);
          if (messageValue != null) {
            messages2 = messageValue;
            break;
          }
        }
        return messages2;
      }
      function tm(key) {
        const messages2 = resolveMessages(key);
        return messages2 != null ? messages2 : __root ? __root.tm(key) || {} : {};
      }
      function getLocaleMessage(locale2) {
        return _messages.value[locale2] || {};
      }
      function setLocaleMessage(locale2, message) {
        if (flatJson) {
          const _message = { [locale2]: message };
          for (const key in _message) {
            if (shared.hasOwn(_message, key)) {
              handleFlatJson(_message[key]);
            }
          }
          message = _message[locale2];
        }
        _messages.value[locale2] = message;
        _context.messages = _messages.value;
      }
      function mergeLocaleMessage(locale2, message) {
        _messages.value[locale2] = _messages.value[locale2] || {};
        const _message = { [locale2]: message };
        if (flatJson) {
          for (const key in _message) {
            if (shared.hasOwn(_message, key)) {
              handleFlatJson(_message[key]);
            }
          }
        }
        message = _message[locale2];
        shared.deepCopy(message, _messages.value[locale2]);
        _context.messages = _messages.value;
      }
      function getDateTimeFormat(locale2) {
        return _datetimeFormats.value[locale2] || {};
      }
      function setDateTimeFormat(locale2, format3) {
        _datetimeFormats.value[locale2] = format3;
        _context.datetimeFormats = _datetimeFormats.value;
        coreBase.clearDateTimeFormat(_context, locale2, format3);
      }
      function mergeDateTimeFormat(locale2, format3) {
        _datetimeFormats.value[locale2] = shared.assign(_datetimeFormats.value[locale2] || {}, format3);
        _context.datetimeFormats = _datetimeFormats.value;
        coreBase.clearDateTimeFormat(_context, locale2, format3);
      }
      function getNumberFormat(locale2) {
        return _numberFormats.value[locale2] || {};
      }
      function setNumberFormat(locale2, format3) {
        _numberFormats.value[locale2] = format3;
        _context.numberFormats = _numberFormats.value;
        coreBase.clearNumberFormat(_context, locale2, format3);
      }
      function mergeNumberFormat(locale2, format3) {
        _numberFormats.value[locale2] = shared.assign(_numberFormats.value[locale2] || {}, format3);
        _context.numberFormats = _numberFormats.value;
        coreBase.clearNumberFormat(_context, locale2, format3);
      }
      composerID++;
      if (__root && shared.inBrowser) {
        vue.watch(__root.locale, (val) => {
          if (_inheritLocale) {
            _locale.value = val;
            _context.locale = val;
            coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);
          }
        });
        vue.watch(__root.fallbackLocale, (val) => {
          if (_inheritLocale) {
            _fallbackLocale.value = val;
            _context.fallbackLocale = val;
            coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);
          }
        });
      }
      const composer = {
        id: composerID,
        locale,
        fallbackLocale,
        get inheritLocale() {
          return _inheritLocale;
        },
        set inheritLocale(val) {
          _inheritLocale = val;
          if (val && __root) {
            _locale.value = __root.locale.value;
            _fallbackLocale.value = __root.fallbackLocale.value;
            coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);
          }
        },
        get availableLocales() {
          return Object.keys(_messages.value).sort();
        },
        messages,
        get modifiers() {
          return _modifiers;
        },
        get pluralRules() {
          return _pluralRules || {};
        },
        get isGlobal() {
          return _isGlobal;
        },
        get missingWarn() {
          return _missingWarn;
        },
        set missingWarn(val) {
          _missingWarn = val;
          _context.missingWarn = _missingWarn;
        },
        get fallbackWarn() {
          return _fallbackWarn;
        },
        set fallbackWarn(val) {
          _fallbackWarn = val;
          _context.fallbackWarn = _fallbackWarn;
        },
        get fallbackRoot() {
          return _fallbackRoot;
        },
        set fallbackRoot(val) {
          _fallbackRoot = val;
        },
        get fallbackFormat() {
          return _fallbackFormat;
        },
        set fallbackFormat(val) {
          _fallbackFormat = val;
          _context.fallbackFormat = _fallbackFormat;
        },
        get warnHtmlMessage() {
          return _warnHtmlMessage;
        },
        set warnHtmlMessage(val) {
          _warnHtmlMessage = val;
          _context.warnHtmlMessage = val;
        },
        get escapeParameter() {
          return _escapeParameter;
        },
        set escapeParameter(val) {
          _escapeParameter = val;
          _context.escapeParameter = val;
        },
        t,
        getLocaleMessage,
        setLocaleMessage,
        mergeLocaleMessage,
        getPostTranslationHandler,
        setPostTranslationHandler,
        getMissingHandler,
        setMissingHandler,
        [SetPluralRulesSymbol]: setPluralRules
      };
      {
        composer.datetimeFormats = datetimeFormats;
        composer.numberFormats = numberFormats;
        composer.rt = rt;
        composer.te = te;
        composer.tm = tm;
        composer.d = d;
        composer.n = n;
        composer.getDateTimeFormat = getDateTimeFormat;
        composer.setDateTimeFormat = setDateTimeFormat;
        composer.mergeDateTimeFormat = mergeDateTimeFormat;
        composer.getNumberFormat = getNumberFormat;
        composer.setNumberFormat = setNumberFormat;
        composer.mergeNumberFormat = mergeNumberFormat;
        composer[InejctWithOptionSymbol] = __injectWithOption;
        composer[TranslateVNodeSymbol] = translateVNode;
        composer[DatetimePartsSymbol] = datetimeParts;
        composer[NumberPartsSymbol] = numberParts;
      }
      {
        composer[EnableEmitter] = (emitter) => {
          _context.__v_emitter = emitter;
        };
        composer[DisableEmitter] = () => {
          _context.__v_emitter = void 0;
        };
      }
      return composer;
    }
    function convertComposerOptions(options) {
      const locale = shared.isString(options.locale) ? options.locale : coreBase.DEFAULT_LOCALE;
      const fallbackLocale = shared.isString(options.fallbackLocale) || shared.isArray(options.fallbackLocale) || shared.isPlainObject(options.fallbackLocale) || options.fallbackLocale === false ? options.fallbackLocale : locale;
      const missing = shared.isFunction(options.missing) ? options.missing : void 0;
      const missingWarn = shared.isBoolean(options.silentTranslationWarn) || shared.isRegExp(options.silentTranslationWarn) ? !options.silentTranslationWarn : true;
      const fallbackWarn = shared.isBoolean(options.silentFallbackWarn) || shared.isRegExp(options.silentFallbackWarn) ? !options.silentFallbackWarn : true;
      const fallbackRoot = shared.isBoolean(options.fallbackRoot) ? options.fallbackRoot : true;
      const fallbackFormat = !!options.formatFallbackMessages;
      const modifiers = shared.isPlainObject(options.modifiers) ? options.modifiers : {};
      const pluralizationRules = options.pluralizationRules;
      const postTranslation = shared.isFunction(options.postTranslation) ? options.postTranslation : void 0;
      const warnHtmlMessage = shared.isString(options.warnHtmlInMessage) ? options.warnHtmlInMessage !== "off" : true;
      const escapeParameter = !!options.escapeParameterHtml;
      const inheritLocale = shared.isBoolean(options.sync) ? options.sync : true;
      if (options.formatter) {
        shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));
      }
      if (options.preserveDirectiveContent) {
        shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));
      }
      let messages = options.messages;
      if (shared.isPlainObject(options.sharedMessages)) {
        const sharedMessages = options.sharedMessages;
        const locales = Object.keys(sharedMessages);
        messages = locales.reduce((messages2, locale2) => {
          const message = messages2[locale2] || (messages2[locale2] = {});
          shared.assign(message, sharedMessages[locale2]);
          return messages2;
        }, messages || {});
      }
      const { __i18n, __root, __injectWithOption } = options;
      const datetimeFormats = options.datetimeFormats;
      const numberFormats = options.numberFormats;
      const flatJson = options.flatJson;
      const translateExistCompatible = options.translateExistCompatible;
      return {
        locale,
        fallbackLocale,
        messages,
        flatJson,
        datetimeFormats,
        numberFormats,
        missing,
        missingWarn,
        fallbackWarn,
        fallbackRoot,
        fallbackFormat,
        modifiers,
        pluralRules: pluralizationRules,
        postTranslation,
        warnHtmlMessage,
        escapeParameter,
        messageResolver: options.messageResolver,
        inheritLocale,
        translateExistCompatible,
        __i18n,
        __root,
        __injectWithOption
      };
    }
    function createVueI18n(options = {}, VueI18nLegacy) {
      {
        const composer = createComposer(convertComposerOptions(options));
        const { __extender } = options;
        const vueI18n = {
          // id
          id: composer.id,
          // locale
          get locale() {
            return composer.locale.value;
          },
          set locale(val) {
            composer.locale.value = val;
          },
          // fallbackLocale
          get fallbackLocale() {
            return composer.fallbackLocale.value;
          },
          set fallbackLocale(val) {
            composer.fallbackLocale.value = val;
          },
          // messages
          get messages() {
            return composer.messages.value;
          },
          // datetimeFormats
          get datetimeFormats() {
            return composer.datetimeFormats.value;
          },
          // numberFormats
          get numberFormats() {
            return composer.numberFormats.value;
          },
          // availableLocales
          get availableLocales() {
            return composer.availableLocales;
          },
          // formatter
          get formatter() {
            shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));
            return {
              interpolate() {
                return [];
              }
            };
          },
          set formatter(val) {
            shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));
          },
          // missing
          get missing() {
            return composer.getMissingHandler();
          },
          set missing(handler) {
            composer.setMissingHandler(handler);
          },
          // silentTranslationWarn
          get silentTranslationWarn() {
            return shared.isBoolean(composer.missingWarn) ? !composer.missingWarn : composer.missingWarn;
          },
          set silentTranslationWarn(val) {
            composer.missingWarn = shared.isBoolean(val) ? !val : val;
          },
          // silentFallbackWarn
          get silentFallbackWarn() {
            return shared.isBoolean(composer.fallbackWarn) ? !composer.fallbackWarn : composer.fallbackWarn;
          },
          set silentFallbackWarn(val) {
            composer.fallbackWarn = shared.isBoolean(val) ? !val : val;
          },
          // modifiers
          get modifiers() {
            return composer.modifiers;
          },
          // formatFallbackMessages
          get formatFallbackMessages() {
            return composer.fallbackFormat;
          },
          set formatFallbackMessages(val) {
            composer.fallbackFormat = val;
          },
          // postTranslation
          get postTranslation() {
            return composer.getPostTranslationHandler();
          },
          set postTranslation(handler) {
            composer.setPostTranslationHandler(handler);
          },
          // sync
          get sync() {
            return composer.inheritLocale;
          },
          set sync(val) {
            composer.inheritLocale = val;
          },
          // warnInHtmlMessage
          get warnHtmlInMessage() {
            return composer.warnHtmlMessage ? "warn" : "off";
          },
          set warnHtmlInMessage(val) {
            composer.warnHtmlMessage = val !== "off";
          },
          // escapeParameterHtml
          get escapeParameterHtml() {
            return composer.escapeParameter;
          },
          set escapeParameterHtml(val) {
            composer.escapeParameter = val;
          },
          // preserveDirectiveContent
          get preserveDirectiveContent() {
            shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));
            return true;
          },
          set preserveDirectiveContent(val) {
            shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));
          },
          // pluralizationRules
          get pluralizationRules() {
            return composer.pluralRules || {};
          },
          // for internal
          __composer: composer,
          // t
          t(...args) {
            const [arg1, arg2, arg3] = args;
            const options2 = {};
            let list = null;
            let named = null;
            if (!shared.isString(arg1)) {
              throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);
            }
            const key = arg1;
            if (shared.isString(arg2)) {
              options2.locale = arg2;
            } else if (shared.isArray(arg2)) {
              list = arg2;
            } else if (shared.isPlainObject(arg2)) {
              named = arg2;
            }
            if (shared.isArray(arg3)) {
              list = arg3;
            } else if (shared.isPlainObject(arg3)) {
              named = arg3;
            }
            return Reflect.apply(composer.t, composer, [
              key,
              list || named || {},
              options2
            ]);
          },
          rt(...args) {
            return Reflect.apply(composer.rt, composer, [...args]);
          },
          // tc
          tc(...args) {
            const [arg1, arg2, arg3] = args;
            const options2 = { plural: 1 };
            let list = null;
            let named = null;
            if (!shared.isString(arg1)) {
              throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);
            }
            const key = arg1;
            if (shared.isString(arg2)) {
              options2.locale = arg2;
            } else if (shared.isNumber(arg2)) {
              options2.plural = arg2;
            } else if (shared.isArray(arg2)) {
              list = arg2;
            } else if (shared.isPlainObject(arg2)) {
              named = arg2;
            }
            if (shared.isString(arg3)) {
              options2.locale = arg3;
            } else if (shared.isArray(arg3)) {
              list = arg3;
            } else if (shared.isPlainObject(arg3)) {
              named = arg3;
            }
            return Reflect.apply(composer.t, composer, [
              key,
              list || named || {},
              options2
            ]);
          },
          // te
          te(key, locale) {
            return composer.te(key, locale);
          },
          // tm
          tm(key) {
            return composer.tm(key);
          },
          // getLocaleMessage
          getLocaleMessage(locale) {
            return composer.getLocaleMessage(locale);
          },
          // setLocaleMessage
          setLocaleMessage(locale, message) {
            composer.setLocaleMessage(locale, message);
          },
          // mergeLocaleMessage
          mergeLocaleMessage(locale, message) {
            composer.mergeLocaleMessage(locale, message);
          },
          // d
          d(...args) {
            return Reflect.apply(composer.d, composer, [...args]);
          },
          // getDateTimeFormat
          getDateTimeFormat(locale) {
            return composer.getDateTimeFormat(locale);
          },
          // setDateTimeFormat
          setDateTimeFormat(locale, format3) {
            composer.setDateTimeFormat(locale, format3);
          },
          // mergeDateTimeFormat
          mergeDateTimeFormat(locale, format3) {
            composer.mergeDateTimeFormat(locale, format3);
          },
          // n
          n(...args) {
            return Reflect.apply(composer.n, composer, [...args]);
          },
          // getNumberFormat
          getNumberFormat(locale) {
            return composer.getNumberFormat(locale);
          },
          // setNumberFormat
          setNumberFormat(locale, format3) {
            composer.setNumberFormat(locale, format3);
          },
          // mergeNumberFormat
          mergeNumberFormat(locale, format3) {
            composer.mergeNumberFormat(locale, format3);
          },
          // getChoiceIndex
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          getChoiceIndex(choice, choicesLength) {
            shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_GET_CHOICE_INDEX));
            return -1;
          }
        };
        vueI18n.__extender = __extender;
        {
          vueI18n.__enableEmitter = (emitter) => {
            const __composer = composer;
            __composer[EnableEmitter] && __composer[EnableEmitter](emitter);
          };
          vueI18n.__disableEmitter = () => {
            const __composer = composer;
            __composer[DisableEmitter] && __composer[DisableEmitter]();
          };
        }
        return vueI18n;
      }
    }
    var baseFormatProps = {
      tag: {
        type: [String, Object]
      },
      locale: {
        type: String
      },
      scope: {
        type: String,
        // NOTE: avoid https://github.com/microsoft/rushstack/issues/1050
        validator: (val) => val === "parent" || val === "global",
        default: "parent"
        /* ComponentI18nScope */
      },
      i18n: {
        type: Object
      }
    };
    function getInterpolateArg({ slots }, keys) {
      if (keys.length === 1 && keys[0] === "default") {
        const ret = slots.default ? slots.default() : [];
        return ret.reduce((slot, current) => {
          return [
            ...slot,
            // prettier-ignore
            ...current.type === vue.Fragment ? current.children : [current]
          ];
        }, []);
      } else {
        return keys.reduce((arg, key) => {
          const slot = slots[key];
          if (slot) {
            arg[key] = slot();
          }
          return arg;
        }, {});
      }
    }
    function getFragmentableTag(tag) {
      return vue.Fragment;
    }
    var TranslationImpl = vue.defineComponent({
      /* eslint-disable */
      name: "i18n-t",
      props: shared.assign({
        keypath: {
          type: String,
          required: true
        },
        plural: {
          type: [Number, String],
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          validator: (val) => shared.isNumber(val) || !isNaN(val)
        }
      }, baseFormatProps),
      /* eslint-enable */
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      setup(props, context) {
        const { slots, attrs } = context;
        const i18n = props.i18n || useI18n({
          useScope: props.scope,
          __useComponent: true
        });
        return () => {
          const keys = Object.keys(slots).filter((key) => key !== "_");
          const options = {};
          if (props.locale) {
            options.locale = props.locale;
          }
          if (props.plural !== void 0) {
            options.plural = shared.isString(props.plural) ? +props.plural : props.plural;
          }
          const arg = getInterpolateArg(context, keys);
          const children = i18n[TranslateVNodeSymbol](props.keypath, arg, options);
          const assignedAttrs = shared.assign({}, attrs);
          const tag = shared.isString(props.tag) || shared.isObject(props.tag) ? props.tag : getFragmentableTag();
          return vue.h(tag, assignedAttrs, children);
        };
      }
    });
    var Translation = TranslationImpl;
    var I18nT = Translation;
    function isVNode(target) {
      return shared.isArray(target) && !shared.isString(target[0]);
    }
    function renderFormatter(props, context, slotKeys, partFormatter) {
      const { slots, attrs } = context;
      return () => {
        const options = { part: true };
        let overrides = {};
        if (props.locale) {
          options.locale = props.locale;
        }
        if (shared.isString(props.format)) {
          options.key = props.format;
        } else if (shared.isObject(props.format)) {
          if (shared.isString(props.format.key)) {
            options.key = props.format.key;
          }
          overrides = Object.keys(props.format).reduce((options2, prop) => {
            return slotKeys.includes(prop) ? shared.assign({}, options2, { [prop]: props.format[prop] }) : options2;
          }, {});
        }
        const parts = partFormatter(...[props.value, options, overrides]);
        let children = [options.key];
        if (shared.isArray(parts)) {
          children = parts.map((part, index) => {
            const slot = slots[part.type];
            const node = slot ? slot({ [part.type]: part.value, index, parts }) : [part.value];
            if (isVNode(node)) {
              node[0].key = `${part.type}-${index}`;
            }
            return node;
          });
        } else if (shared.isString(parts)) {
          children = [parts];
        }
        const assignedAttrs = shared.assign({}, attrs);
        const tag = shared.isString(props.tag) || shared.isObject(props.tag) ? props.tag : getFragmentableTag();
        return vue.h(tag, assignedAttrs, children);
      };
    }
    var NumberFormatImpl = vue.defineComponent({
      /* eslint-disable */
      name: "i18n-n",
      props: shared.assign({
        value: {
          type: Number,
          required: true
        },
        format: {
          type: [String, Object]
        }
      }, baseFormatProps),
      /* eslint-enable */
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      setup(props, context) {
        const i18n = props.i18n || useI18n({
          useScope: props.scope,
          __useComponent: true
        });
        return renderFormatter(props, context, coreBase.NUMBER_FORMAT_OPTIONS_KEYS, (...args) => (
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          i18n[NumberPartsSymbol](...args)
        ));
      }
    });
    var NumberFormat = NumberFormatImpl;
    var I18nN = NumberFormat;
    var DatetimeFormatImpl = vue.defineComponent({
      /* eslint-disable */
      name: "i18n-d",
      props: shared.assign({
        value: {
          type: [Number, Date],
          required: true
        },
        format: {
          type: [String, Object]
        }
      }, baseFormatProps),
      /* eslint-enable */
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      setup(props, context) {
        const i18n = props.i18n || useI18n({
          useScope: props.scope,
          __useComponent: true
        });
        return renderFormatter(props, context, coreBase.DATETIME_FORMAT_OPTIONS_KEYS, (...args) => (
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          i18n[DatetimePartsSymbol](...args)
        ));
      }
    });
    var DatetimeFormat = DatetimeFormatImpl;
    var I18nD = DatetimeFormat;
    function getComposer$1(i18n, instance) {
      const i18nInternal = i18n;
      if (i18n.mode === "composition") {
        return i18nInternal.__getInstance(instance) || i18n.global;
      } else {
        const vueI18n = i18nInternal.__getInstance(instance);
        return vueI18n != null ? vueI18n.__composer : i18n.global.__composer;
      }
    }
    function vTDirective(i18n) {
      const _process = (binding) => {
        const { instance, modifiers, value } = binding;
        if (!instance || !instance.$) {
          throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);
        }
        const composer = getComposer$1(i18n, instance.$);
        if (modifiers.preserve) {
          shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_PRESERVE));
        }
        const parsedValue = parseValue(value);
        return [
          Reflect.apply(composer.t, composer, [...makeParams(parsedValue)]),
          composer
        ];
      };
      const register = (el, binding) => {
        const [textContent, composer] = _process(binding);
        if (shared.inBrowser && i18n.global === composer) {
          el.__i18nWatcher = vue.watch(composer.locale, () => {
            binding.instance && binding.instance.$forceUpdate();
          });
        }
        el.__composer = composer;
        el.textContent = textContent;
      };
      const unregister = (el) => {
        if (shared.inBrowser && el.__i18nWatcher) {
          el.__i18nWatcher();
          el.__i18nWatcher = void 0;
          delete el.__i18nWatcher;
        }
        if (el.__composer) {
          el.__composer = void 0;
          delete el.__composer;
        }
      };
      const update = (el, { value }) => {
        if (el.__composer) {
          const composer = el.__composer;
          const parsedValue = parseValue(value);
          el.textContent = Reflect.apply(composer.t, composer, [
            ...makeParams(parsedValue)
          ]);
        }
      };
      const getSSRProps = (binding) => {
        const [textContent] = _process(binding);
        return { textContent };
      };
      return {
        created: register,
        unmounted: unregister,
        beforeUpdate: update,
        getSSRProps
      };
    }
    function parseValue(value) {
      if (shared.isString(value)) {
        return { path: value };
      } else if (shared.isPlainObject(value)) {
        if (!("path" in value)) {
          throw createI18nError(I18nErrorCodes.REQUIRED_VALUE, "path");
        }
        return value;
      } else {
        throw createI18nError(I18nErrorCodes.INVALID_VALUE);
      }
    }
    function makeParams(value) {
      const { path, locale, args, choice, plural } = value;
      const options = {};
      const named = args || {};
      if (shared.isString(locale)) {
        options.locale = locale;
      }
      if (shared.isNumber(choice)) {
        options.plural = choice;
      }
      if (shared.isNumber(plural)) {
        options.plural = plural;
      }
      return [path, named, options];
    }
    function apply(app, i18n, ...options) {
      const pluginOptions = shared.isPlainObject(options[0]) ? options[0] : {};
      const useI18nComponentName = !!pluginOptions.useI18nComponentName;
      const globalInstall = shared.isBoolean(pluginOptions.globalInstall) ? pluginOptions.globalInstall : true;
      if (globalInstall && useI18nComponentName) {
        shared.warn(getWarnMessage2(I18nWarnCodes.COMPONENT_NAME_LEGACY_COMPATIBLE, {
          name: Translation.name
        }));
      }
      if (globalInstall) {
        [!useI18nComponentName ? Translation.name : "i18n", "I18nT"].forEach((name) => app.component(name, Translation));
        [NumberFormat.name, "I18nN"].forEach((name) => app.component(name, NumberFormat));
        [DatetimeFormat.name, "I18nD"].forEach((name) => app.component(name, DatetimeFormat));
      }
      {
        app.directive("t", vTDirective(i18n));
      }
    }
    function defineMixin(vuei18n, composer, i18n) {
      return {
        beforeCreate() {
          const instance = vue.getCurrentInstance();
          if (!instance) {
            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);
          }
          const options = this.$options;
          if (options.i18n) {
            const optionsI18n = options.i18n;
            if (options.__i18n) {
              optionsI18n.__i18n = options.__i18n;
            }
            optionsI18n.__root = composer;
            if (this === this.$root) {
              this.$i18n = mergeToGlobal(vuei18n, optionsI18n);
            } else {
              optionsI18n.__injectWithOption = true;
              optionsI18n.__extender = i18n.__vueI18nExtend;
              this.$i18n = createVueI18n(optionsI18n);
              const _vueI18n = this.$i18n;
              if (_vueI18n.__extender) {
                _vueI18n.__disposer = _vueI18n.__extender(this.$i18n);
              }
            }
          } else if (options.__i18n) {
            if (this === this.$root) {
              this.$i18n = mergeToGlobal(vuei18n, options);
            } else {
              this.$i18n = createVueI18n({
                __i18n: options.__i18n,
                __injectWithOption: true,
                __extender: i18n.__vueI18nExtend,
                __root: composer
              });
              const _vueI18n = this.$i18n;
              if (_vueI18n.__extender) {
                _vueI18n.__disposer = _vueI18n.__extender(this.$i18n);
              }
            }
          } else {
            this.$i18n = vuei18n;
          }
          if (options.__i18nGlobal) {
            adjustI18nResources(composer, options, options);
          }
          this.$t = (...args) => this.$i18n.t(...args);
          this.$rt = (...args) => this.$i18n.rt(...args);
          this.$tc = (...args) => this.$i18n.tc(...args);
          this.$te = (key, locale) => this.$i18n.te(key, locale);
          this.$d = (...args) => this.$i18n.d(...args);
          this.$n = (...args) => this.$i18n.n(...args);
          this.$tm = (key) => this.$i18n.tm(key);
          i18n.__setInstance(instance, this.$i18n);
        },
        mounted() {
        },
        unmounted() {
          const instance = vue.getCurrentInstance();
          if (!instance) {
            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);
          }
          const _vueI18n = this.$i18n;
          delete this.$t;
          delete this.$rt;
          delete this.$tc;
          delete this.$te;
          delete this.$d;
          delete this.$n;
          delete this.$tm;
          if (_vueI18n.__disposer) {
            _vueI18n.__disposer();
            delete _vueI18n.__disposer;
            delete _vueI18n.__extender;
          }
          i18n.__deleteInstance(instance);
          delete this.$i18n;
        }
      };
    }
    function mergeToGlobal(g, options) {
      g.locale = options.locale || g.locale;
      g.fallbackLocale = options.fallbackLocale || g.fallbackLocale;
      g.missing = options.missing || g.missing;
      g.silentTranslationWarn = options.silentTranslationWarn || g.silentFallbackWarn;
      g.silentFallbackWarn = options.silentFallbackWarn || g.silentFallbackWarn;
      g.formatFallbackMessages = options.formatFallbackMessages || g.formatFallbackMessages;
      g.postTranslation = options.postTranslation || g.postTranslation;
      g.warnHtmlInMessage = options.warnHtmlInMessage || g.warnHtmlInMessage;
      g.escapeParameterHtml = options.escapeParameterHtml || g.escapeParameterHtml;
      g.sync = options.sync || g.sync;
      g.__composer[SetPluralRulesSymbol](options.pluralizationRules || g.pluralizationRules);
      const messages = getLocaleMessages(g.locale, {
        messages: options.messages,
        __i18n: options.__i18n
      });
      Object.keys(messages).forEach((locale) => g.mergeLocaleMessage(locale, messages[locale]));
      if (options.datetimeFormats) {
        Object.keys(options.datetimeFormats).forEach((locale) => g.mergeDateTimeFormat(locale, options.datetimeFormats[locale]));
      }
      if (options.numberFormats) {
        Object.keys(options.numberFormats).forEach((locale) => g.mergeNumberFormat(locale, options.numberFormats[locale]));
      }
      return g;
    }
    var I18nInjectionKey = shared.makeSymbol("global-vue-i18n");
    function createI18n(options = {}, VueI18nLegacy) {
      const __legacyMode = shared.isBoolean(options.legacy) ? options.legacy : true;
      const __globalInjection = shared.isBoolean(options.globalInjection) ? options.globalInjection : true;
      const __allowComposition = __legacyMode ? !!options.allowComposition : true;
      const __instances = /* @__PURE__ */ new Map();
      const [globalScope, __global] = createGlobal(options, __legacyMode);
      const symbol = shared.makeSymbol("vue-i18n");
      {
        if (__legacyMode && __allowComposition && true) {
          shared.warn(getWarnMessage2(I18nWarnCodes.NOTICE_DROP_ALLOW_COMPOSITION));
        }
      }
      function __getInstance(component) {
        return __instances.get(component) || null;
      }
      function __setInstance(component, instance) {
        __instances.set(component, instance);
      }
      function __deleteInstance(component) {
        __instances.delete(component);
      }
      {
        const i18n = {
          // mode
          get mode() {
            return __legacyMode ? "legacy" : "composition";
          },
          // allowComposition
          get allowComposition() {
            return __allowComposition;
          },
          // install plugin
          async install(app, ...options2) {
            app.__VUE_I18N_SYMBOL__ = symbol;
            app.provide(app.__VUE_I18N_SYMBOL__, i18n);
            if (shared.isPlainObject(options2[0])) {
              const opts = options2[0];
              i18n.__composerExtend = opts.__composerExtend;
              i18n.__vueI18nExtend = opts.__vueI18nExtend;
            }
            let globalReleaseHandler = null;
            if (!__legacyMode && __globalInjection) {
              globalReleaseHandler = injectGlobalFields(app, i18n.global);
            }
            {
              apply(app, i18n, ...options2);
            }
            if (__legacyMode) {
              app.mixin(defineMixin(__global, __global.__composer, i18n));
            }
            const unmountApp = app.unmount;
            app.unmount = () => {
              globalReleaseHandler && globalReleaseHandler();
              i18n.dispose();
              unmountApp();
            };
          },
          // global accessor
          get global() {
            return __global;
          },
          dispose() {
            globalScope.stop();
          },
          // @internal
          __instances,
          // @internal
          __getInstance,
          // @internal
          __setInstance,
          // @internal
          __deleteInstance
        };
        return i18n;
      }
    }
    function useI18n(options = {}) {
      const instance = vue.getCurrentInstance();
      if (instance == null) {
        throw createI18nError(I18nErrorCodes.MUST_BE_CALL_SETUP_TOP);
      }
      if (!instance.isCE && instance.appContext.app != null && !instance.appContext.app.__VUE_I18N_SYMBOL__) {
        throw createI18nError(I18nErrorCodes.NOT_INSTALLED);
      }
      const i18n = getI18nInstance(instance);
      const gl = getGlobalComposer(i18n);
      const componentOptions = getComponentOptions(instance);
      const scope = getScope(options, componentOptions);
      {
        if (i18n.mode === "legacy" && !options.__useComponent) {
          if (!i18n.allowComposition) {
            throw createI18nError(I18nErrorCodes.NOT_AVAILABLE_IN_LEGACY_MODE);
          }
          return useI18nForLegacy(instance, scope, gl, options);
        }
      }
      if (scope === "global") {
        adjustI18nResources(gl, options, componentOptions);
        return gl;
      }
      if (scope === "parent") {
        let composer2 = getComposer(i18n, instance, options.__useComponent);
        if (composer2 == null) {
          {
            shared.warn(getWarnMessage2(I18nWarnCodes.NOT_FOUND_PARENT_SCOPE));
          }
          composer2 = gl;
        }
        return composer2;
      }
      const i18nInternal = i18n;
      let composer = i18nInternal.__getInstance(instance);
      if (composer == null) {
        const composerOptions = shared.assign({}, options);
        if ("__i18n" in componentOptions) {
          composerOptions.__i18n = componentOptions.__i18n;
        }
        if (gl) {
          composerOptions.__root = gl;
        }
        composer = createComposer(composerOptions);
        if (i18nInternal.__composerExtend) {
          composer[DisposeSymbol] = i18nInternal.__composerExtend(composer);
        }
        setupLifeCycle(i18nInternal, instance, composer);
        i18nInternal.__setInstance(instance, composer);
      }
      return composer;
    }
    var castToVueI18n = (i18n) => {
      if (!(__VUE_I18N_BRIDGE__ in i18n)) {
        throw createI18nError(I18nErrorCodes.NOT_COMPATIBLE_LEGACY_VUE_I18N);
      }
      return i18n;
    };
    function createGlobal(options, legacyMode, VueI18nLegacy) {
      const scope = vue.effectScope();
      {
        const obj = legacyMode ? scope.run(() => createVueI18n(options)) : scope.run(() => createComposer(options));
        if (obj == null) {
          throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);
        }
        return [scope, obj];
      }
    }
    function getI18nInstance(instance) {
      {
        const i18n = vue.inject(!instance.isCE ? instance.appContext.app.__VUE_I18N_SYMBOL__ : I18nInjectionKey);
        if (!i18n) {
          throw createI18nError(!instance.isCE ? I18nErrorCodes.UNEXPECTED_ERROR : I18nErrorCodes.NOT_INSTALLED_WITH_PROVIDE);
        }
        return i18n;
      }
    }
    function getScope(options, componentOptions) {
      return shared.isEmptyObject(options) ? "__i18n" in componentOptions ? "local" : "global" : !options.useScope ? "local" : options.useScope;
    }
    function getGlobalComposer(i18n) {
      return i18n.mode === "composition" ? i18n.global : i18n.global.__composer;
    }
    function getComposer(i18n, target, useComponent = false) {
      let composer = null;
      const root = target.root;
      let current = getParentComponentInstance(target, useComponent);
      while (current != null) {
        const i18nInternal = i18n;
        if (i18n.mode === "composition") {
          composer = i18nInternal.__getInstance(current);
        } else {
          {
            const vueI18n = i18nInternal.__getInstance(current);
            if (vueI18n != null) {
              composer = vueI18n.__composer;
              if (useComponent && composer && !composer[InejctWithOptionSymbol]) {
                composer = null;
              }
            }
          }
        }
        if (composer != null) {
          break;
        }
        if (root === current) {
          break;
        }
        current = current.parent;
      }
      return composer;
    }
    function getParentComponentInstance(target, useComponent = false) {
      if (target == null) {
        return null;
      }
      {
        return !useComponent ? target.parent : target.vnode.ctx || target.parent;
      }
    }
    function setupLifeCycle(i18n, target, composer) {
      {
        vue.onMounted(() => {
        }, target);
        vue.onUnmounted(() => {
          const _composer = composer;
          i18n.__deleteInstance(target);
          const dispose = _composer[DisposeSymbol];
          if (dispose) {
            dispose();
            delete _composer[DisposeSymbol];
          }
        }, target);
      }
    }
    function useI18nForLegacy(instance, scope, root, options = {}) {
      const isLocalScope = scope === "local";
      const _composer = vue.shallowRef(null);
      if (isLocalScope && instance.proxy && !(instance.proxy.$options.i18n || instance.proxy.$options.__i18n)) {
        throw createI18nError(I18nErrorCodes.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);
      }
      const _inheritLocale = shared.isBoolean(options.inheritLocale) ? options.inheritLocale : !shared.isString(options.locale);
      const _locale = vue.ref(
        // prettier-ignore
        !isLocalScope || _inheritLocale ? root.locale.value : shared.isString(options.locale) ? options.locale : coreBase.DEFAULT_LOCALE
      );
      const _fallbackLocale = vue.ref(
        // prettier-ignore
        !isLocalScope || _inheritLocale ? root.fallbackLocale.value : shared.isString(options.fallbackLocale) || shared.isArray(options.fallbackLocale) || shared.isPlainObject(options.fallbackLocale) || options.fallbackLocale === false ? options.fallbackLocale : _locale.value
      );
      const _messages = vue.ref(getLocaleMessages(_locale.value, options));
      const _datetimeFormats = vue.ref(shared.isPlainObject(options.datetimeFormats) ? options.datetimeFormats : { [_locale.value]: {} });
      const _numberFormats = vue.ref(shared.isPlainObject(options.numberFormats) ? options.numberFormats : { [_locale.value]: {} });
      const _missingWarn = isLocalScope ? root.missingWarn : shared.isBoolean(options.missingWarn) || shared.isRegExp(options.missingWarn) ? options.missingWarn : true;
      const _fallbackWarn = isLocalScope ? root.fallbackWarn : shared.isBoolean(options.fallbackWarn) || shared.isRegExp(options.fallbackWarn) ? options.fallbackWarn : true;
      const _fallbackRoot = isLocalScope ? root.fallbackRoot : shared.isBoolean(options.fallbackRoot) ? options.fallbackRoot : true;
      const _fallbackFormat = !!options.fallbackFormat;
      const _missing = shared.isFunction(options.missing) ? options.missing : null;
      const _postTranslation = shared.isFunction(options.postTranslation) ? options.postTranslation : null;
      const _warnHtmlMessage = isLocalScope ? root.warnHtmlMessage : shared.isBoolean(options.warnHtmlMessage) ? options.warnHtmlMessage : true;
      const _escapeParameter = !!options.escapeParameter;
      const _modifiers = isLocalScope ? root.modifiers : shared.isPlainObject(options.modifiers) ? options.modifiers : {};
      const _pluralRules = options.pluralRules || isLocalScope && root.pluralRules;
      function trackReactivityValues() {
        return [
          _locale.value,
          _fallbackLocale.value,
          _messages.value,
          _datetimeFormats.value,
          _numberFormats.value
        ];
      }
      const locale = vue.computed({
        get: () => {
          return _composer.value ? _composer.value.locale.value : _locale.value;
        },
        set: (val) => {
          if (_composer.value) {
            _composer.value.locale.value = val;
          }
          _locale.value = val;
        }
      });
      const fallbackLocale = vue.computed({
        get: () => {
          return _composer.value ? _composer.value.fallbackLocale.value : _fallbackLocale.value;
        },
        set: (val) => {
          if (_composer.value) {
            _composer.value.fallbackLocale.value = val;
          }
          _fallbackLocale.value = val;
        }
      });
      const messages = vue.computed(() => {
        if (_composer.value) {
          return _composer.value.messages.value;
        } else {
          return _messages.value;
        }
      });
      const datetimeFormats = vue.computed(() => _datetimeFormats.value);
      const numberFormats = vue.computed(() => _numberFormats.value);
      function getPostTranslationHandler() {
        return _composer.value ? _composer.value.getPostTranslationHandler() : _postTranslation;
      }
      function setPostTranslationHandler(handler) {
        if (_composer.value) {
          _composer.value.setPostTranslationHandler(handler);
        }
      }
      function getMissingHandler() {
        return _composer.value ? _composer.value.getMissingHandler() : _missing;
      }
      function setMissingHandler(handler) {
        if (_composer.value) {
          _composer.value.setMissingHandler(handler);
        }
      }
      function warpWithDeps(fn) {
        trackReactivityValues();
        return fn();
      }
      function t(...args) {
        return _composer.value ? warpWithDeps(() => Reflect.apply(_composer.value.t, null, [...args])) : warpWithDeps(() => "");
      }
      function rt(...args) {
        return _composer.value ? Reflect.apply(_composer.value.rt, null, [...args]) : "";
      }
      function d(...args) {
        return _composer.value ? warpWithDeps(() => Reflect.apply(_composer.value.d, null, [...args])) : warpWithDeps(() => "");
      }
      function n(...args) {
        return _composer.value ? warpWithDeps(() => Reflect.apply(_composer.value.n, null, [...args])) : warpWithDeps(() => "");
      }
      function tm(key) {
        return _composer.value ? _composer.value.tm(key) : {};
      }
      function te(key, locale2) {
        return _composer.value ? _composer.value.te(key, locale2) : false;
      }
      function getLocaleMessage(locale2) {
        return _composer.value ? _composer.value.getLocaleMessage(locale2) : {};
      }
      function setLocaleMessage(locale2, message) {
        if (_composer.value) {
          _composer.value.setLocaleMessage(locale2, message);
          _messages.value[locale2] = message;
        }
      }
      function mergeLocaleMessage(locale2, message) {
        if (_composer.value) {
          _composer.value.mergeLocaleMessage(locale2, message);
        }
      }
      function getDateTimeFormat(locale2) {
        return _composer.value ? _composer.value.getDateTimeFormat(locale2) : {};
      }
      function setDateTimeFormat(locale2, format3) {
        if (_composer.value) {
          _composer.value.setDateTimeFormat(locale2, format3);
          _datetimeFormats.value[locale2] = format3;
        }
      }
      function mergeDateTimeFormat(locale2, format3) {
        if (_composer.value) {
          _composer.value.mergeDateTimeFormat(locale2, format3);
        }
      }
      function getNumberFormat(locale2) {
        return _composer.value ? _composer.value.getNumberFormat(locale2) : {};
      }
      function setNumberFormat(locale2, format3) {
        if (_composer.value) {
          _composer.value.setNumberFormat(locale2, format3);
          _numberFormats.value[locale2] = format3;
        }
      }
      function mergeNumberFormat(locale2, format3) {
        if (_composer.value) {
          _composer.value.mergeNumberFormat(locale2, format3);
        }
      }
      const wrapper = {
        get id() {
          return _composer.value ? _composer.value.id : -1;
        },
        locale,
        fallbackLocale,
        messages,
        datetimeFormats,
        numberFormats,
        get inheritLocale() {
          return _composer.value ? _composer.value.inheritLocale : _inheritLocale;
        },
        set inheritLocale(val) {
          if (_composer.value) {
            _composer.value.inheritLocale = val;
          }
        },
        get availableLocales() {
          return _composer.value ? _composer.value.availableLocales : Object.keys(_messages.value);
        },
        get modifiers() {
          return _composer.value ? _composer.value.modifiers : _modifiers;
        },
        get pluralRules() {
          return _composer.value ? _composer.value.pluralRules : _pluralRules;
        },
        get isGlobal() {
          return _composer.value ? _composer.value.isGlobal : false;
        },
        get missingWarn() {
          return _composer.value ? _composer.value.missingWarn : _missingWarn;
        },
        set missingWarn(val) {
          if (_composer.value) {
            _composer.value.missingWarn = val;
          }
        },
        get fallbackWarn() {
          return _composer.value ? _composer.value.fallbackWarn : _fallbackWarn;
        },
        set fallbackWarn(val) {
          if (_composer.value) {
            _composer.value.missingWarn = val;
          }
        },
        get fallbackRoot() {
          return _composer.value ? _composer.value.fallbackRoot : _fallbackRoot;
        },
        set fallbackRoot(val) {
          if (_composer.value) {
            _composer.value.fallbackRoot = val;
          }
        },
        get fallbackFormat() {
          return _composer.value ? _composer.value.fallbackFormat : _fallbackFormat;
        },
        set fallbackFormat(val) {
          if (_composer.value) {
            _composer.value.fallbackFormat = val;
          }
        },
        get warnHtmlMessage() {
          return _composer.value ? _composer.value.warnHtmlMessage : _warnHtmlMessage;
        },
        set warnHtmlMessage(val) {
          if (_composer.value) {
            _composer.value.warnHtmlMessage = val;
          }
        },
        get escapeParameter() {
          return _composer.value ? _composer.value.escapeParameter : _escapeParameter;
        },
        set escapeParameter(val) {
          if (_composer.value) {
            _composer.value.escapeParameter = val;
          }
        },
        t,
        getPostTranslationHandler,
        setPostTranslationHandler,
        getMissingHandler,
        setMissingHandler,
        rt,
        d,
        n,
        tm,
        te,
        getLocaleMessage,
        setLocaleMessage,
        mergeLocaleMessage,
        getDateTimeFormat,
        setDateTimeFormat,
        mergeDateTimeFormat,
        getNumberFormat,
        setNumberFormat,
        mergeNumberFormat
      };
      function sync(composer) {
        composer.locale.value = _locale.value;
        composer.fallbackLocale.value = _fallbackLocale.value;
        Object.keys(_messages.value).forEach((locale2) => {
          composer.mergeLocaleMessage(locale2, _messages.value[locale2]);
        });
        Object.keys(_datetimeFormats.value).forEach((locale2) => {
          composer.mergeDateTimeFormat(locale2, _datetimeFormats.value[locale2]);
        });
        Object.keys(_numberFormats.value).forEach((locale2) => {
          composer.mergeNumberFormat(locale2, _numberFormats.value[locale2]);
        });
        composer.escapeParameter = _escapeParameter;
        composer.fallbackFormat = _fallbackFormat;
        composer.fallbackRoot = _fallbackRoot;
        composer.fallbackWarn = _fallbackWarn;
        composer.missingWarn = _missingWarn;
        composer.warnHtmlMessage = _warnHtmlMessage;
      }
      vue.onBeforeMount(() => {
        if (instance.proxy == null || instance.proxy.$i18n == null) {
          throw createI18nError(I18nErrorCodes.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);
        }
        const composer = _composer.value = instance.proxy.$i18n.__composer;
        if (scope === "global") {
          _locale.value = composer.locale.value;
          _fallbackLocale.value = composer.fallbackLocale.value;
          _messages.value = composer.messages.value;
          _datetimeFormats.value = composer.datetimeFormats.value;
          _numberFormats.value = composer.numberFormats.value;
        } else if (isLocalScope) {
          sync(composer);
        }
      });
      return wrapper;
    }
    var globalExportProps = [
      "locale",
      "fallbackLocale",
      "availableLocales"
    ];
    var globalExportMethods = ["t", "rt", "d", "n", "tm", "te"];
    function injectGlobalFields(app, composer) {
      const i18n = /* @__PURE__ */ Object.create(null);
      globalExportProps.forEach((prop) => {
        const desc = Object.getOwnPropertyDescriptor(composer, prop);
        if (!desc) {
          throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);
        }
        const wrap = vue.isRef(desc.value) ? {
          get() {
            return desc.value.value;
          },
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          set(val) {
            desc.value.value = val;
          }
        } : {
          get() {
            return desc.get && desc.get();
          }
        };
        Object.defineProperty(i18n, prop, wrap);
      });
      app.config.globalProperties.$i18n = i18n;
      globalExportMethods.forEach((method) => {
        const desc = Object.getOwnPropertyDescriptor(composer, method);
        if (!desc || !desc.value) {
          throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);
        }
        Object.defineProperty(app.config.globalProperties, `$${method}`, desc);
      });
      const dispose = () => {
        delete app.config.globalProperties.$i18n;
        globalExportMethods.forEach((method) => {
          delete app.config.globalProperties[`$${method}`];
        });
      };
      return dispose;
    }
    {
      coreBase.registerMessageCompiler(coreBase.compile);
    }
    coreBase.registerMessageResolver(coreBase.resolveValue);
    coreBase.registerLocaleFallbacker(coreBase.fallbackWithLocaleChain);
    {
      const target = shared.getGlobalThis();
      target.__INTLIFY__ = true;
      coreBase.setDevToolsHook(target.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__);
    }
    exports.DatetimeFormat = DatetimeFormat;
    exports.I18nD = I18nD;
    exports.I18nInjectionKey = I18nInjectionKey;
    exports.I18nN = I18nN;
    exports.I18nT = I18nT;
    exports.NumberFormat = NumberFormat;
    exports.Translation = Translation;
    exports.VERSION = VERSION2;
    exports.castToVueI18n = castToVueI18n;
    exports.createI18n = createI18n;
    exports.useI18n = useI18n;
    exports.vTDirective = vTDirective;
  }
});

// node_modules/vue-i18n/dist/vue-i18n.cjs.js
var require_vue_i18n_cjs = __commonJS({
  "node_modules/vue-i18n/dist/vue-i18n.cjs.js"(exports, module) {
    module.exports = require_vue_i18n();
  }
});
export default require_vue_i18n_cjs();
/*! Bundled license information:

@intlify/shared/dist/shared.esm-browser.js:
  (*!
    * shared v9.14.1
    * (c) 2024 kazuya kawaguchi
    * Released under the MIT License.
    *)

@intlify/core-base/dist/core-base.esm-browser.js:
  (*!
    * core-base v9.14.1
    * (c) 2024 kazuya kawaguchi
    * Released under the MIT License.
    *)

@vue/shared/dist/shared.esm-bundler.js:
  (**
  * @vue/shared v3.5.2
  * (c) 2018-present Yuxi (Evan) You and Vue contributors
  * @license MIT
  **)
  (*! #__NO_SIDE_EFFECTS__ *)

@vue/compiler-core/dist/compiler-core.esm-bundler.js:
  (**
  * @vue/compiler-core v3.5.2
  * (c) 2018-present Yuxi (Evan) You and Vue contributors
  * @license MIT
  **)

@vue/compiler-dom/dist/compiler-dom.esm-bundler.js:
  (**
  * @vue/compiler-dom v3.5.2
  * (c) 2018-present Yuxi (Evan) You and Vue contributors
  * @license MIT
  **)

vue/dist/vue.cjs.js:
  (**
  * vue v3.5.2
  * (c) 2018-present Yuxi (Evan) You and Vue contributors
  * @license MIT
  **)

vue-i18n/dist/vue-i18n.cjs:
  (*!
    * vue-i18n v9.14.1
    * (c) 2024 kazuya kawaguchi
    * Released under the MIT License.
    *)
*/
//# sourceMappingURL=vue-i18n_dist_vue-i18n__cjs__js.js.map
