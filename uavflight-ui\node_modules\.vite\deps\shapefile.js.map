{"version": 3, "sources": ["../../array-source/cancel.js", "../../array-source/read.js", "../../array-source/index.js", "../../path-source/fetch.js", "../../path-source/request.js", "../../path-source/index.js", "../../stream-source/index.js", "../../slice-source/empty.js", "../../slice-source/cancel.js", "../../slice-source/concat.js", "../../slice-source/read.js", "../../slice-source/slice.js", "../../slice-source/index.js", "../../shapefile/dbf/cancel.js", "../../shapefile/dbf/boolean.js", "../../shapefile/dbf/date.js", "../../shapefile/dbf/number.js", "../../shapefile/dbf/string.js", "../../shapefile/dbf/read.js", "../../shapefile/view.js", "../../shapefile/dbf/index.js", "../../shapefile/shp/cancel.js", "../../shapefile/shp/multipoint.js", "../../shapefile/shp/null.js", "../../shapefile/shp/point.js", "../../shapefile/shp/polygon.js", "../../shapefile/shp/polyline.js", "../../shapefile/shp/concat.js", "../../shapefile/shp/read.js", "../../shapefile/shp/index.js", "../../shapefile/shapefile/cancel.js", "../../shapefile/shapefile/read.js", "../../shapefile/shapefile/index.js", "../../shapefile/index.js"], "sourcesContent": ["export default function() {\n  this._array = null;\n  return Promise.resolve();\n}\n", "export default function() {\n  var array = this._array;\n  this._array = null;\n  return Promise.resolve(array ? {done: false, value: array} : {done: true, value: undefined});\n}\n", "import array_cancel from \"./cancel\";\nimport array_read from \"./read\";\n\nexport default function array(array) {\n  return new ArraySource(array instanceof Uint8Array ? array : new Uint8Array(array));\n}\n\nfunction ArraySource(array) {\n  this._array = array;\n}\n\nArraySource.prototype.read = array_read;\nArraySource.prototype.cancel = array_cancel;\n", "import array from \"array-source\";\n\nexport default function(url) {\n  return fetch(url).then(function(response) {\n    return response.body && response.body.getReader\n        ? response.body.getReader()\n        : response.arrayBuffer().then(array);\n  });\n}\n", "import array from \"array-source\";\n\nexport default function(url) {\n  return new Promise(function(resolve, reject) {\n    var request = new XMLHttpRequest;\n    request.responseType = \"arraybuffer\";\n    request.onload = function() { resolve(array(request.response)); };\n    request.onerror = reject;\n    request.ontimeout = reject;\n    request.open(\"GET\", url, true);\n    request.send();\n  });\n}\n", "import fetchPath from \"./fetch\";\nimport requestPath from \"./request\";\n\nexport default function path(path) {\n  return (typeof fetch === \"function\" ? fetchPath : requestPath)(path);\n}\n", "export default function stream(source) {\n  return typeof source.read === \"function\" ? source : source.getReader();\n}\n", "export default new Uint8Array(0);\n", "export default function() {\n  return this._source.cancel();\n}\n", "export default function concat(a, b) {\n  if (!a.length) return b;\n  if (!b.length) return a;\n  var c = new Uint8Array(a.length + b.length);\n  c.set(a);\n  c.set(b, a.length);\n  return c;\n}\n", "import concat from \"./concat\";\nimport empty from \"./empty\";\n\nexport default function() {\n  var that = this, array = that._array.subarray(that._index);\n  return that._source.read().then(function(result) {\n    that._array = empty;\n    that._index = 0;\n    return result.done ? (array.length > 0\n        ? {done: false, value: array}\n        : {done: true, value: undefined})\n        : {done: false, value: concat(array, result.value)};\n  });\n}\n", "import empty from \"./empty\";\n\nexport default function(length) {\n  if ((length |= 0) < 0) throw new Error(\"invalid length\");\n  var that = this, index = this._array.length - this._index;\n\n  // If the request fits within the remaining buffer, resolve it immediately.\n  if (this._index + length <= this._array.length) {\n    return Promise.resolve(this._array.subarray(this._index, this._index += length));\n  }\n\n  // Otherwise, read chunks repeatedly until the request is fulfilled.\n  var array = new Uint8Array(length);\n  array.set(this._array.subarray(this._index));\n  return (function read() {\n    return that._source.read().then(function(result) {\n\n      // When done, it’s possible the request wasn’t fully fullfilled!\n      // If so, the pre-allocated array is too big and needs slicing.\n      if (result.done) {\n        that._array = empty;\n        that._index = 0;\n        return index > 0 ? array.subarray(0, index) : null;\n      }\n\n      // If this chunk fulfills the request, return the resulting array.\n      if (index + result.value.length >= length) {\n        that._array = result.value;\n        that._index = length - index;\n        array.set(result.value.subarray(0, length - index), index);\n        return array;\n      }\n\n      // Otherwise copy this chunk into the array, then read the next chunk.\n      array.set(result.value, index);\n      index += result.value.length;\n      return read();\n    });\n  })();\n}\n", "import empty from \"./empty\";\nimport slice_cancel from \"./cancel\";\nimport slice_read from \"./read\";\nimport slice_slice from \"./slice\";\n\nexport default function slice(source) {\n  return typeof source.slice === \"function\" ? source :\n      new SliceSource(typeof source.read === \"function\" ? source\n          : source.getReader());\n}\n\nfunction SliceSource(source) {\n  this._source = source;\n  this._array = empty;\n  this._index = 0;\n}\n\nSliceSource.prototype.read = slice_read;\nSliceSource.prototype.slice = slice_slice;\nSliceSource.prototype.cancel = slice_cancel;\n", "export default function() {\n  return this._source.cancel();\n}\n", "export default function(value) {\n  return /^[nf]$/i.test(value) ? false\n      : /^[yt]$/i.test(value) ? true\n      : null;\n}\n", "export default function(value) {\n  return new Date(+value.substring(0, 4), value.substring(4, 6) - 1, +value.substring(6, 8));\n}\n", "export default function(value) {\n  return !(value = value.trim()) || isNaN(value = +value) ? null : value;\n}\n", "export default function(value) {\n  return value.trim() || null;\n}\n", "import readBoolean from \"./boolean\";\nimport readDate from \"./date\";\nimport readNumber from \"./number\";\nimport readString from \"./string\";\n\nvar types = {\n  B: readNumber,\n  C: readString,\n  D: readDate,\n  F: readNumber,\n  L: readBoolean,\n  M: readNumber,\n  N: readNumber\n};\n\nexport default function() {\n  var that = this, i = 1;\n  return that._source.slice(that._recordLength).then(function(value) {\n    return value && (value[0] !== 0x1a) ? {done: false, value: that._fields.reduce(function(p, f) {\n      p[f.name] = types[f.type](that._decode(value.subarray(i, i += f.length)));\n      return p;\n    }, {})} : {done: true, value: undefined};\n  });\n}\n", "export default function(array) {\n  return new DataView(array.buffer, array.byteOffset, array.byteLength);\n}\n", "import slice from \"slice-source\";\nimport dbf_cancel from \"./cancel\";\nimport dbf_read from \"./read\";\nimport view from \"../view\";\n\nexport default function(source, decoder) {\n  source = slice(source);\n  return source.slice(32).then(function(array) {\n    var head = view(array);\n    return source.slice(head.getUint16(8, true) - 32).then(function(array) {\n      return new Dbf(source, decoder, head, view(array));\n    });\n  });\n}\n\nfunction Dbf(source, decoder, head, body) {\n  this._source = source;\n  this._decode = decoder.decode.bind(decoder);\n  this._recordLength = head.getUint16(10, true);\n  this._fields = [];\n  for (var n = 0; body.getUint8(n) !== 0x0d; n += 32) {\n    for (var j = 0; j < 11; ++j) if (body.getUint8(n + j) === 0) break;\n    this._fields.push({\n      name: this._decode(new Uint8Array(body.buffer, body.byteOffset + n, j)),\n      type: String.fromCharCode(body.getUint8(n + 11)),\n      length: body.getUint8(n + 16)\n    });\n  }\n}\n\nvar prototype = Dbf.prototype;\nprototype.read = dbf_read;\nprototype.cancel = dbf_cancel;\n", "export default function cancel() {\n  return this._source.cancel();\n}\n", "export default function(record) {\n  var i = 40, j, n = record.getInt32(36, true), coordinates = new Array(n);\n  for (j = 0; j < n; ++j, i += 16) coordinates[j] = [record.getFloat64(i, true), record.getFloat64(i + 8, true)];\n  return {type: \"MultiPoint\", coordinates: coordinates};\n};\n", "export default function() {\n  return null;\n};\n", "export default function(record) {\n  return {type: \"Point\", coordinates: [record.getFloat64(4, true), record.getFloat64(12, true)]};\n};\n", "export default function(record) {\n  var i = 44, j, n = record.getInt32(36, true), m = record.getInt32(40, true), parts = new Array(n), points = new Array(m), polygons = [], holes = [];\n  for (j = 0; j < n; ++j, i += 4) parts[j] = record.getInt32(i, true);\n  for (j = 0; j < m; ++j, i += 16) points[j] = [record.getFloat64(i, true), record.getFloat64(i + 8, true)];\n\n  parts.forEach(function(i, j) {\n    var ring = points.slice(i, parts[j + 1]);\n    if (ringClockwise(ring)) polygons.push([ring]);\n    else holes.push(ring);\n  });\n\n  holes.forEach(function(hole) {\n    polygons.some(function(polygon) {\n      if (ringContainsSome(polygon[0], hole)) {\n        polygon.push(hole);\n        return true;\n      }\n    }) || polygons.push([hole]);\n  });\n\n  return polygons.length === 1\n      ? {type: \"Polygon\", coordinates: polygons[0]}\n      : {type: \"MultiPolygon\", coordinates: polygons};\n};\n\nfunction ringClockwise(ring) {\n  if ((n = ring.length) < 4) return false;\n  var i = 0, n, area = ring[n - 1][1] * ring[0][0] - ring[n - 1][0] * ring[0][1];\n  while (++i < n) area += ring[i - 1][1] * ring[i][0] - ring[i - 1][0] * ring[i][1];\n  return area >= 0;\n}\n\nfunction ringContainsSome(ring, hole) {\n  var i = -1, n = hole.length, c;\n  while (++i < n) {\n    if (c = ringContains(ring, hole[i])) {\n      return c > 0;\n    }\n  }\n  return false;\n}\n\nfunction ringContains(ring, point) {\n  var x = point[0], y = point[1], contains = -1;\n  for (var i = 0, n = ring.length, j = n - 1; i < n; j = i++) {\n    var pi = ring[i], xi = pi[0], yi = pi[1],\n        pj = ring[j], xj = pj[0], yj = pj[1];\n    if (segmentContains(pi, pj, point)) {\n      return 0;\n    }\n    if (((yi > y) !== (yj > y)) && ((x < (xj - xi) * (y - yi) / (yj - yi) + xi))) {\n      contains = -contains;\n    }\n  }\n  return contains;\n}\n\nfunction segmentContains(p0, p1, p2) {\n  var x20 = p2[0] - p0[0], y20 = p2[1] - p0[1];\n  if (x20 === 0 && y20 === 0) return true;\n  var x10 = p1[0] - p0[0], y10 = p1[1] - p0[1];\n  if (x10 === 0 && y10 === 0) return false;\n  var t = (x20 * x10 + y20 * y10) / (x10 * x10 + y10 * y10);\n  return t < 0 || t > 1 ? false : t === 0 || t === 1 ? true : t * x10 === x20 && t * y10 === y20;\n}\n", "export default function(record) {\n  var i = 44, j, n = record.getInt32(36, true), m = record.getInt32(40, true), parts = new Array(n), points = new Array(m);\n  for (j = 0; j < n; ++j, i += 4) parts[j] = record.getInt32(i, true);\n  for (j = 0; j < m; ++j, i += 16) points[j] = [record.getFloat64(i, true), record.getFloat64(i + 8, true)];\n  return n === 1\n      ? {type: \"LineString\", coordinates: points}\n      : {type: \"MultiLineString\", coordinates: parts.map(function(i, j) { return points.slice(i, parts[j + 1]); })};\n};\n", "export default function(a, b) {\n  var ab = new Uint8Array(a.length + b.length);\n  ab.set(a, 0);\n  ab.set(b, a.length);\n  return ab;\n}\n", "import concat from \"./concat\";\nimport view from \"../view\";\n\nexport default function() {\n  var that = this;\n  ++that._index;\n  return that._source.slice(12).then(function(array) {\n    if (array == null) return {done: true, value: undefined};\n    var header = view(array);\n\n    // If the record starts with an invalid shape type (see #36), scan ahead in\n    // four-byte increments to find the next valid record, identified by the\n    // expected index, a non-empty content length and a valid shape type.\n    function skip() {\n      return that._source.slice(4).then(function(chunk) {\n        if (chunk == null) return {done: true, value: undefined};\n        header = view(array = concat(array.slice(4), chunk));\n        return header.getInt32(0, false) !== that._index ? skip() : read();\n      });\n    }\n\n    // All records should have at least four bytes (for the record shape type),\n    // so an invalid content length indicates corruption.\n    function read() {\n      var length = header.getInt32(4, false) * 2 - 4, type = header.getInt32(8, true);\n      return length < 0 || (type && type !== that._type) ? skip() : that._source.slice(length).then(function(chunk) {\n        return {done: false, value: type ? that._parse(view(concat(array.slice(8), chunk))) : null};\n      });\n    }\n\n    return read();\n  });\n}\n", "import slice from \"slice-source\";\nimport view from \"../view\";\nimport shp_cancel from \"./cancel\";\nimport parseMultiPoint from \"./multipoint\";\nimport parseNull from \"./null\";\nimport parsePoint from \"./point\";\nimport parsePolygon from \"./polygon\";\nimport parsePolyLine from \"./polyline\";\nimport shp_read from \"./read\";\n\nvar parsers = {\n  0: parseNull,\n  1: parsePoint,\n  3: parsePolyLine,\n  5: parsePolygon,\n  8: parseMultiPoint,\n  11: parsePoint, // PointZ\n  13: parsePolyLine, // PolyLineZ\n  15: parsePolygon, // PolygonZ\n  18: parseMultiPoint, // MultiPointZ\n  21: parsePoint, // PointM\n  23: parsePolyLine, // PolyLineM\n  25: parsePolygon, // PolygonM\n  28: parseMultiPoint // MultiPointM\n};\n\nexport default function(source) {\n  source = slice(source);\n  return source.slice(100).then(function(array) {\n    return new Shp(source, view(array));\n  });\n};\n\nfunction Shp(source, header) {\n  var type = header.getInt32(32, true);\n  if (!(type in parsers)) throw new Error(\"unsupported shape type: \" + type);\n  this._source = source;\n  this._type = type;\n  this._index = 0;\n  this._parse = parsers[type];\n  this.bbox = [header.getFloat64(36, true), header.getFloat64(44, true), header.getFloat64(52, true), header.getFloat64(60, true)];\n}\n\nvar prototype = Shp.prototype;\nprototype.read = shp_read;\nprototype.cancel = shp_cancel;\n", "function noop() {}\n\nexport default function() {\n  return Promise.all([\n    this._dbf && this._dbf.cancel(),\n    this._shp.cancel()\n  ]).then(noop);\n}\n", "export default function() {\n  var that = this;\n  return Promise.all([\n    that._dbf ? that._dbf.read() : {value: {}},\n    that._shp.read()\n  ]).then(function(results) {\n    var dbf = results[0], shp = results[1];\n    return shp.done ? shp : {\n      done: false,\n      value: {\n        type: \"Feature\",\n        properties: dbf.value,\n        geometry: shp.value\n      }\n    };\n  });\n};\n", "import dbf from \"../dbf/index\";\nimport shp from \"../shp/index\";\nimport shapefile_cancel from \"./cancel\";\nimport shapefile_read from \"./read\";\n\nexport default function(shpSource, dbfSource, decoder) {\n  return Promise.all([\n    shp(shpSource),\n    dbfSource && dbf(dbfSource, decoder)\n  ]).then(function(sources) {\n    return new Shapefile(sources[0], sources[1]);\n  });\n}\n\nfunction Shapefile(shp, dbf) {\n  this._shp = shp;\n  this._dbf = dbf;\n  this.bbox = shp.bbox;\n}\n\nvar prototype = Shapefile.prototype;\nprototype.read = shapefile_read;\nprototype.cancel = shapefile_cancel;\n", "import path from \"path-source\";\nimport array from \"array-source\";\nimport stream from \"stream-source\";\nimport dbf from \"./dbf/index\";\nimport shapefile from \"./shapefile/index\";\nimport shp from \"./shp/index\";\n\nexport function open(shp, dbf, options) {\n  if (typeof dbf === \"string\") {\n    if (!/\\.dbf$/.test(dbf)) dbf += \".dbf\";\n    dbf = path(dbf, options);\n  } else if (dbf instanceof ArrayBuffer || dbf instanceof Uint8Array) {\n    dbf = array(dbf);\n  } else if (dbf != null) {\n    dbf = stream(dbf);\n  }\n  if (typeof shp === \"string\") {\n    if (!/\\.shp$/.test(shp)) shp += \".shp\";\n    if (dbf === undefined) dbf = path(shp.substring(0, shp.length - 4) + \".dbf\", options).catch(function() {});\n    shp = path(shp, options);\n  } else if (shp instanceof ArrayBuffer || shp instanceof Uint8Array) {\n    shp = array(shp);\n  } else {\n    shp = stream(shp);\n  }\n  return Promise.all([shp, dbf]).then(function(sources) {\n    var shp = sources[0], dbf = sources[1], encoding = \"windows-1252\";\n    if (options && options.encoding != null) encoding = options.encoding;\n    return shapefile(shp, dbf, dbf && new TextDecoder(encoding));\n  });\n}\n\nexport function openShp(source, options) {\n  if (typeof source === \"string\") {\n    if (!/\\.shp$/.test(source)) source += \".shp\";\n    source = path(source, options);\n  } else if (source instanceof ArrayBuffer || source instanceof Uint8Array) {\n    source = array(source);\n  } else {\n    source = stream(source);\n  }\n  return Promise.resolve(source).then(shp);\n}\n\nexport function openDbf(source, options) {\n  var encoding = \"windows-1252\";\n  if (options && options.encoding != null) encoding = options.encoding;\n  encoding = new TextDecoder(encoding);\n  if (typeof source === \"string\") {\n    if (!/\\.dbf$/.test(source)) source += \".dbf\";\n    source = path(source, options);\n  } else if (source instanceof ArrayBuffer || source instanceof Uint8Array) {\n    source = array(source);\n  } else {\n    source = stream(source);\n  }\n  return Promise.resolve(source).then(function(source) {\n    return dbf(source, encoding);\n  });\n}\n\nexport function read(shp, dbf, options) {\n  return open(shp, dbf, options).then(function(source) {\n    var features = [], collection = {type: \"FeatureCollection\", features: features, bbox: source.bbox};\n    return source.read().then(function read(result) {\n      if (result.done) return collection;\n      features.push(result.value);\n      return source.read().then(read);\n    });\n  });\n}\n"], "mappings": ";;;AAAe,SAAR,iBAAmB;AACxB,OAAK,SAAS;AACd,SAAO,QAAQ,QAAQ;AACzB;;;ACHe,SAAR,eAAmB;AACxB,MAAIA,SAAQ,KAAK;AACjB,OAAK,SAAS;AACd,SAAO,QAAQ,QAAQA,SAAQ,EAAC,MAAM,OAAO,OAAOA,OAAK,IAAI,EAAC,MAAM,MAAM,OAAO,OAAS,CAAC;AAC7F;;;ACDe,SAAR,MAAuBC,QAAO;AACnC,SAAO,IAAI,YAAYA,kBAAiB,aAAaA,SAAQ,IAAI,WAAWA,MAAK,CAAC;AACpF;AAEA,SAAS,YAAYA,QAAO;AAC1B,OAAK,SAASA;AAChB;AAEA,YAAY,UAAU,OAAO;AAC7B,YAAY,UAAU,SAAS;;;ACVhB,SAAR,cAAiB,KAAK;AAC3B,SAAO,MAAM,GAAG,EAAE,KAAK,SAAS,UAAU;AACxC,WAAO,SAAS,QAAQ,SAAS,KAAK,YAChC,SAAS,KAAK,UAAU,IACxB,SAAS,YAAY,EAAE,KAAK,KAAK;AAAA,EACzC,CAAC;AACH;;;ACNe,SAAR,gBAAiB,KAAK;AAC3B,SAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,QAAI,UAAU,IAAI;AAClB,YAAQ,eAAe;AACvB,YAAQ,SAAS,WAAW;AAAE,cAAQ,MAAM,QAAQ,QAAQ,CAAC;AAAA,IAAG;AAChE,YAAQ,UAAU;AAClB,YAAQ,YAAY;AACpB,YAAQ,KAAK,OAAO,KAAK,IAAI;AAC7B,YAAQ,KAAK;AAAA,EACf,CAAC;AACH;;;ACTe,SAAR,KAAsBC,OAAM;AACjC,UAAQ,OAAO,UAAU,aAAa,gBAAY,iBAAaA,KAAI;AACrE;;;ACLe,SAAR,OAAwB,QAAQ;AACrC,SAAO,OAAO,OAAO,SAAS,aAAa,SAAS,OAAO,UAAU;AACvE;;;ACFA,IAAO,gBAAQ,IAAI,WAAW,CAAC;;;ACAhB,SAARC,kBAAmB;AACxB,SAAO,KAAK,QAAQ,OAAO;AAC7B;;;ACFe,SAAR,OAAwB,GAAG,GAAG;AACnC,MAAI,CAAC,EAAE,OAAQ,QAAO;AACtB,MAAI,CAAC,EAAE,OAAQ,QAAO;AACtB,MAAI,IAAI,IAAI,WAAW,EAAE,SAAS,EAAE,MAAM;AAC1C,IAAE,IAAI,CAAC;AACP,IAAE,IAAI,GAAG,EAAE,MAAM;AACjB,SAAO;AACT;;;ACJe,SAARC,gBAAmB;AACxB,MAAI,OAAO,MAAMC,SAAQ,KAAK,OAAO,SAAS,KAAK,MAAM;AACzD,SAAO,KAAK,QAAQ,KAAK,EAAE,KAAK,SAAS,QAAQ;AAC/C,SAAK,SAAS;AACd,SAAK,SAAS;AACd,WAAO,OAAO,OAAQA,OAAM,SAAS,IAC/B,EAAC,MAAM,OAAO,OAAOA,OAAK,IAC1B,EAAC,MAAM,MAAM,OAAO,OAAS,IAC7B,EAAC,MAAM,OAAO,OAAO,OAAOA,QAAO,OAAO,KAAK,EAAC;AAAA,EACxD,CAAC;AACH;;;ACXe,SAAR,cAAiB,QAAQ;AAC9B,OAAK,UAAU,KAAK,EAAG,OAAM,IAAI,MAAM,gBAAgB;AACvD,MAAI,OAAO,MAAM,QAAQ,KAAK,OAAO,SAAS,KAAK;AAGnD,MAAI,KAAK,SAAS,UAAU,KAAK,OAAO,QAAQ;AAC9C,WAAO,QAAQ,QAAQ,KAAK,OAAO,SAAS,KAAK,QAAQ,KAAK,UAAU,MAAM,CAAC;AAAA,EACjF;AAGA,MAAIC,SAAQ,IAAI,WAAW,MAAM;AACjC,EAAAA,OAAM,IAAI,KAAK,OAAO,SAAS,KAAK,MAAM,CAAC;AAC3C,SAAQ,SAASC,QAAO;AACtB,WAAO,KAAK,QAAQ,KAAK,EAAE,KAAK,SAAS,QAAQ;AAI/C,UAAI,OAAO,MAAM;AACf,aAAK,SAAS;AACd,aAAK,SAAS;AACd,eAAO,QAAQ,IAAID,OAAM,SAAS,GAAG,KAAK,IAAI;AAAA,MAChD;AAGA,UAAI,QAAQ,OAAO,MAAM,UAAU,QAAQ;AACzC,aAAK,SAAS,OAAO;AACrB,aAAK,SAAS,SAAS;AACvB,QAAAA,OAAM,IAAI,OAAO,MAAM,SAAS,GAAG,SAAS,KAAK,GAAG,KAAK;AACzD,eAAOA;AAAA,MACT;AAGA,MAAAA,OAAM,IAAI,OAAO,OAAO,KAAK;AAC7B,eAAS,OAAO,MAAM;AACtB,aAAOC,MAAK;AAAA,IACd,CAAC;AAAA,EACH,EAAG;AACL;;;AClCe,SAAR,MAAuB,QAAQ;AACpC,SAAO,OAAO,OAAO,UAAU,aAAa,SACxC,IAAI,YAAY,OAAO,OAAO,SAAS,aAAa,SAC9C,OAAO,UAAU,CAAC;AAC9B;AAEA,SAAS,YAAY,QAAQ;AAC3B,OAAK,UAAU;AACf,OAAK,SAAS;AACd,OAAK,SAAS;AAChB;AAEA,YAAY,UAAU,OAAOC;AAC7B,YAAY,UAAU,QAAQ;AAC9B,YAAY,UAAU,SAASC;;;ACnBhB,SAARC,kBAAmB;AACxB,SAAO,KAAK,QAAQ,OAAO;AAC7B;;;ACFe,SAAR,gBAAiB,OAAO;AAC7B,SAAO,UAAU,KAAK,KAAK,IAAI,QACzB,UAAU,KAAK,KAAK,IAAI,OACxB;AACR;;;ACJe,SAAR,aAAiB,OAAO;AAC7B,SAAO,IAAI,KAAK,CAAC,MAAM,UAAU,GAAG,CAAC,GAAG,MAAM,UAAU,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,UAAU,GAAG,CAAC,CAAC;AAC3F;;;ACFe,SAAR,eAAiB,OAAO;AAC7B,SAAO,EAAE,QAAQ,MAAM,KAAK,MAAM,MAAM,QAAQ,CAAC,KAAK,IAAI,OAAO;AACnE;;;ACFe,SAAR,eAAiB,OAAO;AAC7B,SAAO,MAAM,KAAK,KAAK;AACzB;;;ACGA,IAAI,QAAQ;AAAA,EACV,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL;AAEe,SAARC,gBAAmB;AACxB,MAAI,OAAO,MAAM,IAAI;AACrB,SAAO,KAAK,QAAQ,MAAM,KAAK,aAAa,EAAE,KAAK,SAAS,OAAO;AACjE,WAAO,SAAU,MAAM,CAAC,MAAM,KAAQ,EAAC,MAAM,OAAO,OAAO,KAAK,QAAQ,OAAO,SAAS,GAAG,GAAG;AAC5F,QAAE,EAAE,IAAI,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,QAAQ,MAAM,SAAS,GAAG,KAAK,EAAE,MAAM,CAAC,CAAC;AACxE,aAAO;AAAA,IACT,GAAG,CAAC,CAAC,EAAC,IAAI,EAAC,MAAM,MAAM,OAAO,OAAS;AAAA,EACzC,CAAC;AACH;;;ACvBe,SAAR,aAAiBC,QAAO;AAC7B,SAAO,IAAI,SAASA,OAAM,QAAQA,OAAM,YAAYA,OAAM,UAAU;AACtE;;;ACGe,SAAR,YAAiB,QAAQ,SAAS;AACvC,WAAS,MAAM,MAAM;AACrB,SAAO,OAAO,MAAM,EAAE,EAAE,KAAK,SAASC,QAAO;AAC3C,QAAI,OAAO,aAAKA,MAAK;AACrB,WAAO,OAAO,MAAM,KAAK,UAAU,GAAG,IAAI,IAAI,EAAE,EAAE,KAAK,SAASA,QAAO;AACrE,aAAO,IAAI,IAAI,QAAQ,SAAS,MAAM,aAAKA,MAAK,CAAC;AAAA,IACnD,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,IAAI,QAAQ,SAAS,MAAM,MAAM;AACxC,OAAK,UAAU;AACf,OAAK,UAAU,QAAQ,OAAO,KAAK,OAAO;AAC1C,OAAK,gBAAgB,KAAK,UAAU,IAAI,IAAI;AAC5C,OAAK,UAAU,CAAC;AAChB,WAAS,IAAI,GAAG,KAAK,SAAS,CAAC,MAAM,IAAM,KAAK,IAAI;AAClD,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG,KAAI,KAAK,SAAS,IAAI,CAAC,MAAM,EAAG;AAC7D,SAAK,QAAQ,KAAK;AAAA,MAChB,MAAM,KAAK,QAAQ,IAAI,WAAW,KAAK,QAAQ,KAAK,aAAa,GAAG,CAAC,CAAC;AAAA,MACtE,MAAM,OAAO,aAAa,KAAK,SAAS,IAAI,EAAE,CAAC;AAAA,MAC/C,QAAQ,KAAK,SAAS,IAAI,EAAE;AAAA,IAC9B,CAAC;AAAA,EACH;AACF;AAEA,IAAI,YAAY,IAAI;AACpB,UAAU,OAAOC;AACjB,UAAU,SAASC;;;AChCJ,SAAR,SAA0B;AAC/B,SAAO,KAAK,QAAQ,OAAO;AAC7B;;;ACFe,SAAR,mBAAiB,QAAQ;AAC9B,MAAI,IAAI,IAAI,GAAG,IAAI,OAAO,SAAS,IAAI,IAAI,GAAG,cAAc,IAAI,MAAM,CAAC;AACvE,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,GAAI,aAAY,CAAC,IAAI,CAAC,OAAO,WAAW,GAAG,IAAI,GAAG,OAAO,WAAW,IAAI,GAAG,IAAI,CAAC;AAC7G,SAAO,EAAC,MAAM,cAAc,YAAwB;AACtD;;;ACJe,SAAR,eAAmB;AACxB,SAAO;AACT;;;ACFe,SAAR,cAAiB,QAAQ;AAC9B,SAAO,EAAC,MAAM,SAAS,aAAa,CAAC,OAAO,WAAW,GAAG,IAAI,GAAG,OAAO,WAAW,IAAI,IAAI,CAAC,EAAC;AAC/F;;;ACFe,SAAR,gBAAiB,QAAQ;AAC9B,MAAI,IAAI,IAAI,GAAG,IAAI,OAAO,SAAS,IAAI,IAAI,GAAG,IAAI,OAAO,SAAS,IAAI,IAAI,GAAG,QAAQ,IAAI,MAAM,CAAC,GAAG,SAAS,IAAI,MAAM,CAAC,GAAG,WAAW,CAAC,GAAG,QAAQ,CAAC;AAClJ,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,EAAG,OAAM,CAAC,IAAI,OAAO,SAAS,GAAG,IAAI;AAClE,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,GAAI,QAAO,CAAC,IAAI,CAAC,OAAO,WAAW,GAAG,IAAI,GAAG,OAAO,WAAW,IAAI,GAAG,IAAI,CAAC;AAExG,QAAM,QAAQ,SAASC,IAAGC,IAAG;AAC3B,QAAI,OAAO,OAAO,MAAMD,IAAG,MAAMC,KAAI,CAAC,CAAC;AACvC,QAAI,cAAc,IAAI,EAAG,UAAS,KAAK,CAAC,IAAI,CAAC;AAAA,QACxC,OAAM,KAAK,IAAI;AAAA,EACtB,CAAC;AAED,QAAM,QAAQ,SAAS,MAAM;AAC3B,aAAS,KAAK,SAAS,SAAS;AAC9B,UAAI,iBAAiB,QAAQ,CAAC,GAAG,IAAI,GAAG;AACtC,gBAAQ,KAAK,IAAI;AACjB,eAAO;AAAA,MACT;AAAA,IACF,CAAC,KAAK,SAAS,KAAK,CAAC,IAAI,CAAC;AAAA,EAC5B,CAAC;AAED,SAAO,SAAS,WAAW,IACrB,EAAC,MAAM,WAAW,aAAa,SAAS,CAAC,EAAC,IAC1C,EAAC,MAAM,gBAAgB,aAAa,SAAQ;AACpD;AAEA,SAAS,cAAc,MAAM;AAC3B,OAAK,IAAI,KAAK,UAAU,EAAG,QAAO;AAClC,MAAI,IAAI,GAAG,GAAG,OAAO,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC7E,SAAO,EAAE,IAAI,EAAG,SAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAChF,SAAO,QAAQ;AACjB;AAEA,SAAS,iBAAiB,MAAM,MAAM;AACpC,MAAI,IAAI,IAAI,IAAI,KAAK,QAAQ;AAC7B,SAAO,EAAE,IAAI,GAAG;AACd,QAAI,IAAI,aAAa,MAAM,KAAK,CAAC,CAAC,GAAG;AACnC,aAAO,IAAI;AAAA,IACb;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,aAAa,MAAM,OAAO;AACjC,MAAI,IAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,WAAW;AAC3C,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK;AAC1D,QAAI,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GACnC,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC;AACvC,QAAI,gBAAgB,IAAI,IAAI,KAAK,GAAG;AAClC,aAAO;AAAA,IACT;AACA,QAAM,KAAK,MAAQ,KAAK,KAAS,KAAK,KAAK,OAAO,IAAI,OAAO,KAAK,MAAM,IAAM;AAC5E,iBAAW,CAAC;AAAA,IACd;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,IAAI,IAAI,IAAI;AACnC,MAAI,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAC3C,MAAI,QAAQ,KAAK,QAAQ,EAAG,QAAO;AACnC,MAAI,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC;AAC3C,MAAI,QAAQ,KAAK,QAAQ,EAAG,QAAO;AACnC,MAAI,KAAK,MAAM,MAAM,MAAM,QAAQ,MAAM,MAAM,MAAM;AACrD,SAAO,IAAI,KAAK,IAAI,IAAI,QAAQ,MAAM,KAAK,MAAM,IAAI,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ;AAC7F;;;AChEe,SAAR,iBAAiB,QAAQ;AAC9B,MAAI,IAAI,IAAI,GAAG,IAAI,OAAO,SAAS,IAAI,IAAI,GAAG,IAAI,OAAO,SAAS,IAAI,IAAI,GAAG,QAAQ,IAAI,MAAM,CAAC,GAAG,SAAS,IAAI,MAAM,CAAC;AACvH,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,EAAG,OAAM,CAAC,IAAI,OAAO,SAAS,GAAG,IAAI;AAClE,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,GAAI,QAAO,CAAC,IAAI,CAAC,OAAO,WAAW,GAAG,IAAI,GAAG,OAAO,WAAW,IAAI,GAAG,IAAI,CAAC;AACxG,SAAO,MAAM,IACP,EAAC,MAAM,cAAc,aAAa,OAAM,IACxC,EAAC,MAAM,mBAAmB,aAAa,MAAM,IAAI,SAASC,IAAGC,IAAG;AAAE,WAAO,OAAO,MAAMD,IAAG,MAAMC,KAAI,CAAC,CAAC;AAAA,EAAG,CAAC,EAAC;AAClH;;;ACPe,SAAR,eAAiB,GAAG,GAAG;AAC5B,MAAI,KAAK,IAAI,WAAW,EAAE,SAAS,EAAE,MAAM;AAC3C,KAAG,IAAI,GAAG,CAAC;AACX,KAAG,IAAI,GAAG,EAAE,MAAM;AAClB,SAAO;AACT;;;ACFe,SAARC,gBAAmB;AACxB,MAAI,OAAO;AACX,IAAE,KAAK;AACP,SAAO,KAAK,QAAQ,MAAM,EAAE,EAAE,KAAK,SAASC,QAAO;AACjD,QAAIA,UAAS,KAAM,QAAO,EAAC,MAAM,MAAM,OAAO,OAAS;AACvD,QAAI,SAAS,aAAKA,MAAK;AAKvB,aAAS,OAAO;AACd,aAAO,KAAK,QAAQ,MAAM,CAAC,EAAE,KAAK,SAAS,OAAO;AAChD,YAAI,SAAS,KAAM,QAAO,EAAC,MAAM,MAAM,OAAO,OAAS;AACvD,iBAAS,aAAKA,SAAQ,eAAOA,OAAM,MAAM,CAAC,GAAG,KAAK,CAAC;AACnD,eAAO,OAAO,SAAS,GAAG,KAAK,MAAM,KAAK,SAAS,KAAK,IAAIC,MAAK;AAAA,MACnE,CAAC;AAAA,IACH;AAIA,aAASA,QAAO;AACd,UAAI,SAAS,OAAO,SAAS,GAAG,KAAK,IAAI,IAAI,GAAG,OAAO,OAAO,SAAS,GAAG,IAAI;AAC9E,aAAO,SAAS,KAAM,QAAQ,SAAS,KAAK,QAAS,KAAK,IAAI,KAAK,QAAQ,MAAM,MAAM,EAAE,KAAK,SAAS,OAAO;AAC5G,eAAO,EAAC,MAAM,OAAO,OAAO,OAAO,KAAK,OAAO,aAAK,eAAOD,OAAM,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,KAAI;AAAA,MAC5F,CAAC;AAAA,IACH;AAEA,WAAOC,MAAK;AAAA,EACd,CAAC;AACH;;;ACtBA,IAAI,UAAU;AAAA,EACZ,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA;AAAA,EACJ,IAAI;AAAA;AAAA,EACJ,IAAI;AAAA;AAAA,EACJ,IAAI;AAAA;AAAA,EACJ,IAAI;AAAA;AAAA,EACJ,IAAI;AAAA;AAAA,EACJ,IAAI;AAAA;AAAA,EACJ,IAAI;AAAA;AACN;AAEe,SAAR,YAAiB,QAAQ;AAC9B,WAAS,MAAM,MAAM;AACrB,SAAO,OAAO,MAAM,GAAG,EAAE,KAAK,SAASC,QAAO;AAC5C,WAAO,IAAI,IAAI,QAAQ,aAAKA,MAAK,CAAC;AAAA,EACpC,CAAC;AACH;AAEA,SAAS,IAAI,QAAQ,QAAQ;AAC3B,MAAI,OAAO,OAAO,SAAS,IAAI,IAAI;AACnC,MAAI,EAAE,QAAQ,SAAU,OAAM,IAAI,MAAM,6BAA6B,IAAI;AACzE,OAAK,UAAU;AACf,OAAK,QAAQ;AACb,OAAK,SAAS;AACd,OAAK,SAAS,QAAQ,IAAI;AAC1B,OAAK,OAAO,CAAC,OAAO,WAAW,IAAI,IAAI,GAAG,OAAO,WAAW,IAAI,IAAI,GAAG,OAAO,WAAW,IAAI,IAAI,GAAG,OAAO,WAAW,IAAI,IAAI,CAAC;AACjI;AAEA,IAAIC,aAAY,IAAI;AACpBA,WAAU,OAAOC;AACjBD,WAAU,SAAS;;;AC7CnB,SAAS,OAAO;AAAC;AAEF,SAARE,kBAAmB;AACxB,SAAO,QAAQ,IAAI;AAAA,IACjB,KAAK,QAAQ,KAAK,KAAK,OAAO;AAAA,IAC9B,KAAK,KAAK,OAAO;AAAA,EACnB,CAAC,EAAE,KAAK,IAAI;AACd;;;ACPe,SAARC,gBAAmB;AACxB,MAAI,OAAO;AACX,SAAO,QAAQ,IAAI;AAAA,IACjB,KAAK,OAAO,KAAK,KAAK,KAAK,IAAI,EAAC,OAAO,CAAC,EAAC;AAAA,IACzC,KAAK,KAAK,KAAK;AAAA,EACjB,CAAC,EAAE,KAAK,SAAS,SAAS;AACxB,QAAI,MAAM,QAAQ,CAAC,GAAG,MAAM,QAAQ,CAAC;AACrC,WAAO,IAAI,OAAO,MAAM;AAAA,MACtB,MAAM;AAAA,MACN,OAAO;AAAA,QACL,MAAM;AAAA,QACN,YAAY,IAAI;AAAA,QAChB,UAAU,IAAI;AAAA,MAChB;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;ACXe,SAAR,kBAAiB,WAAW,WAAW,SAAS;AACrD,SAAO,QAAQ,IAAI;AAAA,IACjB,YAAI,SAAS;AAAA,IACb,aAAa,YAAI,WAAW,OAAO;AAAA,EACrC,CAAC,EAAE,KAAK,SAAS,SAAS;AACxB,WAAO,IAAI,UAAU,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAAA,EAC7C,CAAC;AACH;AAEA,SAAS,UAAU,KAAK,KAAK;AAC3B,OAAK,OAAO;AACZ,OAAK,OAAO;AACZ,OAAK,OAAO,IAAI;AAClB;AAEA,IAAIC,aAAY,UAAU;AAC1BA,WAAU,OAAOC;AACjBD,WAAU,SAASE;;;ACfZ,SAAS,KAAK,KAAK,KAAK,SAAS;AACtC,MAAI,OAAO,QAAQ,UAAU;AAC3B,QAAI,CAAC,SAAS,KAAK,GAAG,EAAG,QAAO;AAChC,UAAM,KAAK,KAAK,OAAO;AAAA,EACzB,WAAW,eAAe,eAAe,eAAe,YAAY;AAClE,UAAM,MAAM,GAAG;AAAA,EACjB,WAAW,OAAO,MAAM;AACtB,UAAM,OAAO,GAAG;AAAA,EAClB;AACA,MAAI,OAAO,QAAQ,UAAU;AAC3B,QAAI,CAAC,SAAS,KAAK,GAAG,EAAG,QAAO;AAChC,QAAI,QAAQ,OAAW,OAAM,KAAK,IAAI,UAAU,GAAG,IAAI,SAAS,CAAC,IAAI,QAAQ,OAAO,EAAE,MAAM,WAAW;AAAA,IAAC,CAAC;AACzG,UAAM,KAAK,KAAK,OAAO;AAAA,EACzB,WAAW,eAAe,eAAe,eAAe,YAAY;AAClE,UAAM,MAAM,GAAG;AAAA,EACjB,OAAO;AACL,UAAM,OAAO,GAAG;AAAA,EAClB;AACA,SAAO,QAAQ,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,SAAS,SAAS;AACpD,QAAIC,OAAM,QAAQ,CAAC,GAAGC,OAAM,QAAQ,CAAC,GAAG,WAAW;AACnD,QAAI,WAAW,QAAQ,YAAY,KAAM,YAAW,QAAQ;AAC5D,WAAO,kBAAUD,MAAKC,MAAKA,QAAO,IAAI,YAAY,QAAQ,CAAC;AAAA,EAC7D,CAAC;AACH;AAEO,SAAS,QAAQ,QAAQ,SAAS;AACvC,MAAI,OAAO,WAAW,UAAU;AAC9B,QAAI,CAAC,SAAS,KAAK,MAAM,EAAG,WAAU;AACtC,aAAS,KAAK,QAAQ,OAAO;AAAA,EAC/B,WAAW,kBAAkB,eAAe,kBAAkB,YAAY;AACxE,aAAS,MAAM,MAAM;AAAA,EACvB,OAAO;AACL,aAAS,OAAO,MAAM;AAAA,EACxB;AACA,SAAO,QAAQ,QAAQ,MAAM,EAAE,KAAK,WAAG;AACzC;AAEO,SAAS,QAAQ,QAAQ,SAAS;AACvC,MAAI,WAAW;AACf,MAAI,WAAW,QAAQ,YAAY,KAAM,YAAW,QAAQ;AAC5D,aAAW,IAAI,YAAY,QAAQ;AACnC,MAAI,OAAO,WAAW,UAAU;AAC9B,QAAI,CAAC,SAAS,KAAK,MAAM,EAAG,WAAU;AACtC,aAAS,KAAK,QAAQ,OAAO;AAAA,EAC/B,WAAW,kBAAkB,eAAe,kBAAkB,YAAY;AACxE,aAAS,MAAM,MAAM;AAAA,EACvB,OAAO;AACL,aAAS,OAAO,MAAM;AAAA,EACxB;AACA,SAAO,QAAQ,QAAQ,MAAM,EAAE,KAAK,SAASC,SAAQ;AACnD,WAAO,YAAIA,SAAQ,QAAQ;AAAA,EAC7B,CAAC;AACH;AAEO,SAAS,KAAK,KAAK,KAAK,SAAS;AACtC,SAAO,KAAK,KAAK,KAAK,OAAO,EAAE,KAAK,SAAS,QAAQ;AACnD,QAAI,WAAW,CAAC,GAAG,aAAa,EAAC,MAAM,qBAAqB,UAAoB,MAAM,OAAO,KAAI;AACjG,WAAO,OAAO,KAAK,EAAE,KAAK,SAASC,MAAK,QAAQ;AAC9C,UAAI,OAAO,KAAM,QAAO;AACxB,eAAS,KAAK,OAAO,KAAK;AAC1B,aAAO,OAAO,KAAK,EAAE,KAAKA,KAAI;AAAA,IAChC,CAAC;AAAA,EACH,CAAC;AACH;", "names": ["array", "array", "path", "cancel_default", "read_default", "array", "array", "read", "read_default", "cancel_default", "cancel_default", "read_default", "array", "array", "read_default", "cancel_default", "i", "j", "i", "j", "read_default", "array", "read", "array", "prototype", "read_default", "cancel_default", "read_default", "prototype", "read_default", "cancel_default", "shp", "dbf", "source", "read"]}