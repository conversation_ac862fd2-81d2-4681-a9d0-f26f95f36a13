import { computed, reactive, watch } from 'vue';
import { useMapLayerManagerStore } from '/@/stores/olMapLayer/mapLayerManager';

// 图层列表状态 - 使用响应式对象而非计算属性，以避免在toggle时自动更新顺序
export const layerListState = reactive({
  items: [] as any[]
});

// 已加载图层列表
export const loadedLayers = computed(() => {
  const mapLayerStore = useMapLayerManagerStore();
  // 从 mapLayerStore 获取最新的已加载图层信息
  const layers = mapLayerStore.mapConfig?.layers || [];
  
  // 获取已加载的图层，并按照zIndex降序排列（zIndex高的在前面/上层）
  const loadedLayersList = layers
    .filter(layer => mapLayerStore.isLayerLoaded(layer.id))
    .sort((a, b) => {
      // 获取图层实例的zIndex
      const aZIndex = a.layerInstance?.getZIndex() || 0;
      const bZIndex = b.layerInstance?.getZIndex() || 0;
      return bZIndex - aZIndex; // 降序，高zIndex在前
    })
    .map(layer => {
      // 返回包含所有必要属性的对象
      return {
        id: layer.id,
        name: layer.name,
        type: layer.type,
        protocol: layer.protocol,
        visible: layer.active !== false, // 默认可见
        geometryType: layer.geometryType,
        originalLayer: layer  // 保留原始图层引用，确保能够访问到所有属性
      };
    });
    
  return loadedLayersList;
});

// 未加载图层列表
export const unloadedLayers = computed(() => {
  const mapLayerStore = useMapLayerManagerStore();
  const layers = mapLayerStore.mapConfig?.layers || [];
  const unloadedLayersList = layers.filter(layer => !mapLayerStore.isLayerLoaded(layer.id));
  
  // 按theme分组未加载的图层
  const themeGroups: Record<string, any[]> = {};
  const noThemeLayers: any[] = [];
  
  // 对未加载的图层进行分组
  unloadedLayersList.forEach(layer => {
    const layerObj = {
      id: layer.id,
      name: layer.name,
      type: layer.type,
      protocol: layer.protocol,
      originalLayer: layer  // 保留原始图层引用
    };
    
    // 如果有theme属性，则按theme分组
    if (layer.theme) {
      if (!themeGroups[layer.theme]) {
        themeGroups[layer.theme] = [];
      }
      themeGroups[layer.theme].push(layerObj);
    } else {
      // 没有theme的图层直接加入结果
      noThemeLayers.push(layerObj);
    }
  });
  
  // 构建最终结果，将每个theme作为一级节点
  const result: any[] = [];
  
  // 添加按主题分组的图层
  Object.keys(themeGroups).forEach(theme => {
    result.push({
      id: `theme-${theme}`,
      name: theme,
      isTheme: true,
      children: themeGroups[theme]
    });
  });
  
  // 添加没有主题的图层
  result.push(...noThemeLayers);
  
  return result;
});

// 整合图层列表（包含所有图层，按主题分组）
export const allLayers = computed(() => {
  const mapLayerStore = useMapLayerManagerStore();
  const layers = mapLayerStore.mapConfig?.layers || [];
  
  // 按主题分组所有图层
  const themeGroups: Record<string, any[]> = {};
  const noThemeLayers: any[] = [];
  
  // 对所有图层进行分组，同时标记是否已加载
  layers.forEach(layer => {
    const layerObj = {
      id: layer.id,
      name: layer.name,
      type: layer.type,
      protocol: layer.protocol,
      loaded: mapLayerStore.isLayerLoaded(layer.id),
      visible: layer.active !== false,
      geometryType: layer.geometryType,
      originalLayer: layer  // 保留原始图层引用
    };
    
    // 如果有theme属性，则按theme分组
    if (layer.theme) {
      if (!themeGroups[layer.theme]) {
        themeGroups[layer.theme] = [];
      }
      themeGroups[layer.theme].push(layerObj);
    } else {
      // 没有theme的图层直接加入结果
      noThemeLayers.push(layerObj);
    }
  });
  
  // 构建最终结果，将每个theme作为一级节点
  const result: any[] = [];
  
  // 添加按主题分组的图层
  Object.keys(themeGroups).forEach(theme => {
    const children = themeGroups[theme];
    // 计算主题下图层的加载状态
    const loadedCount = children.filter(layer => layer.loaded).length;
    
    result.push({
      id: `theme-${theme}`,
      name: theme,
      isTheme: true,
      loaded: loadedCount === children.length, // 当所有子图层都加载时，主题复选框为选中状态
      indeterminate: loadedCount > 0 && loadedCount < children.length, // 部分加载时为半选状态
      children
    });
  });
  
  // 添加没有主题的图层
  result.push(...noThemeLayers);
  
  return result;
});

// 根据图层类型获取图标
export function getLayerIcon(type: string) {
  switch(type) {
    case 'vector': return 'el-icon-picture-outline';
    case 'raster': return 'el-icon-picture';
    default: return 'el-icon-map-location';
  }
}

// 初始化图层列表
export function initLayerList() {
  const mapLayerStore = useMapLayerManagerStore();
  
  if (!mapLayerStore.mapConfig) {
    layerListState.items = [];
    return 0;
  }
  
  // 创建图层项数组，按照zIndex降序排列（zIndex高的在前面/上层）
  const layers = [...mapLayerStore.mapConfig.layers]
    .filter(layer => mapLayerStore.loadedLayers.includes(layer.id));
    
  // 按图层的显示顺序排序（查找图层的OL实例，获取zIndex）
  layers.sort((a, b) => {
    const aZIndex = a.layerInstance?.getZIndex() || 0;
    const bZIndex = b.layerInstance?.getZIndex() || 0;
    return bZIndex - aZIndex; // 降序，高zIndex在前
  });
  
  // 检查是否有图层丢失
  if (layers.length !== mapLayerStore.loadedLayers.length) {
    console.warn(`图层列表初始化警告: 预期 ${mapLayerStore.loadedLayers.length} 个图层，但只找到 ${layers.length} 个`);
    // 尝试恢复缺失的图层
    const missingLayerIds = mapLayerStore.loadedLayers.filter(
      id => !layers.some(layer => layer.id === id)
    );
    
    if (missingLayerIds.length > 0) {
      console.warn('尝试恢复缺失的图层:', missingLayerIds);
      missingLayerIds.forEach(id => {
        const layer = mapLayerStore.getLayerById(id);
        if (layer) {
          layers.push(layer);
        }
      });
    }
  }
  
  // 映射为树节点数据
  layerListState.items = layers.map(layer => {
    // 获取完整的图层信息
    const fullLayer = mapLayerStore.getLayerById(layer.id);
    let geometryType = fullLayer?.geometryType;
    
    // 提取样式信息
    let defaultStyle = fullLayer?.defaultStyle;
    
    // 如果defaultStyle是字符串（样式名称），从styleLibrary获取
    if (typeof defaultStyle === 'string') {
      defaultStyle = mapLayerStore.styleLibrary[defaultStyle] || {};
    }
    
    // 对于矢量图层，如果没有指定几何类型，尝试从图层实例的第一个要素中提取
    if (layer.type === 'vector' && !geometryType && layer.layerInstance) {
      // 尝试获取第一个要素的几何类型
      const source = layer.layerInstance.getSource();
      if (source && typeof source.getFeatures === 'function') {
        const features = source.getFeatures();
        if (features && features.length > 0) {
          // 获取第一个要素的几何类型
          const geometry = features[0].getGeometry();
          if (geometry) {
            geometryType = geometry.getType();
          }
        }
      }
    }
    
    return {
      id: layer.id,
      name: layer.name,
      type: layer.type,
      protocol: layer.protocol,
      visible: layer.active !== false,
      icon: getLayerIcon(layer.type),
      originalLayer: {
        ...fullLayer,
        geometryType: geometryType || fullLayer?.geometryType,
        defaultStyle: defaultStyle || fullLayer?.defaultStyle
      }
    };
  });
  
  console.log('图层列表已初始化，共', layerListState.items.length, '项');
  return layerListState.items.length;
}

/**
 * 刷新图层列表
 * 在图层状态变化后调用此函数以更新UI显示
 */
export function refreshLayerList() {
  // 先获取最新图层状态
  initLayerList();
  console.log('图层列表已刷新');
}

// 更新单个图层的状态
export function updateLayerItem(layerId: string, properties: Partial<any>) {
  const item = layerListState.items.find(item => item.id === layerId);
  if (item) {
    Object.assign(item, properties);
  }
}

// 重新排序图层
export function reorderLayers(newOrder: string[]) {
  const mapLayerStore = useMapLayerManagerStore();
  
  if (!mapLayerStore.olMap) {
    console.error('地图实例不存在，无法重排图层');
    return 0;
  }
  
  let successCount = 0;
  
  // 计算最大zIndex值，确保新顺序中的图层zIndex值是连续的
  const maxZIndex = mapLayerStore.getMaxZIndex();
  
  // 根据新顺序设置图层的zIndex
  // 列表中越靠前的图层，zIndex越大，显示在越上方
  newOrder.forEach((layerId, index) => {
    const layer = mapLayerStore.getLayerById(layerId);
    if (layer && layer.layerInstance) {
      // 计算新的zIndex，列表顶部的图层zIndex最大
      const newZIndex = maxZIndex - index;
      
      // 设置图层的zIndex
      layer.layerInstance.setZIndex(newZIndex);
      successCount++;
    }
  });
  
  return successCount;
}

// 单独添加图层
export async function addLayerOnTop(layerId: string) {
  const mapLayerStore = useMapLayerManagerStore();
  
  try {
    // 检查图层是否存在
    const layerConfig = mapLayerStore.getLayerById(layerId);
    if (!layerConfig) {
      console.error(`添加图层失败: ID为 ${layerId} 的图层不存在`);
      return false;
    }
    
    // 确保图层实例已清理（如果之前被移除）
    if (layerConfig.layerInstance) {
      console.log(`图层 ${layerId} 可能已经存在实例，尝试先移除`);
          mapLayerStore.removeLayer(layerId);
    }
    
    // 添加图层到地图
    const success = await mapLayerStore.addLayer(layerId);
    
    if (!success) {
      console.error(`添加图层 ${layerId} 失败`);
      return false;
    }
    
    // 将新添加的图层移到最上层（最大zIndex）
    const layer = mapLayerStore.getLayerById(layerId);
    if (layer && layer.layerInstance) {
      // 获取当前最大zIndex并加1
      const maxZIndex = mapLayerStore.getMaxZIndex();
        layer.layerInstance.setZIndex(maxZIndex + 1);
      console.log(`图层 ${layerId} 已设置为最上层，zIndex = ${maxZIndex + 1}`);
    }
    
    return true;
  } catch (error) {
    console.error('添加图层出错:', error);
    return false;
  }
}

// 添加同一主题的所有图层
export async function addThemeLayers(theme: string) {
  const mapLayerStore = useMapLayerManagerStore();
  const layers = mapLayerStore.mapConfig?.layers || [];
  
  // 找出属于该主题的所有图层
  const themeLayers = layers.filter(layer => layer.theme === theme);
  
  if (themeLayers.length === 0) {
    console.warn(`未找到主题为 ${theme} 的图层`);
    return 0;
  }
  
  let successCount = 0;
  
  // 依次添加每个图层
  for (const layer of themeLayers) {
    // 检查图层是否已经加载
    if (mapLayerStore.isLayerLoaded(layer.id)) {
      console.log(`图层 ${layer.id} 已加载，跳过`);
      successCount++;
      continue;
    }
    
    // 添加图层
      const success = await addLayerOnTop(layer.id);
      if (success) {
        successCount++;
      }
  }
  
  return successCount;
}

// 移除同一主题的所有图层
export function removeThemeLayers(theme: string) {
  const mapLayerStore = useMapLayerManagerStore();
  const layers = mapLayerStore.mapConfig?.layers || [];
  
  // 找出属于该主题的所有已加载图层
  const themeLayerIds = layers
    .filter(layer => layer.theme === theme && mapLayerStore.isLayerLoaded(layer.id))
    .map(layer => layer.id);
  
  if (themeLayerIds.length === 0) {
    console.warn(`未找到已加载的主题 ${theme} 图层`);
    return 0;
  }
  
  let successCount = 0;
  
  // 依次移除每个图层
  themeLayerIds.forEach(layerId => {
    const success = mapLayerStore.removeLayer(layerId);
    if (success) {
      successCount++;
    }
  });
  
  return successCount;
}

// 判断某个主题的所有图层是否已加载
export function isThemeFullyLoaded(theme: string): boolean {
  const mapLayerStore = useMapLayerManagerStore();
  const layers = mapLayerStore.mapConfig?.layers || [];
  
  // 找出属于该主题的所有图层
  const themeLayers = layers.filter(layer => layer.theme === theme);
  
  if (themeLayers.length === 0) {
    return false;
  }
  
  // 检查是否所有图层都已加载
  return themeLayers.every(layer => mapLayerStore.isLayerLoaded(layer.id));
}

// 判断某个主题的图层是否部分加载
export function isThemePartiallyLoaded(theme: string): boolean {
  const mapLayerStore = useMapLayerManagerStore();
  const layers = mapLayerStore.mapConfig?.layers || [];
  
  // 找出属于该主题的所有图层
  const themeLayers = layers.filter(layer => layer.theme === theme);
  
  if (themeLayers.length === 0) {
    return false;
  }
  
  // 检查是否有部分图层已加载
  const loadedCount = themeLayers.filter(layer => mapLayerStore.isLayerLoaded(layer.id)).length;
  return loadedCount > 0 && loadedCount < themeLayers.length;
} 