{"version": 3, "sources": ["../../ol/ViewHint.js", "../../ol/ViewProperty.js", "../../ol/centerconstraint.js", "../../ol/resolutionconstraint.js", "../../ol/rotationconstraint.js", "../../ol/View.js"], "sourcesContent": ["/**\n * @module ol/ViewHint\n */\n\n/**\n * @enum {number}\n */\nexport default {\n  ANIMATING: 0,\n  INTERACTING: 1,\n};\n", "/**\n * @module ol/ViewProperty\n */\n\n/**\n * @enum {string}\n */\nexport default {\n  CENTER: 'center',\n  RESOLUTION: 'resolution',\n  ROTATION: 'rotation',\n};\n", "/**\n * @module ol/centerconstraint\n */\nimport {clamp} from './math.js';\n\n/**\n * @typedef {function((import(\"./coordinate.js\").Coordinate|undefined), number, import(\"./size.js\").Size, boolean=, Array<number>=): (import(\"./coordinate.js\").Coordinate|undefined)} Type\n */\n\n/**\n * @param {import(\"./extent.js\").Extent} extent Extent.\n * @param {boolean} onlyCenter If true, the constraint will only apply to the view center.\n * @param {boolean} smooth If true, the view will be able to go slightly out of the given extent\n * (only during interaction and animation).\n * @return {Type} The constraint.\n */\nexport function createExtent(extent, onlyCenter, smooth) {\n  return (\n    /**\n     * @param {import(\"./coordinate.js\").Coordinate|undefined} center Center.\n     * @param {number|undefined} resolution Resolution.\n     * @param {import(\"./size.js\").Size} size Viewport size; unused if `onlyCenter` was specified.\n     * @param {boolean} [isMoving] True if an interaction or animation is in progress.\n     * @param {Array<number>} [centerShift] Shift between map center and viewport center.\n     * @return {import(\"./coordinate.js\").Coordinate|undefined} Center.\n     */\n    function (center, resolution, size, isMoving, centerShift) {\n      if (!center) {\n        return undefined;\n      }\n      if (!resolution && !onlyCenter) {\n        return center;\n      }\n      const viewWidth = onlyCenter ? 0 : size[0] * resolution;\n      const viewHeight = onlyCenter ? 0 : size[1] * resolution;\n      const shiftX = centerShift ? centerShift[0] : 0;\n      const shiftY = centerShift ? centerShift[1] : 0;\n      let minX = extent[0] + viewWidth / 2 + shiftX;\n      let maxX = extent[2] - viewWidth / 2 + shiftX;\n      let minY = extent[1] + viewHeight / 2 + shiftY;\n      let maxY = extent[3] - viewHeight / 2 + shiftY;\n\n      // note: when zooming out of bounds, min and max values for x and y may\n      // end up inverted (min > max); this has to be accounted for\n      if (minX > maxX) {\n        minX = (maxX + minX) / 2;\n        maxX = minX;\n      }\n      if (minY > maxY) {\n        minY = (maxY + minY) / 2;\n        maxY = minY;\n      }\n\n      let x = clamp(center[0], minX, maxX);\n      let y = clamp(center[1], minY, maxY);\n\n      // during an interaction, allow some overscroll\n      if (isMoving && smooth && resolution) {\n        const ratio = 30 * resolution;\n        x +=\n          -ratio * Math.log(1 + Math.max(0, minX - center[0]) / ratio) +\n          ratio * Math.log(1 + Math.max(0, center[0] - maxX) / ratio);\n        y +=\n          -ratio * Math.log(1 + Math.max(0, minY - center[1]) / ratio) +\n          ratio * Math.log(1 + Math.max(0, center[1] - maxY) / ratio);\n      }\n\n      return [x, y];\n    }\n  );\n}\n\n/**\n * @param {import(\"./coordinate.js\").Coordinate} [center] Center.\n * @return {import(\"./coordinate.js\").Coordinate|undefined} Center.\n */\nexport function none(center) {\n  return center;\n}\n", "/**\n * @module ol/resolutionconstraint\n */\nimport {clamp} from './math.js';\nimport {getHeight, getWidth} from './extent.js';\nimport {linearFindNearest} from './array.js';\n\n/**\n * @typedef {function((number|undefined), number, import(\"./size.js\").Size, boolean=): (number|undefined)} Type\n */\n\n/**\n * Returns a modified resolution taking into account the viewport size and maximum\n * allowed extent.\n * @param {number} resolution Resolution\n * @param {import(\"./extent.js\").Extent} maxExtent Maximum allowed extent.\n * @param {import(\"./size.js\").Size} viewportSize Viewport size.\n * @param {boolean} showFullExtent Whether to show the full extent.\n * @return {number} Capped resolution.\n */\nfunction getViewportClampedResolution(\n  resolution,\n  maxExtent,\n  viewportSize,\n  showFullExtent\n) {\n  const xResolution = getWidth(maxExtent) / viewportSize[0];\n  const yResolution = getHeight(maxExtent) / viewportSize[1];\n\n  if (showFullExtent) {\n    return Math.min(resolution, Math.max(xResolution, yResolution));\n  }\n  return Math.min(resolution, Math.min(xResolution, yResolution));\n}\n\n/**\n * Returns a modified resolution to be between maxResolution and minResolution while\n * still allowing the value to be slightly out of bounds.\n * Note: the computation is based on the logarithm function (ln):\n *  - at 1, ln(x) is 0\n *  - above 1, ln(x) keeps increasing but at a much slower pace than x\n * The final result is clamped to prevent getting too far away from bounds.\n * @param {number} resolution Resolution.\n * @param {number} maxResolution Max resolution.\n * @param {number} minResolution Min resolution.\n * @return {number} Smoothed resolution.\n */\nfunction getSmoothClampedResolution(resolution, maxResolution, minResolution) {\n  let result = Math.min(resolution, maxResolution);\n  const ratio = 50;\n\n  result *=\n    Math.log(1 + ratio * Math.max(0, resolution / maxResolution - 1)) / ratio +\n    1;\n  if (minResolution) {\n    result = Math.max(result, minResolution);\n    result /=\n      Math.log(1 + ratio * Math.max(0, minResolution / resolution - 1)) /\n        ratio +\n      1;\n  }\n  return clamp(result, minResolution / 2, maxResolution * 2);\n}\n\n/**\n * @param {Array<number>} resolutions Resolutions.\n * @param {boolean} [smooth] If true, the view will be able to slightly exceed resolution limits. Default: true.\n * @param {import(\"./extent.js\").Extent} [maxExtent] Maximum allowed extent.\n * @param {boolean} [showFullExtent] If true, allows us to show the full extent. Default: false.\n * @return {Type} Zoom function.\n */\nexport function createSnapToResolutions(\n  resolutions,\n  smooth,\n  maxExtent,\n  showFullExtent\n) {\n  smooth = smooth !== undefined ? smooth : true;\n  return (\n    /**\n     * @param {number|undefined} resolution Resolution.\n     * @param {number} direction Direction.\n     * @param {import(\"./size.js\").Size} size Viewport size.\n     * @param {boolean} [isMoving] True if an interaction or animation is in progress.\n     * @return {number|undefined} Resolution.\n     */\n    function (resolution, direction, size, isMoving) {\n      if (resolution !== undefined) {\n        const maxResolution = resolutions[0];\n        const minResolution = resolutions[resolutions.length - 1];\n        const cappedMaxRes = maxExtent\n          ? getViewportClampedResolution(\n              maxResolution,\n              maxExtent,\n              size,\n              showFullExtent\n            )\n          : maxResolution;\n\n        // during interacting or animating, allow intermediary values\n        if (isMoving) {\n          if (!smooth) {\n            return clamp(resolution, minResolution, cappedMaxRes);\n          }\n          return getSmoothClampedResolution(\n            resolution,\n            cappedMaxRes,\n            minResolution\n          );\n        }\n\n        const capped = Math.min(cappedMaxRes, resolution);\n        const z = Math.floor(linearFindNearest(resolutions, capped, direction));\n        if (resolutions[z] > cappedMaxRes && z < resolutions.length - 1) {\n          return resolutions[z + 1];\n        }\n        return resolutions[z];\n      }\n      return undefined;\n    }\n  );\n}\n\n/**\n * @param {number} power Power.\n * @param {number} maxResolution Maximum resolution.\n * @param {number} [minResolution] Minimum resolution.\n * @param {boolean} [smooth] If true, the view will be able to slightly exceed resolution limits. Default: true.\n * @param {import(\"./extent.js\").Extent} [maxExtent] Maximum allowed extent.\n * @param {boolean} [showFullExtent] If true, allows us to show the full extent. Default: false.\n * @return {Type} Zoom function.\n */\nexport function createSnapToPower(\n  power,\n  maxResolution,\n  minResolution,\n  smooth,\n  maxExtent,\n  showFullExtent\n) {\n  smooth = smooth !== undefined ? smooth : true;\n  minResolution = minResolution !== undefined ? minResolution : 0;\n\n  return (\n    /**\n     * @param {number|undefined} resolution Resolution.\n     * @param {number} direction Direction.\n     * @param {import(\"./size.js\").Size} size Viewport size.\n     * @param {boolean} [isMoving] True if an interaction or animation is in progress.\n     * @return {number|undefined} Resolution.\n     */\n    function (resolution, direction, size, isMoving) {\n      if (resolution !== undefined) {\n        const cappedMaxRes = maxExtent\n          ? getViewportClampedResolution(\n              maxResolution,\n              maxExtent,\n              size,\n              showFullExtent\n            )\n          : maxResolution;\n\n        // during interacting or animating, allow intermediary values\n        if (isMoving) {\n          if (!smooth) {\n            return clamp(resolution, minResolution, cappedMaxRes);\n          }\n          return getSmoothClampedResolution(\n            resolution,\n            cappedMaxRes,\n            minResolution\n          );\n        }\n\n        const tolerance = 1e-9;\n        const minZoomLevel = Math.ceil(\n          Math.log(maxResolution / cappedMaxRes) / Math.log(power) - tolerance\n        );\n        const offset = -direction * (0.5 - tolerance) + 0.5;\n        const capped = Math.min(cappedMaxRes, resolution);\n        const cappedZoomLevel = Math.floor(\n          Math.log(maxResolution / capped) / Math.log(power) + offset\n        );\n        const zoomLevel = Math.max(minZoomLevel, cappedZoomLevel);\n        const newResolution = maxResolution / Math.pow(power, zoomLevel);\n        return clamp(newResolution, minResolution, cappedMaxRes);\n      }\n      return undefined;\n    }\n  );\n}\n\n/**\n * @param {number} maxResolution Max resolution.\n * @param {number} minResolution Min resolution.\n * @param {boolean} [smooth] If true, the view will be able to slightly exceed resolution limits. Default: true.\n * @param {import(\"./extent.js\").Extent} [maxExtent] Maximum allowed extent.\n * @param {boolean} [showFullExtent] If true, allows us to show the full extent. Default: false.\n * @return {Type} Zoom function.\n */\nexport function createMinMaxResolution(\n  maxResolution,\n  minResolution,\n  smooth,\n  maxExtent,\n  showFullExtent\n) {\n  smooth = smooth !== undefined ? smooth : true;\n\n  return (\n    /**\n     * @param {number|undefined} resolution Resolution.\n     * @param {number} direction Direction.\n     * @param {import(\"./size.js\").Size} size Viewport size.\n     * @param {boolean} [isMoving] True if an interaction or animation is in progress.\n     * @return {number|undefined} Resolution.\n     */\n    function (resolution, direction, size, isMoving) {\n      if (resolution !== undefined) {\n        const cappedMaxRes = maxExtent\n          ? getViewportClampedResolution(\n              maxResolution,\n              maxExtent,\n              size,\n              showFullExtent\n            )\n          : maxResolution;\n\n        if (!smooth || !isMoving) {\n          return clamp(resolution, minResolution, cappedMaxRes);\n        }\n        return getSmoothClampedResolution(\n          resolution,\n          cappedMaxRes,\n          minResolution\n        );\n      }\n      return undefined;\n    }\n  );\n}\n", "/**\n * @module ol/rotationconstraint\n */\nimport {toRadians} from './math.js';\n\n/**\n * @typedef {function((number|undefined), boolean=): (number|undefined)} Type\n */\n\n/**\n * @param {number|undefined} rotation Rotation.\n * @return {number|undefined} Rotation.\n */\nexport function disable(rotation) {\n  if (rotation !== undefined) {\n    return 0;\n  }\n  return undefined;\n}\n\n/**\n * @param {number|undefined} rotation Rotation.\n * @return {number|undefined} Rotation.\n */\nexport function none(rotation) {\n  if (rotation !== undefined) {\n    return rotation;\n  }\n  return undefined;\n}\n\n/**\n * @param {number} n N.\n * @return {Type} Rotation constraint.\n */\nexport function createSnapToN(n) {\n  const theta = (2 * Math.PI) / n;\n  return (\n    /**\n     * @param {number|undefined} rotation Rotation.\n     * @param {boolean} [isMoving] True if an interaction or animation is in progress.\n     * @return {number|undefined} Rotation.\n     */\n    function (rotation, isMoving) {\n      if (isMoving) {\n        return rotation;\n      }\n\n      if (rotation !== undefined) {\n        rotation = Math.floor(rotation / theta + 0.5) * theta;\n        return rotation;\n      }\n      return undefined;\n    }\n  );\n}\n\n/**\n * @param {number} [tolerance] Tolerance.\n * @return {Type} Rotation constraint.\n */\nexport function createSnapToZero(tolerance) {\n  tolerance = tolerance || toRadians(5);\n  return (\n    /**\n     * @param {number|undefined} rotation Rotation.\n     * @param {boolean} [isMoving] True if an interaction or animation is in progress.\n     * @return {number|undefined} Rotation.\n     */\n    function (rotation, isMoving) {\n      if (isMoving) {\n        return rotation;\n      }\n\n      if (rotation !== undefined) {\n        if (Math.abs(rotation) <= tolerance) {\n          return 0;\n        }\n        return rotation;\n      }\n      return undefined;\n    }\n  );\n}\n", "/**\n * @module ol/View\n */\nimport BaseObject from './Object.js';\nimport ViewHint from './ViewHint.js';\nimport ViewProperty from './ViewProperty.js';\nimport {DEFAULT_TILE_SIZE} from './tilegrid/common.js';\nimport {\n  METERS_PER_UNIT,\n  createProjection,\n  disableCoordinateWarning,\n  fromUserCoordinate,\n  fromUserExtent,\n  getUserProjection,\n  toUserCoordinate,\n  toUserExtent,\n} from './proj.js';\nimport {VOID} from './functions.js';\nimport {\n  add as addCoordinate,\n  equals as coordinatesEqual,\n  equals,\n  rotate as rotateCoordinate,\n} from './coordinate.js';\nimport {assert} from './asserts.js';\nimport {none as centerNone, createExtent} from './centerconstraint.js';\nimport {clamp, modulo} from './math.js';\nimport {\n  createMinMaxResolution,\n  createSnapToPower,\n  createSnapToResolutions,\n} from './resolutionconstraint.js';\nimport {\n  createSnapToN,\n  createSnapToZero,\n  disable,\n  none as rotationNone,\n} from './rotationconstraint.js';\nimport {easeOut, inAndOut} from './easing.js';\nimport {\n  getCenter,\n  getForViewAndSize,\n  getHeight,\n  getWidth,\n  isEmpty,\n} from './extent.js';\nimport {linearFindNearest} from './array.js';\nimport {fromExtent as polygonFromExtent} from './geom/Polygon.js';\n\n/**\n * An animation configuration\n *\n * @typedef {Object} Animation\n * @property {import(\"./coordinate.js\").Coordinate} [sourceCenter] Source center.\n * @property {import(\"./coordinate.js\").Coordinate} [targetCenter] Target center.\n * @property {number} [sourceResolution] Source resolution.\n * @property {number} [targetResolution] Target resolution.\n * @property {number} [sourceRotation] Source rotation.\n * @property {number} [targetRotation] Target rotation.\n * @property {import(\"./coordinate.js\").Coordinate} [anchor] Anchor.\n * @property {number} start Start.\n * @property {number} duration Duration.\n * @property {boolean} complete Complete.\n * @property {function(number):number} easing Easing.\n * @property {function(boolean):void} callback Callback.\n */\n\n/**\n * @typedef {Object} Constraints\n * @property {import(\"./centerconstraint.js\").Type} center Center.\n * @property {import(\"./resolutionconstraint.js\").Type} resolution Resolution.\n * @property {import(\"./rotationconstraint.js\").Type} rotation Rotation.\n */\n\n/**\n * @typedef {Object} FitOptions\n * @property {import(\"./size.js\").Size} [size] The size in pixels of the box to fit\n * the extent into. Default is the current size of the first map in the DOM that\n * uses this view, or `[100, 100]` if no such map is found.\n * @property {!Array<number>} [padding=[0, 0, 0, 0]] Padding (in pixels) to be\n * cleared inside the view. Values in the array are top, right, bottom and left\n * padding.\n * @property {boolean} [nearest=false] If the view `constrainResolution` option is `true`,\n * get the nearest extent instead of the closest that actually fits the view.\n * @property {number} [minResolution=0] Minimum resolution that we zoom to.\n * @property {number} [maxZoom] Maximum zoom level that we zoom to. If\n * `minResolution` is given, this property is ignored.\n * @property {number} [duration] The duration of the animation in milliseconds.\n * By default, there is no animation to the target extent.\n * @property {function(number):number} [easing] The easing function used during\n * the animation (defaults to {@link module:ol/easing.inAndOut}).\n * The function will be called for each frame with a number representing a\n * fraction of the animation's duration.  The function should return a number\n * between 0 and 1 representing the progress toward the destination state.\n * @property {function(boolean):void} [callback] Function called when the view is in\n * its final position. The callback will be called with `true` if the animation\n * series completed on its own or `false` if it was cancelled.\n */\n\n/**\n * @typedef {Object} ViewOptions\n * @property {import(\"./coordinate.js\").Coordinate} [center] The initial center for\n * the view. If a user projection is not set, the coordinate system for the center is\n * specified with the `projection` option. Layer sources will not be fetched if this\n * is not set, but the center can be set later with {@link #setCenter}.\n * @property {boolean|number} [constrainRotation=true] Rotation constraint.\n * `false` means no constraint. `true` means no constraint, but snap to zero\n * near zero. A number constrains the rotation to that number of values. For\n * example, `4` will constrain the rotation to 0, 90, 180, and 270 degrees.\n * @property {boolean} [enableRotation=true] Enable rotation.\n * If `false`, a rotation constraint that always sets the rotation to zero is\n * used. The `constrainRotation` option has no effect if `enableRotation` is\n * `false`.\n * @property {import(\"./extent.js\").Extent} [extent] The extent that constrains the\n * view, in other words, nothing outside of this extent can be visible on the map.\n * @property {boolean} [constrainOnlyCenter=false] If true, the extent\n * constraint will only apply to the view center and not the whole extent.\n * @property {boolean} [smoothExtentConstraint=true] If true, the extent\n * constraint will be applied smoothly, i.e. allow the view to go slightly outside\n * of the given `extent`.\n * @property {number} [maxResolution] The maximum resolution used to determine\n * the resolution constraint. It is used together with `minResolution` (or\n * `maxZoom`) and `zoomFactor`. If unspecified it is calculated in such a way\n * that the projection's validity extent fits in a 256x256 px tile. If the\n * projection is Spherical Mercator (the default) then `maxResolution` defaults\n * to `40075016.68557849 / 256 = 156543.03392804097`.\n * @property {number} [minResolution] The minimum resolution used to determine\n * the resolution constraint.  It is used together with `maxResolution` (or\n * `minZoom`) and `zoomFactor`.  If unspecified it is calculated assuming 29\n * zoom levels (with a factor of 2). If the projection is Spherical Mercator\n * (the default) then `minResolution` defaults to\n * `40075016.68557849 / 256 / Math.pow(2, 28) = 0.0005831682455839253`.\n * @property {number} [maxZoom=28] The maximum zoom level used to determine the\n * resolution constraint. It is used together with `minZoom` (or\n * `maxResolution`) and `zoomFactor`.  Note that if `minResolution` is also\n * provided, it is given precedence over `maxZoom`.\n * @property {number} [minZoom=0] The minimum zoom level used to determine the\n * resolution constraint. It is used together with `maxZoom` (or\n * `minResolution`) and `zoomFactor`.  Note that if `maxResolution` is also\n * provided, it is given precedence over `minZoom`.\n * @property {boolean} [multiWorld=false] If `false` the view is constrained so\n * only one world is visible, and you cannot pan off the edge.  If `true` the map\n * may show multiple worlds at low zoom levels.  Only used if the `projection` is\n * global.  Note that if `extent` is also provided it is given precedence.\n * @property {boolean} [constrainResolution=false] If true, the view will always\n * animate to the closest zoom level after an interaction; false means\n * intermediary zoom levels are allowed.\n * @property {boolean} [smoothResolutionConstraint=true] If true, the resolution\n * min/max values will be applied smoothly, i. e. allow the view to exceed slightly\n * the given resolution or zoom bounds.\n * @property {boolean} [showFullExtent=false] Allow the view to be zoomed out to\n * show the full configured extent. By default, when a view is configured with an\n * extent, users will not be able to zoom out so the viewport exceeds the extent in\n * either dimension. This means the full extent may not be visible if the viewport\n * is taller or wider than the aspect ratio of the configured extent. If\n * showFullExtent is true, the user will be able to zoom out so that the viewport\n * exceeds the height or width of the configured extent, but not both, allowing the\n * full extent to be shown.\n * @property {import(\"./proj.js\").ProjectionLike} [projection='EPSG:3857'] The\n * projection. The default is Spherical Mercator.\n * @property {number} [resolution] The initial resolution for the view. The\n * units are `projection` units per pixel (e.g. meters per pixel). An\n * alternative to setting this is to set `zoom`. Layer sources will not be\n * fetched if neither this nor `zoom` are defined, but they can be set later\n * with {@link #setZoom} or {@link #setResolution}.\n * @property {Array<number>} [resolutions] Resolutions that determine the\n * zoom levels if specified. The index in the array corresponds to the zoom level,\n * therefore the resolution values have to be in descending order. It also constrains\n * the resolution by the minimum and maximum value. If set the `maxResolution`,\n * `minResolution`, `minZoom`, `maxZoom`, and `zoomFactor` options are ignored.\n * @property {number} [rotation=0] The initial rotation for the view in radians\n * (positive rotation clockwise, 0 means North).\n * @property {number} [zoom] Only used if `resolution` is not defined. Zoom\n * level used to calculate the initial resolution for the view.\n * @property {number} [zoomFactor=2] The zoom factor used to compute the\n * corresponding resolution.\n * @property {!Array<number>} [padding=[0, 0, 0, 0]] Padding (in css pixels).\n * If the map viewport is partially covered with other content (overlays) along\n * its edges, this setting allows to shift the center of the viewport away from\n * that content. The order of the values is top, right, bottom, left.\n */\n\n/**\n * @typedef {Object} AnimationOptions\n * @property {import(\"./coordinate.js\").Coordinate} [center] The center of the view at the end of\n * the animation.\n * @property {number} [zoom] The zoom level of the view at the end of the\n * animation. This takes precedence over `resolution`.\n * @property {number} [resolution] The resolution of the view at the end\n * of the animation.  If `zoom` is also provided, this option will be ignored.\n * @property {number} [rotation] The rotation of the view at the end of\n * the animation.\n * @property {import(\"./coordinate.js\").Coordinate} [anchor] Optional anchor to remain fixed\n * during a rotation or resolution animation.\n * @property {number} [duration=1000] The duration of the animation in milliseconds.\n * @property {function(number):number} [easing] The easing function used\n * during the animation (defaults to {@link module:ol/easing.inAndOut}).\n * The function will be called for each frame with a number representing a\n * fraction of the animation's duration.  The function should return a number\n * between 0 and 1 representing the progress toward the destination state.\n */\n\n/**\n * @typedef {Object} State\n * @property {import(\"./coordinate.js\").Coordinate} center Center (in view projection coordinates).\n * @property {import(\"./proj/Projection.js\").default} projection Projection.\n * @property {number} resolution Resolution.\n * @property {import(\"./coordinate.js\").Coordinate} [nextCenter] The next center during an animation series.\n * @property {number} [nextResolution] The next resolution during an animation series.\n * @property {number} [nextRotation] The next rotation during an animation series.\n * @property {number} rotation Rotation.\n * @property {number} zoom Zoom.\n */\n\n/**\n * Like {@link import(\"./Map.js\").FrameState}, but just `viewState` and `extent`.\n * @typedef {Object} ViewStateLayerStateExtent\n * @property {State} viewState View state.\n * @property {import(\"./extent.js\").Extent} extent Extent (in user projection coordinates).\n * @property {Array<import(\"./layer/Layer.js\").State>} [layerStatesArray] Layer states.\n */\n\n/**\n * Default min zoom level for the map view.\n * @type {number}\n */\nconst DEFAULT_MIN_ZOOM = 0;\n\n/**\n * @typedef {import(\"./ObjectEventType\").Types|'change:center'|'change:resolution'|'change:rotation'} ViewObjectEventTypes\n */\n\n/***\n * @template Return\n * @typedef {import(\"./Observable\").OnSignature<import(\"./Observable\").EventTypes, import(\"./events/Event.js\").default, Return> &\n *   import(\"./Observable\").OnSignature<ViewObjectEventTypes, import(\"./Object\").ObjectEvent, Return> &\n *   import(\"./Observable\").CombinedOnSignature<import(\"./Observable\").EventTypes|ViewObjectEventTypes, Return>} ViewOnSignature\n */\n\n/**\n * @classdesc\n * A View object represents a simple 2D view of the map.\n *\n * This is the object to act upon to change the center, resolution,\n * and rotation of the map.\n *\n * A View has a `projection`. The projection determines the\n * coordinate system of the center, and its units determine the units of the\n * resolution (projection units per pixel). The default projection is\n * Web Mercator (EPSG:3857).\n *\n * ### The view states\n *\n * A View is determined by three states: `center`, `resolution`,\n * and `rotation`. Each state has a corresponding getter and setter, e.g.\n * `getCenter` and `setCenter` for the `center` state.\n *\n * The `zoom` state is actually not saved on the view: all computations\n * internally use the `resolution` state. Still, the `setZoom` and `getZoom`\n * methods are available, as well as `getResolutionForZoom` and\n * `getZoomForResolution` to switch from one system to the other.\n *\n * ### The constraints\n *\n * `setCenter`, `setResolution` and `setRotation` can be used to change the\n * states of the view, but any constraint defined in the constructor will\n * be applied along the way.\n *\n * A View object can have a *resolution constraint*, a *rotation constraint*\n * and a *center constraint*.\n *\n * The *resolution constraint* typically restricts min/max values and\n * snaps to specific resolutions. It is determined by the following\n * options: `resolutions`, `maxResolution`, `maxZoom` and `zoomFactor`.\n * If `resolutions` is set, the other three options are ignored. See\n * documentation for each option for more information. By default, the view\n * only has a min/max restriction and allow intermediary zoom levels when\n * pinch-zooming for example.\n *\n * The *rotation constraint* snaps to specific angles. It is determined\n * by the following options: `enableRotation` and `constrainRotation`.\n * By default rotation is allowed and its value is snapped to zero when approaching the\n * horizontal.\n *\n * The *center constraint* is determined by the `extent` option. By\n * default the view center is not constrained at all.\n *\n * ### Changing the view state\n *\n * It is important to note that `setZoom`, `setResolution`, `setCenter` and\n * `setRotation` are subject to the above mentioned constraints. As such, it\n * may sometimes not be possible to know in advance the resulting state of the\n * View. For example, calling `setResolution(10)` does not guarantee that\n * `getResolution()` will return `10`.\n *\n * A consequence of this is that, when applying a delta on the view state, one\n * should use `adjustCenter`, `adjustRotation`, `adjustZoom` and `adjustResolution`\n * rather than the corresponding setters. This will let view do its internal\n * computations. Besides, the `adjust*` methods also take an `anchor`\n * argument which allows specifying an origin for the transformation.\n *\n * ### Interacting with the view\n *\n * View constraints are usually only applied when the view is *at rest*, meaning that\n * no interaction or animation is ongoing. As such, if the user puts the view in a\n * state that is not equivalent to a constrained one (e.g. rotating the view when\n * the snap angle is 0), an animation will be triggered at the interaction end to\n * put back the view to a stable state;\n *\n * @api\n */\nclass View extends BaseObject {\n  /**\n   * @param {ViewOptions} [options] View options.\n   */\n  constructor(options) {\n    super();\n\n    /***\n     * @type {ViewOnSignature<import(\"./events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {ViewOnSignature<import(\"./events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {ViewOnSignature<void>}\n     */\n    this.un;\n\n    options = Object.assign({}, options);\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.hints_ = [0, 0];\n\n    /**\n     * @private\n     * @type {Array<Array<Animation>>}\n     */\n    this.animations_ = [];\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.updateAnimationKey_;\n\n    /**\n     * @private\n     * @const\n     * @type {import(\"./proj/Projection.js\").default}\n     */\n    this.projection_ = createProjection(options.projection, 'EPSG:3857');\n\n    /**\n     * @private\n     * @type {import(\"./size.js\").Size}\n     */\n    this.viewportSize_ = [100, 100];\n\n    /**\n     * @private\n     * @type {import(\"./coordinate.js\").Coordinate|undefined}\n     */\n    this.targetCenter_ = null;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.targetResolution_;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.targetRotation_;\n\n    /**\n     * @private\n     * @type {import(\"./coordinate.js\").Coordinate}\n     */\n    this.nextCenter_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.nextResolution_;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.nextRotation_;\n\n    /**\n     * @private\n     * @type {import(\"./coordinate.js\").Coordinate|undefined}\n     */\n    this.cancelAnchor_ = undefined;\n\n    if (options.projection) {\n      disableCoordinateWarning();\n    }\n    if (options.center) {\n      options.center = fromUserCoordinate(options.center, this.projection_);\n    }\n    if (options.extent) {\n      options.extent = fromUserExtent(options.extent, this.projection_);\n    }\n\n    this.applyOptions_(options);\n  }\n\n  /**\n   * Set up the view with the given options.\n   * @param {ViewOptions} options View options.\n   */\n  applyOptions_(options) {\n    const properties = Object.assign({}, options);\n    for (const key in ViewProperty) {\n      delete properties[key];\n    }\n    this.setProperties(properties, true);\n\n    const resolutionConstraintInfo = createResolutionConstraint(options);\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxResolution_ = resolutionConstraintInfo.maxResolution;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.minResolution_ = resolutionConstraintInfo.minResolution;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.zoomFactor_ = resolutionConstraintInfo.zoomFactor;\n\n    /**\n     * @private\n     * @type {Array<number>|undefined}\n     */\n    this.resolutions_ = options.resolutions;\n\n    /**\n     * @type {Array<number>|undefined}\n     * @private\n     */\n    this.padding_ = options.padding;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.minZoom_ = resolutionConstraintInfo.minZoom;\n\n    const centerConstraint = createCenterConstraint(options);\n    const resolutionConstraint = resolutionConstraintInfo.constraint;\n    const rotationConstraint = createRotationConstraint(options);\n\n    /**\n     * @private\n     * @type {Constraints}\n     */\n    this.constraints_ = {\n      center: centerConstraint,\n      resolution: resolutionConstraint,\n      rotation: rotationConstraint,\n    };\n\n    this.setRotation(options.rotation !== undefined ? options.rotation : 0);\n    this.setCenterInternal(\n      options.center !== undefined ? options.center : null\n    );\n    if (options.resolution !== undefined) {\n      this.setResolution(options.resolution);\n    } else if (options.zoom !== undefined) {\n      this.setZoom(options.zoom);\n    }\n  }\n\n  /**\n   * Padding (in css pixels).\n   * If the map viewport is partially covered with other content (overlays) along\n   * its edges, this setting allows to shift the center of the viewport away from that\n   * content. The order of the values in the array is top, right, bottom, left.\n   * The default is no padding, which is equivalent to `[0, 0, 0, 0]`.\n   * @type {Array<number>|undefined}\n   * @api\n   */\n  get padding() {\n    return this.padding_;\n  }\n  set padding(padding) {\n    let oldPadding = this.padding_;\n    this.padding_ = padding;\n    const center = this.getCenterInternal();\n    if (center) {\n      const newPadding = padding || [0, 0, 0, 0];\n      oldPadding = oldPadding || [0, 0, 0, 0];\n      const resolution = this.getResolution();\n      const offsetX =\n        (resolution / 2) *\n        (newPadding[3] - oldPadding[3] + oldPadding[1] - newPadding[1]);\n      const offsetY =\n        (resolution / 2) *\n        (newPadding[0] - oldPadding[0] + oldPadding[2] - newPadding[2]);\n      this.setCenterInternal([center[0] + offsetX, center[1] - offsetY]);\n    }\n  }\n\n  /**\n   * Get an updated version of the view options used to construct the view.  The\n   * current resolution (or zoom), center, and rotation are applied to any stored\n   * options.  The provided options can be used to apply new min/max zoom or\n   * resolution limits.\n   * @param {ViewOptions} newOptions New options to be applied.\n   * @return {ViewOptions} New options updated with the current view state.\n   */\n  getUpdatedOptions_(newOptions) {\n    const options = this.getProperties();\n\n    // preserve resolution (or zoom)\n    if (options.resolution !== undefined) {\n      options.resolution = this.getResolution();\n    } else {\n      options.zoom = this.getZoom();\n    }\n\n    // preserve center\n    options.center = this.getCenterInternal();\n\n    // preserve rotation\n    options.rotation = this.getRotation();\n\n    return Object.assign({}, options, newOptions);\n  }\n\n  /**\n   * Animate the view.  The view's center, zoom (or resolution), and rotation\n   * can be animated for smooth transitions between view states.  For example,\n   * to animate the view to a new zoom level:\n   *\n   *     view.animate({zoom: view.getZoom() + 1});\n   *\n   * By default, the animation lasts one second and uses in-and-out easing.  You\n   * can customize this behavior by including `duration` (in milliseconds) and\n   * `easing` options (see {@link module:ol/easing}).\n   *\n   * To chain together multiple animations, call the method with multiple\n   * animation objects.  For example, to first zoom and then pan:\n   *\n   *     view.animate({zoom: 10}, {center: [0, 0]});\n   *\n   * If you provide a function as the last argument to the animate method, it\n   * will get called at the end of an animation series.  The callback will be\n   * called with `true` if the animation series completed on its own or `false`\n   * if it was cancelled.\n   *\n   * Animations are cancelled by user interactions (e.g. dragging the map) or by\n   * calling `view.setCenter()`, `view.setResolution()`, or `view.setRotation()`\n   * (or another method that calls one of these).\n   *\n   * @param {...(AnimationOptions|function(boolean): void)} var_args Animation\n   *     options.  Multiple animations can be run in series by passing multiple\n   *     options objects.  To run multiple animations in parallel, call the method\n   *     multiple times.  An optional callback can be provided as a final\n   *     argument.  The callback will be called with a boolean indicating whether\n   *     the animation completed without being cancelled.\n   * @api\n   */\n  animate(var_args) {\n    if (this.isDef() && !this.getAnimating()) {\n      this.resolveConstraints(0);\n    }\n    const args = new Array(arguments.length);\n    for (let i = 0; i < args.length; ++i) {\n      let options = arguments[i];\n      if (options.center) {\n        options = Object.assign({}, options);\n        options.center = fromUserCoordinate(\n          options.center,\n          this.getProjection()\n        );\n      }\n      if (options.anchor) {\n        options = Object.assign({}, options);\n        options.anchor = fromUserCoordinate(\n          options.anchor,\n          this.getProjection()\n        );\n      }\n      args[i] = options;\n    }\n    this.animateInternal.apply(this, args);\n  }\n\n  /**\n   * @param {...(AnimationOptions|function(boolean): void)} var_args Animation options.\n   */\n  animateInternal(var_args) {\n    let animationCount = arguments.length;\n    let callback;\n    if (\n      animationCount > 1 &&\n      typeof arguments[animationCount - 1] === 'function'\n    ) {\n      callback = arguments[animationCount - 1];\n      --animationCount;\n    }\n\n    let i = 0;\n    for (; i < animationCount && !this.isDef(); ++i) {\n      // if view properties are not yet set, shortcut to the final state\n      const state = arguments[i];\n      if (state.center) {\n        this.setCenterInternal(state.center);\n      }\n      if (state.zoom !== undefined) {\n        this.setZoom(state.zoom);\n      } else if (state.resolution) {\n        this.setResolution(state.resolution);\n      }\n      if (state.rotation !== undefined) {\n        this.setRotation(state.rotation);\n      }\n    }\n    if (i === animationCount) {\n      if (callback) {\n        animationCallback(callback, true);\n      }\n      return;\n    }\n\n    let start = Date.now();\n    let center = this.targetCenter_.slice();\n    let resolution = this.targetResolution_;\n    let rotation = this.targetRotation_;\n    const series = [];\n    for (; i < animationCount; ++i) {\n      const options = /** @type {AnimationOptions} */ (arguments[i]);\n\n      const animation = {\n        start: start,\n        complete: false,\n        anchor: options.anchor,\n        duration: options.duration !== undefined ? options.duration : 1000,\n        easing: options.easing || inAndOut,\n        callback: callback,\n      };\n\n      if (options.center) {\n        animation.sourceCenter = center;\n        animation.targetCenter = options.center.slice();\n        center = animation.targetCenter;\n      }\n\n      if (options.zoom !== undefined) {\n        animation.sourceResolution = resolution;\n        animation.targetResolution = this.getResolutionForZoom(options.zoom);\n        resolution = animation.targetResolution;\n      } else if (options.resolution) {\n        animation.sourceResolution = resolution;\n        animation.targetResolution = options.resolution;\n        resolution = animation.targetResolution;\n      }\n\n      if (options.rotation !== undefined) {\n        animation.sourceRotation = rotation;\n        const delta =\n          modulo(options.rotation - rotation + Math.PI, 2 * Math.PI) - Math.PI;\n        animation.targetRotation = rotation + delta;\n        rotation = animation.targetRotation;\n      }\n\n      // check if animation is a no-op\n      if (isNoopAnimation(animation)) {\n        animation.complete = true;\n        // we still push it onto the series for callback handling\n      } else {\n        start += animation.duration;\n      }\n      series.push(animation);\n    }\n    this.animations_.push(series);\n    this.setHint(ViewHint.ANIMATING, 1);\n    this.updateAnimations_();\n  }\n\n  /**\n   * Determine if the view is being animated.\n   * @return {boolean} The view is being animated.\n   * @api\n   */\n  getAnimating() {\n    return this.hints_[ViewHint.ANIMATING] > 0;\n  }\n\n  /**\n   * Determine if the user is interacting with the view, such as panning or zooming.\n   * @return {boolean} The view is being interacted with.\n   * @api\n   */\n  getInteracting() {\n    return this.hints_[ViewHint.INTERACTING] > 0;\n  }\n\n  /**\n   * Cancel any ongoing animations.\n   * @api\n   */\n  cancelAnimations() {\n    this.setHint(ViewHint.ANIMATING, -this.hints_[ViewHint.ANIMATING]);\n    let anchor;\n    for (let i = 0, ii = this.animations_.length; i < ii; ++i) {\n      const series = this.animations_[i];\n      if (series[0].callback) {\n        animationCallback(series[0].callback, false);\n      }\n      if (!anchor) {\n        for (let j = 0, jj = series.length; j < jj; ++j) {\n          const animation = series[j];\n          if (!animation.complete) {\n            anchor = animation.anchor;\n            break;\n          }\n        }\n      }\n    }\n    this.animations_.length = 0;\n    this.cancelAnchor_ = anchor;\n    this.nextCenter_ = null;\n    this.nextResolution_ = NaN;\n    this.nextRotation_ = NaN;\n  }\n\n  /**\n   * Update all animations.\n   */\n  updateAnimations_() {\n    if (this.updateAnimationKey_ !== undefined) {\n      cancelAnimationFrame(this.updateAnimationKey_);\n      this.updateAnimationKey_ = undefined;\n    }\n    if (!this.getAnimating()) {\n      return;\n    }\n    const now = Date.now();\n    let more = false;\n    for (let i = this.animations_.length - 1; i >= 0; --i) {\n      const series = this.animations_[i];\n      let seriesComplete = true;\n      for (let j = 0, jj = series.length; j < jj; ++j) {\n        const animation = series[j];\n        if (animation.complete) {\n          continue;\n        }\n        const elapsed = now - animation.start;\n        let fraction =\n          animation.duration > 0 ? elapsed / animation.duration : 1;\n        if (fraction >= 1) {\n          animation.complete = true;\n          fraction = 1;\n        } else {\n          seriesComplete = false;\n        }\n        const progress = animation.easing(fraction);\n        if (animation.sourceCenter) {\n          const x0 = animation.sourceCenter[0];\n          const y0 = animation.sourceCenter[1];\n          const x1 = animation.targetCenter[0];\n          const y1 = animation.targetCenter[1];\n          this.nextCenter_ = animation.targetCenter;\n          const x = x0 + progress * (x1 - x0);\n          const y = y0 + progress * (y1 - y0);\n          this.targetCenter_ = [x, y];\n        }\n        if (animation.sourceResolution && animation.targetResolution) {\n          const resolution =\n            progress === 1\n              ? animation.targetResolution\n              : animation.sourceResolution +\n                progress *\n                  (animation.targetResolution - animation.sourceResolution);\n          if (animation.anchor) {\n            const size = this.getViewportSize_(this.getRotation());\n            const constrainedResolution = this.constraints_.resolution(\n              resolution,\n              0,\n              size,\n              true\n            );\n            this.targetCenter_ = this.calculateCenterZoom(\n              constrainedResolution,\n              animation.anchor\n            );\n          }\n          this.nextResolution_ = animation.targetResolution;\n          this.targetResolution_ = resolution;\n          this.applyTargetState_(true);\n        }\n        if (\n          animation.sourceRotation !== undefined &&\n          animation.targetRotation !== undefined\n        ) {\n          const rotation =\n            progress === 1\n              ? modulo(animation.targetRotation + Math.PI, 2 * Math.PI) -\n                Math.PI\n              : animation.sourceRotation +\n                progress *\n                  (animation.targetRotation - animation.sourceRotation);\n          if (animation.anchor) {\n            const constrainedRotation = this.constraints_.rotation(\n              rotation,\n              true\n            );\n            this.targetCenter_ = this.calculateCenterRotate(\n              constrainedRotation,\n              animation.anchor\n            );\n          }\n          this.nextRotation_ = animation.targetRotation;\n          this.targetRotation_ = rotation;\n        }\n        this.applyTargetState_(true);\n        more = true;\n        if (!animation.complete) {\n          break;\n        }\n      }\n      if (seriesComplete) {\n        this.animations_[i] = null;\n        this.setHint(ViewHint.ANIMATING, -1);\n        this.nextCenter_ = null;\n        this.nextResolution_ = NaN;\n        this.nextRotation_ = NaN;\n        const callback = series[0].callback;\n        if (callback) {\n          animationCallback(callback, true);\n        }\n      }\n    }\n    // prune completed series\n    this.animations_ = this.animations_.filter(Boolean);\n    if (more && this.updateAnimationKey_ === undefined) {\n      this.updateAnimationKey_ = requestAnimationFrame(\n        this.updateAnimations_.bind(this)\n      );\n    }\n  }\n\n  /**\n   * @param {number} rotation Target rotation.\n   * @param {import(\"./coordinate.js\").Coordinate} anchor Rotation anchor.\n   * @return {import(\"./coordinate.js\").Coordinate|undefined} Center for rotation and anchor.\n   */\n  calculateCenterRotate(rotation, anchor) {\n    let center;\n    const currentCenter = this.getCenterInternal();\n    if (currentCenter !== undefined) {\n      center = [currentCenter[0] - anchor[0], currentCenter[1] - anchor[1]];\n      rotateCoordinate(center, rotation - this.getRotation());\n      addCoordinate(center, anchor);\n    }\n    return center;\n  }\n\n  /**\n   * @param {number} resolution Target resolution.\n   * @param {import(\"./coordinate.js\").Coordinate} anchor Zoom anchor.\n   * @return {import(\"./coordinate.js\").Coordinate|undefined} Center for resolution and anchor.\n   */\n  calculateCenterZoom(resolution, anchor) {\n    let center;\n    const currentCenter = this.getCenterInternal();\n    const currentResolution = this.getResolution();\n    if (currentCenter !== undefined && currentResolution !== undefined) {\n      const x =\n        anchor[0] -\n        (resolution * (anchor[0] - currentCenter[0])) / currentResolution;\n      const y =\n        anchor[1] -\n        (resolution * (anchor[1] - currentCenter[1])) / currentResolution;\n      center = [x, y];\n    }\n    return center;\n  }\n\n  /**\n   * Returns the current viewport size.\n   * @private\n   * @param {number} [rotation] Take into account the rotation of the viewport when giving the size\n   * @return {import(\"./size.js\").Size} Viewport size or `[100, 100]` when no viewport is found.\n   */\n  getViewportSize_(rotation) {\n    const size = this.viewportSize_;\n    if (rotation) {\n      const w = size[0];\n      const h = size[1];\n      return [\n        Math.abs(w * Math.cos(rotation)) + Math.abs(h * Math.sin(rotation)),\n        Math.abs(w * Math.sin(rotation)) + Math.abs(h * Math.cos(rotation)),\n      ];\n    }\n    return size;\n  }\n\n  /**\n   * Stores the viewport size on the view. The viewport size is not read every time from the DOM\n   * to avoid performance hit and layout reflow.\n   * This should be done on map size change.\n   * Note: the constraints are not resolved during an animation to avoid stopping it\n   * @param {import(\"./size.js\").Size} [size] Viewport size; if undefined, [100, 100] is assumed\n   */\n  setViewportSize(size) {\n    this.viewportSize_ = Array.isArray(size) ? size.slice() : [100, 100];\n    if (!this.getAnimating()) {\n      this.resolveConstraints(0);\n    }\n  }\n\n  /**\n   * Get the view center.\n   * @return {import(\"./coordinate.js\").Coordinate|undefined} The center of the view.\n   * @observable\n   * @api\n   */\n  getCenter() {\n    const center = this.getCenterInternal();\n    if (!center) {\n      return center;\n    }\n    return toUserCoordinate(center, this.getProjection());\n  }\n\n  /**\n   * Get the view center without transforming to user projection.\n   * @return {import(\"./coordinate.js\").Coordinate|undefined} The center of the view.\n   */\n  getCenterInternal() {\n    return /** @type {import(\"./coordinate.js\").Coordinate|undefined} */ (\n      this.get(ViewProperty.CENTER)\n    );\n  }\n\n  /**\n   * @return {Constraints} Constraints.\n   */\n  getConstraints() {\n    return this.constraints_;\n  }\n\n  /**\n   * @return {boolean} Resolution constraint is set\n   */\n  getConstrainResolution() {\n    return this.get('constrainResolution');\n  }\n\n  /**\n   * @param {Array<number>} [hints] Destination array.\n   * @return {Array<number>} Hint.\n   */\n  getHints(hints) {\n    if (hints !== undefined) {\n      hints[0] = this.hints_[0];\n      hints[1] = this.hints_[1];\n      return hints;\n    }\n    return this.hints_.slice();\n  }\n\n  /**\n   * Calculate the extent for the current view state and the passed size.\n   * The size is the pixel dimensions of the box into which the calculated extent\n   * should fit. In most cases you want to get the extent of the entire map,\n   * that is `map.getSize()`.\n   * @param {import(\"./size.js\").Size} [size] Box pixel size. If not provided, the size\n   * of the map that uses this view will be used.\n   * @return {import(\"./extent.js\").Extent} Extent.\n   * @api\n   */\n  calculateExtent(size) {\n    const extent = this.calculateExtentInternal(size);\n    return toUserExtent(extent, this.getProjection());\n  }\n\n  /**\n   * @param {import(\"./size.js\").Size} [size] Box pixel size. If not provided,\n   * the map's last known viewport size will be used.\n   * @return {import(\"./extent.js\").Extent} Extent.\n   */\n  calculateExtentInternal(size) {\n    size = size || this.getViewportSizeMinusPadding_();\n    const center = /** @type {!import(\"./coordinate.js\").Coordinate} */ (\n      this.getCenterInternal()\n    );\n    assert(center, 1); // The view center is not defined\n    const resolution = /** @type {!number} */ (this.getResolution());\n    assert(resolution !== undefined, 2); // The view resolution is not defined\n    const rotation = /** @type {!number} */ (this.getRotation());\n    assert(rotation !== undefined, 3); // The view rotation is not defined\n\n    return getForViewAndSize(center, resolution, rotation, size);\n  }\n\n  /**\n   * Get the maximum resolution of the view.\n   * @return {number} The maximum resolution of the view.\n   * @api\n   */\n  getMaxResolution() {\n    return this.maxResolution_;\n  }\n\n  /**\n   * Get the minimum resolution of the view.\n   * @return {number} The minimum resolution of the view.\n   * @api\n   */\n  getMinResolution() {\n    return this.minResolution_;\n  }\n\n  /**\n   * Get the maximum zoom level for the view.\n   * @return {number} The maximum zoom level.\n   * @api\n   */\n  getMaxZoom() {\n    return /** @type {number} */ (\n      this.getZoomForResolution(this.minResolution_)\n    );\n  }\n\n  /**\n   * Set a new maximum zoom level for the view.\n   * @param {number} zoom The maximum zoom level.\n   * @api\n   */\n  setMaxZoom(zoom) {\n    this.applyOptions_(this.getUpdatedOptions_({maxZoom: zoom}));\n  }\n\n  /**\n   * Get the minimum zoom level for the view.\n   * @return {number} The minimum zoom level.\n   * @api\n   */\n  getMinZoom() {\n    return /** @type {number} */ (\n      this.getZoomForResolution(this.maxResolution_)\n    );\n  }\n\n  /**\n   * Set a new minimum zoom level for the view.\n   * @param {number} zoom The minimum zoom level.\n   * @api\n   */\n  setMinZoom(zoom) {\n    this.applyOptions_(this.getUpdatedOptions_({minZoom: zoom}));\n  }\n\n  /**\n   * Set whether the view should allow intermediary zoom levels.\n   * @param {boolean} enabled Whether the resolution is constrained.\n   * @api\n   */\n  setConstrainResolution(enabled) {\n    this.applyOptions_(this.getUpdatedOptions_({constrainResolution: enabled}));\n  }\n\n  /**\n   * Get the view projection.\n   * @return {import(\"./proj/Projection.js\").default} The projection of the view.\n   * @api\n   */\n  getProjection() {\n    return this.projection_;\n  }\n\n  /**\n   * Get the view resolution.\n   * @return {number|undefined} The resolution of the view.\n   * @observable\n   * @api\n   */\n  getResolution() {\n    return /** @type {number|undefined} */ (this.get(ViewProperty.RESOLUTION));\n  }\n\n  /**\n   * Get the resolutions for the view. This returns the array of resolutions\n   * passed to the constructor of the View, or undefined if none were given.\n   * @return {Array<number>|undefined} The resolutions of the view.\n   * @api\n   */\n  getResolutions() {\n    return this.resolutions_;\n  }\n\n  /**\n   * Get the resolution for a provided extent (in map units) and size (in pixels).\n   * @param {import(\"./extent.js\").Extent} extent Extent.\n   * @param {import(\"./size.js\").Size} [size] Box pixel size.\n   * @return {number} The resolution at which the provided extent will render at\n   *     the given size.\n   * @api\n   */\n  getResolutionForExtent(extent, size) {\n    return this.getResolutionForExtentInternal(\n      fromUserExtent(extent, this.getProjection()),\n      size\n    );\n  }\n\n  /**\n   * Get the resolution for a provided extent (in map units) and size (in pixels).\n   * @param {import(\"./extent.js\").Extent} extent Extent.\n   * @param {import(\"./size.js\").Size} [size] Box pixel size.\n   * @return {number} The resolution at which the provided extent will render at\n   *     the given size.\n   */\n  getResolutionForExtentInternal(extent, size) {\n    size = size || this.getViewportSizeMinusPadding_();\n    const xResolution = getWidth(extent) / size[0];\n    const yResolution = getHeight(extent) / size[1];\n    return Math.max(xResolution, yResolution);\n  }\n\n  /**\n   * Return a function that returns a value between 0 and 1 for a\n   * resolution. Exponential scaling is assumed.\n   * @param {number} [power] Power.\n   * @return {function(number): number} Resolution for value function.\n   */\n  getResolutionForValueFunction(power) {\n    power = power || 2;\n    const maxResolution = this.getConstrainedResolution(this.maxResolution_);\n    const minResolution = this.minResolution_;\n    const max = Math.log(maxResolution / minResolution) / Math.log(power);\n    return (\n      /**\n       * @param {number} value Value.\n       * @return {number} Resolution.\n       */\n      function (value) {\n        const resolution = maxResolution / Math.pow(power, value * max);\n        return resolution;\n      }\n    );\n  }\n\n  /**\n   * Get the view rotation.\n   * @return {number} The rotation of the view in radians.\n   * @observable\n   * @api\n   */\n  getRotation() {\n    return /** @type {number} */ (this.get(ViewProperty.ROTATION));\n  }\n\n  /**\n   * Return a function that returns a resolution for a value between\n   * 0 and 1. Exponential scaling is assumed.\n   * @param {number} [power] Power.\n   * @return {function(number): number} Value for resolution function.\n   */\n  getValueForResolutionFunction(power) {\n    const logPower = Math.log(power || 2);\n    const maxResolution = this.getConstrainedResolution(this.maxResolution_);\n    const minResolution = this.minResolution_;\n    const max = Math.log(maxResolution / minResolution) / logPower;\n    return (\n      /**\n       * @param {number} resolution Resolution.\n       * @return {number} Value.\n       */\n      function (resolution) {\n        const value = Math.log(maxResolution / resolution) / logPower / max;\n        return value;\n      }\n    );\n  }\n\n  /**\n   * Returns the size of the viewport minus padding.\n   * @private\n   * @param {number} [rotation] Take into account the rotation of the viewport when giving the size\n   * @return {import(\"./size.js\").Size} Viewport size reduced by the padding.\n   */\n  getViewportSizeMinusPadding_(rotation) {\n    let size = this.getViewportSize_(rotation);\n    const padding = this.padding_;\n    if (padding) {\n      size = [\n        size[0] - padding[1] - padding[3],\n        size[1] - padding[0] - padding[2],\n      ];\n    }\n    return size;\n  }\n\n  /**\n   * @return {State} View state.\n   */\n  getState() {\n    const projection = this.getProjection();\n    const resolution = this.getResolution();\n    const rotation = this.getRotation();\n    let center = /** @type {import(\"./coordinate.js\").Coordinate} */ (\n      this.getCenterInternal()\n    );\n    const padding = this.padding_;\n    if (padding) {\n      const reducedSize = this.getViewportSizeMinusPadding_();\n      center = calculateCenterOn(\n        center,\n        this.getViewportSize_(),\n        [reducedSize[0] / 2 + padding[3], reducedSize[1] / 2 + padding[0]],\n        resolution,\n        rotation\n      );\n    }\n    return {\n      center: center.slice(0),\n      projection: projection !== undefined ? projection : null,\n      resolution: resolution,\n      nextCenter: this.nextCenter_,\n      nextResolution: this.nextResolution_,\n      nextRotation: this.nextRotation_,\n      rotation: rotation,\n      zoom: this.getZoom(),\n    };\n  }\n\n  /**\n   * @return {ViewStateLayerStateExtent} Like `FrameState`, but just `viewState` and `extent`.\n   */\n  getViewStateAndExtent() {\n    return {\n      viewState: this.getState(),\n      extent: this.calculateExtent(),\n    };\n  }\n\n  /**\n   * Get the current zoom level. This method may return non-integer zoom levels\n   * if the view does not constrain the resolution, or if an interaction or\n   * animation is underway.\n   * @return {number|undefined} Zoom.\n   * @api\n   */\n  getZoom() {\n    let zoom;\n    const resolution = this.getResolution();\n    if (resolution !== undefined) {\n      zoom = this.getZoomForResolution(resolution);\n    }\n    return zoom;\n  }\n\n  /**\n   * Get the zoom level for a resolution.\n   * @param {number} resolution The resolution.\n   * @return {number|undefined} The zoom level for the provided resolution.\n   * @api\n   */\n  getZoomForResolution(resolution) {\n    let offset = this.minZoom_ || 0;\n    let max, zoomFactor;\n    if (this.resolutions_) {\n      const nearest = linearFindNearest(this.resolutions_, resolution, 1);\n      offset = nearest;\n      max = this.resolutions_[nearest];\n      if (nearest == this.resolutions_.length - 1) {\n        zoomFactor = 2;\n      } else {\n        zoomFactor = max / this.resolutions_[nearest + 1];\n      }\n    } else {\n      max = this.maxResolution_;\n      zoomFactor = this.zoomFactor_;\n    }\n    return offset + Math.log(max / resolution) / Math.log(zoomFactor);\n  }\n\n  /**\n   * Get the resolution for a zoom level.\n   * @param {number} zoom Zoom level.\n   * @return {number} The view resolution for the provided zoom level.\n   * @api\n   */\n  getResolutionForZoom(zoom) {\n    if (this.resolutions_) {\n      if (this.resolutions_.length <= 1) {\n        return 0;\n      }\n      const baseLevel = clamp(\n        Math.floor(zoom),\n        0,\n        this.resolutions_.length - 2\n      );\n      const zoomFactor =\n        this.resolutions_[baseLevel] / this.resolutions_[baseLevel + 1];\n      return (\n        this.resolutions_[baseLevel] /\n        Math.pow(zoomFactor, clamp(zoom - baseLevel, 0, 1))\n      );\n    }\n    return (\n      this.maxResolution_ / Math.pow(this.zoomFactor_, zoom - this.minZoom_)\n    );\n  }\n\n  /**\n   * Fit the given geometry or extent based on the given map size and border.\n   * The size is pixel dimensions of the box to fit the extent into.\n   * In most cases you will want to use the map size, that is `map.getSize()`.\n   * Takes care of the map angle.\n   * @param {import(\"./geom/SimpleGeometry.js\").default|import(\"./extent.js\").Extent} geometryOrExtent The geometry or\n   *     extent to fit the view to.\n   * @param {FitOptions} [options] Options.\n   * @api\n   */\n  fit(geometryOrExtent, options) {\n    /** @type {import(\"./geom/SimpleGeometry.js\").default} */\n    let geometry;\n    assert(\n      Array.isArray(geometryOrExtent) ||\n        typeof (/** @type {?} */ (geometryOrExtent).getSimplifiedGeometry) ===\n          'function',\n      24\n    ); // Invalid extent or geometry provided as `geometry`\n    if (Array.isArray(geometryOrExtent)) {\n      assert(!isEmpty(geometryOrExtent), 25); // Cannot fit empty extent provided as `geometry`\n      const extent = fromUserExtent(geometryOrExtent, this.getProjection());\n      geometry = polygonFromExtent(extent);\n    } else if (geometryOrExtent.getType() === 'Circle') {\n      const extent = fromUserExtent(\n        geometryOrExtent.getExtent(),\n        this.getProjection()\n      );\n      geometry = polygonFromExtent(extent);\n      geometry.rotate(this.getRotation(), getCenter(extent));\n    } else {\n      const userProjection = getUserProjection();\n      if (userProjection) {\n        geometry = /** @type {import(\"./geom/SimpleGeometry.js\").default} */ (\n          geometryOrExtent\n            .clone()\n            .transform(userProjection, this.getProjection())\n        );\n      } else {\n        geometry = geometryOrExtent;\n      }\n    }\n\n    this.fitInternal(geometry, options);\n  }\n\n  /**\n   * Calculate rotated extent\n   * @param {import(\"./geom/SimpleGeometry.js\").default} geometry The geometry.\n   * @return {import(\"./extent\").Extent} The rotated extent for the geometry.\n   */\n  rotatedExtentForGeometry(geometry) {\n    const rotation = this.getRotation();\n    const cosAngle = Math.cos(rotation);\n    const sinAngle = Math.sin(-rotation);\n    const coords = geometry.getFlatCoordinates();\n    const stride = geometry.getStride();\n    let minRotX = +Infinity;\n    let minRotY = +Infinity;\n    let maxRotX = -Infinity;\n    let maxRotY = -Infinity;\n    for (let i = 0, ii = coords.length; i < ii; i += stride) {\n      const rotX = coords[i] * cosAngle - coords[i + 1] * sinAngle;\n      const rotY = coords[i] * sinAngle + coords[i + 1] * cosAngle;\n      minRotX = Math.min(minRotX, rotX);\n      minRotY = Math.min(minRotY, rotY);\n      maxRotX = Math.max(maxRotX, rotX);\n      maxRotY = Math.max(maxRotY, rotY);\n    }\n    return [minRotX, minRotY, maxRotX, maxRotY];\n  }\n\n  /**\n   * @param {import(\"./geom/SimpleGeometry.js\").default} geometry The geometry.\n   * @param {FitOptions} [options] Options.\n   */\n  fitInternal(geometry, options) {\n    options = options || {};\n    let size = options.size;\n    if (!size) {\n      size = this.getViewportSizeMinusPadding_();\n    }\n    const padding =\n      options.padding !== undefined ? options.padding : [0, 0, 0, 0];\n    const nearest = options.nearest !== undefined ? options.nearest : false;\n    let minResolution;\n    if (options.minResolution !== undefined) {\n      minResolution = options.minResolution;\n    } else if (options.maxZoom !== undefined) {\n      minResolution = this.getResolutionForZoom(options.maxZoom);\n    } else {\n      minResolution = 0;\n    }\n\n    const rotatedExtent = this.rotatedExtentForGeometry(geometry);\n\n    // calculate resolution\n    let resolution = this.getResolutionForExtentInternal(rotatedExtent, [\n      size[0] - padding[1] - padding[3],\n      size[1] - padding[0] - padding[2],\n    ]);\n    resolution = isNaN(resolution)\n      ? minResolution\n      : Math.max(resolution, minResolution);\n    resolution = this.getConstrainedResolution(resolution, nearest ? 0 : 1);\n\n    // calculate center\n    const rotation = this.getRotation();\n    const sinAngle = Math.sin(rotation);\n    const cosAngle = Math.cos(rotation);\n    const centerRot = getCenter(rotatedExtent);\n    centerRot[0] += ((padding[1] - padding[3]) / 2) * resolution;\n    centerRot[1] += ((padding[0] - padding[2]) / 2) * resolution;\n    const centerX = centerRot[0] * cosAngle - centerRot[1] * sinAngle;\n    const centerY = centerRot[1] * cosAngle + centerRot[0] * sinAngle;\n    const center = this.getConstrainedCenter([centerX, centerY], resolution);\n    const callback = options.callback ? options.callback : VOID;\n\n    if (options.duration !== undefined) {\n      this.animateInternal(\n        {\n          resolution: resolution,\n          center: center,\n          duration: options.duration,\n          easing: options.easing,\n        },\n        callback\n      );\n    } else {\n      this.targetResolution_ = resolution;\n      this.targetCenter_ = center;\n      this.applyTargetState_(false, true);\n      animationCallback(callback, true);\n    }\n  }\n\n  /**\n   * Center on coordinate and view position.\n   * @param {import(\"./coordinate.js\").Coordinate} coordinate Coordinate.\n   * @param {import(\"./size.js\").Size} size Box pixel size.\n   * @param {import(\"./pixel.js\").Pixel} position Position on the view to center on.\n   * @api\n   */\n  centerOn(coordinate, size, position) {\n    this.centerOnInternal(\n      fromUserCoordinate(coordinate, this.getProjection()),\n      size,\n      position\n    );\n  }\n\n  /**\n   * @param {import(\"./coordinate.js\").Coordinate} coordinate Coordinate.\n   * @param {import(\"./size.js\").Size} size Box pixel size.\n   * @param {import(\"./pixel.js\").Pixel} position Position on the view to center on.\n   */\n  centerOnInternal(coordinate, size, position) {\n    this.setCenterInternal(\n      calculateCenterOn(\n        coordinate,\n        size,\n        position,\n        this.getResolution(),\n        this.getRotation()\n      )\n    );\n  }\n\n  /**\n   * Calculates the shift between map and viewport center.\n   * @param {import(\"./coordinate.js\").Coordinate} center Center.\n   * @param {number} resolution Resolution.\n   * @param {number} rotation Rotation.\n   * @param {import(\"./size.js\").Size} size Size.\n   * @return {Array<number>|undefined} Center shift.\n   */\n  calculateCenterShift(center, resolution, rotation, size) {\n    let centerShift;\n    const padding = this.padding_;\n    if (padding && center) {\n      const reducedSize = this.getViewportSizeMinusPadding_(-rotation);\n      const shiftedCenter = calculateCenterOn(\n        center,\n        size,\n        [reducedSize[0] / 2 + padding[3], reducedSize[1] / 2 + padding[0]],\n        resolution,\n        rotation\n      );\n      centerShift = [\n        center[0] - shiftedCenter[0],\n        center[1] - shiftedCenter[1],\n      ];\n    }\n    return centerShift;\n  }\n\n  /**\n   * @return {boolean} Is defined.\n   */\n  isDef() {\n    return !!this.getCenterInternal() && this.getResolution() !== undefined;\n  }\n\n  /**\n   * Adds relative coordinates to the center of the view. Any extent constraint will apply.\n   * @param {import(\"./coordinate.js\").Coordinate} deltaCoordinates Relative value to add.\n   * @api\n   */\n  adjustCenter(deltaCoordinates) {\n    const center = toUserCoordinate(this.targetCenter_, this.getProjection());\n    this.setCenter([\n      center[0] + deltaCoordinates[0],\n      center[1] + deltaCoordinates[1],\n    ]);\n  }\n\n  /**\n   * Adds relative coordinates to the center of the view. Any extent constraint will apply.\n   * @param {import(\"./coordinate.js\").Coordinate} deltaCoordinates Relative value to add.\n   */\n  adjustCenterInternal(deltaCoordinates) {\n    const center = this.targetCenter_;\n    this.setCenterInternal([\n      center[0] + deltaCoordinates[0],\n      center[1] + deltaCoordinates[1],\n    ]);\n  }\n\n  /**\n   * Multiply the view resolution by a ratio, optionally using an anchor. Any resolution\n   * constraint will apply.\n   * @param {number} ratio The ratio to apply on the view resolution.\n   * @param {import(\"./coordinate.js\").Coordinate} [anchor] The origin of the transformation.\n   * @api\n   */\n  adjustResolution(ratio, anchor) {\n    anchor = anchor && fromUserCoordinate(anchor, this.getProjection());\n    this.adjustResolutionInternal(ratio, anchor);\n  }\n\n  /**\n   * Multiply the view resolution by a ratio, optionally using an anchor. Any resolution\n   * constraint will apply.\n   * @param {number} ratio The ratio to apply on the view resolution.\n   * @param {import(\"./coordinate.js\").Coordinate} [anchor] The origin of the transformation.\n   */\n  adjustResolutionInternal(ratio, anchor) {\n    const isMoving = this.getAnimating() || this.getInteracting();\n    const size = this.getViewportSize_(this.getRotation());\n    const newResolution = this.constraints_.resolution(\n      this.targetResolution_ * ratio,\n      0,\n      size,\n      isMoving\n    );\n\n    if (anchor) {\n      this.targetCenter_ = this.calculateCenterZoom(newResolution, anchor);\n    }\n\n    this.targetResolution_ *= ratio;\n    this.applyTargetState_();\n  }\n\n  /**\n   * Adds a value to the view zoom level, optionally using an anchor. Any resolution\n   * constraint will apply.\n   * @param {number} delta Relative value to add to the zoom level.\n   * @param {import(\"./coordinate.js\").Coordinate} [anchor] The origin of the transformation.\n   * @api\n   */\n  adjustZoom(delta, anchor) {\n    this.adjustResolution(Math.pow(this.zoomFactor_, -delta), anchor);\n  }\n\n  /**\n   * Adds a value to the view rotation, optionally using an anchor. Any rotation\n   * constraint will apply.\n   * @param {number} delta Relative value to add to the zoom rotation, in radians.\n   * @param {import(\"./coordinate.js\").Coordinate} [anchor] The rotation center.\n   * @api\n   */\n  adjustRotation(delta, anchor) {\n    if (anchor) {\n      anchor = fromUserCoordinate(anchor, this.getProjection());\n    }\n    this.adjustRotationInternal(delta, anchor);\n  }\n\n  /**\n   * @param {number} delta Relative value to add to the zoom rotation, in radians.\n   * @param {import(\"./coordinate.js\").Coordinate} [anchor] The rotation center.\n   */\n  adjustRotationInternal(delta, anchor) {\n    const isMoving = this.getAnimating() || this.getInteracting();\n    const newRotation = this.constraints_.rotation(\n      this.targetRotation_ + delta,\n      isMoving\n    );\n    if (anchor) {\n      this.targetCenter_ = this.calculateCenterRotate(newRotation, anchor);\n    }\n    this.targetRotation_ += delta;\n    this.applyTargetState_();\n  }\n\n  /**\n   * Set the center of the current view. Any extent constraint will apply.\n   * @param {import(\"./coordinate.js\").Coordinate|undefined} center The center of the view.\n   * @observable\n   * @api\n   */\n  setCenter(center) {\n    this.setCenterInternal(\n      center ? fromUserCoordinate(center, this.getProjection()) : center\n    );\n  }\n\n  /**\n   * Set the center using the view projection (not the user projection).\n   * @param {import(\"./coordinate.js\").Coordinate|undefined} center The center of the view.\n   */\n  setCenterInternal(center) {\n    this.targetCenter_ = center;\n    this.applyTargetState_();\n  }\n\n  /**\n   * @param {import(\"./ViewHint.js\").default} hint Hint.\n   * @param {number} delta Delta.\n   * @return {number} New value.\n   */\n  setHint(hint, delta) {\n    this.hints_[hint] += delta;\n    this.changed();\n    return this.hints_[hint];\n  }\n\n  /**\n   * Set the resolution for this view. Any resolution constraint will apply.\n   * @param {number|undefined} resolution The resolution of the view.\n   * @observable\n   * @api\n   */\n  setResolution(resolution) {\n    this.targetResolution_ = resolution;\n    this.applyTargetState_();\n  }\n\n  /**\n   * Set the rotation for this view. Any rotation constraint will apply.\n   * @param {number} rotation The rotation of the view in radians.\n   * @observable\n   * @api\n   */\n  setRotation(rotation) {\n    this.targetRotation_ = rotation;\n    this.applyTargetState_();\n  }\n\n  /**\n   * Zoom to a specific zoom level. Any resolution constrain will apply.\n   * @param {number} zoom Zoom level.\n   * @api\n   */\n  setZoom(zoom) {\n    this.setResolution(this.getResolutionForZoom(zoom));\n  }\n\n  /**\n   * Recompute rotation/resolution/center based on target values.\n   * Note: we have to compute rotation first, then resolution and center considering that\n   * parameters can influence one another in case a view extent constraint is present.\n   * @param {boolean} [doNotCancelAnims] Do not cancel animations.\n   * @param {boolean} [forceMoving] Apply constraints as if the view is moving.\n   * @private\n   */\n  applyTargetState_(doNotCancelAnims, forceMoving) {\n    const isMoving =\n      this.getAnimating() || this.getInteracting() || forceMoving;\n\n    // compute rotation\n    const newRotation = this.constraints_.rotation(\n      this.targetRotation_,\n      isMoving\n    );\n    const size = this.getViewportSize_(newRotation);\n    const newResolution = this.constraints_.resolution(\n      this.targetResolution_,\n      0,\n      size,\n      isMoving\n    );\n    const newCenter = this.constraints_.center(\n      this.targetCenter_,\n      newResolution,\n      size,\n      isMoving,\n      this.calculateCenterShift(\n        this.targetCenter_,\n        newResolution,\n        newRotation,\n        size\n      )\n    );\n\n    if (this.get(ViewProperty.ROTATION) !== newRotation) {\n      this.set(ViewProperty.ROTATION, newRotation);\n    }\n    if (this.get(ViewProperty.RESOLUTION) !== newResolution) {\n      this.set(ViewProperty.RESOLUTION, newResolution);\n      this.set('zoom', this.getZoom(), true);\n    }\n    if (\n      !newCenter ||\n      !this.get(ViewProperty.CENTER) ||\n      !equals(this.get(ViewProperty.CENTER), newCenter)\n    ) {\n      this.set(ViewProperty.CENTER, newCenter);\n    }\n\n    if (this.getAnimating() && !doNotCancelAnims) {\n      this.cancelAnimations();\n    }\n    this.cancelAnchor_ = undefined;\n  }\n\n  /**\n   * If any constraints need to be applied, an animation will be triggered.\n   * This is typically done on interaction end.\n   * Note: calling this with a duration of 0 will apply the constrained values straight away,\n   * without animation.\n   * @param {number} [duration] The animation duration in ms.\n   * @param {number} [resolutionDirection] Which direction to zoom.\n   * @param {import(\"./coordinate.js\").Coordinate} [anchor] The origin of the transformation.\n   */\n  resolveConstraints(duration, resolutionDirection, anchor) {\n    duration = duration !== undefined ? duration : 200;\n    const direction = resolutionDirection || 0;\n\n    const newRotation = this.constraints_.rotation(this.targetRotation_);\n    const size = this.getViewportSize_(newRotation);\n    const newResolution = this.constraints_.resolution(\n      this.targetResolution_,\n      direction,\n      size\n    );\n    const newCenter = this.constraints_.center(\n      this.targetCenter_,\n      newResolution,\n      size,\n      false,\n      this.calculateCenterShift(\n        this.targetCenter_,\n        newResolution,\n        newRotation,\n        size\n      )\n    );\n\n    if (duration === 0 && !this.cancelAnchor_) {\n      this.targetResolution_ = newResolution;\n      this.targetRotation_ = newRotation;\n      this.targetCenter_ = newCenter;\n      this.applyTargetState_();\n      return;\n    }\n\n    anchor = anchor || (duration === 0 ? this.cancelAnchor_ : undefined);\n    this.cancelAnchor_ = undefined;\n\n    if (\n      this.getResolution() !== newResolution ||\n      this.getRotation() !== newRotation ||\n      !this.getCenterInternal() ||\n      !equals(this.getCenterInternal(), newCenter)\n    ) {\n      if (this.getAnimating()) {\n        this.cancelAnimations();\n      }\n\n      this.animateInternal({\n        rotation: newRotation,\n        center: newCenter,\n        resolution: newResolution,\n        duration: duration,\n        easing: easeOut,\n        anchor: anchor,\n      });\n    }\n  }\n\n  /**\n   * Notify the View that an interaction has started.\n   * The view state will be resolved to a stable one if needed\n   * (depending on its constraints).\n   * @api\n   */\n  beginInteraction() {\n    this.resolveConstraints(0);\n\n    this.setHint(ViewHint.INTERACTING, 1);\n  }\n\n  /**\n   * Notify the View that an interaction has ended. The view state will be resolved\n   * to a stable one if needed (depending on its constraints).\n   * @param {number} [duration] Animation duration in ms.\n   * @param {number} [resolutionDirection] Which direction to zoom.\n   * @param {import(\"./coordinate.js\").Coordinate} [anchor] The origin of the transformation.\n   * @api\n   */\n  endInteraction(duration, resolutionDirection, anchor) {\n    anchor = anchor && fromUserCoordinate(anchor, this.getProjection());\n    this.endInteractionInternal(duration, resolutionDirection, anchor);\n  }\n\n  /**\n   * Notify the View that an interaction has ended. The view state will be resolved\n   * to a stable one if needed (depending on its constraints).\n   * @param {number} [duration] Animation duration in ms.\n   * @param {number} [resolutionDirection] Which direction to zoom.\n   * @param {import(\"./coordinate.js\").Coordinate} [anchor] The origin of the transformation.\n   */\n  endInteractionInternal(duration, resolutionDirection, anchor) {\n    if (!this.getInteracting()) {\n      return;\n    }\n    this.setHint(ViewHint.INTERACTING, -1);\n    this.resolveConstraints(duration, resolutionDirection, anchor);\n  }\n\n  /**\n   * Get a valid position for the view center according to the current constraints.\n   * @param {import(\"./coordinate.js\").Coordinate|undefined} targetCenter Target center position.\n   * @param {number} [targetResolution] Target resolution. If not supplied, the current one will be used.\n   * This is useful to guess a valid center position at a different zoom level.\n   * @return {import(\"./coordinate.js\").Coordinate|undefined} Valid center position.\n   */\n  getConstrainedCenter(targetCenter, targetResolution) {\n    const size = this.getViewportSize_(this.getRotation());\n    return this.constraints_.center(\n      targetCenter,\n      targetResolution || this.getResolution(),\n      size\n    );\n  }\n\n  /**\n   * Get a valid zoom level according to the current view constraints.\n   * @param {number|undefined} targetZoom Target zoom.\n   * @param {number} [direction=0] Indicate which resolution should be used\n   * by a renderer if the view resolution does not match any resolution of the tile source.\n   * If 0, the nearest resolution will be used. If 1, the nearest lower resolution\n   * will be used. If -1, the nearest higher resolution will be used.\n   * @return {number|undefined} Valid zoom level.\n   */\n  getConstrainedZoom(targetZoom, direction) {\n    const targetRes = this.getResolutionForZoom(targetZoom);\n    return this.getZoomForResolution(\n      this.getConstrainedResolution(targetRes, direction)\n    );\n  }\n\n  /**\n   * Get a valid resolution according to the current view constraints.\n   * @param {number|undefined} targetResolution Target resolution.\n   * @param {number} [direction=0] Indicate which resolution should be used\n   * by a renderer if the view resolution does not match any resolution of the tile source.\n   * If 0, the nearest resolution will be used. If 1, the nearest lower resolution\n   * will be used. If -1, the nearest higher resolution will be used.\n   * @return {number|undefined} Valid resolution.\n   */\n  getConstrainedResolution(targetResolution, direction) {\n    direction = direction || 0;\n    const size = this.getViewportSize_(this.getRotation());\n\n    return this.constraints_.resolution(targetResolution, direction, size);\n  }\n}\n\n/**\n * @param {Function} callback Callback.\n * @param {*} returnValue Return value.\n */\nfunction animationCallback(callback, returnValue) {\n  setTimeout(function () {\n    callback(returnValue);\n  }, 0);\n}\n\n/**\n * @param {ViewOptions} options View options.\n * @return {import(\"./centerconstraint.js\").Type} The constraint.\n */\nexport function createCenterConstraint(options) {\n  if (options.extent !== undefined) {\n    const smooth =\n      options.smoothExtentConstraint !== undefined\n        ? options.smoothExtentConstraint\n        : true;\n    return createExtent(options.extent, options.constrainOnlyCenter, smooth);\n  }\n\n  const projection = createProjection(options.projection, 'EPSG:3857');\n  if (options.multiWorld !== true && projection.isGlobal()) {\n    const extent = projection.getExtent().slice();\n    extent[0] = -Infinity;\n    extent[2] = Infinity;\n    return createExtent(extent, false, false);\n  }\n\n  return centerNone;\n}\n\n/**\n * @param {ViewOptions} options View options.\n * @return {{constraint: import(\"./resolutionconstraint.js\").Type, maxResolution: number,\n *     minResolution: number, minZoom: number, zoomFactor: number}} The constraint.\n */\nexport function createResolutionConstraint(options) {\n  let resolutionConstraint;\n  let maxResolution;\n  let minResolution;\n\n  // TODO: move these to be ol constants\n  // see https://github.com/openlayers/openlayers/issues/2076\n  const defaultMaxZoom = 28;\n  const defaultZoomFactor = 2;\n\n  let minZoom =\n    options.minZoom !== undefined ? options.minZoom : DEFAULT_MIN_ZOOM;\n\n  let maxZoom =\n    options.maxZoom !== undefined ? options.maxZoom : defaultMaxZoom;\n\n  const zoomFactor =\n    options.zoomFactor !== undefined ? options.zoomFactor : defaultZoomFactor;\n\n  const multiWorld =\n    options.multiWorld !== undefined ? options.multiWorld : false;\n\n  const smooth =\n    options.smoothResolutionConstraint !== undefined\n      ? options.smoothResolutionConstraint\n      : true;\n\n  const showFullExtent =\n    options.showFullExtent !== undefined ? options.showFullExtent : false;\n\n  const projection = createProjection(options.projection, 'EPSG:3857');\n  const projExtent = projection.getExtent();\n  let constrainOnlyCenter = options.constrainOnlyCenter;\n  let extent = options.extent;\n  if (!multiWorld && !extent && projection.isGlobal()) {\n    constrainOnlyCenter = false;\n    extent = projExtent;\n  }\n\n  if (options.resolutions !== undefined) {\n    const resolutions = options.resolutions;\n    maxResolution = resolutions[minZoom];\n    minResolution =\n      resolutions[maxZoom] !== undefined\n        ? resolutions[maxZoom]\n        : resolutions[resolutions.length - 1];\n\n    if (options.constrainResolution) {\n      resolutionConstraint = createSnapToResolutions(\n        resolutions,\n        smooth,\n        !constrainOnlyCenter && extent,\n        showFullExtent\n      );\n    } else {\n      resolutionConstraint = createMinMaxResolution(\n        maxResolution,\n        minResolution,\n        smooth,\n        !constrainOnlyCenter && extent,\n        showFullExtent\n      );\n    }\n  } else {\n    // calculate the default min and max resolution\n    const size = !projExtent\n      ? // use an extent that can fit the whole world if need be\n        (360 * METERS_PER_UNIT.degrees) / projection.getMetersPerUnit()\n      : Math.max(getWidth(projExtent), getHeight(projExtent));\n\n    const defaultMaxResolution =\n      size / DEFAULT_TILE_SIZE / Math.pow(defaultZoomFactor, DEFAULT_MIN_ZOOM);\n\n    const defaultMinResolution =\n      defaultMaxResolution /\n      Math.pow(defaultZoomFactor, defaultMaxZoom - DEFAULT_MIN_ZOOM);\n\n    // user provided maxResolution takes precedence\n    maxResolution = options.maxResolution;\n    if (maxResolution !== undefined) {\n      minZoom = 0;\n    } else {\n      maxResolution = defaultMaxResolution / Math.pow(zoomFactor, minZoom);\n    }\n\n    // user provided minResolution takes precedence\n    minResolution = options.minResolution;\n    if (minResolution === undefined) {\n      if (options.maxZoom !== undefined) {\n        if (options.maxResolution !== undefined) {\n          minResolution = maxResolution / Math.pow(zoomFactor, maxZoom);\n        } else {\n          minResolution = defaultMaxResolution / Math.pow(zoomFactor, maxZoom);\n        }\n      } else {\n        minResolution = defaultMinResolution;\n      }\n    }\n\n    // given discrete zoom levels, minResolution may be different than provided\n    maxZoom =\n      minZoom +\n      Math.floor(\n        Math.log(maxResolution / minResolution) / Math.log(zoomFactor)\n      );\n    minResolution = maxResolution / Math.pow(zoomFactor, maxZoom - minZoom);\n\n    if (options.constrainResolution) {\n      resolutionConstraint = createSnapToPower(\n        zoomFactor,\n        maxResolution,\n        minResolution,\n        smooth,\n        !constrainOnlyCenter && extent,\n        showFullExtent\n      );\n    } else {\n      resolutionConstraint = createMinMaxResolution(\n        maxResolution,\n        minResolution,\n        smooth,\n        !constrainOnlyCenter && extent,\n        showFullExtent\n      );\n    }\n  }\n  return {\n    constraint: resolutionConstraint,\n    maxResolution: maxResolution,\n    minResolution: minResolution,\n    minZoom: minZoom,\n    zoomFactor: zoomFactor,\n  };\n}\n\n/**\n * @param {ViewOptions} options View options.\n * @return {import(\"./rotationconstraint.js\").Type} Rotation constraint.\n */\nexport function createRotationConstraint(options) {\n  const enableRotation =\n    options.enableRotation !== undefined ? options.enableRotation : true;\n  if (enableRotation) {\n    const constrainRotation = options.constrainRotation;\n    if (constrainRotation === undefined || constrainRotation === true) {\n      return createSnapToZero();\n    }\n    if (constrainRotation === false) {\n      return rotationNone;\n    }\n    if (typeof constrainRotation === 'number') {\n      return createSnapToN(constrainRotation);\n    }\n    return rotationNone;\n  }\n  return disable;\n}\n\n/**\n * Determine if an animation involves no view change.\n * @param {Animation} animation The animation.\n * @return {boolean} The animation involves no view change.\n */\nexport function isNoopAnimation(animation) {\n  if (animation.sourceCenter && animation.targetCenter) {\n    if (!coordinatesEqual(animation.sourceCenter, animation.targetCenter)) {\n      return false;\n    }\n  }\n  if (animation.sourceResolution !== animation.targetResolution) {\n    return false;\n  }\n  if (animation.sourceRotation !== animation.targetRotation) {\n    return false;\n  }\n  return true;\n}\n\n/**\n * @param {import(\"./coordinate.js\").Coordinate} coordinate Coordinate.\n * @param {import(\"./size.js\").Size} size Box pixel size.\n * @param {import(\"./pixel.js\").Pixel} position Position on the view to center on.\n * @param {number} resolution Resolution.\n * @param {number} rotation Rotation.\n * @return {import(\"./coordinate.js\").Coordinate} Shifted center.\n */\nfunction calculateCenterOn(coordinate, size, position, resolution, rotation) {\n  // calculate rotated position\n  const cosAngle = Math.cos(-rotation);\n  let sinAngle = Math.sin(-rotation);\n  let rotX = coordinate[0] * cosAngle - coordinate[1] * sinAngle;\n  let rotY = coordinate[1] * cosAngle + coordinate[0] * sinAngle;\n  rotX += (size[0] / 2 - position[0]) * resolution;\n  rotY += (position[1] - size[1] / 2) * resolution;\n\n  // go back to original angle\n  sinAngle = -sinAngle; // go back to original rotation\n  const centerX = rotX * cosAngle - rotY * sinAngle;\n  const centerY = rotY * cosAngle + rotX * sinAngle;\n\n  return [centerX, centerY];\n}\n\nexport default View;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAO,mBAAQ;AAAA,EACb,WAAW;AAAA,EACX,aAAa;AACf;;;ACHA,IAAO,uBAAQ;AAAA,EACb,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AACZ;;;ACKO,SAAS,aAAa,QAAQ,YAAY,QAAQ;AACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASE,SAAU,QAAQ,YAAY,MAAM,UAAU,aAAa;AACzD,UAAI,CAAC,QAAQ;AACX,eAAO;AAAA,MACT;AACA,UAAI,CAAC,cAAc,CAAC,YAAY;AAC9B,eAAO;AAAA,MACT;AACA,YAAM,YAAY,aAAa,IAAI,KAAK,CAAC,IAAI;AAC7C,YAAM,aAAa,aAAa,IAAI,KAAK,CAAC,IAAI;AAC9C,YAAM,SAAS,cAAc,YAAY,CAAC,IAAI;AAC9C,YAAM,SAAS,cAAc,YAAY,CAAC,IAAI;AAC9C,UAAI,OAAO,OAAO,CAAC,IAAI,YAAY,IAAI;AACvC,UAAI,OAAO,OAAO,CAAC,IAAI,YAAY,IAAI;AACvC,UAAI,OAAO,OAAO,CAAC,IAAI,aAAa,IAAI;AACxC,UAAI,OAAO,OAAO,CAAC,IAAI,aAAa,IAAI;AAIxC,UAAI,OAAO,MAAM;AACf,gBAAQ,OAAO,QAAQ;AACvB,eAAO;AAAA,MACT;AACA,UAAI,OAAO,MAAM;AACf,gBAAQ,OAAO,QAAQ;AACvB,eAAO;AAAA,MACT;AAEA,UAAI,IAAI,MAAM,OAAO,CAAC,GAAG,MAAM,IAAI;AACnC,UAAI,IAAI,MAAM,OAAO,CAAC,GAAG,MAAM,IAAI;AAGnC,UAAI,YAAY,UAAU,YAAY;AACpC,cAAM,QAAQ,KAAK;AACnB,aACE,CAAC,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,OAAO,OAAO,CAAC,CAAC,IAAI,KAAK,IAC3D,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,KAAK;AAC5D,aACE,CAAC,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,OAAO,OAAO,CAAC,CAAC,IAAI,KAAK,IAC3D,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,KAAK;AAAA,MAC9D;AAEA,aAAO,CAAC,GAAG,CAAC;AAAA,IACd;AAAA;AAEJ;AAMO,SAAS,KAAK,QAAQ;AAC3B,SAAO;AACT;;;AC1DA,SAAS,6BACP,YACA,WACA,cACA,gBACA;AACA,QAAM,cAAc,SAAS,SAAS,IAAI,aAAa,CAAC;AACxD,QAAM,cAAc,UAAU,SAAS,IAAI,aAAa,CAAC;AAEzD,MAAI,gBAAgB;AAClB,WAAO,KAAK,IAAI,YAAY,KAAK,IAAI,aAAa,WAAW,CAAC;AAAA,EAChE;AACA,SAAO,KAAK,IAAI,YAAY,KAAK,IAAI,aAAa,WAAW,CAAC;AAChE;AAcA,SAAS,2BAA2B,YAAY,eAAe,eAAe;AAC5E,MAAI,SAAS,KAAK,IAAI,YAAY,aAAa;AAC/C,QAAM,QAAQ;AAEd,YACE,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,GAAG,aAAa,gBAAgB,CAAC,CAAC,IAAI,QACpE;AACF,MAAI,eAAe;AACjB,aAAS,KAAK,IAAI,QAAQ,aAAa;AACvC,cACE,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,GAAG,gBAAgB,aAAa,CAAC,CAAC,IAC9D,QACF;AAAA,EACJ;AACA,SAAO,MAAM,QAAQ,gBAAgB,GAAG,gBAAgB,CAAC;AAC3D;AASO,SAAS,wBACd,aACA,QACA,WACA,gBACA;AACA,WAAS,WAAW,SAAY,SAAS;AACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQE,SAAU,YAAY,WAAW,MAAM,UAAU;AAC/C,UAAI,eAAe,QAAW;AAC5B,cAAM,gBAAgB,YAAY,CAAC;AACnC,cAAM,gBAAgB,YAAY,YAAY,SAAS,CAAC;AACxD,cAAM,eAAe,YACjB;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IACA;AAGJ,YAAI,UAAU;AACZ,cAAI,CAAC,QAAQ;AACX,mBAAO,MAAM,YAAY,eAAe,YAAY;AAAA,UACtD;AACA,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAEA,cAAM,SAAS,KAAK,IAAI,cAAc,UAAU;AAChD,cAAM,IAAI,KAAK,MAAM,kBAAkB,aAAa,QAAQ,SAAS,CAAC;AACtE,YAAI,YAAY,CAAC,IAAI,gBAAgB,IAAI,YAAY,SAAS,GAAG;AAC/D,iBAAO,YAAY,IAAI,CAAC;AAAA,QAC1B;AACA,eAAO,YAAY,CAAC;AAAA,MACtB;AACA,aAAO;AAAA,IACT;AAAA;AAEJ;AAWO,SAAS,kBACd,OACA,eACA,eACA,QACA,WACA,gBACA;AACA,WAAS,WAAW,SAAY,SAAS;AACzC,kBAAgB,kBAAkB,SAAY,gBAAgB;AAE9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQE,SAAU,YAAY,WAAW,MAAM,UAAU;AAC/C,UAAI,eAAe,QAAW;AAC5B,cAAM,eAAe,YACjB;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IACA;AAGJ,YAAI,UAAU;AACZ,cAAI,CAAC,QAAQ;AACX,mBAAO,MAAM,YAAY,eAAe,YAAY;AAAA,UACtD;AACA,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAEA,cAAM,YAAY;AAClB,cAAM,eAAe,KAAK;AAAA,UACxB,KAAK,IAAI,gBAAgB,YAAY,IAAI,KAAK,IAAI,KAAK,IAAI;AAAA,QAC7D;AACA,cAAM,SAAS,CAAC,aAAa,MAAM,aAAa;AAChD,cAAM,SAAS,KAAK,IAAI,cAAc,UAAU;AAChD,cAAM,kBAAkB,KAAK;AAAA,UAC3B,KAAK,IAAI,gBAAgB,MAAM,IAAI,KAAK,IAAI,KAAK,IAAI;AAAA,QACvD;AACA,cAAM,YAAY,KAAK,IAAI,cAAc,eAAe;AACxD,cAAM,gBAAgB,gBAAgB,KAAK,IAAI,OAAO,SAAS;AAC/D,eAAO,MAAM,eAAe,eAAe,YAAY;AAAA,MACzD;AACA,aAAO;AAAA,IACT;AAAA;AAEJ;AAUO,SAAS,uBACd,eACA,eACA,QACA,WACA,gBACA;AACA,WAAS,WAAW,SAAY,SAAS;AAEzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQE,SAAU,YAAY,WAAW,MAAM,UAAU;AAC/C,UAAI,eAAe,QAAW;AAC5B,cAAM,eAAe,YACjB;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IACA;AAEJ,YAAI,CAAC,UAAU,CAAC,UAAU;AACxB,iBAAO,MAAM,YAAY,eAAe,YAAY;AAAA,QACtD;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAEJ;;;ACnOO,SAAS,QAAQ,UAAU;AAChC,MAAI,aAAa,QAAW;AAC1B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAMO,SAASA,MAAK,UAAU;AAC7B,MAAI,aAAa,QAAW;AAC1B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAMO,SAAS,cAAc,GAAG;AAC/B,QAAM,QAAS,IAAI,KAAK,KAAM;AAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAME,SAAU,UAAU,UAAU;AAC5B,UAAI,UAAU;AACZ,eAAO;AAAA,MACT;AAEA,UAAI,aAAa,QAAW;AAC1B,mBAAW,KAAK,MAAM,WAAW,QAAQ,GAAG,IAAI;AAChD,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA;AAEJ;AAMO,SAAS,iBAAiB,WAAW;AAC1C,cAAY,aAAa,UAAU,CAAC;AACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAME,SAAU,UAAU,UAAU;AAC5B,UAAI,UAAU;AACZ,eAAO;AAAA,MACT;AAEA,UAAI,aAAa,QAAW;AAC1B,YAAI,KAAK,IAAI,QAAQ,KAAK,WAAW;AACnC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA;AAEJ;;;AC+IA,IAAM,mBAAmB;AAqFzB,IAAM,OAAN,cAAmB,eAAW;AAAA;AAAA;AAAA;AAAA,EAI5B,YAAY,SAAS;AACnB,UAAM;AAKN,SAAK;AAKL,SAAK;AAKL,SAAK;AAEL,cAAU,OAAO,OAAO,CAAC,GAAG,OAAO;AAMnC,SAAK,SAAS,CAAC,GAAG,CAAC;AAMnB,SAAK,cAAc,CAAC;AAMpB,SAAK;AAOL,SAAK,cAAc,iBAAiB,QAAQ,YAAY,WAAW;AAMnE,SAAK,gBAAgB,CAAC,KAAK,GAAG;AAM9B,SAAK,gBAAgB;AAMrB,SAAK;AAML,SAAK;AAML,SAAK,cAAc;AAMnB,SAAK;AAML,SAAK;AAML,SAAK,gBAAgB;AAErB,QAAI,QAAQ,YAAY;AACtB,+BAAyB;AAAA,IAC3B;AACA,QAAI,QAAQ,QAAQ;AAClB,cAAQ,SAAS,mBAAmB,QAAQ,QAAQ,KAAK,WAAW;AAAA,IACtE;AACA,QAAI,QAAQ,QAAQ;AAClB,cAAQ,SAAS,eAAe,QAAQ,QAAQ,KAAK,WAAW;AAAA,IAClE;AAEA,SAAK,cAAc,OAAO;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,SAAS;AACrB,UAAM,aAAa,OAAO,OAAO,CAAC,GAAG,OAAO;AAC5C,eAAW,OAAO,sBAAc;AAC9B,aAAO,WAAW,GAAG;AAAA,IACvB;AACA,SAAK,cAAc,YAAY,IAAI;AAEnC,UAAM,2BAA2B,2BAA2B,OAAO;AAMnE,SAAK,iBAAiB,yBAAyB;AAM/C,SAAK,iBAAiB,yBAAyB;AAM/C,SAAK,cAAc,yBAAyB;AAM5C,SAAK,eAAe,QAAQ;AAM5B,SAAK,WAAW,QAAQ;AAMxB,SAAK,WAAW,yBAAyB;AAEzC,UAAM,mBAAmB,uBAAuB,OAAO;AACvD,UAAM,uBAAuB,yBAAyB;AACtD,UAAM,qBAAqB,yBAAyB,OAAO;AAM3D,SAAK,eAAe;AAAA,MAClB,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ;AAEA,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW,CAAC;AACtE,SAAK;AAAA,MACH,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAAA,IAClD;AACA,QAAI,QAAQ,eAAe,QAAW;AACpC,WAAK,cAAc,QAAQ,UAAU;AAAA,IACvC,WAAW,QAAQ,SAAS,QAAW;AACrC,WAAK,QAAQ,QAAQ,IAAI;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,QAAI,aAAa,KAAK;AACtB,SAAK,WAAW;AAChB,UAAM,SAAS,KAAK,kBAAkB;AACtC,QAAI,QAAQ;AACV,YAAM,aAAa,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC;AACzC,mBAAa,cAAc,CAAC,GAAG,GAAG,GAAG,CAAC;AACtC,YAAM,aAAa,KAAK,cAAc;AACtC,YAAM,UACH,aAAa,KACb,WAAW,CAAC,IAAI,WAAW,CAAC,IAAI,WAAW,CAAC,IAAI,WAAW,CAAC;AAC/D,YAAM,UACH,aAAa,KACb,WAAW,CAAC,IAAI,WAAW,CAAC,IAAI,WAAW,CAAC,IAAI,WAAW,CAAC;AAC/D,WAAK,kBAAkB,CAAC,OAAO,CAAC,IAAI,SAAS,OAAO,CAAC,IAAI,OAAO,CAAC;AAAA,IACnE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,mBAAmB,YAAY;AAC7B,UAAM,UAAU,KAAK,cAAc;AAGnC,QAAI,QAAQ,eAAe,QAAW;AACpC,cAAQ,aAAa,KAAK,cAAc;AAAA,IAC1C,OAAO;AACL,cAAQ,OAAO,KAAK,QAAQ;AAAA,IAC9B;AAGA,YAAQ,SAAS,KAAK,kBAAkB;AAGxC,YAAQ,WAAW,KAAK,YAAY;AAEpC,WAAO,OAAO,OAAO,CAAC,GAAG,SAAS,UAAU;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmCA,QAAQ,UAAU;AAChB,QAAI,KAAK,MAAM,KAAK,CAAC,KAAK,aAAa,GAAG;AACxC,WAAK,mBAAmB,CAAC;AAAA,IAC3B;AACA,UAAM,OAAO,IAAI,MAAM,UAAU,MAAM;AACvC,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,UAAI,UAAU,UAAU,CAAC;AACzB,UAAI,QAAQ,QAAQ;AAClB,kBAAU,OAAO,OAAO,CAAC,GAAG,OAAO;AACnC,gBAAQ,SAAS;AAAA,UACf,QAAQ;AAAA,UACR,KAAK,cAAc;AAAA,QACrB;AAAA,MACF;AACA,UAAI,QAAQ,QAAQ;AAClB,kBAAU,OAAO,OAAO,CAAC,GAAG,OAAO;AACnC,gBAAQ,SAAS;AAAA,UACf,QAAQ;AAAA,UACR,KAAK,cAAc;AAAA,QACrB;AAAA,MACF;AACA,WAAK,CAAC,IAAI;AAAA,IACZ;AACA,SAAK,gBAAgB,MAAM,MAAM,IAAI;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,UAAU;AACxB,QAAI,iBAAiB,UAAU;AAC/B,QAAI;AACJ,QACE,iBAAiB,KACjB,OAAO,UAAU,iBAAiB,CAAC,MAAM,YACzC;AACA,iBAAW,UAAU,iBAAiB,CAAC;AACvC,QAAE;AAAA,IACJ;AAEA,QAAI,IAAI;AACR,WAAO,IAAI,kBAAkB,CAAC,KAAK,MAAM,GAAG,EAAE,GAAG;AAE/C,YAAM,QAAQ,UAAU,CAAC;AACzB,UAAI,MAAM,QAAQ;AAChB,aAAK,kBAAkB,MAAM,MAAM;AAAA,MACrC;AACA,UAAI,MAAM,SAAS,QAAW;AAC5B,aAAK,QAAQ,MAAM,IAAI;AAAA,MACzB,WAAW,MAAM,YAAY;AAC3B,aAAK,cAAc,MAAM,UAAU;AAAA,MACrC;AACA,UAAI,MAAM,aAAa,QAAW;AAChC,aAAK,YAAY,MAAM,QAAQ;AAAA,MACjC;AAAA,IACF;AACA,QAAI,MAAM,gBAAgB;AACxB,UAAI,UAAU;AACZ,0BAAkB,UAAU,IAAI;AAAA,MAClC;AACA;AAAA,IACF;AAEA,QAAI,QAAQ,KAAK,IAAI;AACrB,QAAI,SAAS,KAAK,cAAc,MAAM;AACtC,QAAI,aAAa,KAAK;AACtB,QAAI,WAAW,KAAK;AACpB,UAAM,SAAS,CAAC;AAChB,WAAO,IAAI,gBAAgB,EAAE,GAAG;AAC9B,YAAM;AAAA;AAAA,QAA2C,UAAU,CAAC;AAAA;AAE5D,YAAM,YAAY;AAAA,QAChB;AAAA,QACA,UAAU;AAAA,QACV,QAAQ,QAAQ;AAAA,QAChB,UAAU,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAAA,QAC9D,QAAQ,QAAQ,UAAU;AAAA,QAC1B;AAAA,MACF;AAEA,UAAI,QAAQ,QAAQ;AAClB,kBAAU,eAAe;AACzB,kBAAU,eAAe,QAAQ,OAAO,MAAM;AAC9C,iBAAS,UAAU;AAAA,MACrB;AAEA,UAAI,QAAQ,SAAS,QAAW;AAC9B,kBAAU,mBAAmB;AAC7B,kBAAU,mBAAmB,KAAK,qBAAqB,QAAQ,IAAI;AACnE,qBAAa,UAAU;AAAA,MACzB,WAAW,QAAQ,YAAY;AAC7B,kBAAU,mBAAmB;AAC7B,kBAAU,mBAAmB,QAAQ;AACrC,qBAAa,UAAU;AAAA,MACzB;AAEA,UAAI,QAAQ,aAAa,QAAW;AAClC,kBAAU,iBAAiB;AAC3B,cAAM,QACJ,OAAO,QAAQ,WAAW,WAAW,KAAK,IAAI,IAAI,KAAK,EAAE,IAAI,KAAK;AACpE,kBAAU,iBAAiB,WAAW;AACtC,mBAAW,UAAU;AAAA,MACvB;AAGA,UAAI,gBAAgB,SAAS,GAAG;AAC9B,kBAAU,WAAW;AAAA,MAEvB,OAAO;AACL,iBAAS,UAAU;AAAA,MACrB;AACA,aAAO,KAAK,SAAS;AAAA,IACvB;AACA,SAAK,YAAY,KAAK,MAAM;AAC5B,SAAK,QAAQ,iBAAS,WAAW,CAAC;AAClC,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe;AACb,WAAO,KAAK,OAAO,iBAAS,SAAS,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,WAAO,KAAK,OAAO,iBAAS,WAAW,IAAI;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB;AACjB,SAAK,QAAQ,iBAAS,WAAW,CAAC,KAAK,OAAO,iBAAS,SAAS,CAAC;AACjE,QAAI;AACJ,aAAS,IAAI,GAAG,KAAK,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACzD,YAAM,SAAS,KAAK,YAAY,CAAC;AACjC,UAAI,OAAO,CAAC,EAAE,UAAU;AACtB,0BAAkB,OAAO,CAAC,EAAE,UAAU,KAAK;AAAA,MAC7C;AACA,UAAI,CAAC,QAAQ;AACX,iBAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,gBAAM,YAAY,OAAO,CAAC;AAC1B,cAAI,CAAC,UAAU,UAAU;AACvB,qBAAS,UAAU;AACnB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,YAAY,SAAS;AAC1B,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,QAAI,KAAK,wBAAwB,QAAW;AAC1C,2BAAqB,KAAK,mBAAmB;AAC7C,WAAK,sBAAsB;AAAA,IAC7B;AACA,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB;AAAA,IACF;AACA,UAAM,MAAM,KAAK,IAAI;AACrB,QAAI,OAAO;AACX,aAAS,IAAI,KAAK,YAAY,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACrD,YAAM,SAAS,KAAK,YAAY,CAAC;AACjC,UAAI,iBAAiB;AACrB,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,cAAM,YAAY,OAAO,CAAC;AAC1B,YAAI,UAAU,UAAU;AACtB;AAAA,QACF;AACA,cAAM,UAAU,MAAM,UAAU;AAChC,YAAI,WACF,UAAU,WAAW,IAAI,UAAU,UAAU,WAAW;AAC1D,YAAI,YAAY,GAAG;AACjB,oBAAU,WAAW;AACrB,qBAAW;AAAA,QACb,OAAO;AACL,2BAAiB;AAAA,QACnB;AACA,cAAM,WAAW,UAAU,OAAO,QAAQ;AAC1C,YAAI,UAAU,cAAc;AAC1B,gBAAM,KAAK,UAAU,aAAa,CAAC;AACnC,gBAAM,KAAK,UAAU,aAAa,CAAC;AACnC,gBAAM,KAAK,UAAU,aAAa,CAAC;AACnC,gBAAM,KAAK,UAAU,aAAa,CAAC;AACnC,eAAK,cAAc,UAAU;AAC7B,gBAAM,IAAI,KAAK,YAAY,KAAK;AAChC,gBAAM,IAAI,KAAK,YAAY,KAAK;AAChC,eAAK,gBAAgB,CAAC,GAAG,CAAC;AAAA,QAC5B;AACA,YAAI,UAAU,oBAAoB,UAAU,kBAAkB;AAC5D,gBAAM,aACJ,aAAa,IACT,UAAU,mBACV,UAAU,mBACV,YACG,UAAU,mBAAmB,UAAU;AAChD,cAAI,UAAU,QAAQ;AACpB,kBAAM,OAAO,KAAK,iBAAiB,KAAK,YAAY,CAAC;AACrD,kBAAM,wBAAwB,KAAK,aAAa;AAAA,cAC9C;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AACA,iBAAK,gBAAgB,KAAK;AAAA,cACxB;AAAA,cACA,UAAU;AAAA,YACZ;AAAA,UACF;AACA,eAAK,kBAAkB,UAAU;AACjC,eAAK,oBAAoB;AACzB,eAAK,kBAAkB,IAAI;AAAA,QAC7B;AACA,YACE,UAAU,mBAAmB,UAC7B,UAAU,mBAAmB,QAC7B;AACA,gBAAM,WACJ,aAAa,IACT,OAAO,UAAU,iBAAiB,KAAK,IAAI,IAAI,KAAK,EAAE,IACtD,KAAK,KACL,UAAU,iBACV,YACG,UAAU,iBAAiB,UAAU;AAC9C,cAAI,UAAU,QAAQ;AACpB,kBAAM,sBAAsB,KAAK,aAAa;AAAA,cAC5C;AAAA,cACA;AAAA,YACF;AACA,iBAAK,gBAAgB,KAAK;AAAA,cACxB;AAAA,cACA,UAAU;AAAA,YACZ;AAAA,UACF;AACA,eAAK,gBAAgB,UAAU;AAC/B,eAAK,kBAAkB;AAAA,QACzB;AACA,aAAK,kBAAkB,IAAI;AAC3B,eAAO;AACP,YAAI,CAAC,UAAU,UAAU;AACvB;AAAA,QACF;AAAA,MACF;AACA,UAAI,gBAAgB;AAClB,aAAK,YAAY,CAAC,IAAI;AACtB,aAAK,QAAQ,iBAAS,WAAW,EAAE;AACnC,aAAK,cAAc;AACnB,aAAK,kBAAkB;AACvB,aAAK,gBAAgB;AACrB,cAAM,WAAW,OAAO,CAAC,EAAE;AAC3B,YAAI,UAAU;AACZ,4BAAkB,UAAU,IAAI;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AAEA,SAAK,cAAc,KAAK,YAAY,OAAO,OAAO;AAClD,QAAI,QAAQ,KAAK,wBAAwB,QAAW;AAClD,WAAK,sBAAsB;AAAA,QACzB,KAAK,kBAAkB,KAAK,IAAI;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,UAAU,QAAQ;AACtC,QAAI;AACJ,UAAM,gBAAgB,KAAK,kBAAkB;AAC7C,QAAI,kBAAkB,QAAW;AAC/B,eAAS,CAAC,cAAc,CAAC,IAAI,OAAO,CAAC,GAAG,cAAc,CAAC,IAAI,OAAO,CAAC,CAAC;AACpE,aAAiB,QAAQ,WAAW,KAAK,YAAY,CAAC;AACtD,UAAc,QAAQ,MAAM;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,YAAY,QAAQ;AACtC,QAAI;AACJ,UAAM,gBAAgB,KAAK,kBAAkB;AAC7C,UAAM,oBAAoB,KAAK,cAAc;AAC7C,QAAI,kBAAkB,UAAa,sBAAsB,QAAW;AAClE,YAAM,IACJ,OAAO,CAAC,IACP,cAAc,OAAO,CAAC,IAAI,cAAc,CAAC,KAAM;AAClD,YAAM,IACJ,OAAO,CAAC,IACP,cAAc,OAAO,CAAC,IAAI,cAAc,CAAC,KAAM;AAClD,eAAS,CAAC,GAAG,CAAC;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,UAAU;AACzB,UAAM,OAAO,KAAK;AAClB,QAAI,UAAU;AACZ,YAAM,IAAI,KAAK,CAAC;AAChB,YAAM,IAAI,KAAK,CAAC;AAChB,aAAO;AAAA,QACL,KAAK,IAAI,IAAI,KAAK,IAAI,QAAQ,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,QAAQ,CAAC;AAAA,QAClE,KAAK,IAAI,IAAI,KAAK,IAAI,QAAQ,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,QAAQ,CAAC;AAAA,MACpE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,MAAM;AACpB,SAAK,gBAAgB,MAAM,QAAQ,IAAI,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,GAAG;AACnE,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,WAAK,mBAAmB,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV,UAAM,SAAS,KAAK,kBAAkB;AACtC,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB,QAAQ,KAAK,cAAc,CAAC;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB;AAAA;AAAA,MACE,KAAK,IAAI,qBAAa,MAAM;AAAA;AAAA,EAEhC;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACvB,WAAO,KAAK,IAAI,qBAAqB;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO;AACd,QAAI,UAAU,QAAW;AACvB,YAAM,CAAC,IAAI,KAAK,OAAO,CAAC;AACxB,YAAM,CAAC,IAAI,KAAK,OAAO,CAAC;AACxB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,OAAO,MAAM;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,gBAAgB,MAAM;AACpB,UAAM,SAAS,KAAK,wBAAwB,IAAI;AAChD,WAAO,aAAa,QAAQ,KAAK,cAAc,CAAC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB,MAAM;AAC5B,WAAO,QAAQ,KAAK,6BAA6B;AACjD,UAAM;AAAA;AAAA,MACJ,KAAK,kBAAkB;AAAA;AAEzB,WAAO,QAAQ,CAAC;AAChB,UAAM;AAAA;AAAA,MAAqC,KAAK,cAAc;AAAA;AAC9D,WAAO,eAAe,QAAW,CAAC;AAClC,UAAM;AAAA;AAAA,MAAmC,KAAK,YAAY;AAAA;AAC1D,WAAO,aAAa,QAAW,CAAC;AAEhC,WAAO,kBAAkB,QAAQ,YAAY,UAAU,IAAI;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX;AAAA;AAAA,MACE,KAAK,qBAAqB,KAAK,cAAc;AAAA;AAAA,EAEjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,MAAM;AACf,SAAK,cAAc,KAAK,mBAAmB,EAAC,SAAS,KAAI,CAAC,CAAC;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX;AAAA;AAAA,MACE,KAAK,qBAAqB,KAAK,cAAc;AAAA;AAAA,EAEjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,MAAM;AACf,SAAK,cAAc,KAAK,mBAAmB,EAAC,SAAS,KAAI,CAAC,CAAC;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,uBAAuB,SAAS;AAC9B,SAAK,cAAc,KAAK,mBAAmB,EAAC,qBAAqB,QAAO,CAAC,CAAC;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB;AACd;AAAA;AAAA,MAAwC,KAAK,IAAI,qBAAa,UAAU;AAAA;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,uBAAuB,QAAQ,MAAM;AACnC,WAAO,KAAK;AAAA,MACV,eAAe,QAAQ,KAAK,cAAc,CAAC;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,+BAA+B,QAAQ,MAAM;AAC3C,WAAO,QAAQ,KAAK,6BAA6B;AACjD,UAAM,cAAc,SAAS,MAAM,IAAI,KAAK,CAAC;AAC7C,UAAM,cAAc,UAAU,MAAM,IAAI,KAAK,CAAC;AAC9C,WAAO,KAAK,IAAI,aAAa,WAAW;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,8BAA8B,OAAO;AACnC,YAAQ,SAAS;AACjB,UAAM,gBAAgB,KAAK,yBAAyB,KAAK,cAAc;AACvE,UAAM,gBAAgB,KAAK;AAC3B,UAAM,MAAM,KAAK,IAAI,gBAAgB,aAAa,IAAI,KAAK,IAAI,KAAK;AACpE;AAAA;AAAA;AAAA;AAAA;AAAA,MAKE,SAAU,OAAO;AACf,cAAM,aAAa,gBAAgB,KAAK,IAAI,OAAO,QAAQ,GAAG;AAC9D,eAAO;AAAA,MACT;AAAA;AAAA,EAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc;AACZ;AAAA;AAAA,MAA8B,KAAK,IAAI,qBAAa,QAAQ;AAAA;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,8BAA8B,OAAO;AACnC,UAAM,WAAW,KAAK,IAAI,SAAS,CAAC;AACpC,UAAM,gBAAgB,KAAK,yBAAyB,KAAK,cAAc;AACvE,UAAM,gBAAgB,KAAK;AAC3B,UAAM,MAAM,KAAK,IAAI,gBAAgB,aAAa,IAAI;AACtD;AAAA;AAAA;AAAA;AAAA;AAAA,MAKE,SAAU,YAAY;AACpB,cAAM,QAAQ,KAAK,IAAI,gBAAgB,UAAU,IAAI,WAAW;AAChE,eAAO;AAAA,MACT;AAAA;AAAA,EAEJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,6BAA6B,UAAU;AACrC,QAAI,OAAO,KAAK,iBAAiB,QAAQ;AACzC,UAAM,UAAU,KAAK;AACrB,QAAI,SAAS;AACX,aAAO;AAAA,QACL,KAAK,CAAC,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,QAChC,KAAK,CAAC,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,MAClC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,WAAW,KAAK,YAAY;AAClC,QAAI;AAAA;AAAA,MACF,KAAK,kBAAkB;AAAA;AAEzB,UAAM,UAAU,KAAK;AACrB,QAAI,SAAS;AACX,YAAM,cAAc,KAAK,6BAA6B;AACtD,eAAS;AAAA,QACP;AAAA,QACA,KAAK,iBAAiB;AAAA,QACtB,CAAC,YAAY,CAAC,IAAI,IAAI,QAAQ,CAAC,GAAG,YAAY,CAAC,IAAI,IAAI,QAAQ,CAAC,CAAC;AAAA,QACjE;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,QAAQ,OAAO,MAAM,CAAC;AAAA,MACtB,YAAY,eAAe,SAAY,aAAa;AAAA,MACpD;AAAA,MACA,YAAY,KAAK;AAAA,MACjB,gBAAgB,KAAK;AAAA,MACrB,cAAc,KAAK;AAAA,MACnB;AAAA,MACA,MAAM,KAAK,QAAQ;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,WAAO;AAAA,MACL,WAAW,KAAK,SAAS;AAAA,MACzB,QAAQ,KAAK,gBAAgB;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU;AACR,QAAI;AACJ,UAAM,aAAa,KAAK,cAAc;AACtC,QAAI,eAAe,QAAW;AAC5B,aAAO,KAAK,qBAAqB,UAAU;AAAA,IAC7C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,YAAY;AAC/B,QAAI,SAAS,KAAK,YAAY;AAC9B,QAAI,KAAK;AACT,QAAI,KAAK,cAAc;AACrB,YAAM,UAAU,kBAAkB,KAAK,cAAc,YAAY,CAAC;AAClE,eAAS;AACT,YAAM,KAAK,aAAa,OAAO;AAC/B,UAAI,WAAW,KAAK,aAAa,SAAS,GAAG;AAC3C,qBAAa;AAAA,MACf,OAAO;AACL,qBAAa,MAAM,KAAK,aAAa,UAAU,CAAC;AAAA,MAClD;AAAA,IACF,OAAO;AACL,YAAM,KAAK;AACX,mBAAa,KAAK;AAAA,IACpB;AACA,WAAO,SAAS,KAAK,IAAI,MAAM,UAAU,IAAI,KAAK,IAAI,UAAU;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,MAAM;AACzB,QAAI,KAAK,cAAc;AACrB,UAAI,KAAK,aAAa,UAAU,GAAG;AACjC,eAAO;AAAA,MACT;AACA,YAAM,YAAY;AAAA,QAChB,KAAK,MAAM,IAAI;AAAA,QACf;AAAA,QACA,KAAK,aAAa,SAAS;AAAA,MAC7B;AACA,YAAM,aACJ,KAAK,aAAa,SAAS,IAAI,KAAK,aAAa,YAAY,CAAC;AAChE,aACE,KAAK,aAAa,SAAS,IAC3B,KAAK,IAAI,YAAY,MAAM,OAAO,WAAW,GAAG,CAAC,CAAC;AAAA,IAEtD;AACA,WACE,KAAK,iBAAiB,KAAK,IAAI,KAAK,aAAa,OAAO,KAAK,QAAQ;AAAA,EAEzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,IAAI,kBAAkB,SAAS;AAE7B,QAAI;AACJ;AAAA,MACE,MAAM,QAAQ,gBAAgB,KAC5B;AAAA,MAA0B,iBAAkB,0BAC1C;AAAA,MACJ;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,gBAAgB,GAAG;AACnC,aAAO,CAAC,QAAQ,gBAAgB,GAAG,EAAE;AACrC,YAAM,SAAS,eAAe,kBAAkB,KAAK,cAAc,CAAC;AACpE,iBAAW,WAAkB,MAAM;AAAA,IACrC,WAAW,iBAAiB,QAAQ,MAAM,UAAU;AAClD,YAAM,SAAS;AAAA,QACb,iBAAiB,UAAU;AAAA,QAC3B,KAAK,cAAc;AAAA,MACrB;AACA,iBAAW,WAAkB,MAAM;AACnC,eAAS,OAAO,KAAK,YAAY,GAAG,UAAU,MAAM,CAAC;AAAA,IACvD,OAAO;AACL,YAAM,iBAAiB,kBAAkB;AACzC,UAAI,gBAAgB;AAClB;AAAA,QACE,iBACG,MAAM,EACN,UAAU,gBAAgB,KAAK,cAAc,CAAC;AAAA,MAErD,OAAO;AACL,mBAAW;AAAA,MACb;AAAA,IACF;AAEA,SAAK,YAAY,UAAU,OAAO;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,yBAAyB,UAAU;AACjC,UAAM,WAAW,KAAK,YAAY;AAClC,UAAM,WAAW,KAAK,IAAI,QAAQ;AAClC,UAAM,WAAW,KAAK,IAAI,CAAC,QAAQ;AACnC,UAAM,SAAS,SAAS,mBAAmB;AAC3C,UAAM,SAAS,SAAS,UAAU;AAClC,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,UAAU;AACd,aAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK,QAAQ;AACvD,YAAM,OAAO,OAAO,CAAC,IAAI,WAAW,OAAO,IAAI,CAAC,IAAI;AACpD,YAAM,OAAO,OAAO,CAAC,IAAI,WAAW,OAAO,IAAI,CAAC,IAAI;AACpD,gBAAU,KAAK,IAAI,SAAS,IAAI;AAChC,gBAAU,KAAK,IAAI,SAAS,IAAI;AAChC,gBAAU,KAAK,IAAI,SAAS,IAAI;AAChC,gBAAU,KAAK,IAAI,SAAS,IAAI;AAAA,IAClC;AACA,WAAO,CAAC,SAAS,SAAS,SAAS,OAAO;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,UAAU,SAAS;AAC7B,cAAU,WAAW,CAAC;AACtB,QAAI,OAAO,QAAQ;AACnB,QAAI,CAAC,MAAM;AACT,aAAO,KAAK,6BAA6B;AAAA,IAC3C;AACA,UAAM,UACJ,QAAQ,YAAY,SAAY,QAAQ,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;AAC/D,UAAM,UAAU,QAAQ,YAAY,SAAY,QAAQ,UAAU;AAClE,QAAI;AACJ,QAAI,QAAQ,kBAAkB,QAAW;AACvC,sBAAgB,QAAQ;AAAA,IAC1B,WAAW,QAAQ,YAAY,QAAW;AACxC,sBAAgB,KAAK,qBAAqB,QAAQ,OAAO;AAAA,IAC3D,OAAO;AACL,sBAAgB;AAAA,IAClB;AAEA,UAAM,gBAAgB,KAAK,yBAAyB,QAAQ;AAG5D,QAAI,aAAa,KAAK,+BAA+B,eAAe;AAAA,MAClE,KAAK,CAAC,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,MAChC,KAAK,CAAC,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,IAClC,CAAC;AACD,iBAAa,MAAM,UAAU,IACzB,gBACA,KAAK,IAAI,YAAY,aAAa;AACtC,iBAAa,KAAK,yBAAyB,YAAY,UAAU,IAAI,CAAC;AAGtE,UAAM,WAAW,KAAK,YAAY;AAClC,UAAM,WAAW,KAAK,IAAI,QAAQ;AAClC,UAAM,WAAW,KAAK,IAAI,QAAQ;AAClC,UAAM,YAAY,UAAU,aAAa;AACzC,cAAU,CAAC,MAAO,QAAQ,CAAC,IAAI,QAAQ,CAAC,KAAK,IAAK;AAClD,cAAU,CAAC,MAAO,QAAQ,CAAC,IAAI,QAAQ,CAAC,KAAK,IAAK;AAClD,UAAM,UAAU,UAAU,CAAC,IAAI,WAAW,UAAU,CAAC,IAAI;AACzD,UAAM,UAAU,UAAU,CAAC,IAAI,WAAW,UAAU,CAAC,IAAI;AACzD,UAAM,SAAS,KAAK,qBAAqB,CAAC,SAAS,OAAO,GAAG,UAAU;AACvE,UAAM,WAAW,QAAQ,WAAW,QAAQ,WAAW;AAEvD,QAAI,QAAQ,aAAa,QAAW;AAClC,WAAK;AAAA,QACH;AAAA,UACE;AAAA,UACA;AAAA,UACA,UAAU,QAAQ;AAAA,UAClB,QAAQ,QAAQ;AAAA,QAClB;AAAA,QACA;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,oBAAoB;AACzB,WAAK,gBAAgB;AACrB,WAAK,kBAAkB,OAAO,IAAI;AAClC,wBAAkB,UAAU,IAAI;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,YAAY,MAAM,UAAU;AACnC,SAAK;AAAA,MACH,mBAAmB,YAAY,KAAK,cAAc,CAAC;AAAA,MACnD;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,YAAY,MAAM,UAAU;AAC3C,SAAK;AAAA,MACH;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK,cAAc;AAAA,QACnB,KAAK,YAAY;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,qBAAqB,QAAQ,YAAY,UAAU,MAAM;AACvD,QAAI;AACJ,UAAM,UAAU,KAAK;AACrB,QAAI,WAAW,QAAQ;AACrB,YAAM,cAAc,KAAK,6BAA6B,CAAC,QAAQ;AAC/D,YAAM,gBAAgB;AAAA,QACpB;AAAA,QACA;AAAA,QACA,CAAC,YAAY,CAAC,IAAI,IAAI,QAAQ,CAAC,GAAG,YAAY,CAAC,IAAI,IAAI,QAAQ,CAAC,CAAC;AAAA,QACjE;AAAA,QACA;AAAA,MACF;AACA,oBAAc;AAAA,QACZ,OAAO,CAAC,IAAI,cAAc,CAAC;AAAA,QAC3B,OAAO,CAAC,IAAI,cAAc,CAAC;AAAA,MAC7B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,WAAO,CAAC,CAAC,KAAK,kBAAkB,KAAK,KAAK,cAAc,MAAM;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,kBAAkB;AAC7B,UAAM,SAAS,iBAAiB,KAAK,eAAe,KAAK,cAAc,CAAC;AACxE,SAAK,UAAU;AAAA,MACb,OAAO,CAAC,IAAI,iBAAiB,CAAC;AAAA,MAC9B,OAAO,CAAC,IAAI,iBAAiB,CAAC;AAAA,IAChC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,kBAAkB;AACrC,UAAM,SAAS,KAAK;AACpB,SAAK,kBAAkB;AAAA,MACrB,OAAO,CAAC,IAAI,iBAAiB,CAAC;AAAA,MAC9B,OAAO,CAAC,IAAI,iBAAiB,CAAC;AAAA,IAChC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,OAAO,QAAQ;AAC9B,aAAS,UAAU,mBAAmB,QAAQ,KAAK,cAAc,CAAC;AAClE,SAAK,yBAAyB,OAAO,MAAM;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,yBAAyB,OAAO,QAAQ;AACtC,UAAM,WAAW,KAAK,aAAa,KAAK,KAAK,eAAe;AAC5D,UAAM,OAAO,KAAK,iBAAiB,KAAK,YAAY,CAAC;AACrD,UAAM,gBAAgB,KAAK,aAAa;AAAA,MACtC,KAAK,oBAAoB;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAI,QAAQ;AACV,WAAK,gBAAgB,KAAK,oBAAoB,eAAe,MAAM;AAAA,IACrE;AAEA,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,OAAO,QAAQ;AACxB,SAAK,iBAAiB,KAAK,IAAI,KAAK,aAAa,CAAC,KAAK,GAAG,MAAM;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,OAAO,QAAQ;AAC5B,QAAI,QAAQ;AACV,eAAS,mBAAmB,QAAQ,KAAK,cAAc,CAAC;AAAA,IAC1D;AACA,SAAK,uBAAuB,OAAO,MAAM;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,OAAO,QAAQ;AACpC,UAAM,WAAW,KAAK,aAAa,KAAK,KAAK,eAAe;AAC5D,UAAM,cAAc,KAAK,aAAa;AAAA,MACpC,KAAK,kBAAkB;AAAA,MACvB;AAAA,IACF;AACA,QAAI,QAAQ;AACV,WAAK,gBAAgB,KAAK,sBAAsB,aAAa,MAAM;AAAA,IACrE;AACA,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,SAAK;AAAA,MACH,SAAS,mBAAmB,QAAQ,KAAK,cAAc,CAAC,IAAI;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,QAAQ;AACxB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,MAAM,OAAO;AACnB,SAAK,OAAO,IAAI,KAAK;AACrB,SAAK,QAAQ;AACb,WAAO,KAAK,OAAO,IAAI;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,YAAY;AACxB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,UAAU;AACpB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,MAAM;AACZ,SAAK,cAAc,KAAK,qBAAqB,IAAI,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,kBAAkB,kBAAkB,aAAa;AAC/C,UAAM,WACJ,KAAK,aAAa,KAAK,KAAK,eAAe,KAAK;AAGlD,UAAM,cAAc,KAAK,aAAa;AAAA,MACpC,KAAK;AAAA,MACL;AAAA,IACF;AACA,UAAM,OAAO,KAAK,iBAAiB,WAAW;AAC9C,UAAM,gBAAgB,KAAK,aAAa;AAAA,MACtC,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,YAAY,KAAK,aAAa;AAAA,MAClC,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA,QACH,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,IAAI,qBAAa,QAAQ,MAAM,aAAa;AACnD,WAAK,IAAI,qBAAa,UAAU,WAAW;AAAA,IAC7C;AACA,QAAI,KAAK,IAAI,qBAAa,UAAU,MAAM,eAAe;AACvD,WAAK,IAAI,qBAAa,YAAY,aAAa;AAC/C,WAAK,IAAI,QAAQ,KAAK,QAAQ,GAAG,IAAI;AAAA,IACvC;AACA,QACE,CAAC,aACD,CAAC,KAAK,IAAI,qBAAa,MAAM,KAC7B,CAAC,OAAO,KAAK,IAAI,qBAAa,MAAM,GAAG,SAAS,GAChD;AACA,WAAK,IAAI,qBAAa,QAAQ,SAAS;AAAA,IACzC;AAEA,QAAI,KAAK,aAAa,KAAK,CAAC,kBAAkB;AAC5C,WAAK,iBAAiB;AAAA,IACxB;AACA,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,mBAAmB,UAAU,qBAAqB,QAAQ;AACxD,eAAW,aAAa,SAAY,WAAW;AAC/C,UAAM,YAAY,uBAAuB;AAEzC,UAAM,cAAc,KAAK,aAAa,SAAS,KAAK,eAAe;AACnE,UAAM,OAAO,KAAK,iBAAiB,WAAW;AAC9C,UAAM,gBAAgB,KAAK,aAAa;AAAA,MACtC,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AACA,UAAM,YAAY,KAAK,aAAa;AAAA,MAClC,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA,QACH,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,QAAI,aAAa,KAAK,CAAC,KAAK,eAAe;AACzC,WAAK,oBAAoB;AACzB,WAAK,kBAAkB;AACvB,WAAK,gBAAgB;AACrB,WAAK,kBAAkB;AACvB;AAAA,IACF;AAEA,aAAS,WAAW,aAAa,IAAI,KAAK,gBAAgB;AAC1D,SAAK,gBAAgB;AAErB,QACE,KAAK,cAAc,MAAM,iBACzB,KAAK,YAAY,MAAM,eACvB,CAAC,KAAK,kBAAkB,KACxB,CAAC,OAAO,KAAK,kBAAkB,GAAG,SAAS,GAC3C;AACA,UAAI,KAAK,aAAa,GAAG;AACvB,aAAK,iBAAiB;AAAA,MACxB;AAEA,WAAK,gBAAgB;AAAA,QACnB,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ;AAAA,QACA,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB;AACjB,SAAK,mBAAmB,CAAC;AAEzB,SAAK,QAAQ,iBAAS,aAAa,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,UAAU,qBAAqB,QAAQ;AACpD,aAAS,UAAU,mBAAmB,QAAQ,KAAK,cAAc,CAAC;AAClE,SAAK,uBAAuB,UAAU,qBAAqB,MAAM;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,uBAAuB,UAAU,qBAAqB,QAAQ;AAC5D,QAAI,CAAC,KAAK,eAAe,GAAG;AAC1B;AAAA,IACF;AACA,SAAK,QAAQ,iBAAS,aAAa,EAAE;AACrC,SAAK,mBAAmB,UAAU,qBAAqB,MAAM;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,qBAAqB,cAAc,kBAAkB;AACnD,UAAM,OAAO,KAAK,iBAAiB,KAAK,YAAY,CAAC;AACrD,WAAO,KAAK,aAAa;AAAA,MACvB;AAAA,MACA,oBAAoB,KAAK,cAAc;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,mBAAmB,YAAY,WAAW;AACxC,UAAM,YAAY,KAAK,qBAAqB,UAAU;AACtD,WAAO,KAAK;AAAA,MACV,KAAK,yBAAyB,WAAW,SAAS;AAAA,IACpD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,yBAAyB,kBAAkB,WAAW;AACpD,gBAAY,aAAa;AACzB,UAAM,OAAO,KAAK,iBAAiB,KAAK,YAAY,CAAC;AAErD,WAAO,KAAK,aAAa,WAAW,kBAAkB,WAAW,IAAI;AAAA,EACvE;AACF;AAMA,SAAS,kBAAkB,UAAU,aAAa;AAChD,aAAW,WAAY;AACrB,aAAS,WAAW;AAAA,EACtB,GAAG,CAAC;AACN;AAMO,SAAS,uBAAuB,SAAS;AAC9C,MAAI,QAAQ,WAAW,QAAW;AAChC,UAAM,SACJ,QAAQ,2BAA2B,SAC/B,QAAQ,yBACR;AACN,WAAO,aAAa,QAAQ,QAAQ,QAAQ,qBAAqB,MAAM;AAAA,EACzE;AAEA,QAAM,aAAa,iBAAiB,QAAQ,YAAY,WAAW;AACnE,MAAI,QAAQ,eAAe,QAAQ,WAAW,SAAS,GAAG;AACxD,UAAM,SAAS,WAAW,UAAU,EAAE,MAAM;AAC5C,WAAO,CAAC,IAAI;AACZ,WAAO,CAAC,IAAI;AACZ,WAAO,aAAa,QAAQ,OAAO,KAAK;AAAA,EAC1C;AAEA,SAAO;AACT;AAOO,SAAS,2BAA2B,SAAS;AAClD,MAAI;AACJ,MAAI;AACJ,MAAI;AAIJ,QAAM,iBAAiB;AACvB,QAAM,oBAAoB;AAE1B,MAAI,UACF,QAAQ,YAAY,SAAY,QAAQ,UAAU;AAEpD,MAAI,UACF,QAAQ,YAAY,SAAY,QAAQ,UAAU;AAEpD,QAAM,aACJ,QAAQ,eAAe,SAAY,QAAQ,aAAa;AAE1D,QAAM,aACJ,QAAQ,eAAe,SAAY,QAAQ,aAAa;AAE1D,QAAM,SACJ,QAAQ,+BAA+B,SACnC,QAAQ,6BACR;AAEN,QAAM,iBACJ,QAAQ,mBAAmB,SAAY,QAAQ,iBAAiB;AAElE,QAAM,aAAa,iBAAiB,QAAQ,YAAY,WAAW;AACnE,QAAM,aAAa,WAAW,UAAU;AACxC,MAAI,sBAAsB,QAAQ;AAClC,MAAI,SAAS,QAAQ;AACrB,MAAI,CAAC,cAAc,CAAC,UAAU,WAAW,SAAS,GAAG;AACnD,0BAAsB;AACtB,aAAS;AAAA,EACX;AAEA,MAAI,QAAQ,gBAAgB,QAAW;AACrC,UAAM,cAAc,QAAQ;AAC5B,oBAAgB,YAAY,OAAO;AACnC,oBACE,YAAY,OAAO,MAAM,SACrB,YAAY,OAAO,IACnB,YAAY,YAAY,SAAS,CAAC;AAExC,QAAI,QAAQ,qBAAqB;AAC/B,6BAAuB;AAAA,QACrB;AAAA,QACA;AAAA,QACA,CAAC,uBAAuB;AAAA,QACxB;AAAA,MACF;AAAA,IACF,OAAO;AACL,6BAAuB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA,CAAC,uBAAuB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AAEL,UAAM,OAAO,CAAC;AAAA;AAAA,MAET,MAAM,gBAAgB,UAAW,WAAW,iBAAiB;AAAA,QAC9D,KAAK,IAAI,SAAS,UAAU,GAAG,UAAU,UAAU,CAAC;AAExD,UAAM,uBACJ,OAAO,oBAAoB,KAAK,IAAI,mBAAmB,gBAAgB;AAEzE,UAAM,uBACJ,uBACA,KAAK,IAAI,mBAAmB,iBAAiB,gBAAgB;AAG/D,oBAAgB,QAAQ;AACxB,QAAI,kBAAkB,QAAW;AAC/B,gBAAU;AAAA,IACZ,OAAO;AACL,sBAAgB,uBAAuB,KAAK,IAAI,YAAY,OAAO;AAAA,IACrE;AAGA,oBAAgB,QAAQ;AACxB,QAAI,kBAAkB,QAAW;AAC/B,UAAI,QAAQ,YAAY,QAAW;AACjC,YAAI,QAAQ,kBAAkB,QAAW;AACvC,0BAAgB,gBAAgB,KAAK,IAAI,YAAY,OAAO;AAAA,QAC9D,OAAO;AACL,0BAAgB,uBAAuB,KAAK,IAAI,YAAY,OAAO;AAAA,QACrE;AAAA,MACF,OAAO;AACL,wBAAgB;AAAA,MAClB;AAAA,IACF;AAGA,cACE,UACA,KAAK;AAAA,MACH,KAAK,IAAI,gBAAgB,aAAa,IAAI,KAAK,IAAI,UAAU;AAAA,IAC/D;AACF,oBAAgB,gBAAgB,KAAK,IAAI,YAAY,UAAU,OAAO;AAEtE,QAAI,QAAQ,qBAAqB;AAC/B,6BAAuB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,CAAC,uBAAuB;AAAA,QACxB;AAAA,MACF;AAAA,IACF,OAAO;AACL,6BAAuB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA,CAAC,uBAAuB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAMO,SAAS,yBAAyB,SAAS;AAChD,QAAM,iBACJ,QAAQ,mBAAmB,SAAY,QAAQ,iBAAiB;AAClE,MAAI,gBAAgB;AAClB,UAAM,oBAAoB,QAAQ;AAClC,QAAI,sBAAsB,UAAa,sBAAsB,MAAM;AACjE,aAAO,iBAAiB;AAAA,IAC1B;AACA,QAAI,sBAAsB,OAAO;AAC/B,aAAOC;AAAA,IACT;AACA,QAAI,OAAO,sBAAsB,UAAU;AACzC,aAAO,cAAc,iBAAiB;AAAA,IACxC;AACA,WAAOA;AAAA,EACT;AACA,SAAO;AACT;AAOO,SAAS,gBAAgB,WAAW;AACzC,MAAI,UAAU,gBAAgB,UAAU,cAAc;AACpD,QAAI,CAAC,OAAiB,UAAU,cAAc,UAAU,YAAY,GAAG;AACrE,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,UAAU,qBAAqB,UAAU,kBAAkB;AAC7D,WAAO;AAAA,EACT;AACA,MAAI,UAAU,mBAAmB,UAAU,gBAAgB;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAUA,SAAS,kBAAkB,YAAY,MAAM,UAAU,YAAY,UAAU;AAE3E,QAAM,WAAW,KAAK,IAAI,CAAC,QAAQ;AACnC,MAAI,WAAW,KAAK,IAAI,CAAC,QAAQ;AACjC,MAAI,OAAO,WAAW,CAAC,IAAI,WAAW,WAAW,CAAC,IAAI;AACtD,MAAI,OAAO,WAAW,CAAC,IAAI,WAAW,WAAW,CAAC,IAAI;AACtD,WAAS,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC,KAAK;AACtC,WAAS,SAAS,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK;AAGtC,aAAW,CAAC;AACZ,QAAM,UAAU,OAAO,WAAW,OAAO;AACzC,QAAM,UAAU,OAAO,WAAW,OAAO;AAEzC,SAAO,CAAC,SAAS,OAAO;AAC1B;AAEA,IAAO,eAAQ;", "names": ["none", "none"]}