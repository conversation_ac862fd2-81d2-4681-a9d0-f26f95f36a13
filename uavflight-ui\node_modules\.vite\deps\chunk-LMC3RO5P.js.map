{"version": 3, "sources": ["../../ol/easing.js"], "sourcesContent": ["/**\n * @module ol/easing\n */\n\n/**\n * Start slow and speed up.\n * @param {number} t Input between 0 and 1.\n * @return {number} Output between 0 and 1.\n * @api\n */\nexport function easeIn(t) {\n  return Math.pow(t, 3);\n}\n\n/**\n * Start fast and slow down.\n * @param {number} t Input between 0 and 1.\n * @return {number} Output between 0 and 1.\n * @api\n */\nexport function easeOut(t) {\n  return 1 - easeIn(1 - t);\n}\n\n/**\n * Start slow, speed up, and then slow down again.\n * @param {number} t Input between 0 and 1.\n * @return {number} Output between 0 and 1.\n * @api\n */\nexport function inAndOut(t) {\n  return 3 * t * t - 2 * t * t * t;\n}\n\n/**\n * Maintain a constant speed over time.\n * @param {number} t Input between 0 and 1.\n * @return {number} Output between 0 and 1.\n * @api\n */\nexport function linear(t) {\n  return t;\n}\n\n/**\n * Start slow, speed up, and at the very end slow down again.  This has the\n * same general behavior as {@link module:ol/easing.inAndOut}, but the final\n * slowdown is delayed.\n * @param {number} t Input between 0 and 1.\n * @return {number} Output between 0 and 1.\n * @api\n */\nexport function upAndDown(t) {\n  if (t < 0.5) {\n    return inAndOut(2 * t);\n  }\n  return 1 - inAndOut(2 * (t - 0.5));\n}\n"], "mappings": ";AAUO,SAAS,OAAO,GAAG;AACxB,SAAO,KAAK,IAAI,GAAG,CAAC;AACtB;AAQO,SAAS,QAAQ,GAAG;AACzB,SAAO,IAAI,OAAO,IAAI,CAAC;AACzB;AAQO,SAAS,SAAS,GAAG;AAC1B,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACjC;AAQO,SAAS,OAAO,GAAG;AACxB,SAAO;AACT;", "names": []}