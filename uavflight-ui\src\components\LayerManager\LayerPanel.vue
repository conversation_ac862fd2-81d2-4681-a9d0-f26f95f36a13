<!--
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-06-04 15:25:29
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-06-27 16:31:59
 * @FilePath: \uavflight-ui\src\components\LayerManager\LayerPanel.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="layer-panel" :class="{ 'collapsed': isCollapsed }" ref="panelRef">
    <div class="panel-header" @mousedown="startDrag">
      <h3>图层管理</h3>
      <div class="panel-controls">
        <el-tooltip content="刷新图层" placement="top">
          <el-button size="small" circle @click="refreshLayers">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip :content="isCollapsed ? '展开' : '收起'" placement="top">
          <el-button size="small" circle @click="toggleCollapse">
            <el-icon><component :is="isCollapsed ? 'ArrowRight' : 'ArrowLeft'" /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="关闭" placement="top">
          <el-button size="small" circle @click="closePanel">
            <el-icon><Close /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>
    
    <div v-show="!isCollapsed" class="panel-content">
      <LayerList ref="layerListRef" />
    </div>
  </div>
</template>

<script setup lang="ts" name="LayerPanel">
import { ref, onMounted, onBeforeUnmount, defineComponent, defineEmits } from 'vue';
import { Refresh, ArrowRight, ArrowLeft, Close } from '@element-plus/icons-vue';
import LayerList from './LayerList.vue';

// 定义事件
const emit = defineEmits(['close']);

const isCollapsed = ref(false);
const layerListRef = ref();
const panelRef = ref<HTMLElement | null>(null);

// 拖拽相关变量
let isDragging = false;
let offsetX = 0;
let offsetY = 0;

// 开始拖拽
const startDrag = (e: MouseEvent) => {
  if (!panelRef.value) return;

  // 阻止事件冒泡和默认行为
  e.stopPropagation();
  e.preventDefault();
  
  // 计算鼠标点击位置与面板左上角的偏移量
  const rect = panelRef.value.getBoundingClientRect();
  offsetX = e.clientX - rect.left;
  offsetY = e.clientY - rect.top;
  
  isDragging = true;
  
  // 添加事件监听
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

// 鼠标移动处理
const handleMouseMove = (e: MouseEvent) => {
  if (!isDragging || !panelRef.value) return;
  
  e.preventDefault();
  
  // 计算新位置
  const x = e.clientX - offsetX;
  const y = e.clientY - offsetY;
  
  // 获取视口尺寸和面板尺寸
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  const panelWidth = panelRef.value.offsetWidth;
  const panelHeight = panelRef.value.offsetHeight;
  
  // 限制面板在视口范围内
  const boundedX = Math.max(0, Math.min(x, viewportWidth - panelWidth));
  const boundedY = Math.max(0, Math.min(y, viewportHeight - panelHeight));
  
  // 设置面板位置
  panelRef.value.style.left = `${boundedX}px`;
  panelRef.value.style.top = `${boundedY}px`;
  panelRef.value.style.right = 'auto'; // 清除right属性
};

// 鼠标释放处理
const handleMouseUp = () => {
  isDragging = false;
  
  // 移除事件监听
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
};

// 切换面板展开/收起状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 刷新图层列表
const refreshLayers = () => {
  if (layerListRef.value) {
    layerListRef.value.refreshLayers();
  }
};

// 关闭面板
const closePanel = () => {
  emit('close');
};

// 组件挂载时初始化
onMounted(() => {
  if (panelRef.value) {
    // 设置初始位置为左侧
    panelRef.value.style.top = '120px';
    panelRef.value.style.left = '20px';
    panelRef.value.style.right = 'auto';
  }
});

// 组件卸载前清理事件监听
onBeforeUnmount(() => {
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
});

// 添加默认导出
defineComponent({
  name: 'LayerPanel'
});
</script>

<style scoped>
.layer-panel {
  position: fixed; /* 使用fixed代替absolute，避免滚动影响 */
  width: 300px;
  background-color: rgba(2, 49, 52, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  transition: width 0.3s ease;
  max-height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
  cursor: move;
}

.layer-panel.collapsed {
  width: auto;
  cursor: default;
}

.panel-header {
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: move;
}

.panel-header h3 {
  margin: 0;
  color: #c8dede;
  font-size: 16px;
}

.panel-controls {
  display: flex;
  gap: 8px;
}

.panel-content {
  padding: 12px;
  overflow-y: auto;
  flex: 1;
  max-height: calc(100vh - 80px);
  cursor: default;
}
</style> 