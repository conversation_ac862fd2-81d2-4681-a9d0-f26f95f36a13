<template>
	<div class="layout-navbars-breadcrumb-user pr15" :style="{ flex: layoutUserFlexNum }">
		<div class="layout-navbars-breadcrumb-user-icon screen-stats" @click="bigScreenClick">
			<el-icon>
				<ele-Monitor />
			</el-icon>
			<span class="screen-text">大屏统计</span>
		</div>
		<div class="layout-navbars-breadcrumb-user-icon" @click="onLockClick">
			<el-icon :title="$t('layout.threeLockScreenTime')">
				<ele-Lock />
			</el-icon>
		</div>
		<div class="layout-navbars-breadcrumb-user-icon" @click="onSearchClick">
			<el-icon :title="$t('user.title2')">
				<ele-Search />
			</el-icon>
		</div>
		<div v-auth="'admin.per'" class="layout-navbars-breadcrumb-user-icon" @click="onLayoutSetingClick">
			<el-icon :title="$t('user.title3')">
				<ele-Setting />
			</el-icon>
		</div>
		<!-- <div class="layout-navbars-breadcrumb-user-icon">
			<el-popover placement="bottom" trigger="click" transition="el-zoom-in-top" :width="300" :persistent="false">
				<template #reference>
					<el-badge :is-dot="isDot">
						<el-icon :title="$t('user.title4')">
							<ele-Bell />
						</el-icon>
					</el-badge>
				</template>
				<template #default>
					<UserNews ref="newsRef" />
				</template>
			</el-popover>
		</div> -->
		<!-- <div style="margin-right: 10px">|</div> -->
		<!-- <div class="layout-navbars-breadcrumb-user-icon mr10" @click="onScreenfullClick">
			<i
				class="iconfont"
				:title="state.isScreenfull ? $t('user.title6') : $t('user.title5')"
				:class="!state.isScreenfull ? 'icon-fullscreen' : 'icon-tuichuquanping'"
			></i>
		</div> -->
		<el-dropdown :show-timeout="70" :hide-timeout="50" @command="onHandleCommandClick">
			<span class="layout-navbars-breadcrumb-user-link">
				<img :src="baseURL + userInfos.user.avatar" class="layout-navbars-breadcrumb-user-link-photo mr5" />
				{{ userInfos.deptName }}-{{ userInfos.user.name }}
				<el-icon class="el-icon--right">
					<ele-ArrowDown />
				</el-icon>
			</span>
			<template #dropdown>
				<el-dropdown-menu>
					<el-dropdown-item command="/home">{{ $t('user.dropdown1') }}</el-dropdown-item>
					<el-dropdown-item command="personal">{{ $t('user.dropdown2') }}</el-dropdown-item>
					<el-dropdown-item divided command="logOut">{{ $t('user.dropdown5') }}</el-dropdown-item>
				</el-dropdown-menu>
			</template>
		</el-dropdown>
		<Search ref="searchRef" />
		<global-websocket uri="/admin/ws/info" v-if="websocketEnable" @rollback="rollback" />
		<personal-drawer ref="personalDrawerRef"></personal-drawer>
	</div>
</template>

<script setup lang="ts" name="layoutBreadcrumbUser">
import { logout } from '/@/api/login';
import { ElMessageBox, ElMessage } from 'element-plus';
import screenfull from 'screenfull';
import { useI18n } from 'vue-i18n';
import { useUserInfo } from '/@/stores/userInfo';
import { useThemeConfig } from '/@/stores/themeConfig';
import other from '/@/utils/other';
import mittBus from '/@/utils/mitt';
import { Session, Local } from '/@/utils/storage';
import { formatAxis } from '/@/utils/formatTime';
import { useMsg } from '/@/stores/msg';

// 引入组件
const GlobalWebsocket = defineAsyncComponent(() => import('/@/components/Websocket/index.vue'));
const UserNews = defineAsyncComponent(() => import('/@/layout/navBars/breadcrumb/userNews.vue'));
const Search = defineAsyncComponent(() => import('/@/layout/navBars/breadcrumb/search.vue'));
const PersonalDrawer = defineAsyncComponent(() => import('/@/views/admin/user/personal.vue'));

// 定义变量内容
const { locale, t } = useI18n();
const router = useRouter();
const stores = useUserInfo();
const storesThemeConfig = useThemeConfig();
const { userInfos } = storeToRefs(stores);
const { themeConfig } = storeToRefs(storesThemeConfig);
const searchRef = ref();
const newsRef = ref();
const personalDrawerRef = ref();

interface State {
	[key: string]: boolean | string;
	isScreenfull: boolean;
	disabledI18n: string;
	disabledSize: string;
}

const state = reactive<State>({
	isScreenfull: false,
	disabledI18n: 'zh-cn',
	disabledSize: 'large',
});

// 是否开启websocket
const websocketEnable = ref(import.meta.env.VITE_WEBSOCKET_ENABLE === 'true');

// 设置分割样式
const layoutUserFlexNum = computed(() => {
	let num: string | number = '';
	const { layout, isClassicSplitMenu } = themeConfig.value;
	const layoutArr: string[] = ['defaults', 'columns'];
	if (layoutArr.includes(layout) || (layout === 'classic' && !isClassicSplitMenu)) num = '1';
	else num = '';
	return num;
});
// 全屏点击时
const onScreenfullClick = () => {
	if (!screenfull.isEnabled) {
		ElMessage.warning('暂不不支持全屏');
		return false;
	}
	screenfull.toggle();
	screenfull.on('change', () => {
		if (screenfull.isFullscreen) state.isScreenfull = true;
		else state.isScreenfull = false;
	});
};
// 布局配置 icon 点击时
const onLayoutSetingClick = () => {
	mittBus.emit('openSetingsDrawer');
};
// 下拉菜单点击时
const onHandleCommandClick = (path: string) => {
	if (path === 'logOut') {
		ElMessageBox({
			closeOnClickModal: false,
			closeOnPressEscape: false,
			title: t('user.logOutTitle'),
			message: t('user.logOutMessage'),
			showCancelButton: true,
			confirmButtonText: t('user.logOutConfirm'),
			cancelButtonText: t('user.logOutCancel'),
			buttonSize: 'default',
			beforeClose: (action, instance, done) => {
				if (action === 'confirm') {
					instance.confirmButtonLoading = true;
					instance.confirmButtonText = t('user.logOutExit');
					setTimeout(() => {
						done();
						setTimeout(() => {
							instance.confirmButtonLoading = false;
						}, 300);
					}, 700);
				} else {
					done();
				}
			},
		})
			.then(async () => {
				// 关闭全部的标签页
				mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 3, ...router }));
				// 调用注销token接口
				await logout();
				// 清除缓存/token等
				Session.clear();
				// 使用 reload 时，不需要调用 resetRoute() 重置路由
				window.location.reload();
			})
			.catch(() => {});
	} else if (path === 'personal') {
		// 打开个人页面
		personalDrawerRef.value.open();
	} else {
		router.push(path);
	}
};
// 菜单搜索点击
const onSearchClick = () => {
	searchRef.value.openSearch();
};
// 语言切换
const onLanguageChange = (lang: string) => {
	Local.remove('themeConfig');
	themeConfig.value.globalI18n = lang;
	Local.set('themeConfig', themeConfig.value);
	locale.value = lang;
	other.useTitle();
	initI18nOrSize('globalI18n', 'disabledI18n');
};
// 锁屏
const onLockClick = () => {
	themeConfig.value.isLockScreen = true;
	themeConfig.value.lockScreenTime = 0;
	Local.set('themeConfig', themeConfig.value);
};

// 跳转大屏
const bigScreenClick = () => {
	const { href } = router.resolve({
		path: '/bigScreen',
		// query: {},
	});
	window.open(href, '_blank');
};

// 初始化组件大小/i18n
const initI18nOrSize = (value: string, attr: string) => {
	state[attr] = Local.get('themeConfig')[value];
};

// 获取到消息
const rollback = (msg: string) => {
	useMsg().setMsg({ label: 'websocket消息', value: msg, time: formatAxis(new Date()) });
};

const isDot = computed(() => {
	return useMsg().getAllMsg().length > 0;
});

// 页面加载时
onMounted(() => {
	if (Local.get('themeConfig')) {
		initI18nOrSize('globalComponentSize', 'disabledSize');
		initI18nOrSize('globalI18n', 'disabledI18n');
	}
});
</script>

<style scoped lang="scss">
.layout-navbars-breadcrumb-user {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	gap: 6px;

	&-link {
		height: 32px;
		display: flex;
		align-items: center;
		white-space: nowrap;
		padding: 0 16px;
		border: 1px solid rgba(108, 117, 125, 0.3);
		border-radius: 16px;
		background: linear-gradient(135deg, rgba(248, 249, 250, 0.1) 0%, rgba(248, 249, 250, 0.2) 100%);
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		position: relative;
		overflow: hidden;
		cursor: pointer;
		color: #6c757d;
		font-size: 13px;
		font-weight: 500;

		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: -100%;
			width: 100%;
			height: 100%;
			background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
			transition: left 0.5s;
		}

		&:hover {
			transform: translateY(-1px);
			background: linear-gradient(135deg, rgba(248, 249, 250, 0.15) 0%, rgba(248, 249, 250, 0.25) 100%);
			border-color: rgba(108, 117, 125, 0.5);
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
			color: #495057;

			&::before {
				left: 100%;
			}

			.layout-navbars-breadcrumb-user-link-photo {
				transform: scale(1.05);
			}

			.el-icon {
				transform: rotate(180deg);
			}
		}

		&-photo {
			width: 24px;
			height: 24px;
			border-radius: 50%;
			margin-right: 8px;
			border: 2px solid rgba(108, 117, 125, 0.2);
			transition: all 0.3s ease;
		}

		.el-icon {
			margin-left: 8px;
			font-size: 12px;
			transition: all 0.3s ease;
			color: #6c757d;
		}
	}

	&-icon {
		padding: 0;
		cursor: pointer;
		color: var(--next-bg-topBarColor);
		height: 32px;
		width: 32px;
		line-height: 32px;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1px solid rgba(108, 117, 125, 0.3);
		border-radius: 16px;
		margin: 6px 0;
		background: linear-gradient(135deg, rgba(248, 249, 250, 0.1) 0%, rgba(248, 249, 250, 0.2) 100%);
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		position: relative;
		overflow: hidden;

		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: -100%;
			width: 100%;
			height: 100%;
			background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
			transition: left 0.5s;
		}

		.el-icon {
			font-size: 14px;
			color: #6c757d;
			transition: all 0.3s ease;
		}

		&:hover {
			transform: translateY(-1px);
			background: linear-gradient(135deg, rgba(248, 249, 250, 0.15) 0%, rgba(248, 249, 250, 0.25) 100%);
			border-color: rgba(108, 117, 125, 0.5);
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);

			&::before {
				left: 100%;
			}

			.el-icon {
				color: #495057;
				transform: scale(1.05);
			}
		}

		&:active {
			transform: translateY(0);
			box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
		}

		&.screen-stats {
			width: auto;
			height: 32px;
			padding: 0 16px;
			border: 1px solid rgba(42, 119, 41, 0.3);
			border-radius: 16px;
			background: linear-gradient(135deg, rgba(42, 119, 41, 0.1) 0%, rgba(42, 119, 41, 0.2) 100%);
			box-shadow: 0 2px 8px rgba(42, 119, 41, 0.15);
			transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
			position: relative;
			overflow: hidden;

			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: -100%;
				width: 100%;
				height: 100%;
				background: linear-gradient(90deg, transparent, rgba(42, 119, 41, 0.1), transparent);
				transition: left 0.5s;
			}

			.el-icon {
				margin-right: 6px;
				color: #2a7729;
				font-size: 14px;
				transition: all 0.3s ease;
			}

			.screen-text {
				font-size: 13px;
				font-weight: 500;
				color: #2a7729;
				letter-spacing: 0.3px;
			}

			&:hover {
				transform: translateY(-1px);
				box-shadow: 0 4px 12px rgba(42, 119, 41, 0.25);
				background: linear-gradient(135deg, rgba(42, 119, 41, 0.15) 0%, rgba(42, 119, 41, 0.25) 100%);
				border-color: rgba(42, 119, 41, 0.5);

				&::before {
					left: 100%;
				}

				.el-icon {
					color: #1e5a1e;
					transform: scale(1.05);
				}

				.screen-text {
					color: #1e5a1e;
				}
			}

			&:active {
				transform: translateY(0);
				box-shadow: 0 2px 6px rgba(42, 119, 41, 0.2);
			}
		}
	}

	:deep(.el-dropdown) {
		color: var(--next-bg-topBarColor);
	}

	:deep(.el-dropdown-menu) {
		border: 1px solid rgba(108, 117, 125, 0.2);
		border-radius: 12px;
		box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
		padding: 8px;
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(10px);
		min-width: 100%;
		width: max-content;

		.el-dropdown-menu__item {
			border-radius: 8px;
			margin: 2px 0;
			padding: 8px 12px;
			font-size: 13px;
			color: #6c757d;
			transition: all 0.3s ease;

			&:hover {
				background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(248, 249, 250, 0.9) 100%);
				color: #495057;
				transform: translateX(2px);
			}

			&.is-divided {
				border-top: 1px solid rgba(108, 117, 125, 0.1);
				margin-top: 8px;
				padding-top: 12px;
			}
		}
	}

	:deep(.el-badge) {
		height: 40px;
		line-height: 40px;
		display: flex;
		align-items: center;
	}

	:deep(.el-badge__content.is-fixed) {
		top: 12px;
	}
}
</style>
