{"version": 3, "sources": ["../../ol/format/JSONFeature.js", "../../ol/format/GeoJSON.js"], "sourcesContent": ["/**\n * @module ol/format/JSONFeature\n */\nimport FeatureFormat from './Feature.js';\nimport {abstract} from '../util.js';\n\n/**\n * @classdesc\n * Abstract base class; normally only used for creating subclasses and not\n * instantiated in apps.\n * Base class for JSON feature formats.\n *\n * @abstract\n */\nclass JSONFeature extends FeatureFormat {\n  constructor() {\n    super();\n  }\n\n  /**\n   * @return {import(\"./Feature.js\").Type} Format.\n   */\n  getType() {\n    return 'json';\n  }\n\n  /**\n   * Read a feature.  Only works for a single feature. Use `readFeatures` to\n   * read a feature collection.\n   *\n   * @param {ArrayBuffer|Document|Element|Object|string} source Source.\n   * @param {import(\"./Feature.js\").ReadOptions} [options] Read options.\n   * @return {import(\"../Feature.js\").default} Feature.\n   * @api\n   */\n  readFeature(source, options) {\n    return this.readFeatureFromObject(\n      getObject(source),\n      this.getReadOptions(source, options)\n    );\n  }\n\n  /**\n   * Read all features.  Works with both a single feature and a feature\n   * collection.\n   *\n   * @param {ArrayBuffer|Document|Element|Object|string} source Source.\n   * @param {import(\"./Feature.js\").ReadOptions} [options] Read options.\n   * @return {Array<import(\"../Feature.js\").default>} Features.\n   * @api\n   */\n  readFeatures(source, options) {\n    return this.readFeaturesFromObject(\n      getObject(source),\n      this.getReadOptions(source, options)\n    );\n  }\n\n  /**\n   * @abstract\n   * @param {Object} object Object.\n   * @param {import(\"./Feature.js\").ReadOptions} [options] Read options.\n   * @protected\n   * @return {import(\"../Feature.js\").default} Feature.\n   */\n  readFeatureFromObject(object, options) {\n    return abstract();\n  }\n\n  /**\n   * @abstract\n   * @param {Object} object Object.\n   * @param {import(\"./Feature.js\").ReadOptions} [options] Read options.\n   * @protected\n   * @return {Array<import(\"../Feature.js\").default>} Features.\n   */\n  readFeaturesFromObject(object, options) {\n    return abstract();\n  }\n\n  /**\n   * Read a geometry.\n   *\n   * @param {ArrayBuffer|Document|Element|Object|string} source Source.\n   * @param {import(\"./Feature.js\").ReadOptions} [options] Read options.\n   * @return {import(\"../geom/Geometry.js\").default} Geometry.\n   * @api\n   */\n  readGeometry(source, options) {\n    return this.readGeometryFromObject(\n      getObject(source),\n      this.getReadOptions(source, options)\n    );\n  }\n\n  /**\n   * @abstract\n   * @param {Object} object Object.\n   * @param {import(\"./Feature.js\").ReadOptions} [options] Read options.\n   * @protected\n   * @return {import(\"../geom/Geometry.js\").default} Geometry.\n   */\n  readGeometryFromObject(object, options) {\n    return abstract();\n  }\n\n  /**\n   * Read the projection.\n   *\n   * @param {ArrayBuffer|Document|Element|Object|string} source Source.\n   * @return {import(\"../proj/Projection.js\").default} Projection.\n   * @api\n   */\n  readProjection(source) {\n    return this.readProjectionFromObject(getObject(source));\n  }\n\n  /**\n   * @abstract\n   * @param {Object} object Object.\n   * @protected\n   * @return {import(\"../proj/Projection.js\").default} Projection.\n   */\n  readProjectionFromObject(object) {\n    return abstract();\n  }\n\n  /**\n   * Encode a feature as string.\n   *\n   * @param {import(\"../Feature.js\").default} feature Feature.\n   * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n   * @return {string} Encoded feature.\n   * @api\n   */\n  writeFeature(feature, options) {\n    return JSON.stringify(this.writeFeatureObject(feature, options));\n  }\n\n  /**\n   * @abstract\n   * @param {import(\"../Feature.js\").default} feature Feature.\n   * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n   * @return {Object} Object.\n   */\n  writeFeatureObject(feature, options) {\n    return abstract();\n  }\n\n  /**\n   * Encode an array of features as string.\n   *\n   * @param {Array<import(\"../Feature.js\").default>} features Features.\n   * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n   * @return {string} Encoded features.\n   * @api\n   */\n  writeFeatures(features, options) {\n    return JSON.stringify(this.writeFeaturesObject(features, options));\n  }\n\n  /**\n   * @abstract\n   * @param {Array<import(\"../Feature.js\").default>} features Features.\n   * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n   * @return {Object} Object.\n   */\n  writeFeaturesObject(features, options) {\n    return abstract();\n  }\n\n  /**\n   * Encode a geometry as string.\n   *\n   * @param {import(\"../geom/Geometry.js\").default} geometry Geometry.\n   * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n   * @return {string} Encoded geometry.\n   * @api\n   */\n  writeGeometry(geometry, options) {\n    return JSON.stringify(this.writeGeometryObject(geometry, options));\n  }\n\n  /**\n   * @abstract\n   * @param {import(\"../geom/Geometry.js\").default} geometry Geometry.\n   * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n   * @return {Object} Object.\n   */\n  writeGeometryObject(geometry, options) {\n    return abstract();\n  }\n}\n\n/**\n * @param {Document|Element|Object|string} source Source.\n * @return {Object} Object.\n */\nfunction getObject(source) {\n  if (typeof source === 'string') {\n    const object = JSON.parse(source);\n    return object ? /** @type {Object} */ (object) : null;\n  }\n  if (source !== null) {\n    return source;\n  }\n  return null;\n}\n\nexport default JSONFeature;\n", "/**\n * @module ol/format/GeoJSON\n */\n\nimport Feature from '../Feature.js';\nimport GeometryCollection from '../geom/GeometryCollection.js';\nimport JSONFeature from './JSONFeature.js';\nimport LineString from '../geom/LineString.js';\nimport MultiLineString from '../geom/MultiLineString.js';\nimport MultiPoint from '../geom/MultiPoint.js';\nimport MultiPolygon from '../geom/MultiPolygon.js';\nimport Point from '../geom/Point.js';\nimport Polygon from '../geom/Polygon.js';\nimport {assert} from '../asserts.js';\nimport {get as getProjection} from '../proj.js';\nimport {isEmpty} from '../obj.js';\nimport {transformGeometryWithOptions} from './Feature.js';\n\n/**\n * @typedef {import(\"geojson\").GeoJSON} GeoJSONObject\n * @typedef {import(\"geojson\").Feature} GeoJSONFeature\n * @typedef {import(\"geojson\").FeatureCollection} GeoJSONFeatureCollection\n * @typedef {import(\"geojson\").Geometry} GeoJSONGeometry\n * @typedef {import(\"geojson\").Point} GeoJSONPoint\n * @typedef {import(\"geojson\").LineString} GeoJSONLineString\n * @typedef {import(\"geojson\").Polygon} GeoJSONPolygon\n * @typedef {import(\"geojson\").MultiPoint} GeoJSONMultiPoint\n * @typedef {import(\"geojson\").MultiLineString} GeoJSONMultiLineString\n * @typedef {import(\"geojson\").MultiPolygon} GeoJSONMultiPolygon\n * @typedef {import(\"geojson\").GeometryCollection} GeoJSONGeometryCollection\n */\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../proj.js\").ProjectionLike} [dataProjection='EPSG:4326'] Default data projection.\n * @property {import(\"../proj.js\").ProjectionLike} [featureProjection] Projection for features read or\n * written by the format.  Options passed to read or write methods will take precedence.\n * @property {string} [geometryName] Geometry name to use when creating features.\n * @property {boolean} [extractGeometryName=false] Certain GeoJSON providers include\n * the geometry_name field in the feature GeoJSON. If set to `true` the GeoJSON reader\n * will look for that field to set the geometry name. If both this field is set to `true`\n * and a `geometryName` is provided, the `geometryName` will take precedence.\n */\n\n/**\n * @classdesc\n * Feature format for reading and writing data in the GeoJSON format.\n *\n * @api\n */\nclass GeoJSON extends JSONFeature {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    super();\n\n    /**\n     * @type {import(\"../proj/Projection.js\").default}\n     */\n    this.dataProjection = getProjection(\n      options.dataProjection ? options.dataProjection : 'EPSG:4326'\n    );\n\n    if (options.featureProjection) {\n      /**\n       * @type {import(\"../proj/Projection.js\").default}\n       */\n      this.defaultFeatureProjection = getProjection(options.featureProjection);\n    }\n\n    /**\n     * Name of the geometry attribute for features.\n     * @type {string|undefined}\n     * @private\n     */\n    this.geometryName_ = options.geometryName;\n\n    /**\n     * Look for the geometry name in the feature GeoJSON\n     * @type {boolean|undefined}\n     * @private\n     */\n    this.extractGeometryName_ = options.extractGeometryName;\n\n    this.supportedMediaTypes = [\n      'application/geo+json',\n      'application/vnd.geo+json',\n    ];\n  }\n\n  /**\n   * @param {Object} object Object.\n   * @param {import(\"./Feature.js\").ReadOptions} [options] Read options.\n   * @protected\n   * @return {import(\"../Feature.js\").default} Feature.\n   */\n  readFeatureFromObject(object, options) {\n    /**\n     * @type {GeoJSONFeature}\n     */\n    let geoJSONFeature = null;\n    if (object['type'] === 'Feature') {\n      geoJSONFeature = /** @type {GeoJSONFeature} */ (object);\n    } else {\n      geoJSONFeature = {\n        'type': 'Feature',\n        'geometry': /** @type {GeoJSONGeometry} */ (object),\n        'properties': null,\n      };\n    }\n\n    const geometry = readGeometry(geoJSONFeature['geometry'], options);\n    const feature = new Feature();\n    if (this.geometryName_) {\n      feature.setGeometryName(this.geometryName_);\n    } else if (\n      this.extractGeometryName_ &&\n      'geometry_name' in geoJSONFeature !== undefined\n    ) {\n      feature.setGeometryName(geoJSONFeature['geometry_name']);\n    }\n    feature.setGeometry(geometry);\n\n    if ('id' in geoJSONFeature) {\n      feature.setId(geoJSONFeature['id']);\n    }\n\n    if (geoJSONFeature['properties']) {\n      feature.setProperties(geoJSONFeature['properties'], true);\n    }\n    return feature;\n  }\n\n  /**\n   * @param {Object} object Object.\n   * @param {import(\"./Feature.js\").ReadOptions} [options] Read options.\n   * @protected\n   * @return {Array<Feature>} Features.\n   */\n  readFeaturesFromObject(object, options) {\n    const geoJSONObject = /** @type {GeoJSONObject} */ (object);\n    /** @type {Array<import(\"../Feature.js\").default>} */\n    let features = null;\n    if (geoJSONObject['type'] === 'FeatureCollection') {\n      const geoJSONFeatureCollection = /** @type {GeoJSONFeatureCollection} */ (\n        object\n      );\n      features = [];\n      const geoJSONFeatures = geoJSONFeatureCollection['features'];\n      for (let i = 0, ii = geoJSONFeatures.length; i < ii; ++i) {\n        features.push(this.readFeatureFromObject(geoJSONFeatures[i], options));\n      }\n    } else {\n      features = [this.readFeatureFromObject(object, options)];\n    }\n    return features;\n  }\n\n  /**\n   * @param {GeoJSONGeometry} object Object.\n   * @param {import(\"./Feature.js\").ReadOptions} [options] Read options.\n   * @protected\n   * @return {import(\"../geom/Geometry.js\").default} Geometry.\n   */\n  readGeometryFromObject(object, options) {\n    return readGeometry(object, options);\n  }\n\n  /**\n   * @param {Object} object Object.\n   * @protected\n   * @return {import(\"../proj/Projection.js\").default} Projection.\n   */\n  readProjectionFromObject(object) {\n    const crs = object['crs'];\n    let projection;\n    if (crs) {\n      if (crs['type'] == 'name') {\n        projection = getProjection(crs['properties']['name']);\n      } else if (crs['type'] === 'EPSG') {\n        projection = getProjection('EPSG:' + crs['properties']['code']);\n      } else {\n        assert(false, 36); // Unknown SRS type\n      }\n    } else {\n      projection = this.dataProjection;\n    }\n    return /** @type {import(\"../proj/Projection.js\").default} */ (projection);\n  }\n\n  /**\n   * Encode a feature as a GeoJSON Feature object.\n   *\n   * @param {import(\"../Feature.js\").default} feature Feature.\n   * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n   * @return {GeoJSONFeature} Object.\n   * @api\n   */\n  writeFeatureObject(feature, options) {\n    options = this.adaptOptions(options);\n\n    /** @type {GeoJSONFeature} */\n    const object = {\n      'type': 'Feature',\n      geometry: null,\n      properties: null,\n    };\n\n    const id = feature.getId();\n    if (id !== undefined) {\n      object.id = id;\n    }\n\n    if (!feature.hasProperties()) {\n      return object;\n    }\n\n    const properties = feature.getProperties();\n    const geometry = feature.getGeometry();\n    if (geometry) {\n      object.geometry = writeGeometry(geometry, options);\n\n      delete properties[feature.getGeometryName()];\n    }\n\n    if (!isEmpty(properties)) {\n      object.properties = properties;\n    }\n\n    return object;\n  }\n\n  /**\n   * Encode an array of features as a GeoJSON object.\n   *\n   * @param {Array<import(\"../Feature.js\").default>} features Features.\n   * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n   * @return {GeoJSONFeatureCollection} GeoJSON Object.\n   * @api\n   */\n  writeFeaturesObject(features, options) {\n    options = this.adaptOptions(options);\n    const objects = [];\n    for (let i = 0, ii = features.length; i < ii; ++i) {\n      objects.push(this.writeFeatureObject(features[i], options));\n    }\n    return {\n      type: 'FeatureCollection',\n      features: objects,\n    };\n  }\n\n  /**\n   * Encode a geometry as a GeoJSON object.\n   *\n   * @param {import(\"../geom/Geometry.js\").default} geometry Geometry.\n   * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n   * @return {GeoJSONGeometry|GeoJSONGeometryCollection} Object.\n   * @api\n   */\n  writeGeometryObject(geometry, options) {\n    return writeGeometry(geometry, this.adaptOptions(options));\n  }\n}\n\n/**\n * @param {GeoJSONGeometry|GeoJSONGeometryCollection} object Object.\n * @param {import(\"./Feature.js\").ReadOptions} [options] Read options.\n * @return {import(\"../geom/Geometry.js\").default} Geometry.\n */\nfunction readGeometry(object, options) {\n  if (!object) {\n    return null;\n  }\n\n  /**\n   * @type {import(\"../geom/Geometry.js\").default}\n   */\n  let geometry;\n  switch (object['type']) {\n    case 'Point': {\n      geometry = readPointGeometry(/** @type {GeoJSONPoint} */ (object));\n      break;\n    }\n    case 'LineString': {\n      geometry = readLineStringGeometry(\n        /** @type {GeoJSONLineString} */ (object)\n      );\n      break;\n    }\n    case 'Polygon': {\n      geometry = readPolygonGeometry(/** @type {GeoJSONPolygon} */ (object));\n      break;\n    }\n    case 'MultiPoint': {\n      geometry = readMultiPointGeometry(\n        /** @type {GeoJSONMultiPoint} */ (object)\n      );\n      break;\n    }\n    case 'MultiLineString': {\n      geometry = readMultiLineStringGeometry(\n        /** @type {GeoJSONMultiLineString} */ (object)\n      );\n      break;\n    }\n    case 'MultiPolygon': {\n      geometry = readMultiPolygonGeometry(\n        /** @type {GeoJSONMultiPolygon} */ (object)\n      );\n      break;\n    }\n    case 'GeometryCollection': {\n      geometry = readGeometryCollectionGeometry(\n        /** @type {GeoJSONGeometryCollection} */ (object)\n      );\n      break;\n    }\n    default: {\n      throw new Error('Unsupported GeoJSON type: ' + object['type']);\n    }\n  }\n  return transformGeometryWithOptions(geometry, false, options);\n}\n\n/**\n * @param {GeoJSONGeometryCollection} object Object.\n * @param {import(\"./Feature.js\").ReadOptions} [options] Read options.\n * @return {GeometryCollection} Geometry collection.\n */\nfunction readGeometryCollectionGeometry(object, options) {\n  const geometries = object['geometries'].map(\n    /**\n     * @param {GeoJSONGeometry} geometry Geometry.\n     * @return {import(\"../geom/Geometry.js\").default} geometry Geometry.\n     */\n    function (geometry) {\n      return readGeometry(geometry, options);\n    }\n  );\n  return new GeometryCollection(geometries);\n}\n\n/**\n * @param {GeoJSONPoint} object Object.\n * @return {Point} Point.\n */\nfunction readPointGeometry(object) {\n  return new Point(object['coordinates']);\n}\n\n/**\n * @param {GeoJSONLineString} object Object.\n * @return {LineString} LineString.\n */\nfunction readLineStringGeometry(object) {\n  return new LineString(object['coordinates']);\n}\n\n/**\n * @param {GeoJSONMultiLineString} object Object.\n * @return {MultiLineString} MultiLineString.\n */\nfunction readMultiLineStringGeometry(object) {\n  return new MultiLineString(object['coordinates']);\n}\n\n/**\n * @param {GeoJSONMultiPoint} object Object.\n * @return {MultiPoint} MultiPoint.\n */\nfunction readMultiPointGeometry(object) {\n  return new MultiPoint(object['coordinates']);\n}\n\n/**\n * @param {GeoJSONMultiPolygon} object Object.\n * @return {MultiPolygon} MultiPolygon.\n */\nfunction readMultiPolygonGeometry(object) {\n  return new MultiPolygon(object['coordinates']);\n}\n\n/**\n * @param {GeoJSONPolygon} object Object.\n * @return {Polygon} Polygon.\n */\nfunction readPolygonGeometry(object) {\n  return new Polygon(object['coordinates']);\n}\n\n/**\n * @param {import(\"../geom/Geometry.js\").default} geometry Geometry.\n * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n * @return {GeoJSONGeometry} GeoJSON geometry.\n */\nfunction writeGeometry(geometry, options) {\n  geometry = transformGeometryWithOptions(geometry, true, options);\n  const type = geometry.getType();\n\n  /** @type {GeoJSONGeometry} */\n  let geoJSON;\n  switch (type) {\n    case 'Point': {\n      geoJSON = writePointGeometry(/** @type {Point} */ (geometry), options);\n      break;\n    }\n    case 'LineString': {\n      geoJSON = writeLineStringGeometry(\n        /** @type {LineString} */ (geometry),\n        options\n      );\n      break;\n    }\n    case 'Polygon': {\n      geoJSON = writePolygonGeometry(\n        /** @type {Polygon} */ (geometry),\n        options\n      );\n      break;\n    }\n    case 'MultiPoint': {\n      geoJSON = writeMultiPointGeometry(\n        /** @type {MultiPoint} */ (geometry),\n        options\n      );\n      break;\n    }\n    case 'MultiLineString': {\n      geoJSON = writeMultiLineStringGeometry(\n        /** @type {MultiLineString} */ (geometry),\n        options\n      );\n      break;\n    }\n    case 'MultiPolygon': {\n      geoJSON = writeMultiPolygonGeometry(\n        /** @type {MultiPolygon} */ (geometry),\n        options\n      );\n      break;\n    }\n    case 'GeometryCollection': {\n      geoJSON = writeGeometryCollectionGeometry(\n        /** @type {GeometryCollection} */ (geometry),\n        options\n      );\n      break;\n    }\n    case 'Circle': {\n      geoJSON = {\n        type: 'GeometryCollection',\n        geometries: [],\n      };\n      break;\n    }\n    default: {\n      throw new Error('Unsupported geometry type: ' + type);\n    }\n  }\n  return geoJSON;\n}\n\n/**\n * @param {GeometryCollection} geometry Geometry.\n * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n * @return {GeoJSONGeometryCollection} GeoJSON geometry collection.\n */\nfunction writeGeometryCollectionGeometry(geometry, options) {\n  options = Object.assign({}, options);\n  delete options.featureProjection;\n  const geometries = geometry.getGeometriesArray().map(function (geometry) {\n    return writeGeometry(geometry, options);\n  });\n  return {\n    type: 'GeometryCollection',\n    geometries: geometries,\n  };\n}\n\n/**\n * @param {LineString} geometry Geometry.\n * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n * @return {GeoJSONGeometry} GeoJSON geometry.\n */\nfunction writeLineStringGeometry(geometry, options) {\n  return {\n    type: 'LineString',\n    coordinates: geometry.getCoordinates(),\n  };\n}\n\n/**\n * @param {MultiLineString} geometry Geometry.\n * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n * @return {GeoJSONGeometry} GeoJSON geometry.\n */\nfunction writeMultiLineStringGeometry(geometry, options) {\n  return {\n    type: 'MultiLineString',\n    coordinates: geometry.getCoordinates(),\n  };\n}\n\n/**\n * @param {MultiPoint} geometry Geometry.\n * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n * @return {GeoJSONGeometry} GeoJSON geometry.\n */\nfunction writeMultiPointGeometry(geometry, options) {\n  return {\n    type: 'MultiPoint',\n    coordinates: geometry.getCoordinates(),\n  };\n}\n\n/**\n * @param {MultiPolygon} geometry Geometry.\n * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n * @return {GeoJSONGeometry} GeoJSON geometry.\n */\nfunction writeMultiPolygonGeometry(geometry, options) {\n  let right;\n  if (options) {\n    right = options.rightHanded;\n  }\n  return {\n    type: 'MultiPolygon',\n    coordinates: geometry.getCoordinates(right),\n  };\n}\n\n/**\n * @param {Point} geometry Geometry.\n * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n * @return {GeoJSONGeometry} GeoJSON geometry.\n */\nfunction writePointGeometry(geometry, options) {\n  return {\n    type: 'Point',\n    coordinates: geometry.getCoordinates(),\n  };\n}\n\n/**\n * @param {Polygon} geometry Geometry.\n * @param {import(\"./Feature.js\").WriteOptions} [options] Write options.\n * @return {GeoJSONGeometry} GeoJSON geometry.\n */\nfunction writePolygonGeometry(geometry, options) {\n  let right;\n  if (options) {\n    right = options.rightHanded;\n  }\n  return {\n    type: 'Polygon',\n    coordinates: geometry.getCoordinates(right),\n  };\n}\n\nexport default GeoJSON;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,IAAM,cAAN,cAA0BA,iBAAc;AAAA,EACtC,cAAc;AACZ,UAAM;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,YAAY,QAAQ,SAAS;AAC3B,WAAO,KAAK;AAAA,MACV,UAAU,MAAM;AAAA,MAChB,KAAK,eAAe,QAAQ,OAAO;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,aAAa,QAAQ,SAAS;AAC5B,WAAO,KAAK;AAAA,MACV,UAAU,MAAM;AAAA,MAChB,KAAK,eAAe,QAAQ,OAAO;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,sBAAsB,QAAQ,SAAS;AACrC,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,uBAAuB,QAAQ,SAAS;AACtC,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAAa,QAAQ,SAAS;AAC5B,WAAO,KAAK;AAAA,MACV,UAAU,MAAM;AAAA,MAChB,KAAK,eAAe,QAAQ,OAAO;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,uBAAuB,QAAQ,SAAS;AACtC,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,QAAQ;AACrB,WAAO,KAAK,yBAAyB,UAAU,MAAM,CAAC;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,yBAAyB,QAAQ;AAC/B,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAAa,SAAS,SAAS;AAC7B,WAAO,KAAK,UAAU,KAAK,mBAAmB,SAAS,OAAO,CAAC;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB,SAAS,SAAS;AACnC,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,cAAc,UAAU,SAAS;AAC/B,WAAO,KAAK,UAAU,KAAK,oBAAoB,UAAU,OAAO,CAAC;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB,UAAU,SAAS;AACrC,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,cAAc,UAAU,SAAS;AAC/B,WAAO,KAAK,UAAU,KAAK,oBAAoB,UAAU,OAAO,CAAC;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB,UAAU,SAAS;AACrC,WAAO,SAAS;AAAA,EAClB;AACF;AAMA,SAAS,UAAU,QAAQ;AACzB,MAAI,OAAO,WAAW,UAAU;AAC9B,UAAM,SAAS,KAAK,MAAM,MAAM;AAChC,WAAO;AAAA;AAAA,MAAgC;AAAA,QAAU;AAAA,EACnD;AACA,MAAI,WAAW,MAAM;AACnB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,IAAO,sBAAQ;;;AC/Jf,IAAM,UAAN,cAAsB,oBAAY;AAAA;AAAA;AAAA;AAAA,EAIhC,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM;AAKN,SAAK,iBAAiB;AAAA,MACpB,QAAQ,iBAAiB,QAAQ,iBAAiB;AAAA,IACpD;AAEA,QAAI,QAAQ,mBAAmB;AAI7B,WAAK,2BAA2B,IAAc,QAAQ,iBAAiB;AAAA,IACzE;AAOA,SAAK,gBAAgB,QAAQ;AAO7B,SAAK,uBAAuB,QAAQ;AAEpC,SAAK,sBAAsB;AAAA,MACzB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,QAAQ,SAAS;AAIrC,QAAI,iBAAiB;AACrB,QAAI,OAAO,MAAM,MAAM,WAAW;AAChC;AAAA,MAAgD;AAAA,IAClD,OAAO;AACL,uBAAiB;AAAA,QACf,QAAQ;AAAA,QACR;AAAA;AAAA,UAA4C;AAAA;AAAA,QAC5C,cAAc;AAAA,MAChB;AAAA,IACF;AAEA,UAAM,WAAW,aAAa,eAAe,UAAU,GAAG,OAAO;AACjE,UAAM,UAAU,IAAI,gBAAQ;AAC5B,QAAI,KAAK,eAAe;AACtB,cAAQ,gBAAgB,KAAK,aAAa;AAAA,IAC5C,WACE,KAAK,wBACL,mBAAmB,mBAAmB,QACtC;AACA,cAAQ,gBAAgB,eAAe,eAAe,CAAC;AAAA,IACzD;AACA,YAAQ,YAAY,QAAQ;AAE5B,QAAI,QAAQ,gBAAgB;AAC1B,cAAQ,MAAM,eAAe,IAAI,CAAC;AAAA,IACpC;AAEA,QAAI,eAAe,YAAY,GAAG;AAChC,cAAQ,cAAc,eAAe,YAAY,GAAG,IAAI;AAAA,IAC1D;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,uBAAuB,QAAQ,SAAS;AACtC,UAAM;AAAA;AAAA,MAA8C;AAAA;AAEpD,QAAI,WAAW;AACf,QAAI,cAAc,MAAM,MAAM,qBAAqB;AACjD,YAAM;AAAA;AAAA,QACJ;AAAA;AAEF,iBAAW,CAAC;AACZ,YAAM,kBAAkB,yBAAyB,UAAU;AAC3D,eAAS,IAAI,GAAG,KAAK,gBAAgB,QAAQ,IAAI,IAAI,EAAE,GAAG;AACxD,iBAAS,KAAK,KAAK,sBAAsB,gBAAgB,CAAC,GAAG,OAAO,CAAC;AAAA,MACvE;AAAA,IACF,OAAO;AACL,iBAAW,CAAC,KAAK,sBAAsB,QAAQ,OAAO,CAAC;AAAA,IACzD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,uBAAuB,QAAQ,SAAS;AACtC,WAAO,aAAa,QAAQ,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,yBAAyB,QAAQ;AAC/B,UAAM,MAAM,OAAO,KAAK;AACxB,QAAI;AACJ,QAAI,KAAK;AACP,UAAI,IAAI,MAAM,KAAK,QAAQ;AACzB,qBAAa,IAAc,IAAI,YAAY,EAAE,MAAM,CAAC;AAAA,MACtD,WAAW,IAAI,MAAM,MAAM,QAAQ;AACjC,qBAAa,IAAc,UAAU,IAAI,YAAY,EAAE,MAAM,CAAC;AAAA,MAChE,OAAO;AACL,eAAO,OAAO,EAAE;AAAA,MAClB;AAAA,IACF,OAAO;AACL,mBAAa,KAAK;AAAA,IACpB;AACA;AAAA;AAAA,MAA+D;AAAA;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,mBAAmB,SAAS,SAAS;AACnC,cAAU,KAAK,aAAa,OAAO;AAGnC,UAAM,SAAS;AAAA,MACb,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AAEA,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,OAAO,QAAW;AACpB,aAAO,KAAK;AAAA,IACd;AAEA,QAAI,CAAC,QAAQ,cAAc,GAAG;AAC5B,aAAO;AAAA,IACT;AAEA,UAAM,aAAa,QAAQ,cAAc;AACzC,UAAM,WAAW,QAAQ,YAAY;AACrC,QAAI,UAAU;AACZ,aAAO,WAAW,cAAc,UAAU,OAAO;AAEjD,aAAO,WAAW,QAAQ,gBAAgB,CAAC;AAAA,IAC7C;AAEA,QAAI,CAAC,QAAQ,UAAU,GAAG;AACxB,aAAO,aAAa;AAAA,IACtB;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,oBAAoB,UAAU,SAAS;AACrC,cAAU,KAAK,aAAa,OAAO;AACnC,UAAM,UAAU,CAAC;AACjB,aAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,EAAE,GAAG;AACjD,cAAQ,KAAK,KAAK,mBAAmB,SAAS,CAAC,GAAG,OAAO,CAAC;AAAA,IAC5D;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,oBAAoB,UAAU,SAAS;AACrC,WAAO,cAAc,UAAU,KAAK,aAAa,OAAO,CAAC;AAAA,EAC3D;AACF;AAOA,SAAS,aAAa,QAAQ,SAAS;AACrC,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAKA,MAAI;AACJ,UAAQ,OAAO,MAAM,GAAG;AAAA,IACtB,KAAK,SAAS;AACZ,iBAAW;AAAA;AAAA,QAA+C;AAAA,MAAO;AACjE;AAAA,IACF;AAAA,IACA,KAAK,cAAc;AACjB,iBAAW;AAAA;AAAA,QACyB;AAAA,MACpC;AACA;AAAA,IACF;AAAA,IACA,KAAK,WAAW;AACd,iBAAW;AAAA;AAAA,QAAmD;AAAA,MAAO;AACrE;AAAA,IACF;AAAA,IACA,KAAK,cAAc;AACjB,iBAAW;AAAA;AAAA,QACyB;AAAA,MACpC;AACA;AAAA,IACF;AAAA,IACA,KAAK,mBAAmB;AACtB,iBAAW;AAAA;AAAA,QAC8B;AAAA,MACzC;AACA;AAAA,IACF;AAAA,IACA,KAAK,gBAAgB;AACnB,iBAAW;AAAA;AAAA,QAC2B;AAAA,MACtC;AACA;AAAA,IACF;AAAA,IACA,KAAK,sBAAsB;AACzB,iBAAW;AAAA;AAAA,QACiC;AAAA,MAC5C;AACA;AAAA,IACF;AAAA,IACA,SAAS;AACP,YAAM,IAAI,MAAM,+BAA+B,OAAO,MAAM,CAAC;AAAA,IAC/D;AAAA,EACF;AACA,SAAO,6BAA6B,UAAU,OAAO,OAAO;AAC9D;AAOA,SAAS,+BAA+B,QAAQ,SAAS;AACvD,QAAM,aAAa,OAAO,YAAY,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,IAKtC,SAAU,UAAU;AAClB,aAAO,aAAa,UAAU,OAAO;AAAA,IACvC;AAAA,EACF;AACA,SAAO,IAAI,2BAAmB,UAAU;AAC1C;AAMA,SAAS,kBAAkB,QAAQ;AACjC,SAAO,IAAI,cAAM,OAAO,aAAa,CAAC;AACxC;AAMA,SAAS,uBAAuB,QAAQ;AACtC,SAAO,IAAI,mBAAW,OAAO,aAAa,CAAC;AAC7C;AAMA,SAAS,4BAA4B,QAAQ;AAC3C,SAAO,IAAI,wBAAgB,OAAO,aAAa,CAAC;AAClD;AAMA,SAAS,uBAAuB,QAAQ;AACtC,SAAO,IAAI,mBAAW,OAAO,aAAa,CAAC;AAC7C;AAMA,SAAS,yBAAyB,QAAQ;AACxC,SAAO,IAAI,qBAAa,OAAO,aAAa,CAAC;AAC/C;AAMA,SAAS,oBAAoB,QAAQ;AACnC,SAAO,IAAI,gBAAQ,OAAO,aAAa,CAAC;AAC1C;AAOA,SAAS,cAAc,UAAU,SAAS;AACxC,aAAW,6BAA6B,UAAU,MAAM,OAAO;AAC/D,QAAM,OAAO,SAAS,QAAQ;AAG9B,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK,SAAS;AACZ,gBAAU;AAAA;AAAA,QAAyC;AAAA,QAAW;AAAA,MAAO;AACrE;AAAA,IACF;AAAA,IACA,KAAK,cAAc;AACjB,gBAAU;AAAA;AAAA,QACmB;AAAA,QAC3B;AAAA,MACF;AACA;AAAA,IACF;AAAA,IACA,KAAK,WAAW;AACd,gBAAU;AAAA;AAAA,QACgB;AAAA,QACxB;AAAA,MACF;AACA;AAAA,IACF;AAAA,IACA,KAAK,cAAc;AACjB,gBAAU;AAAA;AAAA,QACmB;AAAA,QAC3B;AAAA,MACF;AACA;AAAA,IACF;AAAA,IACA,KAAK,mBAAmB;AACtB,gBAAU;AAAA;AAAA,QACwB;AAAA,QAChC;AAAA,MACF;AACA;AAAA,IACF;AAAA,IACA,KAAK,gBAAgB;AACnB,gBAAU;AAAA;AAAA,QACqB;AAAA,QAC7B;AAAA,MACF;AACA;AAAA,IACF;AAAA,IACA,KAAK,sBAAsB;AACzB,gBAAU;AAAA;AAAA,QAC2B;AAAA,QACnC;AAAA,MACF;AACA;AAAA,IACF;AAAA,IACA,KAAK,UAAU;AACb,gBAAU;AAAA,QACR,MAAM;AAAA,QACN,YAAY,CAAC;AAAA,MACf;AACA;AAAA,IACF;AAAA,IACA,SAAS;AACP,YAAM,IAAI,MAAM,gCAAgC,IAAI;AAAA,IACtD;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,gCAAgC,UAAU,SAAS;AAC1D,YAAU,OAAO,OAAO,CAAC,GAAG,OAAO;AACnC,SAAO,QAAQ;AACf,QAAM,aAAa,SAAS,mBAAmB,EAAE,IAAI,SAAUC,WAAU;AACvE,WAAO,cAAcA,WAAU,OAAO;AAAA,EACxC,CAAC;AACD,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,EACF;AACF;AAOA,SAAS,wBAAwB,UAAU,SAAS;AAClD,SAAO;AAAA,IACL,MAAM;AAAA,IACN,aAAa,SAAS,eAAe;AAAA,EACvC;AACF;AAOA,SAAS,6BAA6B,UAAU,SAAS;AACvD,SAAO;AAAA,IACL,MAAM;AAAA,IACN,aAAa,SAAS,eAAe;AAAA,EACvC;AACF;AAOA,SAAS,wBAAwB,UAAU,SAAS;AAClD,SAAO;AAAA,IACL,MAAM;AAAA,IACN,aAAa,SAAS,eAAe;AAAA,EACvC;AACF;AAOA,SAAS,0BAA0B,UAAU,SAAS;AACpD,MAAI;AACJ,MAAI,SAAS;AACX,YAAQ,QAAQ;AAAA,EAClB;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,aAAa,SAAS,eAAe,KAAK;AAAA,EAC5C;AACF;AAOA,SAAS,mBAAmB,UAAU,SAAS;AAC7C,SAAO;AAAA,IACL,MAAM;AAAA,IACN,aAAa,SAAS,eAAe;AAAA,EACvC;AACF;AAOA,SAAS,qBAAqB,UAAU,SAAS;AAC/C,MAAI;AACJ,MAAI,SAAS;AACX,YAAQ,QAAQ;AAAA,EAClB;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,aAAa,SAAS,eAAe,KAAK;AAAA,EAC5C;AACF;AAEA,IAAO,kBAAQ;", "names": ["Feature_default", "geometry"]}