<template>
	<div ref="chartContainer" :style="{ width: props.width, height: props.height }"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
import type { ECharts, EChartsOption } from 'echarts';

export interface ChartDataItem {
	name: string;
	value: number;
	parent?: string;
}

const props = withDefaults(
	defineProps<{
		innerData: ChartDataItem[];
		outerData: ChartDataItem[];
		width?: string;
		height?: string;
		showLegend?: boolean;
	}>(),
	{
		width: '100%',
		height: '500px',
	}
);

const chartContainer = ref<HTMLElement>();
let chart: ECharts | null = null;

// 圆角配置
const borderRadius = {
	inner: 6, // 内环扇形圆角
	outer: 6, // 外环扇形圆角
};

// 初始化图表
const initChart = () => {
	if (!chartContainer.value) return;
	chart = echarts.init(chartContainer.value);
	setOption();
};

// 图表配置
const setOption = () => {
	if (!chart) return;

	const option: EChartsOption = {
		tooltip: {
			trigger: 'item',
			formatter: '{a}<br/>{b}: {c} ({d}%)',
		},
		legend: props.showLegend
			? {
					type: 'scroll',
					orient: 'horizontal',
				}
			: { show: false },
		series: [
			{
				name: '业务场景',
				type: 'pie',
				radius: ['0%', '40%'],
				data: props.innerData,
				label: { show: false },
				itemStyle: {
					borderRadius: borderRadius.inner,
					borderWidth: 2,
					borderColor: '#fff',
					opacity: 1,
				},
				emphasis: {
					itemStyle: {
						shadowBlur: 20,
						shadowColor: 'rgba(0, 0, 0, 0.3)',
					},
				},
			},
			{
				name: '事件类型',
				type: 'pie',
				radius: ['40%', '75%'],
				data: props.outerData,
				itemStyle: {
					borderRadius: borderRadius.outer,
					borderWidth: 2,
					borderColor: '#fff',
					opacity: 1,
				},
				label: {
					show: true,
					formatter: '{b}\n{d}%',
					fontSize: 14,
				},
				emphasis: {
					itemStyle: {
						shadowBlur: 20,
						shadowColor: 'rgba(0, 0, 0, 0.3)',
					},
				},
			},
		],
	};

	chart.setOption(option);
};

// 智能灰度处理函数
const smartGrayout = (activeSeries: number, activeIndex: number) => {
	if (!chart) return;

	// 获取关联索引
	const getRelatedIndices = () => {
		const currentItem = activeSeries === 0 ? props.innerData[activeIndex] : props.outerData[activeIndex];

		return {
			inner: activeSeries === 0 ? [activeIndex] : props.innerData.findIndex((i) => i.name === currentItem.parent),
			outer:
				activeSeries === 1
					? [activeIndex]
					: props.outerData.map((item, idx) => (item.parent === currentItem.name ? idx : -1)).filter((i) => i !== -1),
		};
	};

	const { inner, outer } = getRelatedIndices();

	// 设置全局灰度
	chart.setOption({
		series: [
			{
				data: props.innerData.map((_, idx) => ({
					itemStyle: {
						opacity: inner.includes(idx) ? 1 : 0.3,
					},
				})),
			},
			{
				data: props.outerData.map((_, idx) => ({
					itemStyle: {
						opacity: outer.includes(idx) ? 1 : 0.3,
					},
				})),
			},
		],
	});
};

// 响应式更新
watch(
	() => [props.innerData, props.outerData],
	() => {
		chart?.setOption({
			series: [{ data: props.innerData }, { data: props.outerData }],
		});
	}
);

// 生命周期
onMounted(() => {
	initChart();
	window.addEventListener('resize', () => chart?.resize());
});

onBeforeUnmount(() => {
	window.removeEventListener('resize', () => chart?.resize());
	chart?.dispose();
});
</script>
