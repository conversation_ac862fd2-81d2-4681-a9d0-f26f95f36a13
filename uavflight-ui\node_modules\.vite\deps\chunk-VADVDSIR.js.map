{"version": 3, "sources": ["../../ol/layer/BaseTile.js", "../../ol/layer/Tile.js"], "sourcesContent": ["/**\n * @module ol/layer/BaseTile\n */\nimport Layer from './Layer.js';\nimport TileProperty from './TileProperty.js';\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"./Base\").BaseLayerObjectEventTypes|\n *     import(\"./Layer.js\").LayerEventType|'change:preload'|'change:useInterimTilesOnError', import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../render/EventType\").LayerRenderEventTypes, import(\"../render/Event\").default, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"./Base\").BaseLayerObjectEventTypes|\n *   import(\"./Layer.js\").LayerEventType|'change:preload'|'change:useInterimTilesOnError'|import(\"../render/EventType\").LayerRenderEventTypes, Return>} BaseTileLayerOnSignature\n */\n\n/**\n * @template {import(\"../source/Tile.js\").default} TileSourceType\n * @typedef {Object} Options\n * @property {string} [className='ol-layer'] A CSS class name to set to the layer element.\n * @property {number} [opacity=1] Opacity (0, 1).\n * @property {boolean} [visible=true] Visibility.\n * @property {import(\"../extent.js\").Extent} [extent] The bounding extent for layer rendering.  The layer will not be\n * rendered outside of this extent.\n * @property {number} [zIndex] The z-index for layer rendering.  At rendering time, the layers\n * will be ordered, first by Z-index and then by position. When `undefined`, a `zIndex` of 0 is assumed\n * for layers that are added to the map's `layers` collection, or `Infinity` when the layer's `setMap()`\n * method was used.\n * @property {number} [minResolution] The minimum resolution (inclusive) at which this layer will be\n * visible.\n * @property {number} [maxResolution] The maximum resolution (exclusive) below which this layer will\n * be visible.\n * @property {number} [minZoom] The minimum view zoom level (exclusive) above which this layer will be\n * visible.\n * @property {number} [maxZoom] The maximum view zoom level (inclusive) at which this layer will\n * be visible.\n * @property {number} [preload=0] Preload. Load low-resolution tiles up to `preload` levels. `0`\n * means no preloading.\n * @property {TileSourceType} [source] Source for this layer.\n * @property {import(\"../Map.js\").default} [map] Sets the layer as overlay on a map. The map will not manage\n * this layer in its layers collection, and the layer will be rendered on top. This is useful for\n * temporary layers. The standard way to add a layer to a map and have it managed by the map is to\n * use {@link import(\"../Map.js\").default#addLayer map.addLayer()}.\n * @property {boolean} [useInterimTilesOnError=true] Use interim tiles on error.\n * @property {Object<string, *>} [properties] Arbitrary observable properties. Can be accessed with `#get()` and `#set()`.\n */\n\n/**\n * @classdesc\n * For layer sources that provide pre-rendered, tiled images in grids that are\n * organized by zoom levels for specific resolutions.\n * Note that any property set in the options is set as a {@link module:ol/Object~BaseObject}\n * property on the layer object; for example, setting `title: 'My Title'` in the\n * options means that `title` is observable, and has get/set accessors.\n *\n * @template {import(\"../source/Tile.js\").default} TileSourceType\n * @template {import(\"../renderer/Layer.js\").default} RendererType\n * @extends {Layer<TileSourceType, RendererType>}\n * @api\n */\nclass BaseTileLayer extends Layer {\n  /**\n   * @param {Options<TileSourceType>} [options] Tile layer options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    const baseOptions = Object.assign({}, options);\n\n    delete baseOptions.preload;\n    delete baseOptions.useInterimTilesOnError;\n    super(baseOptions);\n\n    /***\n     * @type {BaseTileLayerOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {BaseTileLayerOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {BaseTileLayerOnSignature<void>}\n     */\n    this.un;\n\n    this.setPreload(options.preload !== undefined ? options.preload : 0);\n    this.setUseInterimTilesOnError(\n      options.useInterimTilesOnError !== undefined\n        ? options.useInterimTilesOnError\n        : true\n    );\n  }\n\n  /**\n   * Return the level as number to which we will preload tiles up to.\n   * @return {number} The level to preload tiles up to.\n   * @observable\n   * @api\n   */\n  getPreload() {\n    return /** @type {number} */ (this.get(TileProperty.PRELOAD));\n  }\n\n  /**\n   * Set the level as number to which we will preload tiles up to.\n   * @param {number} preload The level to preload tiles up to.\n   * @observable\n   * @api\n   */\n  setPreload(preload) {\n    this.set(TileProperty.PRELOAD, preload);\n  }\n\n  /**\n   * Whether we use interim tiles on error.\n   * @return {boolean} Use interim tiles on error.\n   * @observable\n   * @api\n   */\n  getUseInterimTilesOnError() {\n    return /** @type {boolean} */ (\n      this.get(TileProperty.USE_INTERIM_TILES_ON_ERROR)\n    );\n  }\n\n  /**\n   * Set whether we use interim tiles on error.\n   * @param {boolean} useInterimTilesOnError Use interim tiles on error.\n   * @observable\n   * @api\n   */\n  setUseInterimTilesOnError(useInterimTilesOnError) {\n    this.set(TileProperty.USE_INTERIM_TILES_ON_ERROR, useInterimTilesOnError);\n  }\n\n  /**\n   * Get data for a pixel location.  The return type depends on the source data.  For image tiles,\n   * a four element RGBA array will be returned.  For data tiles, the array length will match the\n   * number of bands in the dataset.  For requests outside the layer extent, `null` will be returned.\n   * Data for a image tiles can only be retrieved if the source's `crossOrigin` property is set.\n   *\n   * ```js\n   * // display layer data on every pointer move\n   * map.on('pointermove', (event) => {\n   *   console.log(layer.getData(event.pixel));\n   * });\n   * ```\n   * @param {import(\"../pixel\").Pixel} pixel Pixel.\n   * @return {Uint8ClampedArray|Uint8Array|Float32Array|DataView|null} Pixel data.\n   * @api\n   */\n  getData(pixel) {\n    return super.getData(pixel);\n  }\n}\n\nexport default BaseTileLayer;\n", "/**\n * @module ol/layer/Tile\n */\nimport BaseTileLayer from './BaseTile.js';\nimport CanvasTileLayerRenderer from '../renderer/canvas/TileLayer.js';\n\n/**\n * @classdesc\n * For layer sources that provide pre-rendered, tiled images in grids that are\n * organized by zoom levels for specific resolutions.\n * Note that any property set in the options is set as a {@link module:ol/Object~BaseObject}\n * property on the layer object; for example, setting `title: 'My Title'` in the\n * options means that `title` is observable, and has get/set accessors.\n *\n * @template {import(\"../source/Tile.js\").default} TileSourceType\n * @extends BaseTileLayer<TileSourceType, CanvasTileLayerRenderer>\n * @api\n */\nclass TileLayer extends BaseTileLayer {\n  /**\n   * @param {import(\"./BaseTile.js\").Options<TileSourceType>} [options] Tile layer options.\n   */\n  constructor(options) {\n    super(options);\n  }\n\n  createRenderer() {\n    return new CanvasTileLayerRenderer(this);\n  }\n}\n\nexport default TileLayer;\n"], "mappings": ";;;;;;;;;AA4DA,IAAM,gBAAN,cAA4B,cAAM;AAAA;AAAA;AAAA;AAAA,EAIhC,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM,cAAc,OAAO,OAAO,CAAC,GAAG,OAAO;AAE7C,WAAO,YAAY;AACnB,WAAO,YAAY;AACnB,UAAM,WAAW;AAKjB,SAAK;AAKL,SAAK;AAKL,SAAK;AAEL,SAAK,WAAW,QAAQ,YAAY,SAAY,QAAQ,UAAU,CAAC;AACnE,SAAK;AAAA,MACH,QAAQ,2BAA2B,SAC/B,QAAQ,yBACR;AAAA,IACN;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa;AACX;AAAA;AAAA,MAA8B,KAAK,IAAI,qBAAa,OAAO;AAAA;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAClB,SAAK,IAAI,qBAAa,SAAS,OAAO;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,4BAA4B;AAC1B;AAAA;AAAA,MACE,KAAK,IAAI,qBAAa,0BAA0B;AAAA;AAAA,EAEpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,0BAA0B,wBAAwB;AAChD,SAAK,IAAI,qBAAa,4BAA4B,sBAAsB;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,QAAQ,OAAO;AACb,WAAO,MAAM,QAAQ,KAAK;AAAA,EAC5B;AACF;AAEA,IAAO,mBAAQ;;;AC7If,IAAM,YAAN,cAAwB,iBAAc;AAAA;AAAA;AAAA;AAAA,EAIpC,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACf;AAAA,EAEA,iBAAiB;AACf,WAAO,IAAI,kBAAwB,IAAI;AAAA,EACzC;AACF;AAEA,IAAO,eAAQ;", "names": []}