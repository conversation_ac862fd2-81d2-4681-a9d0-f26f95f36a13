/**
 * @file types.ts
 * @description 地图图层管理相关的类型定义文件
 * 
 * 该文件定义了地图图层管理所需的各种接口和类型，包括：
 * - 图层参数接口（LayerParameters）
 * - 图层样式接口（LayerStyle）
 * - 样式规则接口（StyleRule）
 * - 地图图层配置接口（MapLayer）
 * - 地图配置接口（MapConfig）
 * 
 * 这些类型定义为整个地图图层管理系统提供了类型安全和代码提示，
 * 确保图层配置和样式的一致性和可靠性。
 * 
 * <AUTHOR>
 * @date 2025-05-19
 */
/*
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-05-16 08:34:21
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-05-16 10:45:13
 * @FilePath: \xmfx-ui\src\stores\mapLayer\types.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
/*
 * @Description: 地图图层类型定义
 */

/**
 * 地图图层参数
 */
export interface LayerParameters {
	service?: string;
	format?: string;
	transparent?: boolean;
	[key: string]: any;
}

/**
 * 图层样式
 */
export interface LayerStyle {
	color?: string;
	weight?: number;
	opacity?: number;
	fillColor?: string;
	fillOpacity?: number;
	image?: {
		src: string;
		scale?: number;
		anchor?: [number, number];
		rotation?: number;
	};
	label?: {
		text: string;
		font?: string;
		fill?: {
			color: string;
		};
		stroke?: {
			color: string;
			width: number;
		};
	};
}

/**
 * 样式规则
 */
export interface StyleRule {
	filter: {
		property: string;
		operator: '=' | '!=' | '>' | '<' | '>=' | '<=' | 'in';
		value: any;
	};
	style: LayerStyle | string;
}

/**
 * 地图图层配置
 */
export interface MapLayer {
	id: string;
	name: string;
	type: 'raster' | 'vector';
	protocol?: 'XYZ' | 'WMS' | 'WMTS' | 'GeoJSON' | 'MVT' | 'WFS';
	url: string;
	initialLoad: boolean;
	// 矢量图层的几何类型
	geometryType?: 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon';
	// WMS 特定属性
	layers?: string;
	parameters?: LayerParameters;
	// 样式
	defaultStyle?: LayerStyle | string;
	styleRules?: StyleRule[];
	// 状态属性
	active?: boolean;
	layerInstance?: any;
	// 事件标识
	event?: string;
	// 标注相关属性
	labelField?: string;
	labelStyle?: {
		color: string;
		fontSize: number;
		[key: string]: any;
	};
	showLabels?: boolean;
	zIndex?: number;
	opacity?: number;
	// 图层范围，用于栅格图层的缩放
	extent?: [number, number, number, number];
}

/**
 * 地图配置
 */
export interface MapConfig {
	layers: MapLayer[];
	styles?: Record<string, LayerStyle>;
} 