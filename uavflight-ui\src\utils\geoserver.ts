/*
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-06-28 09:43:17
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-07-25 11:41:57
 * @FilePath: \uavflight-ui\src\utils\geoserver.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-06-18 15:42:20
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-07-25 11:24:04
 * @FilePath: \uavflight-ui\src\utils\geoserver.ts
 * @Description: Geoserver服务工具函数
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
/**
 * Geoserver 配置工具
 */

/**
 * 获取 Geoserver 基础 URL
 * @returns Geoserver 的基础 URL
 */
export function getGeoserverBaseUrl(): string {
  // 从环境变量获取Geoserver地址和端口
  const host = import.meta.env.VITE_GEOSERVER_HOST || import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
  const port = import.meta.env.VITE_GEOSERVER_PORT || import.meta.env.VITE_MAP_SERVER_PORT_GEOSERVER || '8083';
  const path = import.meta.env.VITE_GEOSERVER_PATH || '/geoserver';
  
  // 不再判断是否为本地环境，总是返回完整URL
  return `http://${host}:${port}${path}`;
}

/**
 * 获取高清查询服务基础URL
 * @returns 高清查询服务的基础URL
 */
export function getHDQueryBaseUrl(): string {
  // 从环境变量获取高清查询服务地址和端口
  const host = import.meta.env.VITE_GEOSERVER_HOST || import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
  const port = import.meta.env.VITE_GEOSERVER_HD_PORT || '5000';
  
  // 不再判断是否为本地环境，总是返回完整URL
  return `http://${host}:${port}`;
}

/**
 * 构建图层位置查询URL
 * @param lat 纬度
 * @param lon 经度
 * @param workspace 工作区名称
 * @returns 构建的查询URL
 */
export function buildLayerQueryUrl(lat: number, lon: number, workspace: string): string {
  const baseUrl = getHDQueryBaseUrl();
  const apiPath = '/api/query_intersecting_layers';

  const searchParams = new URLSearchParams();
  searchParams.append('lat', String(lat));
  searchParams.append('lon', String(lon));
  searchParams.append('workspace', workspace);

  return `${baseUrl}${apiPath}?${searchParams.toString()}`;
}

/**
 * 构建Geoserver WFS URL
 * @param workspace 工作区名称
 * @param layerName 图层名称
 * @param params 附加参数
 * @returns 构建的WFS URL
 */
export function buildGeoserverWfsUrl(workspace: string, layerName: string, params: Record<string, any> = {}): string {
  const baseUrl = getGeoserverBaseUrl();
  const defaultParams = {
    service: 'WFS',
    version: '1.2.0',
    request: 'GetFeature',
    typeName: `${workspace}:${layerName}`,
    // 确保始终使用JSON格式而不是XML
    outputFormat: 'application/json',
    ...params,
  };
  
  // 再次确保outputFormat被设置为application/json，防止被params覆盖
  if (defaultParams.outputFormat !== 'application/json') {
    console.warn(`WFS输出格式被设置为 ${defaultParams.outputFormat}，强制修改为application/json以避免XML解析错误`);
    defaultParams.outputFormat = 'application/json';
  }
  
  const searchParams = new URLSearchParams();
  for (const [key, value] of Object.entries(defaultParams)) {
    // 不添加srsname参数到URL查询字符串中
    if (key !== 'srsname') {
      searchParams.append(key, String(value));
    }
  }
  
  return `${baseUrl}/wfs?${searchParams.toString()}`;
}

/**
 * 构建Geoserver WMS URL
 * @param workspace 工作区名称
 * @param layerName 图层名称
 * @param params 附加参数
 * @returns 构建的WMS URL
 */
export function buildGeoserverWmsUrl(workspace: string, layerName: string, params: Record<string, any> = {}): string {
  const baseUrl = getGeoserverBaseUrl();
  const defaultParams = {
    service: 'WMS',
    version: '1.1.0',
    request: 'GetMap',
    layers: `${workspace}:${layerName}`,
    format: 'image/png',
    transparent: true,
    ...params
  };
  
  const searchParams = new URLSearchParams();
  for (const [key, value] of Object.entries(defaultParams)) {
    searchParams.append(key, String(value));
  }
  
  return `${baseUrl}/wms?${searchParams.toString()}`;
}

/**
 * 构建Geoserver WMTS URL
 * @param workspace 工作区名称
 * @param layerName 图层名称
 * @param params 附加参数
 * @returns 构建的WMTS URL
 */
export function buildGeoserverWmtsUrl(workspace: string, layerName: string, params: Record<string, any> = {}): string {
  const baseUrl = getGeoserverBaseUrl();
  // 移除末尾斜杠，防止双斜杠问题
  const baseUrlTrimmed = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
  
  // 始终使用默认值，无需从配置中获取
  const defaultParams = {
    service: 'WMTS',
    version: '1.0.0',
    request: 'GetTile',
    layer: `${workspace}:${layerName}`,
    style: '',
    format: 'image/png',
    tileMatrixSet: 'EPSG:4326',
    ...params
  };
  
  // 始终使用KVP格式（更可靠），不再支持RESTful模式
  const urlParams = new URLSearchParams();
  for (const [key, value] of Object.entries(defaultParams)) {
    urlParams.append(key, value.toString());
  }
  
  // 构建正确的WMTS服务路径
  let wmtsPath = 'gwc/service/wmts';
  // 如果baseUrl已经包含了geoserver，则不需要添加workspace路径
  if (!baseUrlTrimmed.includes('/geoserver/')) {
    wmtsPath = `${workspace}/` + wmtsPath;
  }
  
  const url = `${baseUrlTrimmed}/${wmtsPath}?${urlParams.toString()}`;
  console.log(`构建的WMTS URL (KVP): ${url}`);
  return url;
}

/**
 * 向后兼容的函数
 * @deprecated 使用 getGeoserverBaseUrl 替代
 */
export function getGeoserverUrl(): string {
  return `${getGeoserverBaseUrl()}/wfs`;
}

/**
 * 向后兼容的函数
 * @deprecated 使用 buildGeoserverWmsUrl 替代
 */
export function getGeoserverWmsUrl(): string {
  return `${getGeoserverBaseUrl()}/wms`;
}

/**
 * 向后兼容的函数
 * @deprecated 使用 buildGeoserverWfsUrl 替代
 */
export function getGeoserverWfsUrl(): string {
  return getGeoserverUrl();
}

/**
 * 获取图层边界框
 * @param workspace 工作区名称
 * @param layer 图层名称
 * @returns Promise，包含图层边界框信息
 */
export async function getLayerBbox(workspace: string, layer: string): Promise<any> {
  try {
    // 检查是否在开发环境中，如果是则使用代理
    const isDev = import.meta.env.DEV;
    let url: string;

    if (isDev) {
      // 开发环境使用代理，避免 CORS 问题
      url = `/api/management/layers/bbox/?workspace=${encodeURIComponent(workspace)}&layer=${encodeURIComponent(layer)}`;
      console.log(`开发环境，使用代理URL: ${url}`);
    } else {
      // 生产环境直接访问
      const host = import.meta.env.VITE_GEOSERVER_HOST || import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
      const port = import.meta.env.VITE_GEOSERVER_HD_PORT || '5083';
      url = `http://${host}:${port}/api/management/layers/bbox/?workspace=${encodeURIComponent(workspace)}&layer=${encodeURIComponent(layer)}`;
      console.log(`生产环境，直接访问URL: ${url}`);
    }

    console.log(`获取图层边界框，请求URL: ${url}`);
    
    // 设置超时时间
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 增加超时时间到10秒
    
    try {
      // 发起请求
      console.log(`开始发送请求到: ${url}`);
      const response = await fetch(url, {
        signal: controller.signal,
        method: 'GET',
        mode: 'cors', // 明确指定 CORS 模式
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
      
      clearTimeout(timeoutId); // 清除超时
      
      console.log(`收到响应，状态码: ${response.status}`);
      
      if (!response.ok) {
        throw new Error(`HTTP 错误: ${response.status}`);
      }
      
      // 获取响应文本并尝试解析
      const responseText = await response.text();
      console.log(`响应内容: ${responseText.substring(0, 200)}${responseText.length > 200 ? '...' : ''}`);
      
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error(`JSON解析错误: ${parseError}`);
        throw new Error(`无法解析JSON响应: ${responseText.substring(0, 100)}`);
      }
      
      console.log(`解析后的数据: `, data);
      
      // 验证返回的数据
      if (!data) {
        console.warn(`API返回的数据为空`);
        return {
          status: 'error',
          message: 'API返回的数据为空'
        };
      }
      
      if (data.status !== 'success') {
        console.warn(`API返回的状态不是success: ${data.status}`);
        return {
          status: 'error',
          message: `API返回的状态不是success: ${data.status}`,
          originalData: data
        };
      }
      
      if (!data.bbox) {
        console.warn(`API返回的数据中没有bbox字段: ${JSON.stringify(data)}`);
        return {
          status: 'error',
          message: 'API返回的数据中没有bbox字段',
          originalData: data
        };
      }
      
      // 检查是否返回了有效的边界框
      const { latLon } = data.bbox;
      console.log("data",data)
      console.log("data.bbox",data.bbox)
      console.log("latLon",latLon)
      if (!latLon || 
          !('minx' in latLon) || !('miny' in latLon) || 
          !('maxx' in latLon) || !('maxy' in latLon)) {
        console.warn(`API返回的bbox数据缺少必要的坐标信息: ${JSON.stringify(data.bbox)}`);
        return {
          status: 'error',
          message: 'API返回的边界框数据缺少必要的坐标信息',
          originalData: data
        };
      }
      
      // 检查是否为全球范围，这可能是一个默认值而非实际范围
      if (latLon.minx === -180 && latLon.miny === -90 && 
          latLon.maxx === 180 && latLon.maxy === 90) {
        console.warn(`API返回了全球范围边界框，这可能不是图层的实际边界`);
      }
      
      console.log(`获取到图层 ${workspace}:${layer} 的边界框数据:`, data);
      return data;
      
    } catch (fetchError: any) {
      if (fetchError.name === 'AbortError') {
        console.error(`请求超时: ${url}`);
        throw new Error(`请求超时: ${url}`);
      }

      // 详细的错误信息
      console.error(`请求失败详情:`, {
        url: url,
        error: fetchError,
        message: fetchError.message,
        name: fetchError.name,
        stack: fetchError.stack
      });

      // 检查是否是网络错误
      if (fetchError.message.includes('Failed to fetch') ||
          fetchError.message.includes('Network request failed') ||
          fetchError.message.includes('CORS')) {
        throw new Error(`网络请求失败，可能是CORS跨域问题或服务器不可达: ${url}`);
      }

      throw fetchError;
    } finally {
      clearTimeout(timeoutId);
    }
  } catch (error: any) {
    console.error(`获取图层 ${workspace}:${layer} 边界框失败:`, error);
    throw error;
  }
}

/**
 * 测试 Geoserver API 连接
 * @returns Promise，包含连接测试结果
 */
export async function testGeoserverConnection(): Promise<any> {
  try {
    const isDev = import.meta.env.DEV;
    let url: string;

    if (isDev) {
      url = '/api/management/layers/bbox?workspace=testodm&layer=20250705171600';
      console.log(`测试开发环境代理连接: ${url}`);
    } else {
      const host = import.meta.env.VITE_GEOSERVER_HOST || '127.0.0.1';
      const port = import.meta.env.VITE_GEOSERVER_HD_PORT || '5083';
      url = `http://${host}:${port}/api/management/layers/bbox?workspace=testodm&layer=20250705171600`;
      console.log(`测试生产环境直接连接: ${url}`);
    }

    const response = await fetch(url, {
      method: 'GET',
      mode: 'cors',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    console.log(`连接测试响应状态: ${response.status}`);

    if (!response.ok) {
      throw new Error(`HTTP 错误: ${response.status}`);
    }

    const data = await response.json();
    console.log(`连接测试成功:`, data);
    return data;

  } catch (error: any) {
    console.error(`连接测试失败:`, error);
    throw error;
  }
}