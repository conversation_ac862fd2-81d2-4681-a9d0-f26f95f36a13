<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row shadow="hover" class="ml10">
				<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
					<el-form-item prop="departmentName" label="部门">
						<el-input placeholder="请输入部门名称" style="width: 250px" v-model="state.queryForm.departmentName"> </el-input>
					</el-form-item>
					<el-form-item prop="businessTypeName" label="业务场景">
						<el-input placeholder="请输入业务场景" style="width: 250px" v-model="state.queryForm.businessTypeName"> </el-input>
					</el-form-item>
					<br />
					<el-form-item prop="cityName" label="区域">
						<el-cascader
							clearable
							filterable
							placeholder="请选择区域"
							style="width: 490px"
							v-model="selectCityCode"
							:options="gxData"
							:props="cascaderProps"
						/>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="searchFun()">
							{{ $t('common.queryBtn') }}
						</el-button>
					</el-form-item>
					<!-- <el-form-item>
						<el-cascader v-model="text1" :options="gxData" />
					</el-form-item> -->
				</el-form>
			</el-row>
			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()" v-auth="'admin_businessAuditDepartment_add'">
						新 增
					</el-button>
					<!-- <el-button plain icon="upload-filled" type="primary" class="ml10" @click="excelUploadRef.show()" v-auth="'sys_user_add'"> 导 入 </el-button> -->
					<el-button
						plain
						:disabled="multiple"
						icon="Delete"
						type="primary"
						v-auth="'admin_businessAuditDepartment_del'"
						@click="handleDelete(selectObjs)"
					>
						删 除
					</el-button>
				</div>
			</el-row>
			<el-table
				:data="state.dataList"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				@selection-change="selectionChangHandle"
				@sort-change="sortChangeHandle"
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column type="index" label="序号" width="60" />
				<el-table-column prop="cityName" label="区域" show-overflow-tooltip />
				<el-table-column prop="businessTypeName" label="业务类型" show-overflow-tooltip />
				<el-table-column prop="departmentName" label="审核部门" show-overflow-tooltip />
				<el-table-column label="操作" width="150">
					<template #default="scope">
						<el-button icon="view" text type="primary" @click="formDialogRef.openDialog(scope.row)">查看</el-button>
						<el-button icon="delete" text type="primary" v-auth="'admin_businessAuditDepartment_del'" @click="handleDelete([scope.row.id])"
							>删除</el-button
						>
					</template>
				</el-table-column>
			</el-table>
			<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />

		<!-- 导入excel (需要在 upms-biz/resources/file 下维护模板) -->
		<!-- <upload-excel
			ref="excelUploadRef"
			title="导入"
			url="/manage/auditDepartment/import"
			temp-url="/admin/sys-file/local/file/businessAuditDepartment.xlsx"
			@refreshDataList="getDataList"
		/> -->
	</div>
</template>

<script setup lang="ts" name="systemBusinessAuditDepartment">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchList, delObjs } from '/@/api/manage/auditDepartment';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
import gxData from '/@/assets/guangxi-area.json';

const cascaderProps = {
	checkStrictly: true,
};

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// 定义查询字典

// 定义变量内容
const formDialogRef = ref();
const excelUploadRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields();
	// 清空多选
	selectObjs.value = [];
	selectCityCode.value = [];
	getDataList();
};

const selectCityCode = ref(import.meta.env.VITE_DEFAULT_REGION.split(','));

// 清空搜索条件
const searchFun = () => {
	console.log(selectCityCode.value);
	if (selectCityCode.value && selectCityCode.value !== []) {
		state.queryForm.cityCode = selectCityCode.value[selectCityCode.value.length - 1];
	} else {
		state.queryForm.cityCode = '';
	}
	getDataList();
};

// 导出excel
const exportExcel = () => {
	downBlobFile('/manage/auditDepartment/export', Object.assign(state.queryForm, { ids: selectObjs }), 'businessAuditDepartment.xlsx');
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm('此操作将永久删除');
	} catch {
		return;
	}

	try {
		await delObjs(ids);
		getDataList();
		useMessage().success('删除成功');
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
</script>
