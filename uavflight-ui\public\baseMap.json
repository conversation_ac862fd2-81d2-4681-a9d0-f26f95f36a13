{"layers": [{"id": "basemap_xyz", "name": "南宁地图", "type": "raster", "initialLoad": true, "protocol": "WMTS", "url": "http://127.0.0.1:8083/geoserver/gwc/demo/drone:geotiff_coverage?gridSet=EPSG:4326&format=image/png"}, {"id": "custom_points1", "name": "自定义点数据1", "type": "CustomPoint", "protocol": "CustomPointJSON", "url": "http://127.0.0.1:81/TT051/TT051_v1_MYolov8n/TT051_v1_MYolov8n.json", "initialLoad": false, "categories": ["4", "0", "1"], "event": "TT051", "theme": "自定义数据"}, {"id": "custom_points2", "name": "自定义点数据2", "type": "CustomPoint", "protocol": "CustomPointJSON", "url": "http://127.0.0.1:81/TT052/TT052_v1_MYolov8n/TT052_v1_MYolov8n.json", "initialLoad": false, "categories": ["4", "0", "1"], "event": "TT052", "theme": "自定义数据"}, {"id": "custom_points3", "name": "自定义点数据3", "type": "CustomPoint", "protocol": "CustomPointJSON", "url": "http://127.0.0.1:81/TT053/TT053_v1_MYolov8n/TT053_v1_MYolov8n.json", "initialLoad": false, "categories": ["4", "0", "1"], "event": "TT053", "theme": "自定义数据"}, {"id": "custom_points4", "name": "自定义点数据4", "type": "CustomPoint", "protocol": "CustomPointJSON", "url": "http://127.0.0.1:81/TT054/TT054_v1_MYolov8n/TT054_v1_MYolov8n.json", "initialLoad": false, "categories": ["4", "0", "1"], "event": "TT054"}, {"id": "custom_points5", "name": "自定义点数据5", "type": "CustomPoint", "protocol": "CustomPointJSON", "url": "http://127.0.0.1:81/TT055/TT055_v1_MYolov8n/TT055_v1_MYolov8n.json", "initialLoad": false, "categories": ["4", "0", "1"], "event": "TT055"}, {"id": "custom_points6", "name": "自定义点数据6", "type": "CustomPoint", "protocol": "CustomPointJSON", "url": "http://127.0.0.1:81/TT056/TT056_v1_MYolov8n/TT056_v1_MYolov8n.json", "initialLoad": false, "categories": ["4", "0", "1"], "event": "TT056"}, {"id": "tasks_polygons", "name": "任务区域", "type": "vector", "protocol": "GeoJSON", "geometryType": "MultiPolygon", "url": "http://127.0.0.1:8083/geoserver/drone/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=drone%3Aarea2&maxFeatures=50&outputFormat=application%2Fjson", "initialLoad": false, "event": "TT01", "defaultStyle": "default_polygon", "styleRules": [{"filter": {"property": "id", "operator": "=", "value": "3"}, "style": "red_polygon"}, {"filter": {"property": "id", "operator": "=", "value": "4"}, "style": "green_polygon"}]}, {"id": "terrain_dem", "name": "地形高程模型", "type": "terrain", "protocol": "DEM", "url": "http://127.0.0.1:8083/geoserver/wcs?service=WCS&version=2.0.1&request=GetCoverage&coverageId=drone:dem_coverage&format=image/tiff", "initialLoad": false, "parameters": {"heightFactor": 1.0, "waterMask": false, "requestVertexNormals": true}}, {"id": "mvt_example", "name": "MVT示例图层", "type": "vector", "protocol": "MVT", "url": "http://127.0.0.1:8083/geoserver/gwc/demo/drone:merged_optimized/{z}/{x}/{y}.mvt", "initialLoad": true, "defaultStyle": "default_mvt", "styleRules": [{"filter": {"property": "DN", "operator": "=", "value": 1}, "style": "red_mvt"}, {"filter": {"property": "DN", "operator": "=", "value": 2}, "style": "orange_mvt"}]}]}