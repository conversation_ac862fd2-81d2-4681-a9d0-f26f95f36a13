{"version": 3, "sources": ["../../ol/extent/Relationship.js", "../../ol/AssertionError.js", "../../ol/asserts.js", "../../ol/extent.js"], "sourcesContent": ["/**\n * @module ol/extent/Relationship\n */\n\n/**\n * Relationship to an extent.\n * @enum {number}\n */\nexport default {\n  UNKNOWN: 0,\n  INTERSECTING: 1,\n  ABOVE: 2,\n  RIGHT: 4,\n  BELOW: 8,\n  LEFT: 16,\n};\n", "/**\n * @module ol/AssertionError\n */\n\n/** @type {Object<number, string>} */\nconst messages = {\n  1: 'The view center is not defined',\n  2: 'The view resolution is not defined',\n  3: 'The view rotation is not defined',\n  4: '`image` and `src` cannot be provided at the same time',\n  5: '`imgSize` must be set when `image` is provided',\n  7: '`format` must be set when `url` is set',\n  8: 'Unknown `serverType` configured',\n  9: '`url` must be configured or set using `#setUrl()`',\n  10: 'The default `geometryFunction` can only handle `Point` geometries',\n  11: '`options.featureTypes` must be an Array',\n  12: '`options.geometryName` must also be provided when `options.bbox` is set',\n  13: 'Invalid corner',\n  14: 'Invalid color',\n  15: 'Tried to get a value for a key that does not exist in the cache',\n  16: 'Tried to set a value for a key that is used already',\n  17: '`resolutions` must be sorted in descending order',\n  18: 'Either `origin` or `origins` must be configured, never both',\n  19: 'Number of `tileSizes` and `resolutions` must be equal',\n  20: 'Number of `origins` and `resolutions` must be equal',\n  22: 'Either `tileSize` or `tileSizes` must be configured, never both',\n  24: 'Invalid extent or geometry provided as `geometry`',\n  25: 'Cannot fit empty extent provided as `geometry`',\n  26: 'Features must have an id set',\n  27: 'Features must have an id set',\n  28: '`renderMode` must be `\"hybrid\"` or `\"vector\"`',\n  30: 'The passed `feature` was already added to the source',\n  31: 'Tried to enqueue an `element` that was already added to the queue',\n  32: 'Transformation matrix cannot be inverted',\n  33: 'Invalid units',\n  34: 'Invalid geometry layout',\n  36: 'Unknown SRS type',\n  37: 'Unknown geometry type found',\n  38: '`styleMapValue` has an unknown type',\n  39: 'Unknown geometry type',\n  40: 'Expected `feature` to have a geometry',\n  41: 'Expected an `ol/style/Style` or an array of `ol/style/Style.js`',\n  42: 'Question unknown, the answer is 42',\n  43: 'Expected `layers` to be an array or a `Collection`',\n  47: 'Expected `controls` to be an array or an `ol/Collection`',\n  48: 'Expected `interactions` to be an array or an `ol/Collection`',\n  49: 'Expected `overlays` to be an array or an `ol/Collection`',\n  50: '`options.featureTypes` should be an Array',\n  51: 'Either `url` or `tileJSON` options must be provided',\n  52: 'Unknown `serverType` configured',\n  53: 'Unknown `tierSizeCalculation` configured',\n  55: 'The {-y} placeholder requires a tile grid with extent',\n  56: 'mapBrowserEvent must originate from a pointer event',\n  57: 'At least 2 conditions are required',\n  59: 'Invalid command found in the PBF',\n  60: 'Missing or invalid `size`',\n  61: 'Cannot determine IIIF Image API version from provided image information JSON',\n  62: 'A `WebGLArrayBuffer` must either be of type `ELEMENT_ARRAY_BUFFER` or `ARRAY_BUFFER`',\n  64: 'Layer opacity must be a number',\n  66: '`forEachFeatureAtCoordinate` cannot be used on a WebGL layer if the hit detection logic has not been enabled. This is done by providing adequate shaders using the `hitVertexShader` and `hitFragmentShader` properties of `WebGLPointsLayerRenderer`',\n  67: 'A layer can only be added to the map once. Use either `layer.setMap()` or `map.addLayer()`, not both',\n  68: 'A VectorTile source can only be rendered if it has a projection compatible with the view projection',\n  69: '`width` or `height` cannot be provided together with `scale`',\n};\n\n/**\n * Error object thrown when an assertion failed. This is an ECMA-262 Error,\n * extended with a `code` property.\n * See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error.\n */\nclass AssertionError extends Error {\n  /**\n   * @param {number} code Error code.\n   */\n  constructor(code) {\n    const message = messages[code];\n\n    super(message);\n\n    /**\n     * Error code. The meaning of the code can be found on\n     * https://openlayers.org/en/latest/doc/errors/ (replace `latest` with\n     * the version found in the OpenLayers script's header comment if a version\n     * other than the latest is used).\n     * @type {number}\n     * @deprecated ol/AssertionError and error codes will be removed in v8.0\n     * @api\n     */\n    this.code = code;\n\n    /**\n     * @type {string}\n     */\n    this.name = 'AssertionError';\n\n    // Re-assign message, see https://github.com/Rich-Harris/buble/issues/40\n    this.message = message;\n  }\n}\n\nexport default AssertionError;\n", "/**\n * @module ol/asserts\n */\nimport AssertionError from './AssertionError.js';\n\n/**\n * @param {*} assertion Assertion we expected to be truthy.\n * @param {number} errorCode Error code.\n */\nexport function assert(assertion, errorCode) {\n  if (!assertion) {\n    throw new AssertionError(errorCode);\n  }\n}\n", "/**\n * @module ol/extent\n */\nimport Relationship from './extent/Relationship.js';\nimport {assert} from './asserts.js';\n\n/**\n * An array of numbers representing an extent: `[minx, miny, maxx, maxy]`.\n * @typedef {Array<number>} Extent\n * @api\n */\n\n/**\n * Extent corner.\n * @typedef {'bottom-left' | 'bottom-right' | 'top-left' | 'top-right'} Corner\n */\n\n/**\n * Build an extent that includes all given coordinates.\n *\n * @param {Array<import(\"./coordinate.js\").Coordinate>} coordinates Coordinates.\n * @return {Extent} Bounding extent.\n * @api\n */\nexport function boundingExtent(coordinates) {\n  const extent = createEmpty();\n  for (let i = 0, ii = coordinates.length; i < ii; ++i) {\n    extendCoordinate(extent, coordinates[i]);\n  }\n  return extent;\n}\n\n/**\n * @param {Array<number>} xs Xs.\n * @param {Array<number>} ys Ys.\n * @param {Extent} [dest] Destination extent.\n * @private\n * @return {Extent} Extent.\n */\nfunction _boundingExtentXYs(xs, ys, dest) {\n  const minX = Math.min.apply(null, xs);\n  const minY = Math.min.apply(null, ys);\n  const maxX = Math.max.apply(null, xs);\n  const maxY = Math.max.apply(null, ys);\n  return createOrUpdate(minX, minY, maxX, maxY, dest);\n}\n\n/**\n * Return extent increased by the provided value.\n * @param {Extent} extent Extent.\n * @param {number} value The amount by which the extent should be buffered.\n * @param {Extent} [dest] Extent.\n * @return {Extent} Extent.\n * @api\n */\nexport function buffer(extent, value, dest) {\n  if (dest) {\n    dest[0] = extent[0] - value;\n    dest[1] = extent[1] - value;\n    dest[2] = extent[2] + value;\n    dest[3] = extent[3] + value;\n    return dest;\n  }\n  return [\n    extent[0] - value,\n    extent[1] - value,\n    extent[2] + value,\n    extent[3] + value,\n  ];\n}\n\n/**\n * Creates a clone of an extent.\n *\n * @param {Extent} extent Extent to clone.\n * @param {Extent} [dest] Extent.\n * @return {Extent} The clone.\n */\nexport function clone(extent, dest) {\n  if (dest) {\n    dest[0] = extent[0];\n    dest[1] = extent[1];\n    dest[2] = extent[2];\n    dest[3] = extent[3];\n    return dest;\n  }\n  return extent.slice();\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {number} x X.\n * @param {number} y Y.\n * @return {number} Closest squared distance.\n */\nexport function closestSquaredDistanceXY(extent, x, y) {\n  let dx, dy;\n  if (x < extent[0]) {\n    dx = extent[0] - x;\n  } else if (extent[2] < x) {\n    dx = x - extent[2];\n  } else {\n    dx = 0;\n  }\n  if (y < extent[1]) {\n    dy = extent[1] - y;\n  } else if (extent[3] < y) {\n    dy = y - extent[3];\n  } else {\n    dy = 0;\n  }\n  return dx * dx + dy * dy;\n}\n\n/**\n * Check if the passed coordinate is contained or on the edge of the extent.\n *\n * @param {Extent} extent Extent.\n * @param {import(\"./coordinate.js\").Coordinate} coordinate Coordinate.\n * @return {boolean} The coordinate is contained in the extent.\n * @api\n */\nexport function containsCoordinate(extent, coordinate) {\n  return containsXY(extent, coordinate[0], coordinate[1]);\n}\n\n/**\n * Check if one extent contains another.\n *\n * An extent is deemed contained if it lies completely within the other extent,\n * including if they share one or more edges.\n *\n * @param {Extent} extent1 Extent 1.\n * @param {Extent} extent2 Extent 2.\n * @return {boolean} The second extent is contained by or on the edge of the\n *     first.\n * @api\n */\nexport function containsExtent(extent1, extent2) {\n  return (\n    extent1[0] <= extent2[0] &&\n    extent2[2] <= extent1[2] &&\n    extent1[1] <= extent2[1] &&\n    extent2[3] <= extent1[3]\n  );\n}\n\n/**\n * Check if the passed coordinate is contained or on the edge of the extent.\n *\n * @param {Extent} extent Extent.\n * @param {number} x X coordinate.\n * @param {number} y Y coordinate.\n * @return {boolean} The x, y values are contained in the extent.\n * @api\n */\nexport function containsXY(extent, x, y) {\n  return extent[0] <= x && x <= extent[2] && extent[1] <= y && y <= extent[3];\n}\n\n/**\n * Get the relationship between a coordinate and extent.\n * @param {Extent} extent The extent.\n * @param {import(\"./coordinate.js\").Coordinate} coordinate The coordinate.\n * @return {import(\"./extent/Relationship.js\").default} The relationship (bitwise compare with\n *     import(\"./extent/Relationship.js\").Relationship).\n */\nexport function coordinateRelationship(extent, coordinate) {\n  const minX = extent[0];\n  const minY = extent[1];\n  const maxX = extent[2];\n  const maxY = extent[3];\n  const x = coordinate[0];\n  const y = coordinate[1];\n  let relationship = Relationship.UNKNOWN;\n  if (x < minX) {\n    relationship = relationship | Relationship.LEFT;\n  } else if (x > maxX) {\n    relationship = relationship | Relationship.RIGHT;\n  }\n  if (y < minY) {\n    relationship = relationship | Relationship.BELOW;\n  } else if (y > maxY) {\n    relationship = relationship | Relationship.ABOVE;\n  }\n  if (relationship === Relationship.UNKNOWN) {\n    relationship = Relationship.INTERSECTING;\n  }\n  return relationship;\n}\n\n/**\n * Create an empty extent.\n * @return {Extent} Empty extent.\n * @api\n */\nexport function createEmpty() {\n  return [Infinity, Infinity, -Infinity, -Infinity];\n}\n\n/**\n * Create a new extent or update the provided extent.\n * @param {number} minX Minimum X.\n * @param {number} minY Minimum Y.\n * @param {number} maxX Maximum X.\n * @param {number} maxY Maximum Y.\n * @param {Extent} [dest] Destination extent.\n * @return {Extent} Extent.\n */\nexport function createOrUpdate(minX, minY, maxX, maxY, dest) {\n  if (dest) {\n    dest[0] = minX;\n    dest[1] = minY;\n    dest[2] = maxX;\n    dest[3] = maxY;\n    return dest;\n  }\n  return [minX, minY, maxX, maxY];\n}\n\n/**\n * Create a new empty extent or make the provided one empty.\n * @param {Extent} [dest] Extent.\n * @return {Extent} Extent.\n */\nexport function createOrUpdateEmpty(dest) {\n  return createOrUpdate(Infinity, Infinity, -Infinity, -Infinity, dest);\n}\n\n/**\n * @param {import(\"./coordinate.js\").Coordinate} coordinate Coordinate.\n * @param {Extent} [dest] Extent.\n * @return {Extent} Extent.\n */\nexport function createOrUpdateFromCoordinate(coordinate, dest) {\n  const x = coordinate[0];\n  const y = coordinate[1];\n  return createOrUpdate(x, y, x, y, dest);\n}\n\n/**\n * @param {Array<import(\"./coordinate.js\").Coordinate>} coordinates Coordinates.\n * @param {Extent} [dest] Extent.\n * @return {Extent} Extent.\n */\nexport function createOrUpdateFromCoordinates(coordinates, dest) {\n  const extent = createOrUpdateEmpty(dest);\n  return extendCoordinates(extent, coordinates);\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {Extent} [dest] Extent.\n * @return {Extent} Extent.\n */\nexport function createOrUpdateFromFlatCoordinates(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  dest\n) {\n  const extent = createOrUpdateEmpty(dest);\n  return extendFlatCoordinates(extent, flatCoordinates, offset, end, stride);\n}\n\n/**\n * @param {Array<Array<import(\"./coordinate.js\").Coordinate>>} rings Rings.\n * @param {Extent} [dest] Extent.\n * @return {Extent} Extent.\n */\nexport function createOrUpdateFromRings(rings, dest) {\n  const extent = createOrUpdateEmpty(dest);\n  return extendRings(extent, rings);\n}\n\n/**\n * Determine if two extents are equivalent.\n * @param {Extent} extent1 Extent 1.\n * @param {Extent} extent2 Extent 2.\n * @return {boolean} The two extents are equivalent.\n * @api\n */\nexport function equals(extent1, extent2) {\n  return (\n    extent1[0] == extent2[0] &&\n    extent1[2] == extent2[2] &&\n    extent1[1] == extent2[1] &&\n    extent1[3] == extent2[3]\n  );\n}\n\n/**\n * Determine if two extents are approximately equivalent.\n * @param {Extent} extent1 Extent 1.\n * @param {Extent} extent2 Extent 2.\n * @param {number} tolerance Tolerance in extent coordinate units.\n * @return {boolean} The two extents differ by less than the tolerance.\n */\nexport function approximatelyEquals(extent1, extent2, tolerance) {\n  return (\n    Math.abs(extent1[0] - extent2[0]) < tolerance &&\n    Math.abs(extent1[2] - extent2[2]) < tolerance &&\n    Math.abs(extent1[1] - extent2[1]) < tolerance &&\n    Math.abs(extent1[3] - extent2[3]) < tolerance\n  );\n}\n\n/**\n * Modify an extent to include another extent.\n * @param {Extent} extent1 The extent to be modified.\n * @param {Extent} extent2 The extent that will be included in the first.\n * @return {Extent} A reference to the first (extended) extent.\n * @api\n */\nexport function extend(extent1, extent2) {\n  if (extent2[0] < extent1[0]) {\n    extent1[0] = extent2[0];\n  }\n  if (extent2[2] > extent1[2]) {\n    extent1[2] = extent2[2];\n  }\n  if (extent2[1] < extent1[1]) {\n    extent1[1] = extent2[1];\n  }\n  if (extent2[3] > extent1[3]) {\n    extent1[3] = extent2[3];\n  }\n  return extent1;\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {import(\"./coordinate.js\").Coordinate} coordinate Coordinate.\n */\nexport function extendCoordinate(extent, coordinate) {\n  if (coordinate[0] < extent[0]) {\n    extent[0] = coordinate[0];\n  }\n  if (coordinate[0] > extent[2]) {\n    extent[2] = coordinate[0];\n  }\n  if (coordinate[1] < extent[1]) {\n    extent[1] = coordinate[1];\n  }\n  if (coordinate[1] > extent[3]) {\n    extent[3] = coordinate[1];\n  }\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {Array<import(\"./coordinate.js\").Coordinate>} coordinates Coordinates.\n * @return {Extent} Extent.\n */\nexport function extendCoordinates(extent, coordinates) {\n  for (let i = 0, ii = coordinates.length; i < ii; ++i) {\n    extendCoordinate(extent, coordinates[i]);\n  }\n  return extent;\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @return {Extent} Extent.\n */\nexport function extendFlatCoordinates(\n  extent,\n  flatCoordinates,\n  offset,\n  end,\n  stride\n) {\n  for (; offset < end; offset += stride) {\n    extendXY(extent, flatCoordinates[offset], flatCoordinates[offset + 1]);\n  }\n  return extent;\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {Array<Array<import(\"./coordinate.js\").Coordinate>>} rings Rings.\n * @return {Extent} Extent.\n */\nexport function extendRings(extent, rings) {\n  for (let i = 0, ii = rings.length; i < ii; ++i) {\n    extendCoordinates(extent, rings[i]);\n  }\n  return extent;\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {number} x X.\n * @param {number} y Y.\n */\nexport function extendXY(extent, x, y) {\n  extent[0] = Math.min(extent[0], x);\n  extent[1] = Math.min(extent[1], y);\n  extent[2] = Math.max(extent[2], x);\n  extent[3] = Math.max(extent[3], y);\n}\n\n/**\n * This function calls `callback` for each corner of the extent. If the\n * callback returns a truthy value the function returns that value\n * immediately. Otherwise the function returns `false`.\n * @param {Extent} extent Extent.\n * @param {function(import(\"./coordinate.js\").Coordinate): S} callback Callback.\n * @return {S|boolean} Value.\n * @template S\n */\nexport function forEachCorner(extent, callback) {\n  let val;\n  val = callback(getBottomLeft(extent));\n  if (val) {\n    return val;\n  }\n  val = callback(getBottomRight(extent));\n  if (val) {\n    return val;\n  }\n  val = callback(getTopRight(extent));\n  if (val) {\n    return val;\n  }\n  val = callback(getTopLeft(extent));\n  if (val) {\n    return val;\n  }\n  return false;\n}\n\n/**\n * Get the size of an extent.\n * @param {Extent} extent Extent.\n * @return {number} Area.\n * @api\n */\nexport function getArea(extent) {\n  let area = 0;\n  if (!isEmpty(extent)) {\n    area = getWidth(extent) * getHeight(extent);\n  }\n  return area;\n}\n\n/**\n * Get the bottom left coordinate of an extent.\n * @param {Extent} extent Extent.\n * @return {import(\"./coordinate.js\").Coordinate} Bottom left coordinate.\n * @api\n */\nexport function getBottomLeft(extent) {\n  return [extent[0], extent[1]];\n}\n\n/**\n * Get the bottom right coordinate of an extent.\n * @param {Extent} extent Extent.\n * @return {import(\"./coordinate.js\").Coordinate} Bottom right coordinate.\n * @api\n */\nexport function getBottomRight(extent) {\n  return [extent[2], extent[1]];\n}\n\n/**\n * Get the center coordinate of an extent.\n * @param {Extent} extent Extent.\n * @return {import(\"./coordinate.js\").Coordinate} Center.\n * @api\n */\nexport function getCenter(extent) {\n  return [(extent[0] + extent[2]) / 2, (extent[1] + extent[3]) / 2];\n}\n\n/**\n * Get a corner coordinate of an extent.\n * @param {Extent} extent Extent.\n * @param {Corner} corner Corner.\n * @return {import(\"./coordinate.js\").Coordinate} Corner coordinate.\n */\nexport function getCorner(extent, corner) {\n  let coordinate;\n  if (corner === 'bottom-left') {\n    coordinate = getBottomLeft(extent);\n  } else if (corner === 'bottom-right') {\n    coordinate = getBottomRight(extent);\n  } else if (corner === 'top-left') {\n    coordinate = getTopLeft(extent);\n  } else if (corner === 'top-right') {\n    coordinate = getTopRight(extent);\n  } else {\n    assert(false, 13); // Invalid corner\n  }\n  return coordinate;\n}\n\n/**\n * @param {Extent} extent1 Extent 1.\n * @param {Extent} extent2 Extent 2.\n * @return {number} Enlarged area.\n */\nexport function getEnlargedArea(extent1, extent2) {\n  const minX = Math.min(extent1[0], extent2[0]);\n  const minY = Math.min(extent1[1], extent2[1]);\n  const maxX = Math.max(extent1[2], extent2[2]);\n  const maxY = Math.max(extent1[3], extent2[3]);\n  return (maxX - minX) * (maxY - minY);\n}\n\n/**\n * @param {import(\"./coordinate.js\").Coordinate} center Center.\n * @param {number} resolution Resolution.\n * @param {number} rotation Rotation.\n * @param {import(\"./size.js\").Size} size Size.\n * @param {Extent} [dest] Destination extent.\n * @return {Extent} Extent.\n */\nexport function getForViewAndSize(center, resolution, rotation, size, dest) {\n  const [x0, y0, x1, y1, x2, y2, x3, y3] = getRotatedViewport(\n    center,\n    resolution,\n    rotation,\n    size\n  );\n  return createOrUpdate(\n    Math.min(x0, x1, x2, x3),\n    Math.min(y0, y1, y2, y3),\n    Math.max(x0, x1, x2, x3),\n    Math.max(y0, y1, y2, y3),\n    dest\n  );\n}\n\n/**\n * @param {import(\"./coordinate.js\").Coordinate} center Center.\n * @param {number} resolution Resolution.\n * @param {number} rotation Rotation.\n * @param {import(\"./size.js\").Size} size Size.\n * @return {Array<number>} Linear ring representing the viewport.\n */\nexport function getRotatedViewport(center, resolution, rotation, size) {\n  const dx = (resolution * size[0]) / 2;\n  const dy = (resolution * size[1]) / 2;\n  const cosRotation = Math.cos(rotation);\n  const sinRotation = Math.sin(rotation);\n  const xCos = dx * cosRotation;\n  const xSin = dx * sinRotation;\n  const yCos = dy * cosRotation;\n  const ySin = dy * sinRotation;\n  const x = center[0];\n  const y = center[1];\n  return [\n    x - xCos + ySin,\n    y - xSin - yCos,\n    x - xCos - ySin,\n    y - xSin + yCos,\n    x + xCos - ySin,\n    y + xSin + yCos,\n    x + xCos + ySin,\n    y + xSin - yCos,\n    x - xCos + ySin,\n    y - xSin - yCos,\n  ];\n}\n\n/**\n * Get the height of an extent.\n * @param {Extent} extent Extent.\n * @return {number} Height.\n * @api\n */\nexport function getHeight(extent) {\n  return extent[3] - extent[1];\n}\n\n/**\n * @param {Extent} extent1 Extent 1.\n * @param {Extent} extent2 Extent 2.\n * @return {number} Intersection area.\n */\nexport function getIntersectionArea(extent1, extent2) {\n  const intersection = getIntersection(extent1, extent2);\n  return getArea(intersection);\n}\n\n/**\n * Get the intersection of two extents.\n * @param {Extent} extent1 Extent 1.\n * @param {Extent} extent2 Extent 2.\n * @param {Extent} [dest] Optional extent to populate with intersection.\n * @return {Extent} Intersecting extent.\n * @api\n */\nexport function getIntersection(extent1, extent2, dest) {\n  const intersection = dest ? dest : createEmpty();\n  if (intersects(extent1, extent2)) {\n    if (extent1[0] > extent2[0]) {\n      intersection[0] = extent1[0];\n    } else {\n      intersection[0] = extent2[0];\n    }\n    if (extent1[1] > extent2[1]) {\n      intersection[1] = extent1[1];\n    } else {\n      intersection[1] = extent2[1];\n    }\n    if (extent1[2] < extent2[2]) {\n      intersection[2] = extent1[2];\n    } else {\n      intersection[2] = extent2[2];\n    }\n    if (extent1[3] < extent2[3]) {\n      intersection[3] = extent1[3];\n    } else {\n      intersection[3] = extent2[3];\n    }\n  } else {\n    createOrUpdateEmpty(intersection);\n  }\n  return intersection;\n}\n\n/**\n * @param {Extent} extent Extent.\n * @return {number} Margin.\n */\nexport function getMargin(extent) {\n  return getWidth(extent) + getHeight(extent);\n}\n\n/**\n * Get the size (width, height) of an extent.\n * @param {Extent} extent The extent.\n * @return {import(\"./size.js\").Size} The extent size.\n * @api\n */\nexport function getSize(extent) {\n  return [extent[2] - extent[0], extent[3] - extent[1]];\n}\n\n/**\n * Get the top left coordinate of an extent.\n * @param {Extent} extent Extent.\n * @return {import(\"./coordinate.js\").Coordinate} Top left coordinate.\n * @api\n */\nexport function getTopLeft(extent) {\n  return [extent[0], extent[3]];\n}\n\n/**\n * Get the top right coordinate of an extent.\n * @param {Extent} extent Extent.\n * @return {import(\"./coordinate.js\").Coordinate} Top right coordinate.\n * @api\n */\nexport function getTopRight(extent) {\n  return [extent[2], extent[3]];\n}\n\n/**\n * Get the width of an extent.\n * @param {Extent} extent Extent.\n * @return {number} Width.\n * @api\n */\nexport function getWidth(extent) {\n  return extent[2] - extent[0];\n}\n\n/**\n * Determine if one extent intersects another.\n * @param {Extent} extent1 Extent 1.\n * @param {Extent} extent2 Extent.\n * @return {boolean} The two extents intersect.\n * @api\n */\nexport function intersects(extent1, extent2) {\n  return (\n    extent1[0] <= extent2[2] &&\n    extent1[2] >= extent2[0] &&\n    extent1[1] <= extent2[3] &&\n    extent1[3] >= extent2[1]\n  );\n}\n\n/**\n * Determine if an extent is empty.\n * @param {Extent} extent Extent.\n * @return {boolean} Is empty.\n * @api\n */\nexport function isEmpty(extent) {\n  return extent[2] < extent[0] || extent[3] < extent[1];\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {Extent} [dest] Extent.\n * @return {Extent} Extent.\n */\nexport function returnOrUpdate(extent, dest) {\n  if (dest) {\n    dest[0] = extent[0];\n    dest[1] = extent[1];\n    dest[2] = extent[2];\n    dest[3] = extent[3];\n    return dest;\n  }\n  return extent;\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {number} value Value.\n */\nexport function scaleFromCenter(extent, value) {\n  const deltaX = ((extent[2] - extent[0]) / 2) * (value - 1);\n  const deltaY = ((extent[3] - extent[1]) / 2) * (value - 1);\n  extent[0] -= deltaX;\n  extent[2] += deltaX;\n  extent[1] -= deltaY;\n  extent[3] += deltaY;\n}\n\n/**\n * Determine if the segment between two coordinates intersects (crosses,\n * touches, or is contained by) the provided extent.\n * @param {Extent} extent The extent.\n * @param {import(\"./coordinate.js\").Coordinate} start Segment start coordinate.\n * @param {import(\"./coordinate.js\").Coordinate} end Segment end coordinate.\n * @return {boolean} The segment intersects the extent.\n */\nexport function intersectsSegment(extent, start, end) {\n  let intersects = false;\n  const startRel = coordinateRelationship(extent, start);\n  const endRel = coordinateRelationship(extent, end);\n  if (\n    startRel === Relationship.INTERSECTING ||\n    endRel === Relationship.INTERSECTING\n  ) {\n    intersects = true;\n  } else {\n    const minX = extent[0];\n    const minY = extent[1];\n    const maxX = extent[2];\n    const maxY = extent[3];\n    const startX = start[0];\n    const startY = start[1];\n    const endX = end[0];\n    const endY = end[1];\n    const slope = (endY - startY) / (endX - startX);\n    let x, y;\n    if (!!(endRel & Relationship.ABOVE) && !(startRel & Relationship.ABOVE)) {\n      // potentially intersects top\n      x = endX - (endY - maxY) / slope;\n      intersects = x >= minX && x <= maxX;\n    }\n    if (\n      !intersects &&\n      !!(endRel & Relationship.RIGHT) &&\n      !(startRel & Relationship.RIGHT)\n    ) {\n      // potentially intersects right\n      y = endY - (endX - maxX) * slope;\n      intersects = y >= minY && y <= maxY;\n    }\n    if (\n      !intersects &&\n      !!(endRel & Relationship.BELOW) &&\n      !(startRel & Relationship.BELOW)\n    ) {\n      // potentially intersects bottom\n      x = endX - (endY - minY) / slope;\n      intersects = x >= minX && x <= maxX;\n    }\n    if (\n      !intersects &&\n      !!(endRel & Relationship.LEFT) &&\n      !(startRel & Relationship.LEFT)\n    ) {\n      // potentially intersects left\n      y = endY - (endX - minX) * slope;\n      intersects = y >= minY && y <= maxY;\n    }\n  }\n  return intersects;\n}\n\n/**\n * Apply a transform function to the extent.\n * @param {Extent} extent Extent.\n * @param {import(\"./proj.js\").TransformFunction} transformFn Transform function.\n * Called with `[minX, minY, maxX, maxY]` extent coordinates.\n * @param {Extent} [dest] Destination extent.\n * @param {number} [stops] Number of stops per side used for the transform.\n * By default only the corners are used.\n * @return {Extent} Extent.\n * @api\n */\nexport function applyTransform(extent, transformFn, dest, stops) {\n  if (isEmpty(extent)) {\n    return createOrUpdateEmpty(dest);\n  }\n  let coordinates = [];\n  if (stops > 1) {\n    const width = extent[2] - extent[0];\n    const height = extent[3] - extent[1];\n    for (let i = 0; i < stops; ++i) {\n      coordinates.push(\n        extent[0] + (width * i) / stops,\n        extent[1],\n        extent[2],\n        extent[1] + (height * i) / stops,\n        extent[2] - (width * i) / stops,\n        extent[3],\n        extent[0],\n        extent[3] - (height * i) / stops\n      );\n    }\n  } else {\n    coordinates = [\n      extent[0],\n      extent[1],\n      extent[2],\n      extent[1],\n      extent[2],\n      extent[3],\n      extent[0],\n      extent[3],\n    ];\n  }\n  transformFn(coordinates, coordinates, 2);\n  const xs = [];\n  const ys = [];\n  for (let i = 0, l = coordinates.length; i < l; i += 2) {\n    xs.push(coordinates[i]);\n    ys.push(coordinates[i + 1]);\n  }\n  return _boundingExtentXYs(xs, ys, dest);\n}\n\n/**\n * Modifies the provided extent in-place to be within the real world\n * extent.\n *\n * @param {Extent} extent Extent.\n * @param {import(\"./proj/Projection.js\").default} projection Projection\n * @return {Extent} The extent within the real world extent.\n */\nexport function wrapX(extent, projection) {\n  const projectionExtent = projection.getExtent();\n  const center = getCenter(extent);\n  if (\n    projection.canWrapX() &&\n    (center[0] < projectionExtent[0] || center[0] >= projectionExtent[2])\n  ) {\n    const worldWidth = getWidth(projectionExtent);\n    const worldsAway = Math.floor(\n      (center[0] - projectionExtent[0]) / worldWidth\n    );\n    const offset = worldsAway * worldWidth;\n    extent[0] -= offset;\n    extent[2] -= offset;\n  }\n  return extent;\n}\n\n/**\n * Fits the extent to the real world\n *\n * If the extent does not cross the anti meridian, this will return the extent in an array\n * If the extent crosses the anti meridian, the extent will be sliced, so each part fits within the\n * real world\n *\n *\n * @param {Extent} extent Extent.\n * @param {import(\"./proj/Projection.js\").default} projection Projection\n * @return {Array<Extent>} The extent within the real world extent.\n */\nexport function wrapAndSliceX(extent, projection) {\n  if (projection.canWrapX()) {\n    const projectionExtent = projection.getExtent();\n\n    if (!isFinite(extent[0]) || !isFinite(extent[2])) {\n      return [[projectionExtent[0], extent[1], projectionExtent[2], extent[3]]];\n    }\n\n    wrapX(extent, projection);\n    const worldWidth = getWidth(projectionExtent);\n\n    if (getWidth(extent) > worldWidth) {\n      // the extent wraps around on itself\n      return [[projectionExtent[0], extent[1], projectionExtent[2], extent[3]]];\n    }\n    if (extent[0] < projectionExtent[0]) {\n      // the extent crosses the anti meridian, so it needs to be sliced\n      return [\n        [extent[0] + worldWidth, extent[1], projectionExtent[2], extent[3]],\n        [projectionExtent[0], extent[1], extent[2], extent[3]],\n      ];\n    }\n    if (extent[2] > projectionExtent[2]) {\n      // the extent crosses the anti meridian, so it needs to be sliced\n      return [\n        [extent[0], extent[1], projectionExtent[2], extent[3]],\n        [projectionExtent[0], extent[1], extent[2] - worldWidth, extent[3]],\n      ];\n    }\n  }\n\n  return [extent];\n}\n"], "mappings": ";AAQA,IAAO,uBAAQ;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAAA,EACd,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AACR;;;ACVA,IAAM,WAAW;AAAA,EACf,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AAOA,IAAM,iBAAN,cAA6B,MAAM;AAAA;AAAA;AAAA;AAAA,EAIjC,YAAY,MAAM;AAChB,UAAM,UAAU,SAAS,IAAI;AAE7B,UAAM,OAAO;AAWb,SAAK,OAAO;AAKZ,SAAK,OAAO;AAGZ,SAAK,UAAU;AAAA,EACjB;AACF;AAEA,IAAO,yBAAQ;;;AC3FR,SAAS,OAAO,WAAW,WAAW;AAC3C,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,uBAAe,SAAS;AAAA,EACpC;AACF;;;ACWO,SAAS,eAAe,aAAa;AAC1C,QAAM,SAAS,YAAY;AAC3B,WAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,qBAAiB,QAAQ,YAAY,CAAC,CAAC;AAAA,EACzC;AACA,SAAO;AACT;AASA,SAAS,mBAAmB,IAAI,IAAI,MAAM;AACxC,QAAM,OAAO,KAAK,IAAI,MAAM,MAAM,EAAE;AACpC,QAAM,OAAO,KAAK,IAAI,MAAM,MAAM,EAAE;AACpC,QAAM,OAAO,KAAK,IAAI,MAAM,MAAM,EAAE;AACpC,QAAM,OAAO,KAAK,IAAI,MAAM,MAAM,EAAE;AACpC,SAAO,eAAe,MAAM,MAAM,MAAM,MAAM,IAAI;AACpD;AAUO,SAAS,OAAO,QAAQ,OAAO,MAAM;AAC1C,MAAI,MAAM;AACR,SAAK,CAAC,IAAI,OAAO,CAAC,IAAI;AACtB,SAAK,CAAC,IAAI,OAAO,CAAC,IAAI;AACtB,SAAK,CAAC,IAAI,OAAO,CAAC,IAAI;AACtB,SAAK,CAAC,IAAI,OAAO,CAAC,IAAI;AACtB,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,OAAO,CAAC,IAAI;AAAA,IACZ,OAAO,CAAC,IAAI;AAAA,IACZ,OAAO,CAAC,IAAI;AAAA,IACZ,OAAO,CAAC,IAAI;AAAA,EACd;AACF;AASO,SAAS,MAAM,QAAQ,MAAM;AAClC,MAAI,MAAM;AACR,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,WAAO;AAAA,EACT;AACA,SAAO,OAAO,MAAM;AACtB;AAQO,SAAS,yBAAyB,QAAQ,GAAG,GAAG;AACrD,MAAI,IAAI;AACR,MAAI,IAAI,OAAO,CAAC,GAAG;AACjB,SAAK,OAAO,CAAC,IAAI;AAAA,EACnB,WAAW,OAAO,CAAC,IAAI,GAAG;AACxB,SAAK,IAAI,OAAO,CAAC;AAAA,EACnB,OAAO;AACL,SAAK;AAAA,EACP;AACA,MAAI,IAAI,OAAO,CAAC,GAAG;AACjB,SAAK,OAAO,CAAC,IAAI;AAAA,EACnB,WAAW,OAAO,CAAC,IAAI,GAAG;AACxB,SAAK,IAAI,OAAO,CAAC;AAAA,EACnB,OAAO;AACL,SAAK;AAAA,EACP;AACA,SAAO,KAAK,KAAK,KAAK;AACxB;AAUO,SAAS,mBAAmB,QAAQ,YAAY;AACrD,SAAO,WAAW,QAAQ,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AACxD;AAcO,SAAS,eAAe,SAAS,SAAS;AAC/C,SACE,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC;AAE3B;AAWO,SAAS,WAAW,QAAQ,GAAG,GAAG;AACvC,SAAO,OAAO,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAC5E;AASO,SAAS,uBAAuB,QAAQ,YAAY;AACzD,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,IAAI,WAAW,CAAC;AACtB,QAAM,IAAI,WAAW,CAAC;AACtB,MAAI,eAAe,qBAAa;AAChC,MAAI,IAAI,MAAM;AACZ,mBAAe,eAAe,qBAAa;AAAA,EAC7C,WAAW,IAAI,MAAM;AACnB,mBAAe,eAAe,qBAAa;AAAA,EAC7C;AACA,MAAI,IAAI,MAAM;AACZ,mBAAe,eAAe,qBAAa;AAAA,EAC7C,WAAW,IAAI,MAAM;AACnB,mBAAe,eAAe,qBAAa;AAAA,EAC7C;AACA,MAAI,iBAAiB,qBAAa,SAAS;AACzC,mBAAe,qBAAa;AAAA,EAC9B;AACA,SAAO;AACT;AAOO,SAAS,cAAc;AAC5B,SAAO,CAAC,UAAU,UAAU,WAAW,SAAS;AAClD;AAWO,SAAS,eAAe,MAAM,MAAM,MAAM,MAAM,MAAM;AAC3D,MAAI,MAAM;AACR,SAAK,CAAC,IAAI;AACV,SAAK,CAAC,IAAI;AACV,SAAK,CAAC,IAAI;AACV,SAAK,CAAC,IAAI;AACV,WAAO;AAAA,EACT;AACA,SAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAChC;AAOO,SAAS,oBAAoB,MAAM;AACxC,SAAO,eAAe,UAAU,UAAU,WAAW,WAAW,IAAI;AACtE;AAOO,SAAS,6BAA6B,YAAY,MAAM;AAC7D,QAAM,IAAI,WAAW,CAAC;AACtB,QAAM,IAAI,WAAW,CAAC;AACtB,SAAO,eAAe,GAAG,GAAG,GAAG,GAAG,IAAI;AACxC;AAOO,SAAS,8BAA8B,aAAa,MAAM;AAC/D,QAAM,SAAS,oBAAoB,IAAI;AACvC,SAAO,kBAAkB,QAAQ,WAAW;AAC9C;AAUO,SAAS,kCACd,iBACA,QACA,KACA,QACA,MACA;AACA,QAAM,SAAS,oBAAoB,IAAI;AACvC,SAAO,sBAAsB,QAAQ,iBAAiB,QAAQ,KAAK,MAAM;AAC3E;AAOO,SAAS,wBAAwB,OAAO,MAAM;AACnD,QAAM,SAAS,oBAAoB,IAAI;AACvC,SAAO,YAAY,QAAQ,KAAK;AAClC;AASO,SAAS,OAAO,SAAS,SAAS;AACvC,SACE,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC;AAE3B;AASO,SAAS,oBAAoB,SAAS,SAAS,WAAW;AAC/D,SACE,KAAK,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,aACpC,KAAK,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,aACpC,KAAK,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,aACpC,KAAK,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI;AAExC;AASO,SAAS,OAAO,SAAS,SAAS;AACvC,MAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,YAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,EACxB;AACA,MAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,YAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,EACxB;AACA,MAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,YAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,EACxB;AACA,MAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,YAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,EACxB;AACA,SAAO;AACT;AAMO,SAAS,iBAAiB,QAAQ,YAAY;AACnD,MAAI,WAAW,CAAC,IAAI,OAAO,CAAC,GAAG;AAC7B,WAAO,CAAC,IAAI,WAAW,CAAC;AAAA,EAC1B;AACA,MAAI,WAAW,CAAC,IAAI,OAAO,CAAC,GAAG;AAC7B,WAAO,CAAC,IAAI,WAAW,CAAC;AAAA,EAC1B;AACA,MAAI,WAAW,CAAC,IAAI,OAAO,CAAC,GAAG;AAC7B,WAAO,CAAC,IAAI,WAAW,CAAC;AAAA,EAC1B;AACA,MAAI,WAAW,CAAC,IAAI,OAAO,CAAC,GAAG;AAC7B,WAAO,CAAC,IAAI,WAAW,CAAC;AAAA,EAC1B;AACF;AAOO,SAAS,kBAAkB,QAAQ,aAAa;AACrD,WAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,qBAAiB,QAAQ,YAAY,CAAC,CAAC;AAAA,EACzC;AACA,SAAO;AACT;AAUO,SAAS,sBACd,QACA,iBACA,QACA,KACA,QACA;AACA,SAAO,SAAS,KAAK,UAAU,QAAQ;AACrC,aAAS,QAAQ,gBAAgB,MAAM,GAAG,gBAAgB,SAAS,CAAC,CAAC;AAAA,EACvE;AACA,SAAO;AACT;AAOO,SAAS,YAAY,QAAQ,OAAO;AACzC,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,sBAAkB,QAAQ,MAAM,CAAC,CAAC;AAAA,EACpC;AACA,SAAO;AACT;AAOO,SAAS,SAAS,QAAQ,GAAG,GAAG;AACrC,SAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC;AACjC,SAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC;AACjC,SAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC;AACjC,SAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC;AACnC;AAWO,SAAS,cAAc,QAAQ,UAAU;AAC9C,MAAI;AACJ,QAAM,SAAS,cAAc,MAAM,CAAC;AACpC,MAAI,KAAK;AACP,WAAO;AAAA,EACT;AACA,QAAM,SAAS,eAAe,MAAM,CAAC;AACrC,MAAI,KAAK;AACP,WAAO;AAAA,EACT;AACA,QAAM,SAAS,YAAY,MAAM,CAAC;AAClC,MAAI,KAAK;AACP,WAAO;AAAA,EACT;AACA,QAAM,SAAS,WAAW,MAAM,CAAC;AACjC,MAAI,KAAK;AACP,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAQO,SAAS,QAAQ,QAAQ;AAC9B,MAAI,OAAO;AACX,MAAI,CAAC,QAAQ,MAAM,GAAG;AACpB,WAAO,SAAS,MAAM,IAAI,UAAU,MAAM;AAAA,EAC5C;AACA,SAAO;AACT;AAQO,SAAS,cAAc,QAAQ;AACpC,SAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAC9B;AAQO,SAAS,eAAe,QAAQ;AACrC,SAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAC9B;AAQO,SAAS,UAAU,QAAQ;AAChC,SAAO,EAAE,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC;AAClE;AAQO,SAAS,UAAU,QAAQ,QAAQ;AACxC,MAAI;AACJ,MAAI,WAAW,eAAe;AAC5B,iBAAa,cAAc,MAAM;AAAA,EACnC,WAAW,WAAW,gBAAgB;AACpC,iBAAa,eAAe,MAAM;AAAA,EACpC,WAAW,WAAW,YAAY;AAChC,iBAAa,WAAW,MAAM;AAAA,EAChC,WAAW,WAAW,aAAa;AACjC,iBAAa,YAAY,MAAM;AAAA,EACjC,OAAO;AACL,WAAO,OAAO,EAAE;AAAA,EAClB;AACA,SAAO;AACT;AAOO,SAAS,gBAAgB,SAAS,SAAS;AAChD,QAAM,OAAO,KAAK,IAAI,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAC5C,QAAM,OAAO,KAAK,IAAI,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAC5C,QAAM,OAAO,KAAK,IAAI,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAC5C,QAAM,OAAO,KAAK,IAAI,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAC5C,UAAQ,OAAO,SAAS,OAAO;AACjC;AAUO,SAAS,kBAAkB,QAAQ,YAAY,UAAU,MAAM,MAAM;AAC1E,QAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO;AAAA,IACL,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACvB,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACvB,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACvB,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACvB;AAAA,EACF;AACF;AASO,SAAS,mBAAmB,QAAQ,YAAY,UAAU,MAAM;AACrE,QAAM,KAAM,aAAa,KAAK,CAAC,IAAK;AACpC,QAAM,KAAM,aAAa,KAAK,CAAC,IAAK;AACpC,QAAM,cAAc,KAAK,IAAI,QAAQ;AACrC,QAAM,cAAc,KAAK,IAAI,QAAQ;AACrC,QAAM,OAAO,KAAK;AAClB,QAAM,OAAO,KAAK;AAClB,QAAM,OAAO,KAAK;AAClB,QAAM,OAAO,KAAK;AAClB,QAAM,IAAI,OAAO,CAAC;AAClB,QAAM,IAAI,OAAO,CAAC;AAClB,SAAO;AAAA,IACL,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,EACb;AACF;AAQO,SAAS,UAAU,QAAQ;AAChC,SAAO,OAAO,CAAC,IAAI,OAAO,CAAC;AAC7B;AAOO,SAAS,oBAAoB,SAAS,SAAS;AACpD,QAAM,eAAe,gBAAgB,SAAS,OAAO;AACrD,SAAO,QAAQ,YAAY;AAC7B;AAUO,SAAS,gBAAgB,SAAS,SAAS,MAAM;AACtD,QAAM,eAAe,OAAO,OAAO,YAAY;AAC/C,MAAI,WAAW,SAAS,OAAO,GAAG;AAChC,QAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B,OAAO;AACL,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B;AACA,QAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B,OAAO;AACL,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B;AACA,QAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B,OAAO;AACL,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B;AACA,QAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B,OAAO;AACL,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B;AAAA,EACF,OAAO;AACL,wBAAoB,YAAY;AAAA,EAClC;AACA,SAAO;AACT;AAMO,SAAS,UAAU,QAAQ;AAChC,SAAO,SAAS,MAAM,IAAI,UAAU,MAAM;AAC5C;AAQO,SAAS,QAAQ,QAAQ;AAC9B,SAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC;AACtD;AAQO,SAAS,WAAW,QAAQ;AACjC,SAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAC9B;AAQO,SAAS,YAAY,QAAQ;AAClC,SAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAC9B;AAQO,SAAS,SAAS,QAAQ;AAC/B,SAAO,OAAO,CAAC,IAAI,OAAO,CAAC;AAC7B;AASO,SAAS,WAAW,SAAS,SAAS;AAC3C,SACE,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC;AAE3B;AAQO,SAAS,QAAQ,QAAQ;AAC9B,SAAO,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC;AACtD;AAOO,SAAS,eAAe,QAAQ,MAAM;AAC3C,MAAI,MAAM;AACR,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAMO,SAAS,gBAAgB,QAAQ,OAAO;AAC7C,QAAM,UAAW,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,KAAM,QAAQ;AACxD,QAAM,UAAW,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,KAAM,QAAQ;AACxD,SAAO,CAAC,KAAK;AACb,SAAO,CAAC,KAAK;AACb,SAAO,CAAC,KAAK;AACb,SAAO,CAAC,KAAK;AACf;AAUO,SAAS,kBAAkB,QAAQ,OAAO,KAAK;AACpD,MAAIA,cAAa;AACjB,QAAM,WAAW,uBAAuB,QAAQ,KAAK;AACrD,QAAM,SAAS,uBAAuB,QAAQ,GAAG;AACjD,MACE,aAAa,qBAAa,gBAC1B,WAAW,qBAAa,cACxB;AACA,IAAAA,cAAa;AAAA,EACf,OAAO;AACL,UAAM,OAAO,OAAO,CAAC;AACrB,UAAM,OAAO,OAAO,CAAC;AACrB,UAAM,OAAO,OAAO,CAAC;AACrB,UAAM,OAAO,OAAO,CAAC;AACrB,UAAM,SAAS,MAAM,CAAC;AACtB,UAAM,SAAS,MAAM,CAAC;AACtB,UAAM,OAAO,IAAI,CAAC;AAClB,UAAM,OAAO,IAAI,CAAC;AAClB,UAAM,SAAS,OAAO,WAAW,OAAO;AACxC,QAAI,GAAG;AACP,QAAI,CAAC,EAAE,SAAS,qBAAa,UAAU,EAAE,WAAW,qBAAa,QAAQ;AAEvE,UAAI,QAAQ,OAAO,QAAQ;AAC3B,MAAAA,cAAa,KAAK,QAAQ,KAAK;AAAA,IACjC;AACA,QACE,CAACA,eACD,CAAC,EAAE,SAAS,qBAAa,UACzB,EAAE,WAAW,qBAAa,QAC1B;AAEA,UAAI,QAAQ,OAAO,QAAQ;AAC3B,MAAAA,cAAa,KAAK,QAAQ,KAAK;AAAA,IACjC;AACA,QACE,CAACA,eACD,CAAC,EAAE,SAAS,qBAAa,UACzB,EAAE,WAAW,qBAAa,QAC1B;AAEA,UAAI,QAAQ,OAAO,QAAQ;AAC3B,MAAAA,cAAa,KAAK,QAAQ,KAAK;AAAA,IACjC;AACA,QACE,CAACA,eACD,CAAC,EAAE,SAAS,qBAAa,SACzB,EAAE,WAAW,qBAAa,OAC1B;AAEA,UAAI,QAAQ,OAAO,QAAQ;AAC3B,MAAAA,cAAa,KAAK,QAAQ,KAAK;AAAA,IACjC;AAAA,EACF;AACA,SAAOA;AACT;AAaO,SAAS,eAAe,QAAQ,aAAa,MAAM,OAAO;AAC/D,MAAI,QAAQ,MAAM,GAAG;AACnB,WAAO,oBAAoB,IAAI;AAAA,EACjC;AACA,MAAI,cAAc,CAAC;AACnB,MAAI,QAAQ,GAAG;AACb,UAAM,QAAQ,OAAO,CAAC,IAAI,OAAO,CAAC;AAClC,UAAM,SAAS,OAAO,CAAC,IAAI,OAAO,CAAC;AACnC,aAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAC9B,kBAAY;AAAA,QACV,OAAO,CAAC,IAAK,QAAQ,IAAK;AAAA,QAC1B,OAAO,CAAC;AAAA,QACR,OAAO,CAAC;AAAA,QACR,OAAO,CAAC,IAAK,SAAS,IAAK;AAAA,QAC3B,OAAO,CAAC,IAAK,QAAQ,IAAK;AAAA,QAC1B,OAAO,CAAC;AAAA,QACR,OAAO,CAAC;AAAA,QACR,OAAO,CAAC,IAAK,SAAS,IAAK;AAAA,MAC7B;AAAA,IACF;AAAA,EACF,OAAO;AACL,kBAAc;AAAA,MACZ,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,IACV;AAAA,EACF;AACA,cAAY,aAAa,aAAa,CAAC;AACvC,QAAM,KAAK,CAAC;AACZ,QAAM,KAAK,CAAC;AACZ,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,IAAI,GAAG,KAAK,GAAG;AACrD,OAAG,KAAK,YAAY,CAAC,CAAC;AACtB,OAAG,KAAK,YAAY,IAAI,CAAC,CAAC;AAAA,EAC5B;AACA,SAAO,mBAAmB,IAAI,IAAI,IAAI;AACxC;AAUO,SAAS,MAAM,QAAQ,YAAY;AACxC,QAAM,mBAAmB,WAAW,UAAU;AAC9C,QAAM,SAAS,UAAU,MAAM;AAC/B,MACE,WAAW,SAAS,MACnB,OAAO,CAAC,IAAI,iBAAiB,CAAC,KAAK,OAAO,CAAC,KAAK,iBAAiB,CAAC,IACnE;AACA,UAAM,aAAa,SAAS,gBAAgB;AAC5C,UAAM,aAAa,KAAK;AAAA,OACrB,OAAO,CAAC,IAAI,iBAAiB,CAAC,KAAK;AAAA,IACtC;AACA,UAAM,SAAS,aAAa;AAC5B,WAAO,CAAC,KAAK;AACb,WAAO,CAAC,KAAK;AAAA,EACf;AACA,SAAO;AACT;AAcO,SAAS,cAAc,QAAQ,YAAY;AAChD,MAAI,WAAW,SAAS,GAAG;AACzB,UAAM,mBAAmB,WAAW,UAAU;AAE9C,QAAI,CAAC,SAAS,OAAO,CAAC,CAAC,KAAK,CAAC,SAAS,OAAO,CAAC,CAAC,GAAG;AAChD,aAAO,CAAC,CAAC,iBAAiB,CAAC,GAAG,OAAO,CAAC,GAAG,iBAAiB,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;AAAA,IAC1E;AAEA,UAAM,QAAQ,UAAU;AACxB,UAAM,aAAa,SAAS,gBAAgB;AAE5C,QAAI,SAAS,MAAM,IAAI,YAAY;AAEjC,aAAO,CAAC,CAAC,iBAAiB,CAAC,GAAG,OAAO,CAAC,GAAG,iBAAiB,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;AAAA,IAC1E;AACA,QAAI,OAAO,CAAC,IAAI,iBAAiB,CAAC,GAAG;AAEnC,aAAO;AAAA,QACL,CAAC,OAAO,CAAC,IAAI,YAAY,OAAO,CAAC,GAAG,iBAAiB,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,QAClE,CAAC,iBAAiB,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MACvD;AAAA,IACF;AACA,QAAI,OAAO,CAAC,IAAI,iBAAiB,CAAC,GAAG;AAEnC,aAAO;AAAA,QACL,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,iBAAiB,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,QACrD,CAAC,iBAAiB,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,YAAY,OAAO,CAAC,CAAC;AAAA,MACpE;AAAA,IACF;AAAA,EACF;AAEA,SAAO,CAAC,MAAM;AAChB;", "names": ["intersects"]}