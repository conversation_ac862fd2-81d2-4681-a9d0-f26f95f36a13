{"version": 3, "sources": ["../../ol/uri.js"], "sourcesContent": ["/**\n * @module ol/uri\n */\n\n/**\n * Appends query parameters to a URI.\n *\n * @param {string} uri The original URI, which may already have query data.\n * @param {!Object} params An object where keys are URI-encoded parameter keys,\n *     and the values are arbitrary types or arrays.\n * @return {string} The new URI.\n */\nexport function appendParams(uri, params) {\n  const keyParams = [];\n  // Skip any null or undefined parameter values\n  Object.keys(params).forEach(function (k) {\n    if (params[k] !== null && params[k] !== undefined) {\n      keyParams.push(k + '=' + encodeURIComponent(params[k]));\n    }\n  });\n  const qs = keyParams.join('&');\n  // remove any trailing ? or &\n  uri = uri.replace(/[?&]$/, '');\n  // append ? or & depending on whether uri has existing parameters\n  uri += uri.includes('?') ? '&' : '?';\n  return uri + qs;\n}\n"], "mappings": ";AAYO,SAAS,aAAa,KAAK,QAAQ;AACxC,QAAM,YAAY,CAAC;AAEnB,SAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,GAAG;AACvC,QAAI,OAAO,CAAC,MAAM,QAAQ,OAAO,CAAC,MAAM,QAAW;AACjD,gBAAU,KAAK,IAAI,MAAM,mBAAmB,OAAO,CAAC,CAAC,CAAC;AAAA,IACxD;AAAA,EACF,CAAC;AACD,QAAM,KAAK,UAAU,KAAK,GAAG;AAE7B,QAAM,IAAI,QAAQ,SAAS,EAAE;AAE7B,SAAO,IAAI,SAAS,GAAG,IAAI,MAAM;AACjC,SAAO,MAAM;AACf;", "names": []}