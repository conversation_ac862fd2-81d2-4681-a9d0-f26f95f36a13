/*
 * @Author: AI Assistant
 * @Date: 2025-07-15
 * @Description: 快拼图层管理器类型定义
 *
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved.
 */

// 地图配置
export interface MapConfig {
  mapConfig?: {
    initialView?: {
      center?: [number, number];
      zoom?: number;
      duration?: number;
    };
    queryConfig?: {
      defaultWorkspace?: string;
    };
  };
  layers: Array<LayerConfig>;
}

// 图层配置
export interface LayerConfig {
  id: string;
  name: string;
  type: 'raster' | 'vector' | 'group';
  initialLoad?: boolean;
  protocol?: 'WMS' | 'WMTS' | 'WFS' | 'XYZ';
  workspace?: string;
  layerName?: string;
  url?: string;
  defaultStyle?: string;
  geometryType?: string;
  labelField?: string;
  showLabels?: boolean;
  theme?: string;
  labelStyle?: LabelStyle;
  styleRules?: Array<StyleRule>;
  opacity?: number;
  visible?: boolean;
  zIndex?: number;
}

// 样式配置
export interface StyleConfig {
  styles: {
    [key: string]: any;
  };
}

// 标签样式
export interface LabelStyle {
  color?: string;
  fontSize?: number;
  fontWeight?: string;
  offsetX?: number;
  offsetY?: number;
  stroke?: boolean;
  strokeColor?: string;
  strokeWidth?: number;
}

// 样式规则
export interface StyleRule {
  filter: {
    property: string;
    operator: '=' | '!=' | '>' | '<' | '>=' | '<=';
    value: string | number;
  };
  style: {
    color?: string;
    weight?: number;
    opacity?: number;
    fillColor?: string;
    fillOpacity?: number;
  };
}

// 图层组类型
export interface LayerGroup {
  name: string;
  layers: Array<EnhancedLayerConfig>;
}

// 增强型图层配置（包含UI状态）
export interface EnhancedLayerConfig extends LayerConfig {
  visible: boolean;
  opacity: number;
  showOpacityControl: boolean;
  showStyleControl: boolean;
  style: any;
}

// 地图定位配置
export interface ViewConfig {
  center: [number, number];
  zoom: number;
  duration?: number;
} 