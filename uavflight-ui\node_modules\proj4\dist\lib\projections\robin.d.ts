export function init(): void;
export class init {
    x0: number;
    y0: number;
    long0: number;
    es: number;
    title: string;
}
export function forward(ll: any): {
    x: number;
    y: any;
};
export function inverse(xy: any): {
    x: number;
    y: number;
};
export const names: string[];
declare namespace _default {
    export { init };
    export { forward };
    export { inverse };
    export { names };
}
export default _default;
