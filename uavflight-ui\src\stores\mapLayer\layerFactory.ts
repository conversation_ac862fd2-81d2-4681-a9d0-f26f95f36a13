/*
 * @Description: 地图图层创建工厂
 */
import * as Cesium from 'cesium';
import { MapLayer } from './types';
import { evaluateFilter, applyStyle, getStyle } from './styleUtils';
import { UrlParser } from '/@/utils/urlParser';
import { CesiumMVTImageryProvider } from 'cesium-mvt-imagery-provider';
import CesiumPointManager from '/@/utils/map/CesiumPointManager';
import { getMapServerIp } from '/@/utils/mapUrlReplacer';

/**
 * 处理图层URL，确保使用正确的IP地址
 * @param url 原始URL
 * @returns 处理后的URL
 */
function processLayerUrl(url: string): string {
	if (!url) return url;
	
	// 获取配置的地图服务IP
	const mapServerIp = getMapServerIp();
	
	// 替换localhost和127.0.0.1为配置的IP
	return url.replace(/(https?:\/\/)(localhost|127\.0\.0\.1)([:/])/g, `$1${mapServerIp}$3`);
}

/**
 * 创建栅格图层 - WMS
 * @param layerConfig 图层配置
 * @param viewer Cesium.Viewer实例
 * @returns 创建的图层实例
 */
export function createWmsLayer(layerConfig: MapLayer, viewer: Cesium.Viewer): any {
	console.log(`创建WMS图层: ${layerConfig.name}`);

	// 处理URL中的IP地址
	const processedUrl = processLayerUrl(layerConfig.url);
	
	// 解析OpenLayers3格式的WMS URL
	const wmsInfo = UrlParser.parseOLWmsUrl(processedUrl);
	console.log(wmsInfo);
	
	// 在Cesium中添加WMS图层
	const wmsLayer = new Cesium.WebMapServiceImageryProvider({
		url: wmsInfo.baseUrl,
		layers: wmsInfo.parameters.layers,
		parameters: {
			format: 'image/png',
			transparent: true,
			service: 'WMS',
		},
	});

	// 添加到Cesium的图层集合中
	return viewer.imageryLayers.addImageryProvider(wmsLayer);
}

/**
 * 创建栅格图层 - WMTS
 * @param layerConfig 图层配置
 * @param viewer Cesium.Viewer实例
 * @returns 创建的图层实例
 */
export function createWmtsLayer(layerConfig: MapLayer, viewer: Cesium.Viewer): any {
	console.log(`创建WMTS图层: ${layerConfig.name}`);

	// 处理URL中的IP地址
	const processedUrl = processLayerUrl(layerConfig.url);
	
	// 解析WMTS URL
	const wmtsInfo = UrlParser.parseWMTSUrl(processedUrl);
	console.log('WMTS解析结果:', wmtsInfo);

	// 创建适当的TilingScheme
	let tilingScheme: Cesium.TilingScheme;

	if (wmtsInfo.tileMatrixSetID === 'EPSG:4326') {
		tilingScheme = new Cesium.GeographicTilingScheme();
	} else {
		// 默认使用WebMercator (EPSG:3857)
		tilingScheme = new Cesium.WebMercatorTilingScheme();
	}

	// 在Cesium中添加WMTS图层
	const wmtsLayer = new Cesium.WebMapTileServiceImageryProvider({
		url: wmtsInfo.serviceUrl,
		layer: wmtsInfo.layer,
		style: wmtsInfo.style,
		format: wmtsInfo.format,
		tileMatrixSetID: wmtsInfo.tileMatrixSetID,
		tilingScheme: tilingScheme,
	});
	
	console.log('WMTS图层添加到Cesium的图层集合中');
	// 添加到Cesium的图层集合中
	return viewer.imageryLayers.addImageryProvider(wmtsLayer);
}

/**
 * 创建栅格图层 - XYZ
 * @param layerConfig 图层配置
 * @param viewer Cesium.Viewer实例
 * @returns 创建的图层实例
 */
export function createXyzLayer(layerConfig: MapLayer, viewer: Cesium.Viewer): any {
	console.log(`创建XYZ图层: ${layerConfig.name}`);

	// 处理URL中的IP地址
	const processedUrl = processLayerUrl(layerConfig.url);

	// 在Cesium中添加XYZ图层
	const xyzLayer = new Cesium.UrlTemplateImageryProvider({
		url: processedUrl,
	});

	return viewer.imageryLayers.addImageryProvider(xyzLayer);
}

/**
 * 创建矢量图层 - GeoJSON
 * @param layerConfig 图层配置
 * @param viewer Cesium.Viewer实例
 * @returns 创建的图层实例Promise
 */
export function createGeoJsonLayer(layerConfig: MapLayer, viewer: Cesium.Viewer): Promise<Cesium.GeoJsonDataSource> {
	console.log(`创建GeoJSON图层: ${layerConfig.name}, 几何类型: ${layerConfig.geometryType}`);

	// 处理URL中的IP地址
	const processedUrl = processLayerUrl(layerConfig.url);

	// 异步处理样式和加载GeoJSON
	return (async () => {
		// 默认样式对象
		const defaultStyleObj = {
			point: {
				color: '#FF0000',
				pixelSize: 10,
				outlineColor: '#FFFFFF',
				outlineWidth: 2,
				heightReference: Cesium.HeightReference.CLAMP_TO_GROUND // 默认点贴地
			},
			polyline: {
				material: '#FF0000',
				width: 3,
				clampToGround: layerConfig.clampToGround !== undefined ? layerConfig.clampToGround : true // 默认线贴地
			},
			polygon: {
				material: '#FF000088',
				outline: true,
				outlineColor: '#FF0000',
				outlineWidth: 2,
				clampToGround: layerConfig.clampToGround !== undefined ? layerConfig.clampToGround : true // 默认面贴地
			}
		};

		// 获取默认样式
		let defaultStyle: any = defaultStyleObj;
		
		if (layerConfig.defaultStyle) {
			// 如果defaultStyle是字符串，从样式配置中加载
			if (typeof layerConfig.defaultStyle === 'string') {
				const loadedStyle = await getStyle(layerConfig.defaultStyle);
				if (loadedStyle) {
					defaultStyle = loadedStyle;
					console.log(`从样式配置中加载默认样式: ${layerConfig.defaultStyle}`);
				}
			} else {
				// 直接使用配置中的样式对象
				defaultStyle = layerConfig.defaultStyle;
			}
		}

		// 创建加载选项，设置贴地相关选项
		const geoJsonOptions: any = {
			stroke: Cesium.Color.fromCssColorString(
				defaultStyle.polyline?.material && typeof defaultStyle.polyline.material === 'string' 
					? defaultStyle.polyline.material 
					: defaultStyle.polygon?.outlineColor || '#FF0000'
			),
			strokeWidth: defaultStyle.polyline?.width || 3,
			fill: Cesium.Color.fromCssColorString(
				defaultStyle.polygon?.material && typeof defaultStyle.polygon.material === 'string' 
					? defaultStyle.polygon.material 
					: '#FF000088'
			),
			clampToGround: layerConfig.clampToGround !== undefined ? layerConfig.clampToGround : true, // 默认贴地
		};

		// 在Cesium中加载GeoJSON数据
		const dataSource = await Cesium.GeoJsonDataSource.load(processedUrl, geoJsonOptions);
		const entities = dataSource.entities.values;
		
		// 判断是否有样式规则
		if (layerConfig.styleRules && layerConfig.styleRules.length > 0) {
			console.log(`图层 ${layerConfig.name} 有 ${layerConfig.styleRules.length} 条样式规则，应用规则样式`);
			
			// 有样式规则，只应用规则样式
			for (const entity of entities) {
				// 获取实体的属性
				const properties = entity.properties;
				if (!properties) {
					console.warn('实体没有properties属性');
					continue;
				}
				
				let matchedRule = false;
				// 检查每个样式规则
				for (const rule of layerConfig.styleRules) {
					// 应用过滤器
					const isMatch = evaluateFilter(properties, rule.filter);
					
					if (isMatch) {
						// 如果匹配，应用样式
						console.log(`规则匹配，应用样式`);
						
						// 如果样式是字符串，从样式配置中加载
						if (typeof rule.style === 'string') {
							const styleFromConfig = await getStyle(rule.style);
							if (styleFromConfig) {
								applyStyle(entity, styleFromConfig, layerConfig.geometryType);
								console.log(`应用从配置加载的样式: ${rule.style}`);
							}
						} else {
							// 直接应用样式对象
							applyStyle(entity, rule.style, layerConfig.geometryType);
							console.log(`应用直接定义的样式`);
						}
						
						matchedRule = true;
						break; // 应用第一个匹配的规则后停止
					}
				}
				
				// 如果没有匹配的规则，才应用默认样式
				if (!matchedRule) {
					console.log(`没有匹配的规则，应用默认样式`);
					applyStyle(entity, defaultStyle, layerConfig.geometryType);
				}
				
				// 确保贴地选项生效
				applyClampToGround(entity, layerConfig);
			}
		} else {
			console.log(`图层 ${layerConfig.name} 没有样式规则，应用默认样式`);
			// 没有样式规则，应用默认样式到所有实体
			for (const entity of entities) {
				applyStyle(entity, defaultStyle, layerConfig.geometryType);
				// 确保贴地选项生效
				applyClampToGround(entity, layerConfig);
			}
		}

		// 添加数据源到Cesium
		viewer.dataSources.add(dataSource);
		
		return dataSource;
	})();
}

/**
 * 确保实体的贴地选项生效
 * @param entity Cesium实体
 * @param layerConfig 图层配置
 */
function applyClampToGround(entity: Cesium.Entity, layerConfig: MapLayer): void {
	const clampToGround = layerConfig.clampToGround !== undefined ? layerConfig.clampToGround : true;
	
	// 对点应用高度参考
	if (entity.point) {
		entity.point.heightReference = new Cesium.ConstantProperty(
			clampToGround ? Cesium.HeightReference.CLAMP_TO_GROUND : Cesium.HeightReference.NONE
		);
	}
	
	// 对线应用贴地属性
	if (entity.polyline) {
		entity.polyline.clampToGround = new Cesium.ConstantProperty(clampToGround);
	}
	
	// 对面应用贴地属性
	if (entity.polygon) {
		// Cesium的polygon没有直接的clampToGround属性，
		// 但可以通过设置perPositionHeight来达到类似效果
		if (clampToGround) {
			entity.polygon.perPositionHeight = new Cesium.ConstantProperty(false);
			if (typeof entity.polygon.height !== 'undefined') {
				entity.polygon.height = undefined;
			}
		}
	}
	
	// 对广告牌应用高度参考
	if (entity.billboard) {
		entity.billboard.heightReference = new Cesium.ConstantProperty(
			clampToGround ? Cesium.HeightReference.CLAMP_TO_GROUND : Cesium.HeightReference.NONE
		);
	}
	
	// 对标签应用高度参考
	if (entity.label) {
		entity.label.heightReference = new Cesium.ConstantProperty(
			clampToGround ? Cesium.HeightReference.CLAMP_TO_GROUND : Cesium.HeightReference.NONE
		);
	}
}

/**
 * 创建矢量图层 - WFS
 * @param layerConfig 图层配置
 * @param viewer Cesium.Viewer实例
 * @returns 创建的图层实例Promise
 */
export function createWfsLayer(layerConfig: MapLayer, viewer: Cesium.Viewer): Promise<Cesium.GeoJsonDataSource> {
	// 处理WFS图层
	console.log(`创建WFS图层: ${layerConfig.name}, 几何类型: ${layerConfig.geometryType}`);

	// 处理URL中的IP地址
	const processedUrl = processLayerUrl(layerConfig.url);

	return (async () => {
		// 构建WFS请求URL
		const wfsParams = layerConfig.wfsParams || {};
		const defaultParams = {
			service: 'WFS',
			version: '1.1.0',
			request: 'GetFeature',
			outputFormat: 'application/json',
			...wfsParams
		};

		let wfsUrl = processedUrl;
		if (!wfsUrl.includes('?')) {
			wfsUrl += '?';
		} else if (!wfsUrl.endsWith('&') && !wfsUrl.endsWith('?')) {
			wfsUrl += '&';
		}

		// 添加WFS参数
		for (const key in defaultParams) {
			wfsUrl += `${key}=${defaultParams[key]}&`;
		}
		wfsUrl = wfsUrl.substring(0, wfsUrl.length - 1); // 移除最后一个 &

		console.log(`构建的WFS请求URL: ${wfsUrl}`);

		// 默认样式对象
		const defaultStyleObj = {
			point: {
				color: '#FF0000',
				pixelSize: 10,
				outlineColor: '#FFFFFF',
				outlineWidth: 2,
				heightReference: Cesium.HeightReference.CLAMP_TO_GROUND // 默认点贴地
			},
			polyline: {
				material: '#FF0000',
				width: 3,
				clampToGround: layerConfig.clampToGround !== undefined ? layerConfig.clampToGround : true // 默认线贴地
			},
			polygon: {
				material: '#FF000088',
				outline: true,
				outlineColor: '#FF0000',
				outlineWidth: 2,
				clampToGround: layerConfig.clampToGround !== undefined ? layerConfig.clampToGround : true // 默认面贴地
			}
		};

		// 获取默认样式
		let defaultStyle: any = defaultStyleObj;
		
		if (layerConfig.defaultStyle) {
			// 如果defaultStyle是字符串，从样式配置中加载
			if (typeof layerConfig.defaultStyle === 'string') {
				const loadedStyle = await getStyle(layerConfig.defaultStyle);
				if (loadedStyle) {
					defaultStyle = loadedStyle;
					console.log(`从样式配置中加载默认样式: ${layerConfig.defaultStyle}`);
				}
			} else {
				// 直接使用配置中的样式对象
				defaultStyle = layerConfig.defaultStyle;
			}
		}

		// 创建加载选项，设置贴地相关选项
		const wfsOptions: any = {
			stroke: Cesium.Color.fromCssColorString(
				defaultStyle.polyline?.material && typeof defaultStyle.polyline.material === 'string' 
					? defaultStyle.polyline.material 
					: defaultStyle.polygon?.outlineColor || '#FF0000'
			),
			strokeWidth: defaultStyle.polyline?.width || 3,
			fill: Cesium.Color.fromCssColorString(
				defaultStyle.polygon?.material && typeof defaultStyle.polygon.material === 'string' 
					? defaultStyle.polygon.material 
					: '#FF000088'
			),
			clampToGround: layerConfig.clampToGround !== undefined ? layerConfig.clampToGround : true, // 默认贴地
		};

		// 在Cesium中加载WFS数据
		const dataSource = await Cesium.GeoJsonDataSource.load(wfsUrl, wfsOptions);
		const entities = dataSource.entities.values;
		
		// 判断是否有样式规则
		if (layerConfig.styleRules && layerConfig.styleRules.length > 0) {
			console.log(`图层 ${layerConfig.name} 有 ${layerConfig.styleRules.length} 条样式规则，应用规则样式`);
			
			// 有样式规则，只应用规则样式
			for (const entity of entities) {
				// 获取实体的属性
				const properties = entity.properties;
				if (!properties) {
					console.warn('实体没有properties属性');
					continue;
				}

				let matchedRule = false;
				// 检查每个样式规则
				for (const rule of layerConfig.styleRules) {
					// 应用过滤器
					const isMatch = evaluateFilter(properties, rule.filter);
					
					if (isMatch) {
						// 如果匹配，应用样式
						console.log(`规则匹配，应用样式`);
						
						// 如果样式是字符串，从样式配置中加载
						if (typeof rule.style === 'string') {
							const styleFromConfig = await getStyle(rule.style);
							if (styleFromConfig) {
								applyStyle(entity, styleFromConfig, layerConfig.geometryType);
								console.log(`应用从配置加载的样式: ${rule.style}`);
							}
						} else {
							// 直接应用样式对象
							applyStyle(entity, rule.style, layerConfig.geometryType);
							console.log(`应用直接定义的样式`);
						}
						
						matchedRule = true;
						break; // 应用第一个匹配的规则后停止
					}
				}
				
				// 如果没有匹配的规则，才应用默认样式
				if (!matchedRule) {
					console.log(`没有匹配的规则，应用默认样式`);
					applyStyle(entity, defaultStyle, layerConfig.geometryType);
				}
				
				// 确保贴地选项生效
				applyClampToGround(entity, layerConfig);
			}
		} else {
			console.log(`图层 ${layerConfig.name} 没有样式规则，应用默认样式`);
			// 没有样式规则，应用默认样式到所有实体
			for (const entity of entities) {
				applyStyle(entity, defaultStyle, layerConfig.geometryType);
				// 确保贴地选项生效
				applyClampToGround(entity, layerConfig);
			}
		}

		// 添加数据源到Cesium
		viewer.dataSources.add(dataSource);
		
		return dataSource;
	})();
}

/**
 * 创建矢量图层 - MVT
 * @param layerConfig 图层配置
 * @param viewer Cesium.Viewer实例
 * @returns 创建的图层实例
 */
export function createMvtLayer(layerConfig: MapLayer, viewer: Cesium.Viewer): any {
	console.log(`创建MVT图层: ${layerConfig.name}`);

	// 处理URL中的IP地址
	const processedUrl = processLayerUrl(layerConfig.url);

	// 获取是否贴地
	const clampToGround = layerConfig.clampToGround !== undefined ? layerConfig.clampToGround : true;

	try {
		// 创建默认样式函数
		const styleFunction = (feature: any) => {
			// 默认样式
			const defaultStyle = {
				strokeStyle: '#FF0000',  // 线框颜色
				lineWidth: 2,         // 线宽
				fillStyle: '#FF000088', // 填充颜色 (带透明度)
				lineJoin: 'round',
				lineCap: 'round'
				// CesiumMVTImageryProvider不直接支持clampToGround
			};

			let styleToApply = { ...defaultStyle };

			// 如果有样式规则，则尝试应用
			if (layerConfig.styleRules && layerConfig.styleRules.length > 0) {
				// 获取特性属性
				const properties = feature.properties;
				if (!properties) return styleToApply;

				// 检查每个样式规则
				for (const rule of layerConfig.styleRules) {
					// 创建一个模拟的Cesium Properties对象，以便可以使用evaluateFilter函数
					const mockProperties = {
						getValue: () => {},
						hasProperty: (name: string) => properties.hasOwnProperty(name),
						propertyNames: Object.keys(properties),
						_propertyNames: Object.keys(properties),
						...Object.keys(properties).reduce((acc: any, key) => {
							acc[key] = {
								getValue: () => properties[key]
							};
							return acc;
						}, {})
					};

					// 应用过滤器
					const isMatch = evaluateFilter(mockProperties, rule.filter);
					if (isMatch) {
						// 如果匹配，应用样式
						if (typeof rule.style === 'object') {
							if (rule.style.polyline) {
								if (rule.style.polyline.material) {
									// 如果是字符串颜色
									if (typeof rule.style.polyline.material === 'string') {
										styleToApply.strokeStyle = rule.style.polyline.material;
									} 
									// 如果是材质对象
									else if (rule.style.polyline.material.color) {
										styleToApply.strokeStyle = rule.style.polyline.material.color;
									}
								}
								if (rule.style.polyline.width !== undefined) {
									styleToApply.lineWidth = rule.style.polyline.width;
								}
							}
							if (rule.style.polygon) {
								if (rule.style.polygon.material) {
									// 如果是字符串颜色
									if (typeof rule.style.polygon.material === 'string') {
										styleToApply.fillStyle = rule.style.polygon.material;
									} 
									// 如果是材质对象
									else if (rule.style.polygon.material.color) {
										styleToApply.fillStyle = rule.style.polygon.material.color;
									}
								}
								if (rule.style.polygon.outline !== undefined && rule.style.polygon.outline === false) {
									styleToApply.lineWidth = 0;
								}
							}
						}
						break; // 应用第一个匹配的规则后停止
					}
				}
			} 
			// 如果有自定义样式函数，使用它
			else if (layerConfig.mvtStyle) {
				const customStyle = layerConfig.mvtStyle(feature, null);
				if (customStyle) {
					if (customStyle.color !== undefined) styleToApply.strokeStyle = customStyle.color;
					if (customStyle.weight !== undefined) styleToApply.lineWidth = customStyle.weight;
					if (customStyle.fillColor !== undefined) styleToApply.fillStyle = customStyle.fillColor;
				}
			}

			return styleToApply;
		};

		// 确保URL模板符合CesiumMVTImageryProvider的要求
		let urlTemplate = processedUrl;
		if (!urlTemplate.includes('{z}') || !urlTemplate.includes('{x}') || !urlTemplate.includes('{y}')) {
			// 如果URL不包含瓦片坐标模板，添加它们
			urlTemplate = urlTemplate.replace(/\/$/, '') + '/{z}/{x}/{y}.mvt';
		}
		
		// 使用类型断言绕过类型检查
		const mvtOptions: any = {
			urlTemplate: urlTemplate,
			subdomains: ['a', 'b', 'c'], // 可以根据需要调整
			style: styleFunction,
			maximumLevel: 19, // 最大缩放级别
			minimumLevel: 0, // 最小缩放级别
			credit: layerConfig.name,
		};
		
		// 添加可选的API密钥
		if (layerConfig.parameters?.key) {
			mvtOptions.key = layerConfig.parameters.key;
		}
		
		// 创建MVT图层
		console.log('创建MVT图层，URL模板:', urlTemplate);
		const mvtProvider = new CesiumMVTImageryProvider(mvtOptions);

		// 添加到Cesium的图层集合中
		const layer = viewer.imageryLayers.addImageryProvider(mvtProvider);
		
		return layer;
	} catch (error) {
		console.error('创建MVT图层时出错:', error);
		return null;
	}
}

/**
 * 创建自定义点数据图层 - CustomPointJSON
 * @param layerConfig 图层配置
 * @param viewer Cesium.Viewer实例
 * @returns 创建的图层管理器实例
 */
export function createCustomPointJsonLayer(layerConfig: MapLayer, viewer: Cesium.Viewer): CesiumPointManager {
	console.log(`创建自定义点数据图层: ${layerConfig.name}`);
	
	// 处理URL中的IP地址
	const processedUrl = processLayerUrl(layerConfig.url);
	
	// 获取是否贴地选项
	const clampToGround = layerConfig.clampToGround !== undefined ? layerConfig.clampToGround : true;
	
	// 创建点管理器选项
	const pointOptions = {
		clampToGround: clampToGround,
		heightReference: clampToGround ? Cesium.HeightReference.CLAMP_TO_GROUND : Cesium.HeightReference.NONE,
		modelScale: 1.5, // 默认模型缩放
		billboardScale: 1.2 // 默认图片缩放
	};
	
	// 如果有自定义样式，应用样式参数
	if (layerConfig.defaultStyle) {
		const style = layerConfig.defaultStyle;
		if (typeof style === 'object') {
			// 从样式中提取billboard相关属性
			if (style.billboard) {
				if (style.billboard.scale !== undefined) {
					pointOptions.billboardScale = style.billboard.scale;
				}
				if (style.billboard.heightReference !== undefined) {
					pointOptions.heightReference = style.billboard.heightReference;
				}
			}
			
			// 判断是否覆盖贴地选项
			if (style.billboard?.heightReference !== undefined || 
				style.point?.heightReference !== undefined) {
				pointOptions.clampToGround = 
					(style.billboard?.heightReference === Cesium.HeightReference.CLAMP_TO_GROUND ||
					style.point?.heightReference === Cesium.HeightReference.CLAMP_TO_GROUND);
			}
		}
	}
	
	// 使用CesiumPointManager加载和管理点数据，传入配置选项
	const pointManager = new CesiumPointManager(viewer, pointOptions);
	
	// 从配置中获取要加载的类别
	const categories = layerConfig.categories || [];
	
	if (categories.length > 0) {
		console.log(`加载指定类别的点数据: ${categories.join(', ')}`);
		
		// 加载点数据
		pointManager.loadFromJsonByCategories(processedUrl, categories)
			.then(() => {
				console.log(`成功加载 ${layerConfig.name} 图层的点数据`);
			})
			.catch(error => {
				console.error(`加载 ${layerConfig.name} 图层的点数据时发生错误:`, error);
			});
			
		// 添加点击事件处理
		pointManager.addClickEventHandler((properties) => {
			console.log('点击了点:', properties);
			// 可以在这里添加点击后的自定义处理逻辑
		});
	} else {
		console.warn(`图层 ${layerConfig.name} 未指定要加载的点数据类别`);
	}
	
	return pointManager;
}

/**
 * 创建DEM地形图层
 * @param layerConfig 图层配置
 * @param viewer Cesium.Viewer实例
 * @returns 创建的地形提供者实例
 */
export function createDemLayer(layerConfig: MapLayer, viewer: Cesium.Viewer): Promise<any> {
	console.log(`创建DEM地形图层: ${layerConfig.name}`);
	
	// 处理URL中的IP地址
	const processedUrl = processLayerUrl(layerConfig.url);
	
	return new Promise((resolve, reject) => {
		try {
			// 解析DEM URL
			const parameters = layerConfig.parameters || {};
			
			// 设置地形提供者
			const terrainProvider = new Cesium.CesiumTerrainProvider({
				url: processedUrl,
				requestVertexNormals: parameters.requestVertexNormals === true,
				requestWaterMask: parameters.waterMask === true,
			});
			
			// 在加载完成后设置地形
			terrainProvider.readyPromise.then(() => {
				console.log(`DEM地形图层已准备就绪`);
				
				// 应用高度因子（如果指定）
				if (parameters.heightFactor && parameters.heightFactor !== 1.0) {
					const heightFactor = parameters.heightFactor;
					console.log(`应用高度因子: ${heightFactor}`);
					
					// 由于sampleTerrain是只读的，我们不能直接替换它
					// 但我们可以在使用时应用高度因子
					// 这里仅记录日志，实际应用需要在具体使用地形高度的地方进行调整
					console.log(`需要在应用中手动应用高度因子: ${heightFactor}`);
				}
				
				// 设置Cesium地形
				viewer.terrainProvider = terrainProvider;
				
				// 启用深度测试，提高3D效果
				viewer.scene.globe.depthTestAgainstTerrain = true;
				
				// 启用地形照明
				if (parameters.enableLighting !== false) {
					viewer.scene.globe.enableLighting = true;
				}
				
				resolve(terrainProvider);
			}).catch((error) => {
				console.error(`加载DEM地形出错:`, error);
				reject(error);
			});
		} catch (error) {
			console.error(`创建DEM地形图层出错:`, error);
			reject(error);
		}
	});
} 