<!--
 * @Author: AI Assistant
 * @Date: 2025-01-08
 * @Description: SHP文件加载器面板
 *
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved.
-->
<template>
  <div class="shp-loader-panel">
    <div class="panel-header">
      <h3>SHP文件加载器</h3>
      <p class="panel-description">加载本地SHP文件到地图中进行查看和属性查询</p>
    </div>

    <div class="panel-content">
      <!-- 文件上传区域 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          class="shp-upload"
          drag
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleFileChange"
          accept=".shp,.dbf,.shx,.prj,.cpg"
          multiple
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将SHP文件拖拽到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              请选择完整的SHP文件集合（.shp, .dbf, .shx等）
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 文件列表 -->
      <div v-if="uploadedFiles.length > 0" class="file-list-section">
        <h4>已选择文件：</h4>
        <div class="file-list">
          <div v-for="file in uploadedFiles" :key="file.name" class="file-item">
            <el-icon><Document /></el-icon>
            <span class="file-name">{{ file.name }}</span>
            <span class="file-size">{{ formatFileSize(file.size) }}</span>
            <el-button
              type="danger"
              size="small"
              circle
              :icon="Delete"
              @click="removeFile(file)"
            />
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!hasValidShpFiles"
          @click="loadShpToMap"
        >
          <el-icon><MapLocation /></el-icon>
          加载到地图
        </el-button>
        <el-button
          type="info"
          @click="debugFileStatus"
        >
          调试信息
        </el-button>
        <el-button
          v-if="loadedLayers.length > 0"
          type="warning"
          @click="clearAllLayers"
        >
          <el-icon><Delete /></el-icon>
          清除所有图层
        </el-button>
      </div>

      <!-- 已加载图层列表 -->
      <div v-if="loadedLayers.length > 0" class="loaded-layers-section">
        <h4>已加载图层：</h4>
        <div class="layer-list">
          <div v-for="layer in loadedLayers" :key="layer.id" class="layer-item">
            <div class="layer-info">
              <el-icon><Files /></el-icon>
              <span class="layer-name">{{ layer.name }}</span>
              <el-tag size="small" type="info">{{ layer.featureCount }} 个要素</el-tag>
              <el-tag
                v-if="layer.originalCRS && layer.originalCRS !== 'EPSG:4326'"
                size="small"
                type="warning"
              >
                {{ layer.originalCRS }} → WGS84
              </el-tag>
              <el-tag
                v-else-if="layer.originalCRS === 'EPSG:4326'"
                size="small"
                type="success"
              >
                WGS84
              </el-tag>
            </div>
            <div class="layer-actions">
              <el-button
                size="small"
                type="primary"
                @click="zoomToLayer(layer)"
              >
                定位
              </el-button>
              <el-button
                size="small"
                type="info"
                @click="toggleLayerVisibility(layer)"
              >
                {{ layer.visible ? '隐藏' : '显示' }}
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="removeLayer(layer)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-status">
        <el-progress :percentage="loadingProgress" :status="loadingStatus" />
        <p class="loading-text">{{ loadingText }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  UploadFilled,
  Document,
  Delete,
  MapLocation,
  Files
} from '@element-plus/icons-vue';
import * as shapefile from 'shapefile';
import { Vector as VectorLayer } from 'ol/layer';
import { Vector as VectorSource } from 'ol/source';
import { GeoJSON } from 'ol/format';
import { Style, Stroke, Fill, Circle } from 'ol/style';
import { transform } from 'ol/proj';
import { register } from 'ol/proj/proj4';
// @ts-ignore
import proj4 from 'proj4';
import commonFunction from '/@/utils/commonFunction';

// 注入地图实例
const map = inject('map') as any;

// 获取通用函数
const { getRandomColor } = commonFunction();

// 常用坐标系定义
const commonProjections = {
  // 中国常用坐标系
  'EPSG:4490': '+proj=longlat +ellps=GRS80 +no_defs +type=crs', // CGCS2000
  'EPSG:4214': '+proj=longlat +ellps=krass +no_defs +type=crs', // Beijing 1954
  'EPSG:4610': '+proj=longlat +ellps=krass +no_defs +type=crs', // Xian 1980
  'EPSG:3857': '+proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +wktext +no_defs +type=crs', // Web Mercator

  // UTM 投影 (中国区域) - WGS84 基准
  'EPSG:32648': '+proj=utm +zone=48 +ellps=WGS84 +datum=WGS84 +units=m +no_defs +type=crs', // UTM Zone 48N
  'EPSG:32649': '+proj=utm +zone=49 +ellps=WGS84 +datum=WGS84 +units=m +no_defs +type=crs', // UTM Zone 49N
  'EPSG:32650': '+proj=utm +zone=50 +ellps=WGS84 +datum=WGS84 +units=m +no_defs +type=crs', // UTM Zone 50N
  'EPSG:32651': '+proj=utm +zone=51 +ellps=WGS84 +datum=WGS84 +units=m +no_defs +type=crs', // UTM Zone 51N

  // 高斯-克吕格投影
  'EPSG:2433': '+proj=tmerc +lat_0=0 +lon_0=75 +k=1 +x_0=500000 +y_0=0 +ellps=krass +units=m +no_defs +type=crs', // Pulkovo 1942 / Gauss-Kruger zone 13
  'EPSG:2434': '+proj=tmerc +lat_0=0 +lon_0=81 +k=1 +x_0=500000 +y_0=0 +ellps=krass +units=m +no_defs +type=crs', // Pulkovo 1942 / Gauss-Kruger zone 14
  'EPSG:2435': '+proj=tmerc +lat_0=0 +lon_0=87 +k=1 +x_0=500000 +y_0=0 +ellps=krass +units=m +no_defs +type=crs', // Pulkovo 1942 / Gauss-Kruger zone 15

  // 其他常用投影
  'EPSG:4547': '+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs +type=crs', // CGCS2000 / 3-degree Gauss-Kruger zone 40
  'EPSG:4548': '+proj=tmerc +lat_0=0 +lon_0=123 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs +type=crs', // CGCS2000 / 3-degree Gauss-Kruger zone 41
};



// 响应式数据
const uploadRef = ref();
const uploadedFiles = ref<File[]>([]);
const loadedLayers = ref<any[]>([]);
const loading = ref(false);
const loadingProgress = ref(0);
const loadingStatus = ref<'success' | 'exception' | undefined>(undefined);
const loadingText = ref('');

// 计算属性
const hasValidShpFiles = computed(() => {
  const hasShp = uploadedFiles.value.some(file => file.name.toLowerCase().endsWith('.shp'));
  console.log('🔍 文件验证:', {
    上传文件数量: uploadedFiles.value.length,
    文件列表: uploadedFiles.value.map(f => f.name),
    是否包含SHP: hasShp,
    验证结果: hasShp ? '通过' : '失败'
  });
  return hasShp;
});

// 文件处理
const handleFileChange = (file: any) => {
  const newFile = file.raw;

  console.log('📁 接收到文件:', {
    文件名: newFile.name,
    文件大小: newFile.size,
    文件类型: newFile.type,
    最后修改: new Date(newFile.lastModified).toLocaleString(),
    文件对象: newFile
  });

  // 检查文件扩展名
  const fileName = newFile.name.toLowerCase();
  const validExtensions = ['.shp', '.dbf', '.shx', '.prj', '.cpg'];
  const isValidExtension = validExtensions.some(ext => fileName.endsWith(ext));

  if (!isValidExtension) {
    console.warn('⚠️ 不支持的文件类型:', newFile.name);
    ElMessage.warning(`不支持的文件类型: ${newFile.name}`);
    return;
  }

  // 检查文件是否已存在
  const exists = uploadedFiles.value.some(f => f.name === newFile.name);
  if (exists) {
    console.warn('⚠️ 文件已存在:', newFile.name);
    ElMessage.warning(`文件 ${newFile.name} 已存在`);
    return;
  }

  uploadedFiles.value.push(newFile);
  console.log('✅ 文件添加成功:', {
    文件名: newFile.name,
    文件大小: `${(newFile.size / 1024).toFixed(2)} KB`,
    当前文件总数: uploadedFiles.value.length,
    所有文件: uploadedFiles.value.map(f => f.name)
  });

  // 检查是否有SHP文件
  const hasShp = uploadedFiles.value.some(f => f.name.toLowerCase().endsWith('.shp'));
  console.log('🔍 SHP文件检查:', {
    是否包含SHP文件: hasShp,
    按钮状态: hasShp ? '可用' : '禁用'
  });
};

const removeFile = (fileToRemove: File) => {
  const index = uploadedFiles.value.findIndex(f => f.name === fileToRemove.name);
  if (index > -1) {
    uploadedFiles.value.splice(index, 1);
    console.log('移除文件:', fileToRemove.name);
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取实际的地图对象
const getMapInstance = () => {
  // map 是通过 provide/inject 传递的响应式对象
  const mapInstance = map?.value || map;

  console.log('🔍 获取地图实例:', {
    原始map对象: map,
    map类型: typeof map,
    mapValue: map?.value,
    mapValue类型: typeof map?.value,
    最终实例: mapInstance,
    最终实例类型: typeof mapInstance,
    是否有getView方法: mapInstance && typeof mapInstance.getView === 'function',
    是否有addLayer方法: mapInstance && typeof mapInstance.addLayer === 'function',
    实例的所有方法: mapInstance ? Object.getOwnPropertyNames(Object.getPrototypeOf(mapInstance)).slice(0, 10) : '无'
  });

  // 验证地图实例是否有效
  if (!mapInstance || typeof mapInstance.getView !== 'function') {
    console.error('❌ 无效的地图实例:', mapInstance);
    return null;
  }

  return mapInstance;
};

// 调试地图注入
console.log('🗺️ SHP加载器初始化:', {
  地图实例: map,
  地图类型: typeof map,
  是否为响应式: map?.value !== undefined,
  实际地图对象: map?.value || map,
  地图验证: getMapInstance() ? '有效' : '无效'
});

// 坐标系检测和转换功能
const detectCoordinateSystem = (geojson: any): string => {
  if (!geojson.features || geojson.features.length === 0) {
    return 'EPSG:4326'; // 默认返回 WGS84
  }

  // 获取第一个要素的坐标
  const firstFeature = geojson.features[0];
  if (!firstFeature.geometry || !firstFeature.geometry.coordinates) {
    return 'EPSG:4326';
  }

  // 获取坐标范围
  let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;

  const extractCoords = (coords: any) => {
    if (typeof coords[0] === 'number') {
      // 单个坐标点
      minX = Math.min(minX, coords[0]);
      maxX = Math.max(maxX, coords[0]);
      minY = Math.min(minY, coords[1]);
      maxY = Math.max(maxY, coords[1]);
    } else {
      // 嵌套数组
      coords.forEach((coord: any) => extractCoords(coord));
    }
  };

  // 分析前几个要素的坐标范围
  geojson.features.slice(0, Math.min(10, geojson.features.length)).forEach((feature: any) => {
    if (feature.geometry && feature.geometry.coordinates) {
      extractCoords(feature.geometry.coordinates);
    }
  });

  console.log('📍 坐标范围分析:', {
    X范围: [minX, maxX],
    Y范围: [minY, maxY],
    X跨度: maxX - minX,
    Y跨度: maxY - minY
  });

  // 坐标系判断逻辑
  if (minX >= -180 && maxX <= 180 && minY >= -90 && maxY <= 90) {
    // 经纬度坐标系
    if (minX > 70 && maxX < 140 && minY > 10 && maxY < 60) {
      // 中国区域，可能是 CGCS2000 或 WGS84
      console.log('🌍 检测为中国区域经纬度坐标系，假设为 EPSG:4326 (WGS84)');
      return 'EPSG:4326';
    } else {
      console.log('🌍 检测为全球经纬度坐标系 EPSG:4326 (WGS84)');
      return 'EPSG:4326';
    }
  } else if (Math.abs(minX) > 1000000 || Math.abs(maxX) > 1000000) {
    // 投影坐标系 (米为单位)
    if (minX > 200000 && maxX < 800000 && minY > 2000000 && maxY < 6000000) {
      // 可能是中国的高斯-克吕格投影
      console.log('🗺️ 检测为高斯-克吕格投影坐标系，假设为 EPSG:4547');
      return 'EPSG:4547';
    } else if (minX > -20000000 && maxX < 20000000 && minY > -20000000 && maxY < 20000000) {
      // 可能是 Web Mercator
      console.log('🗺️ 检测为 Web Mercator 投影 EPSG:3857');
      return 'EPSG:3857';
    } else {
      // UTM 或其他投影
      console.log('🗺️ 检测为 UTM 或其他投影坐标系，假设为 EPSG:32649');
      return 'EPSG:32649';
    }
  } else {
    // 其他情况，默认为 WGS84
    console.log('❓ 无法确定坐标系，默认使用 EPSG:4326');
    return 'EPSG:4326';
  }
};

// PRJ 文件解析功能
const parsePRJFile = (prjText: string): string => {
  if (!prjText) {
    return 'EPSG:4326';
  }

  console.log('📄 解析 PRJ 文件内容:', prjText);

  // 常见的 WKT 到 EPSG 映射
  const wktToEPSG: { [key: string]: string } = {
    // UTM 投影
    'WGS_1984_UTM_Zone_48N': 'EPSG:32648',
    'WGS_1984_UTM_Zone_49N': 'EPSG:32649',
    'WGS_1984_UTM_Zone_50N': 'EPSG:32650',
    'WGS_1984_UTM_Zone_51N': 'EPSG:32651',

    // 地理坐标系
    'GCS_WGS_1984': 'EPSG:4326',
    'GCS_China_Geodetic_Coordinate_System_2000': 'EPSG:4490',
    'GCS_Beijing_1954': 'EPSG:4214',
    'GCS_Xian_1980': 'EPSG:4610',

    // Web Mercator
    'WGS_1984_Web_Mercator_Auxiliary_Sphere': 'EPSG:3857'
  };

  // 尝试匹配投影名称
  for (const [wktName, epsgCode] of Object.entries(wktToEPSG)) {
    if (prjText.includes(wktName)) {
      console.log(`✅ PRJ 文件匹配: ${wktName} → ${epsgCode}`);
      return epsgCode;
    }
  }

  // 检查是否包含 UTM Zone 信息
  const utmMatch = prjText.match(/UTM_Zone_(\d+)N/);
  if (utmMatch) {
    const zone = parseInt(utmMatch[1]);
    const epsgCode = `EPSG:${32600 + zone}`;
    console.log(`✅ UTM Zone 检测: Zone ${zone} → ${epsgCode}`);
    return epsgCode;
  }

  // 检查中央经线来判断 UTM 分带
  const centralMeridianMatch = prjText.match(/Central_Meridian[",\s]*([0-9.-]+)/);
  if (centralMeridianMatch) {
    const centralMeridian = parseFloat(centralMeridianMatch[1]);
    console.log('🌍 检测到中央经线:', centralMeridian);

    if (centralMeridian === 105.0) {
      console.log('✅ 中央经线 105° → UTM Zone 48N (EPSG:32648)');
      return 'EPSG:32648';
    } else if (centralMeridian === 111.0) {
      console.log('✅ 中央经线 111° → UTM Zone 49N (EPSG:32649)');
      return 'EPSG:32649';
    } else if (centralMeridian === 117.0) {
      console.log('✅ 中央经线 117° → UTM Zone 50N (EPSG:32650)');
      return 'EPSG:32650';
    } else if (centralMeridian === 123.0) {
      console.log('✅ 中央经线 123° → UTM Zone 51N (EPSG:32651)');
      return 'EPSG:32651';
    }
  }

  // 如果是地理坐标系（包含 GEOGCS 但不包含 PROJCS）
  if (prjText.includes('GEOGCS') && !prjText.includes('PROJCS')) {
    console.log('✅ 检测为地理坐标系 → EPSG:4326');
    return 'EPSG:4326';
  }

  // 默认返回 WGS84
  console.log('⚠️ 无法识别 PRJ 文件，默认使用 EPSG:4326');
  return 'EPSG:4326';
};

// 坐标转换功能
const transformGeoJSONToWGS84 = (geojson: any, sourceEPSG: string): any => {
  if (sourceEPSG === 'EPSG:4326') {
    console.log('✅ 数据已经是 WGS84 坐标系，无需转换');
    return geojson;
  }

  console.log(`🔄 开始坐标转换: ${sourceEPSG} → EPSG:4326`);

  try {
    // 注册坐标系（如果 proj4 可用）
    if (typeof proj4 !== 'undefined' && typeof register !== 'undefined') {
      // 注册源坐标系
      if ((commonProjections as any)[sourceEPSG]) {
        proj4.defs(sourceEPSG, (commonProjections as any)[sourceEPSG]);
        register(proj4);
        console.log(`📝 已注册坐标系: ${sourceEPSG}`);
      }
    }

    // 转换坐标的递归函数
    const transformCoordinates = (coords: any): any => {
      if (typeof coords[0] === 'number') {
        // 单个坐标点 [x, y] 或 [x, y, z]
        try {
          if (typeof transform !== 'undefined') {
            const originalCoord = [coords[0], coords[1]];
            const transformed = transform(originalCoord, sourceEPSG, 'EPSG:4326');

            // 验证转换结果是否合理
            if (transformed[0] >= -180 && transformed[0] <= 180 &&
                transformed[1] >= -90 && transformed[1] <= 90) {
              return coords.length > 2 ? [transformed[0], transformed[1], coords[2]] : transformed;
            } else {
              console.warn('⚠️ 转换结果超出有效范围:', {
                原始坐标: originalCoord,
                转换结果: transformed,
                源坐标系: sourceEPSG
              });
              return coords;
            }
          } else {
            console.warn('⚠️ OpenLayers transform 不可用');
            return coords;
          }
        } catch (error) {
          console.warn('⚠️ 坐标转换失败:', {
            原始坐标: [coords[0], coords[1]],
            源坐标系: sourceEPSG,
            错误: error
          });
          return coords;
        }
      } else {
        // 嵌套数组，递归处理
        return coords.map((coord: any) => transformCoordinates(coord));
      }
    };

    // 创建转换后的 GeoJSON
    const transformedGeoJSON = {
      ...geojson,
      crs: {
        type: 'name',
        properties: {
          name: 'EPSG:4326'
        }
      },
      features: geojson.features.map((feature: any) => ({
        ...feature,
        geometry: feature.geometry ? {
          ...feature.geometry,
          coordinates: transformCoordinates(feature.geometry.coordinates)
        } : null
      }))
    };

    // 计算转换后的坐标范围
    let minLon = Infinity, maxLon = -Infinity, minLat = Infinity, maxLat = -Infinity;
    transformedGeoJSON.features.forEach((feature: any) => {
      if (feature.geometry && feature.geometry.coordinates) {
        const extractBounds = (coords: any) => {
          if (typeof coords[0] === 'number') {
            minLon = Math.min(minLon, coords[0]);
            maxLon = Math.max(maxLon, coords[0]);
            minLat = Math.min(minLat, coords[1]);
            maxLat = Math.max(maxLat, coords[1]);
          } else {
            coords.forEach((coord: any) => extractBounds(coord));
          }
        };
        extractBounds(feature.geometry.coordinates);
      }
    });

    console.log('✅ 坐标转换完成');
    console.log('📍 转换后坐标范围:', {
      经度范围: [minLon, maxLon],
      纬度范围: [minLat, maxLat],
      中心点: [(minLon + maxLon) / 2, (minLat + maxLat) / 2],
      范围有效性: minLon >= -180 && maxLon <= 180 && minLat >= -90 && maxLat <= 90 ? '有效' : '无效'
    });

    return transformedGeoJSON;

  } catch (error) {
    console.error('❌ 坐标转换失败:', error);
    console.log('🔄 返回原始数据');
    return geojson;
  }
};

// SHP文件加载
const loadShpToMap = async () => {
  const mapInstance = getMapInstance();

  console.log('🚀 开始加载SHP文件到地图...');
  console.log('📋 当前状态检查:', {
    地图对象: map ? '已注入' : '未注入',
    地图实例: mapInstance ? '已获取' : '未获取',
    地图类型: typeof mapInstance,
    上传文件数量: uploadedFiles.value.length,
    文件列表: uploadedFiles.value.map(f => ({ 名称: f.name, 大小: f.size })),
    SHP文件验证: hasValidShpFiles.value,
    按钮状态: hasValidShpFiles.value ? '可用' : '禁用'
  });

  if (!mapInstance) {
    console.error('❌ 地图对象未注入或未初始化');
    ElMessage.error('地图对象未初始化，请确保地图已加载');
    return;
  }

  if (!hasValidShpFiles.value) {
    console.error('❌ 没有有效的SHP文件');
    console.log('📁 当前文件详情:', uploadedFiles.value.map(f => ({
      文件名: f.name,
      扩展名: f.name.split('.').pop(),
      是否为SHP: f.name.toLowerCase().endsWith('.shp')
    })));
    ElMessage.error('请先选择有效的SHP文件');
    return;
  }

  loading.value = true;
  loadingProgress.value = 0;
  loadingStatus.value = undefined;
  loadingText.value = '正在读取SHP文件...';

  try {
    // 查找SHP文件和相关文件
    const shpFile = uploadedFiles.value.find(f => f.name.endsWith('.shp'));
    const baseName = shpFile?.name.replace('.shp', '');
    const dbfFile = uploadedFiles.value.find(f => f.name === `${baseName}.dbf`);
    const prjFile = uploadedFiles.value.find(f => f.name === `${baseName}.prj`);

    if (!shpFile) {
      throw new Error('未找到SHP文件');
    }

    console.log('开始处理SHP文件:', {
      shpFile: shpFile.name,
      dbfFile: dbfFile?.name || '未找到',
      prjFile: prjFile?.name || '未找到',
      totalFiles: uploadedFiles.value.length
    });

    loadingProgress.value = 20;
    loadingText.value = '正在解析SHP文件...';

    // 读取文件为ArrayBuffer
    const shpBuffer = await readFileAsArrayBuffer(shpFile);
    const dbfBuffer = dbfFile ? await readFileAsArrayBuffer(dbfFile) : undefined;
    const prjText = prjFile ? await readFileAsText(prjFile) : undefined;

    loadingProgress.value = 40;
    loadingText.value = '正在转换为GeoJSON...';

    // 使用shapefile库转换为GeoJSON
    const geojson = await convertShpToGeoJSON(shpBuffer, dbfBuffer);

    console.log('SHP转换完成:', {
      type: geojson.type,
      features: geojson.features?.length || 0,
      crs: geojson.crs,
      bbox: geojson.bbox
    });

    // 详细输出GeoJSON信息
    console.group(`📁 SHP文件 "${shpFile.name}" 转换结果`);
    console.log('🔄 完整的GeoJSON对象:', geojson);
    console.log('📊 GeoJSON统计信息:', {
      类型: geojson.type,
      要素数量: geojson.features?.length || 0,
      坐标系: geojson.crs || '未指定',
      边界框: geojson.bbox || '未计算'
    });

    // 输出前几个要素的详细信息
    if (geojson.features && geojson.features.length > 0) {
      console.log('🎯 前3个要素详情:');
      geojson.features.slice(0, 3).forEach((feature: any, index: number) => {
        console.log(`要素 ${index + 1}:`, {
          几何类型: feature.geometry?.type,
          属性数量: Object.keys(feature.properties || {}).length,
          属性字段: Object.keys(feature.properties || {}),
          几何坐标: feature.geometry?.coordinates ? '已包含' : '缺失',
          完整要素: feature
        });
      });

      // 如果要素很多，显示省略信息
      if (geojson.features.length > 3) {
        console.log(`... 还有 ${geojson.features.length - 3} 个要素未显示`);
      }
    }

    // 输出属性字段统计
    if (geojson.features && geojson.features.length > 0) {
      const allProperties = new Set();
      geojson.features.forEach((feature: any) => {
        if (feature.properties) {
          Object.keys(feature.properties).forEach(key => allProperties.add(key));
        }
      });
      console.log('📋 所有属性字段:', Array.from(allProperties));
    }

    console.groupEnd();

    loadingProgress.value = 50;
    loadingText.value = '正在检测坐标系...';

    // 检测坐标系 - 优先使用 PRJ 文件
    let detectedCRS: string;
    if (prjText) {
      detectedCRS = parsePRJFile(prjText);
      console.log('🔍 从 PRJ 文件检测到坐标系:', detectedCRS);
    } else {
      detectedCRS = detectCoordinateSystem(geojson);
      console.log('🔍 从坐标范围检测到坐标系:', detectedCRS);
    }

    loadingProgress.value = 60;
    loadingText.value = '正在转换坐标系...';

    // 转换坐标系到 WGS84
    const transformedGeoJSON = transformGeoJSONToWGS84(geojson, detectedCRS);

    loadingProgress.value = 80;
    loadingText.value = '正在添加到地图...';

    // 添加到地图
    await addGeoJSONToMap(transformedGeoJSON, shpFile.name, detectedCRS);

    loadingProgress.value = 100;
    loadingStatus.value = 'success';
    loadingText.value = '加载完成！';

    ElMessage.success(`成功加载SHP文件: ${shpFile.name}`);

  } catch (error) {
    console.error('加载SHP文件失败:', error);
    loadingStatus.value = 'exception';
    loadingText.value = '加载失败';
    ElMessage.error(`加载失败: ${error instanceof Error ? error.message : '未知错误'}`);
  } finally {
    setTimeout(() => {
      loading.value = false;
      loadingProgress.value = 0;
      loadingStatus.value = undefined;
      loadingText.value = '';
    }, 2000);
  }
};

// 辅助函数
const readFileAsArrayBuffer = (file: File): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as ArrayBuffer);
    reader.onerror = () => reject(new Error('文件读取失败'));
    reader.readAsArrayBuffer(file);
  });
};

const readFileAsText = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = () => reject(new Error('文件读取失败'));
    reader.readAsText(file);
  });
};

const convertShpToGeoJSON = async (shpBuffer: ArrayBuffer, dbfBuffer?: ArrayBuffer): Promise<any> => {
  console.log('🔄 开始使用shapefile库解析...');
  console.log('📁 文件信息:', {
    'SHP文件大小': `${(shpBuffer.byteLength / 1024).toFixed(2)} KB`,
    'DBF文件大小': dbfBuffer ? `${(dbfBuffer.byteLength / 1024).toFixed(2)} KB` : '未提供',
    '是否包含属性': dbfBuffer ? '是' : '否'
  });

  try {
    // 使用 shapefile.read() 方法直接读取所有要素
    console.log('📖 调用 shapefile.read() 方法...');
    const geojson = await shapefile.read(shpBuffer, dbfBuffer);

    console.log('✅ shapefile库解析完成!', {
      总要素数: geojson.features?.length || 0,
      解析状态: '成功',
      数据完整性: geojson.features?.length > 0 ? '正常' : '无数据',
      GeoJSON类型: geojson.type
    });

    // 输出前几个要素的详细信息
    if (geojson.features && geojson.features.length > 0) {
      console.log('🎯 前3个要素详情:');
      geojson.features.slice(0, 3).forEach((feature: any, index: number) => {
        console.log(`📍 要素 ${index + 1}:`, {
          几何类型: feature.geometry?.type,
          属性字段数: Object.keys(feature.properties || {}).length,
          属性字段: Object.keys(feature.properties || {}),
          坐标数量: feature.geometry?.coordinates ?
            (Array.isArray(feature.geometry.coordinates[0]) ?
              feature.geometry.coordinates.length : 1) : 0,
          完整要素: feature
        });
      });
    }

    return geojson;

  } catch (error) {
    console.error('❌ shapefile解析失败:', error);

    // 尝试使用流式读取方法作为备选
    console.log('🔄 尝试使用流式读取方法...');
    try {
      const features: any[] = [];

      // 使用 shapefile.open() 创建流
      const source = shapefile.open(shpBuffer, dbfBuffer);

      console.log('📖 shapefile.open() 返回:', source);
      console.log('📖 source 类型:', typeof source);
      console.log('📖 source 方法:', Object.getOwnPropertyNames(source));

      // 检查 source 是否有 read 方法
      if (typeof source.read === 'function') {
        let result;
        let featureCount = 0;
        while (!(result = await source.read()).done) {
          if (result.value) {
            featureCount++;
            features.push(result.value);

            if (featureCount <= 3) {
              console.log(`📍 流式读取要素 ${featureCount}:`, result.value);
            }
          }
        }
      } else {
        throw new Error('source 对象没有 read 方法');
      }

      const geojson = {
        type: 'FeatureCollection',
        features: features
      };

      console.log('✅ 流式读取完成!', {
        总要素数: features.length,
        解析状态: '成功'
      });

      return geojson;

    } catch (streamError) {
      console.error('❌ 流式读取也失败:', streamError);
      throw new Error(`SHP文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
};

const addGeoJSONToMap = async (geojson: any, fileName: string, sourceCRS?: string): Promise<void> => {
  const mapInstance = getMapInstance();

  if (!mapInstance) {
    throw new Error('地图实例不可用');
  }

  // 创建矢量数据源
  const vectorSource = new VectorSource({
    features: new GeoJSON().readFeatures(geojson, {
      featureProjection: mapInstance.getView().getProjection()
    })
  });

  // 创建样式
  const style = new Style({
    stroke: new Stroke({
      color: getRandomColor(),
      width: 2
    }),
    fill: new Fill({
      color: getRandomColor() + '20' // 添加透明度
    }),
    image: new Circle({
      radius: 6,
      fill: new Fill({
        color: getRandomColor()
      }),
      stroke: new Stroke({
        color: '#fff',
        width: 2
      })
    })
  });

  // 创建矢量图层
  const vectorLayer = new VectorLayer({
    source: vectorSource,
    style: style
  });

  // 添加到地图
  mapInstance.addLayer(vectorLayer);

  // 记录已加载的图层
  const layerInfo = {
    id: Date.now().toString(),
    name: fileName.replace('.shp', ''),
    layer: vectorLayer,
    source: vectorSource,
    featureCount: vectorSource.getFeatures().length,
    visible: true,
    geojson: geojson,
    originalCRS: sourceCRS || '未知',
    targetCRS: 'EPSG:4326'
  };

  loadedLayers.value.push(layerInfo);

  console.log('🗺️ 图层已成功添加到地图:', {
    图层名称: layerInfo.name,
    要素数量: layerInfo.featureCount,
    图层ID: layerInfo.id,
    可见状态: layerInfo.visible,
    原始坐标系: layerInfo.originalCRS,
    目标坐标系: layerInfo.targetCRS,
    坐标转换: layerInfo.originalCRS !== 'EPSG:4326' ? '已转换' : '无需转换',
    图层对象: layerInfo.layer,
    数据源: layerInfo.source
  });

  console.log('📍 图层范围信息:', {
    边界框: layerInfo.source.getExtent(),
    投影: mapInstance.getView().getProjection().getCode()
  });
};

// 图层管理
const zoomToLayer = (layerInfo: any) => {
  const mapInstance = getMapInstance();
  if (layerInfo.source && mapInstance) {
    const extent = layerInfo.source.getExtent();
    mapInstance.getView().fit(extent, { padding: [20, 20, 20, 20] });
    console.log('定位到图层:', layerInfo.name);
  }
};

const toggleLayerVisibility = (layerInfo: any) => {
  if (layerInfo.layer) {
    layerInfo.visible = !layerInfo.visible;
    layerInfo.layer.setVisible(layerInfo.visible);
    console.log('切换图层可见性:', layerInfo.name, layerInfo.visible);
  }
};

const removeLayer = (layerInfo: any) => {
  ElMessageBox.confirm(
    `确定要删除图层 "${layerInfo.name}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const mapInstance = getMapInstance();
    if (layerInfo.layer && mapInstance) {
      mapInstance.removeLayer(layerInfo.layer);
    }

    const index = loadedLayers.value.findIndex(l => l.id === layerInfo.id);
    if (index > -1) {
      loadedLayers.value.splice(index, 1);
    }

    console.log('删除图层:', layerInfo.name);
    ElMessage.success('图层已删除');
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

const clearAllLayers = () => {
  ElMessageBox.confirm(
    '确定要清除所有已加载的SHP图层吗？',
    '确认清除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const mapInstance = getMapInstance();
    loadedLayers.value.forEach(layerInfo => {
      if (layerInfo.layer && mapInstance) {
        mapInstance.removeLayer(layerInfo.layer);
      }
    });

    loadedLayers.value = [];
    uploadedFiles.value = [];

    console.log('清除所有SHP图层');
    ElMessage.success('所有图层已清除');
  }).catch(() => {
    ElMessage.info('已取消清除');
  });
};

// 调试函数
const debugFileStatus = () => {
  console.group('🔍 SHP加载器调试信息');
  console.log('📁 上传文件状态:', {
    文件数量: uploadedFiles.value.length,
    文件详情: uploadedFiles.value.map(f => ({
      文件名: f.name,
      文件大小: f.size,
      文件类型: f.type,
      扩展名: f.name.split('.').pop()?.toLowerCase(),
      是否为SHP: f.name.toLowerCase().endsWith('.shp'),
      文件对象: f
    }))
  });

  console.log('✅ 验证结果:', {
    hasValidShpFiles: hasValidShpFiles.value,
    SHP文件列表: uploadedFiles.value.filter(f => f.name.toLowerCase().endsWith('.shp')).map(f => f.name),
    DBF文件列表: uploadedFiles.value.filter(f => f.name.toLowerCase().endsWith('.dbf')).map(f => f.name),
    其他文件: uploadedFiles.value.filter(f => !f.name.toLowerCase().endsWith('.shp') && !f.name.toLowerCase().endsWith('.dbf')).map(f => f.name)
  });

  const mapInstance = getMapInstance();
  console.log('🗺️ 地图状态:', {
    注入的地图对象: map,
    实际地图实例: mapInstance,
    地图是否存在: !!mapInstance,
    地图投影: mapInstance ? mapInstance.getView().getProjection().getCode() : '未知',
    地图图层数量: mapInstance ? mapInstance.getLayers().getLength() : 0
  });

  console.log('🎛️ 组件状态:', {
    loading: loading.value,
    loadingProgress: loadingProgress.value,
    loadingText: loadingText.value,
    loadedLayers: loadedLayers.value.length
  });

  console.groupEnd();

  ElMessage.info('调试信息已输出到控制台，请查看Console标签页');
};
</script>

<style scoped lang="scss">
.shp-loader-panel {
  padding: 20px;
  height: 100%;
  overflow-y: auto;

  .panel-header {
    margin-bottom: 20px;

    h3 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 18px;
      font-weight: 600;
    }

    .panel-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .panel-content {
    .upload-section {
      margin-bottom: 20px;

      .shp-upload {
        :deep(.el-upload) {
          width: 100%;
        }

        :deep(.el-upload-dragger) {
          width: 100%;
          height: 120px;
          border: 2px dashed #d9d9d9;
          border-radius: 6px;
          cursor: pointer;
          position: relative;
          overflow: hidden;
          transition: border-color 0.3s;

          &:hover {
            border-color: #409EFF;
          }
        }

        :deep(.el-icon--upload) {
          font-size: 28px;
          color: #8c939d;
          margin: 20px 0 16px;
        }

        :deep(.el-upload__text) {
          color: #606266;
          font-size: 14px;
          text-align: center;

          em {
            color: #409EFF;
            font-style: normal;
          }
        }

        :deep(.el-upload__tip) {
          font-size: 12px;
          color: #909399;
          margin-top: 7px;
        }
      }
    }

    .file-list-section {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }

      .file-list {
        border: 1px solid #EBEEF5;
        border-radius: 4px;

        .file-item {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          border-bottom: 1px solid #EBEEF5;

          &:last-child {
            border-bottom: none;
          }

          .el-icon {
            margin-right: 8px;
            color: #909399;
          }

          .file-name {
            flex: 1;
            font-size: 14px;
            color: #303133;
          }

          .file-size {
            margin-right: 8px;
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }

    .action-buttons {
      margin-bottom: 20px;
      display: flex;
      gap: 12px;

      .el-button {
        flex: 1;
      }
    }

    .loaded-layers-section {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }

      .layer-list {
        border: 1px solid #EBEEF5;
        border-radius: 4px;

        .layer-item {
          padding: 12px;
          border-bottom: 1px solid #EBEEF5;

          &:last-child {
            border-bottom: none;
          }

          .layer-info {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .el-icon {
              margin-right: 8px;
              color: #409EFF;
            }

            .layer-name {
              flex: 1;
              font-size: 14px;
              color: #303133;
              font-weight: 500;
            }

            .el-tag {
              margin-left: 8px;
            }
          }

          .layer-actions {
            display: flex;
            gap: 8px;

            .el-button {
              flex: 1;
            }
          }
        }
      }
    }

    .loading-status {
      margin-top: 20px;

      .el-progress {
        margin-bottom: 12px;
      }

      .loading-text {
        margin: 0;
        text-align: center;
        font-size: 14px;
        color: #606266;
      }
    }
  }
}
</style>