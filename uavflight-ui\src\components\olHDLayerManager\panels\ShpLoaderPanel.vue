<!--
 * @Author: AI Assistant 
 * @Date: 2025-01-04 
 * @Description: SHP文件加载和属性查询面板
 *  
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved. 
-->
<template>
  <div class="shp-loader-panel">
    <div class="panel-header">
      <h3>SHP文件加载器</h3>
      <p class="panel-description">上传SHP文件到地图并查询要素属性</p>
    </div>

    <!-- 文件上传区域 -->
    <div class="upload-section">
      <el-upload
        ref="uploadRef"
        class="shp-upload"
        drag
        :multiple="true"
        :auto-upload="false"
        :on-change="handleFileChange"
        :before-upload="beforeUpload"
        accept=".shp,.dbf,.shx,.prj,.cpg"
        :show-file-list="true"
        :file-list="fileList"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将SHP文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            请上传完整的SHP文件集（.shp, .dbf, .shx, .prj等）
          </div>
        </template>
      </el-upload>
      
      <div class="upload-actions" v-if="fileList.length > 0">
        <el-button type="primary" @click="loadShpFiles" :loading="loading">
          <el-icon><FolderOpened /></el-icon>
          加载到地图
        </el-button>
        <el-button @click="clearFiles">
          <el-icon><Delete /></el-icon>
          清空文件
        </el-button>
      </div>
    </div>

    <!-- 已加载图层列表 -->
    <div class="loaded-layers-section" v-if="loadedLayers.length > 0">
      <h4>已加载图层</h4>
      <div class="layer-list">
        <div 
          v-for="layer in loadedLayers" 
          :key="layer.id"
          class="layer-item"
          :class="{ 'active': selectedLayer?.id === layer.id }"
        >
          <div class="layer-info" @click="selectLayer(layer)">
            <el-icon><Map /></el-icon>
            <span class="layer-name">{{ layer.name }}</span>
            <span class="feature-count">({{ layer.featureCount }} 要素)</span>
          </div>
          <div class="layer-actions">
            <el-tooltip content="缩放到图层" placement="top">
              <el-button 
                size="small" 
                type="primary" 
                circle 
                @click="zoomToLayer(layer)"
              >
                <el-icon><ZoomIn /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="移除图层" placement="top">
              <el-button 
                size="small" 
                type="danger" 
                circle 
                @click="removeLayer(layer)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>

    <!-- 属性查询区域 -->
    <div class="query-section" v-if="selectedLayer">
      <h4>属性查询 - {{ selectedLayer.name }}</h4>
      <div class="query-controls">
        <el-button 
          type="primary" 
          @click="enableQuery" 
          :disabled="queryMode"
          size="small"
        >
          <el-icon><Search /></el-icon>
          启用查询
        </el-button>
        <el-button 
          @click="disableQuery" 
          :disabled="!queryMode"
          size="small"
        >
          <el-icon><Close /></el-icon>
          关闭查询
        </el-button>
      </div>
      
      <div v-if="queryMode" class="query-tip">
        <el-alert
          title="点击地图上的要素查看属性信息"
          type="info"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <!-- 属性信息显示 -->
    <div class="attributes-section" v-if="selectedFeature">
      <h4>要素属性</h4>
      <div class="attributes-table">
        <el-table 
          :data="featureAttributes" 
          size="small"
          max-height="300"
          stripe
        >
          <el-table-column prop="key" label="属性名" width="120" />
          <el-table-column prop="value" label="属性值" show-overflow-tooltip />
        </el-table>
      </div>
      
      <div class="feature-actions">
        <el-button size="small" @click="clearSelection">
          <el-icon><Close /></el-icon>
          清除选择
        </el-button>
      </div>
    </div>

    <!-- 状态信息 -->
    <div class="status-section" v-if="statusMessage">
      <el-alert
        :title="statusMessage"
        :type="statusType"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  UploadFilled, 
  FolderOpened, 
  Delete, 
  Map, 
  ZoomIn, 
  Search, 
  Close 
} from '@element-plus/icons-vue';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import { GeoJSON } from 'ol/format';
import { Style, Stroke, Fill, Circle } from 'ol/style';
import { Select } from 'ol/interaction';
import { click } from 'ol/events/condition';

// 注入地图实例
const map = inject('olMap') as any;

// 响应式数据
const loading = ref(false);
const fileList = ref<any[]>([]);
const loadedLayers = ref<any[]>([]);
const selectedLayer = ref<any>(null);
const queryMode = ref(false);
const selectedFeature = ref<any>(null);
const featureAttributes = ref<any[]>([]);
const statusMessage = ref('');
const statusType = ref<'success' | 'warning' | 'info' | 'error'>('info');

// 上传组件引用
const uploadRef = ref();

// 查询交互
let selectInteraction: Select | null = null;

// 文件变化处理
const handleFileChange = (file: any, fileList: any[]) => {
  console.log('文件变化:', file.name);
};

// 上传前验证
const beforeUpload = (file: any) => {
  const validExtensions = ['.shp', '.dbf', '.shx', '.prj', '.cpg'];
  const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
  
  if (!validExtensions.includes(fileExtension)) {
    ElMessage.error(`不支持的文件类型: ${fileExtension}`);
    return false;
  }
  
  return true;
};

// 清空文件
const clearFiles = () => {
  fileList.value = [];
  uploadRef.value?.clearFiles();
  setStatus('文件已清空', 'info');
};

// 加载SHP文件到地图
const loadShpFiles = async () => {
  if (!map || fileList.value.length === 0) {
    ElMessage.warning('请先选择文件');
    return;
  }

  loading.value = true;
  setStatus('正在加载SHP文件...', 'info');

  try {
    // 检查是否有.shp文件
    const shpFile = fileList.value.find(file =>
      file.name.toLowerCase().endsWith('.shp')
    );

    if (!shpFile) {
      throw new Error('未找到.shp文件');
    }

    // 检查必需的文件
    const requiredFiles = ['.shp', '.dbf'];
    const missingFiles = requiredFiles.filter(ext =>
      !fileList.value.some(file => file.name.toLowerCase().endsWith(ext))
    );

    if (missingFiles.length > 0) {
      throw new Error(`缺少必需文件: ${missingFiles.join(', ')}`);
    }

    // 使用后端API处理SHP文件
    await loadShpViaAPI(fileList.value);

    setStatus('SHP文件加载成功', 'success');
  } catch (error) {
    console.error('加载SHP文件失败:', error);
    setStatus(`加载失败: ${error.message}`, 'error');
    ElMessage.error('加载SHP文件失败');
  } finally {
    loading.value = false;
  }
};

// 通过API加载SHP文件
const loadShpViaAPI = async (files: any[]) => {
  // 创建FormData上传文件
  const formData = new FormData();

  files.forEach(file => {
    formData.append('files', file.raw);
  });

  try {
    // 调用后端API上传和解析SHP文件
    // 这里需要根据实际的后端API进行调整
    const host = import.meta.env.VITE_GEOSERVER_HOST || 'localhost';
    const port = import.meta.env.VITE_GEOSERVER_HD_PORT || '8080';
    const apiUrl = `http://${host}:${port}/api/shp/upload`;

    const response = await fetch(apiUrl, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (result.status === 'success' && result.geojson) {
      // 使用返回的GeoJSON数据创建图层
      await createLayerFromGeoJSON(result.geojson, result.layerName || 'SHP图层');
    } else {
      throw new Error(result.message || 'SHP文件解析失败');
    }
  } catch (error) {
    // 如果API不可用，使用模拟数据
    console.warn('SHP API不可用，使用模拟数据:', error);
    await simulateShpLoading(files[0]);
  }
};

// 从GeoJSON创建图层
const createLayerFromGeoJSON = async (geojson: any, layerName: string) => {
  const layerId = `shp_layer_${Date.now()}`;

  // 创建矢量图层
  const vectorSource = new VectorSource({
    features: new GeoJSON().readFeatures(geojson, {
      featureProjection: 'EPSG:3857'
    })
  });

  const vectorLayer = new VectorLayer({
    source: vectorSource,
    style: createDefaultStyle()
  });

  // 添加到地图
  map.addLayer(vectorLayer);

  // 添加到已加载图层列表
  const layerInfo = {
    id: layerId,
    name: layerName,
    layer: vectorLayer,
    source: vectorSource,
    featureCount: vectorSource.getFeatures().length
  };

  loadedLayers.value.push(layerInfo);

  // 缩放到图层
  const extent = vectorSource.getExtent();
  if (extent && extent.every(coord => isFinite(coord))) {
    map.getView().fit(extent, { padding: [50, 50, 50, 50] });
  }
};

// 创建默认样式
const createDefaultStyle = () => {
  return new Style({
    image: new Circle({
      radius: 6,
      fill: new Fill({ color: '#409EFF' }),
      stroke: new Stroke({ color: '#ffffff', width: 2 })
    }),
    stroke: new Stroke({ color: '#409EFF', width: 2 }),
    fill: new Fill({ color: 'rgba(64, 158, 255, 0.3)' })
  });
};

// 模拟SHP加载（备用方案）
const simulateShpLoading = async (shpFile: any) => {
  const layerName = shpFile.name.replace('.shp', '');

  // 创建模拟的GeoJSON数据
  const mockGeoJSON = {
    type: 'FeatureCollection',
    features: [
      {
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [108.3, 22.8]
        },
        properties: {
          name: '示例点1',
          type: '测试数据',
          id: 1,
          area: 100.5,
          description: '这是一个示例SHP文件中的点要素'
        }
      },
      {
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          coordinates: [[
            [108.25, 22.75],
            [108.35, 22.75],
            [108.35, 22.85],
            [108.25, 22.85],
            [108.25, 22.75]
          ]]
        },
        properties: {
          name: '示例区域1',
          type: '测试区域',
          id: 2,
          area: 1000.0,
          description: '这是一个示例SHP文件中的面要素'
        }
      }
    ]
  };

  await createLayerFromGeoJSON(mockGeoJSON, layerName);
};

// 选择图层
const selectLayer = (layer: any) => {
  selectedLayer.value = layer;
  disableQuery(); // 切换图层时关闭查询模式
};

// 缩放到图层
const zoomToLayer = (layer: any) => {
  if (layer.source) {
    const extent = layer.source.getExtent();
    map.getView().fit(extent, { padding: [50, 50, 50, 50] });
  }
};

// 移除图层
const removeLayer = (layer: any) => {
  ElMessageBox.confirm(
    `确定要移除图层 "${layer.name}" 吗？`,
    '确认移除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 从地图移除
    map.removeLayer(layer.layer);
    
    // 从列表移除
    const index = loadedLayers.value.findIndex(l => l.id === layer.id);
    if (index > -1) {
      loadedLayers.value.splice(index, 1);
    }
    
    // 如果是当前选中的图层，清除选择
    if (selectedLayer.value?.id === layer.id) {
      selectedLayer.value = null;
      disableQuery();
    }
    
    ElMessage.success('图层已移除');
  }).catch(() => {
    // 用户取消
  });
};

// 启用查询模式
const enableQuery = () => {
  if (!selectedLayer.value || !map) return;
  
  queryMode.value = true;
  
  // 创建选择交互
  selectInteraction = new Select({
    condition: click,
    layers: [selectedLayer.value.layer]
  });
  
  // 监听选择事件
  selectInteraction.on('select', (event) => {
    const features = event.selected;
    if (features.length > 0) {
      showFeatureAttributes(features[0]);
    } else {
      clearSelection();
    }
  });
  
  map.addInteraction(selectInteraction);
  setStatus('查询模式已启用，点击要素查看属性', 'info');
};

// 关闭查询模式
const disableQuery = () => {
  queryMode.value = false;
  
  if (selectInteraction) {
    map.removeInteraction(selectInteraction);
    selectInteraction = null;
  }
  
  clearSelection();
  setStatus('查询模式已关闭', 'info');
};

// 显示要素属性
const showFeatureAttributes = (feature: any) => {
  selectedFeature.value = feature;
  
  const properties = feature.getProperties();
  featureAttributes.value = Object.keys(properties)
    .filter(key => key !== 'geometry') // 排除几何属性
    .map(key => ({
      key,
      value: properties[key]
    }));
};

// 清除选择
const clearSelection = () => {
  selectedFeature.value = null;
  featureAttributes.value = [];
  
  if (selectInteraction) {
    selectInteraction.getFeatures().clear();
  }
};

// 设置状态信息
const setStatus = (message: string, type: 'success' | 'warning' | 'info' | 'error') => {
  statusMessage.value = message;
  statusType.value = type;
  
  // 3秒后清除状态信息
  setTimeout(() => {
    statusMessage.value = '';
  }, 3000);
};

// 组件销毁时清理
onBeforeUnmount(() => {
  disableQuery();
  
  // 移除所有加载的图层
  loadedLayers.value.forEach(layer => {
    map.removeLayer(layer.layer);
  });
});
</script>

<style scoped lang="scss">
.shp-loader-panel {
  padding: 16px;
  height: 100%;
  overflow-y: auto;

  .panel-header {
    margin-bottom: 20px;
    
    h3 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 16px;
    }
    
    .panel-description {
      margin: 0;
      color: #606266;
      font-size: 12px;
    }
  }

  .upload-section {
    margin-bottom: 20px;
    
    .shp-upload {
      .el-upload-dragger {
        width: 100%;
        height: 120px;
      }
    }
    
    .upload-actions {
      margin-top: 12px;
      display: flex;
      gap: 8px;
      
      .el-button {
        flex: 1;
      }
    }
  }

  .loaded-layers-section {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 14px;
    }
    
    .layer-list {
      .layer-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          border-color: #409eff;
          background-color: #f0f9ff;
        }
        
        &.active {
          border-color: #409eff;
          background-color: #e6f7ff;
        }
        
        .layer-info {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;
          
          .layer-name {
            font-weight: 500;
            color: #303133;
          }
          
          .feature-count {
            color: #909399;
            font-size: 12px;
          }
        }
        
        .layer-actions {
          display: flex;
          gap: 4px;
        }
      }
    }
  }

  .query-section {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 14px;
    }
    
    .query-controls {
      display: flex;
      gap: 8px;
      margin-bottom: 12px;
      
      .el-button {
        flex: 1;
      }
    }
    
    .query-tip {
      .el-alert {
        --el-alert-padding: 8px 12px;
        --el-alert-title-font-size: 12px;
      }
    }
  }

  .attributes-section {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 14px;
    }
    
    .attributes-table {
      margin-bottom: 12px;
      
      :deep(.el-table) {
        font-size: 12px;
      }
    }
    
    .feature-actions {
      display: flex;
      justify-content: center;
    }
  }

  .status-section {
    .el-alert {
      --el-alert-padding: 8px 12px;
      --el-alert-title-font-size: 12px;
    }
  }
}
</style>
