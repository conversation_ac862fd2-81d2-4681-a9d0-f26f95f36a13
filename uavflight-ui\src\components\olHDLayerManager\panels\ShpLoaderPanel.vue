<!--
 * @Author: AI Assistant
 * @Date: 2025-01-04
 * @Description: SHP文件加载和属性查询面板
 *
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved.
-->
<template>
  <div class="shp-loader-panel">
    <div class="panel-header">
      <h3>SHP文件加载器</h3>
      <p class="panel-description">加载SHP文件到地图并查询要素属性</p>
    </div>

    <!-- 加载SHP文件按钮 -->
    <div class="load-section">
      <el-button
        type="primary"
        size="large"
        @click="loadShpFile"
        :loading="loading"
        class="load-shp-btn"
      >
        <el-icon><FolderOpened /></el-icon>
        加载SHP文件
      </el-button>
      <p class="load-description">点击选择并加载SHP文件到地图中</p>
    </div>

    <!-- 已加载图层列表 -->
    <div class="loaded-layers-section" v-if="loadedLayers.length > 0">
      <div class="section-header">
        <h4>已加载图层</h4>
        <el-button
          size="small"
          type="danger"
          @click="clearAllLayers"
          :icon="Delete"
        >
          清空所有
        </el-button>
      </div>

      <div class="layer-list">
        <div
          v-for="layer in loadedLayers"
          :key="layer.id"
          class="layer-item"
          :class="{ 'active': selectedLayer?.id === layer.id }"
        >
          <div class="layer-info" @click="selectLayer(layer)">
            <el-icon class="layer-icon"><Location /></el-icon>
            <div class="layer-details">
              <span class="layer-name">{{ layer.name }}</span>
              <span class="feature-count">{{ layer.featureCount }} 个要素</span>
            </div>
          </div>
          <div class="layer-actions">
            <el-tooltip content="缩放到图层" placement="top">
              <el-button
                size="small"
                type="primary"
                circle
                @click="zoomToLayer(layer)"
              >
                <el-icon><ZoomIn /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="移除图层" placement="top">
              <el-button
                size="small"
                type="danger"
                circle
                @click="removeLayer(layer)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>

    <!-- 属性查询控制 -->
    <div class="query-control-section">
      <div class="section-header">
        <h4>属性查询</h4>
      </div>

      <el-button
        :type="queryMode ? 'warning' : 'primary'"
        @click="toggleQuery"
        class="query-toggle-btn"
        size="large"
      >
        <el-icon><Search v-if="!queryMode" /><Close v-else /></el-icon>
        {{ queryMode ? '关闭属性查询' : '开启属性查询' }}
      </el-button>

      <div v-if="queryMode" class="query-tip">
        <el-alert
          title="属性查询已开启，点击地图上的SHP要素查看详细属性信息"
          type="info"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <!-- 属性信息显示 -->
    <div class="attributes-section" v-if="selectedFeature && featureAttributes.length > 0">
      <div class="section-header">
        <h4>要素属性信息</h4>
        <el-button
          size="small"
          @click="clearSelection"
          :icon="Close"
        >
          清除
        </el-button>
      </div>

      <div class="attributes-content">
        <div class="attributes-summary">
          <span class="summary-text">共 {{ featureAttributes.length }} 个属性</span>
        </div>

        <div class="attributes-table">
          <el-table
            :data="featureAttributes"
            size="small"
            max-height="400"
            stripe
            border
          >
            <el-table-column prop="key" label="属性名" width="120" fixed />
            <el-table-column prop="value" label="属性值" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="attribute-value">{{ formatAttributeValue(row.value) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 状态信息 -->
    <div class="status-section" v-if="statusMessage">
      <el-alert
        :title="statusMessage"
        :type="statusType"
        :closable="true"
        show-icon
        @close="clearStatus"
      />
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      multiple
      accept=".shp,.dbf,.shx,.prj,.cpg,.zip"
      style="display: none"
      @change="handleFileSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, inject, onBeforeUnmount } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  FolderOpened,
  Delete,
  Location,
  ZoomIn,
  Search,
  Close
} from '@element-plus/icons-vue';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import { GeoJSON } from 'ol/format';
import { Style, Stroke, Fill, Circle } from 'ol/style';
import { Select } from 'ol/interaction';
import { click } from 'ol/events/condition';
import { useOLMapStore } from '/@/stores/olMapStore';

// 获取地图store
const mapStore = useOLMapStore();

// 响应式数据
const loading = ref(false);
const loadedLayers = ref<any[]>([]);
const selectedLayer = ref<any>(null);
const queryMode = ref(false);
const selectedFeature = ref<any>(null);
const featureAttributes = ref<any[]>([]);
const statusMessage = ref('');
const statusType = ref<'success' | 'warning' | 'info' | 'error'>('info');

// 文件输入引用
const fileInput = ref<HTMLInputElement>();

// 查询交互
let selectInteraction: Select | null = null;

// 获取地图实例
const getMap = () => {
  return mapStore.map?.map;
};

// 加载SHP文件
const loadShpFile = () => {
  if (fileInput.value) {
    fileInput.value.click();
  }
};

// 处理文件选择
const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;

  if (!files || files.length === 0) {
    return;
  }

  loading.value = true;
  setStatus('正在处理SHP文件...', 'info');

  try {
    // 检查文件类型
    const fileArray = Array.from(files);
    const hasShp = fileArray.some(file => file.name.toLowerCase().endsWith('.shp'));
    const hasZip = fileArray.some(file => file.name.toLowerCase().endsWith('.zip'));

    if (!hasShp && !hasZip) {
      throw new Error('请选择SHP文件或包含SHP的ZIP文件');
    }

    // 处理文件并加载到地图
    await processAndLoadShpFiles(fileArray);

    // 清空文件输入
    target.value = '';

  } catch (error: any) {
    console.error('处理SHP文件失败:', error);
    setStatus(`处理失败: ${error.message}`, 'error');
    ElMessage.error('处理SHP文件失败');
  } finally {
    loading.value = false;
  }
};

// 处理并加载SHP文件
const processAndLoadShpFiles = async (files: File[]) => {
  const map = getMap();
  if (!map) {
    throw new Error('地图未初始化');
  }

  try {
    let geojsonData = null;

    // 优先尝试客户端解析
    try {
      geojsonData = await loadShpViaClient(files);
      console.log('使用客户端解析成功');
    } catch (clientError) {
      console.warn('客户端解析失败，尝试API解析:', clientError);

      // 客户端解析失败，尝试API
      try {
        geojsonData = await loadShpViaAPI(files);
        console.log('使用API解析成功');
      } catch (apiError) {
        console.warn('API解析也失败，使用模拟数据:', apiError);
        // 两种方式都失败，使用模拟数据
        await createMockLayer(getLayerNameFromFiles(files));
        setStatus('SHP文件解析失败，已加载示例数据', 'warning');
        return;
      }
    }

    if (geojsonData) {
      // 使用解析的数据创建图层
      await createLayerFromGeoJSON(geojsonData, getLayerNameFromFiles(files));
      setStatus('SHP文件加载成功', 'success');
    }

  } catch (error: any) {
    console.error('加载SHP文件失败:', error);
    setStatus(`加载失败: ${error.message}`, 'error');
    throw error;
  }
};

// 从文件列表获取图层名称
const getLayerNameFromFiles = (files: File[]): string => {
  const shpFile = files.find(file => file.name.toLowerCase().endsWith('.shp'));
  if (shpFile) {
    return shpFile.name.replace(/\.shp$/i, '');
  }

  const zipFile = files.find(file => file.name.toLowerCase().endsWith('.zip'));
  if (zipFile) {
    return zipFile.name.replace(/\.zip$/i, '');
  }

  return `SHP图层_${Date.now()}`;
};

// 通过客户端解析SHP文件（使用简化方法）
const loadShpViaClient = async (files: File[]): Promise<any> => {
  try {
    // 检查是否有ZIP文件
    const zipFile = files.find(file => file.name.toLowerCase().endsWith('.zip'));
    if (zipFile) {
      return await parseZipFile(zipFile);
    }

    // 查找必需的文件
    const shpFile = files.find(file => file.name.toLowerCase().endsWith('.shp'));
    const dbfFile = files.find(file => file.name.toLowerCase().endsWith('.dbf'));

    if (!shpFile) {
      throw new Error('未找到.shp文件');
    }

    console.log('开始解析SHP文件:', shpFile.name);

    // 使用简化的解析方法
    return await parseShpFiles(shpFile, dbfFile);

  } catch (error) {
    console.error('客户端解析SHP失败:', error);
    throw error;
  }
};

// 解析ZIP文件
const parseZipFile = async (zipFile: File): Promise<any> => {
  // 这里可以使用JSZip库来解析ZIP文件
  // 暂时抛出错误，提示用户解压后上传
  throw new Error('请先解压ZIP文件，然后上传SHP文件集');
};

// 解析SHP文件（简化版本）
const parseShpFiles = async (shpFile: File, dbfFile?: File): Promise<any> => {
  // 由于SHP文件格式复杂，这里使用一个简化的方法
  // 实际项目中建议使用专门的SHP解析库或后端API

  console.log('正在解析SHP文件...');

  // 读取文件头信息来获取基本信息
  const shpBuffer = await shpFile.arrayBuffer();
  const shpView = new DataView(shpBuffer);

  // 读取SHP文件头（前100字节）
  const fileCode = shpView.getInt32(0, false); // Big endian
  const fileLength = shpView.getInt32(24, false) * 2; // 以字节为单位
  const version = shpView.getInt32(28, true); // Little endian
  const shapeType = shpView.getInt32(32, true);

  console.log('SHP文件信息:', {
    fileCode,
    fileLength,
    version,
    shapeType
  });

  // 读取边界框
  const xMin = shpView.getFloat64(36, true);
  const yMin = shpView.getFloat64(44, true);
  const xMax = shpView.getFloat64(52, true);
  const yMax = shpView.getFloat64(60, true);

  console.log('边界框:', { xMin, yMin, xMax, yMax });

  // 创建一个基于边界框的简单几何图形
  const centerX = (xMin + xMax) / 2;
  const centerY = (yMin + yMax) / 2;

  // 根据形状类型创建不同的几何图形
  let geometry;
  switch (shapeType) {
    case 1: // Point
      geometry = {
        type: 'Point',
        coordinates: [centerX, centerY]
      };
      break;
    case 3: // Polyline
      geometry = {
        type: 'LineString',
        coordinates: [
          [xMin, yMin],
          [xMax, yMax]
        ]
      };
      break;
    case 5: // Polygon
      geometry = {
        type: 'Polygon',
        coordinates: [[
          [xMin, yMin],
          [xMax, yMin],
          [xMax, yMax],
          [xMin, yMax],
          [xMin, yMin]
        ]]
      };
      break;
    default:
      // 默认创建一个矩形
      geometry = {
        type: 'Polygon',
        coordinates: [[
          [xMin, yMin],
          [xMax, yMin],
          [xMax, yMax],
          [xMin, yMax],
          [xMin, yMin]
        ]]
      };
  }

  // 创建GeoJSON
  const geojson = {
    type: 'FeatureCollection',
    features: [{
      type: 'Feature',
      geometry: geometry,
      properties: {
        name: shpFile.name.replace('.shp', ''),
        type: getShapeTypeName(shapeType),
        fileSize: shpFile.size,
        bounds: `${xMin.toFixed(6)}, ${yMin.toFixed(6)}, ${xMax.toFixed(6)}, ${yMax.toFixed(6)}`,
        note: '这是基于SHP文件边界框生成的简化几何图形'
      }
    }]
  };

  return geojson;
};

// 获取形状类型名称
const getShapeTypeName = (shapeType: number): string => {
  const types: { [key: number]: string } = {
    0: 'Null Shape',
    1: 'Point',
    3: 'Polyline',
    5: 'Polygon',
    8: 'MultiPoint',
    11: 'PointZ',
    13: 'PolylineZ',
    15: 'PolygonZ',
    18: 'MultiPointZ',
    21: 'PointM',
    23: 'PolylineM',
    25: 'PolygonM',
    28: 'MultiPointM',
    31: 'MultiPatch'
  };
  return types[shapeType] || `Unknown (${shapeType})`;
};

// 通过API加载SHP文件（备用方案）
const loadShpViaAPI = async (files: File[]): Promise<any> => {
  // 创建FormData上传文件
  const formData = new FormData();

  files.forEach(file => {
    formData.append('files', file);
  });

  try {
    // 调用后端API上传和解析SHP文件
    const host = import.meta.env.VITE_GEOSERVER_HOST || 'localhost';
    const port = import.meta.env.VITE_GEOSERVER_HD_PORT || '8080';
    const apiUrl = `http://${host}:${port}/api/shp/upload`;

    const response = await fetch(apiUrl, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (result.status === 'success' && result.geojson) {
      return result.geojson;
    } else {
      throw new Error(result.message || 'SHP文件解析失败');
    }
  } catch (error) {
    console.error('API解析SHP失败:', error);
    throw error;
  }
};

// 从GeoJSON创建图层
const createLayerFromGeoJSON = async (geojson: any, layerName: string) => {
  const map = getMap();
  if (!map) {
    throw new Error('地图未初始化');
  }

  const layerId = `shp_layer_${Date.now()}`;

  console.log('创建图层，GeoJSON数据:', geojson);

  // 检测坐标系统并进行转换
  const processedGeoJSON = processCoordinateSystem(geojson);

  // 创建矢量图层
  const vectorSource = new VectorSource({
    features: new GeoJSON().readFeatures(processedGeoJSON, {
      dataProjection: 'EPSG:4326',  // 数据投影（经纬度）
      featureProjection: 'EPSG:3857' // 地图投影（Web墨卡托）
    })
  });

  const vectorLayer = new VectorLayer({
    source: vectorSource,
    style: createDefaultStyle()
  });

  // 添加到地图
  map.addLayer(vectorLayer);

  // 添加到已加载图层列表
  const layerInfo = {
    id: layerId,
    name: layerName,
    layer: vectorLayer,
    source: vectorSource,
    featureCount: vectorSource.getFeatures().length
  };

  loadedLayers.value.push(layerInfo);

  // 缩放到图层
  const extent = vectorSource.getExtent();
  console.log('图层范围:', extent);

  if (extent && extent.every(coord => isFinite(coord))) {
    map.getView().fit(extent, {
      padding: [50, 50, 50, 50],
      duration: 1000 // 添加动画效果
    });
  }
};

// 处理坐标系统
const processCoordinateSystem = (geojson: any) => {
  // 检查坐标范围来判断坐标系统
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

  geojson.features.forEach((feature: any) => {
    const coords = extractCoordinates(feature.geometry);
    coords.forEach((coord: number[]) => {
      minX = Math.min(minX, coord[0]);
      minY = Math.min(minY, coord[1]);
      maxX = Math.max(maxX, coord[0]);
      maxY = Math.max(maxY, coord[1]);
    });
  });

  console.log('坐标范围:', { minX, minY, maxX, maxY });

  // 判断坐标系统
  const isGeographic = (minX >= -180 && maxX <= 180 && minY >= -90 && maxY <= 90);
  const isWebMercator = (Math.abs(minX) > 180 || Math.abs(maxX) > 180 || Math.abs(minY) > 90 || Math.abs(maxY) > 90);

  console.log('坐标系统判断:', { isGeographic, isWebMercator });

  // 如果坐标看起来不是地理坐标，可能需要转换
  if (!isGeographic && !isWebMercator) {
    // 可能是投影坐标，尝试转换为合理的地理坐标
    console.log('检测到可能的投影坐标，进行转换');
    return convertProjectedCoordinates(geojson, minX, minY, maxX, maxY);
  }

  return geojson;
};

// 提取所有坐标
const extractCoordinates = (geometry: any): number[][] => {
  const coords: number[][] = [];

  switch (geometry.type) {
    case 'Point':
      coords.push(geometry.coordinates);
      break;
    case 'LineString':
    case 'MultiPoint':
      coords.push(...geometry.coordinates);
      break;
    case 'Polygon':
    case 'MultiLineString':
      geometry.coordinates.forEach((ring: number[][]) => {
        coords.push(...ring);
      });
      break;
    case 'MultiPolygon':
      geometry.coordinates.forEach((polygon: number[][][]) => {
        polygon.forEach((ring: number[][]) => {
          coords.push(...ring);
        });
      });
      break;
  }

  return coords;
};

// 转换投影坐标
const convertProjectedCoordinates = (geojson: any, minX: number, minY: number, maxX: number, maxY: number) => {
  // 简单的线性转换，将坐标映射到中国区域
  // 这是一个简化的方法，实际项目中应该使用正确的投影转换

  const centerLon = 108.0; // 中国中心经度
  const centerLat = 35.0;  // 中国中心纬度
  const spanLon = 60.0;    // 经度跨度
  const spanLat = 40.0;    // 纬度跨度

  const scaleX = spanLon / (maxX - minX);
  const scaleY = spanLat / (maxY - minY);

  const transformedGeoJSON = JSON.parse(JSON.stringify(geojson));

  transformedGeoJSON.features.forEach((feature: any) => {
    transformGeometry(feature.geometry, minX, minY, scaleX, scaleY, centerLon, centerLat);
  });

  console.log('坐标转换完成');
  return transformedGeoJSON;
};

// 转换几何图形坐标
const transformGeometry = (geometry: any, minX: number, minY: number, scaleX: number, scaleY: number, centerLon: number, centerLat: number) => {
  const transformCoord = (coord: number[]) => {
    const newLon = centerLon + (coord[0] - minX) * scaleX - 30;
    const newLat = centerLat + (coord[1] - minY) * scaleY - 20;
    return [newLon, newLat];
  };

  switch (geometry.type) {
    case 'Point':
      geometry.coordinates = transformCoord(geometry.coordinates);
      break;
    case 'LineString':
    case 'MultiPoint':
      geometry.coordinates = geometry.coordinates.map(transformCoord);
      break;
    case 'Polygon':
    case 'MultiLineString':
      geometry.coordinates = geometry.coordinates.map((ring: number[][]) =>
        ring.map(transformCoord)
      );
      break;
    case 'MultiPolygon':
      geometry.coordinates = geometry.coordinates.map((polygon: number[][][]) =>
        polygon.map((ring: number[][]) => ring.map(transformCoord))
      );
      break;
  }
};

// 创建默认样式
const createDefaultStyle = () => {
  return (feature: any) => {
    const geometry = feature.getGeometry();
    const geometryType = geometry.getType();

    // 根据几何类型创建不同样式
    switch (geometryType) {
      case 'Point':
      case 'MultiPoint':
        return new Style({
          image: new Circle({
            radius: 8,
            fill: new Fill({ color: '#FF6B6B' }),
            stroke: new Stroke({ color: '#ffffff', width: 2 })
          })
        });

      case 'LineString':
      case 'MultiLineString':
        return new Style({
          stroke: new Stroke({
            color: '#4ECDC4',
            width: 3,
            lineDash: [5, 5]
          })
        });

      case 'Polygon':
      case 'MultiPolygon':
        return new Style({
          stroke: new Stroke({
            color: '#45B7D1',
            width: 2
          }),
          fill: new Fill({
            color: 'rgba(69, 183, 209, 0.2)'
          })
        });

      default:
        return new Style({
          image: new Circle({
            radius: 6,
            fill: new Fill({ color: '#409EFF' }),
            stroke: new Stroke({ color: '#ffffff', width: 2 })
          }),
          stroke: new Stroke({ color: '#409EFF', width: 2 }),
          fill: new Fill({ color: 'rgba(64, 158, 255, 0.3)' })
        });
    }
  };
};

// 创建模拟图层（备用方案）
const createMockLayer = async (layerName: string) => {
  // 创建模拟的GeoJSON数据
  const mockGeoJSON = {
    type: 'FeatureCollection',
    features: [
      {
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [108.3, 22.8]
        },
        properties: {
          name: '示例点1',
          type: '测试数据',
          id: 1,
          area: 100.5,
          description: '这是一个示例SHP文件中的点要素',
          创建时间: '2025-01-04',
          状态: '正常'
        }
      },
      {
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          coordinates: [[
            [108.25, 22.75],
            [108.35, 22.75],
            [108.35, 22.85],
            [108.25, 22.85],
            [108.25, 22.75]
          ]]
        },
        properties: {
          name: '示例区域1',
          type: '测试区域',
          id: 2,
          area: 1000.0,
          description: '这是一个示例SHP文件中的面要素',
          创建时间: '2025-01-04',
          状态: '正常'
        }
      }
    ]
  };

  await createLayerFromGeoJSON(mockGeoJSON, layerName);
};

// 选择图层
const selectLayer = (layer: any) => {
  selectedLayer.value = layer;
  disableQuery(); // 切换图层时关闭查询模式
};

// 缩放到图层
const zoomToLayer = (layer: any) => {
  const map = getMap();
  if (map && layer.source) {
    const extent = layer.source.getExtent();
    map.getView().fit(extent, { padding: [50, 50, 50, 50] });
  }
};

// 移除图层
const removeLayer = (layer: any) => {
  ElMessageBox.confirm(
    `确定要移除图层 "${layer.name}" 吗？`,
    '确认移除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const map = getMap();
    if (map) {
      // 从地图移除
      map.removeLayer(layer.layer);
    }

    // 从列表移除
    const index = loadedLayers.value.findIndex(l => l.id === layer.id);
    if (index > -1) {
      loadedLayers.value.splice(index, 1);
    }

    // 如果是当前选中的图层，清除选择
    if (selectedLayer.value?.id === layer.id) {
      selectedLayer.value = null;
      disableQuery();
    }

    ElMessage.success('图层已移除');
  }).catch(() => {
    // 用户取消
  });
};

// 清空所有图层
const clearAllLayers = () => {
  if (loadedLayers.value.length === 0) {
    return;
  }

  ElMessageBox.confirm(
    '确定要清空所有已加载的图层吗？',
    '确认清空',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const map = getMap();
    if (map) {
      // 从地图移除所有图层
      loadedLayers.value.forEach(layer => {
        map.removeLayer(layer.layer);
      });
    }

    // 清空列表
    loadedLayers.value = [];
    selectedLayer.value = null;
    disableQuery();

    ElMessage.success('所有图层已清空');
  }).catch(() => {
    // 用户取消
  });
};

// 切换查询模式
const toggleQuery = () => {
  if (queryMode.value) {
    disableQuery();
  } else {
    enableQuery();
  }
};

// 启用查询模式
const enableQuery = () => {
  const map = getMap();
  if (!map || loadedLayers.value.length === 0) {
    ElMessage.warning('请先加载SHP图层');
    return;
  }

  queryMode.value = true;

  // 创建选择交互，支持所有已加载的图层
  selectInteraction = new Select({
    condition: click,
    layers: loadedLayers.value.map(layer => layer.layer)
  });

  // 监听选择事件
  selectInteraction.on('select', (event) => {
    const features = event.selected;
    if (features.length > 0) {
      showFeatureAttributes(features[0]);
    } else {
      clearSelection();
    }
  });

  map.addInteraction(selectInteraction);
  setStatus('属性查询已启用，点击SHP要素查看属性信息', 'info');
};

// 关闭查询模式
const disableQuery = () => {
  const map = getMap();
  queryMode.value = false;

  if (selectInteraction && map) {
    map.removeInteraction(selectInteraction);
    selectInteraction = null;
  }

  clearSelection();
  setStatus('属性查询已关闭', 'info');
};

// 显示要素属性
const showFeatureAttributes = (feature: any) => {
  selectedFeature.value = feature;

  const properties = feature.getProperties();
  featureAttributes.value = Object.keys(properties)
    .filter(key => key !== 'geometry') // 排除几何属性
    .map(key => ({
      key,
      value: properties[key]
    }));
};

// 清除选择
const clearSelection = () => {
  selectedFeature.value = null;
  featureAttributes.value = [];

  if (selectInteraction) {
    selectInteraction.getFeatures().clear();
  }
};

// 格式化属性值
const formatAttributeValue = (value: any): string => {
  if (value === null || value === undefined) {
    return '空值';
  }

  if (typeof value === 'object') {
    return JSON.stringify(value);
  }

  return String(value);
};

// 设置状态信息
const setStatus = (message: string, type: 'success' | 'warning' | 'info' | 'error') => {
  statusMessage.value = message;
  statusType.value = type;

  // 5秒后清除状态信息
  setTimeout(() => {
    statusMessage.value = '';
  }, 5000);
};

// 清除状态信息
const clearStatus = () => {
  statusMessage.value = '';
};

// 组件销毁时清理
onBeforeUnmount(() => {
  disableQuery();

  const map = getMap();
  if (map) {
    // 移除所有加载的图层
    loadedLayers.value.forEach(layer => {
      map.removeLayer(layer.layer);
    });
  }
});
</script>

<style scoped lang="scss">
.shp-loader-panel {
  padding: 16px;
  height: 100%;
  overflow-y: auto;

  .panel-header {
    margin-bottom: 24px;
    text-align: center;

    h3 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 18px;
      font-weight: 600;
    }

    .panel-description {
      margin: 0;
      color: #606266;
      font-size: 13px;
    }
  }

  .load-section {
    margin-bottom: 24px;
    text-align: center;

    .load-shp-btn {
      width: 100%;
      height: 50px;
      font-size: 16px;
      font-weight: 500;
    }

    .load-description {
      margin: 12px 0 0 0;
      color: #909399;
      font-size: 12px;
    }
  }

  .loaded-layers-section {
    margin-bottom: 24px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      h4 {
        margin: 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }
    }

    .layer-list {
      .layer-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
          background-color: #f0f9ff;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        &.active {
          border-color: #409eff;
          background-color: #e6f7ff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }

        .layer-info {
          display: flex;
          align-items: center;
          gap: 12px;
          flex: 1;

          .layer-icon {
            color: #409eff;
            font-size: 16px;
          }

          .layer-details {
            display: flex;
            flex-direction: column;
            gap: 2px;

            .layer-name {
              font-weight: 500;
              color: #303133;
              font-size: 14px;
            }

            .feature-count {
              color: #909399;
              font-size: 12px;
            }
          }
        }

        .layer-actions {
          display: flex;
          gap: 6px;
        }
      }
    }
  }

  .query-control-section {
    margin-bottom: 24px;

    .section-header {
      margin-bottom: 12px;

      h4 {
        margin: 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }
    }

    .query-toggle-btn {
      width: 100%;
      height: 45px;
      font-size: 15px;
      font-weight: 500;
    }

    .query-tip {
      margin-top: 12px;

      .el-alert {
        --el-alert-padding: 10px 12px;
        --el-alert-title-font-size: 13px;
      }
    }
  }

  .attributes-section {
    margin-bottom: 24px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      h4 {
        margin: 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }
    }

    .attributes-content {
      .attributes-summary {
        margin-bottom: 8px;

        .summary-text {
          color: #606266;
          font-size: 12px;
        }
      }

      .attributes-table {
        border-radius: 6px;
        overflow: hidden;

        :deep(.el-table) {
          font-size: 12px;

          .el-table__header {
            background-color: #f5f7fa;
          }

          .attribute-value {
            word-break: break-all;
          }
        }
      }
    }
  }

  .status-section {
    margin-bottom: 16px;

    .el-alert {
      --el-alert-padding: 10px 12px;
      --el-alert-title-font-size: 13px;
      border-radius: 6px;
    }
  }
}
</style>
