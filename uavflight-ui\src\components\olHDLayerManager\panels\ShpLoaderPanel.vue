<!--
 * @Author: AI Assistant
 * @Date: 2025-01-08
 * @Description: SHP文件加载器面板
 *
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved.
-->
<template>
  <div class="shp-loader-panel">
    <div class="panel-header">
      <h3>SHP文件加载器</h3>
      <p class="panel-description">加载本地SHP文件到地图中进行查看和属性查询</p>
    </div>

    <div class="panel-content">
      <!-- 文件上传区域 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          class="shp-upload"
          drag
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleFileChange"
          accept=".shp,.dbf,.shx,.prj,.cpg"
          multiple
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将SHP文件拖拽到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              请选择完整的SHP文件集合（.shp, .dbf, .shx等）
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 文件列表 -->
      <div v-if="uploadedFiles.length > 0" class="file-list-section">
        <h4>已选择文件：</h4>
        <div class="file-list">
          <div v-for="file in uploadedFiles" :key="file.name" class="file-item">
            <el-icon><Document /></el-icon>
            <span class="file-name">{{ file.name }}</span>
            <span class="file-size">{{ formatFileSize(file.size) }}</span>
            <el-button
              type="danger"
              size="small"
              circle
              :icon="Delete"
              @click="removeFile(file)"
            />
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!hasValidShpFiles"
          @click="loadShpToMap"
        >
          <el-icon><MapLocation /></el-icon>
          加载到地图
        </el-button>
        <el-button
          type="info"
          @click="debugFileStatus"
        >
          调试信息
        </el-button>
        <el-button
          v-if="loadedLayers.length > 0"
          type="warning"
          @click="clearAllLayers"
        >
          <el-icon><Delete /></el-icon>
          清除所有图层
        </el-button>
      </div>

      <!-- 已加载图层列表 -->
      <div v-if="loadedLayers.length > 0" class="loaded-layers-section">
        <h4>已加载图层：</h4>
        <div class="layer-list">
          <div v-for="layer in loadedLayers" :key="layer.id" class="layer-item">
            <div class="layer-info">
              <el-icon><Files /></el-icon>
              <span class="layer-name">{{ layer.name }}</span>
              <el-tag size="small" type="info">{{ layer.featureCount }} 个要素</el-tag>
            </div>
            <div class="layer-actions">
              <el-button
                size="small"
                type="primary"
                @click="zoomToLayer(layer)"
              >
                定位
              </el-button>
              <el-button
                size="small"
                type="info"
                @click="toggleLayerVisibility(layer)"
              >
                {{ layer.visible ? '隐藏' : '显示' }}
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="removeLayer(layer)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-status">
        <el-progress :percentage="loadingProgress" :status="loadingStatus" />
        <p class="loading-text">{{ loadingText }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  UploadFilled,
  Document,
  Delete,
  MapLocation,
  Files
} from '@element-plus/icons-vue';
import * as shapefile from 'shapefile';
import { Vector as VectorLayer } from 'ol/layer';
import { Vector as VectorSource } from 'ol/source';
import { GeoJSON } from 'ol/format';
import { Style, Stroke, Fill, Circle } from 'ol/style';
import commonFunction from '/@/utils/commonFunction';

// 注入地图实例
const map = inject('map') as any;

// 获取通用函数
const { getRandomColor } = commonFunction();

// 调试地图注入
console.log('🗺️ SHP加载器初始化:', {
  地图实例: map,
  地图类型: typeof map,
  是否为响应式: map?.value !== undefined,
  实际地图对象: map?.value || map
});

// 响应式数据
const uploadRef = ref();
const uploadedFiles = ref<File[]>([]);
const loadedLayers = ref<any[]>([]);
const loading = ref(false);
const loadingProgress = ref(0);
const loadingStatus = ref<'success' | 'exception' | undefined>(undefined);
const loadingText = ref('');

// 计算属性
const hasValidShpFiles = computed(() => {
  const hasShp = uploadedFiles.value.some(file => file.name.toLowerCase().endsWith('.shp'));
  console.log('🔍 文件验证:', {
    上传文件数量: uploadedFiles.value.length,
    文件列表: uploadedFiles.value.map(f => f.name),
    是否包含SHP: hasShp,
    验证结果: hasShp ? '通过' : '失败'
  });
  return hasShp;
});

// 文件处理
const handleFileChange = (file: any) => {
  const newFile = file.raw;

  console.log('📁 接收到文件:', {
    文件名: newFile.name,
    文件大小: newFile.size,
    文件类型: newFile.type,
    最后修改: new Date(newFile.lastModified).toLocaleString(),
    文件对象: newFile
  });

  // 检查文件扩展名
  const fileName = newFile.name.toLowerCase();
  const validExtensions = ['.shp', '.dbf', '.shx', '.prj', '.cpg'];
  const isValidExtension = validExtensions.some(ext => fileName.endsWith(ext));

  if (!isValidExtension) {
    console.warn('⚠️ 不支持的文件类型:', newFile.name);
    ElMessage.warning(`不支持的文件类型: ${newFile.name}`);
    return;
  }

  // 检查文件是否已存在
  const exists = uploadedFiles.value.some(f => f.name === newFile.name);
  if (exists) {
    console.warn('⚠️ 文件已存在:', newFile.name);
    ElMessage.warning(`文件 ${newFile.name} 已存在`);
    return;
  }

  uploadedFiles.value.push(newFile);
  console.log('✅ 文件添加成功:', {
    文件名: newFile.name,
    文件大小: `${(newFile.size / 1024).toFixed(2)} KB`,
    当前文件总数: uploadedFiles.value.length,
    所有文件: uploadedFiles.value.map(f => f.name)
  });

  // 检查是否有SHP文件
  const hasShp = uploadedFiles.value.some(f => f.name.toLowerCase().endsWith('.shp'));
  console.log('🔍 SHP文件检查:', {
    是否包含SHP文件: hasShp,
    按钮状态: hasShp ? '可用' : '禁用'
  });
};

const removeFile = (fileToRemove: File) => {
  const index = uploadedFiles.value.findIndex(f => f.name === fileToRemove.name);
  if (index > -1) {
    uploadedFiles.value.splice(index, 1);
    console.log('移除文件:', fileToRemove.name);
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取实际的地图对象
const getMapInstance = () => {
  return map?.value || map;
};

// SHP文件加载
const loadShpToMap = async () => {
  const mapInstance = getMapInstance();

  console.log('🚀 开始加载SHP文件到地图...');
  console.log('📋 当前状态检查:', {
    地图对象: map ? '已注入' : '未注入',
    地图实例: mapInstance ? '已获取' : '未获取',
    地图类型: typeof mapInstance,
    上传文件数量: uploadedFiles.value.length,
    文件列表: uploadedFiles.value.map(f => ({ 名称: f.name, 大小: f.size })),
    SHP文件验证: hasValidShpFiles.value,
    按钮状态: hasValidShpFiles.value ? '可用' : '禁用'
  });

  if (!mapInstance) {
    console.error('❌ 地图对象未注入或未初始化');
    ElMessage.error('地图对象未初始化，请确保地图已加载');
    return;
  }

  if (!hasValidShpFiles.value) {
    console.error('❌ 没有有效的SHP文件');
    console.log('📁 当前文件详情:', uploadedFiles.value.map(f => ({
      文件名: f.name,
      扩展名: f.name.split('.').pop(),
      是否为SHP: f.name.toLowerCase().endsWith('.shp')
    })));
    ElMessage.error('请先选择有效的SHP文件');
    return;
  }

  loading.value = true;
  loadingProgress.value = 0;
  loadingStatus.value = undefined;
  loadingText.value = '正在读取SHP文件...';

  try {
    // 查找SHP文件
    const shpFile = uploadedFiles.value.find(f => f.name.endsWith('.shp'));
    const dbfFile = uploadedFiles.value.find(f => f.name.replace('.dbf', '') === shpFile?.name.replace('.shp', ''));

    if (!shpFile) {
      throw new Error('未找到SHP文件');
    }

    console.log('开始处理SHP文件:', {
      shpFile: shpFile.name,
      dbfFile: dbfFile?.name || '未找到',
      totalFiles: uploadedFiles.value.length
    });

    loadingProgress.value = 20;
    loadingText.value = '正在解析SHP文件...';

    // 读取文件为ArrayBuffer
    const shpBuffer = await readFileAsArrayBuffer(shpFile);
    const dbfBuffer = dbfFile ? await readFileAsArrayBuffer(dbfFile) : undefined;

    loadingProgress.value = 40;
    loadingText.value = '正在转换为GeoJSON...';

    // 使用shapefile库转换为GeoJSON
    const geojson = await convertShpToGeoJSON(shpBuffer, dbfBuffer);

    console.log('SHP转换完成:', {
      type: geojson.type,
      features: geojson.features?.length || 0,
      crs: geojson.crs,
      bbox: geojson.bbox
    });

    // 详细输出GeoJSON信息
    console.group(`📁 SHP文件 "${shpFile.name}" 转换结果`);
    console.log('🔄 完整的GeoJSON对象:', geojson);
    console.log('📊 GeoJSON统计信息:', {
      类型: geojson.type,
      要素数量: geojson.features?.length || 0,
      坐标系: geojson.crs || '未指定',
      边界框: geojson.bbox || '未计算'
    });

    // 输出前几个要素的详细信息
    if (geojson.features && geojson.features.length > 0) {
      console.log('🎯 前3个要素详情:');
      geojson.features.slice(0, 3).forEach((feature: any, index: number) => {
        console.log(`要素 ${index + 1}:`, {
          几何类型: feature.geometry?.type,
          属性数量: Object.keys(feature.properties || {}).length,
          属性字段: Object.keys(feature.properties || {}),
          几何坐标: feature.geometry?.coordinates ? '已包含' : '缺失',
          完整要素: feature
        });
      });

      // 如果要素很多，显示省略信息
      if (geojson.features.length > 3) {
        console.log(`... 还有 ${geojson.features.length - 3} 个要素未显示`);
      }
    }

    // 输出属性字段统计
    if (geojson.features && geojson.features.length > 0) {
      const allProperties = new Set();
      geojson.features.forEach((feature: any) => {
        if (feature.properties) {
          Object.keys(feature.properties).forEach(key => allProperties.add(key));
        }
      });
      console.log('📋 所有属性字段:', Array.from(allProperties));
    }

    console.groupEnd();

    loadingProgress.value = 60;
    loadingText.value = '正在添加到地图...';

    // 添加到地图
    await addGeoJSONToMap(geojson, shpFile.name);

    loadingProgress.value = 100;
    loadingStatus.value = 'success';
    loadingText.value = '加载完成！';

    ElMessage.success(`成功加载SHP文件: ${shpFile.name}`);

  } catch (error) {
    console.error('加载SHP文件失败:', error);
    loadingStatus.value = 'exception';
    loadingText.value = '加载失败';
    ElMessage.error(`加载失败: ${error instanceof Error ? error.message : '未知错误'}`);
  } finally {
    setTimeout(() => {
      loading.value = false;
      loadingProgress.value = 0;
      loadingStatus.value = undefined;
      loadingText.value = '';
    }, 2000);
  }
};

// 辅助函数
const readFileAsArrayBuffer = (file: File): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as ArrayBuffer);
    reader.onerror = () => reject(new Error('文件读取失败'));
    reader.readAsArrayBuffer(file);
  });
};

const convertShpToGeoJSON = async (shpBuffer: ArrayBuffer, dbfBuffer?: ArrayBuffer): Promise<any> => {
  const features: any[] = [];

  console.log('🔄 开始使用shapefile库解析...');
  console.log('📁 文件信息:', {
    'SHP文件大小': `${(shpBuffer.byteLength / 1024).toFixed(2)} KB`,
    'DBF文件大小': dbfBuffer ? `${(dbfBuffer.byteLength / 1024).toFixed(2)} KB` : '未提供',
    '是否包含属性': dbfBuffer ? '是' : '否'
  });

  // 使用shapefile库读取
  const source = shapefile.open(shpBuffer, dbfBuffer);

  let result;
  let featureCount = 0;
  while (!(result = await source.read()).done) {
    if (result.value) {
      featureCount++;
      features.push(result.value);

      // 每读取10个要素输出一次进度，避免控制台刷屏
      if (featureCount <= 5 || featureCount % 10 === 0) {
        console.log(`📍 读取要素 ${featureCount}:`, {
          几何类型: result.value.geometry?.type,
          属性字段数: Object.keys(result.value.properties || {}).length,
          坐标数量: result.value.geometry?.coordinates ?
            (Array.isArray(result.value.geometry.coordinates[0]) ?
              result.value.geometry.coordinates.length : 1) : 0
        });

        // 只输出前3个要素的完整信息
        if (featureCount <= 3) {
          console.log(`要素 ${featureCount} 详细信息:`, result.value);
        }
      }
    }
  }

  const geojson = {
    type: 'FeatureCollection',
    features: features
  };

  console.log('✅ shapefile库解析完成!', {
    总要素数: features.length,
    解析状态: '成功',
    数据完整性: features.length > 0 ? '正常' : '无数据'
  });

  return geojson;
};

const addGeoJSONToMap = async (geojson: any, fileName: string): Promise<void> => {
  const mapInstance = getMapInstance();

  if (!mapInstance) {
    throw new Error('地图实例不可用');
  }

  // 创建矢量数据源
  const vectorSource = new VectorSource({
    features: new GeoJSON().readFeatures(geojson, {
      featureProjection: mapInstance.getView().getProjection()
    })
  });

  // 创建样式
  const style = new Style({
    stroke: new Stroke({
      color: getRandomColor(),
      width: 2
    }),
    fill: new Fill({
      color: getRandomColor() + '20' // 添加透明度
    }),
    image: new Circle({
      radius: 6,
      fill: new Fill({
        color: getRandomColor()
      }),
      stroke: new Stroke({
        color: '#fff',
        width: 2
      })
    })
  });

  // 创建矢量图层
  const vectorLayer = new VectorLayer({
    source: vectorSource,
    style: style
  });

  // 添加到地图
  mapInstance.addLayer(vectorLayer);

  // 记录已加载的图层
  const layerInfo = {
    id: Date.now().toString(),
    name: fileName.replace('.shp', ''),
    layer: vectorLayer,
    source: vectorSource,
    featureCount: vectorSource.getFeatures().length,
    visible: true,
    geojson: geojson
  };

  loadedLayers.value.push(layerInfo);

  console.log('🗺️ 图层已成功添加到地图:', {
    图层名称: layerInfo.name,
    要素数量: layerInfo.featureCount,
    图层ID: layerInfo.id,
    可见状态: layerInfo.visible,
    图层对象: layerInfo.layer,
    数据源: layerInfo.source
  });

  console.log('📍 图层范围信息:', {
    边界框: layerInfo.source.getExtent(),
    投影: mapInstance.getView().getProjection().getCode()
  });
};

// 图层管理
const zoomToLayer = (layerInfo: any) => {
  const mapInstance = getMapInstance();
  if (layerInfo.source && mapInstance) {
    const extent = layerInfo.source.getExtent();
    mapInstance.getView().fit(extent, { padding: [20, 20, 20, 20] });
    console.log('定位到图层:', layerInfo.name);
  }
};

const toggleLayerVisibility = (layerInfo: any) => {
  if (layerInfo.layer) {
    layerInfo.visible = !layerInfo.visible;
    layerInfo.layer.setVisible(layerInfo.visible);
    console.log('切换图层可见性:', layerInfo.name, layerInfo.visible);
  }
};

const removeLayer = (layerInfo: any) => {
  ElMessageBox.confirm(
    `确定要删除图层 "${layerInfo.name}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const mapInstance = getMapInstance();
    if (layerInfo.layer && mapInstance) {
      mapInstance.removeLayer(layerInfo.layer);
    }

    const index = loadedLayers.value.findIndex(l => l.id === layerInfo.id);
    if (index > -1) {
      loadedLayers.value.splice(index, 1);
    }

    console.log('删除图层:', layerInfo.name);
    ElMessage.success('图层已删除');
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

const clearAllLayers = () => {
  ElMessageBox.confirm(
    '确定要清除所有已加载的SHP图层吗？',
    '确认清除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const mapInstance = getMapInstance();
    loadedLayers.value.forEach(layerInfo => {
      if (layerInfo.layer && mapInstance) {
        mapInstance.removeLayer(layerInfo.layer);
      }
    });

    loadedLayers.value = [];
    uploadedFiles.value = [];

    console.log('清除所有SHP图层');
    ElMessage.success('所有图层已清除');
  }).catch(() => {
    ElMessage.info('已取消清除');
  });
};

// 调试函数
const debugFileStatus = () => {
  console.group('🔍 SHP加载器调试信息');
  console.log('📁 上传文件状态:', {
    文件数量: uploadedFiles.value.length,
    文件详情: uploadedFiles.value.map(f => ({
      文件名: f.name,
      文件大小: f.size,
      文件类型: f.type,
      扩展名: f.name.split('.').pop()?.toLowerCase(),
      是否为SHP: f.name.toLowerCase().endsWith('.shp'),
      文件对象: f
    }))
  });

  console.log('✅ 验证结果:', {
    hasValidShpFiles: hasValidShpFiles.value,
    SHP文件列表: uploadedFiles.value.filter(f => f.name.toLowerCase().endsWith('.shp')).map(f => f.name),
    DBF文件列表: uploadedFiles.value.filter(f => f.name.toLowerCase().endsWith('.dbf')).map(f => f.name),
    其他文件: uploadedFiles.value.filter(f => !f.name.toLowerCase().endsWith('.shp') && !f.name.toLowerCase().endsWith('.dbf')).map(f => f.name)
  });

  const mapInstance = getMapInstance();
  console.log('🗺️ 地图状态:', {
    注入的地图对象: map,
    实际地图实例: mapInstance,
    地图是否存在: !!mapInstance,
    地图投影: mapInstance ? mapInstance.getView().getProjection().getCode() : '未知',
    地图图层数量: mapInstance ? mapInstance.getLayers().getLength() : 0
  });

  console.log('🎛️ 组件状态:', {
    loading: loading.value,
    loadingProgress: loadingProgress.value,
    loadingText: loadingText.value,
    loadedLayers: loadedLayers.value.length
  });

  console.groupEnd();

  ElMessage.info('调试信息已输出到控制台，请查看Console标签页');
};
</script>

<style scoped lang="scss">
.shp-loader-panel {
  padding: 20px;
  height: 100%;
  overflow-y: auto;

  .panel-header {
    margin-bottom: 20px;

    h3 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 18px;
      font-weight: 600;
    }

    .panel-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .panel-content {
    .upload-section {
      margin-bottom: 20px;

      .shp-upload {
        :deep(.el-upload) {
          width: 100%;
        }

        :deep(.el-upload-dragger) {
          width: 100%;
          height: 120px;
          border: 2px dashed #d9d9d9;
          border-radius: 6px;
          cursor: pointer;
          position: relative;
          overflow: hidden;
          transition: border-color 0.3s;

          &:hover {
            border-color: #409EFF;
          }
        }

        :deep(.el-icon--upload) {
          font-size: 28px;
          color: #8c939d;
          margin: 20px 0 16px;
        }

        :deep(.el-upload__text) {
          color: #606266;
          font-size: 14px;
          text-align: center;

          em {
            color: #409EFF;
            font-style: normal;
          }
        }

        :deep(.el-upload__tip) {
          font-size: 12px;
          color: #909399;
          margin-top: 7px;
        }
      }
    }

    .file-list-section {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }

      .file-list {
        border: 1px solid #EBEEF5;
        border-radius: 4px;

        .file-item {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          border-bottom: 1px solid #EBEEF5;

          &:last-child {
            border-bottom: none;
          }

          .el-icon {
            margin-right: 8px;
            color: #909399;
          }

          .file-name {
            flex: 1;
            font-size: 14px;
            color: #303133;
          }

          .file-size {
            margin-right: 8px;
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }

    .action-buttons {
      margin-bottom: 20px;
      display: flex;
      gap: 12px;

      .el-button {
        flex: 1;
      }
    }

    .loaded-layers-section {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }

      .layer-list {
        border: 1px solid #EBEEF5;
        border-radius: 4px;

        .layer-item {
          padding: 12px;
          border-bottom: 1px solid #EBEEF5;

          &:last-child {
            border-bottom: none;
          }

          .layer-info {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .el-icon {
              margin-right: 8px;
              color: #409EFF;
            }

            .layer-name {
              flex: 1;
              font-size: 14px;
              color: #303133;
              font-weight: 500;
            }

            .el-tag {
              margin-left: 8px;
            }
          }

          .layer-actions {
            display: flex;
            gap: 8px;

            .el-button {
              flex: 1;
            }
          }
        }
      }
    }

    .loading-status {
      margin-top: 20px;

      .el-progress {
        margin-bottom: 12px;
      }

      .loading-text {
        margin: 0;
        text-align: center;
        font-size: 14px;
        color: #606266;
      }
    }
  }
}
</style>