<!--
 * @Author: AI Assistant
 * @Date: 2025-01-08
 * @Description: SHP文件加载器面板
 *
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved.
-->
<template>
  <div class="shp-loader-panel">
    <div class="panel-header">
      <h3>SHP文件加载器</h3>
      <p class="panel-description">加载本地SHP文件到地图中进行查看和属性查询</p>
    </div>

    <div class="panel-content">
      <!-- 文件上传区域 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          class="shp-upload"
          drag
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleFileChange"
          accept=".shp"
          :multiple="false"
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将SHP文件拖拽到此处，或<em>点击选择SHP文件</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只需选择.shp文件，系统会自动查找同目录下的相关文件（.dbf, .prj, .shx等）
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 手动选择相关文件 -->
      <div v-if="showManualFileSelection" class="manual-file-selection">
        <el-alert
          :title="`为 ${currentBaseName}.shp 选择相关文件`"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>建议选择以下文件以获得最佳效果：</p>
            <ul>
              <li><strong>{{ currentBaseName }}.prj</strong> - 坐标系信息（重要）</li>
              <li><strong>{{ currentBaseName }}.dbf</strong> - 属性数据</li>
              <li><strong>{{ currentBaseName }}.shx</strong> - 索引文件</li>
            </ul>
          </template>
        </el-alert>

        <div class="manual-file-buttons">
          <input
            ref="manualFileInputRef"
            type="file"
            multiple
            accept=".dbf,.prj,.shx,.cpg"
            @change="handleManualFileSelection"
            style="display: none"
          />
          <el-button @click="selectManualFiles" type="primary">
            选择相关文件
          </el-button>
          <el-button @click="skipManualSelection" type="info" plain>
            跳过，直接加载
          </el-button>
        </div>
      </div>

      <!-- 文件列表 -->
      <div v-if="uploadedFiles.length > 0" class="file-list-section">
        <h4>已选择文件：</h4>
        <div class="file-list">
          <div v-for="file in uploadedFiles" :key="file.name" class="file-item">
            <el-icon><Document /></el-icon>
            <span class="file-name">{{ file.name }}</span>
            <span class="file-size">{{ formatFileSize(file.size) }}</span>
            <el-button
              type="danger"
              size="small"
              circle
              :icon="Delete"
              @click="removeFile(file)"
            />
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!hasValidShpFiles"
          @click="loadShpToMap"
        >
          <el-icon><MapLocation /></el-icon>
          加载到地图
        </el-button>
        <el-button
          type="info"
          @click="debugFileStatus"
        >
          调试信息
        </el-button>
        <el-button
          type="success"
          size="small"
          @click="testCoordinateTransform"
        >
          测试转换
        </el-button>
        <el-button
          v-if="loadedLayers.length > 0"
          type="warning"
          @click="clearAllLayers"
        >
          <el-icon><Delete /></el-icon>
          清除所有图层
        </el-button>
        <el-button
          v-if="uploadedFiles.length > 0"
          type="danger"
          size="small"
          @click="forceCGCS2000Conversion"
        >
          强制CGCS2000转换
        </el-button>
      </div>

      <!-- 已加载图层列表 -->
      <div v-if="loadedLayers.length > 0" class="loaded-layers-section">
        <h4>已加载图层：</h4>
        <div class="layer-list">
          <div v-for="layer in loadedLayers" :key="layer.id" class="layer-item">
            <div class="layer-info">
              <el-icon><Files /></el-icon>
              <span class="layer-name">{{ layer.name }}</span>
              <el-tag size="small" type="info">{{ layer.featureCount }} 个要素</el-tag>
              <el-tag
                v-if="layer.originalCRS && layer.originalCRS !== 'EPSG:4326'"
                size="small"
                type="warning"
              >
                {{ layer.originalCRS }} → WGS84
              </el-tag>
              <el-tag
                v-else-if="layer.originalCRS === 'EPSG:4326'"
                size="small"
                type="success"
              >
                WGS84
              </el-tag>
            </div>
            <div class="layer-actions">
              <el-button
                size="small"
                type="primary"
                @click="zoomToLayer(layer)"
              >
                定位
              </el-button>
              <el-button
                size="small"
                type="info"
                @click="toggleLayerVisibility(layer)"
              >
                {{ layer.visible ? '隐藏' : '显示' }}
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="removeLayer(layer)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-status">
        <el-progress :percentage="loadingProgress" :status="loadingStatus" />
        <p class="loading-text">{{ loadingText }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  UploadFilled,
  Document,
  Delete,
  MapLocation,
  Files
} from '@element-plus/icons-vue';
import * as shapefile from 'shapefile';
import { Vector as VectorLayer } from 'ol/layer';
import { Vector as VectorSource } from 'ol/source';
import { GeoJSON } from 'ol/format';
import { Style, Stroke, Fill, Circle } from 'ol/style';
import { transform } from 'ol/proj';
import { register } from 'ol/proj/proj4';
// @ts-ignore
import proj4 from 'proj4';
import commonFunction from '/@/utils/commonFunction';

// 注入地图实例
const map = inject('map') as any;

// 获取通用函数
const { getRandomColor } = commonFunction();

// 常用坐标系定义
const commonProjections = {
  // 中国常用坐标系
  'EPSG:4490': '+proj=longlat +ellps=GRS80 +no_defs +type=crs', // CGCS2000
  'EPSG:4214': '+proj=longlat +ellps=krass +no_defs +type=crs', // Beijing 1954
  'EPSG:4610': '+proj=longlat +ellps=krass +no_defs +type=crs', // Xian 1980
  'EPSG:3857': '+proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +wktext +no_defs +type=crs', // Web Mercator

  // UTM 投影 (中国区域) - WGS84 基准
  'EPSG:32648': '+proj=utm +zone=48 +ellps=WGS84 +datum=WGS84 +units=m +no_defs +type=crs', // UTM Zone 48N
  'EPSG:32649': '+proj=utm +zone=49 +ellps=WGS84 +datum=WGS84 +units=m +no_defs +type=crs', // UTM Zone 49N
  'EPSG:32650': '+proj=utm +zone=50 +ellps=WGS84 +datum=WGS84 +units=m +no_defs +type=crs', // UTM Zone 50N
  'EPSG:32651': '+proj=utm +zone=51 +ellps=WGS84 +datum=WGS84 +units=m +no_defs +type=crs', // UTM Zone 51N

  // 高斯-克吕格投影
  'EPSG:2433': '+proj=tmerc +lat_0=0 +lon_0=75 +k=1 +x_0=500000 +y_0=0 +ellps=krass +units=m +no_defs +type=crs', // Pulkovo 1942 / Gauss-Kruger zone 13
  'EPSG:2434': '+proj=tmerc +lat_0=0 +lon_0=81 +k=1 +x_0=500000 +y_0=0 +ellps=krass +units=m +no_defs +type=crs', // Pulkovo 1942 / Gauss-Kruger zone 14
  'EPSG:2435': '+proj=tmerc +lat_0=0 +lon_0=87 +k=1 +x_0=500000 +y_0=0 +ellps=krass +units=m +no_defs +type=crs', // Pulkovo 1942 / Gauss-Kruger zone 15

  // 其他常用投影
  'EPSG:4547': '+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs +type=crs', // CGCS2000 / 3-degree Gauss-Kruger zone 40
  'EPSG:4548': '+proj=tmerc +lat_0=0 +lon_0=123 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs +type=crs', // CGCS2000 / 3-degree Gauss-Kruger zone 41

  // CGCS2000 3度分带高斯-克吕格投影
  'EPSG:4525': '+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=37500000 +y_0=0 +ellps=GRS80 +datum=CGCS2000 +units=m +no_defs +type=crs', // CGCS2000 / 3-degree Gauss-Kruger zone 37
};



// 响应式数据
const uploadRef = ref();
const uploadedFiles = ref<File[]>([]);
const loadedLayers = ref<any[]>([]);
const loading = ref(false);
const loadingProgress = ref(0);
const loadingStatus = ref<'success' | 'exception' | undefined>(undefined);
const loadingText = ref('');
const showManualFileSelection = ref(false);
const currentBaseName = ref('');
const manualFileInputRef = ref();

// 计算属性
const hasValidShpFiles = computed(() => {
  const hasShp = uploadedFiles.value.some(file => file.name.toLowerCase().endsWith('.shp'));
  console.log('🔍 文件验证:', {
    上传文件数量: uploadedFiles.value.length,
    文件列表: uploadedFiles.value.map(f => f.name),
    是否包含SHP: hasShp,
    验证结果: hasShp ? '通过' : '失败'
  });
  return hasShp;
});

// SHP 文件处理 - 只需要选择 SHP 文件
const handleFileChange = async (file: any) => {
  const shpFile = file.raw;

  console.log('📁 接收到SHP文件:', {
    文件名: shpFile.name,
    文件大小: shpFile.size,
    文件类型: shpFile.type,
    最后修改: new Date(shpFile.lastModified).toLocaleString()
  });

  // 验证是否为 SHP 文件
  if (!shpFile.name.toLowerCase().endsWith('.shp')) {
    ElMessage.error('请选择 .shp 文件');
    return;
  }

  // 清除之前的文件
  uploadedFiles.value = [];

  // 添加 SHP 文件
  uploadedFiles.value.push(shpFile);

  // 尝试自动查找同目录下的相关文件
  await findRelatedFiles(shpFile);

  console.log('✅ SHP文件处理完成:', {
    主文件: shpFile.name,
    总文件数: uploadedFiles.value.length,
    文件列表: uploadedFiles.value.map(f => f.name)
  });
};

// 自动查找同目录下的相关文件
const findRelatedFiles = async (shpFile: File) => {
  const baseName = shpFile.name.replace(/\.shp$/i, '');
  const relatedExtensions = ['dbf', 'prj', 'shx', 'cpg'];

  console.log(`🔍 正在查找 "${baseName}" 的相关文件...`);

  // 注意：由于浏览器安全限制，我们无法直接访问文件系统
  // 这里我们需要提示用户手动选择相关文件，或者使用 File System Access API（如果支持）

  if ('showDirectoryPicker' in window) {
    // 使用现代浏览器的 File System Access API
    try {
      await findRelatedFilesWithFSAPI(baseName, relatedExtensions);
    } catch (error) {
      console.log('⚠️ 无法自动查找相关文件，请手动选择完整的文件集');
      promptForRelatedFiles(baseName);
    }
  } else {
    // 降级方案：提示用户手动选择
    promptForRelatedFiles(baseName);
  }
};

// 使用 File System Access API 查找相关文件
const findRelatedFilesWithFSAPI = async (baseName: string, extensions: string[]) => {
  try {
    // 请求访问目录
    const dirHandle = await (window as any).showDirectoryPicker();

    console.log('📂 获得目录访问权限，正在查找相关文件...');

    for (const ext of extensions) {
      const fileName = `${baseName}.${ext}`;
      try {
        const fileHandle = await dirHandle.getFileHandle(fileName);
        const file = await fileHandle.getFile();

        uploadedFiles.value.push(file);
        console.log(`✅ 找到相关文件: ${fileName}`);
      } catch (error) {
        console.log(`⚠️ 未找到文件: ${fileName}`);
      }
    }

    // 检查文件完整性
    checkFoundFiles(baseName);

  } catch (error) {
    console.error('❌ 目录访问失败:', error);
    throw error;
  }
};

// 提示用户手动选择相关文件
const promptForRelatedFiles = (baseName: string) => {
  ElMessage({
    message: `已选择 ${baseName}.shp，建议您也选择同名的 .prj, .dbf, .shx 文件以获得最佳效果`,
    type: 'info',
    duration: 5000
  });

  // 显示手动选择按钮
  showManualFileSelection.value = true;
  currentBaseName.value = baseName;
};

// 检查找到的文件
const checkFoundFiles = (baseName: string) => {
  const foundFiles = {
    shp: uploadedFiles.value.find(f => f.name.toLowerCase().endsWith('.shp')),
    dbf: uploadedFiles.value.find(f => f.name.toLowerCase().endsWith('.dbf')),
    prj: uploadedFiles.value.find(f => f.name.toLowerCase().endsWith('.prj')),
    shx: uploadedFiles.value.find(f => f.name.toLowerCase().endsWith('.shx')),
    cpg: uploadedFiles.value.find(f => f.name.toLowerCase().endsWith('.cpg'))
  };

  console.log(`📋 文件集 "${baseName}" 完整性检查:`, {
    SHP文件: foundFiles.shp ? '✅' : '❌',
    DBF文件: foundFiles.dbf ? '✅' : '⚠️ 未找到',
    PRJ文件: foundFiles.prj ? '✅' : '⚠️ 未找到（坐标系信息缺失）',
    SHX文件: foundFiles.shx ? '✅' : '⚠️ 未找到',
    CPG文件: foundFiles.cpg ? '✅' : '⚠️ 未找到'
  });

  if (!foundFiles.prj) {
    ElMessage.warning('未找到 PRJ 文件，将使用坐标范围推测坐标系，可能不够准确');
  }

  if (!foundFiles.dbf) {
    ElMessage.info('未找到 DBF 文件，将无法显示属性信息');
  }
};

// 手动文件选择相关函数
const selectManualFiles = () => {
  manualFileInputRef.value?.click();
};

const handleManualFileSelection = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files) {
    Array.from(target.files).forEach(file => {
      // 验证文件名是否匹配当前基础名称
      const baseName = file.name.replace(/\.(dbf|prj|shx|cpg)$/i, '');
      if (baseName === currentBaseName.value) {
        uploadedFiles.value.push(file);
        console.log(`✅ 手动添加相关文件: ${file.name}`);
      } else {
        console.warn(`⚠️ 文件名不匹配: ${file.name} (期望: ${currentBaseName.value}.*)`);
        ElMessage.warning(`文件名不匹配: ${file.name}`);
      }
    });

    // 重新检查文件完整性
    checkFoundFiles(currentBaseName.value);

    // 隐藏手动选择界面
    showManualFileSelection.value = false;
  }
};

const skipManualSelection = () => {
  showManualFileSelection.value = false;
  ElMessage.info('已跳过相关文件选择，将仅使用 SHP 文件进行加载');
};

const removeFile = (fileToRemove: File) => {
  const index = uploadedFiles.value.findIndex(f => f.name === fileToRemove.name);
  if (index > -1) {
    uploadedFiles.value.splice(index, 1);
    console.log('移除文件:', fileToRemove.name);
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取实际的地图对象
const getMapInstance = () => {
  // map 是通过 provide/inject 传递的响应式对象
  const mapInstance = map?.value || map;

  console.log('🔍 获取地图实例:', {
    原始map对象: map,
    map类型: typeof map,
    mapValue: map?.value,
    mapValue类型: typeof map?.value,
    最终实例: mapInstance,
    最终实例类型: typeof mapInstance,
    是否有getView方法: mapInstance && typeof mapInstance.getView === 'function',
    是否有addLayer方法: mapInstance && typeof mapInstance.addLayer === 'function',
    实例的所有方法: mapInstance ? Object.getOwnPropertyNames(Object.getPrototypeOf(mapInstance)).slice(0, 10) : '无'
  });

  // 验证地图实例是否有效
  if (!mapInstance || typeof mapInstance.getView !== 'function') {
    console.error('❌ 无效的地图实例:', mapInstance);
    return null;
  }

  return mapInstance;
};

// 调试地图注入
console.log('🗺️ SHP加载器初始化:', {
  地图实例: map,
  地图类型: typeof map,
  是否为响应式: map?.value !== undefined,
  实际地图对象: map?.value || map,
  地图验证: getMapInstance() ? '有效' : '无效'
});

// 坐标系检测和转换功能
const detectCoordinateSystem = (geojson: any): string => {
  if (!geojson.features || geojson.features.length === 0) {
    return 'EPSG:4326'; // 默认返回 WGS84
  }

  // 获取第一个要素的坐标
  const firstFeature = geojson.features[0];
  if (!firstFeature.geometry || !firstFeature.geometry.coordinates) {
    return 'EPSG:4326';
  }

  // 获取坐标范围
  let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;

  const extractCoords = (coords: any) => {
    if (typeof coords[0] === 'number') {
      // 单个坐标点
      minX = Math.min(minX, coords[0]);
      maxX = Math.max(maxX, coords[0]);
      minY = Math.min(minY, coords[1]);
      maxY = Math.max(maxY, coords[1]);
    } else {
      // 嵌套数组
      coords.forEach((coord: any) => extractCoords(coord));
    }
  };

  // 分析前几个要素的坐标范围
  geojson.features.slice(0, Math.min(10, geojson.features.length)).forEach((feature: any) => {
    if (feature.geometry && feature.geometry.coordinates) {
      extractCoords(feature.geometry.coordinates);
    }
  });

  console.log('📍 坐标范围分析:', {
    X范围: [minX, maxX],
    Y范围: [minY, maxY],
    X跨度: maxX - minX,
    Y跨度: maxY - minY,
    坐标量级: {
      X最小值: Math.abs(minX),
      Y最小值: Math.abs(minY),
      是否大坐标: Math.abs(minX) > 1000 || Math.abs(minY) > 1000
    },
    可能的坐标系判断: {
      是否UTM: Math.abs(minX) > 100000 && Math.abs(minY) > 1000000,
      是否经纬度: minX >= -180 && maxX <= 180 && minY >= -90 && maxY <= 90,
      坐标中心点: [(minX + maxX) / 2, (minY + maxY) / 2]
    }
  });

  // 坐标系判断逻辑
  if (minX >= -180 && maxX <= 180 && minY >= -90 && maxY <= 90) {
    // 可能是经纬度坐标系，但需要进一步检查
    const xSpan = maxX - minX;
    const ySpan = maxY - minY;

    // 如果跨度很小（小于0.1度），可能是投影坐标系被误判
    if (xSpan < 0.1 && ySpan < 0.1) {
      console.log('⚠️ 坐标跨度很小，可能是投影坐标系，假设为 UTM Zone 48N EPSG:32648');
      return 'EPSG:32648';
    }

    if (minX > 70 && maxX < 140 && minY > 10 && maxY < 60) {
      // 中国区域，可能是 CGCS2000 或 WGS84
      console.log('🌍 检测为中国区域经纬度坐标系，假设为 EPSG:4326 (WGS84)');
      return 'EPSG:4326';
    } else {
      console.log('🌍 检测为全球经纬度坐标系 EPSG:4326 (WGS84)');
      return 'EPSG:4326';
    }
  } else if (Math.abs(minX) > 1000000 || Math.abs(maxX) > 1000000) {
    // 投影坐标系 (米为单位)
    if (minX > 200000 && maxX < 800000 && minY > 2000000 && maxY < 6000000) {
      // 可能是中国的高斯-克吕格投影
      console.log('🗺️ 检测为高斯-克吕格投影坐标系，假设为 EPSG:4547');
      return 'EPSG:4547';
    } else if (minX > -20000000 && maxX < 20000000 && minY > -20000000 && maxY < 20000000) {
      // 可能是 Web Mercator
      console.log('🗺️ 检测为 Web Mercator 投影 EPSG:3857');
      return 'EPSG:3857';
    } else if (minX > 100000 && maxX < 900000 && minY > 2000000 && maxY < 6000000) {
      // 中国区域的投影坐标系，根据 X 坐标判断分带
      if (minX > 700000 && maxX < 800000 && minY > 2400000 && maxY < 2600000) {
        // 您的数据范围：X [760459, 761325], Y [2486009, 2487082]
        console.log('🗺️ 检测为 CGCS2000 3度分带高斯-克吕格投影 zone 37 EPSG:4525');
        return 'EPSG:4525';
      } else if (minX > 200000 && maxX < 800000) {
        console.log('🗺️ 检测为 UTM Zone 48N 投影 EPSG:32648');
        return 'EPSG:32648';
      } else {
        console.log('🗺️ 检测为 UTM 投影坐标系，假设为 EPSG:32649');
        return 'EPSG:32649';
      }
    } else {
      // 其他投影
      console.log('🗺️ 检测为其他投影坐标系，假设为 EPSG:32648');
      return 'EPSG:32648';
    }
  } else {
    // 其他情况，默认为 WGS84
    console.log('❓ 无法确定坐标系，默认使用 EPSG:4326');
    return 'EPSG:4326';
  }
};

// PRJ 文件解析功能 - 精确的坐标系识别
const parsePRJFile = (prjText: string): string => {
  if (!prjText) {
    return 'UNKNOWN';
  }

  console.log('📄 解析 PRJ 文件内容:', prjText);

  // 精确的 WKT 解析 - 基于您提供的实际 WKT 格式
  const wktPatterns = [
    // CGCS2000 3度分带高斯-克吕格投影系列 - 精确匹配
    {
      pattern: /PROJCS\["CGCS2000_3_Degree_GK_Zone_37"/i,
      epsg: 'EPSG:4525',
      name: 'CGCS2000 / 3-degree Gauss-Kruger zone 37'
    },
    {
      pattern: /PROJCS\["CGCS2000_3_Degree_GK_Zone_38"/i,
      epsg: 'EPSG:4526',
      name: 'CGCS2000 / 3-degree Gauss-Kruger zone 38'
    },
    {
      pattern: /PROJCS\["CGCS2000_3_Degree_GK_Zone_39"/i,
      epsg: 'EPSG:4527',
      name: 'CGCS2000 / 3-degree Gauss-Kruger zone 39'
    },
    {
      pattern: /PROJCS\["CGCS2000_3_Degree_GK_Zone_40"/i,
      epsg: 'EPSG:4547',
      name: 'CGCS2000 / 3-degree Gauss-Kruger zone 40'
    },
    {
      pattern: /PROJCS\["CGCS2000_3_Degree_GK_Zone_41"/i,
      epsg: 'EPSG:4548',
      name: 'CGCS2000 / 3-degree Gauss-Kruger zone 41'
    },

    // 通过中央经线识别 CGCS2000 3度分带
    {
      pattern: /PROJECTION\["Gauss_Kruger"\].*PARAMETER\["Central_Meridian",111\.0\]/i,
      epsg: 'EPSG:4525',
      name: 'CGCS2000 / 3-degree Gauss-Kruger zone 37 (Central Meridian 111°)'
    },
    {
      pattern: /PROJECTION\["Gauss_Kruger"\].*PARAMETER\["Central_Meridian",114\.0\]/i,
      epsg: 'EPSG:4526',
      name: 'CGCS2000 / 3-degree Gauss-Kruger zone 38 (Central Meridian 114°)'
    },
    {
      pattern: /PROJECTION\["Gauss_Kruger"\].*PARAMETER\["Central_Meridian",117\.0\]/i,
      epsg: 'EPSG:4527',
      name: 'CGCS2000 / 3-degree Gauss-Kruger zone 39 (Central Meridian 117°)'
    },
    {
      pattern: /PROJECTION\["Gauss_Kruger"\].*PARAMETER\["Central_Meridian",120\.0\]/i,
      epsg: 'EPSG:4547',
      name: 'CGCS2000 / 3-degree Gauss-Kruger zone 40 (Central Meridian 120°)'
    },

    // UTM 系列
    {
      pattern: /PROJCS\["WGS_1984_UTM_Zone_48N"/i,
      epsg: 'EPSG:32648',
      name: 'WGS 84 / UTM zone 48N'
    },
    {
      pattern: /PROJCS\["WGS_1984_UTM_Zone_49N"/i,
      epsg: 'EPSG:32649',
      name: 'WGS 84 / UTM zone 49N'
    },

    // 地理坐标系 - 注意：只有在没有 PROJCS 的情况下才是地理坐标系
    {
      pattern: /^GEOGCS\["GCS_WGS_1984"/i,
      epsg: 'EPSG:4326',
      name: 'WGS 84',
      condition: (text: string) => !text.includes('PROJCS')
    },
    {
      pattern: /^GEOGCS\["GCS_China_Geodetic_Coordinate_System_2000"/i,
      epsg: 'EPSG:4490',
      name: 'China Geodetic Coordinate System 2000',
      condition: (text: string) => !text.includes('PROJCS')
    },

    // Web Mercator
    {
      pattern: /PROJCS\["WGS_1984_Web_Mercator"/i,
      epsg: 'EPSG:3857',
      name: 'WGS 84 / Pseudo-Mercator'
    }
  ];

  // 尝试匹配已知模式
  for (const item of wktPatterns) {
    if (item.pattern.test(prjText)) {
      // 检查额外条件（如果有）
      if (item.condition && !item.condition(prjText)) {
        continue;
      }
      console.log(`✅ PRJ 文件精确匹配: ${item.name} → ${item.epsg}`);
      return item.epsg;
    }
  }


  // 检查是否包含 UTM Zone 信息
  const utmMatch = prjText.match(/UTM_Zone_(\d+)N/);
  if (utmMatch) {
    const zone = parseInt(utmMatch[1]);
    const epsgCode = `EPSG:${32600 + zone}`;
    console.log(`✅ UTM Zone 检测: Zone ${zone} → ${epsgCode}`);
    return epsgCode;
  }

  // 检查中央经线来判断 UTM 分带
  const centralMeridianMatch = prjText.match(/Central_Meridian[",\s]*([0-9.-]+)/);
  if (centralMeridianMatch) {
    const centralMeridian = parseFloat(centralMeridianMatch[1]);
    console.log('🌍 检测到中央经线:', centralMeridian);

    if (centralMeridian === 105.0) {
      console.log('✅ 中央经线 105° → UTM Zone 48N (EPSG:32648)');
      return 'EPSG:32648';
    } else if (centralMeridian === 111.0) {
      console.log('✅ 中央经线 111° → UTM Zone 49N (EPSG:32649)');
      return 'EPSG:32649';
    } else if (centralMeridian === 117.0) {
      console.log('✅ 中央经线 117° → UTM Zone 50N (EPSG:32650)');
      return 'EPSG:32650';
    } else if (centralMeridian === 123.0) {
      console.log('✅ 中央经线 123° → UTM Zone 51N (EPSG:32651)');
      return 'EPSG:32651';
    }
  }

  // 如果是地理坐标系（包含 GEOGCS 但不包含 PROJCS）
  if (prjText.includes('GEOGCS') && !prjText.includes('PROJCS')) {
    console.log('✅ 检测为地理坐标系 → EPSG:4326');
    return 'EPSG:4326';
  }

  // 默认返回 WGS84
  console.log('⚠️ 无法识别 PRJ 文件，默认使用 EPSG:4326');
  return 'EPSG:4326';
};

// 坐标转换功能
const transformGeoJSONToWGS84 = (geojson: any, sourceEPSG: string): any => {
  if (sourceEPSG === 'EPSG:4326') {
    console.log('✅ 数据已经是 WGS84 坐标系，无需转换');
    return geojson;
  }

  console.log(`🔄 开始坐标转换: ${sourceEPSG} → EPSG:4326`);

  try {
    // 注册坐标系（如果 proj4 可用）
    if (typeof proj4 !== 'undefined' && typeof register !== 'undefined') {
      // 注册源坐标系
      if ((commonProjections as any)[sourceEPSG]) {
        proj4.defs(sourceEPSG, (commonProjections as any)[sourceEPSG]);
        register(proj4);
        console.log(`📝 已注册坐标系: ${sourceEPSG}`);
      }
    }

    // 转换坐标的递归函数
    const transformCoordinates = (coords: any): any => {
      if (typeof coords[0] === 'number') {
        // 单个坐标点 [x, y] 或 [x, y, z]
        try {
          if (typeof transform !== 'undefined') {
            const originalCoord = [coords[0], coords[1]];
            const transformed = transform(originalCoord, sourceEPSG, 'EPSG:4326');

            // 验证转换结果是否合理
            if (transformed[0] >= -180 && transformed[0] <= 180 &&
                transformed[1] >= -90 && transformed[1] <= 90) {
              return coords.length > 2 ? [transformed[0], transformed[1], coords[2]] : transformed;
            } else {
              console.warn('⚠️ 转换结果超出有效范围:', {
                原始坐标: originalCoord,
                转换结果: transformed,
                源坐标系: sourceEPSG
              });
              return coords;
            }
          } else {
            console.warn('⚠️ OpenLayers transform 不可用');
            return coords;
          }
        } catch (error) {
          console.warn('⚠️ 坐标转换失败:', {
            原始坐标: [coords[0], coords[1]],
            源坐标系: sourceEPSG,
            错误: error
          });
          return coords;
        }
      } else {
        // 嵌套数组，递归处理
        return coords.map((coord: any) => transformCoordinates(coord));
      }
    };

    // 创建转换后的 GeoJSON
    const transformedGeoJSON = {
      ...geojson,
      crs: {
        type: 'name',
        properties: {
          name: 'EPSG:4326'
        }
      },
      features: geojson.features.map((feature: any) => ({
        ...feature,
        geometry: feature.geometry ? {
          ...feature.geometry,
          coordinates: transformCoordinates(feature.geometry.coordinates)
        } : null
      }))
    };

    // 计算转换后的坐标范围
    let minLon = Infinity, maxLon = -Infinity, minLat = Infinity, maxLat = -Infinity;
    transformedGeoJSON.features.forEach((feature: any) => {
      if (feature.geometry && feature.geometry.coordinates) {
        const extractBounds = (coords: any) => {
          if (typeof coords[0] === 'number') {
            minLon = Math.min(minLon, coords[0]);
            maxLon = Math.max(maxLon, coords[0]);
            minLat = Math.min(minLat, coords[1]);
            maxLat = Math.max(maxLat, coords[1]);
          } else {
            coords.forEach((coord: any) => extractBounds(coord));
          }
        };
        extractBounds(feature.geometry.coordinates);
      }
    });

    console.log('✅ 坐标转换完成');
    console.log('📍 转换后坐标范围:', {
      经度范围: [minLon, maxLon],
      纬度范围: [minLat, maxLat],
      中心点: [(minLon + maxLon) / 2, (minLat + maxLat) / 2],
      范围有效性: minLon >= -180 && maxLon <= 180 && minLat >= -90 && maxLat <= 90 ? '有效' : '无效'
    });

    return transformedGeoJSON;

  } catch (error) {
    console.error('❌ 坐标转换失败:', error);
    console.log('🔄 返回原始数据');
    return geojson;
  }
};

// SHP文件加载 - 简化版本
const loadShpToMap = async () => {
  const mapInstance = getMapInstance();

  console.log('🚀 开始加载SHP文件到地图...');

  if (!mapInstance) {
    console.error('❌ 地图对象未注入或未初始化');
    ElMessage.error('地图对象未初始化，请确保地图已加载');
    return;
  }

  if (!hasValidShpFiles.value) {
    console.error('❌ 没有有效的SHP文件');
    ElMessage.error('请先选择有效的SHP文件');
    return;
  }

  // 获取文件集
  const shpFile = uploadedFiles.value.find(f => f.name.toLowerCase().endsWith('.shp'));
  if (!shpFile) {
    ElMessage.error('未找到 SHP 文件');
    return;
  }

  const baseName = shpFile.name.replace(/\.shp$/i, '');
  const dbfFile = uploadedFiles.value.find(f => f.name.toLowerCase() === `${baseName.toLowerCase()}.dbf`);
  const prjFile = uploadedFiles.value.find(f => f.name.toLowerCase() === `${baseName.toLowerCase()}.prj`);
  const shxFile = uploadedFiles.value.find(f => f.name.toLowerCase() === `${baseName.toLowerCase()}.shx`);

  console.log(`📦 处理文件集 "${baseName}":`, {
    SHP文件: shpFile.name,
    DBF文件: dbfFile?.name || '未找到',
    PRJ文件: prjFile?.name || '未找到',
    SHX文件: shxFile?.name || '未找到'
  });

  // 处理文件集
  await processShapefileSet(baseName, {
    shp: shpFile,
    dbf: dbfFile,
    prj: prjFile,
    shx: shxFile
  }, mapInstance);
};

// 处理单个 Shapefile 文件集
const processShapefileSet = async (baseName: string, fileSet: any, mapInstance: any) => {
  loading.value = true;
  loadingProgress.value = 0;
  loadingStatus.value = undefined;
  loadingText.value = `正在处理 ${baseName}...`;

  try {
    console.log(`📁 处理文件集 "${baseName}":`, {
      SHP文件: fileSet.shp?.name || '未找到',
      DBF文件: fileSet.dbf?.name || '未找到',
      PRJ文件: fileSet.prj?.name || '未找到',
      SHX文件: fileSet.shx?.name || '未找到'
    });

    loadingProgress.value = 20;
    loadingText.value = '正在读取文件...';

    // 读取文件内容
    const shpBuffer = await readFileAsArrayBuffer(fileSet.shp);
    const dbfBuffer = fileSet.dbf ? await readFileAsArrayBuffer(fileSet.dbf) : undefined;
    const prjText = fileSet.prj ? await readFileAsText(fileSet.prj) : undefined;

    console.log('📄 文件读取完成:', {
      'SHP大小': `${(shpBuffer.byteLength / 1024).toFixed(2)} KB`,
      'DBF大小': dbfBuffer ? `${(dbfBuffer.byteLength / 1024).toFixed(2)} KB` : '未提供',
      'PRJ内容': prjText ? '已读取' : '未提供'
    });

    loadingProgress.value = 40;
    loadingText.value = '正在解析几何数据...';

    // 解析 SHP 文件
    const geojson = await shapefile.read(shpBuffer, dbfBuffer);

    console.log('✅ SHP解析完成:', {
      要素数量: geojson.features?.length || 0,
      几何类型: geojson.features?.[0]?.geometry?.type || '未知'
    });

    loadingProgress.value = 60;
    loadingText.value = '正在识别坐标系...';

    // 识别坐标系
    let detectedCRS: string;
    if (prjText) {
      detectedCRS = parsePRJFile(prjText);
      console.log(`🎯 从PRJ文件识别坐标系: ${detectedCRS}`);
    } else {
      detectedCRS = detectCoordinateSystem(geojson);
      console.log(`🎯 从坐标范围推测坐标系: ${detectedCRS}`);
    }

    loadingProgress.value = 80;
    loadingText.value = '正在转换坐标系...';

    // 转换坐标系
    const transformedGeoJSON = transformGeoJSONToWGS84(geojson, detectedCRS);

    loadingProgress.value = 90;
    loadingText.value = '正在添加到地图...';

    // 添加到地图
    await addGeoJSONToMap(transformedGeoJSON, baseName, detectedCRS);

    loadingProgress.value = 100;
    loadingStatus.value = 'success';
    loadingText.value = '加载完成！';

    ElMessage.success(`成功加载: ${baseName}`);

  } catch (error) {
    console.error(`❌ 处理文件集 "${baseName}" 失败:`, error);
    loadingStatus.value = 'exception';
    loadingText.value = '加载失败';
    ElMessage.error(`加载失败: ${error instanceof Error ? error.message : '未知错误'}`);
  } finally {
    setTimeout(() => {
      loading.value = false;
      loadingProgress.value = 0;
      loadingStatus.value = undefined;
      loadingText.value = '';
    }, 2000);
  }
};



// 辅助函数
const readFileAsArrayBuffer = (file: File): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as ArrayBuffer);
    reader.onerror = () => reject(new Error('文件读取失败'));
    reader.readAsArrayBuffer(file);
  });
};

const readFileAsText = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = () => reject(new Error('文件读取失败'));
    reader.readAsText(file);
  });
};



const addGeoJSONToMap = async (geojson: any, fileName: string, sourceCRS?: string): Promise<void> => {
  const mapInstance = getMapInstance();

  if (!mapInstance) {
    throw new Error('地图实例不可用');
  }

  // 创建矢量数据源
  console.log('📊 创建矢量数据源:', {
    要素数量: geojson.features?.length || 0,
    地图投影: mapInstance.getView().getProjection().getCode(),
    数据投影: 'EPSG:4326' // 转换后的数据都是 WGS84
  });

  const vectorSource = new VectorSource({
    features: new GeoJSON().readFeatures(geojson, {
      dataProjection: 'EPSG:4326', // 数据是 WGS84
      featureProjection: mapInstance.getView().getProjection() // 地图投影
    })
  });

  console.log('✅ 矢量数据源创建完成:', {
    要素数量: vectorSource.getFeatures().length,
    数据范围: vectorSource.getExtent()
  });

  // 创建样式
  const style = new Style({
    stroke: new Stroke({
      color: getRandomColor(),
      width: 2
    }),
    fill: new Fill({
      color: getRandomColor() + '20' // 添加透明度
    }),
    image: new Circle({
      radius: 6,
      fill: new Fill({
        color: getRandomColor()
      }),
      stroke: new Stroke({
        color: '#fff',
        width: 2
      })
    })
  });

  // 创建矢量图层
  const vectorLayer = new VectorLayer({
    source: vectorSource,
    style: style
  });

  // 添加到地图
  mapInstance.addLayer(vectorLayer);

  // 记录已加载的图层
  const layerInfo = {
    id: Date.now().toString(),
    name: fileName.replace('.shp', ''),
    layer: vectorLayer,
    source: vectorSource,
    featureCount: vectorSource.getFeatures().length,
    visible: true,
    geojson: geojson,
    originalCRS: sourceCRS || '未知',
    targetCRS: 'EPSG:4326'
  };

  loadedLayers.value.push(layerInfo);

  console.log('🗺️ 图层已成功添加到地图:', {
    图层名称: layerInfo.name,
    要素数量: layerInfo.featureCount,
    图层ID: layerInfo.id,
    可见状态: layerInfo.visible,
    原始坐标系: layerInfo.originalCRS,
    目标坐标系: layerInfo.targetCRS,
    坐标转换: layerInfo.originalCRS !== 'EPSG:4326' ? '已转换' : '无需转换',
    图层对象: layerInfo.layer,
    数据源: layerInfo.source
  });

  console.log('📍 图层范围信息:', {
    边界框: layerInfo.source.getExtent(),
    投影: mapInstance.getView().getProjection().getCode()
  });
};

// 图层管理
const zoomToLayer = (layerInfo: any) => {
  const mapInstance = getMapInstance();
  if (layerInfo.source && mapInstance) {
    try {
      const extent = layerInfo.source.getExtent();
      console.log('📍 图层范围信息:', {
        图层名称: layerInfo.name,
        原始范围: extent,
        范围有效性: extent && extent.length === 4 &&
          extent[0] !== Infinity && extent[1] !== Infinity &&
          extent[2] !== -Infinity && extent[3] !== -Infinity ? '有效' : '无效'
      });

      if (extent && extent.length === 4 &&
          extent[0] !== Infinity && extent[1] !== Infinity &&
          extent[2] !== -Infinity && extent[3] !== -Infinity) {

        // 检查范围是否合理
        const width = extent[2] - extent[0];
        const height = extent[3] - extent[1];

        if (width > 0 && height > 0) {
          mapInstance.getView().fit(extent, {
            padding: [50, 50, 50, 50],
            duration: 1000,
            maxZoom: 18
          });
          console.log('✅ 成功定位到图层:', layerInfo.name);
        } else {
          console.error('❌ 图层范围无效:', { width, height, extent });
          ElMessage.error('图层范围无效，无法定位');
        }
      } else {
        console.error('❌ 无法获取有效的图层范围:', extent);
        ElMessage.error('无法获取图层范围，请检查数据');
      }
    } catch (error) {
      console.error('❌ 定位图层时发生错误:', error);
      ElMessage.error('定位图层失败');
    }
  } else {
    console.error('❌ 缺少必要的对象:', {
      图层数据源: !!layerInfo.source,
      地图实例: !!mapInstance
    });
    ElMessage.error('缺少必要的地图或图层信息');
  }
};

const toggleLayerVisibility = (layerInfo: any) => {
  if (layerInfo.layer) {
    layerInfo.visible = !layerInfo.visible;
    layerInfo.layer.setVisible(layerInfo.visible);
    console.log('切换图层可见性:', layerInfo.name, layerInfo.visible);
  }
};

const removeLayer = (layerInfo: any) => {
  ElMessageBox.confirm(
    `确定要删除图层 "${layerInfo.name}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const mapInstance = getMapInstance();
    if (layerInfo.layer && mapInstance) {
      mapInstance.removeLayer(layerInfo.layer);
    }

    const index = loadedLayers.value.findIndex(l => l.id === layerInfo.id);
    if (index > -1) {
      loadedLayers.value.splice(index, 1);
    }

    console.log('删除图层:', layerInfo.name);
    ElMessage.success('图层已删除');
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

const clearAllLayers = () => {
  ElMessageBox.confirm(
    '确定要清除所有已加载的SHP图层吗？',
    '确认清除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const mapInstance = getMapInstance();
    loadedLayers.value.forEach(layerInfo => {
      if (layerInfo.layer && mapInstance) {
        mapInstance.removeLayer(layerInfo.layer);
      }
    });

    loadedLayers.value = [];
    uploadedFiles.value = [];

    console.log('清除所有SHP图层');
    ElMessage.success('所有图层已清除');
  }).catch(() => {
    ElMessage.info('已取消清除');
  });
};

// 调试函数
const debugFileStatus = () => {
  console.group('🔍 SHP加载器调试信息');
  console.log('📁 上传文件状态:', {
    文件数量: uploadedFiles.value.length,
    文件详情: uploadedFiles.value.map(f => ({
      文件名: f.name,
      文件大小: f.size,
      文件类型: f.type,
      扩展名: f.name.split('.').pop()?.toLowerCase(),
      是否为SHP: f.name.toLowerCase().endsWith('.shp'),
      文件对象: f
    }))
  });

  console.log('✅ 验证结果:', {
    hasValidShpFiles: hasValidShpFiles.value,
    SHP文件列表: uploadedFiles.value.filter(f => f.name.toLowerCase().endsWith('.shp')).map(f => f.name),
    DBF文件列表: uploadedFiles.value.filter(f => f.name.toLowerCase().endsWith('.dbf')).map(f => f.name),
    其他文件: uploadedFiles.value.filter(f => !f.name.toLowerCase().endsWith('.shp') && !f.name.toLowerCase().endsWith('.dbf')).map(f => f.name)
  });

  const mapInstance = getMapInstance();
  console.log('🗺️ 地图状态:', {
    注入的地图对象: map,
    实际地图实例: mapInstance,
    地图是否存在: !!mapInstance,
    地图投影: mapInstance ? mapInstance.getView().getProjection().getCode() : '未知',
    地图图层数量: mapInstance ? mapInstance.getLayers().getLength() : 0
  });

  console.log('🎛️ 组件状态:', {
    loading: loading.value,
    loadingProgress: loadingProgress.value,
    loadingText: loadingText.value,
    loadedLayers: loadedLayers.value.length
  });

  console.groupEnd();

  ElMessage.info('调试信息已输出到控制台，请查看Console标签页');
};

// 测试坐标转换函数
const testCoordinateTransform = () => {
  console.group('🧪 坐标转换测试');

  // 测试 UTM Zone 48N 到 WGS84 的转换
  const testCoords = [
    [500000, 2500000], // UTM Zone 48N 中心点
    [400000, 2400000], // UTM Zone 48N 西南
    [600000, 2600000]  // UTM Zone 48N 东北
  ];

  console.log('🔄 测试 EPSG:32648 → EPSG:4326 转换:');

  // 注册 UTM Zone 48N 坐标系
  try {
    if (typeof proj4 !== 'undefined' && typeof register !== 'undefined') {
      proj4.defs('EPSG:32648', '+proj=utm +zone=48 +ellps=WGS84 +datum=WGS84 +units=m +no_defs +type=crs');
      register(proj4);
      console.log('✅ 已注册 EPSG:32648 坐标系');
    }

    testCoords.forEach((coord, index) => {
      try {
        const transformed = transform(coord, 'EPSG:32648', 'EPSG:4326');
        console.log(`测试点 ${index + 1}:`, {
          原始坐标: coord,
          转换结果: transformed,
          经度: transformed[0].toFixed(6),
          纬度: transformed[1].toFixed(6),
          有效性: transformed[0] >= -180 && transformed[0] <= 180 &&
                  transformed[1] >= -90 && transformed[1] <= 90 ? '有效' : '无效'
        });
      } catch (error) {
        console.error(`测试点 ${index + 1} 转换失败:`, error);
      }
    });

  } catch (error) {
    console.error('❌ 坐标转换测试失败:', error);
  }

  console.groupEnd();
  ElMessage.success('坐标转换测试完成，请查看控制台');
};

// 强制 CGCS2000 转换函数
const forceCGCS2000Conversion = async () => {
  if (uploadedFiles.value.length === 0) {
    ElMessage.error('请先选择 SHP 文件');
    return;
  }

  console.group('🔧 强制 CGCS2000 转换测试');
  console.log('假设数据为 CGCS2000 3度分带高斯-克吕格投影 zone 37 (EPSG:4525)，强制转换为 WGS84');

  try {
    // 清除现有图层
    clearAllLayers();

    // 重新加载，但强制使用 CGCS2000 坐标系
    loading.value = true;
    loadingText.value = '强制 CGCS2000 转换中...';

    const shpFile = uploadedFiles.value.find(f => f.name.endsWith('.shp'));
    if (!shpFile) {
      throw new Error('未找到SHP文件');
    }

    // 读取和解析文件
    const shpBuffer = await readFileAsArrayBuffer(shpFile);
    const geojson = await shapefile.read(shpBuffer);

    console.log('🔄 原始 GeoJSON (假设为 CGCS2000):', geojson);

    // 强制转换坐标系
    const transformedGeoJSON = transformGeoJSONToWGS84(geojson, 'EPSG:4525');

    console.log('✅ 强制转换后的 GeoJSON:', transformedGeoJSON);

    // 添加到地图
    await addGeoJSONToMap(transformedGeoJSON, shpFile.name + '_CGCS2000转换', 'EPSG:4525');

    ElMessage.success('强制 CGCS2000 转换完成！');

  } catch (error) {
    console.error('❌ 强制转换失败:', error);
    ElMessage.error('强制转换失败');
  } finally {
    loading.value = false;
    loadingText.value = '';
    console.groupEnd();
  }
};
</script>

<style scoped lang="scss">
.shp-loader-panel {
  padding: 20px;
  height: 100%;
  overflow-y: auto;

  .panel-header {
    margin-bottom: 20px;

    h3 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 18px;
      font-weight: 600;
    }

    .panel-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .panel-content {
    .upload-section {
      margin-bottom: 20px;

      .shp-upload {
        :deep(.el-upload) {
          width: 100%;
        }

        :deep(.el-upload-dragger) {
          width: 100%;
          height: 120px;
          border: 2px dashed #d9d9d9;
          border-radius: 6px;
          cursor: pointer;
          position: relative;
          overflow: hidden;
          transition: border-color 0.3s;

          &:hover {
            border-color: #409EFF;
          }
        }

        :deep(.el-icon--upload) {
          font-size: 28px;
          color: #8c939d;
          margin: 20px 0 16px;
        }

        :deep(.el-upload__text) {
          color: #606266;
          font-size: 14px;
          text-align: center;

          em {
            color: #409EFF;
            font-style: normal;
          }
        }

        :deep(.el-upload__tip) {
          font-size: 12px;
          color: #909399;
          margin-top: 7px;
        }
      }
    }

    .file-list-section {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }

      .file-list {
        border: 1px solid #EBEEF5;
        border-radius: 4px;

        .file-item {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          border-bottom: 1px solid #EBEEF5;

          &:last-child {
            border-bottom: none;
          }

          .el-icon {
            margin-right: 8px;
            color: #909399;
          }

          .file-name {
            flex: 1;
            font-size: 14px;
            color: #303133;
          }

          .file-size {
            margin-right: 8px;
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }

    .action-buttons {
      margin-bottom: 20px;
      display: flex;
      gap: 12px;

      .el-button {
        flex: 1;
      }
    }

    .loaded-layers-section {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }

      .layer-list {
        border: 1px solid #EBEEF5;
        border-radius: 4px;

        .layer-item {
          padding: 12px;
          border-bottom: 1px solid #EBEEF5;

          &:last-child {
            border-bottom: none;
          }

          .layer-info {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .el-icon {
              margin-right: 8px;
              color: #409EFF;
            }

            .layer-name {
              flex: 1;
              font-size: 14px;
              color: #303133;
              font-weight: 500;
            }

            .el-tag {
              margin-left: 8px;
            }
          }

          .layer-actions {
            display: flex;
            gap: 8px;

            .el-button {
              flex: 1;
            }
          }
        }
      }
    }

    .loading-status {
      margin-top: 20px;

      .el-progress {
        margin-bottom: 12px;
      }

      .loading-text {
        margin: 0;
        text-align: center;
        font-size: 14px;
        color: #606266;
      }
    }
  }
}
</style>