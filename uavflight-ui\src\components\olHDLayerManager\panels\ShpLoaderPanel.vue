<!--
 * @Author: AI Assistant
 * @Date: 2025-01-08
 * @Description: SHP文件管理器面板 - 重新设计
 *
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved.
-->
<template>
  <div class="shp-manager-panel">
    <el-card class="panel-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="panel-title">
            <el-icon><FolderOpened /></el-icon>
            SHP文件管理器
          </span>
        </div>
      </template>

      <!-- 主要操作按钮 -->
      <div class="main-actions">
        <el-button
          type="primary"
          size="small"
          @click="showUploadDialog = true"
        >
          <el-icon><Upload /></el-icon>
          加载数据
        </el-button>
        <el-button
          type="success"
          size="small"
          :disabled="!hasLoadedLayers"
          @click="toggleAttributeQuery"
        >
          <el-icon><Search /></el-icon>
          {{ attributeQueryEnabled ? '关闭属性查询' : '开启属性查询' }}
        </el-button>
      </div>

      <!-- 图层管理 -->
      <div v-if="loadedLayers.length > 0" class="layer-management">
        <div class="layer-selector-row">
          <el-select
            v-model="selectedLayerId"
            placeholder="选择图层进行操作"
            class="layer-select"
            @change="onLayerSelect"
          >
            <el-option
              v-for="layer in loadedLayers"
              :key="layer.id"
              :label="`${layer.name} (${layer.featureCount}个要素)`"
              :value="layer.id"
            >
              <div class="layer-option">
                <span>{{ layer.name }}</span>
              </div>
            </el-option>
          </el-select>

          <div v-if="selectedLayer" class="layer-operations">
            <el-tooltip content="缩放至图层" placement="top">
              <el-button
                size="small"
                circle
                @click="zoomToLayer(selectedLayer)"
              >
                <el-icon><Location /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="删除图层" placement="top">
              <el-button
                size="small"
                type="danger"
                circle
                @click="removeLayer(selectedLayer)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </div>

      <!-- 属性展示 -->
      <div v-if="attributeQueryEnabled" class="attribute-display">
        <div v-if="!selectedFeature" class="no-feature-selected">
          <el-empty 
            description="点击地图上的要素查看属性信息"
            :image-size="80"
          />
        </div>
        <div v-else class="feature-attributes">
          <div class="feature-header">
            <h4>要素属性</h4>
            <el-tag type="info" size="small">
              {{ selectedFeature.geometry?.type || '未知几何类型' }}
            </el-tag>
          </div>
          <el-table 
            :data="featureAttributesList" 
            size="small"
            stripe
            max-height="300"
          >
            <el-table-column prop="key" label="属性名" width="120" />
            <el-table-column prop="value" label="属性值" />
          </el-table>
        </div>
      </div>

      <!-- 上传对话框 -->
      <el-dialog
        v-model="showUploadDialog"
        title="上传SHP文件"
        width="600px"
        :close-on-click-modal="false"
      >
        <div class="upload-dialog-content">
          <el-upload
            ref="uploadRef"
            class="shp-upload"
            drag
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileChange"
            accept=".shp,.dbf,.shx,.prj,.cpg"
            multiple
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将SHP文件集拖拽到此处，或<em>点击选择多个文件</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                请同时选择完整的SHP文件集（.shp, .dbf, .prj, .shx等）
              </div>
            </template>
          </el-upload>
          
          <!-- 文件列表 -->
          <div v-if="uploadedFiles.length > 0" class="file-list">
            <el-divider content-position="left">已选择的文件</el-divider>
            <div class="file-items">
              <div 
                v-for="file in uploadedFiles" 
                :key="file.name"
                class="file-item"
              >
                <el-tag 
                  :type="getFileTagType(file.name)"
                  size="small"
                >
                  {{ file.name }}
                </el-tag>
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
              </div>
            </div>
          </div>

          <!-- 加载进度 -->
          <div v-if="loading" class="loading-section">
            <el-progress 
              :percentage="loadingProgress" 
              :status="loadingStatus"
              :stroke-width="8"
            />
            <p class="loading-text">{{ loadingText }}</p>
          </div>
        </div>
        
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="showUploadDialog = false">取消</el-button>
            <el-button @click="clearFiles" v-if="uploadedFiles.length > 0">清空文件</el-button>
            <el-button 
              type="primary" 
              :disabled="!hasValidShpFiles"
              :loading="loading"
              @click="loadShpToMap"
            >
              加载到地图
            </el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  UploadFilled,
  FolderOpened,
  Upload,
  Search,
  Location,
  Delete
} from '@element-plus/icons-vue'
import VectorLayer from 'ol/layer/Vector'
import VectorSource from 'ol/source/Vector'
import GeoJSON from 'ol/format/GeoJSON'
import { Style, Fill, Stroke } from 'ol/style'
import { transform } from 'ol/proj'
import { register } from 'ol/proj/proj4'
import proj4 from 'proj4'
import * as shapefile from 'shapefile'

// 注入地图实例
const map = inject('map') as any

// 响应式数据
const uploadRef = ref()
const uploadedFiles = ref<File[]>([])
const loadedLayers = ref<any[]>([])
const loading = ref(false)
const loadingProgress = ref(0)
const loadingStatus = ref<'success' | 'exception' | undefined>(undefined)
const loadingText = ref('')
const showUploadDialog = ref(false)
const attributeQueryEnabled = ref(false)
const selectedLayerId = ref('')
const selectedFeature = ref<any>(null)

// 计算属性
const hasValidShpFiles = computed(() => {
  return uploadedFiles.value.some(f => f.name.toLowerCase().endsWith('.shp'))
})

const hasLoadedLayers = computed(() => {
  return loadedLayers.value.length > 0
})

const selectedLayer = computed(() => {
  return loadedLayers.value.find(layer => layer.id === selectedLayerId.value)
})

const featureAttributesList = computed(() => {
  if (!selectedFeature.value?.properties) return []
  return Object.entries(selectedFeature.value.properties).map(([key, value]) => ({
    key,
    value: value?.toString() || ''
  }))
})

// 坐标系定义
const projDefinitions: { [key: string]: string } = {
  'EPSG:32648': '+proj=utm +zone=48 +ellps=WGS84 +datum=WGS84 +units=m +no_defs +type=crs',
  'EPSG:32649': '+proj=utm +zone=49 +ellps=WGS84 +datum=WGS84 +units=m +no_defs +type=crs',
  'EPSG:4525': '+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=37500000 +y_0=0 +ellps=GRS80 +datum=CGCS2000 +units=m +no_defs +type=crs',
  'EPSG:4526': '+proj=tmerc +lat_0=0 +lon_0=114 +k=1 +x_0=38500000 +y_0=0 +ellps=GRS80 +datum=CGCS2000 +units=m +no_defs +type=crs',
  'EPSG:4547': '+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs +type=crs'
}

// 注册坐标系
Object.entries(projDefinitions).forEach(([epsg, def]) => {
  proj4.defs(epsg, def)
})
register(proj4)

// 获取地图实例
const getMapInstance = () => {
  return map?.value || map
}

// 文件处理
const handleFileChange = (file: any) => {
  const newFile = file.raw

  const fileName = newFile.name.toLowerCase()
  const validExtensions = ['.shp', '.dbf', '.shx', '.prj', '.cpg']
  const isValidExtension = validExtensions.some(ext => fileName.endsWith(ext))

  if (!isValidExtension) {
    ElMessage.warning(`不支持的文件类型: ${newFile.name}`)
    return
  }

  const existingIndex = uploadedFiles.value.findIndex(f => f.name === newFile.name)
  if (existingIndex >= 0) {
    uploadedFiles.value[existingIndex] = newFile
  } else {
    uploadedFiles.value.push(newFile)
  }

  checkFileSetCompleteness()
}

// 检查文件集完整性
const checkFileSetCompleteness = () => {
  const fileGroups = new Map<string, { shp?: File, dbf?: File, prj?: File, shx?: File, cpg?: File }>()

  uploadedFiles.value.forEach(file => {
    const baseName = file.name.replace(/\.(shp|dbf|shx|prj|cpg)$/i, '')
    const extension = file.name.split('.').pop()?.toLowerCase()

    if (!fileGroups.has(baseName)) {
      fileGroups.set(baseName, {})
    }

    const group = fileGroups.get(baseName)!
    if (extension === 'shp') group.shp = file
    else if (extension === 'dbf') group.dbf = file
    else if (extension === 'prj') group.prj = file
    else if (extension === 'shx') group.shx = file
    else if (extension === 'cpg') group.cpg = file
  })

  // 只在文件集合完整时才显示提示，避免每次添加文件都弹出警告
  fileGroups.forEach((group, baseName) => {
    if (group.shp) {
      // 只有当有 SHP 文件时才检查其他文件
      const missingFiles: string[] = []
      if (!group.prj) missingFiles.push('PRJ')
      if (!group.dbf) missingFiles.push('DBF')

      // 只在第一次完成文件集时提示，避免重复提示
      if (missingFiles.length > 0 && uploadedFiles.value.length >= 1) {
        // 使用 setTimeout 避免在文件上传过程中频繁提示
        setTimeout(() => {
          if (missingFiles.includes('PRJ')) {
            ElMessage.warning(`建议为 "${baseName}" 添加 PRJ 文件以获得准确的坐标系信息`)
          }
        }, 500)
      }
    }
  })
}

// PRJ 文件解析
const parsePRJFile = (prjText: string): string => {
  if (!prjText) return 'UNKNOWN'

  const wktPatterns = [
    {
      pattern: /PROJCS\["CGCS2000_3_Degree_GK_Zone_37"/i,
      epsg: 'EPSG:4525',
      name: 'CGCS2000 / 3-degree Gauss-Kruger zone 37'
    },
    {
      pattern: /PROJCS\["CGCS2000_3_Degree_GK_Zone_38"/i,
      epsg: 'EPSG:4526',
      name: 'CGCS2000 / 3-degree Gauss-Kruger zone 38'
    },
    {
      pattern: /PROJCS\["WGS_1984_UTM_Zone_48N"/i,
      epsg: 'EPSG:32648',
      name: 'WGS 84 / UTM zone 48N'
    },
    {
      pattern: /PROJECTION\["Gauss_Kruger"\].*PARAMETER\["Central_Meridian",111\.0\]/i,
      epsg: 'EPSG:4525',
      name: 'CGCS2000 / 3-degree Gauss-Kruger zone 37 (Central Meridian 111°)'
    }
  ]

  for (const item of wktPatterns) {
    if (item.pattern.test(prjText)) {
      return item.epsg
    }
  }

  return 'UNKNOWN'
}

// 坐标转换
const transformGeoJSONToWGS84 = (geojson: any, sourceEPSG: string): any => {
  if (sourceEPSG === 'EPSG:4326' || sourceEPSG === 'UNKNOWN') {
    return geojson
  }

  const transformedGeoJSON = JSON.parse(JSON.stringify(geojson))

  transformedGeoJSON.features.forEach((feature: any) => {
    if (feature.geometry && feature.geometry.coordinates) {
      const transformCoordinates = (coords: any): any => {
        if (typeof coords[0] === 'number') {
          try {
            return transform(coords, sourceEPSG, 'EPSG:4326')
          } catch (error) {
            return coords
          }
        } else {
          return coords.map((coord: any) => transformCoordinates(coord))
        }
      }

      feature.geometry.coordinates = transformCoordinates(feature.geometry.coordinates)
    }
  })

  return transformedGeoJSON
}

// 辅助函数
const readFileAsArrayBuffer = (file: File): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as ArrayBuffer)
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsArrayBuffer(file)
  })
}

const readFileAsText = (file: File, encoding: string = 'utf-8'): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsText(file, encoding)
  })
}

// 中文编码转换函数 - 浏览器兼容版本
const fixEncoding = (str: string): string => {
  if (!str || typeof str !== 'string') return str

  try {
    // 检测是否包含可能的编码问题字符
    if (/[\u00C0-\u00FF]/.test(str) || /[��]/.test(str)) {

      // 方法1: 尝试 UTF-8 解码
      try {
        const bytes = new Uint8Array(str.length)
        for (let i = 0; i < str.length; i++) {
          bytes[i] = str.charCodeAt(i) & 0xFF
        }

        const decoded = new TextDecoder('utf-8', { fatal: false }).decode(bytes)
        if (/[\u4e00-\u9fff]/.test(decoded) && !decoded.includes('�') && decoded !== str) {
          console.log('UTF-8 解码成功:', str, '->', decoded)
          return decoded
        }
      } catch (e) {
        // UTF-8 解码失败
      }

      // 方法2: 尝试 URL 解码方法
      try {
        const encoded = str.split('').map(c =>
          '%' + c.charCodeAt(0).toString(16).padStart(2, '0')
        ).join('')
        const decoded = decodeURIComponent(encoded)
        if (/[\u4e00-\u9fff]/.test(decoded) && decoded !== str) {
          console.log('URL 解码成功:', str, '->', decoded)
          return decoded
        }
      } catch (e) {
        // URL 解码失败
      }

      // 如果 UTF-8 解码失败，尝试简单的字符映射
      // 这是一个简化的方法，适用于常见的乱码情况
      let result = str

      // 常见乱码模式的替换
      const patterns: [RegExp, string][] = [
        [/æ²™/g, '沙'],
        [/å¤´/g, '头'],
        [/é•‡/g, '镇'],
        [/æ‹›/g, '招'],
        [/å•†/g, '商'],
        [/å¼•/g, '引'],
        [/èµ„/g, '资'],
        [/ç§/g, '种'],
        [/ç²®/g, '粮'],
        [/é£Ÿ/g, '食'],
        [/ç‰‡/g, '片'],
        [/åŒº/g, '区'],
        [/äº©/g, '亩'],
        [/ä¼˜/g, '优'],
        [/ç§€/g, '秀'],
        [/æ–°/g, '新'],
        [/æ'/g, '村'],
        [/å§"/g, '委'],
        [/ä¼š/g, '会'],
        [/è€•/g, '耕'],
        [/åœ°/g, '地'],
        [/æ°´/g, '水'],
        [/ç"°/g, '田'],
        [/è·/g, '距'],
        [/ç¦»/g, '离'],
        [/å¹²/g, '干'],
        [/é"/g, '道'],
        [/æº/g, '源'],
        [/ç"µ/g, '电'],
        [/é«˜/g, '高'],
        [/æ ‡/g, '标'],
        [/å‡†/g, '准'],
        [/å†œ/g, '农'],
        [/å»º/g, '建'],
        [/è®¾/g, '设'],
        [/é¡¹/g, '项'],
        [/ç›®/g, '目'],
        [/æ—±/g, '旱'],
        [/æ°¸/g, '永'],
        [/ä¹…/g, '久'],
        [/åŸº/g, '基'],
        [/æœ¬/g, '本'],
        [/ç"Ÿ/g, '生'],
        [/äº§/g, '产'],
        [/åŠŸ/g, '功'],
        [/èƒ½/g, '能']
      ]

      // 应用字符映射
      for (const [pattern, replacement] of patterns) {
        result = result.replace(pattern, replacement)
      }

      if (result !== str) {
        return result
      }
    }
  } catch (error) {
    console.warn('编码转换失败:', error)
  }

  return str
}

// 主要功能函数
const loadShpToMap = async () => {
  const mapInstance = getMapInstance()

  if (!mapInstance) {
    ElMessage.error('地图对象未初始化')
    return
  }

  if (!hasValidShpFiles.value) {
    ElMessage.error('请先选择有效的SHP文件')
    return
  }

  const shpFile = uploadedFiles.value.find(f => f.name.toLowerCase().endsWith('.shp'))
  if (!shpFile) {
    ElMessage.error('未找到 SHP 文件')
    return
  }

  const baseName = shpFile.name.replace(/\.shp$/i, '')
  const dbfFile = uploadedFiles.value.find(f => f.name.toLowerCase() === `${baseName.toLowerCase()}.dbf`)
  const prjFile = uploadedFiles.value.find(f => f.name.toLowerCase() === `${baseName.toLowerCase()}.prj`)

  loading.value = true
  loadingProgress.value = 0
  loadingText.value = '正在处理文件...'

  try {
    loadingProgress.value = 20
    loadingText.value = '正在读取文件...'

    const shpBuffer = await readFileAsArrayBuffer(shpFile)
    const dbfBuffer = dbfFile ? await readFileAsArrayBuffer(dbfFile) : undefined
    const prjText = prjFile ? await readFileAsText(prjFile) : undefined

    loadingProgress.value = 40
    loadingText.value = '正在解析几何数据...'

    // 读取 SHP 和 DBF 文件
    const geojson = await shapefile.read(shpBuffer, dbfBuffer)

    // 处理中文编码问题
    if (geojson.features) {
      geojson.features.forEach((feature: any) => {
        if (feature.properties) {
          Object.keys(feature.properties).forEach(key => {
            const value = feature.properties[key]
            if (typeof value === 'string' && value) {
              // 使用编码转换函数
              feature.properties[key] = fixEncoding(value)
            }
          })
        }
      })
    }

    loadingProgress.value = 60
    loadingText.value = '正在识别坐标系...'

    let detectedCRS: string = 'EPSG:4326'
    if (prjText) {
      detectedCRS = parsePRJFile(prjText)
    }

    loadingProgress.value = 80
    loadingText.value = '正在转换坐标系...'

    const transformedGeoJSON = transformGeoJSONToWGS84(geojson, detectedCRS)

    loadingProgress.value = 90
    loadingText.value = '正在添加到地图...'

    await addGeoJSONToMap(transformedGeoJSON, baseName, detectedCRS)

    loadingProgress.value = 100
    loadingStatus.value = 'success'
    loadingText.value = '加载完成！'

    ElMessage.success(`成功加载: ${baseName}`)
    showUploadDialog.value = false

  } catch (error) {
    loadingStatus.value = 'exception'
    loadingText.value = '加载失败'
    ElMessage.error(`加载失败: ${error instanceof Error ? error.message : '未知错误'}`)
  } finally {
    setTimeout(() => {
      loading.value = false
      loadingProgress.value = 0
      loadingStatus.value = undefined
      loadingText.value = ''
    }, 2000)
  }
}

const addGeoJSONToMap = async (geojson: any, name: string, originalCRS: string) => {
  const mapInstance = getMapInstance()

  const format = new GeoJSON()
  const vectorSource = new VectorSource({
    features: format.readFeatures(geojson, {
      dataProjection: 'EPSG:4326',
      featureProjection: mapInstance.getView().getProjection()
    })
  })

  const vectorLayer = new VectorLayer({
    source: vectorSource,
    style: new Style({
      stroke: new Stroke({
        color: '#3399CC',
        width: 2
      }),
      fill: new Fill({
        color: 'rgba(51, 153, 204, 0.1)'
      })
    })
  })

  mapInstance.addLayer(vectorLayer)

  const layerInfo = {
    id: Date.now().toString(),
    name: name,
    layer: vectorLayer,
    source: vectorSource,
    visible: true,
    originalCRS: originalCRS,
    coordinateTransform: originalCRS === 'EPSG:4326' ? '无需转换' : `${originalCRS} → WGS84`,
    featureCount: vectorSource.getFeatures().length
  }

  loadedLayers.value.push(layerInfo)

  // 自动选择新加载的图层
  selectedLayerId.value = layerInfo.id

  // 定位到图层
  const extent = vectorSource.getExtent()
  if (extent && extent.length === 4) {
    mapInstance.getView().fit(extent, {
      padding: [50, 50, 50, 50],
      duration: 1000,
      maxZoom: 18
    })
  }
}

// 图层操作函数
const onLayerSelect = () => {
  // 图层选择时的处理
}

const zoomToLayer = (layerInfo: any) => {
  const mapInstance = getMapInstance()
  if (layerInfo.source && mapInstance) {
    try {
      const extent = layerInfo.source.getExtent()
      if (extent && extent.length === 4) {
        mapInstance.getView().fit(extent, {
          padding: [50, 50, 50, 50],
          duration: 1000,
          maxZoom: 18
        })
        ElMessage.success(`已定位到图层: ${layerInfo.name}`)
      } else {
        ElMessage.error('无法获取图层范围')
      }
    } catch (error) {
      ElMessage.error('定位图层失败')
    }
  }
}


const removeLayer = async (layerInfo: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除图层 "${layerInfo.name}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const mapInstance = getMapInstance()
    if (mapInstance && layerInfo.layer) {
      mapInstance.removeLayer(layerInfo.layer)
    }

    const index = loadedLayers.value.findIndex(layer => layer.id === layerInfo.id)
    if (index > -1) {
      loadedLayers.value.splice(index, 1)
    }

    if (selectedLayerId.value === layerInfo.id) {
      selectedLayerId.value = ''
    }

    ElMessage.success(`已移除图层: ${layerInfo.name}`)
  } catch (error) {
    // 用户取消操作
  }
}

// 属性查询功能
const toggleAttributeQuery = () => {
  attributeQueryEnabled.value = !attributeQueryEnabled.value

  const mapInstance = getMapInstance()
  if (!mapInstance) return

  if (attributeQueryEnabled.value) {
    // 启用属性查询
    mapInstance.on('singleclick', handleMapClick)
    ElMessage.success('属性查询已开启，点击地图要素查看属性')
  } else {
    // 关闭属性查询
    mapInstance.un('singleclick', handleMapClick)
    selectedFeature.value = null
    ElMessage.info('属性查询已关闭')
  }
}

const handleMapClick = (event: any) => {
  const mapInstance = getMapInstance()
  if (!mapInstance) return

  const features = mapInstance.getFeaturesAtPixel(event.pixel)
  if (features && features.length > 0) {
    const feature = features[0]
    const properties = feature.getProperties()

    // 移除几何属性，只保留业务属性
    delete properties.geometry

    selectedFeature.value = {
      geometry: feature.getGeometry(),
      properties: properties
    }
  } else {
    selectedFeature.value = null
  }
}

// 工具函数
const getFileTagType = (fileName: string): string => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  switch (ext) {
    case 'shp': return 'primary'
    case 'prj': return 'success'
    case 'dbf': return 'warning'
    case 'shx': return 'info'
    default: return 'default'
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const clearFiles = () => {
  uploadedFiles.value = []
  ElMessage.info('已清空所有文件')
}

// 生命周期
onMounted(() => {
  // 组件挂载时的初始化
})

onUnmounted(() => {
  // 组件卸载时清理事件监听
  const mapInstance = getMapInstance()
  if (mapInstance && attributeQueryEnabled.value) {
    mapInstance.un('singleclick', handleMapClick)
  }
})
</script>

<style lang="scss" scoped>
.shp-manager-panel {
  width: 100%;
  height: 100%;
  color: #fff;
}

.panel-card {
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.panel-card :deep(.el-card__header) {
  background: rgba(0, 0, 0, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
}

.panel-card :deep(.el-card__body) {
  background: transparent;
  color: #fff;
  padding: 12px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.main-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.main-actions .el-button {
  flex: 1;
  height: 36px;
  font-size: 14px;
  font-weight: 500;
  background: rgba(64, 158, 255, 0.8);
  border-color: rgba(64, 158, 255, 0.9);
  color: white;
  transition: all 0.3s ease;
}

.main-actions .el-button:hover {
  background: rgba(102, 177, 255, 0.9);
  border-color: rgba(102, 177, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
}

.main-actions .el-button.is-disabled {
  background: rgba(64, 158, 255, 0.3);
  border-color: rgba(64, 158, 255, 0.3);
  color: rgba(255, 255, 255, 0.5);
}

.layer-management {
  margin-bottom: 8px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  padding: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.layer-selector-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.layer-select {
  flex: 1;
}

.layer-select :deep(.el-input__inner) {
  background: rgba(0, 0, 0, 0.5);
  border-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.layer-select :deep(.el-input__inner:focus) {
  border-color: rgba(64, 158, 255, 0.8);
}

.layer-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  color: #333; /* 下拉框选项文字改为黑色 */
}

.layer-operations {
  display: flex;
  gap: 4px;
}

.layer-operations .el-button {
  background: rgba(64, 158, 255, 0.6);
  border-color: rgba(64, 158, 255, 0.8);
  color: white;
  width: 32px;
  height: 32px;
}

.layer-operations .el-button:hover {
  background: rgba(102, 177, 255, 0.8);
  border-color: rgba(102, 177, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
}

.attribute-display {
  margin-bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  padding: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.no-feature-selected {
  text-align: center;
  padding: 20px;
  color: rgba(255, 255, 255, 0.6);
}

.feature-attributes {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.feature-header h4 {
  margin: 0;
  color: #fff;
  font-size: 14px;
}

.attribute-display :deep(.el-table) {
  background: transparent;
  color: #fff;
}

.attribute-display :deep(.el-table th) {
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-color: rgba(255, 255, 255, 0.1);
}

.attribute-display :deep(.el-table td) {
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  border-color: rgba(255, 255, 255, 0.1);
}

.attribute-display :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: rgba(0, 0, 0, 0.4);
}

/* 分隔线样式 */
:deep(.el-divider) {
  border-color: rgba(255, 255, 255, 0.2);
  margin: 8px 0;
}

:deep(.el-divider__text) {
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  font-size: 13px;
}

.upload-dialog-content {
  padding: 0;
}

.shp-upload {
  margin-bottom: 20px;
}

.shp-upload :deep(.el-upload-dragger) {
  width: 100%;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s;
}

.shp-upload :deep(.el-upload-dragger:hover) {
  border-color: #409eff;
}

.shp-upload :deep(.el-icon--upload) {
  font-size: 28px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.shp-upload :deep(.el-upload__text) {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.shp-upload :deep(.el-upload__tip) {
  font-size: 12px;
  color: #909399;
  margin-top: 7px;
  text-align: center;
}

.file-list {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}

.file-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.loading-section {
  margin-top: 20px;
  text-align: center;
}

.loading-text {
  margin-top: 8px;
  font-size: 14px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 对话框样式 */
:deep(.el-dialog) {
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-dialog__header) {
  background: rgba(0, 0, 0, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-dialog__title) {
  color: #fff;
}

:deep(.el-dialog__body) {
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
}

/* 下拉框选项样式 */
:deep(.el-select-dropdown) {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

:deep(.el-select-dropdown__item) {
  color: #333;
  background: transparent;
}

:deep(.el-select-dropdown__item:hover) {
  background: rgba(64, 158, 255, 0.1);
  color: #333;
}

:deep(.el-select-dropdown__item.selected) {
  background: rgba(64, 158, 255, 0.2);
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-actions {
    flex-direction: column;
  }

  .layer-operations .el-button-group {
    width: 100%;
  }

  .layer-operations .el-button {
    flex: 1;
  }
}
</style>
