const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/form.CrFRFcCA.js","assets/table.BupIqdAe.js","assets/index.C0-0gsfl.js","assets/vue.CnN__PXn.js","assets/index.CB8Sa4Ps.css","assets/table.BZ9Kkbjw.js"])))=>i.map(i=>d[i]);
import{v as I,d as S,e as D,a as O,c,J as R,__tla as T}from"./index.C0-0gsfl.js";import{c as E,e as F,g as p,h as j,__tla as q}from"./table.BZ9Kkbjw.js";import{d as A,l as L,k as y,A as v,o as M,B as N,a as P,b as V,f as z,t as n,v as r,E as m,u as l,j as G}from"./vue.CnN__PXn.js";let h,H=Promise.all([(()=>{try{return T}catch{}})(),(()=>{try{return q}catch{}})()]).then(async()=>{let u,d;u={class:"layout-padding"},d={class:"layout-padding-auto layout-padding-view"},h=A({__name:"index",setup(K){const b=G(()=>O(()=>import("./form.CrFRFcCA.js").then(async t=>(await t.__tla,t)),__vite__mapDeps([0,1,2,3,4,5]))),{t:f}=I.useI18n(),o=L(),s=y(),_=y(),k=v(["tab","card","table","cascader"]),w=v({languageMenu:!1,externalLink:!1,formTemplates:!1,widgetNameReadonly:!1,eventCollapse:!1,clearDesignerButton:!0,previewFormButton:!1,importJsonButton:!1,exportJsonButton:!1,exportCodeButton:!0,generateSFCButton:!1});M(()=>{C()});const C=()=>{const{tableName:t,dsName:e}=o.query;t&&e?E(e,t).then(a=>{s.value.loadJson(a)}):S().confirm("\u8868\u5355\u521D\u59CB\u5316\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u9009\u62E9\u8868\u70B9\u51FB\u8BBE\u8BA1\u6253\u5F00").then(()=>{D.emit("onCurrentContextmenuClick",Object.assign({},{contextMenuClickId:1,...o}))})},J=t=>{F(t).then(e=>{s.value.loadJson(JSON.parse(e.data.formInfo)),c().success(f("common.optSuccessText"))})},x=()=>{const{tableName:t,dsName:e}=o.query;if(t&&e){const a=s.value.getFormJson();p({dsName:e,tableName:t,formInfo:JSON.stringify(a)}).then(()=>{c().success(f("common.optSuccessText"))})}},B=async()=>{try{const{tableName:t,dsName:e}=o.query;if(!t||!e)throw new Error("\u8868\u540D\u6216\u6570\u636E\u6E90\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A");const a=s.value.getFormJson(),{data:i}=await p({dsName:e,tableName:t,formInfo:JSON.stringify(a)}),g=await j(i.id);R(g,"form.vue")}catch(t){c().error(t.message)}};return(t,e)=>{const a=N("el-button"),i=N("v-form-designer");return V(),P("div",u,[z("div",d,[n(i,{ref_key:"vfDesignerRef",ref:s,"banned-widgets":l(k),"designer-config":l(w)},{customToolButtons:r(()=>[n(a,{link:"",type:"primary",onClick:x},{default:r(()=>e[1]||(e[1]=[m("\u4FDD\u5B58")])),_:1}),n(a,{link:"",type:"primary",onClick:B},{default:r(()=>e[2]||(e[2]=[m("\u5BFC\u51FA")])),_:1}),n(a,{link:"",type:"primary",onClick:e[0]||(e[0]=g=>l(_).openDialog())},{default:r(()=>e[3]||(e[3]=[m("\u5386\u53F2")])),_:1})]),_:1},8,["banned-widgets","designer-config"])]),n(l(b),{ref_key:"formDialogRef",ref:_,onRefresh:J},null,512)])}}})});export{H as __tla,h as default};
