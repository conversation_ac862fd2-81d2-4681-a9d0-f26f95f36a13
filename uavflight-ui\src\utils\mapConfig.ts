/**
 * @file mapConfig.ts
 * @description 地图配置加载工具，用于从后端API加载地图配置文件
 * 替代原先直接从nginx服务器获取配置文件的方式
 */

import axios from 'axios';

// 默认配置
const DEFAULT_CONFIG = {
  baseMapUrl: '/api/map/base-map',
  baseMap2Url: '/api/map/base-map2',
  baseMap3Url: '/api/map/base-map3',
  baseStyleUrl: '/api/map/base-style',
  baseStyle2Url: '/api/map/base-style2',
  baseStyle3Url: '/api/map/base-style3',
};

/**
 * 获取服务器基础URL
 * @returns API服务器基础URL
 */
export function getApiBaseUrl() {
  const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
  const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8080';
  return `http://${geoserverIp}:${geoserverPort}`;
}

/**
 * 加载地图配置文件
 * @param configType 配置文件类型
 * @returns 配置文件内容
 */
export async function loadMapConfigFile(configType: 'baseMap' | 'baseMap2' | 'baseMap3' | 'baseStyle' | 'baseStyle2' | 'baseStyle3') {
  try {
    // 确定API URL
    const baseUrl = getApiBaseUrl();
    let apiUrl = '';

    // 根据配置类型选择对应的API
    switch (configType) {
      case 'baseMap':
        apiUrl = `${baseUrl}${DEFAULT_CONFIG.baseMapUrl}`;
        break;
      case 'baseMap2':
        apiUrl = `${baseUrl}${DEFAULT_CONFIG.baseMap2Url}`;
        break;
      case 'baseMap3':
        apiUrl = `${baseUrl}${DEFAULT_CONFIG.baseMap3Url}`;
        break;
      case 'baseStyle':
        apiUrl = `${baseUrl}${DEFAULT_CONFIG.baseStyleUrl}`;
        break;
      case 'baseStyle2':
        apiUrl = `${baseUrl}${DEFAULT_CONFIG.baseStyle2Url}`;
        break;
      case 'baseStyle3':
        apiUrl = `${baseUrl}${DEFAULT_CONFIG.baseStyle3Url}`;
        break;
    }

    console.log(`从API加载地图配置 ${configType}，URL: ${apiUrl}`);

    // 发送请求并获取配置
    const response = await axios.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error(`加载地图配置 ${configType} 失败:`, error);
    throw new Error(`加载地图配置 ${configType} 失败: ${error.message || '未知错误'}`);
  }
}

/**
 * 同时加载地图配置和样式配置
 * @param mapConfigType 地图配置类型
 * @param styleConfigType 样式配置类型
 * @returns 包含地图配置和样式配置的对象
 */
export async function loadMapAndStyleConfig(
  mapConfigType: 'baseMap' | 'baseMap2' | 'baseMap3',
  styleConfigType: 'baseStyle' | 'baseStyle2' | 'baseStyle3'
) {
  try {
    // 并行加载两个配置文件
    const [mapConfig, styleConfig] = await Promise.all([
      loadMapConfigFile(mapConfigType),
      loadMapConfigFile(styleConfigType),
    ]);

    return { mapConfig, styleConfig };
  } catch (error) {
    console.error('加载地图和样式配置失败:', error);
    throw error;
  }
}

/**
 * 为public/yizhi目录下的组件加载配置文件
 * 这个函数保留了原先从nginx直接获取文件的方式，以便在迁移期间平滑过渡
 * @param configType 配置文件类型
 * @returns 配置文件内容
 */
export async function loadPublicMapConfigFile(configType: 'baseMap' | 'baseMap2' | 'baseMap3' | 'baseStyle' | 'baseStyle2' | 'baseStyle3') {
  try {
    // 获取URL
    const mapServerIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const mapServerPort = import.meta.env.VITE_MAP_SERVER_PORT_NGINX || '81';
    const mapServerUrl = `http://${mapServerIp}:${mapServerPort}`;
    
    // 构建配置文件URL
    const configUrl = `${mapServerUrl}/${configType}.json`;
    console.log(`为yizhi组件从nginx加载地图配置 ${configType}，URL: ${configUrl}`);
    
    // 发送请求并获取配置
    const response = await fetch(configUrl);
    if (!response.ok) {
      throw new Error(`配置文件加载失败: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`加载地图配置 ${configType} 失败:`, error);
    throw new Error(`加载地图配置 ${configType} 失败: ${error.message || '未知错误'}`);
  }
} 