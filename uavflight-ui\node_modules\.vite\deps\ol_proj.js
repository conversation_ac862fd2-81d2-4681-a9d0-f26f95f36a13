import {
  METERS_PER_UNIT,
  Projection_default,
  addCommon,
  addCoordinateTransforms,
  addEquivalentProjections,
  addEquivalentTransforms,
  addProjection,
  addProjections,
  clearAllProjections,
  clearUserProjection,
  cloneTransform,
  createProjection,
  createSafeCoordinateTransform,
  createTransformFromCoordinateTransform,
  disableCoordinateWarning,
  equivalent,
  fromLonLat,
  fromUserCoordinate,
  fromUserExtent,
  fromUserResolution,
  get,
  getPointResolution,
  getTransform,
  getTransformFromProjections,
  getUserProjection,
  identityTransform,
  setUserProjection,
  toLonLat,
  toUserCoordinate,
  toUserExtent,
  toUserResolution,
  transform,
  transformExtent,
  transformWithProjections,
  useGeographic
} from "./chunk-VO4OC6Q7.js";
import "./chunk-IGSFLGV4.js";
import "./chunk-X7AUGEB2.js";
import "./chunk-Q6I4HU3X.js";
import "./chunk-KPFVJIQJ.js";
import "./chunk-PLDDJCW6.js";
export {
  METERS_PER_UNIT,
  Projection_default as Projection,
  addCommon,
  addCoordinateTransforms,
  addEquivalentProjections,
  addEquivalentTransforms,
  addProjection,
  addProjections,
  clearAllProjections,
  clearUserProjection,
  cloneTransform,
  createProjection,
  createSafeCoordinateTransform,
  createTransformFromCoordinateTransform,
  disableCoordinateWarning,
  equivalent,
  fromLonLat,
  fromUserCoordinate,
  fromUserExtent,
  fromUserResolution,
  get,
  getPointResolution,
  getTransform,
  getTransformFromProjections,
  getUserProjection,
  identityTransform,
  setUserProjection,
  toLonLat,
  toUserCoordinate,
  toUserExtent,
  toUserResolution,
  transform,
  transformExtent,
  transformWithProjections,
  useGeographic
};
//# sourceMappingURL=ol_proj.js.map
