
<template>
  <div class="spatial-analysis-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>空间分析管理</h2>
        <span class="subtitle">管理和查看已完成的空间分析任务</span>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Refresh" @click="refreshTasks" :loading="loading">
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 筛选和搜索区域 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索任务ID"
          clearable
          prefix-icon="Search"
          style="width: 300px"
          @input="handleSearch"
        />
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          @change="handleDateFilter"
          style="width: 400px"
        />
      </div>
      <div class="filter-right">
        <el-select
          v-model="sortField"
          placeholder="排序字段"
          style="width: 150px"
          @change="handleSort"
        >
          <el-option label="完成时间" value="endTime" />
          <el-option label="任务ID" value="id" />
        </el-select>
        <el-select
          v-model="sortOrder"
          placeholder="排序方式"
          style="width: 120px"
          @change="handleSort"
        >
          <el-option label="降序" value="desc" />
          <el-option label="升序" value="asc" />
        </el-select>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section" v-if="!loading">
      <div class="stat-item">
        <span class="stat-label">总任务数：</span>
        <span class="stat-value">{{ totalTasks }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已完成：</span>
        <span class="stat-value">{{ completedTasks }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">当前显示：</span>
        <span class="stat-value">{{ filteredTasks.length }}</span>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-section">
      <el-table
        v-loading="loading"
        :data="paginatedTasks"
        stripe
        style="width: 100%"
        :default-sort="{ prop: 'endTime', order: 'descending' }"
      >
        <el-table-column prop="id" label="任务ID" width="180" sortable>
          <template #default="{ row }">
            <el-tag type="primary">{{ row.id }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="endTime" label="完成时间" width="200" sortable>
          <template #default="{ row }">
            <el-icon><Clock /></el-icon>
            {{ formatDateTime(row.endTime) }}
          </template>
        </el-table-column>

        <el-table-column label="空间范围" min-width="300">
          <template #default="{ row }">
            <div class="bbox-info">
              <div class="bbox-row">
                <span class="bbox-label">经度：</span>
                <span class="bbox-value">{{ formatCoordinate(row.bbox.minx) }} ~ {{ formatCoordinate(row.bbox.maxx) }}</span>
              </div>
              <div class="bbox-row">
                <span class="bbox-label">纬度：</span>
                <span class="bbox-value">{{ formatCoordinate(row.bbox.miny) }} ~ {{ formatCoordinate(row.bbox.maxy) }}</span>
              </div>
              <div class="bbox-row">
                <span class="bbox-label">坐标系：</span>
                <span class="bbox-value">{{ row.bbox.crs }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="analysisCount" label="完成分析数" width="120" align="center">
          <template #default="{ row }">
            <el-tag type="info">{{ row.analysisCount }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150" align="center">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewAnalysisDetails(row)"
              :icon="View"
            >
              分析详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredTasks.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 分析详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="分析详情"
      width="80%"
      :close-on-click-modal="false"
    >
      <div v-if="selectedTask" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ selectedTask.id }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ formatDateTime(selectedTask.endTime) }}</el-descriptions-item>
          <el-descriptions-item label="图层名称">{{ selectedTask.layer_name }}</el-descriptions-item>
          <el-descriptions-item label="主题">{{ selectedTask.theme }}</el-descriptions-item>
          <el-descriptions-item label="坐标系">{{ selectedTask.bbox.crs }}</el-descriptions-item>
          <el-descriptions-item label="完成分析数">{{ selectedTask.analysisCount }}</el-descriptions-item>
        </el-descriptions>

        <div class="bbox-detail">
          <h4>空间范围详情</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="coord-item">
                <label>最小经度：</label>
                <span>{{ formatCoordinate(selectedTask.bbox.minx) }}</span>
              </div>
              <div class="coord-item">
                <label>最大经度：</label>
                <span>{{ formatCoordinate(selectedTask.bbox.maxx) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="coord-item">
                <label>最小纬度：</label>
                <span>{{ formatCoordinate(selectedTask.bbox.miny) }}</span>
              </div>
              <div class="coord-item">
                <label>最大纬度：</label>
                <span>{{ formatCoordinate(selectedTask.bbox.maxy) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 这里后续可以添加更多分析详情内容 -->
        <div class="analysis-placeholder">
          <el-empty description="分析详情功能开发中..." />
        </div>
      </div>

      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
// Vue相关导入
import { onMounted, ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Refresh, Clock, View } from '@element-plus/icons-vue';

// 接口类型定义
interface BBox {
  minx: number;
  miny: number;
  maxx: number;
  maxy: number;
  crs: string;
}

interface TaskDetails {
  id: string;
  startTime: string;
  endTime: string;
  status: string;
  map_config: {
    status: string;
    start_time: string;
    end_time: string;
    bbox: BBox;
    layer_id: string;
    theme: string;
  };
}

interface Task {
  task_id: string;
  start_time: string;
  end_time: string;
  status: string;
  has_task_info: boolean;
  details: TaskDetails;
}

interface ApiResponse {
  status: string;
  count: number;
  tasks: Task[];
}

interface ProcessedTask {
  id: string;
  endTime: string;
  bbox: BBox;
  analysisCount: number;
  layer_name: string;
  theme: string;
}

// 响应式数据
const loading = ref(false);
const tasks = ref<Task[]>([]);
const searchQuery = ref('');
const dateRange = ref<[string, string] | null>(null);
const sortField = ref('endTime');
const sortOrder = ref('desc');
const currentPage = ref(1);
const pageSize = ref(20);
const detailDialogVisible = ref(false);
const selectedTask = ref<ProcessedTask | null>(null);

// 计算属性
const totalTasks = computed(() => tasks.value.length);

const completedTasks = computed(() =>
  tasks.value.filter(task => task.status === '完成').length
);

const filteredTasks = computed(() => {
  let filtered = tasks.value
    .filter(task => task.status === '完成' && task.details?.map_config?.bbox)
    .map(task => ({
      id: task.details.id,
      endTime: task.details.endTime,
      bbox: task.details.map_config.bbox,
      analysisCount: 0, // 暂时设为0，后续实现
      layer_name: task.details.map_config.layer_id,
      theme: task.details.map_config.theme
    }));

  // 搜索过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.trim().toLowerCase();
    filtered = filtered.filter(task =>
      task.id.toLowerCase().includes(query)
    );
  }

  // 时间范围过滤
  if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
    const [startDate, endDate] = dateRange.value;
    filtered = filtered.filter(task => {
      const taskTime = new Date(task.endTime).getTime();
      const start = new Date(startDate).getTime();
      const end = new Date(endDate).getTime();
      return taskTime >= start && taskTime <= end;
    });
  }

  // 排序
  filtered.sort((a, b) => {
    let aValue: any, bValue: any;

    if (sortField.value === 'endTime') {
      aValue = new Date(a.endTime).getTime();
      bValue = new Date(b.endTime).getTime();
    } else {
      aValue = a[sortField.value as keyof ProcessedTask];
      bValue = b[sortField.value as keyof ProcessedTask];
    }

    if (sortOrder.value === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  return filtered;
});

const paginatedTasks = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredTasks.value.slice(start, end);
});

// 方法
const fetchTasks = async () => {
  loading.value = true;
  try {
    const geoserverIp = import.meta.env.VITE_MAP_SERVER_IP || '127.0.0.1';
    const geoserverPort = import.meta.env.VITE_GEOSERVER_HD_PORT || '8091';
    const apiUrl = `http://${geoserverIp}:${geoserverPort}/api/map/odm/tasks`;

    console.log('获取ODM任务列表，URL:', apiUrl);

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ApiResponse = await response.json();

    if (data.status === 'success') {
      tasks.value = data.tasks;
      console.log(`成功获取 ${data.count} 个任务，其中 ${completedTasks.value} 个已完成`);
    } else {
      throw new Error('API返回状态不正确');
    }
  } catch (error) {
    console.error('获取任务列表失败:', error);
    ElMessage.error('获取任务列表失败，请检查网络连接');
  } finally {
    loading.value = false;
  }
};

const refreshTasks = async () => {
  await fetchTasks();
  ElMessage.success('数据刷新成功');
};

const handleSearch = () => {
  currentPage.value = 1; // 搜索时重置到第一页
};

const handleDateFilter = () => {
  currentPage.value = 1; // 筛选时重置到第一页
};

const handleSort = () => {
  currentPage.value = 1; // 排序时重置到第一页
};

const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  currentPage.value = 1;
};

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
};

const viewAnalysisDetails = (task: ProcessedTask) => {
  selectedTask.value = task;
  detailDialogVisible.value = true;
};

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

const formatCoordinate = (coord: number) => {
  return coord.toFixed(6);
};

// 生命周期
onMounted(() => {
  fetchTasks();
});
</script>

<style lang="scss" scoped>
.spatial-analysis-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .header-left {
      h2 {
        margin: 0 0 8px 0;
        color: #303133;
        font-size: 24px;
        font-weight: 600;
      }

      .subtitle {
        color: #909399;
        font-size: 14px;
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .filter-left {
      display: flex;
      gap: 16px;
      align-items: center;
    }

    .filter-right {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }

  .stats-section {
    display: flex;
    gap: 24px;
    margin-bottom: 20px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .stat-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .stat-label {
        color: #606266;
        font-size: 14px;
      }

      .stat-value {
        color: #409EFF;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }

  .task-list-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .bbox-info {
      .bbox-row {
        display: flex;
        align-items: center;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }

        .bbox-label {
          color: #909399;
          font-size: 12px;
          width: 50px;
          flex-shrink: 0;
        }

        .bbox-value {
          color: #606266;
          font-size: 12px;
          font-family: 'Courier New', monospace;
        }
      }
    }

    .pagination-section {
      padding: 20px;
      display: flex;
      justify-content: center;
      border-top: 1px solid #ebeef5;
    }
  }

  .detail-content {
    .bbox-detail {
      margin-top: 24px;

      h4 {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }

      .coord-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        label {
          color: #606266;
          font-size: 14px;
          width: 80px;
          flex-shrink: 0;
        }

        span {
          color: #303133;
          font-size: 14px;
          font-family: 'Courier New', monospace;
        }
      }
    }

    .analysis-placeholder {
      margin-top: 24px;
      padding: 40px;
      text-align: center;
      border: 1px dashed #dcdfe6;
      border-radius: 8px;
      background-color: #fafafa;
    }
  }
}

// 全局样式覆盖
:deep(.el-table) {
  .el-table__header {
    background-color: #f8f9fa;
  }

  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

:deep(.el-pagination) {
  .el-pagination__total {
    color: #606266;
  }
}
</style>