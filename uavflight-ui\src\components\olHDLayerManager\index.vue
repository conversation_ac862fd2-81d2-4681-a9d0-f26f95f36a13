<!--
 * @Author: AI Assistant 
 * @Date: 2025-07-13 
 * @Description: 快拼图层管理器组件
 *  
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved. 
-->
<template>
  <div class="hd-layer-manager-container">
    <!-- 快拼图层管理器内容 -->
    <div class="hd-layer-manager-content">
      <!-- 标题栏 -->
      <div class="hd-layer-manager-header">
        <h2 class="hd-layer-manager-title">快拼图层</h2>
      </div>
        
      <!-- 内容区域 -->
      <div class="hd-layer-panels">
        <!-- 左侧内容区域 - 根据选择动态显示不同面板 -->
        <div class="panel-content-area">
          <component :is="currentPanel" v-if="currentPanel"></component>
          <div v-else class="empty-panel">
            <p>请选择右侧面板进行操作</p>
          </div>
        </div>
        
        <!-- 右侧面板选择器 -->
        <div class="panel-selector">
          <div 
            v-for="(panel, index) in panels.filter(p => !p.hidden)" 
            :key="index"
            class="panel-select-item"
            :class="{ 'active': currentPanelName === panel.name }"
            @click="switchPanel(panel.name)"
          >
            <el-tooltip 
              :content="panel.label" 
              placement="left" 
              :enterable="false"
            >
              <div class="panel-icon-wrapper">
                <el-icon><component :is="panel.icon" /></el-icon>
                <div class="panel-label">{{ panel.label }}</div>
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 引用独立弹窗组件 -->
    <LayerQueryDialog 
      :visible="layerQueryDialogVisible" 
      :status="layerQueryStatus"
      :query-layers="layerQueryResult.layers || []"
      :error="layerQueryError"
      :query-location="lastQueryCoords"
      @close="layerQueryDialogVisible = false"
      @retry="retryLayerQuery"
      @toggle-layer="handleToggleLayer"
    />
  </div>
</template>

<script setup>
import { ref, markRaw, onMounted, computed, provide, reactive, onBeforeUnmount } from 'vue';
import { 
  Setting, 
  VideoCamera, 
  Location,
  Loading,
  CircleClose,
  Close,
  Document
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { useHDMapLayerStore } from '/@/stores/hdMapLayer';
import { useOLMapStore } from '/@/stores/olMapStore';
import { transform } from 'ol/proj';
import { buildLayerQueryUrl } from '/@/utils/geoserver';
import axios from 'axios';
import { loadMapConfigFile } from '/@/utils/mapConfig';

// 导入面板组件和弹窗组件
import BaseLayerControl from './panels/BaseLayerControl.vue';
import MapAnnotations from './panels/MapAnnotations.vue';
import DroneVideoLayer from './panels/DroneVideoLayer.vue';
import FeatureAttributesPanel from './panels/FeatureAttributesPanel.vue';
import LayerQueryDialog from './LayerQueryDialog.vue';

// 面板定义
const panels = [
  { name: 'baseLayerControl', label: '图层控制', component: markRaw(BaseLayerControl), icon: Setting },
  { name: 'featureAttributes', label: '要素属性', component: markRaw(FeatureAttributesPanel), icon: Document },
  // { name: 'mapAnnotations', label: '地图标注', component: markRaw(MapAnnotations), icon: Location },
  // { name: 'droneVideo', label: '无人机影像', component: markRaw(DroneVideoLayer), icon: VideoCamera },
 
];

// 当前选中的面板
const currentPanelName = ref('');
const currentPanel = ref(null);

// Store
const hdMapLayerStore = useHDMapLayerStore();
const mapStore = useOLMapStore();

// 图层查询相关
const isQueryModeActive = ref(false);
const layerQueryDialogVisible = ref(false);
const layerQueryStatus = ref('idle');
const layerQueryResult = ref({
  count: 0,
  layers: [],
  status: ''
});
const layerQueryError = ref('');
const defaultWorkspace = ref('');
const lastQueryCoords = ref({ longitude: 0, latitude: 0 });

// 要素属性相关
const featureAttributes = ref({});
const attributeLayerId = ref('');
const attributeTitle = ref('');

// 提供属性数据给面板组件
provide('featureAttributes', featureAttributes);
provide('attributeLayerId', attributeLayerId);
provide('attributeTitle', attributeTitle);
provide('closeAttributesPanel', closeAttributesPanel);

/**
 * 显示要素属性面板
 * @param {Object} data 属性数据
 * @param {string} layerId 图层ID
 * @param {string} title 标题
 */
function showAttributesPanel(data, layerId, title) {
  // 设置属性数据
  featureAttributes.value = data;
  attributeLayerId.value = layerId;
  attributeTitle.value = title;
  
  // 如果当前不在属性面板，则切换到属性面板
  // 如果已经在属性面板，则不进行切换，保持在当前面板
  if (currentPanelName.value !== 'featureAttributes') {
    switchPanel('featureAttributes');
  }
  
  // 触发initialized事件，通知外部组件
  emit('attributes-panel-shown', { layerId });
}

/**
 * 关闭要素属性面板
 */
function closeAttributesPanel() {
  // 返回到图层控制面板
  switchPanel('baseLayerControl');
  
  // 清除属性数据
  featureAttributes.value = {};
  attributeLayerId.value = '';
  attributeTitle.value = '';
}

// 对外暴露的方法
defineExpose({
  showAttributesPanel,
  closeAttributesPanel
});

// 加载配置
async function loadConfig() {
  try {
    // 从新的API接口加载baseMap3配置
    console.log('正在加载快拼图层查询配置...');
    
    const config = await loadMapConfigFile('baseMap3');
    
    if (config.mapConfig?.queryConfig?.defaultWorkspace) {
      defaultWorkspace.value = config.mapConfig.queryConfig.defaultWorkspace;
      console.log('已设置默认工作空间:', defaultWorkspace.value);
    } else {
      // 如果没有找到配置，使用默认值
      defaultWorkspace.value = 'test_myworkspace';
      console.warn('未在配置中找到默认工作空间，使用默认值:', defaultWorkspace.value);
    }
  } catch (error) {
    console.error('加载高精度地图配置失败:', error);
    // 设置默认值
    defaultWorkspace.value = 'test_myworkspace';
  }
}

// 过滤后的查询图层（排除已加载的图层）
const filteredQueryLayers = computed(() => {
  if (!layerQueryResult.value.layers) return [];
  
  // 返回所有图层，已加载的图层也显示但标记为已加载
  return layerQueryResult.value.layers;
});

// 切换面板
const switchPanel = (panelName) => {
  if (currentPanelName.value === panelName) {
    // 如果点击的是当前激活的面板，则取消选择
    currentPanelName.value = '';
    currentPanel.value = null;
  } else {
    // 切换到新面板
    currentPanelName.value = panelName;
    const selectedPanel = panels.find(p => p.name === panelName);
    currentPanel.value = selectedPanel ? selectedPanel.component : null;
    console.log(`已切换到${selectedPanel?.label || ''}面板`);
  }
};

// 切换位置查询模式
function toggleQueryMode() {
  isQueryModeActive.value = !isQueryModeActive.value;
  
  if (isQueryModeActive.value) {
    // 启用查询模式时，修改鼠标样式
    if (mapStore.map?.mapContainer) {
      mapStore.map.mapContainer.style.cursor = 'crosshair';
    }
    
    // 添加地图点击事件监听器
    if (mapStore.map?.map) {
      mapStore.map.map.on('click', handleMapClick);
    }
    
    // 显示激活提示
    ElMessage({
      message: '查询模式已激活，点击地图位置查询图层，再次点击按钮可关闭',
      type: 'success',
      duration: 3000
    });
  } else {
    // 禁用查询模式时，恢复默认鼠标样式
    if (mapStore.map?.mapContainer) {
      mapStore.map.mapContainer.style.cursor = '';
    }
    
    // 移除地图点击事件监听器
    if (mapStore.map?.map) {
      mapStore.map.map.un('click', handleMapClick);
    }
    
    // 显示停用提示
    ElMessage({
      message: '查询模式已关闭',
      type: 'info',
      duration: 2000
    });
  }
}

// 处理地图点击事件
async function handleMapClick(event) {
  // 获取点击位置的坐标（Web墨卡托投影）
  const clickCoord = event.coordinate;
  
  // 将坐标从Web墨卡托投影转换为WGS84经纬度
  const wgs84Coord = transform(clickCoord, 'EPSG:3857', 'EPSG:4326');
  
  // 更新查询结果
  const queryResult = {
    longitude: wgs84Coord[0],
    latitude: wgs84Coord[1]
  };
  
  // 保存当前查询坐标
  lastQueryCoords.value = { ...queryResult };
  
  // 查询该位置的图层
  await queryLayersAtLocation(wgs84Coord[1], wgs84Coord[0]);
}

// 处理图层切换状态
function handleToggleLayer({ layer, checked }) {
  try {
    const layerConfig = hdMapLayerStore.mapConfig?.layers.find(l => l.id === layer.id);
    if (!layerConfig) {
      console.error(`图层配置未找到：${layer.id}`);
      return;
    }

    // 根据勾选状态加载或移除图层
    if (checked) {
      // 加载图层
      layerConfig.visible = true;
      const layerInstance = hdMapLayerStore.layerManager?.addLayer(layerConfig);
      if (layerInstance) {
        updateLayerVisibility(layer.id, true);
        ElMessage.success(`已加载图层: ${getLayerName(layer.id)}`);
      } else {
        ElMessage.error(`图层加载失败: ${getLayerName(layer.id)}`);
        // 恢复勾选状态，避免UI与实际状态不符
        const layerInResult = layerQueryResult.value.layers.find(l => l.id === layer.id);
        if (layerInResult) {
          layerInResult.selected = false;
        }
      }
    } else {
      // 移除图层
      layerConfig.visible = false;
      hdMapLayerStore.layerManager?.removeLayer(layer.id);
      updateLayerVisibility(layer.id, false);
      ElMessage.success(`已移除图层: ${getLayerName(layer.id)}`);
    }

    // 刷新图层组状态
    hdMapLayerStore.refreshLayerGroups();
  } catch (error) {
    console.error(`处理图层状态切换失败:`, error);
    ElMessage.error('操作失败，请重试');
  }
}

// 查询指定位置的图层
async function queryLayersAtLocation(lat, lon) {
  layerQueryStatus.value = 'loading';
  layerQueryDialogVisible.value = true;
  
  try {
    // 使用工具函数构建查询URL
    const apiUrl = buildLayerQueryUrl(lat, lon, defaultWorkspace.value);
    const response = await axios.get(apiUrl);
    
    if (response.data.status === 'success') {
      // 先保存原始结果
      const originalResult = response.data;
      
      // 获取当前项目配置中的所有图层ID
      const configLayers = hdMapLayerStore.mapConfig?.layers || [];
      const configLayerIds = configLayers.map(layer => layer.id);
      
      // 过滤查询结果，只保留存在于配置中的图层
      const filteredLayers = originalResult.layers.filter(layer => 
        configLayerIds.includes(layer.id)
      );
      
      // 更新过滤后的结果
      layerQueryResult.value = {
        ...originalResult,
        count: filteredLayers.length,
        layers: filteredLayers
      };
      
      // 记录过滤情况
      const filteredOutCount = originalResult.count - filteredLayers.length;
      if (filteredOutCount > 0) {
        console.log(`已过滤掉 ${filteredOutCount} 个不在项目配置中的图层`);
      }
      
      // 为每个图层添加selected属性，已加载的图层默认勾选
      layerQueryResult.value.layers.forEach(layer => {
        layer.selected = isLayerLoaded(layer.id);
      });
      
      if (layerQueryResult.value.layers.length === 0) {
        // 没有找到图层
        ElMessage({
          message: '该位置未找到图层',
          type: 'info',
          duration: 3000
        });
      }
      
      layerQueryStatus.value = 'success';
    } else {
      layerQueryError.value = response.data.message || '查询失败';
      layerQueryStatus.value = 'error';
    }
  } catch (error) {
    console.error('图层查询失败:', error);
    layerQueryError.value = error instanceof Error ? error.message : '网络请求失败';
    layerQueryStatus.value = 'error';
  }
}

// 重试图层查询
async function retryLayerQuery() {
  await queryLayersAtLocation(lastQueryCoords.value.latitude, lastQueryCoords.value.longitude);
}

// 检查图层是否已加载
function isLayerLoaded(layerId) {
  return hdMapLayerStore.layerGroups.some(group => 
    group.layers.some(layer => layer.id === layerId && layer.visible)
  );
}

// 获取图层名称
function getLayerName(layerId) {
  const configLayers = hdMapLayerStore.mapConfig?.layers || [];
  const layer = configLayers.find(layer => layer.id === layerId);
  return layer?.name || layerId;
}

// 获取图层主题
function getLayerTheme(layerId) {
  const configLayers = hdMapLayerStore.mapConfig?.layers || [];
  const layer = configLayers.find(layer => layer.id === layerId);
  return layer?.theme || '';
}

// 更新图层可见性状态
function updateLayerVisibility(layerId, visible) {
  // 遍历所有图层组，找到对应图层并更新状态
  hdMapLayerStore.layerGroups.forEach(group => {
    const layer = group.layers.find(l => l.id === layerId);
    if (layer) {
      layer.visible = visible;
    }
  });
}

// 向子组件提供图层查询功能
provide('layerQuery', {
  toggleQueryMode,
  isQueryModeActive
});

// 组件加载后初始化
onMounted(async () => {
  console.log('快拼图层管理器已加载');
  
  // 加载地图配置获取默认工作空间
  await loadConfig();
  
  // 默认选中第一个面板
  if (panels.length > 0) {
    switchPanel(panels[0].name);
  }
  
  // 通知父组件初始化完成
  emit('initialized', 0);
});

// 在组件卸载前清理
onBeforeUnmount(() => {
  // 如果组件卸载时查询模式仍然激活，确保关闭查询模式
  if (isQueryModeActive.value) {
    toggleQueryMode();
  }
});

// 定义事件
const emit = defineEmits(['initialized']);

// 接收属性
const props = defineProps({
    position: {
      type: String,
      default: 'right'
    },
    visible: {
      type: Boolean,
      default: true
    },
    gisToolsRef: {
      type: Object,
      default: null
    }
});
</script>

<style lang="scss">
@import './index.scss';

// 面板区域样式
.hd-layer-panels {
  flex: 1;
  display: flex;
  width: 100%;
  height: calc(100% - 42px); // 减去标题栏高度
  overflow: hidden;
}

// 左侧内容区域
.panel-content-area {
  flex: 1;
  overflow: hidden;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

// 右侧面板选择器
.panel-selector {
  width: 80px;
  background-color: rgba(11, 18, 32, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 0;
}

// 面板选择项
.panel-select-item {
  width: 100%;
  height: 70px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 5px;
}

.panel-select-item:hover {
  background-color: rgba(64, 158, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.panel-select-item.active {
  background-color: rgba(64, 158, 255, 0.2);
  border-left-color: #409EFF;
  color: #fff;
}

// 图标样式
.panel-icon-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.panel-icon-wrapper .el-icon {
  font-size: 22px;
}

.panel-label {
  font-size: 12px;
  white-space: nowrap;
  text-align: center;
}

// 空面板状态
.empty-panel {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

// 图层查询弹窗样式
.custom-layer-query-dialog {
  position: fixed;
  width: 500px;
  background-color: #1a2133;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.5);
  color: white;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-height: 80vh;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background-color: #1a2133;
    cursor: move;
    user-select: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .dialog-title {
      font-size: 16px;
      font-weight: bold;
      color: white;
    }

    .dialog-close {
      font-size: 20px;
      color: rgba(255, 255, 255, 0.8);
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: white;
      }
    }
  }

  .dialog-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #1a2133;
    text-align: left;
    color: white;
    max-height: calc(80vh - 50px);
  }
  
  .query-loading, .query-error, .no-layers-found {
    text-align: center;
    padding: 20px 0;
    
    .el-icon {
      font-size: 32px;
      margin-bottom: 10px;
    }
  }
  
  .query-error {
    .el-icon {
      color: #f56c6c;
    }
  }
  
  .result-summary {
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  .loaded-tag {
    margin: 0 auto;
    display: block;
    text-align: center;
  }

  .layer-status {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: bold;
  }

  .layer-status.loaded {
    background-color: #67C23A;
    color: white;
  }

  .layer-status.unloaded {
    background-color: #F56C6C;
    color: white;
  }
  
  .dialog-footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
  
  :deep(.el-table) {
    background-color: transparent;
    color: white;
    
    .el-table__header th {
      background-color: #1a2133;
      color: white;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .el-table__row {
      background-color: transparent;
      
      &:hover > td {
        background-color: rgba(255, 255, 255, 0.1) !important;
      }
      
      td {
        background-color: transparent;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
      }
    }
  }
}
</style> 