/*
 * @Description: 地图图层管理类型定义
 */
import * as Cesium from 'cesium';

// 定义图层参数接口
export interface LayerParameters {
	service?: string;
	format?: string;
	transparent?: boolean;
	[key: string]: any;
}

// 定义图层样式接口
export interface LayerStyle {
	// Cesium点样式
	point?: {
		color?: string; // CSS颜色
		pixelSize?: number;
		outlineColor?: string; // CSS颜色
		outlineWidth?: number;
		show?: boolean;
		heightReference?: number; // 增加高度参考，使用Cesium.HeightReference枚举
	};
	// Cesium线样式
	polyline?: {
		material?: {
			color?: string; // CSS颜色
			dashLength?: number; // 虚线长度
			dashPattern?: number; // 虚线模式
		} | string; // 颜色字符串快捷方式
		width?: number;
		clampToGround?: boolean; // 是否贴地
		show?: boolean;
		zIndex?: number; // 叠加顺序
	};
	// Cesium面样式
	polygon?: {
		material?: {
			color?: string; // CSS颜色
		} | string; // 颜色字符串快捷方式
		outline?: boolean;
		outlineColor?: string; // CSS颜色
		outlineWidth?: number;
		show?: boolean;
		height?: number; // 多边形高度
		heightReference?: number; // 高度参考，使用Cesium.HeightReference枚举
		extrudedHeight?: number; // 拉伸高度
		perPositionHeight?: boolean; // 使用每个点的高度
		clampToGround?: boolean; // 是否贴地
	};
	// Cesium标签样式
	label?: {
		text?: string;
		font?: string;
		style?: string; // 'FILL' | 'OUTLINE' | 'FILL_AND_OUTLINE'
		fillColor?: string; // CSS颜色
		outlineColor?: string; // CSS颜色
		outlineWidth?: number;
		show?: boolean;
		pixelOffset?: [number, number];
		eyeOffset?: [number, number, number];
		horizontalOrigin?: string; // 'LEFT' | 'CENTER' | 'RIGHT'
		verticalOrigin?: string; // 'TOP' | 'CENTER' | 'BASELINE' | 'BOTTOM'
		scale?: number;
		heightReference?: number; // 高度参考，使用Cesium.HeightReference枚举
	};
	// Cesium广告牌样式
	billboard?: {
		image?: string; // 图片URL
		width?: number;
		height?: number;
		color?: string; // CSS颜色
		scale?: number;
		rotation?: number;
		horizontalOrigin?: string; // 'LEFT' | 'CENTER' | 'RIGHT' 
		verticalOrigin?: string; // 'TOP' | 'CENTER' | 'BASELINE' | 'BOTTOM'
		pixelOffset?: [number, number];
		eyeOffset?: [number, number, number];
		show?: boolean;
		heightReference?: number; // 高度参考，使用Cesium.HeightReference枚举
	};
}

// 定义过滤条件接口
export interface FilterCondition {
	property: string;
	operator: '=' | '!==' | '!=' | '>' | '<' | '>=' | '<=' | 'between' | 'inRange' | 'in' | 'notIn';
	value: string | number | boolean | string[] | number[] | [number, number];
	min?: number;
	max?: number;
}

// 定义样式规则接口
export interface StyleRule {
	filter: FilterCondition;
	style: LayerStyle | string;  // 可以是样式对象或样式名称字符串
}

// 定义地图图层接口
export interface MapLayer {
	id: string;
	name: string;
	type: 'raster' | 'vector' | 'CustomPoint' | 'terrain';
	protocol?: 'XYZ' | 'WMS' | 'WMTS' | 'WFS' | 'GeoJSON' | 'MVT' | 'CustomPointJSON' | 'DEM';
	url: string;
	initialLoad: boolean;
	// 矢量图层的几何类型
	geometryType?: 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon';
	// WMS 特定属性
	layers?: string;
	parameters?: LayerParameters;
	// MVT 特定属性
	mvtStyle?: (feature: any, tileCoords: any) => {
		color?: string;
		weight?: number;
		opacity?: number;
		fillColor?: string;
		fillOpacity?: number;
	};
	// 自定义点JSON特定属性
	categories?: string[];
	// 其他属性
	defaultStyle?: LayerStyle | string;  // 可以是样式对象或样式名称字符串
	styleRules?: StyleRule[];
	wfsParams?: any; // 使用WfsParams类型
	active?: boolean;
	layerInstance?: any;
	// 事件标识
	event?: string;
	// 主题分类
	theme?: string;
	// 是否贴地
	clampToGround?: boolean;
}

// 定义地图配置接口
export interface MapConfig {
	layers: MapLayer[];
} 