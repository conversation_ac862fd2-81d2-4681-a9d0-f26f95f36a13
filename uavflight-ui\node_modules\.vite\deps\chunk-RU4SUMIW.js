import {
  Feature_default as Feature_default2,
  transformGeometryWithOptions
} from "./chunk-KAQLSQQJ.js";
import {
  Feature_default
} from "./chunk-CNS5CUNB.js";
import {
  GeometryCollection_default,
  LineString_default,
  MultiLineString_default,
  MultiPoint_default,
  MultiPolygon_default
} from "./chunk-UI5YC7UD.js";
import {
  Point_default,
  Polygon_default
} from "./chunk-YTA5E23K.js";
import {
  abstract
} from "./chunk-R3IGJG57.js";
import {
  get
} from "./chunk-UCUVXMCA.js";
import {
  isEmpty
} from "./chunk-X7AUGEB2.js";
import {
  assert
} from "./chunk-F2MRU6YO.js";

// node_modules/ol/format/JSONFeature.js
var JSONFeature = class extends Feature_default2 {
  constructor() {
    super();
  }
  /**
   * @return {import("./Feature.js").Type} Format.
   */
  getType() {
    return "json";
  }
  /**
   * Read a feature.  Only works for a single feature. Use `readFeatures` to
   * read a feature collection.
   *
   * @param {ArrayBuffer|Document|Element|Object|string} source Source.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @return {import("../Feature.js").default} Feature.
   * @api
   */
  readFeature(source, options) {
    return this.readFeatureFromObject(
      getObject(source),
      this.getReadOptions(source, options)
    );
  }
  /**
   * Read all features.  Works with both a single feature and a feature
   * collection.
   *
   * @param {ArrayBuffer|Document|Element|Object|string} source Source.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @return {Array<import("../Feature.js").default>} Features.
   * @api
   */
  readFeatures(source, options) {
    return this.readFeaturesFromObject(
      getObject(source),
      this.getReadOptions(source, options)
    );
  }
  /**
   * @abstract
   * @param {Object} object Object.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {import("../Feature.js").default} Feature.
   */
  readFeatureFromObject(object, options) {
    return abstract();
  }
  /**
   * @abstract
   * @param {Object} object Object.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {Array<import("../Feature.js").default>} Features.
   */
  readFeaturesFromObject(object, options) {
    return abstract();
  }
  /**
   * Read a geometry.
   *
   * @param {ArrayBuffer|Document|Element|Object|string} source Source.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @return {import("../geom/Geometry.js").default} Geometry.
   * @api
   */
  readGeometry(source, options) {
    return this.readGeometryFromObject(
      getObject(source),
      this.getReadOptions(source, options)
    );
  }
  /**
   * @abstract
   * @param {Object} object Object.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {import("../geom/Geometry.js").default} Geometry.
   */
  readGeometryFromObject(object, options) {
    return abstract();
  }
  /**
   * Read the projection.
   *
   * @param {ArrayBuffer|Document|Element|Object|string} source Source.
   * @return {import("../proj/Projection.js").default} Projection.
   * @api
   */
  readProjection(source) {
    return this.readProjectionFromObject(getObject(source));
  }
  /**
   * @abstract
   * @param {Object} object Object.
   * @protected
   * @return {import("../proj/Projection.js").default} Projection.
   */
  readProjectionFromObject(object) {
    return abstract();
  }
  /**
   * Encode a feature as string.
   *
   * @param {import("../Feature.js").default} feature Feature.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {string} Encoded feature.
   * @api
   */
  writeFeature(feature, options) {
    return JSON.stringify(this.writeFeatureObject(feature, options));
  }
  /**
   * @abstract
   * @param {import("../Feature.js").default} feature Feature.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {Object} Object.
   */
  writeFeatureObject(feature, options) {
    return abstract();
  }
  /**
   * Encode an array of features as string.
   *
   * @param {Array<import("../Feature.js").default>} features Features.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {string} Encoded features.
   * @api
   */
  writeFeatures(features, options) {
    return JSON.stringify(this.writeFeaturesObject(features, options));
  }
  /**
   * @abstract
   * @param {Array<import("../Feature.js").default>} features Features.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {Object} Object.
   */
  writeFeaturesObject(features, options) {
    return abstract();
  }
  /**
   * Encode a geometry as string.
   *
   * @param {import("../geom/Geometry.js").default} geometry Geometry.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {string} Encoded geometry.
   * @api
   */
  writeGeometry(geometry, options) {
    return JSON.stringify(this.writeGeometryObject(geometry, options));
  }
  /**
   * @abstract
   * @param {import("../geom/Geometry.js").default} geometry Geometry.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {Object} Object.
   */
  writeGeometryObject(geometry, options) {
    return abstract();
  }
};
function getObject(source) {
  if (typeof source === "string") {
    const object = JSON.parse(source);
    return object ? (
      /** @type {Object} */
      object
    ) : null;
  }
  if (source !== null) {
    return source;
  }
  return null;
}
var JSONFeature_default = JSONFeature;

// node_modules/ol/format/GeoJSON.js
var GeoJSON = class extends JSONFeature_default {
  /**
   * @param {Options} [options] Options.
   */
  constructor(options) {
    options = options ? options : {};
    super();
    this.dataProjection = get(
      options.dataProjection ? options.dataProjection : "EPSG:4326"
    );
    if (options.featureProjection) {
      this.defaultFeatureProjection = get(options.featureProjection);
    }
    this.geometryName_ = options.geometryName;
    this.extractGeometryName_ = options.extractGeometryName;
    this.supportedMediaTypes = [
      "application/geo+json",
      "application/vnd.geo+json"
    ];
  }
  /**
   * @param {Object} object Object.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {import("../Feature.js").default} Feature.
   */
  readFeatureFromObject(object, options) {
    let geoJSONFeature = null;
    if (object["type"] === "Feature") {
      geoJSONFeature = /** @type {GeoJSONFeature} */
      object;
    } else {
      geoJSONFeature = {
        "type": "Feature",
        "geometry": (
          /** @type {GeoJSONGeometry} */
          object
        ),
        "properties": null
      };
    }
    const geometry = readGeometry(geoJSONFeature["geometry"], options);
    const feature = new Feature_default();
    if (this.geometryName_) {
      feature.setGeometryName(this.geometryName_);
    } else if (this.extractGeometryName_ && "geometry_name" in geoJSONFeature !== void 0) {
      feature.setGeometryName(geoJSONFeature["geometry_name"]);
    }
    feature.setGeometry(geometry);
    if ("id" in geoJSONFeature) {
      feature.setId(geoJSONFeature["id"]);
    }
    if (geoJSONFeature["properties"]) {
      feature.setProperties(geoJSONFeature["properties"], true);
    }
    return feature;
  }
  /**
   * @param {Object} object Object.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {Array<Feature>} Features.
   */
  readFeaturesFromObject(object, options) {
    const geoJSONObject = (
      /** @type {GeoJSONObject} */
      object
    );
    let features = null;
    if (geoJSONObject["type"] === "FeatureCollection") {
      const geoJSONFeatureCollection = (
        /** @type {GeoJSONFeatureCollection} */
        object
      );
      features = [];
      const geoJSONFeatures = geoJSONFeatureCollection["features"];
      for (let i = 0, ii = geoJSONFeatures.length; i < ii; ++i) {
        features.push(this.readFeatureFromObject(geoJSONFeatures[i], options));
      }
    } else {
      features = [this.readFeatureFromObject(object, options)];
    }
    return features;
  }
  /**
   * @param {GeoJSONGeometry} object Object.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {import("../geom/Geometry.js").default} Geometry.
   */
  readGeometryFromObject(object, options) {
    return readGeometry(object, options);
  }
  /**
   * @param {Object} object Object.
   * @protected
   * @return {import("../proj/Projection.js").default} Projection.
   */
  readProjectionFromObject(object) {
    const crs = object["crs"];
    let projection;
    if (crs) {
      if (crs["type"] == "name") {
        projection = get(crs["properties"]["name"]);
      } else if (crs["type"] === "EPSG") {
        projection = get("EPSG:" + crs["properties"]["code"]);
      } else {
        assert(false, 36);
      }
    } else {
      projection = this.dataProjection;
    }
    return (
      /** @type {import("../proj/Projection.js").default} */
      projection
    );
  }
  /**
   * Encode a feature as a GeoJSON Feature object.
   *
   * @param {import("../Feature.js").default} feature Feature.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {GeoJSONFeature} Object.
   * @api
   */
  writeFeatureObject(feature, options) {
    options = this.adaptOptions(options);
    const object = {
      "type": "Feature",
      geometry: null,
      properties: null
    };
    const id = feature.getId();
    if (id !== void 0) {
      object.id = id;
    }
    if (!feature.hasProperties()) {
      return object;
    }
    const properties = feature.getProperties();
    const geometry = feature.getGeometry();
    if (geometry) {
      object.geometry = writeGeometry(geometry, options);
      delete properties[feature.getGeometryName()];
    }
    if (!isEmpty(properties)) {
      object.properties = properties;
    }
    return object;
  }
  /**
   * Encode an array of features as a GeoJSON object.
   *
   * @param {Array<import("../Feature.js").default>} features Features.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {GeoJSONFeatureCollection} GeoJSON Object.
   * @api
   */
  writeFeaturesObject(features, options) {
    options = this.adaptOptions(options);
    const objects = [];
    for (let i = 0, ii = features.length; i < ii; ++i) {
      objects.push(this.writeFeatureObject(features[i], options));
    }
    return {
      type: "FeatureCollection",
      features: objects
    };
  }
  /**
   * Encode a geometry as a GeoJSON object.
   *
   * @param {import("../geom/Geometry.js").default} geometry Geometry.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {GeoJSONGeometry|GeoJSONGeometryCollection} Object.
   * @api
   */
  writeGeometryObject(geometry, options) {
    return writeGeometry(geometry, this.adaptOptions(options));
  }
};
function readGeometry(object, options) {
  if (!object) {
    return null;
  }
  let geometry;
  switch (object["type"]) {
    case "Point": {
      geometry = readPointGeometry(
        /** @type {GeoJSONPoint} */
        object
      );
      break;
    }
    case "LineString": {
      geometry = readLineStringGeometry(
        /** @type {GeoJSONLineString} */
        object
      );
      break;
    }
    case "Polygon": {
      geometry = readPolygonGeometry(
        /** @type {GeoJSONPolygon} */
        object
      );
      break;
    }
    case "MultiPoint": {
      geometry = readMultiPointGeometry(
        /** @type {GeoJSONMultiPoint} */
        object
      );
      break;
    }
    case "MultiLineString": {
      geometry = readMultiLineStringGeometry(
        /** @type {GeoJSONMultiLineString} */
        object
      );
      break;
    }
    case "MultiPolygon": {
      geometry = readMultiPolygonGeometry(
        /** @type {GeoJSONMultiPolygon} */
        object
      );
      break;
    }
    case "GeometryCollection": {
      geometry = readGeometryCollectionGeometry(
        /** @type {GeoJSONGeometryCollection} */
        object
      );
      break;
    }
    default: {
      throw new Error("Unsupported GeoJSON type: " + object["type"]);
    }
  }
  return transformGeometryWithOptions(geometry, false, options);
}
function readGeometryCollectionGeometry(object, options) {
  const geometries = object["geometries"].map(
    /**
     * @param {GeoJSONGeometry} geometry Geometry.
     * @return {import("../geom/Geometry.js").default} geometry Geometry.
     */
    function(geometry) {
      return readGeometry(geometry, options);
    }
  );
  return new GeometryCollection_default(geometries);
}
function readPointGeometry(object) {
  return new Point_default(object["coordinates"]);
}
function readLineStringGeometry(object) {
  return new LineString_default(object["coordinates"]);
}
function readMultiLineStringGeometry(object) {
  return new MultiLineString_default(object["coordinates"]);
}
function readMultiPointGeometry(object) {
  return new MultiPoint_default(object["coordinates"]);
}
function readMultiPolygonGeometry(object) {
  return new MultiPolygon_default(object["coordinates"]);
}
function readPolygonGeometry(object) {
  return new Polygon_default(object["coordinates"]);
}
function writeGeometry(geometry, options) {
  geometry = transformGeometryWithOptions(geometry, true, options);
  const type = geometry.getType();
  let geoJSON;
  switch (type) {
    case "Point": {
      geoJSON = writePointGeometry(
        /** @type {Point} */
        geometry,
        options
      );
      break;
    }
    case "LineString": {
      geoJSON = writeLineStringGeometry(
        /** @type {LineString} */
        geometry,
        options
      );
      break;
    }
    case "Polygon": {
      geoJSON = writePolygonGeometry(
        /** @type {Polygon} */
        geometry,
        options
      );
      break;
    }
    case "MultiPoint": {
      geoJSON = writeMultiPointGeometry(
        /** @type {MultiPoint} */
        geometry,
        options
      );
      break;
    }
    case "MultiLineString": {
      geoJSON = writeMultiLineStringGeometry(
        /** @type {MultiLineString} */
        geometry,
        options
      );
      break;
    }
    case "MultiPolygon": {
      geoJSON = writeMultiPolygonGeometry(
        /** @type {MultiPolygon} */
        geometry,
        options
      );
      break;
    }
    case "GeometryCollection": {
      geoJSON = writeGeometryCollectionGeometry(
        /** @type {GeometryCollection} */
        geometry,
        options
      );
      break;
    }
    case "Circle": {
      geoJSON = {
        type: "GeometryCollection",
        geometries: []
      };
      break;
    }
    default: {
      throw new Error("Unsupported geometry type: " + type);
    }
  }
  return geoJSON;
}
function writeGeometryCollectionGeometry(geometry, options) {
  options = Object.assign({}, options);
  delete options.featureProjection;
  const geometries = geometry.getGeometriesArray().map(function(geometry2) {
    return writeGeometry(geometry2, options);
  });
  return {
    type: "GeometryCollection",
    geometries
  };
}
function writeLineStringGeometry(geometry, options) {
  return {
    type: "LineString",
    coordinates: geometry.getCoordinates()
  };
}
function writeMultiLineStringGeometry(geometry, options) {
  return {
    type: "MultiLineString",
    coordinates: geometry.getCoordinates()
  };
}
function writeMultiPointGeometry(geometry, options) {
  return {
    type: "MultiPoint",
    coordinates: geometry.getCoordinates()
  };
}
function writeMultiPolygonGeometry(geometry, options) {
  let right;
  if (options) {
    right = options.rightHanded;
  }
  return {
    type: "MultiPolygon",
    coordinates: geometry.getCoordinates(right)
  };
}
function writePointGeometry(geometry, options) {
  return {
    type: "Point",
    coordinates: geometry.getCoordinates()
  };
}
function writePolygonGeometry(geometry, options) {
  let right;
  if (options) {
    right = options.rightHanded;
  }
  return {
    type: "Polygon",
    coordinates: geometry.getCoordinates(right)
  };
}
var GeoJSON_default = GeoJSON;

export {
  JSONFeature_default,
  GeoJSON_default
};
//# sourceMappingURL=chunk-RU4SUMIW.js.map
