{"version": 3, "sources": ["../../ol/style/Fill.js", "../../ol/style/Stroke.js", "../../ol/style/Image.js", "../../ol/colorlike.js", "../../ol/style/RegularShape.js", "../../ol/style/Circle.js", "../../ol/style/Style.js", "../../ol/style/Text.js", "../../ol/style/IconImage.js", "../../ol/style/Icon.js"], "sourcesContent": ["/**\n * @module ol/style/Fill\n */\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike|null} [color=null] A color, gradient or pattern.\n * See {@link module:ol/color~Color} and {@link module:ol/colorlike~ColorLike} for possible formats.\n * Default null; if null, the Canvas/renderer default black will be used.\n */\n\n/**\n * @classdesc\n * Set fill style for vector features.\n * @api\n */\nclass Fill {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options || {};\n\n    /**\n     * @private\n     * @type {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike|null}\n     */\n    this.color_ = options.color !== undefined ? options.color : null;\n  }\n\n  /**\n   * Clones the style. The color is not cloned if it is an {@link module:ol/colorlike~ColorLike}.\n   * @return {Fill} The cloned style.\n   * @api\n   */\n  clone() {\n    const color = this.getColor();\n    return new Fill({\n      color: Array.isArray(color) ? color.slice() : color || undefined,\n    });\n  }\n\n  /**\n   * Get the fill color.\n   * @return {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike|null} Color.\n   * @api\n   */\n  getColor() {\n    return this.color_;\n  }\n\n  /**\n   * Set the color.\n   *\n   * @param {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike|null} color Color.\n   * @api\n   */\n  setColor(color) {\n    this.color_ = color;\n  }\n}\n\nexport default Fill;\n", "/**\n * @module ol/style/Stroke\n */\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike} [color] A color, gradient or pattern.\n * See {@link module:ol/color~Color} and {@link module:ol/colorlike~ColorLike} for possible formats.\n * Default null; if null, the Canvas/renderer default black will be used.\n * @property {CanvasLineCap} [lineCap='round'] Line cap style: `butt`, `round`, or `square`.\n * @property {CanvasLineJoin} [lineJoin='round'] Line join style: `bevel`, `round`, or `miter`.\n * @property {Array<number>} [lineDash] Line dash pattern. Default is `null` (no dash).\n * @property {number} [lineDashOffset=0] Line dash offset.\n * @property {number} [miterLimit=10] Miter limit.\n * @property {number} [width] Width.\n */\n\n/**\n * @classdesc\n * Set stroke style for vector features.\n * Note that the defaults given are the Canvas defaults, which will be used if\n * option is not defined. The `get` functions return whatever was entered in\n * the options; they will not return the default.\n * @api\n */\nclass Stroke {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options || {};\n\n    /**\n     * @private\n     * @type {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike}\n     */\n    this.color_ = options.color !== undefined ? options.color : null;\n\n    /**\n     * @private\n     * @type {CanvasLineCap|undefined}\n     */\n    this.lineCap_ = options.lineCap;\n\n    /**\n     * @private\n     * @type {Array<number>|null}\n     */\n    this.lineDash_ = options.lineDash !== undefined ? options.lineDash : null;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.lineDashOffset_ = options.lineDashOffset;\n\n    /**\n     * @private\n     * @type {CanvasLineJoin|undefined}\n     */\n    this.lineJoin_ = options.lineJoin;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.miterLimit_ = options.miterLimit;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.width_ = options.width;\n  }\n\n  /**\n   * Clones the style.\n   * @return {Stroke} The cloned style.\n   * @api\n   */\n  clone() {\n    const color = this.getColor();\n    return new Stroke({\n      color: Array.isArray(color) ? color.slice() : color || undefined,\n      lineCap: this.getLineCap(),\n      lineDash: this.getLineDash() ? this.getLineDash().slice() : undefined,\n      lineDashOffset: this.getLineDashOffset(),\n      lineJoin: this.getLineJoin(),\n      miterLimit: this.getMiterLimit(),\n      width: this.getWidth(),\n    });\n  }\n\n  /**\n   * Get the stroke color.\n   * @return {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike} Color.\n   * @api\n   */\n  getColor() {\n    return this.color_;\n  }\n\n  /**\n   * Get the line cap type for the stroke.\n   * @return {CanvasLineCap|undefined} Line cap.\n   * @api\n   */\n  getLineCap() {\n    return this.lineCap_;\n  }\n\n  /**\n   * Get the line dash style for the stroke.\n   * @return {Array<number>|null} Line dash.\n   * @api\n   */\n  getLineDash() {\n    return this.lineDash_;\n  }\n\n  /**\n   * Get the line dash offset for the stroke.\n   * @return {number|undefined} Line dash offset.\n   * @api\n   */\n  getLineDashOffset() {\n    return this.lineDashOffset_;\n  }\n\n  /**\n   * Get the line join type for the stroke.\n   * @return {CanvasLineJoin|undefined} Line join.\n   * @api\n   */\n  getLineJoin() {\n    return this.lineJoin_;\n  }\n\n  /**\n   * Get the miter limit for the stroke.\n   * @return {number|undefined} Miter limit.\n   * @api\n   */\n  getMiterLimit() {\n    return this.miterLimit_;\n  }\n\n  /**\n   * Get the stroke width.\n   * @return {number|undefined} Width.\n   * @api\n   */\n  getWidth() {\n    return this.width_;\n  }\n\n  /**\n   * Set the color.\n   *\n   * @param {import(\"../color.js\").Color|import(\"../colorlike.js\").ColorLike} color Color.\n   * @api\n   */\n  setColor(color) {\n    this.color_ = color;\n  }\n\n  /**\n   * Set the line cap.\n   *\n   * @param {CanvasLineCap|undefined} lineCap Line cap.\n   * @api\n   */\n  setLineCap(lineCap) {\n    this.lineCap_ = lineCap;\n  }\n\n  /**\n   * Set the line dash.\n   *\n   * @param {Array<number>|null} lineDash Line dash.\n   * @api\n   */\n  setLineDash(lineDash) {\n    this.lineDash_ = lineDash;\n  }\n\n  /**\n   * Set the line dash offset.\n   *\n   * @param {number|undefined} lineDashOffset Line dash offset.\n   * @api\n   */\n  setLineDashOffset(lineDashOffset) {\n    this.lineDashOffset_ = lineDashOffset;\n  }\n\n  /**\n   * Set the line join.\n   *\n   * @param {CanvasLineJoin|undefined} lineJoin Line join.\n   * @api\n   */\n  setLineJoin(lineJoin) {\n    this.lineJoin_ = lineJoin;\n  }\n\n  /**\n   * Set the miter limit.\n   *\n   * @param {number|undefined} miterLimit Miter limit.\n   * @api\n   */\n  setMiterLimit(miterLimit) {\n    this.miterLimit_ = miterLimit;\n  }\n\n  /**\n   * Set the width.\n   *\n   * @param {number|undefined} width Width.\n   * @api\n   */\n  setWidth(width) {\n    this.width_ = width;\n  }\n}\n\nexport default Stroke;\n", "/**\n * @module ol/style/Image\n */\nimport {abstract} from '../util.js';\nimport {toSize} from '../size.js';\n\n/**\n * @typedef {Object} Options\n * @property {number} opacity Opacity.\n * @property {boolean} rotateWithView If the image should get rotated with the view.\n * @property {number} rotation Rotation.\n * @property {number|import(\"../size.js\").Size} scale Scale.\n * @property {Array<number>} displacement Displacement.\n * @property {\"declutter\"|\"obstacle\"|\"none\"|undefined} declutterMode Declutter mode: `declutter`, `obstacle`, 'none */\n\n/**\n * @classdesc\n * A base class used for creating subclasses and not instantiated in\n * apps. Base class for {@link module:ol/style/Icon~Icon}, {@link module:ol/style/Circle~CircleStyle} and\n * {@link module:ol/style/RegularShape~RegularShape}.\n * @abstract\n * @api\n */\nclass ImageStyle {\n  /**\n   * @param {Options} options Options.\n   */\n  constructor(options) {\n    /**\n     * @private\n     * @type {number}\n     */\n    this.opacity_ = options.opacity;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.rotateWithView_ = options.rotateWithView;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.rotation_ = options.rotation;\n\n    /**\n     * @private\n     * @type {number|import(\"../size.js\").Size}\n     */\n    this.scale_ = options.scale;\n\n    /**\n     * @private\n     * @type {import(\"../size.js\").Size}\n     */\n    this.scaleArray_ = toSize(options.scale);\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.displacement_ = options.displacement;\n\n    /**\n     * @private\n     * @type {\"declutter\"|\"obstacle\"|\"none\"|undefined}\n     */\n    this.declutterMode_ = options.declutterMode;\n  }\n\n  /**\n   * Clones the style.\n   * @return {ImageStyle} The cloned style.\n   * @api\n   */\n  clone() {\n    const scale = this.getScale();\n    return new ImageStyle({\n      opacity: this.getOpacity(),\n      scale: Array.isArray(scale) ? scale.slice() : scale,\n      rotation: this.getRotation(),\n      rotateWithView: this.getRotateWithView(),\n      displacement: this.getDisplacement().slice(),\n      declutterMode: this.getDeclutterMode(),\n    });\n  }\n\n  /**\n   * Get the symbolizer opacity.\n   * @return {number} Opacity.\n   * @api\n   */\n  getOpacity() {\n    return this.opacity_;\n  }\n\n  /**\n   * Determine whether the symbolizer rotates with the map.\n   * @return {boolean} Rotate with map.\n   * @api\n   */\n  getRotateWithView() {\n    return this.rotateWithView_;\n  }\n\n  /**\n   * Get the symoblizer rotation.\n   * @return {number} Rotation.\n   * @api\n   */\n  getRotation() {\n    return this.rotation_;\n  }\n\n  /**\n   * Get the symbolizer scale.\n   * @return {number|import(\"../size.js\").Size} Scale.\n   * @api\n   */\n  getScale() {\n    return this.scale_;\n  }\n\n  /**\n   * Get the symbolizer scale array.\n   * @return {import(\"../size.js\").Size} Scale array.\n   */\n  getScaleArray() {\n    return this.scaleArray_;\n  }\n\n  /**\n   * Get the displacement of the shape\n   * @return {Array<number>} Shape's center displacement\n   * @api\n   */\n  getDisplacement() {\n    return this.displacement_;\n  }\n\n  /**\n   * Get the declutter mode of the shape\n   * @return {\"declutter\"|\"obstacle\"|\"none\"|undefined} Shape's declutter mode\n   * @api\n   */\n  getDeclutterMode() {\n    return this.declutterMode_;\n  }\n\n  /**\n   * Get the anchor point in pixels. The anchor determines the center point for the\n   * symbolizer.\n   * @abstract\n   * @return {Array<number>} Anchor.\n   */\n  getAnchor() {\n    return abstract();\n  }\n\n  /**\n   * Get the image element for the symbolizer.\n   * @abstract\n   * @param {number} pixelRatio Pixel ratio.\n   * @return {HTMLCanvasElement|HTMLVideoElement|HTMLImageElement} Image element.\n   */\n  getImage(pixelRatio) {\n    return abstract();\n  }\n\n  /**\n   * @abstract\n   * @return {HTMLCanvasElement|HTMLVideoElement|HTMLImageElement} Image element.\n   */\n  getHitDetectionImage() {\n    return abstract();\n  }\n\n  /**\n   * Get the image pixel ratio.\n   * @param {number} pixelRatio Pixel ratio.\n   * @return {number} Pixel ratio.\n   */\n  getPixelRatio(pixelRatio) {\n    return 1;\n  }\n\n  /**\n   * @abstract\n   * @return {import(\"../ImageState.js\").default} Image state.\n   */\n  getImageState() {\n    return abstract();\n  }\n\n  /**\n   * @abstract\n   * @return {import(\"../size.js\").Size} Image size.\n   */\n  getImageSize() {\n    return abstract();\n  }\n\n  /**\n   * Get the origin of the symbolizer.\n   * @abstract\n   * @return {Array<number>} Origin.\n   */\n  getOrigin() {\n    return abstract();\n  }\n\n  /**\n   * Get the size of the symbolizer (in pixels).\n   * @abstract\n   * @return {import(\"../size.js\").Size} Size.\n   */\n  getSize() {\n    return abstract();\n  }\n\n  /**\n   * Set the displacement.\n   *\n   * @param {Array<number>} displacement Displacement.\n   * @api\n   */\n  setDisplacement(displacement) {\n    this.displacement_ = displacement;\n  }\n\n  /**\n   * Set the opacity.\n   *\n   * @param {number} opacity Opacity.\n   * @api\n   */\n  setOpacity(opacity) {\n    this.opacity_ = opacity;\n  }\n\n  /**\n   * Set whether to rotate the style with the view.\n   *\n   * @param {boolean} rotateWithView Rotate with map.\n   * @api\n   */\n  setRotateWithView(rotateWithView) {\n    this.rotateWithView_ = rotateWithView;\n  }\n\n  /**\n   * Set the rotation.\n   *\n   * @param {number} rotation Rotation.\n   * @api\n   */\n  setRotation(rotation) {\n    this.rotation_ = rotation;\n  }\n\n  /**\n   * Set the scale.\n   *\n   * @param {number|import(\"../size.js\").Size} scale Scale.\n   * @api\n   */\n  setScale(scale) {\n    this.scale_ = scale;\n    this.scaleArray_ = toSize(scale);\n  }\n\n  /**\n   * @abstract\n   * @param {function(import(\"../events/Event.js\").default): void} listener Listener function.\n   */\n  listenImageChange(listener) {\n    abstract();\n  }\n\n  /**\n   * Load not yet loaded URI.\n   * @abstract\n   */\n  load() {\n    abstract();\n  }\n\n  /**\n   * @abstract\n   * @param {function(import(\"../events/Event.js\").default): void} listener Listener function.\n   */\n  unlistenImageChange(listener) {\n    abstract();\n  }\n}\n\nexport default ImageStyle;\n", "/**\n * @module ol/colorlike\n */\nimport {toString} from './color.js';\n\n/**\n * A type accepted by CanvasRenderingContext2D.fillStyle\n * or CanvasRenderingContext2D.strokeStyle.\n * Represents a color, pattern, or gradient. The origin for patterns and\n * gradients as fill style is an increment of 512 css pixels from map coordinate\n * `[0, 0]`. For seamless repeat patterns, width and height of the pattern image\n * must be a factor of two (2, 4, 8, ..., 512).\n *\n * @typedef {string|CanvasPattern|CanvasGradient} ColorLike\n * @api\n */\n\n/**\n * @param {import(\"./color.js\").Color|ColorLike} color Color.\n * @return {ColorLike} The color as an {@link ol/colorlike~ColorLike}.\n * @api\n */\nexport function asColorLike(color) {\n  if (Array.isArray(color)) {\n    return toString(color);\n  }\n  return color;\n}\n", "/**\n * @module ol/style/RegularShape\n */\n\nimport ImageState from '../ImageState.js';\nimport ImageStyle from './Image.js';\nimport {asArray} from '../color.js';\nimport {asColorLike} from '../colorlike.js';\nimport {createCanvasContext2D} from '../dom.js';\nimport {\n  defaultFillStyle,\n  defaultLineJoin,\n  defaultLineWidth,\n  defaultMiterLimit,\n  defaultStrokeStyle,\n} from '../render/canvas.js';\n\n/**\n * Specify radius for regular polygons, or radius1 and radius2 for stars.\n * @typedef {Object} Options\n * @property {import(\"./Fill.js\").default} [fill] Fill style.\n * @property {number} points Number of points for stars and regular polygons. In case of a polygon, the number of points\n * is the number of sides.\n * @property {number} [radius] Radius of a regular polygon.\n * @property {number} [radius1] First radius of a star. Ignored if radius is set.\n * @property {number} [radius2] Second radius of a star.\n * @property {number} [angle=0] <PERSON>hape's angle in radians. A value of 0 will have one of the shape's points facing up.\n * @property {Array<number>} [displacement=[0, 0]] Displacement of the shape in pixels.\n * Positive values will shift the shape right and up.\n * @property {import(\"./Stroke.js\").default} [stroke] Stroke style.\n * @property {number} [rotation=0] Rotation in radians (positive rotation clockwise).\n * @property {boolean} [rotateWithView=false] Whether to rotate the shape with the view.\n * @property {number|import(\"../size.js\").Size} [scale=1] Scale. Unless two dimensional scaling is required a better\n * result may be obtained with appropriate settings for `radius`, `radius1` and `radius2`.\n * @property {\"declutter\"|\"obstacle\"|\"none\"|undefined} [declutterMode] Declutter mode.\n */\n\n/**\n * @typedef {Object} RenderOptions\n * @property {import(\"../colorlike.js\").ColorLike} [strokeStyle] StrokeStyle.\n * @property {number} strokeWidth StrokeWidth.\n * @property {number} size Size.\n * @property {Array<number>|null} lineDash LineDash.\n * @property {number} lineDashOffset LineDashOffset.\n * @property {CanvasLineJoin} lineJoin LineJoin.\n * @property {number} miterLimit MiterLimit.\n */\n\n/**\n * @classdesc\n * Set regular shape style for vector features. The resulting shape will be\n * a regular polygon when `radius` is provided, or a star when `radius1` and\n * `radius2` are provided.\n * @api\n */\nclass RegularShape extends ImageStyle {\n  /**\n   * @param {Options} options Options.\n   */\n  constructor(options) {\n    /**\n     * @type {boolean}\n     */\n    const rotateWithView =\n      options.rotateWithView !== undefined ? options.rotateWithView : false;\n\n    super({\n      opacity: 1,\n      rotateWithView: rotateWithView,\n      rotation: options.rotation !== undefined ? options.rotation : 0,\n      scale: options.scale !== undefined ? options.scale : 1,\n      displacement:\n        options.displacement !== undefined ? options.displacement : [0, 0],\n      declutterMode: options.declutterMode,\n    });\n\n    /**\n     * @private\n     * @type {Object<number, HTMLCanvasElement>}\n     */\n    this.canvas_ = undefined;\n\n    /**\n     * @private\n     * @type {HTMLCanvasElement}\n     */\n    this.hitDetectionCanvas_ = null;\n\n    /**\n     * @private\n     * @type {import(\"./Fill.js\").default}\n     */\n    this.fill_ = options.fill !== undefined ? options.fill : null;\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.origin_ = [0, 0];\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.points_ = options.points;\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.radius_ =\n      options.radius !== undefined ? options.radius : options.radius1;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.radius2_ = options.radius2;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.angle_ = options.angle !== undefined ? options.angle : 0;\n\n    /**\n     * @private\n     * @type {import(\"./Stroke.js\").default}\n     */\n    this.stroke_ = options.stroke !== undefined ? options.stroke : null;\n\n    /**\n     * @private\n     * @type {import(\"../size.js\").Size}\n     */\n    this.size_ = null;\n\n    /**\n     * @private\n     * @type {RenderOptions}\n     */\n    this.renderOptions_ = null;\n\n    this.render();\n  }\n\n  /**\n   * Clones the style.\n   * @return {RegularShape} The cloned style.\n   * @api\n   */\n  clone() {\n    const scale = this.getScale();\n    const style = new RegularShape({\n      fill: this.getFill() ? this.getFill().clone() : undefined,\n      points: this.getPoints(),\n      radius: this.getRadius(),\n      radius2: this.getRadius2(),\n      angle: this.getAngle(),\n      stroke: this.getStroke() ? this.getStroke().clone() : undefined,\n      rotation: this.getRotation(),\n      rotateWithView: this.getRotateWithView(),\n      scale: Array.isArray(scale) ? scale.slice() : scale,\n      displacement: this.getDisplacement().slice(),\n      declutterMode: this.getDeclutterMode(),\n    });\n    style.setOpacity(this.getOpacity());\n    return style;\n  }\n\n  /**\n   * Get the anchor point in pixels. The anchor determines the center point for the\n   * symbolizer.\n   * @return {Array<number>} Anchor.\n   * @api\n   */\n  getAnchor() {\n    const size = this.size_;\n    if (!size) {\n      return null;\n    }\n    const displacement = this.getDisplacement();\n    const scale = this.getScaleArray();\n    // anchor is scaled by renderer but displacement should not be scaled\n    // so divide by scale here\n    return [\n      size[0] / 2 - displacement[0] / scale[0],\n      size[1] / 2 + displacement[1] / scale[1],\n    ];\n  }\n\n  /**\n   * Get the angle used in generating the shape.\n   * @return {number} Shape's rotation in radians.\n   * @api\n   */\n  getAngle() {\n    return this.angle_;\n  }\n\n  /**\n   * Get the fill style for the shape.\n   * @return {import(\"./Fill.js\").default} Fill style.\n   * @api\n   */\n  getFill() {\n    return this.fill_;\n  }\n\n  /**\n   * Set the fill style.\n   * @param {import(\"./Fill.js\").default} fill Fill style.\n   * @api\n   */\n  setFill(fill) {\n    this.fill_ = fill;\n    this.render();\n  }\n\n  /**\n   * @return {HTMLCanvasElement} Image element.\n   */\n  getHitDetectionImage() {\n    if (!this.hitDetectionCanvas_) {\n      this.createHitDetectionCanvas_(this.renderOptions_);\n    }\n    return this.hitDetectionCanvas_;\n  }\n\n  /**\n   * Get the image icon.\n   * @param {number} pixelRatio Pixel ratio.\n   * @return {HTMLCanvasElement} Image or Canvas element.\n   * @api\n   */\n  getImage(pixelRatio) {\n    let image = this.canvas_[pixelRatio];\n    if (!image) {\n      const renderOptions = this.renderOptions_;\n      const context = createCanvasContext2D(\n        renderOptions.size * pixelRatio,\n        renderOptions.size * pixelRatio\n      );\n      this.draw_(renderOptions, context, pixelRatio);\n\n      image = context.canvas;\n      this.canvas_[pixelRatio] = image;\n    }\n    return image;\n  }\n\n  /**\n   * Get the image pixel ratio.\n   * @param {number} pixelRatio Pixel ratio.\n   * @return {number} Pixel ratio.\n   */\n  getPixelRatio(pixelRatio) {\n    return pixelRatio;\n  }\n\n  /**\n   * @return {import(\"../size.js\").Size} Image size.\n   */\n  getImageSize() {\n    return this.size_;\n  }\n\n  /**\n   * @return {import(\"../ImageState.js\").default} Image state.\n   */\n  getImageState() {\n    return ImageState.LOADED;\n  }\n\n  /**\n   * Get the origin of the symbolizer.\n   * @return {Array<number>} Origin.\n   * @api\n   */\n  getOrigin() {\n    return this.origin_;\n  }\n\n  /**\n   * Get the number of points for generating the shape.\n   * @return {number} Number of points for stars and regular polygons.\n   * @api\n   */\n  getPoints() {\n    return this.points_;\n  }\n\n  /**\n   * Get the (primary) radius for the shape.\n   * @return {number} Radius.\n   * @api\n   */\n  getRadius() {\n    return this.radius_;\n  }\n\n  /**\n   * Get the secondary radius for the shape.\n   * @return {number|undefined} Radius2.\n   * @api\n   */\n  getRadius2() {\n    return this.radius2_;\n  }\n\n  /**\n   * Get the size of the symbolizer (in pixels).\n   * @return {import(\"../size.js\").Size} Size.\n   * @api\n   */\n  getSize() {\n    return this.size_;\n  }\n\n  /**\n   * Get the stroke style for the shape.\n   * @return {import(\"./Stroke.js\").default} Stroke style.\n   * @api\n   */\n  getStroke() {\n    return this.stroke_;\n  }\n\n  /**\n   * Set the stroke style.\n   * @param {import(\"./Stroke.js\").default} stroke Stroke style.\n   * @api\n   */\n  setStroke(stroke) {\n    this.stroke_ = stroke;\n    this.render();\n  }\n\n  /**\n   * @param {function(import(\"../events/Event.js\").default): void} listener Listener function.\n   */\n  listenImageChange(listener) {}\n\n  /**\n   * Load not yet loaded URI.\n   */\n  load() {}\n\n  /**\n   * @param {function(import(\"../events/Event.js\").default): void} listener Listener function.\n   */\n  unlistenImageChange(listener) {}\n\n  /**\n   * Calculate additional canvas size needed for the miter.\n   * @param {string} lineJoin Line join\n   * @param {number} strokeWidth Stroke width\n   * @param {number} miterLimit Miter limit\n   * @return {number} Additional canvas size needed\n   * @private\n   */\n  calculateLineJoinSize_(lineJoin, strokeWidth, miterLimit) {\n    if (\n      strokeWidth === 0 ||\n      this.points_ === Infinity ||\n      (lineJoin !== 'bevel' && lineJoin !== 'miter')\n    ) {\n      return strokeWidth;\n    }\n    // m  | ^\n    // i  | |\\                  .\n    // t >|  #\\\n    // e  | |\\ \\              .\n    // r      \\s\\\n    //      |  \\t\\          .                 .\n    //          \\r\\                      .   .\n    //      |    \\o\\      .          .  . . .\n    //          e \\k\\            .  .    . .\n    //      |      \\e\\  .    .  .       . .\n    //       d      \\ \\  .  .          . .\n    //      | _ _a_ _\\#  .            . .\n    //   r1          / `             . .\n    //      |                       . .\n    //       b     /               . .\n    //      |                     . .\n    //           / r2            . .\n    //      |                        .   .\n    //         /                           .   .\n    //      |α                                   .   .\n    //       /                                         .   .\n    //      ° center\n    let r1 = this.radius_;\n    let r2 = this.radius2_ === undefined ? r1 : this.radius2_;\n    if (r1 < r2) {\n      const tmp = r1;\n      r1 = r2;\n      r2 = tmp;\n    }\n    const points =\n      this.radius2_ === undefined ? this.points_ : this.points_ * 2;\n    const alpha = (2 * Math.PI) / points;\n    const a = r2 * Math.sin(alpha);\n    const b = Math.sqrt(r2 * r2 - a * a);\n    const d = r1 - b;\n    const e = Math.sqrt(a * a + d * d);\n    const miterRatio = e / a;\n    if (lineJoin === 'miter' && miterRatio <= miterLimit) {\n      return miterRatio * strokeWidth;\n    }\n    // Calculate the distance from center to the stroke corner where\n    // it was cut short because of the miter limit.\n    //              l\n    //        ----+---- <= distance from center to here is maxr\n    //       /####|k ##\\\n    //      /#####^#####\\\n    //     /#### /+\\# s #\\\n    //    /### h/+++\\# t #\\\n    //   /### t/+++++\\# r #\\\n    //  /### a/+++++++\\# o #\\\n    // /### p/++ fill +\\# k #\\\n    ///#### /+++++^+++++\\# e #\\\n    //#####/+++++/+\\+++++\\#####\\\n    const k = strokeWidth / 2 / miterRatio;\n    const l = (strokeWidth / 2) * (d / e);\n    const maxr = Math.sqrt((r1 + k) * (r1 + k) + l * l);\n    const bevelAdd = maxr - r1;\n    if (this.radius2_ === undefined || lineJoin === 'bevel') {\n      return bevelAdd * 2;\n    }\n    // If outer miter is over the miter limit the inner miter may reach through the\n    // center and be longer than the bevel, same calculation as above but swap r1 / r2.\n    const aa = r1 * Math.sin(alpha);\n    const bb = Math.sqrt(r1 * r1 - aa * aa);\n    const dd = r2 - bb;\n    const ee = Math.sqrt(aa * aa + dd * dd);\n    const innerMiterRatio = ee / aa;\n    if (innerMiterRatio <= miterLimit) {\n      const innerLength = (innerMiterRatio * strokeWidth) / 2 - r2 - r1;\n      return 2 * Math.max(bevelAdd, innerLength);\n    }\n    return bevelAdd * 2;\n  }\n\n  /**\n   * @return {RenderOptions}  The render options\n   * @protected\n   */\n  createRenderOptions() {\n    let lineJoin = defaultLineJoin;\n    let miterLimit = 0;\n    let lineDash = null;\n    let lineDashOffset = 0;\n    let strokeStyle;\n    let strokeWidth = 0;\n\n    if (this.stroke_) {\n      strokeStyle = this.stroke_.getColor();\n      if (strokeStyle === null) {\n        strokeStyle = defaultStrokeStyle;\n      }\n      strokeStyle = asColorLike(strokeStyle);\n      strokeWidth = this.stroke_.getWidth();\n      if (strokeWidth === undefined) {\n        strokeWidth = defaultLineWidth;\n      }\n      lineDash = this.stroke_.getLineDash();\n      lineDashOffset = this.stroke_.getLineDashOffset();\n      lineJoin = this.stroke_.getLineJoin();\n      if (lineJoin === undefined) {\n        lineJoin = defaultLineJoin;\n      }\n      miterLimit = this.stroke_.getMiterLimit();\n      if (miterLimit === undefined) {\n        miterLimit = defaultMiterLimit;\n      }\n    }\n\n    const add = this.calculateLineJoinSize_(lineJoin, strokeWidth, miterLimit);\n    const maxRadius = Math.max(this.radius_, this.radius2_ || 0);\n    const size = Math.ceil(2 * maxRadius + add);\n\n    return {\n      strokeStyle: strokeStyle,\n      strokeWidth: strokeWidth,\n      size: size,\n      lineDash: lineDash,\n      lineDashOffset: lineDashOffset,\n      lineJoin: lineJoin,\n      miterLimit: miterLimit,\n    };\n  }\n\n  /**\n   * @protected\n   */\n  render() {\n    this.renderOptions_ = this.createRenderOptions();\n    const size = this.renderOptions_.size;\n    this.canvas_ = {};\n    this.size_ = [size, size];\n  }\n\n  /**\n   * @private\n   * @param {RenderOptions} renderOptions Render options.\n   * @param {CanvasRenderingContext2D} context The rendering context.\n   * @param {number} pixelRatio The pixel ratio.\n   */\n  draw_(renderOptions, context, pixelRatio) {\n    context.scale(pixelRatio, pixelRatio);\n    // set origin to canvas center\n    context.translate(renderOptions.size / 2, renderOptions.size / 2);\n\n    this.createPath_(context);\n\n    if (this.fill_) {\n      let color = this.fill_.getColor();\n      if (color === null) {\n        color = defaultFillStyle;\n      }\n      context.fillStyle = asColorLike(color);\n      context.fill();\n    }\n    if (this.stroke_) {\n      context.strokeStyle = renderOptions.strokeStyle;\n      context.lineWidth = renderOptions.strokeWidth;\n      if (renderOptions.lineDash) {\n        context.setLineDash(renderOptions.lineDash);\n        context.lineDashOffset = renderOptions.lineDashOffset;\n      }\n      context.lineJoin = renderOptions.lineJoin;\n      context.miterLimit = renderOptions.miterLimit;\n      context.stroke();\n    }\n  }\n\n  /**\n   * @private\n   * @param {RenderOptions} renderOptions Render options.\n   */\n  createHitDetectionCanvas_(renderOptions) {\n    if (this.fill_) {\n      let color = this.fill_.getColor();\n\n      // determine if fill is transparent (or pattern or gradient)\n      let opacity = 0;\n      if (typeof color === 'string') {\n        color = asArray(color);\n      }\n      if (color === null) {\n        opacity = 1;\n      } else if (Array.isArray(color)) {\n        opacity = color.length === 4 ? color[3] : 1;\n      }\n      if (opacity === 0) {\n        // if a transparent fill style is set, create an extra hit-detection image\n        // with a default fill style\n        const context = createCanvasContext2D(\n          renderOptions.size,\n          renderOptions.size\n        );\n        this.hitDetectionCanvas_ = context.canvas;\n\n        this.drawHitDetectionCanvas_(renderOptions, context);\n      }\n    }\n    if (!this.hitDetectionCanvas_) {\n      this.hitDetectionCanvas_ = this.getImage(1);\n    }\n  }\n\n  /**\n   * @private\n   * @param {CanvasRenderingContext2D} context The context to draw in.\n   */\n  createPath_(context) {\n    let points = this.points_;\n    const radius = this.radius_;\n    if (points === Infinity) {\n      context.arc(0, 0, radius, 0, 2 * Math.PI);\n    } else {\n      const radius2 = this.radius2_ === undefined ? radius : this.radius2_;\n      if (this.radius2_ !== undefined) {\n        points *= 2;\n      }\n      const startAngle = this.angle_ - Math.PI / 2;\n      const step = (2 * Math.PI) / points;\n      for (let i = 0; i < points; i++) {\n        const angle0 = startAngle + i * step;\n        const radiusC = i % 2 === 0 ? radius : radius2;\n        context.lineTo(radiusC * Math.cos(angle0), radiusC * Math.sin(angle0));\n      }\n      context.closePath();\n    }\n  }\n\n  /**\n   * @private\n   * @param {RenderOptions} renderOptions Render options.\n   * @param {CanvasRenderingContext2D} context The context.\n   */\n  drawHitDetectionCanvas_(renderOptions, context) {\n    // set origin to canvas center\n    context.translate(renderOptions.size / 2, renderOptions.size / 2);\n\n    this.createPath_(context);\n\n    context.fillStyle = defaultFillStyle;\n    context.fill();\n    if (this.stroke_) {\n      context.strokeStyle = renderOptions.strokeStyle;\n      context.lineWidth = renderOptions.strokeWidth;\n      if (renderOptions.lineDash) {\n        context.setLineDash(renderOptions.lineDash);\n        context.lineDashOffset = renderOptions.lineDashOffset;\n      }\n      context.lineJoin = renderOptions.lineJoin;\n      context.miterLimit = renderOptions.miterLimit;\n      context.stroke();\n    }\n  }\n}\n\nexport default RegularShape;\n", "/**\n * @module ol/style/Circle\n */\n\nimport RegularShape from './RegularShape.js';\n\n/**\n * @typedef {Object} Options\n * @property {import(\"./Fill.js\").default} [fill] Fill style.\n * @property {number} radius Circle radius.\n * @property {import(\"./Stroke.js\").default} [stroke] Stroke style.\n * @property {Array<number>} [displacement=[0,0]] displacement\n * @property {number|import(\"../size.js\").Size} [scale=1] Scale. A two dimensional scale will produce an ellipse.\n * Unless two dimensional scaling is required a better result may be obtained with an appropriate setting for `radius`.\n * @property {number} [rotation=0] Rotation in radians\n * (positive rotation clockwise, meaningful only when used in conjunction with a two dimensional scale).\n * @property {boolean} [rotateWithView=false] Whether to rotate the shape with the view\n * (meaningful only when used in conjunction with a two dimensional scale).\n * @property {\"declutter\"|\"obstacle\"|\"none\"|undefined} [declutterMode] Declutter mode\n */\n\n/**\n * @classdesc\n * Set circle style for vector features.\n * @api\n */\nclass CircleStyle extends RegularShape {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {radius: 5};\n\n    super({\n      points: Infinity,\n      fill: options.fill,\n      radius: options.radius,\n      stroke: options.stroke,\n      scale: options.scale !== undefined ? options.scale : 1,\n      rotation: options.rotation !== undefined ? options.rotation : 0,\n      rotateWithView:\n        options.rotateWithView !== undefined ? options.rotateWithView : false,\n      displacement:\n        options.displacement !== undefined ? options.displacement : [0, 0],\n      declutterMode: options.declutterMode,\n    });\n  }\n\n  /**\n   * Clones the style.\n   * @return {CircleStyle} The cloned style.\n   * @api\n   */\n  clone() {\n    const scale = this.getScale();\n    const style = new CircleStyle({\n      fill: this.getFill() ? this.getFill().clone() : undefined,\n      stroke: this.getStroke() ? this.getStroke().clone() : undefined,\n      radius: this.getRadius(),\n      scale: Array.isArray(scale) ? scale.slice() : scale,\n      rotation: this.getRotation(),\n      rotateWithView: this.getRotateWithView(),\n      displacement: this.getDisplacement().slice(),\n      declutterMode: this.getDeclutterMode(),\n    });\n    style.setOpacity(this.getOpacity());\n    return style;\n  }\n\n  /**\n   * Set the circle radius.\n   *\n   * @param {number} radius Circle radius.\n   * @api\n   */\n  setRadius(radius) {\n    this.radius_ = radius;\n    this.render();\n  }\n}\n\nexport default CircleStyle;\n", "/**\n * @module ol/style/Style\n */\n\nimport CircleStyle from './Circle.js';\nimport Fill from './Fill.js';\nimport Stroke from './Stroke.js';\nimport {assert} from '../asserts.js';\n\n/**\n * A function that takes an {@link module:ol/Feature~Feature} and a `{number}`\n * representing the view's resolution. The function should return a\n * {@link module:ol/style/Style~Style} or an array of them. This way e.g. a\n * vector layer can be styled. If the function returns `undefined`, the\n * feature will not be rendered.\n *\n * @typedef {function(import(\"../Feature.js\").FeatureLike, number):(Style|Array<Style>|void)} StyleFunction\n */\n\n/**\n * A {@link Style}, an array of {@link Style}, or a {@link StyleFunction}.\n * @typedef {Style|Array<Style>|StyleFunction} StyleLike\n */\n\n/**\n * A function that takes an {@link module:ol/Feature~Feature} as argument and returns an\n * {@link module:ol/geom/Geometry~Geometry} that will be rendered and styled for the feature.\n *\n * @typedef {function(import(\"../Feature.js\").FeatureLike):\n *     (import(\"../geom/Geometry.js\").default|import(\"../render/Feature.js\").default|undefined)} GeometryFunction\n */\n\n/**\n * Custom renderer function. Takes two arguments:\n *\n * 1. The pixel coordinates of the geometry in GeoJSON notation.\n * 2. The {@link module:ol/render~State} of the layer renderer.\n *\n * @typedef {function((import(\"../coordinate.js\").Coordinate|Array<import(\"../coordinate.js\").Coordinate>|Array<Array<import(\"../coordinate.js\").Coordinate>>|Array<Array<Array<import(\"../coordinate.js\").Coordinate>>>),import(\"../render.js\").State): void} RenderFunction\n */\n\n/**\n * @typedef {Object} Options\n * @property {string|import(\"../geom/Geometry.js\").default|GeometryFunction} [geometry] Feature property or geometry\n * or function returning a geometry to render for this style.\n * @property {import(\"./Fill.js\").default} [fill] Fill style.\n * @property {import(\"./Image.js\").default} [image] Image style.\n * @property {RenderFunction} [renderer] Custom renderer. When configured, `fill`, `stroke` and `image` will be\n * ignored, and the provided function will be called with each render frame for each geometry.\n * @property {RenderFunction} [hitDetectionRenderer] Custom renderer for hit detection. If provided will be used\n * in hit detection rendering.\n * @property {import(\"./Stroke.js\").default} [stroke] Stroke style.\n * @property {import(\"./Text.js\").default} [text] Text style.\n * @property {number} [zIndex] Z index.\n */\n\n/**\n * @classdesc\n * Container for vector feature rendering styles. Any changes made to the style\n * or its children through `set*()` methods will not take effect until the\n * feature or layer that uses the style is re-rendered.\n *\n * ## Feature styles\n *\n * If no style is defined, the following default style is used:\n * ```js\n *  import {Circle, Fill, Stroke, Style} from 'ol/style.js';\n *\n *  const fill = new Fill({\n *    color: 'rgba(255,255,255,0.4)',\n *  });\n *  const stroke = new Stroke({\n *    color: '#3399CC',\n *    width: 1.25,\n *  });\n *  const styles = [\n *    new Style({\n *      image: new Circle({\n *        fill: fill,\n *        stroke: stroke,\n *        radius: 5,\n *      }),\n *      fill: fill,\n *      stroke: stroke,\n *    }),\n *  ];\n * ```\n *\n * A separate editing style has the following defaults:\n * ```js\n *  import {Circle, Fill, Stroke, Style} from 'ol/style.js';\n *\n *  const styles = {};\n *  const white = [255, 255, 255, 1];\n *  const blue = [0, 153, 255, 1];\n *  const width = 3;\n *  styles['Polygon'] = [\n *    new Style({\n *      fill: new Fill({\n *        color: [255, 255, 255, 0.5],\n *      }),\n *    }),\n *  ];\n *  styles['MultiPolygon'] =\n *      styles['Polygon'];\n *  styles['LineString'] = [\n *    new Style({\n *      stroke: new Stroke({\n *        color: white,\n *        width: width + 2,\n *      }),\n *    }),\n *    new Style({\n *      stroke: new Stroke({\n *        color: blue,\n *        width: width,\n *      }),\n *    }),\n *  ];\n *  styles['MultiLineString'] = styles['LineString'];\n *\n *  styles['Circle'] = styles['Polygon'].concat(\n *    styles['LineString']\n *  );\n *\n *  styles['Point'] = [\n *    new Style({\n *      image: new Circle({\n *        radius: width * 2,\n *        fill: new Fill({\n *          color: blue,\n *        }),\n *        stroke: new Stroke({\n *          color: white,\n *          width: width / 2,\n *        }),\n *      }),\n *      zIndex: Infinity,\n *    }),\n *  ];\n *  styles['MultiPoint'] =\n *      styles['Point'];\n *  styles['GeometryCollection'] =\n *      styles['Polygon'].concat(\n *          styles['LineString'],\n *          styles['Point']\n *      );\n * ```\n *\n * @api\n */\nclass Style {\n  /**\n   * @param {Options} [options] Style options.\n   */\n  constructor(options) {\n    options = options || {};\n\n    /**\n     * @private\n     * @type {string|import(\"../geom/Geometry.js\").default|GeometryFunction}\n     */\n    this.geometry_ = null;\n\n    /**\n     * @private\n     * @type {!GeometryFunction}\n     */\n    this.geometryFunction_ = defaultGeometryFunction;\n\n    if (options.geometry !== undefined) {\n      this.setGeometry(options.geometry);\n    }\n\n    /**\n     * @private\n     * @type {import(\"./Fill.js\").default}\n     */\n    this.fill_ = options.fill !== undefined ? options.fill : null;\n\n    /**\n     * @private\n     * @type {import(\"./Image.js\").default}\n     */\n    this.image_ = options.image !== undefined ? options.image : null;\n\n    /**\n     * @private\n     * @type {RenderFunction|null}\n     */\n    this.renderer_ = options.renderer !== undefined ? options.renderer : null;\n\n    /**\n     * @private\n     * @type {RenderFunction|null}\n     */\n    this.hitDetectionRenderer_ =\n      options.hitDetectionRenderer !== undefined\n        ? options.hitDetectionRenderer\n        : null;\n\n    /**\n     * @private\n     * @type {import(\"./Stroke.js\").default}\n     */\n    this.stroke_ = options.stroke !== undefined ? options.stroke : null;\n\n    /**\n     * @private\n     * @type {import(\"./Text.js\").default}\n     */\n    this.text_ = options.text !== undefined ? options.text : null;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.zIndex_ = options.zIndex;\n  }\n\n  /**\n   * Clones the style.\n   * @return {Style} The cloned style.\n   * @api\n   */\n  clone() {\n    let geometry = this.getGeometry();\n    if (geometry && typeof geometry === 'object') {\n      geometry = /** @type {import(\"../geom/Geometry.js\").default} */ (\n        geometry\n      ).clone();\n    }\n    return new Style({\n      geometry: geometry,\n      fill: this.getFill() ? this.getFill().clone() : undefined,\n      image: this.getImage() ? this.getImage().clone() : undefined,\n      renderer: this.getRenderer(),\n      stroke: this.getStroke() ? this.getStroke().clone() : undefined,\n      text: this.getText() ? this.getText().clone() : undefined,\n      zIndex: this.getZIndex(),\n    });\n  }\n\n  /**\n   * Get the custom renderer function that was configured with\n   * {@link #setRenderer} or the `renderer` constructor option.\n   * @return {RenderFunction|null} Custom renderer function.\n   * @api\n   */\n  getRenderer() {\n    return this.renderer_;\n  }\n\n  /**\n   * Sets a custom renderer function for this style. When set, `fill`, `stroke`\n   * and `image` options of the style will be ignored.\n   * @param {RenderFunction|null} renderer Custom renderer function.\n   * @api\n   */\n  setRenderer(renderer) {\n    this.renderer_ = renderer;\n  }\n\n  /**\n   * Sets a custom renderer function for this style used\n   * in hit detection.\n   * @param {RenderFunction|null} renderer Custom renderer function.\n   * @api\n   */\n  setHitDetectionRenderer(renderer) {\n    this.hitDetectionRenderer_ = renderer;\n  }\n\n  /**\n   * Get the custom renderer function that was configured with\n   * {@link #setHitDetectionRenderer} or the `hitDetectionRenderer` constructor option.\n   * @return {RenderFunction|null} Custom renderer function.\n   * @api\n   */\n  getHitDetectionRenderer() {\n    return this.hitDetectionRenderer_;\n  }\n\n  /**\n   * Get the geometry to be rendered.\n   * @return {string|import(\"../geom/Geometry.js\").default|GeometryFunction}\n   * Feature property or geometry or function that returns the geometry that will\n   * be rendered with this style.\n   * @api\n   */\n  getGeometry() {\n    return this.geometry_;\n  }\n\n  /**\n   * Get the function used to generate a geometry for rendering.\n   * @return {!GeometryFunction} Function that is called with a feature\n   * and returns the geometry to render instead of the feature's geometry.\n   * @api\n   */\n  getGeometryFunction() {\n    return this.geometryFunction_;\n  }\n\n  /**\n   * Get the fill style.\n   * @return {import(\"./Fill.js\").default} Fill style.\n   * @api\n   */\n  getFill() {\n    return this.fill_;\n  }\n\n  /**\n   * Set the fill style.\n   * @param {import(\"./Fill.js\").default} fill Fill style.\n   * @api\n   */\n  setFill(fill) {\n    this.fill_ = fill;\n  }\n\n  /**\n   * Get the image style.\n   * @return {import(\"./Image.js\").default} Image style.\n   * @api\n   */\n  getImage() {\n    return this.image_;\n  }\n\n  /**\n   * Set the image style.\n   * @param {import(\"./Image.js\").default} image Image style.\n   * @api\n   */\n  setImage(image) {\n    this.image_ = image;\n  }\n\n  /**\n   * Get the stroke style.\n   * @return {import(\"./Stroke.js\").default} Stroke style.\n   * @api\n   */\n  getStroke() {\n    return this.stroke_;\n  }\n\n  /**\n   * Set the stroke style.\n   * @param {import(\"./Stroke.js\").default} stroke Stroke style.\n   * @api\n   */\n  setStroke(stroke) {\n    this.stroke_ = stroke;\n  }\n\n  /**\n   * Get the text style.\n   * @return {import(\"./Text.js\").default} Text style.\n   * @api\n   */\n  getText() {\n    return this.text_;\n  }\n\n  /**\n   * Set the text style.\n   * @param {import(\"./Text.js\").default} text Text style.\n   * @api\n   */\n  setText(text) {\n    this.text_ = text;\n  }\n\n  /**\n   * Get the z-index for the style.\n   * @return {number|undefined} ZIndex.\n   * @api\n   */\n  getZIndex() {\n    return this.zIndex_;\n  }\n\n  /**\n   * Set a geometry that is rendered instead of the feature's geometry.\n   *\n   * @param {string|import(\"../geom/Geometry.js\").default|GeometryFunction} geometry\n   *     Feature property or geometry or function returning a geometry to render\n   *     for this style.\n   * @api\n   */\n  setGeometry(geometry) {\n    if (typeof geometry === 'function') {\n      this.geometryFunction_ = geometry;\n    } else if (typeof geometry === 'string') {\n      this.geometryFunction_ = function (feature) {\n        return /** @type {import(\"../geom/Geometry.js\").default} */ (\n          feature.get(geometry)\n        );\n      };\n    } else if (!geometry) {\n      this.geometryFunction_ = defaultGeometryFunction;\n    } else if (geometry !== undefined) {\n      this.geometryFunction_ = function () {\n        return /** @type {import(\"../geom/Geometry.js\").default} */ (geometry);\n      };\n    }\n    this.geometry_ = geometry;\n  }\n\n  /**\n   * Set the z-index.\n   *\n   * @param {number|undefined} zIndex ZIndex.\n   * @api\n   */\n  setZIndex(zIndex) {\n    this.zIndex_ = zIndex;\n  }\n}\n\n/**\n * Convert the provided object into a style function.  Functions passed through\n * unchanged.  Arrays of Style or single style objects wrapped in a\n * new style function.\n * @param {StyleFunction|Array<Style>|Style} obj\n *     A style function, a single style, or an array of styles.\n * @return {StyleFunction} A style function.\n */\nexport function toFunction(obj) {\n  let styleFunction;\n\n  if (typeof obj === 'function') {\n    styleFunction = obj;\n  } else {\n    /**\n     * @type {Array<Style>}\n     */\n    let styles;\n    if (Array.isArray(obj)) {\n      styles = obj;\n    } else {\n      assert(typeof (/** @type {?} */ (obj).getZIndex) === 'function', 41); // Expected an `Style` or an array of `Style`\n      const style = /** @type {Style} */ (obj);\n      styles = [style];\n    }\n    styleFunction = function () {\n      return styles;\n    };\n  }\n  return styleFunction;\n}\n\n/**\n * @type {Array<Style>|null}\n */\nlet defaultStyles = null;\n\n/**\n * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n * @param {number} resolution Resolution.\n * @return {Array<Style>} Style.\n */\nexport function createDefaultStyle(feature, resolution) {\n  // We don't use an immediately-invoked function\n  // and a closure so we don't get an error at script evaluation time in\n  // browsers that do not support Canvas. (import(\"./Circle.js\").CircleStyle does\n  // canvas.getContext('2d') at construction time, which will cause an.error\n  // in such browsers.)\n  if (!defaultStyles) {\n    const fill = new Fill({\n      color: 'rgba(255,255,255,0.4)',\n    });\n    const stroke = new Stroke({\n      color: '#3399CC',\n      width: 1.25,\n    });\n    defaultStyles = [\n      new Style({\n        image: new CircleStyle({\n          fill: fill,\n          stroke: stroke,\n          radius: 5,\n        }),\n        fill: fill,\n        stroke: stroke,\n      }),\n    ];\n  }\n  return defaultStyles;\n}\n\n/**\n * Default styles for editing features.\n * @return {Object<import(\"../geom/Geometry.js\").Type, Array<Style>>} Styles\n */\nexport function createEditingStyle() {\n  /** @type {Object<import(\"../geom/Geometry.js\").Type, Array<Style>>} */\n  const styles = {};\n  const white = [255, 255, 255, 1];\n  const blue = [0, 153, 255, 1];\n  const width = 3;\n  styles['Polygon'] = [\n    new Style({\n      fill: new Fill({\n        color: [255, 255, 255, 0.5],\n      }),\n    }),\n  ];\n  styles['MultiPolygon'] = styles['Polygon'];\n\n  styles['LineString'] = [\n    new Style({\n      stroke: new Stroke({\n        color: white,\n        width: width + 2,\n      }),\n    }),\n    new Style({\n      stroke: new Stroke({\n        color: blue,\n        width: width,\n      }),\n    }),\n  ];\n  styles['MultiLineString'] = styles['LineString'];\n\n  styles['Circle'] = styles['Polygon'].concat(styles['LineString']);\n\n  styles['Point'] = [\n    new Style({\n      image: new CircleStyle({\n        radius: width * 2,\n        fill: new Fill({\n          color: blue,\n        }),\n        stroke: new Stroke({\n          color: white,\n          width: width / 2,\n        }),\n      }),\n      zIndex: Infinity,\n    }),\n  ];\n  styles['MultiPoint'] = styles['Point'];\n\n  styles['GeometryCollection'] = styles['Polygon'].concat(\n    styles['LineString'],\n    styles['Point']\n  );\n\n  return styles;\n}\n\n/**\n * Function that is called with a feature and returns its default geometry.\n * @param {import(\"../Feature.js\").FeatureLike} feature Feature to get the geometry for.\n * @return {import(\"../geom/Geometry.js\").default|import(\"../render/Feature.js\").default|undefined} Geometry to render.\n */\nfunction defaultGeometryFunction(feature) {\n  return feature.getGeometry();\n}\n\nexport default Style;\n", "/**\n * @module ol/style/Text\n */\nimport Fill from './Fill.js';\nimport {toSize} from '../size.js';\n\n/**\n * @typedef {'point' | 'line'} TextPlacement\n * Default text placement is `'point'`. Note that\n * `'line'` requires the underlying geometry to be a {@link module:ol/geom/LineString~LineString},\n * {@link module:ol/geom/Polygon~Polygon}, {@link module:ol/geom/MultiLineString~MultiLineString} or\n * {@link module:ol/geom/MultiPolygon~MultiPolygon}.\n */\n\n/**\n * @typedef {'left' | 'center' | 'right'} TextJustify\n */\n\n/**\n * The default fill color to use if no fill was set at construction time; a\n * blackish `#333`.\n *\n * @const {string}\n */\nconst DEFAULT_FILL_COLOR = '#333';\n\n/**\n * @typedef {Object} Options\n * @property {string} [font] Font style as CSS `font` value, see:\n * https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/font. Default is `'10px sans-serif'`\n * @property {number} [maxAngle=Math.PI/4] When `placement` is set to `'line'`, allow a maximum angle between adjacent characters.\n * The expected value is in radians, and the default is 45° (`Math.PI / 4`).\n * @property {number} [offsetX=0] Horizontal text offset in pixels. A positive will shift the text right.\n * @property {number} [offsetY=0] Vertical text offset in pixels. A positive will shift the text down.\n * @property {boolean} [overflow=false] For polygon labels or when `placement` is set to `'line'`, allow text to exceed\n * the width of the polygon at the label position or the length of the path that it follows.\n * @property {TextPlacement} [placement='point'] Text placement.\n * @property {number} [repeat] Repeat interval. When set, the text will be repeated at this interval, which specifies\n * the distance between two text anchors in pixels. Only available when `placement` is set to `'line'`. Overrides 'textAlign'.\n * @property {number|import(\"../size.js\").Size} [scale] Scale.\n * @property {boolean} [rotateWithView=false] Whether to rotate the text with the view.\n * @property {number} [rotation=0] Rotation in radians (positive rotation clockwise).\n * @property {string|Array<string>} [text] Text content or rich text content. For plain text provide a string, which can\n * contain line breaks (`\\n`). For rich text provide an array of text/font tuples. A tuple consists of the text to\n * render and the font to use (or `''` to use the text style's font). A line break has to be a separate tuple (i.e. `'\\n', ''`).\n * **Example:** `['foo', 'bold 10px sans-serif', ' bar', 'italic 10px sans-serif', ' baz', '']` will yield \"**foo** *bar* baz\".\n * **Note:** Rich text is not supported for `placement: 'line'` or the immediate rendering API.\n * @property {CanvasTextAlign} [textAlign] Text alignment. Possible values: `'left'`, `'right'`, `'center'`, `'end'` or `'start'`.\n * Default is `'center'` for `placement: 'point'`. For `placement: 'line'`, the default is to let the renderer choose a\n * placement where `maxAngle` is not exceeded.\n * @property {TextJustify} [justify] Text justification within the text box.\n * If not set, text is justified towards the `textAlign` anchor.\n * Otherwise, use options `'left'`, `'center'`, or `'right'` to justify the text within the text box.\n * **Note:** `justify` is ignored for immediate rendering and also for `placement: 'line'`.\n * @property {CanvasTextBaseline} [textBaseline='middle'] Text base line. Possible values: `'bottom'`, `'top'`, `'middle'`, `'alphabetic'`,\n * `'hanging'`, `'ideographic'`.\n * @property {import(\"./Fill.js\").default|null} [fill] Fill style. If none is provided, we'll use a dark fill-style (#333). Specify `null` for no fill.\n * @property {import(\"./Stroke.js\").default} [stroke] Stroke style.\n * @property {import(\"./Fill.js\").default} [backgroundFill] Fill style for the text background when `placement` is\n * `'point'`. Default is no fill.\n * @property {import(\"./Stroke.js\").default} [backgroundStroke] Stroke style for the text background  when `placement`\n * is `'point'`. Default is no stroke.\n * @property {Array<number>} [padding=[0, 0, 0, 0]] Padding in pixels around the text for decluttering and background. The order of\n * values in the array is `[top, right, bottom, left]`.\n */\n\n/**\n * @classdesc\n * Set text style for vector features.\n * @api\n */\nclass Text {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options || {};\n\n    /**\n     * @private\n     * @type {string|undefined}\n     */\n    this.font_ = options.font;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.rotation_ = options.rotation;\n\n    /**\n     * @private\n     * @type {boolean|undefined}\n     */\n    this.rotateWithView_ = options.rotateWithView;\n\n    /**\n     * @private\n     * @type {number|import(\"../size.js\").Size|undefined}\n     */\n    this.scale_ = options.scale;\n\n    /**\n     * @private\n     * @type {import(\"../size.js\").Size}\n     */\n    this.scaleArray_ = toSize(options.scale !== undefined ? options.scale : 1);\n\n    /**\n     * @private\n     * @type {string|Array<string>|undefined}\n     */\n    this.text_ = options.text;\n\n    /**\n     * @private\n     * @type {CanvasTextAlign|undefined}\n     */\n    this.textAlign_ = options.textAlign;\n\n    /**\n     * @private\n     * @type {TextJustify|undefined}\n     */\n    this.justify_ = options.justify;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.repeat_ = options.repeat;\n\n    /**\n     * @private\n     * @type {CanvasTextBaseline|undefined}\n     */\n    this.textBaseline_ = options.textBaseline;\n\n    /**\n     * @private\n     * @type {import(\"./Fill.js\").default}\n     */\n    this.fill_ =\n      options.fill !== undefined\n        ? options.fill\n        : new Fill({color: DEFAULT_FILL_COLOR});\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxAngle_ =\n      options.maxAngle !== undefined ? options.maxAngle : Math.PI / 4;\n\n    /**\n     * @private\n     * @type {TextPlacement}\n     */\n    this.placement_ =\n      options.placement !== undefined ? options.placement : 'point';\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.overflow_ = !!options.overflow;\n\n    /**\n     * @private\n     * @type {import(\"./Stroke.js\").default}\n     */\n    this.stroke_ = options.stroke !== undefined ? options.stroke : null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.offsetX_ = options.offsetX !== undefined ? options.offsetX : 0;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.offsetY_ = options.offsetY !== undefined ? options.offsetY : 0;\n\n    /**\n     * @private\n     * @type {import(\"./Fill.js\").default}\n     */\n    this.backgroundFill_ = options.backgroundFill\n      ? options.backgroundFill\n      : null;\n\n    /**\n     * @private\n     * @type {import(\"./Stroke.js\").default}\n     */\n    this.backgroundStroke_ = options.backgroundStroke\n      ? options.backgroundStroke\n      : null;\n\n    /**\n     * @private\n     * @type {Array<number>|null}\n     */\n    this.padding_ = options.padding === undefined ? null : options.padding;\n  }\n\n  /**\n   * Clones the style.\n   * @return {Text} The cloned style.\n   * @api\n   */\n  clone() {\n    const scale = this.getScale();\n    return new Text({\n      font: this.getFont(),\n      placement: this.getPlacement(),\n      repeat: this.getRepeat(),\n      maxAngle: this.getMaxAngle(),\n      overflow: this.getOverflow(),\n      rotation: this.getRotation(),\n      rotateWithView: this.getRotateWithView(),\n      scale: Array.isArray(scale) ? scale.slice() : scale,\n      text: this.getText(),\n      textAlign: this.getTextAlign(),\n      justify: this.getJustify(),\n      textBaseline: this.getTextBaseline(),\n      fill: this.getFill() ? this.getFill().clone() : undefined,\n      stroke: this.getStroke() ? this.getStroke().clone() : undefined,\n      offsetX: this.getOffsetX(),\n      offsetY: this.getOffsetY(),\n      backgroundFill: this.getBackgroundFill()\n        ? this.getBackgroundFill().clone()\n        : undefined,\n      backgroundStroke: this.getBackgroundStroke()\n        ? this.getBackgroundStroke().clone()\n        : undefined,\n      padding: this.getPadding() || undefined,\n    });\n  }\n\n  /**\n   * Get the `overflow` configuration.\n   * @return {boolean} Let text overflow the length of the path they follow.\n   * @api\n   */\n  getOverflow() {\n    return this.overflow_;\n  }\n\n  /**\n   * Get the font name.\n   * @return {string|undefined} Font.\n   * @api\n   */\n  getFont() {\n    return this.font_;\n  }\n\n  /**\n   * Get the maximum angle between adjacent characters.\n   * @return {number} Angle in radians.\n   * @api\n   */\n  getMaxAngle() {\n    return this.maxAngle_;\n  }\n\n  /**\n   * Get the label placement.\n   * @return {TextPlacement} Text placement.\n   * @api\n   */\n  getPlacement() {\n    return this.placement_;\n  }\n\n  /**\n   * Get the repeat interval of the text.\n   * @return {number|undefined} Repeat interval in pixels.\n   * @api\n   */\n  getRepeat() {\n    return this.repeat_;\n  }\n\n  /**\n   * Get the x-offset for the text.\n   * @return {number} Horizontal text offset.\n   * @api\n   */\n  getOffsetX() {\n    return this.offsetX_;\n  }\n\n  /**\n   * Get the y-offset for the text.\n   * @return {number} Vertical text offset.\n   * @api\n   */\n  getOffsetY() {\n    return this.offsetY_;\n  }\n\n  /**\n   * Get the fill style for the text.\n   * @return {import(\"./Fill.js\").default} Fill style.\n   * @api\n   */\n  getFill() {\n    return this.fill_;\n  }\n\n  /**\n   * Determine whether the text rotates with the map.\n   * @return {boolean|undefined} Rotate with map.\n   * @api\n   */\n  getRotateWithView() {\n    return this.rotateWithView_;\n  }\n\n  /**\n   * Get the text rotation.\n   * @return {number|undefined} Rotation.\n   * @api\n   */\n  getRotation() {\n    return this.rotation_;\n  }\n\n  /**\n   * Get the text scale.\n   * @return {number|import(\"../size.js\").Size|undefined} Scale.\n   * @api\n   */\n  getScale() {\n    return this.scale_;\n  }\n\n  /**\n   * Get the symbolizer scale array.\n   * @return {import(\"../size.js\").Size} Scale array.\n   */\n  getScaleArray() {\n    return this.scaleArray_;\n  }\n\n  /**\n   * Get the stroke style for the text.\n   * @return {import(\"./Stroke.js\").default} Stroke style.\n   * @api\n   */\n  getStroke() {\n    return this.stroke_;\n  }\n\n  /**\n   * Get the text to be rendered.\n   * @return {string|Array<string>|undefined} Text.\n   * @api\n   */\n  getText() {\n    return this.text_;\n  }\n\n  /**\n   * Get the text alignment.\n   * @return {CanvasTextAlign|undefined} Text align.\n   * @api\n   */\n  getTextAlign() {\n    return this.textAlign_;\n  }\n\n  /**\n   * Get the justification.\n   * @return {TextJustify|undefined} Justification.\n   * @api\n   */\n  getJustify() {\n    return this.justify_;\n  }\n\n  /**\n   * Get the text baseline.\n   * @return {CanvasTextBaseline|undefined} Text baseline.\n   * @api\n   */\n  getTextBaseline() {\n    return this.textBaseline_;\n  }\n\n  /**\n   * Get the background fill style for the text.\n   * @return {import(\"./Fill.js\").default} Fill style.\n   * @api\n   */\n  getBackgroundFill() {\n    return this.backgroundFill_;\n  }\n\n  /**\n   * Get the background stroke style for the text.\n   * @return {import(\"./Stroke.js\").default} Stroke style.\n   * @api\n   */\n  getBackgroundStroke() {\n    return this.backgroundStroke_;\n  }\n\n  /**\n   * Get the padding for the text.\n   * @return {Array<number>|null} Padding.\n   * @api\n   */\n  getPadding() {\n    return this.padding_;\n  }\n\n  /**\n   * Set the `overflow` property.\n   *\n   * @param {boolean} overflow Let text overflow the path that it follows.\n   * @api\n   */\n  setOverflow(overflow) {\n    this.overflow_ = overflow;\n  }\n\n  /**\n   * Set the font.\n   *\n   * @param {string|undefined} font Font.\n   * @api\n   */\n  setFont(font) {\n    this.font_ = font;\n  }\n\n  /**\n   * Set the maximum angle between adjacent characters.\n   *\n   * @param {number} maxAngle Angle in radians.\n   * @api\n   */\n  setMaxAngle(maxAngle) {\n    this.maxAngle_ = maxAngle;\n  }\n\n  /**\n   * Set the x offset.\n   *\n   * @param {number} offsetX Horizontal text offset.\n   * @api\n   */\n  setOffsetX(offsetX) {\n    this.offsetX_ = offsetX;\n  }\n\n  /**\n   * Set the y offset.\n   *\n   * @param {number} offsetY Vertical text offset.\n   * @api\n   */\n  setOffsetY(offsetY) {\n    this.offsetY_ = offsetY;\n  }\n\n  /**\n   * Set the text placement.\n   *\n   * @param {TextPlacement} placement Placement.\n   * @api\n   */\n  setPlacement(placement) {\n    this.placement_ = placement;\n  }\n\n  /**\n   * Set the repeat interval of the text.\n   * @param {number|undefined} [repeat] Repeat interval in pixels.\n   * @api\n   */\n  setRepeat(repeat) {\n    this.repeat_ = repeat;\n  }\n\n  /**\n   * Set whether to rotate the text with the view.\n   *\n   * @param {boolean} rotateWithView Rotate with map.\n   * @api\n   */\n  setRotateWithView(rotateWithView) {\n    this.rotateWithView_ = rotateWithView;\n  }\n\n  /**\n   * Set the fill.\n   *\n   * @param {import(\"./Fill.js\").default} fill Fill style.\n   * @api\n   */\n  setFill(fill) {\n    this.fill_ = fill;\n  }\n\n  /**\n   * Set the rotation.\n   *\n   * @param {number|undefined} rotation Rotation.\n   * @api\n   */\n  setRotation(rotation) {\n    this.rotation_ = rotation;\n  }\n\n  /**\n   * Set the scale.\n   *\n   * @param {number|import(\"../size.js\").Size|undefined} scale Scale.\n   * @api\n   */\n  setScale(scale) {\n    this.scale_ = scale;\n    this.scaleArray_ = toSize(scale !== undefined ? scale : 1);\n  }\n\n  /**\n   * Set the stroke.\n   *\n   * @param {import(\"./Stroke.js\").default} stroke Stroke style.\n   * @api\n   */\n  setStroke(stroke) {\n    this.stroke_ = stroke;\n  }\n\n  /**\n   * Set the text.\n   *\n   * @param {string|Array<string>|undefined} text Text.\n   * @api\n   */\n  setText(text) {\n    this.text_ = text;\n  }\n\n  /**\n   * Set the text alignment.\n   *\n   * @param {CanvasTextAlign|undefined} textAlign Text align.\n   * @api\n   */\n  setTextAlign(textAlign) {\n    this.textAlign_ = textAlign;\n  }\n\n  /**\n   * Set the justification.\n   *\n   * @param {TextJustify|undefined} justify Justification.\n   * @api\n   */\n  setJustify(justify) {\n    this.justify_ = justify;\n  }\n\n  /**\n   * Set the text baseline.\n   *\n   * @param {CanvasTextBaseline|undefined} textBaseline Text baseline.\n   * @api\n   */\n  setTextBaseline(textBaseline) {\n    this.textBaseline_ = textBaseline;\n  }\n\n  /**\n   * Set the background fill.\n   *\n   * @param {import(\"./Fill.js\").default} fill Fill style.\n   * @api\n   */\n  setBackgroundFill(fill) {\n    this.backgroundFill_ = fill;\n  }\n\n  /**\n   * Set the background stroke.\n   *\n   * @param {import(\"./Stroke.js\").default} stroke Stroke style.\n   * @api\n   */\n  setBackgroundStroke(stroke) {\n    this.backgroundStroke_ = stroke;\n  }\n\n  /**\n   * Set the padding (`[top, right, bottom, left]`).\n   *\n   * @param {Array<number>|null} padding Padding.\n   * @api\n   */\n  setPadding(padding) {\n    this.padding_ = padding;\n  }\n}\n\nexport default Text;\n", "/**\n * @module ol/style/IconImage\n */\n\nimport EventTarget from '../events/Target.js';\nimport EventType from '../events/EventType.js';\nimport ImageState from '../ImageState.js';\nimport {asString} from '../color.js';\nimport {createCanvasContext2D} from '../dom.js';\nimport {shared as iconImageCache} from './IconImageCache.js';\nimport {listenImage} from '../Image.js';\n\n/**\n * @type {CanvasRenderingContext2D}\n */\nlet taintedTestContext = null;\n\nclass IconImage extends EventTarget {\n  /**\n   * @param {HTMLImageElement|HTMLCanvasElement} image Image.\n   * @param {string|undefined} src Src.\n   * @param {import(\"../size.js\").Size} size Size.\n   * @param {?string} crossOrigin Cross origin.\n   * @param {import(\"../ImageState.js\").default} imageState Image state.\n   * @param {import(\"../color.js\").Color} color Color.\n   */\n  constructor(image, src, size, crossOrigin, imageState, color) {\n    super();\n\n    /**\n     * @private\n     * @type {HTMLImageElement|HTMLCanvasElement}\n     */\n    this.hitDetectionImage_ = null;\n\n    /**\n     * @private\n     * @type {HTMLImageElement|HTMLCanvasElement}\n     */\n    this.image_ = image;\n\n    /**\n     * @private\n     * @type {string|null}\n     */\n    this.crossOrigin_ = crossOrigin;\n\n    /**\n     * @private\n     * @type {Object<number, HTMLCanvasElement>}\n     */\n    this.canvas_ = {};\n\n    /**\n     * @private\n     * @type {import(\"../color.js\").Color}\n     */\n    this.color_ = color;\n\n    /**\n     * @private\n     * @type {?function():void}\n     */\n    this.unlisten_ = null;\n\n    /**\n     * @private\n     * @type {import(\"../ImageState.js\").default}\n     */\n    this.imageState_ = imageState;\n\n    /**\n     * @private\n     * @type {import(\"../size.js\").Size}\n     */\n    this.size_ = size;\n\n    /**\n     * @private\n     * @type {string|undefined}\n     */\n    this.src_ = src;\n\n    /**\n     * @private\n     */\n    this.tainted_;\n  }\n\n  /**\n   * @private\n   */\n  initializeImage_() {\n    this.image_ = new Image();\n    if (this.crossOrigin_ !== null) {\n      this.image_.crossOrigin = this.crossOrigin_;\n    }\n  }\n\n  /**\n   * @private\n   * @return {boolean} The image canvas is tainted.\n   */\n  isTainted_() {\n    if (this.tainted_ === undefined && this.imageState_ === ImageState.LOADED) {\n      if (!taintedTestContext) {\n        taintedTestContext = createCanvasContext2D(1, 1, undefined, {\n          willReadFrequently: true,\n        });\n      }\n      taintedTestContext.drawImage(this.image_, 0, 0);\n      try {\n        taintedTestContext.getImageData(0, 0, 1, 1);\n        this.tainted_ = false;\n      } catch (e) {\n        taintedTestContext = null;\n        this.tainted_ = true;\n      }\n    }\n    return this.tainted_ === true;\n  }\n\n  /**\n   * @private\n   */\n  dispatchChangeEvent_() {\n    this.dispatchEvent(EventType.CHANGE);\n  }\n\n  /**\n   * @private\n   */\n  handleImageError_() {\n    this.imageState_ = ImageState.ERROR;\n    this.unlistenImage_();\n    this.dispatchChangeEvent_();\n  }\n\n  /**\n   * @private\n   */\n  handleImageLoad_() {\n    this.imageState_ = ImageState.LOADED;\n    if (this.size_) {\n      this.image_.width = this.size_[0];\n      this.image_.height = this.size_[1];\n    } else {\n      this.size_ = [this.image_.width, this.image_.height];\n    }\n    this.unlistenImage_();\n    this.dispatchChangeEvent_();\n  }\n\n  /**\n   * @param {number} pixelRatio Pixel ratio.\n   * @return {HTMLImageElement|HTMLCanvasElement} Image or Canvas element.\n   */\n  getImage(pixelRatio) {\n    if (!this.image_) {\n      this.initializeImage_();\n    }\n    this.replaceColor_(pixelRatio);\n    return this.canvas_[pixelRatio] ? this.canvas_[pixelRatio] : this.image_;\n  }\n\n  /**\n   * @param {number} pixelRatio Pixel ratio.\n   * @return {number} Image or Canvas element.\n   */\n  getPixelRatio(pixelRatio) {\n    this.replaceColor_(pixelRatio);\n    return this.canvas_[pixelRatio] ? pixelRatio : 1;\n  }\n\n  /**\n   * @return {import(\"../ImageState.js\").default} Image state.\n   */\n  getImageState() {\n    return this.imageState_;\n  }\n\n  /**\n   * @return {HTMLImageElement|HTMLCanvasElement} Image element.\n   */\n  getHitDetectionImage() {\n    if (!this.image_) {\n      this.initializeImage_();\n    }\n    if (!this.hitDetectionImage_) {\n      if (this.isTainted_()) {\n        const width = this.size_[0];\n        const height = this.size_[1];\n        const context = createCanvasContext2D(width, height);\n        context.fillRect(0, 0, width, height);\n        this.hitDetectionImage_ = context.canvas;\n      } else {\n        this.hitDetectionImage_ = this.image_;\n      }\n    }\n    return this.hitDetectionImage_;\n  }\n\n  /**\n   * Get the size of the icon (in pixels).\n   * @return {import(\"../size.js\").Size} Image size.\n   */\n  getSize() {\n    return this.size_;\n  }\n\n  /**\n   * @return {string|undefined} Image src.\n   */\n  getSrc() {\n    return this.src_;\n  }\n\n  /**\n   * Load not yet loaded URI.\n   */\n  load() {\n    if (this.imageState_ !== ImageState.IDLE) {\n      return;\n    }\n    if (!this.image_) {\n      this.initializeImage_();\n    }\n\n    this.imageState_ = ImageState.LOADING;\n    try {\n      /** @type {HTMLImageElement} */ (this.image_).src = this.src_;\n    } catch (e) {\n      this.handleImageError_();\n    }\n    this.unlisten_ = listenImage(\n      this.image_,\n      this.handleImageLoad_.bind(this),\n      this.handleImageError_.bind(this)\n    );\n  }\n\n  /**\n   * @param {number} pixelRatio Pixel ratio.\n   * @private\n   */\n  replaceColor_(pixelRatio) {\n    if (\n      !this.color_ ||\n      this.canvas_[pixelRatio] ||\n      this.imageState_ !== ImageState.LOADED\n    ) {\n      return;\n    }\n\n    const image = this.image_;\n    const canvas = document.createElement('canvas');\n    canvas.width = Math.ceil(image.width * pixelRatio);\n    canvas.height = Math.ceil(image.height * pixelRatio);\n\n    const ctx = canvas.getContext('2d');\n    ctx.scale(pixelRatio, pixelRatio);\n    ctx.drawImage(image, 0, 0);\n\n    ctx.globalCompositeOperation = 'multiply';\n    ctx.fillStyle = asString(this.color_);\n    ctx.fillRect(0, 0, canvas.width / pixelRatio, canvas.height / pixelRatio);\n\n    ctx.globalCompositeOperation = 'destination-in';\n    ctx.drawImage(image, 0, 0);\n\n    this.canvas_[pixelRatio] = canvas;\n  }\n\n  /**\n   * Discards event handlers which listen for load completion or errors.\n   *\n   * @private\n   */\n  unlistenImage_() {\n    if (this.unlisten_) {\n      this.unlisten_();\n      this.unlisten_ = null;\n    }\n  }\n}\n\n/**\n * @param {HTMLImageElement|HTMLCanvasElement} image Image.\n * @param {string} src Src.\n * @param {import(\"../size.js\").Size} size Size.\n * @param {?string} crossOrigin Cross origin.\n * @param {import(\"../ImageState.js\").default} imageState Image state.\n * @param {import(\"../color.js\").Color} color Color.\n * @return {IconImage} Icon image.\n */\nexport function get(image, src, size, crossOrigin, imageState, color) {\n  let iconImage = iconImageCache.get(src, crossOrigin, color);\n  if (!iconImage) {\n    iconImage = new IconImage(image, src, size, crossOrigin, imageState, color);\n    iconImageCache.set(src, crossOrigin, color, iconImage);\n  }\n  return iconImage;\n}\n\nexport default IconImage;\n", "/**\n * @module ol/style/Icon\n */\nimport EventType from '../events/EventType.js';\nimport ImageState from '../ImageState.js';\nimport ImageStyle from './Image.js';\nimport {asArray} from '../color.js';\nimport {assert} from '../asserts.js';\nimport {get as getIconImage} from './IconImage.js';\nimport {getUid} from '../util.js';\n\n/**\n * @typedef {'fraction' | 'pixels'} IconAnchorUnits\n * Anchor unit can be either a fraction of the icon size or in pixels.\n */\n\n/**\n * @typedef {'bottom-left' | 'bottom-right' | 'top-left' | 'top-right'} IconOrigin\n * Icon origin. One of 'bottom-left', 'bottom-right', 'top-left', 'top-right'.\n */\n\n/**\n * @typedef {Object} Options\n * @property {Array<number>} [anchor=[0.5, 0.5]] Anchor. Default value is the icon center.\n * @property {IconOrigin} [anchorOrigin='top-left'] Origin of the anchor: `bottom-left`, `bottom-right`,\n * `top-left` or `top-right`.\n * @property {IconAnchorUnits} [anchorXUnits='fraction'] Units in which the anchor x value is\n * specified. A value of `'fraction'` indicates the x value is a fraction of the icon. A value of `'pixels'` indicates\n * the x value in pixels.\n * @property {IconAnchorUnits} [anchorYUnits='fraction'] Units in which the anchor y value is\n * specified. A value of `'fraction'` indicates the y value is a fraction of the icon. A value of `'pixels'` indicates\n * the y value in pixels.\n * @property {import(\"../color.js\").Color|string} [color] Color to tint the icon. If not specified,\n * the icon will be left as is.\n * @property {null|string} [crossOrigin] The `crossOrigin` attribute for loaded images. Note that you must provide a\n * `crossOrigin` value if you want to access pixel data with the Canvas renderer.\n * See https://developer.mozilla.org/en-US/docs/Web/HTML/CORS_enabled_image for more detail.\n * @property {HTMLImageElement|HTMLCanvasElement} [img] Image object for the icon. If the `src` option is not provided then the\n * provided image must already be loaded. And in that case, it is required\n * to provide the size of the image, with the `imgSize` option.\n * @property {import(\"../size.js\").Size} [imgSize] Image size in pixels. Only required if `img` is set and `src` is not.\n * The provided `imgSize` needs to match the actual size of the image.\n * @property {Array<number>} [displacement=[0, 0]] Displacement of the icon in pixels.\n * Positive values will shift the icon right and up.\n * @property {number} [opacity=1] Opacity of the icon.\n * @property {number} [width] The width of the icon in pixels. This can't be used together with `scale`.\n * @property {number} [height] The height of the icon in pixels. This can't be used together with `scale`.\n * @property {number|import(\"../size.js\").Size} [scale=1] Scale.\n * @property {boolean} [rotateWithView=false] Whether to rotate the icon with the view.\n * @property {number} [rotation=0] Rotation in radians (positive rotation clockwise).\n * @property {Array<number>} [offset=[0, 0]] Offset which, together with `size` and `offsetOrigin`, defines the\n * sub-rectangle to use from the original (sprite) image.\n * @property {IconOrigin} [offsetOrigin='top-left'] Origin of the offset: `bottom-left`, `bottom-right`,\n * `top-left` or `top-right`.\n * @property {import(\"../size.js\").Size} [size] Icon size in pixels. Used together with `offset` to define the\n * sub-rectangle to use from the original (sprite) image.\n * @property {string} [src] Image source URI.\n * @property {\"declutter\"|\"obstacle\"|\"none\"|undefined} [declutterMode] Declutter mode.\n */\n\n/**\n * @param {number} width The width.\n * @param {number} height The height.\n * @param {number|undefined} wantedWidth The wanted width.\n * @param {number|undefined} wantedHeight The wanted height.\n * @return {number|Array<number>} The scale.\n */\nfunction calculateScale(width, height, wantedWidth, wantedHeight) {\n  if (wantedWidth !== undefined && wantedHeight !== undefined) {\n    return [wantedWidth / width, wantedHeight / height];\n  }\n  if (wantedWidth !== undefined) {\n    return wantedWidth / width;\n  }\n  if (wantedHeight !== undefined) {\n    return wantedHeight / height;\n  }\n  return 1;\n}\n\n/**\n * @classdesc\n * Set icon style for vector features.\n * @api\n */\nclass Icon extends ImageStyle {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options || {};\n\n    /**\n     * @type {number}\n     */\n    const opacity = options.opacity !== undefined ? options.opacity : 1;\n\n    /**\n     * @type {number}\n     */\n    const rotation = options.rotation !== undefined ? options.rotation : 0;\n\n    /**\n     * @type {number|import(\"../size.js\").Size}\n     */\n    const scale = options.scale !== undefined ? options.scale : 1;\n\n    /**\n     * @type {boolean}\n     */\n    const rotateWithView =\n      options.rotateWithView !== undefined ? options.rotateWithView : false;\n\n    super({\n      opacity: opacity,\n      rotation: rotation,\n      scale: scale,\n      displacement:\n        options.displacement !== undefined ? options.displacement : [0, 0],\n      rotateWithView: rotateWithView,\n      declutterMode: options.declutterMode,\n    });\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.anchor_ = options.anchor !== undefined ? options.anchor : [0.5, 0.5];\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.normalizedAnchor_ = null;\n\n    /**\n     * @private\n     * @type {IconOrigin}\n     */\n    this.anchorOrigin_ =\n      options.anchorOrigin !== undefined ? options.anchorOrigin : 'top-left';\n\n    /**\n     * @private\n     * @type {IconAnchorUnits}\n     */\n    this.anchorXUnits_ =\n      options.anchorXUnits !== undefined ? options.anchorXUnits : 'fraction';\n\n    /**\n     * @private\n     * @type {IconAnchorUnits}\n     */\n    this.anchorYUnits_ =\n      options.anchorYUnits !== undefined ? options.anchorYUnits : 'fraction';\n\n    /**\n     * @private\n     * @type {?string}\n     */\n    this.crossOrigin_ =\n      options.crossOrigin !== undefined ? options.crossOrigin : null;\n\n    /**\n     * @type {HTMLImageElement|HTMLCanvasElement}\n     */\n    const image = options.img !== undefined ? options.img : null;\n\n    /**\n     * @private\n     * @type {import(\"../size.js\").Size|undefined}\n     */\n    this.imgSize_ = options.imgSize;\n\n    /**\n     * @type {string|undefined}\n     */\n    let src = options.src;\n\n    assert(!(src !== undefined && image), 4); // `image` and `src` cannot be provided at the same time\n    assert(!image || (image && this.imgSize_), 5); // `imgSize` must be set when `image` is provided\n\n    if ((src === undefined || src.length === 0) && image) {\n      src = /** @type {HTMLImageElement} */ (image).src || getUid(image);\n    }\n    assert(src !== undefined && src.length > 0, 6); // A defined and non-empty `src` or `image` must be provided\n\n    // `width` or `height` cannot be provided together with `scale`\n    assert(\n      !(\n        (options.width !== undefined || options.height !== undefined) &&\n        options.scale !== undefined\n      ),\n      69\n    );\n\n    /**\n     * @type {import(\"../ImageState.js\").default}\n     */\n    const imageState =\n      options.src !== undefined ? ImageState.IDLE : ImageState.LOADED;\n\n    /**\n     * @private\n     * @type {import(\"../color.js\").Color}\n     */\n    this.color_ = options.color !== undefined ? asArray(options.color) : null;\n\n    /**\n     * @private\n     * @type {import(\"./IconImage.js\").default}\n     */\n    this.iconImage_ = getIconImage(\n      image,\n      /** @type {string} */ (src),\n      this.imgSize_ !== undefined ? this.imgSize_ : null,\n      this.crossOrigin_,\n      imageState,\n      this.color_\n    );\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.offset_ = options.offset !== undefined ? options.offset : [0, 0];\n    /**\n     * @private\n     * @type {IconOrigin}\n     */\n    this.offsetOrigin_ =\n      options.offsetOrigin !== undefined ? options.offsetOrigin : 'top-left';\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.origin_ = null;\n\n    /**\n     * @private\n     * @type {import(\"../size.js\").Size}\n     */\n    this.size_ = options.size !== undefined ? options.size : null;\n\n    /**\n     * Calculate the scale if width or height were given.\n     */\n    if (options.width !== undefined || options.height !== undefined) {\n      let width, height;\n      if (options.size) {\n        [width, height] = options.size;\n      } else {\n        const image = this.getImage(1);\n        if (\n          image instanceof HTMLCanvasElement ||\n          (image.src && image.complete)\n        ) {\n          width = image.width;\n          height = image.height;\n        } else {\n          this.initialOptions_ = options;\n          const onload = () => {\n            this.unlistenImageChange(onload);\n            if (!this.initialOptions_) {\n              return;\n            }\n            const imageSize = this.iconImage_.getSize();\n            this.setScale(\n              calculateScale(\n                imageSize[0],\n                imageSize[1],\n                options.width,\n                options.height\n              )\n            );\n          };\n          this.listenImageChange(onload);\n          return;\n        }\n      }\n      if (width !== undefined) {\n        this.setScale(\n          calculateScale(width, height, options.width, options.height)\n        );\n      }\n    }\n  }\n\n  /**\n   * Clones the style. The underlying Image/HTMLCanvasElement is not cloned.\n   * @return {Icon} The cloned style.\n   * @api\n   */\n  clone() {\n    let scale, width, height;\n    if (this.initialOptions_) {\n      width = this.initialOptions_.width;\n      height = this.initialOptions_.height;\n    } else {\n      scale = this.getScale();\n      scale = Array.isArray(scale) ? scale.slice() : scale;\n    }\n    const clone = new Icon({\n      anchor: this.anchor_.slice(),\n      anchorOrigin: this.anchorOrigin_,\n      anchorXUnits: this.anchorXUnits_,\n      anchorYUnits: this.anchorYUnits_,\n      color:\n        this.color_ && this.color_.slice\n          ? this.color_.slice()\n          : this.color_ || undefined,\n      crossOrigin: this.crossOrigin_,\n      imgSize: this.imgSize_,\n      offset: this.offset_.slice(),\n      offsetOrigin: this.offsetOrigin_,\n      opacity: this.getOpacity(),\n      rotateWithView: this.getRotateWithView(),\n      rotation: this.getRotation(),\n      scale,\n      width,\n      height,\n      size: this.size_ !== null ? this.size_.slice() : undefined,\n      src: this.getSrc(),\n      displacement: this.getDisplacement().slice(),\n      declutterMode: this.getDeclutterMode(),\n    });\n    return clone;\n  }\n\n  /**\n   * Get the anchor point in pixels. The anchor determines the center point for the\n   * symbolizer.\n   * @return {Array<number>} Anchor.\n   * @api\n   */\n  getAnchor() {\n    let anchor = this.normalizedAnchor_;\n    if (!anchor) {\n      anchor = this.anchor_;\n      const size = this.getSize();\n      if (\n        this.anchorXUnits_ == 'fraction' ||\n        this.anchorYUnits_ == 'fraction'\n      ) {\n        if (!size) {\n          return null;\n        }\n        anchor = this.anchor_.slice();\n        if (this.anchorXUnits_ == 'fraction') {\n          anchor[0] *= size[0];\n        }\n        if (this.anchorYUnits_ == 'fraction') {\n          anchor[1] *= size[1];\n        }\n      }\n\n      if (this.anchorOrigin_ != 'top-left') {\n        if (!size) {\n          return null;\n        }\n        if (anchor === this.anchor_) {\n          anchor = this.anchor_.slice();\n        }\n        if (\n          this.anchorOrigin_ == 'top-right' ||\n          this.anchorOrigin_ == 'bottom-right'\n        ) {\n          anchor[0] = -anchor[0] + size[0];\n        }\n        if (\n          this.anchorOrigin_ == 'bottom-left' ||\n          this.anchorOrigin_ == 'bottom-right'\n        ) {\n          anchor[1] = -anchor[1] + size[1];\n        }\n      }\n      this.normalizedAnchor_ = anchor;\n    }\n    const displacement = this.getDisplacement();\n    const scale = this.getScaleArray();\n    // anchor is scaled by renderer but displacement should not be scaled\n    // so divide by scale here\n    return [\n      anchor[0] - displacement[0] / scale[0],\n      anchor[1] + displacement[1] / scale[1],\n    ];\n  }\n\n  /**\n   * Set the anchor point. The anchor determines the center point for the\n   * symbolizer.\n   *\n   * @param {Array<number>} anchor Anchor.\n   * @api\n   */\n  setAnchor(anchor) {\n    this.anchor_ = anchor;\n    this.normalizedAnchor_ = null;\n  }\n\n  /**\n   * Get the icon color.\n   * @return {import(\"../color.js\").Color} Color.\n   * @api\n   */\n  getColor() {\n    return this.color_;\n  }\n\n  /**\n   * Get the image icon.\n   * @param {number} pixelRatio Pixel ratio.\n   * @return {HTMLImageElement|HTMLCanvasElement} Image or Canvas element.\n   * @api\n   */\n  getImage(pixelRatio) {\n    return this.iconImage_.getImage(pixelRatio);\n  }\n\n  /**\n   * Get the pixel ratio.\n   * @param {number} pixelRatio Pixel ratio.\n   * @return {number} The pixel ratio of the image.\n   * @api\n   */\n  getPixelRatio(pixelRatio) {\n    return this.iconImage_.getPixelRatio(pixelRatio);\n  }\n\n  /**\n   * @return {import(\"../size.js\").Size} Image size.\n   */\n  getImageSize() {\n    return this.iconImage_.getSize();\n  }\n\n  /**\n   * @return {import(\"../ImageState.js\").default} Image state.\n   */\n  getImageState() {\n    return this.iconImage_.getImageState();\n  }\n\n  /**\n   * @return {HTMLImageElement|HTMLCanvasElement} Image element.\n   */\n  getHitDetectionImage() {\n    return this.iconImage_.getHitDetectionImage();\n  }\n\n  /**\n   * Get the origin of the symbolizer.\n   * @return {Array<number>} Origin.\n   * @api\n   */\n  getOrigin() {\n    if (this.origin_) {\n      return this.origin_;\n    }\n    let offset = this.offset_;\n\n    if (this.offsetOrigin_ != 'top-left') {\n      const size = this.getSize();\n      const iconImageSize = this.iconImage_.getSize();\n      if (!size || !iconImageSize) {\n        return null;\n      }\n      offset = offset.slice();\n      if (\n        this.offsetOrigin_ == 'top-right' ||\n        this.offsetOrigin_ == 'bottom-right'\n      ) {\n        offset[0] = iconImageSize[0] - size[0] - offset[0];\n      }\n      if (\n        this.offsetOrigin_ == 'bottom-left' ||\n        this.offsetOrigin_ == 'bottom-right'\n      ) {\n        offset[1] = iconImageSize[1] - size[1] - offset[1];\n      }\n    }\n    this.origin_ = offset;\n    return this.origin_;\n  }\n\n  /**\n   * Get the image URL.\n   * @return {string|undefined} Image src.\n   * @api\n   */\n  getSrc() {\n    return this.iconImage_.getSrc();\n  }\n\n  /**\n   * Get the size of the icon (in pixels).\n   * @return {import(\"../size.js\").Size} Image size.\n   * @api\n   */\n  getSize() {\n    return !this.size_ ? this.iconImage_.getSize() : this.size_;\n  }\n\n  /**\n   * Get the width of the icon (in pixels). Will return undefined when the icon image is not yet loaded.\n   * @return {number} Icon width (in pixels).\n   * @api\n   */\n  getWidth() {\n    const scale = this.getScaleArray();\n    if (this.size_) {\n      return this.size_[0] * scale[0];\n    }\n    if (this.iconImage_.getImageState() == ImageState.LOADED) {\n      return this.iconImage_.getSize()[0] * scale[0];\n    }\n    return undefined;\n  }\n\n  /**\n   * Get the height of the icon (in pixels). Will return undefined when the icon image is not yet loaded.\n   * @return {number} Icon height (in pixels).\n   * @api\n   */\n  getHeight() {\n    const scale = this.getScaleArray();\n    if (this.size_) {\n      return this.size_[1] * scale[1];\n    }\n    if (this.iconImage_.getImageState() == ImageState.LOADED) {\n      return this.iconImage_.getSize()[1] * scale[1];\n    }\n    return undefined;\n  }\n\n  /**\n   * Set the scale.\n   *\n   * @param {number|import(\"../size.js\").Size} scale Scale.\n   * @api\n   */\n  setScale(scale) {\n    delete this.initialOptions_;\n    super.setScale(scale);\n  }\n\n  /**\n   * @param {function(import(\"../events/Event.js\").default): void} listener Listener function.\n   */\n  listenImageChange(listener) {\n    this.iconImage_.addEventListener(EventType.CHANGE, listener);\n  }\n\n  /**\n   * Load not yet loaded URI.\n   * When rendering a feature with an icon style, the vector renderer will\n   * automatically call this method. However, you might want to call this\n   * method yourself for preloading or other purposes.\n   * @api\n   */\n  load() {\n    this.iconImage_.load();\n  }\n\n  /**\n   * @param {function(import(\"../events/Event.js\").default): void} listener Listener function.\n   */\n  unlistenImageChange(listener) {\n    this.iconImage_.removeEventListener(EventType.CHANGE, listener);\n  }\n}\n\nexport default Icon;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAM,OAAN,MAAM,MAAK;AAAA;AAAA;AAAA;AAAA,EAIT,YAAY,SAAS;AACnB,cAAU,WAAW,CAAC;AAMtB,SAAK,SAAS,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,UAAM,QAAQ,KAAK,SAAS;AAC5B,WAAO,IAAI,MAAK;AAAA,MACd,OAAO,MAAM,QAAQ,KAAK,IAAI,MAAM,MAAM,IAAI,SAAS;AAAA,IACzD,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO;AACd,SAAK,SAAS;AAAA,EAChB;AACF;AAEA,IAAO,eAAQ;;;ACrCf,IAAM,SAAN,MAAM,QAAO;AAAA;AAAA;AAAA;AAAA,EAIX,YAAY,SAAS;AACnB,cAAU,WAAW,CAAC;AAMtB,SAAK,SAAS,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAM5D,SAAK,WAAW,QAAQ;AAMxB,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAMrE,SAAK,kBAAkB,QAAQ;AAM/B,SAAK,YAAY,QAAQ;AAMzB,SAAK,cAAc,QAAQ;AAM3B,SAAK,SAAS,QAAQ;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,UAAM,QAAQ,KAAK,SAAS;AAC5B,WAAO,IAAI,QAAO;AAAA,MAChB,OAAO,MAAM,QAAQ,KAAK,IAAI,MAAM,MAAM,IAAI,SAAS;AAAA,MACvD,SAAS,KAAK,WAAW;AAAA,MACzB,UAAU,KAAK,YAAY,IAAI,KAAK,YAAY,EAAE,MAAM,IAAI;AAAA,MAC5D,gBAAgB,KAAK,kBAAkB;AAAA,MACvC,UAAU,KAAK,YAAY;AAAA,MAC3B,YAAY,KAAK,cAAc;AAAA,MAC/B,OAAO,KAAK,SAAS;AAAA,IACvB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO;AACd,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAClB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,UAAU;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAkB,gBAAgB;AAChC,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,UAAU;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,YAAY;AACxB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO;AACd,SAAK,SAAS;AAAA,EAChB;AACF;AAEA,IAAO,iBAAQ;;;AC5Mf,IAAM,aAAN,MAAM,YAAW;AAAA;AAAA;AAAA;AAAA,EAIf,YAAY,SAAS;AAKnB,SAAK,WAAW,QAAQ;AAMxB,SAAK,kBAAkB,QAAQ;AAM/B,SAAK,YAAY,QAAQ;AAMzB,SAAK,SAAS,QAAQ;AAMtB,SAAK,cAAc,OAAO,QAAQ,KAAK;AAMvC,SAAK,gBAAgB,QAAQ;AAM7B,SAAK,iBAAiB,QAAQ;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,UAAM,QAAQ,KAAK,SAAS;AAC5B,WAAO,IAAI,YAAW;AAAA,MACpB,SAAS,KAAK,WAAW;AAAA,MACzB,OAAO,MAAM,QAAQ,KAAK,IAAI,MAAM,MAAM,IAAI;AAAA,MAC9C,UAAU,KAAK,YAAY;AAAA,MAC3B,gBAAgB,KAAK,kBAAkB;AAAA,MACvC,cAAc,KAAK,gBAAgB,EAAE,MAAM;AAAA,MAC3C,eAAe,KAAK,iBAAiB;AAAA,IACvC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,YAAY;AACnB,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB;AACrB,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,YAAY;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AACd,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AACb,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,cAAc;AAC5B,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAClB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAkB,gBAAgB;AAChC,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,UAAU;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO;AACd,SAAK,SAAS;AACd,SAAK,cAAc,OAAO,KAAK;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,UAAU;AAC1B,aAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACL,aAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,UAAU;AAC5B,aAAS;AAAA,EACX;AACF;AAEA,IAAO,gBAAQ;;;ACnRR,SAAS,YAAY,OAAO;AACjC,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,WAAO,SAAS,KAAK;AAAA,EACvB;AACA,SAAO;AACT;;;AC4BA,IAAM,eAAN,MAAM,sBAAqB,cAAW;AAAA;AAAA;AAAA;AAAA,EAIpC,YAAY,SAAS;AAInB,UAAM,iBACJ,QAAQ,mBAAmB,SAAY,QAAQ,iBAAiB;AAElE,UAAM;AAAA,MACJ,SAAS;AAAA,MACT;AAAA,MACA,UAAU,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAAA,MAC9D,OAAO,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAAA,MACrD,cACE,QAAQ,iBAAiB,SAAY,QAAQ,eAAe,CAAC,GAAG,CAAC;AAAA,MACnE,eAAe,QAAQ;AAAA,IACzB,CAAC;AAMD,SAAK,UAAU;AAMf,SAAK,sBAAsB;AAM3B,SAAK,QAAQ,QAAQ,SAAS,SAAY,QAAQ,OAAO;AAMzD,SAAK,UAAU,CAAC,GAAG,CAAC;AAMpB,SAAK,UAAU,QAAQ;AAMvB,SAAK,UACH,QAAQ,WAAW,SAAY,QAAQ,SAAS,QAAQ;AAM1D,SAAK,WAAW,QAAQ;AAMxB,SAAK,SAAS,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAM5D,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAM/D,SAAK,QAAQ;AAMb,SAAK,iBAAiB;AAEtB,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,QAAQ,IAAI,cAAa;AAAA,MAC7B,MAAM,KAAK,QAAQ,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAI;AAAA,MAChD,QAAQ,KAAK,UAAU;AAAA,MACvB,QAAQ,KAAK,UAAU;AAAA,MACvB,SAAS,KAAK,WAAW;AAAA,MACzB,OAAO,KAAK,SAAS;AAAA,MACrB,QAAQ,KAAK,UAAU,IAAI,KAAK,UAAU,EAAE,MAAM,IAAI;AAAA,MACtD,UAAU,KAAK,YAAY;AAAA,MAC3B,gBAAgB,KAAK,kBAAkB;AAAA,MACvC,OAAO,MAAM,QAAQ,KAAK,IAAI,MAAM,MAAM,IAAI;AAAA,MAC9C,cAAc,KAAK,gBAAgB,EAAE,MAAM;AAAA,MAC3C,eAAe,KAAK,iBAAiB;AAAA,IACvC,CAAC;AACD,UAAM,WAAW,KAAK,WAAW,CAAC;AAClC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV,UAAM,OAAO,KAAK;AAClB,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,UAAM,eAAe,KAAK,gBAAgB;AAC1C,UAAM,QAAQ,KAAK,cAAc;AAGjC,WAAO;AAAA,MACL,KAAK,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI,MAAM,CAAC;AAAA,MACvC,KAAK,CAAC,IAAI,IAAI,aAAa,CAAC,IAAI,MAAM,CAAC;AAAA,IACzC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,MAAM;AACZ,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,QAAI,CAAC,KAAK,qBAAqB;AAC7B,WAAK,0BAA0B,KAAK,cAAc;AAAA,IACpD;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,YAAY;AACnB,QAAI,QAAQ,KAAK,QAAQ,UAAU;AACnC,QAAI,CAAC,OAAO;AACV,YAAM,gBAAgB,KAAK;AAC3B,YAAM,UAAU;AAAA,QACd,cAAc,OAAO;AAAA,QACrB,cAAc,OAAO;AAAA,MACvB;AACA,WAAK,MAAM,eAAe,SAAS,UAAU;AAE7C,cAAQ,QAAQ;AAChB,WAAK,QAAQ,UAAU,IAAI;AAAA,IAC7B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,YAAY;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,WAAO,mBAAW;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ;AAChB,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,UAAU;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA,EAK7B,OAAO;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA,EAKR,oBAAoB,UAAU;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU/B,uBAAuB,UAAU,aAAa,YAAY;AACxD,QACE,gBAAgB,KAChB,KAAK,YAAY,YAChB,aAAa,WAAW,aAAa,SACtC;AACA,aAAO;AAAA,IACT;AAuBA,QAAI,KAAK,KAAK;AACd,QAAI,KAAK,KAAK,aAAa,SAAY,KAAK,KAAK;AACjD,QAAI,KAAK,IAAI;AACX,YAAM,MAAM;AACZ,WAAK;AACL,WAAK;AAAA,IACP;AACA,UAAM,SACJ,KAAK,aAAa,SAAY,KAAK,UAAU,KAAK,UAAU;AAC9D,UAAM,QAAS,IAAI,KAAK,KAAM;AAC9B,UAAM,IAAI,KAAK,KAAK,IAAI,KAAK;AAC7B,UAAM,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC;AACnC,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AACjC,UAAM,aAAa,IAAI;AACvB,QAAI,aAAa,WAAW,cAAc,YAAY;AACpD,aAAO,aAAa;AAAA,IACtB;AAcA,UAAM,IAAI,cAAc,IAAI;AAC5B,UAAM,IAAK,cAAc,KAAM,IAAI;AACnC,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC;AAClD,UAAM,WAAW,OAAO;AACxB,QAAI,KAAK,aAAa,UAAa,aAAa,SAAS;AACvD,aAAO,WAAW;AAAA,IACpB;AAGA,UAAM,KAAK,KAAK,KAAK,IAAI,KAAK;AAC9B,UAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACtC,UAAM,KAAK,KAAK;AAChB,UAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AACtC,UAAM,kBAAkB,KAAK;AAC7B,QAAI,mBAAmB,YAAY;AACjC,YAAM,cAAe,kBAAkB,cAAe,IAAI,KAAK;AAC/D,aAAO,IAAI,KAAK,IAAI,UAAU,WAAW;AAAA,IAC3C;AACA,WAAO,WAAW;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB;AACpB,QAAI,WAAW;AACf,QAAI,aAAa;AACjB,QAAI,WAAW;AACf,QAAI,iBAAiB;AACrB,QAAI;AACJ,QAAI,cAAc;AAElB,QAAI,KAAK,SAAS;AAChB,oBAAc,KAAK,QAAQ,SAAS;AACpC,UAAI,gBAAgB,MAAM;AACxB,sBAAc;AAAA,MAChB;AACA,oBAAc,YAAY,WAAW;AACrC,oBAAc,KAAK,QAAQ,SAAS;AACpC,UAAI,gBAAgB,QAAW;AAC7B,sBAAc;AAAA,MAChB;AACA,iBAAW,KAAK,QAAQ,YAAY;AACpC,uBAAiB,KAAK,QAAQ,kBAAkB;AAChD,iBAAW,KAAK,QAAQ,YAAY;AACpC,UAAI,aAAa,QAAW;AAC1B,mBAAW;AAAA,MACb;AACA,mBAAa,KAAK,QAAQ,cAAc;AACxC,UAAI,eAAe,QAAW;AAC5B,qBAAa;AAAA,MACf;AAAA,IACF;AAEA,UAAM,MAAM,KAAK,uBAAuB,UAAU,aAAa,UAAU;AACzE,UAAM,YAAY,KAAK,IAAI,KAAK,SAAS,KAAK,YAAY,CAAC;AAC3D,UAAM,OAAO,KAAK,KAAK,IAAI,YAAY,GAAG;AAE1C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,SAAK,iBAAiB,KAAK,oBAAoB;AAC/C,UAAM,OAAO,KAAK,eAAe;AACjC,SAAK,UAAU,CAAC;AAChB,SAAK,QAAQ,CAAC,MAAM,IAAI;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,eAAe,SAAS,YAAY;AACxC,YAAQ,MAAM,YAAY,UAAU;AAEpC,YAAQ,UAAU,cAAc,OAAO,GAAG,cAAc,OAAO,CAAC;AAEhE,SAAK,YAAY,OAAO;AAExB,QAAI,KAAK,OAAO;AACd,UAAI,QAAQ,KAAK,MAAM,SAAS;AAChC,UAAI,UAAU,MAAM;AAClB,gBAAQ;AAAA,MACV;AACA,cAAQ,YAAY,YAAY,KAAK;AACrC,cAAQ,KAAK;AAAA,IACf;AACA,QAAI,KAAK,SAAS;AAChB,cAAQ,cAAc,cAAc;AACpC,cAAQ,YAAY,cAAc;AAClC,UAAI,cAAc,UAAU;AAC1B,gBAAQ,YAAY,cAAc,QAAQ;AAC1C,gBAAQ,iBAAiB,cAAc;AAAA,MACzC;AACA,cAAQ,WAAW,cAAc;AACjC,cAAQ,aAAa,cAAc;AACnC,cAAQ,OAAO;AAAA,IACjB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,0BAA0B,eAAe;AACvC,QAAI,KAAK,OAAO;AACd,UAAI,QAAQ,KAAK,MAAM,SAAS;AAGhC,UAAI,UAAU;AACd,UAAI,OAAO,UAAU,UAAU;AAC7B,gBAAQ,QAAQ,KAAK;AAAA,MACvB;AACA,UAAI,UAAU,MAAM;AAClB,kBAAU;AAAA,MACZ,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,kBAAU,MAAM,WAAW,IAAI,MAAM,CAAC,IAAI;AAAA,MAC5C;AACA,UAAI,YAAY,GAAG;AAGjB,cAAM,UAAU;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,QAChB;AACA,aAAK,sBAAsB,QAAQ;AAEnC,aAAK,wBAAwB,eAAe,OAAO;AAAA,MACrD;AAAA,IACF;AACA,QAAI,CAAC,KAAK,qBAAqB;AAC7B,WAAK,sBAAsB,KAAK,SAAS,CAAC;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,SAAS;AACnB,QAAI,SAAS,KAAK;AAClB,UAAM,SAAS,KAAK;AACpB,QAAI,WAAW,UAAU;AACvB,cAAQ,IAAI,GAAG,GAAG,QAAQ,GAAG,IAAI,KAAK,EAAE;AAAA,IAC1C,OAAO;AACL,YAAM,UAAU,KAAK,aAAa,SAAY,SAAS,KAAK;AAC5D,UAAI,KAAK,aAAa,QAAW;AAC/B,kBAAU;AAAA,MACZ;AACA,YAAM,aAAa,KAAK,SAAS,KAAK,KAAK;AAC3C,YAAM,OAAQ,IAAI,KAAK,KAAM;AAC7B,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAM,SAAS,aAAa,IAAI;AAChC,cAAM,UAAU,IAAI,MAAM,IAAI,SAAS;AACvC,gBAAQ,OAAO,UAAU,KAAK,IAAI,MAAM,GAAG,UAAU,KAAK,IAAI,MAAM,CAAC;AAAA,MACvE;AACA,cAAQ,UAAU;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB,eAAe,SAAS;AAE9C,YAAQ,UAAU,cAAc,OAAO,GAAG,cAAc,OAAO,CAAC;AAEhE,SAAK,YAAY,OAAO;AAExB,YAAQ,YAAY;AACpB,YAAQ,KAAK;AACb,QAAI,KAAK,SAAS;AAChB,cAAQ,cAAc,cAAc;AACpC,cAAQ,YAAY,cAAc;AAClC,UAAI,cAAc,UAAU;AAC1B,gBAAQ,YAAY,cAAc,QAAQ;AAC1C,gBAAQ,iBAAiB,cAAc;AAAA,MACzC;AACA,cAAQ,WAAW,cAAc;AACjC,cAAQ,aAAa,cAAc;AACnC,cAAQ,OAAO;AAAA,IACjB;AAAA,EACF;AACF;AAEA,IAAO,uBAAQ;;;ACrlBf,IAAM,cAAN,MAAM,qBAAoB,qBAAa;AAAA;AAAA;AAAA;AAAA,EAIrC,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,EAAC,QAAQ,EAAC;AAExC,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR,MAAM,QAAQ;AAAA,MACd,QAAQ,QAAQ;AAAA,MAChB,QAAQ,QAAQ;AAAA,MAChB,OAAO,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAAA,MACrD,UAAU,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAAA,MAC9D,gBACE,QAAQ,mBAAmB,SAAY,QAAQ,iBAAiB;AAAA,MAClE,cACE,QAAQ,iBAAiB,SAAY,QAAQ,eAAe,CAAC,GAAG,CAAC;AAAA,MACnE,eAAe,QAAQ;AAAA,IACzB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,QAAQ,IAAI,aAAY;AAAA,MAC5B,MAAM,KAAK,QAAQ,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAI;AAAA,MAChD,QAAQ,KAAK,UAAU,IAAI,KAAK,UAAU,EAAE,MAAM,IAAI;AAAA,MACtD,QAAQ,KAAK,UAAU;AAAA,MACvB,OAAO,MAAM,QAAQ,KAAK,IAAI,MAAM,MAAM,IAAI;AAAA,MAC9C,UAAU,KAAK,YAAY;AAAA,MAC3B,gBAAgB,KAAK,kBAAkB;AAAA,MACvC,cAAc,KAAK,gBAAgB,EAAE,MAAM;AAAA,MAC3C,eAAe,KAAK,iBAAiB;AAAA,IACvC,CAAC;AACD,UAAM,WAAW,KAAK,WAAW,CAAC;AAClC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EACd;AACF;AAEA,IAAO,iBAAQ;;;ACsEf,IAAM,QAAN,MAAM,OAAM;AAAA;AAAA;AAAA;AAAA,EAIV,YAAY,SAAS;AACnB,cAAU,WAAW,CAAC;AAMtB,SAAK,YAAY;AAMjB,SAAK,oBAAoB;AAEzB,QAAI,QAAQ,aAAa,QAAW;AAClC,WAAK,YAAY,QAAQ,QAAQ;AAAA,IACnC;AAMA,SAAK,QAAQ,QAAQ,SAAS,SAAY,QAAQ,OAAO;AAMzD,SAAK,SAAS,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAM5D,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAMrE,SAAK,wBACH,QAAQ,yBAAyB,SAC7B,QAAQ,uBACR;AAMN,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAM/D,SAAK,QAAQ,QAAQ,SAAS,SAAY,QAAQ,OAAO;AAMzD,SAAK,UAAU,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,QAAI,WAAW,KAAK,YAAY;AAChC,QAAI,YAAY,OAAO,aAAa,UAAU;AAC5C;AAAA,MACE,SACA,MAAM;AAAA,IACV;AACA,WAAO,IAAI,OAAM;AAAA,MACf;AAAA,MACA,MAAM,KAAK,QAAQ,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAI;AAAA,MAChD,OAAO,KAAK,SAAS,IAAI,KAAK,SAAS,EAAE,MAAM,IAAI;AAAA,MACnD,UAAU,KAAK,YAAY;AAAA,MAC3B,QAAQ,KAAK,UAAU,IAAI,KAAK,UAAU,EAAE,MAAM,IAAI;AAAA,MACtD,MAAM,KAAK,QAAQ,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAI;AAAA,MAChD,QAAQ,KAAK,UAAU;AAAA,IACzB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,UAAU;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,wBAAwB,UAAU;AAChC,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,0BAA0B;AACxB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,MAAM;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,OAAO;AACd,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,MAAM;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,UAAU;AACpB,QAAI,OAAO,aAAa,YAAY;AAClC,WAAK,oBAAoB;AAAA,IAC3B,WAAW,OAAO,aAAa,UAAU;AACvC,WAAK,oBAAoB,SAAU,SAAS;AAC1C;AAAA;AAAA,UACE,QAAQ,IAAI,QAAQ;AAAA;AAAA,MAExB;AAAA,IACF,WAAW,CAAC,UAAU;AACpB,WAAK,oBAAoB;AAAA,IAC3B,WAAW,aAAa,QAAW;AACjC,WAAK,oBAAoB,WAAY;AACnC;AAAA;AAAA,UAA6D;AAAA;AAAA,MAC/D;AAAA,IACF;AACA,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,SAAK,UAAU;AAAA,EACjB;AACF;AAUO,SAAS,WAAW,KAAK;AAC9B,MAAI;AAEJ,MAAI,OAAO,QAAQ,YAAY;AAC7B,oBAAgB;AAAA,EAClB,OAAO;AAIL,QAAI;AACJ,QAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,eAAS;AAAA,IACX,OAAO;AACL,aAAO;AAAA,MAA0B,IAAK,cAAe,YAAY,EAAE;AACnE,YAAM;AAAA;AAAA,QAA8B;AAAA;AACpC,eAAS,CAAC,KAAK;AAAA,IACjB;AACA,oBAAgB,WAAY;AAC1B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAKA,IAAI,gBAAgB;AAOb,SAAS,mBAAmB,SAAS,YAAY;AAMtD,MAAI,CAAC,eAAe;AAClB,UAAM,OAAO,IAAI,aAAK;AAAA,MACpB,OAAO;AAAA,IACT,CAAC;AACD,UAAM,SAAS,IAAI,eAAO;AAAA,MACxB,OAAO;AAAA,MACP,OAAO;AAAA,IACT,CAAC;AACD,oBAAgB;AAAA,MACd,IAAI,MAAM;AAAA,QACR,OAAO,IAAI,eAAY;AAAA,UACrB;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,QACD;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AACT;AAMO,SAAS,qBAAqB;AAEnC,QAAM,SAAS,CAAC;AAChB,QAAM,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC;AAC/B,QAAM,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC;AAC5B,QAAM,QAAQ;AACd,SAAO,SAAS,IAAI;AAAA,IAClB,IAAI,MAAM;AAAA,MACR,MAAM,IAAI,aAAK;AAAA,QACb,OAAO,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MAC5B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,SAAO,cAAc,IAAI,OAAO,SAAS;AAEzC,SAAO,YAAY,IAAI;AAAA,IACrB,IAAI,MAAM;AAAA,MACR,QAAQ,IAAI,eAAO;AAAA,QACjB,OAAO;AAAA,QACP,OAAO,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,IACD,IAAI,MAAM;AAAA,MACR,QAAQ,IAAI,eAAO;AAAA,QACjB,OAAO;AAAA,QACP;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,SAAO,iBAAiB,IAAI,OAAO,YAAY;AAE/C,SAAO,QAAQ,IAAI,OAAO,SAAS,EAAE,OAAO,OAAO,YAAY,CAAC;AAEhE,SAAO,OAAO,IAAI;AAAA,IAChB,IAAI,MAAM;AAAA,MACR,OAAO,IAAI,eAAY;AAAA,QACrB,QAAQ,QAAQ;AAAA,QAChB,MAAM,IAAI,aAAK;AAAA,UACb,OAAO;AAAA,QACT,CAAC;AAAA,QACD,QAAQ,IAAI,eAAO;AAAA,UACjB,OAAO;AAAA,UACP,OAAO,QAAQ;AAAA,QACjB,CAAC;AAAA,MACH,CAAC;AAAA,MACD,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACA,SAAO,YAAY,IAAI,OAAO,OAAO;AAErC,SAAO,oBAAoB,IAAI,OAAO,SAAS,EAAE;AAAA,IAC/C,OAAO,YAAY;AAAA,IACnB,OAAO,OAAO;AAAA,EAChB;AAEA,SAAO;AACT;AAOA,SAAS,wBAAwB,SAAS;AACxC,SAAO,QAAQ,YAAY;AAC7B;AAEA,IAAO,gBAAQ;;;AC7hBf,IAAM,qBAAqB;AA+C3B,IAAM,OAAN,MAAM,MAAK;AAAA;AAAA;AAAA;AAAA,EAIT,YAAY,SAAS;AACnB,cAAU,WAAW,CAAC;AAMtB,SAAK,QAAQ,QAAQ;AAMrB,SAAK,YAAY,QAAQ;AAMzB,SAAK,kBAAkB,QAAQ;AAM/B,SAAK,SAAS,QAAQ;AAMtB,SAAK,cAAc,OAAO,QAAQ,UAAU,SAAY,QAAQ,QAAQ,CAAC;AAMzE,SAAK,QAAQ,QAAQ;AAMrB,SAAK,aAAa,QAAQ;AAM1B,SAAK,WAAW,QAAQ;AAMxB,SAAK,UAAU,QAAQ;AAMvB,SAAK,gBAAgB,QAAQ;AAM7B,SAAK,QACH,QAAQ,SAAS,SACb,QAAQ,OACR,IAAI,aAAK,EAAC,OAAO,mBAAkB,CAAC;AAM1C,SAAK,YACH,QAAQ,aAAa,SAAY,QAAQ,WAAW,KAAK,KAAK;AAMhE,SAAK,aACH,QAAQ,cAAc,SAAY,QAAQ,YAAY;AAMxD,SAAK,YAAY,CAAC,CAAC,QAAQ;AAM3B,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAM/D,SAAK,WAAW,QAAQ,YAAY,SAAY,QAAQ,UAAU;AAMlE,SAAK,WAAW,QAAQ,YAAY,SAAY,QAAQ,UAAU;AAMlE,SAAK,kBAAkB,QAAQ,iBAC3B,QAAQ,iBACR;AAMJ,SAAK,oBAAoB,QAAQ,mBAC7B,QAAQ,mBACR;AAMJ,SAAK,WAAW,QAAQ,YAAY,SAAY,OAAO,QAAQ;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,UAAM,QAAQ,KAAK,SAAS;AAC5B,WAAO,IAAI,MAAK;AAAA,MACd,MAAM,KAAK,QAAQ;AAAA,MACnB,WAAW,KAAK,aAAa;AAAA,MAC7B,QAAQ,KAAK,UAAU;AAAA,MACvB,UAAU,KAAK,YAAY;AAAA,MAC3B,UAAU,KAAK,YAAY;AAAA,MAC3B,UAAU,KAAK,YAAY;AAAA,MAC3B,gBAAgB,KAAK,kBAAkB;AAAA,MACvC,OAAO,MAAM,QAAQ,KAAK,IAAI,MAAM,MAAM,IAAI;AAAA,MAC9C,MAAM,KAAK,QAAQ;AAAA,MACnB,WAAW,KAAK,aAAa;AAAA,MAC7B,SAAS,KAAK,WAAW;AAAA,MACzB,cAAc,KAAK,gBAAgB;AAAA,MACnC,MAAM,KAAK,QAAQ,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAI;AAAA,MAChD,QAAQ,KAAK,UAAU,IAAI,KAAK,UAAU,EAAE,MAAM,IAAI;AAAA,MACtD,SAAS,KAAK,WAAW;AAAA,MACzB,SAAS,KAAK,WAAW;AAAA,MACzB,gBAAgB,KAAK,kBAAkB,IACnC,KAAK,kBAAkB,EAAE,MAAM,IAC/B;AAAA,MACJ,kBAAkB,KAAK,oBAAoB,IACvC,KAAK,oBAAoB,EAAE,MAAM,IACjC;AAAA,MACJ,SAAS,KAAK,WAAW,KAAK;AAAA,IAChC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,UAAU;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,MAAM;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,UAAU;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAClB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAClB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,WAAW;AACtB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAkB,gBAAgB;AAChC,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,MAAM;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,UAAU;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO;AACd,SAAK,SAAS;AACd,SAAK,cAAc,OAAO,UAAU,SAAY,QAAQ,CAAC;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,MAAM;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,WAAW;AACtB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAClB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,cAAc;AAC5B,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAkB,MAAM;AACtB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB,QAAQ;AAC1B,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAClB,SAAK,WAAW;AAAA,EAClB;AACF;AAEA,IAAO,eAAQ;;;ACrlBf,IAAI,qBAAqB;AAEzB,IAAM,YAAN,cAAwB,eAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASlC,YAAY,OAAO,KAAK,MAAM,aAAa,YAAY,OAAO;AAC5D,UAAM;AAMN,SAAK,qBAAqB;AAM1B,SAAK,SAAS;AAMd,SAAK,eAAe;AAMpB,SAAK,UAAU,CAAC;AAMhB,SAAK,SAAS;AAMd,SAAK,YAAY;AAMjB,SAAK,cAAc;AAMnB,SAAK,QAAQ;AAMb,SAAK,OAAO;AAKZ,SAAK;AAAA,EACP;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,SAAK,SAAS,IAAI,MAAM;AACxB,QAAI,KAAK,iBAAiB,MAAM;AAC9B,WAAK,OAAO,cAAc,KAAK;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa;AACX,QAAI,KAAK,aAAa,UAAa,KAAK,gBAAgB,mBAAW,QAAQ;AACzE,UAAI,CAAC,oBAAoB;AACvB,6BAAqB,sBAAsB,GAAG,GAAG,QAAW;AAAA,UAC1D,oBAAoB;AAAA,QACtB,CAAC;AAAA,MACH;AACA,yBAAmB,UAAU,KAAK,QAAQ,GAAG,CAAC;AAC9C,UAAI;AACF,2BAAmB,aAAa,GAAG,GAAG,GAAG,CAAC;AAC1C,aAAK,WAAW;AAAA,MAClB,SAAS,GAAG;AACV,6BAAqB;AACrB,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AACA,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,SAAK,cAAc,kBAAU,MAAM;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,SAAK,cAAc,mBAAW;AAC9B,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,SAAK,cAAc,mBAAW;AAC9B,QAAI,KAAK,OAAO;AACd,WAAK,OAAO,QAAQ,KAAK,MAAM,CAAC;AAChC,WAAK,OAAO,SAAS,KAAK,MAAM,CAAC;AAAA,IACnC,OAAO;AACL,WAAK,QAAQ,CAAC,KAAK,OAAO,OAAO,KAAK,OAAO,MAAM;AAAA,IACrD;AACA,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,YAAY;AACnB,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,iBAAiB;AAAA,IACxB;AACA,SAAK,cAAc,UAAU;AAC7B,WAAO,KAAK,QAAQ,UAAU,IAAI,KAAK,QAAQ,UAAU,IAAI,KAAK;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,YAAY;AACxB,SAAK,cAAc,UAAU;AAC7B,WAAO,KAAK,QAAQ,UAAU,IAAI,aAAa;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,CAAC,KAAK,oBAAoB;AAC5B,UAAI,KAAK,WAAW,GAAG;AACrB,cAAM,QAAQ,KAAK,MAAM,CAAC;AAC1B,cAAM,SAAS,KAAK,MAAM,CAAC;AAC3B,cAAM,UAAU,sBAAsB,OAAO,MAAM;AACnD,gBAAQ,SAAS,GAAG,GAAG,OAAO,MAAM;AACpC,aAAK,qBAAqB,QAAQ;AAAA,MACpC,OAAO;AACL,aAAK,qBAAqB,KAAK;AAAA,MACjC;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,QAAI,KAAK,gBAAgB,mBAAW,MAAM;AACxC;AAAA,IACF;AACA,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,iBAAiB;AAAA,IACxB;AAEA,SAAK,cAAc,mBAAW;AAC9B,QAAI;AAC8B,MAAC,KAAK,OAAQ,MAAM,KAAK;AAAA,IAC3D,SAAS,GAAG;AACV,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,YAAY;AAAA,MACf,KAAK;AAAA,MACL,KAAK,iBAAiB,KAAK,IAAI;AAAA,MAC/B,KAAK,kBAAkB,KAAK,IAAI;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,YAAY;AACxB,QACE,CAAC,KAAK,UACN,KAAK,QAAQ,UAAU,KACvB,KAAK,gBAAgB,mBAAW,QAChC;AACA;AAAA,IACF;AAEA,UAAM,QAAQ,KAAK;AACnB,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,QAAQ,KAAK,KAAK,MAAM,QAAQ,UAAU;AACjD,WAAO,SAAS,KAAK,KAAK,MAAM,SAAS,UAAU;AAEnD,UAAM,MAAM,OAAO,WAAW,IAAI;AAClC,QAAI,MAAM,YAAY,UAAU;AAChC,QAAI,UAAU,OAAO,GAAG,CAAC;AAEzB,QAAI,2BAA2B;AAC/B,QAAI,YAAY,SAAS,KAAK,MAAM;AACpC,QAAI,SAAS,GAAG,GAAG,OAAO,QAAQ,YAAY,OAAO,SAAS,UAAU;AAExE,QAAI,2BAA2B;AAC/B,QAAI,UAAU,OAAO,GAAG,CAAC;AAEzB,SAAK,QAAQ,UAAU,IAAI;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU;AACf,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AACF;AAWO,SAAS,IAAI,OAAO,KAAK,MAAM,aAAa,YAAY,OAAO;AACpE,MAAI,YAAY,OAAe,IAAI,KAAK,aAAa,KAAK;AAC1D,MAAI,CAAC,WAAW;AACd,gBAAY,IAAI,UAAU,OAAO,KAAK,MAAM,aAAa,YAAY,KAAK;AAC1E,WAAe,IAAI,KAAK,aAAa,OAAO,SAAS;AAAA,EACvD;AACA,SAAO;AACT;AAEA,IAAO,oBAAQ;;;AC7Of,SAAS,eAAe,OAAO,QAAQ,aAAa,cAAc;AAChE,MAAI,gBAAgB,UAAa,iBAAiB,QAAW;AAC3D,WAAO,CAAC,cAAc,OAAO,eAAe,MAAM;AAAA,EACpD;AACA,MAAI,gBAAgB,QAAW;AAC7B,WAAO,cAAc;AAAA,EACvB;AACA,MAAI,iBAAiB,QAAW;AAC9B,WAAO,eAAe;AAAA,EACxB;AACA,SAAO;AACT;AAOA,IAAM,OAAN,MAAM,cAAa,cAAW;AAAA;AAAA;AAAA;AAAA,EAI5B,YAAY,SAAS;AACnB,cAAU,WAAW,CAAC;AAKtB,UAAM,UAAU,QAAQ,YAAY,SAAY,QAAQ,UAAU;AAKlE,UAAM,WAAW,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAKrE,UAAM,QAAQ,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAK5D,UAAM,iBACJ,QAAQ,mBAAmB,SAAY,QAAQ,iBAAiB;AAElE,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,cACE,QAAQ,iBAAiB,SAAY,QAAQ,eAAe,CAAC,GAAG,CAAC;AAAA,MACnE;AAAA,MACA,eAAe,QAAQ;AAAA,IACzB,CAAC;AAMD,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS,CAAC,KAAK,GAAG;AAMxE,SAAK,oBAAoB;AAMzB,SAAK,gBACH,QAAQ,iBAAiB,SAAY,QAAQ,eAAe;AAM9D,SAAK,gBACH,QAAQ,iBAAiB,SAAY,QAAQ,eAAe;AAM9D,SAAK,gBACH,QAAQ,iBAAiB,SAAY,QAAQ,eAAe;AAM9D,SAAK,eACH,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAK5D,UAAM,QAAQ,QAAQ,QAAQ,SAAY,QAAQ,MAAM;AAMxD,SAAK,WAAW,QAAQ;AAKxB,QAAI,MAAM,QAAQ;AAElB,WAAO,EAAE,QAAQ,UAAa,QAAQ,CAAC;AACvC,WAAO,CAAC,SAAU,SAAS,KAAK,UAAW,CAAC;AAE5C,SAAK,QAAQ,UAAa,IAAI,WAAW,MAAM,OAAO;AACpD;AAAA,MAAuC,MAAO,OAAO,OAAO,KAAK;AAAA,IACnE;AACA,WAAO,QAAQ,UAAa,IAAI,SAAS,GAAG,CAAC;AAG7C;AAAA,MACE,GACG,QAAQ,UAAU,UAAa,QAAQ,WAAW,WACnD,QAAQ,UAAU;AAAA,MAEpB;AAAA,IACF;AAKA,UAAM,aACJ,QAAQ,QAAQ,SAAY,mBAAW,OAAO,mBAAW;AAM3D,SAAK,SAAS,QAAQ,UAAU,SAAY,QAAQ,QAAQ,KAAK,IAAI;AAMrE,SAAK,aAAa;AAAA,MAChB;AAAA;AAAA,MACuB;AAAA,MACvB,KAAK,aAAa,SAAY,KAAK,WAAW;AAAA,MAC9C,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,IACP;AAMA,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS,CAAC,GAAG,CAAC;AAKpE,SAAK,gBACH,QAAQ,iBAAiB,SAAY,QAAQ,eAAe;AAM9D,SAAK,UAAU;AAMf,SAAK,QAAQ,QAAQ,SAAS,SAAY,QAAQ,OAAO;AAKzD,QAAI,QAAQ,UAAU,UAAa,QAAQ,WAAW,QAAW;AAC/D,UAAI,OAAO;AACX,UAAI,QAAQ,MAAM;AAChB,SAAC,OAAO,MAAM,IAAI,QAAQ;AAAA,MAC5B,OAAO;AACL,cAAMA,SAAQ,KAAK,SAAS,CAAC;AAC7B,YACEA,kBAAiB,qBAChBA,OAAM,OAAOA,OAAM,UACpB;AACA,kBAAQA,OAAM;AACd,mBAASA,OAAM;AAAA,QACjB,OAAO;AACL,eAAK,kBAAkB;AACvB,gBAAM,SAAS,MAAM;AACnB,iBAAK,oBAAoB,MAAM;AAC/B,gBAAI,CAAC,KAAK,iBAAiB;AACzB;AAAA,YACF;AACA,kBAAM,YAAY,KAAK,WAAW,QAAQ;AAC1C,iBAAK;AAAA,cACH;AAAA,gBACE,UAAU,CAAC;AAAA,gBACX,UAAU,CAAC;AAAA,gBACX,QAAQ;AAAA,gBACR,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,UACF;AACA,eAAK,kBAAkB,MAAM;AAC7B;AAAA,QACF;AAAA,MACF;AACA,UAAI,UAAU,QAAW;AACvB,aAAK;AAAA,UACH,eAAe,OAAO,QAAQ,QAAQ,OAAO,QAAQ,MAAM;AAAA,QAC7D;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,QAAI,OAAO,OAAO;AAClB,QAAI,KAAK,iBAAiB;AACxB,cAAQ,KAAK,gBAAgB;AAC7B,eAAS,KAAK,gBAAgB;AAAA,IAChC,OAAO;AACL,cAAQ,KAAK,SAAS;AACtB,cAAQ,MAAM,QAAQ,KAAK,IAAI,MAAM,MAAM,IAAI;AAAA,IACjD;AACA,UAAM,QAAQ,IAAI,MAAK;AAAA,MACrB,QAAQ,KAAK,QAAQ,MAAM;AAAA,MAC3B,cAAc,KAAK;AAAA,MACnB,cAAc,KAAK;AAAA,MACnB,cAAc,KAAK;AAAA,MACnB,OACE,KAAK,UAAU,KAAK,OAAO,QACvB,KAAK,OAAO,MAAM,IAClB,KAAK,UAAU;AAAA,MACrB,aAAa,KAAK;AAAA,MAClB,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK,QAAQ,MAAM;AAAA,MAC3B,cAAc,KAAK;AAAA,MACnB,SAAS,KAAK,WAAW;AAAA,MACzB,gBAAgB,KAAK,kBAAkB;AAAA,MACvC,UAAU,KAAK,YAAY;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,UAAU,OAAO,KAAK,MAAM,MAAM,IAAI;AAAA,MACjD,KAAK,KAAK,OAAO;AAAA,MACjB,cAAc,KAAK,gBAAgB,EAAE,MAAM;AAAA,MAC3C,eAAe,KAAK,iBAAiB;AAAA,IACvC,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV,QAAI,SAAS,KAAK;AAClB,QAAI,CAAC,QAAQ;AACX,eAAS,KAAK;AACd,YAAM,OAAO,KAAK,QAAQ;AAC1B,UACE,KAAK,iBAAiB,cACtB,KAAK,iBAAiB,YACtB;AACA,YAAI,CAAC,MAAM;AACT,iBAAO;AAAA,QACT;AACA,iBAAS,KAAK,QAAQ,MAAM;AAC5B,YAAI,KAAK,iBAAiB,YAAY;AACpC,iBAAO,CAAC,KAAK,KAAK,CAAC;AAAA,QACrB;AACA,YAAI,KAAK,iBAAiB,YAAY;AACpC,iBAAO,CAAC,KAAK,KAAK,CAAC;AAAA,QACrB;AAAA,MACF;AAEA,UAAI,KAAK,iBAAiB,YAAY;AACpC,YAAI,CAAC,MAAM;AACT,iBAAO;AAAA,QACT;AACA,YAAI,WAAW,KAAK,SAAS;AAC3B,mBAAS,KAAK,QAAQ,MAAM;AAAA,QAC9B;AACA,YACE,KAAK,iBAAiB,eACtB,KAAK,iBAAiB,gBACtB;AACA,iBAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;AAAA,QACjC;AACA,YACE,KAAK,iBAAiB,iBACtB,KAAK,iBAAiB,gBACtB;AACA,iBAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;AAAA,QACjC;AAAA,MACF;AACA,WAAK,oBAAoB;AAAA,IAC3B;AACA,UAAM,eAAe,KAAK,gBAAgB;AAC1C,UAAM,QAAQ,KAAK,cAAc;AAGjC,WAAO;AAAA,MACL,OAAO,CAAC,IAAI,aAAa,CAAC,IAAI,MAAM,CAAC;AAAA,MACrC,OAAO,CAAC,IAAI,aAAa,CAAC,IAAI,MAAM,CAAC;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,QAAQ;AAChB,SAAK,UAAU;AACf,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,YAAY;AACnB,WAAO,KAAK,WAAW,SAAS,UAAU;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,YAAY;AACxB,WAAO,KAAK,WAAW,cAAc,UAAU;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,WAAO,KAAK,WAAW,QAAQ;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,WAAO,KAAK,WAAW,cAAc;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,WAAO,KAAK,WAAW,qBAAqB;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK;AAAA,IACd;AACA,QAAI,SAAS,KAAK;AAElB,QAAI,KAAK,iBAAiB,YAAY;AACpC,YAAM,OAAO,KAAK,QAAQ;AAC1B,YAAM,gBAAgB,KAAK,WAAW,QAAQ;AAC9C,UAAI,CAAC,QAAQ,CAAC,eAAe;AAC3B,eAAO;AAAA,MACT;AACA,eAAS,OAAO,MAAM;AACtB,UACE,KAAK,iBAAiB,eACtB,KAAK,iBAAiB,gBACtB;AACA,eAAO,CAAC,IAAI,cAAc,CAAC,IAAI,KAAK,CAAC,IAAI,OAAO,CAAC;AAAA,MACnD;AACA,UACE,KAAK,iBAAiB,iBACtB,KAAK,iBAAiB,gBACtB;AACA,eAAO,CAAC,IAAI,cAAc,CAAC,IAAI,KAAK,CAAC,IAAI,OAAO,CAAC;AAAA,MACnD;AAAA,IACF;AACA,SAAK,UAAU;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS;AACP,WAAO,KAAK,WAAW,OAAO;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,CAAC,KAAK,QAAQ,KAAK,WAAW,QAAQ,IAAI,KAAK;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,UAAM,QAAQ,KAAK,cAAc;AACjC,QAAI,KAAK,OAAO;AACd,aAAO,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC;AAAA,IAChC;AACA,QAAI,KAAK,WAAW,cAAc,KAAK,mBAAW,QAAQ;AACxD,aAAO,KAAK,WAAW,QAAQ,EAAE,CAAC,IAAI,MAAM,CAAC;AAAA,IAC/C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,UAAM,QAAQ,KAAK,cAAc;AACjC,QAAI,KAAK,OAAO;AACd,aAAO,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC;AAAA,IAChC;AACA,QAAI,KAAK,WAAW,cAAc,KAAK,mBAAW,QAAQ;AACxD,aAAO,KAAK,WAAW,QAAQ,EAAE,CAAC,IAAI,MAAM,CAAC;AAAA,IAC/C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO;AACd,WAAO,KAAK;AACZ,UAAM,SAAS,KAAK;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,UAAU;AAC1B,SAAK,WAAW,iBAAiB,kBAAU,QAAQ,QAAQ;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO;AACL,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,UAAU;AAC5B,SAAK,WAAW,oBAAoB,kBAAU,QAAQ,QAAQ;AAAA,EAChE;AACF;AAEA,IAAO,eAAQ;", "names": ["image"]}