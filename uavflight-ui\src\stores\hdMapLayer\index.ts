/*
 * @Author: AI Assistant
 * @Date: 2025-07-15
 * @Description: 高精度地图图层管理Store
 *
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved.
 */

import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import axios from 'axios';
import HDMapLayerManager from './mapLayerManager';
import { LayerConfig, StyleConfig, MapConfig } from './types';
import { ElMessage } from 'element-plus';
import { loadMapConfigFile, loadMapAndStyleConfig } from '/@/utils/mapConfig';

// 高精度地图图层管理Store
export const useHDMapLayerStore = defineStore('hdMapLayer', () => {
  // 图层管理器实例
  const layerManager = ref<HDMapLayerManager | null>(null);
  
  // 地图配置
  const mapConfig = ref<MapConfig | null>(null);
  const styleConfig = ref<StyleConfig | null>(null);
  
  // 图层列表（按主题分组）
  const layerGroups = ref<Array<{ name: string; layers: Array<any> }>>([]);
  
  // 主题列表
  const themesList = computed(() => {
    const themes = new Set<string>();
    if (mapConfig.value && mapConfig.value.layers) {
      mapConfig.value.layers.forEach(layer => {
        if (layer.theme) {
          themes.add(layer.theme);
        }
      });
    }
    return Array.from(themes);
  });

  // 加载地图配置
  const loadMapConfig = async (mapConfigFile = 'baseMap3', styleConfigFile = 'baseStyle3') => {
    try {
      console.log('正在加载高精度地图配置...');
      
      // 去掉文件名中的.json后缀（如果有）
      const mapConfigType = mapConfigFile.replace('.json', '') as 'baseMap3';
      const styleConfigType = styleConfigFile.replace('.json', '') as 'baseStyle3';
      
      // 使用新的工具函数加载配置
      const { mapConfig: mapConfigData, styleConfig: styleConfigData } = 
        await loadMapAndStyleConfig(mapConfigType, styleConfigType);
      
      // 更新状态
      mapConfig.value = mapConfigData;
      styleConfig.value = styleConfigData;
      
      // 创建图层管理器
      layerManager.value = new HDMapLayerManager(mapConfig.value, styleConfig.value);
      
      // 初始化图层组
      initializeLayerGroups();
      
      return { mapConfig: mapConfig.value, styleConfig: styleConfig.value };
    } catch (error) {
      console.error('加载高精度地图配置失败:', error);
      ElMessage.error('加载高精度地图配置失败');
      throw error;
    }
  };

  // 初始化图层组（按主题分组）
  const initializeLayerGroups = () => {
    const groups = new Map<string, Array<any>>();
    
    // 没有主题的图层归为"未分类"组
    const defaultGroupName = '未分类';
    
    // 按主题分组
    if (mapConfig.value && mapConfig.value.layers) {
      mapConfig.value.layers.forEach(layer => {
        // 为每个图层添加UI状态
        const enhancedLayer = {
          ...layer,
          visible: !!layer.initialLoad,
          opacity: 100,
          showOpacityControl: false,
          showStyleControl: false,
          style: { ...(layerManager.value?.getLayerStyle(layer) || {}) },
        };
        
        const groupName = layer.theme || defaultGroupName;
        
        if (!groups.has(groupName)) {
          groups.set(groupName, []);
        }
        
        groups.get(groupName)!.push(enhancedLayer);
      });
    }
    
    // 转换为数组格式
    layerGroups.value = Array.from(groups.entries()).map(([name, layers]) => ({
      name,
      layers,
    }));
  };

  // 切换图层组可见性
  const toggleGroupVisibility = (group: any, visible: boolean) => {
    group.layers.forEach((layer: any) => {
      toggleLayerVisibility(layer, visible);
    });
  };

  // 切换单个图层可见性
  const toggleLayerVisibility = (layer: any, visible: boolean) => {
    layer.visible = visible;
    
    if (layerManager.value) {
      if (visible) {
        layerManager.value.addLayer(layer);
      } else {
        layerManager.value.removeLayer(layer.id);
      }
    }
  };

  // 设置图层透明度
  const setLayerOpacity = (layer: any, opacity: number) => {
    layer.opacity = opacity;
    
    if (layerManager.value) {
      layerManager.value.setLayerOpacity(layer.id, opacity / 100);
    }
  };

  // 更新图层样式
  const updateLayerStyle = (layer: any) => {
    if (layerManager.value && layer.type === 'vector') {
      layerManager.value.updateLayerStyle(layer.id, layer.style);
    }
  };

  // 缩放到图层
  const zoomToLayer = (layer: any) => {
    if (layerManager.value) {
      layerManager.value.zoomToLayer(layer.id);
    }
  };

  // 切换图层标签显示
  const toggleLayerLabels = (layer: any) => {
    layer.showLabels = !layer.showLabels;
    
    if (layerManager.value && layer.labelField) {
      layerManager.value.toggleLayerLabels(layer.id, layer.showLabels, layer.labelField, layer.labelStyle);
    }
  };

  // 获取图层实例
  const getLayer = (layerId: string) => {
    if (layerManager.value) {
      return layerManager.value.getLayer(layerId);
    }
    return null;
  };

  // 刷新图层组状态
  const refreshLayerGroups = () => {
    // 遍历所有图层组和图层，检查实际加载状态并更新UI状态
    layerGroups.value.forEach(group => {
      group.layers.forEach(layer => {
        // 检查图层是否实际加载
        const layerInstance = layerManager.value?.getLayer(layer.id);
        // 如果图层实例存在但UI状态为未加载，则更新UI状态
        if (layerInstance && !layer.visible) {
          layer.visible = true;
        }
        // 如果图层实例不存在但UI状态为已加载，则更新UI状态
        else if (!layerInstance && layer.visible) {
          layer.visible = false;
        }
      });
    });
  };

  return {
    mapConfig,
    styleConfig,
    layerGroups,
    themesList,
    layerManager,
    loadMapConfig,
    toggleGroupVisibility,
    toggleLayerVisibility,
    setLayerOpacity,
    updateLayerStyle,
    zoomToLayer,
    toggleLayerLabels,
    getLayer,
    refreshLayerGroups,
  };
}); 