/* 引入样式变量 */
@import "../../theme/mixins/index.scss";

/* 图层管理器容器 */
.layer-manager-container {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: rgba(15, 21, 32, 0.5); /* 50%透明度 */
  /* 移除毛玻璃效果 */
  color: #fff;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  /* 标题栏 */
  .layer-manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    
    .layer-manager-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
  }

  /* 内容区域 */
  .panel-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    
    /* 搜索容器 */
    .search-container {
      padding: 8px;
      // background-color: rgba(15, 21, 32, 0.8);
      z-index: 2;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      position: sticky;
      top: 0;
}

    /* 可滚动内容区域 */
    .scrollable-content {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 8px;
      
      /* 自定义滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: rgba(64, 158, 255, 0.5);
      }

      &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
      }
      
      /* 空图层提示 */
      .empty-layer-message {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100px;
        color: rgba(255, 255, 255, 0.5);
        font-size: 14px;
      }
      
      /* 确保树组件在滚动区域内正确显示 */
      .el-tree {
        background-color: transparent;
        height: 100%;
      }
    }
  }
  
  /* 树状图自定义样式 */
  :deep(.el-tree) {
    background-color: transparent;
    color: #fff;
    
    .el-tree-node {
      color: #fff;
      
      &:hover > .el-tree-node__content {
        background-color: rgba(255, 255, 255, 0.1);
        color: #fff;
      }
      
      .el-tree-node__content {
        height: auto;
        min-height: 32px;
          color: #fff;
        cursor: pointer; /* 确保鼠标指针为手型，整个节点可点击 */
        
        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
          color: #fff;
        }
      }
      
      &.is-expanded {
        > .el-tree-node__content {
          background-color: rgba(64, 158, 255, 0.1);
          color: #fff;
          
          &:hover {
            background-color: rgba(64, 158, 255, 0.2);
          }
        }
  }
  
      &.is-current > .el-tree-node__content {
        background-color: rgba(64, 158, 255, 0.2);
        color: #fff;
  }
}

    /* 树节点缩进 */
    .el-tree-node__children .el-tree-node__content {
      padding-left: 8px;
    }
    
    /* 树节点展开图标 */
    .el-tree-node__expand-icon {
      color: rgba(255, 255, 255, 0.7);
      
      &.is-leaf {
        color: transparent;
      }
    }
}

/* 节点内容样式 */
.custom-tree-node {
    color: #fff;
  width: 100%;
  display: flex;
  align-items: center;
    padding: 6px 4px;
  
  .node-content {
      flex: 1;
    display: flex;
    align-items: center;
    overflow: hidden;
      max-width: calc(100% - 75px);
    
      /* 图层名称 */
    .layer-name {
      margin-left: 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
        max-width: 130px;
        color: #fff; /* 确保文字颜色为白色 */
        text-shadow: 0 0 2px rgba(0, 0, 0, 0.5); /* 添加文字阴影增强可读性 */
    }
    
      /* 图层类型标签 */
    .layer-type {
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 10px;
      background: rgba(255, 255, 255, 0.1);
        margin-left: 6px;
        color: #fff; /* 确保文字颜色为白色 */
    }
    
      /* 图层主题标签 */
    .layer-theme {
        font-size: 10px;
      padding: 2px 6px;
      border-radius: 10px;
        background: rgba(82, 196, 26, 0.1);
        color: #52c41a;
        margin-left: 6px;
      }

      /* 主题节点样式 */
      &.theme-node {
        font-weight: bold;
        .layer-name {
          color: #8cc5ff;
          text-shadow: 0 0 2px rgba(0, 0, 0, 0.7);
    }
  }
}

.node-actions {
  display: flex;
      gap: 4px;
      
  /* 改进缩放至图层按钮样式 */
  .el-button--text {
    color: #409EFF; /* 明亮的蓝色，提高可见度 */
    background-color: rgba(255, 255, 255, 0.15); /* 添加半透明白色背景 */
    border-radius: 3px;
    padding: 2px 4px;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.25);
      color: #79bbff;
    }
    
    .el-icon {
      font-size: 14px;
      filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5)); /* 添加阴影效果增强可见性 */
    }
  }
    }
  }

  /* 复选框样式 */
  :deep(.el-checkbox) {
    margin-right: 4px;
    
    .el-checkbox__input {
      .el-checkbox__inner {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
      }
      
      &.is-checked {
        .el-checkbox__inner {
          background-color: #409EFF;
          border-color: #409EFF;
        }
        
        /* 确保勾选状态下文本保持白色 */
        + .el-checkbox__label {
          color: #fff !important;
        }
      }
      
      &.is-indeterminate {
        .el-checkbox__inner {
          background-color: #409EFF;
          border-color: #409EFF;
        }
        
        /* 确保半选状态下文本保持白色 */
        + .el-checkbox__label {
          color: #fff !important;
        }
      }
    }
  }

  /* 主题节点样式 */
  .theme-node {
    cursor: pointer;
    width: 100%;
    padding: 2px 0;
  }

  .theme-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
    margin-left: 4px;
    width: 8px;
    height: 8px;
    position: relative;
    
    &:before {
      content: '';
      position: absolute;
      width: 6px;
      height: 6px;
      background-color: #E6A23C;
      border-radius: 50%;
    }
  }
  
  .theme-name {
    font-weight: bold;
    margin-left: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
    color: #fff; /* 确保文字颜色为白色 */
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.5); /* 添加文字阴影增强可读性 */
  }
  
  /* 图层类型指示器 */
  .layer-type-indicator {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* 矢量图层样式预览 */
  .style-preview {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    border-radius: 3px;
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    
    &:hover {
      outline: 1px solid rgba(255, 255, 255, 0.5);
  }
    
    /* 点预览 */
.point-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .point-inner {
    width: 8px;
    height: 8px;
    border-radius: 50%;
        background-color: rgba(51, 136, 255, 0.6);
        border: 1px solid #3388ff;
  }
}

    /* 线预览 */
.line-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  
  .line-inner {
    width: 100%;
        border-top: 2px solid #3388ff;
  }
}

    /* 面预览 */
.polygon-preview {
  width: 100%;
  height: 100%;
  
  .polygon-inner {
    width: 100%;
    height: 100%;
        background-color: rgba(51, 136, 255, 0.3);
        border: 1px solid #3388ff;
  }
}
  }
  
  /* 栅格图层指示器 */
.raster-indicator {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    
    .raster-icon {
      width: 12px;
      height: 12px;
    position: relative;
    overflow: hidden;
    
    .raster-grid {
      width: 100%;
      height: 100%;
        background-image: linear-gradient(to right, rgba(255,255,255,0.5) 1px, transparent 1px),
                         linear-gradient(to bottom, rgba(255,255,255,0.5) 1px, transparent 1px);
        background-size: 4px 4px;
    }
  }
}

  /* 右侧收缩/展开控件 */
  .layer-collapse-bar {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    width: 16px;
    height: 40px;
    background-color: rgba(64, 158, 255, 0.2);
    border-radius: 4px 0 0 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
    
    &:hover {
      background-color: rgba(64, 158, 255, 0.4);
    }
    
    .collapse-icon {
      color: #fff;
    }
  }
  
  /* 不活跃的图层按钮样式 */
  .inactive-layer {
    opacity: 0.5;
  }

  /* 右键菜单 */
  .context-menu {
    position: absolute;
    z-index: 100;
    background-color: rgba(36, 37, 46, 0.9);
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
    padding: 5px 0;
    min-width: 150px;
  
    .menu-items {
      .menu-item {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        cursor: pointer;
        user-select: none;
    
    &:hover {
          background-color: rgba(64, 158, 255, 0.2);
  }
  
        .el-icon {
          margin-right: 8px;
          font-size: 16px;
        }
        
        span {
          font-size: 14px;
  }
}
    }
  }
  
  /* 样式编辑器 */
  .style-editor-content {
    padding: 10px;
    
    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      color: #333;
      font-size: 16px;
    }
    
    .el-form-item {
      margin-bottom: 20px;
    }
    
    /* 线条样式选择器 */
    .custom-select {
      display: flex;
      gap: 10px;
      margin-top: 5px;
      
      .select-item {
        cursor: pointer;
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #dcdfe6;
        width: 60px;
        text-align: center;
        
        &.active {
          background-color: #ecf5ff;
          border-color: #409eff;
        }
        
        .line-preview {
          width: 100%;
          height: 0;
          margin-bottom: 5px;
          
          &.solid {
            border-top: 2px solid #333;
  }
  
          &.dashed {
            border-top: 2px dashed #333;
  }
          
          &.dotted {
            border-top: 2px dotted #333;
          }
        }
        
        span {
          font-size: 12px;
          color: #606266;
        }
      }
    }

    /* 样式预览框 */
    .style-preview-container {
      margin-top: 20px;
      
      h4 {
        margin-top: 0;
        margin-bottom: 10px;
        color: #333;
        font-size: 14px;
}

      .preview-box {
        height: 60px;
  display: flex;
  align-items: center;
        justify-content: center;
        background-color: #f5f7fa;
        border-radius: 4px;
        
        /* 点预览大样式 */
        .point-preview-large {
          .point-inner {
            border-radius: 50%;
  }
}

        /* 线预览大样式 */
        .line-preview-large {
          width: 80%;
  
          .line-inner {
            width: 100%;
  }
} 

        /* 面预览大样式 */
        .polygon-preview-large {
          width: 60px;
          height: 40px;
          
          .polygon-inner {
            width: 100%;
            height: 100%;
            border-radius: 4px;
  }
}
      }
    }
  }
  
  /* 对话框底部按钮 */
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
}

  /* 标注编辑器 */
  .label-editor-content {
    padding: 10px;
    
    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      color: #333;
      font-size: 16px;
  }
    
    .el-form-item {
      margin-bottom: 20px;
    }
  }
} 