define(["./defaultValue-0a909f67","./Matrix3-a348023f","./ArcType-ce2e50ab","./Transforms-01e95659","./ComponentDatatype-77274976","./EllipsoidTangentPlane-6308603a","./GeometryAttribute-f5d71750","./GeometryAttributes-f06a2792","./GeometryInstance-99908f4f","./GeometryOffsetAttribute-04332ce7","./GeometryPipeline-049a5b67","./IndexDatatype-2149f06c","./Math-e97915da","./PolygonGeometryLibrary-364ec499","./PolygonPipeline-f5f5011c","./Matrix2-7146c9ca","./RuntimeError-06c93819","./combine-ca22a614","./WebGLConstants-a8cc3e8c","./AxisAlignedBoundingBox-65ccb1a5","./IntersectionTests-0bb04fde","./Plane-8575e17c","./AttributeCompression-50c9aeba","./EncodedCartesian3-0fb84db0","./arrayRemoveDuplicates-e9673044","./EllipsoidRhumbLine-9b24aab2"],(function(e,t,i,o,r,n,a,s,l,y,u,p,c,d,f,g,m,h,b,P,E,A,_,G,L,T){"use strict";const H=[],v=[];function C(e,t,o,y,u){const c=n.EllipsoidTangentPlane.fromPoints(t,e).projectPointsOntoPlane(t,H);let g,m;f.PolygonPipeline.computeWindingOrder2D(c)===f.WindingOrder.CLOCKWISE&&(c.reverse(),t=t.slice().reverse());let h=t.length,b=0;if(y)for(g=new Float64Array(2*h*3),m=0;m<h;m++){const e=t[m],i=t[(m+1)%h];g[b++]=e.x,g[b++]=e.y,g[b++]=e.z,g[b++]=i.x,g[b++]=i.y,g[b++]=i.z}else{let r=0;if(u===i.ArcType.GEODESIC)for(m=0;m<h;m++)r+=d.PolygonGeometryLibrary.subdivideLineCount(t[m],t[(m+1)%h],o);else if(u===i.ArcType.RHUMB)for(m=0;m<h;m++)r+=d.PolygonGeometryLibrary.subdivideRhumbLineCount(e,t[m],t[(m+1)%h],o);for(g=new Float64Array(3*r),m=0;m<h;m++){let r;u===i.ArcType.GEODESIC?r=d.PolygonGeometryLibrary.subdivideLine(t[m],t[(m+1)%h],o,v):u===i.ArcType.RHUMB&&(r=d.PolygonGeometryLibrary.subdivideRhumbLine(e,t[m],t[(m+1)%h],o,v));const n=r.length;for(let e=0;e<n;++e)g[b++]=r[e]}}h=g.length/3;const P=2*h,E=p.IndexDatatype.createTypedArray(h,P);for(b=0,m=0;m<h-1;m++)E[b++]=m,E[b++]=m+1;return E[b++]=h-1,E[b++]=0,new l.GeometryInstance({geometry:new a.Geometry({attributes:new s.GeometryAttributes({position:new a.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:g})}),indices:E,primitiveType:a.PrimitiveType.LINES})})}function x(e,t,o,y,u){const c=n.EllipsoidTangentPlane.fromPoints(t,e).projectPointsOntoPlane(t,H);let g,m;f.PolygonPipeline.computeWindingOrder2D(c)===f.WindingOrder.CLOCKWISE&&(c.reverse(),t=t.slice().reverse());let h=t.length;const b=new Array(h);let P=0;if(y)for(g=new Float64Array(2*h*3*2),m=0;m<h;++m){b[m]=P/3;const e=t[m],i=t[(m+1)%h];g[P++]=e.x,g[P++]=e.y,g[P++]=e.z,g[P++]=i.x,g[P++]=i.y,g[P++]=i.z}else{let r=0;if(u===i.ArcType.GEODESIC)for(m=0;m<h;m++)r+=d.PolygonGeometryLibrary.subdivideLineCount(t[m],t[(m+1)%h],o);else if(u===i.ArcType.RHUMB)for(m=0;m<h;m++)r+=d.PolygonGeometryLibrary.subdivideRhumbLineCount(e,t[m],t[(m+1)%h],o);for(g=new Float64Array(3*r*2),m=0;m<h;++m){let r;b[m]=P/3,u===i.ArcType.GEODESIC?r=d.PolygonGeometryLibrary.subdivideLine(t[m],t[(m+1)%h],o,v):u===i.ArcType.RHUMB&&(r=d.PolygonGeometryLibrary.subdivideRhumbLine(e,t[m],t[(m+1)%h],o,v));const n=r.length;for(let e=0;e<n;++e)g[P++]=r[e]}}h=g.length/6;const E=b.length,A=2*(2*h+E),_=p.IndexDatatype.createTypedArray(h+E,A);for(P=0,m=0;m<h;++m)_[P++]=m,_[P++]=(m+1)%h,_[P++]=m+h,_[P++]=(m+1)%h+h;for(m=0;m<E;m++){const e=b[m];_[P++]=e,_[P++]=e+h}return new l.GeometryInstance({geometry:new a.Geometry({attributes:new s.GeometryAttributes({position:new a.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:g})}),indices:_,primitiveType:a.PrimitiveType.LINES})})}function O(o){const r=o.polygonHierarchy,n=e.defaultValue(o.ellipsoid,t.Ellipsoid.WGS84),a=e.defaultValue(o.granularity,c.CesiumMath.RADIANS_PER_DEGREE),s=e.defaultValue(o.perPositionHeight,!1),l=s&&e.defined(o.extrudedHeight),y=e.defaultValue(o.arcType,i.ArcType.GEODESIC);let u=e.defaultValue(o.height,0),p=e.defaultValue(o.extrudedHeight,u);if(!l){const e=Math.max(u,p);p=Math.min(u,p),u=e}this._ellipsoid=t.Ellipsoid.clone(n),this._granularity=a,this._height=u,this._extrudedHeight=p,this._arcType=y,this._polygonHierarchy=r,this._perPositionHeight=s,this._perPositionHeightExtrude=l,this._offsetAttribute=o.offsetAttribute,this._workerName="createPolygonOutlineGeometry",this.packedLength=d.PolygonGeometryLibrary.computeHierarchyPackedLength(r,t.Cartesian3)+t.Ellipsoid.packedLength+8}O.pack=function(i,o,r){return r=e.defaultValue(r,0),r=d.PolygonGeometryLibrary.packPolygonHierarchy(i._polygonHierarchy,o,r,t.Cartesian3),t.Ellipsoid.pack(i._ellipsoid,o,r),r+=t.Ellipsoid.packedLength,o[r++]=i._height,o[r++]=i._extrudedHeight,o[r++]=i._granularity,o[r++]=i._perPositionHeightExtrude?1:0,o[r++]=i._perPositionHeight?1:0,o[r++]=i._arcType,o[r++]=e.defaultValue(i._offsetAttribute,-1),o[r]=i.packedLength,o};const D=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),I={polygonHierarchy:{}};return O.unpack=function(i,o,r){o=e.defaultValue(o,0);const n=d.PolygonGeometryLibrary.unpackPolygonHierarchy(i,o,t.Cartesian3);o=n.startingIndex,delete n.startingIndex;const a=t.Ellipsoid.unpack(i,o,D);o+=t.Ellipsoid.packedLength;const s=i[o++],l=i[o++],y=i[o++],u=1===i[o++],p=1===i[o++],c=i[o++],f=i[o++],g=i[o];return e.defined(r)||(r=new O(I)),r._polygonHierarchy=n,r._ellipsoid=t.Ellipsoid.clone(a,r._ellipsoid),r._height=s,r._extrudedHeight=l,r._granularity=y,r._perPositionHeight=p,r._perPositionHeightExtrude=u,r._arcType=c,r._offsetAttribute=-1===f?void 0:f,r.packedLength=g,r},O.fromPositions=function(t){return new O({polygonHierarchy:{positions:(t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT)).positions},height:t.height,extrudedHeight:t.extrudedHeight,ellipsoid:t.ellipsoid,granularity:t.granularity,perPositionHeight:t.perPositionHeight,arcType:t.arcType,offsetAttribute:t.offsetAttribute})},O.createGeometry=function(t){const i=t._ellipsoid,n=t._granularity,s=t._polygonHierarchy,l=t._perPositionHeight,p=t._arcType,g=d.PolygonGeometryLibrary.polygonOutlinesFromHierarchy(s,!l,i);if(0===g.length)return;let m;const h=[],b=c.CesiumMath.chordLength(n,i.maximumRadius),P=t._height,E=t._extrudedHeight;let A,_;if(t._perPositionHeightExtrude||!c.CesiumMath.equalsEpsilon(P,E,0,c.CesiumMath.EPSILON2))for(_=0;_<g.length;_++){if(m=x(i,g[_],b,l,p),m.geometry=d.PolygonGeometryLibrary.scaleToGeodeticHeightExtruded(m.geometry,P,E,i,l),e.defined(t._offsetAttribute)){const e=m.geometry.attributes.position.values.length/3;let i=new Uint8Array(e);t._offsetAttribute===y.GeometryOffsetAttribute.TOP?i=i.fill(1,0,e/2):(A=t._offsetAttribute===y.GeometryOffsetAttribute.NONE?0:1,i=i.fill(A)),m.geometry.attributes.applyOffset=new a.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:i})}h.push(m)}else for(_=0;_<g.length;_++){if(m=C(i,g[_],b,l,p),m.geometry.attributes.position.values=f.PolygonPipeline.scaleToGeodeticHeight(m.geometry.attributes.position.values,P,i,!l),e.defined(t._offsetAttribute)){const e=m.geometry.attributes.position.values.length;A=t._offsetAttribute===y.GeometryOffsetAttribute.NONE?0:1;const i=new Uint8Array(e/3).fill(A);m.geometry.attributes.applyOffset=new a.GeometryAttribute({componentDatatype:r.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:i})}h.push(m)}const G=u.GeometryPipeline.combineInstances(h)[0],L=o.BoundingSphere.fromVertices(G.attributes.position.values);return new a.Geometry({attributes:G.attributes,indices:G.indices,primitiveType:G.primitiveType,boundingSphere:L,offsetAttribute:t._offsetAttribute})},function(i,o){return e.defined(o)&&(i=O.unpack(i,o)),i._ellipsoid=t.Ellipsoid.clone(i._ellipsoid),O.createGeometry(i)}}));
