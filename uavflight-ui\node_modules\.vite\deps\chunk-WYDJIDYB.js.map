{"version": 3, "sources": ["../../ol/proj/Units.js", "../../ol/proj/Projection.js", "../../ol/proj/epsg3857.js", "../../ol/proj/epsg4326.js", "../../ol/proj/projections.js", "../../ol/proj/transforms.js", "../../ol/string.js", "../../ol/coordinate.js", "../../ol/console.js", "../../ol/proj.js"], "sourcesContent": ["/**\n * @module ol/proj/Units\n */\n\n/**\n * @typedef {'radians' | 'degrees' | 'ft' | 'm' | 'pixels' | 'tile-pixels' | 'us-ft'} Units\n * Projection units.\n */\n\n/**\n * See http://duff.ess.washington.edu/data/raster/drg/docs/geotiff.txt\n * @type {Object<number, Units>}\n */\nconst unitByCode = {\n  '9001': 'm',\n  '9002': 'ft',\n  '9003': 'us-ft',\n  '9101': 'radians',\n  '9102': 'degrees',\n};\n\n/**\n * @param {number} code Unit code.\n * @return {Units} Units.\n */\nexport function fromCode(code) {\n  return unitByCode[code];\n}\n\n/**\n * @typedef {Object} MetersPerUnitLookup\n * @property {number} radians Radians\n * @property {number} degrees Degrees\n * @property {number} ft  Feet\n * @property {number} m Meters\n * @property {number} us-ft US feet\n */\n\n/**\n * Meters per unit lookup table.\n * @const\n * @type {MetersPerUnitLookup}\n * @api\n */\nexport const METERS_PER_UNIT = {\n  // use the radius of the Normal sphere\n  'radians': 6370997 / (2 * Math.PI),\n  'degrees': (2 * Math.PI * 6370997) / 360,\n  'ft': 0.3048,\n  'm': 1,\n  'us-ft': 1200 / 3937,\n};\n", "/**\n * @module ol/proj/Projection\n */\nimport {METERS_PER_UNIT} from './Units.js';\n\n/**\n * @typedef {Object} Options\n * @property {string} code The SRS identifier code, e.g. `EPSG:4326`.\n * @property {import(\"./Units.js\").Units} [units] Units. Required unless a\n * proj4 projection is defined for `code`.\n * @property {import(\"../extent.js\").Extent} [extent] The validity extent for the SRS.\n * @property {string} [axisOrientation='enu'] The axis orientation as specified in Proj4.\n * @property {boolean} [global=false] Whether the projection is valid for the whole globe.\n * @property {number} [metersPerUnit] The meters per unit for the SRS.\n * If not provided, the `units` are used to get the meters per unit from the {@link METERS_PER_UNIT}\n * lookup table.\n * @property {import(\"../extent.js\").Extent} [worldExtent] The world extent for the SRS.\n * @property {function(number, import(\"../coordinate.js\").Coordinate):number} [getPointResolution]\n * Function to determine resolution at a point. The function is called with a\n * `number` view resolution and a {@link module:ol/coordinate~Coordinate} as arguments, and returns\n * the `number` resolution in projection units at the passed coordinate. If this is `undefined`,\n * the default {@link module:ol/proj.getPointResolution} function will be used.\n */\n\n/**\n * @classdesc\n * Projection definition class. One of these is created for each projection\n * supported in the application and stored in the {@link module:ol/proj} namespace.\n * You can use these in applications, but this is not required, as API params\n * and options use {@link module:ol/proj~ProjectionLike} which means the simple string\n * code will suffice.\n *\n * You can use {@link module:ol/proj.get} to retrieve the object for a particular\n * projection.\n *\n * The library includes definitions for `EPSG:4326` and `EPSG:3857`, together\n * with the following aliases:\n * * `EPSG:4326`: CRS:84, urn:ogc:def:crs:EPSG:6.6:4326,\n *     urn:ogc:def:crs:OGC:1.3:CRS84, urn:ogc:def:crs:OGC:2:84,\n *     http://www.opengis.net/gml/srs/epsg.xml#4326,\n *     urn:x-ogc:def:crs:EPSG:4326\n * * `EPSG:3857`: EPSG:102100, EPSG:102113, EPSG:900913,\n *     urn:ogc:def:crs:EPSG:6.18:3:3857,\n *     http://www.opengis.net/gml/srs/epsg.xml#3857\n *\n * If you use [proj4js](https://github.com/proj4js/proj4js), aliases can\n * be added using `proj4.defs()`. After all required projection definitions are\n * added, call the {@link module:ol/proj/proj4.register} function.\n *\n * @api\n */\nclass Projection {\n  /**\n   * @param {Options} options Projection options.\n   */\n  constructor(options) {\n    /**\n     * @private\n     * @type {string}\n     */\n    this.code_ = options.code;\n\n    /**\n     * Units of projected coordinates. When set to `TILE_PIXELS`, a\n     * `this.extent_` and `this.worldExtent_` must be configured properly for each\n     * tile.\n     * @private\n     * @type {import(\"./Units.js\").Units}\n     */\n    this.units_ = /** @type {import(\"./Units.js\").Units} */ (options.units);\n\n    /**\n     * Validity extent of the projection in projected coordinates. For projections\n     * with `TILE_PIXELS` units, this is the extent of the tile in\n     * tile pixel space.\n     * @private\n     * @type {import(\"../extent.js\").Extent}\n     */\n    this.extent_ = options.extent !== undefined ? options.extent : null;\n\n    /**\n     * Extent of the world in EPSG:4326. For projections with\n     * `TILE_PIXELS` units, this is the extent of the tile in\n     * projected coordinate space.\n     * @private\n     * @type {import(\"../extent.js\").Extent}\n     */\n    this.worldExtent_ =\n      options.worldExtent !== undefined ? options.worldExtent : null;\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.axisOrientation_ =\n      options.axisOrientation !== undefined ? options.axisOrientation : 'enu';\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.global_ = options.global !== undefined ? options.global : false;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.canWrapX_ = !!(this.global_ && this.extent_);\n\n    /**\n     * @private\n     * @type {function(number, import(\"../coordinate.js\").Coordinate):number|undefined}\n     */\n    this.getPointResolutionFunc_ = options.getPointResolution;\n\n    /**\n     * @private\n     * @type {import(\"../tilegrid/TileGrid.js\").default}\n     */\n    this.defaultTileGrid_ = null;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.metersPerUnit_ = options.metersPerUnit;\n  }\n\n  /**\n   * @return {boolean} The projection is suitable for wrapping the x-axis\n   */\n  canWrapX() {\n    return this.canWrapX_;\n  }\n\n  /**\n   * Get the code for this projection, e.g. 'EPSG:4326'.\n   * @return {string} Code.\n   * @api\n   */\n  getCode() {\n    return this.code_;\n  }\n\n  /**\n   * Get the validity extent for this projection.\n   * @return {import(\"../extent.js\").Extent} Extent.\n   * @api\n   */\n  getExtent() {\n    return this.extent_;\n  }\n\n  /**\n   * Get the units of this projection.\n   * @return {import(\"./Units.js\").Units} Units.\n   * @api\n   */\n  getUnits() {\n    return this.units_;\n  }\n\n  /**\n   * Get the amount of meters per unit of this projection.  If the projection is\n   * not configured with `metersPerUnit` or a units identifier, the return is\n   * `undefined`.\n   * @return {number|undefined} Meters.\n   * @api\n   */\n  getMetersPerUnit() {\n    return this.metersPerUnit_ || METERS_PER_UNIT[this.units_];\n  }\n\n  /**\n   * Get the world extent for this projection.\n   * @return {import(\"../extent.js\").Extent} Extent.\n   * @api\n   */\n  getWorldExtent() {\n    return this.worldExtent_;\n  }\n\n  /**\n   * Get the axis orientation of this projection.\n   * Example values are:\n   * enu - the default easting, northing, elevation.\n   * neu - northing, easting, up - useful for \"lat/long\" geographic coordinates,\n   *     or south orientated transverse mercator.\n   * wnu - westing, northing, up - some planetary coordinate systems have\n   *     \"west positive\" coordinate systems\n   * @return {string} Axis orientation.\n   * @api\n   */\n  getAxisOrientation() {\n    return this.axisOrientation_;\n  }\n\n  /**\n   * Is this projection a global projection which spans the whole world?\n   * @return {boolean} Whether the projection is global.\n   * @api\n   */\n  isGlobal() {\n    return this.global_;\n  }\n\n  /**\n   * Set if the projection is a global projection which spans the whole world\n   * @param {boolean} global Whether the projection is global.\n   * @api\n   */\n  setGlobal(global) {\n    this.global_ = global;\n    this.canWrapX_ = !!(global && this.extent_);\n  }\n\n  /**\n   * @return {import(\"../tilegrid/TileGrid.js\").default} The default tile grid.\n   */\n  getDefaultTileGrid() {\n    return this.defaultTileGrid_;\n  }\n\n  /**\n   * @param {import(\"../tilegrid/TileGrid.js\").default} tileGrid The default tile grid.\n   */\n  setDefaultTileGrid(tileGrid) {\n    this.defaultTileGrid_ = tileGrid;\n  }\n\n  /**\n   * Set the validity extent for this projection.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @api\n   */\n  setExtent(extent) {\n    this.extent_ = extent;\n    this.canWrapX_ = !!(this.global_ && extent);\n  }\n\n  /**\n   * Set the world extent for this projection.\n   * @param {import(\"../extent.js\").Extent} worldExtent World extent\n   *     [minlon, minlat, maxlon, maxlat].\n   * @api\n   */\n  setWorldExtent(worldExtent) {\n    this.worldExtent_ = worldExtent;\n  }\n\n  /**\n   * Set the getPointResolution function (see {@link module:ol/proj.getPointResolution}\n   * for this projection.\n   * @param {function(number, import(\"../coordinate.js\").Coordinate):number} func Function\n   * @api\n   */\n  setGetPointResolution(func) {\n    this.getPointResolutionFunc_ = func;\n  }\n\n  /**\n   * Get the custom point resolution function for this projection (if set).\n   * @return {function(number, import(\"../coordinate.js\").Coordinate):number|undefined} The custom point\n   * resolution function (if set).\n   */\n  getPointResolutionFunc() {\n    return this.getPointResolutionFunc_;\n  }\n}\n\nexport default Projection;\n", "/**\n * @module ol/proj/epsg3857\n */\nimport Projection from './Projection.js';\n\n/**\n * Radius of WGS84 sphere\n *\n * @const\n * @type {number}\n */\nexport const RADIUS = 6378137;\n\n/**\n * @const\n * @type {number}\n */\nexport const HALF_SIZE = Math.PI * RADIUS;\n\n/**\n * @const\n * @type {import(\"../extent.js\").Extent}\n */\nexport const EXTENT = [-HALF_SIZE, -HALF_SIZE, HALF_SIZE, HALF_SIZE];\n\n/**\n * @const\n * @type {import(\"../extent.js\").Extent}\n */\nexport const WORLD_EXTENT = [-180, -85, 180, 85];\n\n/**\n * Maximum safe value in y direction\n * @const\n * @type {number}\n */\nexport const MAX_SAFE_Y = RADIUS * Math.log(Math.tan(Math.PI / 2));\n\n/**\n * @classdesc\n * Projection object for web/spherical Mercator (EPSG:3857).\n */\nclass EPSG3857Projection extends Projection {\n  /**\n   * @param {string} code Code.\n   */\n  constructor(code) {\n    super({\n      code: code,\n      units: 'm',\n      extent: EXTENT,\n      global: true,\n      worldExtent: WORLD_EXTENT,\n      getPointResolution: function (resolution, point) {\n        return resolution / Math.cosh(point[1] / RADIUS);\n      },\n    });\n  }\n}\n\n/**\n * Projections equal to EPSG:3857.\n *\n * @const\n * @type {Array<import(\"./Projection.js\").default>}\n */\nexport const PROJECTIONS = [\n  new EPSG3857Projection('EPSG:3857'),\n  new EPSG3857Projection('EPSG:102100'),\n  new EPSG3857Projection('EPSG:102113'),\n  new EPSG3857Projection('EPSG:900913'),\n  new EPSG3857Projection('http://www.opengis.net/def/crs/EPSG/0/3857'),\n  new EPSG3857Projection('http://www.opengis.net/gml/srs/epsg.xml#3857'),\n];\n\n/**\n * Transformation from EPSG:4326 to EPSG:3857.\n *\n * @param {Array<number>} input Input array of coordinate values.\n * @param {Array<number>} [output] Output array of coordinate values.\n * @param {number} [dimension] Dimension (default is `2`).\n * @return {Array<number>} Output array of coordinate values.\n */\nexport function fromEPSG4326(input, output, dimension) {\n  const length = input.length;\n  dimension = dimension > 1 ? dimension : 2;\n  if (output === undefined) {\n    if (dimension > 2) {\n      // preserve values beyond second dimension\n      output = input.slice();\n    } else {\n      output = new Array(length);\n    }\n  }\n  for (let i = 0; i < length; i += dimension) {\n    output[i] = (HALF_SIZE * input[i]) / 180;\n    let y = RADIUS * Math.log(Math.tan((Math.PI * (+input[i + 1] + 90)) / 360));\n    if (y > MAX_SAFE_Y) {\n      y = MAX_SAFE_Y;\n    } else if (y < -MAX_SAFE_Y) {\n      y = -MAX_SAFE_Y;\n    }\n    output[i + 1] = y;\n  }\n  return output;\n}\n\n/**\n * Transformation from EPSG:3857 to EPSG:4326.\n *\n * @param {Array<number>} input Input array of coordinate values.\n * @param {Array<number>} [output] Output array of coordinate values.\n * @param {number} [dimension] Dimension (default is `2`).\n * @return {Array<number>} Output array of coordinate values.\n */\nexport function toEPSG4326(input, output, dimension) {\n  const length = input.length;\n  dimension = dimension > 1 ? dimension : 2;\n  if (output === undefined) {\n    if (dimension > 2) {\n      // preserve values beyond second dimension\n      output = input.slice();\n    } else {\n      output = new Array(length);\n    }\n  }\n  for (let i = 0; i < length; i += dimension) {\n    output[i] = (180 * input[i]) / HALF_SIZE;\n    output[i + 1] =\n      (360 * Math.atan(Math.exp(input[i + 1] / RADIUS))) / Math.PI - 90;\n  }\n  return output;\n}\n", "/**\n * @module ol/proj/epsg4326\n */\nimport Projection from './Projection.js';\n\n/**\n * Semi-major radius of the WGS84 ellipsoid.\n *\n * @const\n * @type {number}\n */\nexport const RADIUS = 6378137;\n\n/**\n * Extent of the EPSG:4326 projection which is the whole world.\n *\n * @const\n * @type {import(\"../extent.js\").Extent}\n */\nexport const EXTENT = [-180, -90, 180, 90];\n\n/**\n * @const\n * @type {number}\n */\nexport const METERS_PER_UNIT = (Math.PI * RADIUS) / 180;\n\n/**\n * @classdesc\n * Projection object for WGS84 geographic coordinates (EPSG:4326).\n *\n * Note that OpenLayers does not strictly comply with the EPSG definition.\n * The EPSG registry defines 4326 as a CRS for Latitude,Longitude (y,x).\n * OpenLayers treats EPSG:4326 as a pseudo-projection, with x,y coordinates.\n */\nclass EPSG4326Projection extends Projection {\n  /**\n   * @param {string} code Code.\n   * @param {string} [axisOrientation] Axis orientation.\n   */\n  constructor(code, axisOrientation) {\n    super({\n      code: code,\n      units: 'degrees',\n      extent: EXTENT,\n      axisOrientation: axisOrientation,\n      global: true,\n      metersPerUnit: METERS_PER_UNIT,\n      worldExtent: EXTENT,\n    });\n  }\n}\n\n/**\n * Projections equal to EPSG:4326.\n *\n * @const\n * @type {Array<import(\"./Projection.js\").default>}\n */\nexport const PROJECTIONS = [\n  new EPSG4326Projection('CRS:84'),\n  new EPSG4326Projection('EPSG:4326', 'neu'),\n  new EPSG4326Projection('urn:ogc:def:crs:OGC:1.3:CRS84'),\n  new EPSG4326Projection('urn:ogc:def:crs:OGC:2:84'),\n  new EPSG4326Projection('http://www.opengis.net/def/crs/OGC/1.3/CRS84'),\n  new EPSG4326Projection('http://www.opengis.net/gml/srs/epsg.xml#4326', 'neu'),\n  new EPSG4326Projection('http://www.opengis.net/def/crs/EPSG/0/4326', 'neu'),\n];\n", "/**\n * @module ol/proj/projections\n */\n\n/**\n * @type {Object<string, import(\"./Projection.js\").default>}\n */\nlet cache = {};\n\n/**\n * Clear the projections cache.\n */\nexport function clear() {\n  cache = {};\n}\n\n/**\n * Get a cached projection by code.\n * @param {string} code The code for the projection.\n * @return {import(\"./Projection.js\").default} The projection (if cached).\n */\nexport function get(code) {\n  return (\n    cache[code] ||\n    cache[code.replace(/urn:(x-)?ogc:def:crs:EPSG:(.*:)?(\\w+)$/, 'EPSG:$3')] ||\n    null\n  );\n}\n\n/**\n * Add a projection to the cache.\n * @param {string} code The projection code.\n * @param {import(\"./Projection.js\").default} projection The projection to cache.\n */\nexport function add(code, projection) {\n  cache[code] = projection;\n}\n", "/**\n * @module ol/proj/transforms\n */\nimport {isEmpty} from '../obj.js';\n\n/**\n * @private\n * @type {!Object<string, Object<string, import(\"../proj.js\").TransformFunction>>}\n */\nlet transforms = {};\n\n/**\n * Clear the transform cache.\n */\nexport function clear() {\n  transforms = {};\n}\n\n/**\n * Registers a conversion function to convert coordinates from the source\n * projection to the destination projection.\n *\n * @param {import(\"./Projection.js\").default} source Source.\n * @param {import(\"./Projection.js\").default} destination Destination.\n * @param {import(\"../proj.js\").TransformFunction} transformFn Transform.\n */\nexport function add(source, destination, transformFn) {\n  const sourceCode = source.getCode();\n  const destinationCode = destination.getCode();\n  if (!(sourceCode in transforms)) {\n    transforms[sourceCode] = {};\n  }\n  transforms[sourceCode][destinationCode] = transformFn;\n}\n\n/**\n * Unregisters the conversion function to convert coordinates from the source\n * projection to the destination projection.  This method is used to clean up\n * cached transforms during testing.\n *\n * @param {import(\"./Projection.js\").default} source Source projection.\n * @param {import(\"./Projection.js\").default} destination Destination projection.\n * @return {import(\"../proj.js\").TransformFunction} transformFn The unregistered transform.\n */\nexport function remove(source, destination) {\n  const sourceCode = source.getCode();\n  const destinationCode = destination.getCode();\n  const transform = transforms[sourceCode][destinationCode];\n  delete transforms[sourceCode][destinationCode];\n  if (isEmpty(transforms[sourceCode])) {\n    delete transforms[sourceCode];\n  }\n  return transform;\n}\n\n/**\n * Get a transform given a source code and a destination code.\n * @param {string} sourceCode The code for the source projection.\n * @param {string} destinationCode The code for the destination projection.\n * @return {import(\"../proj.js\").TransformFunction|undefined} The transform function (if found).\n */\nexport function get(sourceCode, destinationCode) {\n  let transform;\n  if (sourceCode in transforms && destinationCode in transforms[sourceCode]) {\n    transform = transforms[sourceCode][destinationCode];\n  }\n  return transform;\n}\n", "/**\n * @module ol/string\n */\n\n/**\n * @param {number} number Number to be formatted\n * @param {number} width The desired width\n * @param {number} [precision] Precision of the output string (i.e. number of decimal places)\n * @return {string} Formatted string\n */\nexport function padNumber(number, width, precision) {\n  const numberString =\n    precision !== undefined ? number.toFixed(precision) : '' + number;\n  let decimal = numberString.indexOf('.');\n  decimal = decimal === -1 ? numberString.length : decimal;\n  return decimal > width\n    ? numberString\n    : new Array(1 + width - decimal).join('0') + numberString;\n}\n\n/**\n * Adapted from https://github.com/omichelsen/compare-versions/blob/master/index.js\n * @param {string|number} v1 First version\n * @param {string|number} v2 Second version\n * @return {number} Value\n */\nexport function compareVersions(v1, v2) {\n  const s1 = ('' + v1).split('.');\n  const s2 = ('' + v2).split('.');\n\n  for (let i = 0; i < Math.max(s1.length, s2.length); i++) {\n    const n1 = parseInt(s1[i] || '0', 10);\n    const n2 = parseInt(s2[i] || '0', 10);\n\n    if (n1 > n2) {\n      return 1;\n    }\n    if (n2 > n1) {\n      return -1;\n    }\n  }\n\n  return 0;\n}\n", "/**\n * @module ol/coordinate\n */\nimport {getWidth} from './extent.js';\nimport {modulo, toFixed} from './math.js';\nimport {padNumber} from './string.js';\n\n/**\n * An array of numbers representing an `xy`, `xyz` or `xyzm` coordinate.\n * Example: `[16, 48]`.\n * @typedef {Array<number>} Coordinate\n * @api\n */\n\n/**\n * A function that takes a {@link module:ol/coordinate~Coordinate} and\n * transforms it into a `{string}`.\n *\n * @typedef {function((Coordinate|undefined)): string} CoordinateFormat\n * @api\n */\n\n/**\n * Add `delta` to `coordinate`. `coordinate` is modified in place and returned\n * by the function.\n *\n * Example:\n *\n *     import {add} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     add(coord, [-2, 4]);\n *     // coord is now [5.85, 51.983333]\n *\n * @param {Coordinate} coordinate Coordinate.\n * @param {Coordinate} delta Delta.\n * @return {Coordinate} The input coordinate adjusted by\n * the given delta.\n * @api\n */\nexport function add(coordinate, delta) {\n  coordinate[0] += +delta[0];\n  coordinate[1] += +delta[1];\n  return coordinate;\n}\n\n/**\n * Calculates the point closest to the passed coordinate on the passed circle.\n *\n * @param {Coordinate} coordinate The coordinate.\n * @param {import(\"./geom/Circle.js\").default} circle The circle.\n * @return {Coordinate} Closest point on the circumference.\n */\nexport function closestOnCircle(coordinate, circle) {\n  const r = circle.getRadius();\n  const center = circle.getCenter();\n  const x0 = center[0];\n  const y0 = center[1];\n  const x1 = coordinate[0];\n  const y1 = coordinate[1];\n\n  let dx = x1 - x0;\n  const dy = y1 - y0;\n  if (dx === 0 && dy === 0) {\n    dx = 1;\n  }\n  const d = Math.sqrt(dx * dx + dy * dy);\n\n  const x = x0 + (r * dx) / d;\n  const y = y0 + (r * dy) / d;\n\n  return [x, y];\n}\n\n/**\n * Calculates the point closest to the passed coordinate on the passed segment.\n * This is the foot of the perpendicular of the coordinate to the segment when\n * the foot is on the segment, or the closest segment coordinate when the foot\n * is outside the segment.\n *\n * @param {Coordinate} coordinate The coordinate.\n * @param {Array<Coordinate>} segment The two coordinates\n * of the segment.\n * @return {Coordinate} The foot of the perpendicular of\n * the coordinate to the segment.\n */\nexport function closestOnSegment(coordinate, segment) {\n  const x0 = coordinate[0];\n  const y0 = coordinate[1];\n  const start = segment[0];\n  const end = segment[1];\n  const x1 = start[0];\n  const y1 = start[1];\n  const x2 = end[0];\n  const y2 = end[1];\n  const dx = x2 - x1;\n  const dy = y2 - y1;\n  const along =\n    dx === 0 && dy === 0\n      ? 0\n      : (dx * (x0 - x1) + dy * (y0 - y1)) / (dx * dx + dy * dy || 0);\n  let x, y;\n  if (along <= 0) {\n    x = x1;\n    y = y1;\n  } else if (along >= 1) {\n    x = x2;\n    y = y2;\n  } else {\n    x = x1 + along * dx;\n    y = y1 + along * dy;\n  }\n  return [x, y];\n}\n\n/**\n * Returns a {@link module:ol/coordinate~CoordinateFormat} function that can be\n * used to format\n * a {Coordinate} to a string.\n *\n * Example without specifying the fractional digits:\n *\n *     import {createStringXY} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const stringifyFunc = createStringXY();\n *     const out = stringifyFunc(coord);\n *     // out is now '8, 48'\n *\n * Example with explicitly specifying 2 fractional digits:\n *\n *     import {createStringXY} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const stringifyFunc = createStringXY(2);\n *     const out = stringifyFunc(coord);\n *     // out is now '7.85, 47.98'\n *\n * @param {number} [fractionDigits] The number of digits to include\n *    after the decimal point. Default is `0`.\n * @return {CoordinateFormat} Coordinate format.\n * @api\n */\nexport function createStringXY(fractionDigits) {\n  return (\n    /**\n     * @param {Coordinate} coordinate Coordinate.\n     * @return {string} String XY.\n     */\n    function (coordinate) {\n      return toStringXY(coordinate, fractionDigits);\n    }\n  );\n}\n\n/**\n * @param {string} hemispheres Hemispheres.\n * @param {number} degrees Degrees.\n * @param {number} [fractionDigits] The number of digits to include\n *    after the decimal point. Default is `0`.\n * @return {string} String.\n */\nexport function degreesToStringHDMS(hemispheres, degrees, fractionDigits) {\n  const normalizedDegrees = modulo(degrees + 180, 360) - 180;\n  const x = Math.abs(3600 * normalizedDegrees);\n  const decimals = fractionDigits || 0;\n\n  let deg = Math.floor(x / 3600);\n  let min = Math.floor((x - deg * 3600) / 60);\n  let sec = toFixed(x - deg * 3600 - min * 60, decimals);\n\n  if (sec >= 60) {\n    sec = 0;\n    min += 1;\n  }\n\n  if (min >= 60) {\n    min = 0;\n    deg += 1;\n  }\n\n  let hdms = deg + '\\u00b0';\n  if (min !== 0 || sec !== 0) {\n    hdms += ' ' + padNumber(min, 2) + '\\u2032';\n  }\n  if (sec !== 0) {\n    hdms += ' ' + padNumber(sec, 2, decimals) + '\\u2033';\n  }\n  if (normalizedDegrees !== 0) {\n    hdms += ' ' + hemispheres.charAt(normalizedDegrees < 0 ? 1 : 0);\n  }\n\n  return hdms;\n}\n\n/**\n * Transforms the given {@link module:ol/coordinate~Coordinate} to a string\n * using the given string template. The strings `{x}` and `{y}` in the template\n * will be replaced with the first and second coordinate values respectively.\n *\n * Example without specifying the fractional digits:\n *\n *     import {format} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const template = 'Coordinate is ({x}|{y}).';\n *     const out = format(coord, template);\n *     // out is now 'Coordinate is (8|48).'\n *\n * Example explicitly specifying the fractional digits:\n *\n *     import {format} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const template = 'Coordinate is ({x}|{y}).';\n *     const out = format(coord, template, 2);\n *     // out is now 'Coordinate is (7.85|47.98).'\n *\n * @param {Coordinate} coordinate Coordinate.\n * @param {string} template A template string with `{x}` and `{y}` placeholders\n *     that will be replaced by first and second coordinate values.\n * @param {number} [fractionDigits] The number of digits to include\n *    after the decimal point. Default is `0`.\n * @return {string} Formatted coordinate.\n * @api\n */\nexport function format(coordinate, template, fractionDigits) {\n  if (coordinate) {\n    return template\n      .replace('{x}', coordinate[0].toFixed(fractionDigits))\n      .replace('{y}', coordinate[1].toFixed(fractionDigits));\n  }\n  return '';\n}\n\n/**\n * @param {Coordinate} coordinate1 First coordinate.\n * @param {Coordinate} coordinate2 Second coordinate.\n * @return {boolean} The two coordinates are equal.\n */\nexport function equals(coordinate1, coordinate2) {\n  let equals = true;\n  for (let i = coordinate1.length - 1; i >= 0; --i) {\n    if (coordinate1[i] != coordinate2[i]) {\n      equals = false;\n      break;\n    }\n  }\n  return equals;\n}\n\n/**\n * Rotate `coordinate` by `angle`. `coordinate` is modified in place and\n * returned by the function.\n *\n * Example:\n *\n *     import {rotate} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const rotateRadians = Math.PI / 2; // 90 degrees\n *     rotate(coord, rotateRadians);\n *     // coord is now [-47.983333, 7.85]\n *\n * @param {Coordinate} coordinate Coordinate.\n * @param {number} angle Angle in radian.\n * @return {Coordinate} Coordinate.\n * @api\n */\nexport function rotate(coordinate, angle) {\n  const cosAngle = Math.cos(angle);\n  const sinAngle = Math.sin(angle);\n  const x = coordinate[0] * cosAngle - coordinate[1] * sinAngle;\n  const y = coordinate[1] * cosAngle + coordinate[0] * sinAngle;\n  coordinate[0] = x;\n  coordinate[1] = y;\n  return coordinate;\n}\n\n/**\n * Scale `coordinate` by `scale`. `coordinate` is modified in place and returned\n * by the function.\n *\n * Example:\n *\n *     import {scale as scaleCoordinate} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const scale = 1.2;\n *     scaleCoordinate(coord, scale);\n *     // coord is now [9.42, 57.5799996]\n *\n * @param {Coordinate} coordinate Coordinate.\n * @param {number} scale Scale factor.\n * @return {Coordinate} Coordinate.\n */\nexport function scale(coordinate, scale) {\n  coordinate[0] *= scale;\n  coordinate[1] *= scale;\n  return coordinate;\n}\n\n/**\n * @param {Coordinate} coord1 First coordinate.\n * @param {Coordinate} coord2 Second coordinate.\n * @return {number} Squared distance between coord1 and coord2.\n */\nexport function squaredDistance(coord1, coord2) {\n  const dx = coord1[0] - coord2[0];\n  const dy = coord1[1] - coord2[1];\n  return dx * dx + dy * dy;\n}\n\n/**\n * @param {Coordinate} coord1 First coordinate.\n * @param {Coordinate} coord2 Second coordinate.\n * @return {number} Distance between coord1 and coord2.\n */\nexport function distance(coord1, coord2) {\n  return Math.sqrt(squaredDistance(coord1, coord2));\n}\n\n/**\n * Calculate the squared distance from a coordinate to a line segment.\n *\n * @param {Coordinate} coordinate Coordinate of the point.\n * @param {Array<Coordinate>} segment Line segment (2\n * coordinates).\n * @return {number} Squared distance from the point to the line segment.\n */\nexport function squaredDistanceToSegment(coordinate, segment) {\n  return squaredDistance(coordinate, closestOnSegment(coordinate, segment));\n}\n\n/**\n * Format a geographic coordinate with the hemisphere, degrees, minutes, and\n * seconds.\n *\n * Example without specifying fractional digits:\n *\n *     import {toStringHDMS} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const out = toStringHDMS(coord);\n *     // out is now '47° 58′ 60″ N 7° 50′ 60″ E'\n *\n * Example explicitly specifying 1 fractional digit:\n *\n *     import {toStringHDMS} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const out = toStringHDMS(coord, 1);\n *     // out is now '47° 58′ 60.0″ N 7° 50′ 60.0″ E'\n *\n * @param {Coordinate} coordinate Coordinate.\n * @param {number} [fractionDigits] The number of digits to include\n *    after the decimal point. Default is `0`.\n * @return {string} Hemisphere, degrees, minutes and seconds.\n * @api\n */\nexport function toStringHDMS(coordinate, fractionDigits) {\n  if (coordinate) {\n    return (\n      degreesToStringHDMS('NS', coordinate[1], fractionDigits) +\n      ' ' +\n      degreesToStringHDMS('EW', coordinate[0], fractionDigits)\n    );\n  }\n  return '';\n}\n\n/**\n * Format a coordinate as a comma delimited string.\n *\n * Example without specifying fractional digits:\n *\n *     import {toStringXY} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const out = toStringXY(coord);\n *     // out is now '8, 48'\n *\n * Example explicitly specifying 1 fractional digit:\n *\n *     import {toStringXY} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const out = toStringXY(coord, 1);\n *     // out is now '7.8, 48.0'\n *\n * @param {Coordinate} coordinate Coordinate.\n * @param {number} [fractionDigits] The number of digits to include\n *    after the decimal point. Default is `0`.\n * @return {string} XY.\n * @api\n */\nexport function toStringXY(coordinate, fractionDigits) {\n  return format(coordinate, '{x}, {y}', fractionDigits);\n}\n\n/**\n * Modifies the provided coordinate in-place to be within the real world\n * extent. The lower projection extent boundary is inclusive, the upper one\n * exclusive.\n *\n * @param {Coordinate} coordinate Coordinate.\n * @param {import(\"./proj/Projection.js\").default} projection Projection.\n * @return {Coordinate} The coordinate within the real world extent.\n */\nexport function wrapX(coordinate, projection) {\n  if (projection.canWrapX()) {\n    const worldWidth = getWidth(projection.getExtent());\n    const worldsAway = getWorldsAway(coordinate, projection, worldWidth);\n    if (worldsAway) {\n      coordinate[0] -= worldsAway * worldWidth;\n    }\n  }\n  return coordinate;\n}\n/**\n * @param {Coordinate} coordinate Coordinate.\n * @param {import(\"./proj/Projection.js\").default} projection Projection.\n * @param {number} [sourceExtentWidth] Width of the source extent.\n * @return {number} Offset in world widths.\n */\nexport function getWorldsAway(coordinate, projection, sourceExtentWidth) {\n  const projectionExtent = projection.getExtent();\n  let worldsAway = 0;\n  if (\n    projection.canWrapX() &&\n    (coordinate[0] < projectionExtent[0] || coordinate[0] > projectionExtent[2])\n  ) {\n    sourceExtentWidth = sourceExtentWidth || getWidth(projectionExtent);\n    worldsAway = Math.floor(\n      (coordinate[0] - projectionExtent[0]) / sourceExtentWidth\n    );\n  }\n  return worldsAway;\n}\n", "/**\n * @module ol/console\n */\n\n/**\n * @typedef {'info'|'warn'|'error'|'none'} Level\n */\n\n/**\n * @type {Object<Level, number>}\n */\nconst levels = {\n  info: 1,\n  warn: 2,\n  error: 3,\n  none: 4,\n};\n\n/**\n * @type {number}\n */\nlet level = levels.info;\n\n/**\n * Set the logging level.  By default, the level is set to 'info' and all\n * messages will be logged.  Set to 'warn' to only display warnings and errors.\n * Set to 'error' to only display errors.  Set to 'none' to silence all messages.\n *\n * @param {Level} l The new level.\n */\nexport function setLevel(l) {\n  level = levels[l];\n}\n\nexport function log(...args) {\n  if (level > levels.info) {\n    return;\n  }\n  console.log(...args); // eslint-disable-line no-console\n}\n\nexport function warn(...args) {\n  if (level > levels.warn) {\n    return;\n  }\n  console.warn(...args); // eslint-disable-line no-console\n}\n\nexport function error(...args) {\n  if (level > levels.error) {\n    return;\n  }\n  console.error(...args); // eslint-disable-line no-console\n}\n", "/**\n * @module ol/proj\n */\n\n/**\n * The ol/proj module stores:\n * * a list of {@link module:ol/proj/Projection~Projection}\n * objects, one for each projection supported by the application\n * * a list of transform functions needed to convert coordinates in one projection\n * into another.\n *\n * The static functions are the methods used to maintain these.\n * Each transform function can handle not only simple coordinate pairs, but also\n * large arrays of coordinates such as vector geometries.\n *\n * When loaded, the library adds projection objects for EPSG:4326 (WGS84\n * geographic coordinates) and EPSG:3857 (Web or Spherical Mercator, as used\n * for example by Bing Maps or OpenStreetMap), together with the relevant\n * transform functions.\n *\n * Additional transforms may be added by using the http://proj4js.org/\n * library (version 2.2 or later). You can use the full build supplied by\n * Proj4js, or create a custom build to support those projections you need; see\n * the Proj4js website for how to do this. You also need the Proj4js definitions\n * for the required projections. These definitions can be obtained from\n * https://epsg.io/, and are a JS function, so can be loaded in a script\n * tag (as in the examples) or pasted into your application.\n *\n * After all required projection definitions are added to proj4's registry (by\n * using `proj4.defs()`), simply call `register(proj4)` from the `ol/proj/proj4`\n * package. Existing transforms are not changed by this function. See\n * examples/wms-image-custom-proj for an example of this.\n *\n * Additional projection definitions can be registered with `proj4.defs()` any\n * time. Just make sure to call `register(proj4)` again; for example, with user-supplied data where you don't\n * know in advance what projections are needed, you can initially load minimal\n * support and then load whichever are requested.\n *\n * Note that Proj4js does not support projection extents. If you want to add\n * one for creating default tile grids, you can add it after the Projection\n * object has been created with `setExtent`, for example,\n * `get('EPSG:1234').setExtent(extent)`.\n *\n * In addition to Proj4js support, any transform functions can be added with\n * {@link module:ol/proj.addCoordinateTransforms}. To use this, you must first create\n * a {@link module:ol/proj/Projection~Projection} object for the new projection and add it with\n * {@link module:ol/proj.addProjection}. You can then add the forward and inverse\n * functions with {@link module:ol/proj.addCoordinateTransforms}. See\n * examples/wms-custom-proj for an example of this.\n *\n * Note that if no transforms are needed and you only need to define the\n * projection, just add a {@link module:ol/proj/Projection~Projection} with\n * {@link module:ol/proj.addProjection}. See examples/wms-no-proj for an example of\n * this.\n */\nimport Projection from './proj/Projection.js';\nimport {\n  PROJECTIONS as EPSG3857_PROJECTIONS,\n  fromEPSG4326,\n  toEPSG4326,\n} from './proj/epsg3857.js';\nimport {PROJECTIONS as EPSG4326_PROJECTIONS} from './proj/epsg4326.js';\nimport {METERS_PER_UNIT} from './proj/Units.js';\nimport {\n  add as addProj,\n  clear as clearProj,\n  get as getProj,\n} from './proj/projections.js';\nimport {\n  add as addTransformFunc,\n  clear as clearTransformFuncs,\n  get as getTransformFunc,\n} from './proj/transforms.js';\nimport {applyTransform, getWidth} from './extent.js';\nimport {clamp, modulo} from './math.js';\nimport {equals, getWorldsAway} from './coordinate.js';\nimport {getDistance} from './sphere.js';\nimport {warn} from './console.js';\n\n/**\n * A projection as {@link module:ol/proj/Projection~Projection}, SRS identifier\n * string or undefined.\n * @typedef {Projection|string|undefined} ProjectionLike\n * @api\n */\n\n/**\n * A transform function accepts an array of input coordinate values, an optional\n * output array, and an optional dimension (default should be 2).  The function\n * transforms the input coordinate values, populates the output array, and\n * returns the output array.\n *\n * @typedef {function(Array<number>, Array<number>=, number=): Array<number>} TransformFunction\n * @api\n */\n\nexport {METERS_PER_UNIT};\n\nexport {Projection};\n\nlet showCoordinateWarning = true;\n\n/**\n * @param {boolean} [disable = true] Disable console info about `useGeographic()`\n */\nexport function disableCoordinateWarning(disable) {\n  const hide = disable === undefined ? true : disable;\n  showCoordinateWarning = !hide;\n}\n\n/**\n * @param {Array<number>} input Input coordinate array.\n * @param {Array<number>} [output] Output array of coordinate values.\n * @return {Array<number>} Output coordinate array (new array, same coordinate\n *     values).\n */\nexport function cloneTransform(input, output) {\n  if (output !== undefined) {\n    for (let i = 0, ii = input.length; i < ii; ++i) {\n      output[i] = input[i];\n    }\n    output = output;\n  } else {\n    output = input.slice();\n  }\n  return output;\n}\n\n/**\n * @param {Array<number>} input Input coordinate array.\n * @param {Array<number>} [output] Output array of coordinate values.\n * @return {Array<number>} Input coordinate array (same array as input).\n */\nexport function identityTransform(input, output) {\n  if (output !== undefined && input !== output) {\n    for (let i = 0, ii = input.length; i < ii; ++i) {\n      output[i] = input[i];\n    }\n    input = output;\n  }\n  return input;\n}\n\n/**\n * Add a Projection object to the list of supported projections that can be\n * looked up by their code.\n *\n * @param {Projection} projection Projection instance.\n * @api\n */\nexport function addProjection(projection) {\n  addProj(projection.getCode(), projection);\n  addTransformFunc(projection, projection, cloneTransform);\n}\n\n/**\n * @param {Array<Projection>} projections Projections.\n */\nexport function addProjections(projections) {\n  projections.forEach(addProjection);\n}\n\n/**\n * Fetches a Projection object for the code specified.\n *\n * @param {ProjectionLike} projectionLike Either a code string which is\n *     a combination of authority and identifier such as \"EPSG:4326\", or an\n *     existing projection object, or undefined.\n * @return {Projection|null} Projection object, or null if not in list.\n * @api\n */\nexport function get(projectionLike) {\n  return typeof projectionLike === 'string'\n    ? getProj(/** @type {string} */ (projectionLike))\n    : /** @type {Projection} */ (projectionLike) || null;\n}\n\n/**\n * Get the resolution of the point in degrees or distance units.\n * For projections with degrees as the unit this will simply return the\n * provided resolution. For other projections the point resolution is\n * by default estimated by transforming the `point` pixel to EPSG:4326,\n * measuring its width and height on the normal sphere,\n * and taking the average of the width and height.\n * A custom function can be provided for a specific projection, either\n * by setting the `getPointResolution` option in the\n * {@link module:ol/proj/Projection~Projection} constructor or by using\n * {@link module:ol/proj/Projection~Projection#setGetPointResolution} to change an existing\n * projection object.\n * @param {ProjectionLike} projection The projection.\n * @param {number} resolution Nominal resolution in projection units.\n * @param {import(\"./coordinate.js\").Coordinate} point Point to find adjusted resolution at.\n * @param {import(\"./proj/Units.js\").Units} [units] Units to get the point resolution in.\n * Default is the projection's units.\n * @return {number} Point resolution.\n * @api\n */\nexport function getPointResolution(projection, resolution, point, units) {\n  projection = get(projection);\n  let pointResolution;\n  const getter = projection.getPointResolutionFunc();\n  if (getter) {\n    pointResolution = getter(resolution, point);\n    if (units && units !== projection.getUnits()) {\n      const metersPerUnit = projection.getMetersPerUnit();\n      if (metersPerUnit) {\n        pointResolution =\n          (pointResolution * metersPerUnit) / METERS_PER_UNIT[units];\n      }\n    }\n  } else {\n    const projUnits = projection.getUnits();\n    if ((projUnits == 'degrees' && !units) || units == 'degrees') {\n      pointResolution = resolution;\n    } else {\n      // Estimate point resolution by transforming the center pixel to EPSG:4326,\n      // measuring its width and height on the normal sphere, and taking the\n      // average of the width and height.\n      const toEPSG4326 = getTransformFromProjections(\n        projection,\n        get('EPSG:4326')\n      );\n      if (toEPSG4326 === identityTransform && projUnits !== 'degrees') {\n        // no transform is available\n        pointResolution = resolution * projection.getMetersPerUnit();\n      } else {\n        let vertices = [\n          point[0] - resolution / 2,\n          point[1],\n          point[0] + resolution / 2,\n          point[1],\n          point[0],\n          point[1] - resolution / 2,\n          point[0],\n          point[1] + resolution / 2,\n        ];\n        vertices = toEPSG4326(vertices, vertices, 2);\n        const width = getDistance(vertices.slice(0, 2), vertices.slice(2, 4));\n        const height = getDistance(vertices.slice(4, 6), vertices.slice(6, 8));\n        pointResolution = (width + height) / 2;\n      }\n      const metersPerUnit = units\n        ? METERS_PER_UNIT[units]\n        : projection.getMetersPerUnit();\n      if (metersPerUnit !== undefined) {\n        pointResolution /= metersPerUnit;\n      }\n    }\n  }\n  return pointResolution;\n}\n\n/**\n * Registers transformation functions that don't alter coordinates. Those allow\n * to transform between projections with equal meaning.\n *\n * @param {Array<Projection>} projections Projections.\n * @api\n */\nexport function addEquivalentProjections(projections) {\n  addProjections(projections);\n  projections.forEach(function (source) {\n    projections.forEach(function (destination) {\n      if (source !== destination) {\n        addTransformFunc(source, destination, cloneTransform);\n      }\n    });\n  });\n}\n\n/**\n * Registers transformation functions to convert coordinates in any projection\n * in projection1 to any projection in projection2.\n *\n * @param {Array<Projection>} projections1 Projections with equal\n *     meaning.\n * @param {Array<Projection>} projections2 Projections with equal\n *     meaning.\n * @param {TransformFunction} forwardTransform Transformation from any\n *   projection in projection1 to any projection in projection2.\n * @param {TransformFunction} inverseTransform Transform from any projection\n *   in projection2 to any projection in projection1..\n */\nexport function addEquivalentTransforms(\n  projections1,\n  projections2,\n  forwardTransform,\n  inverseTransform\n) {\n  projections1.forEach(function (projection1) {\n    projections2.forEach(function (projection2) {\n      addTransformFunc(projection1, projection2, forwardTransform);\n      addTransformFunc(projection2, projection1, inverseTransform);\n    });\n  });\n}\n\n/**\n * Clear all cached projections and transforms.\n */\nexport function clearAllProjections() {\n  clearProj();\n  clearTransformFuncs();\n}\n\n/**\n * @param {Projection|string|undefined} projection Projection.\n * @param {string} defaultCode Default code.\n * @return {Projection} Projection.\n */\nexport function createProjection(projection, defaultCode) {\n  if (!projection) {\n    return get(defaultCode);\n  }\n  if (typeof projection === 'string') {\n    return get(projection);\n  }\n  return /** @type {Projection} */ (projection);\n}\n\n/**\n * Creates a {@link module:ol/proj~TransformFunction} from a simple 2D coordinate transform\n * function.\n * @param {function(import(\"./coordinate.js\").Coordinate): import(\"./coordinate.js\").Coordinate} coordTransform Coordinate\n *     transform.\n * @return {TransformFunction} Transform function.\n */\nexport function createTransformFromCoordinateTransform(coordTransform) {\n  return (\n    /**\n     * @param {Array<number>} input Input.\n     * @param {Array<number>} [output] Output.\n     * @param {number} [dimension] Dimension.\n     * @return {Array<number>} Output.\n     */\n    function (input, output, dimension) {\n      const length = input.length;\n      dimension = dimension !== undefined ? dimension : 2;\n      output = output !== undefined ? output : new Array(length);\n      for (let i = 0; i < length; i += dimension) {\n        const point = coordTransform(input.slice(i, i + dimension));\n        const pointLength = point.length;\n        for (let j = 0, jj = dimension; j < jj; ++j) {\n          output[i + j] = j >= pointLength ? input[i + j] : point[j];\n        }\n      }\n      return output;\n    }\n  );\n}\n\n/**\n * Registers coordinate transform functions to convert coordinates between the\n * source projection and the destination projection.\n * The forward and inverse functions convert coordinate pairs; this function\n * converts these into the functions used internally which also handle\n * extents and coordinate arrays.\n *\n * @param {ProjectionLike} source Source projection.\n * @param {ProjectionLike} destination Destination projection.\n * @param {function(import(\"./coordinate.js\").Coordinate): import(\"./coordinate.js\").Coordinate} forward The forward transform\n *     function (that is, from the source projection to the destination\n *     projection) that takes a {@link module:ol/coordinate~Coordinate} as argument and returns\n *     the transformed {@link module:ol/coordinate~Coordinate}.\n * @param {function(import(\"./coordinate.js\").Coordinate): import(\"./coordinate.js\").Coordinate} inverse The inverse transform\n *     function (that is, from the destination projection to the source\n *     projection) that takes a {@link module:ol/coordinate~Coordinate} as argument and returns\n *     the transformed {@link module:ol/coordinate~Coordinate}. If the transform function can only\n *     transform less dimensions than the input coordinate, it is supposeed to return a coordinate\n *     with only the length it can transform. The other dimensions will be taken unchanged from the\n *     source.\n * @api\n */\nexport function addCoordinateTransforms(source, destination, forward, inverse) {\n  const sourceProj = get(source);\n  const destProj = get(destination);\n  addTransformFunc(\n    sourceProj,\n    destProj,\n    createTransformFromCoordinateTransform(forward)\n  );\n  addTransformFunc(\n    destProj,\n    sourceProj,\n    createTransformFromCoordinateTransform(inverse)\n  );\n}\n\n/**\n * Transforms a coordinate from longitude/latitude to a different projection.\n * @param {import(\"./coordinate.js\").Coordinate} coordinate Coordinate as longitude and latitude, i.e.\n *     an array with longitude as 1st and latitude as 2nd element.\n * @param {ProjectionLike} [projection] Target projection. The\n *     default is Web Mercator, i.e. 'EPSG:3857'.\n * @return {import(\"./coordinate.js\").Coordinate} Coordinate projected to the target projection.\n * @api\n */\nexport function fromLonLat(coordinate, projection) {\n  disableCoordinateWarning();\n  return transform(\n    coordinate,\n    'EPSG:4326',\n    projection !== undefined ? projection : 'EPSG:3857'\n  );\n}\n\n/**\n * Transforms a coordinate to longitude/latitude.\n * @param {import(\"./coordinate.js\").Coordinate} coordinate Projected coordinate.\n * @param {ProjectionLike} [projection] Projection of the coordinate.\n *     The default is Web Mercator, i.e. 'EPSG:3857'.\n * @return {import(\"./coordinate.js\").Coordinate} Coordinate as longitude and latitude, i.e. an array\n *     with longitude as 1st and latitude as 2nd element.\n * @api\n */\nexport function toLonLat(coordinate, projection) {\n  const lonLat = transform(\n    coordinate,\n    projection !== undefined ? projection : 'EPSG:3857',\n    'EPSG:4326'\n  );\n  const lon = lonLat[0];\n  if (lon < -180 || lon > 180) {\n    lonLat[0] = modulo(lon + 180, 360) - 180;\n  }\n  return lonLat;\n}\n\n/**\n * Checks if two projections are the same, that is every coordinate in one\n * projection does represent the same geographic point as the same coordinate in\n * the other projection.\n *\n * @param {Projection} projection1 Projection 1.\n * @param {Projection} projection2 Projection 2.\n * @return {boolean} Equivalent.\n * @api\n */\nexport function equivalent(projection1, projection2) {\n  if (projection1 === projection2) {\n    return true;\n  }\n  const equalUnits = projection1.getUnits() === projection2.getUnits();\n  if (projection1.getCode() === projection2.getCode()) {\n    return equalUnits;\n  }\n  const transformFunc = getTransformFromProjections(projection1, projection2);\n  return transformFunc === cloneTransform && equalUnits;\n}\n\n/**\n * Searches in the list of transform functions for the function for converting\n * coordinates from the source projection to the destination projection.\n *\n * @param {Projection} sourceProjection Source Projection object.\n * @param {Projection} destinationProjection Destination Projection\n *     object.\n * @return {TransformFunction} Transform function.\n */\nexport function getTransformFromProjections(\n  sourceProjection,\n  destinationProjection\n) {\n  const sourceCode = sourceProjection.getCode();\n  const destinationCode = destinationProjection.getCode();\n  let transformFunc = getTransformFunc(sourceCode, destinationCode);\n  if (!transformFunc) {\n    transformFunc = identityTransform;\n  }\n  return transformFunc;\n}\n\n/**\n * Given the projection-like objects, searches for a transformation\n * function to convert a coordinates array from the source projection to the\n * destination projection.\n *\n * @param {ProjectionLike} source Source.\n * @param {ProjectionLike} destination Destination.\n * @return {TransformFunction} Transform function.\n * @api\n */\nexport function getTransform(source, destination) {\n  const sourceProjection = get(source);\n  const destinationProjection = get(destination);\n  return getTransformFromProjections(sourceProjection, destinationProjection);\n}\n\n/**\n * Transforms a coordinate from source projection to destination projection.\n * This returns a new coordinate (and does not modify the original).\n *\n * See {@link module:ol/proj.transformExtent} for extent transformation.\n * See the transform method of {@link module:ol/geom/Geometry~Geometry} and its\n * subclasses for geometry transforms.\n *\n * @param {import(\"./coordinate.js\").Coordinate} coordinate Coordinate.\n * @param {ProjectionLike} source Source projection-like.\n * @param {ProjectionLike} destination Destination projection-like.\n * @return {import(\"./coordinate.js\").Coordinate} Coordinate.\n * @api\n */\nexport function transform(coordinate, source, destination) {\n  const transformFunc = getTransform(source, destination);\n  return transformFunc(coordinate, undefined, coordinate.length);\n}\n\n/**\n * Transforms an extent from source projection to destination projection.  This\n * returns a new extent (and does not modify the original).\n *\n * @param {import(\"./extent.js\").Extent} extent The extent to transform.\n * @param {ProjectionLike} source Source projection-like.\n * @param {ProjectionLike} destination Destination projection-like.\n * @param {number} [stops] Number of stops per side used for the transform.\n * By default only the corners are used.\n * @return {import(\"./extent.js\").Extent} The transformed extent.\n * @api\n */\nexport function transformExtent(extent, source, destination, stops) {\n  const transformFunc = getTransform(source, destination);\n  return applyTransform(extent, transformFunc, undefined, stops);\n}\n\n/**\n * Transforms the given point to the destination projection.\n *\n * @param {import(\"./coordinate.js\").Coordinate} point Point.\n * @param {Projection} sourceProjection Source projection.\n * @param {Projection} destinationProjection Destination projection.\n * @return {import(\"./coordinate.js\").Coordinate} Point.\n */\nexport function transformWithProjections(\n  point,\n  sourceProjection,\n  destinationProjection\n) {\n  const transformFunc = getTransformFromProjections(\n    sourceProjection,\n    destinationProjection\n  );\n  return transformFunc(point);\n}\n\n/**\n * @type {Projection|null}\n */\nlet userProjection = null;\n\n/**\n * Set the projection for coordinates supplied from and returned by API methods.\n * This includes all API methods except for those interacting with tile grids,\n * plus {@link import(\"./Map.js\").FrameState} and {@link import(\"./View.js\").State}.\n * @param {ProjectionLike} projection The user projection.\n * @api\n */\nexport function setUserProjection(projection) {\n  userProjection = get(projection);\n}\n\n/**\n * Clear the user projection if set.\n * @api\n */\nexport function clearUserProjection() {\n  userProjection = null;\n}\n\n/**\n * Get the projection for coordinates supplied from and returned by API methods.\n * @return {Projection|null} The user projection (or null if not set).\n * @api\n */\nexport function getUserProjection() {\n  return userProjection;\n}\n\n/**\n * Use geographic coordinates (WGS-84 datum) in API methods.\n * This includes all API methods except for those interacting with tile grids,\n * plus {@link import(\"./Map.js\").FrameState} and {@link import(\"./View.js\").State}.\n * @api\n */\nexport function useGeographic() {\n  setUserProjection('EPSG:4326');\n}\n\n/**\n * Return a coordinate transformed into the user projection.  If no user projection\n * is set, the original coordinate is returned.\n * @param {Array<number>} coordinate Input coordinate.\n * @param {ProjectionLike} sourceProjection The input coordinate projection.\n * @return {Array<number>} The input coordinate in the user projection.\n */\nexport function toUserCoordinate(coordinate, sourceProjection) {\n  if (!userProjection) {\n    return coordinate;\n  }\n  return transform(coordinate, sourceProjection, userProjection);\n}\n\n/**\n * Return a coordinate transformed from the user projection.  If no user projection\n * is set, the original coordinate is returned.\n * @param {Array<number>} coordinate Input coordinate.\n * @param {ProjectionLike} destProjection The destination projection.\n * @return {Array<number>} The input coordinate transformed.\n */\nexport function fromUserCoordinate(coordinate, destProjection) {\n  if (!userProjection) {\n    if (\n      showCoordinateWarning &&\n      !equals(coordinate, [0, 0]) &&\n      coordinate[0] >= -180 &&\n      coordinate[0] <= 180 &&\n      coordinate[1] >= -90 &&\n      coordinate[1] <= 90\n    ) {\n      showCoordinateWarning = false;\n      warn(\n        'Call useGeographic() from ol/proj once to work with [longitude, latitude] coordinates.'\n      );\n    }\n    return coordinate;\n  }\n  return transform(coordinate, userProjection, destProjection);\n}\n\n/**\n * Return an extent transformed into the user projection.  If no user projection\n * is set, the original extent is returned.\n * @param {import(\"./extent.js\").Extent} extent Input extent.\n * @param {ProjectionLike} sourceProjection The input extent projection.\n * @return {import(\"./extent.js\").Extent} The input extent in the user projection.\n */\nexport function toUserExtent(extent, sourceProjection) {\n  if (!userProjection) {\n    return extent;\n  }\n  return transformExtent(extent, sourceProjection, userProjection);\n}\n\n/**\n * Return an extent transformed from the user projection.  If no user projection\n * is set, the original extent is returned.\n * @param {import(\"./extent.js\").Extent} extent Input extent.\n * @param {ProjectionLike} destProjection The destination projection.\n * @return {import(\"./extent.js\").Extent} The input extent transformed.\n */\nexport function fromUserExtent(extent, destProjection) {\n  if (!userProjection) {\n    return extent;\n  }\n  return transformExtent(extent, userProjection, destProjection);\n}\n\n/**\n * Return the resolution in user projection units per pixel. If no user projection\n * is set, or source or user projection are missing units, the original resolution\n * is returned.\n * @param {number} resolution Resolution in input projection units per pixel.\n * @param {ProjectionLike} sourceProjection The input projection.\n * @return {number} Resolution in user projection units per pixel.\n */\nexport function toUserResolution(resolution, sourceProjection) {\n  if (!userProjection) {\n    return resolution;\n  }\n  const sourceUnits = get(sourceProjection).getUnits();\n  const userUnits = userProjection.getUnits();\n  return sourceUnits && userUnits\n    ? (resolution * METERS_PER_UNIT[sourceUnits]) / METERS_PER_UNIT[userUnits]\n    : resolution;\n}\n\n/**\n * Return the resolution in user projection units per pixel. If no user projection\n * is set, or source or user projection are missing units, the original resolution\n * is returned.\n * @param {number} resolution Resolution in user projection units per pixel.\n * @param {ProjectionLike} destProjection The destination projection.\n * @return {number} Resolution in destination projection units per pixel.\n */\nexport function fromUserResolution(resolution, destProjection) {\n  if (!userProjection) {\n    return resolution;\n  }\n  const sourceUnits = get(destProjection).getUnits();\n  const userUnits = userProjection.getUnits();\n  return sourceUnits && userUnits\n    ? (resolution * METERS_PER_UNIT[userUnits]) / METERS_PER_UNIT[sourceUnits]\n    : resolution;\n}\n\n/**\n * Creates a safe coordinate transform function from a coordinate transform function.\n * \"Safe\" means that it can handle wrapping of x-coordinates for global projections,\n * and that coordinates exceeding the source projection validity extent's range will be\n * clamped to the validity range.\n * @param {Projection} sourceProj Source projection.\n * @param {Projection} destProj Destination projection.\n * @param {function(import(\"./coordinate.js\").Coordinate): import(\"./coordinate.js\").Coordinate} transform Transform function (source to destination).\n * @return {function(import(\"./coordinate.js\").Coordinate): import(\"./coordinate.js\").Coordinate} Safe transform function (source to destination).\n */\nexport function createSafeCoordinateTransform(sourceProj, destProj, transform) {\n  return function (coord) {\n    let transformed, worldsAway;\n    if (sourceProj.canWrapX()) {\n      const sourceExtent = sourceProj.getExtent();\n      const sourceExtentWidth = getWidth(sourceExtent);\n      coord = coord.slice(0);\n      worldsAway = getWorldsAway(coord, sourceProj, sourceExtentWidth);\n      if (worldsAway) {\n        // Move x to the real world\n        coord[0] = coord[0] - worldsAway * sourceExtentWidth;\n      }\n      coord[0] = clamp(coord[0], sourceExtent[0], sourceExtent[2]);\n      coord[1] = clamp(coord[1], sourceExtent[1], sourceExtent[3]);\n      transformed = transform(coord);\n    } else {\n      transformed = transform(coord);\n    }\n    if (worldsAway && destProj.canWrapX()) {\n      // Move transformed coordinate back to the offset world\n      transformed[0] += worldsAway * getWidth(destProj.getExtent());\n    }\n    return transformed;\n  };\n}\n\n/**\n * Add transforms to and from EPSG:4326 and EPSG:3857.  This function is called\n * by when this module is executed and should only need to be called again after\n * `clearAllProjections()` is called (e.g. in tests).\n */\nexport function addCommon() {\n  // Add transformations that don't alter coordinates to convert within set of\n  // projections with equal meaning.\n  addEquivalentProjections(EPSG3857_PROJECTIONS);\n  addEquivalentProjections(EPSG4326_PROJECTIONS);\n  // Add transformations to convert EPSG:4326 like coordinates to EPSG:3857 like\n  // coordinates and back.\n  addEquivalentTransforms(\n    EPSG4326_PROJECTIONS,\n    EPSG3857_PROJECTIONS,\n    fromEPSG4326,\n    toEPSG4326\n  );\n}\n\naddCommon();\n"], "mappings": ";;;;;;;;;;;;;;AAaA,IAAM,aAAa;AAAA,EACjB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACV;AAMO,SAAS,SAAS,MAAM;AAC7B,SAAO,WAAW,IAAI;AACxB;AAiBO,IAAM,kBAAkB;AAAA;AAAA,EAE7B,WAAW,WAAW,IAAI,KAAK;AAAA,EAC/B,WAAY,IAAI,KAAK,KAAK,UAAW;AAAA,EACrC,MAAM;AAAA,EACN,KAAK;AAAA,EACL,SAAS,OAAO;AAClB;;;ACAA,IAAM,aAAN,MAAiB;AAAA;AAAA;AAAA;AAAA,EAIf,YAAY,SAAS;AAKnB,SAAK,QAAQ,QAAQ;AASrB,SAAK;AAAA,IAAoD,QAAQ;AASjE,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAS/D,SAAK,eACH,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAM5D,SAAK,mBACH,QAAQ,oBAAoB,SAAY,QAAQ,kBAAkB;AAMpE,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAM/D,SAAK,YAAY,CAAC,EAAE,KAAK,WAAW,KAAK;AAMzC,SAAK,0BAA0B,QAAQ;AAMvC,SAAK,mBAAmB;AAMxB,SAAK,iBAAiB,QAAQ;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,mBAAmB;AACjB,WAAO,KAAK,kBAAkB,gBAAgB,KAAK,MAAM;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ;AAChB,SAAK,UAAU;AACf,SAAK,YAAY,CAAC,EAAE,UAAU,KAAK;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,UAAU;AAC3B,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ;AAChB,SAAK,UAAU;AACf,SAAK,YAAY,CAAC,EAAE,KAAK,WAAW;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,aAAa;AAC1B,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,MAAM;AAC1B,SAAK,0BAA0B;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,yBAAyB;AACvB,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAO,qBAAQ;;;ACnQR,IAAM,SAAS;AAMf,IAAM,YAAY,KAAK,KAAK;AAM5B,IAAM,SAAS,CAAC,CAAC,WAAW,CAAC,WAAW,WAAW,SAAS;AAM5D,IAAM,eAAe,CAAC,MAAM,KAAK,KAAK,EAAE;AAOxC,IAAM,aAAa,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC;AAMjE,IAAM,qBAAN,cAAiC,mBAAW;AAAA;AAAA;AAAA;AAAA,EAI1C,YAAY,MAAM;AAChB,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,oBAAoB,SAAU,YAAY,OAAO;AAC/C,eAAO,aAAa,KAAK,KAAK,MAAM,CAAC,IAAI,MAAM;AAAA,MACjD;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAQO,IAAM,cAAc;AAAA,EACzB,IAAI,mBAAmB,WAAW;AAAA,EAClC,IAAI,mBAAmB,aAAa;AAAA,EACpC,IAAI,mBAAmB,aAAa;AAAA,EACpC,IAAI,mBAAmB,aAAa;AAAA,EACpC,IAAI,mBAAmB,4CAA4C;AAAA,EACnE,IAAI,mBAAmB,8CAA8C;AACvE;AAUO,SAAS,aAAa,OAAO,QAAQ,WAAW;AACrD,QAAM,SAAS,MAAM;AACrB,cAAY,YAAY,IAAI,YAAY;AACxC,MAAI,WAAW,QAAW;AACxB,QAAI,YAAY,GAAG;AAEjB,eAAS,MAAM,MAAM;AAAA,IACvB,OAAO;AACL,eAAS,IAAI,MAAM,MAAM;AAAA,IAC3B;AAAA,EACF;AACA,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAK,YAAY,MAAM,CAAC,IAAK;AACrC,QAAI,IAAI,SAAS,KAAK,IAAI,KAAK,IAAK,KAAK,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,MAAO,GAAG,CAAC;AAC1E,QAAI,IAAI,YAAY;AAClB,UAAI;AAAA,IACN,WAAW,IAAI,CAAC,YAAY;AAC1B,UAAI,CAAC;AAAA,IACP;AACA,WAAO,IAAI,CAAC,IAAI;AAAA,EAClB;AACA,SAAO;AACT;AAUO,SAAS,WAAW,OAAO,QAAQ,WAAW;AACnD,QAAM,SAAS,MAAM;AACrB,cAAY,YAAY,IAAI,YAAY;AACxC,MAAI,WAAW,QAAW;AACxB,QAAI,YAAY,GAAG;AAEjB,eAAS,MAAM,MAAM;AAAA,IACvB,OAAO;AACL,eAAS,IAAI,MAAM,MAAM;AAAA,IAC3B;AAAA,EACF;AACA,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,WAAW;AAC1C,WAAO,CAAC,IAAK,MAAM,MAAM,CAAC,IAAK;AAC/B,WAAO,IAAI,CAAC,IACT,MAAM,KAAK,KAAK,KAAK,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,IAAK,KAAK,KAAK;AAAA,EACnE;AACA,SAAO;AACT;;;ACzHO,IAAMA,UAAS;AAQf,IAAMC,UAAS,CAAC,MAAM,KAAK,KAAK,EAAE;AAMlC,IAAMC,mBAAmB,KAAK,KAAKF,UAAU;AAUpD,IAAM,qBAAN,cAAiC,mBAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1C,YAAY,MAAM,iBAAiB;AACjC,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP,QAAQC;AAAA,MACR;AAAA,MACA,QAAQ;AAAA,MACR,eAAeC;AAAA,MACf,aAAaD;AAAA,IACf,CAAC;AAAA,EACH;AACF;AAQO,IAAME,eAAc;AAAA,EACzB,IAAI,mBAAmB,QAAQ;AAAA,EAC/B,IAAI,mBAAmB,aAAa,KAAK;AAAA,EACzC,IAAI,mBAAmB,+BAA+B;AAAA,EACtD,IAAI,mBAAmB,0BAA0B;AAAA,EACjD,IAAI,mBAAmB,8CAA8C;AAAA,EACrE,IAAI,mBAAmB,gDAAgD,KAAK;AAAA,EAC5E,IAAI,mBAAmB,8CAA8C,KAAK;AAC5E;;;AC5DA,IAAI,QAAQ,CAAC;AAKN,SAAS,QAAQ;AACtB,UAAQ,CAAC;AACX;AAOO,SAAS,IAAI,MAAM;AACxB,SACE,MAAM,IAAI,KACV,MAAM,KAAK,QAAQ,0CAA0C,SAAS,CAAC,KACvE;AAEJ;AAOO,SAAS,IAAI,MAAM,YAAY;AACpC,QAAM,IAAI,IAAI;AAChB;;;AC3BA,IAAI,aAAa,CAAC;AAKX,SAASC,SAAQ;AACtB,eAAa,CAAC;AAChB;AAUO,SAASC,KAAI,QAAQ,aAAa,aAAa;AACpD,QAAM,aAAa,OAAO,QAAQ;AAClC,QAAM,kBAAkB,YAAY,QAAQ;AAC5C,MAAI,EAAE,cAAc,aAAa;AAC/B,eAAW,UAAU,IAAI,CAAC;AAAA,EAC5B;AACA,aAAW,UAAU,EAAE,eAAe,IAAI;AAC5C;AA4BO,SAASC,KAAI,YAAY,iBAAiB;AAC/C,MAAIC;AACJ,MAAI,cAAc,cAAc,mBAAmB,WAAW,UAAU,GAAG;AACzE,IAAAA,aAAY,WAAW,UAAU,EAAE,eAAe;AAAA,EACpD;AACA,SAAOA;AACT;;;ACzDO,SAAS,UAAU,QAAQ,OAAO,WAAW;AAClD,QAAM,eACJ,cAAc,SAAY,OAAO,QAAQ,SAAS,IAAI,KAAK;AAC7D,MAAI,UAAU,aAAa,QAAQ,GAAG;AACtC,YAAU,YAAY,KAAK,aAAa,SAAS;AACjD,SAAO,UAAU,QACb,eACA,IAAI,MAAM,IAAI,QAAQ,OAAO,EAAE,KAAK,GAAG,IAAI;AACjD;AAQO,SAAS,gBAAgB,IAAI,IAAI;AACtC,QAAM,MAAM,KAAK,IAAI,MAAM,GAAG;AAC9B,QAAM,MAAM,KAAK,IAAI,MAAM,GAAG;AAE9B,WAAS,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,QAAQ,GAAG,MAAM,GAAG,KAAK;AACvD,UAAM,KAAK,SAAS,GAAG,CAAC,KAAK,KAAK,EAAE;AACpC,UAAM,KAAK,SAAS,GAAG,CAAC,KAAK,KAAK,EAAE;AAEpC,QAAI,KAAK,IAAI;AACX,aAAO;AAAA,IACT;AACA,QAAI,KAAK,IAAI;AACX,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACHO,SAASC,KAAI,YAAY,OAAO;AACrC,aAAW,CAAC,KAAK,CAAC,MAAM,CAAC;AACzB,aAAW,CAAC,KAAK,CAAC,MAAM,CAAC;AACzB,SAAO;AACT;AASO,SAAS,gBAAgB,YAAY,QAAQ;AAClD,QAAM,IAAI,OAAO,UAAU;AAC3B,QAAM,SAAS,OAAO,UAAU;AAChC,QAAM,KAAK,OAAO,CAAC;AACnB,QAAM,KAAK,OAAO,CAAC;AACnB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AAEvB,MAAI,KAAK,KAAK;AACd,QAAM,KAAK,KAAK;AAChB,MAAI,OAAO,KAAK,OAAO,GAAG;AACxB,SAAK;AAAA,EACP;AACA,QAAM,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAErC,QAAM,IAAI,KAAM,IAAI,KAAM;AAC1B,QAAM,IAAI,KAAM,IAAI,KAAM;AAE1B,SAAO,CAAC,GAAG,CAAC;AACd;AAcO,SAAS,iBAAiB,YAAY,SAAS;AACpD,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,QAAQ,QAAQ,CAAC;AACvB,QAAM,MAAM,QAAQ,CAAC;AACrB,QAAM,KAAK,MAAM,CAAC;AAClB,QAAM,KAAK,MAAM,CAAC;AAClB,QAAM,KAAK,IAAI,CAAC;AAChB,QAAM,KAAK,IAAI,CAAC;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,QACJ,OAAO,KAAK,OAAO,IACf,KACC,MAAM,KAAK,MAAM,MAAM,KAAK,QAAQ,KAAK,KAAK,KAAK,MAAM;AAChE,MAAI,GAAG;AACP,MAAI,SAAS,GAAG;AACd,QAAI;AACJ,QAAI;AAAA,EACN,WAAW,SAAS,GAAG;AACrB,QAAI;AACJ,QAAI;AAAA,EACN,OAAO;AACL,QAAI,KAAK,QAAQ;AACjB,QAAI,KAAK,QAAQ;AAAA,EACnB;AACA,SAAO,CAAC,GAAG,CAAC;AACd;AAiDO,SAAS,oBAAoB,aAAa,SAAS,gBAAgB;AACxE,QAAM,oBAAoB,OAAO,UAAU,KAAK,GAAG,IAAI;AACvD,QAAM,IAAI,KAAK,IAAI,OAAO,iBAAiB;AAC3C,QAAM,WAAW,kBAAkB;AAEnC,MAAI,MAAM,KAAK,MAAM,IAAI,IAAI;AAC7B,MAAI,MAAM,KAAK,OAAO,IAAI,MAAM,QAAQ,EAAE;AAC1C,MAAI,MAAM,QAAQ,IAAI,MAAM,OAAO,MAAM,IAAI,QAAQ;AAErD,MAAI,OAAO,IAAI;AACb,UAAM;AACN,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,IAAI;AACb,UAAM;AACN,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM;AACjB,MAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B,YAAQ,MAAM,UAAU,KAAK,CAAC,IAAI;AAAA,EACpC;AACA,MAAI,QAAQ,GAAG;AACb,YAAQ,MAAM,UAAU,KAAK,GAAG,QAAQ,IAAI;AAAA,EAC9C;AACA,MAAI,sBAAsB,GAAG;AAC3B,YAAQ,MAAM,YAAY,OAAO,oBAAoB,IAAI,IAAI,CAAC;AAAA,EAChE;AAEA,SAAO;AACT;AA+CO,SAAS,OAAO,aAAa,aAAa;AAC/C,MAAIC,UAAS;AACb,WAAS,IAAI,YAAY,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAChD,QAAI,YAAY,CAAC,KAAK,YAAY,CAAC,GAAG;AACpC,MAAAA,UAAS;AACT;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;AAoBO,SAAS,OAAO,YAAY,OAAO;AACxC,QAAM,WAAW,KAAK,IAAI,KAAK;AAC/B,QAAM,WAAW,KAAK,IAAI,KAAK;AAC/B,QAAM,IAAI,WAAW,CAAC,IAAI,WAAW,WAAW,CAAC,IAAI;AACrD,QAAM,IAAI,WAAW,CAAC,IAAI,WAAW,WAAW,CAAC,IAAI;AACrD,aAAW,CAAC,IAAI;AAChB,aAAW,CAAC,IAAI;AAChB,SAAO;AACT;AAmBO,SAAS,MAAM,YAAYC,QAAO;AACvC,aAAW,CAAC,KAAKA;AACjB,aAAW,CAAC,KAAKA;AACjB,SAAO;AACT;AAOO,SAAS,gBAAgB,QAAQ,QAAQ;AAC9C,QAAM,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC;AAC/B,QAAM,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC;AAC/B,SAAO,KAAK,KAAK,KAAK;AACxB;AAOO,SAAS,SAAS,QAAQ,QAAQ;AACvC,SAAO,KAAK,KAAK,gBAAgB,QAAQ,MAAM,CAAC;AAClD;AAUO,SAAS,yBAAyB,YAAY,SAAS;AAC5D,SAAO,gBAAgB,YAAY,iBAAiB,YAAY,OAAO,CAAC;AAC1E;AA6EO,SAAS,MAAM,YAAY,YAAY;AAC5C,MAAI,WAAW,SAAS,GAAG;AACzB,UAAM,aAAa,SAAS,WAAW,UAAU,CAAC;AAClD,UAAM,aAAa,cAAc,YAAY,YAAY,UAAU;AACnE,QAAI,YAAY;AACd,iBAAW,CAAC,KAAK,aAAa;AAAA,IAChC;AAAA,EACF;AACA,SAAO;AACT;AAOO,SAAS,cAAc,YAAY,YAAY,mBAAmB;AACvE,QAAM,mBAAmB,WAAW,UAAU;AAC9C,MAAI,aAAa;AACjB,MACE,WAAW,SAAS,MACnB,WAAW,CAAC,IAAI,iBAAiB,CAAC,KAAK,WAAW,CAAC,IAAI,iBAAiB,CAAC,IAC1E;AACA,wBAAoB,qBAAqB,SAAS,gBAAgB;AAClE,iBAAa,KAAK;AAAA,OACf,WAAW,CAAC,IAAI,iBAAiB,CAAC,KAAK;AAAA,IAC1C;AAAA,EACF;AACA,SAAO;AACT;;;AC3aA,IAAM,SAAS;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AACR;AAKA,IAAI,QAAQ,OAAO;AAoBZ,SAAS,QAAQ,MAAM;AAC5B,MAAI,QAAQ,OAAO,MAAM;AACvB;AAAA,EACF;AACA,UAAQ,KAAK,GAAG,IAAI;AACtB;AAEO,SAAS,SAAS,MAAM;AAC7B,MAAI,QAAQ,OAAO,OAAO;AACxB;AAAA,EACF;AACA,UAAQ,MAAM,GAAG,IAAI;AACvB;;;AC+CA,IAAI,wBAAwB;AAKrB,SAAS,yBAAyB,SAAS;AAChD,QAAM,OAAO,YAAY,SAAY,OAAO;AAC5C,0BAAwB,CAAC;AAC3B;AAQO,SAAS,eAAe,OAAO,QAAQ;AAC5C,MAAI,WAAW,QAAW;AACxB,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,aAAO,CAAC,IAAI,MAAM,CAAC;AAAA,IACrB;AACA,aAAS;AAAA,EACX,OAAO;AACL,aAAS,MAAM,MAAM;AAAA,EACvB;AACA,SAAO;AACT;AAOO,SAAS,kBAAkB,OAAO,QAAQ;AAC/C,MAAI,WAAW,UAAa,UAAU,QAAQ;AAC5C,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,aAAO,CAAC,IAAI,MAAM,CAAC;AAAA,IACrB;AACA,YAAQ;AAAA,EACV;AACA,SAAO;AACT;AASO,SAAS,cAAc,YAAY;AACxC,MAAQ,WAAW,QAAQ,GAAG,UAAU;AACxC,EAAAC,KAAiB,YAAY,YAAY,cAAc;AACzD;AAKO,SAAS,eAAe,aAAa;AAC1C,cAAY,QAAQ,aAAa;AACnC;AAWO,SAASC,KAAI,gBAAgB;AAClC,SAAO,OAAO,mBAAmB,WAC7B;AAAA;AAAA,IAA+B;AAAA,EAAe;AAAA;AAAA,IACnB,kBAAmB;AAAA;AACpD;AAsBO,SAAS,mBAAmB,YAAY,YAAY,OAAO,OAAO;AACvE,eAAaA,KAAI,UAAU;AAC3B,MAAI;AACJ,QAAM,SAAS,WAAW,uBAAuB;AACjD,MAAI,QAAQ;AACV,sBAAkB,OAAO,YAAY,KAAK;AAC1C,QAAI,SAAS,UAAU,WAAW,SAAS,GAAG;AAC5C,YAAM,gBAAgB,WAAW,iBAAiB;AAClD,UAAI,eAAe;AACjB,0BACG,kBAAkB,gBAAiB,gBAAgB,KAAK;AAAA,MAC7D;AAAA,IACF;AAAA,EACF,OAAO;AACL,UAAM,YAAY,WAAW,SAAS;AACtC,QAAK,aAAa,aAAa,CAAC,SAAU,SAAS,WAAW;AAC5D,wBAAkB;AAAA,IACpB,OAAO;AAIL,YAAMC,cAAa;AAAA,QACjB;AAAA,QACAD,KAAI,WAAW;AAAA,MACjB;AACA,UAAIC,gBAAe,qBAAqB,cAAc,WAAW;AAE/D,0BAAkB,aAAa,WAAW,iBAAiB;AAAA,MAC7D,OAAO;AACL,YAAI,WAAW;AAAA,UACb,MAAM,CAAC,IAAI,aAAa;AAAA,UACxB,MAAM,CAAC;AAAA,UACP,MAAM,CAAC,IAAI,aAAa;AAAA,UACxB,MAAM,CAAC;AAAA,UACP,MAAM,CAAC;AAAA,UACP,MAAM,CAAC,IAAI,aAAa;AAAA,UACxB,MAAM,CAAC;AAAA,UACP,MAAM,CAAC,IAAI,aAAa;AAAA,QAC1B;AACA,mBAAWA,YAAW,UAAU,UAAU,CAAC;AAC3C,cAAM,QAAQ,YAAY,SAAS,MAAM,GAAG,CAAC,GAAG,SAAS,MAAM,GAAG,CAAC,CAAC;AACpE,cAAM,SAAS,YAAY,SAAS,MAAM,GAAG,CAAC,GAAG,SAAS,MAAM,GAAG,CAAC,CAAC;AACrE,2BAAmB,QAAQ,UAAU;AAAA,MACvC;AACA,YAAM,gBAAgB,QAClB,gBAAgB,KAAK,IACrB,WAAW,iBAAiB;AAChC,UAAI,kBAAkB,QAAW;AAC/B,2BAAmB;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AASO,SAAS,yBAAyB,aAAa;AACpD,iBAAe,WAAW;AAC1B,cAAY,QAAQ,SAAU,QAAQ;AACpC,gBAAY,QAAQ,SAAU,aAAa;AACzC,UAAI,WAAW,aAAa;AAC1B,QAAAF,KAAiB,QAAQ,aAAa,cAAc;AAAA,MACtD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAeO,SAAS,wBACd,cACA,cACA,kBACA,kBACA;AACA,eAAa,QAAQ,SAAU,aAAa;AAC1C,iBAAa,QAAQ,SAAU,aAAa;AAC1C,MAAAA,KAAiB,aAAa,aAAa,gBAAgB;AAC3D,MAAAA,KAAiB,aAAa,aAAa,gBAAgB;AAAA,IAC7D,CAAC;AAAA,EACH,CAAC;AACH;AAKO,SAAS,sBAAsB;AACpC,QAAU;AACV,EAAAG,OAAoB;AACtB;AAOO,SAAS,iBAAiB,YAAY,aAAa;AACxD,MAAI,CAAC,YAAY;AACf,WAAOF,KAAI,WAAW;AAAA,EACxB;AACA,MAAI,OAAO,eAAe,UAAU;AAClC,WAAOA,KAAI,UAAU;AAAA,EACvB;AACA;AAAA;AAAA,IAAkC;AAAA;AACpC;AASO,SAAS,uCAAuC,gBAAgB;AACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOE,SAAU,OAAO,QAAQ,WAAW;AAClC,YAAM,SAAS,MAAM;AACrB,kBAAY,cAAc,SAAY,YAAY;AAClD,eAAS,WAAW,SAAY,SAAS,IAAI,MAAM,MAAM;AACzD,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,WAAW;AAC1C,cAAM,QAAQ,eAAe,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC;AAC1D,cAAM,cAAc,MAAM;AAC1B,iBAAS,IAAI,GAAG,KAAK,WAAW,IAAI,IAAI,EAAE,GAAG;AAC3C,iBAAO,IAAI,CAAC,IAAI,KAAK,cAAc,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;AAAA,QAC3D;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAEJ;AAwBO,SAAS,wBAAwB,QAAQ,aAAa,SAAS,SAAS;AAC7E,QAAM,aAAaA,KAAI,MAAM;AAC7B,QAAM,WAAWA,KAAI,WAAW;AAChC,EAAAD;AAAA,IACE;AAAA,IACA;AAAA,IACA,uCAAuC,OAAO;AAAA,EAChD;AACA,EAAAA;AAAA,IACE;AAAA,IACA;AAAA,IACA,uCAAuC,OAAO;AAAA,EAChD;AACF;AAWO,SAAS,WAAW,YAAY,YAAY;AACjD,2BAAyB;AACzB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,eAAe,SAAY,aAAa;AAAA,EAC1C;AACF;AAWO,SAAS,SAAS,YAAY,YAAY;AAC/C,QAAM,SAAS;AAAA,IACb;AAAA,IACA,eAAe,SAAY,aAAa;AAAA,IACxC;AAAA,EACF;AACA,QAAM,MAAM,OAAO,CAAC;AACpB,MAAI,MAAM,QAAQ,MAAM,KAAK;AAC3B,WAAO,CAAC,IAAI,OAAO,MAAM,KAAK,GAAG,IAAI;AAAA,EACvC;AACA,SAAO;AACT;AAYO,SAAS,WAAW,aAAa,aAAa;AACnD,MAAI,gBAAgB,aAAa;AAC/B,WAAO;AAAA,EACT;AACA,QAAM,aAAa,YAAY,SAAS,MAAM,YAAY,SAAS;AACnE,MAAI,YAAY,QAAQ,MAAM,YAAY,QAAQ,GAAG;AACnD,WAAO;AAAA,EACT;AACA,QAAM,gBAAgB,4BAA4B,aAAa,WAAW;AAC1E,SAAO,kBAAkB,kBAAkB;AAC7C;AAWO,SAAS,4BACd,kBACA,uBACA;AACA,QAAM,aAAa,iBAAiB,QAAQ;AAC5C,QAAM,kBAAkB,sBAAsB,QAAQ;AACtD,MAAI,gBAAgBC,KAAiB,YAAY,eAAe;AAChE,MAAI,CAAC,eAAe;AAClB,oBAAgB;AAAA,EAClB;AACA,SAAO;AACT;AAYO,SAAS,aAAa,QAAQ,aAAa;AAChD,QAAM,mBAAmBA,KAAI,MAAM;AACnC,QAAM,wBAAwBA,KAAI,WAAW;AAC7C,SAAO,4BAA4B,kBAAkB,qBAAqB;AAC5E;AAgBO,SAAS,UAAU,YAAY,QAAQ,aAAa;AACzD,QAAM,gBAAgB,aAAa,QAAQ,WAAW;AACtD,SAAO,cAAc,YAAY,QAAW,WAAW,MAAM;AAC/D;AAcO,SAAS,gBAAgB,QAAQ,QAAQ,aAAa,OAAO;AAClE,QAAM,gBAAgB,aAAa,QAAQ,WAAW;AACtD,SAAO,eAAe,QAAQ,eAAe,QAAW,KAAK;AAC/D;AAUO,SAAS,yBACd,OACA,kBACA,uBACA;AACA,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA;AAAA,EACF;AACA,SAAO,cAAc,KAAK;AAC5B;AAKA,IAAI,iBAAiB;AASd,SAAS,kBAAkB,YAAY;AAC5C,mBAAiBA,KAAI,UAAU;AACjC;AAMO,SAAS,sBAAsB;AACpC,mBAAiB;AACnB;AAOO,SAAS,oBAAoB;AAClC,SAAO;AACT;AAQO,SAAS,gBAAgB;AAC9B,oBAAkB,WAAW;AAC/B;AASO,SAAS,iBAAiB,YAAY,kBAAkB;AAC7D,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,SAAO,UAAU,YAAY,kBAAkB,cAAc;AAC/D;AASO,SAAS,mBAAmB,YAAY,gBAAgB;AAC7D,MAAI,CAAC,gBAAgB;AACnB,QACE,yBACA,CAAC,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,KAC1B,WAAW,CAAC,KAAK,QACjB,WAAW,CAAC,KAAK,OACjB,WAAW,CAAC,KAAK,OACjB,WAAW,CAAC,KAAK,IACjB;AACA,8BAAwB;AACxB;AAAA,QACE;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,UAAU,YAAY,gBAAgB,cAAc;AAC7D;AASO,SAAS,aAAa,QAAQ,kBAAkB;AACrD,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,QAAQ,kBAAkB,cAAc;AACjE;AASO,SAAS,eAAe,QAAQ,gBAAgB;AACrD,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,QAAQ,gBAAgB,cAAc;AAC/D;AAUO,SAAS,iBAAiB,YAAY,kBAAkB;AAC7D,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,QAAM,cAAcA,KAAI,gBAAgB,EAAE,SAAS;AACnD,QAAM,YAAY,eAAe,SAAS;AAC1C,SAAO,eAAe,YACjB,aAAa,gBAAgB,WAAW,IAAK,gBAAgB,SAAS,IACvE;AACN;AAUO,SAAS,mBAAmB,YAAY,gBAAgB;AAC7D,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,QAAM,cAAcA,KAAI,cAAc,EAAE,SAAS;AACjD,QAAM,YAAY,eAAe,SAAS;AAC1C,SAAO,eAAe,YACjB,aAAa,gBAAgB,SAAS,IAAK,gBAAgB,WAAW,IACvE;AACN;AAYO,SAAS,8BAA8B,YAAY,UAAUG,YAAW;AAC7E,SAAO,SAAU,OAAO;AACtB,QAAI,aAAa;AACjB,QAAI,WAAW,SAAS,GAAG;AACzB,YAAM,eAAe,WAAW,UAAU;AAC1C,YAAM,oBAAoB,SAAS,YAAY;AAC/C,cAAQ,MAAM,MAAM,CAAC;AACrB,mBAAa,cAAc,OAAO,YAAY,iBAAiB;AAC/D,UAAI,YAAY;AAEd,cAAM,CAAC,IAAI,MAAM,CAAC,IAAI,aAAa;AAAA,MACrC;AACA,YAAM,CAAC,IAAI,MAAM,MAAM,CAAC,GAAG,aAAa,CAAC,GAAG,aAAa,CAAC,CAAC;AAC3D,YAAM,CAAC,IAAI,MAAM,MAAM,CAAC,GAAG,aAAa,CAAC,GAAG,aAAa,CAAC,CAAC;AAC3D,oBAAcA,WAAU,KAAK;AAAA,IAC/B,OAAO;AACL,oBAAcA,WAAU,KAAK;AAAA,IAC/B;AACA,QAAI,cAAc,SAAS,SAAS,GAAG;AAErC,kBAAY,CAAC,KAAK,aAAa,SAAS,SAAS,UAAU,CAAC;AAAA,IAC9D;AACA,WAAO;AAAA,EACT;AACF;AAOO,SAAS,YAAY;AAG1B,2BAAyB,WAAoB;AAC7C,2BAAyBC,YAAoB;AAG7C;AAAA,IACEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,UAAU;", "names": ["RADIUS", "EXTENT", "METERS_PER_UNIT", "PROJECTIONS", "clear", "add", "get", "transform", "add", "equals", "scale", "add", "get", "toEPSG4326", "clear", "transform", "PROJECTIONS"]}