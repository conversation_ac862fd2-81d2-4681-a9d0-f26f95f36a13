/*
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-04-15 10:06:00
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-07-09 15:48:05
 * @FilePath: \uavflight-ui\src\stores\earth.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { defineStore } from 'pinia';
import Earth from '/@/utils/map/earth';
import * as Cesium from 'cesium';
import {
  useDroneControlStore
} from "/@/stores/droneControl/droneControl";
// 不要在这里导入 useMapLayerManagerStore，避免循环依赖
import type { useMapLayerManagerStore } from './mapLayerManager';

const DroneControl = useDroneControlStore();
export const useEarthStore = defineStore({
	// 模块ID
	id: 'earthStore',
	state: () => {
		return {
			viewer: undefined as Cesium.Viewer | undefined,
			earth: undefined as Earth | undefined,
			// 图层管理器引用
			mapLayerManager: null as ReturnType<typeof useMapLayerManagerStore> | null,
		};
	},
	actions: {
		init() {
			// 如果没有初始化 viewer，则不执行后续代码
			if (!this.viewer) return;
			
			try {
				// 禁用选中高亮
				// this.viewer.scene.screenSpaceCameraController.enableInputs = false;
				this.viewer.selectedEntityChanged.addEventListener((entity: Cesium.Entity | undefined) => {
					if (this.viewer) this.viewer.selectedEntity = undefined;
				});
				
				// 初始化地球
				this.earth = new Earth(this.viewer);
				this.viewer.scene.globe.depthTestAgainstTerrain = true;
				
				// 禁用默认在线地形服务，使用椭球体地形提供器
				this.viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider();
				
				// 设置起始点位置
				// const CUMTPosition = Cesium.Cartesian3.fromDegrees(108.329943, 22.766656, 2000); // 经度，纬度，海拔
				const CUMTPosition = Cesium.Cartesian3.fromDegrees(107.910395,22.641764, 2000); // 经度，纬度，海拔
				this.viewer.camera.flyTo({
					destination: CUMTPosition,
					orientation: {
						heading: Cesium.Math.toRadians(10), // 偏航角
						pitch: Cesium.Math.toRadians(-90), // 俯仰角，设置为负值将相机指向地面
						roll: Cesium.Math.toRadians(0), // 滚转角
					},
					duration: 1, // 动画持续时间，单位秒
				});
				
				// 初始化无人机控制
				if (this.viewer) {
					DroneControl.init(this.viewer);
				}

				// 初始化地图图层管理
				// 延迟导入 mapLayerManager，避免循环依赖
				setTimeout(() => {
					import('./mapLayerManager').then(({ useMapLayerManagerStore }) => {
						this.mapLayerManager = useMapLayerManagerStore();
						
						// 使用新的初始化方法，一步完成加载配置和初始化图层
						if (this.viewer) {
							this.mapLayerManager.loadMapConfigAndInitialize(this.viewer)
								.then(success => {
									if (success) {
										console.log('地图配置加载成功并初始化了默认图层');
										
										// 可以显示加载了哪些图层的信息
										console.log('已加载的图层:', this.mapLayerManager?.loadedLayers);
										
										// 这里可以添加其他初始化后的操作
									} else {
										console.warn('地图配置加载失败或初始化失败');
									}
								})
								.catch(error => {
									console.error('加载地图配置并初始化图层失败:', error);
								});
						} else {
							console.warn('Cesium viewer未初始化，无法加载地图图层');
						}
					}).catch(err => {
						console.error('导入 mapLayerManager 失败:', err);
					});
				}, 100);

				// 加载其他图层
				
			} catch (error) {
				console.error('初始化地图出错:', error);
			}
		},
		
		setViewer(viewer: Cesium.Viewer) {
			this.viewer = viewer;
			this.init();
		},
		
		getViewer() {
			if (this.viewer) {
				return this.viewer;
			}
			return undefined;
		},
		
		getEarth() {
			if (this.earth) {
				return this.earth;
			}
			return undefined;
		},

		/**
		 * 添加图层到地图
		 * @param layerId 图层ID
		 * @param force 是否强制添加，即使已加载也重新添加
		 * @returns 返回添加结果和消息
		 */
		addLayer(layerId: string, force: boolean = false): { success: boolean; message: string } {
			if (!this.mapLayerManager) {
				return { success: false, message: '图层管理器未初始化，无法添加图层' };
			}
			return this.mapLayerManager.addLayer(layerId, force);
		},

		/**
		 * 切换指定事件名称的所有图层
		 * @param eventName 事件名称
		 * @param show 是否显示，true为显示，false为隐藏
		 * @returns 操作是否成功
		 */
		toggleEventLayers(eventName: string, show: boolean): boolean {
			if (!this.mapLayerManager) {
				console.error('图层管理器未初始化，无法切换事件图层');
				return false;
			}
			return this.mapLayerManager.toggleEventLayers(eventName, show);
		},

		/**
		 * 从地图中移除图层
		 * @param layerId 图层ID
		 * @returns 是否成功移除图层
		 */
		removeLayer(layerId: string): boolean {
			if (!this.mapLayerManager) {
				console.error('图层管理器未初始化，无法移除图层');
				return false;
			}
			return this.mapLayerManager.removeLayerFromMap(layerId);
		},

		/**
		 * 重置所有图层
		 */
		resetLayers(): void {
			if (!this.mapLayerManager) {
				console.error('图层管理器未初始化，无法重置图层');
				return;
			}
			this.mapLayerManager.resetLayers();
		},

		/**
		 * 获取图层管理器实例
		 * 在特殊情况下，如果需要直接访问图层管理器的更多功能，可以使用此方法
		 * @returns 图层管理器实例
		 */
		getLayerManager() {
			return this.mapLayerManager;
		},
	},
});
