/*
 * @Description: 地图图层样式工具函数
 */
import * as Cesium from 'cesium';
import { LayerStyle, FilterCondition } from './types';
import { loadMapConfigFile } from '/@/utils/mapConfig';

// 样式配置缓存
let styleConfigCache: any = null;

/**
 * 从样式配置中加载样式
 * @param styleName 样式名称
 * @returns 样式对象，如果找不到则返回null
 */
export async function loadStyleFromStyleConfig(styleName: string): Promise<LayerStyle | null> {
	// 如果样式配置未加载，则加载它
	if (styleConfigCache === null) {
		try {
			// 使用新的工具函数加载样式配置
			console.log('正在加载样式配置...');
			styleConfigCache = await loadMapConfigFile('baseStyle');
			console.log('样式配置加载成功:', styleConfigCache);
		} catch (error) {
			console.error('加载样式配置失败:', error);
			return null;
		}
	}

	// 从缓存中查找样式
	if (styleConfigCache && styleConfigCache.styles && styleConfigCache.styles[styleName]) {
		return styleConfigCache.styles[styleName];
	}

	console.warn(`样式 ${styleName} 未在配置中找到`);
	return null;
}

/**
 * 获取样式，支持字符串样式名称或直接的样式对象
 * @param style 样式名称或样式对象
 * @returns Promise<样式对象>
 */
export async function getStyle(style: string | LayerStyle): Promise<LayerStyle | null> {
	if (typeof style === 'string') {
		return await loadStyleFromStyleConfig(style);
	}
	return style;
}

/**
 * 评估过滤条件是否匹配
 * @param properties 实体属性
 * @param filter 过滤条件
 * @returns 是否匹配
 */
export function evaluateFilter(properties: Cesium.PropertyBag, filter: FilterCondition): boolean {
	try {
		// 检查属性是否存在
		if (!properties || !filter || !filter.property) {
			console.log('属性对象或过滤条件无效');
			return false;
		}
		
		if (!properties.hasProperty(filter.property)) {
			console.log(`属性 ${filter.property} 不存在`);
			return false;
		}
		
		// 安全获取属性值 - 正确的调用方式
		let propValue;
		try {
			propValue = properties[filter.property].getValue(Cesium.JulianDate.now());
		} catch (error) {
			console.error(`获取属性 ${filter.property} 值时出错:`, error);
			return false;
		}
		
		if (propValue === undefined || propValue === null) {
			console.log(`属性 ${filter.property} 值为${propValue === undefined ? 'undefined' : 'null'}`);
			return false;
		}
		
		// 简化比较逻辑，避免过多的类型转换和特殊情况处理
		switch (filter.operator) {
			case '=':
				// 尝试数值比较（如果两者都可以转为数字）
				if (!isNaN(Number(propValue)) && !isNaN(Number(filter.value))) {
					return Number(propValue) === Number(filter.value);
				}
				// 否则字符串比较
				return String(propValue) === String(filter.value);
			
			case '!=':
				if (!isNaN(Number(propValue)) && !isNaN(Number(filter.value))) {
					return Number(propValue) !== Number(filter.value);
				}
				return String(propValue) !== String(filter.value);
			
			case '>':
				if (!isNaN(Number(propValue)) && !isNaN(Number(filter.value))) {
					return Number(propValue) > Number(filter.value);
				}
				return String(propValue) > String(filter.value);
			
			case '<':
				if (!isNaN(Number(propValue)) && !isNaN(Number(filter.value))) {
					return Number(propValue) < Number(filter.value);
				}
				return String(propValue) < String(filter.value);
			
			case '>=':
				if (!isNaN(Number(propValue)) && !isNaN(Number(filter.value))) {
					return Number(propValue) >= Number(filter.value);
				}
				return String(propValue) >= String(filter.value);
			
			case '<=':
				if (!isNaN(Number(propValue)) && !isNaN(Number(filter.value))) {
					return Number(propValue) <= Number(filter.value);
				}
				return String(propValue) <= String(filter.value);
			
			case 'between':
				const range = filter.value as [number, number];
				const numValue = Number(propValue);
				return !isNaN(numValue) && numValue >= range[0] && numValue <= range[1];
			
			case 'inRange':
				if (filter.min === undefined || filter.max === undefined) {
					return false;
				}
				const rangeValue = Number(propValue);
				return !isNaN(rangeValue) && rangeValue >= filter.min && rangeValue <= filter.max;
			
			case 'in':
				const valueList = filter.value as (string | number)[];
				// 检查原始值或转换后的值是否在列表中
				return valueList.some(v => 
					v === propValue || 
					(!isNaN(Number(propValue)) && Number(propValue) === Number(v))
				);
			
			case 'notIn':
				const excludeList = filter.value as (string | number)[];
				// 检查原始值或转换后的值是否不在列表中
				return !excludeList.some(v => 
					v === propValue || 
					(!isNaN(Number(propValue)) && Number(propValue) === Number(v))
				);
			
			default:
				console.log(`不支持的操作符: ${filter.operator}`);
				return false;
		}
	} catch (error) {
		console.error(`过滤条件评估出错:`, error);
		return false;
	}
}

/**
 * 应用样式到Cesium实体
 * @param entity Cesium实体
 * @param style 样式对象
 * @param geometryType 几何类型
 */
export function applyStyle(entity: any, style: any, geometryType?: string): void {
	if (!style) return;

	// 如果没有指定几何类型，尝试从实体类型推断
	if (!geometryType) {
		if (entity.point) geometryType = 'Point';
		else if (entity.polyline) geometryType = 'LineString';
		else if (entity.polygon) geometryType = 'Polygon';
	}

	try {
		// 应用点样式
		if ((geometryType?.includes('Point') || entity.point) && style.point) {
			if (!entity.point) {
				entity.point = new Cesium.PointGraphics();
			}

			if (style.point.color !== undefined) {
				entity.point.color = new Cesium.ConstantProperty(
					Cesium.Color.fromCssColorString(style.point.color)
				);
			}

			if (style.point.pixelSize !== undefined) {
				entity.point.pixelSize = new Cesium.ConstantProperty(style.point.pixelSize);
			}

			if (style.point.outlineColor !== undefined) {
				entity.point.outlineColor = new Cesium.ConstantProperty(
					Cesium.Color.fromCssColorString(style.point.outlineColor)
				);
			}

			if (style.point.outlineWidth !== undefined) {
				entity.point.outlineWidth = new Cesium.ConstantProperty(style.point.outlineWidth);
			}

			if (style.point.show !== undefined) {
				entity.point.show = new Cesium.ConstantProperty(style.point.show);
			}
			
			if (style.point.heightReference !== undefined) {
				entity.point.heightReference = new Cesium.ConstantProperty(style.point.heightReference);
			}
		}

		// 应用线样式
		if ((geometryType?.includes('Line') || entity.polyline) && style.polyline) {
			if (!entity.polyline) {
				entity.polyline = new Cesium.PolylineGraphics();
			}

			if (style.polyline.material !== undefined) {
				if (typeof style.polyline.material === 'string') {
					entity.polyline.material = new Cesium.ColorMaterialProperty(
						Cesium.Color.fromCssColorString(style.polyline.material)
					);
				} else if (typeof style.polyline.material === 'object') {
					if (style.polyline.material.color) {
						entity.polyline.material = new Cesium.ColorMaterialProperty(
							Cesium.Color.fromCssColorString(style.polyline.material.color)
						);
					}
				}
			}

			if (style.polyline.width !== undefined) {
				entity.polyline.width = new Cesium.ConstantProperty(style.polyline.width);
			}

			if (style.polyline.clampToGround !== undefined) {
				entity.polyline.clampToGround = new Cesium.ConstantProperty(style.polyline.clampToGround);
			}

			if (style.polyline.show !== undefined) {
				entity.polyline.show = new Cesium.ConstantProperty(style.polyline.show);
			}
			
			if (style.polyline.zIndex !== undefined) {
				entity.polyline.zIndex = new Cesium.ConstantProperty(style.polyline.zIndex);
			}
		}

		// 应用面样式
		if ((geometryType?.includes('Polygon') || entity.polygon) && style.polygon) {
			if (!entity.polygon) {
				entity.polygon = new Cesium.PolygonGraphics();
			}

			if (style.polygon.material !== undefined) {
				if (typeof style.polygon.material === 'string') {
					entity.polygon.material = new Cesium.ColorMaterialProperty(
						Cesium.Color.fromCssColorString(style.polygon.material)
					);
				} else if (typeof style.polygon.material === 'object') {
					if (style.polygon.material.color) {
						entity.polygon.material = new Cesium.ColorMaterialProperty(
							Cesium.Color.fromCssColorString(style.polygon.material.color)
						);
					}
				}
			}

			if (style.polygon.outline !== undefined) {
				entity.polygon.outline = new Cesium.ConstantProperty(style.polygon.outline);
			}

			if (style.polygon.outlineColor !== undefined) {
				entity.polygon.outlineColor = new Cesium.ConstantProperty(
					Cesium.Color.fromCssColorString(style.polygon.outlineColor)
				);
			}

			if (style.polygon.outlineWidth !== undefined) {
				entity.polygon.outlineWidth = new Cesium.ConstantProperty(style.polygon.outlineWidth);
			}

			if (style.polygon.show !== undefined) {
				entity.polygon.show = new Cesium.ConstantProperty(style.polygon.show);
			}
			
			if (style.polygon.height !== undefined) {
				entity.polygon.height = new Cesium.ConstantProperty(style.polygon.height);
			}
			
			if (style.polygon.heightReference !== undefined) {
				entity.polygon.heightReference = new Cesium.ConstantProperty(style.polygon.heightReference);
			}
			
			if (style.polygon.extrudedHeight !== undefined) {
				entity.polygon.extrudedHeight = new Cesium.ConstantProperty(style.polygon.extrudedHeight);
			}
			
			if (style.polygon.perPositionHeight !== undefined) {
				entity.polygon.perPositionHeight = new Cesium.ConstantProperty(style.polygon.perPositionHeight);
			}
			
			if (style.polygon.clampToGround !== undefined) {
				// Polygon没有直接的clampToGround属性，我们通过设置perPositionHeight来实现
				entity.polygon.perPositionHeight = new Cesium.ConstantProperty(!style.polygon.clampToGround);
			}
		}

		// 应用标签样式
		if (style.label) {
			if (!entity.label) {
				entity.label = new Cesium.LabelGraphics();
			}

			if (style.label.text !== undefined) {
				entity.label.text = new Cesium.ConstantProperty(style.label.text);
			}

			if (style.label.font !== undefined) {
				entity.label.font = new Cesium.ConstantProperty(style.label.font);
			}

			if (style.label.style !== undefined) {
				let labelStyle: any = Cesium.LabelStyle.FILL;
				if (style.label.style === 'OUTLINE') {
					labelStyle = Cesium.LabelStyle.OUTLINE;
				} else if (style.label.style === 'FILL_AND_OUTLINE') {
					labelStyle = Cesium.LabelStyle.FILL_AND_OUTLINE;
				}
				entity.label.style = new Cesium.ConstantProperty(labelStyle);
			}

			if (style.label.fillColor !== undefined) {
				entity.label.fillColor = new Cesium.ConstantProperty(
					Cesium.Color.fromCssColorString(style.label.fillColor)
				);
			}

			if (style.label.outlineColor !== undefined) {
				entity.label.outlineColor = new Cesium.ConstantProperty(
					Cesium.Color.fromCssColorString(style.label.outlineColor)
				);
			}

			if (style.label.outlineWidth !== undefined) {
				entity.label.outlineWidth = new Cesium.ConstantProperty(style.label.outlineWidth);
			}

			if (style.label.show !== undefined) {
				entity.label.show = new Cesium.ConstantProperty(style.label.show);
			}
			
			if (style.label.heightReference !== undefined) {
				entity.label.heightReference = new Cesium.ConstantProperty(style.label.heightReference);
			}
		}

		// 应用广告牌样式
		if (style.billboard) {
			if (!entity.billboard) {
				entity.billboard = new Cesium.BillboardGraphics();
			}

			if (style.billboard.image !== undefined) {
				entity.billboard.image = new Cesium.ConstantProperty(style.billboard.image);
			}

			if (style.billboard.width !== undefined) {
				entity.billboard.width = new Cesium.ConstantProperty(style.billboard.width);
			}

			if (style.billboard.height !== undefined) {
				entity.billboard.height = new Cesium.ConstantProperty(style.billboard.height);
			}

			if (style.billboard.scale !== undefined) {
				entity.billboard.scale = new Cesium.ConstantProperty(style.billboard.scale);
			}

			if (style.billboard.rotation !== undefined) {
				entity.billboard.rotation = new Cesium.ConstantProperty(style.billboard.rotation);
			}

			if (style.billboard.show !== undefined) {
				entity.billboard.show = new Cesium.ConstantProperty(style.billboard.show);
			}
			
			if (style.billboard.heightReference !== undefined) {
				entity.billboard.heightReference = new Cesium.ConstantProperty(style.billboard.heightReference);
			}
		}
	} catch (error) {
		console.error('应用样式时出错:', error);
	}
} 