{"version": 3, "sources": ["../../ol/MapEventType.js"], "sourcesContent": ["/**\n * @module ol/MapEventType\n */\n\n/**\n * @enum {string}\n */\nexport default {\n  /**\n   * Triggered after a map frame is rendered.\n   * @event module:ol/MapEvent~MapEvent#postrender\n   * @api\n   */\n  POSTRENDER: 'postrender',\n\n  /**\n   * Triggered when the map starts moving.\n   * @event module:ol/MapEvent~MapEvent#movestart\n   * @api\n   */\n  MOVESTART: 'movestart',\n\n  /**\n   * Triggered after the map is moved.\n   * @event module:ol/MapEvent~MapEvent#moveend\n   * @api\n   */\n  MOVEEND: 'moveend',\n\n  /**\n   * Triggered when loading of additional map data (tiles, images, features) starts.\n   * @event module:ol/MapEvent~MapEvent#loadstart\n   * @api\n   */\n  LOADSTART: 'loadstart',\n\n  /**\n   * Triggered when loading of additional map data has completed.\n   * @event module:ol/MapEvent~MapEvent#loadend\n   * @api\n   */\n  LOADEND: 'loadend',\n};\n\n/***\n * @typedef {'postrender'|'movestart'|'moveend'|'loadstart'|'loadend'} Types\n */\n"], "mappings": ";AAOA,IAAO,uBAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOZ,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOX,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOX,SAAS;AACX;", "names": []}