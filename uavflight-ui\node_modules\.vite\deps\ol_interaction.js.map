{"version": 3, "sources": ["../../ol/interaction/DblClickDragZoom.js", "../../ol/interaction/DragAndDrop.js", "../../ol/interaction/DragRotateAndZoom.js", "../../ol/interaction/Extent.js", "../../ol/interaction/Link.js", "../../ol/interaction/Modify.js", "../../ol/interaction/Select.js", "../../ol/events/SnapEvent.js", "../../ol/interaction/Snap.js", "../../ol/interaction/Translate.js"], "sourcesContent": ["/**\n * @module ol/interaction/DblClickDragZoom\n */\nimport Interaction from './Interaction.js';\nimport MapBrowserEventType from '../MapBrowserEventType.js';\n\n/**\n * @typedef {Object} Options\n * @property {number} [duration=400] Animation duration in milliseconds. *\n * @property {number} [delta=1] The zoom delta applied on move of one pixel. *\n * @property {function(boolean):boolean} [stopDown]\n * Should the down event be propagated to other interactions, or should be\n * stopped?\n */\n\n/**\n * @classdesc\n * Allows the user to zoom the map by double tap/clik then drag up/down\n * with one finger/left mouse.\n * @api\n */\nclass DblClickDragZoom extends Interaction {\n  /**\n   * @param {Options} [opt_options] Options.\n   */\n  constructor(opt_options) {\n    const options = opt_options ? opt_options : {};\n\n    super(\n      /** @type {import(\"./Interaction.js\").InteractionOptions} */ (options)\n    );\n\n    if (options.stopDown) {\n      this.stopDown = options.stopDown;\n    }\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.scaleDeltaByPixel_ = options.delta ? options.delta : 0.01;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.duration_ = options.duration !== undefined ? options.duration : 250;\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.handlingDownUpSequence_ = false;\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.handlingDoubleDownSequence_ = false;\n\n    /**\n     * @type {ReturnType<typeof setTimeout>}\n     * @private\n     */\n    this.doubleTapTimeoutId_ = undefined;\n\n    /**\n     * @type {!Object<string, PointerEvent>}\n     * @private\n     */\n    this.trackedPointers_ = {};\n\n    /**\n     * @type {Array<PointerEvent>}\n     * @protected\n     */\n    this.targetPointers = [];\n  }\n\n  /**\n   * Handles the {@link module:ol/MapBrowserEvent~MapBrowserEvent  map browser event} and may call into\n   * other functions, if event sequences like e.g. 'drag' or 'down-up' etc. are\n   * detected.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n   * @return {boolean} `false` to stop event propagation.\n   * @api\n   */\n  handleEvent(mapBrowserEvent) {\n    if (!mapBrowserEvent.originalEvent) {\n      return true;\n    }\n\n    let stopEvent = false;\n    this.updateTrackedPointers_(mapBrowserEvent);\n    if (this.handlingDownUpSequence_) {\n      if (mapBrowserEvent.type == MapBrowserEventType.POINTERDRAG) {\n        this.handleDragEvent(mapBrowserEvent);\n        // prevent page scrolling during dragging\n        mapBrowserEvent.originalEvent.preventDefault();\n      } else if (mapBrowserEvent.type == MapBrowserEventType.POINTERUP) {\n        const handledUp = this.handleUpEvent(mapBrowserEvent);\n        this.handlingDownUpSequence_ = handledUp;\n      }\n    } else {\n      if (mapBrowserEvent.type == MapBrowserEventType.POINTERDOWN) {\n        if (this.handlingDoubleDownSequence_) {\n          this.handlingDoubleDownSequence_ = false;\n          const handled = this.handleDownEvent(mapBrowserEvent);\n          this.handlingDownUpSequence_ = handled;\n          stopEvent = this.stopDown(handled);\n        } else {\n          stopEvent = this.stopDown(false);\n          this.waitForDblTap_();\n        }\n      }\n    }\n    return !stopEvent;\n  }\n\n  /**\n   * Handle pointer drag events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   */\n  handleDragEvent(mapBrowserEvent) {\n    let scaleDelta = 1.0;\n\n    const touch0 = this.targetPointers[0];\n    const touch1 = this.down_.originalEvent;\n    const distance = touch0.clientY - touch1.clientY;\n\n    if (this.lastDistance_ !== undefined) {\n      scaleDelta =\n        1 - (this.lastDistance_ - distance) * this.scaleDeltaByPixel_;\n    }\n    this.lastDistance_ = distance;\n\n    if (scaleDelta != 1.0) {\n      this.lastScaleDelta_ = scaleDelta;\n    }\n\n    // scale, bypass the resolution constraint\n    const map = mapBrowserEvent.map;\n    const view = map.getView();\n    map.render();\n    view.adjustResolutionInternal(scaleDelta);\n  }\n\n  /**\n   * Handle pointer down events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleDownEvent(mapBrowserEvent) {\n    if (this.targetPointers.length == 1) {\n      const map = mapBrowserEvent.map;\n      this.anchor_ = null;\n      this.lastDistance_ = undefined;\n      this.lastScaleDelta_ = 1;\n      this.down_ = mapBrowserEvent;\n      if (!this.handlingDownUpSequence_) {\n        map.getView().beginInteraction();\n      }\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Handle pointer up events zooming out.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleUpEvent(mapBrowserEvent) {\n    if (this.targetPointers.length == 0) {\n      const map = mapBrowserEvent.map;\n      const view = map.getView();\n      const direction = this.lastScaleDelta_ > 1 ? 1 : -1;\n      view.endInteraction(this.duration_, direction);\n      this.handlingDownUpSequence_ = false;\n      this.handlingDoubleDownSequence_ = false;\n      return false;\n    }\n    return true;\n  }\n\n  /**\n   * This function is used to determine if \"down\" events should be propagated\n   * to other interactions or should be stopped.\n   * @param {boolean} handled Was the event handled by the interaction?\n   * @return {boolean} Should the `down` event be stopped?\n   */\n  stopDown(handled) {\n    return handled;\n  }\n\n  /**\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @private\n   */\n  updateTrackedPointers_(mapBrowserEvent) {\n    if (isPointerDraggingEvent(mapBrowserEvent)) {\n      const event = mapBrowserEvent.originalEvent;\n\n      const id = event.pointerId.toString();\n      if (mapBrowserEvent.type == MapBrowserEventType.POINTERUP) {\n        delete this.trackedPointers_[id];\n      } else if (mapBrowserEvent.type == MapBrowserEventType.POINTERDOWN) {\n        this.trackedPointers_[id] = event;\n      } else if (id in this.trackedPointers_) {\n        // update only when there was a pointerdown event for this pointer\n        this.trackedPointers_[id] = event;\n      }\n      this.targetPointers = Object.values(this.trackedPointers_);\n    }\n  }\n\n  /**\n   * Wait the second double finger tap.\n   * @private\n   */\n  waitForDblTap_() {\n    if (this.doubleTapTimeoutId_ !== undefined) {\n      // double-click\n      clearTimeout(this.doubleTapTimeoutId_);\n      this.doubleTapTimeoutId_ = undefined;\n    } else {\n      this.handlingDoubleDownSequence_ = true;\n      this.doubleTapTimeoutId_ = setTimeout(\n        this.endInteraction_.bind(this),\n        250\n      );\n    }\n  }\n\n  /**\n   * @private\n   */\n  endInteraction_() {\n    this.handlingDoubleDownSequence_ = false;\n    this.doubleTapTimeoutId_ = undefined;\n  }\n}\n\n/**\n * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n * @return {boolean} Whether the event is a pointerdown, pointerdrag\n *     or pointerup event.\n */\nfunction isPointerDraggingEvent(mapBrowserEvent) {\n  const type = mapBrowserEvent.type;\n  return (\n    type === MapBrowserEventType.POINTERDOWN ||\n    type === MapBrowserEventType.POINTERDRAG ||\n    type === MapBrowserEventType.POINTERUP\n  );\n}\n\nexport default DblClickDragZoom;\n", "/**\n * @module ol/interaction/DragAndDrop\n */\n// FIXME should handle all geo-referenced data, not just vector data\n\nimport Event from '../events/Event.js';\nimport EventType from '../events/EventType.js';\nimport Interaction from './Interaction.js';\nimport {TRUE} from '../functions.js';\nimport {get as getProjection, getUserProjection} from '../proj.js';\nimport {listen, unlistenBy<PERSON>ey} from '../events.js';\n\n/**\n * @typedef {Object} Options\n * @property {Array<typeof import(\"../format/Feature.js\").default|import(\"../format/Feature.js\").default>} [formatConstructors] Format constructors\n * (and/or formats pre-constructed with options).\n * @property {import(\"../source/Vector.js\").default} [source] Optional vector source where features will be added.  If a source is provided\n * all existing features will be removed and new features will be added when\n * they are dropped on the target.  If you want to add features to a vector\n * source without removing the existing features (append only), instead of\n * providing the source option listen for the \"addfeatures\" event.\n * @property {import(\"../proj.js\").ProjectionLike} [projection] Target projection. By default, the map's view's projection is used.\n * @property {HTMLElement} [target] The element that is used as the drop target, default is the viewport element.\n */\n\n/**\n * @enum {string}\n */\nconst DragAndDropEventType = {\n  /**\n   * Triggered when features are added\n   * @event DragAndDropEvent#addfeatures\n   * @api\n   */\n  ADD_FEATURES: 'addfeatures',\n};\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/interaction/DragAndDrop~DragAndDrop} instances are instances\n * of this type.\n */\nexport class DragAndDropEvent extends Event {\n  /**\n   * @param {DragAndDropEventType} type Type.\n   * @param {File} file File.\n   * @param {Array<import(\"../Feature.js\").default>} [features] Features.\n   * @param {import(\"../proj/Projection.js\").default} [projection] Projection.\n   */\n  constructor(type, file, features, projection) {\n    super(type);\n\n    /**\n     * The features parsed from dropped data.\n     * @type {Array<import(\"../Feature.js\").FeatureLike>|undefined}\n     * @api\n     */\n    this.features = features;\n\n    /**\n     * The dropped file.\n     * @type {File}\n     * @api\n     */\n    this.file = file;\n\n    /**\n     * The feature projection.\n     * @type {import(\"../proj/Projection.js\").default|undefined}\n     * @api\n     */\n    this.projection = projection;\n  }\n}\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types|\n *     'change:active', import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<'addfeatures', DragAndDropEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types|\n *     'change:active'|'addfeatures', Return>} DragAndDropOnSignature\n */\n\n/**\n * @classdesc\n * Handles input of vector data by drag and drop.\n *\n * @api\n *\n * @fires DragAndDropEvent\n */\nclass DragAndDrop extends Interaction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    super({\n      handleEvent: TRUE,\n    });\n\n    /***\n     * @type {DragAndDropOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {DragAndDropOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {DragAndDropOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.readAsBuffer_ = false;\n\n    /**\n     * @private\n     * @type {Array<import(\"../format/Feature.js\").default>}\n     */\n    this.formats_ = [];\n    const formatConstructors = options.formatConstructors\n      ? options.formatConstructors\n      : [];\n    for (let i = 0, ii = formatConstructors.length; i < ii; ++i) {\n      let format = formatConstructors[i];\n      if (typeof format === 'function') {\n        format = new format();\n      }\n      this.formats_.push(format);\n      this.readAsBuffer_ =\n        this.readAsBuffer_ || format.getType() === 'arraybuffer';\n    }\n\n    /**\n     * @private\n     * @type {import(\"../proj/Projection.js\").default}\n     */\n    this.projection_ = options.projection\n      ? getProjection(options.projection)\n      : null;\n\n    /**\n     * @private\n     * @type {?Array<import(\"../events.js\").EventsKey>}\n     */\n    this.dropListenKeys_ = null;\n\n    /**\n     * @private\n     * @type {import(\"../source/Vector.js\").default}\n     */\n    this.source_ = options.source || null;\n\n    /**\n     * @private\n     * @type {HTMLElement|null}\n     */\n    this.target = options.target ? options.target : null;\n  }\n\n  /**\n   * @param {File} file File.\n   * @param {Event} event Load event.\n   * @private\n   */\n  handleResult_(file, event) {\n    const result = event.target.result;\n    const map = this.getMap();\n    let projection = this.projection_;\n    if (!projection) {\n      projection = getUserProjection();\n      if (!projection) {\n        const view = map.getView();\n        projection = view.getProjection();\n      }\n    }\n\n    let text;\n    const formats = this.formats_;\n    for (let i = 0, ii = formats.length; i < ii; ++i) {\n      const format = formats[i];\n      let input = result;\n      if (this.readAsBuffer_ && format.getType() !== 'arraybuffer') {\n        if (text === undefined) {\n          text = new TextDecoder().decode(result);\n        }\n        input = text;\n      }\n      const features = this.tryReadFeatures_(format, input, {\n        featureProjection: projection,\n      });\n      if (features && features.length > 0) {\n        if (this.source_) {\n          this.source_.clear();\n          this.source_.addFeatures(features);\n        }\n        this.dispatchEvent(\n          new DragAndDropEvent(\n            DragAndDropEventType.ADD_FEATURES,\n            file,\n            features,\n            projection\n          )\n        );\n        break;\n      }\n    }\n  }\n\n  /**\n   * @private\n   */\n  registerListeners_() {\n    const map = this.getMap();\n    if (map) {\n      const dropArea = this.target ? this.target : map.getViewport();\n      this.dropListenKeys_ = [\n        listen(dropArea, EventType.DROP, this.handleDrop, this),\n        listen(dropArea, EventType.DRAGENTER, this.handleStop, this),\n        listen(dropArea, EventType.DRAGOVER, this.handleStop, this),\n        listen(dropArea, EventType.DROP, this.handleStop, this),\n      ];\n    }\n  }\n\n  /**\n   * Activate or deactivate the interaction.\n   * @param {boolean} active Active.\n   * @observable\n   * @api\n   */\n  setActive(active) {\n    if (!this.getActive() && active) {\n      this.registerListeners_();\n    }\n    if (this.getActive() && !active) {\n      this.unregisterListeners_();\n    }\n    super.setActive(active);\n  }\n\n  /**\n   * Remove the interaction from its current map and attach it to the new map.\n   * Subclasses may set up event handlers to get notified about changes to\n   * the map here.\n   * @param {import(\"../Map.js\").default} map Map.\n   */\n  setMap(map) {\n    this.unregisterListeners_();\n    super.setMap(map);\n    if (this.getActive()) {\n      this.registerListeners_();\n    }\n  }\n\n  /**\n   * @param {import(\"../format/Feature.js\").default} format Format.\n   * @param {string} text Text.\n   * @param {import(\"../format/Feature.js\").ReadOptions} options Read options.\n   * @private\n   * @return {Array<import(\"../Feature.js\").default>} Features.\n   */\n  tryReadFeatures_(format, text, options) {\n    try {\n      return (\n        /** @type {Array<import(\"../Feature.js\").default>} */\n        (format.readFeatures(text, options))\n      );\n    } catch (e) {\n      return null;\n    }\n  }\n\n  /**\n   * @private\n   */\n  unregisterListeners_() {\n    if (this.dropListenKeys_) {\n      this.dropListenKeys_.forEach(unlistenByKey);\n      this.dropListenKeys_ = null;\n    }\n  }\n\n  /**\n   * @param {DragEvent} event Event.\n   */\n  handleDrop(event) {\n    const files = event.dataTransfer.files;\n    for (let i = 0, ii = files.length; i < ii; ++i) {\n      const file = files.item(i);\n      const reader = new FileReader();\n      reader.addEventListener(\n        EventType.LOAD,\n        this.handleResult_.bind(this, file)\n      );\n      if (this.readAsBuffer_) {\n        reader.readAsArrayBuffer(file);\n      } else {\n        reader.readAsText(file);\n      }\n    }\n  }\n\n  /**\n   * @param {DragEvent} event Event.\n   */\n  handleStop(event) {\n    event.stopPropagation();\n    event.preventDefault();\n    event.dataTransfer.dropEffect = 'copy';\n  }\n}\n\nexport default DragAndDrop;\n", "/**\n * @module ol/interaction/DragRotateAndZoom\n */\nimport PointerInteraction from './Pointer.js';\nimport {mouseOnly, shiftKeyOnly} from '../events/condition.js';\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../events/condition.js\").Condition} [condition] A function that\n * takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * boolean to indicate whether that event should be handled.\n * Default is {@link module:ol/events/condition.shiftKeyOnly}.\n * @property {number} [duration=400] Animation duration in milliseconds.\n */\n\n/**\n * @classdesc\n * Allows the user to zoom and rotate the map by clicking and dragging\n * on the map.  By default, this interaction is limited to when the shift\n * key is held down.\n *\n * This interaction is only supported for mouse devices.\n *\n * And this interaction is not included in the default interactions.\n * @api\n */\nclass DragRotateAndZoom extends PointerInteraction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    super(/** @type {import(\"./Pointer.js\").Options} */ (options));\n\n    /**\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.condition_ = options.condition ? options.condition : shiftKeyOnly;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.lastAngle_ = undefined;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.lastMagnitude_ = undefined;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.lastScaleDelta_ = 0;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.duration_ = options.duration !== undefined ? options.duration : 400;\n  }\n\n  /**\n   * Handle pointer drag events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   */\n  handleDragEvent(mapBrowserEvent) {\n    if (!mouseOnly(mapBrowserEvent)) {\n      return;\n    }\n\n    const map = mapBrowserEvent.map;\n    const size = map.getSize();\n    const offset = mapBrowserEvent.pixel;\n    const deltaX = offset[0] - size[0] / 2;\n    const deltaY = size[1] / 2 - offset[1];\n    const theta = Math.atan2(deltaY, deltaX);\n    const magnitude = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n    const view = map.getView();\n    if (this.lastAngle_ !== undefined) {\n      const angleDelta = this.lastAngle_ - theta;\n      view.adjustRotationInternal(angleDelta);\n    }\n    this.lastAngle_ = theta;\n    if (this.lastMagnitude_ !== undefined) {\n      view.adjustResolutionInternal(this.lastMagnitude_ / magnitude);\n    }\n    if (this.lastMagnitude_ !== undefined) {\n      this.lastScaleDelta_ = this.lastMagnitude_ / magnitude;\n    }\n    this.lastMagnitude_ = magnitude;\n  }\n\n  /**\n   * Handle pointer up events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleUpEvent(mapBrowserEvent) {\n    if (!mouseOnly(mapBrowserEvent)) {\n      return true;\n    }\n\n    const map = mapBrowserEvent.map;\n    const view = map.getView();\n    const direction = this.lastScaleDelta_ > 1 ? 1 : -1;\n    view.endInteraction(this.duration_, direction);\n    this.lastScaleDelta_ = 0;\n    return false;\n  }\n\n  /**\n   * Handle pointer down events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleDownEvent(mapBrowserEvent) {\n    if (!mouseOnly(mapBrowserEvent)) {\n      return false;\n    }\n\n    if (this.condition_(mapBrowserEvent)) {\n      mapBrowserEvent.map.getView().beginInteraction();\n      this.lastAngle_ = undefined;\n      this.lastMagnitude_ = undefined;\n      return true;\n    }\n    return false;\n  }\n}\n\nexport default DragRotateAndZoom;\n", "/**\n * @module ol/interaction/Extent\n */\nimport Event from '../events/Event.js';\nimport Feature from '../Feature.js';\nimport MapBrowserEventType from '../MapBrowserEventType.js';\nimport Point from '../geom/Point.js';\nimport PointerInteraction from './Pointer.js';\nimport VectorLayer from '../layer/Vector.js';\nimport VectorSource from '../source/Vector.js';\nimport {always} from '../events/condition.js';\nimport {boundingExtent, getArea} from '../extent.js';\nimport {\n  closestOnSegment,\n  distance as coordinateDistance,\n  squaredDistance as squaredCoordinateDistance,\n  squaredDistanceToSegment,\n} from '../coordinate.js';\nimport {createEditingStyle} from '../style/Style.js';\nimport {fromExtent as polygonFromExtent} from '../geom/Polygon.js';\nimport {toUserExtent} from '../proj.js';\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../events/condition.js\").Condition} [condition] A function that\n * takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * boolean to indicate whether that event should be handled.\n * Default is {@link module:ol/events/condition.always}.\n * @property {import(\"../extent.js\").Extent} [extent] Initial extent. Defaults to no\n * initial extent.\n * @property {import(\"../style/Style.js\").StyleLike} [boxStyle]\n * Style for the drawn extent box. Defaults to the `Polygon` editing style\n * documented in {@link module:ol/style/Style~Style}\n * @property {number} [pixelTolerance=10] Pixel tolerance for considering the\n * pointer close enough to a segment or vertex for editing.\n * @property {import(\"../style/Style.js\").StyleLike} [pointerStyle]\n * Style for the cursor used to draw the extent. Defaults to the `Point` editing style\n * documented in {@link module:ol/style/Style~Style}\n * @property {boolean} [wrapX=false] Wrap the drawn extent across multiple maps\n * in the X direction? Only affects visuals, not functionality.\n */\n\n/**\n * @enum {string}\n */\nconst ExtentEventType = {\n  /**\n   * Triggered after the extent is changed\n   * @event ExtentEvent#extentchanged\n   * @api\n   */\n  EXTENTCHANGED: 'extentchanged',\n};\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/interaction/Extent~Extent} instances are\n * instances of this type.\n */\nexport class ExtentEvent extends Event {\n  /**\n   * @param {import(\"../extent.js\").Extent} extent the new extent\n   */\n  constructor(extent) {\n    super(ExtentEventType.EXTENTCHANGED);\n\n    /**\n     * The current extent.\n     * @type {import(\"../extent.js\").Extent}\n     * @api\n     */\n    this.extent = extent;\n  }\n}\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types|\n *     'change:active', import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<'extentchanged', ExtentEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types|\n *     'change:active'|'extentchanged', Return>} ExtentOnSignature\n */\n\n/**\n * @classdesc\n * Allows the user to draw a vector box by clicking and dragging on the map.\n * Once drawn, the vector box can be modified by dragging its vertices or edges.\n * This interaction is only supported for mouse devices.\n *\n * @fires ExtentEvent\n * @api\n */\nclass Extent extends PointerInteraction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options || {};\n\n    super(/** @type {import(\"./Pointer.js\").Options} */ (options));\n\n    /***\n     * @type {ExtentOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {ExtentOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {ExtentOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * Condition\n     * @type {import(\"../events/condition.js\").Condition}\n     * @private\n     */\n    this.condition_ = options.condition ? options.condition : always;\n\n    /**\n     * Extent of the drawn box\n     * @type {import(\"../extent.js\").Extent}\n     * @private\n     */\n    this.extent_ = null;\n\n    /**\n     * Handler for pointer move events\n     * @type {function (import(\"../coordinate.js\").Coordinate): import(\"../extent.js\").Extent|null}\n     * @private\n     */\n    this.pointerHandler_ = null;\n\n    /**\n     * Pixel threshold to snap to extent\n     * @type {number}\n     * @private\n     */\n    this.pixelTolerance_ =\n      options.pixelTolerance !== undefined ? options.pixelTolerance : 10;\n\n    /**\n     * Is the pointer snapped to an extent vertex\n     * @type {boolean}\n     * @private\n     */\n    this.snappedToVertex_ = false;\n\n    /**\n     * Feature for displaying the visible extent\n     * @type {Feature}\n     * @private\n     */\n    this.extentFeature_ = null;\n\n    /**\n     * Feature for displaying the visible pointer\n     * @type {Feature<Point>}\n     * @private\n     */\n    this.vertexFeature_ = null;\n\n    if (!options) {\n      options = {};\n    }\n\n    /**\n     * Layer for the extentFeature\n     * @type {VectorLayer}\n     * @private\n     */\n    this.extentOverlay_ = new VectorLayer({\n      source: new VectorSource({\n        useSpatialIndex: false,\n        wrapX: !!options.wrapX,\n      }),\n      style: options.boxStyle\n        ? options.boxStyle\n        : getDefaultExtentStyleFunction(),\n      updateWhileAnimating: true,\n      updateWhileInteracting: true,\n    });\n\n    /**\n     * Layer for the vertexFeature\n     * @type {VectorLayer}\n     * @private\n     */\n    this.vertexOverlay_ = new VectorLayer({\n      source: new VectorSource({\n        useSpatialIndex: false,\n        wrapX: !!options.wrapX,\n      }),\n      style: options.pointerStyle\n        ? options.pointerStyle\n        : getDefaultPointerStyleFunction(),\n      updateWhileAnimating: true,\n      updateWhileInteracting: true,\n    });\n\n    if (options.extent) {\n      this.setExtent(options.extent);\n    }\n  }\n\n  /**\n   * @param {import(\"../pixel.js\").Pixel} pixel cursor location\n   * @param {import(\"../Map.js\").default} map map\n   * @return {import(\"../coordinate.js\").Coordinate|null} snapped vertex on extent\n   * @private\n   */\n  snapToVertex_(pixel, map) {\n    const pixelCoordinate = map.getCoordinateFromPixelInternal(pixel);\n    const sortByDistance = function (a, b) {\n      return (\n        squaredDistanceToSegment(pixelCoordinate, a) -\n        squaredDistanceToSegment(pixelCoordinate, b)\n      );\n    };\n    const extent = this.getExtentInternal();\n    if (extent) {\n      //convert extents to line segments and find the segment closest to pixelCoordinate\n      const segments = getSegments(extent);\n      segments.sort(sortByDistance);\n      const closestSegment = segments[0];\n\n      let vertex = closestOnSegment(pixelCoordinate, closestSegment);\n      const vertexPixel = map.getPixelFromCoordinateInternal(vertex);\n\n      //if the distance is within tolerance, snap to the segment\n      if (coordinateDistance(pixel, vertexPixel) <= this.pixelTolerance_) {\n        //test if we should further snap to a vertex\n        const pixel1 = map.getPixelFromCoordinateInternal(closestSegment[0]);\n        const pixel2 = map.getPixelFromCoordinateInternal(closestSegment[1]);\n        const squaredDist1 = squaredCoordinateDistance(vertexPixel, pixel1);\n        const squaredDist2 = squaredCoordinateDistance(vertexPixel, pixel2);\n        const dist = Math.sqrt(Math.min(squaredDist1, squaredDist2));\n        this.snappedToVertex_ = dist <= this.pixelTolerance_;\n        if (this.snappedToVertex_) {\n          vertex =\n            squaredDist1 > squaredDist2 ? closestSegment[1] : closestSegment[0];\n        }\n        return vertex;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent pointer move event\n   * @private\n   */\n  handlePointerMove_(mapBrowserEvent) {\n    const pixel = mapBrowserEvent.pixel;\n    const map = mapBrowserEvent.map;\n\n    let vertex = this.snapToVertex_(pixel, map);\n    if (!vertex) {\n      vertex = map.getCoordinateFromPixelInternal(pixel);\n    }\n    this.createOrUpdatePointerFeature_(vertex);\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} extent extent\n   * @return {Feature} extent as featrue\n   * @private\n   */\n  createOrUpdateExtentFeature_(extent) {\n    let extentFeature = this.extentFeature_;\n\n    if (!extentFeature) {\n      if (!extent) {\n        extentFeature = new Feature({});\n      } else {\n        extentFeature = new Feature(polygonFromExtent(extent));\n      }\n      this.extentFeature_ = extentFeature;\n      this.extentOverlay_.getSource().addFeature(extentFeature);\n    } else {\n      if (!extent) {\n        extentFeature.setGeometry(undefined);\n      } else {\n        extentFeature.setGeometry(polygonFromExtent(extent));\n      }\n    }\n    return extentFeature;\n  }\n\n  /**\n   * @param {import(\"../coordinate.js\").Coordinate} vertex location of feature\n   * @return {Feature} vertex as feature\n   * @private\n   */\n  createOrUpdatePointerFeature_(vertex) {\n    let vertexFeature = this.vertexFeature_;\n    if (!vertexFeature) {\n      vertexFeature = new Feature(new Point(vertex));\n      this.vertexFeature_ = vertexFeature;\n      this.vertexOverlay_.getSource().addFeature(vertexFeature);\n    } else {\n      const geometry = vertexFeature.getGeometry();\n      geometry.setCoordinates(vertex);\n    }\n    return vertexFeature;\n  }\n\n  /**\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n   * @return {boolean} `false` to stop event propagation.\n   */\n  handleEvent(mapBrowserEvent) {\n    if (!mapBrowserEvent.originalEvent || !this.condition_(mapBrowserEvent)) {\n      return true;\n    }\n    //display pointer (if not dragging)\n    if (\n      mapBrowserEvent.type == MapBrowserEventType.POINTERMOVE &&\n      !this.handlingDownUpSequence\n    ) {\n      this.handlePointerMove_(mapBrowserEvent);\n    }\n    //call pointer to determine up/down/drag\n    super.handleEvent(mapBrowserEvent);\n    //return false to stop propagation\n    return false;\n  }\n\n  /**\n   * Handle pointer down events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleDownEvent(mapBrowserEvent) {\n    const pixel = mapBrowserEvent.pixel;\n    const map = mapBrowserEvent.map;\n\n    const extent = this.getExtentInternal();\n    let vertex = this.snapToVertex_(pixel, map);\n\n    //find the extent corner opposite the passed corner\n    const getOpposingPoint = function (point) {\n      let x_ = null;\n      let y_ = null;\n      if (point[0] == extent[0]) {\n        x_ = extent[2];\n      } else if (point[0] == extent[2]) {\n        x_ = extent[0];\n      }\n      if (point[1] == extent[1]) {\n        y_ = extent[3];\n      } else if (point[1] == extent[3]) {\n        y_ = extent[1];\n      }\n      if (x_ !== null && y_ !== null) {\n        return [x_, y_];\n      }\n      return null;\n    };\n    if (vertex && extent) {\n      const x =\n        vertex[0] == extent[0] || vertex[0] == extent[2] ? vertex[0] : null;\n      const y =\n        vertex[1] == extent[1] || vertex[1] == extent[3] ? vertex[1] : null;\n\n      //snap to point\n      if (x !== null && y !== null) {\n        this.pointerHandler_ = getPointHandler(getOpposingPoint(vertex));\n        //snap to edge\n      } else if (x !== null) {\n        this.pointerHandler_ = getEdgeHandler(\n          getOpposingPoint([x, extent[1]]),\n          getOpposingPoint([x, extent[3]])\n        );\n      } else if (y !== null) {\n        this.pointerHandler_ = getEdgeHandler(\n          getOpposingPoint([extent[0], y]),\n          getOpposingPoint([extent[2], y])\n        );\n      }\n      //no snap - new bbox\n    } else {\n      vertex = map.getCoordinateFromPixelInternal(pixel);\n      this.setExtent([vertex[0], vertex[1], vertex[0], vertex[1]]);\n      this.pointerHandler_ = getPointHandler(vertex);\n    }\n    return true; //event handled; start downup sequence\n  }\n\n  /**\n   * Handle pointer drag events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   */\n  handleDragEvent(mapBrowserEvent) {\n    if (this.pointerHandler_) {\n      const pixelCoordinate = mapBrowserEvent.coordinate;\n      this.setExtent(this.pointerHandler_(pixelCoordinate));\n      this.createOrUpdatePointerFeature_(pixelCoordinate);\n    }\n  }\n\n  /**\n   * Handle pointer up events.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleUpEvent(mapBrowserEvent) {\n    this.pointerHandler_ = null;\n    //If bbox is zero area, set to null;\n    const extent = this.getExtentInternal();\n    if (!extent || getArea(extent) === 0) {\n      this.setExtent(null);\n    }\n    return false; //Stop handling downup sequence\n  }\n\n  /**\n   * Remove the interaction from its current map and attach it to the new map.\n   * Subclasses may set up event handlers to get notified about changes to\n   * the map here.\n   * @param {import(\"../Map.js\").default} map Map.\n   */\n  setMap(map) {\n    this.extentOverlay_.setMap(map);\n    this.vertexOverlay_.setMap(map);\n    super.setMap(map);\n  }\n\n  /**\n   * Returns the current drawn extent in the view projection (or user projection if set)\n   *\n   * @return {import(\"../extent.js\").Extent} Drawn extent in the view projection.\n   * @api\n   */\n  getExtent() {\n    return toUserExtent(\n      this.getExtentInternal(),\n      this.getMap().getView().getProjection()\n    );\n  }\n\n  /**\n   * Returns the current drawn extent in the view projection\n   *\n   * @return {import(\"../extent.js\").Extent} Drawn extent in the view projection.\n   * @api\n   */\n  getExtentInternal() {\n    return this.extent_;\n  }\n\n  /**\n   * Manually sets the drawn extent, using the view projection.\n   *\n   * @param {import(\"../extent.js\").Extent} extent Extent\n   * @api\n   */\n  setExtent(extent) {\n    //Null extent means no bbox\n    this.extent_ = extent ? extent : null;\n    this.createOrUpdateExtentFeature_(extent);\n    this.dispatchEvent(new ExtentEvent(this.extent_));\n  }\n}\n\n/**\n * Returns the default style for the drawn bbox\n *\n * @return {import(\"../style/Style.js\").StyleFunction} Default Extent style\n */\nfunction getDefaultExtentStyleFunction() {\n  const style = createEditingStyle();\n  return function (feature, resolution) {\n    return style['Polygon'];\n  };\n}\n\n/**\n * Returns the default style for the pointer\n *\n * @return {import(\"../style/Style.js\").StyleFunction} Default pointer style\n */\nfunction getDefaultPointerStyleFunction() {\n  const style = createEditingStyle();\n  return function (feature, resolution) {\n    return style['Point'];\n  };\n}\n\n/**\n * @param {import(\"../coordinate.js\").Coordinate} fixedPoint corner that will be unchanged in the new extent\n * @return {function (import(\"../coordinate.js\").Coordinate): import(\"../extent.js\").Extent} event handler\n */\nfunction getPointHandler(fixedPoint) {\n  return function (point) {\n    return boundingExtent([fixedPoint, point]);\n  };\n}\n\n/**\n * @param {import(\"../coordinate.js\").Coordinate} fixedP1 first corner that will be unchanged in the new extent\n * @param {import(\"../coordinate.js\").Coordinate} fixedP2 second corner that will be unchanged in the new extent\n * @return {function (import(\"../coordinate.js\").Coordinate): import(\"../extent.js\").Extent|null} event handler\n */\nfunction getEdgeHandler(fixedP1, fixedP2) {\n  if (fixedP1[0] == fixedP2[0]) {\n    return function (point) {\n      return boundingExtent([fixedP1, [point[0], fixedP2[1]]]);\n    };\n  }\n  if (fixedP1[1] == fixedP2[1]) {\n    return function (point) {\n      return boundingExtent([fixedP1, [fixedP2[0], point[1]]]);\n    };\n  }\n  return null;\n}\n\n/**\n * @param {import(\"../extent.js\").Extent} extent extent\n * @return {Array<Array<import(\"../coordinate.js\").Coordinate>>} extent line segments\n */\nfunction getSegments(extent) {\n  return [\n    [\n      [extent[0], extent[1]],\n      [extent[0], extent[3]],\n    ],\n    [\n      [extent[0], extent[3]],\n      [extent[2], extent[3]],\n    ],\n    [\n      [extent[2], extent[3]],\n      [extent[2], extent[1]],\n    ],\n    [\n      [extent[2], extent[1]],\n      [extent[0], extent[1]],\n    ],\n  ];\n}\n\nexport default Extent;\n", "/**\n * @module ol/interaction/Link\n */\nimport EventType from '../events/EventType.js';\nimport Interaction from './Interaction.js';\nimport MapEventType from '../MapEventType.js';\nimport {listen, unlistenBy<PERSON>ey} from '../events.js';\nimport {toFixed} from '../math.js';\n\n/**\n * @param {number} number A number.\n * @return {number} A number with at most 5 decimal places.\n */\nfunction to5(number) {\n  return toFixed(number, 5);\n}\n\n/**\n * @param {string} string A string.\n * @return {number} A number representing the string.\n */\nfunction readNumber(string) {\n  return parseFloat(string);\n}\n\n/**\n * @param {number} number A number.\n * @return {string} A string representing the number.\n */\nfunction writeNumber(number) {\n  return to5(number).toString();\n}\n\n/**\n * @param {number} a A number.\n * @param {number} b A number.\n * @return {boolean} The numbers are different.\n */\nfunction differentNumber(a, b) {\n  if (isNaN(a)) {\n    return false;\n  }\n  return a !== readNumber(writeNumber(b));\n}\n\n/**\n * @param {Array<number>} a An array of two numbers.\n * @param {Array<number>} b An array of two numbers.\n * @return {boolean} The arrays are different.\n */\nfunction differentArray(a, b) {\n  return differentNumber(a[0], b[0]) || differentNumber(a[1], b[1]);\n}\n\n/** @typedef {'x'|'y'|'z'|'r'|'l'} Params */\n\n/**\n * @typedef {Object} Options\n * @property {boolean|import('../View.js').AnimationOptions} [animate=true] Animate view transitions.\n * @property {Array<Params>} [params=['x', 'y', 'z', 'r', 'l']] Properties to track. Default is to track\n * `x` (center x), `y` (center y), `z` (zoom), `r` (rotation) and `l` (layers).\n * @property {boolean} [replace=false] Replace the current URL without creating the new entry in browser history.\n * By default, changes in the map state result in a new entry being added to the browser history.\n * @property {string} [prefix=''] By default, the URL will be updated with search parameters x, y, z, and r.  To\n * avoid collisions with existing search parameters that your application uses, you can supply a custom prefix for\n * the ones used by this interaction (e.g. 'ol:').\n */\n\n/**\n * @classdesc\n * An interaction that synchronizes the map state with the URL.\n *\n * @api\n */\nclass Link extends Interaction {\n  /**\n   * @param {Options} [options] Link options.\n   */\n  constructor(options) {\n    super();\n\n    options = Object.assign(\n      {\n        animate: true,\n        params: ['x', 'y', 'z', 'r', 'l'],\n        replace: false,\n        prefix: '',\n      },\n      options || {}\n    );\n\n    let animationOptions;\n    if (options.animate === true) {\n      animationOptions = {duration: 250};\n    } else if (!options.animate) {\n      animationOptions = null;\n    } else {\n      animationOptions = options.animate;\n    }\n\n    /**\n     * @type {import('../View.js').AnimationOptions|null}\n     * @private\n     */\n    this.animationOptions_ = animationOptions;\n\n    /**\n     * @type {Object<Params, boolean>}\n     * @private\n     */\n    this.params_ = options.params.reduce((acc, value) => {\n      acc[value] = true;\n      return acc;\n    }, {});\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.replace_ = options.replace;\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.prefix_ = options.prefix;\n\n    /**\n     * @private\n     * @type {!Array<import(\"../events.js\").EventsKey>}\n     */\n    this.listenerKeys_ = [];\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.initial_ = true;\n\n    this.updateState_ = this.updateState_.bind(this);\n  }\n\n  /**\n   * @private\n   * @param {string} name A parameter name.\n   * @return {string} A name with the prefix applied.\n   */\n  getParamName_(name) {\n    if (!this.prefix_) {\n      return name;\n    }\n    return this.prefix_ + name;\n  }\n\n  /**\n   * @private\n   * @param {URLSearchParams} params The search params.\n   * @param {string} name The unprefixed parameter name.\n   * @return {string|null} The parameter value.\n   */\n  get_(params, name) {\n    return params.get(this.getParamName_(name));\n  }\n\n  /**\n   * @private\n   * @param {URLSearchParams} params The search params.\n   * @param {string} name The unprefixed parameter name.\n   * @param {string} value The param value.\n   */\n  set_(params, name, value) {\n    if (!(name in this.params_)) {\n      return;\n    }\n    params.set(this.getParamName_(name), value);\n  }\n\n  /**\n   * @private\n   * @param {URLSearchParams} params The search params.\n   * @param {string} name The unprefixed parameter name.\n   */\n  delete_(params, name) {\n    if (!(name in this.params_)) {\n      return;\n    }\n    params.delete(this.getParamName_(name));\n  }\n\n  /**\n   * @param {import(\"../Map.js\").default|null} map Map.\n   */\n  setMap(map) {\n    const oldMap = this.getMap();\n    super.setMap(map);\n    if (map === oldMap) {\n      return;\n    }\n    if (oldMap) {\n      this.unregisterListeners_(oldMap);\n    }\n    if (map) {\n      this.initial_ = true;\n      this.updateState_();\n      this.registerListeners_(map);\n    }\n  }\n\n  /**\n   * @param {import(\"../Map.js\").default} map Map.\n   * @private\n   */\n  registerListeners_(map) {\n    this.listenerKeys_.push(\n      listen(map, MapEventType.MOVEEND, this.updateUrl_, this),\n      listen(map.getLayerGroup(), EventType.CHANGE, this.updateUrl_, this),\n      listen(map, 'change:layergroup', this.handleChangeLayerGroup_, this)\n    );\n\n    if (!this.replace_) {\n      addEventListener('popstate', this.updateState_);\n    }\n  }\n\n  /**\n   * @param {import(\"../Map.js\").default} map Map.\n   * @private\n   */\n  unregisterListeners_(map) {\n    for (let i = 0, ii = this.listenerKeys_.length; i < ii; ++i) {\n      unlistenByKey(this.listenerKeys_[i]);\n    }\n    this.listenerKeys_.length = 0;\n\n    if (!this.replace_) {\n      removeEventListener('popstate', this.updateState_);\n    }\n\n    const url = new URL(window.location.href);\n    const params = url.searchParams;\n    this.delete_(params, 'x');\n    this.delete_(params, 'y');\n    this.delete_(params, 'z');\n    this.delete_(params, 'r');\n    this.delete_(params, 'l');\n    window.history.replaceState(null, '', url);\n  }\n\n  /**\n   * @private\n   */\n  handleChangeLayerGroup_() {\n    const map = this.getMap();\n    if (!map) {\n      return;\n    }\n    this.unregisterListeners_(map);\n    this.registerListeners_(map);\n    this.initial_ = true;\n    this.updateUrl_();\n  }\n\n  /**\n   * @private\n   */\n  updateState_() {\n    const map = this.getMap();\n    if (!map) {\n      return;\n    }\n    const view = map.getView();\n    if (!view) {\n      return;\n    }\n    const url = new URL(window.location.href);\n    const params = url.searchParams;\n\n    let updateView = false;\n\n    /**\n     * @type {import('../View.js').AnimationOptions}\n     */\n    const viewProperties = {};\n\n    const zoom = readNumber(this.get_(params, 'z'));\n    if ('z' in this.params_ && differentNumber(zoom, view.getZoom())) {\n      updateView = true;\n      viewProperties.zoom = zoom;\n    }\n\n    const rotation = readNumber(this.get_(params, 'r'));\n    if ('r' in this.params_ && differentNumber(rotation, view.getRotation())) {\n      updateView = true;\n      viewProperties.rotation = rotation;\n    }\n\n    const center = [\n      readNumber(this.get_(params, 'x')),\n      readNumber(this.get_(params, 'y')),\n    ];\n    if (\n      ('x' in this.params_ || 'y' in this.params_) &&\n      differentArray(center, view.getCenter())\n    ) {\n      updateView = true;\n      viewProperties.center = center;\n    }\n\n    if (updateView) {\n      if (!this.initial_ && this.animationOptions_) {\n        view.animate(Object.assign(viewProperties, this.animationOptions_));\n      } else {\n        if (viewProperties.center) {\n          view.setCenter(viewProperties.center);\n        }\n        if ('zoom' in viewProperties) {\n          view.setZoom(viewProperties.zoom);\n        }\n        if ('rotation' in viewProperties) {\n          view.setRotation(viewProperties.rotation);\n        }\n      }\n    }\n\n    const layers = map.getAllLayers();\n    const layersParam = this.get_(params, 'l');\n    if (\n      'l' in this.params_ &&\n      layersParam &&\n      layersParam.length === layers.length\n    ) {\n      for (let i = 0, ii = layers.length; i < ii; ++i) {\n        const value = parseInt(layersParam[i]);\n        if (!isNaN(value)) {\n          const visible = Boolean(value);\n          const layer = layers[i];\n          if (layer.getVisible() !== visible) {\n            layer.setVisible(visible);\n          }\n        }\n      }\n    }\n  }\n\n  /**\n   * @private\n   */\n  updateUrl_() {\n    const map = this.getMap();\n    if (!map) {\n      return;\n    }\n    const view = map.getView();\n    if (!view) {\n      return;\n    }\n    const initial = this.initial_;\n    this.initial_ = false;\n\n    const center = view.getCenter();\n    const zoom = view.getZoom();\n    const rotation = view.getRotation();\n\n    const layers = map.getAllLayers();\n    const visibilities = new Array(layers.length);\n    for (let i = 0, ii = layers.length; i < ii; ++i) {\n      visibilities[i] = layers[i].getVisible() ? '1' : '0';\n    }\n\n    const url = new URL(window.location.href);\n    const params = url.searchParams;\n\n    this.set_(params, 'x', writeNumber(center[0]));\n    this.set_(params, 'y', writeNumber(center[1]));\n    this.set_(params, 'z', writeNumber(zoom));\n    this.set_(params, 'r', writeNumber(rotation));\n    this.set_(params, 'l', visibilities.join(''));\n\n    if (url.href !== window.location.href) {\n      if (initial || this.replace_) {\n        window.history.replaceState(history.state, '', url);\n      } else {\n        window.history.pushState(null, '', url);\n      }\n    }\n  }\n}\n\nexport default Link;\n", "/**\n * @module ol/interaction/Modify\n */\nimport Collection from '../Collection.js';\nimport CollectionEventType from '../CollectionEventType.js';\nimport Event from '../events/Event.js';\nimport EventType from '../events/EventType.js';\nimport Feature from '../Feature.js';\nimport MapBrowserEventType from '../MapBrowserEventType.js';\nimport Point from '../geom/Point.js';\nimport PointerInteraction from './Pointer.js';\nimport RBush from '../structs/RBush.js';\nimport VectorEventType from '../source/VectorEventType.js';\nimport VectorLayer from '../layer/Vector.js';\nimport VectorSource from '../source/Vector.js';\nimport {\n  altKeyOnly,\n  always,\n  primaryAction,\n  singleClick,\n} from '../events/condition.js';\nimport {\n  boundingExtent,\n  buffer as bufferExtent,\n  createOrUpdateFromCoordinate as createExtent,\n} from '../extent.js';\nimport {\n  closestOnSegment,\n  distance as coordinateDistance,\n  equals as coordinatesEqual,\n  squaredDistance as squaredCoordinateDistance,\n  squaredDistanceToSegment,\n} from '../coordinate.js';\nimport {createEditingStyle} from '../style/Style.js';\nimport {equals} from '../array.js';\nimport {fromCircle} from '../geom/Polygon.js';\nimport {\n  fromUserCoordinate,\n  fromUserExtent,\n  getUserProjection,\n  toUserCoordinate,\n  toUserExtent,\n} from '../proj.js';\nimport {getUid} from '../util.js';\n\n/**\n * The segment index assigned to a circle's center when\n * breaking up a circle into ModifySegmentDataType segments.\n * @type {number}\n */\nconst CIRCLE_CENTER_INDEX = 0;\n\n/**\n * The segment index assigned to a circle's circumference when\n * breaking up a circle into ModifySegmentDataType segments.\n * @type {number}\n */\nconst CIRCLE_CIRCUMFERENCE_INDEX = 1;\n\nconst tempExtent = [0, 0, 0, 0];\nconst tempSegment = [];\n\n/**\n * @enum {string}\n */\nconst ModifyEventType = {\n  /**\n   * Triggered upon feature modification start\n   * @event ModifyEvent#modifystart\n   * @api\n   */\n  MODIFYSTART: 'modifystart',\n  /**\n   * Triggered upon feature modification end\n   * @event ModifyEvent#modifyend\n   * @api\n   */\n  MODIFYEND: 'modifyend',\n};\n\n/**\n * @typedef {Object} SegmentData\n * @property {Array<number>} [depth] Depth.\n * @property {Feature} feature Feature.\n * @property {import(\"../geom/SimpleGeometry.js\").default} geometry Geometry.\n * @property {number} [index] Index.\n * @property {Array<Array<number>>} segment Segment.\n * @property {Array<SegmentData>} [featureSegments] FeatureSegments.\n */\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../events/condition.js\").Condition} [condition] A function that\n * takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * boolean to indicate whether that event will be considered to add or move a\n * vertex to the sketch. Default is\n * {@link module:ol/events/condition.primaryAction}.\n * @property {import(\"../events/condition.js\").Condition} [deleteCondition] A function\n * that takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * boolean to indicate whether that event should be handled. By default,\n * {@link module:ol/events/condition.singleClick} with\n * {@link module:ol/events/condition.altKeyOnly} results in a vertex deletion.\n * @property {import(\"../events/condition.js\").Condition} [insertVertexCondition] A\n * function that takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and\n * returns a boolean to indicate whether a new vertex should be added to the sketch\n * features. Default is {@link module:ol/events/condition.always}.\n * @property {number} [pixelTolerance=10] Pixel tolerance for considering the\n * pointer close enough to a segment or vertex for editing.\n * @property {import(\"../style/Style.js\").StyleLike|import(\"../style/flat.js\").FlatStyleLike} [style]\n * Style used for the modification point or vertex. For linestrings and polygons, this will\n * be the affected vertex, for circles a point along the circle, and for points the actual\n * point. If not configured, the default edit style is used (see {@link module:ol/style/Style~Style}).\n * When using a style function, the point feature passed to the function will have a `features`\n * property - an array whose entries are the features that are being modified, and a `geometries`\n * property - an array whose entries are the geometries that are being modified. Both arrays are\n * in the same order. The `geometries` are only useful when modifying geometry collections, where\n * the geometry will be the particular geometry from the collection that is being modified.\n * @property {VectorSource} [source] The vector source with\n * features to modify.  If a vector source is not provided, a feature collection\n * must be provided with the `features` option.\n * @property {boolean|import(\"../layer/BaseVector\").default} [hitDetection] When configured, point\n * features will be considered for modification based on their visual appearance, instead of being within\n * the `pixelTolerance` from the pointer location. When a {@link module:ol/layer/BaseVector~BaseVectorLayer} is\n * provided, only the rendered representation of the features on that layer will be considered.\n * @property {Collection<Feature>} [features]\n * The features the interaction works on.  If a feature collection is not\n * provided, a vector source must be provided with the `source` option.\n * @property {boolean} [wrapX=false] Wrap the world horizontally on the sketch\n * overlay.\n * @property {boolean} [snapToPointer=!hitDetection] The vertex, point or segment being modified snaps to the\n * pointer coordinate when clicked within the `pixelTolerance`.\n */\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/interaction/Modify~Modify} instances are\n * instances of this type.\n */\nexport class ModifyEvent extends Event {\n  /**\n   * @param {ModifyEventType} type Type.\n   * @param {Collection<Feature>} features\n   * The features modified.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent\n   * Associated {@link module:ol/MapBrowserEvent~MapBrowserEvent}.\n   */\n  constructor(type, features, mapBrowserEvent) {\n    super(type);\n\n    /**\n     * The features being modified.\n     * @type {Collection<Feature>}\n     * @api\n     */\n    this.features = features;\n\n    /**\n     * Associated {@link module:ol/MapBrowserEvent~MapBrowserEvent}.\n     * @type {import(\"../MapBrowserEvent.js\").default}\n     * @api\n     */\n    this.mapBrowserEvent = mapBrowserEvent;\n  }\n}\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types|\n *     'change:active', import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<'modifyend'|'modifystart', ModifyEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types|\n *     'change:active'|'modifyend'|'modifystart', Return>} ModifyOnSignature\n */\n\n/**\n * @classdesc\n * Interaction for modifying feature geometries.  To modify features that have\n * been added to an existing source, construct the modify interaction with the\n * `source` option.  If you want to modify features in a collection (for example,\n * the collection used by a select interaction), construct the interaction with\n * the `features` option.  The interaction must be constructed with either a\n * `source` or `features` option.\n *\n * Cartesian distance from the pointer is used to determine the features that\n * will be modified. This means that geometries will only be considered for\n * modification when they are within the configured `pixelTolerance`. For point\n * geometries, the `hitDetection` option can be used to match their visual\n * appearance.\n *\n * By default, the interaction will allow deletion of vertices when the `alt`\n * key is pressed.  To configure the interaction with a different condition\n * for deletion, use the `deleteCondition` option.\n * @fires ModifyEvent\n * @api\n */\nclass Modify extends PointerInteraction {\n  /**\n   * @param {Options} options Options.\n   */\n  constructor(options) {\n    super(/** @type {import(\"./Pointer.js\").Options} */ (options));\n\n    /***\n     * @type {ModifyOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {ModifyOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {ModifyOnSignature<void>}\n     */\n    this.un;\n\n    /** @private */\n    this.boundHandleFeatureChange_ = this.handleFeatureChange_.bind(this);\n\n    /**\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.condition_ = options.condition ? options.condition : primaryAction;\n\n    /**\n     * @private\n     * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Browser event.\n     * @return {boolean} Combined condition result.\n     */\n    this.defaultDeleteCondition_ = function (mapBrowserEvent) {\n      return altKeyOnly(mapBrowserEvent) && singleClick(mapBrowserEvent);\n    };\n\n    /**\n     * @type {import(\"../events/condition.js\").Condition}\n     * @private\n     */\n    this.deleteCondition_ = options.deleteCondition\n      ? options.deleteCondition\n      : this.defaultDeleteCondition_;\n\n    /**\n     * @type {import(\"../events/condition.js\").Condition}\n     * @private\n     */\n    this.insertVertexCondition_ = options.insertVertexCondition\n      ? options.insertVertexCondition\n      : always;\n\n    /**\n     * Editing vertex.\n     * @type {Feature<Point>}\n     * @private\n     */\n    this.vertexFeature_ = null;\n\n    /**\n     * Segments intersecting {@link this.vertexFeature_} by segment uid.\n     * @type {Object<string, boolean>}\n     * @private\n     */\n    this.vertexSegments_ = null;\n\n    /**\n     * @type {import(\"../pixel.js\").Pixel}\n     * @private\n     */\n    this.lastPixel_ = [0, 0];\n\n    /**\n     * Tracks if the next `singleclick` event should be ignored to prevent\n     * accidental deletion right after vertex creation.\n     * @type {boolean}\n     * @private\n     */\n    this.ignoreNextSingleClick_ = false;\n\n    /**\n     * @type {Collection<Feature>}\n     * @private\n     */\n    this.featuresBeingModified_ = null;\n\n    /**\n     * Segment RTree for each layer\n     * @type {RBush<SegmentData>}\n     * @private\n     */\n    this.rBush_ = new RBush();\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.pixelTolerance_ =\n      options.pixelTolerance !== undefined ? options.pixelTolerance : 10;\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.snappedToVertex_ = false;\n\n    /**\n     * Indicate whether the interaction is currently changing a feature's\n     * coordinates.\n     * @type {boolean}\n     * @private\n     */\n    this.changingFeature_ = false;\n\n    /**\n     * @type {Array}\n     * @private\n     */\n    this.dragSegments_ = [];\n\n    /**\n     * Draw overlay where sketch features are drawn.\n     * @type {VectorLayer}\n     * @private\n     */\n    this.overlay_ = new VectorLayer({\n      source: new VectorSource({\n        useSpatialIndex: false,\n        wrapX: !!options.wrapX,\n      }),\n      style: options.style ? options.style : getDefaultStyleFunction(),\n      updateWhileAnimating: true,\n      updateWhileInteracting: true,\n    });\n\n    /**\n     * @const\n     * @private\n     * @type {!Object<string, function(Feature, import(\"../geom/Geometry.js\").default): void>}\n     */\n    this.SEGMENT_WRITERS_ = {\n      'Point': this.writePointGeometry_.bind(this),\n      'LineString': this.writeLineStringGeometry_.bind(this),\n      'LinearRing': this.writeLineStringGeometry_.bind(this),\n      'Polygon': this.writePolygonGeometry_.bind(this),\n      'MultiPoint': this.writeMultiPointGeometry_.bind(this),\n      'MultiLineString': this.writeMultiLineStringGeometry_.bind(this),\n      'MultiPolygon': this.writeMultiPolygonGeometry_.bind(this),\n      'Circle': this.writeCircleGeometry_.bind(this),\n      'GeometryCollection': this.writeGeometryCollectionGeometry_.bind(this),\n    };\n\n    /**\n     * @type {VectorSource}\n     * @private\n     */\n    this.source_ = null;\n\n    /**\n     * @type {boolean|import(\"../layer/BaseVector\").default}\n     */\n    this.hitDetection_ = null;\n\n    /** @type {Collection<Feature>} */\n    let features;\n    if (options.features) {\n      features = options.features;\n    } else if (options.source) {\n      this.source_ = options.source;\n      features = new Collection(this.source_.getFeatures());\n      this.source_.addEventListener(\n        VectorEventType.ADDFEATURE,\n        this.handleSourceAdd_.bind(this)\n      );\n      this.source_.addEventListener(\n        VectorEventType.REMOVEFEATURE,\n        this.handleSourceRemove_.bind(this)\n      );\n    }\n    if (!features) {\n      throw new Error(\n        'The modify interaction requires features, a source or a layer'\n      );\n    }\n    if (options.hitDetection) {\n      this.hitDetection_ = options.hitDetection;\n    }\n\n    /**\n     * @type {Collection<Feature>}\n     * @private\n     */\n    this.features_ = features;\n\n    this.features_.forEach(this.addFeature_.bind(this));\n    this.features_.addEventListener(\n      CollectionEventType.ADD,\n      this.handleFeatureAdd_.bind(this)\n    );\n    this.features_.addEventListener(\n      CollectionEventType.REMOVE,\n      this.handleFeatureRemove_.bind(this)\n    );\n\n    /**\n     * @type {import(\"../MapBrowserEvent.js\").default}\n     * @private\n     */\n    this.lastPointerEvent_ = null;\n\n    /**\n     * Delta (x, y in map units) between matched rtree vertex and pointer vertex.\n     * @type {Array<number>}\n     */\n    this.delta_ = [0, 0];\n\n    /**\n     * @private\n     */\n    this.snapToPointer_ =\n      options.snapToPointer === undefined\n        ? !this.hitDetection_\n        : options.snapToPointer;\n  }\n\n  /**\n   * @param {Feature} feature Feature.\n   * @private\n   */\n  addFeature_(feature) {\n    const geometry = feature.getGeometry();\n    if (geometry) {\n      const writer = this.SEGMENT_WRITERS_[geometry.getType()];\n      if (writer) {\n        writer(feature, geometry);\n      }\n    }\n    const map = this.getMap();\n    if (map && map.isRendered() && this.getActive()) {\n      this.handlePointerAtPixel_(this.lastPixel_, map);\n    }\n    feature.addEventListener(EventType.CHANGE, this.boundHandleFeatureChange_);\n  }\n\n  /**\n   * @param {import(\"../MapBrowserEvent.js\").default} evt Map browser event.\n   * @param {Array<Array<SegmentData>>} segments The segments subject to modification.\n   * @private\n   */\n  willModifyFeatures_(evt, segments) {\n    if (!this.featuresBeingModified_) {\n      this.featuresBeingModified_ = new Collection();\n      const features = this.featuresBeingModified_.getArray();\n      for (let i = 0, ii = segments.length; i < ii; ++i) {\n        const segment = segments[i];\n        for (let s = 0, ss = segment.length; s < ss; ++s) {\n          const feature = segment[s].feature;\n          if (feature && !features.includes(feature)) {\n            this.featuresBeingModified_.push(feature);\n          }\n        }\n      }\n      if (this.featuresBeingModified_.getLength() === 0) {\n        this.featuresBeingModified_ = null;\n      } else {\n        this.dispatchEvent(\n          new ModifyEvent(\n            ModifyEventType.MODIFYSTART,\n            this.featuresBeingModified_,\n            evt\n          )\n        );\n      }\n    }\n  }\n\n  /**\n   * @param {Feature} feature Feature.\n   * @private\n   */\n  removeFeature_(feature) {\n    this.removeFeatureSegmentData_(feature);\n    // Remove the vertex feature if the collection of candidate features is empty.\n    if (this.vertexFeature_ && this.features_.getLength() === 0) {\n      this.overlay_.getSource().removeFeature(this.vertexFeature_);\n      this.vertexFeature_ = null;\n    }\n    feature.removeEventListener(\n      EventType.CHANGE,\n      this.boundHandleFeatureChange_\n    );\n  }\n\n  /**\n   * @param {Feature} feature Feature.\n   * @private\n   */\n  removeFeatureSegmentData_(feature) {\n    const rBush = this.rBush_;\n    /** @type {Array<SegmentData>} */\n    const nodesToRemove = [];\n    rBush.forEach(\n      /**\n       * @param {SegmentData} node RTree node.\n       */\n      function (node) {\n        if (feature === node.feature) {\n          nodesToRemove.push(node);\n        }\n      }\n    );\n    for (let i = nodesToRemove.length - 1; i >= 0; --i) {\n      const nodeToRemove = nodesToRemove[i];\n      for (let j = this.dragSegments_.length - 1; j >= 0; --j) {\n        if (this.dragSegments_[j][0] === nodeToRemove) {\n          this.dragSegments_.splice(j, 1);\n        }\n      }\n      rBush.remove(nodeToRemove);\n    }\n  }\n\n  /**\n   * Activate or deactivate the interaction.\n   * @param {boolean} active Active.\n   * @observable\n   * @api\n   */\n  setActive(active) {\n    if (this.vertexFeature_ && !active) {\n      this.overlay_.getSource().removeFeature(this.vertexFeature_);\n      this.vertexFeature_ = null;\n    }\n    super.setActive(active);\n  }\n\n  /**\n   * Remove the interaction from its current map and attach it to the new map.\n   * Subclasses may set up event handlers to get notified about changes to\n   * the map here.\n   * @param {import(\"../Map.js\").default} map Map.\n   */\n  setMap(map) {\n    this.overlay_.setMap(map);\n    super.setMap(map);\n  }\n\n  /**\n   * Get the overlay layer that this interaction renders the modification point or vertex to.\n   * @return {VectorLayer} Overlay layer.\n   * @api\n   */\n  getOverlay() {\n    return this.overlay_;\n  }\n\n  /**\n   * @param {import(\"../source/Vector.js\").VectorSourceEvent} event Event.\n   * @private\n   */\n  handleSourceAdd_(event) {\n    if (event.feature) {\n      this.features_.push(event.feature);\n    }\n  }\n\n  /**\n   * @param {import(\"../source/Vector.js\").VectorSourceEvent} event Event.\n   * @private\n   */\n  handleSourceRemove_(event) {\n    if (event.feature) {\n      this.features_.remove(event.feature);\n    }\n  }\n\n  /**\n   * @param {import(\"../Collection.js\").CollectionEvent<Feature>} evt Event.\n   * @private\n   */\n  handleFeatureAdd_(evt) {\n    this.addFeature_(evt.element);\n  }\n\n  /**\n   * @param {import(\"../events/Event.js\").default} evt Event.\n   * @private\n   */\n  handleFeatureChange_(evt) {\n    if (!this.changingFeature_) {\n      const feature = /** @type {Feature} */ (evt.target);\n      this.removeFeature_(feature);\n      this.addFeature_(feature);\n    }\n  }\n\n  /**\n   * @param {import(\"../Collection.js\").CollectionEvent<Feature>} evt Event.\n   * @private\n   */\n  handleFeatureRemove_(evt) {\n    this.removeFeature_(evt.element);\n  }\n\n  /**\n   * @param {Feature} feature Feature\n   * @param {Point} geometry Geometry.\n   * @private\n   */\n  writePointGeometry_(feature, geometry) {\n    const coordinates = geometry.getCoordinates();\n\n    /** @type {SegmentData} */\n    const segmentData = {\n      feature: feature,\n      geometry: geometry,\n      segment: [coordinates, coordinates],\n    };\n\n    this.rBush_.insert(geometry.getExtent(), segmentData);\n  }\n\n  /**\n   * @param {Feature} feature Feature\n   * @param {import(\"../geom/MultiPoint.js\").default} geometry Geometry.\n   * @private\n   */\n  writeMultiPointGeometry_(feature, geometry) {\n    const points = geometry.getCoordinates();\n    for (let i = 0, ii = points.length; i < ii; ++i) {\n      const coordinates = points[i];\n\n      /** @type {SegmentData} */\n      const segmentData = {\n        feature: feature,\n        geometry: geometry,\n        depth: [i],\n        index: i,\n        segment: [coordinates, coordinates],\n      };\n\n      this.rBush_.insert(geometry.getExtent(), segmentData);\n    }\n  }\n\n  /**\n   * @param {Feature} feature Feature\n   * @param {import(\"../geom/LineString.js\").default} geometry Geometry.\n   * @private\n   */\n  writeLineStringGeometry_(feature, geometry) {\n    const coordinates = geometry.getCoordinates();\n    for (let i = 0, ii = coordinates.length - 1; i < ii; ++i) {\n      const segment = coordinates.slice(i, i + 2);\n\n      /** @type {SegmentData} */\n      const segmentData = {\n        feature: feature,\n        geometry: geometry,\n        index: i,\n        segment: segment,\n      };\n\n      this.rBush_.insert(boundingExtent(segment), segmentData);\n    }\n  }\n\n  /**\n   * @param {Feature} feature Feature\n   * @param {import(\"../geom/MultiLineString.js\").default} geometry Geometry.\n   * @private\n   */\n  writeMultiLineStringGeometry_(feature, geometry) {\n    const lines = geometry.getCoordinates();\n    for (let j = 0, jj = lines.length; j < jj; ++j) {\n      const coordinates = lines[j];\n      for (let i = 0, ii = coordinates.length - 1; i < ii; ++i) {\n        const segment = coordinates.slice(i, i + 2);\n\n        /** @type {SegmentData} */\n        const segmentData = {\n          feature: feature,\n          geometry: geometry,\n          depth: [j],\n          index: i,\n          segment: segment,\n        };\n\n        this.rBush_.insert(boundingExtent(segment), segmentData);\n      }\n    }\n  }\n\n  /**\n   * @param {Feature} feature Feature\n   * @param {import(\"../geom/Polygon.js\").default} geometry Geometry.\n   * @private\n   */\n  writePolygonGeometry_(feature, geometry) {\n    const rings = geometry.getCoordinates();\n    for (let j = 0, jj = rings.length; j < jj; ++j) {\n      const coordinates = rings[j];\n      for (let i = 0, ii = coordinates.length - 1; i < ii; ++i) {\n        const segment = coordinates.slice(i, i + 2);\n\n        /** @type {SegmentData} */\n        const segmentData = {\n          feature: feature,\n          geometry: geometry,\n          depth: [j],\n          index: i,\n          segment: segment,\n        };\n\n        this.rBush_.insert(boundingExtent(segment), segmentData);\n      }\n    }\n  }\n\n  /**\n   * @param {Feature} feature Feature\n   * @param {import(\"../geom/MultiPolygon.js\").default} geometry Geometry.\n   * @private\n   */\n  writeMultiPolygonGeometry_(feature, geometry) {\n    const polygons = geometry.getCoordinates();\n    for (let k = 0, kk = polygons.length; k < kk; ++k) {\n      const rings = polygons[k];\n      for (let j = 0, jj = rings.length; j < jj; ++j) {\n        const coordinates = rings[j];\n        for (let i = 0, ii = coordinates.length - 1; i < ii; ++i) {\n          const segment = coordinates.slice(i, i + 2);\n\n          /** @type {SegmentData} */\n          const segmentData = {\n            feature: feature,\n            geometry: geometry,\n            depth: [j, k],\n            index: i,\n            segment: segment,\n          };\n\n          this.rBush_.insert(boundingExtent(segment), segmentData);\n        }\n      }\n    }\n  }\n\n  /**\n   * We convert a circle into two segments.  The segment at index\n   * {@link CIRCLE_CENTER_INDEX} is the\n   * circle's center (a point).  The segment at index\n   * {@link CIRCLE_CIRCUMFERENCE_INDEX} is\n   * the circumference, and is not a line segment.\n   *\n   * @param {Feature} feature Feature.\n   * @param {import(\"../geom/Circle.js\").default} geometry Geometry.\n   * @private\n   */\n  writeCircleGeometry_(feature, geometry) {\n    const coordinates = geometry.getCenter();\n\n    /** @type {SegmentData} */\n    const centerSegmentData = {\n      feature: feature,\n      geometry: geometry,\n      index: CIRCLE_CENTER_INDEX,\n      segment: [coordinates, coordinates],\n    };\n\n    /** @type {SegmentData} */\n    const circumferenceSegmentData = {\n      feature: feature,\n      geometry: geometry,\n      index: CIRCLE_CIRCUMFERENCE_INDEX,\n      segment: [coordinates, coordinates],\n    };\n\n    const featureSegments = [centerSegmentData, circumferenceSegmentData];\n    centerSegmentData.featureSegments = featureSegments;\n    circumferenceSegmentData.featureSegments = featureSegments;\n    this.rBush_.insert(createExtent(coordinates), centerSegmentData);\n    let circleGeometry = /** @type {import(\"../geom/Geometry.js\").default} */ (\n      geometry\n    );\n    const userProjection = getUserProjection();\n    if (userProjection && this.getMap()) {\n      const projection = this.getMap().getView().getProjection();\n      circleGeometry = circleGeometry\n        .clone()\n        .transform(userProjection, projection);\n      circleGeometry = fromCircle(\n        /** @type {import(\"../geom/Circle.js\").default} */ (circleGeometry)\n      ).transform(projection, userProjection);\n    }\n    this.rBush_.insert(circleGeometry.getExtent(), circumferenceSegmentData);\n  }\n\n  /**\n   * @param {Feature} feature Feature\n   * @param {import(\"../geom/GeometryCollection.js\").default} geometry Geometry.\n   * @private\n   */\n  writeGeometryCollectionGeometry_(feature, geometry) {\n    const geometries = geometry.getGeometriesArray();\n    for (let i = 0; i < geometries.length; ++i) {\n      const geometry = geometries[i];\n      const writer = this.SEGMENT_WRITERS_[geometry.getType()];\n      writer(feature, geometry);\n    }\n  }\n\n  /**\n   * @param {import(\"../coordinate.js\").Coordinate} coordinates Coordinates.\n   * @param {Array<Feature>} features The features being modified.\n   * @param {Array<import(\"../geom/SimpleGeometry.js\").default>} geometries The geometries being modified.\n   * @return {Feature} Vertex feature.\n   * @private\n   */\n  createOrUpdateVertexFeature_(coordinates, features, geometries) {\n    let vertexFeature = this.vertexFeature_;\n    if (!vertexFeature) {\n      vertexFeature = new Feature(new Point(coordinates));\n      this.vertexFeature_ = vertexFeature;\n      this.overlay_.getSource().addFeature(vertexFeature);\n    } else {\n      const geometry = vertexFeature.getGeometry();\n      geometry.setCoordinates(coordinates);\n    }\n    vertexFeature.set('features', features);\n    vertexFeature.set('geometries', geometries);\n    return vertexFeature;\n  }\n\n  /**\n   * Handles the {@link module:ol/MapBrowserEvent~MapBrowserEvent map browser event} and may modify the geometry.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n   * @return {boolean} `false` to stop event propagation.\n   */\n  handleEvent(mapBrowserEvent) {\n    if (!mapBrowserEvent.originalEvent) {\n      return true;\n    }\n    this.lastPointerEvent_ = mapBrowserEvent;\n\n    let handled;\n    if (\n      !mapBrowserEvent.map.getView().getInteracting() &&\n      mapBrowserEvent.type == MapBrowserEventType.POINTERMOVE &&\n      !this.handlingDownUpSequence\n    ) {\n      this.handlePointerMove_(mapBrowserEvent);\n    }\n    if (this.vertexFeature_ && this.deleteCondition_(mapBrowserEvent)) {\n      if (\n        mapBrowserEvent.type != MapBrowserEventType.SINGLECLICK ||\n        !this.ignoreNextSingleClick_\n      ) {\n        handled = this.removePoint();\n      } else {\n        handled = true;\n      }\n    }\n\n    if (mapBrowserEvent.type == MapBrowserEventType.SINGLECLICK) {\n      this.ignoreNextSingleClick_ = false;\n    }\n\n    return super.handleEvent(mapBrowserEvent) && !handled;\n  }\n\n  /**\n   * Handle pointer drag events.\n   * @param {import(\"../MapBrowserEvent.js\").default} evt Event.\n   */\n  handleDragEvent(evt) {\n    this.ignoreNextSingleClick_ = false;\n    this.willModifyFeatures_(evt, this.dragSegments_);\n\n    const vertex = [\n      evt.coordinate[0] + this.delta_[0],\n      evt.coordinate[1] + this.delta_[1],\n    ];\n    const features = [];\n    const geometries = [];\n    for (let i = 0, ii = this.dragSegments_.length; i < ii; ++i) {\n      const dragSegment = this.dragSegments_[i];\n      const segmentData = dragSegment[0];\n      const feature = segmentData.feature;\n      if (!features.includes(feature)) {\n        features.push(feature);\n      }\n      const geometry = segmentData.geometry;\n      if (!geometries.includes(geometry)) {\n        geometries.push(geometry);\n      }\n      const depth = segmentData.depth;\n      let coordinates;\n      const segment = segmentData.segment;\n      const index = dragSegment[1];\n\n      while (vertex.length < geometry.getStride()) {\n        vertex.push(segment[index][vertex.length]);\n      }\n\n      switch (geometry.getType()) {\n        case 'Point':\n          coordinates = vertex;\n          segment[0] = vertex;\n          segment[1] = vertex;\n          break;\n        case 'MultiPoint':\n          coordinates = geometry.getCoordinates();\n          coordinates[segmentData.index] = vertex;\n          segment[0] = vertex;\n          segment[1] = vertex;\n          break;\n        case 'LineString':\n          coordinates = geometry.getCoordinates();\n          coordinates[segmentData.index + index] = vertex;\n          segment[index] = vertex;\n          break;\n        case 'MultiLineString':\n          coordinates = geometry.getCoordinates();\n          coordinates[depth[0]][segmentData.index + index] = vertex;\n          segment[index] = vertex;\n          break;\n        case 'Polygon':\n          coordinates = geometry.getCoordinates();\n          coordinates[depth[0]][segmentData.index + index] = vertex;\n          segment[index] = vertex;\n          break;\n        case 'MultiPolygon':\n          coordinates = geometry.getCoordinates();\n          coordinates[depth[1]][depth[0]][segmentData.index + index] = vertex;\n          segment[index] = vertex;\n          break;\n        case 'Circle':\n          segment[0] = vertex;\n          segment[1] = vertex;\n          if (segmentData.index === CIRCLE_CENTER_INDEX) {\n            this.changingFeature_ = true;\n            geometry.setCenter(vertex);\n            this.changingFeature_ = false;\n          } else {\n            // We're dragging the circle's circumference:\n            this.changingFeature_ = true;\n            const projection = evt.map.getView().getProjection();\n            let radius = coordinateDistance(\n              fromUserCoordinate(geometry.getCenter(), projection),\n              fromUserCoordinate(vertex, projection)\n            );\n            const userProjection = getUserProjection();\n            if (userProjection) {\n              const circleGeometry = geometry\n                .clone()\n                .transform(userProjection, projection);\n              circleGeometry.setRadius(radius);\n              radius = circleGeometry\n                .transform(projection, userProjection)\n                .getRadius();\n            }\n            geometry.setRadius(radius);\n            this.changingFeature_ = false;\n          }\n          break;\n        default:\n        // pass\n      }\n\n      if (coordinates) {\n        this.setGeometryCoordinates_(geometry, coordinates);\n      }\n    }\n    this.createOrUpdateVertexFeature_(vertex, features, geometries);\n  }\n\n  /**\n   * Handle pointer down events.\n   * @param {import(\"../MapBrowserEvent.js\").default} evt Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleDownEvent(evt) {\n    if (!this.condition_(evt)) {\n      return false;\n    }\n    const pixelCoordinate = evt.coordinate;\n    this.handlePointerAtPixel_(evt.pixel, evt.map, pixelCoordinate);\n    this.dragSegments_.length = 0;\n    this.featuresBeingModified_ = null;\n    const vertexFeature = this.vertexFeature_;\n    if (vertexFeature) {\n      const projection = evt.map.getView().getProjection();\n      const insertVertices = [];\n      const vertex = vertexFeature.getGeometry().getCoordinates();\n      const vertexExtent = boundingExtent([vertex]);\n      const segmentDataMatches = this.rBush_.getInExtent(vertexExtent);\n      const componentSegments = {};\n      segmentDataMatches.sort(compareIndexes);\n      for (let i = 0, ii = segmentDataMatches.length; i < ii; ++i) {\n        const segmentDataMatch = segmentDataMatches[i];\n        const segment = segmentDataMatch.segment;\n        let uid = getUid(segmentDataMatch.geometry);\n        const depth = segmentDataMatch.depth;\n        if (depth) {\n          uid += '-' + depth.join('-'); // separate feature components\n        }\n        if (!componentSegments[uid]) {\n          componentSegments[uid] = new Array(2);\n        }\n\n        if (\n          segmentDataMatch.geometry.getType() === 'Circle' &&\n          segmentDataMatch.index === CIRCLE_CIRCUMFERENCE_INDEX\n        ) {\n          const closestVertex = closestOnSegmentData(\n            pixelCoordinate,\n            segmentDataMatch,\n            projection\n          );\n          if (\n            coordinatesEqual(closestVertex, vertex) &&\n            !componentSegments[uid][0]\n          ) {\n            this.dragSegments_.push([segmentDataMatch, 0]);\n            componentSegments[uid][0] = segmentDataMatch;\n          }\n          continue;\n        }\n\n        if (\n          coordinatesEqual(segment[0], vertex) &&\n          !componentSegments[uid][0]\n        ) {\n          this.dragSegments_.push([segmentDataMatch, 0]);\n          componentSegments[uid][0] = segmentDataMatch;\n          continue;\n        }\n\n        if (\n          coordinatesEqual(segment[1], vertex) &&\n          !componentSegments[uid][1]\n        ) {\n          if (\n            componentSegments[uid][0] &&\n            componentSegments[uid][0].index === 0\n          ) {\n            let coordinates = segmentDataMatch.geometry.getCoordinates();\n            switch (segmentDataMatch.geometry.getType()) {\n              // prevent dragging closed linestrings by the connecting node\n              case 'LineString':\n              case 'MultiLineString':\n                continue;\n              // if dragging the first vertex of a polygon, ensure the other segment\n              // belongs to the closing vertex of the linear ring\n              case 'MultiPolygon':\n                coordinates = coordinates[depth[1]];\n              /* falls through */\n              case 'Polygon':\n                if (\n                  segmentDataMatch.index !==\n                  coordinates[depth[0]].length - 2\n                ) {\n                  continue;\n                }\n                break;\n              default:\n              // pass\n            }\n          }\n\n          this.dragSegments_.push([segmentDataMatch, 1]);\n          componentSegments[uid][1] = segmentDataMatch;\n          continue;\n        }\n\n        if (\n          getUid(segment) in this.vertexSegments_ &&\n          !componentSegments[uid][0] &&\n          !componentSegments[uid][1] &&\n          this.insertVertexCondition_(evt)\n        ) {\n          insertVertices.push(segmentDataMatch);\n        }\n      }\n\n      if (insertVertices.length) {\n        this.willModifyFeatures_(evt, [insertVertices]);\n      }\n\n      for (let j = insertVertices.length - 1; j >= 0; --j) {\n        this.insertVertex_(insertVertices[j], vertex);\n      }\n    }\n    return !!this.vertexFeature_;\n  }\n\n  /**\n   * Handle pointer up events.\n   * @param {import(\"../MapBrowserEvent.js\").default} evt Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleUpEvent(evt) {\n    for (let i = this.dragSegments_.length - 1; i >= 0; --i) {\n      const segmentData = this.dragSegments_[i][0];\n      const geometry = segmentData.geometry;\n      if (geometry.getType() === 'Circle') {\n        // Update a circle object in the R* bush:\n        const coordinates = geometry.getCenter();\n        const centerSegmentData = segmentData.featureSegments[0];\n        const circumferenceSegmentData = segmentData.featureSegments[1];\n        centerSegmentData.segment[0] = coordinates;\n        centerSegmentData.segment[1] = coordinates;\n        circumferenceSegmentData.segment[0] = coordinates;\n        circumferenceSegmentData.segment[1] = coordinates;\n        this.rBush_.update(createExtent(coordinates), centerSegmentData);\n        let circleGeometry = geometry;\n        const userProjection = getUserProjection();\n        if (userProjection) {\n          const projection = evt.map.getView().getProjection();\n          circleGeometry = circleGeometry\n            .clone()\n            .transform(userProjection, projection);\n          circleGeometry = fromCircle(circleGeometry).transform(\n            projection,\n            userProjection\n          );\n        }\n        this.rBush_.update(\n          circleGeometry.getExtent(),\n          circumferenceSegmentData\n        );\n      } else {\n        this.rBush_.update(boundingExtent(segmentData.segment), segmentData);\n      }\n    }\n    if (this.featuresBeingModified_) {\n      this.dispatchEvent(\n        new ModifyEvent(\n          ModifyEventType.MODIFYEND,\n          this.featuresBeingModified_,\n          evt\n        )\n      );\n      this.featuresBeingModified_ = null;\n    }\n    return false;\n  }\n\n  /**\n   * @param {import(\"../MapBrowserEvent.js\").default} evt Event.\n   * @private\n   */\n  handlePointerMove_(evt) {\n    this.lastPixel_ = evt.pixel;\n    this.handlePointerAtPixel_(evt.pixel, evt.map, evt.coordinate);\n  }\n\n  /**\n   * @param {import(\"../pixel.js\").Pixel} pixel Pixel\n   * @param {import(\"../Map.js\").default} map Map.\n   * @param {import(\"../coordinate.js\").Coordinate} [coordinate] The pixel Coordinate.\n   * @private\n   */\n  handlePointerAtPixel_(pixel, map, coordinate) {\n    const pixelCoordinate = coordinate || map.getCoordinateFromPixel(pixel);\n    const projection = map.getView().getProjection();\n    const sortByDistance = function (a, b) {\n      return (\n        projectedDistanceToSegmentDataSquared(pixelCoordinate, a, projection) -\n        projectedDistanceToSegmentDataSquared(pixelCoordinate, b, projection)\n      );\n    };\n\n    /** @type {Array<SegmentData>|undefined} */\n    let nodes;\n    /** @type {Point|undefined} */\n    let hitPointGeometry;\n    if (this.hitDetection_) {\n      const layerFilter =\n        typeof this.hitDetection_ === 'object'\n          ? (layer) => layer === this.hitDetection_\n          : undefined;\n      map.forEachFeatureAtPixel(\n        pixel,\n        (feature, layer, geometry) => {\n          if (geometry) {\n            geometry = new Point(\n              toUserCoordinate(geometry.getCoordinates(), projection)\n            );\n          }\n          const geom = geometry || feature.getGeometry();\n          if (\n            geom.getType() === 'Point' &&\n            feature instanceof Feature &&\n            this.features_.getArray().includes(feature)\n          ) {\n            hitPointGeometry = /** @type {Point} */ (geom);\n            const coordinate = /** @type {Point} */ (feature.getGeometry())\n              .getFlatCoordinates()\n              .slice(0, 2);\n            nodes = [\n              {\n                feature,\n                geometry: hitPointGeometry,\n                segment: [coordinate, coordinate],\n              },\n            ];\n          }\n          return true;\n        },\n        {layerFilter}\n      );\n    }\n    if (!nodes) {\n      const viewExtent = fromUserExtent(\n        createExtent(pixelCoordinate, tempExtent),\n        projection\n      );\n      const buffer = map.getView().getResolution() * this.pixelTolerance_;\n      const box = toUserExtent(\n        bufferExtent(viewExtent, buffer, tempExtent),\n        projection\n      );\n      nodes = this.rBush_.getInExtent(box);\n    }\n\n    if (nodes && nodes.length > 0) {\n      const node = nodes.sort(sortByDistance)[0];\n      const closestSegment = node.segment;\n      let vertex = closestOnSegmentData(pixelCoordinate, node, projection);\n      const vertexPixel = map.getPixelFromCoordinate(vertex);\n      let dist = coordinateDistance(pixel, vertexPixel);\n      if (hitPointGeometry || dist <= this.pixelTolerance_) {\n        /** @type {Object<string, boolean>} */\n        const vertexSegments = {};\n        vertexSegments[getUid(closestSegment)] = true;\n\n        if (!this.snapToPointer_) {\n          this.delta_[0] = vertex[0] - pixelCoordinate[0];\n          this.delta_[1] = vertex[1] - pixelCoordinate[1];\n        }\n        if (\n          node.geometry.getType() === 'Circle' &&\n          node.index === CIRCLE_CIRCUMFERENCE_INDEX\n        ) {\n          this.snappedToVertex_ = true;\n          this.createOrUpdateVertexFeature_(\n            vertex,\n            [node.feature],\n            [node.geometry]\n          );\n        } else {\n          const pixel1 = map.getPixelFromCoordinate(closestSegment[0]);\n          const pixel2 = map.getPixelFromCoordinate(closestSegment[1]);\n          const squaredDist1 = squaredCoordinateDistance(vertexPixel, pixel1);\n          const squaredDist2 = squaredCoordinateDistance(vertexPixel, pixel2);\n          dist = Math.sqrt(Math.min(squaredDist1, squaredDist2));\n          this.snappedToVertex_ = dist <= this.pixelTolerance_;\n          if (this.snappedToVertex_) {\n            vertex =\n              squaredDist1 > squaredDist2\n                ? closestSegment[1]\n                : closestSegment[0];\n          }\n          this.createOrUpdateVertexFeature_(\n            vertex,\n            [node.feature],\n            [node.geometry]\n          );\n          const geometries = {};\n          geometries[getUid(node.geometry)] = true;\n          for (let i = 1, ii = nodes.length; i < ii; ++i) {\n            const segment = nodes[i].segment;\n            if (\n              (coordinatesEqual(closestSegment[0], segment[0]) &&\n                coordinatesEqual(closestSegment[1], segment[1])) ||\n              (coordinatesEqual(closestSegment[0], segment[1]) &&\n                coordinatesEqual(closestSegment[1], segment[0]))\n            ) {\n              const geometryUid = getUid(nodes[i].geometry);\n              if (!(geometryUid in geometries)) {\n                geometries[geometryUid] = true;\n                vertexSegments[getUid(segment)] = true;\n              }\n            } else {\n              break;\n            }\n          }\n        }\n\n        this.vertexSegments_ = vertexSegments;\n        return;\n      }\n    }\n    if (this.vertexFeature_) {\n      this.overlay_.getSource().removeFeature(this.vertexFeature_);\n      this.vertexFeature_ = null;\n    }\n  }\n\n  /**\n   * @param {SegmentData} segmentData Segment data.\n   * @param {import(\"../coordinate.js\").Coordinate} vertex Vertex.\n   * @private\n   */\n  insertVertex_(segmentData, vertex) {\n    const segment = segmentData.segment;\n    const feature = segmentData.feature;\n    const geometry = segmentData.geometry;\n    const depth = segmentData.depth;\n    const index = segmentData.index;\n    let coordinates;\n\n    while (vertex.length < geometry.getStride()) {\n      vertex.push(0);\n    }\n\n    switch (geometry.getType()) {\n      case 'MultiLineString':\n        coordinates = geometry.getCoordinates();\n        coordinates[depth[0]].splice(index + 1, 0, vertex);\n        break;\n      case 'Polygon':\n        coordinates = geometry.getCoordinates();\n        coordinates[depth[0]].splice(index + 1, 0, vertex);\n        break;\n      case 'MultiPolygon':\n        coordinates = geometry.getCoordinates();\n        coordinates[depth[1]][depth[0]].splice(index + 1, 0, vertex);\n        break;\n      case 'LineString':\n        coordinates = geometry.getCoordinates();\n        coordinates.splice(index + 1, 0, vertex);\n        break;\n      default:\n        return;\n    }\n\n    this.setGeometryCoordinates_(geometry, coordinates);\n    const rTree = this.rBush_;\n    rTree.remove(segmentData);\n    this.updateSegmentIndices_(geometry, index, depth, 1);\n\n    /** @type {SegmentData} */\n    const newSegmentData = {\n      segment: [segment[0], vertex],\n      feature: feature,\n      geometry: geometry,\n      depth: depth,\n      index: index,\n    };\n\n    rTree.insert(boundingExtent(newSegmentData.segment), newSegmentData);\n    this.dragSegments_.push([newSegmentData, 1]);\n\n    /** @type {SegmentData} */\n    const newSegmentData2 = {\n      segment: [vertex, segment[1]],\n      feature: feature,\n      geometry: geometry,\n      depth: depth,\n      index: index + 1,\n    };\n\n    rTree.insert(boundingExtent(newSegmentData2.segment), newSegmentData2);\n    this.dragSegments_.push([newSegmentData2, 0]);\n    this.ignoreNextSingleClick_ = true;\n  }\n\n  /**\n   * Removes the vertex currently being pointed.\n   * @return {boolean} True when a vertex was removed.\n   * @api\n   */\n  removePoint() {\n    if (\n      this.lastPointerEvent_ &&\n      this.lastPointerEvent_.type != MapBrowserEventType.POINTERDRAG\n    ) {\n      const evt = this.lastPointerEvent_;\n      this.willModifyFeatures_(evt, this.dragSegments_);\n      const removed = this.removeVertex_();\n      if (this.featuresBeingModified_) {\n        this.dispatchEvent(\n          new ModifyEvent(\n            ModifyEventType.MODIFYEND,\n            this.featuresBeingModified_,\n            evt\n          )\n        );\n      }\n\n      this.featuresBeingModified_ = null;\n      return removed;\n    }\n    return false;\n  }\n\n  /**\n   * Removes a vertex from all matching features.\n   * @return {boolean} True when a vertex was removed.\n   * @private\n   */\n  removeVertex_() {\n    const dragSegments = this.dragSegments_;\n    const segmentsByFeature = {};\n    let deleted = false;\n    let component, coordinates, dragSegment, geometry, i, index, left;\n    let newIndex, right, segmentData, uid;\n    for (i = dragSegments.length - 1; i >= 0; --i) {\n      dragSegment = dragSegments[i];\n      segmentData = dragSegment[0];\n      uid = getUid(segmentData.feature);\n      if (segmentData.depth) {\n        // separate feature components\n        uid += '-' + segmentData.depth.join('-');\n      }\n      if (!(uid in segmentsByFeature)) {\n        segmentsByFeature[uid] = {};\n      }\n      if (dragSegment[1] === 0) {\n        segmentsByFeature[uid].right = segmentData;\n        segmentsByFeature[uid].index = segmentData.index;\n      } else if (dragSegment[1] == 1) {\n        segmentsByFeature[uid].left = segmentData;\n        segmentsByFeature[uid].index = segmentData.index + 1;\n      }\n    }\n    for (uid in segmentsByFeature) {\n      right = segmentsByFeature[uid].right;\n      left = segmentsByFeature[uid].left;\n      index = segmentsByFeature[uid].index;\n      newIndex = index - 1;\n      if (left !== undefined) {\n        segmentData = left;\n      } else {\n        segmentData = right;\n      }\n      if (newIndex < 0) {\n        newIndex = 0;\n      }\n      geometry = segmentData.geometry;\n      coordinates = geometry.getCoordinates();\n      component = coordinates;\n      deleted = false;\n      switch (geometry.getType()) {\n        case 'MultiLineString':\n          if (coordinates[segmentData.depth[0]].length > 2) {\n            coordinates[segmentData.depth[0]].splice(index, 1);\n            deleted = true;\n          }\n          break;\n        case 'LineString':\n          if (coordinates.length > 2) {\n            coordinates.splice(index, 1);\n            deleted = true;\n          }\n          break;\n        case 'MultiPolygon':\n          component = component[segmentData.depth[1]];\n        /* falls through */\n        case 'Polygon':\n          component = component[segmentData.depth[0]];\n          if (component.length > 4) {\n            if (index == component.length - 1) {\n              index = 0;\n            }\n            component.splice(index, 1);\n            deleted = true;\n            if (index === 0) {\n              // close the ring again\n              component.pop();\n              component.push(component[0]);\n              newIndex = component.length - 1;\n            }\n          }\n          break;\n        default:\n        // pass\n      }\n\n      if (deleted) {\n        this.setGeometryCoordinates_(geometry, coordinates);\n        const segments = [];\n        if (left !== undefined) {\n          this.rBush_.remove(left);\n          segments.push(left.segment[0]);\n        }\n        if (right !== undefined) {\n          this.rBush_.remove(right);\n          segments.push(right.segment[1]);\n        }\n        if (left !== undefined && right !== undefined) {\n          /** @type {SegmentData} */\n          const newSegmentData = {\n            depth: segmentData.depth,\n            feature: segmentData.feature,\n            geometry: segmentData.geometry,\n            index: newIndex,\n            segment: segments,\n          };\n\n          this.rBush_.insert(\n            boundingExtent(newSegmentData.segment),\n            newSegmentData\n          );\n        }\n        this.updateSegmentIndices_(geometry, index, segmentData.depth, -1);\n        if (this.vertexFeature_) {\n          this.overlay_.getSource().removeFeature(this.vertexFeature_);\n          this.vertexFeature_ = null;\n        }\n        dragSegments.length = 0;\n      }\n    }\n    return deleted;\n  }\n\n  /**\n   * @param {import(\"../geom/SimpleGeometry.js\").default} geometry Geometry.\n   * @param {Array} coordinates Coordinates.\n   * @private\n   */\n  setGeometryCoordinates_(geometry, coordinates) {\n    this.changingFeature_ = true;\n    geometry.setCoordinates(coordinates);\n    this.changingFeature_ = false;\n  }\n\n  /**\n   * @param {import(\"../geom/SimpleGeometry.js\").default} geometry Geometry.\n   * @param {number} index Index.\n   * @param {Array<number>|undefined} depth Depth.\n   * @param {number} delta Delta (1 or -1).\n   * @private\n   */\n  updateSegmentIndices_(geometry, index, depth, delta) {\n    this.rBush_.forEachInExtent(\n      geometry.getExtent(),\n      function (segmentDataMatch) {\n        if (\n          segmentDataMatch.geometry === geometry &&\n          (depth === undefined ||\n            segmentDataMatch.depth === undefined ||\n            equals(segmentDataMatch.depth, depth)) &&\n          segmentDataMatch.index > index\n        ) {\n          segmentDataMatch.index += delta;\n        }\n      }\n    );\n  }\n}\n\n/**\n * @param {SegmentData} a The first segment data.\n * @param {SegmentData} b The second segment data.\n * @return {number} The difference in indexes.\n */\nfunction compareIndexes(a, b) {\n  return a.index - b.index;\n}\n\n/**\n * Returns the distance from a point to a line segment.\n *\n * @param {import(\"../coordinate.js\").Coordinate} pointCoordinates The coordinates of the point from\n *        which to calculate the distance.\n * @param {SegmentData} segmentData The object describing the line\n *        segment we are calculating the distance to.\n * @param {import(\"../proj/Projection.js\").default} projection The view projection.\n * @return {number} The square of the distance between a point and a line segment.\n */\nfunction projectedDistanceToSegmentDataSquared(\n  pointCoordinates,\n  segmentData,\n  projection\n) {\n  const geometry = segmentData.geometry;\n\n  if (geometry.getType() === 'Circle') {\n    let circleGeometry = /** @type {import(\"../geom/Circle.js\").default} */ (\n      geometry\n    );\n\n    if (segmentData.index === CIRCLE_CIRCUMFERENCE_INDEX) {\n      const userProjection = getUserProjection();\n      if (userProjection) {\n        circleGeometry = /** @type {import(\"../geom/Circle.js\").default} */ (\n          circleGeometry.clone().transform(userProjection, projection)\n        );\n      }\n      const distanceToCenterSquared = squaredCoordinateDistance(\n        circleGeometry.getCenter(),\n        fromUserCoordinate(pointCoordinates, projection)\n      );\n      const distanceToCircumference =\n        Math.sqrt(distanceToCenterSquared) - circleGeometry.getRadius();\n      return distanceToCircumference * distanceToCircumference;\n    }\n  }\n\n  const coordinate = fromUserCoordinate(pointCoordinates, projection);\n  tempSegment[0] = fromUserCoordinate(segmentData.segment[0], projection);\n  tempSegment[1] = fromUserCoordinate(segmentData.segment[1], projection);\n  return squaredDistanceToSegment(coordinate, tempSegment);\n}\n\n/**\n * Returns the point closest to a given line segment.\n *\n * @param {import(\"../coordinate.js\").Coordinate} pointCoordinates The point to which a closest point\n *        should be found.\n * @param {SegmentData} segmentData The object describing the line\n *        segment which should contain the closest point.\n * @param {import(\"../proj/Projection.js\").default} projection The view projection.\n * @return {import(\"../coordinate.js\").Coordinate} The point closest to the specified line segment.\n */\nfunction closestOnSegmentData(pointCoordinates, segmentData, projection) {\n  const geometry = segmentData.geometry;\n\n  if (\n    geometry.getType() === 'Circle' &&\n    segmentData.index === CIRCLE_CIRCUMFERENCE_INDEX\n  ) {\n    let circleGeometry = /** @type {import(\"../geom/Circle.js\").default} */ (\n      geometry\n    );\n    const userProjection = getUserProjection();\n    if (userProjection) {\n      circleGeometry = /** @type {import(\"../geom/Circle.js\").default} */ (\n        circleGeometry.clone().transform(userProjection, projection)\n      );\n    }\n    return toUserCoordinate(\n      circleGeometry.getClosestPoint(\n        fromUserCoordinate(pointCoordinates, projection)\n      ),\n      projection\n    );\n  }\n  const coordinate = fromUserCoordinate(pointCoordinates, projection);\n  tempSegment[0] = fromUserCoordinate(segmentData.segment[0], projection);\n  tempSegment[1] = fromUserCoordinate(segmentData.segment[1], projection);\n  return toUserCoordinate(\n    closestOnSegment(coordinate, tempSegment),\n    projection\n  );\n}\n\n/**\n * @return {import(\"../style/Style.js\").StyleFunction} Styles.\n */\nfunction getDefaultStyleFunction() {\n  const style = createEditingStyle();\n  return function (feature, resolution) {\n    return style['Point'];\n  };\n}\n\nexport default Modify;\n", "/**\n * @module ol/interaction/Select\n */\nimport Collection from '../Collection.js';\nimport CollectionEventType from '../CollectionEventType.js';\nimport Event from '../events/Event.js';\nimport Feature from '../Feature.js';\nimport Interaction from './Interaction.js';\nimport VectorLayer from '../layer/Vector.js';\nimport {TRUE} from '../functions.js';\nimport {clear} from '../obj.js';\nimport {createEditingStyle} from '../style/Style.js';\nimport {extend} from '../array.js';\nimport {getUid} from '../util.js';\nimport {never, shiftKeyOnly, singleClick} from '../events/condition.js';\n\n/**\n * @enum {string}\n */\nconst SelectEventType = {\n  /**\n   * Triggered when feature(s) has been (de)selected.\n   * @event SelectEvent#select\n   * @api\n   */\n  SELECT: 'select',\n};\n\n/**\n * A function that takes an {@link module:ol/Feature~Feature} and returns `true` if the feature may be\n * selected or `false` otherwise.\n * @typedef {function(import(\"../Feature.js\").default, import(\"../layer/Layer.js\").default<import(\"../source/Source\").default>):boolean} FilterFunction\n */\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../events/condition.js\").Condition} [addCondition] A function\n * that takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * boolean to indicate whether that event should be handled.\n * By default, this is {@link module:ol/events/condition.never}. Use this if you\n * want to use different events for add and remove instead of `toggle`.\n * @property {import(\"../events/condition.js\").Condition} [condition] A function that\n * takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * boolean to indicate whether that event should be handled. This is the event\n * for the selected features as a whole. By default, this is\n * {@link module:ol/events/condition.singleClick}. Clicking on a feature selects that\n * feature and removes any that were in the selection. Clicking outside any\n * feature removes all from the selection.\n * See `toggle`, `add`, `remove` options for adding/removing extra features to/\n * from the selection.\n * @property {Array<import(\"../layer/Layer.js\").default>|function(import(\"../layer/Layer.js\").default<import(\"../source/Source\").default>): boolean} [layers]\n * A list of layers from which features should be selected. Alternatively, a\n * filter function can be provided. The function will be called for each layer\n * in the map and should return `true` for layers that you want to be\n * selectable. If the option is absent, all visible layers will be considered\n * selectable.\n * @property {import(\"../style/Style.js\").StyleLike|null} [style]\n * Style for the selected features. By default the default edit style is used\n * (see {@link module:ol/style/Style~Style}). Set to `null` if this interaction should not apply\n * any style changes for selected features.\n * If set to a falsey value, the selected feature's style will not change.\n * @property {import(\"../events/condition.js\").Condition} [removeCondition] A function\n * that takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * boolean to indicate whether that event should be handled.\n * By default, this is {@link module:ol/events/condition.never}. Use this if you\n * want to use different events for add and remove instead of `toggle`.\n * @property {import(\"../events/condition.js\").Condition} [toggleCondition] A function\n * that takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * boolean to indicate whether that event should be handled. This is in addition\n * to the `condition` event. By default,\n * {@link module:ol/events/condition.shiftKeyOnly}, i.e. pressing `shift` as\n * well as the `condition` event, adds that feature to the current selection if\n * it is not currently selected, and removes it if it is. See `add` and `remove`\n * if you want to use different events instead of a toggle.\n * @property {boolean} [multi=false] A boolean that determines if the default\n * behaviour should select only single features or all (overlapping) features at\n * the clicked map position. The default of `false` means single select.\n * @property {Collection<Feature>} [features]\n * Collection where the interaction will place selected features. Optional. If\n * not set the interaction will create a collection. In any case the collection\n * used by the interaction is returned by\n * {@link module:ol/interaction/Select~Select#getFeatures}.\n * @property {FilterFunction} [filter] A function\n * that takes an {@link module:ol/Feature~Feature} and an\n * {@link module:ol/layer/Layer~Layer} and returns `true` if the feature may be\n * selected or `false` otherwise.\n * @property {number} [hitTolerance=0] Hit-detection tolerance. Pixels inside\n * the radius around the given position will be checked for features.\n */\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/interaction/Select~Select} instances are instances of\n * this type.\n */\nexport class SelectEvent extends Event {\n  /**\n   * @param {SelectEventType} type The event type.\n   * @param {Array<import(\"../Feature.js\").default>} selected Selected features.\n   * @param {Array<import(\"../Feature.js\").default>} deselected Deselected features.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Associated\n   *     {@link module:ol/MapBrowserEvent~MapBrowserEvent}.\n   */\n  constructor(type, selected, deselected, mapBrowserEvent) {\n    super(type);\n\n    /**\n     * Selected features array.\n     * @type {Array<import(\"../Feature.js\").default>}\n     * @api\n     */\n    this.selected = selected;\n\n    /**\n     * Deselected features array.\n     * @type {Array<import(\"../Feature.js\").default>}\n     * @api\n     */\n    this.deselected = deselected;\n\n    /**\n     * Associated {@link module:ol/MapBrowserEvent~MapBrowserEvent}.\n     * @type {import(\"../MapBrowserEvent.js\").default}\n     * @api\n     */\n    this.mapBrowserEvent = mapBrowserEvent;\n  }\n}\n\n/**\n * Original feature styles to reset to when features are no longer selected.\n * @type {Object<number, import(\"../style/Style.js\").default|Array<import(\"../style/Style.js\").default>|import(\"../style/Style.js\").StyleFunction>}\n */\nconst originalFeatureStyles = {};\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types|\n *     'change:active', import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<'select', SelectEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types|\n *     'change:active'|'select', Return>} SelectOnSignature\n */\n\n/**\n * @classdesc\n * Interaction for selecting vector features. By default, selected features are\n * styled differently, so this interaction can be used for visual highlighting,\n * as well as selecting features for other actions, such as modification or\n * output. There are three ways of controlling which features are selected:\n * using the browser event as defined by the `condition` and optionally the\n * `toggle`, `add`/`remove`, and `multi` options; a `layers` filter; and a\n * further feature filter using the `filter` option.\n *\n * @fires SelectEvent\n * @api\n */\nclass Select extends Interaction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    super();\n\n    /***\n     * @type {SelectOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {SelectOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {SelectOnSignature<void>}\n     */\n    this.un;\n\n    options = options ? options : {};\n\n    /**\n     * @private\n     */\n    this.boundAddFeature_ = this.addFeature_.bind(this);\n\n    /**\n     * @private\n     */\n    this.boundRemoveFeature_ = this.removeFeature_.bind(this);\n\n    /**\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.condition_ = options.condition ? options.condition : singleClick;\n\n    /**\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.addCondition_ = options.addCondition ? options.addCondition : never;\n\n    /**\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.removeCondition_ = options.removeCondition\n      ? options.removeCondition\n      : never;\n\n    /**\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.toggleCondition_ = options.toggleCondition\n      ? options.toggleCondition\n      : shiftKeyOnly;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.multi_ = options.multi ? options.multi : false;\n\n    /**\n     * @private\n     * @type {FilterFunction}\n     */\n    this.filter_ = options.filter ? options.filter : TRUE;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.hitTolerance_ = options.hitTolerance ? options.hitTolerance : 0;\n\n    /**\n     * @private\n     * @type {import(\"../style/Style.js\").default|Array<import(\"../style/Style.js\").default>|import(\"../style/Style.js\").StyleFunction|null}\n     */\n    this.style_ =\n      options.style !== undefined ? options.style : getDefaultStyleFunction();\n\n    /**\n     * @private\n     * @type {Collection<Feature>}\n     */\n    this.features_ = options.features || new Collection();\n\n    /** @type {function(import(\"../layer/Layer.js\").default<import(\"../source/Source\").default>): boolean} */\n    let layerFilter;\n    if (options.layers) {\n      if (typeof options.layers === 'function') {\n        layerFilter = options.layers;\n      } else {\n        const layers = options.layers;\n        layerFilter = function (layer) {\n          return layers.includes(layer);\n        };\n      }\n    } else {\n      layerFilter = TRUE;\n    }\n\n    /**\n     * @private\n     * @type {function(import(\"../layer/Layer.js\").default<import(\"../source/Source\").default>): boolean}\n     */\n    this.layerFilter_ = layerFilter;\n\n    /**\n     * An association between selected feature (key)\n     * and layer (value)\n     * @private\n     * @type {Object<string, import(\"../layer/Layer.js\").default>}\n     */\n    this.featureLayerAssociation_ = {};\n  }\n\n  /**\n   * @param {import(\"../Feature.js\").default} feature Feature.\n   * @param {import(\"../layer/Layer.js\").default} layer Layer.\n   * @private\n   */\n  addFeatureLayerAssociation_(feature, layer) {\n    this.featureLayerAssociation_[getUid(feature)] = layer;\n  }\n\n  /**\n   * Get the selected features.\n   * @return {Collection<Feature>} Features collection.\n   * @api\n   */\n  getFeatures() {\n    return this.features_;\n  }\n\n  /**\n   * Returns the Hit-detection tolerance.\n   * @return {number} Hit tolerance in pixels.\n   * @api\n   */\n  getHitTolerance() {\n    return this.hitTolerance_;\n  }\n\n  /**\n   * Returns the associated {@link module:ol/layer/Vector~VectorLayer vector layer} of\n   * a selected feature.\n   * @param {import(\"../Feature.js\").default} feature Feature\n   * @return {import('../layer/Vector.js').default} Layer.\n   * @api\n   */\n  getLayer(feature) {\n    return /** @type {import('../layer/Vector.js').default} */ (\n      this.featureLayerAssociation_[getUid(feature)]\n    );\n  }\n\n  /**\n   * Hit-detection tolerance. Pixels inside the radius around the given position\n   * will be checked for features.\n   * @param {number} hitTolerance Hit tolerance in pixels.\n   * @api\n   */\n  setHitTolerance(hitTolerance) {\n    this.hitTolerance_ = hitTolerance;\n  }\n\n  /**\n   * Remove the interaction from its current map, if any,  and attach it to a new\n   * map, if any. Pass `null` to just remove the interaction from the current map.\n   * @param {import(\"../Map.js\").default|null} map Map.\n   * @api\n   */\n  setMap(map) {\n    const currentMap = this.getMap();\n    if (currentMap && this.style_) {\n      this.features_.forEach(this.restorePreviousStyle_.bind(this));\n    }\n    super.setMap(map);\n    if (map) {\n      this.features_.addEventListener(\n        CollectionEventType.ADD,\n        this.boundAddFeature_\n      );\n      this.features_.addEventListener(\n        CollectionEventType.REMOVE,\n        this.boundRemoveFeature_\n      );\n\n      if (this.style_) {\n        this.features_.forEach(this.applySelectedStyle_.bind(this));\n      }\n    } else {\n      this.features_.removeEventListener(\n        CollectionEventType.ADD,\n        this.boundAddFeature_\n      );\n      this.features_.removeEventListener(\n        CollectionEventType.REMOVE,\n        this.boundRemoveFeature_\n      );\n    }\n  }\n\n  /**\n   * @param {import(\"../Collection.js\").CollectionEvent<Feature>} evt Event.\n   * @private\n   */\n  addFeature_(evt) {\n    const feature = evt.element;\n    if (this.style_) {\n      this.applySelectedStyle_(feature);\n    }\n    if (!this.getLayer(feature)) {\n      const layer = /** @type {VectorLayer} */ (\n        this.getMap()\n          .getAllLayers()\n          .find(function (layer) {\n            if (\n              layer instanceof VectorLayer &&\n              layer.getSource() &&\n              layer.getSource().hasFeature(feature)\n            ) {\n              return layer;\n            }\n          })\n      );\n      if (layer) {\n        this.addFeatureLayerAssociation_(feature, layer);\n      }\n    }\n  }\n\n  /**\n   * @param {import(\"../Collection.js\").CollectionEvent<Feature>} evt Event.\n   * @private\n   */\n  removeFeature_(evt) {\n    if (this.style_) {\n      this.restorePreviousStyle_(evt.element);\n    }\n  }\n\n  /**\n   * @return {import(\"../style/Style.js\").StyleLike|null} Select style.\n   */\n  getStyle() {\n    return this.style_;\n  }\n\n  /**\n   * @param {Feature} feature Feature\n   * @private\n   */\n  applySelectedStyle_(feature) {\n    const key = getUid(feature);\n    if (!(key in originalFeatureStyles)) {\n      originalFeatureStyles[key] = feature.getStyle();\n    }\n    feature.setStyle(this.style_);\n  }\n\n  /**\n   * @param {Feature} feature Feature\n   * @private\n   */\n  restorePreviousStyle_(feature) {\n    const interactions = this.getMap().getInteractions().getArray();\n    for (let i = interactions.length - 1; i >= 0; --i) {\n      const interaction = interactions[i];\n      if (\n        interaction !== this &&\n        interaction instanceof Select &&\n        interaction.getStyle() &&\n        interaction.getFeatures().getArray().lastIndexOf(feature) !== -1\n      ) {\n        feature.setStyle(interaction.getStyle());\n        return;\n      }\n    }\n\n    const key = getUid(feature);\n    feature.setStyle(originalFeatureStyles[key]);\n    delete originalFeatureStyles[key];\n  }\n\n  /**\n   * @param {Feature} feature Feature.\n   * @private\n   */\n  removeFeatureLayerAssociation_(feature) {\n    delete this.featureLayerAssociation_[getUid(feature)];\n  }\n\n  /**\n   * Handles the {@link module:ol/MapBrowserEvent~MapBrowserEvent map browser event} and may change the\n   * selected state of features.\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n   * @return {boolean} `false` to stop event propagation.\n   */\n  handleEvent(mapBrowserEvent) {\n    if (!this.condition_(mapBrowserEvent)) {\n      return true;\n    }\n    const add = this.addCondition_(mapBrowserEvent);\n    const remove = this.removeCondition_(mapBrowserEvent);\n    const toggle = this.toggleCondition_(mapBrowserEvent);\n    const set = !add && !remove && !toggle;\n    const map = mapBrowserEvent.map;\n    const features = this.getFeatures();\n\n    /**\n     * @type {Array<Feature>}\n     */\n    const deselected = [];\n\n    /**\n     * @type {Array<Feature>}\n     */\n    const selected = [];\n\n    if (set) {\n      // Replace the currently selected feature(s) with the feature(s) at the\n      // pixel, or clear the selected feature(s) if there is no feature at\n      // the pixel.\n      clear(this.featureLayerAssociation_);\n      map.forEachFeatureAtPixel(\n        mapBrowserEvent.pixel,\n        /**\n         * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n         * @param {import(\"../layer/Layer.js\").default} layer Layer.\n         * @return {boolean|undefined} Continue to iterate over the features.\n         */\n        (feature, layer) => {\n          if (!(feature instanceof Feature) || !this.filter_(feature, layer)) {\n            return;\n          }\n          this.addFeatureLayerAssociation_(feature, layer);\n          selected.push(feature);\n          return !this.multi_;\n        },\n        {\n          layerFilter: this.layerFilter_,\n          hitTolerance: this.hitTolerance_,\n        }\n      );\n      for (let i = features.getLength() - 1; i >= 0; --i) {\n        const feature = features.item(i);\n        const index = selected.indexOf(feature);\n        if (index > -1) {\n          // feature is already selected\n          selected.splice(index, 1);\n        } else {\n          features.remove(feature);\n          deselected.push(feature);\n        }\n      }\n      if (selected.length !== 0) {\n        features.extend(selected);\n      }\n    } else {\n      // Modify the currently selected feature(s).\n      map.forEachFeatureAtPixel(\n        mapBrowserEvent.pixel,\n        /**\n         * @param {import(\"../Feature.js\").FeatureLike} feature Feature.\n         * @param {import(\"../layer/Layer.js\").default} layer Layer.\n         * @return {boolean|undefined} Continue to iterate over the features.\n         */\n        (feature, layer) => {\n          if (!(feature instanceof Feature) || !this.filter_(feature, layer)) {\n            return;\n          }\n          if ((add || toggle) && !features.getArray().includes(feature)) {\n            this.addFeatureLayerAssociation_(feature, layer);\n            selected.push(feature);\n          } else if (\n            (remove || toggle) &&\n            features.getArray().includes(feature)\n          ) {\n            deselected.push(feature);\n            this.removeFeatureLayerAssociation_(feature);\n          }\n          return !this.multi_;\n        },\n        {\n          layerFilter: this.layerFilter_,\n          hitTolerance: this.hitTolerance_,\n        }\n      );\n      for (let j = deselected.length - 1; j >= 0; --j) {\n        features.remove(deselected[j]);\n      }\n      features.extend(selected);\n    }\n    if (selected.length > 0 || deselected.length > 0) {\n      this.dispatchEvent(\n        new SelectEvent(\n          SelectEventType.SELECT,\n          selected,\n          deselected,\n          mapBrowserEvent\n        )\n      );\n    }\n    return true;\n  }\n}\n\n/**\n * @return {import(\"../style/Style.js\").StyleFunction} Styles.\n */\nfunction getDefaultStyleFunction() {\n  const styles = createEditingStyle();\n  extend(styles['Polygon'], styles['LineString']);\n  extend(styles['GeometryCollection'], styles['LineString']);\n\n  return function (feature) {\n    if (!feature.getGeometry()) {\n      return null;\n    }\n    return styles[feature.getGeometry().getType()];\n  };\n}\n\nexport default Select;\n", "/**\n * @module ol/events/SnapEvent\n */\nimport Event from './Event.js';\n\n/**\n * @enum {string}\n */\nexport const SnapEventType = {\n  /**\n   * Triggered upon snapping to vertex or edge\n   * @event SnapEvent#snap\n   * @api\n   */\n  SNAP: 'snap',\n};\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/interaction/Snap~Snap} instances are instances of this\n */\nexport class SnapEvent extends Event {\n  /**\n   * @param {SnapEventType} type Type.\n   * @param {Object} options Options.\n   * @param {import(\"../coordinate.js\").Coordinate} options.vertex The snapped vertex.\n   * @param {import(\"../coordinate.js\").Coordinate} options.vertexPixel The pixel of the snapped vertex.\n   * @param {import(\"../Feature.js\").default} options.feature The feature being snapped.\n   */\n  constructor(type, options) {\n    super(type);\n    /**\n     * The Map coordinate of the snapped point.\n     * @type {import(\"../coordinate.js\").Coordinate}\n     * @api\n     */\n    this.vertex = options.vertex;\n    /**\n     * The Map pixel of the snapped point.\n     * @type {Array<number>&Array<number>}\n     * @api\n     */\n    this.vertexPixel = options.vertexPixel;\n    /**\n     * The feature closest to the snapped point.\n     * @type {import(\"../Feature.js\").default<import(\"../geom/Geometry.js\").default>}\n     * @api\n     */\n    this.feature = options.feature;\n  }\n}\n", "/**\n * @module ol/interaction/Snap\n */\nimport CollectionEventType from '../CollectionEventType.js';\nimport EventType from '../events/EventType.js';\nimport PointerInteraction from './Pointer.js';\nimport RBush from '../structs/RBush.js';\nimport VectorEventType from '../source/VectorEventType.js';\nimport {FALSE, TRUE} from '../functions.js';\nimport {SnapEvent, SnapEventType} from '../events/SnapEvent.js';\nimport {boundingExtent, buffer, createEmpty} from '../extent.js';\nimport {\n  closestOnCircle,\n  closestOnSegment,\n  squaredDistance,\n} from '../coordinate.js';\nimport {fromCircle} from '../geom/Polygon.js';\nimport {\n  fromUserCoordinate,\n  getUserProjection,\n  toUserCoordinate,\n  toUserExtent,\n} from '../proj.js';\nimport {getUid} from '../util.js';\nimport {listen, unlistenByKey} from '../events.js';\n\n/**\n * @typedef {Object} Result\n * @property {import(\"../coordinate.js\").Coordinate|null} vertex Vertex.\n * @property {import(\"../pixel.js\").Pixel|null} vertexPixel VertexPixel.\n * @property {import(\"../Feature.js\").default|null} feature Feature.\n */\n\n/**\n * @typedef {Object} SegmentData\n * @property {import(\"../Feature.js\").default} feature Feature.\n * @property {Array<import(\"../coordinate.js\").Coordinate>} segment Segment.\n */\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../Collection.js\").default<import(\"../Feature.js\").default>} [features] Snap to these features. Either this option or source should be provided.\n * @property {boolean} [edge=true] Snap to edges.\n * @property {boolean} [vertex=true] Snap to vertices.\n * @property {number} [pixelTolerance=10] Pixel tolerance for considering the pointer close enough to a segment or\n * vertex for snapping.\n * @property {import(\"../source/Vector.js\").default} [source] Snap to features from this source. Either this option or features should be provided\n */\n\n/**\n * @param  {import(\"../source/Vector.js\").VectorSourceEvent|import(\"../Collection.js\").CollectionEvent<import(\"../Feature.js\").default>} evt Event.\n * @return {import(\"../Feature.js\").default|null} Feature.\n */\nfunction getFeatureFromEvent(evt) {\n  if (\n    /** @type {import(\"../source/Vector.js\").VectorSourceEvent} */ (evt).feature\n  ) {\n    return /** @type {import(\"../source/Vector.js\").VectorSourceEvent} */ (evt)\n      .feature;\n  }\n  if (\n    /** @type {import(\"../Collection.js\").CollectionEvent<import(\"../Feature.js\").default>} */ (\n      evt\n    ).element\n  ) {\n    return /** @type {import(\"../Collection.js\").CollectionEvent<import(\"../Feature.js\").default>} */ (\n      evt\n    ).element;\n  }\n  return null;\n}\n\nconst tempSegment = [];\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types|\n *     'change:active', import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<'snap', SnapEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types|\n *     'change:active'|'snap', Return>} SnapOnSignature\n */\n\n/**\n * @classdesc\n * Handles snapping of vector features while modifying or drawing them.  The\n * features can come from a {@link module:ol/source/Vector~VectorSource} or {@link module:ol/Collection~Collection}\n * Any interaction object that allows the user to interact\n * with the features using the mouse can benefit from the snapping, as long\n * as it is added before.\n *\n * The snap interaction modifies map browser event `coordinate` and `pixel`\n * properties to force the snap to occur to any interaction that them.\n *\n * Example:\n *\n *     import Snap from 'ol/interaction/Snap.js';\n *\n *     const snap = new Snap({\n *       source: source\n *     });\n *\n *     map.addInteraction(snap);\n *\n * @fires SnapEvent\n * @api\n */\nclass Snap extends PointerInteraction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    const pointerOptions = /** @type {import(\"./Pointer.js\").Options} */ (\n      options\n    );\n\n    if (!pointerOptions.handleDownEvent) {\n      pointerOptions.handleDownEvent = TRUE;\n    }\n\n    if (!pointerOptions.stopDown) {\n      pointerOptions.stopDown = FALSE;\n    }\n\n    super(pointerOptions);\n\n    /***\n     * @type {SnapOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {SnapOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {SnapOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * @type {import(\"../source/Vector.js\").default|null}\n     * @private\n     */\n    this.source_ = options.source ? options.source : null;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.vertex_ = options.vertex !== undefined ? options.vertex : true;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.edge_ = options.edge !== undefined ? options.edge : true;\n\n    /**\n     * @type {import(\"../Collection.js\").default<import(\"../Feature.js\").default>|null}\n     * @private\n     */\n    this.features_ = options.features ? options.features : null;\n\n    /**\n     * @type {Array<import(\"../events.js\").EventsKey>}\n     * @private\n     */\n    this.featuresListenerKeys_ = [];\n\n    /**\n     * @type {Object<string, import(\"../events.js\").EventsKey>}\n     * @private\n     */\n    this.featureChangeListenerKeys_ = {};\n\n    /**\n     * Extents are preserved so indexed segment can be quickly removed\n     * when its feature geometry changes\n     * @type {Object<string, import(\"../extent.js\").Extent>}\n     * @private\n     */\n    this.indexedFeaturesExtents_ = {};\n\n    /**\n     * If a feature geometry changes while a pointer drag|move event occurs, the\n     * feature doesn't get updated right away.  It will be at the next 'pointerup'\n     * event fired.\n     * @type {!Object<string, import(\"../Feature.js\").default>}\n     * @private\n     */\n    this.pendingFeatures_ = {};\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.pixelTolerance_ =\n      options.pixelTolerance !== undefined ? options.pixelTolerance : 10;\n\n    /**\n     * Segment RTree for each layer\n     * @type {import(\"../structs/RBush.js\").default<SegmentData>}\n     * @private\n     */\n    this.rBush_ = new RBush();\n\n    /**\n     * @const\n     * @private\n     * @type {Object<string, function(Array<Array<import('../coordinate.js').Coordinate>>, import(\"../geom/Geometry.js\").default): void>}\n     */\n    this.GEOMETRY_SEGMENTERS_ = {\n      'Point': this.segmentPointGeometry_.bind(this),\n      'LineString': this.segmentLineStringGeometry_.bind(this),\n      'LinearRing': this.segmentLineStringGeometry_.bind(this),\n      'Polygon': this.segmentPolygonGeometry_.bind(this),\n      'MultiPoint': this.segmentMultiPointGeometry_.bind(this),\n      'MultiLineString': this.segmentMultiLineStringGeometry_.bind(this),\n      'MultiPolygon': this.segmentMultiPolygonGeometry_.bind(this),\n      'GeometryCollection': this.segmentGeometryCollectionGeometry_.bind(this),\n      'Circle': this.segmentCircleGeometry_.bind(this),\n    };\n  }\n\n  /**\n   * Add a feature to the collection of features that we may snap to.\n   * @param {import(\"../Feature.js\").default} feature Feature.\n   * @param {boolean} [register] Whether to listen to the feature change or not\n   *     Defaults to `true`.\n   * @api\n   */\n  addFeature(feature, register) {\n    register = register !== undefined ? register : true;\n    const feature_uid = getUid(feature);\n    const geometry = feature.getGeometry();\n    if (geometry) {\n      const segmenter = this.GEOMETRY_SEGMENTERS_[geometry.getType()];\n      if (segmenter) {\n        this.indexedFeaturesExtents_[feature_uid] = geometry.getExtent(\n          createEmpty()\n        );\n        const segments =\n          /** @type {Array<Array<import('../coordinate.js').Coordinate>>} */ ([]);\n        segmenter(segments, geometry);\n        if (segments.length === 1) {\n          this.rBush_.insert(boundingExtent(segments[0]), {\n            feature: feature,\n            segment: segments[0],\n          });\n        } else if (segments.length > 1) {\n          const extents = segments.map((s) => boundingExtent(s));\n          const segmentsData = segments.map((segment) => ({\n            feature: feature,\n            segment: segment,\n          }));\n          this.rBush_.load(extents, segmentsData);\n        }\n      }\n    }\n\n    if (register) {\n      this.featureChangeListenerKeys_[feature_uid] = listen(\n        feature,\n        EventType.CHANGE,\n        this.handleFeatureChange_,\n        this\n      );\n    }\n  }\n\n  /**\n   * @return {import(\"../Collection.js\").default<import(\"../Feature.js\").default>|Array<import(\"../Feature.js\").default>} Features.\n   * @private\n   */\n  getFeatures_() {\n    /** @type {import(\"../Collection.js\").default<import(\"../Feature.js\").default>|Array<import(\"../Feature.js\").default>} */\n    let features;\n    if (this.features_) {\n      features = this.features_;\n    } else if (this.source_) {\n      features = this.source_.getFeatures();\n    }\n    return features;\n  }\n\n  /**\n   * @param {import(\"../MapBrowserEvent.js\").default} evt Map browser event.\n   * @return {boolean} `false` to stop event propagation.\n   * @api\n   */\n  handleEvent(evt) {\n    const result = this.snapTo(evt.pixel, evt.coordinate, evt.map);\n    if (result) {\n      evt.coordinate = result.vertex.slice(0, 2);\n      evt.pixel = result.vertexPixel;\n      this.dispatchEvent(\n        new SnapEvent(SnapEventType.SNAP, {\n          vertex: evt.coordinate,\n          vertexPixel: evt.pixel,\n          feature: result.feature,\n        })\n      );\n    }\n    return super.handleEvent(evt);\n  }\n\n  /**\n   * @param {import(\"../source/Vector.js\").VectorSourceEvent|import(\"../Collection.js\").CollectionEvent<import(\"../Feature.js\").default>} evt Event.\n   * @private\n   */\n  handleFeatureAdd_(evt) {\n    const feature = getFeatureFromEvent(evt);\n    if (feature) {\n      this.addFeature(feature);\n    }\n  }\n\n  /**\n   * @param {import(\"../source/Vector.js\").VectorSourceEvent|import(\"../Collection.js\").CollectionEvent<import(\"../Feature.js\").default>} evt Event.\n   * @private\n   */\n  handleFeatureRemove_(evt) {\n    const feature = getFeatureFromEvent(evt);\n    if (feature) {\n      this.removeFeature(feature);\n    }\n  }\n\n  /**\n   * @param {import(\"../events/Event.js\").default} evt Event.\n   * @private\n   */\n  handleFeatureChange_(evt) {\n    const feature = /** @type {import(\"../Feature.js\").default} */ (evt.target);\n    if (this.handlingDownUpSequence) {\n      const uid = getUid(feature);\n      if (!(uid in this.pendingFeatures_)) {\n        this.pendingFeatures_[uid] = feature;\n      }\n    } else {\n      this.updateFeature_(feature);\n    }\n  }\n\n  /**\n   * Handle pointer up events.\n   * @param {import(\"../MapBrowserEvent.js\").default} evt Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleUpEvent(evt) {\n    const featuresToUpdate = Object.values(this.pendingFeatures_);\n    if (featuresToUpdate.length) {\n      featuresToUpdate.forEach(this.updateFeature_.bind(this));\n      this.pendingFeatures_ = {};\n    }\n    return false;\n  }\n\n  /**\n   * Remove a feature from the collection of features that we may snap to.\n   * @param {import(\"../Feature.js\").default} feature Feature\n   * @param {boolean} [unlisten] Whether to unlisten to the feature change\n   *     or not. Defaults to `true`.\n   * @api\n   */\n  removeFeature(feature, unlisten) {\n    const unregister = unlisten !== undefined ? unlisten : true;\n    const feature_uid = getUid(feature);\n    const extent = this.indexedFeaturesExtents_[feature_uid];\n    if (extent) {\n      const rBush = this.rBush_;\n      const nodesToRemove = [];\n      rBush.forEachInExtent(extent, function (node) {\n        if (feature === node.feature) {\n          nodesToRemove.push(node);\n        }\n      });\n      for (let i = nodesToRemove.length - 1; i >= 0; --i) {\n        rBush.remove(nodesToRemove[i]);\n      }\n    }\n\n    if (unregister) {\n      unlistenByKey(this.featureChangeListenerKeys_[feature_uid]);\n      delete this.featureChangeListenerKeys_[feature_uid];\n    }\n  }\n\n  /**\n   * Remove the interaction from its current map and attach it to the new map.\n   * Subclasses may set up event handlers to get notified about changes to\n   * the map here.\n   * @param {import(\"../Map.js\").default} map Map.\n   */\n  setMap(map) {\n    const currentMap = this.getMap();\n    const keys = this.featuresListenerKeys_;\n    const features = /** @type {Array<import(\"../Feature.js\").default>} */ (\n      this.getFeatures_()\n    );\n\n    if (currentMap) {\n      keys.forEach(unlistenByKey);\n      keys.length = 0;\n      this.rBush_.clear();\n      Object.values(this.featureChangeListenerKeys_).forEach(unlistenByKey);\n      this.featureChangeListenerKeys_ = {};\n    }\n    super.setMap(map);\n\n    if (map) {\n      if (this.features_) {\n        keys.push(\n          listen(\n            this.features_,\n            CollectionEventType.ADD,\n            this.handleFeatureAdd_,\n            this\n          ),\n          listen(\n            this.features_,\n            CollectionEventType.REMOVE,\n            this.handleFeatureRemove_,\n            this\n          )\n        );\n      } else if (this.source_) {\n        keys.push(\n          listen(\n            this.source_,\n            VectorEventType.ADDFEATURE,\n            this.handleFeatureAdd_,\n            this\n          ),\n          listen(\n            this.source_,\n            VectorEventType.REMOVEFEATURE,\n            this.handleFeatureRemove_,\n            this\n          )\n        );\n      }\n      features.forEach((feature) => this.addFeature(feature));\n    }\n  }\n\n  /**\n   * @param {import(\"../pixel.js\").Pixel} pixel Pixel\n   * @param {import(\"../coordinate.js\").Coordinate} pixelCoordinate Coordinate\n   * @param {import(\"../Map.js\").default} map Map.\n   * @return {Result|null} Snap result\n   */\n  snapTo(pixel, pixelCoordinate, map) {\n    const projection = map.getView().getProjection();\n    const projectedCoordinate = fromUserCoordinate(pixelCoordinate, projection);\n\n    const box = toUserExtent(\n      buffer(\n        boundingExtent([projectedCoordinate]),\n        map.getView().getResolution() * this.pixelTolerance_\n      ),\n      projection\n    );\n\n    const segments = this.rBush_.getInExtent(box);\n    const segmentsLength = segments.length;\n    if (segmentsLength === 0) {\n      return null;\n    }\n\n    let closestVertex;\n    let minSquaredDistance = Infinity;\n    let closestFeature;\n\n    const squaredPixelTolerance = this.pixelTolerance_ * this.pixelTolerance_;\n    const getResult = () => {\n      if (closestVertex) {\n        const vertexPixel = map.getPixelFromCoordinate(closestVertex);\n        const squaredPixelDistance = squaredDistance(pixel, vertexPixel);\n        if (squaredPixelDistance <= squaredPixelTolerance) {\n          return {\n            vertex: closestVertex,\n            vertexPixel: [\n              Math.round(vertexPixel[0]),\n              Math.round(vertexPixel[1]),\n            ],\n            feature: closestFeature,\n          };\n        }\n      }\n      return null;\n    };\n\n    if (this.vertex_) {\n      for (let i = 0; i < segmentsLength; ++i) {\n        const segmentData = segments[i];\n        if (segmentData.feature.getGeometry().getType() !== 'Circle') {\n          segmentData.segment.forEach((vertex) => {\n            const tempVertexCoord = fromUserCoordinate(vertex, projection);\n            const delta = squaredDistance(projectedCoordinate, tempVertexCoord);\n            if (delta < minSquaredDistance) {\n              closestVertex = vertex;\n              minSquaredDistance = delta;\n              closestFeature = segmentData.feature;\n            }\n          });\n        }\n      }\n      const result = getResult();\n      if (result) {\n        return result;\n      }\n    }\n\n    if (this.edge_) {\n      for (let i = 0; i < segmentsLength; ++i) {\n        let vertex = null;\n        const segmentData = segments[i];\n        if (segmentData.feature.getGeometry().getType() === 'Circle') {\n          let circleGeometry = segmentData.feature.getGeometry();\n          const userProjection = getUserProjection();\n          if (userProjection) {\n            circleGeometry = circleGeometry\n              .clone()\n              .transform(userProjection, projection);\n          }\n          vertex = closestOnCircle(\n            projectedCoordinate,\n            /** @type {import(\"../geom/Circle.js\").default} */ (circleGeometry)\n          );\n        } else {\n          const [segmentStart, segmentEnd] = segmentData.segment;\n          // points have only one coordinate\n          if (segmentEnd) {\n            tempSegment[0] = fromUserCoordinate(segmentStart, projection);\n            tempSegment[1] = fromUserCoordinate(segmentEnd, projection);\n            vertex = closestOnSegment(projectedCoordinate, tempSegment);\n          }\n        }\n        if (vertex) {\n          const delta = squaredDistance(projectedCoordinate, vertex);\n          if (delta < minSquaredDistance) {\n            closestVertex = toUserCoordinate(vertex, projection);\n            minSquaredDistance = delta;\n          }\n        }\n      }\n\n      const result = getResult();\n      if (result) {\n        return result;\n      }\n    }\n\n    return null;\n  }\n\n  /**\n   * @param {import(\"../Feature.js\").default} feature Feature\n   * @private\n   */\n  updateFeature_(feature) {\n    this.removeFeature(feature, false);\n    this.addFeature(feature, false);\n  }\n\n  /**\n   * @param {Array<Array<import('../coordinate.js').Coordinate>>} segments Segments\n   * @param {import(\"../geom/Circle.js\").default} geometry Geometry.\n   * @private\n   */\n  segmentCircleGeometry_(segments, geometry) {\n    const projection = this.getMap().getView().getProjection();\n    let circleGeometry = geometry;\n    const userProjection = getUserProjection();\n    if (userProjection) {\n      circleGeometry = /** @type {import(\"../geom/Circle.js\").default} */ (\n        circleGeometry.clone().transform(userProjection, projection)\n      );\n    }\n    const polygon = fromCircle(circleGeometry);\n    if (userProjection) {\n      polygon.transform(projection, userProjection);\n    }\n    const coordinates = polygon.getCoordinates()[0];\n    for (let i = 0, ii = coordinates.length - 1; i < ii; ++i) {\n      segments.push(coordinates.slice(i, i + 2));\n    }\n  }\n\n  /**\n   * @param {Array<Array<import('../coordinate.js').Coordinate>>} segments Segments\n   * @param {import(\"../geom/GeometryCollection.js\").default} geometry Geometry.\n   * @private\n   */\n  segmentGeometryCollectionGeometry_(segments, geometry) {\n    const geometries = geometry.getGeometriesArray();\n    for (let i = 0; i < geometries.length; ++i) {\n      const segmenter = this.GEOMETRY_SEGMENTERS_[geometries[i].getType()];\n      if (segmenter) {\n        segmenter(segments, geometries[i]);\n      }\n    }\n  }\n\n  /**\n   * @param {Array<Array<import('../coordinate.js').Coordinate>>} segments Segments\n   * @param {import(\"../geom/LineString.js\").default} geometry Geometry.\n   * @private\n   */\n  segmentLineStringGeometry_(segments, geometry) {\n    const coordinates = geometry.getCoordinates();\n    for (let i = 0, ii = coordinates.length - 1; i < ii; ++i) {\n      segments.push(coordinates.slice(i, i + 2));\n    }\n  }\n\n  /**\n   * @param {Array<Array<import('../coordinate.js').Coordinate>>} segments Segments\n   * @param {import(\"../geom/MultiLineString.js\").default} geometry Geometry.\n   * @private\n   */\n  segmentMultiLineStringGeometry_(segments, geometry) {\n    const lines = geometry.getCoordinates();\n    for (let j = 0, jj = lines.length; j < jj; ++j) {\n      const coordinates = lines[j];\n      for (let i = 0, ii = coordinates.length - 1; i < ii; ++i) {\n        segments.push(coordinates.slice(i, i + 2));\n      }\n    }\n  }\n\n  /**\n   * @param {Array<Array<import('../coordinate.js').Coordinate>>} segments Segments\n   * @param {import(\"../geom/MultiPoint.js\").default} geometry Geometry.\n   * @private\n   */\n  segmentMultiPointGeometry_(segments, geometry) {\n    geometry.getCoordinates().forEach((point) => {\n      segments.push([point]);\n    });\n  }\n\n  /**\n   * @param {Array<Array<import('../coordinate.js').Coordinate>>} segments Segments\n   * @param {import(\"../geom/MultiPolygon.js\").default} geometry Geometry.\n   * @private\n   */\n  segmentMultiPolygonGeometry_(segments, geometry) {\n    const polygons = geometry.getCoordinates();\n    for (let k = 0, kk = polygons.length; k < kk; ++k) {\n      const rings = polygons[k];\n      for (let j = 0, jj = rings.length; j < jj; ++j) {\n        const coordinates = rings[j];\n        for (let i = 0, ii = coordinates.length - 1; i < ii; ++i) {\n          segments.push(coordinates.slice(i, i + 2));\n        }\n      }\n    }\n  }\n\n  /**\n   * @param {Array<Array<import('../coordinate.js').Coordinate>>} segments Segments\n   * @param {import(\"../geom/Point.js\").default} geometry Geometry.\n   * @private\n   */\n  segmentPointGeometry_(segments, geometry) {\n    segments.push([geometry.getCoordinates()]);\n  }\n\n  /**\n   * @param {Array<Array<import('../coordinate.js').Coordinate>>} segments Segments\n   * @param {import(\"../geom/Polygon.js\").default} geometry Geometry.\n   * @private\n   */\n  segmentPolygonGeometry_(segments, geometry) {\n    const rings = geometry.getCoordinates();\n    for (let j = 0, jj = rings.length; j < jj; ++j) {\n      const coordinates = rings[j];\n      for (let i = 0, ii = coordinates.length - 1; i < ii; ++i) {\n        segments.push(coordinates.slice(i, i + 2));\n      }\n    }\n  }\n}\n\nexport default Snap;\n", "/**\n * @module ol/interaction/Translate\n */\nimport Collection from '../Collection.js';\nimport Event from '../events/Event.js';\nimport Feature from '../Feature.js';\nimport InteractionProperty from './Property.js';\nimport PointerInteraction from './Pointer.js';\nimport {TRUE} from '../functions.js';\nimport {always} from '../events/condition.js';\nimport {fromUserCoordinate, getUserProjection} from '../proj.js';\n\n/**\n * @enum {string}\n */\nconst TranslateEventType = {\n  /**\n   * Triggered upon feature translation start.\n   * @event TranslateEvent#translatestart\n   * @api\n   */\n  TRANSLATESTART: 'translatestart',\n  /**\n   * Triggered upon feature translation.\n   * @event TranslateEvent#translating\n   * @api\n   */\n  TRANSLATING: 'translating',\n  /**\n   * Triggered upon feature translation end.\n   * @event TranslateEvent#translateend\n   * @api\n   */\n  TRANSLATEEND: 'translateend',\n};\n\n/**\n * A function that takes an {@link module:ol/Feature~Feature} or\n * {@link module:ol/render/Feature~RenderFeature} and an\n * {@link module:ol/layer/Layer~Layer} and returns `true` if the feature may be\n * translated or `false` otherwise.\n * @typedef {function(Feature, import(\"../layer/Layer.js\").default<import(\"../source/Source\").default>):boolean} FilterFunction\n */\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../events/condition.js\").Condition} [condition] A function that\n * takes an {@link module:ol/MapBrowserEvent~MapBrowserEvent} and returns a\n * boolean to indicate whether that event should be handled.\n * Default is {@link module:ol/events/condition.always}.\n * @property {Collection<Feature>} [features] Features contained in this collection will be able to be translated together.\n * @property {Array<import(\"../layer/Layer.js\").default>|function(import(\"../layer/Layer.js\").default<import(\"../source/Source\").default>): boolean} [layers] A list of layers from which features should be\n * translated. Alternatively, a filter function can be provided. The\n * function will be called for each layer in the map and should return\n * `true` for layers that you want to be translatable. If the option is\n * absent, all visible layers will be considered translatable.\n * Not used if `features` is provided.\n * @property {FilterFunction} [filter] A function\n * that takes an {@link module:ol/Feature~Feature} and an\n * {@link module:ol/layer/Layer~Layer} and returns `true` if the feature may be\n * translated or `false` otherwise. Not used if `features` is provided.\n * @property {number} [hitTolerance=0] Hit-detection tolerance. Pixels inside the radius around the given position\n * will be checked for features.\n */\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/interaction/Translate~Translate} instances\n * are instances of this type.\n */\nexport class TranslateEvent extends Event {\n  /**\n   * @param {TranslateEventType} type Type.\n   * @param {Collection<Feature>} features The features translated.\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate The event coordinate.\n   * @param {import(\"../coordinate.js\").Coordinate} startCoordinate The original coordinates before.translation started\n   * @param {import(\"../MapBrowserEvent.js\").default} mapBrowserEvent Map browser event.\n   */\n  constructor(type, features, coordinate, startCoordinate, mapBrowserEvent) {\n    super(type);\n\n    /**\n     * The features being translated.\n     * @type {Collection<Feature>}\n     * @api\n     */\n    this.features = features;\n\n    /**\n     * The coordinate of the drag event.\n     * @const\n     * @type {import(\"../coordinate.js\").Coordinate}\n     * @api\n     */\n    this.coordinate = coordinate;\n\n    /**\n     * The coordinate of the start position before translation started.\n     * @const\n     * @type {import(\"../coordinate.js\").Coordinate}\n     * @api\n     */\n    this.startCoordinate = startCoordinate;\n\n    /**\n     * Associated {@link module:ol/MapBrowserEvent~MapBrowserEvent}.\n     * @type {import(\"../MapBrowserEvent.js\").default}\n     * @api\n     */\n    this.mapBrowserEvent = mapBrowserEvent;\n  }\n}\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types|\n *     'change:active', import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<'translateend'|'translatestart'|'translating', TranslateEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types|\n *     'change:active'|'translateend'|'translatestart'|'translating', Return>} TranslateOnSignature\n */\n\n/**\n * @classdesc\n * Interaction for translating (moving) features.\n * If you want to translate multiple features in a single action (for example,\n * the collection used by a select interaction), construct the interaction with\n * the `features` option.\n *\n * @fires TranslateEvent\n * @api\n */\nclass Translate extends PointerInteraction {\n  /**\n   * @param {Options} [options] Options.\n   */\n  constructor(options) {\n    options = options ? options : {};\n\n    super(/** @type {import(\"./Pointer.js\").Options} */ (options));\n\n    /***\n     * @type {TranslateOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {TranslateOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {TranslateOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * The last position we translated to.\n     * @type {import(\"../coordinate.js\").Coordinate}\n     * @private\n     */\n    this.lastCoordinate_ = null;\n\n    /**\n     * The start position before translation started.\n     * @type {import(\"../coordinate.js\").Coordinate}\n     * @private\n     */\n    this.startCoordinate_ = null;\n\n    /**\n     * @type {Collection<Feature>|null}\n     * @private\n     */\n    this.features_ = options.features !== undefined ? options.features : null;\n\n    /** @type {function(import(\"../layer/Layer.js\").default<import(\"../source/Source\").default>): boolean} */\n    let layerFilter;\n    if (options.layers && !this.features_) {\n      if (typeof options.layers === 'function') {\n        layerFilter = options.layers;\n      } else {\n        const layers = options.layers;\n        layerFilter = function (layer) {\n          return layers.includes(layer);\n        };\n      }\n    } else {\n      layerFilter = TRUE;\n    }\n\n    /**\n     * @private\n     * @type {function(import(\"../layer/Layer.js\").default<import(\"../source/Source\").default>): boolean}\n     */\n    this.layerFilter_ = layerFilter;\n\n    /**\n     * @private\n     * @type {FilterFunction}\n     */\n    this.filter_ = options.filter && !this.features_ ? options.filter : TRUE;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.hitTolerance_ = options.hitTolerance ? options.hitTolerance : 0;\n\n    /**\n     * @private\n     * @type {import(\"../events/condition.js\").Condition}\n     */\n    this.condition_ = options.condition ? options.condition : always;\n\n    /**\n     * @type {Feature}\n     * @private\n     */\n    this.lastFeature_ = null;\n\n    this.addChangeListener(\n      InteractionProperty.ACTIVE,\n      this.handleActiveChanged_\n    );\n  }\n\n  /**\n   * Handle pointer down events.\n   * @param {import(\"../MapBrowserEvent.js\").default} event Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleDownEvent(event) {\n    if (!event.originalEvent || !this.condition_(event)) {\n      return false;\n    }\n    this.lastFeature_ = this.featuresAtPixel_(event.pixel, event.map);\n    if (!this.lastCoordinate_ && this.lastFeature_) {\n      this.startCoordinate_ = event.coordinate;\n      this.lastCoordinate_ = event.coordinate;\n      this.handleMoveEvent(event);\n\n      const features = this.features_ || new Collection([this.lastFeature_]);\n\n      this.dispatchEvent(\n        new TranslateEvent(\n          TranslateEventType.TRANSLATESTART,\n          features,\n          event.coordinate,\n          this.startCoordinate_,\n          event\n        )\n      );\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Handle pointer up events.\n   * @param {import(\"../MapBrowserEvent.js\").default} event Event.\n   * @return {boolean} If the event was consumed.\n   */\n  handleUpEvent(event) {\n    if (this.lastCoordinate_) {\n      this.lastCoordinate_ = null;\n      this.handleMoveEvent(event);\n\n      const features = this.features_ || new Collection([this.lastFeature_]);\n\n      this.dispatchEvent(\n        new TranslateEvent(\n          TranslateEventType.TRANSLATEEND,\n          features,\n          event.coordinate,\n          this.startCoordinate_,\n          event\n        )\n      );\n      // cleanup\n      this.startCoordinate_ = null;\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Handle pointer drag events.\n   * @param {import(\"../MapBrowserEvent.js\").default} event Event.\n   */\n  handleDragEvent(event) {\n    if (this.lastCoordinate_) {\n      const newCoordinate = event.coordinate;\n      const projection = event.map.getView().getProjection();\n\n      const newViewCoordinate = fromUserCoordinate(newCoordinate, projection);\n      const lastViewCoordinate = fromUserCoordinate(\n        this.lastCoordinate_,\n        projection\n      );\n      const deltaX = newViewCoordinate[0] - lastViewCoordinate[0];\n      const deltaY = newViewCoordinate[1] - lastViewCoordinate[1];\n\n      const features = this.features_ || new Collection([this.lastFeature_]);\n      const userProjection = getUserProjection();\n\n      features.forEach(function (feature) {\n        const geom = feature.getGeometry();\n        if (userProjection) {\n          geom.transform(userProjection, projection);\n          geom.translate(deltaX, deltaY);\n          geom.transform(projection, userProjection);\n        } else {\n          geom.translate(deltaX, deltaY);\n        }\n        feature.setGeometry(geom);\n      });\n\n      this.lastCoordinate_ = newCoordinate;\n\n      this.dispatchEvent(\n        new TranslateEvent(\n          TranslateEventType.TRANSLATING,\n          features,\n          newCoordinate,\n          this.startCoordinate_,\n          event\n        )\n      );\n    }\n  }\n\n  /**\n   * Handle pointer move events.\n   * @param {import(\"../MapBrowserEvent.js\").default} event Event.\n   */\n  handleMoveEvent(event) {\n    const elem = event.map.getViewport();\n\n    // Change the cursor to grab/grabbing if hovering any of the features managed\n    // by the interaction\n    if (this.featuresAtPixel_(event.pixel, event.map)) {\n      elem.classList.remove(this.lastCoordinate_ ? 'ol-grab' : 'ol-grabbing');\n      elem.classList.add(this.lastCoordinate_ ? 'ol-grabbing' : 'ol-grab');\n    } else {\n      elem.classList.remove('ol-grab', 'ol-grabbing');\n    }\n  }\n\n  /**\n   * Tests to see if the given coordinates intersects any of our selected\n   * features.\n   * @param {import(\"../pixel.js\").Pixel} pixel Pixel coordinate to test for intersection.\n   * @param {import(\"../Map.js\").default} map Map to test the intersection on.\n   * @return {Feature} Returns the feature found at the specified pixel\n   * coordinates.\n   * @private\n   */\n  featuresAtPixel_(pixel, map) {\n    return map.forEachFeatureAtPixel(\n      pixel,\n      (feature, layer) => {\n        if (!(feature instanceof Feature) || !this.filter_(feature, layer)) {\n          return undefined;\n        }\n        if (this.features_ && !this.features_.getArray().includes(feature)) {\n          return undefined;\n        }\n        return feature;\n      },\n      {\n        layerFilter: this.layerFilter_,\n        hitTolerance: this.hitTolerance_,\n      }\n    );\n  }\n\n  /**\n   * Returns the Hit-detection tolerance.\n   * @return {number} Hit tolerance in pixels.\n   * @api\n   */\n  getHitTolerance() {\n    return this.hitTolerance_;\n  }\n\n  /**\n   * Hit-detection tolerance. Pixels inside the radius around the given position\n   * will be checked for features.\n   * @param {number} hitTolerance Hit tolerance in pixels.\n   * @api\n   */\n  setHitTolerance(hitTolerance) {\n    this.hitTolerance_ = hitTolerance;\n  }\n\n  /**\n   * Remove the interaction from its current map and attach it to the new map.\n   * Subclasses may set up event handlers to get notified about changes to\n   * the map here.\n   * @param {import(\"../Map.js\").default} map Map.\n   */\n  setMap(map) {\n    const oldMap = this.getMap();\n    super.setMap(map);\n    this.updateState_(oldMap);\n  }\n\n  /**\n   * @private\n   */\n  handleActiveChanged_() {\n    this.updateState_(null);\n  }\n\n  /**\n   * @param {import(\"../Map.js\").default} oldMap Old map.\n   * @private\n   */\n  updateState_(oldMap) {\n    let map = this.getMap();\n    const active = this.getActive();\n    if (!map || !active) {\n      map = map || oldMap;\n      if (map) {\n        const elem = map.getViewport();\n        elem.classList.remove('ol-grab', 'ol-grabbing');\n      }\n    }\n  }\n}\n\nexport default Translate;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAM,mBAAN,cAA+B,oBAAY;AAAA;AAAA;AAAA;AAAA,EAIzC,YAAY,aAAa;AACvB,UAAM,UAAU,cAAc,cAAc,CAAC;AAE7C;AAAA;AAAA,MACgE;AAAA,IAChE;AAEA,QAAI,QAAQ,UAAU;AACpB,WAAK,WAAW,QAAQ;AAAA,IAC1B;AAMA,SAAK,qBAAqB,QAAQ,QAAQ,QAAQ,QAAQ;AAM1D,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAMrE,SAAK,0BAA0B;AAM/B,SAAK,8BAA8B;AAMnC,SAAK,sBAAsB;AAM3B,SAAK,mBAAmB,CAAC;AAMzB,SAAK,iBAAiB,CAAC;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,iBAAiB;AAC3B,QAAI,CAAC,gBAAgB,eAAe;AAClC,aAAO;AAAA,IACT;AAEA,QAAI,YAAY;AAChB,SAAK,uBAAuB,eAAe;AAC3C,QAAI,KAAK,yBAAyB;AAChC,UAAI,gBAAgB,QAAQ,4BAAoB,aAAa;AAC3D,aAAK,gBAAgB,eAAe;AAEpC,wBAAgB,cAAc,eAAe;AAAA,MAC/C,WAAW,gBAAgB,QAAQ,4BAAoB,WAAW;AAChE,cAAM,YAAY,KAAK,cAAc,eAAe;AACpD,aAAK,0BAA0B;AAAA,MACjC;AAAA,IACF,OAAO;AACL,UAAI,gBAAgB,QAAQ,4BAAoB,aAAa;AAC3D,YAAI,KAAK,6BAA6B;AACpC,eAAK,8BAA8B;AACnC,gBAAM,UAAU,KAAK,gBAAgB,eAAe;AACpD,eAAK,0BAA0B;AAC/B,sBAAY,KAAK,SAAS,OAAO;AAAA,QACnC,OAAO;AACL,sBAAY,KAAK,SAAS,KAAK;AAC/B,eAAK,eAAe;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AACA,WAAO,CAAC;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,iBAAiB;AAC/B,QAAI,aAAa;AAEjB,UAAM,SAAS,KAAK,eAAe,CAAC;AACpC,UAAM,SAAS,KAAK,MAAM;AAC1B,UAAMA,YAAW,OAAO,UAAU,OAAO;AAEzC,QAAI,KAAK,kBAAkB,QAAW;AACpC,mBACE,KAAK,KAAK,gBAAgBA,aAAY,KAAK;AAAA,IAC/C;AACA,SAAK,gBAAgBA;AAErB,QAAI,cAAc,GAAK;AACrB,WAAK,kBAAkB;AAAA,IACzB;AAGA,UAAM,MAAM,gBAAgB;AAC5B,UAAM,OAAO,IAAI,QAAQ;AACzB,QAAI,OAAO;AACX,SAAK,yBAAyB,UAAU;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,iBAAiB;AAC/B,QAAI,KAAK,eAAe,UAAU,GAAG;AACnC,YAAM,MAAM,gBAAgB;AAC5B,WAAK,UAAU;AACf,WAAK,gBAAgB;AACrB,WAAK,kBAAkB;AACvB,WAAK,QAAQ;AACb,UAAI,CAAC,KAAK,yBAAyB;AACjC,YAAI,QAAQ,EAAE,iBAAiB;AAAA,MACjC;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,iBAAiB;AAC7B,QAAI,KAAK,eAAe,UAAU,GAAG;AACnC,YAAM,MAAM,gBAAgB;AAC5B,YAAM,OAAO,IAAI,QAAQ;AACzB,YAAM,YAAY,KAAK,kBAAkB,IAAI,IAAI;AACjD,WAAK,eAAe,KAAK,WAAW,SAAS;AAC7C,WAAK,0BAA0B;AAC/B,WAAK,8BAA8B;AACnC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,SAAS;AAChB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,iBAAiB;AACtC,QAAI,uBAAuB,eAAe,GAAG;AAC3C,YAAM,QAAQ,gBAAgB;AAE9B,YAAM,KAAK,MAAM,UAAU,SAAS;AACpC,UAAI,gBAAgB,QAAQ,4BAAoB,WAAW;AACzD,eAAO,KAAK,iBAAiB,EAAE;AAAA,MACjC,WAAW,gBAAgB,QAAQ,4BAAoB,aAAa;AAClE,aAAK,iBAAiB,EAAE,IAAI;AAAA,MAC9B,WAAW,MAAM,KAAK,kBAAkB;AAEtC,aAAK,iBAAiB,EAAE,IAAI;AAAA,MAC9B;AACA,WAAK,iBAAiB,OAAO,OAAO,KAAK,gBAAgB;AAAA,IAC3D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB;AACf,QAAI,KAAK,wBAAwB,QAAW;AAE1C,mBAAa,KAAK,mBAAmB;AACrC,WAAK,sBAAsB;AAAA,IAC7B,OAAO;AACL,WAAK,8BAA8B;AACnC,WAAK,sBAAsB;AAAA,QACzB,KAAK,gBAAgB,KAAK,IAAI;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,SAAK,8BAA8B;AACnC,SAAK,sBAAsB;AAAA,EAC7B;AACF;AAOA,SAAS,uBAAuB,iBAAiB;AAC/C,QAAM,OAAO,gBAAgB;AAC7B,SACE,SAAS,4BAAoB,eAC7B,SAAS,4BAAoB,eAC7B,SAAS,4BAAoB;AAEjC;AAEA,IAAO,2BAAQ;;;ACrOf,IAAM,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3B,cAAc;AAChB;AAOO,IAAM,mBAAN,cAA+B,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO1C,YAAY,MAAM,MAAM,UAAU,YAAY;AAC5C,UAAM,IAAI;AAOV,SAAK,WAAW;AAOhB,SAAK,OAAO;AAOZ,SAAK,aAAa;AAAA,EACpB;AACF;AAoBA,IAAM,cAAN,cAA0B,oBAAY;AAAA;AAAA;AAAA;AAAA,EAIpC,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM;AAAA,MACJ,aAAa;AAAA,IACf,CAAC;AAKD,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,SAAK,gBAAgB;AAMrB,SAAK,WAAW,CAAC;AACjB,UAAM,qBAAqB,QAAQ,qBAC/B,QAAQ,qBACR,CAAC;AACL,aAAS,IAAI,GAAG,KAAK,mBAAmB,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC3D,UAAI,SAAS,mBAAmB,CAAC;AACjC,UAAI,OAAO,WAAW,YAAY;AAChC,iBAAS,IAAI,OAAO;AAAA,MACtB;AACA,WAAK,SAAS,KAAK,MAAM;AACzB,WAAK,gBACH,KAAK,iBAAiB,OAAO,QAAQ,MAAM;AAAA,IAC/C;AAMA,SAAK,cAAc,QAAQ,aACvB,IAAc,QAAQ,UAAU,IAChC;AAMJ,SAAK,kBAAkB;AAMvB,SAAK,UAAU,QAAQ,UAAU;AAMjC,SAAK,SAAS,QAAQ,SAAS,QAAQ,SAAS;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,MAAM,OAAO;AACzB,UAAM,SAAS,MAAM,OAAO;AAC5B,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,aAAa,KAAK;AACtB,QAAI,CAAC,YAAY;AACf,mBAAa,kBAAkB;AAC/B,UAAI,CAAC,YAAY;AACf,cAAM,OAAO,IAAI,QAAQ;AACzB,qBAAa,KAAK,cAAc;AAAA,MAClC;AAAA,IACF;AAEA,QAAI;AACJ,UAAM,UAAU,KAAK;AACrB,aAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,EAAE,GAAG;AAChD,YAAM,SAAS,QAAQ,CAAC;AACxB,UAAI,QAAQ;AACZ,UAAI,KAAK,iBAAiB,OAAO,QAAQ,MAAM,eAAe;AAC5D,YAAI,SAAS,QAAW;AACtB,iBAAO,IAAI,YAAY,EAAE,OAAO,MAAM;AAAA,QACxC;AACA,gBAAQ;AAAA,MACV;AACA,YAAM,WAAW,KAAK,iBAAiB,QAAQ,OAAO;AAAA,QACpD,mBAAmB;AAAA,MACrB,CAAC;AACD,UAAI,YAAY,SAAS,SAAS,GAAG;AACnC,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,MAAM;AACnB,eAAK,QAAQ,YAAY,QAAQ;AAAA,QACnC;AACA,aAAK;AAAA,UACH,IAAI;AAAA,YACF,qBAAqB;AAAA,YACrB;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,KAAK;AACP,YAAM,WAAW,KAAK,SAAS,KAAK,SAAS,IAAI,YAAY;AAC7D,WAAK,kBAAkB;AAAA,QACrB,OAAO,UAAU,kBAAU,MAAM,KAAK,YAAY,IAAI;AAAA,QACtD,OAAO,UAAU,kBAAU,WAAW,KAAK,YAAY,IAAI;AAAA,QAC3D,OAAO,UAAU,kBAAU,UAAU,KAAK,YAAY,IAAI;AAAA,QAC1D,OAAO,UAAU,kBAAU,MAAM,KAAK,YAAY,IAAI;AAAA,MACxD;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,QAAI,CAAC,KAAK,UAAU,KAAK,QAAQ;AAC/B,WAAK,mBAAmB;AAAA,IAC1B;AACA,QAAI,KAAK,UAAU,KAAK,CAAC,QAAQ;AAC/B,WAAK,qBAAqB;AAAA,IAC5B;AACA,UAAM,UAAU,MAAM;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KAAK;AACV,SAAK,qBAAqB;AAC1B,UAAM,OAAO,GAAG;AAChB,QAAI,KAAK,UAAU,GAAG;AACpB,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,QAAQ,MAAM,SAAS;AACtC,QAAI;AACF;AAAA;AAAA,QAEG,OAAO,aAAa,MAAM,OAAO;AAAA;AAAA,IAEtC,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,QAAQ,aAAa;AAC1C,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AAChB,UAAM,QAAQ,MAAM,aAAa;AACjC,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,YAAM,OAAO,MAAM,KAAK,CAAC;AACzB,YAAM,SAAS,IAAI,WAAW;AAC9B,aAAO;AAAA,QACL,kBAAU;AAAA,QACV,KAAK,cAAc,KAAK,MAAM,IAAI;AAAA,MACpC;AACA,UAAI,KAAK,eAAe;AACtB,eAAO,kBAAkB,IAAI;AAAA,MAC/B,OAAO;AACL,eAAO,WAAW,IAAI;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AAChB,UAAM,gBAAgB;AACtB,UAAM,eAAe;AACrB,UAAM,aAAa,aAAa;AAAA,EAClC;AACF;AAEA,IAAO,sBAAQ;;;ACzSf,IAAM,oBAAN,cAAgC,gBAAmB;AAAA;AAAA;AAAA;AAAA,EAIjD,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B;AAAA;AAAA,MAAqD;AAAA,IAAQ;AAM7D,SAAK,aAAa,QAAQ,YAAY,QAAQ,YAAY;AAM1D,SAAK,aAAa;AAMlB,SAAK,iBAAiB;AAMtB,SAAK,kBAAkB;AAMvB,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,iBAAiB;AAC/B,QAAI,CAAC,UAAU,eAAe,GAAG;AAC/B;AAAA,IACF;AAEA,UAAM,MAAM,gBAAgB;AAC5B,UAAM,OAAO,IAAI,QAAQ;AACzB,UAAM,SAAS,gBAAgB;AAC/B,UAAM,SAAS,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI;AACrC,UAAM,SAAS,KAAK,CAAC,IAAI,IAAI,OAAO,CAAC;AACrC,UAAM,QAAQ,KAAK,MAAM,QAAQ,MAAM;AACvC,UAAM,YAAY,KAAK,KAAK,SAAS,SAAS,SAAS,MAAM;AAC7D,UAAM,OAAO,IAAI,QAAQ;AACzB,QAAI,KAAK,eAAe,QAAW;AACjC,YAAM,aAAa,KAAK,aAAa;AACrC,WAAK,uBAAuB,UAAU;AAAA,IACxC;AACA,SAAK,aAAa;AAClB,QAAI,KAAK,mBAAmB,QAAW;AACrC,WAAK,yBAAyB,KAAK,iBAAiB,SAAS;AAAA,IAC/D;AACA,QAAI,KAAK,mBAAmB,QAAW;AACrC,WAAK,kBAAkB,KAAK,iBAAiB;AAAA,IAC/C;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,iBAAiB;AAC7B,QAAI,CAAC,UAAU,eAAe,GAAG;AAC/B,aAAO;AAAA,IACT;AAEA,UAAM,MAAM,gBAAgB;AAC5B,UAAM,OAAO,IAAI,QAAQ;AACzB,UAAM,YAAY,KAAK,kBAAkB,IAAI,IAAI;AACjD,SAAK,eAAe,KAAK,WAAW,SAAS;AAC7C,SAAK,kBAAkB;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,iBAAiB;AAC/B,QAAI,CAAC,UAAU,eAAe,GAAG;AAC/B,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,WAAW,eAAe,GAAG;AACpC,sBAAgB,IAAI,QAAQ,EAAE,iBAAiB;AAC/C,WAAK,aAAa;AAClB,WAAK,iBAAiB;AACtB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAO,4BAAQ;;;AC1Ff,IAAM,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtB,eAAe;AACjB;AAOO,IAAM,cAAN,cAA0B,cAAM;AAAA;AAAA;AAAA;AAAA,EAIrC,YAAY,QAAQ;AAClB,UAAM,gBAAgB,aAAa;AAOnC,SAAK,SAAS;AAAA,EAChB;AACF;AAqBA,IAAM,SAAN,cAAqB,gBAAmB;AAAA;AAAA;AAAA;AAAA,EAItC,YAAY,SAAS;AACnB,cAAU,WAAW,CAAC;AAEtB;AAAA;AAAA,MAAqD;AAAA,IAAQ;AAK7D,SAAK;AAKL,SAAK;AAKL,SAAK;AAOL,SAAK,aAAa,QAAQ,YAAY,QAAQ,YAAY;AAO1D,SAAK,UAAU;AAOf,SAAK,kBAAkB;AAOvB,SAAK,kBACH,QAAQ,mBAAmB,SAAY,QAAQ,iBAAiB;AAOlE,SAAK,mBAAmB;AAOxB,SAAK,iBAAiB;AAOtB,SAAK,iBAAiB;AAEtB,QAAI,CAAC,SAAS;AACZ,gBAAU,CAAC;AAAA,IACb;AAOA,SAAK,iBAAiB,IAAIC,gBAAY;AAAA,MACpC,QAAQ,IAAI,eAAa;AAAA,QACvB,iBAAiB;AAAA,QACjB,OAAO,CAAC,CAAC,QAAQ;AAAA,MACnB,CAAC;AAAA,MACD,OAAO,QAAQ,WACX,QAAQ,WACR,8BAA8B;AAAA,MAClC,sBAAsB;AAAA,MACtB,wBAAwB;AAAA,IAC1B,CAAC;AAOD,SAAK,iBAAiB,IAAIA,gBAAY;AAAA,MACpC,QAAQ,IAAI,eAAa;AAAA,QACvB,iBAAiB;AAAA,QACjB,OAAO,CAAC,CAAC,QAAQ;AAAA,MACnB,CAAC;AAAA,MACD,OAAO,QAAQ,eACX,QAAQ,eACR,+BAA+B;AAAA,MACnC,sBAAsB;AAAA,MACtB,wBAAwB;AAAA,IAC1B,CAAC;AAED,QAAI,QAAQ,QAAQ;AAClB,WAAK,UAAU,QAAQ,MAAM;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,OAAO,KAAK;AACxB,UAAM,kBAAkB,IAAI,+BAA+B,KAAK;AAChE,UAAM,iBAAiB,SAAU,GAAG,GAAG;AACrC,aACE,yBAAyB,iBAAiB,CAAC,IAC3C,yBAAyB,iBAAiB,CAAC;AAAA,IAE/C;AACA,UAAM,SAAS,KAAK,kBAAkB;AACtC,QAAI,QAAQ;AAEV,YAAM,WAAW,YAAY,MAAM;AACnC,eAAS,KAAK,cAAc;AAC5B,YAAM,iBAAiB,SAAS,CAAC;AAEjC,UAAI,SAAS,iBAAiB,iBAAiB,cAAc;AAC7D,YAAM,cAAc,IAAI,+BAA+B,MAAM;AAG7D,UAAI,SAAmB,OAAO,WAAW,KAAK,KAAK,iBAAiB;AAElE,cAAM,SAAS,IAAI,+BAA+B,eAAe,CAAC,CAAC;AACnE,cAAM,SAAS,IAAI,+BAA+B,eAAe,CAAC,CAAC;AACnE,cAAM,eAAe,gBAA0B,aAAa,MAAM;AAClE,cAAM,eAAe,gBAA0B,aAAa,MAAM;AAClE,cAAM,OAAO,KAAK,KAAK,KAAK,IAAI,cAAc,YAAY,CAAC;AAC3D,aAAK,mBAAmB,QAAQ,KAAK;AACrC,YAAI,KAAK,kBAAkB;AACzB,mBACE,eAAe,eAAe,eAAe,CAAC,IAAI,eAAe,CAAC;AAAA,QACtE;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,iBAAiB;AAClC,UAAM,QAAQ,gBAAgB;AAC9B,UAAM,MAAM,gBAAgB;AAE5B,QAAI,SAAS,KAAK,cAAc,OAAO,GAAG;AAC1C,QAAI,CAAC,QAAQ;AACX,eAAS,IAAI,+BAA+B,KAAK;AAAA,IACnD;AACA,SAAK,8BAA8B,MAAM;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,6BAA6B,QAAQ;AACnC,QAAI,gBAAgB,KAAK;AAEzB,QAAI,CAAC,eAAe;AAClB,UAAI,CAAC,QAAQ;AACX,wBAAgB,IAAI,gBAAQ,CAAC,CAAC;AAAA,MAChC,OAAO;AACL,wBAAgB,IAAI,gBAAQ,WAAkB,MAAM,CAAC;AAAA,MACvD;AACA,WAAK,iBAAiB;AACtB,WAAK,eAAe,UAAU,EAAE,WAAW,aAAa;AAAA,IAC1D,OAAO;AACL,UAAI,CAAC,QAAQ;AACX,sBAAc,YAAY,MAAS;AAAA,MACrC,OAAO;AACL,sBAAc,YAAY,WAAkB,MAAM,CAAC;AAAA,MACrD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,8BAA8B,QAAQ;AACpC,QAAI,gBAAgB,KAAK;AACzB,QAAI,CAAC,eAAe;AAClB,sBAAgB,IAAI,gBAAQ,IAAI,cAAM,MAAM,CAAC;AAC7C,WAAK,iBAAiB;AACtB,WAAK,eAAe,UAAU,EAAE,WAAW,aAAa;AAAA,IAC1D,OAAO;AACL,YAAM,WAAW,cAAc,YAAY;AAC3C,eAAS,eAAe,MAAM;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,iBAAiB;AAC3B,QAAI,CAAC,gBAAgB,iBAAiB,CAAC,KAAK,WAAW,eAAe,GAAG;AACvE,aAAO;AAAA,IACT;AAEA,QACE,gBAAgB,QAAQ,4BAAoB,eAC5C,CAAC,KAAK,wBACN;AACA,WAAK,mBAAmB,eAAe;AAAA,IACzC;AAEA,UAAM,YAAY,eAAe;AAEjC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,iBAAiB;AAC/B,UAAM,QAAQ,gBAAgB;AAC9B,UAAM,MAAM,gBAAgB;AAE5B,UAAM,SAAS,KAAK,kBAAkB;AACtC,QAAI,SAAS,KAAK,cAAc,OAAO,GAAG;AAG1C,UAAM,mBAAmB,SAAU,OAAO;AACxC,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,MAAM,CAAC,KAAK,OAAO,CAAC,GAAG;AACzB,aAAK,OAAO,CAAC;AAAA,MACf,WAAW,MAAM,CAAC,KAAK,OAAO,CAAC,GAAG;AAChC,aAAK,OAAO,CAAC;AAAA,MACf;AACA,UAAI,MAAM,CAAC,KAAK,OAAO,CAAC,GAAG;AACzB,aAAK,OAAO,CAAC;AAAA,MACf,WAAW,MAAM,CAAC,KAAK,OAAO,CAAC,GAAG;AAChC,aAAK,OAAO,CAAC;AAAA,MACf;AACA,UAAI,OAAO,QAAQ,OAAO,MAAM;AAC9B,eAAO,CAAC,IAAI,EAAE;AAAA,MAChB;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,QAAQ;AACpB,YAAM,IACJ,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI;AACjE,YAAM,IACJ,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI;AAGjE,UAAI,MAAM,QAAQ,MAAM,MAAM;AAC5B,aAAK,kBAAkB,gBAAgB,iBAAiB,MAAM,CAAC;AAAA,MAEjE,WAAW,MAAM,MAAM;AACrB,aAAK,kBAAkB;AAAA,UACrB,iBAAiB,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;AAAA,UAC/B,iBAAiB,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;AAAA,QACjC;AAAA,MACF,WAAW,MAAM,MAAM;AACrB,aAAK,kBAAkB;AAAA,UACrB,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAAA,UAC/B,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAAA,QACjC;AAAA,MACF;AAAA,IAEF,OAAO;AACL,eAAS,IAAI,+BAA+B,KAAK;AACjD,WAAK,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;AAC3D,WAAK,kBAAkB,gBAAgB,MAAM;AAAA,IAC/C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,iBAAiB;AAC/B,QAAI,KAAK,iBAAiB;AACxB,YAAM,kBAAkB,gBAAgB;AACxC,WAAK,UAAU,KAAK,gBAAgB,eAAe,CAAC;AACpD,WAAK,8BAA8B,eAAe;AAAA,IACpD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,iBAAiB;AAC7B,SAAK,kBAAkB;AAEvB,UAAM,SAAS,KAAK,kBAAkB;AACtC,QAAI,CAAC,UAAU,QAAQ,MAAM,MAAM,GAAG;AACpC,WAAK,UAAU,IAAI;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KAAK;AACV,SAAK,eAAe,OAAO,GAAG;AAC9B,SAAK,eAAe,OAAO,GAAG;AAC9B,UAAM,OAAO,GAAG;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV,WAAO;AAAA,MACL,KAAK,kBAAkB;AAAA,MACvB,KAAK,OAAO,EAAE,QAAQ,EAAE,cAAc;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAEhB,SAAK,UAAU,SAAS,SAAS;AACjC,SAAK,6BAA6B,MAAM;AACxC,SAAK,cAAc,IAAI,YAAY,KAAK,OAAO,CAAC;AAAA,EAClD;AACF;AAOA,SAAS,gCAAgC;AACvC,QAAM,QAAQ,mBAAmB;AACjC,SAAO,SAAU,SAAS,YAAY;AACpC,WAAO,MAAM,SAAS;AAAA,EACxB;AACF;AAOA,SAAS,iCAAiC;AACxC,QAAM,QAAQ,mBAAmB;AACjC,SAAO,SAAU,SAAS,YAAY;AACpC,WAAO,MAAM,OAAO;AAAA,EACtB;AACF;AAMA,SAAS,gBAAgB,YAAY;AACnC,SAAO,SAAU,OAAO;AACtB,WAAO,eAAe,CAAC,YAAY,KAAK,CAAC;AAAA,EAC3C;AACF;AAOA,SAAS,eAAe,SAAS,SAAS;AACxC,MAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC,GAAG;AAC5B,WAAO,SAAU,OAAO;AACtB,aAAO,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAAA,IACzD;AAAA,EACF;AACA,MAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC,GAAG;AAC5B,WAAO,SAAU,OAAO;AACtB,aAAO,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAAA,IACzD;AAAA,EACF;AACA,SAAO;AACT;AAMA,SAAS,YAAY,QAAQ;AAC3B,SAAO;AAAA,IACL;AAAA,MACE,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MACrB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,IACvB;AAAA,IACA;AAAA,MACE,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MACrB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,IACvB;AAAA,IACA;AAAA,MACE,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MACrB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,IACvB;AAAA,IACA;AAAA,MACE,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MACrB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,IACvB;AAAA,EACF;AACF;AAEA,IAAO,iBAAQ;;;ACxhBf,SAAS,IAAI,QAAQ;AACnB,SAAO,QAAQ,QAAQ,CAAC;AAC1B;AAMA,SAAS,WAAW,QAAQ;AAC1B,SAAO,WAAW,MAAM;AAC1B;AAMA,SAAS,YAAY,QAAQ;AAC3B,SAAO,IAAI,MAAM,EAAE,SAAS;AAC9B;AAOA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,MAAI,MAAM,CAAC,GAAG;AACZ,WAAO;AAAA,EACT;AACA,SAAO,MAAM,WAAW,YAAY,CAAC,CAAC;AACxC;AAOA,SAAS,eAAe,GAAG,GAAG;AAC5B,SAAO,gBAAgB,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,gBAAgB,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAClE;AAsBA,IAAM,OAAN,cAAmB,oBAAY;AAAA;AAAA;AAAA;AAAA,EAI7B,YAAY,SAAS;AACnB,UAAM;AAEN,cAAU,OAAO;AAAA,MACf;AAAA,QACE,SAAS;AAAA,QACT,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,QAChC,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,WAAW,CAAC;AAAA,IACd;AAEA,QAAI;AACJ,QAAI,QAAQ,YAAY,MAAM;AAC5B,yBAAmB,EAAC,UAAU,IAAG;AAAA,IACnC,WAAW,CAAC,QAAQ,SAAS;AAC3B,yBAAmB;AAAA,IACrB,OAAO;AACL,yBAAmB,QAAQ;AAAA,IAC7B;AAMA,SAAK,oBAAoB;AAMzB,SAAK,UAAU,QAAQ,OAAO,OAAO,CAAC,KAAK,UAAU;AACnD,UAAI,KAAK,IAAI;AACb,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAML,SAAK,WAAW,QAAQ;AAMxB,SAAK,UAAU,QAAQ;AAMvB,SAAK,gBAAgB,CAAC;AAMtB,SAAK,WAAW;AAEhB,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,MAAM;AAClB,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,QAAQ,MAAM;AACjB,WAAO,OAAO,IAAI,KAAK,cAAc,IAAI,CAAC;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,QAAQ,MAAM,OAAO;AACxB,QAAI,EAAE,QAAQ,KAAK,UAAU;AAC3B;AAAA,IACF;AACA,WAAO,IAAI,KAAK,cAAc,IAAI,GAAG,KAAK;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,QAAQ,MAAM;AACpB,QAAI,EAAE,QAAQ,KAAK,UAAU;AAC3B;AAAA,IACF;AACA,WAAO,OAAO,KAAK,cAAc,IAAI,CAAC;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,KAAK;AACV,UAAM,SAAS,KAAK,OAAO;AAC3B,UAAM,OAAO,GAAG;AAChB,QAAI,QAAQ,QAAQ;AAClB;AAAA,IACF;AACA,QAAI,QAAQ;AACV,WAAK,qBAAqB,MAAM;AAAA,IAClC;AACA,QAAI,KAAK;AACP,WAAK,WAAW;AAChB,WAAK,aAAa;AAClB,WAAK,mBAAmB,GAAG;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,KAAK;AACtB,SAAK,cAAc;AAAA,MACjB,OAAO,KAAK,qBAAa,SAAS,KAAK,YAAY,IAAI;AAAA,MACvD,OAAO,IAAI,cAAc,GAAG,kBAAU,QAAQ,KAAK,YAAY,IAAI;AAAA,MACnE,OAAO,KAAK,qBAAqB,KAAK,yBAAyB,IAAI;AAAA,IACrE;AAEA,QAAI,CAAC,KAAK,UAAU;AAClB,uBAAiB,YAAY,KAAK,YAAY;AAAA,IAChD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,KAAK,cAAc,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC3D,oBAAc,KAAK,cAAc,CAAC,CAAC;AAAA,IACrC;AACA,SAAK,cAAc,SAAS;AAE5B,QAAI,CAAC,KAAK,UAAU;AAClB,0BAAoB,YAAY,KAAK,YAAY;AAAA,IACnD;AAEA,UAAM,MAAM,IAAI,IAAI,OAAO,SAAS,IAAI;AACxC,UAAM,SAAS,IAAI;AACnB,SAAK,QAAQ,QAAQ,GAAG;AACxB,SAAK,QAAQ,QAAQ,GAAG;AACxB,SAAK,QAAQ,QAAQ,GAAG;AACxB,SAAK,QAAQ,QAAQ,GAAG;AACxB,SAAK,QAAQ,QAAQ,GAAG;AACxB,WAAO,QAAQ,aAAa,MAAM,IAAI,GAAG;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B;AACxB,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,SAAK,qBAAqB,GAAG;AAC7B,SAAK,mBAAmB,GAAG;AAC3B,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,OAAO,IAAI,QAAQ;AACzB,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,UAAM,MAAM,IAAI,IAAI,OAAO,SAAS,IAAI;AACxC,UAAM,SAAS,IAAI;AAEnB,QAAI,aAAa;AAKjB,UAAM,iBAAiB,CAAC;AAExB,UAAM,OAAO,WAAW,KAAK,KAAK,QAAQ,GAAG,CAAC;AAC9C,QAAI,OAAO,KAAK,WAAW,gBAAgB,MAAM,KAAK,QAAQ,CAAC,GAAG;AAChE,mBAAa;AACb,qBAAe,OAAO;AAAA,IACxB;AAEA,UAAM,WAAW,WAAW,KAAK,KAAK,QAAQ,GAAG,CAAC;AAClD,QAAI,OAAO,KAAK,WAAW,gBAAgB,UAAU,KAAK,YAAY,CAAC,GAAG;AACxE,mBAAa;AACb,qBAAe,WAAW;AAAA,IAC5B;AAEA,UAAM,SAAS;AAAA,MACb,WAAW,KAAK,KAAK,QAAQ,GAAG,CAAC;AAAA,MACjC,WAAW,KAAK,KAAK,QAAQ,GAAG,CAAC;AAAA,IACnC;AACA,SACG,OAAO,KAAK,WAAW,OAAO,KAAK,YACpC,eAAe,QAAQ,KAAK,UAAU,CAAC,GACvC;AACA,mBAAa;AACb,qBAAe,SAAS;AAAA,IAC1B;AAEA,QAAI,YAAY;AACd,UAAI,CAAC,KAAK,YAAY,KAAK,mBAAmB;AAC5C,aAAK,QAAQ,OAAO,OAAO,gBAAgB,KAAK,iBAAiB,CAAC;AAAA,MACpE,OAAO;AACL,YAAI,eAAe,QAAQ;AACzB,eAAK,UAAU,eAAe,MAAM;AAAA,QACtC;AACA,YAAI,UAAU,gBAAgB;AAC5B,eAAK,QAAQ,eAAe,IAAI;AAAA,QAClC;AACA,YAAI,cAAc,gBAAgB;AAChC,eAAK,YAAY,eAAe,QAAQ;AAAA,QAC1C;AAAA,MACF;AAAA,IACF;AAEA,UAAM,SAAS,IAAI,aAAa;AAChC,UAAM,cAAc,KAAK,KAAK,QAAQ,GAAG;AACzC,QACE,OAAO,KAAK,WACZ,eACA,YAAY,WAAW,OAAO,QAC9B;AACA,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,cAAM,QAAQ,SAAS,YAAY,CAAC,CAAC;AACrC,YAAI,CAAC,MAAM,KAAK,GAAG;AACjB,gBAAM,UAAU,QAAQ,KAAK;AAC7B,gBAAM,QAAQ,OAAO,CAAC;AACtB,cAAI,MAAM,WAAW,MAAM,SAAS;AAClC,kBAAM,WAAW,OAAO;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,OAAO,IAAI,QAAQ;AACzB,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,UAAM,UAAU,KAAK;AACrB,SAAK,WAAW;AAEhB,UAAM,SAAS,KAAK,UAAU;AAC9B,UAAM,OAAO,KAAK,QAAQ;AAC1B,UAAM,WAAW,KAAK,YAAY;AAElC,UAAM,SAAS,IAAI,aAAa;AAChC,UAAM,eAAe,IAAI,MAAM,OAAO,MAAM;AAC5C,aAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,mBAAa,CAAC,IAAI,OAAO,CAAC,EAAE,WAAW,IAAI,MAAM;AAAA,IACnD;AAEA,UAAM,MAAM,IAAI,IAAI,OAAO,SAAS,IAAI;AACxC,UAAM,SAAS,IAAI;AAEnB,SAAK,KAAK,QAAQ,KAAK,YAAY,OAAO,CAAC,CAAC,CAAC;AAC7C,SAAK,KAAK,QAAQ,KAAK,YAAY,OAAO,CAAC,CAAC,CAAC;AAC7C,SAAK,KAAK,QAAQ,KAAK,YAAY,IAAI,CAAC;AACxC,SAAK,KAAK,QAAQ,KAAK,YAAY,QAAQ,CAAC;AAC5C,SAAK,KAAK,QAAQ,KAAK,aAAa,KAAK,EAAE,CAAC;AAE5C,QAAI,IAAI,SAAS,OAAO,SAAS,MAAM;AACrC,UAAI,WAAW,KAAK,UAAU;AAC5B,eAAO,QAAQ,aAAa,QAAQ,OAAO,IAAI,GAAG;AAAA,MACpD,OAAO;AACL,eAAO,QAAQ,UAAU,MAAM,IAAI,GAAG;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,eAAQ;;;AClVf,IAAM,sBAAsB;AAO5B,IAAM,6BAA6B;AAEnC,IAAM,aAAa,CAAC,GAAG,GAAG,GAAG,CAAC;AAC9B,IAAM,cAAc,CAAC;AAKrB,IAAM,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,WAAW;AACb;AA4DO,IAAM,cAAN,cAA0B,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrC,YAAY,MAAM,UAAU,iBAAiB;AAC3C,UAAM,IAAI;AAOV,SAAK,WAAW;AAOhB,SAAK,kBAAkB;AAAA,EACzB;AACF;AAiCA,IAAM,SAAN,cAAqB,gBAAmB;AAAA;AAAA;AAAA;AAAA,EAItC,YAAY,SAAS;AACnB;AAAA;AAAA,MAAqD;AAAA,IAAQ;AAK7D,SAAK;AAKL,SAAK;AAKL,SAAK;AAGL,SAAK,4BAA4B,KAAK,qBAAqB,KAAK,IAAI;AAMpE,SAAK,aAAa,QAAQ,YAAY,QAAQ,YAAY;AAO1D,SAAK,0BAA0B,SAAU,iBAAiB;AACxD,aAAO,WAAW,eAAe,KAAK,YAAY,eAAe;AAAA,IACnE;AAMA,SAAK,mBAAmB,QAAQ,kBAC5B,QAAQ,kBACR,KAAK;AAMT,SAAK,yBAAyB,QAAQ,wBAClC,QAAQ,wBACR;AAOJ,SAAK,iBAAiB;AAOtB,SAAK,kBAAkB;AAMvB,SAAK,aAAa,CAAC,GAAG,CAAC;AAQvB,SAAK,yBAAyB;AAM9B,SAAK,yBAAyB;AAO9B,SAAK,SAAS,IAAI,cAAM;AAMxB,SAAK,kBACH,QAAQ,mBAAmB,SAAY,QAAQ,iBAAiB;AAMlE,SAAK,mBAAmB;AAQxB,SAAK,mBAAmB;AAMxB,SAAK,gBAAgB,CAAC;AAOtB,SAAK,WAAW,IAAIC,gBAAY;AAAA,MAC9B,QAAQ,IAAI,eAAa;AAAA,QACvB,iBAAiB;AAAA,QACjB,OAAO,CAAC,CAAC,QAAQ;AAAA,MACnB,CAAC;AAAA,MACD,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,wBAAwB;AAAA,MAC/D,sBAAsB;AAAA,MACtB,wBAAwB;AAAA,IAC1B,CAAC;AAOD,SAAK,mBAAmB;AAAA,MACtB,SAAS,KAAK,oBAAoB,KAAK,IAAI;AAAA,MAC3C,cAAc,KAAK,yBAAyB,KAAK,IAAI;AAAA,MACrD,cAAc,KAAK,yBAAyB,KAAK,IAAI;AAAA,MACrD,WAAW,KAAK,sBAAsB,KAAK,IAAI;AAAA,MAC/C,cAAc,KAAK,yBAAyB,KAAK,IAAI;AAAA,MACrD,mBAAmB,KAAK,8BAA8B,KAAK,IAAI;AAAA,MAC/D,gBAAgB,KAAK,2BAA2B,KAAK,IAAI;AAAA,MACzD,UAAU,KAAK,qBAAqB,KAAK,IAAI;AAAA,MAC7C,sBAAsB,KAAK,iCAAiC,KAAK,IAAI;AAAA,IACvE;AAMA,SAAK,UAAU;AAKf,SAAK,gBAAgB;AAGrB,QAAI;AACJ,QAAI,QAAQ,UAAU;AACpB,iBAAW,QAAQ;AAAA,IACrB,WAAW,QAAQ,QAAQ;AACzB,WAAK,UAAU,QAAQ;AACvB,iBAAW,IAAI,mBAAW,KAAK,QAAQ,YAAY,CAAC;AACpD,WAAK,QAAQ;AAAA,QACX,wBAAgB;AAAA,QAChB,KAAK,iBAAiB,KAAK,IAAI;AAAA,MACjC;AACA,WAAK,QAAQ;AAAA,QACX,wBAAgB;AAAA,QAChB,KAAK,oBAAoB,KAAK,IAAI;AAAA,MACpC;AAAA,IACF;AACA,QAAI,CAAC,UAAU;AACb,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,QAAI,QAAQ,cAAc;AACxB,WAAK,gBAAgB,QAAQ;AAAA,IAC/B;AAMA,SAAK,YAAY;AAEjB,SAAK,UAAU,QAAQ,KAAK,YAAY,KAAK,IAAI,CAAC;AAClD,SAAK,UAAU;AAAA,MACb,4BAAoB;AAAA,MACpB,KAAK,kBAAkB,KAAK,IAAI;AAAA,IAClC;AACA,SAAK,UAAU;AAAA,MACb,4BAAoB;AAAA,MACpB,KAAK,qBAAqB,KAAK,IAAI;AAAA,IACrC;AAMA,SAAK,oBAAoB;AAMzB,SAAK,SAAS,CAAC,GAAG,CAAC;AAKnB,SAAK,iBACH,QAAQ,kBAAkB,SACtB,CAAC,KAAK,gBACN,QAAQ;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,SAAS;AACnB,UAAM,WAAW,QAAQ,YAAY;AACrC,QAAI,UAAU;AACZ,YAAM,SAAS,KAAK,iBAAiB,SAAS,QAAQ,CAAC;AACvD,UAAI,QAAQ;AACV,eAAO,SAAS,QAAQ;AAAA,MAC1B;AAAA,IACF;AACA,UAAM,MAAM,KAAK,OAAO;AACxB,QAAI,OAAO,IAAI,WAAW,KAAK,KAAK,UAAU,GAAG;AAC/C,WAAK,sBAAsB,KAAK,YAAY,GAAG;AAAA,IACjD;AACA,YAAQ,iBAAiB,kBAAU,QAAQ,KAAK,yBAAyB;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,KAAK,UAAU;AACjC,QAAI,CAAC,KAAK,wBAAwB;AAChC,WAAK,yBAAyB,IAAI,mBAAW;AAC7C,YAAM,WAAW,KAAK,uBAAuB,SAAS;AACtD,eAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,EAAE,GAAG;AACjD,cAAM,UAAU,SAAS,CAAC;AAC1B,iBAAS,IAAI,GAAG,KAAK,QAAQ,QAAQ,IAAI,IAAI,EAAE,GAAG;AAChD,gBAAM,UAAU,QAAQ,CAAC,EAAE;AAC3B,cAAI,WAAW,CAAC,SAAS,SAAS,OAAO,GAAG;AAC1C,iBAAK,uBAAuB,KAAK,OAAO;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK,uBAAuB,UAAU,MAAM,GAAG;AACjD,aAAK,yBAAyB;AAAA,MAChC,OAAO;AACL,aAAK;AAAA,UACH,IAAI;AAAA,YACF,gBAAgB;AAAA,YAChB,KAAK;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,SAAS;AACtB,SAAK,0BAA0B,OAAO;AAEtC,QAAI,KAAK,kBAAkB,KAAK,UAAU,UAAU,MAAM,GAAG;AAC3D,WAAK,SAAS,UAAU,EAAE,cAAc,KAAK,cAAc;AAC3D,WAAK,iBAAiB;AAAA,IACxB;AACA,YAAQ;AAAA,MACN,kBAAU;AAAA,MACV,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,0BAA0B,SAAS;AACjC,UAAM,QAAQ,KAAK;AAEnB,UAAM,gBAAgB,CAAC;AACvB,UAAM;AAAA;AAAA;AAAA;AAAA,MAIJ,SAAU,MAAM;AACd,YAAI,YAAY,KAAK,SAAS;AAC5B,wBAAc,KAAK,IAAI;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,aAAS,IAAI,cAAc,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAClD,YAAM,eAAe,cAAc,CAAC;AACpC,eAAS,IAAI,KAAK,cAAc,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACvD,YAAI,KAAK,cAAc,CAAC,EAAE,CAAC,MAAM,cAAc;AAC7C,eAAK,cAAc,OAAO,GAAG,CAAC;AAAA,QAChC;AAAA,MACF;AACA,YAAM,OAAO,YAAY;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,QAAI,KAAK,kBAAkB,CAAC,QAAQ;AAClC,WAAK,SAAS,UAAU,EAAE,cAAc,KAAK,cAAc;AAC3D,WAAK,iBAAiB;AAAA,IACxB;AACA,UAAM,UAAU,MAAM;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KAAK;AACV,SAAK,SAAS,OAAO,GAAG;AACxB,UAAM,OAAO,GAAG;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,OAAO;AACtB,QAAI,MAAM,SAAS;AACjB,WAAK,UAAU,KAAK,MAAM,OAAO;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,OAAO;AACzB,QAAI,MAAM,SAAS;AACjB,WAAK,UAAU,OAAO,MAAM,OAAO;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,KAAK;AACrB,SAAK,YAAY,IAAI,OAAO;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,KAAK;AACxB,QAAI,CAAC,KAAK,kBAAkB;AAC1B,YAAM;AAAA;AAAA,QAAkC,IAAI;AAAA;AAC5C,WAAK,eAAe,OAAO;AAC3B,WAAK,YAAY,OAAO;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,KAAK;AACxB,SAAK,eAAe,IAAI,OAAO;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,SAAS,UAAU;AACrC,UAAM,cAAc,SAAS,eAAe;AAG5C,UAAM,cAAc;AAAA,MAClB;AAAA,MACA;AAAA,MACA,SAAS,CAAC,aAAa,WAAW;AAAA,IACpC;AAEA,SAAK,OAAO,OAAO,SAAS,UAAU,GAAG,WAAW;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,yBAAyB,SAAS,UAAU;AAC1C,UAAM,SAAS,SAAS,eAAe;AACvC,aAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,YAAM,cAAc,OAAO,CAAC;AAG5B,YAAM,cAAc;AAAA,QAClB;AAAA,QACA;AAAA,QACA,OAAO,CAAC,CAAC;AAAA,QACT,OAAO;AAAA,QACP,SAAS,CAAC,aAAa,WAAW;AAAA,MACpC;AAEA,WAAK,OAAO,OAAO,SAAS,UAAU,GAAG,WAAW;AAAA,IACtD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,yBAAyB,SAAS,UAAU;AAC1C,UAAM,cAAc,SAAS,eAAe;AAC5C,aAAS,IAAI,GAAG,KAAK,YAAY,SAAS,GAAG,IAAI,IAAI,EAAE,GAAG;AACxD,YAAM,UAAU,YAAY,MAAM,GAAG,IAAI,CAAC;AAG1C,YAAM,cAAc;AAAA,QAClB;AAAA,QACA;AAAA,QACA,OAAO;AAAA,QACP;AAAA,MACF;AAEA,WAAK,OAAO,OAAO,eAAe,OAAO,GAAG,WAAW;AAAA,IACzD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,8BAA8B,SAAS,UAAU;AAC/C,UAAM,QAAQ,SAAS,eAAe;AACtC,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,YAAM,cAAc,MAAM,CAAC;AAC3B,eAAS,IAAI,GAAG,KAAK,YAAY,SAAS,GAAG,IAAI,IAAI,EAAE,GAAG;AACxD,cAAM,UAAU,YAAY,MAAM,GAAG,IAAI,CAAC;AAG1C,cAAM,cAAc;AAAA,UAClB;AAAA,UACA;AAAA,UACA,OAAO,CAAC,CAAC;AAAA,UACT,OAAO;AAAA,UACP;AAAA,QACF;AAEA,aAAK,OAAO,OAAO,eAAe,OAAO,GAAG,WAAW;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,SAAS,UAAU;AACvC,UAAM,QAAQ,SAAS,eAAe;AACtC,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,YAAM,cAAc,MAAM,CAAC;AAC3B,eAAS,IAAI,GAAG,KAAK,YAAY,SAAS,GAAG,IAAI,IAAI,EAAE,GAAG;AACxD,cAAM,UAAU,YAAY,MAAM,GAAG,IAAI,CAAC;AAG1C,cAAM,cAAc;AAAA,UAClB;AAAA,UACA;AAAA,UACA,OAAO,CAAC,CAAC;AAAA,UACT,OAAO;AAAA,UACP;AAAA,QACF;AAEA,aAAK,OAAO,OAAO,eAAe,OAAO,GAAG,WAAW;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,2BAA2B,SAAS,UAAU;AAC5C,UAAM,WAAW,SAAS,eAAe;AACzC,aAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,EAAE,GAAG;AACjD,YAAM,QAAQ,SAAS,CAAC;AACxB,eAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,cAAM,cAAc,MAAM,CAAC;AAC3B,iBAAS,IAAI,GAAG,KAAK,YAAY,SAAS,GAAG,IAAI,IAAI,EAAE,GAAG;AACxD,gBAAM,UAAU,YAAY,MAAM,GAAG,IAAI,CAAC;AAG1C,gBAAM,cAAc;AAAA,YAClB;AAAA,YACA;AAAA,YACA,OAAO,CAAC,GAAG,CAAC;AAAA,YACZ,OAAO;AAAA,YACP;AAAA,UACF;AAEA,eAAK,OAAO,OAAO,eAAe,OAAO,GAAG,WAAW;AAAA,QACzD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,qBAAqB,SAAS,UAAU;AACtC,UAAM,cAAc,SAAS,UAAU;AAGvC,UAAM,oBAAoB;AAAA,MACxB;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,SAAS,CAAC,aAAa,WAAW;AAAA,IACpC;AAGA,UAAM,2BAA2B;AAAA,MAC/B;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,SAAS,CAAC,aAAa,WAAW;AAAA,IACpC;AAEA,UAAM,kBAAkB,CAAC,mBAAmB,wBAAwB;AACpE,sBAAkB,kBAAkB;AACpC,6BAAyB,kBAAkB;AAC3C,SAAK,OAAO,OAAO,6BAAa,WAAW,GAAG,iBAAiB;AAC/D,QAAI;AAAA;AAAA,MACF;AAAA;AAEF,UAAM,iBAAiB,kBAAkB;AACzC,QAAI,kBAAkB,KAAK,OAAO,GAAG;AACnC,YAAM,aAAa,KAAK,OAAO,EAAE,QAAQ,EAAE,cAAc;AACzD,uBAAiB,eACd,MAAM,EACN,UAAU,gBAAgB,UAAU;AACvC,uBAAiB;AAAA;AAAA,QACqC;AAAA,MACtD,EAAE,UAAU,YAAY,cAAc;AAAA,IACxC;AACA,SAAK,OAAO,OAAO,eAAe,UAAU,GAAG,wBAAwB;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iCAAiC,SAAS,UAAU;AAClD,UAAM,aAAa,SAAS,mBAAmB;AAC/C,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AAC1C,YAAMC,YAAW,WAAW,CAAC;AAC7B,YAAM,SAAS,KAAK,iBAAiBA,UAAS,QAAQ,CAAC;AACvD,aAAO,SAASA,SAAQ;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,6BAA6B,aAAa,UAAU,YAAY;AAC9D,QAAI,gBAAgB,KAAK;AACzB,QAAI,CAAC,eAAe;AAClB,sBAAgB,IAAI,gBAAQ,IAAI,cAAM,WAAW,CAAC;AAClD,WAAK,iBAAiB;AACtB,WAAK,SAAS,UAAU,EAAE,WAAW,aAAa;AAAA,IACpD,OAAO;AACL,YAAM,WAAW,cAAc,YAAY;AAC3C,eAAS,eAAe,WAAW;AAAA,IACrC;AACA,kBAAc,IAAI,YAAY,QAAQ;AACtC,kBAAc,IAAI,cAAc,UAAU;AAC1C,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,iBAAiB;AAC3B,QAAI,CAAC,gBAAgB,eAAe;AAClC,aAAO;AAAA,IACT;AACA,SAAK,oBAAoB;AAEzB,QAAI;AACJ,QACE,CAAC,gBAAgB,IAAI,QAAQ,EAAE,eAAe,KAC9C,gBAAgB,QAAQ,4BAAoB,eAC5C,CAAC,KAAK,wBACN;AACA,WAAK,mBAAmB,eAAe;AAAA,IACzC;AACA,QAAI,KAAK,kBAAkB,KAAK,iBAAiB,eAAe,GAAG;AACjE,UACE,gBAAgB,QAAQ,4BAAoB,eAC5C,CAAC,KAAK,wBACN;AACA,kBAAU,KAAK,YAAY;AAAA,MAC7B,OAAO;AACL,kBAAU;AAAA,MACZ;AAAA,IACF;AAEA,QAAI,gBAAgB,QAAQ,4BAAoB,aAAa;AAC3D,WAAK,yBAAyB;AAAA,IAChC;AAEA,WAAO,MAAM,YAAY,eAAe,KAAK,CAAC;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,KAAK;AACnB,SAAK,yBAAyB;AAC9B,SAAK,oBAAoB,KAAK,KAAK,aAAa;AAEhD,UAAM,SAAS;AAAA,MACb,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,CAAC;AAAA,MACjC,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,CAAC;AAAA,IACnC;AACA,UAAM,WAAW,CAAC;AAClB,UAAM,aAAa,CAAC;AACpB,aAAS,IAAI,GAAG,KAAK,KAAK,cAAc,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC3D,YAAM,cAAc,KAAK,cAAc,CAAC;AACxC,YAAM,cAAc,YAAY,CAAC;AACjC,YAAM,UAAU,YAAY;AAC5B,UAAI,CAAC,SAAS,SAAS,OAAO,GAAG;AAC/B,iBAAS,KAAK,OAAO;AAAA,MACvB;AACA,YAAM,WAAW,YAAY;AAC7B,UAAI,CAAC,WAAW,SAAS,QAAQ,GAAG;AAClC,mBAAW,KAAK,QAAQ;AAAA,MAC1B;AACA,YAAM,QAAQ,YAAY;AAC1B,UAAI;AACJ,YAAM,UAAU,YAAY;AAC5B,YAAM,QAAQ,YAAY,CAAC;AAE3B,aAAO,OAAO,SAAS,SAAS,UAAU,GAAG;AAC3C,eAAO,KAAK,QAAQ,KAAK,EAAE,OAAO,MAAM,CAAC;AAAA,MAC3C;AAEA,cAAQ,SAAS,QAAQ,GAAG;AAAA,QAC1B,KAAK;AACH,wBAAc;AACd,kBAAQ,CAAC,IAAI;AACb,kBAAQ,CAAC,IAAI;AACb;AAAA,QACF,KAAK;AACH,wBAAc,SAAS,eAAe;AACtC,sBAAY,YAAY,KAAK,IAAI;AACjC,kBAAQ,CAAC,IAAI;AACb,kBAAQ,CAAC,IAAI;AACb;AAAA,QACF,KAAK;AACH,wBAAc,SAAS,eAAe;AACtC,sBAAY,YAAY,QAAQ,KAAK,IAAI;AACzC,kBAAQ,KAAK,IAAI;AACjB;AAAA,QACF,KAAK;AACH,wBAAc,SAAS,eAAe;AACtC,sBAAY,MAAM,CAAC,CAAC,EAAE,YAAY,QAAQ,KAAK,IAAI;AACnD,kBAAQ,KAAK,IAAI;AACjB;AAAA,QACF,KAAK;AACH,wBAAc,SAAS,eAAe;AACtC,sBAAY,MAAM,CAAC,CAAC,EAAE,YAAY,QAAQ,KAAK,IAAI;AACnD,kBAAQ,KAAK,IAAI;AACjB;AAAA,QACF,KAAK;AACH,wBAAc,SAAS,eAAe;AACtC,sBAAY,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,YAAY,QAAQ,KAAK,IAAI;AAC7D,kBAAQ,KAAK,IAAI;AACjB;AAAA,QACF,KAAK;AACH,kBAAQ,CAAC,IAAI;AACb,kBAAQ,CAAC,IAAI;AACb,cAAI,YAAY,UAAU,qBAAqB;AAC7C,iBAAK,mBAAmB;AACxB,qBAAS,UAAU,MAAM;AACzB,iBAAK,mBAAmB;AAAA,UAC1B,OAAO;AAEL,iBAAK,mBAAmB;AACxB,kBAAM,aAAa,IAAI,IAAI,QAAQ,EAAE,cAAc;AACnD,gBAAI,SAAS;AAAA,cACX,mBAAmB,SAAS,UAAU,GAAG,UAAU;AAAA,cACnD,mBAAmB,QAAQ,UAAU;AAAA,YACvC;AACA,kBAAM,iBAAiB,kBAAkB;AACzC,gBAAI,gBAAgB;AAClB,oBAAM,iBAAiB,SACpB,MAAM,EACN,UAAU,gBAAgB,UAAU;AACvC,6BAAe,UAAU,MAAM;AAC/B,uBAAS,eACN,UAAU,YAAY,cAAc,EACpC,UAAU;AAAA,YACf;AACA,qBAAS,UAAU,MAAM;AACzB,iBAAK,mBAAmB;AAAA,UAC1B;AACA;AAAA,QACF;AAAA,MAEF;AAEA,UAAI,aAAa;AACf,aAAK,wBAAwB,UAAU,WAAW;AAAA,MACpD;AAAA,IACF;AACA,SAAK,6BAA6B,QAAQ,UAAU,UAAU;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,KAAK;AACnB,QAAI,CAAC,KAAK,WAAW,GAAG,GAAG;AACzB,aAAO;AAAA,IACT;AACA,UAAM,kBAAkB,IAAI;AAC5B,SAAK,sBAAsB,IAAI,OAAO,IAAI,KAAK,eAAe;AAC9D,SAAK,cAAc,SAAS;AAC5B,SAAK,yBAAyB;AAC9B,UAAM,gBAAgB,KAAK;AAC3B,QAAI,eAAe;AACjB,YAAM,aAAa,IAAI,IAAI,QAAQ,EAAE,cAAc;AACnD,YAAM,iBAAiB,CAAC;AACxB,YAAM,SAAS,cAAc,YAAY,EAAE,eAAe;AAC1D,YAAM,eAAe,eAAe,CAAC,MAAM,CAAC;AAC5C,YAAM,qBAAqB,KAAK,OAAO,YAAY,YAAY;AAC/D,YAAM,oBAAoB,CAAC;AAC3B,yBAAmB,KAAK,cAAc;AACtC,eAAS,IAAI,GAAG,KAAK,mBAAmB,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC3D,cAAM,mBAAmB,mBAAmB,CAAC;AAC7C,cAAM,UAAU,iBAAiB;AACjC,YAAI,MAAM,OAAO,iBAAiB,QAAQ;AAC1C,cAAM,QAAQ,iBAAiB;AAC/B,YAAI,OAAO;AACT,iBAAO,MAAM,MAAM,KAAK,GAAG;AAAA,QAC7B;AACA,YAAI,CAAC,kBAAkB,GAAG,GAAG;AAC3B,4BAAkB,GAAG,IAAI,IAAI,MAAM,CAAC;AAAA,QACtC;AAEA,YACE,iBAAiB,SAAS,QAAQ,MAAM,YACxC,iBAAiB,UAAU,4BAC3B;AACA,gBAAM,gBAAgB;AAAA,YACpB;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,cACEC,QAAiB,eAAe,MAAM,KACtC,CAAC,kBAAkB,GAAG,EAAE,CAAC,GACzB;AACA,iBAAK,cAAc,KAAK,CAAC,kBAAkB,CAAC,CAAC;AAC7C,8BAAkB,GAAG,EAAE,CAAC,IAAI;AAAA,UAC9B;AACA;AAAA,QACF;AAEA,YACEA,QAAiB,QAAQ,CAAC,GAAG,MAAM,KACnC,CAAC,kBAAkB,GAAG,EAAE,CAAC,GACzB;AACA,eAAK,cAAc,KAAK,CAAC,kBAAkB,CAAC,CAAC;AAC7C,4BAAkB,GAAG,EAAE,CAAC,IAAI;AAC5B;AAAA,QACF;AAEA,YACEA,QAAiB,QAAQ,CAAC,GAAG,MAAM,KACnC,CAAC,kBAAkB,GAAG,EAAE,CAAC,GACzB;AACA,cACE,kBAAkB,GAAG,EAAE,CAAC,KACxB,kBAAkB,GAAG,EAAE,CAAC,EAAE,UAAU,GACpC;AACA,gBAAI,cAAc,iBAAiB,SAAS,eAAe;AAC3D,oBAAQ,iBAAiB,SAAS,QAAQ,GAAG;AAAA,cAE3C,KAAK;AAAA,cACL,KAAK;AACH;AAAA,cAGF,KAAK;AACH,8BAAc,YAAY,MAAM,CAAC,CAAC;AAAA,cAEpC,KAAK;AACH,oBACE,iBAAiB,UACjB,YAAY,MAAM,CAAC,CAAC,EAAE,SAAS,GAC/B;AACA;AAAA,gBACF;AACA;AAAA,cACF;AAAA,YAEF;AAAA,UACF;AAEA,eAAK,cAAc,KAAK,CAAC,kBAAkB,CAAC,CAAC;AAC7C,4BAAkB,GAAG,EAAE,CAAC,IAAI;AAC5B;AAAA,QACF;AAEA,YACE,OAAO,OAAO,KAAK,KAAK,mBACxB,CAAC,kBAAkB,GAAG,EAAE,CAAC,KACzB,CAAC,kBAAkB,GAAG,EAAE,CAAC,KACzB,KAAK,uBAAuB,GAAG,GAC/B;AACA,yBAAe,KAAK,gBAAgB;AAAA,QACtC;AAAA,MACF;AAEA,UAAI,eAAe,QAAQ;AACzB,aAAK,oBAAoB,KAAK,CAAC,cAAc,CAAC;AAAA,MAChD;AAEA,eAAS,IAAI,eAAe,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACnD,aAAK,cAAc,eAAe,CAAC,GAAG,MAAM;AAAA,MAC9C;AAAA,IACF;AACA,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,KAAK;AACjB,aAAS,IAAI,KAAK,cAAc,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACvD,YAAM,cAAc,KAAK,cAAc,CAAC,EAAE,CAAC;AAC3C,YAAM,WAAW,YAAY;AAC7B,UAAI,SAAS,QAAQ,MAAM,UAAU;AAEnC,cAAM,cAAc,SAAS,UAAU;AACvC,cAAM,oBAAoB,YAAY,gBAAgB,CAAC;AACvD,cAAM,2BAA2B,YAAY,gBAAgB,CAAC;AAC9D,0BAAkB,QAAQ,CAAC,IAAI;AAC/B,0BAAkB,QAAQ,CAAC,IAAI;AAC/B,iCAAyB,QAAQ,CAAC,IAAI;AACtC,iCAAyB,QAAQ,CAAC,IAAI;AACtC,aAAK,OAAO,OAAO,6BAAa,WAAW,GAAG,iBAAiB;AAC/D,YAAI,iBAAiB;AACrB,cAAM,iBAAiB,kBAAkB;AACzC,YAAI,gBAAgB;AAClB,gBAAM,aAAa,IAAI,IAAI,QAAQ,EAAE,cAAc;AACnD,2BAAiB,eACd,MAAM,EACN,UAAU,gBAAgB,UAAU;AACvC,2BAAiB,WAAW,cAAc,EAAE;AAAA,YAC1C;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,aAAK,OAAO;AAAA,UACV,eAAe,UAAU;AAAA,UACzB;AAAA,QACF;AAAA,MACF,OAAO;AACL,aAAK,OAAO,OAAO,eAAe,YAAY,OAAO,GAAG,WAAW;AAAA,MACrE;AAAA,IACF;AACA,QAAI,KAAK,wBAAwB;AAC/B,WAAK;AAAA,QACH,IAAI;AAAA,UACF,gBAAgB;AAAA,UAChB,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,WAAK,yBAAyB;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,KAAK;AACtB,SAAK,aAAa,IAAI;AACtB,SAAK,sBAAsB,IAAI,OAAO,IAAI,KAAK,IAAI,UAAU;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,OAAO,KAAK,YAAY;AAC5C,UAAM,kBAAkB,cAAc,IAAI,uBAAuB,KAAK;AACtE,UAAM,aAAa,IAAI,QAAQ,EAAE,cAAc;AAC/C,UAAM,iBAAiB,SAAU,GAAG,GAAG;AACrC,aACE,sCAAsC,iBAAiB,GAAG,UAAU,IACpE,sCAAsC,iBAAiB,GAAG,UAAU;AAAA,IAExE;AAGA,QAAI;AAEJ,QAAI;AACJ,QAAI,KAAK,eAAe;AACtB,YAAM,cACJ,OAAO,KAAK,kBAAkB,WAC1B,CAAC,UAAU,UAAU,KAAK,gBAC1B;AACN,UAAI;AAAA,QACF;AAAA,QACA,CAAC,SAAS,OAAO,aAAa;AAC5B,cAAI,UAAU;AACZ,uBAAW,IAAI;AAAA,cACb,iBAAiB,SAAS,eAAe,GAAG,UAAU;AAAA,YACxD;AAAA,UACF;AACA,gBAAM,OAAO,YAAY,QAAQ,YAAY;AAC7C,cACE,KAAK,QAAQ,MAAM,WACnB,mBAAmB,mBACnB,KAAK,UAAU,SAAS,EAAE,SAAS,OAAO,GAC1C;AACA;AAAA,YAAyC;AACzC,kBAAMC;AAAA;AAAA,cAAmC,QAAQ,YAAY,EAC1D,mBAAmB,EACnB,MAAM,GAAG,CAAC;AAAA;AACb,oBAAQ;AAAA,cACN;AAAA,gBACE;AAAA,gBACA,UAAU;AAAA,gBACV,SAAS,CAACA,aAAYA,WAAU;AAAA,cAClC;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,QACA,EAAC,YAAW;AAAA,MACd;AAAA,IACF;AACA,QAAI,CAAC,OAAO;AACV,YAAM,aAAa;AAAA,QACjB,6BAAa,iBAAiB,UAAU;AAAA,QACxC;AAAA,MACF;AACA,YAAMC,UAAS,IAAI,QAAQ,EAAE,cAAc,IAAI,KAAK;AACpD,YAAM,MAAM;AAAA,QACV,OAAa,YAAYA,SAAQ,UAAU;AAAA,QAC3C;AAAA,MACF;AACA,cAAQ,KAAK,OAAO,YAAY,GAAG;AAAA,IACrC;AAEA,QAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,YAAM,OAAO,MAAM,KAAK,cAAc,EAAE,CAAC;AACzC,YAAM,iBAAiB,KAAK;AAC5B,UAAI,SAAS,qBAAqB,iBAAiB,MAAM,UAAU;AACnE,YAAM,cAAc,IAAI,uBAAuB,MAAM;AACrD,UAAI,OAAO,SAAmB,OAAO,WAAW;AAChD,UAAI,oBAAoB,QAAQ,KAAK,iBAAiB;AAEpD,cAAM,iBAAiB,CAAC;AACxB,uBAAe,OAAO,cAAc,CAAC,IAAI;AAEzC,YAAI,CAAC,KAAK,gBAAgB;AACxB,eAAK,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,gBAAgB,CAAC;AAC9C,eAAK,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,gBAAgB,CAAC;AAAA,QAChD;AACA,YACE,KAAK,SAAS,QAAQ,MAAM,YAC5B,KAAK,UAAU,4BACf;AACA,eAAK,mBAAmB;AACxB,eAAK;AAAA,YACH;AAAA,YACA,CAAC,KAAK,OAAO;AAAA,YACb,CAAC,KAAK,QAAQ;AAAA,UAChB;AAAA,QACF,OAAO;AACL,gBAAM,SAAS,IAAI,uBAAuB,eAAe,CAAC,CAAC;AAC3D,gBAAM,SAAS,IAAI,uBAAuB,eAAe,CAAC,CAAC;AAC3D,gBAAM,eAAe,gBAA0B,aAAa,MAAM;AAClE,gBAAM,eAAe,gBAA0B,aAAa,MAAM;AAClE,iBAAO,KAAK,KAAK,KAAK,IAAI,cAAc,YAAY,CAAC;AACrD,eAAK,mBAAmB,QAAQ,KAAK;AACrC,cAAI,KAAK,kBAAkB;AACzB,qBACE,eAAe,eACX,eAAe,CAAC,IAChB,eAAe,CAAC;AAAA,UACxB;AACA,eAAK;AAAA,YACH;AAAA,YACA,CAAC,KAAK,OAAO;AAAA,YACb,CAAC,KAAK,QAAQ;AAAA,UAChB;AACA,gBAAM,aAAa,CAAC;AACpB,qBAAW,OAAO,KAAK,QAAQ,CAAC,IAAI;AACpC,mBAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,kBAAM,UAAU,MAAM,CAAC,EAAE;AACzB,gBACGF,QAAiB,eAAe,CAAC,GAAG,QAAQ,CAAC,CAAC,KAC7CA,QAAiB,eAAe,CAAC,GAAG,QAAQ,CAAC,CAAC,KAC/CA,QAAiB,eAAe,CAAC,GAAG,QAAQ,CAAC,CAAC,KAC7CA,QAAiB,eAAe,CAAC,GAAG,QAAQ,CAAC,CAAC,GAChD;AACA,oBAAM,cAAc,OAAO,MAAM,CAAC,EAAE,QAAQ;AAC5C,kBAAI,EAAE,eAAe,aAAa;AAChC,2BAAW,WAAW,IAAI;AAC1B,+BAAe,OAAO,OAAO,CAAC,IAAI;AAAA,cACpC;AAAA,YACF,OAAO;AACL;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,aAAK,kBAAkB;AACvB;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,gBAAgB;AACvB,WAAK,SAAS,UAAU,EAAE,cAAc,KAAK,cAAc;AAC3D,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,aAAa,QAAQ;AACjC,UAAM,UAAU,YAAY;AAC5B,UAAM,UAAU,YAAY;AAC5B,UAAM,WAAW,YAAY;AAC7B,UAAM,QAAQ,YAAY;AAC1B,UAAM,QAAQ,YAAY;AAC1B,QAAI;AAEJ,WAAO,OAAO,SAAS,SAAS,UAAU,GAAG;AAC3C,aAAO,KAAK,CAAC;AAAA,IACf;AAEA,YAAQ,SAAS,QAAQ,GAAG;AAAA,MAC1B,KAAK;AACH,sBAAc,SAAS,eAAe;AACtC,oBAAY,MAAM,CAAC,CAAC,EAAE,OAAO,QAAQ,GAAG,GAAG,MAAM;AACjD;AAAA,MACF,KAAK;AACH,sBAAc,SAAS,eAAe;AACtC,oBAAY,MAAM,CAAC,CAAC,EAAE,OAAO,QAAQ,GAAG,GAAG,MAAM;AACjD;AAAA,MACF,KAAK;AACH,sBAAc,SAAS,eAAe;AACtC,oBAAY,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,OAAO,QAAQ,GAAG,GAAG,MAAM;AAC3D;AAAA,MACF,KAAK;AACH,sBAAc,SAAS,eAAe;AACtC,oBAAY,OAAO,QAAQ,GAAG,GAAG,MAAM;AACvC;AAAA,MACF;AACE;AAAA,IACJ;AAEA,SAAK,wBAAwB,UAAU,WAAW;AAClD,UAAM,QAAQ,KAAK;AACnB,UAAM,OAAO,WAAW;AACxB,SAAK,sBAAsB,UAAU,OAAO,OAAO,CAAC;AAGpD,UAAM,iBAAiB;AAAA,MACrB,SAAS,CAAC,QAAQ,CAAC,GAAG,MAAM;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,UAAM,OAAO,eAAe,eAAe,OAAO,GAAG,cAAc;AACnE,SAAK,cAAc,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAG3C,UAAM,kBAAkB;AAAA,MACtB,SAAS,CAAC,QAAQ,QAAQ,CAAC,CAAC;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,QAAQ;AAAA,IACjB;AAEA,UAAM,OAAO,eAAe,gBAAgB,OAAO,GAAG,eAAe;AACrE,SAAK,cAAc,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAC5C,SAAK,yBAAyB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,QACE,KAAK,qBACL,KAAK,kBAAkB,QAAQ,4BAAoB,aACnD;AACA,YAAM,MAAM,KAAK;AACjB,WAAK,oBAAoB,KAAK,KAAK,aAAa;AAChD,YAAM,UAAU,KAAK,cAAc;AACnC,UAAI,KAAK,wBAAwB;AAC/B,aAAK;AAAA,UACH,IAAI;AAAA,YACF,gBAAgB;AAAA,YAChB,KAAK;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,WAAK,yBAAyB;AAC9B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACd,UAAM,eAAe,KAAK;AAC1B,UAAM,oBAAoB,CAAC;AAC3B,QAAI,UAAU;AACd,QAAI,WAAW,aAAa,aAAa,UAAU,GAAG,OAAO;AAC7D,QAAI,UAAU,OAAO,aAAa;AAClC,SAAK,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAC7C,oBAAc,aAAa,CAAC;AAC5B,oBAAc,YAAY,CAAC;AAC3B,YAAM,OAAO,YAAY,OAAO;AAChC,UAAI,YAAY,OAAO;AAErB,eAAO,MAAM,YAAY,MAAM,KAAK,GAAG;AAAA,MACzC;AACA,UAAI,EAAE,OAAO,oBAAoB;AAC/B,0BAAkB,GAAG,IAAI,CAAC;AAAA,MAC5B;AACA,UAAI,YAAY,CAAC,MAAM,GAAG;AACxB,0BAAkB,GAAG,EAAE,QAAQ;AAC/B,0BAAkB,GAAG,EAAE,QAAQ,YAAY;AAAA,MAC7C,WAAW,YAAY,CAAC,KAAK,GAAG;AAC9B,0BAAkB,GAAG,EAAE,OAAO;AAC9B,0BAAkB,GAAG,EAAE,QAAQ,YAAY,QAAQ;AAAA,MACrD;AAAA,IACF;AACA,SAAK,OAAO,mBAAmB;AAC7B,cAAQ,kBAAkB,GAAG,EAAE;AAC/B,aAAO,kBAAkB,GAAG,EAAE;AAC9B,cAAQ,kBAAkB,GAAG,EAAE;AAC/B,iBAAW,QAAQ;AACnB,UAAI,SAAS,QAAW;AACtB,sBAAc;AAAA,MAChB,OAAO;AACL,sBAAc;AAAA,MAChB;AACA,UAAI,WAAW,GAAG;AAChB,mBAAW;AAAA,MACb;AACA,iBAAW,YAAY;AACvB,oBAAc,SAAS,eAAe;AACtC,kBAAY;AACZ,gBAAU;AACV,cAAQ,SAAS,QAAQ,GAAG;AAAA,QAC1B,KAAK;AACH,cAAI,YAAY,YAAY,MAAM,CAAC,CAAC,EAAE,SAAS,GAAG;AAChD,wBAAY,YAAY,MAAM,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC;AACjD,sBAAU;AAAA,UACZ;AACA;AAAA,QACF,KAAK;AACH,cAAI,YAAY,SAAS,GAAG;AAC1B,wBAAY,OAAO,OAAO,CAAC;AAC3B,sBAAU;AAAA,UACZ;AACA;AAAA,QACF,KAAK;AACH,sBAAY,UAAU,YAAY,MAAM,CAAC,CAAC;AAAA,QAE5C,KAAK;AACH,sBAAY,UAAU,YAAY,MAAM,CAAC,CAAC;AAC1C,cAAI,UAAU,SAAS,GAAG;AACxB,gBAAI,SAAS,UAAU,SAAS,GAAG;AACjC,sBAAQ;AAAA,YACV;AACA,sBAAU,OAAO,OAAO,CAAC;AACzB,sBAAU;AACV,gBAAI,UAAU,GAAG;AAEf,wBAAU,IAAI;AACd,wBAAU,KAAK,UAAU,CAAC,CAAC;AAC3B,yBAAW,UAAU,SAAS;AAAA,YAChC;AAAA,UACF;AACA;AAAA,QACF;AAAA,MAEF;AAEA,UAAI,SAAS;AACX,aAAK,wBAAwB,UAAU,WAAW;AAClD,cAAM,WAAW,CAAC;AAClB,YAAI,SAAS,QAAW;AACtB,eAAK,OAAO,OAAO,IAAI;AACvB,mBAAS,KAAK,KAAK,QAAQ,CAAC,CAAC;AAAA,QAC/B;AACA,YAAI,UAAU,QAAW;AACvB,eAAK,OAAO,OAAO,KAAK;AACxB,mBAAS,KAAK,MAAM,QAAQ,CAAC,CAAC;AAAA,QAChC;AACA,YAAI,SAAS,UAAa,UAAU,QAAW;AAE7C,gBAAM,iBAAiB;AAAA,YACrB,OAAO,YAAY;AAAA,YACnB,SAAS,YAAY;AAAA,YACrB,UAAU,YAAY;AAAA,YACtB,OAAO;AAAA,YACP,SAAS;AAAA,UACX;AAEA,eAAK,OAAO;AAAA,YACV,eAAe,eAAe,OAAO;AAAA,YACrC;AAAA,UACF;AAAA,QACF;AACA,aAAK,sBAAsB,UAAU,OAAO,YAAY,OAAO,EAAE;AACjE,YAAI,KAAK,gBAAgB;AACvB,eAAK,SAAS,UAAU,EAAE,cAAc,KAAK,cAAc;AAC3D,eAAK,iBAAiB;AAAA,QACxB;AACA,qBAAa,SAAS;AAAA,MACxB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB,UAAU,aAAa;AAC7C,SAAK,mBAAmB;AACxB,aAAS,eAAe,WAAW;AACnC,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,sBAAsB,UAAU,OAAO,OAAO,OAAO;AACnD,SAAK,OAAO;AAAA,MACV,SAAS,UAAU;AAAA,MACnB,SAAU,kBAAkB;AAC1B,YACE,iBAAiB,aAAa,aAC7B,UAAU,UACT,iBAAiB,UAAU,UAC3B,OAAO,iBAAiB,OAAO,KAAK,MACtC,iBAAiB,QAAQ,OACzB;AACA,2BAAiB,SAAS;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAOA,SAAS,eAAe,GAAG,GAAG;AAC5B,SAAO,EAAE,QAAQ,EAAE;AACrB;AAYA,SAAS,sCACP,kBACA,aACA,YACA;AACA,QAAM,WAAW,YAAY;AAE7B,MAAI,SAAS,QAAQ,MAAM,UAAU;AACnC,QAAI;AAAA;AAAA,MACF;AAAA;AAGF,QAAI,YAAY,UAAU,4BAA4B;AACpD,YAAM,iBAAiB,kBAAkB;AACzC,UAAI,gBAAgB;AAClB;AAAA,QACE,eAAe,MAAM,EAAE,UAAU,gBAAgB,UAAU;AAAA,MAE/D;AACA,YAAM,0BAA0B;AAAA,QAC9B,eAAe,UAAU;AAAA,QACzB,mBAAmB,kBAAkB,UAAU;AAAA,MACjD;AACA,YAAM,0BACJ,KAAK,KAAK,uBAAuB,IAAI,eAAe,UAAU;AAChE,aAAO,0BAA0B;AAAA,IACnC;AAAA,EACF;AAEA,QAAM,aAAa,mBAAmB,kBAAkB,UAAU;AAClE,cAAY,CAAC,IAAI,mBAAmB,YAAY,QAAQ,CAAC,GAAG,UAAU;AACtE,cAAY,CAAC,IAAI,mBAAmB,YAAY,QAAQ,CAAC,GAAG,UAAU;AACtE,SAAO,yBAAyB,YAAY,WAAW;AACzD;AAYA,SAAS,qBAAqB,kBAAkB,aAAa,YAAY;AACvE,QAAM,WAAW,YAAY;AAE7B,MACE,SAAS,QAAQ,MAAM,YACvB,YAAY,UAAU,4BACtB;AACA,QAAI;AAAA;AAAA,MACF;AAAA;AAEF,UAAM,iBAAiB,kBAAkB;AACzC,QAAI,gBAAgB;AAClB;AAAA,MACE,eAAe,MAAM,EAAE,UAAU,gBAAgB,UAAU;AAAA,IAE/D;AACA,WAAO;AAAA,MACL,eAAe;AAAA,QACb,mBAAmB,kBAAkB,UAAU;AAAA,MACjD;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,mBAAmB,kBAAkB,UAAU;AAClE,cAAY,CAAC,IAAI,mBAAmB,YAAY,QAAQ,CAAC,GAAG,UAAU;AACtE,cAAY,CAAC,IAAI,mBAAmB,YAAY,QAAQ,CAAC,GAAG,UAAU;AACtE,SAAO;AAAA,IACL,iBAAiB,YAAY,WAAW;AAAA,IACxC;AAAA,EACF;AACF;AAKA,SAAS,0BAA0B;AACjC,QAAM,QAAQ,mBAAmB;AACjC,SAAO,SAAU,SAAS,YAAY;AACpC,WAAO,MAAM,OAAO;AAAA,EACtB;AACF;AAEA,IAAO,iBAAQ;;;AC1mDf,IAAM,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtB,QAAQ;AACV;AAqEO,IAAM,cAAN,cAA0B,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrC,YAAY,MAAM,UAAU,YAAY,iBAAiB;AACvD,UAAM,IAAI;AAOV,SAAK,WAAW;AAOhB,SAAK,aAAa;AAOlB,SAAK,kBAAkB;AAAA,EACzB;AACF;AAMA,IAAM,wBAAwB,CAAC;AAyB/B,IAAM,SAAN,MAAM,gBAAe,oBAAY;AAAA;AAAA;AAAA;AAAA,EAI/B,YAAY,SAAS;AACnB,UAAM;AAKN,SAAK;AAKL,SAAK;AAKL,SAAK;AAEL,cAAU,UAAU,UAAU,CAAC;AAK/B,SAAK,mBAAmB,KAAK,YAAY,KAAK,IAAI;AAKlD,SAAK,sBAAsB,KAAK,eAAe,KAAK,IAAI;AAMxD,SAAK,aAAa,QAAQ,YAAY,QAAQ,YAAY;AAM1D,SAAK,gBAAgB,QAAQ,eAAe,QAAQ,eAAe;AAMnE,SAAK,mBAAmB,QAAQ,kBAC5B,QAAQ,kBACR;AAMJ,SAAK,mBAAmB,QAAQ,kBAC5B,QAAQ,kBACR;AAMJ,SAAK,SAAS,QAAQ,QAAQ,QAAQ,QAAQ;AAM9C,SAAK,UAAU,QAAQ,SAAS,QAAQ,SAAS;AAMjD,SAAK,gBAAgB,QAAQ,eAAe,QAAQ,eAAe;AAMnE,SAAK,SACH,QAAQ,UAAU,SAAY,QAAQ,QAAQG,yBAAwB;AAMxE,SAAK,YAAY,QAAQ,YAAY,IAAI,mBAAW;AAGpD,QAAI;AACJ,QAAI,QAAQ,QAAQ;AAClB,UAAI,OAAO,QAAQ,WAAW,YAAY;AACxC,sBAAc,QAAQ;AAAA,MACxB,OAAO;AACL,cAAM,SAAS,QAAQ;AACvB,sBAAc,SAAU,OAAO;AAC7B,iBAAO,OAAO,SAAS,KAAK;AAAA,QAC9B;AAAA,MACF;AAAA,IACF,OAAO;AACL,oBAAc;AAAA,IAChB;AAMA,SAAK,eAAe;AAQpB,SAAK,2BAA2B,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,4BAA4B,SAAS,OAAO;AAC1C,SAAK,yBAAyB,OAAO,OAAO,CAAC,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,SAAS;AAChB;AAAA;AAAA,MACE,KAAK,yBAAyB,OAAO,OAAO,CAAC;AAAA;AAAA,EAEjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,cAAc;AAC5B,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KAAK;AACV,UAAM,aAAa,KAAK,OAAO;AAC/B,QAAI,cAAc,KAAK,QAAQ;AAC7B,WAAK,UAAU,QAAQ,KAAK,sBAAsB,KAAK,IAAI,CAAC;AAAA,IAC9D;AACA,UAAM,OAAO,GAAG;AAChB,QAAI,KAAK;AACP,WAAK,UAAU;AAAA,QACb,4BAAoB;AAAA,QACpB,KAAK;AAAA,MACP;AACA,WAAK,UAAU;AAAA,QACb,4BAAoB;AAAA,QACpB,KAAK;AAAA,MACP;AAEA,UAAI,KAAK,QAAQ;AACf,aAAK,UAAU,QAAQ,KAAK,oBAAoB,KAAK,IAAI,CAAC;AAAA,MAC5D;AAAA,IACF,OAAO;AACL,WAAK,UAAU;AAAA,QACb,4BAAoB;AAAA,QACpB,KAAK;AAAA,MACP;AACA,WAAK,UAAU;AAAA,QACb,4BAAoB;AAAA,QACpB,KAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,KAAK;AACf,UAAM,UAAU,IAAI;AACpB,QAAI,KAAK,QAAQ;AACf,WAAK,oBAAoB,OAAO;AAAA,IAClC;AACA,QAAI,CAAC,KAAK,SAAS,OAAO,GAAG;AAC3B,YAAM;AAAA;AAAA,QACJ,KAAK,OAAO,EACT,aAAa,EACb,KAAK,SAAUC,QAAO;AACrB,cACEA,kBAAiBC,mBACjBD,OAAM,UAAU,KAChBA,OAAM,UAAU,EAAE,WAAW,OAAO,GACpC;AACA,mBAAOA;AAAA,UACT;AAAA,QACF,CAAC;AAAA;AAEL,UAAI,OAAO;AACT,aAAK,4BAA4B,SAAS,KAAK;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,KAAK;AAClB,QAAI,KAAK,QAAQ;AACf,WAAK,sBAAsB,IAAI,OAAO;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,SAAS;AAC3B,UAAM,MAAM,OAAO,OAAO;AAC1B,QAAI,EAAE,OAAO,wBAAwB;AACnC,4BAAsB,GAAG,IAAI,QAAQ,SAAS;AAAA,IAChD;AACA,YAAQ,SAAS,KAAK,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,SAAS;AAC7B,UAAM,eAAe,KAAK,OAAO,EAAE,gBAAgB,EAAE,SAAS;AAC9D,aAAS,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACjD,YAAM,cAAc,aAAa,CAAC;AAClC,UACE,gBAAgB,QAChB,uBAAuB,WACvB,YAAY,SAAS,KACrB,YAAY,YAAY,EAAE,SAAS,EAAE,YAAY,OAAO,MAAM,IAC9D;AACA,gBAAQ,SAAS,YAAY,SAAS,CAAC;AACvC;AAAA,MACF;AAAA,IACF;AAEA,UAAM,MAAM,OAAO,OAAO;AAC1B,YAAQ,SAAS,sBAAsB,GAAG,CAAC;AAC3C,WAAO,sBAAsB,GAAG;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,+BAA+B,SAAS;AACtC,WAAO,KAAK,yBAAyB,OAAO,OAAO,CAAC;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,iBAAiB;AAC3B,QAAI,CAAC,KAAK,WAAW,eAAe,GAAG;AACrC,aAAO;AAAA,IACT;AACA,UAAM,MAAM,KAAK,cAAc,eAAe;AAC9C,UAAM,SAAS,KAAK,iBAAiB,eAAe;AACpD,UAAM,SAAS,KAAK,iBAAiB,eAAe;AACpD,UAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AAChC,UAAM,MAAM,gBAAgB;AAC5B,UAAM,WAAW,KAAK,YAAY;AAKlC,UAAM,aAAa,CAAC;AAKpB,UAAM,WAAW,CAAC;AAElB,QAAI,KAAK;AAIP,YAAM,KAAK,wBAAwB;AACnC,UAAI;AAAA,QACF,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMhB,CAAC,SAAS,UAAU;AAClB,cAAI,EAAE,mBAAmB,oBAAY,CAAC,KAAK,QAAQ,SAAS,KAAK,GAAG;AAClE;AAAA,UACF;AACA,eAAK,4BAA4B,SAAS,KAAK;AAC/C,mBAAS,KAAK,OAAO;AACrB,iBAAO,CAAC,KAAK;AAAA,QACf;AAAA,QACA;AAAA,UACE,aAAa,KAAK;AAAA,UAClB,cAAc,KAAK;AAAA,QACrB;AAAA,MACF;AACA,eAAS,IAAI,SAAS,UAAU,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAClD,cAAM,UAAU,SAAS,KAAK,CAAC;AAC/B,cAAM,QAAQ,SAAS,QAAQ,OAAO;AACtC,YAAI,QAAQ,IAAI;AAEd,mBAAS,OAAO,OAAO,CAAC;AAAA,QAC1B,OAAO;AACL,mBAAS,OAAO,OAAO;AACvB,qBAAW,KAAK,OAAO;AAAA,QACzB;AAAA,MACF;AACA,UAAI,SAAS,WAAW,GAAG;AACzB,iBAAS,OAAO,QAAQ;AAAA,MAC1B;AAAA,IACF,OAAO;AAEL,UAAI;AAAA,QACF,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMhB,CAAC,SAAS,UAAU;AAClB,cAAI,EAAE,mBAAmB,oBAAY,CAAC,KAAK,QAAQ,SAAS,KAAK,GAAG;AAClE;AAAA,UACF;AACA,eAAK,OAAO,WAAW,CAAC,SAAS,SAAS,EAAE,SAAS,OAAO,GAAG;AAC7D,iBAAK,4BAA4B,SAAS,KAAK;AAC/C,qBAAS,KAAK,OAAO;AAAA,UACvB,YACG,UAAU,WACX,SAAS,SAAS,EAAE,SAAS,OAAO,GACpC;AACA,uBAAW,KAAK,OAAO;AACvB,iBAAK,+BAA+B,OAAO;AAAA,UAC7C;AACA,iBAAO,CAAC,KAAK;AAAA,QACf;AAAA,QACA;AAAA,UACE,aAAa,KAAK;AAAA,UAClB,cAAc,KAAK;AAAA,QACrB;AAAA,MACF;AACA,eAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAC/C,iBAAS,OAAO,WAAW,CAAC,CAAC;AAAA,MAC/B;AACA,eAAS,OAAO,QAAQ;AAAA,IAC1B;AACA,QAAI,SAAS,SAAS,KAAK,WAAW,SAAS,GAAG;AAChD,WAAK;AAAA,QACH,IAAI;AAAA,UACF,gBAAgB;AAAA,UAChB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAKA,SAASD,2BAA0B;AACjC,QAAM,SAAS,mBAAmB;AAClC,SAAO,OAAO,SAAS,GAAG,OAAO,YAAY,CAAC;AAC9C,SAAO,OAAO,oBAAoB,GAAG,OAAO,YAAY,CAAC;AAEzD,SAAO,SAAU,SAAS;AACxB,QAAI,CAAC,QAAQ,YAAY,GAAG;AAC1B,aAAO;AAAA,IACT;AACA,WAAO,OAAO,QAAQ,YAAY,EAAE,QAAQ,CAAC;AAAA,EAC/C;AACF;AAEA,IAAO,iBAAQ;;;ACrkBR,IAAM,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3B,MAAM;AACR;AAMO,IAAM,YAAN,cAAwB,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnC,YAAY,MAAM,SAAS;AACzB,UAAM,IAAI;AAMV,SAAK,SAAS,QAAQ;AAMtB,SAAK,cAAc,QAAQ;AAM3B,SAAK,UAAU,QAAQ;AAAA,EACzB;AACF;;;ACGA,SAAS,oBAAoB,KAAK;AAChC;AAAA;AAAA,IACkE,IAAK;AAAA,IACrE;AACA;AAAA;AAAA,MAAuE,IACpE;AAAA;AAAA,EACL;AACA;AAAA;AAAA,IAEI,IACA;AAAA,IACF;AACA;AAAA;AAAA,MACE,IACA;AAAA;AAAA,EACJ;AACA,SAAO;AACT;AAEA,IAAMG,eAAc,CAAC;AAoCrB,IAAM,OAAN,cAAmB,gBAAmB;AAAA;AAAA;AAAA;AAAA,EAIpC,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B,UAAM;AAAA;AAAA,MACJ;AAAA;AAGF,QAAI,CAAC,eAAe,iBAAiB;AACnC,qBAAe,kBAAkB;AAAA,IACnC;AAEA,QAAI,CAAC,eAAe,UAAU;AAC5B,qBAAe,WAAW;AAAA,IAC5B;AAEA,UAAM,cAAc;AAKpB,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,SAAK,UAAU,QAAQ,SAAS,QAAQ,SAAS;AAMjD,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAM/D,SAAK,QAAQ,QAAQ,SAAS,SAAY,QAAQ,OAAO;AAMzD,SAAK,YAAY,QAAQ,WAAW,QAAQ,WAAW;AAMvD,SAAK,wBAAwB,CAAC;AAM9B,SAAK,6BAA6B,CAAC;AAQnC,SAAK,0BAA0B,CAAC;AAShC,SAAK,mBAAmB,CAAC;AAMzB,SAAK,kBACH,QAAQ,mBAAmB,SAAY,QAAQ,iBAAiB;AAOlE,SAAK,SAAS,IAAI,cAAM;AAOxB,SAAK,uBAAuB;AAAA,MAC1B,SAAS,KAAK,sBAAsB,KAAK,IAAI;AAAA,MAC7C,cAAc,KAAK,2BAA2B,KAAK,IAAI;AAAA,MACvD,cAAc,KAAK,2BAA2B,KAAK,IAAI;AAAA,MACvD,WAAW,KAAK,wBAAwB,KAAK,IAAI;AAAA,MACjD,cAAc,KAAK,2BAA2B,KAAK,IAAI;AAAA,MACvD,mBAAmB,KAAK,gCAAgC,KAAK,IAAI;AAAA,MACjE,gBAAgB,KAAK,6BAA6B,KAAK,IAAI;AAAA,MAC3D,sBAAsB,KAAK,mCAAmC,KAAK,IAAI;AAAA,MACvE,UAAU,KAAK,uBAAuB,KAAK,IAAI;AAAA,IACjD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,SAAS,UAAU;AAC5B,eAAW,aAAa,SAAY,WAAW;AAC/C,UAAM,cAAc,OAAO,OAAO;AAClC,UAAM,WAAW,QAAQ,YAAY;AACrC,QAAI,UAAU;AACZ,YAAM,YAAY,KAAK,qBAAqB,SAAS,QAAQ,CAAC;AAC9D,UAAI,WAAW;AACb,aAAK,wBAAwB,WAAW,IAAI,SAAS;AAAA,UACnD,YAAY;AAAA,QACd;AACA,cAAM;AAAA;AAAA,UACgE,CAAC;AAAA;AACvE,kBAAU,UAAU,QAAQ;AAC5B,YAAI,SAAS,WAAW,GAAG;AACzB,eAAK,OAAO,OAAO,eAAe,SAAS,CAAC,CAAC,GAAG;AAAA,YAC9C;AAAA,YACA,SAAS,SAAS,CAAC;AAAA,UACrB,CAAC;AAAA,QACH,WAAW,SAAS,SAAS,GAAG;AAC9B,gBAAM,UAAU,SAAS,IAAI,CAAC,MAAM,eAAe,CAAC,CAAC;AACrD,gBAAM,eAAe,SAAS,IAAI,CAAC,aAAa;AAAA,YAC9C;AAAA,YACA;AAAA,UACF,EAAE;AACF,eAAK,OAAO,KAAK,SAAS,YAAY;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AAEA,QAAI,UAAU;AACZ,WAAK,2BAA2B,WAAW,IAAI;AAAA,QAC7C;AAAA,QACA,kBAAU;AAAA,QACV,KAAK;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AAEb,QAAI;AACJ,QAAI,KAAK,WAAW;AAClB,iBAAW,KAAK;AAAA,IAClB,WAAW,KAAK,SAAS;AACvB,iBAAW,KAAK,QAAQ,YAAY;AAAA,IACtC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,KAAK;AACf,UAAM,SAAS,KAAK,OAAO,IAAI,OAAO,IAAI,YAAY,IAAI,GAAG;AAC7D,QAAI,QAAQ;AACV,UAAI,aAAa,OAAO,OAAO,MAAM,GAAG,CAAC;AACzC,UAAI,QAAQ,OAAO;AACnB,WAAK;AAAA,QACH,IAAI,UAAU,cAAc,MAAM;AAAA,UAChC,QAAQ,IAAI;AAAA,UACZ,aAAa,IAAI;AAAA,UACjB,SAAS,OAAO;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO,MAAM,YAAY,GAAG;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,KAAK;AACrB,UAAM,UAAU,oBAAoB,GAAG;AACvC,QAAI,SAAS;AACX,WAAK,WAAW,OAAO;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,KAAK;AACxB,UAAM,UAAU,oBAAoB,GAAG;AACvC,QAAI,SAAS;AACX,WAAK,cAAc,OAAO;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,KAAK;AACxB,UAAM;AAAA;AAAA,MAA0D,IAAI;AAAA;AACpE,QAAI,KAAK,wBAAwB;AAC/B,YAAM,MAAM,OAAO,OAAO;AAC1B,UAAI,EAAE,OAAO,KAAK,mBAAmB;AACnC,aAAK,iBAAiB,GAAG,IAAI;AAAA,MAC/B;AAAA,IACF,OAAO;AACL,WAAK,eAAe,OAAO;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,KAAK;AACjB,UAAM,mBAAmB,OAAO,OAAO,KAAK,gBAAgB;AAC5D,QAAI,iBAAiB,QAAQ;AAC3B,uBAAiB,QAAQ,KAAK,eAAe,KAAK,IAAI,CAAC;AACvD,WAAK,mBAAmB,CAAC;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,SAAS,UAAU;AAC/B,UAAM,aAAa,aAAa,SAAY,WAAW;AACvD,UAAM,cAAc,OAAO,OAAO;AAClC,UAAM,SAAS,KAAK,wBAAwB,WAAW;AACvD,QAAI,QAAQ;AACV,YAAM,QAAQ,KAAK;AACnB,YAAM,gBAAgB,CAAC;AACvB,YAAM,gBAAgB,QAAQ,SAAU,MAAM;AAC5C,YAAI,YAAY,KAAK,SAAS;AAC5B,wBAAc,KAAK,IAAI;AAAA,QACzB;AAAA,MACF,CAAC;AACD,eAAS,IAAI,cAAc,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAClD,cAAM,OAAO,cAAc,CAAC,CAAC;AAAA,MAC/B;AAAA,IACF;AAEA,QAAI,YAAY;AACd,oBAAc,KAAK,2BAA2B,WAAW,CAAC;AAC1D,aAAO,KAAK,2BAA2B,WAAW;AAAA,IACpD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KAAK;AACV,UAAM,aAAa,KAAK,OAAO;AAC/B,UAAM,OAAO,KAAK;AAClB,UAAM;AAAA;AAAA,MACJ,KAAK,aAAa;AAAA;AAGpB,QAAI,YAAY;AACd,WAAK,QAAQ,aAAa;AAC1B,WAAK,SAAS;AACd,WAAK,OAAO,MAAM;AAClB,aAAO,OAAO,KAAK,0BAA0B,EAAE,QAAQ,aAAa;AACpE,WAAK,6BAA6B,CAAC;AAAA,IACrC;AACA,UAAM,OAAO,GAAG;AAEhB,QAAI,KAAK;AACP,UAAI,KAAK,WAAW;AAClB,aAAK;AAAA,UACH;AAAA,YACE,KAAK;AAAA,YACL,4BAAoB;AAAA,YACpB,KAAK;AAAA,YACL;AAAA,UACF;AAAA,UACA;AAAA,YACE,KAAK;AAAA,YACL,4BAAoB;AAAA,YACpB,KAAK;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF,WAAW,KAAK,SAAS;AACvB,aAAK;AAAA,UACH;AAAA,YACE,KAAK;AAAA,YACL,wBAAgB;AAAA,YAChB,KAAK;AAAA,YACL;AAAA,UACF;AAAA,UACA;AAAA,YACE,KAAK;AAAA,YACL,wBAAgB;AAAA,YAChB,KAAK;AAAA,YACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,eAAS,QAAQ,CAAC,YAAY,KAAK,WAAW,OAAO,CAAC;AAAA,IACxD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,OAAO,iBAAiB,KAAK;AAClC,UAAM,aAAa,IAAI,QAAQ,EAAE,cAAc;AAC/C,UAAM,sBAAsB,mBAAmB,iBAAiB,UAAU;AAE1E,UAAM,MAAM;AAAA,MACV;AAAA,QACE,eAAe,CAAC,mBAAmB,CAAC;AAAA,QACpC,IAAI,QAAQ,EAAE,cAAc,IAAI,KAAK;AAAA,MACvC;AAAA,MACA;AAAA,IACF;AAEA,UAAM,WAAW,KAAK,OAAO,YAAY,GAAG;AAC5C,UAAM,iBAAiB,SAAS;AAChC,QAAI,mBAAmB,GAAG;AACxB,aAAO;AAAA,IACT;AAEA,QAAI;AACJ,QAAI,qBAAqB;AACzB,QAAI;AAEJ,UAAM,wBAAwB,KAAK,kBAAkB,KAAK;AAC1D,UAAM,YAAY,MAAM;AACtB,UAAI,eAAe;AACjB,cAAM,cAAc,IAAI,uBAAuB,aAAa;AAC5D,cAAM,uBAAuB,gBAAgB,OAAO,WAAW;AAC/D,YAAI,wBAAwB,uBAAuB;AACjD,iBAAO;AAAA,YACL,QAAQ;AAAA,YACR,aAAa;AAAA,cACX,KAAK,MAAM,YAAY,CAAC,CAAC;AAAA,cACzB,KAAK,MAAM,YAAY,CAAC,CAAC;AAAA,YAC3B;AAAA,YACA,SAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,SAAS;AAChB,eAAS,IAAI,GAAG,IAAI,gBAAgB,EAAE,GAAG;AACvC,cAAM,cAAc,SAAS,CAAC;AAC9B,YAAI,YAAY,QAAQ,YAAY,EAAE,QAAQ,MAAM,UAAU;AAC5D,sBAAY,QAAQ,QAAQ,CAAC,WAAW;AACtC,kBAAM,kBAAkB,mBAAmB,QAAQ,UAAU;AAC7D,kBAAM,QAAQ,gBAAgB,qBAAqB,eAAe;AAClE,gBAAI,QAAQ,oBAAoB;AAC9B,8BAAgB;AAChB,mCAAqB;AACrB,+BAAiB,YAAY;AAAA,YAC/B;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AACA,YAAM,SAAS,UAAU;AACzB,UAAI,QAAQ;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,KAAK,OAAO;AACd,eAAS,IAAI,GAAG,IAAI,gBAAgB,EAAE,GAAG;AACvC,YAAI,SAAS;AACb,cAAM,cAAc,SAAS,CAAC;AAC9B,YAAI,YAAY,QAAQ,YAAY,EAAE,QAAQ,MAAM,UAAU;AAC5D,cAAI,iBAAiB,YAAY,QAAQ,YAAY;AACrD,gBAAM,iBAAiB,kBAAkB;AACzC,cAAI,gBAAgB;AAClB,6BAAiB,eACd,MAAM,EACN,UAAU,gBAAgB,UAAU;AAAA,UACzC;AACA,mBAAS;AAAA,YACP;AAAA;AAAA,YACoD;AAAA,UACtD;AAAA,QACF,OAAO;AACL,gBAAM,CAAC,cAAc,UAAU,IAAI,YAAY;AAE/C,cAAI,YAAY;AACd,YAAAA,aAAY,CAAC,IAAI,mBAAmB,cAAc,UAAU;AAC5D,YAAAA,aAAY,CAAC,IAAI,mBAAmB,YAAY,UAAU;AAC1D,qBAAS,iBAAiB,qBAAqBA,YAAW;AAAA,UAC5D;AAAA,QACF;AACA,YAAI,QAAQ;AACV,gBAAM,QAAQ,gBAAgB,qBAAqB,MAAM;AACzD,cAAI,QAAQ,oBAAoB;AAC9B,4BAAgB,iBAAiB,QAAQ,UAAU;AACnD,iCAAqB;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAEA,YAAM,SAAS,UAAU;AACzB,UAAI,QAAQ;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,SAAS;AACtB,SAAK,cAAc,SAAS,KAAK;AACjC,SAAK,WAAW,SAAS,KAAK;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,uBAAuB,UAAU,UAAU;AACzC,UAAM,aAAa,KAAK,OAAO,EAAE,QAAQ,EAAE,cAAc;AACzD,QAAI,iBAAiB;AACrB,UAAM,iBAAiB,kBAAkB;AACzC,QAAI,gBAAgB;AAClB;AAAA,MACE,eAAe,MAAM,EAAE,UAAU,gBAAgB,UAAU;AAAA,IAE/D;AACA,UAAM,UAAU,WAAW,cAAc;AACzC,QAAI,gBAAgB;AAClB,cAAQ,UAAU,YAAY,cAAc;AAAA,IAC9C;AACA,UAAM,cAAc,QAAQ,eAAe,EAAE,CAAC;AAC9C,aAAS,IAAI,GAAG,KAAK,YAAY,SAAS,GAAG,IAAI,IAAI,EAAE,GAAG;AACxD,eAAS,KAAK,YAAY,MAAM,GAAG,IAAI,CAAC,CAAC;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mCAAmC,UAAU,UAAU;AACrD,UAAM,aAAa,SAAS,mBAAmB;AAC/C,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AAC1C,YAAM,YAAY,KAAK,qBAAqB,WAAW,CAAC,EAAE,QAAQ,CAAC;AACnE,UAAI,WAAW;AACb,kBAAU,UAAU,WAAW,CAAC,CAAC;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,2BAA2B,UAAU,UAAU;AAC7C,UAAM,cAAc,SAAS,eAAe;AAC5C,aAAS,IAAI,GAAG,KAAK,YAAY,SAAS,GAAG,IAAI,IAAI,EAAE,GAAG;AACxD,eAAS,KAAK,YAAY,MAAM,GAAG,IAAI,CAAC,CAAC;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gCAAgC,UAAU,UAAU;AAClD,UAAM,QAAQ,SAAS,eAAe;AACtC,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,YAAM,cAAc,MAAM,CAAC;AAC3B,eAAS,IAAI,GAAG,KAAK,YAAY,SAAS,GAAG,IAAI,IAAI,EAAE,GAAG;AACxD,iBAAS,KAAK,YAAY,MAAM,GAAG,IAAI,CAAC,CAAC;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,2BAA2B,UAAU,UAAU;AAC7C,aAAS,eAAe,EAAE,QAAQ,CAAC,UAAU;AAC3C,eAAS,KAAK,CAAC,KAAK,CAAC;AAAA,IACvB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,6BAA6B,UAAU,UAAU;AAC/C,UAAM,WAAW,SAAS,eAAe;AACzC,aAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,EAAE,GAAG;AACjD,YAAM,QAAQ,SAAS,CAAC;AACxB,eAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,cAAM,cAAc,MAAM,CAAC;AAC3B,iBAAS,IAAI,GAAG,KAAK,YAAY,SAAS,GAAG,IAAI,IAAI,EAAE,GAAG;AACxD,mBAAS,KAAK,YAAY,MAAM,GAAG,IAAI,CAAC,CAAC;AAAA,QAC3C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,UAAU,UAAU;AACxC,aAAS,KAAK,CAAC,SAAS,eAAe,CAAC,CAAC;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB,UAAU,UAAU;AAC1C,UAAM,QAAQ,SAAS,eAAe;AACtC,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,YAAM,cAAc,MAAM,CAAC;AAC3B,eAAS,IAAI,GAAG,KAAK,YAAY,SAAS,GAAG,IAAI,IAAI,EAAE,GAAG;AACxD,iBAAS,KAAK,YAAY,MAAM,GAAG,IAAI,CAAC,CAAC;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,eAAQ;;;ACpqBf,IAAM,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,cAAc;AAChB;AAoCO,IAAM,iBAAN,cAA6B,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQxC,YAAY,MAAM,UAAU,YAAY,iBAAiB,iBAAiB;AACxE,UAAM,IAAI;AAOV,SAAK,WAAW;AAQhB,SAAK,aAAa;AAQlB,SAAK,kBAAkB;AAOvB,SAAK,kBAAkB;AAAA,EACzB;AACF;AAsBA,IAAM,YAAN,cAAwB,gBAAmB;AAAA;AAAA;AAAA;AAAA,EAIzC,YAAY,SAAS;AACnB,cAAU,UAAU,UAAU,CAAC;AAE/B;AAAA;AAAA,MAAqD;AAAA,IAAQ;AAK7D,SAAK;AAKL,SAAK;AAKL,SAAK;AAOL,SAAK,kBAAkB;AAOvB,SAAK,mBAAmB;AAMxB,SAAK,YAAY,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAGrE,QAAI;AACJ,QAAI,QAAQ,UAAU,CAAC,KAAK,WAAW;AACrC,UAAI,OAAO,QAAQ,WAAW,YAAY;AACxC,sBAAc,QAAQ;AAAA,MACxB,OAAO;AACL,cAAM,SAAS,QAAQ;AACvB,sBAAc,SAAU,OAAO;AAC7B,iBAAO,OAAO,SAAS,KAAK;AAAA,QAC9B;AAAA,MACF;AAAA,IACF,OAAO;AACL,oBAAc;AAAA,IAChB;AAMA,SAAK,eAAe;AAMpB,SAAK,UAAU,QAAQ,UAAU,CAAC,KAAK,YAAY,QAAQ,SAAS;AAMpE,SAAK,gBAAgB,QAAQ,eAAe,QAAQ,eAAe;AAMnE,SAAK,aAAa,QAAQ,YAAY,QAAQ,YAAY;AAM1D,SAAK,eAAe;AAEpB,SAAK;AAAA,MACH,iBAAoB;AAAA,MACpB,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,OAAO;AACrB,QAAI,CAAC,MAAM,iBAAiB,CAAC,KAAK,WAAW,KAAK,GAAG;AACnD,aAAO;AAAA,IACT;AACA,SAAK,eAAe,KAAK,iBAAiB,MAAM,OAAO,MAAM,GAAG;AAChE,QAAI,CAAC,KAAK,mBAAmB,KAAK,cAAc;AAC9C,WAAK,mBAAmB,MAAM;AAC9B,WAAK,kBAAkB,MAAM;AAC7B,WAAK,gBAAgB,KAAK;AAE1B,YAAM,WAAW,KAAK,aAAa,IAAI,mBAAW,CAAC,KAAK,YAAY,CAAC;AAErE,WAAK;AAAA,QACH,IAAI;AAAA,UACF,mBAAmB;AAAA,UACnB;AAAA,UACA,MAAM;AAAA,UACN,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,OAAO;AACnB,QAAI,KAAK,iBAAiB;AACxB,WAAK,kBAAkB;AACvB,WAAK,gBAAgB,KAAK;AAE1B,YAAM,WAAW,KAAK,aAAa,IAAI,mBAAW,CAAC,KAAK,YAAY,CAAC;AAErE,WAAK;AAAA,QACH,IAAI;AAAA,UACF,mBAAmB;AAAA,UACnB;AAAA,UACA,MAAM;AAAA,UACN,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAEA,WAAK,mBAAmB;AACxB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,OAAO;AACrB,QAAI,KAAK,iBAAiB;AACxB,YAAM,gBAAgB,MAAM;AAC5B,YAAM,aAAa,MAAM,IAAI,QAAQ,EAAE,cAAc;AAErD,YAAM,oBAAoB,mBAAmB,eAAe,UAAU;AACtE,YAAM,qBAAqB;AAAA,QACzB,KAAK;AAAA,QACL;AAAA,MACF;AACA,YAAM,SAAS,kBAAkB,CAAC,IAAI,mBAAmB,CAAC;AAC1D,YAAM,SAAS,kBAAkB,CAAC,IAAI,mBAAmB,CAAC;AAE1D,YAAM,WAAW,KAAK,aAAa,IAAI,mBAAW,CAAC,KAAK,YAAY,CAAC;AACrE,YAAM,iBAAiB,kBAAkB;AAEzC,eAAS,QAAQ,SAAU,SAAS;AAClC,cAAM,OAAO,QAAQ,YAAY;AACjC,YAAI,gBAAgB;AAClB,eAAK,UAAU,gBAAgB,UAAU;AACzC,eAAK,UAAU,QAAQ,MAAM;AAC7B,eAAK,UAAU,YAAY,cAAc;AAAA,QAC3C,OAAO;AACL,eAAK,UAAU,QAAQ,MAAM;AAAA,QAC/B;AACA,gBAAQ,YAAY,IAAI;AAAA,MAC1B,CAAC;AAED,WAAK,kBAAkB;AAEvB,WAAK;AAAA,QACH,IAAI;AAAA,UACF,mBAAmB;AAAA,UACnB;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,OAAO;AACrB,UAAM,OAAO,MAAM,IAAI,YAAY;AAInC,QAAI,KAAK,iBAAiB,MAAM,OAAO,MAAM,GAAG,GAAG;AACjD,WAAK,UAAU,OAAO,KAAK,kBAAkB,YAAY,aAAa;AACtE,WAAK,UAAU,IAAI,KAAK,kBAAkB,gBAAgB,SAAS;AAAA,IACrE,OAAO;AACL,WAAK,UAAU,OAAO,WAAW,aAAa;AAAA,IAChD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,iBAAiB,OAAO,KAAK;AAC3B,WAAO,IAAI;AAAA,MACT;AAAA,MACA,CAAC,SAAS,UAAU;AAClB,YAAI,EAAE,mBAAmB,oBAAY,CAAC,KAAK,QAAQ,SAAS,KAAK,GAAG;AAClE,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,aAAa,CAAC,KAAK,UAAU,SAAS,EAAE,SAAS,OAAO,GAAG;AAClE,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA;AAAA,QACE,aAAa,KAAK;AAAA,QAClB,cAAc,KAAK;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,cAAc;AAC5B,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KAAK;AACV,UAAM,SAAS,KAAK,OAAO;AAC3B,UAAM,OAAO,GAAG;AAChB,SAAK,aAAa,MAAM;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,SAAK,aAAa,IAAI;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,QAAQ;AACnB,QAAI,MAAM,KAAK,OAAO;AACtB,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,OAAO,CAAC,QAAQ;AACnB,YAAM,OAAO;AACb,UAAI,KAAK;AACP,cAAM,OAAO,IAAI,YAAY;AAC7B,aAAK,UAAU,OAAO,WAAW,aAAa;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,oBAAQ;", "names": ["distance", "Vector_default", "Vector_default", "geometry", "equals", "coordinate", "buffer", "getDefaultStyleFunction", "layer", "Vector_default", "tempSegment"]}