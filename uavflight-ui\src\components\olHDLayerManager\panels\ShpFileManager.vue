<!--
 * @Author: AI Assistant 
 * @Date: 2025-01-04 
 * @Description: SHP文件临时加载和属性查询面板
 *  
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved. 
-->
<template>
  <div class="shp-file-manager">
    <div class="panel-header">
      <h3>SHP文件管理</h3>
      <p class="panel-description">临时加载SHP文件并进行属性查询</p>
    </div>

    <div class="panel-content">
      <!-- 文件上传区域 -->
      <div class="upload-section">
        <h4>文件上传</h4>
        <div class="upload-area">
          <el-upload
            ref="uploadRef"
            class="shp-upload"
            drag
            :auto-upload="false"
            :multiple="true"
            :accept=".shp,.dbf,.shx,.prj"
            :on-change="handleFileChange"
            :file-list="fileList"
            :show-file-list="true"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将SHP文件拖拽到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                请同时上传 .shp, .dbf 文件（.shx, .prj 可选）
              </div>
            </template>
          </el-upload>
        </div>
        
        <div class="upload-actions">
          <el-button 
            type="primary" 
            @click="loadShpFile" 
            :loading="loading"
            :disabled="!canLoadShp"
          >
            加载到地图
          </el-button>
          <el-button @click="clearFiles">清空文件</el-button>
        </div>
      </div>

      <!-- 已加载图层列表 -->
      <div class="loaded-layers-section" v-if="loadedLayers.length > 0">
        <h4>已加载图层</h4>
        <div class="layer-list">
          <div 
            v-for="layer in loadedLayers" 
            :key="layer.id"
            class="layer-item"
            :class="{ 'active': layer.id === activeLayerId }"
          >
            <div class="layer-info">
              <span class="layer-name">{{ layer.name }}</span>
              <span class="feature-count">{{ layer.featureCount }} 个要素</span>
            </div>
            <div class="layer-actions">
              <el-button 
                size="small" 
                type="primary" 
                @click="toggleAttributeQuery(layer.id)"
                :class="{ 'active': attributeQueryActive && activeLayerId === layer.id }"
              >
                {{ attributeQueryActive && activeLayerId === layer.id ? '关闭查询' : '属性查询' }}
              </el-button>
              <el-button 
                size="small" 
                type="danger" 
                @click="removeLayer(layer.id)"
              >
                移除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 属性查询结果 -->
      <div class="query-result-section" v-if="queryResult">
        <h4>属性信息</h4>
        <div class="attribute-table">
          <el-table :data="[queryResult]" size="small" border>
            <el-table-column 
              v-for="(value, key) in queryResult" 
              :key="key"
              :prop="key"
              :label="key"
              :width="150"
              show-overflow-tooltip
            />
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, inject, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { UploadFilled } from '@element-plus/icons-vue';
import { Vector as VectorLayer } from 'ol/layer';
import { Vector as VectorSource } from 'ol/source';
import { Style, Stroke, Fill, Circle } from 'ol/style';
import { GeoJSON } from 'ol/format';

// 注入地图实例
const map = inject('map');

// 响应式数据
const loading = ref(false);
const fileList = ref([]);
const loadedLayers = ref([]);
const attributeQueryActive = ref(false);
const activeLayerId = ref('');
const queryResult = ref(null);
const uploadRef = ref();

// 地图点击监听器
let mapClickListener = null;

// 计算属性
const canLoadShp = computed(() => {
  const hasShp = fileList.value.some(file => file.name.endsWith('.shp'));
  const hasDbf = fileList.value.some(file => file.name.endsWith('.dbf'));
  return hasShp && hasDbf && !loading.value;
});

// 文件变化处理
const handleFileChange = (file, files) => {
  console.log('文件变化:', file.name, '总文件数:', files.length);
  fileList.value = files;
  
  // 检查文件类型
  const allowedExtensions = ['.shp', '.dbf', '.shx', '.prj'];
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
  
  if (!allowedExtensions.includes(fileExtension)) {
    ElMessage.warning(`不支持的文件类型: ${fileExtension}`);
    return;
  }
  
  console.log('文件类型检查通过:', fileExtension);
};

// 清空文件
const clearFiles = () => {
  fileList.value = [];
  uploadRef.value?.clearFiles();
  console.log('已清空所有文件');
};

// 加载SHP文件到地图
const loadShpFile = async () => {
  if (!canLoadShp.value) {
    ElMessage.warning('请确保同时上传了 .shp 和 .dbf 文件');
    return;
  }

  loading.value = true;
  console.log('开始加载SHP文件...');

  try {
    // 动态导入 shapefile-js
    const shapefile = await import('shapefile');
    console.log('shapefile-js 库加载成功');

    // 获取文件
    const shpFile = fileList.value.find(file => file.name.endsWith('.shp'));
    const dbfFile = fileList.value.find(file => file.name.endsWith('.dbf'));

    if (!shpFile || !dbfFile) {
      throw new Error('缺少必要的 .shp 或 .dbf 文件');
    }

    console.log('找到文件:', shpFile.name, dbfFile.name);

    // 读取文件为 ArrayBuffer
    const shpBuffer = await readFileAsArrayBuffer(shpFile.raw);
    const dbfBuffer = await readFileAsArrayBuffer(dbfFile.raw);

    console.log('文件读取完成，开始解析...');

    // 使用 shapefile-js 解析
    const geojson = await shapefile.read(shpBuffer, dbfBuffer);
    console.log('SHP解析成功，要素数量:', geojson.features.length);

    // 添加到地图
    addGeoJSONToMap(geojson, shpFile.name);

  } catch (error) {
    console.error('加载SHP文件失败:', error);
    ElMessage.error(`加载失败: ${error.message}`);
  } finally {
    loading.value = false;
  }
};

// 读取文件为 ArrayBuffer
const readFileAsArrayBuffer = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = (e) => reject(new Error('文件读取失败'));
    reader.readAsArrayBuffer(file);
  });
};

// 添加 GeoJSON 到地图
const addGeoJSONToMap = (geojson, fileName) => {
  if (!map) {
    console.error('地图实例不存在');
    return;
  }

  try {
    // 创建矢量数据源
    const vectorSource = new VectorSource({
      features: new GeoJSON().readFeatures(geojson, {
        featureProjection: 'EPSG:3857'
      })
    });

    console.log('矢量数据源创建成功，要素数量:', vectorSource.getFeatures().length);

    // 创建样式
    const style = new Style({
      stroke: new Stroke({
        color: '#3399CC',
        width: 2
      }),
      fill: new Fill({
        color: 'rgba(51, 153, 204, 0.2)'
      }),
      image: new Circle({
        radius: 5,
        fill: new Fill({
          color: '#3399CC'
        })
      })
    });

    // 创建矢量图层
    const vectorLayer = new VectorLayer({
      source: vectorSource,
      style: style
    });

    // 生成唯一ID
    const layerId = `shp_${Date.now()}`;
    vectorLayer.set('id', layerId);
    vectorLayer.set('name', fileName);

    // 添加到地图
    map.addLayer(vectorLayer);
    console.log('图层已添加到地图:', layerId);

    // 添加到已加载图层列表
    loadedLayers.value.push({
      id: layerId,
      name: fileName.replace('.shp', ''),
      layer: vectorLayer,
      featureCount: vectorSource.getFeatures().length
    });

    // 缩放到图层范围
    const extent = vectorSource.getExtent();
    if (extent && extent.every(coord => isFinite(coord))) {
      map.getView().fit(extent, { padding: [20, 20, 20, 20] });
      console.log('已缩放到图层范围');
    }

    ElMessage.success(`成功加载 ${fileName}，包含 ${vectorSource.getFeatures().length} 个要素`);

    // 清空文件列表
    clearFiles();

  } catch (error) {
    console.error('添加图层到地图失败:', error);
    ElMessage.error(`添加图层失败: ${error.message}`);
  }
};

// 切换属性查询模式
const toggleAttributeQuery = (layerId) => {
  console.log('切换属性查询模式:', layerId);
  
  if (attributeQueryActive.value && activeLayerId.value === layerId) {
    // 关闭查询模式
    deactivateAttributeQuery();
  } else {
    // 开启查询模式
    activateAttributeQuery(layerId);
  }
};

// 激活属性查询
const activateAttributeQuery = (layerId) => {
  console.log('激活属性查询:', layerId);
  
  // 先关闭之前的查询
  deactivateAttributeQuery();
  
  attributeQueryActive.value = true;
  activeLayerId.value = layerId;
  queryResult.value = null;
  
  // 添加地图点击监听
  if (map) {
    mapClickListener = map.on('singleclick', handleMapClick);
    console.log('地图点击监听已添加');
    
    // 改变鼠标样式
    map.getTargetElement().style.cursor = 'crosshair';
  }
  
  ElMessage.info('属性查询已激活，点击要素查看属性');
};

// 停用属性查询
const deactivateAttributeQuery = () => {
  console.log('停用属性查询');
  
  attributeQueryActive.value = false;
  activeLayerId.value = '';
  queryResult.value = null;
  
  // 移除地图点击监听
  if (mapClickListener) {
    mapClickListener.remove();
    mapClickListener = null;
    console.log('地图点击监听已移除');
  }
  
  // 恢复鼠标样式
  if (map) {
    map.getTargetElement().style.cursor = '';
  }
};

// 处理地图点击
const handleMapClick = (event) => {
  console.log('地图点击事件:', event.coordinate);
  
  if (!attributeQueryActive.value || !activeLayerId.value) {
    return;
  }
  
  // 查找目标图层
  const targetLayer = loadedLayers.value.find(layer => layer.id === activeLayerId.value);
  if (!targetLayer) {
    console.error('未找到目标图层:', activeLayerId.value);
    return;
  }
  
  // 在点击位置查找要素
  const features = [];
  map.forEachFeatureAtPixel(event.pixel, (feature, layer) => {
    if (layer === targetLayer.layer) {
      features.push(feature);
    }
  });
  
  console.log('找到要素数量:', features.length);
  
  if (features.length > 0) {
    // 获取第一个要素的属性
    const feature = features[0];
    const properties = feature.getProperties();
    
    // 移除几何属性
    delete properties.geometry;
    
    console.log('要素属性:', properties);
    queryResult.value = properties;
    
    ElMessage.success('已获取要素属性信息');
  } else {
    queryResult.value = null;
    ElMessage.info('未找到要素，请点击图层要素');
  }
};

// 移除图层
const removeLayer = async (layerId) => {
  console.log('准备移除图层:', layerId);
  
  try {
    await ElMessageBox.confirm('确定要移除这个图层吗？', '确认删除', {
      type: 'warning'
    });
    
    // 查找图层
    const layerIndex = loadedLayers.value.findIndex(layer => layer.id === layerId);
    if (layerIndex === -1) {
      console.error('未找到要移除的图层:', layerId);
      return;
    }
    
    const layerInfo = loadedLayers.value[layerIndex];
    
    // 如果是当前查询的图层，先停用查询
    if (activeLayerId.value === layerId) {
      deactivateAttributeQuery();
    }
    
    // 从地图移除图层
    if (map && layerInfo.layer) {
      map.removeLayer(layerInfo.layer);
      console.log('图层已从地图移除:', layerId);
    }
    
    // 从列表移除
    loadedLayers.value.splice(layerIndex, 1);
    
    ElMessage.success('图层已移除');
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除图层失败:', error);
      ElMessage.error('移除图层失败');
    }
  }
};

// 组件销毁时清理
onBeforeUnmount(() => {
  console.log('SHP文件管理面板销毁，清理资源...');
  
  // 停用属性查询
  deactivateAttributeQuery();
  
  // 移除所有加载的图层
  loadedLayers.value.forEach(layerInfo => {
    if (map && layerInfo.layer) {
      map.removeLayer(layerInfo.layer);
    }
  });
  
  loadedLayers.value = [];
  console.log('资源清理完成');
});

// 组件挂载时的初始化
onMounted(() => {
  console.log('SHP文件管理面板已挂载');
  
  if (!map) {
    console.error('未找到地图实例，SHP文件管理功能可能无法正常工作');
    ElMessage.error('地图实例未找到');
  } else {
    console.log('地图实例已找到，SHP文件管理功能已就绪');
  }
});
</script>

<style scoped lang="scss">
.shp-file-manager {
  height: 100%;
  display: flex;
  flex-direction: column;

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e4e7ed;

    h3 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }

    .panel-description {
      margin: 0;
      color: #606266;
      font-size: 12px;
    }
  }

  .panel-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;

    h4 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }
  }

  .upload-section {
    margin-bottom: 24px;

    .upload-area {
      margin-bottom: 12px;

      .shp-upload {
        width: 100%;

        :deep(.el-upload-dragger) {
          width: 100%;
          height: 120px;
          border: 2px dashed #d9d9d9;
          border-radius: 6px;
          cursor: pointer;
          position: relative;
          overflow: hidden;
          transition: border-color 0.2s;

          &:hover {
            border-color: #409eff;
          }
        }

        :deep(.el-icon--upload) {
          font-size: 28px;
          color: #c0c4cc;
          margin: 20px 0 16px;
        }

        :deep(.el-upload__text) {
          color: #606266;
          font-size: 14px;
          text-align: center;

          em {
            color: #409eff;
            font-style: normal;
          }
        }

        :deep(.el-upload__tip) {
          font-size: 12px;
          color: #909399;
          margin-top: 8px;
        }
      }
    }

    .upload-actions {
      display: flex;
      gap: 8px;

      .el-button {
        flex: 1;
      }
    }
  }

  .loaded-layers-section {
    margin-bottom: 24px;

    .layer-list {
      .layer-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        margin-bottom: 8px;
        transition: all 0.2s;

        &:hover {
          border-color: #c6e2ff;
          background-color: #f5f7fa;
        }

        &.active {
          border-color: #409eff;
          background-color: #ecf5ff;
        }

        .layer-info {
          flex: 1;

          .layer-name {
            display: block;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }

          .feature-count {
            font-size: 12px;
            color: #909399;
          }
        }

        .layer-actions {
          display: flex;
          gap: 8px;

          .el-button {
            &.active {
              background-color: #f56c6c;
              border-color: #f56c6c;
              color: white;
            }
          }
        }
      }
    }
  }

  .query-result-section {
    .attribute-table {
      max-height: 300px;
      overflow: auto;
      border: 1px solid #e4e7ed;
      border-radius: 4px;

      :deep(.el-table) {
        font-size: 12px;

        .el-table__header {
          background-color: #f5f7fa;
        }

        .el-table__body {
          .el-table__row {
            &:hover {
              background-color: #f5f7fa;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .shp-file-manager {
    .upload-actions {
      flex-direction: column;

      .el-button {
        width: 100%;
      }
    }

    .layer-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .layer-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }
}
</style>
