<!--
 * @Author: AI Assistant 
 * @Date: 2025-07-15 
 * @Description: 地图标注面板组件
 *  
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved. 
-->
<template>
  <div class="map-annotations-panel">
    <div class="panel-title">
      <span>地图标注</span>
    </div>
    <div class="panel-content">
      <div class="annotation-list">
        <el-checkbox v-model="showLabels">显示地图标注</el-checkbox>
        <div class="annotation-item" v-for="(item, index) in annotationItems" :key="index">
          <div class="item-header">
            <el-checkbox v-model="item.visible" @change="handleVisibilityChange(item)">{{ item.name }}</el-checkbox>
          </div>
          <div class="item-content" v-if="item.visible">
            <el-slider v-model="item.opacity" :min="0" :max="100" :step="10" 
              @change="handleOpacityChange(item)" show-tooltip></el-slider>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

// 标注列表
const annotationItems = reactive([
  { id: '1', name: '地图标注', visible: true, opacity: 100, type: 'label' },
  { id: '2', name: '兴趣点', visible: true, opacity: 100, type: 'poi' },
  { id: '3', name: '道路名称', visible: true, opacity: 100, type: 'road' },
]);

// 是否显示所有标注
const showLabels = ref(true);

// 处理图层可见性变化
const handleVisibilityChange = (item: any) => {
  console.log(`图层 ${item.name} 可见性已更改为: ${item.visible}`);
  // 这里添加更新地图标注可见性的逻辑
};

// 处理图层透明度变化
const handleOpacityChange = (item: any) => {
  console.log(`图层 ${item.name} 透明度已更改为: ${item.opacity}`);
  // 这里添加更新地图标注透明度的逻辑
};

onMounted(() => {
  console.log('地图标注面板已加载');
});
</script>

<style lang="scss" scoped>
.map-annotations-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
  padding: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.annotation-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.annotation-item {
  background-color: rgba(15, 21, 32, 0.6); /* 与图层管理一致的背景色 */
  border-radius: 4px;
  padding: 10px 10px 10px 16px; /* 增加左侧内边距，让内容往右移动 */
  transition: all 0.3s ease;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-content {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-checkbox__label) {
  color: #fff;
}

:deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.2);
}

:deep(.el-slider__bar) {
  background-color: #409eff;
}

:deep(.el-slider__button) {
  border: 2px solid #409eff;
}
</style> 