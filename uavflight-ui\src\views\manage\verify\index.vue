<template>
	<div class="layout-padding">
		<splitpanes>
			<pane
				:size="isCollapsed ? 0 : 20"
				:style="{ width: isCollapsed ? '0' : '20%', margin: isCollapsed ? '0' : '0 15px 0 10px', overflow: 'hidden' }"
			>
				<el-card style="height: 100%; overflow: auto">
					<div>
						<div class="query-condition-container">
							<div class="title-container">
								<div class="title-before">&nbsp;</div>
								<span class="title">筛选条件</span>
								<el-button class="collapse-btn" type="text" @click="toggleCollapse">
									<el-icon><ArrowLeft /></el-icon>
								</el-button>
							</div>
							<!-- <div class="form-item">
								<label>部门数据权限过滤</label>
								<el-tree-select
									:data="deptData"
									:props="{ value: 'id', label: 'name', children: 'children' }"
									check-strictly
									clearable
									style="width: 100%"
									placeholder="请选择分配部门"
									v-model="department"
								/>
							</div> -->
							<div class="form-item">
								<label>行政区数据权限过滤</label>
								<el-tree-select
									:data="auditRegionData"
									:props="{ value: 'id', label: 'name', children: 'children' }"
									check-strictly
									clearable
									style="width: 100%"
									placeholder="请选择行政区"
									v-model="state.queryForm.cityCode"
								/>
							</div>
							<div class="form-item">
								<label>审核状态</label>
								<el-select v-model="queryStatus" placeholder="请选择审核状态" @change="queryStatusSelect">
									<el-option v-for="item in queryStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</div>
							<div class="form-item">
								<label>时间</label>
								<el-date-picker
									v-model="timeRange"
									type="daterange"
									range-separator="-"
									start-placeholder="开始"
									end-placeholder="结束"
									style="width: 100%"
									:clearable="false"
								></el-date-picker>
							</div>
							<div class="form-item">
								<label>业务场景</label>
								<el-select
									v-model="state.queryForm.businessType"
									@change="changeBusinessType"
									placeholder="请选择业务类型"
									style="width: 100%"
									clearable
								>
									<el-option
										v-for="item in businessTypeData"
										:key="item.businessTypeId"
										:label="item.businessTypeName"
										:value="item.businessTypeId"
									/>
								</el-select>
							</div>
							<div class="form-item">
								<label>事件</label>
								<el-select v-model="state.queryForm.businessEvent" placeholder="请选择事件类型">
									<el-option v-for="item in businessEventData" :key="item.type" :label="item.name" :value="item.type" />
								</el-select>
							</div>
							<!-- <div class="form-item">
								<label>检测类型</label>
								<el-select v-model="detectionType" placeholder="请选择">
									<el-option label="类型1" value="type1"></el-option>
									<el-option label="类型2" value="type2"></el-option>
								</el-select>
							</div> -->
							<div class="button-container">
								<el-button type="default" @click="reset">重置</el-button>
								<el-button type="success" @click="query" style="background-color: #2a7729">查询</el-button>
							</div>
						</div>
					</div>
				</el-card>
			</pane>
			<pane class="ml8" :style="{ width: isCollapsed ? '100%' : '80%', marginLeft: isCollapsed ? '0' : '8px' }">
				<div class="layout-padding-auto layout-padding-view">
					<div class="title-container" style="margin-top: 10px; margin-left: 10px">
						<div class="title-before">&nbsp;</div>
						<span class="title">事件审核</span>
						<el-button v-if="isCollapsed" class="show-filter-btn" @click="toggleCollapse"> 显示筛选条件 </el-button>
					</div>
					<!-- <el-row>
						<div class="mb8" style="width: 100%">
							<div class="status-container">
								<el-card
									class="status-card"
									:class="{ 'pending-card': true, 'selected-card-1': selectedStatus === 'pending' }"
									@click="handleClick('pending')"
								>
									<div class="status-text">待核实</div>
									<div class="status-number">{{ pendingCount }}</div>
								</el-card>
								<el-card
									class="status-card margin-left-div"
									:class="{ 'confirmed-card': true, 'selected-card-2': selectedStatus === 'confirmed' }"
									@click="handleClick('confirmed')"
								>
									<div class="status-text">属实</div>
									<div class="status-number">{{ confirmedCount }}</div>
								</el-card>
								<el-card
									class="status-card margin-left-div"
									:class="{ 'noConfirme-card': true, 'selected-card-3': selectedStatus === 'noConfirme' }"
									@click="handleClick('noConfirme')"
								>
									<div class="status-text">不属实</div>
									<div class="status-number">{{ noConfirmeCount }}</div>
								</el-card>
							</div>
						</div>
					</el-row> -->
					<el-table
						stripe
						v-loading="state.loading"
						:data="state.dataList"
						@selection-change="handleSelectionChange"
						:cell-style="tableStyle.cellStyle"
						:header-cell-style="tableStyle.headerCellStyle"
					>
						<el-table-column label="序号" type="index" width="60" fixed="left" />
						<el-table-column label="街道" fixed="left" prop="cityName" show-overflow-tooltip></el-table-column>
						<el-table-column label="业务场景" prop="businessTypeName" show-overflow-tooltip></el-table-column>
						<el-table-column label="事件名称" prop="businessEventName" width="120"></el-table-column>
						<!-- <el-table-column prop="detectionType" label="检测类型" width="100"></el-table-column> -->
						<el-table-column align="center" label="图片" width="100">
							<template #default="{ row }">
								<el-image
									style="border-radius: 6px; height: 60px"
									:preview-teleported="true"
									:src="getImage(row.picturePath)"
									:preview-src-list="getImageList(row.picturePath)"
									fit="cover"
								/>
							</template>
						</el-table-column>
						<el-table-column label="发生时间" prop="time" show-overflow-tooltip width="200"></el-table-column>
						<el-table-column prop="status" label="审核状态" width="100">
							<template #default="{ row }">
								<span style="color: red" v-if="row.status === '0'"> 未审核 </span>
								<span style="color: green" v-else>已审核</span>
							</template>
						</el-table-column>
						<el-table-column prop="updateTime" label="审核时间">
							<template #default="{ row }">
								<span v-if="row.status === '1'"> {{ row.updateTime }} </span>
								<span v-else>-</span>
							</template>
						</el-table-column>
						<el-table-column label="操作" width="160" fixed="right">
							<template #default="scope">
								<el-button icon="check" v-if="scope.row.status === '0'" text type="success" @click="openAuditDialog(scope.row)"> 审核 </el-button>
								<el-button icon="view" text type="info" @click="openViewDialog(scope.row)"> 查看 </el-button>
							</template>
						</el-table-column>
					</el-table>
					<pagination v-bind="state.pagination" @current-change="currentChangeHandle" @size-change="sizeChangeHandle"> </pagination>
				</div>
			</pane>
		</splitpanes>
		<!-- 引入审核详情组件 -->
		<VerifyForm ref="verifyDialogRef" :close-dialog="closeDialog" @refresh="getDataList()" />
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { pageList, getAuditCount } from '/@/api/manage/verify';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useI18n } from 'vue-i18n';
import { ref } from 'vue';
import VerifyForm from './form.vue';
import { auditRegionTree } from '/@/api/manage/auditDepartment';
import { getBusinessTypeList, getBusinessEventListByType } from '/@/api/manage/businessType';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';

// 定义事件数据类型
interface EventData {
	street: string;
	grid: string;
	scene: string;
	eventName: string;
	// detectionType: string;
	imageUrl: string;
	occurTime: string;
	auditStatus: string;
	userId: string;
	username: string;
}

const department = ref('');

const auditRegionData = ref<any[]>([]);
// 初始化行政区数据
const getAuditRegionDataFun = () => {
	// 获取行政区数据
	auditRegionTree().then((res) => {
		auditRegionData.value = res.data;
	});
};

const reset = () => {
	nextTick(() => {
		state.queryForm = {};
		state.queryForm.status = '0';
		timeRange.value = [];
		queryStatus.value = '';
		getDataList();
	});
};

const timeRange = ref([]);
const query = () => {
	if (timeRange.value && timeRange.value.length > 0) {
		state.queryForm.startTime = timeRange.value[0].getTime();
		state.queryForm.endTime = timeRange.value[1].getTime();
	}
	getDataList();
};

const queryStatus = ref('');
const queryStatusOptions = ref([
	{
		value: 'pending',
		label: '待核实',
	},
	{
		value: 'confirmed',
		label: '属实',
	},
	{
		value: 'noConfirme',
		label: '不属实',
	},
]);

const queryStatusSelect = (val: any) => {
	handleClick(val);
};

const { t } = useI18n();

const verifyDialogRef = ref<InstanceType<typeof VerifyForm> | null>(null);
const selectedStatus = ref('pending');
const pendingCount = ref();
const confirmedCount = ref();
const noConfirmeCount = ref();

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		status: '0',
		auditResult: '',
		cityCode: '',
		businessType: '',
		businessEvent: '',
		startTime: 0,
		endTime: 0,
	},
	pageList: pageList,
	dataList: [],
	loading: false,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

const getImage = (url: string): string => {
	return url;
};

const getImageList = (url: string) => {
	let list = [];
	list.push(url);
	return list;
};

const handleClick = (status: string) => {
	// 更新选中状态
	selectedStatus.value = status;
	switch (status) {
		case 'pending':
			state.queryForm.auditResult = '';
			state.queryForm.status = '0';
			// getDataList();
			break;
		case 'confirmed':
			state.queryForm.status = '1';
			state.queryForm.auditResult = '1';
			// getDataList();
			break;
		case 'noConfirme':
			state.queryForm.status = '1';
			state.queryForm.auditResult = '2';
			// getDataList();
			break;
	}
}; // 补上缺失的右花括号

const closeDialog = () => {
	if (verifyDialogRef.value) {
		verifyDialogRef.value.closeDialog();
	}
};

const businessTypeData = ref<any[]>([]);
// 初始化业务场景数据
const getBusinessTypeDataFun = () => {
	getBusinessTypeList().then((res) => {
		businessTypeData.value = res.data;
	});
};

// 业务事件
const businessEventData = ref<any[]>([]);
const changeBusinessType = async (val) => {
	state.queryForm.businessEvent = '';
	const res = await getBusinessEventListByType({ businessType: val });
	businessEventData.value = res.data;
};

// 根据实际数据类型定义 selectObjs
const selectObjs = ref<EventData[]>([]);

const handleSelectionChange = (selection: EventData[]) => {
	selectObjs.value = selection;
};

const getAuditCountFun = async () => {
	const res = await getAuditCount({});
	pendingCount.value = res.data.pendingCount;
	confirmedCount.value = res.data.confirmedCount;
	noConfirmeCount.value = res.data.noConfirmedCount;
};

const loadData = async () => {
	try {
		// handleClick(selectedStatus.value);
		getAuditRegionDataFun();
		getAuditCountFun();
		getBusinessTypeDataFun();
		state.loading = false;
	} catch (error) {
		console.error('数据加载失败:', error);
	}
};

const openAuditDialog = (rowData: EventData) => {
	verifyDialogRef.value.showDialog(rowData, 2);
};

const openViewDialog = (rowData: EventData) => {
	verifyDialogRef.value.showDialog(rowData, 1);
};

onMounted(() => {
	loadData();
});

// 添加折叠状态控制
const isCollapsed = ref(false);
const toggleCollapse = () => {
	isCollapsed.value = !isCollapsed.value;
};
</script>

<style scoped>
.query-condition-container {
	background-color: #ffffff;
	padding: 0px;
	/* border-radius: 5px;
	box-shadow: 0 0 5px rgba(0, 0, 0, 0.1); */
}
.title-container {
	display: flex;
	align-items: center;
	margin-bottom: 20px;
}
.title-before {
	height: 20px;
	width: 8px;
	background: green;
	margin-right: 8px;
}
.title {
	font-size: 18px;
	font-weight: bold;
	margin-left: 0px;
}
.form-item {
	margin-bottom: 20px;
}
.form-item label {
	display: block;
	margin-bottom: 5px;
}
.el-date-picker {
	align-items: center;
	display: inline-flex;
	padding: 0 10px;
	vertical-align: middle;
	width: 100%;
}

.button-container {
	display: flex;
	justify-content: flex-end;
	margin-top: 20px;
}
.event-review-card {
	width: 100%;
	padding: 10px;
}
.event-review-title {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
	font-size: 18px;
	font-weight: 400;
	color: green;
}
.status-container {
	display: flex;
	justify-content: space-around;
}
.status-card {
	width: 33.3%;
	cursor: pointer;
	margin-bottom: 10px;
	border-radius: 0px;
	/* margin-left: 10px; */
	/* text-align: center; */
}
.margin-left-div {
	margin-left: 10px;
}
.pending-card {
	color: red;
	/* border: 1px solid red; */
}

.confirmed-card {
	color: green;
	/* border: 1px solid green; */
}
.noConfirme-card {
	color: grey;
	/* border: 1px solid #ccc; */
}
.status-text {
	color: inherit;
	font-size: 16px;
	line-height: 1;
}
.status-number {
	font-size: 20px;
	margin-top: 5px;
	font-weight: 600;
}
.check-icon {
	color: red;
	float: right;
	margin-top: -20px;
	margin-right: 10px;
}
.selected-card-1 {
	background-color: rgb(255, 0, 0, 0.03); /* 示例背景颜色，可根据需求调整 */
	border: 1px solid red;
}
.selected-card-2 {
	background-color: rgba(14, 238, 70, 0.03); /* 示例背景颜色，可根据需求调整 */
	border: 1px solid green;
}
.selected-card-3 {
	background-color: rgba(241, 234, 234, 0.05); /* 示例背景颜色，可根据需求调整 */
	border: 1px solid grey;
}

.el-image-viewer__wrapper {
	position: absolute;

	.el-image-viewer__canvas {
		position: absolute;

		height: 70%;

		top: 10%;
	}

	.el-image-viewer__prev {
		left: 30%;
	}

	.el-image-viewer__next {
		right: 30%;
	}

	.el-image-viewer__close {
		right: 25%;
	}
}

.header-container {
	display: flex;
	align-items: center;
	gap: 10px;
	position: relative;
}

.collapse-btn {
	margin-left: auto;
	padding: 0;
	font-size: 16px;
}

.show-filter-btn {
	background-color: white;
	color: #2a7729;
	border: 1px solid #2a7729;
	border-radius: 15px;
	padding: 4px 12px;
	font-size: 12px;
	height: 24px;
	margin-left: 8px;
	display: inline-flex;
	align-items: center;
}

.show-filter-btn:hover {
	background-color: #f0f9f0;
	color: #43d341;
	border-color: #43d341;
}

/* 添加过渡效果 */
.splitpanes__pane {
	transition: all 0.3s ease !important;
}

.ml8 {
	transition: margin-left 0.3s ease;
}
</style>
