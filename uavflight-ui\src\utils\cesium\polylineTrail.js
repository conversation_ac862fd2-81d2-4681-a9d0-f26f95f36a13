/**
 * @Description: 自定义流线材质效果
 */
import * as Cesium from 'cesium';

/**
 * 创建流线材质，可应用于Cesium Polyline
 */
export default class PolylineTrailMaterialProperty {
  constructor(options) {
    options = Cesium.defaultValue(options, Cesium.defaultValue.EMPTY_OBJECT);
    this._definitionChanged = new Cesium.Event();
    this._color = undefined;
    this._colorSubscription = undefined;
    this._time = new Date().getTime();
    
    this.color = options.color || Cesium.Color.CYAN;
    this.trailLength = options.trailLength || 0.5;
    this.period = options.period || 2.0;
  }

  get isConstant() {
    return false;
  }

  get definitionChanged() {
    return this._definitionChanged;
  }

  getType() {
    return 'PolylineTrail';
  }

  getValue(time, result) {
    if (!Cesium.defined(result)) {
      result = {};
    }

    result.color = Cesium.Property.getValueOrClonedDefault(
      this._color,
      time,
      Cesium.Color.WHITE,
      result.color
    );
    
    result.trailLength = this.trailLength;
    result.period = this.period;
    result.time = (((new Date().getTime() - this._time) % (this.period * 1000)) / 1000.0);

    return result;
  }

  equals(other) {
    return (
      this === other ||
      (other instanceof PolylineTrailMaterialProperty &&
        Cesium.Property.equals(this._color, other._color) &&
        this.trailLength === other.trailLength &&
        this.period === other.period)
    );
  }
}

// 定义属性setter和getter
Object.defineProperties(PolylineTrailMaterialProperty.prototype, {
  color: Cesium.createPropertyDescriptor('color')
});

// 注册材质
Cesium.Material.PolylineTrailType = 'PolylineTrail';
Cesium.Material.PolylineTrailSource = `
czm_material czm_getMaterial(czm_materialInput materialInput)
{
    czm_material material = czm_getDefaultMaterial(materialInput);
    vec2 st = materialInput.st;
    
    float time = time;
    float period = period;
    float trailLength = trailLength;
    
    float speed = 1.0 / period;
    float currentPos = fract(time * speed);
    float trailPos = fract(time * speed - trailLength);
    
    vec4 color = color;
    
    float trail = smoothstep(trailPos, currentPos, st.s) * step(st.s, currentPos);
    material.diffuse = color.rgb;
    material.alpha = trail * color.a;
    
    return material;
}
`;

// 注册材质对应的uniform
Cesium.Material._materialCache.addMaterial(Cesium.Material.PolylineTrailType, {
  fabric: {
    type: Cesium.Material.PolylineTrailType,
    uniforms: {
      color: new Cesium.Color(1.0, 0.0, 0.0, 1.0),
      time: 0,
      trailLength: 0.5,
      period: 2.0
    },
    source: Cesium.Material.PolylineTrailSource
  },
  translucent: function (material) {
    return true;
  }
}); 