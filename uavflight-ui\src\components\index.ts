/*
 * @Author: 吴博文 <EMAIL>
 * @Date: 2025-03-28 09:32:45
 * @LastEditors: 吴博文 <EMAIL>
 * @LastEditTime: 2025-07-14 10:30:40
 * @FilePath: \uavflight-ui\src\components\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import ElementIcons from '/@/components/SvgIcon/svgicon';
import Pagination from '/@/components/Pagination/index.vue';
import RightToolbar from '/@/components/RightToolbar/index.vue';
import DictTag from '/@/components/DictTag/index.vue';
import UploadExcel from '/@/components/Upload/Excel.vue';
import UploadFile from '/@/components/Upload/index.vue';
import UploadImg from '/@/components/Upload/Image.vue';
import DelWrap from '/@/components/DelWrap/index.vue';
import Editor from '/@/components/Editor/index.vue';
import Tip from '/@/components/Tip/index.vue';
import olLayerQuery from './olLayerQuery/index.vue';
import OlHDLayerManager from './olHDLayerManager/index.vue';
import OlLayerManager from './olLayerManager/index.vue';
import GisTools from './GisTools/index.vue';
import LayerQueryDialog from './olHDLayerManager/LayerQueryDialog.vue';
import FeatureAttributes from './FeatureAttributes/index.vue';

export { 
  DelWrap, 
  ElementIcons, 
  Pagination, 
  RightToolbar, 
  DictTag, 
  UploadExcel, 
  UploadFile, 
  UploadImg, 
  Editor, 
  Tip, 
  olLayerQuery, 
  OlHDLayerManager,
  OlLayerManager,
  GisTools,
  LayerQueryDialog,
  FeatureAttributes
};

