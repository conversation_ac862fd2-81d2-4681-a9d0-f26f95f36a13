{"name": "shapefile", "version": "0.6.6", "description": "An implementation of the shapefile (.shp) spatial data format.", "keywords": ["g<PERSON><PERSON><PERSON>", "shapefile"], "homepage": "https://github.com/mbostock/shapefile", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "unpkg": "dist/shapefile.js", "jsdelivr": "dist/shapefile.js", "main": "dist/shapefile.node.js", "module": "index.js", "repository": {"type": "git", "url": "https://github.com/mbostock/shapefile.git"}, "bin": {"dbf2json": "bin/dbf2json", "shp2json": "bin/shp2json"}, "scripts": {"pretest": "rm -rf dist && mkdir dist && cp index.node.js dist/shapefile.node.js && rollup -g path-source,array-source,stream-source,slice-source -f cjs -- index.js | tail -n +2 >> dist/shapefile.node.js", "test": "tape 'test/**/*-test.js'", "prepublishOnly": "npm run test && rollup -c --banner \"$(preamble)\" && uglifyjs -b beautify=false,preamble=\"'$(preamble)'\" -o dist/shapefile.min.js -cm -- dist/shapefile.js", "postpublish": "git push && git push --tags && zip -j dist/shapefile.zip -- LICENSE.txt README.md dist/shapefile.js dist/shapefile.min.js"}, "dependencies": {"array-source": "0.0", "commander": "2", "path-source": "0.1", "slice-source": "0.4", "stream-source": "0.3", "text-encoding": "^0.6.4"}, "devDependencies": {"package-preamble": "0.1", "rollup": "0.49", "rollup-plugin-node-resolve": "3", "tape": "4", "uglify-js": "3"}}