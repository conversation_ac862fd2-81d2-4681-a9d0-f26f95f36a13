{"name": "array-source", "version": "0.0.4", "description": "Read arrays as standard WhatWG streams.", "keywords": ["binary", "stream", "reader"], "homepage": "https://github.com/mbostock/array-source", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "main": "dist/array-source.js", "module": "index.js", "repository": {"type": "git", "url": "http://github.com/mbostock/array-source.git"}, "scripts": {"prepublishOnly": "rm -rf dist && mkdir dist && rollup -c --banner \"$(preamble)\" && uglifyjs -b beautify=false,preamble=\"'$(preamble)'\" -o dist/array-source.min.js -c -m -- dist/array-source.js", "postpublish": "git push && git push --tags"}, "devDependencies": {"package-preamble": "0.1", "rollup": "0.49", "uglify-js": "3"}}