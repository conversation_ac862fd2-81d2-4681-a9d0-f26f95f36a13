<template>
	<div class="transparent-select" v-click-outside="closeDropdown">
		<div class="select-header" :class="{ active: isOpen }" @click="toggleDropdown">
			{{ selectedOption || placeholder }}
		</div>
		<transition name="fade">
			<div v-show="isOpen" class="options-container">
				<div v-for="option in options" :key="option.value" class="option-item" @click="selectOption(option)">
					{{ option.label }}
				</div>
			</div>
		</transition>
	</div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

interface SelectOption {
	value: string | number;
	label: string;
}

export default defineComponent({
	name: 'TransparentSelect',
	props: {
		options: {
			type: Array as PropType<SelectOption[]>,
			required: true,
		},
		placeholder: {
			type: String,
			default: '请选择',
		},
		modelValue: {
			type: [String, Number],
			default: '',
		},
	},
	emits: ['update:modelValue'],
	data() {
		return {
			isOpen: false,
			selectedOption: this.modelValue,
		};
	},
	watch: {
		modelValue(newVal) {
			this.selectedOption = newVal;
		},
	},
	methods: {
		toggleDropdown() {
			this.isOpen = !this.isOpen;
		},
		closeDropdown() {
			this.isOpen = false;
		},
		selectOption(option: SelectOption) {
			this.selectedOption = option.label;
			this.$emit('update:modelValue', option.value);
			this.closeDropdown();
		},
	},
	directives: {
		'click-outside': {
			beforeMount(el: HTMLElement, binding) {
				el.clickOutsideEvent = (event: MouseEvent) => {
					if (!(el === event.target || el.contains(event.target as Node))) {
						binding.value();
					}
				};
				document.addEventListener('click', el.clickOutsideEvent);
			},
			unmounted(el: HTMLElement) {
				document.removeEventListener('click', el.clickOutsideEvent);
			},
		},
	},
});
</script>

<style scoped>
.transparent-select {
	position: relative;
	width: 60px;
	/* margin: 10px; */
}

.select-header {
	background: transparent;
	border: none;
	border-bottom: none;
	padding: 0;
	color: #c8dede;
	cursor: pointer;
	position: relative;
	width: 100%;
	text-align: left;
	transition: border-color 0.3s;
}

.select-header::after {
	content: '^';
	position: absolute;
	right: 8px;
	top: 43%;
	transform: translateY(-20%);
	color: #c8dede;
	font-size: 16px;
	transition: transform 0.1s;
	transform: translateY(-50%) rotate(180deg);
}

.select-header.active::after {
	transform: translateY(-50%) rotate(360deg);
}

.options-container {
	position: absolute;
	width: 100%;
	background: none;
	backdrop-filter: blur(5px);
	border: none;
	margin-top: 5px;
	z-index: 1000;
	box-shadow: none;
}

.option-item {
	padding: 10px;
	color: #c8dede;
	cursor: pointer;
	transition: background 0.2s;
}

.option-item:hover {
	background: rgba(0, 0, 0, 0.05);
}

.fade-enter-active,
.fade-leave-active {
	transition:
		opacity 0.3s,
		transform 0.3s;
}

.fade-enter-from,
.fade-leave-to {
	opacity: 0;
	transform: translateY(-10px);
}
</style>
