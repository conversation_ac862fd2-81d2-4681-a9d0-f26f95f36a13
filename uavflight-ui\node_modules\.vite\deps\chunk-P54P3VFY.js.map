{"version": 3, "sources": ["../../ol/format/Feature.js"], "sourcesContent": ["/**\n * @module ol/format/Feature\n */\nimport {abstract} from '../util.js';\nimport {\n  equivalent as equivalentProjection,\n  get as getProjection,\n  transformExtent,\n} from '../proj.js';\n\n/**\n * @typedef {Object} ReadOptions\n * @property {import(\"../proj.js\").ProjectionLike} [dataProjection] Projection of the data we are reading.\n * If not provided, the projection will be derived from the data (where possible) or\n * the `dataProjection` of the format is assigned (where set). If the projection\n * can not be derived from the data and if no `dataProjection` is set for a format,\n * the features will not be reprojected.\n * @property {import(\"../extent.js\").Extent} [extent] Tile extent in map units of the tile being read.\n * This is only required when reading data with tile pixels as geometry units. When configured,\n * a `dataProjection` with `TILE_PIXELS` as `units` and the tile's pixel extent as `extent` needs to be\n * provided.\n * @property {import(\"../proj.js\").ProjectionLike} [featureProjection] Projection of the feature geometries\n * created by the format reader. If not provided, features will be returned in the\n * `dataProjection`.\n */\n\n/**\n * @typedef {Object} WriteOptions\n * @property {import(\"../proj.js\").ProjectionLike} [dataProjection] Projection of the data we are writing.\n * If not provided, the `dataProjection` of the format is assigned (where set).\n * If no `dataProjection` is set for a format, the features will be returned\n * in the `featureProjection`.\n * @property {import(\"../proj.js\").ProjectionLike} [featureProjection] Projection of the feature geometries\n * that will be serialized by the format writer. If not provided, geometries are assumed\n * to be in the `dataProjection` if that is set; in other words, they are not transformed.\n * @property {boolean} [rightHanded] When writing geometries, follow the right-hand\n * rule for linear ring orientation.  This means that polygons will have counter-clockwise\n * exterior rings and clockwise interior rings.  By default, coordinates are serialized\n * as they are provided at construction.  If `true`, the right-hand rule will\n * be applied.  If `false`, the left-hand rule will be applied (clockwise for\n * exterior and counter-clockwise for interior rings).  Note that not all\n * formats support this.  The GeoJSON format does use this property when writing\n * geometries.\n * @property {number} [decimals] Maximum number of decimal places for coordinates.\n * Coordinates are stored internally as floats, but floating-point arithmetic can create\n * coordinates with a large number of decimal places, not generally wanted on output.\n * Set a number here to round coordinates. Can also be used to ensure that\n * coordinates read in can be written back out with the same number of decimals.\n * Default is no rounding.\n */\n\n/**\n * @typedef {'arraybuffer' | 'json' | 'text' | 'xml'} Type\n */\n\n/**\n * @classdesc\n * Abstract base class; normally only used for creating subclasses and not\n * instantiated in apps.\n * Base class for feature formats.\n * {@link module:ol/format/Feature~FeatureFormat} subclasses provide the ability to decode and encode\n * {@link module:ol/Feature~Feature} objects from a variety of commonly used geospatial\n * file formats.  See the documentation for each format for more details.\n *\n * @abstract\n * @api\n */\nclass FeatureFormat {\n  constructor() {\n    /**\n     * @protected\n     * @type {import(\"../proj/Projection.js\").default|undefined}\n     */\n    this.dataProjection = undefined;\n\n    /**\n     * @protected\n     * @type {import(\"../proj/Projection.js\").default|undefined}\n     */\n    this.defaultFeatureProjection = undefined;\n\n    /**\n     * A list media types supported by the format in descending order of preference.\n     * @type {Array<string>}\n     */\n    this.supportedMediaTypes = null;\n  }\n\n  /**\n   * Adds the data projection to the read options.\n   * @param {Document|Element|Object|string} source Source.\n   * @param {ReadOptions} [options] Options.\n   * @return {ReadOptions|undefined} Options.\n   * @protected\n   */\n  getReadOptions(source, options) {\n    if (options) {\n      let dataProjection = options.dataProjection\n        ? getProjection(options.dataProjection)\n        : this.readProjection(source);\n      if (\n        options.extent &&\n        dataProjection &&\n        dataProjection.getUnits() === 'tile-pixels'\n      ) {\n        dataProjection = getProjection(dataProjection);\n        dataProjection.setWorldExtent(options.extent);\n      }\n      options = {\n        dataProjection: dataProjection,\n        featureProjection: options.featureProjection,\n      };\n    }\n    return this.adaptOptions(options);\n  }\n\n  /**\n   * Sets the `dataProjection` on the options, if no `dataProjection`\n   * is set.\n   * @param {WriteOptions|ReadOptions|undefined} options\n   *     Options.\n   * @protected\n   * @return {WriteOptions|ReadOptions|undefined}\n   *     Updated options.\n   */\n  adaptOptions(options) {\n    return Object.assign(\n      {\n        dataProjection: this.dataProjection,\n        featureProjection: this.defaultFeatureProjection,\n      },\n      options\n    );\n  }\n\n  /**\n   * @abstract\n   * @return {Type} The format type.\n   */\n  getType() {\n    return abstract();\n  }\n\n  /**\n   * Read a single feature from a source.\n   *\n   * @abstract\n   * @param {Document|Element|Object|string} source Source.\n   * @param {ReadOptions} [options] Read options.\n   * @return {import(\"../Feature.js\").FeatureLike} Feature.\n   */\n  readFeature(source, options) {\n    return abstract();\n  }\n\n  /**\n   * Read all features from a source.\n   *\n   * @abstract\n   * @param {Document|Element|ArrayBuffer|Object|string} source Source.\n   * @param {ReadOptions} [options] Read options.\n   * @return {Array<import(\"../Feature.js\").FeatureLike>} Features.\n   */\n  readFeatures(source, options) {\n    return abstract();\n  }\n\n  /**\n   * Read a single geometry from a source.\n   *\n   * @abstract\n   * @param {Document|Element|Object|string} source Source.\n   * @param {ReadOptions} [options] Read options.\n   * @return {import(\"../geom/Geometry.js\").default} Geometry.\n   */\n  readGeometry(source, options) {\n    return abstract();\n  }\n\n  /**\n   * Read the projection from a source.\n   *\n   * @abstract\n   * @param {Document|Element|Object|string} source Source.\n   * @return {import(\"../proj/Projection.js\").default|undefined} Projection.\n   */\n  readProjection(source) {\n    return abstract();\n  }\n\n  /**\n   * Encode a feature in this format.\n   *\n   * @abstract\n   * @param {import(\"../Feature.js\").default} feature Feature.\n   * @param {WriteOptions} [options] Write options.\n   * @return {string|ArrayBuffer} Result.\n   */\n  writeFeature(feature, options) {\n    return abstract();\n  }\n\n  /**\n   * Encode an array of features in this format.\n   *\n   * @abstract\n   * @param {Array<import(\"../Feature.js\").default>} features Features.\n   * @param {WriteOptions} [options] Write options.\n   * @return {string|ArrayBuffer} Result.\n   */\n  writeFeatures(features, options) {\n    return abstract();\n  }\n\n  /**\n   * Write a single geometry in this format.\n   *\n   * @abstract\n   * @param {import(\"../geom/Geometry.js\").default} geometry Geometry.\n   * @param {WriteOptions} [options] Write options.\n   * @return {string|ArrayBuffer} Result.\n   */\n  writeGeometry(geometry, options) {\n    return abstract();\n  }\n}\n\nexport default FeatureFormat;\n\n/**\n * @param {import(\"../geom/Geometry.js\").default} geometry Geometry.\n * @param {boolean} write Set to true for writing, false for reading.\n * @param {WriteOptions|ReadOptions} [options] Options.\n * @return {import(\"../geom/Geometry.js\").default} Transformed geometry.\n */\nexport function transformGeometryWithOptions(geometry, write, options) {\n  const featureProjection = options\n    ? getProjection(options.featureProjection)\n    : null;\n  const dataProjection = options ? getProjection(options.dataProjection) : null;\n\n  let transformed;\n  if (\n    featureProjection &&\n    dataProjection &&\n    !equivalentProjection(featureProjection, dataProjection)\n  ) {\n    transformed = (write ? geometry.clone() : geometry).transform(\n      write ? featureProjection : dataProjection,\n      write ? dataProjection : featureProjection\n    );\n  } else {\n    transformed = geometry;\n  }\n  if (\n    write &&\n    options &&\n    /** @type {WriteOptions} */ (options).decimals !== undefined\n  ) {\n    const power = Math.pow(10, /** @type {WriteOptions} */ (options).decimals);\n    // if decimals option on write, round each coordinate appropriately\n    /**\n     * @param {Array<number>} coordinates Coordinates.\n     * @return {Array<number>} Transformed coordinates.\n     */\n    const transform = function (coordinates) {\n      for (let i = 0, ii = coordinates.length; i < ii; ++i) {\n        coordinates[i] = Math.round(coordinates[i] * power) / power;\n      }\n      return coordinates;\n    };\n    if (transformed === geometry) {\n      transformed = geometry.clone();\n    }\n    transformed.applyTransform(transform);\n  }\n  return transformed;\n}\n\n/**\n * @param {import(\"../extent.js\").Extent} extent Extent.\n * @param {ReadOptions} [options] Read options.\n * @return {import(\"../extent.js\").Extent} Transformed extent.\n */\nexport function transformExtentWithOptions(extent, options) {\n  const featureProjection = options\n    ? getProjection(options.featureProjection)\n    : null;\n  const dataProjection = options ? getProjection(options.dataProjection) : null;\n\n  if (\n    featureProjection &&\n    dataProjection &&\n    !equivalentProjection(featureProjection, dataProjection)\n  ) {\n    return transformExtent(extent, dataProjection, featureProjection);\n  }\n  return extent;\n}\n"], "mappings": ";;;;;;;;;;AAmEA,IAAM,gBAAN,MAAoB;AAAA,EAClB,cAAc;AAKZ,SAAK,iBAAiB;AAMtB,SAAK,2BAA2B;AAMhC,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,QAAQ,SAAS;AAC9B,QAAI,SAAS;AACX,UAAI,iBAAiB,QAAQ,iBACzB,IAAc,QAAQ,cAAc,IACpC,KAAK,eAAe,MAAM;AAC9B,UACE,QAAQ,UACR,kBACA,eAAe,SAAS,MAAM,eAC9B;AACA,yBAAiB,IAAc,cAAc;AAC7C,uBAAe,eAAe,QAAQ,MAAM;AAAA,MAC9C;AACA,gBAAU;AAAA,QACR;AAAA,QACA,mBAAmB,QAAQ;AAAA,MAC7B;AAAA,IACF;AACA,WAAO,KAAK,aAAa,OAAO;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,aAAa,SAAS;AACpB,WAAO,OAAO;AAAA,MACZ;AAAA,QACE,gBAAgB,KAAK;AAAA,QACrB,mBAAmB,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,QAAQ,SAAS;AAC3B,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAAa,QAAQ,SAAS;AAC5B,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAAa,QAAQ,SAAS;AAC5B,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,QAAQ;AACrB,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAAa,SAAS,SAAS;AAC7B,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,cAAc,UAAU,SAAS;AAC/B,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,cAAc,UAAU,SAAS;AAC/B,WAAO,SAAS;AAAA,EAClB;AACF;AAEA,IAAO,kBAAQ;AAQR,SAAS,6BAA6B,UAAU,OAAO,SAAS;AACrE,QAAM,oBAAoB,UACtB,IAAc,QAAQ,iBAAiB,IACvC;AACJ,QAAM,iBAAiB,UAAU,IAAc,QAAQ,cAAc,IAAI;AAEzE,MAAI;AACJ,MACE,qBACA,kBACA,CAAC,WAAqB,mBAAmB,cAAc,GACvD;AACA,mBAAe,QAAQ,SAAS,MAAM,IAAI,UAAU;AAAA,MAClD,QAAQ,oBAAoB;AAAA,MAC5B,QAAQ,iBAAiB;AAAA,IAC3B;AAAA,EACF,OAAO;AACL,kBAAc;AAAA,EAChB;AACA,MACE,SACA;AAAA,EAC6B,QAAS,aAAa,QACnD;AACA,UAAM,QAAQ,KAAK;AAAA,MAAI;AAAA;AAAA,MAAiC,QAAS;AAAA,IAAQ;AAMzE,UAAM,YAAY,SAAU,aAAa;AACvC,eAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,oBAAY,CAAC,IAAI,KAAK,MAAM,YAAY,CAAC,IAAI,KAAK,IAAI;AAAA,MACxD;AACA,aAAO;AAAA,IACT;AACA,QAAI,gBAAgB,UAAU;AAC5B,oBAAc,SAAS,MAAM;AAAA,IAC/B;AACA,gBAAY,eAAe,SAAS;AAAA,EACtC;AACA,SAAO;AACT;AAOO,SAAS,2BAA2B,QAAQ,SAAS;AAC1D,QAAM,oBAAoB,UACtB,IAAc,QAAQ,iBAAiB,IACvC;AACJ,QAAM,iBAAiB,UAAU,IAAc,QAAQ,cAAc,IAAI;AAEzE,MACE,qBACA,kBACA,CAAC,WAAqB,mBAAmB,cAAc,GACvD;AACA,WAAO,gBAAgB,QAAQ,gBAAgB,iBAAiB;AAAA,EAClE;AACA,SAAO;AACT;", "names": []}