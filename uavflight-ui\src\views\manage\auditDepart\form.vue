<template>
	<el-dialog :title="form.id ? '查看' : '新增'" v-model="visible" :close-on-click-modal="false" draggable>
		<el-form :disabled="form.id != ''" ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="90px" v-loading="loading">
			<el-form-item prop="selectCodes" label="区域">
				<el-cascader
					placeholder="请选择区域"
					style="width: 80%"
					filterable
					clearable
					:emitPath="false"
					v-model="form.selectCodes"
					:options="gxData"
					:props="cascaderProps"
				/>
			</el-form-item>
			<el-form-item label="业务类型" prop="businessTypeId">
				<el-select v-model="form.businessTypeId" placeholder="请选择业务类型" style="width: 60%" clearable>
					<el-option v-for="item in businessTypeData" :key="item.businessTypeId" :label="item.businessTypeName" :value="item.businessTypeId" />
				</el-select>
			</el-form-item>
			<el-form-item label="分配部门" prop="departmentId">
				<el-tree-select
					:data="deptData"
					:props="{ value: 'id', label: 'name', children: 'children' }"
					check-strictly
					clearable
					style="width: 80%"
					placeholder="请选择分配部门"
					v-model="form.departmentId"
				>
				</el-tree-select>
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取 消</el-button>
				<el-button v-if="!form.id" type="primary" @click="onSubmit" :disabled="loading">确 认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="BusinessAuditDepartmentDialog">
import { useDict } from '/@/hooks/dict';
import { useMessage } from '/@/hooks/message';
import { getObj, addObj, putObj, validateExist } from '/@/api/manage/auditDepartment';
import { rule } from '/@/utils/validate';
import gxData from '/@/assets/guangxi-area.json';
import { deptTree } from '/@/api/admin/dept';
import { getBusinessTypeList } from '/@/api/manage/businessType';
const emit = defineEmits(['refresh']);

const cascaderProps = {
	checkStrictly: true,
};

// 寻找从属关系公共方法
const regroupCascaderData = (
	id, //要寻找的唯一值
	data, // 列表总数据
	key = 'label', //列表总数据 的key
	val = 'value', //列表总数据 的value
	list = 'children' //列表总数据 下属关系的key
) => {
	let _allObj = {};
	const _allArr = [];
	const setData = function (data) {
		let Obj;
		for (const item of data) {
			Obj = {
				[key]: item[key],
				[val]: item[val],
			};
			if (item[val] == id) {
				Obj['isOk'] = true; //如果条件符合，接下来就不会再判断
				return Obj;
			} else if (item[list] && item[list].length) {
				Obj[list] = setData(item[list]);
				if (Obj[list] && Obj[list]['isOk']) {
					Obj['isOk'] = true;
					return Obj;
				}
			} else {
				Obj = null;
			}
		}
		return Obj;
	};
	const getObjData = function (data) {
		// 递归向数组中填充数据
		_allArr.push(data[val]);
		if (data[list]) {
			getObjData(data[list]);
		}
	};
	_allObj = setData(data);
	getObjData(_allObj);
	return {
		Obj: _allObj,
		arr: _allArr,
	};
};

const getCityCodeInfo = () => {
	const arr = regroupCascaderData(form.cityCode, gxData);
	console.log(arr['arr']);
	console.log(import.meta.env.VITE_DEFAULT_REGION);
	nextTick(() => {
		form.selectCodes = arr['arr'];
	});
};

const deptData = ref<any[]>([]);
const businessTypeData = ref<any[]>([]);

// 初始化部门数据
const getDeptData = () => {
	// 获取部门数据
	deptTree().then((res) => {
		deptData.value = res.data;
		// 默认选择第一个
		form.departmentId = res.data[0].id;
	});
};

// 初始化业务场景数据
const getBusinessTypeData = () => {
	getBusinessTypeList().then((res) => {
		businessTypeData.value = res.data;
		// 默认选择第一个
		form.businessTypeId = res.data[0].businessTypeId;
	});
};

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
// 定义字典

const defaultRegion = import.meta.env.VITE_DEFAULT_REGION;
// 提交表单数据
const form = reactive({
	id: '',
	departmentId: '',
	cityCode: '',
	businessTypeId: '',
	selectCodes: [],
});

// 定义校验规则
const dataRules = ref({
	departmentId: [{ required: true, message: '部门不能为空', trigger: 'blur' }],
	selectCodes: [{ required: true, message: '区域不能为空', trigger: 'blur' }],
	businessTypeId: [{ required: true, message: '业务类型不能为空', trigger: 'blur' }],
});

// 打开弹窗
const openDialog = (data: any) => {
	visible.value = true;
	form.id = '';
	// 加载使用的数据
	getDeptData();
	getBusinessTypeData();
	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
		form.selectCodes = defaultRegion.split(',');
	});

	// 获取businessAuditDepartment信息
	if (data) {
		Object.assign(form, data);
		getCityCodeInfo();
		// getBusinessAuditDepartmentData(id);
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		form.cityCode = form.selectCodes[form.selectCodes.length - 1];
		loading.value = true;
		form.id ? await putObj(form) : await addObj(form);
		useMessage().success(form.id ? '修改成功' : '添加成功');
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

// 初始化表单数据
// const getBusinessAuditDepartmentData = (id: string) => {
// 	// 获取数据
// 	loading.value = true;
// 	getObj({ id: id })
// 		.then((res: any) => {
// 			Object.assign(form, res.data[0]);
// 			getCityCodeInfo();
// 		})
// 		.finally(() => {
// 			loading.value = false;
// 		});
// };

// 暴露变量
defineExpose({
	openDialog,
});
</script>
