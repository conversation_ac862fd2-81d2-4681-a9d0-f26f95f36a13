{"version": 3, "sources": ["../../ol/CollectionEventType.js", "../../ol/Collection.js"], "sourcesContent": ["/**\n * @module ol/CollectionEventType\n */\n\n/**\n * @enum {string}\n */\nexport default {\n  /**\n   * Triggered when an item is added to the collection.\n   * @event module:ol/Collection.CollectionEvent#add\n   * @api\n   */\n  ADD: 'add',\n  /**\n   * Triggered when an item is removed from the collection.\n   * @event module:ol/Collection.CollectionEvent#remove\n   * @api\n   */\n  REMOVE: 'remove',\n};\n", "/**\n * @module ol/Collection\n */\nimport AssertionError from './AssertionError.js';\nimport BaseObject from './Object.js';\nimport CollectionEventType from './CollectionEventType.js';\nimport Event from './events/Event.js';\n\n/**\n * @enum {string}\n * @private\n */\nconst Property = {\n  LENGTH: 'length',\n};\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/Collection~Collection} instances are instances of this\n * type.\n * @template T\n */\nexport class CollectionEvent extends Event {\n  /**\n   * @param {import(\"./CollectionEventType.js\").default} type Type.\n   * @param {T} element Element.\n   * @param {number} index The index of the added or removed element.\n   */\n  constructor(type, element, index) {\n    super(type);\n\n    /**\n     * The element that is added to or removed from the collection.\n     * @type {T}\n     * @api\n     */\n    this.element = element;\n\n    /**\n     * The index of the added or removed element.\n     * @type {number}\n     * @api\n     */\n    this.index = index;\n  }\n}\n\n/***\n * @template T\n * @template Return\n * @typedef {import(\"./Observable\").OnSignature<import(\"./Observable\").EventTypes, import(\"./events/Event.js\").default, Return> &\n *   import(\"./Observable\").OnSignature<import(\"./ObjectEventType\").Types|'change:length', import(\"./Object\").ObjectEvent, Return> &\n *   import(\"./Observable\").OnSignature<'add'|'remove', CollectionEvent<T>, Return> &\n *   import(\"./Observable\").CombinedOnSignature<import(\"./Observable\").EventTypes|import(\"./ObjectEventType\").Types|\n *     'change:length'|'add'|'remove',Return>} CollectionOnSignature\n */\n\n/**\n * @typedef {Object} Options\n * @property {boolean} [unique=false] Disallow the same item from being added to\n * the collection twice.\n */\n\n/**\n * @classdesc\n * An expanded version of standard JS Array, adding convenience methods for\n * manipulation. Add and remove changes to the Collection trigger a Collection\n * event. Note that this does not cover changes to the objects _within_ the\n * Collection; they trigger events on the appropriate object, not on the\n * Collection as a whole.\n *\n * @fires CollectionEvent\n *\n * @template T\n * @api\n */\nclass Collection extends BaseObject {\n  /**\n   * @param {Array<T>} [array] Array.\n   * @param {Options} [options] Collection options.\n   */\n  constructor(array, options) {\n    super();\n\n    /***\n     * @type {CollectionOnSignature<T, import(\"./events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {CollectionOnSignature<T, import(\"./events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {CollectionOnSignature<T, void>}\n     */\n    this.un;\n\n    options = options || {};\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.unique_ = !!options.unique;\n\n    /**\n     * @private\n     * @type {!Array<T>}\n     */\n    this.array_ = array ? array : [];\n\n    if (this.unique_) {\n      for (let i = 0, ii = this.array_.length; i < ii; ++i) {\n        this.assertUnique_(this.array_[i], i);\n      }\n    }\n\n    this.updateLength_();\n  }\n\n  /**\n   * Remove all elements from the collection.\n   * @api\n   */\n  clear() {\n    while (this.getLength() > 0) {\n      this.pop();\n    }\n  }\n\n  /**\n   * Add elements to the collection.  This pushes each item in the provided array\n   * to the end of the collection.\n   * @param {!Array<T>} arr Array.\n   * @return {Collection<T>} This collection.\n   * @api\n   */\n  extend(arr) {\n    for (let i = 0, ii = arr.length; i < ii; ++i) {\n      this.push(arr[i]);\n    }\n    return this;\n  }\n\n  /**\n   * Iterate over each element, calling the provided callback.\n   * @param {function(T, number, Array<T>): *} f The function to call\n   *     for every element. This function takes 3 arguments (the element, the\n   *     index and the array). The return value is ignored.\n   * @api\n   */\n  forEach(f) {\n    const array = this.array_;\n    for (let i = 0, ii = array.length; i < ii; ++i) {\n      f(array[i], i, array);\n    }\n  }\n\n  /**\n   * Get a reference to the underlying Array object. Warning: if the array\n   * is mutated, no events will be dispatched by the collection, and the\n   * collection's \"length\" property won't be in sync with the actual length\n   * of the array.\n   * @return {!Array<T>} Array.\n   * @api\n   */\n  getArray() {\n    return this.array_;\n  }\n\n  /**\n   * Get the element at the provided index.\n   * @param {number} index Index.\n   * @return {T} Element.\n   * @api\n   */\n  item(index) {\n    return this.array_[index];\n  }\n\n  /**\n   * Get the length of this collection.\n   * @return {number} The length of the array.\n   * @observable\n   * @api\n   */\n  getLength() {\n    return this.get(Property.LENGTH);\n  }\n\n  /**\n   * Insert an element at the provided index.\n   * @param {number} index Index.\n   * @param {T} elem Element.\n   * @api\n   */\n  insertAt(index, elem) {\n    if (index < 0 || index > this.getLength()) {\n      throw new Error('Index out of bounds: ' + index);\n    }\n    if (this.unique_) {\n      this.assertUnique_(elem);\n    }\n    this.array_.splice(index, 0, elem);\n    this.updateLength_();\n    this.dispatchEvent(\n      new CollectionEvent(CollectionEventType.ADD, elem, index)\n    );\n  }\n\n  /**\n   * Remove the last element of the collection and return it.\n   * Return `undefined` if the collection is empty.\n   * @return {T|undefined} Element.\n   * @api\n   */\n  pop() {\n    return this.removeAt(this.getLength() - 1);\n  }\n\n  /**\n   * Insert the provided element at the end of the collection.\n   * @param {T} elem Element.\n   * @return {number} New length of the collection.\n   * @api\n   */\n  push(elem) {\n    if (this.unique_) {\n      this.assertUnique_(elem);\n    }\n    const n = this.getLength();\n    this.insertAt(n, elem);\n    return this.getLength();\n  }\n\n  /**\n   * Remove the first occurrence of an element from the collection.\n   * @param {T} elem Element.\n   * @return {T|undefined} The removed element or undefined if none found.\n   * @api\n   */\n  remove(elem) {\n    const arr = this.array_;\n    for (let i = 0, ii = arr.length; i < ii; ++i) {\n      if (arr[i] === elem) {\n        return this.removeAt(i);\n      }\n    }\n    return undefined;\n  }\n\n  /**\n   * Remove the element at the provided index and return it.\n   * Return `undefined` if the collection does not contain this index.\n   * @param {number} index Index.\n   * @return {T|undefined} Value.\n   * @api\n   */\n  removeAt(index) {\n    if (index < 0 || index >= this.getLength()) {\n      return undefined;\n    }\n    const prev = this.array_[index];\n    this.array_.splice(index, 1);\n    this.updateLength_();\n    this.dispatchEvent(\n      /** @type {CollectionEvent<T>} */ (\n        new CollectionEvent(CollectionEventType.REMOVE, prev, index)\n      )\n    );\n    return prev;\n  }\n\n  /**\n   * Set the element at the provided index.\n   * @param {number} index Index.\n   * @param {T} elem Element.\n   * @api\n   */\n  setAt(index, elem) {\n    const n = this.getLength();\n    if (index >= n) {\n      this.insertAt(index, elem);\n      return;\n    }\n    if (index < 0) {\n      throw new Error('Index out of bounds: ' + index);\n    }\n    if (this.unique_) {\n      this.assertUnique_(elem, index);\n    }\n    const prev = this.array_[index];\n    this.array_[index] = elem;\n    this.dispatchEvent(\n      /** @type {CollectionEvent<T>} */ (\n        new CollectionEvent(CollectionEventType.REMOVE, prev, index)\n      )\n    );\n    this.dispatchEvent(\n      /** @type {CollectionEvent<T>} */ (\n        new CollectionEvent(CollectionEventType.ADD, elem, index)\n      )\n    );\n  }\n\n  /**\n   * @private\n   */\n  updateLength_() {\n    this.set(Property.LENGTH, this.array_.length);\n  }\n\n  /**\n   * @private\n   * @param {T} elem Element.\n   * @param {number} [except] Optional index to ignore.\n   */\n  assertUnique_(elem, except) {\n    for (let i = 0, ii = this.array_.length; i < ii; ++i) {\n      if (this.array_[i] === elem && i !== except) {\n        throw new AssertionError(58);\n      }\n    }\n  }\n}\n\nexport default Collection;\n"], "mappings": ";;;;;;;;;;;AAOA,IAAO,8BAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAML,QAAQ;AACV;;;ACRA,IAAM,WAAW;AAAA,EACf,QAAQ;AACV;AAQO,IAAM,kBAAN,cAA8B,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzC,YAAY,MAAM,SAAS,OAAO;AAChC,UAAM,IAAI;AAOV,SAAK,UAAU;AAOf,SAAK,QAAQ;AAAA,EACf;AACF;AA+BA,IAAM,aAAN,cAAyB,eAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,YAAY,OAAO,SAAS;AAC1B,UAAM;AAKN,SAAK;AAKL,SAAK;AAKL,SAAK;AAEL,cAAU,WAAW,CAAC;AAMtB,SAAK,UAAU,CAAC,CAAC,QAAQ;AAMzB,SAAK,SAAS,QAAQ,QAAQ,CAAC;AAE/B,QAAI,KAAK,SAAS;AAChB,eAAS,IAAI,GAAG,KAAK,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,aAAK,cAAc,KAAK,OAAO,CAAC,GAAG,CAAC;AAAA,MACtC;AAAA,IACF;AAEA,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACN,WAAO,KAAK,UAAU,IAAI,GAAG;AAC3B,WAAK,IAAI;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,KAAK;AACV,aAAS,IAAI,GAAG,KAAK,IAAI,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC5C,WAAK,KAAK,IAAI,CAAC,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,GAAG;AACT,UAAM,QAAQ,KAAK;AACnB,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,QAAE,MAAM,CAAC,GAAG,GAAG,KAAK;AAAA,IACtB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,OAAO;AACV,WAAO,KAAK,OAAO,KAAK;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV,WAAO,KAAK,IAAI,SAAS,MAAM;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO,MAAM;AACpB,QAAI,QAAQ,KAAK,QAAQ,KAAK,UAAU,GAAG;AACzC,YAAM,IAAI,MAAM,0BAA0B,KAAK;AAAA,IACjD;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,cAAc,IAAI;AAAA,IACzB;AACA,SAAK,OAAO,OAAO,OAAO,GAAG,IAAI;AACjC,SAAK,cAAc;AACnB,SAAK;AAAA,MACH,IAAI,gBAAgB,4BAAoB,KAAK,MAAM,KAAK;AAAA,IAC1D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM;AACJ,WAAO,KAAK,SAAS,KAAK,UAAU,IAAI,CAAC;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,MAAM;AACT,QAAI,KAAK,SAAS;AAChB,WAAK,cAAc,IAAI;AAAA,IACzB;AACA,UAAM,IAAI,KAAK,UAAU;AACzB,SAAK,SAAS,GAAG,IAAI;AACrB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,MAAM;AACX,UAAM,MAAM,KAAK;AACjB,aAAS,IAAI,GAAG,KAAK,IAAI,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC5C,UAAI,IAAI,CAAC,MAAM,MAAM;AACnB,eAAO,KAAK,SAAS,CAAC;AAAA,MACxB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,OAAO;AACd,QAAI,QAAQ,KAAK,SAAS,KAAK,UAAU,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,UAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,SAAK,OAAO,OAAO,OAAO,CAAC;AAC3B,SAAK,cAAc;AACnB,SAAK;AAAA;AAAA,MAED,IAAI,gBAAgB,4BAAoB,QAAQ,MAAM,KAAK;AAAA,IAE/D;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,OAAO,MAAM;AACjB,UAAM,IAAI,KAAK,UAAU;AACzB,QAAI,SAAS,GAAG;AACd,WAAK,SAAS,OAAO,IAAI;AACzB;AAAA,IACF;AACA,QAAI,QAAQ,GAAG;AACb,YAAM,IAAI,MAAM,0BAA0B,KAAK;AAAA,IACjD;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,cAAc,MAAM,KAAK;AAAA,IAChC;AACA,UAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,SAAK,OAAO,KAAK,IAAI;AACrB,SAAK;AAAA;AAAA,MAED,IAAI,gBAAgB,4BAAoB,QAAQ,MAAM,KAAK;AAAA,IAE/D;AACA,SAAK;AAAA;AAAA,MAED,IAAI,gBAAgB,4BAAoB,KAAK,MAAM,KAAK;AAAA,IAE5D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,SAAK,IAAI,SAAS,QAAQ,KAAK,OAAO,MAAM;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,MAAM,QAAQ;AAC1B,aAAS,IAAI,GAAG,KAAK,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,UAAI,KAAK,OAAO,CAAC,MAAM,QAAQ,MAAM,QAAQ;AAC3C,cAAM,IAAI,uBAAe,EAAE;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,qBAAQ;", "names": []}