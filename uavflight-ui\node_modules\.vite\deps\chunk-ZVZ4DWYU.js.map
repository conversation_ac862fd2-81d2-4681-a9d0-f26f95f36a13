{"version": 3, "sources": ["../../ol/render/EventType.js", "../../ol/layer/Property.js", "../../ol/layer/Base.js", "../../ol/layer/Layer.js", "../../ol/render/Event.js"], "sourcesContent": ["/**\n * @module ol/render/EventType\n */\n\n/**\n * @enum {string}\n */\nexport default {\n  /**\n   * Triggered before a layer is rendered.\n   * @event module:ol/render/Event~RenderEvent#prerender\n   * @api\n   */\n  PRERENDER: 'prerender',\n\n  /**\n   * Triggered after a layer is rendered.\n   * @event module:ol/render/Event~RenderEvent#postrender\n   * @api\n   */\n  POSTRENDER: 'postrender',\n\n  /**\n   * Triggered before layers are composed.  When dispatched by the map, the event object will not have\n   * a `context` set.  When dispatched by a layer, the event object will have a `context` set.  Only\n   * WebGL layers currently dispatch this event.\n   * @event module:ol/render/Event~RenderEvent#precompose\n   * @api\n   */\n  PRECOMPOSE: 'precompose',\n\n  /**\n   * Triggered after layers are composed.  When dispatched by the map, the event object will not have\n   * a `context` set.  When dispatched by a layer, the event object will have a `context` set.  Only\n   * WebGL layers currently dispatch this event.\n   * @event module:ol/render/Event~RenderEvent#postcompose\n   * @api\n   */\n  POSTCOMPOSE: 'postcompose',\n\n  /**\n   * Triggered when rendering is complete, i.e. all sources and tiles have\n   * finished loading for the current viewport, and all tiles are faded in.\n   * The event object will not have a `context` set.\n   * @event module:ol/render/Event~RenderEvent#rendercomplete\n   * @api\n   */\n  RENDERCOMPLETE: 'rendercomplete',\n};\n\n/**\n * @typedef {'postrender'|'precompose'|'postcompose'|'rendercomplete'} MapRenderEventTypes\n */\n\n/**\n * @typedef {'postrender'|'prerender'} LayerRenderEventTypes\n */\n", "/**\n * @module ol/layer/Property\n */\n\n/**\n * @enum {string}\n */\nexport default {\n  OPACITY: 'opacity',\n  VISIBLE: 'visible',\n  EXTENT: 'extent',\n  Z_INDEX: 'zIndex',\n  MAX_RESOLUTION: 'maxResolution',\n  MIN_RESOLUTION: 'minResolution',\n  MAX_ZOOM: 'maxZoom',\n  MIN_ZOOM: 'minZoom',\n  SOURCE: 'source',\n  MAP: 'map',\n};\n", "/**\n * @module ol/layer/Base\n */\nimport BaseObject from '../Object.js';\nimport LayerProperty from './Property.js';\nimport {abstract} from '../util.js';\nimport {assert} from '../asserts.js';\nimport {clamp} from '../math.js';\n\n/**\n * A css color, or a function called with a view resolution returning a css color.\n *\n * @typedef {string|function(number):string} BackgroundColor\n * @api\n */\n\n/**\n * @typedef {import(\"../ObjectEventType\").Types|'change:extent'|'change:maxResolution'|'change:maxZoom'|\n *    'change:minResolution'|'change:minZoom'|'change:opacity'|'change:visible'|'change:zIndex'} BaseLayerObjectEventTypes\n */\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<BaseLayerObjectEventTypes, import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|BaseLayerObjectEventTypes, Return>} BaseLayerOnSignature\n */\n\n/**\n * @typedef {Object} Options\n * @property {string} [className='ol-layer'] A CSS class name to set to the layer element.\n * @property {number} [opacity=1] Opacity (0, 1).\n * @property {boolean} [visible=true] Visibility.\n * @property {import(\"../extent.js\").Extent} [extent] The bounding extent for layer rendering.  The layer will not be\n * rendered outside of this extent.\n * @property {number} [zIndex] The z-index for layer rendering.  At rendering time, the layers\n * will be ordered, first by Z-index and then by position. When `undefined`, a `zIndex` of 0 is assumed\n * for layers that are added to the map's `layers` collection, or `Infinity` when the layer's `setMap()`\n * method was used.\n * @property {number} [minResolution] The minimum resolution (inclusive) at which this layer will be\n * visible.\n * @property {number} [maxResolution] The maximum resolution (exclusive) below which this layer will\n * be visible.\n * @property {number} [minZoom] The minimum view zoom level (exclusive) above which this layer will be\n * visible.\n * @property {number} [maxZoom] The maximum view zoom level (inclusive) at which this layer will\n * be visible.\n * @property {BackgroundColor} [background] Background color for the layer. If not specified, no background\n * will be rendered.\n * @property {Object<string, *>} [properties] Arbitrary observable properties. Can be accessed with `#get()` and `#set()`.\n */\n\n/**\n * @classdesc\n * Abstract base class; normally only used for creating subclasses and not\n * instantiated in apps.\n * Note that with {@link module:ol/layer/Base~BaseLayer} and all its subclasses, any property set in\n * the options is set as a {@link module:ol/Object~BaseObject} property on the layer object, so\n * is observable, and has get/set accessors.\n *\n * @api\n */\nclass BaseLayer extends BaseObject {\n  /**\n   * @param {Options} options Layer options.\n   */\n  constructor(options) {\n    super();\n\n    /***\n     * @type {BaseLayerOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {BaseLayerOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {BaseLayerOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * @type {BackgroundColor|false}\n     * @private\n     */\n    this.background_ = options.background;\n\n    /**\n     * @type {Object<string, *>}\n     */\n    const properties = Object.assign({}, options);\n    if (typeof options.properties === 'object') {\n      delete properties.properties;\n      Object.assign(properties, options.properties);\n    }\n\n    properties[LayerProperty.OPACITY] =\n      options.opacity !== undefined ? options.opacity : 1;\n    assert(typeof properties[LayerProperty.OPACITY] === 'number', 64); // Layer opacity must be a number\n\n    properties[LayerProperty.VISIBLE] =\n      options.visible !== undefined ? options.visible : true;\n    properties[LayerProperty.Z_INDEX] = options.zIndex;\n    properties[LayerProperty.MAX_RESOLUTION] =\n      options.maxResolution !== undefined ? options.maxResolution : Infinity;\n    properties[LayerProperty.MIN_RESOLUTION] =\n      options.minResolution !== undefined ? options.minResolution : 0;\n    properties[LayerProperty.MIN_ZOOM] =\n      options.minZoom !== undefined ? options.minZoom : -Infinity;\n    properties[LayerProperty.MAX_ZOOM] =\n      options.maxZoom !== undefined ? options.maxZoom : Infinity;\n\n    /**\n     * @type {string}\n     * @private\n     */\n    this.className_ =\n      properties.className !== undefined ? properties.className : 'ol-layer';\n    delete properties.className;\n\n    this.setProperties(properties);\n\n    /**\n     * @type {import(\"./Layer.js\").State}\n     * @private\n     */\n    this.state_ = null;\n  }\n\n  /**\n   * Get the background for this layer.\n   * @return {BackgroundColor|false} Layer background.\n   */\n  getBackground() {\n    return this.background_;\n  }\n\n  /**\n   * @return {string} CSS class name.\n   */\n  getClassName() {\n    return this.className_;\n  }\n\n  /**\n   * This method is not meant to be called by layers or layer renderers because the state\n   * is incorrect if the layer is included in a layer group.\n   *\n   * @param {boolean} [managed] Layer is managed.\n   * @return {import(\"./Layer.js\").State} Layer state.\n   */\n  getLayerState(managed) {\n    /** @type {import(\"./Layer.js\").State} */\n    const state =\n      this.state_ ||\n      /** @type {?} */ ({\n        layer: this,\n        managed: managed === undefined ? true : managed,\n      });\n    const zIndex = this.getZIndex();\n    state.opacity = clamp(Math.round(this.getOpacity() * 100) / 100, 0, 1);\n    state.visible = this.getVisible();\n    state.extent = this.getExtent();\n    state.zIndex = zIndex === undefined && !state.managed ? Infinity : zIndex;\n    state.maxResolution = this.getMaxResolution();\n    state.minResolution = Math.max(this.getMinResolution(), 0);\n    state.minZoom = this.getMinZoom();\n    state.maxZoom = this.getMaxZoom();\n    this.state_ = state;\n\n    return state;\n  }\n\n  /**\n   * @abstract\n   * @param {Array<import(\"./Layer.js\").default>} [array] Array of layers (to be\n   *     modified in place).\n   * @return {Array<import(\"./Layer.js\").default>} Array of layers.\n   */\n  getLayersArray(array) {\n    return abstract();\n  }\n\n  /**\n   * @abstract\n   * @param {Array<import(\"./Layer.js\").State>} [states] Optional list of layer\n   *     states (to be modified in place).\n   * @return {Array<import(\"./Layer.js\").State>} List of layer states.\n   */\n  getLayerStatesArray(states) {\n    return abstract();\n  }\n\n  /**\n   * Return the {@link module:ol/extent~Extent extent} of the layer or `undefined` if it\n   * will be visible regardless of extent.\n   * @return {import(\"../extent.js\").Extent|undefined} The layer extent.\n   * @observable\n   * @api\n   */\n  getExtent() {\n    return /** @type {import(\"../extent.js\").Extent|undefined} */ (\n      this.get(LayerProperty.EXTENT)\n    );\n  }\n\n  /**\n   * Return the maximum resolution of the layer.\n   * @return {number} The maximum resolution of the layer.\n   * @observable\n   * @api\n   */\n  getMaxResolution() {\n    return /** @type {number} */ (this.get(LayerProperty.MAX_RESOLUTION));\n  }\n\n  /**\n   * Return the minimum resolution of the layer.\n   * @return {number} The minimum resolution of the layer.\n   * @observable\n   * @api\n   */\n  getMinResolution() {\n    return /** @type {number} */ (this.get(LayerProperty.MIN_RESOLUTION));\n  }\n\n  /**\n   * Return the minimum zoom level of the layer.\n   * @return {number} The minimum zoom level of the layer.\n   * @observable\n   * @api\n   */\n  getMinZoom() {\n    return /** @type {number} */ (this.get(LayerProperty.MIN_ZOOM));\n  }\n\n  /**\n   * Return the maximum zoom level of the layer.\n   * @return {number} The maximum zoom level of the layer.\n   * @observable\n   * @api\n   */\n  getMaxZoom() {\n    return /** @type {number} */ (this.get(LayerProperty.MAX_ZOOM));\n  }\n\n  /**\n   * Return the opacity of the layer (between 0 and 1).\n   * @return {number} The opacity of the layer.\n   * @observable\n   * @api\n   */\n  getOpacity() {\n    return /** @type {number} */ (this.get(LayerProperty.OPACITY));\n  }\n\n  /**\n   * @abstract\n   * @return {import(\"../source/Source.js\").State} Source state.\n   */\n  getSourceState() {\n    return abstract();\n  }\n\n  /**\n   * Return the value of this layer's `visible` property. To find out whether the layer\n   * is visible on a map, use `isVisible()` instead.\n   * @return {boolean} The value of the `visible` property of the layer.\n   * @observable\n   * @api\n   */\n  getVisible() {\n    return /** @type {boolean} */ (this.get(LayerProperty.VISIBLE));\n  }\n\n  /**\n   * Return the Z-index of the layer, which is used to order layers before\n   * rendering. The default Z-index is 0.\n   * @return {number} The Z-index of the layer.\n   * @observable\n   * @api\n   */\n  getZIndex() {\n    return /** @type {number} */ (this.get(LayerProperty.Z_INDEX));\n  }\n\n  /**\n   * Sets the background color.\n   * @param {BackgroundColor} [background] Background color.\n   */\n  setBackground(background) {\n    this.background_ = background;\n    this.changed();\n  }\n\n  /**\n   * Set the extent at which the layer is visible.  If `undefined`, the layer\n   * will be visible at all extents.\n   * @param {import(\"../extent.js\").Extent|undefined} extent The extent of the layer.\n   * @observable\n   * @api\n   */\n  setExtent(extent) {\n    this.set(LayerProperty.EXTENT, extent);\n  }\n\n  /**\n   * Set the maximum resolution at which the layer is visible.\n   * @param {number} maxResolution The maximum resolution of the layer.\n   * @observable\n   * @api\n   */\n  setMaxResolution(maxResolution) {\n    this.set(LayerProperty.MAX_RESOLUTION, maxResolution);\n  }\n\n  /**\n   * Set the minimum resolution at which the layer is visible.\n   * @param {number} minResolution The minimum resolution of the layer.\n   * @observable\n   * @api\n   */\n  setMinResolution(minResolution) {\n    this.set(LayerProperty.MIN_RESOLUTION, minResolution);\n  }\n\n  /**\n   * Set the maximum zoom (exclusive) at which the layer is visible.\n   * Note that the zoom levels for layer visibility are based on the\n   * view zoom level, which may be different from a tile source zoom level.\n   * @param {number} maxZoom The maximum zoom of the layer.\n   * @observable\n   * @api\n   */\n  setMaxZoom(maxZoom) {\n    this.set(LayerProperty.MAX_ZOOM, maxZoom);\n  }\n\n  /**\n   * Set the minimum zoom (inclusive) at which the layer is visible.\n   * Note that the zoom levels for layer visibility are based on the\n   * view zoom level, which may be different from a tile source zoom level.\n   * @param {number} minZoom The minimum zoom of the layer.\n   * @observable\n   * @api\n   */\n  setMinZoom(minZoom) {\n    this.set(LayerProperty.MIN_ZOOM, minZoom);\n  }\n\n  /**\n   * Set the opacity of the layer, allowed values range from 0 to 1.\n   * @param {number} opacity The opacity of the layer.\n   * @observable\n   * @api\n   */\n  setOpacity(opacity) {\n    assert(typeof opacity === 'number', 64); // Layer opacity must be a number\n    this.set(LayerProperty.OPACITY, opacity);\n  }\n\n  /**\n   * Set the visibility of the layer (`true` or `false`).\n   * @param {boolean} visible The visibility of the layer.\n   * @observable\n   * @api\n   */\n  setVisible(visible) {\n    this.set(LayerProperty.VISIBLE, visible);\n  }\n\n  /**\n   * Set Z-index of the layer, which is used to order layers before rendering.\n   * The default Z-index is 0.\n   * @param {number} zindex The z-index of the layer.\n   * @observable\n   * @api\n   */\n  setZIndex(zindex) {\n    this.set(LayerProperty.Z_INDEX, zindex);\n  }\n\n  /**\n   * Clean up.\n   */\n  disposeInternal() {\n    if (this.state_) {\n      this.state_.layer = null;\n      this.state_ = null;\n    }\n    super.disposeInternal();\n  }\n}\n\nexport default BaseLayer;\n", "/**\n * @module ol/layer/Layer\n */\nimport BaseLayer from './Base.js';\nimport EventType from '../events/EventType.js';\nimport LayerProperty from './Property.js';\nimport RenderEventType from '../render/EventType.js';\nimport View from '../View.js';\nimport {assert} from '../asserts.js';\nimport {intersects} from '../extent.js';\nimport {listen, unlistenBy<PERSON>ey} from '../events.js';\n\n/**\n * @typedef {function(import(\"../Map.js\").FrameState):HTMLElement} RenderFunction\n */\n\n/**\n * @typedef {'sourceready'|'change:source'} LayerEventType\n */\n\n/***\n * @template Return\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"./Base\").BaseLayerObjectEventTypes|\n *     LayerEventType, import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../render/EventType\").LayerRenderEventTypes, import(\"../render/Event\").default, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"./Base\").BaseLayerObjectEventTypes|LayerEventType|\n *     import(\"../render/EventType\").LayerRenderEventTypes, Return>} LayerOnSignature\n */\n\n/**\n * @template {import(\"../source/Source.js\").default} [SourceType=import(\"../source/Source.js\").default]\n * @typedef {Object} Options\n * @property {string} [className='ol-layer'] A CSS class name to set to the layer element.\n * @property {number} [opacity=1] Opacity (0, 1).\n * @property {boolean} [visible=true] Visibility.\n * @property {import(\"../extent.js\").Extent} [extent] The bounding extent for layer rendering.  The layer will not be\n * rendered outside of this extent.\n * @property {number} [zIndex] The z-index for layer rendering.  At rendering time, the layers\n * will be ordered, first by Z-index and then by position. When `undefined`, a `zIndex` of 0 is assumed\n * for layers that are added to the map's `layers` collection, or `Infinity` when the layer's `setMap()`\n * method was used.\n * @property {number} [minResolution] The minimum resolution (inclusive) at which this layer will be\n * visible.\n * @property {number} [maxResolution] The maximum resolution (exclusive) below which this layer will\n * be visible.\n * @property {number} [minZoom] The minimum view zoom level (exclusive) above which this layer will be\n * visible.\n * @property {number} [maxZoom] The maximum view zoom level (inclusive) at which this layer will\n * be visible.\n * @property {SourceType} [source] Source for this layer.  If not provided to the constructor,\n * the source can be set by calling {@link module:ol/layer/Layer~Layer#setSource layer.setSource(source)} after\n * construction.\n * @property {import(\"../Map.js\").default|null} [map] Map.\n * @property {RenderFunction} [render] Render function. Takes the frame state as input and is expected to return an\n * HTML element. Will overwrite the default rendering for the layer.\n * @property {Object<string, *>} [properties] Arbitrary observable properties. Can be accessed with `#get()` and `#set()`.\n */\n\n/**\n * @typedef {Object} State\n * @property {import(\"./Layer.js\").default} layer Layer.\n * @property {number} opacity Opacity, the value is rounded to two digits to appear after the decimal point.\n * @property {boolean} visible Visible.\n * @property {boolean} managed Managed.\n * @property {import(\"../extent.js\").Extent} [extent] Extent.\n * @property {number} zIndex ZIndex.\n * @property {number} maxResolution Maximum resolution.\n * @property {number} minResolution Minimum resolution.\n * @property {number} minZoom Minimum zoom.\n * @property {number} maxZoom Maximum zoom.\n */\n\n/**\n * @classdesc\n * Base class from which all layer types are derived. This should only be instantiated\n * in the case where a custom layer is added to the map with a custom `render` function.\n * Such a function can be specified in the `options` object, and is expected to return an HTML element.\n *\n * A visual representation of raster or vector map data.\n * Layers group together those properties that pertain to how the data is to be\n * displayed, irrespective of the source of that data.\n *\n * Layers are usually added to a map with [map.addLayer()]{@link import(\"../Map.js\").default#addLayer}.\n * Components like {@link module:ol/interaction/Draw~Draw} use unmanaged layers\n * internally. These unmanaged layers are associated with the map using\n * [layer.setMap()]{@link module:ol/layer/Layer~Layer#setMap} instead.\n *\n * A generic `change` event is fired when the state of the source changes.\n * A `sourceready` event is fired when the layer's source is ready.\n *\n * @fires import(\"../render/Event.js\").RenderEvent#prerender\n * @fires import(\"../render/Event.js\").RenderEvent#postrender\n * @fires import(\"../events/Event.js\").BaseEvent#sourceready\n *\n * @template {import(\"../source/Source.js\").default} [SourceType=import(\"../source/Source.js\").default]\n * @template {import(\"../renderer/Layer.js\").default} [RendererType=import(\"../renderer/Layer.js\").default]\n * @api\n */\nclass Layer extends BaseLayer {\n  /**\n   * @param {Options<SourceType>} options Layer options.\n   */\n  constructor(options) {\n    const baseOptions = Object.assign({}, options);\n    delete baseOptions.source;\n\n    super(baseOptions);\n\n    /***\n     * @type {LayerOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {LayerOnSignature<import(\"../events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {LayerOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * @private\n     * @type {?import(\"../events.js\").EventsKey}\n     */\n    this.mapPrecomposeKey_ = null;\n\n    /**\n     * @private\n     * @type {?import(\"../events.js\").EventsKey}\n     */\n    this.mapRenderKey_ = null;\n\n    /**\n     * @private\n     * @type {?import(\"../events.js\").EventsKey}\n     */\n    this.sourceChangeKey_ = null;\n\n    /**\n     * @private\n     * @type {RendererType}\n     */\n    this.renderer_ = null;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.sourceReady_ = false;\n\n    /**\n     * @protected\n     * @type {boolean}\n     */\n    this.rendered = false;\n\n    // Overwrite default render method with a custom one\n    if (options.render) {\n      this.render = options.render;\n    }\n\n    if (options.map) {\n      this.setMap(options.map);\n    }\n\n    this.addChangeListener(\n      LayerProperty.SOURCE,\n      this.handleSourcePropertyChange_\n    );\n\n    const source = options.source\n      ? /** @type {SourceType} */ (options.source)\n      : null;\n    this.setSource(source);\n  }\n\n  /**\n   * @param {Array<import(\"./Layer.js\").default>} [array] Array of layers (to be modified in place).\n   * @return {Array<import(\"./Layer.js\").default>} Array of layers.\n   */\n  getLayersArray(array) {\n    array = array ? array : [];\n    array.push(this);\n    return array;\n  }\n\n  /**\n   * @param {Array<import(\"./Layer.js\").State>} [states] Optional list of layer states (to be modified in place).\n   * @return {Array<import(\"./Layer.js\").State>} List of layer states.\n   */\n  getLayerStatesArray(states) {\n    states = states ? states : [];\n    states.push(this.getLayerState());\n    return states;\n  }\n\n  /**\n   * Get the layer source.\n   * @return {SourceType|null} The layer source (or `null` if not yet set).\n   * @observable\n   * @api\n   */\n  getSource() {\n    return /** @type {SourceType} */ (this.get(LayerProperty.SOURCE)) || null;\n  }\n\n  /**\n   * @return {SourceType|null} The source being rendered.\n   */\n  getRenderSource() {\n    return this.getSource();\n  }\n\n  /**\n   * @return {import(\"../source/Source.js\").State} Source state.\n   */\n  getSourceState() {\n    const source = this.getSource();\n    return !source ? 'undefined' : source.getState();\n  }\n\n  /**\n   * @private\n   */\n  handleSourceChange_() {\n    this.changed();\n    if (this.sourceReady_ || this.getSource().getState() !== 'ready') {\n      return;\n    }\n    this.sourceReady_ = true;\n    this.dispatchEvent('sourceready');\n  }\n\n  /**\n   * @private\n   */\n  handleSourcePropertyChange_() {\n    if (this.sourceChangeKey_) {\n      unlistenByKey(this.sourceChangeKey_);\n      this.sourceChangeKey_ = null;\n    }\n    this.sourceReady_ = false;\n    const source = this.getSource();\n    if (source) {\n      this.sourceChangeKey_ = listen(\n        source,\n        EventType.CHANGE,\n        this.handleSourceChange_,\n        this\n      );\n      if (source.getState() === 'ready') {\n        this.sourceReady_ = true;\n        setTimeout(() => {\n          this.dispatchEvent('sourceready');\n        }, 0);\n      }\n    }\n    this.changed();\n  }\n\n  /**\n   * @param {import(\"../pixel\").Pixel} pixel Pixel.\n   * @return {Promise<Array<import(\"../Feature\").FeatureLike>>} Promise that resolves with\n   * an array of features.\n   */\n  getFeatures(pixel) {\n    if (!this.renderer_) {\n      return Promise.resolve([]);\n    }\n    return this.renderer_.getFeatures(pixel);\n  }\n\n  /**\n   * @param {import(\"../pixel\").Pixel} pixel Pixel.\n   * @return {Uint8ClampedArray|Uint8Array|Float32Array|DataView|null} Pixel data.\n   */\n  getData(pixel) {\n    if (!this.renderer_ || !this.rendered) {\n      return null;\n    }\n    return this.renderer_.getData(pixel);\n  }\n\n  /**\n   * The layer is visible on the map view, i.e. within its min/max resolution or zoom and\n   * extent, not set to `visible: false`, and not inside a layer group that is set\n   * to `visible: false`.\n   * @param {View|import(\"../View.js\").ViewStateLayerStateExtent} [view] View or {@link import(\"../Map.js\").FrameState}.\n   * Only required when the layer is not added to a map.\n   * @return {boolean} The layer is visible in the map view.\n   * @api\n   */\n  isVisible(view) {\n    let frameState;\n    const map = this.getMapInternal();\n    if (!view && map) {\n      view = map.getView();\n    }\n    if (view instanceof View) {\n      frameState = {\n        viewState: view.getState(),\n        extent: view.calculateExtent(),\n      };\n    } else {\n      frameState = view;\n    }\n    if (!frameState.layerStatesArray && map) {\n      frameState.layerStatesArray = map.getLayerGroup().getLayerStatesArray();\n    }\n    let layerState;\n    if (frameState.layerStatesArray) {\n      layerState = frameState.layerStatesArray.find(\n        (layerState) => layerState.layer === this\n      );\n    } else {\n      layerState = this.getLayerState();\n    }\n\n    const layerExtent = this.getExtent();\n\n    return (\n      inView(layerState, frameState.viewState) &&\n      (!layerExtent || intersects(layerExtent, frameState.extent))\n    );\n  }\n\n  /**\n   * Get the attributions of the source of this layer for the given view.\n   * @param {View|import(\"../View.js\").ViewStateLayerStateExtent} [view] View or {@link import(\"../Map.js\").FrameState}.\n   * Only required when the layer is not added to a map.\n   * @return {Array<string>} Attributions for this layer at the given view.\n   * @api\n   */\n  getAttributions(view) {\n    if (!this.isVisible(view)) {\n      return [];\n    }\n    let getAttributions;\n    const source = this.getSource();\n    if (source) {\n      getAttributions = source.getAttributions();\n    }\n    if (!getAttributions) {\n      return [];\n    }\n    const frameState =\n      view instanceof View ? view.getViewStateAndExtent() : view;\n    let attributions = getAttributions(frameState);\n    if (!Array.isArray(attributions)) {\n      attributions = [attributions];\n    }\n    return attributions;\n  }\n\n  /**\n   * In charge to manage the rendering of the layer. One layer type is\n   * bounded with one layer renderer.\n   * @param {?import(\"../Map.js\").FrameState} frameState Frame state.\n   * @param {HTMLElement} target Target which the renderer may (but need not) use\n   * for rendering its content.\n   * @return {HTMLElement|null} The rendered element.\n   */\n  render(frameState, target) {\n    const layerRenderer = this.getRenderer();\n\n    if (layerRenderer.prepareFrame(frameState)) {\n      this.rendered = true;\n      return layerRenderer.renderFrame(frameState, target);\n    }\n    return null;\n  }\n\n  /**\n   * Called when a layer is not visible during a map render.\n   */\n  unrender() {\n    this.rendered = false;\n  }\n\n  /**\n   * For use inside the library only.\n   * @param {import(\"../Map.js\").default|null} map Map.\n   */\n  setMapInternal(map) {\n    if (!map) {\n      this.unrender();\n    }\n    this.set(LayerProperty.MAP, map);\n  }\n\n  /**\n   * For use inside the library only.\n   * @return {import(\"../Map.js\").default|null} Map.\n   */\n  getMapInternal() {\n    return this.get(LayerProperty.MAP);\n  }\n\n  /**\n   * Sets the layer to be rendered on top of other layers on a map. The map will\n   * not manage this layer in its layers collection. This\n   * is useful for temporary layers. To remove an unmanaged layer from the map,\n   * use `#setMap(null)`.\n   *\n   * To add the layer to a map and have it managed by the map, use\n   * {@link module:ol/Map~Map#addLayer} instead.\n   * @param {import(\"../Map.js\").default|null} map Map.\n   * @api\n   */\n  setMap(map) {\n    if (this.mapPrecomposeKey_) {\n      unlistenByKey(this.mapPrecomposeKey_);\n      this.mapPrecomposeKey_ = null;\n    }\n    if (!map) {\n      this.changed();\n    }\n    if (this.mapRenderKey_) {\n      unlistenByKey(this.mapRenderKey_);\n      this.mapRenderKey_ = null;\n    }\n    if (map) {\n      this.mapPrecomposeKey_ = listen(\n        map,\n        RenderEventType.PRECOMPOSE,\n        function (evt) {\n          const renderEvent =\n            /** @type {import(\"../render/Event.js\").default} */ (evt);\n          const layerStatesArray = renderEvent.frameState.layerStatesArray;\n          const layerState = this.getLayerState(false);\n          // A layer can only be added to the map once. Use either `layer.setMap()` or `map.addLayer()`, not both.\n          assert(\n            !layerStatesArray.some(function (arrayLayerState) {\n              return arrayLayerState.layer === layerState.layer;\n            }),\n            67\n          );\n          layerStatesArray.push(layerState);\n        },\n        this\n      );\n      this.mapRenderKey_ = listen(this, EventType.CHANGE, map.render, map);\n      this.changed();\n    }\n  }\n\n  /**\n   * Set the layer source.\n   * @param {SourceType|null} source The layer source.\n   * @observable\n   * @api\n   */\n  setSource(source) {\n    this.set(LayerProperty.SOURCE, source);\n  }\n\n  /**\n   * Get the renderer for this layer.\n   * @return {RendererType|null} The layer renderer.\n   */\n  getRenderer() {\n    if (!this.renderer_) {\n      this.renderer_ = this.createRenderer();\n    }\n    return this.renderer_;\n  }\n\n  /**\n   * @return {boolean} The layer has a renderer.\n   */\n  hasRenderer() {\n    return !!this.renderer_;\n  }\n\n  /**\n   * Create a renderer for this layer.\n   * @return {RendererType} A layer renderer.\n   * @protected\n   */\n  createRenderer() {\n    return null;\n  }\n\n  /**\n   * Clean up.\n   */\n  disposeInternal() {\n    if (this.renderer_) {\n      this.renderer_.dispose();\n      delete this.renderer_;\n    }\n\n    this.setSource(null);\n    super.disposeInternal();\n  }\n}\n\n/**\n * Return `true` if the layer is visible and if the provided view state\n * has resolution and zoom levels that are in range of the layer's min/max.\n * @param {State} layerState Layer state.\n * @param {import(\"../View.js\").State} viewState View state.\n * @return {boolean} The layer is visible at the given view state.\n */\nexport function inView(layerState, viewState) {\n  if (!layerState.visible) {\n    return false;\n  }\n  const resolution = viewState.resolution;\n  if (\n    resolution < layerState.minResolution ||\n    resolution >= layerState.maxResolution\n  ) {\n    return false;\n  }\n  const zoom = viewState.zoom;\n  return zoom > layerState.minZoom && zoom <= layerState.maxZoom;\n}\n\nexport default Layer;\n", "/**\n * @module ol/render/Event\n */\n\nimport Event from '../events/Event.js';\n\nclass RenderEvent extends Event {\n  /**\n   * @param {import(\"./EventType.js\").default} type Type.\n   * @param {import(\"../transform.js\").Transform} [inversePixelTransform] Transform for\n   *     CSS pixels to rendered pixels.\n   * @param {import(\"../Map.js\").FrameState} [frameState] Frame state.\n   * @param {?(CanvasRenderingContext2D|WebGLRenderingContext)} [context] Context.\n   */\n  constructor(type, inversePixelTransform, frameState, context) {\n    super(type);\n\n    /**\n     * Transform from CSS pixels (relative to the top-left corner of the map viewport)\n     * to rendered pixels on this event's `context`. Only available when a Canvas renderer is used, null otherwise.\n     * @type {import(\"../transform.js\").Transform|undefined}\n     * @api\n     */\n    this.inversePixelTransform = inversePixelTransform;\n\n    /**\n     * An object representing the current render frame state.\n     * @type {import(\"../Map.js\").FrameState|undefined}\n     * @api\n     */\n    this.frameState = frameState;\n\n    /**\n     * Canvas context. Not available when the event is dispatched by the map. For Canvas 2D layers,\n     * the context will be the 2D rendering context.  For WebGL layers, the context will be the WebGL\n     * context.\n     * @type {CanvasRenderingContext2D|WebGLRenderingContext|undefined}\n     * @api\n     */\n    this.context = context;\n  }\n}\n\nexport default RenderEvent;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAOA,qBAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOX,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASZ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASZ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASb,gBAAgB;AAClB;;;ACzCA,IAAO,mBAAQ;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,KAAK;AACP;;;AC4CA,IAAM,YAAN,cAAwB,eAAW;AAAA;AAAA;AAAA;AAAA,EAIjC,YAAY,SAAS;AACnB,UAAM;AAKN,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,SAAK,cAAc,QAAQ;AAK3B,UAAM,aAAa,OAAO,OAAO,CAAC,GAAG,OAAO;AAC5C,QAAI,OAAO,QAAQ,eAAe,UAAU;AAC1C,aAAO,WAAW;AAClB,aAAO,OAAO,YAAY,QAAQ,UAAU;AAAA,IAC9C;AAEA,eAAW,iBAAc,OAAO,IAC9B,QAAQ,YAAY,SAAY,QAAQ,UAAU;AACpD,WAAO,OAAO,WAAW,iBAAc,OAAO,MAAM,UAAU,EAAE;AAEhE,eAAW,iBAAc,OAAO,IAC9B,QAAQ,YAAY,SAAY,QAAQ,UAAU;AACpD,eAAW,iBAAc,OAAO,IAAI,QAAQ;AAC5C,eAAW,iBAAc,cAAc,IACrC,QAAQ,kBAAkB,SAAY,QAAQ,gBAAgB;AAChE,eAAW,iBAAc,cAAc,IACrC,QAAQ,kBAAkB,SAAY,QAAQ,gBAAgB;AAChE,eAAW,iBAAc,QAAQ,IAC/B,QAAQ,YAAY,SAAY,QAAQ,UAAU;AACpD,eAAW,iBAAc,QAAQ,IAC/B,QAAQ,YAAY,SAAY,QAAQ,UAAU;AAMpD,SAAK,aACH,WAAW,cAAc,SAAY,WAAW,YAAY;AAC9D,WAAO,WAAW;AAElB,SAAK,cAAc,UAAU;AAM7B,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,SAAS;AAErB,UAAM,QACJ,KAAK;AAAA,IACa;AAAA,MAChB,OAAO;AAAA,MACP,SAAS,YAAY,SAAY,OAAO;AAAA,IAC1C;AACF,UAAM,SAAS,KAAK,UAAU;AAC9B,UAAM,UAAU,MAAM,KAAK,MAAM,KAAK,WAAW,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC;AACrE,UAAM,UAAU,KAAK,WAAW;AAChC,UAAM,SAAS,KAAK,UAAU;AAC9B,UAAM,SAAS,WAAW,UAAa,CAAC,MAAM,UAAU,WAAW;AACnE,UAAM,gBAAgB,KAAK,iBAAiB;AAC5C,UAAM,gBAAgB,KAAK,IAAI,KAAK,iBAAiB,GAAG,CAAC;AACzD,UAAM,UAAU,KAAK,WAAW;AAChC,UAAM,UAAU,KAAK,WAAW;AAChC,SAAK,SAAS;AAEd,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,OAAO;AACpB,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB,QAAQ;AAC1B,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY;AACV;AAAA;AAAA,MACE,KAAK,IAAI,iBAAc,MAAM;AAAA;AAAA,EAEjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB;AACjB;AAAA;AAAA,MAA8B,KAAK,IAAI,iBAAc,cAAc;AAAA;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB;AACjB;AAAA;AAAA,MAA8B,KAAK,IAAI,iBAAc,cAAc;AAAA;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa;AACX;AAAA;AAAA,MAA8B,KAAK,IAAI,iBAAc,QAAQ;AAAA;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa;AACX;AAAA;AAAA,MAA8B,KAAK,IAAI,iBAAc,QAAQ;AAAA;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa;AACX;AAAA;AAAA,MAA8B,KAAK,IAAI,iBAAc,OAAO;AAAA;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB;AACf,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa;AACX;AAAA;AAAA,MAA+B,KAAK,IAAI,iBAAc,OAAO;AAAA;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY;AACV;AAAA;AAAA,MAA8B,KAAK,IAAI,iBAAc,OAAO;AAAA;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,YAAY;AACxB,SAAK,cAAc;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,QAAQ;AAChB,SAAK,IAAI,iBAAc,QAAQ,MAAM;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,eAAe;AAC9B,SAAK,IAAI,iBAAc,gBAAgB,aAAa;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,eAAe;AAC9B,SAAK,IAAI,iBAAc,gBAAgB,aAAa;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,WAAW,SAAS;AAClB,SAAK,IAAI,iBAAc,UAAU,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,WAAW,SAAS;AAClB,SAAK,IAAI,iBAAc,UAAU,OAAO;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAClB,WAAO,OAAO,YAAY,UAAU,EAAE;AACtC,SAAK,IAAI,iBAAc,SAAS,OAAO;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAClB,SAAK,IAAI,iBAAc,SAAS,OAAO;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,QAAQ;AAChB,SAAK,IAAI,iBAAc,SAAS,MAAM;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,QAAI,KAAK,QAAQ;AACf,WAAK,OAAO,QAAQ;AACpB,WAAK,SAAS;AAAA,IAChB;AACA,UAAM,gBAAgB;AAAA,EACxB;AACF;AAEA,IAAO,eAAQ;;;AC1Sf,IAAM,QAAN,cAAoB,aAAU;AAAA;AAAA;AAAA;AAAA,EAI5B,YAAY,SAAS;AACnB,UAAM,cAAc,OAAO,OAAO,CAAC,GAAG,OAAO;AAC7C,WAAO,YAAY;AAEnB,UAAM,WAAW;AAKjB,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,SAAK,oBAAoB;AAMzB,SAAK,gBAAgB;AAMrB,SAAK,mBAAmB;AAMxB,SAAK,YAAY;AAMjB,SAAK,eAAe;AAMpB,SAAK,WAAW;AAGhB,QAAI,QAAQ,QAAQ;AAClB,WAAK,SAAS,QAAQ;AAAA,IACxB;AAEA,QAAI,QAAQ,KAAK;AACf,WAAK,OAAO,QAAQ,GAAG;AAAA,IACzB;AAEA,SAAK;AAAA,MACH,iBAAc;AAAA,MACd,KAAK;AAAA,IACP;AAEA,UAAM,SAAS,QAAQ;AAAA;AAAA,MACQ,QAAQ;AAAA,QACnC;AACJ,SAAK,UAAU,MAAM;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,OAAO;AACpB,YAAQ,QAAQ,QAAQ,CAAC;AACzB,UAAM,KAAK,IAAI;AACf,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,QAAQ;AAC1B,aAAS,SAAS,SAAS,CAAC;AAC5B,WAAO,KAAK,KAAK,cAAc,CAAC;AAChC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV;AAAA;AAAA,MAAkC,KAAK,IAAI,iBAAc,MAAM,KAAM;AAAA;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,UAAM,SAAS,KAAK,UAAU;AAC9B,WAAO,CAAC,SAAS,cAAc,OAAO,SAAS;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB;AACpB,SAAK,QAAQ;AACb,QAAI,KAAK,gBAAgB,KAAK,UAAU,EAAE,SAAS,MAAM,SAAS;AAChE;AAAA,IACF;AACA,SAAK,eAAe;AACpB,SAAK,cAAc,aAAa;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,8BAA8B;AAC5B,QAAI,KAAK,kBAAkB;AACzB,oBAAc,KAAK,gBAAgB;AACnC,WAAK,mBAAmB;AAAA,IAC1B;AACA,SAAK,eAAe;AACpB,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,QAAQ;AACV,WAAK,mBAAmB;AAAA,QACtB;AAAA,QACA,kBAAU;AAAA,QACV,KAAK;AAAA,QACL;AAAA,MACF;AACA,UAAI,OAAO,SAAS,MAAM,SAAS;AACjC,aAAK,eAAe;AACpB,mBAAW,MAAM;AACf,eAAK,cAAc,aAAa;AAAA,QAClC,GAAG,CAAC;AAAA,MACN;AAAA,IACF;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,OAAO;AACjB,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO,QAAQ,QAAQ,CAAC,CAAC;AAAA,IAC3B;AACA,WAAO,KAAK,UAAU,YAAY,KAAK;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,OAAO;AACb,QAAI,CAAC,KAAK,aAAa,CAAC,KAAK,UAAU;AACrC,aAAO;AAAA,IACT;AACA,WAAO,KAAK,UAAU,QAAQ,KAAK;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,UAAU,MAAM;AACd,QAAI;AACJ,UAAM,MAAM,KAAK,eAAe;AAChC,QAAI,CAAC,QAAQ,KAAK;AAChB,aAAO,IAAI,QAAQ;AAAA,IACrB;AACA,QAAI,gBAAgB,cAAM;AACxB,mBAAa;AAAA,QACX,WAAW,KAAK,SAAS;AAAA,QACzB,QAAQ,KAAK,gBAAgB;AAAA,MAC/B;AAAA,IACF,OAAO;AACL,mBAAa;AAAA,IACf;AACA,QAAI,CAAC,WAAW,oBAAoB,KAAK;AACvC,iBAAW,mBAAmB,IAAI,cAAc,EAAE,oBAAoB;AAAA,IACxE;AACA,QAAI;AACJ,QAAI,WAAW,kBAAkB;AAC/B,mBAAa,WAAW,iBAAiB;AAAA,QACvC,CAACC,gBAAeA,YAAW,UAAU;AAAA,MACvC;AAAA,IACF,OAAO;AACL,mBAAa,KAAK,cAAc;AAAA,IAClC;AAEA,UAAM,cAAc,KAAK,UAAU;AAEnC,WACE,OAAO,YAAY,WAAW,SAAS,MACtC,CAAC,eAAe,WAAW,aAAa,WAAW,MAAM;AAAA,EAE9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,MAAM;AACpB,QAAI,CAAC,KAAK,UAAU,IAAI,GAAG;AACzB,aAAO,CAAC;AAAA,IACV;AACA,QAAI;AACJ,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,QAAQ;AACV,wBAAkB,OAAO,gBAAgB;AAAA,IAC3C;AACA,QAAI,CAAC,iBAAiB;AACpB,aAAO,CAAC;AAAA,IACV;AACA,UAAM,aACJ,gBAAgB,eAAO,KAAK,sBAAsB,IAAI;AACxD,QAAI,eAAe,gBAAgB,UAAU;AAC7C,QAAI,CAAC,MAAM,QAAQ,YAAY,GAAG;AAChC,qBAAe,CAAC,YAAY;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,YAAY,QAAQ;AACzB,UAAM,gBAAgB,KAAK,YAAY;AAEvC,QAAI,cAAc,aAAa,UAAU,GAAG;AAC1C,WAAK,WAAW;AAChB,aAAO,cAAc,YAAY,YAAY,MAAM;AAAA,IACrD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,KAAK;AAClB,QAAI,CAAC,KAAK;AACR,WAAK,SAAS;AAAA,IAChB;AACA,SAAK,IAAI,iBAAc,KAAK,GAAG;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB;AACf,WAAO,KAAK,IAAI,iBAAc,GAAG;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAO,KAAK;AACV,QAAI,KAAK,mBAAmB;AAC1B,oBAAc,KAAK,iBAAiB;AACpC,WAAK,oBAAoB;AAAA,IAC3B;AACA,QAAI,CAAC,KAAK;AACR,WAAK,QAAQ;AAAA,IACf;AACA,QAAI,KAAK,eAAe;AACtB,oBAAc,KAAK,aAAa;AAChC,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI,KAAK;AACP,WAAK,oBAAoB;AAAA,QACvB;AAAA,QACAC,mBAAgB;AAAA,QAChB,SAAU,KAAK;AACb,gBAAM;AAAA;AAAA,YACiD;AAAA;AACvD,gBAAM,mBAAmB,YAAY,WAAW;AAChD,gBAAM,aAAa,KAAK,cAAc,KAAK;AAE3C;AAAA,YACE,CAAC,iBAAiB,KAAK,SAAU,iBAAiB;AAChD,qBAAO,gBAAgB,UAAU,WAAW;AAAA,YAC9C,CAAC;AAAA,YACD;AAAA,UACF;AACA,2BAAiB,KAAK,UAAU;AAAA,QAClC;AAAA,QACA;AAAA,MACF;AACA,WAAK,gBAAgB,OAAO,MAAM,kBAAU,QAAQ,IAAI,QAAQ,GAAG;AACnE,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,SAAK,IAAI,iBAAc,QAAQ,MAAM;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACZ,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY,KAAK,eAAe;AAAA,IACvC;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,QAAQ;AACvB,aAAO,KAAK;AAAA,IACd;AAEA,SAAK,UAAU,IAAI;AACnB,UAAM,gBAAgB;AAAA,EACxB;AACF;AASO,SAAS,OAAO,YAAY,WAAW;AAC5C,MAAI,CAAC,WAAW,SAAS;AACvB,WAAO;AAAA,EACT;AACA,QAAM,aAAa,UAAU;AAC7B,MACE,aAAa,WAAW,iBACxB,cAAc,WAAW,eACzB;AACA,WAAO;AAAA,EACT;AACA,QAAM,OAAO,UAAU;AACvB,SAAO,OAAO,WAAW,WAAW,QAAQ,WAAW;AACzD;AAEA,IAAO,gBAAQ;;;ACrgBf,IAAM,cAAN,cAA0B,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ9B,YAAY,MAAM,uBAAuB,YAAY,SAAS;AAC5D,UAAM,IAAI;AAQV,SAAK,wBAAwB;AAO7B,SAAK,aAAa;AASlB,SAAK,UAAU;AAAA,EACjB;AACF;AAEA,IAAOC,iBAAQ;", "names": ["EventType_default", "layerState", "EventType_default", "Event_default"]}