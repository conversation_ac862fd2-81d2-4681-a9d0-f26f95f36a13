import {
  VOID
} from "./chunk-FD2TC2CV.js";

// node_modules/ol/featureloader.js
var withCredentials = false;
function loadFeaturesXhr(url, format, extent, resolution, projection, success, failure) {
  const xhr2 = new XMLHttpRequest();
  xhr2.open(
    "GET",
    typeof url === "function" ? url(extent, resolution, projection) : url,
    true
  );
  if (format.getType() == "arraybuffer") {
    xhr2.responseType = "arraybuffer";
  }
  xhr2.withCredentials = withCredentials;
  xhr2.onload = function(event) {
    if (!xhr2.status || xhr2.status >= 200 && xhr2.status < 300) {
      const type = format.getType();
      let source;
      if (type == "json" || type == "text") {
        source = xhr2.responseText;
      } else if (type == "xml") {
        source = xhr2.responseXML;
        if (!source) {
          source = new DOMParser().parseFromString(
            xhr2.responseText,
            "application/xml"
          );
        }
      } else if (type == "arraybuffer") {
        source = /** @type {ArrayBuffer} */
        xhr2.response;
      }
      if (source) {
        success(
          /** @type {Array<import("./Feature.js").default>} */
          format.readFeatures(source, {
            extent,
            featureProjection: projection
          }),
          format.readProjection(source)
        );
      } else {
        failure();
      }
    } else {
      failure();
    }
  };
  xhr2.onerror = failure;
  xhr2.send();
}
function xhr(url, format) {
  return function(extent, resolution, projection, success, failure) {
    const source = (
      /** @type {import("./source/Vector").default} */
      this
    );
    loadFeaturesXhr(
      url,
      format,
      extent,
      resolution,
      projection,
      /**
       * @param {Array<import("./Feature.js").default>} features The loaded features.
       * @param {import("./proj/Projection.js").default} dataProjection Data
       * projection.
       */
      function(features, dataProjection) {
        source.addFeatures(features);
        if (success !== void 0) {
          success(features);
        }
      },
      /* FIXME handle error */
      failure ? failure : VOID
    );
  };
}

export {
  loadFeaturesXhr,
  xhr
};
//# sourceMappingURL=chunk-DBCZG22O.js.map
