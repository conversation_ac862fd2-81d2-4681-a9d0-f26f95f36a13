{"version": 3, "sources": ["../../ol/dom.js"], "sourcesContent": ["import {WORKER_OFFSCREEN_CANVAS} from './has.js';\n\n/**\n * @module ol/dom\n */\n\n//FIXME Move this function to the canvas module\n/**\n * Create an html canvas element and returns its 2d context.\n * @param {number} [width] Canvas width.\n * @param {number} [height] Canvas height.\n * @param {Array<HTMLCanvasElement>} [canvasPool] Canvas pool to take existing canvas from.\n * @param {CanvasRenderingContext2DSettings} [settings] CanvasRenderingContext2DSettings\n * @return {CanvasRenderingContext2D} The context.\n */\nexport function createCanvasContext2D(width, height, canvasPool, settings) {\n  /** @type {HTMLCanvasElement|OffscreenCanvas} */\n  let canvas;\n  if (canvasPool && canvasPool.length) {\n    canvas = canvasPool.shift();\n  } else if (WORKER_OFFSCREEN_CANVAS) {\n    canvas = new OffscreenCanvas(width || 300, height || 300);\n  } else {\n    canvas = document.createElement('canvas');\n  }\n  if (width) {\n    canvas.width = width;\n  }\n  if (height) {\n    canvas.height = height;\n  }\n  //FIXME Allow OffscreenCanvasRenderingContext2D as return type\n  return /** @type {CanvasRenderingContext2D} */ (\n    canvas.getContext('2d', settings)\n  );\n}\n\n/**\n * Releases canvas memory to avoid exceeding memory limits in Safari.\n * See https://pqina.nl/blog/total-canvas-memory-use-exceeds-the-maximum-limit/\n * @param {CanvasRenderingContext2D} context Context.\n */\nexport function releaseCanvas(context) {\n  const canvas = context.canvas;\n  canvas.width = 1;\n  canvas.height = 1;\n  context.clearRect(0, 0, 1, 1);\n}\n\n/**\n * Get the current computed width for the given element including margin,\n * padding and border.\n * Equivalent to jQuery's `$(el).outerWidth(true)`.\n * @param {!HTMLElement} element Element.\n * @return {number} The width.\n */\nexport function outerWidth(element) {\n  let width = element.offsetWidth;\n  const style = getComputedStyle(element);\n  width += parseInt(style.marginLeft, 10) + parseInt(style.marginRight, 10);\n\n  return width;\n}\n\n/**\n * Get the current computed height for the given element including margin,\n * padding and border.\n * Equivalent to jQuery's `$(el).outerHeight(true)`.\n * @param {!HTMLElement} element Element.\n * @return {number} The height.\n */\nexport function outerHeight(element) {\n  let height = element.offsetHeight;\n  const style = getComputedStyle(element);\n  height += parseInt(style.marginTop, 10) + parseInt(style.marginBottom, 10);\n\n  return height;\n}\n\n/**\n * @param {Node} newNode Node to replace old node\n * @param {Node} oldNode The node to be replaced\n */\nexport function replaceNode(newNode, oldNode) {\n  const parent = oldNode.parentNode;\n  if (parent) {\n    parent.replaceChild(newNode, oldNode);\n  }\n}\n\n/**\n * @param {Node} node The node to remove.\n * @return {Node|null} The node that was removed or null.\n */\nexport function removeNode(node) {\n  return node && node.parentNode ? node.parentNode.removeChild(node) : null;\n}\n\n/**\n * @param {Node} node The node to remove the children from.\n */\nexport function removeChildren(node) {\n  while (node.lastChild) {\n    node.removeChild(node.lastChild);\n  }\n}\n\n/**\n * Transform the children of a parent node so they match the\n * provided list of children.  This function aims to efficiently\n * remove, add, and reorder child nodes while maintaining a simple\n * implementation (it is not guaranteed to minimize DOM operations).\n * @param {Node} node The parent node whose children need reworking.\n * @param {Array<Node>} children The desired children.\n */\nexport function replaceChildren(node, children) {\n  const oldChildren = node.childNodes;\n\n  for (let i = 0; true; ++i) {\n    const oldChild = oldChildren[i];\n    const newChild = children[i];\n\n    // check if our work is done\n    if (!oldChild && !newChild) {\n      break;\n    }\n\n    // check if children match\n    if (oldChild === newChild) {\n      continue;\n    }\n\n    // check if a new child needs to be added\n    if (!oldChild) {\n      node.appendChild(newChild);\n      continue;\n    }\n\n    // check if an old child needs to be removed\n    if (!newChild) {\n      node.removeChild(oldChild);\n      --i;\n      continue;\n    }\n\n    // reorder\n    node.insertBefore(newChild, oldChild);\n  }\n}\n"], "mappings": ";;;;;AAeO,SAAS,sBAAsB,OAAO,QAAQ,YAAY,UAAU;AAEzE,MAAI;AACJ,MAAI,cAAc,WAAW,QAAQ;AACnC,aAAS,WAAW,MAAM;AAAA,EAC5B,WAAW,yBAAyB;AAClC,aAAS,IAAI,gBAAgB,SAAS,KAAK,UAAU,GAAG;AAAA,EAC1D,OAAO;AACL,aAAS,SAAS,cAAc,QAAQ;AAAA,EAC1C;AACA,MAAI,OAAO;AACT,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,QAAQ;AACV,WAAO,SAAS;AAAA,EAClB;AAEA;AAAA;AAAA,IACE,OAAO,WAAW,MAAM,QAAQ;AAAA;AAEpC;AAOO,SAAS,cAAc,SAAS;AACrC,QAAM,SAAS,QAAQ;AACvB,SAAO,QAAQ;AACf,SAAO,SAAS;AAChB,UAAQ,UAAU,GAAG,GAAG,GAAG,CAAC;AAC9B;AASO,SAAS,WAAW,SAAS;AAClC,MAAI,QAAQ,QAAQ;AACpB,QAAM,QAAQ,iBAAiB,OAAO;AACtC,WAAS,SAAS,MAAM,YAAY,EAAE,IAAI,SAAS,MAAM,aAAa,EAAE;AAExE,SAAO;AACT;AASO,SAAS,YAAY,SAAS;AACnC,MAAI,SAAS,QAAQ;AACrB,QAAM,QAAQ,iBAAiB,OAAO;AACtC,YAAU,SAAS,MAAM,WAAW,EAAE,IAAI,SAAS,MAAM,cAAc,EAAE;AAEzE,SAAO;AACT;AAMO,SAAS,YAAY,SAAS,SAAS;AAC5C,QAAM,SAAS,QAAQ;AACvB,MAAI,QAAQ;AACV,WAAO,aAAa,SAAS,OAAO;AAAA,EACtC;AACF;AAMO,SAAS,WAAW,MAAM;AAC/B,SAAO,QAAQ,KAAK,aAAa,KAAK,WAAW,YAAY,IAAI,IAAI;AACvE;AAKO,SAAS,eAAe,MAAM;AACnC,SAAO,KAAK,WAAW;AACrB,SAAK,YAAY,KAAK,SAAS;AAAA,EACjC;AACF;AAUO,SAAS,gBAAgB,MAAM,UAAU;AAC9C,QAAM,cAAc,KAAK;AAEzB,WAAS,IAAI,GAAG,MAAM,EAAE,GAAG;AACzB,UAAM,WAAW,YAAY,CAAC;AAC9B,UAAM,WAAW,SAAS,CAAC;AAG3B,QAAI,CAAC,YAAY,CAAC,UAAU;AAC1B;AAAA,IACF;AAGA,QAAI,aAAa,UAAU;AACzB;AAAA,IACF;AAGA,QAAI,CAAC,UAAU;AACb,WAAK,YAAY,QAAQ;AACzB;AAAA,IACF;AAGA,QAAI,CAAC,UAAU;AACb,WAAK,YAAY,QAAQ;AACzB,QAAE;AACF;AAAA,IACF;AAGA,SAAK,aAAa,UAAU,QAAQ;AAAA,EACtC;AACF;", "names": []}