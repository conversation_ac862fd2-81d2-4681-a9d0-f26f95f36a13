import axios from 'axios';

const createService = (customConfig = {}) => {
  // 核心配置对象‌:ml-citation{ref="3,5" data="citationList"}
  const baseConfig = {
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json'
    },
    ...customConfig
  };

  // 创建实例时允许覆盖所有配置‌:ml-citation{ref="6,8" data="citationList"}
  const instance = axios.create(baseConfig);

  // 请求拦截器（认证处理）‌:ml-citation{ref="1,4" data="citationList"}
  instance.interceptors.request.use(config => {
    const token = localStorage.getItem('access_token');
    if (token) config.headers.Authorization = `Bearer ${token}`;
    return config;
  }, error => Promise.reject(error));

  // 响应拦截器（错误处理）‌:ml-citation{ref="2,4" data="citationList"}
  instance.interceptors.response.use(
    response => response.data,
    error => {
      if (error.response?.status === 401) {
        window.location.replace('/auth/login');
      }
      return Promise.reject(error.response?.data || error.message)
    }
  );

  return instance;
};

// 创建默认实例（不预设baseURL）‌:ml-citation{ref="3,8" data="citationList"}
const http = createService();

// 请求方法封装（支持完整URL或相对路径）‌:ml-citation{ref="3,7" data="citationList"}
export default {
  get: (url, params, config) => http.get(url, { ...config, params }),
  post: (url, data, config) => http.post(url, data, config),
  put: (url, data, config) => http.put(url, data, config),
  delete: (url, config) => http.delete(url, config),
  
  // 创建带特定baseURL的子实例‌:ml-citation{ref="6,8" data="citationList"}
  createSubInstance: (baseURL) => createService({ baseURL })
};
