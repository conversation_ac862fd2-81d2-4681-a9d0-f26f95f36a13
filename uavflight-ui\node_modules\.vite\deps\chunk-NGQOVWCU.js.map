{"version": 3, "sources": ["../../ol/renderer/canvas/VectorLayer.js", "../../ol/layer/Vector.js"], "sourcesContent": ["/**\n * @module ol/renderer/canvas/VectorLayer\n */\nimport CanvasBuilderGroup from '../../render/canvas/BuilderGroup.js';\nimport CanvasLayerRenderer, {canvasPool} from './Layer.js';\nimport ExecutorGroup from '../../render/canvas/ExecutorGroup.js';\nimport RenderEventType from '../../render/EventType.js';\nimport ViewHint from '../../ViewHint.js';\nimport {\n  HIT_DETECT_RESOLUTION,\n  createHitDetectionImageData,\n  hitDetect,\n} from '../../render/canvas/hitdetect.js';\nimport {\n  apply,\n  makeInverse,\n  makeScale,\n  toString as transformToString,\n} from '../../transform.js';\nimport {\n  buffer,\n  containsExtent,\n  createEmpty,\n  getWidth,\n  intersects as intersectsExtent,\n  wrapX as wrapExtentX,\n} from '../../extent.js';\nimport {createCanvasContext2D, releaseCanvas} from '../../dom.js';\nimport {\n  defaultOrder as defaultRenderOrder,\n  getTolerance as getRenderTolerance,\n  getSquaredTolerance as getSquaredRenderTolerance,\n  renderFeature,\n} from '../vector.js';\nimport {equals} from '../../array.js';\nimport {\n  fromUserExtent,\n  getTransformFromProjections,\n  getUserProjection,\n  toUserExtent,\n  toUserResolution,\n} from '../../proj.js';\nimport {getUid} from '../../util.js';\nimport {wrapX as wrapCoordinateX} from '../../coordinate.js';\n\n/**\n * @classdesc\n * Canvas renderer for vector layers.\n * @api\n */\nclass CanvasVectorLayerRenderer extends CanvasLayerRenderer {\n  /**\n   * @param {import(\"../../layer/BaseVector.js\").default} vectorLayer Vector layer.\n   */\n  constructor(vectorLayer) {\n    super(vectorLayer);\n\n    /** @private */\n    this.boundHandleStyleImageChange_ = this.handleStyleImageChange_.bind(this);\n\n    /**\n     * @type {boolean}\n     */\n    this.animatingOrInteracting_;\n\n    /**\n     * @type {ImageData|null}\n     */\n    this.hitDetectionImageData_ = null;\n\n    /**\n     * @type {Array<import(\"../../Feature.js\").default>}\n     */\n    this.renderedFeatures_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.renderedRevision_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.renderedResolution_ = NaN;\n\n    /**\n     * @private\n     * @type {import(\"../../extent.js\").Extent}\n     */\n    this.renderedExtent_ = createEmpty();\n\n    /**\n     * @private\n     * @type {import(\"../../extent.js\").Extent}\n     */\n    this.wrappedRenderedExtent_ = createEmpty();\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.renderedRotation_;\n\n    /**\n     * @private\n     * @type {import(\"../../coordinate\").Coordinate}\n     */\n    this.renderedCenter_ = null;\n\n    /**\n     * @private\n     * @type {import(\"../../proj/Projection\").default}\n     */\n    this.renderedProjection_ = null;\n\n    /**\n     * @private\n     * @type {function(import(\"../../Feature.js\").default, import(\"../../Feature.js\").default): number|null}\n     */\n    this.renderedRenderOrder_ = null;\n\n    /**\n     * @private\n     * @type {import(\"../../render/canvas/ExecutorGroup\").default}\n     */\n    this.replayGroup_ = null;\n\n    /**\n     * A new replay group had to be created by `prepareFrame()`\n     * @type {boolean}\n     */\n    this.replayGroupChanged = true;\n\n    /**\n     * @type {import(\"../../render/canvas/ExecutorGroup\").default}\n     */\n    this.declutterExecutorGroup = null;\n\n    /**\n     * Clipping to be performed by `renderFrame()`\n     * @type {boolean}\n     */\n    this.clipping = true;\n\n    /**\n     * @private\n     * @type {CanvasRenderingContext2D}\n     */\n    this.compositionContext_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.opacity_ = 1;\n  }\n\n  /**\n   * @param {ExecutorGroup} executorGroup Executor group.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @param {import(\"rbush\").default} [declutterTree] Declutter tree.\n   */\n  renderWorlds(executorGroup, frameState, declutterTree) {\n    const extent = frameState.extent;\n    const viewState = frameState.viewState;\n    const center = viewState.center;\n    const resolution = viewState.resolution;\n    const projection = viewState.projection;\n    const rotation = viewState.rotation;\n    const projectionExtent = projection.getExtent();\n    const vectorSource = this.getLayer().getSource();\n    const pixelRatio = frameState.pixelRatio;\n    const viewHints = frameState.viewHints;\n    const snapToPixel = !(\n      viewHints[ViewHint.ANIMATING] || viewHints[ViewHint.INTERACTING]\n    );\n    const context = this.compositionContext_;\n    const width = Math.round(frameState.size[0] * pixelRatio);\n    const height = Math.round(frameState.size[1] * pixelRatio);\n\n    const multiWorld = vectorSource.getWrapX() && projection.canWrapX();\n    const worldWidth = multiWorld ? getWidth(projectionExtent) : null;\n    const endWorld = multiWorld\n      ? Math.ceil((extent[2] - projectionExtent[2]) / worldWidth) + 1\n      : 1;\n    let world = multiWorld\n      ? Math.floor((extent[0] - projectionExtent[0]) / worldWidth)\n      : 0;\n    do {\n      const transform = this.getRenderTransform(\n        center,\n        resolution,\n        rotation,\n        pixelRatio,\n        width,\n        height,\n        world * worldWidth\n      );\n      executorGroup.execute(\n        context,\n        1,\n        transform,\n        rotation,\n        snapToPixel,\n        undefined,\n        declutterTree\n      );\n    } while (++world < endWorld);\n  }\n\n  setupCompositionContext_() {\n    if (this.opacity_ !== 1) {\n      const compositionContext = createCanvasContext2D(\n        this.context.canvas.width,\n        this.context.canvas.height,\n        canvasPool\n      );\n      this.compositionContext_ = compositionContext;\n    } else {\n      this.compositionContext_ = this.context;\n    }\n  }\n\n  releaseCompositionContext_() {\n    if (this.opacity_ !== 1) {\n      const alpha = this.context.globalAlpha;\n      this.context.globalAlpha = this.opacity_;\n      this.context.drawImage(this.compositionContext_.canvas, 0, 0);\n      this.context.globalAlpha = alpha;\n      releaseCanvas(this.compositionContext_);\n      canvasPool.push(this.compositionContext_.canvas);\n      this.compositionContext_ = null;\n    }\n  }\n\n  /**\n   * Render declutter items for this layer\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   */\n  renderDeclutter(frameState) {\n    if (this.declutterExecutorGroup) {\n      this.setupCompositionContext_();\n      this.renderWorlds(\n        this.declutterExecutorGroup,\n        frameState,\n        frameState.declutterTree\n      );\n      this.releaseCompositionContext_();\n    }\n  }\n\n  /**\n   * Render the layer.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @param {HTMLElement|null} target Target that may be used to render content to.\n   * @return {HTMLElement|null} The rendered element.\n   */\n  renderFrame(frameState, target) {\n    const pixelRatio = frameState.pixelRatio;\n    const layerState = frameState.layerStatesArray[frameState.layerIndex];\n\n    // set forward and inverse pixel transforms\n    makeScale(this.pixelTransform, 1 / pixelRatio, 1 / pixelRatio);\n    makeInverse(this.inversePixelTransform, this.pixelTransform);\n\n    const canvasTransform = transformToString(this.pixelTransform);\n\n    this.useContainer(target, canvasTransform, this.getBackground(frameState));\n    const context = this.context;\n    const canvas = context.canvas;\n\n    const replayGroup = this.replayGroup_;\n    const declutterExecutorGroup = this.declutterExecutorGroup;\n    let render =\n      (replayGroup && !replayGroup.isEmpty()) ||\n      (declutterExecutorGroup && !declutterExecutorGroup.isEmpty());\n    if (!render) {\n      const hasRenderListeners =\n        this.getLayer().hasListener(RenderEventType.PRERENDER) ||\n        this.getLayer().hasListener(RenderEventType.POSTRENDER);\n      if (!hasRenderListeners) {\n        return null;\n      }\n    }\n\n    // resize and clear\n    const width = Math.round(frameState.size[0] * pixelRatio);\n    const height = Math.round(frameState.size[1] * pixelRatio);\n    if (canvas.width != width || canvas.height != height) {\n      canvas.width = width;\n      canvas.height = height;\n      if (canvas.style.transform !== canvasTransform) {\n        canvas.style.transform = canvasTransform;\n      }\n    } else if (!this.containerReused) {\n      context.clearRect(0, 0, width, height);\n    }\n\n    this.preRender(context, frameState);\n\n    const viewState = frameState.viewState;\n    const projection = viewState.projection;\n\n    this.opacity_ = layerState.opacity;\n    this.setupCompositionContext_();\n\n    // clipped rendering if layer extent is set\n    let clipped = false;\n    if (render && layerState.extent && this.clipping) {\n      const layerExtent = fromUserExtent(layerState.extent, projection);\n      render = intersectsExtent(layerExtent, frameState.extent);\n      clipped = render && !containsExtent(layerExtent, frameState.extent);\n      if (clipped) {\n        this.clipUnrotated(this.compositionContext_, frameState, layerExtent);\n      }\n    }\n\n    if (render) {\n      this.renderWorlds(replayGroup, frameState);\n    }\n\n    if (clipped) {\n      this.compositionContext_.restore();\n    }\n\n    this.releaseCompositionContext_();\n\n    this.postRender(context, frameState);\n\n    if (this.renderedRotation_ !== viewState.rotation) {\n      this.renderedRotation_ = viewState.rotation;\n      this.hitDetectionImageData_ = null;\n    }\n    return this.container;\n  }\n\n  /**\n   * Asynchronous layer level hit detection.\n   * @param {import(\"../../pixel.js\").Pixel} pixel Pixel.\n   * @return {Promise<Array<import(\"../../Feature\").default>>} Promise\n   * that resolves with an array of features.\n   */\n  getFeatures(pixel) {\n    return new Promise((resolve) => {\n      if (!this.hitDetectionImageData_ && !this.animatingOrInteracting_) {\n        const size = [this.context.canvas.width, this.context.canvas.height];\n        apply(this.pixelTransform, size);\n        const center = this.renderedCenter_;\n        const resolution = this.renderedResolution_;\n        const rotation = this.renderedRotation_;\n        const projection = this.renderedProjection_;\n        const extent = this.wrappedRenderedExtent_;\n        const layer = this.getLayer();\n        const transforms = [];\n        const width = size[0] * HIT_DETECT_RESOLUTION;\n        const height = size[1] * HIT_DETECT_RESOLUTION;\n        transforms.push(\n          this.getRenderTransform(\n            center,\n            resolution,\n            rotation,\n            HIT_DETECT_RESOLUTION,\n            width,\n            height,\n            0\n          ).slice()\n        );\n        const source = layer.getSource();\n        const projectionExtent = projection.getExtent();\n        if (\n          source.getWrapX() &&\n          projection.canWrapX() &&\n          !containsExtent(projectionExtent, extent)\n        ) {\n          let startX = extent[0];\n          const worldWidth = getWidth(projectionExtent);\n          let world = 0;\n          let offsetX;\n          while (startX < projectionExtent[0]) {\n            --world;\n            offsetX = worldWidth * world;\n            transforms.push(\n              this.getRenderTransform(\n                center,\n                resolution,\n                rotation,\n                HIT_DETECT_RESOLUTION,\n                width,\n                height,\n                offsetX\n              ).slice()\n            );\n            startX += worldWidth;\n          }\n          world = 0;\n          startX = extent[2];\n          while (startX > projectionExtent[2]) {\n            ++world;\n            offsetX = worldWidth * world;\n            transforms.push(\n              this.getRenderTransform(\n                center,\n                resolution,\n                rotation,\n                HIT_DETECT_RESOLUTION,\n                width,\n                height,\n                offsetX\n              ).slice()\n            );\n            startX -= worldWidth;\n          }\n        }\n\n        this.hitDetectionImageData_ = createHitDetectionImageData(\n          size,\n          transforms,\n          this.renderedFeatures_,\n          layer.getStyleFunction(),\n          extent,\n          resolution,\n          rotation\n        );\n      }\n      resolve(\n        hitDetect(pixel, this.renderedFeatures_, this.hitDetectionImageData_)\n      );\n    });\n  }\n\n  /**\n   * @param {import(\"../../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @param {number} hitTolerance Hit tolerance in pixels.\n   * @param {import(\"../vector.js\").FeatureCallback<T>} callback Feature callback.\n   * @param {Array<import(\"../Map.js\").HitMatch<T>>} matches The hit detected matches with tolerance.\n   * @return {T|undefined} Callback result.\n   * @template T\n   */\n  forEachFeatureAtCoordinate(\n    coordinate,\n    frameState,\n    hitTolerance,\n    callback,\n    matches\n  ) {\n    if (!this.replayGroup_) {\n      return undefined;\n    }\n    const resolution = frameState.viewState.resolution;\n    const rotation = frameState.viewState.rotation;\n    const layer = this.getLayer();\n\n    /** @type {!Object<string, import(\"../Map.js\").HitMatch<T>|true>} */\n    const features = {};\n\n    /**\n     * @param {import(\"../../Feature.js\").FeatureLike} feature Feature.\n     * @param {import(\"../../geom/SimpleGeometry.js\").default} geometry Geometry.\n     * @param {number} distanceSq The squared distance to the click position\n     * @return {T|undefined} Callback result.\n     */\n    const featureCallback = function (feature, geometry, distanceSq) {\n      const key = getUid(feature);\n      const match = features[key];\n      if (!match) {\n        if (distanceSq === 0) {\n          features[key] = true;\n          return callback(feature, layer, geometry);\n        }\n        matches.push(\n          (features[key] = {\n            feature: feature,\n            layer: layer,\n            geometry: geometry,\n            distanceSq: distanceSq,\n            callback: callback,\n          })\n        );\n      } else if (match !== true && distanceSq < match.distanceSq) {\n        if (distanceSq === 0) {\n          features[key] = true;\n          matches.splice(matches.lastIndexOf(match), 1);\n          return callback(feature, layer, geometry);\n        }\n        match.geometry = geometry;\n        match.distanceSq = distanceSq;\n      }\n      return undefined;\n    };\n\n    let result;\n    const executorGroups = [this.replayGroup_];\n    if (this.declutterExecutorGroup) {\n      executorGroups.push(this.declutterExecutorGroup);\n    }\n    executorGroups.some((executorGroup) => {\n      return (result = executorGroup.forEachFeatureAtCoordinate(\n        coordinate,\n        resolution,\n        rotation,\n        hitTolerance,\n        featureCallback,\n        executorGroup === this.declutterExecutorGroup &&\n          frameState.declutterTree\n          ? frameState.declutterTree.all().map((item) => item.value)\n          : null\n      ));\n    });\n\n    return result;\n  }\n\n  /**\n   * Perform action necessary to get the layer rendered after new fonts have loaded\n   */\n  handleFontsChanged() {\n    const layer = this.getLayer();\n    if (layer.getVisible() && this.replayGroup_) {\n      layer.changed();\n    }\n  }\n\n  /**\n   * Handle changes in image style state.\n   * @param {import(\"../../events/Event.js\").default} event Image style change event.\n   * @private\n   */\n  handleStyleImageChange_(event) {\n    this.renderIfReadyAndVisible();\n  }\n\n  /**\n   * Determine whether render should be called.\n   * @param {import(\"../../Map.js\").FrameState} frameState Frame state.\n   * @return {boolean} Layer is ready to be rendered.\n   */\n  prepareFrame(frameState) {\n    const vectorLayer = this.getLayer();\n    const vectorSource = vectorLayer.getSource();\n    if (!vectorSource) {\n      return false;\n    }\n\n    const animating = frameState.viewHints[ViewHint.ANIMATING];\n    const interacting = frameState.viewHints[ViewHint.INTERACTING];\n    const updateWhileAnimating = vectorLayer.getUpdateWhileAnimating();\n    const updateWhileInteracting = vectorLayer.getUpdateWhileInteracting();\n\n    if (\n      (this.ready && !updateWhileAnimating && animating) ||\n      (!updateWhileInteracting && interacting)\n    ) {\n      this.animatingOrInteracting_ = true;\n      return true;\n    }\n    this.animatingOrInteracting_ = false;\n\n    const frameStateExtent = frameState.extent;\n    const viewState = frameState.viewState;\n    const projection = viewState.projection;\n    const resolution = viewState.resolution;\n    const pixelRatio = frameState.pixelRatio;\n    const vectorLayerRevision = vectorLayer.getRevision();\n    const vectorLayerRenderBuffer = vectorLayer.getRenderBuffer();\n    let vectorLayerRenderOrder = vectorLayer.getRenderOrder();\n\n    if (vectorLayerRenderOrder === undefined) {\n      vectorLayerRenderOrder = defaultRenderOrder;\n    }\n\n    const center = viewState.center.slice();\n    const extent = buffer(\n      frameStateExtent,\n      vectorLayerRenderBuffer * resolution\n    );\n    const renderedExtent = extent.slice();\n    const loadExtents = [extent.slice()];\n    const projectionExtent = projection.getExtent();\n\n    if (\n      vectorSource.getWrapX() &&\n      projection.canWrapX() &&\n      !containsExtent(projectionExtent, frameState.extent)\n    ) {\n      // For the replay group, we need an extent that intersects the real world\n      // (-180° to +180°). To support geometries in a coordinate range from -540°\n      // to +540°, we add at least 1 world width on each side of the projection\n      // extent. If the viewport is wider than the world, we need to add half of\n      // the viewport width to make sure we cover the whole viewport.\n      const worldWidth = getWidth(projectionExtent);\n      const gutter = Math.max(getWidth(extent) / 2, worldWidth);\n      extent[0] = projectionExtent[0] - gutter;\n      extent[2] = projectionExtent[2] + gutter;\n      wrapCoordinateX(center, projection);\n      const loadExtent = wrapExtentX(loadExtents[0], projection);\n      // If the extent crosses the date line, we load data for both edges of the worlds\n      if (\n        loadExtent[0] < projectionExtent[0] &&\n        loadExtent[2] < projectionExtent[2]\n      ) {\n        loadExtents.push([\n          loadExtent[0] + worldWidth,\n          loadExtent[1],\n          loadExtent[2] + worldWidth,\n          loadExtent[3],\n        ]);\n      } else if (\n        loadExtent[0] > projectionExtent[0] &&\n        loadExtent[2] > projectionExtent[2]\n      ) {\n        loadExtents.push([\n          loadExtent[0] - worldWidth,\n          loadExtent[1],\n          loadExtent[2] - worldWidth,\n          loadExtent[3],\n        ]);\n      }\n    }\n\n    if (\n      this.ready &&\n      this.renderedResolution_ == resolution &&\n      this.renderedRevision_ == vectorLayerRevision &&\n      this.renderedRenderOrder_ == vectorLayerRenderOrder &&\n      containsExtent(this.wrappedRenderedExtent_, extent)\n    ) {\n      if (!equals(this.renderedExtent_, renderedExtent)) {\n        this.hitDetectionImageData_ = null;\n        this.renderedExtent_ = renderedExtent;\n      }\n      this.renderedCenter_ = center;\n      this.replayGroupChanged = false;\n      return true;\n    }\n\n    this.replayGroup_ = null;\n\n    const replayGroup = new CanvasBuilderGroup(\n      getRenderTolerance(resolution, pixelRatio),\n      extent,\n      resolution,\n      pixelRatio\n    );\n\n    let declutterBuilderGroup;\n    if (this.getLayer().getDeclutter()) {\n      declutterBuilderGroup = new CanvasBuilderGroup(\n        getRenderTolerance(resolution, pixelRatio),\n        extent,\n        resolution,\n        pixelRatio\n      );\n    }\n\n    const userProjection = getUserProjection();\n    let userTransform;\n    if (userProjection) {\n      for (let i = 0, ii = loadExtents.length; i < ii; ++i) {\n        const extent = loadExtents[i];\n        const userExtent = toUserExtent(extent, projection);\n        vectorSource.loadFeatures(\n          userExtent,\n          toUserResolution(resolution, projection),\n          userProjection\n        );\n      }\n      userTransform = getTransformFromProjections(userProjection, projection);\n    } else {\n      for (let i = 0, ii = loadExtents.length; i < ii; ++i) {\n        vectorSource.loadFeatures(loadExtents[i], resolution, projection);\n      }\n    }\n\n    const squaredTolerance = getSquaredRenderTolerance(resolution, pixelRatio);\n    let ready = true;\n    const render =\n      /**\n       * @param {import(\"../../Feature.js\").default} feature Feature.\n       */\n      (feature) => {\n        let styles;\n        const styleFunction =\n          feature.getStyleFunction() || vectorLayer.getStyleFunction();\n        if (styleFunction) {\n          styles = styleFunction(feature, resolution);\n        }\n        if (styles) {\n          const dirty = this.renderFeature(\n            feature,\n            squaredTolerance,\n            styles,\n            replayGroup,\n            userTransform,\n            declutterBuilderGroup\n          );\n          ready = ready && !dirty;\n        }\n      };\n\n    const userExtent = toUserExtent(extent, projection);\n    /** @type {Array<import(\"../../Feature.js\").default>} */\n    const features = vectorSource.getFeaturesInExtent(userExtent);\n    if (vectorLayerRenderOrder) {\n      features.sort(vectorLayerRenderOrder);\n    }\n    for (let i = 0, ii = features.length; i < ii; ++i) {\n      render(features[i]);\n    }\n    this.renderedFeatures_ = features;\n    this.ready = ready;\n\n    const replayGroupInstructions = replayGroup.finish();\n    const executorGroup = new ExecutorGroup(\n      extent,\n      resolution,\n      pixelRatio,\n      vectorSource.getOverlaps(),\n      replayGroupInstructions,\n      vectorLayer.getRenderBuffer()\n    );\n\n    if (declutterBuilderGroup) {\n      this.declutterExecutorGroup = new ExecutorGroup(\n        extent,\n        resolution,\n        pixelRatio,\n        vectorSource.getOverlaps(),\n        declutterBuilderGroup.finish(),\n        vectorLayer.getRenderBuffer()\n      );\n    }\n\n    this.renderedResolution_ = resolution;\n    this.renderedRevision_ = vectorLayerRevision;\n    this.renderedRenderOrder_ = vectorLayerRenderOrder;\n    this.renderedExtent_ = renderedExtent;\n    this.wrappedRenderedExtent_ = extent;\n    this.renderedCenter_ = center;\n    this.renderedProjection_ = projection;\n    this.replayGroup_ = executorGroup;\n    this.hitDetectionImageData_ = null;\n\n    this.replayGroupChanged = true;\n    return true;\n  }\n\n  /**\n   * @param {import(\"../../Feature.js\").default} feature Feature.\n   * @param {number} squaredTolerance Squared render tolerance.\n   * @param {import(\"../../style/Style.js\").default|Array<import(\"../../style/Style.js\").default>} styles The style or array of styles.\n   * @param {import(\"../../render/canvas/BuilderGroup.js\").default} builderGroup Builder group.\n   * @param {import(\"../../proj.js\").TransformFunction} [transform] Transform from user to view projection.\n   * @param {import(\"../../render/canvas/BuilderGroup.js\").default} [declutterBuilderGroup] Builder for decluttering.\n   * @return {boolean} `true` if an image is loading.\n   */\n  renderFeature(\n    feature,\n    squaredTolerance,\n    styles,\n    builderGroup,\n    transform,\n    declutterBuilderGroup\n  ) {\n    if (!styles) {\n      return false;\n    }\n    let loading = false;\n    if (Array.isArray(styles)) {\n      for (let i = 0, ii = styles.length; i < ii; ++i) {\n        loading =\n          renderFeature(\n            builderGroup,\n            feature,\n            styles[i],\n            squaredTolerance,\n            this.boundHandleStyleImageChange_,\n            transform,\n            declutterBuilderGroup\n          ) || loading;\n      }\n    } else {\n      loading = renderFeature(\n        builderGroup,\n        feature,\n        styles,\n        squaredTolerance,\n        this.boundHandleStyleImageChange_,\n        transform,\n        declutterBuilderGroup\n      );\n    }\n    return loading;\n  }\n}\n\nexport default CanvasVectorLayerRenderer;\n", "/**\n * @module ol/layer/Vector\n */\nimport BaseVectorLayer from './BaseVector.js';\nimport CanvasVectorLayerRenderer from '../renderer/canvas/VectorLayer.js';\n\n/**\n * @classdesc\n * Vector data is rendered client-side, as vectors. This layer type provides most accurate rendering\n * even during animations. Points and labels stay upright on rotated views. For very large\n * amounts of vector data, performance may suffer during pan and zoom animations. In this case,\n * try {@link module:ol/layer/VectorImage~VectorImageLayer}.\n *\n * Note that any property set in the options is set as a {@link module:ol/Object~BaseObject}\n * property on the layer object; for example, setting `title: 'My Title'` in the\n * options means that `title` is observable, and has get/set accessors.\n *\n * @template {import(\"../source/Vector.js\").default} VectorSourceType\n * @extends {BaseVectorLayer<VectorSourceType, CanvasVectorLayerRenderer>}\n * @api\n */\nclass VectorLayer extends BaseVectorLayer {\n  /**\n   * @param {import(\"./BaseVector.js\").Options<VectorSourceType>} [options] Options.\n   */\n  constructor(options) {\n    super(options);\n  }\n\n  createRenderer() {\n    return new CanvasVectorLayerRenderer(this);\n  }\n}\n\nexport default VectorLayer;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA,IAAM,4BAAN,cAAwC,cAAoB;AAAA;AAAA;AAAA;AAAA,EAI1D,YAAY,aAAa;AACvB,UAAM,WAAW;AAGjB,SAAK,+BAA+B,KAAK,wBAAwB,KAAK,IAAI;AAK1E,SAAK;AAKL,SAAK,yBAAyB;AAK9B,SAAK,oBAAoB;AAMzB,SAAK,oBAAoB;AAMzB,SAAK,sBAAsB;AAM3B,SAAK,kBAAkB,YAAY;AAMnC,SAAK,yBAAyB,YAAY;AAM1C,SAAK;AAML,SAAK,kBAAkB;AAMvB,SAAK,sBAAsB;AAM3B,SAAK,uBAAuB;AAM5B,SAAK,eAAe;AAMpB,SAAK,qBAAqB;AAK1B,SAAK,yBAAyB;AAM9B,SAAK,WAAW;AAMhB,SAAK,sBAAsB;AAM3B,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,eAAe,YAAY,eAAe;AACrD,UAAM,SAAS,WAAW;AAC1B,UAAM,YAAY,WAAW;AAC7B,UAAM,SAAS,UAAU;AACzB,UAAM,aAAa,UAAU;AAC7B,UAAM,aAAa,UAAU;AAC7B,UAAM,WAAW,UAAU;AAC3B,UAAM,mBAAmB,WAAW,UAAU;AAC9C,UAAM,eAAe,KAAK,SAAS,EAAE,UAAU;AAC/C,UAAM,aAAa,WAAW;AAC9B,UAAM,YAAY,WAAW;AAC7B,UAAM,cAAc,EAClB,UAAU,iBAAS,SAAS,KAAK,UAAU,iBAAS,WAAW;AAEjE,UAAM,UAAU,KAAK;AACrB,UAAM,QAAQ,KAAK,MAAM,WAAW,KAAK,CAAC,IAAI,UAAU;AACxD,UAAM,SAAS,KAAK,MAAM,WAAW,KAAK,CAAC,IAAI,UAAU;AAEzD,UAAM,aAAa,aAAa,SAAS,KAAK,WAAW,SAAS;AAClE,UAAM,aAAa,aAAa,SAAS,gBAAgB,IAAI;AAC7D,UAAM,WAAW,aACb,KAAK,MAAM,OAAO,CAAC,IAAI,iBAAiB,CAAC,KAAK,UAAU,IAAI,IAC5D;AACJ,QAAI,QAAQ,aACR,KAAK,OAAO,OAAO,CAAC,IAAI,iBAAiB,CAAC,KAAK,UAAU,IACzD;AACJ,OAAG;AACD,YAAM,YAAY,KAAK;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,MACV;AACA,oBAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,SAAS,EAAE,QAAQ;AAAA,EACrB;AAAA,EAEA,2BAA2B;AACzB,QAAI,KAAK,aAAa,GAAG;AACvB,YAAM,qBAAqB;AAAA,QACzB,KAAK,QAAQ,OAAO;AAAA,QACpB,KAAK,QAAQ,OAAO;AAAA,QACpB;AAAA,MACF;AACA,WAAK,sBAAsB;AAAA,IAC7B,OAAO;AACL,WAAK,sBAAsB,KAAK;AAAA,IAClC;AAAA,EACF;AAAA,EAEA,6BAA6B;AAC3B,QAAI,KAAK,aAAa,GAAG;AACvB,YAAM,QAAQ,KAAK,QAAQ;AAC3B,WAAK,QAAQ,cAAc,KAAK;AAChC,WAAK,QAAQ,UAAU,KAAK,oBAAoB,QAAQ,GAAG,CAAC;AAC5D,WAAK,QAAQ,cAAc;AAC3B,oBAAc,KAAK,mBAAmB;AACtC,iBAAW,KAAK,KAAK,oBAAoB,MAAM;AAC/C,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,YAAY;AAC1B,QAAI,KAAK,wBAAwB;AAC/B,WAAK,yBAAyB;AAC9B,WAAK;AAAA,QACH,KAAK;AAAA,QACL;AAAA,QACA,WAAW;AAAA,MACb;AACA,WAAK,2BAA2B;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,YAAY,QAAQ;AAC9B,UAAM,aAAa,WAAW;AAC9B,UAAM,aAAa,WAAW,iBAAiB,WAAW,UAAU;AAGpE,cAAU,KAAK,gBAAgB,IAAI,YAAY,IAAI,UAAU;AAC7D,gBAAY,KAAK,uBAAuB,KAAK,cAAc;AAE3D,UAAM,kBAAkB,SAAkB,KAAK,cAAc;AAE7D,SAAK,aAAa,QAAQ,iBAAiB,KAAK,cAAc,UAAU,CAAC;AACzE,UAAM,UAAU,KAAK;AACrB,UAAM,SAAS,QAAQ;AAEvB,UAAM,cAAc,KAAK;AACzB,UAAM,yBAAyB,KAAK;AACpC,QAAI,SACD,eAAe,CAAC,YAAY,QAAQ,KACpC,0BAA0B,CAAC,uBAAuB,QAAQ;AAC7D,QAAI,CAAC,QAAQ;AACX,YAAM,qBACJ,KAAK,SAAS,EAAE,YAAY,kBAAgB,SAAS,KACrD,KAAK,SAAS,EAAE,YAAY,kBAAgB,UAAU;AACxD,UAAI,CAAC,oBAAoB;AACvB,eAAO;AAAA,MACT;AAAA,IACF;AAGA,UAAM,QAAQ,KAAK,MAAM,WAAW,KAAK,CAAC,IAAI,UAAU;AACxD,UAAM,SAAS,KAAK,MAAM,WAAW,KAAK,CAAC,IAAI,UAAU;AACzD,QAAI,OAAO,SAAS,SAAS,OAAO,UAAU,QAAQ;AACpD,aAAO,QAAQ;AACf,aAAO,SAAS;AAChB,UAAI,OAAO,MAAM,cAAc,iBAAiB;AAC9C,eAAO,MAAM,YAAY;AAAA,MAC3B;AAAA,IACF,WAAW,CAAC,KAAK,iBAAiB;AAChC,cAAQ,UAAU,GAAG,GAAG,OAAO,MAAM;AAAA,IACvC;AAEA,SAAK,UAAU,SAAS,UAAU;AAElC,UAAM,YAAY,WAAW;AAC7B,UAAM,aAAa,UAAU;AAE7B,SAAK,WAAW,WAAW;AAC3B,SAAK,yBAAyB;AAG9B,QAAI,UAAU;AACd,QAAI,UAAU,WAAW,UAAU,KAAK,UAAU;AAChD,YAAM,cAAc,eAAe,WAAW,QAAQ,UAAU;AAChE,eAAS,WAAiB,aAAa,WAAW,MAAM;AACxD,gBAAU,UAAU,CAAC,eAAe,aAAa,WAAW,MAAM;AAClE,UAAI,SAAS;AACX,aAAK,cAAc,KAAK,qBAAqB,YAAY,WAAW;AAAA,MACtE;AAAA,IACF;AAEA,QAAI,QAAQ;AACV,WAAK,aAAa,aAAa,UAAU;AAAA,IAC3C;AAEA,QAAI,SAAS;AACX,WAAK,oBAAoB,QAAQ;AAAA,IACnC;AAEA,SAAK,2BAA2B;AAEhC,SAAK,WAAW,SAAS,UAAU;AAEnC,QAAI,KAAK,sBAAsB,UAAU,UAAU;AACjD,WAAK,oBAAoB,UAAU;AACnC,WAAK,yBAAyB;AAAA,IAChC;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,OAAO;AACjB,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,UAAI,CAAC,KAAK,0BAA0B,CAAC,KAAK,yBAAyB;AACjE,cAAM,OAAO,CAAC,KAAK,QAAQ,OAAO,OAAO,KAAK,QAAQ,OAAO,MAAM;AACnE,cAAM,KAAK,gBAAgB,IAAI;AAC/B,cAAM,SAAS,KAAK;AACpB,cAAM,aAAa,KAAK;AACxB,cAAM,WAAW,KAAK;AACtB,cAAM,aAAa,KAAK;AACxB,cAAM,SAAS,KAAK;AACpB,cAAM,QAAQ,KAAK,SAAS;AAC5B,cAAM,aAAa,CAAC;AACpB,cAAM,QAAQ,KAAK,CAAC,IAAI;AACxB,cAAM,SAAS,KAAK,CAAC,IAAI;AACzB,mBAAW;AAAA,UACT,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,EAAE,MAAM;AAAA,QACV;AACA,cAAM,SAAS,MAAM,UAAU;AAC/B,cAAM,mBAAmB,WAAW,UAAU;AAC9C,YACE,OAAO,SAAS,KAChB,WAAW,SAAS,KACpB,CAAC,eAAe,kBAAkB,MAAM,GACxC;AACA,cAAI,SAAS,OAAO,CAAC;AACrB,gBAAM,aAAa,SAAS,gBAAgB;AAC5C,cAAI,QAAQ;AACZ,cAAI;AACJ,iBAAO,SAAS,iBAAiB,CAAC,GAAG;AACnC,cAAE;AACF,sBAAU,aAAa;AACvB,uBAAW;AAAA,cACT,KAAK;AAAA,gBACH;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACF,EAAE,MAAM;AAAA,YACV;AACA,sBAAU;AAAA,UACZ;AACA,kBAAQ;AACR,mBAAS,OAAO,CAAC;AACjB,iBAAO,SAAS,iBAAiB,CAAC,GAAG;AACnC,cAAE;AACF,sBAAU,aAAa;AACvB,uBAAW;AAAA,cACT,KAAK;AAAA,gBACH;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACF,EAAE,MAAM;AAAA,YACV;AACA,sBAAU;AAAA,UACZ;AAAA,QACF;AAEA,aAAK,yBAAyB;AAAA,UAC5B;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,MAAM,iBAAiB;AAAA,UACvB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA;AAAA,QACE,UAAU,OAAO,KAAK,mBAAmB,KAAK,sBAAsB;AAAA,MACtE;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,2BACE,YACA,YACA,cACA,UACA,SACA;AACA,QAAI,CAAC,KAAK,cAAc;AACtB,aAAO;AAAA,IACT;AACA,UAAM,aAAa,WAAW,UAAU;AACxC,UAAM,WAAW,WAAW,UAAU;AACtC,UAAM,QAAQ,KAAK,SAAS;AAG5B,UAAM,WAAW,CAAC;AAQlB,UAAM,kBAAkB,SAAU,SAAS,UAAU,YAAY;AAC/D,YAAM,MAAM,OAAO,OAAO;AAC1B,YAAM,QAAQ,SAAS,GAAG;AAC1B,UAAI,CAAC,OAAO;AACV,YAAI,eAAe,GAAG;AACpB,mBAAS,GAAG,IAAI;AAChB,iBAAO,SAAS,SAAS,OAAO,QAAQ;AAAA,QAC1C;AACA,gBAAQ;AAAA,UACL,SAAS,GAAG,IAAI;AAAA,YACf;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF,WAAW,UAAU,QAAQ,aAAa,MAAM,YAAY;AAC1D,YAAI,eAAe,GAAG;AACpB,mBAAS,GAAG,IAAI;AAChB,kBAAQ,OAAO,QAAQ,YAAY,KAAK,GAAG,CAAC;AAC5C,iBAAO,SAAS,SAAS,OAAO,QAAQ;AAAA,QAC1C;AACA,cAAM,WAAW;AACjB,cAAM,aAAa;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AAEA,QAAI;AACJ,UAAM,iBAAiB,CAAC,KAAK,YAAY;AACzC,QAAI,KAAK,wBAAwB;AAC/B,qBAAe,KAAK,KAAK,sBAAsB;AAAA,IACjD;AACA,mBAAe,KAAK,CAAC,kBAAkB;AACrC,aAAQ,SAAS,cAAc;AAAA,QAC7B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,kBAAkB,KAAK,0BACrB,WAAW,gBACT,WAAW,cAAc,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,KAAK,IACvD;AAAA,MACN;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,UAAM,QAAQ,KAAK,SAAS;AAC5B,QAAI,MAAM,WAAW,KAAK,KAAK,cAAc;AAC3C,YAAM,QAAQ;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB,OAAO;AAC7B,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,YAAY;AACvB,UAAM,cAAc,KAAK,SAAS;AAClC,UAAM,eAAe,YAAY,UAAU;AAC3C,QAAI,CAAC,cAAc;AACjB,aAAO;AAAA,IACT;AAEA,UAAM,YAAY,WAAW,UAAU,iBAAS,SAAS;AACzD,UAAM,cAAc,WAAW,UAAU,iBAAS,WAAW;AAC7D,UAAM,uBAAuB,YAAY,wBAAwB;AACjE,UAAM,yBAAyB,YAAY,0BAA0B;AAErE,QACG,KAAK,SAAS,CAAC,wBAAwB,aACvC,CAAC,0BAA0B,aAC5B;AACA,WAAK,0BAA0B;AAC/B,aAAO;AAAA,IACT;AACA,SAAK,0BAA0B;AAE/B,UAAM,mBAAmB,WAAW;AACpC,UAAM,YAAY,WAAW;AAC7B,UAAM,aAAa,UAAU;AAC7B,UAAM,aAAa,UAAU;AAC7B,UAAM,aAAa,WAAW;AAC9B,UAAM,sBAAsB,YAAY,YAAY;AACpD,UAAM,0BAA0B,YAAY,gBAAgB;AAC5D,QAAI,yBAAyB,YAAY,eAAe;AAExD,QAAI,2BAA2B,QAAW;AACxC,+BAAyB;AAAA,IAC3B;AAEA,UAAM,SAAS,UAAU,OAAO,MAAM;AACtC,UAAM,SAAS;AAAA,MACb;AAAA,MACA,0BAA0B;AAAA,IAC5B;AACA,UAAM,iBAAiB,OAAO,MAAM;AACpC,UAAM,cAAc,CAAC,OAAO,MAAM,CAAC;AACnC,UAAM,mBAAmB,WAAW,UAAU;AAE9C,QACE,aAAa,SAAS,KACtB,WAAW,SAAS,KACpB,CAAC,eAAe,kBAAkB,WAAW,MAAM,GACnD;AAMA,YAAM,aAAa,SAAS,gBAAgB;AAC5C,YAAM,SAAS,KAAK,IAAI,SAAS,MAAM,IAAI,GAAG,UAAU;AACxD,aAAO,CAAC,IAAI,iBAAiB,CAAC,IAAI;AAClC,aAAO,CAAC,IAAI,iBAAiB,CAAC,IAAI;AAClC,MAAAA,OAAgB,QAAQ,UAAU;AAClC,YAAM,aAAa,MAAY,YAAY,CAAC,GAAG,UAAU;AAEzD,UACE,WAAW,CAAC,IAAI,iBAAiB,CAAC,KAClC,WAAW,CAAC,IAAI,iBAAiB,CAAC,GAClC;AACA,oBAAY,KAAK;AAAA,UACf,WAAW,CAAC,IAAI;AAAA,UAChB,WAAW,CAAC;AAAA,UACZ,WAAW,CAAC,IAAI;AAAA,UAChB,WAAW,CAAC;AAAA,QACd,CAAC;AAAA,MACH,WACE,WAAW,CAAC,IAAI,iBAAiB,CAAC,KAClC,WAAW,CAAC,IAAI,iBAAiB,CAAC,GAClC;AACA,oBAAY,KAAK;AAAA,UACf,WAAW,CAAC,IAAI;AAAA,UAChB,WAAW,CAAC;AAAA,UACZ,WAAW,CAAC,IAAI;AAAA,UAChB,WAAW,CAAC;AAAA,QACd,CAAC;AAAA,MACH;AAAA,IACF;AAEA,QACE,KAAK,SACL,KAAK,uBAAuB,cAC5B,KAAK,qBAAqB,uBAC1B,KAAK,wBAAwB,0BAC7B,eAAe,KAAK,wBAAwB,MAAM,GAClD;AACA,UAAI,CAAC,OAAO,KAAK,iBAAiB,cAAc,GAAG;AACjD,aAAK,yBAAyB;AAC9B,aAAK,kBAAkB;AAAA,MACzB;AACA,WAAK,kBAAkB;AACvB,WAAK,qBAAqB;AAC1B,aAAO;AAAA,IACT;AAEA,SAAK,eAAe;AAEpB,UAAM,cAAc,IAAI;AAAA,MACtB,aAAmB,YAAY,UAAU;AAAA,MACzC;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAI;AACJ,QAAI,KAAK,SAAS,EAAE,aAAa,GAAG;AAClC,8BAAwB,IAAI;AAAA,QAC1B,aAAmB,YAAY,UAAU;AAAA,QACzC;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,UAAM,iBAAiB,kBAAkB;AACzC,QAAI;AACJ,QAAI,gBAAgB;AAClB,eAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,cAAMC,UAAS,YAAY,CAAC;AAC5B,cAAMC,cAAa,aAAaD,SAAQ,UAAU;AAClD,qBAAa;AAAA,UACXC;AAAA,UACA,iBAAiB,YAAY,UAAU;AAAA,UACvC;AAAA,QACF;AAAA,MACF;AACA,sBAAgB,4BAA4B,gBAAgB,UAAU;AAAA,IACxE,OAAO;AACL,eAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,qBAAa,aAAa,YAAY,CAAC,GAAG,YAAY,UAAU;AAAA,MAClE;AAAA,IACF;AAEA,UAAM,mBAAmB,oBAA0B,YAAY,UAAU;AACzE,QAAI,QAAQ;AACZ,UAAM;AAAA;AAAA;AAAA;AAAA,MAIJ,CAAC,YAAY;AACX,YAAI;AACJ,cAAM,gBACJ,QAAQ,iBAAiB,KAAK,YAAY,iBAAiB;AAC7D,YAAI,eAAe;AACjB,mBAAS,cAAc,SAAS,UAAU;AAAA,QAC5C;AACA,YAAI,QAAQ;AACV,gBAAM,QAAQ,KAAK;AAAA,YACjB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,kBAAQ,SAAS,CAAC;AAAA,QACpB;AAAA,MACF;AAAA;AAEF,UAAM,aAAa,aAAa,QAAQ,UAAU;AAElD,UAAM,WAAW,aAAa,oBAAoB,UAAU;AAC5D,QAAI,wBAAwB;AAC1B,eAAS,KAAK,sBAAsB;AAAA,IACtC;AACA,aAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,EAAE,GAAG;AACjD,aAAO,SAAS,CAAC,CAAC;AAAA,IACpB;AACA,SAAK,oBAAoB;AACzB,SAAK,QAAQ;AAEb,UAAM,0BAA0B,YAAY,OAAO;AACnD,UAAM,gBAAgB,IAAI;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,YAAY;AAAA,MACzB;AAAA,MACA,YAAY,gBAAgB;AAAA,IAC9B;AAEA,QAAI,uBAAuB;AACzB,WAAK,yBAAyB,IAAI;AAAA,QAChC;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa,YAAY;AAAA,QACzB,sBAAsB,OAAO;AAAA,QAC7B,YAAY,gBAAgB;AAAA,MAC9B;AAAA,IACF;AAEA,SAAK,sBAAsB;AAC3B,SAAK,oBAAoB;AACzB,SAAK,uBAAuB;AAC5B,SAAK,kBAAkB;AACvB,SAAK,yBAAyB;AAC9B,SAAK,kBAAkB;AACvB,SAAK,sBAAsB;AAC3B,SAAK,eAAe;AACpB,SAAK,yBAAyB;AAE9B,SAAK,qBAAqB;AAC1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,cACE,SACA,kBACA,QACA,cACA,WACA,uBACA;AACA,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,QAAI,UAAU;AACd,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,kBACE;AAAA,UACE;AAAA,UACA;AAAA,UACA,OAAO,CAAC;AAAA,UACR;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,QACF,KAAK;AAAA,MACT;AAAA,IACF,OAAO;AACL,gBAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAO,sBAAQ;;;ACzwBf,IAAM,cAAN,cAA0B,mBAAgB;AAAA;AAAA;AAAA;AAAA,EAIxC,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACf;AAAA,EAEA,iBAAiB;AACf,WAAO,IAAI,oBAA0B,IAAI;AAAA,EAC3C;AACF;AAEA,IAAO,iBAAQ;", "names": ["wrapX", "extent", "userExtent"]}