export default datums;
declare namespace datums {
    namespace wgs84 {
        let towgs84: string;
        let ellipse: string;
        let datumName: string;
    }
    namespace ch1903 {
        let towgs84_1: string;
        export { towgs84_1 as towgs84 };
        let ellipse_1: string;
        export { ellipse_1 as ellipse };
        let datumName_1: string;
        export { datumName_1 as datumName };
    }
    namespace ggrs87 {
        let towgs84_2: string;
        export { towgs84_2 as towgs84 };
        let ellipse_2: string;
        export { ellipse_2 as ellipse };
        let datumName_2: string;
        export { datumName_2 as datumName };
    }
    namespace nad83 {
        let towgs84_3: string;
        export { towgs84_3 as towgs84 };
        let ellipse_3: string;
        export { ellipse_3 as ellipse };
        let datumName_3: string;
        export { datumName_3 as datumName };
    }
    namespace nad27 {
        export let nadgrids: string;
        let ellipse_4: string;
        export { ellipse_4 as ellipse };
        let datumName_4: string;
        export { datumName_4 as datumName };
    }
    namespace potsdam {
        let towgs84_4: string;
        export { towgs84_4 as towgs84 };
        let ellipse_5: string;
        export { ellipse_5 as ellipse };
        let datumName_5: string;
        export { datumName_5 as datumName };
    }
    namespace carthage {
        let towgs84_5: string;
        export { towgs84_5 as towgs84 };
        let ellipse_6: string;
        export { ellipse_6 as ellipse };
        let datumName_6: string;
        export { datumName_6 as datumName };
    }
    namespace hermannskogel {
        let towgs84_6: string;
        export { towgs84_6 as towgs84 };
        let ellipse_7: string;
        export { ellipse_7 as ellipse };
        let datumName_7: string;
        export { datumName_7 as datumName };
    }
    namespace mgi {
        let towgs84_7: string;
        export { towgs84_7 as towgs84 };
        let ellipse_8: string;
        export { ellipse_8 as ellipse };
        let datumName_8: string;
        export { datumName_8 as datumName };
    }
    namespace osni52 {
        let towgs84_8: string;
        export { towgs84_8 as towgs84 };
        let ellipse_9: string;
        export { ellipse_9 as ellipse };
        let datumName_9: string;
        export { datumName_9 as datumName };
    }
    namespace ire65 {
        let towgs84_9: string;
        export { towgs84_9 as towgs84 };
        let ellipse_10: string;
        export { ellipse_10 as ellipse };
        let datumName_10: string;
        export { datumName_10 as datumName };
    }
    namespace rassadiran {
        let towgs84_10: string;
        export { towgs84_10 as towgs84 };
        let ellipse_11: string;
        export { ellipse_11 as ellipse };
        let datumName_11: string;
        export { datumName_11 as datumName };
    }
    namespace nzgd49 {
        let towgs84_11: string;
        export { towgs84_11 as towgs84 };
        let ellipse_12: string;
        export { ellipse_12 as ellipse };
        let datumName_12: string;
        export { datumName_12 as datumName };
    }
    namespace osgb36 {
        let towgs84_12: string;
        export { towgs84_12 as towgs84 };
        let ellipse_13: string;
        export { ellipse_13 as ellipse };
        let datumName_13: string;
        export { datumName_13 as datumName };
    }
    namespace s_jtsk {
        let towgs84_13: string;
        export { towgs84_13 as towgs84 };
        let ellipse_14: string;
        export { ellipse_14 as ellipse };
        let datumName_14: string;
        export { datumName_14 as datumName };
    }
    namespace beduaram {
        let towgs84_14: string;
        export { towgs84_14 as towgs84 };
        let ellipse_15: string;
        export { ellipse_15 as ellipse };
        let datumName_15: string;
        export { datumName_15 as datumName };
    }
    namespace gunung_segara {
        let towgs84_15: string;
        export { towgs84_15 as towgs84 };
        let ellipse_16: string;
        export { ellipse_16 as ellipse };
        let datumName_16: string;
        export { datumName_16 as datumName };
    }
    namespace rnb72 {
        let towgs84_16: string;
        export { towgs84_16 as towgs84 };
        let ellipse_17: string;
        export { ellipse_17 as ellipse };
        let datumName_17: string;
        export { datumName_17 as datumName };
    }
    namespace EPSG_5451 {
        let towgs84_17: string;
        export { towgs84_17 as towgs84 };
    }
    namespace IGNF_LURESG {
        let towgs84_18: string;
        export { towgs84_18 as towgs84 };
    }
    namespace EPSG_4614 {
        let towgs84_19: string;
        export { towgs84_19 as towgs84 };
    }
    namespace EPSG_4615 {
        let towgs84_20: string;
        export { towgs84_20 as towgs84 };
    }
    namespace ESRI_37241 {
        let towgs84_21: string;
        export { towgs84_21 as towgs84 };
    }
    namespace ESRI_37249 {
        let towgs84_22: string;
        export { towgs84_22 as towgs84 };
    }
    namespace ESRI_37245 {
        let towgs84_23: string;
        export { towgs84_23 as towgs84 };
    }
    namespace EPSG_4178 {
        let towgs84_24: string;
        export { towgs84_24 as towgs84 };
    }
    namespace EPSG_4622 {
        let towgs84_25: string;
        export { towgs84_25 as towgs84 };
    }
    namespace EPSG_4625 {
        let towgs84_26: string;
        export { towgs84_26 as towgs84 };
    }
    namespace EPSG_5252 {
        let towgs84_27: string;
        export { towgs84_27 as towgs84 };
    }
    namespace EPSG_4314 {
        let towgs84_28: string;
        export { towgs84_28 as towgs84 };
    }
    namespace EPSG_4282 {
        let towgs84_29: string;
        export { towgs84_29 as towgs84 };
    }
    namespace EPSG_4231 {
        let towgs84_30: string;
        export { towgs84_30 as towgs84 };
    }
    namespace EPSG_4274 {
        let towgs84_31: string;
        export { towgs84_31 as towgs84 };
    }
    namespace EPSG_4134 {
        let towgs84_32: string;
        export { towgs84_32 as towgs84 };
    }
    namespace EPSG_4254 {
        let towgs84_33: string;
        export { towgs84_33 as towgs84 };
    }
    namespace EPSG_4159 {
        let towgs84_34: string;
        export { towgs84_34 as towgs84 };
    }
    namespace EPSG_4687 {
        let towgs84_35: string;
        export { towgs84_35 as towgs84 };
    }
    namespace EPSG_4227 {
        let towgs84_36: string;
        export { towgs84_36 as towgs84 };
    }
    namespace EPSG_4746 {
        let towgs84_37: string;
        export { towgs84_37 as towgs84 };
    }
    namespace EPSG_4745 {
        let towgs84_38: string;
        export { towgs84_38 as towgs84 };
    }
    namespace EPSG_6311 {
        let towgs84_39: string;
        export { towgs84_39 as towgs84 };
    }
    namespace EPSG_4289 {
        let towgs84_40: string;
        export { towgs84_40 as towgs84 };
    }
    namespace EPSG_4230 {
        let towgs84_41: string;
        export { towgs84_41 as towgs84 };
    }
    namespace EPSG_4154 {
        let towgs84_42: string;
        export { towgs84_42 as towgs84 };
    }
    namespace EPSG_4156 {
        let towgs84_43: string;
        export { towgs84_43 as towgs84 };
    }
    namespace EPSG_4299 {
        let towgs84_44: string;
        export { towgs84_44 as towgs84 };
    }
    namespace EPSG_4179 {
        let towgs84_45: string;
        export { towgs84_45 as towgs84 };
    }
    namespace EPSG_4313 {
        let towgs84_46: string;
        export { towgs84_46 as towgs84 };
    }
    namespace EPSG_4194 {
        let towgs84_47: string;
        export { towgs84_47 as towgs84 };
    }
    namespace EPSG_4195 {
        let towgs84_48: string;
        export { towgs84_48 as towgs84 };
    }
    namespace EPSG_4196 {
        let towgs84_49: string;
        export { towgs84_49 as towgs84 };
    }
    namespace EPSG_4611 {
        let towgs84_50: string;
        export { towgs84_50 as towgs84 };
    }
    namespace EPSG_4633 {
        let towgs84_51: string;
        export { towgs84_51 as towgs84 };
    }
    namespace EPSG_4641 {
        let towgs84_52: string;
        export { towgs84_52 as towgs84 };
    }
    namespace EPSG_4643 {
        let towgs84_53: string;
        export { towgs84_53 as towgs84 };
    }
    namespace EPSG_4300 {
        let towgs84_54: string;
        export { towgs84_54 as towgs84 };
    }
    namespace EPSG_4188 {
        let towgs84_55: string;
        export { towgs84_55 as towgs84 };
    }
    namespace EPSG_4660 {
        let towgs84_56: string;
        export { towgs84_56 as towgs84 };
    }
    namespace EPSG_4662 {
        let towgs84_57: string;
        export { towgs84_57 as towgs84 };
    }
    namespace EPSG_3906 {
        let towgs84_58: string;
        export { towgs84_58 as towgs84 };
    }
    namespace EPSG_4307 {
        let towgs84_59: string;
        export { towgs84_59 as towgs84 };
    }
    namespace EPSG_6892 {
        let towgs84_60: string;
        export { towgs84_60 as towgs84 };
    }
    namespace EPSG_4690 {
        let towgs84_61: string;
        export { towgs84_61 as towgs84 };
    }
    namespace EPSG_4691 {
        let towgs84_62: string;
        export { towgs84_62 as towgs84 };
    }
    namespace EPSG_4629 {
        let towgs84_63: string;
        export { towgs84_63 as towgs84 };
    }
    namespace EPSG_4630 {
        let towgs84_64: string;
        export { towgs84_64 as towgs84 };
    }
    namespace EPSG_4692 {
        let towgs84_65: string;
        export { towgs84_65 as towgs84 };
    }
    namespace EPSG_9333 {
        let towgs84_66: string;
        export { towgs84_66 as towgs84 };
    }
    namespace EPSG_9059 {
        let towgs84_67: string;
        export { towgs84_67 as towgs84 };
    }
    namespace EPSG_4312 {
        let towgs84_68: string;
        export { towgs84_68 as towgs84 };
    }
    namespace EPSG_4123 {
        let towgs84_69: string;
        export { towgs84_69 as towgs84 };
    }
    namespace EPSG_4309 {
        let towgs84_70: string;
        export { towgs84_70 as towgs84 };
    }
    namespace ESRI_104106 {
        let towgs84_71: string;
        export { towgs84_71 as towgs84 };
    }
    namespace EPSG_4281 {
        let towgs84_72: string;
        export { towgs84_72 as towgs84 };
    }
    namespace EPSG_4322 {
        let towgs84_73: string;
        export { towgs84_73 as towgs84 };
    }
    namespace EPSG_4324 {
        let towgs84_74: string;
        export { towgs84_74 as towgs84 };
    }
    namespace EPSG_4284 {
        let towgs84_75: string;
        export { towgs84_75 as towgs84 };
    }
    namespace EPSG_4277 {
        let towgs84_76: string;
        export { towgs84_76 as towgs84 };
    }
    namespace EPSG_4207 {
        let towgs84_77: string;
        export { towgs84_77 as towgs84 };
    }
    namespace EPSG_4688 {
        let towgs84_78: string;
        export { towgs84_78 as towgs84 };
    }
    namespace EPSG_4689 {
        let towgs84_79: string;
        export { towgs84_79 as towgs84 };
    }
    namespace EPSG_4720 {
        let towgs84_80: string;
        export { towgs84_80 as towgs84 };
    }
    namespace EPSG_4273 {
        let towgs84_81: string;
        export { towgs84_81 as towgs84 };
    }
    namespace EPSG_4240 {
        let towgs84_82: string;
        export { towgs84_82 as towgs84 };
    }
    namespace EPSG_4817 {
        let towgs84_83: string;
        export { towgs84_83 as towgs84 };
    }
    namespace ESRI_104131 {
        let towgs84_84: string;
        export { towgs84_84 as towgs84 };
    }
    namespace EPSG_4265 {
        let towgs84_85: string;
        export { towgs84_85 as towgs84 };
    }
    namespace EPSG_4263 {
        let towgs84_86: string;
        export { towgs84_86 as towgs84 };
    }
    namespace EPSG_4298 {
        let towgs84_87: string;
        export { towgs84_87 as towgs84 };
    }
    namespace EPSG_4270 {
        let towgs84_88: string;
        export { towgs84_88 as towgs84 };
    }
    namespace EPSG_4229 {
        let towgs84_89: string;
        export { towgs84_89 as towgs84 };
    }
    namespace EPSG_4220 {
        let towgs84_90: string;
        export { towgs84_90 as towgs84 };
    }
    namespace EPSG_4214 {
        let towgs84_91: string;
        export { towgs84_91 as towgs84 };
    }
    namespace EPSG_4232 {
        let towgs84_92: string;
        export { towgs84_92 as towgs84 };
    }
    namespace EPSG_4238 {
        let towgs84_93: string;
        export { towgs84_93 as towgs84 };
    }
    namespace EPSG_4168 {
        let towgs84_94: string;
        export { towgs84_94 as towgs84 };
    }
    namespace EPSG_4131 {
        let towgs84_95: string;
        export { towgs84_95 as towgs84 };
    }
    namespace EPSG_4152 {
        let towgs84_96: string;
        export { towgs84_96 as towgs84 };
    }
    namespace EPSG_5228 {
        let towgs84_97: string;
        export { towgs84_97 as towgs84 };
    }
    namespace EPSG_8351 {
        let towgs84_98: string;
        export { towgs84_98 as towgs84 };
    }
    namespace EPSG_4683 {
        let towgs84_99: string;
        export { towgs84_99 as towgs84 };
    }
    namespace EPSG_4133 {
        let towgs84_100: string;
        export { towgs84_100 as towgs84 };
    }
    namespace EPSG_7373 {
        let towgs84_101: string;
        export { towgs84_101 as towgs84 };
    }
    namespace EPSG_9075 {
        let towgs84_102: string;
        export { towgs84_102 as towgs84 };
    }
    namespace EPSG_9072 {
        let towgs84_103: string;
        export { towgs84_103 as towgs84 };
    }
    namespace EPSG_9294 {
        let towgs84_104: string;
        export { towgs84_104 as towgs84 };
    }
    namespace EPSG_4212 {
        let towgs84_105: string;
        export { towgs84_105 as towgs84 };
    }
    namespace EPSG_4191 {
        let towgs84_106: string;
        export { towgs84_106 as towgs84 };
    }
    namespace EPSG_4237 {
        let towgs84_107: string;
        export { towgs84_107 as towgs84 };
    }
    namespace EPSG_4740 {
        let towgs84_108: string;
        export { towgs84_108 as towgs84 };
    }
    namespace EPSG_4124 {
        let towgs84_109: string;
        export { towgs84_109 as towgs84 };
    }
    namespace EPSG_5681 {
        let towgs84_110: string;
        export { towgs84_110 as towgs84 };
    }
    namespace EPSG_4141 {
        let towgs84_111: string;
        export { towgs84_111 as towgs84 };
    }
    namespace EPSG_4204 {
        let towgs84_112: string;
        export { towgs84_112 as towgs84 };
    }
    namespace EPSG_4319 {
        let towgs84_113: string;
        export { towgs84_113 as towgs84 };
    }
    namespace EPSG_4200 {
        let towgs84_114: string;
        export { towgs84_114 as towgs84 };
    }
    namespace EPSG_4130 {
        let towgs84_115: string;
        export { towgs84_115 as towgs84 };
    }
    namespace EPSG_4127 {
        let towgs84_116: string;
        export { towgs84_116 as towgs84 };
    }
    namespace EPSG_4149 {
        let towgs84_117: string;
        export { towgs84_117 as towgs84 };
    }
    namespace EPSG_4617 {
        let towgs84_118: string;
        export { towgs84_118 as towgs84 };
    }
    namespace EPSG_4663 {
        let towgs84_119: string;
        export { towgs84_119 as towgs84 };
    }
    namespace EPSG_4664 {
        let towgs84_120: string;
        export { towgs84_120 as towgs84 };
    }
    namespace EPSG_4665 {
        let towgs84_121: string;
        export { towgs84_121 as towgs84 };
    }
    namespace EPSG_4666 {
        let towgs84_122: string;
        export { towgs84_122 as towgs84 };
    }
    namespace EPSG_4756 {
        let towgs84_123: string;
        export { towgs84_123 as towgs84 };
    }
    namespace EPSG_4723 {
        let towgs84_124: string;
        export { towgs84_124 as towgs84 };
    }
    namespace EPSG_4726 {
        let towgs84_125: string;
        export { towgs84_125 as towgs84 };
    }
    namespace EPSG_4267 {
        let towgs84_126: string;
        export { towgs84_126 as towgs84 };
    }
    namespace EPSG_5365 {
        let towgs84_127: string;
        export { towgs84_127 as towgs84 };
    }
    namespace EPSG_4218 {
        let towgs84_128: string;
        export { towgs84_128 as towgs84 };
    }
    namespace EPSG_4242 {
        let towgs84_129: string;
        export { towgs84_129 as towgs84 };
    }
    namespace EPSG_4216 {
        let towgs84_130: string;
        export { towgs84_130 as towgs84 };
    }
    namespace ESRI_104105 {
        let towgs84_131: string;
        export { towgs84_131 as towgs84 };
    }
    namespace ESRI_104129 {
        let towgs84_132: string;
        export { towgs84_132 as towgs84 };
    }
    namespace EPSG_4673 {
        let towgs84_133: string;
        export { towgs84_133 as towgs84 };
    }
    namespace EPSG_4202 {
        let towgs84_134: string;
        export { towgs84_134 as towgs84 };
    }
    namespace EPSG_4203 {
        let towgs84_135: string;
        export { towgs84_135 as towgs84 };
    }
    namespace EPSG_3819 {
        let towgs84_136: string;
        export { towgs84_136 as towgs84 };
    }
    namespace EPSG_8694 {
        let towgs84_137: string;
        export { towgs84_137 as towgs84 };
    }
    namespace EPSG_4145 {
        let towgs84_138: string;
        export { towgs84_138 as towgs84 };
    }
    namespace EPSG_4283 {
        let towgs84_139: string;
        export { towgs84_139 as towgs84 };
    }
    namespace EPSG_4317 {
        let towgs84_140: string;
        export { towgs84_140 as towgs84 };
    }
    namespace EPSG_4272 {
        let towgs84_141: string;
        export { towgs84_141 as towgs84 };
    }
    namespace EPSG_4248 {
        let towgs84_142: string;
        export { towgs84_142 as towgs84 };
    }
    namespace EPSG_5561 {
        let towgs84_143: string;
        export { towgs84_143 as towgs84 };
    }
    namespace EPSG_5233 {
        let towgs84_144: string;
        export { towgs84_144 as towgs84 };
    }
    namespace ESRI_104130 {
        let towgs84_145: string;
        export { towgs84_145 as towgs84 };
    }
    namespace ESRI_104102 {
        let towgs84_146: string;
        export { towgs84_146 as towgs84 };
    }
    namespace ESRI_37207 {
        let towgs84_147: string;
        export { towgs84_147 as towgs84 };
    }
    namespace EPSG_4675 {
        let towgs84_148: string;
        export { towgs84_148 as towgs84 };
    }
    namespace ESRI_104109 {
        let towgs84_149: string;
        export { towgs84_149 as towgs84 };
    }
    namespace ESRI_104112 {
        let towgs84_150: string;
        export { towgs84_150 as towgs84 };
    }
    namespace ESRI_104113 {
        let towgs84_151: string;
        export { towgs84_151 as towgs84 };
    }
    namespace IGNF_WGS72G {
        let towgs84_152: string;
        export { towgs84_152 as towgs84 };
    }
    namespace IGNF_NTFG {
        let towgs84_153: string;
        export { towgs84_153 as towgs84 };
    }
    namespace IGNF_EFATE57G {
        let towgs84_154: string;
        export { towgs84_154 as towgs84 };
    }
    namespace IGNF_PGP50G {
        let towgs84_155: string;
        export { towgs84_155 as towgs84 };
    }
    namespace IGNF_REUN47G {
        let towgs84_156: string;
        export { towgs84_156 as towgs84 };
    }
    namespace IGNF_CSG67G {
        let towgs84_157: string;
        export { towgs84_157 as towgs84 };
    }
    namespace IGNF_GUAD48G {
        let towgs84_158: string;
        export { towgs84_158 as towgs84 };
    }
    namespace IGNF_TAHI51G {
        let towgs84_159: string;
        export { towgs84_159 as towgs84 };
    }
    namespace IGNF_TAHAAG {
        let towgs84_160: string;
        export { towgs84_160 as towgs84 };
    }
    namespace IGNF_NUKU72G {
        let towgs84_161: string;
        export { towgs84_161 as towgs84 };
    }
    namespace IGNF_PETRELS72G {
        let towgs84_162: string;
        export { towgs84_162 as towgs84 };
    }
    namespace IGNF_WALL78G {
        let towgs84_163: string;
        export { towgs84_163 as towgs84 };
    }
    namespace IGNF_MAYO50G {
        let towgs84_164: string;
        export { towgs84_164 as towgs84 };
    }
    namespace IGNF_TANNAG {
        let towgs84_165: string;
        export { towgs84_165 as towgs84 };
    }
    namespace IGNF_IGN72G {
        let towgs84_166: string;
        export { towgs84_166 as towgs84 };
    }
    namespace IGNF_ATIGG {
        let towgs84_167: string;
        export { towgs84_167 as towgs84 };
    }
    namespace IGNF_FANGA84G {
        let towgs84_168: string;
        export { towgs84_168 as towgs84 };
    }
    namespace IGNF_RUSAT84G {
        let towgs84_169: string;
        export { towgs84_169 as towgs84 };
    }
    namespace IGNF_KAUE70G {
        let towgs84_170: string;
        export { towgs84_170 as towgs84 };
    }
    namespace IGNF_MOP90G {
        let towgs84_171: string;
        export { towgs84_171 as towgs84 };
    }
    namespace IGNF_MHPF67G {
        let towgs84_172: string;
        export { towgs84_172 as towgs84 };
    }
    namespace IGNF_TAHI79G {
        let towgs84_173: string;
        export { towgs84_173 as towgs84 };
    }
    namespace IGNF_ANAA92G {
        let towgs84_174: string;
        export { towgs84_174 as towgs84 };
    }
    namespace IGNF_MARQUI72G {
        let towgs84_175: string;
        export { towgs84_175 as towgs84 };
    }
    namespace IGNF_APAT86G {
        let towgs84_176: string;
        export { towgs84_176 as towgs84 };
    }
    namespace IGNF_TUBU69G {
        let towgs84_177: string;
        export { towgs84_177 as towgs84 };
    }
    namespace IGNF_STPM50G {
        let towgs84_178: string;
        export { towgs84_178 as towgs84 };
    }
    namespace EPSG_4150 {
        let towgs84_179: string;
        export { towgs84_179 as towgs84 };
    }
    namespace EPSG_4754 {
        let towgs84_180: string;
        export { towgs84_180 as towgs84 };
    }
    namespace ESRI_104101 {
        let towgs84_181: string;
        export { towgs84_181 as towgs84 };
    }
    namespace EPSG_4693 {
        let towgs84_182: string;
        export { towgs84_182 as towgs84 };
    }
    namespace EPSG_6207 {
        let towgs84_183: string;
        export { towgs84_183 as towgs84 };
    }
    namespace EPSG_4153 {
        let towgs84_184: string;
        export { towgs84_184 as towgs84 };
    }
    namespace EPSG_4132 {
        let towgs84_185: string;
        export { towgs84_185 as towgs84 };
    }
    namespace EPSG_4221 {
        let towgs84_186: string;
        export { towgs84_186 as towgs84 };
    }
    namespace EPSG_4266 {
        let towgs84_187: string;
        export { towgs84_187 as towgs84 };
    }
    namespace EPSG_4193 {
        let towgs84_188: string;
        export { towgs84_188 as towgs84 };
    }
    namespace EPSG_5340 {
        let towgs84_189: string;
        export { towgs84_189 as towgs84 };
    }
    namespace EPSG_4246 {
        let towgs84_190: string;
        export { towgs84_190 as towgs84 };
    }
    namespace EPSG_4318 {
        let towgs84_191: string;
        export { towgs84_191 as towgs84 };
    }
    namespace EPSG_4121 {
        let towgs84_192: string;
        export { towgs84_192 as towgs84 };
    }
    namespace EPSG_4223 {
        let towgs84_193: string;
        export { towgs84_193 as towgs84 };
    }
    namespace EPSG_4158 {
        let towgs84_194: string;
        export { towgs84_194 as towgs84 };
    }
    namespace EPSG_4285 {
        let towgs84_195: string;
        export { towgs84_195 as towgs84 };
    }
    namespace EPSG_4613 {
        let towgs84_196: string;
        export { towgs84_196 as towgs84 };
    }
    namespace EPSG_4607 {
        let towgs84_197: string;
        export { towgs84_197 as towgs84 };
    }
    namespace EPSG_4475 {
        let towgs84_198: string;
        export { towgs84_198 as towgs84 };
    }
    namespace EPSG_4208 {
        let towgs84_199: string;
        export { towgs84_199 as towgs84 };
    }
    namespace EPSG_4743 {
        let towgs84_200: string;
        export { towgs84_200 as towgs84 };
    }
    namespace EPSG_4710 {
        let towgs84_201: string;
        export { towgs84_201 as towgs84 };
    }
    namespace EPSG_7881 {
        let towgs84_202: string;
        export { towgs84_202 as towgs84 };
    }
    namespace EPSG_4682 {
        let towgs84_203: string;
        export { towgs84_203 as towgs84 };
    }
    namespace EPSG_4739 {
        let towgs84_204: string;
        export { towgs84_204 as towgs84 };
    }
    namespace EPSG_4679 {
        let towgs84_205: string;
        export { towgs84_205 as towgs84 };
    }
    namespace EPSG_4750 {
        let towgs84_206: string;
        export { towgs84_206 as towgs84 };
    }
    namespace EPSG_4644 {
        let towgs84_207: string;
        export { towgs84_207 as towgs84 };
    }
    namespace EPSG_4695 {
        let towgs84_208: string;
        export { towgs84_208 as towgs84 };
    }
    namespace EPSG_4292 {
        let towgs84_209: string;
        export { towgs84_209 as towgs84 };
    }
    namespace EPSG_4302 {
        let towgs84_210: string;
        export { towgs84_210 as towgs84 };
    }
    namespace EPSG_4143 {
        let towgs84_211: string;
        export { towgs84_211 as towgs84 };
    }
    namespace EPSG_4606 {
        let towgs84_212: string;
        export { towgs84_212 as towgs84 };
    }
    namespace EPSG_4699 {
        let towgs84_213: string;
        export { towgs84_213 as towgs84 };
    }
    namespace EPSG_4247 {
        let towgs84_214: string;
        export { towgs84_214 as towgs84 };
    }
    namespace EPSG_4160 {
        let towgs84_215: string;
        export { towgs84_215 as towgs84 };
    }
    namespace EPSG_4161 {
        let towgs84_216: string;
        export { towgs84_216 as towgs84 };
    }
    namespace EPSG_9251 {
        let towgs84_217: string;
        export { towgs84_217 as towgs84 };
    }
    namespace EPSG_9253 {
        let towgs84_218: string;
        export { towgs84_218 as towgs84 };
    }
    namespace EPSG_4297 {
        let towgs84_219: string;
        export { towgs84_219 as towgs84 };
    }
    namespace EPSG_4269 {
        let towgs84_220: string;
        export { towgs84_220 as towgs84 };
    }
    namespace EPSG_4301 {
        let towgs84_221: string;
        export { towgs84_221 as towgs84 };
    }
    namespace EPSG_4618 {
        let towgs84_222: string;
        export { towgs84_222 as towgs84 };
    }
    namespace EPSG_4612 {
        let towgs84_223: string;
        export { towgs84_223 as towgs84 };
    }
    namespace EPSG_4678 {
        let towgs84_224: string;
        export { towgs84_224 as towgs84 };
    }
    namespace EPSG_4250 {
        let towgs84_225: string;
        export { towgs84_225 as towgs84 };
    }
    namespace EPSG_4144 {
        let towgs84_226: string;
        export { towgs84_226 as towgs84 };
    }
    namespace EPSG_4147 {
        let towgs84_227: string;
        export { towgs84_227 as towgs84 };
    }
    namespace EPSG_4259 {
        let towgs84_228: string;
        export { towgs84_228 as towgs84 };
    }
    namespace EPSG_4164 {
        let towgs84_229: string;
        export { towgs84_229 as towgs84 };
    }
    namespace EPSG_4211 {
        let towgs84_230: string;
        export { towgs84_230 as towgs84 };
    }
    namespace EPSG_4182 {
        let towgs84_231: string;
        export { towgs84_231 as towgs84 };
    }
    namespace EPSG_4224 {
        let towgs84_232: string;
        export { towgs84_232 as towgs84 };
    }
    namespace EPSG_4225 {
        let towgs84_233: string;
        export { towgs84_233 as towgs84 };
    }
    namespace EPSG_5527 {
        let towgs84_234: string;
        export { towgs84_234 as towgs84 };
    }
    namespace EPSG_4752 {
        let towgs84_235: string;
        export { towgs84_235 as towgs84 };
    }
    namespace EPSG_4310 {
        let towgs84_236: string;
        export { towgs84_236 as towgs84 };
    }
    namespace EPSG_9248 {
        let towgs84_237: string;
        export { towgs84_237 as towgs84 };
    }
    namespace EPSG_4680 {
        let towgs84_238: string;
        export { towgs84_238 as towgs84 };
    }
    namespace EPSG_4701 {
        let towgs84_239: string;
        export { towgs84_239 as towgs84 };
    }
    namespace EPSG_4706 {
        let towgs84_240: string;
        export { towgs84_240 as towgs84 };
    }
    namespace EPSG_4805 {
        let towgs84_241: string;
        export { towgs84_241 as towgs84 };
    }
    namespace EPSG_4201 {
        let towgs84_242: string;
        export { towgs84_242 as towgs84 };
    }
    namespace EPSG_4210 {
        let towgs84_243: string;
        export { towgs84_243 as towgs84 };
    }
    namespace EPSG_4183 {
        let towgs84_244: string;
        export { towgs84_244 as towgs84 };
    }
    namespace EPSG_4139 {
        let towgs84_245: string;
        export { towgs84_245 as towgs84 };
    }
    namespace EPSG_4668 {
        let towgs84_246: string;
        export { towgs84_246 as towgs84 };
    }
    namespace EPSG_4717 {
        let towgs84_247: string;
        export { towgs84_247 as towgs84 };
    }
    namespace EPSG_4732 {
        let towgs84_248: string;
        export { towgs84_248 as towgs84 };
    }
    namespace EPSG_4280 {
        let towgs84_249: string;
        export { towgs84_249 as towgs84 };
    }
    namespace EPSG_4209 {
        let towgs84_250: string;
        export { towgs84_250 as towgs84 };
    }
    namespace EPSG_4261 {
        let towgs84_251: string;
        export { towgs84_251 as towgs84 };
    }
    namespace EPSG_4658 {
        let towgs84_252: string;
        export { towgs84_252 as towgs84 };
    }
    namespace EPSG_4721 {
        let towgs84_253: string;
        export { towgs84_253 as towgs84 };
    }
    namespace EPSG_4222 {
        let towgs84_254: string;
        export { towgs84_254 as towgs84 };
    }
    namespace EPSG_4601 {
        let towgs84_255: string;
        export { towgs84_255 as towgs84 };
    }
    namespace EPSG_4602 {
        let towgs84_256: string;
        export { towgs84_256 as towgs84 };
    }
    namespace EPSG_4603 {
        let towgs84_257: string;
        export { towgs84_257 as towgs84 };
    }
    namespace EPSG_4605 {
        let towgs84_258: string;
        export { towgs84_258 as towgs84 };
    }
    namespace EPSG_4621 {
        let towgs84_259: string;
        export { towgs84_259 as towgs84 };
    }
    namespace EPSG_4657 {
        let towgs84_260: string;
        export { towgs84_260 as towgs84 };
    }
    namespace EPSG_4316 {
        let towgs84_261: string;
        export { towgs84_261 as towgs84 };
    }
    namespace EPSG_4642 {
        let towgs84_262: string;
        export { towgs84_262 as towgs84 };
    }
    namespace EPSG_4698 {
        let towgs84_263: string;
        export { towgs84_263 as towgs84 };
    }
    namespace EPSG_4192 {
        let towgs84_264: string;
        export { towgs84_264 as towgs84 };
    }
    namespace EPSG_4311 {
        let towgs84_265: string;
        export { towgs84_265 as towgs84 };
    }
    namespace EPSG_4135 {
        let towgs84_266: string;
        export { towgs84_266 as towgs84 };
    }
    namespace ESRI_104138 {
        let towgs84_267: string;
        export { towgs84_267 as towgs84 };
    }
    namespace EPSG_4245 {
        let towgs84_268: string;
        export { towgs84_268 as towgs84 };
    }
    namespace EPSG_4142 {
        let towgs84_269: string;
        export { towgs84_269 as towgs84 };
    }
    namespace EPSG_4213 {
        let towgs84_270: string;
        export { towgs84_270 as towgs84 };
    }
    namespace EPSG_4253 {
        let towgs84_271: string;
        export { towgs84_271 as towgs84 };
    }
    namespace EPSG_4129 {
        let towgs84_272: string;
        export { towgs84_272 as towgs84 };
    }
    namespace EPSG_4713 {
        let towgs84_273: string;
        export { towgs84_273 as towgs84 };
    }
    namespace EPSG_4239 {
        let towgs84_274: string;
        export { towgs84_274 as towgs84 };
    }
    namespace EPSG_4146 {
        let towgs84_275: string;
        export { towgs84_275 as towgs84 };
    }
    namespace EPSG_4155 {
        let towgs84_276: string;
        export { towgs84_276 as towgs84 };
    }
    namespace EPSG_4165 {
        let towgs84_277: string;
        export { towgs84_277 as towgs84 };
    }
    namespace EPSG_4672 {
        let towgs84_278: string;
        export { towgs84_278 as towgs84 };
    }
    namespace EPSG_4236 {
        let towgs84_279: string;
        export { towgs84_279 as towgs84 };
    }
    namespace EPSG_4251 {
        let towgs84_280: string;
        export { towgs84_280 as towgs84 };
    }
    namespace EPSG_4271 {
        let towgs84_281: string;
        export { towgs84_281 as towgs84 };
    }
    namespace EPSG_4175 {
        let towgs84_282: string;
        export { towgs84_282 as towgs84 };
    }
    namespace EPSG_4716 {
        let towgs84_283: string;
        export { towgs84_283 as towgs84 };
    }
    namespace EPSG_4315 {
        let towgs84_284: string;
        export { towgs84_284 as towgs84 };
    }
    namespace EPSG_4744 {
        let towgs84_285: string;
        export { towgs84_285 as towgs84 };
    }
    namespace EPSG_4244 {
        let towgs84_286: string;
        export { towgs84_286 as towgs84 };
    }
    namespace EPSG_4293 {
        let towgs84_287: string;
        export { towgs84_287 as towgs84 };
    }
    namespace EPSG_4714 {
        let towgs84_288: string;
        export { towgs84_288 as towgs84 };
    }
    namespace EPSG_4736 {
        let towgs84_289: string;
        export { towgs84_289 as towgs84 };
    }
    namespace EPSG_6883 {
        let towgs84_290: string;
        export { towgs84_290 as towgs84 };
    }
    namespace EPSG_6894 {
        let towgs84_291: string;
        export { towgs84_291 as towgs84 };
    }
    namespace EPSG_4205 {
        let towgs84_292: string;
        export { towgs84_292 as towgs84 };
    }
    namespace EPSG_4256 {
        let towgs84_293: string;
        export { towgs84_293 as towgs84 };
    }
    namespace EPSG_4262 {
        let towgs84_294: string;
        export { towgs84_294 as towgs84 };
    }
    namespace EPSG_4604 {
        let towgs84_295: string;
        export { towgs84_295 as towgs84 };
    }
    namespace EPSG_4169 {
        let towgs84_296: string;
        export { towgs84_296 as towgs84 };
    }
    namespace EPSG_4620 {
        let towgs84_297: string;
        export { towgs84_297 as towgs84 };
    }
    namespace EPSG_4184 {
        let towgs84_298: string;
        export { towgs84_298 as towgs84 };
    }
    namespace EPSG_4616 {
        let towgs84_299: string;
        export { towgs84_299 as towgs84 };
    }
    namespace EPSG_9403 {
        let towgs84_300: string;
        export { towgs84_300 as towgs84 };
    }
    namespace EPSG_4684 {
        let towgs84_301: string;
        export { towgs84_301 as towgs84 };
    }
    namespace EPSG_4708 {
        let towgs84_302: string;
        export { towgs84_302 as towgs84 };
    }
    namespace EPSG_4707 {
        let towgs84_303: string;
        export { towgs84_303 as towgs84 };
    }
    namespace EPSG_4709 {
        let towgs84_304: string;
        export { towgs84_304 as towgs84 };
    }
    namespace EPSG_4712 {
        let towgs84_305: string;
        export { towgs84_305 as towgs84 };
    }
    namespace EPSG_4711 {
        let towgs84_306: string;
        export { towgs84_306 as towgs84 };
    }
    namespace EPSG_4718 {
        let towgs84_307: string;
        export { towgs84_307 as towgs84 };
    }
    namespace EPSG_4719 {
        let towgs84_308: string;
        export { towgs84_308 as towgs84 };
    }
    namespace EPSG_4724 {
        let towgs84_309: string;
        export { towgs84_309 as towgs84 };
    }
    namespace EPSG_4725 {
        let towgs84_310: string;
        export { towgs84_310 as towgs84 };
    }
    namespace EPSG_4735 {
        let towgs84_311: string;
        export { towgs84_311 as towgs84 };
    }
    namespace EPSG_4722 {
        let towgs84_312: string;
        export { towgs84_312 as towgs84 };
    }
    namespace EPSG_4728 {
        let towgs84_313: string;
        export { towgs84_313 as towgs84 };
    }
    namespace EPSG_4734 {
        let towgs84_314: string;
        export { towgs84_314 as towgs84 };
    }
    namespace EPSG_4727 {
        let towgs84_315: string;
        export { towgs84_315 as towgs84 };
    }
    namespace EPSG_4729 {
        let towgs84_316: string;
        export { towgs84_316 as towgs84 };
    }
    namespace EPSG_4730 {
        let towgs84_317: string;
        export { towgs84_317 as towgs84 };
    }
    namespace EPSG_4733 {
        let towgs84_318: string;
        export { towgs84_318 as towgs84 };
    }
    namespace ESRI_37218 {
        let towgs84_319: string;
        export { towgs84_319 as towgs84 };
    }
    namespace ESRI_37240 {
        let towgs84_320: string;
        export { towgs84_320 as towgs84 };
    }
    namespace ESRI_37221 {
        let towgs84_321: string;
        export { towgs84_321 as towgs84 };
    }
    namespace ESRI_4305 {
        let towgs84_322: string;
        export { towgs84_322 as towgs84 };
    }
    namespace ESRI_104139 {
        let towgs84_323: string;
        export { towgs84_323 as towgs84 };
    }
    namespace EPSG_4748 {
        let towgs84_324: string;
        export { towgs84_324 as towgs84 };
    }
    namespace EPSG_4219 {
        let towgs84_325: string;
        export { towgs84_325 as towgs84 };
    }
    namespace EPSG_4255 {
        let towgs84_326: string;
        export { towgs84_326 as towgs84 };
    }
    namespace EPSG_4257 {
        let towgs84_327: string;
        export { towgs84_327 as towgs84 };
    }
    namespace EPSG_4646 {
        let towgs84_328: string;
        export { towgs84_328 as towgs84 };
    }
    namespace EPSG_6881 {
        let towgs84_329: string;
        export { towgs84_329 as towgs84 };
    }
    namespace EPSG_6882 {
        let towgs84_330: string;
        export { towgs84_330 as towgs84 };
    }
    namespace EPSG_4715 {
        let towgs84_331: string;
        export { towgs84_331 as towgs84 };
    }
    namespace IGNF_RGF93GDD {
        let towgs84_332: string;
        export { towgs84_332 as towgs84 };
    }
    namespace IGNF_RGM04GDD {
        let towgs84_333: string;
        export { towgs84_333 as towgs84 };
    }
    namespace IGNF_RGSPM06GDD {
        let towgs84_334: string;
        export { towgs84_334 as towgs84 };
    }
    namespace IGNF_RGTAAF07GDD {
        let towgs84_335: string;
        export { towgs84_335 as towgs84 };
    }
    namespace IGNF_RGFG95GDD {
        let towgs84_336: string;
        export { towgs84_336 as towgs84 };
    }
    namespace IGNF_RGNCG {
        let towgs84_337: string;
        export { towgs84_337 as towgs84 };
    }
    namespace IGNF_RGPFGDD {
        let towgs84_338: string;
        export { towgs84_338 as towgs84 };
    }
    namespace IGNF_ETRS89G {
        let towgs84_339: string;
        export { towgs84_339 as towgs84 };
    }
    namespace IGNF_RGR92GDD {
        let towgs84_340: string;
        export { towgs84_340 as towgs84 };
    }
    namespace EPSG_4173 {
        let towgs84_341: string;
        export { towgs84_341 as towgs84 };
    }
    namespace EPSG_4180 {
        let towgs84_342: string;
        export { towgs84_342 as towgs84 };
    }
    namespace EPSG_4619 {
        let towgs84_343: string;
        export { towgs84_343 as towgs84 };
    }
    namespace EPSG_4667 {
        let towgs84_344: string;
        export { towgs84_344 as towgs84 };
    }
    namespace EPSG_4075 {
        let towgs84_345: string;
        export { towgs84_345 as towgs84 };
    }
    namespace EPSG_6706 {
        let towgs84_346: string;
        export { towgs84_346 as towgs84 };
    }
    namespace EPSG_7798 {
        let towgs84_347: string;
        export { towgs84_347 as towgs84 };
    }
    namespace EPSG_4661 {
        let towgs84_348: string;
        export { towgs84_348 as towgs84 };
    }
    namespace EPSG_4669 {
        let towgs84_349: string;
        export { towgs84_349 as towgs84 };
    }
    namespace EPSG_8685 {
        let towgs84_350: string;
        export { towgs84_350 as towgs84 };
    }
    namespace EPSG_4151 {
        let towgs84_351: string;
        export { towgs84_351 as towgs84 };
    }
    namespace EPSG_9702 {
        let towgs84_352: string;
        export { towgs84_352 as towgs84 };
    }
    namespace EPSG_4758 {
        let towgs84_353: string;
        export { towgs84_353 as towgs84 };
    }
    namespace EPSG_4761 {
        let towgs84_354: string;
        export { towgs84_354 as towgs84 };
    }
    namespace EPSG_4765 {
        let towgs84_355: string;
        export { towgs84_355 as towgs84 };
    }
    namespace EPSG_8997 {
        let towgs84_356: string;
        export { towgs84_356 as towgs84 };
    }
    namespace EPSG_4023 {
        let towgs84_357: string;
        export { towgs84_357 as towgs84 };
    }
    namespace EPSG_4670 {
        let towgs84_358: string;
        export { towgs84_358 as towgs84 };
    }
    namespace EPSG_4694 {
        let towgs84_359: string;
        export { towgs84_359 as towgs84 };
    }
    namespace EPSG_4148 {
        let towgs84_360: string;
        export { towgs84_360 as towgs84 };
    }
    namespace EPSG_4163 {
        let towgs84_361: string;
        export { towgs84_361 as towgs84 };
    }
    namespace EPSG_4167 {
        let towgs84_362: string;
        export { towgs84_362 as towgs84 };
    }
    namespace EPSG_4189 {
        let towgs84_363: string;
        export { towgs84_363 as towgs84 };
    }
    namespace EPSG_4190 {
        let towgs84_364: string;
        export { towgs84_364 as towgs84 };
    }
    namespace EPSG_4176 {
        let towgs84_365: string;
        export { towgs84_365 as towgs84 };
    }
    namespace EPSG_4659 {
        let towgs84_366: string;
        export { towgs84_366 as towgs84 };
    }
    namespace EPSG_3824 {
        let towgs84_367: string;
        export { towgs84_367 as towgs84 };
    }
    namespace EPSG_3889 {
        let towgs84_368: string;
        export { towgs84_368 as towgs84 };
    }
    namespace EPSG_4046 {
        let towgs84_369: string;
        export { towgs84_369 as towgs84 };
    }
    namespace EPSG_4081 {
        let towgs84_370: string;
        export { towgs84_370 as towgs84 };
    }
    namespace EPSG_4558 {
        let towgs84_371: string;
        export { towgs84_371 as towgs84 };
    }
    namespace EPSG_4483 {
        let towgs84_372: string;
        export { towgs84_372 as towgs84 };
    }
    namespace EPSG_5013 {
        let towgs84_373: string;
        export { towgs84_373 as towgs84 };
    }
    namespace EPSG_5264 {
        let towgs84_374: string;
        export { towgs84_374 as towgs84 };
    }
    namespace EPSG_5324 {
        let towgs84_375: string;
        export { towgs84_375 as towgs84 };
    }
    namespace EPSG_5354 {
        let towgs84_376: string;
        export { towgs84_376 as towgs84 };
    }
    namespace EPSG_5371 {
        let towgs84_377: string;
        export { towgs84_377 as towgs84 };
    }
    namespace EPSG_5373 {
        let towgs84_378: string;
        export { towgs84_378 as towgs84 };
    }
    namespace EPSG_5381 {
        let towgs84_379: string;
        export { towgs84_379 as towgs84 };
    }
    namespace EPSG_5393 {
        let towgs84_380: string;
        export { towgs84_380 as towgs84 };
    }
    namespace EPSG_5489 {
        let towgs84_381: string;
        export { towgs84_381 as towgs84 };
    }
    namespace EPSG_5593 {
        let towgs84_382: string;
        export { towgs84_382 as towgs84 };
    }
    namespace EPSG_6135 {
        let towgs84_383: string;
        export { towgs84_383 as towgs84 };
    }
    namespace EPSG_6365 {
        let towgs84_384: string;
        export { towgs84_384 as towgs84 };
    }
    namespace EPSG_5246 {
        let towgs84_385: string;
        export { towgs84_385 as towgs84 };
    }
    namespace EPSG_7886 {
        let towgs84_386: string;
        export { towgs84_386 as towgs84 };
    }
    namespace EPSG_8431 {
        let towgs84_387: string;
        export { towgs84_387 as towgs84 };
    }
    namespace EPSG_8427 {
        let towgs84_388: string;
        export { towgs84_388 as towgs84 };
    }
    namespace EPSG_8699 {
        let towgs84_389: string;
        export { towgs84_389 as towgs84 };
    }
    namespace EPSG_8818 {
        let towgs84_390: string;
        export { towgs84_390 as towgs84 };
    }
    namespace EPSG_4757 {
        let towgs84_391: string;
        export { towgs84_391 as towgs84 };
    }
    namespace EPSG_9140 {
        let towgs84_392: string;
        export { towgs84_392 as towgs84 };
    }
    namespace EPSG_8086 {
        let towgs84_393: string;
        export { towgs84_393 as towgs84 };
    }
    namespace EPSG_4686 {
        let towgs84_394: string;
        export { towgs84_394 as towgs84 };
    }
    namespace EPSG_4737 {
        let towgs84_395: string;
        export { towgs84_395 as towgs84 };
    }
    namespace EPSG_4702 {
        let towgs84_396: string;
        export { towgs84_396 as towgs84 };
    }
    namespace EPSG_4747 {
        let towgs84_397: string;
        export { towgs84_397 as towgs84 };
    }
    namespace EPSG_4749 {
        let towgs84_398: string;
        export { towgs84_398 as towgs84 };
    }
    namespace EPSG_4674 {
        let towgs84_399: string;
        export { towgs84_399 as towgs84 };
    }
    namespace EPSG_4755 {
        let towgs84_400: string;
        export { towgs84_400 as towgs84 };
    }
    namespace EPSG_4759 {
        let towgs84_401: string;
        export { towgs84_401 as towgs84 };
    }
    namespace EPSG_4762 {
        let towgs84_402: string;
        export { towgs84_402 as towgs84 };
    }
    namespace EPSG_4763 {
        let towgs84_403: string;
        export { towgs84_403 as towgs84 };
    }
    namespace EPSG_4764 {
        let towgs84_404: string;
        export { towgs84_404 as towgs84 };
    }
    namespace EPSG_4166 {
        let towgs84_405: string;
        export { towgs84_405 as towgs84 };
    }
    namespace EPSG_4170 {
        let towgs84_406: string;
        export { towgs84_406 as towgs84 };
    }
    namespace EPSG_5546 {
        let towgs84_407: string;
        export { towgs84_407 as towgs84 };
    }
    namespace EPSG_7844 {
        let towgs84_408: string;
        export { towgs84_408 as towgs84 };
    }
    namespace EPSG_4818 {
        let towgs84_409: string;
        export { towgs84_409 as towgs84 };
    }
}
