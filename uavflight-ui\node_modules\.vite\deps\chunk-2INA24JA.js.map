{"version": 3, "sources": ["../../ol/source/Source.js"], "sourcesContent": ["/**\n * @module ol/source/Source\n */\nimport BaseObject from '../Object.js';\nimport {get as getProjection} from '../proj.js';\n\n/**\n * @typedef {'undefined' | 'loading' | 'ready' | 'error'} State\n * State of the source, one of 'undefined', 'loading', 'ready' or 'error'.\n */\n\n/**\n * A function that takes a {@link import(\"../View.js\").ViewStateLayerStateExtent} and returns a string or\n * an array of strings representing source attributions.\n *\n * @typedef {function(import(\"../View.js\").ViewStateLayerStateExtent): (string|Array<string>)} Attribution\n */\n\n/**\n * A type that can be used to provide attribution information for data sources.\n *\n * It represents either\n * * a simple string (e.g. `'© Acme Inc.'`)\n * * an array of simple strings (e.g. `['© Acme Inc.', '© Bacme Inc.']`)\n * * a function that returns a string or array of strings ({@link module:ol/source/Source~Attribution})\n *\n * @typedef {string|Array<string>|Attribution} AttributionLike\n */\n\n/**\n * @typedef {Object} Options\n * @property {AttributionLike} [attributions] Attributions.\n * @property {boolean} [attributionsCollapsible=true] Attributions are collapsible.\n * @property {import(\"../proj.js\").ProjectionLike} [projection] Projection. Default is the view projection.\n * @property {import(\"./Source.js\").State} [state='ready'] State.\n * @property {boolean} [wrapX=false] WrapX.\n * @property {boolean} [interpolate=false] Use interpolated values when resampling.  By default,\n * the nearest neighbor is used when resampling.\n */\n\n/**\n * @classdesc\n * Abstract base class; normally only used for creating subclasses and not\n * instantiated in apps.\n * Base class for {@link module:ol/layer/Layer~Layer} sources.\n *\n * A generic `change` event is triggered when the state of the source changes.\n * @abstract\n * @api\n */\nclass Source extends BaseObject {\n  /**\n   * @param {Options} options Source options.\n   */\n  constructor(options) {\n    super();\n\n    /**\n     * @protected\n     * @type {import(\"../proj/Projection.js\").default|null}\n     */\n    this.projection = getProjection(options.projection);\n\n    /**\n     * @private\n     * @type {?Attribution}\n     */\n    this.attributions_ = adaptAttributions(options.attributions);\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.attributionsCollapsible_ =\n      options.attributionsCollapsible !== undefined\n        ? options.attributionsCollapsible\n        : true;\n\n    /**\n     * This source is currently loading data. Sources that defer loading to the\n     * map's tile queue never set this to `true`.\n     * @type {boolean}\n     */\n    this.loading = false;\n\n    /**\n     * @private\n     * @type {import(\"./Source.js\").State}\n     */\n    this.state_ = options.state !== undefined ? options.state : 'ready';\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.wrapX_ = options.wrapX !== undefined ? options.wrapX : false;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.interpolate_ = !!options.interpolate;\n\n    /**\n     * @protected\n     * @type {function(import(\"../View.js\").ViewOptions):void}\n     */\n    this.viewResolver = null;\n\n    /**\n     * @protected\n     * @type {function(Error):void}\n     */\n    this.viewRejector = null;\n\n    const self = this;\n    /**\n     * @private\n     * @type {Promise<import(\"../View.js\").ViewOptions>}\n     */\n    this.viewPromise_ = new Promise(function (resolve, reject) {\n      self.viewResolver = resolve;\n      self.viewRejector = reject;\n    });\n  }\n\n  /**\n   * Get the attribution function for the source.\n   * @return {?Attribution} Attribution function.\n   * @api\n   */\n  getAttributions() {\n    return this.attributions_;\n  }\n\n  /**\n   * @return {boolean} Attributions are collapsible.\n   * @api\n   */\n  getAttributionsCollapsible() {\n    return this.attributionsCollapsible_;\n  }\n\n  /**\n   * Get the projection of the source.\n   * @return {import(\"../proj/Projection.js\").default|null} Projection.\n   * @api\n   */\n  getProjection() {\n    return this.projection;\n  }\n\n  /**\n   * @param {import(\"../proj/Projection\").default} [projection] Projection.\n   * @return {Array<number>|null} Resolutions.\n   */\n  getResolutions(projection) {\n    return null;\n  }\n\n  /**\n   * @return {Promise<import(\"../View.js\").ViewOptions>} A promise for view-related properties.\n   */\n  getView() {\n    return this.viewPromise_;\n  }\n\n  /**\n   * Get the state of the source, see {@link import(\"./Source.js\").State} for possible states.\n   * @return {import(\"./Source.js\").State} State.\n   * @api\n   */\n  getState() {\n    return this.state_;\n  }\n\n  /**\n   * @return {boolean|undefined} Wrap X.\n   */\n  getWrapX() {\n    return this.wrapX_;\n  }\n\n  /**\n   * @return {boolean} Use linear interpolation when resampling.\n   */\n  getInterpolate() {\n    return this.interpolate_;\n  }\n\n  /**\n   * Refreshes the source. The source will be cleared, and data from the server will be reloaded.\n   * @api\n   */\n  refresh() {\n    this.changed();\n  }\n\n  /**\n   * Set the attributions of the source.\n   * @param {AttributionLike|undefined} attributions Attributions.\n   *     Can be passed as `string`, `Array<string>`, {@link module:ol/source/Source~Attribution},\n   *     or `undefined`.\n   * @api\n   */\n  setAttributions(attributions) {\n    this.attributions_ = adaptAttributions(attributions);\n    this.changed();\n  }\n\n  /**\n   * Set the state of the source.\n   * @param {import(\"./Source.js\").State} state State.\n   */\n  setState(state) {\n    this.state_ = state;\n    this.changed();\n  }\n}\n\n/**\n * Turns the attributions option into an attributions function.\n * @param {AttributionLike|undefined} attributionLike The attribution option.\n * @return {Attribution|null} An attribution function (or null).\n */\nfunction adaptAttributions(attributionLike) {\n  if (!attributionLike) {\n    return null;\n  }\n  if (Array.isArray(attributionLike)) {\n    return function (frameState) {\n      return attributionLike;\n    };\n  }\n\n  if (typeof attributionLike === 'function') {\n    return attributionLike;\n  }\n\n  return function (frameState) {\n    return [attributionLike];\n  };\n}\n\nexport default Source;\n"], "mappings": ";;;;;;;;AAkDA,IAAM,SAAN,cAAqB,eAAW;AAAA;AAAA;AAAA;AAAA,EAI9B,YAAY,SAAS;AACnB,UAAM;AAMN,SAAK,aAAa,IAAc,QAAQ,UAAU;AAMlD,SAAK,gBAAgB,kBAAkB,QAAQ,YAAY;AAM3D,SAAK,2BACH,QAAQ,4BAA4B,SAChC,QAAQ,0BACR;AAON,SAAK,UAAU;AAMf,SAAK,SAAS,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAM5D,SAAK,SAAS,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAM5D,SAAK,eAAe,CAAC,CAAC,QAAQ;AAM9B,SAAK,eAAe;AAMpB,SAAK,eAAe;AAEpB,UAAM,OAAO;AAKb,SAAK,eAAe,IAAI,QAAQ,SAAU,SAAS,QAAQ;AACzD,WAAK,eAAe;AACpB,WAAK,eAAe;AAAA,IACtB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B;AAC3B,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,YAAY;AACzB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,cAAc;AAC5B,SAAK,gBAAgB,kBAAkB,YAAY;AACnD,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO;AACd,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AACF;AAOA,SAAS,kBAAkB,iBAAiB;AAC1C,MAAI,CAAC,iBAAiB;AACpB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,eAAe,GAAG;AAClC,WAAO,SAAU,YAAY;AAC3B,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,OAAO,oBAAoB,YAAY;AACzC,WAAO;AAAA,EACT;AAEA,SAAO,SAAU,YAAY;AAC3B,WAAO,CAAC,eAAe;AAAA,EACzB;AACF;AAEA,IAAO,iBAAQ;", "names": []}