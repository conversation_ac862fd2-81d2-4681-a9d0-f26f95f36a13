const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/parent.CgjdvtIZ.js","assets/index.C0-0gsfl.js","assets/vue.CnN__PXn.js","assets/index.CB8Sa4Ps.css","assets/index.DC-UHNzz.js","assets/index.Dybqck2i.css","assets/index.CLy3zSuB.js"])))=>i.map(i=>d[i]);
import{w as P,u as H,N as L,a as r,__tla as M}from"./index.C0-0gsfl.js";import{d as y,k as O,l as j,s as p,c as l,o as C,B as o,e as m,b as f,v as d,t as s,D as S,u as e,j as i,n as $}from"./vue.CnN__PXn.js";let h,B=Promise.all([(()=>{try{return M}catch{}})()]).then(async()=>{let _;_=y({name:"layoutMain"}),h=y({..._,setup(N,{expose:b}){const w=i(()=>r(()=>import("./parent.CgjdvtIZ.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([0,1,2,3]))),x=i(()=>r(()=>import("./index.DC-UHNzz.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([4,1,2,3,5]))),g=i(()=>r(()=>import("./index.CLy3zSuB.js").then(async a=>(await a.__tla,a)),__vite__mapDeps([6,1,2,3]))),n=O(),v=j(),k=P(),E=H(),{themeConfig:t}=p(E),{isTagsViewCurrenFull:R}=p(k),T=l(()=>t.value.isFooter&&!v.meta.isIframe),D=l(()=>t.value.isFixedHeader),F=l(()=>t.value.isFixedHeader?".layout-backtop-header-fixed .el-scrollbar__wrap":".layout-backtop .el-scrollbar__wrap"),u=l(()=>{if(R.value)return"0px";const{isTagsview:a,layout:c}=t.value;return a&&c!=="classic"?"85px":"51px"});return C(()=>{L.done(600)}),b({layoutMainScrollbarRef:n}),(a,c)=>{const I=o("el-scrollbar"),V=o("el-backtop"),A=o("el-main");return f(),m(A,{class:"layout-main",style:$(e(D)?`height: calc(100% - ${e(u)})`:`minHeight: calc(100% - ${e(u)})`)},{default:d(()=>[s(I,{ref_key:"layoutMainScrollbarRef",ref:n,class:"layout-main-scroll layout-backtop-header-fixed","wrap-class":"layout-main-scroll","view-class":"layout-main-scroll"},{default:d(()=>[s(e(w)),e(T)?(f(),m(e(x),{key:0})):S("",!0)]),_:1},512),s(V,{target:e(F)},null,8,["target"]),s(e(g))]),_:1},8,["style"])}}})});export{B as __tla,h as default};
