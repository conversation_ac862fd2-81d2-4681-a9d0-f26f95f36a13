<template>
  <el-dialog
    v-model="dialogVisible"
    width="90%"
    :style="{ height: '90vh' }"
    destroy-on-close
    :before-close="handleClose"
    class="map-compare-dialog"
  >
    <!-- 自定义标题区域，包含标题和模式切换按钮 -->
    <template #header>
      <div class="dialog-header">
        <div class="dialog-title">地图图层比较</div>
        <div class="mode-toggle">
          <el-radio-group v-model="compareMode" @change="handleModeChange" size="default">
            <el-radio-button label="split">对比模式</el-radio-button>
            <el-radio-button label="swipe">卷帘模式</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </template>

    <div class="map-compare-container">
      <div v-if="loading" class="loading-overlay">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <p>正在加载图层...</p>
      </div>

      <!-- 图层选择区域 -->
      <div class="layer-selection">
        <div class="layer-select">
          <div class="layer-header">
          <span>左侧图层:</span>
            <div class="search-input">
              <el-input
                v-model="leftLayerSearch"
                placeholder="搜索"
                clearable
                prefix-icon="Search"
                size="small"
                @input="filterLeftLayers"
              />
            </div>
            <div class="spatial-search-btn">
              <el-button
                :type="leftSpatialSearchActive ? 'warning' : 'primary'"
                size="small"
                @click="toggleLeftSpatialSearch"
                :icon="leftSpatialSearchActive ? 'Close' : 'Location'"
                :loading="leftSpatialSearchLoading"
              >
                {{ leftSpatialSearchActive ? '取消搜索' : '空间搜索' }}
              </el-button>
            </div>
          </div>
          <el-select 
            v-model="leftLayerId" 
            placeholder="选择左侧图层" 
            filterable 
            :loading="loadingLayers"
            @change="handleLayerChange"
            class="layer-selector"
          >
            <el-option
              v-for="layer in filteredLeftLayers"
              :key="layer.id"
              :label="`${layer.id} (${layer.date})`"
              :value="layer.id"
            />
          </el-select>
        </div>
        
        <!-- 分割线控制区域 -->
        <div class="divider-controls">
          <!-- 交换按钮 -->
          <el-button 
            type="primary" 
            circle 
            :icon="Refresh" 
            @click="swapLayers"
            :disabled="!leftLayerId || !rightLayerId"
            title="交换左右图层"
            class="swap-button"
          />
          
          <el-input-number 
            v-model="sliderValue" 
            :min="10" 
            :max="90" 
            :step="1"
            size="small"
            @change="handleSliderInput"
            :controls="false"
            class="ratio-input"
          >
            <template #append>%</template>
          </el-input-number>
        </div>
        
        <div class="layer-select">
          <div class="layer-header">
          <span>右侧图层:</span>
            <div class="search-input">
              <el-input
                v-model="rightLayerSearch"
                placeholder="搜索"
                clearable
                prefix-icon="Search"
                size="small"
                @input="filterRightLayers"
              />
            </div>
            <div class="spatial-search-btn">
              <el-button
                :type="rightSpatialSearchActive ? 'warning' : 'primary'"
                size="small"
                @click="toggleRightSpatialSearch"
                :icon="rightSpatialSearchActive ? 'Close' : 'Location'"
                :loading="rightSpatialSearchLoading"
              >
                {{ rightSpatialSearchActive ? '取消搜索' : '空间搜索' }}
              </el-button>
            </div>
          </div>
          <el-select 
            v-model="rightLayerId" 
            placeholder="选择右侧图层" 
            filterable
            :loading="loadingLayers"
            @change="handleLayerChange"
            class="layer-selector"
          >
            <el-option
              v-for="layer in filteredRightLayers"
              :key="layer.id"
              :label="`${layer.id} (${layer.date})`"
              :value="layer.id"
            />
          </el-select>
        </div>
      </div>
      
      <!-- 地图容器 -->
      <div class="map-container">
        <!-- 对比模式模式 -->
        <div v-if="compareMode === 'split'" class="maps-container split-mode">
          <div ref="leftMapContainer" class="left-map"></div>
          <div class="map-divider" :style="{ left: `${sliderValue}%` }" @mousedown="onMapDividerDrag"></div>
          <div ref="rightMapContainer" class="right-map"></div>
        </div>
        
        <!-- 卷帘模式 -->
        <div v-else class="maps-container swipe-mode">
          <div ref="singleMapContainer" class="single-map">
            <!-- 卷帘分割线 -->
            <div ref="swipeContainer" id="swipeContainer" :style="{ left: `${sliderValue}%` }" @mousedown="onSwipeMouseDown">
              <div id="swipeDiv">
                <div class="handle"></div>
          </div>
        </div>
      </div>
        </div>
      </div>
      
      <!-- 底部区域已移除 -->
    </div>
  </el-dialog>
</template>

<script lang="ts">
export default {
  name: 'MapCompareDialog'
}
</script>

<script lang="ts" setup>
import { ref, onMounted, defineProps, defineEmits, watch, onBeforeUnmount, nextTick, computed } from 'vue';
import { ElMessage } from 'element-plus';
import Map from 'ol/Map';
import View from 'ol/View';
import TileLayer from 'ol/layer/Tile';
import WMTS from 'ol/source/WMTS';
import WMTSTileGrid from 'ol/tilegrid/WMTS';
import { get as getProjection } from 'ol/proj';
import { getTopLeft } from 'ol/extent';
import TileState from 'ol/TileState';
import { Loading, Search, Refresh, Location, Close } from '@element-plus/icons-vue';
import { fromLonLat } from 'ol/proj';
import { defaults as defaultControls, ScaleLine, ZoomSlider, FullScreen } from 'ol/control';
import { getLayerBbox } from '/@/utils/geoserver';
import axios from 'axios';

// ======= 优先级瓦片加载配置 =======

// 瓦片加载超时时间（毫秒）
// 超过这个时间没加载完就认为"卡住"，主动释放
const tileTimeoutMs = 3000;

// 待重试队列（高优先级瓦片的 key 会存到这里）
const retryQueue: string[] = [];

// 正在加载的瓦片集合： key → 定时器ID
// 用来在 onload/onerror 时清理对应的超时定时器
const loadingTiles = new globalThis.Map<string, number>();

/**
 * 根据 tile 对象生成唯一 key
 * @param tile - 当前加载的瓦片对象
 * @returns 唯一标识，例如 "z/x/y"
 */
function getTileKey(tile: any): string {
  const coord = tile.getTileCoord(); // [z, x, y]
  return coord.join('/');
}

/**
 * 自定义 tileLoadFunction
 * 核心逻辑：
 * 1. 开始加载瓦片时，启动一个超时计时器
 * 2. 如果超时仍未加载完 → 释放并行请求槽位（tile.setState(ERROR)）
 * 3. 释放的瓦片加入 retryQueue，等待优先重试
 */
function priorityTileLoadFunction(tile: any, src: string): void {
  const img = tile.getImage();   // 获取 HTMLImageElement
  const key = getTileKey(tile);  // 唯一标识
  let loaded = false;            // 标记瓦片是否已完成（成功/失败）

  // ======== 瓦片加载成功事件 ========
  img.onload = () => {
    loaded = true;
    const timeoutId = loadingTiles.get(key);
    if (timeoutId) {
      clearTimeout(timeoutId); // 清理超时定时器
    }
    loadingTiles.delete(key);            // 从正在加载集合中移除
    tile.setState(TileState.LOADED);     // 标记为已加载
  };

  // ======== 瓦片加载失败事件 ========
  img.onerror = () => {
    loaded = true;
    const timeoutId = loadingTiles.get(key);
    if (timeoutId) {
      clearTimeout(timeoutId); // 清理超时定时器
    }
    loadingTiles.delete(key);
    tile.setState(TileState.ERROR);      // 标记为加载失败
  };

  // ======== 超时释放机制 ========
  const timeoutId = setTimeout(() => {
    if (!loaded && tile.getState() === TileState.LOADING) {
      console.warn(`Tile ${key} timeout → releasing & queueing retry`);

      // 将该瓦片加入高优先级重试队列
      retryQueue.push(key);

      // 强制标记为 ERROR → 立即释放浏览器并行请求槽位
      tile.setState(TileState.ERROR);
    }
  }, tileTimeoutMs);

  // 把该瓦片加入"正在加载集合"
  loadingTiles.set(key, timeoutId);

  // ======== 正式触发加载 ========
  img.src = src;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  // 可选的初始图层ID
  initialLayerId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible']);

// 基本状态
const dialogVisible = ref(false);
const loading = ref(false);
const loadingLayers = ref(false);
const leftMapContainer = ref<HTMLElement | null>(null);
const rightMapContainer = ref<HTMLElement | null>(null);
const leftMap = ref<Map | null>(null);
const rightMap = ref<Map | null>(null);
const sliderContainer = ref<HTMLElement | null>(null);
const singleMapContainer = ref<HTMLElement | null>(null);
const swipeContainer = ref<HTMLElement | null>(null);
const mapContainer = ref<HTMLElement | null>(null);
const map = ref<Map | null>(null);

// 图层相关状态
const availableLayers = ref<{id: string, date: string}[]>([]);
const leftLayerId = ref('');
const rightLayerId = ref('');

// 滑动比较相关
const sliderValue = ref(50);

// 模式切换
const compareMode = ref('split'); // 'split' 或 'swipe'

// 卷帘拖动状态
const isSwipeDragging = ref(false);
const leftLayerInstance = ref<any>(null);
const rightLayerInstance = ref<any>(null);

// 添加图层搜索功能
const leftLayerSearch = ref('');
const rightLayerSearch = ref('');

// 空间搜索状态
const leftSpatialSearchActive = ref(false);
const rightSpatialSearchActive = ref(false);

// 空间搜索结果
const leftSpatialSearchResults = ref<string[]>([]);
const rightSpatialSearchResults = ref<string[]>([]);

// 空间搜索加载状态
const leftSpatialSearchLoading = ref(false);
const rightSpatialSearchLoading = ref(false);

// 过滤后的图层列表
const filteredLeftLayers = computed(() => {
  let layers = availableLayers.value;

  // 如果有空间搜索结果，优先显示空间搜索结果
  if (leftSpatialSearchActive.value && leftSpatialSearchResults.value.length > 0) {
    layers = availableLayers.value.filter(layer =>
      leftSpatialSearchResults.value.includes(layer.id)
    );
  }

  // 再应用文本搜索过滤
  if (!leftLayerSearch.value) return layers;

  const searchTerm = leftLayerSearch.value.toLowerCase();
  return layers.filter(layer => {
    return layer.id.toLowerCase().includes(searchTerm) ||
           layer.date.toLowerCase().includes(searchTerm);
  });
});

const filteredRightLayers = computed(() => {
  let layers = availableLayers.value;

  // 如果有空间搜索结果，优先显示空间搜索结果
  if (rightSpatialSearchActive.value && rightSpatialSearchResults.value.length > 0) {
    layers = availableLayers.value.filter(layer =>
      rightSpatialSearchResults.value.includes(layer.id)
    );
  }

  // 再应用文本搜索过滤
  if (!rightLayerSearch.value) return layers;

  const searchTerm = rightLayerSearch.value.toLowerCase();
  return layers.filter(layer => {
    return layer.id.toLowerCase().includes(searchTerm) ||
           layer.date.toLowerCase().includes(searchTerm);
  });
});

// 过滤图层方法
const filterLeftLayers = () => {
  // 使用计算属性自动过滤，此方法仅用于触发重新计算
  console.log('过滤左侧图层:', leftLayerSearch.value);
};

const filterRightLayers = () => {
  // 使用计算属性自动过滤，此方法仅用于触发重新计算
  console.log('过滤右侧图层:', rightLayerSearch.value);
};

// 空间搜索接口查询方法
const queryLayerIntersections = async (workspace: string, layerName: string) => {
  try {
    const host = import.meta.env.VITE_GEOSERVER_HOST || 'localhost';
    const port = import.meta.env.VITE_GEOSERVER_HD_PORT || '8080';
    const apiUrl = `http://${host}:${port}/api/layer_intersections`;

    console.log(`查询图层相交信息: ${workspace}:${layerName}`);

    const response = await axios.get(apiUrl, {
      params: {
        workspace: workspace,
        layer: layerName
      }
    });

    if (response.data && response.data.status === 'success') {
      console.log('空间搜索结果:', response.data);

      // 提取相交图层的ID列表
      const intersectingLayerIds = response.data.intersecting_layers.map((layer: any) => layer.id);

      // 过滤出在可用图层列表中存在的图层（求并集）
      const availableIntersectingLayers = intersectingLayerIds.filter((layerId: string) =>
        availableLayers.value.some(availableLayer => availableLayer.id === layerId)
      );

      console.log('可用的相交图层:', availableIntersectingLayers);
      return availableIntersectingLayers;
    } else {
      throw new Error(response.data?.message || '查询图层相交信息失败');
    }
  } catch (error) {
    console.error('空间搜索查询失败:', error);
    ElMessage.error('空间搜索查询失败');
    return [];
  }
};

// 空间搜索切换方法
const toggleLeftSpatialSearch = async () => {
  leftSpatialSearchActive.value = !leftSpatialSearchActive.value;

  if (leftSpatialSearchActive.value) {
    console.log('开启左侧空间搜索功能');

    // 检查右侧是否选中了图层
    if (rightLayerId.value) {
      const parts = rightLayerId.value.split(':');
      if (parts.length === 2) {
        const workspace = parts[0];
        const layerName = parts[1];
        console.log(`右侧选中图层信息 - 工作空间：${workspace} 图层名称：${layerName}`);

        // 开始空间搜索查询
        leftSpatialSearchLoading.value = true;
        try {
          const intersectingLayers = await queryLayerIntersections(workspace, layerName);
          leftSpatialSearchResults.value = intersectingLayers;

          if (intersectingLayers.length > 0) {
            ElMessage.success(`找到 ${intersectingLayers.length} 个相交图层`);
          } else {
            ElMessage.info('未找到相交的图层');
          }
        } catch (error) {
          console.error('空间搜索失败:', error);
        } finally {
          leftSpatialSearchLoading.value = false;
        }
      } else {
        console.log(`右侧选中图层信息 - ${rightLayerId.value}`);
        ElMessage.warning('图层ID格式不正确，无法进行空间搜索');
      }
    } else {
      console.log('右侧未选中任何图层');
      ElMessage.warning('请先选择右侧图层再进行空间搜索');
      leftSpatialSearchActive.value = false; // 重置状态
    }
  } else {
    console.log('取消左侧空间搜索功能');
    // 清理空间搜索相关的状态
    leftSpatialSearchResults.value = [];
    leftSpatialSearchLoading.value = false;
  }
};

const toggleRightSpatialSearch = async () => {
  rightSpatialSearchActive.value = !rightSpatialSearchActive.value;

  if (rightSpatialSearchActive.value) {
    console.log('开启右侧空间搜索功能');

    // 检查左侧是否选中了图层
    if (leftLayerId.value) {
      const parts = leftLayerId.value.split(':');
      if (parts.length === 2) {
        const workspace = parts[0];
        const layerName = parts[1];
        console.log(`左侧选中图层信息 - 工作空间：${workspace} 图层名称：${layerName}`);

        // 开始空间搜索查询
        rightSpatialSearchLoading.value = true;
        try {
          const intersectingLayers = await queryLayerIntersections(workspace, layerName);
          rightSpatialSearchResults.value = intersectingLayers;

          if (intersectingLayers.length > 0) {
            ElMessage.success(`找到 ${intersectingLayers.length} 个相交图层`);
          } else {
            ElMessage.info('未找到相交的图层');
          }
        } catch (error) {
          console.error('空间搜索失败:', error);
        } finally {
          rightSpatialSearchLoading.value = false;
        }
      } else {
        console.log(`左侧选中图层信息 - ${leftLayerId.value}`);
        ElMessage.warning('图层ID格式不正确，无法进行空间搜索');
      }
    } else {
      console.log('左侧未选中任何图层');
      ElMessage.warning('请先选择左侧图层再进行空间搜索');
      rightSpatialSearchActive.value = false; // 重置状态
    }
  } else {
    console.log('取消右侧空间搜索功能');
    // 清理空间搜索相关的状态
    rightSpatialSearchResults.value = [];
    rightSpatialSearchLoading.value = false;
  }
};

// 监听props变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    // 延迟初始化地图，确保DOM已经渲染
    setTimeout(() => {
      if (compareMode.value === 'split') {
        initSplitMaps(); // 初始化分屏对比地图
      } else {
        initSwipeMap(); // 初始化卷帘模式地图
      }
      fetchAvailableLayers();
      
      // 如果有初始图层，设置为左侧图层
      if (props.initialLayerId) {
        leftLayerId.value = props.initialLayerId;
        handleLayerChange();
      }
    }, 300);
  }
});

// 监听模式变化
watch(() => compareMode.value, (newMode) => {
  // 清理所有地图实例
  cleanupMaps();
  
  // 根据模式重新初始化
  setTimeout(() => {
    if (newMode === 'split') {
      initSplitMaps();
    } else {
      initSwipeMap();
    }
    
    // 重新加载图层（如果已选择）
    if (leftLayerId.value || rightLayerId.value) {
      handleLayerChange();
    }
  }, 100);
});

// 监听滑动条变化
watch(() => sliderValue.value, (newVal) => {
  if (compareMode.value === 'split') {
    // 分屏模式：调整两个地图的宽度比例
    if (leftMapContainer.value && rightMapContainer.value) {
      leftMapContainer.value.style.width = `${newVal}%`;
      rightMapContainer.value.style.width = `${100 - newVal}%`;
      
      // 通知地图实例重新计算尺寸
      if (leftMap.value) leftMap.value.updateSize();
      if (rightMap.value) rightMap.value.updateSize();
    }
    } else if (map.value) {
      // 卷帘模式：更新分割线位置并触发渲染
      updateSwipePosition();
      
      // 强制触发地图重新渲染以更新裁剪
      if (leftLayerInstance.value) {
        try {
          map.value.render();
          // 添加延迟检查图层是否可见
          setTimeout(recoverLayers, 200);
        } catch (error) {
          console.error('渲染图层失败:', error);
          // 出错时尝试恢复
          recoverLayers();
        }
      }
    }
  }, { flush: 'post' });

// 监听dialogVisible变化，处理对话框关闭
watch(() => dialogVisible.value, (newVal) => {
  emit('update:visible', newVal);
  
  if (newVal) {
    // 延迟初始化地图，确保DOM已经渲染
    setTimeout(() => {
      if (compareMode.value === 'split') {
        initSplitMaps(); // 初始化分屏对比地图
      } else {
        initSwipeMap(); // 初始化卷帘模式地图
      }
      fetchAvailableLayers();
      
      // 如果有初始图层，设置为左侧图层
      if (props.initialLayerId) {
        leftLayerId.value = props.initialLayerId;
        handleLayerChange();
      }
      
      // 弹窗完全打开后，确保地图和裁剪位置正确
      setTimeout(() => {
        if (map.value) {
          map.value.updateSize();
          updateSwipePosition();
          map.value.render();
        }
      }, 300);
    }, 300);
  } else {
    // 清理地图
    cleanupMaps();
  }
});

// 获取所有已完成任务的图层
const fetchAvailableLayers = async () => {
  loadingLayers.value = true;
  try {
    const host = import.meta.env.VITE_GEOSERVER_HOST || 'localhost';
    const port = import.meta.env.VITE_GEOSERVER_HD_PORT || '8080';
    const response = await axios.get(`http://${host}:${port}/api/map/odm/tasks`);
    
    if (response.data && response.data.status === 'success' && response.data.tasks) {
      // 筛选出已完成的任务
      const completedTasks = response.data.tasks.filter((task: any) => task.status === '完成');
      
      // 构建可用图层列表
      const layers = completedTasks.map((task: any) => {
        const layerId = task.details?.geoserver_publish?.layer_name || 
                      task.details?.map_config?.layer_id || '';
        const startDate = task.start_time ? new Date(task.start_time).toLocaleDateString() : '未知';
        
        return {
          id: layerId,
          date: startDate
        };
      }).filter((layer: {id: string, date: string}) => layer.id); // 过滤掉没有图层ID的任务

      availableLayers.value = layers;
      console.log('可用图层列表:', availableLayers.value);
    } else {
      throw new Error(response.data?.message || '获取任务列表失败');
    }
  } catch (error) {
    console.error('获取可用图层失败:', error);
    ElMessage.error('获取可用图层列表失败');
  } finally {
    loadingLayers.value = false;
  }
};

// 初始化分屏对比模式的两个地图
const initSplitMaps = () => {
  if ((leftMap.value && rightMap.value) || !leftMapContainer.value || !rightMapContainer.value) return;
  
  try {
    loading.value = true;
    
    // 使用合适的中国南方坐标作为初始中心点
    const initialCenter = fromLonLat([108.3, 22.8]); // 南宁附近
    
    // 创建左侧地图，不包含任何控件
    leftMap.value = new Map({
      target: leftMapContainer.value,
      layers: [],
      view: new View({
        center: initialCenter,
        zoom: 10
      }),
      controls: [] // 移除所有控件
    });
    
    // 创建右侧地图，不包含任何控件
    rightMap.value = new Map({
      target: rightMapContainer.value,
      layers: [],
      view: new View({
        center: initialCenter,
        zoom: 10
      }),
      controls: [] // 移除所有控件
    });
    
    // 同步两个地图的视图
    setupSplitViewSync();
    
    loading.value = false;
    console.log('分屏对比模式的两个地图实例已成功初始化');
  } catch (error) {
    console.error('初始化分屏对比地图失败:', error);
    ElMessage.error('初始化地图失败');
    loading.value = false;
  }
};

// 初始化卷帘模式的地图
const initSwipeMap = () => {
  if (map.value || !singleMapContainer.value) return;
  
  try {
    loading.value = true;
    mapContainer.value = singleMapContainer.value;
    
    // 使用合适的中国南方坐标作为初始中心点
    const initialCenter = fromLonLat([108.3, 22.8]); // 南宁附近
    
    // 创建地图，不包含任何控件
    map.value = new Map({
      target: mapContainer.value,
      layers: [],
      view: new View({
        center: initialCenter,
        zoom: 10
      }),
      controls: [] // 移除所有控件
    });
    
    // 监听地图加载完成事件
    map.value.once('rendercomplete', () => {
      console.log('地图首次渲染完成');
      // 确保在地图首次渲染完成后更新卷帘位置
      nextTick(() => {
        updateSwipePosition();
        map.value?.updateSize();
        map.value?.render();
      });
    });
    
    // 初始化卷帘位置
    updateSwipePosition();
    
    // 添加卷帘拖动事件
    setupSwipeDrag();
    
    // 延迟更新一次，确保在弹窗布局稳定后正确显示
    setTimeout(() => {
      if (map.value) {
        map.value.updateSize();
        updateSwipePosition();
        map.value.render();
      }
    }, 500);
    
    loading.value = false;
    console.log('卷帘模式的地图实例已成功初始化');
  } catch (error) {
    console.error('初始化卷帘模式地图失败:', error);
    ElMessage.error('初始化地图失败');
    loading.value = false;
  }
};

// 设置分屏视图同步
const setupSplitViewSync = () => {
  if (!leftMap.value || !rightMap.value) return;
  
  const leftView = leftMap.value.getView();
  const rightView = rightMap.value.getView();
  
  // 当左侧地图视图变化时，同步到右侧地图
  const syncLeftToRight = () => {
    const center = leftView.getCenter();
    const zoom = leftView.getZoom();
    const rotation = leftView.getRotation();
    
    if (center && zoom !== undefined) {
      rightView.setCenter(center);
      rightView.setZoom(zoom);
      rightView.setRotation(rotation);
    }
  };
  
  // 当右侧地图视图变化时，同步到左侧地图
  const syncRightToLeft = () => {
    const center = rightView.getCenter();
    const zoom = rightView.getZoom();
    const rotation = rightView.getRotation();
    
    if (center && zoom !== undefined) {
      leftView.setCenter(center);
      leftView.setZoom(zoom);
      leftView.setRotation(rotation);
    }
  };
  
  // 添加事件监听器，使用类型断言解决类型错误
  (leftMap.value as any).on('moveend', syncLeftToRight);
  (rightMap.value as any).on('moveend', syncRightToLeft);
  
  console.log('分屏地图视图同步已设置');
};

// 设置卷帘拖动功能
const setupSwipeDrag = () => {
  if (!swipeContainer.value) return;
  
  // 添加鼠标移动和鼠标释放事件监听
  document.addEventListener('mousemove', onSwipeMouseMove);
  document.addEventListener('mouseup', onSwipeMouseUp);
};

// 卷帘鼠标按下事件
const onSwipeMouseDown = (e: MouseEvent) => {
  isSwipeDragging.value = true;
  e.preventDefault();
};

// 用于卷帘模式优化的动画帧ID
let swipeAnimationFrameId: number | null = null;

// 卷帘鼠标移动事件
const onSwipeMouseMove = (e: MouseEvent) => {
  if (!isSwipeDragging.value || !mapContainer.value || !map.value) return;
  
  try {
    // 获取地图容器的边界框
    const mapRect = mapContainer.value.getBoundingClientRect();
    const mapWidth = mapRect.width;
    
    // 计算鼠标相对于地图容器的位置
    const offsetX = Math.max(0, Math.min(mapWidth, e.clientX - mapRect.left));
    
    // 计算百分比位置（限制在0-100之间）
    const percent = (offsetX / mapWidth) * 100;
    const newValue = parseFloat(percent.toFixed(2));
    
    // 取消上一帧的更新请求，避免积压渲染任务
    if (swipeAnimationFrameId !== null) {
      cancelAnimationFrame(swipeAnimationFrameId);
    }
    
    // 使用动画帧批量更新，提高性能
    swipeAnimationFrameId = requestAnimationFrame(() => {
      // 直接更新DOM元素，减少Vue响应式更新次数
      if (swipeContainer.value) {
        swipeContainer.value.style.left = `${newValue}%`;
      }
      
      // 只在动画帧中更新一次Vue响应式数据
      sliderValue.value = Math.round(newValue);
      
      // 渲染地图
      if (map.value) {
        map.value.render();
      }
      
      swipeAnimationFrameId = null;
    });
  } catch (error) {
    console.error('鼠标移动计算错误:', error);
  }
};
  
// 添加一个函数来恢复可能消失的图层
const recoverLayers = () => {
  if (!map.value || compareMode.value !== 'swipe') return;
  
  try {
    // 检查两个图层是否存在且可见
    const layers = map.value.getLayers().getArray();
    const hasVisibleLayers = layers.some(layer => layer.getVisible());
    
    // 如果没有可见图层，尝试恢复
    if (!hasVisibleLayers && leftLayerId.value && rightLayerId.value) {
      console.log('检测到图层可能已消失，尝试恢复...');
      
      // 清空图层并重新加载
      map.value.getLayers().clear();
      
      // 重新加载图层
      handleLayerChange();
      
      // 强制重新渲染
      map.value.updateSize();
      map.value.render();
    }
  } catch (error) {
    console.error('恢复图层失败:', error);
  }
};

// 在鼠标释放时检查并恢复图层
const onSwipeMouseUp = () => {
  isSwipeDragging.value = false;
  // 添加延迟检查图层是否可见
  setTimeout(recoverLayers, 100);
};

// 优化updateSwipePosition函数，使用百分比确保稳定定位
const updateSwipePosition = () => {
  if (compareMode.value !== 'swipe' || !swipeContainer.value) return;

  // 直接使用百分比定位，避免计算误差
  try {
    // 使用当前滑动值更新位置
    swipeContainer.value.style.left = `${sliderValue.value}%`;
  } catch (error) {
    console.error('更新卷帘位置出错:', error);
  }
  
  // 立即触发渲染，然后延迟检查图层状态
  if (map.value) {
    requestAnimationFrame(() => {
      map.value?.render();
      setTimeout(recoverLayers, 100);
    });
  }
};

// 处理滑动条变化
const handleSliderChange = () => {
  if (compareMode.value === 'swipe' && map.value) {
    updateSwipePosition();
    map.value.render(); // 触发地图重新渲染
  }
};

// 处理输入框直接修改分割比例
const handleSliderInput = (value: number) => {
  // 确保值在有效范围内
  const validValue = Math.max(10, Math.min(90, value));
  sliderValue.value = validValue;
  
  // 根据不同模式更新分割线
  if (compareMode.value === 'split') {
    // 对比模式下调整地图宽度
    if (leftMapContainer.value && rightMapContainer.value) {
      leftMapContainer.value.style.width = `${validValue}%`;
      rightMapContainer.value.style.width = `${100 - validValue}%`;
      
      // 通知地图实例重新计算尺寸
      if (leftMap.value) leftMap.value.updateSize();
      if (rightMap.value) rightMap.value.updateSize();
    }
  } else {
    // 卷帘模式下更新分割线位置
    updateSwipePosition();
    if (map.value) map.value.render();
  }
};

// 处理图层变更
const handleLayerChange = () => {
  if (!leftLayerId.value || !rightLayerId.value) return;
  
  loading.value = true;
  
  try {
    if (compareMode.value === 'split') {
      // 分屏模式：左右两个地图各加载一个图层
      if (leftMap.value) {
        leftMap.value.getLayers().clear();
        createAndAddWMTSLayer(leftLayerId.value, leftMap.value);
      }
      
      if (rightMap.value) {
        rightMap.value.getLayers().clear();
        createAndAddWMTSLayer(rightLayerId.value, rightMap.value);
      }
    } else {
      // 卷帘模式：一个地图加载两个图层
      if (map.value) {
        map.value.getLayers().clear();
        
        // 先加载底部图层（右侧）
        const bottomLayer = createWMTSLayer(rightLayerId.value);
        if (bottomLayer) {
          // 设置图层层级
          bottomLayer.setZIndex(0);
          
                    // 添加prerender事件，裁剪右侧图层，使其只在分隔线右侧显示
          bottomLayer.on('prerender', function(event: any) {
            const ctx = event.context;
            const canvas = ctx.canvas;
            
            // 即使容器不存在也进行裁剪，防止图层消失
            try {
              // 计算当前卷帘位置（像素）- 使用当前滑动值
              const canvasWidth = canvas.width;
              const currentValue = sliderValue.value;
              const swipePosition = (canvasWidth * currentValue) / 100;
              
              // 裁剪右侧区域（从分隔线到右边缘）
              ctx.save();
              ctx.beginPath();
              ctx.rect(swipePosition, 0, canvasWidth - swipePosition, canvas.height);
              ctx.clip();
            } catch (error) {
              // 发生错误时，保存上下文但不执行裁剪
              ctx.save();
              console.error('右侧裁剪计算错误，已忽略裁剪:', error);
            }
          });
          
          // 添加postrender事件恢复上下文
          bottomLayer.on('postrender', function(event: any) {
            const ctx = event.context;
            ctx.restore();
          });
          
          map.value.addLayer(bottomLayer);
          rightLayerInstance.value = bottomLayer;
  }
        
        // 再加载顶部图层（左侧，带裁剪）
        const topLayer = createWMTSLayer(leftLayerId.value);
        if (topLayer) {
          // 确保顶部图层在最上面显示
          topLayer.setZIndex(1);
          
                    // 添加prerender事件监听（处理渲染前的裁剪）- 优化版本防止闪烁
          topLayer.on('prerender', function(event: any) {
            const ctx = event.context;
            const canvas = ctx.canvas;
            
            // 确保即使DOM元素不存在也能正常渲染
            try {
              // 计算当前卷帘位置的像素值
              const canvasWidth = canvas.width;
              // 使用稳定的滑动值，避免重复计算导致的精度问题
              const currentValue = sliderValue.value;
              const swipePosition = (canvasWidth * currentValue) / 100;
              
              // 裁剪左侧区域
              ctx.save();
              ctx.beginPath();
              ctx.rect(0, 0, swipePosition, canvas.height);
              ctx.clip();
  } catch (error) {
              // 发生错误时至少保存上下文状态
              ctx.save();
              console.error('左侧裁剪计算错误，已忽略裁剪:', error);
            }
          });
          
          // 添加postrender事件监听（恢复渲染上下文）
          topLayer.on('postrender', function(event: any) {
            const ctx = event.context;
            ctx.restore();
          });
          
          map.value.addLayer(topLayer);
          leftLayerInstance.value = topLayer;
    
          // 确保底层图层在最下面显示
          if (rightLayerInstance.value) {
            rightLayerInstance.value.setZIndex(0);
          }
        }
        
        // 尝试缩放到图层范围
        const parts = leftLayerId.value.split(':');
        if (parts.length === 2) {
          zoomToLayerExtent(parts[0], parts[1]);
        }
      }
    }
  } catch (error) {
    console.error('更新图层失败:', error);
    ElMessage.error('更新图层失败');
  } finally {
    loading.value = false;
  }
};

// 创建WMTS图层
const createWMTSLayer = (layerId: string) => {
  if (!layerId) return null;
  
  try {
    // 解析工作空间和图层名
    const parts = layerId.split(':');
    if (parts.length !== 2) {
      throw new Error(`图层ID格式不正确: ${layerId}`);
    }
    
    const workspace = parts[0];
    const layerName = parts[1];
    
    // 获取Geoserver基础URL
    const host = import.meta.env.VITE_GEOSERVER_HOST || 'localhost';
    const port = import.meta.env.VITE_GEOSERVER_PORT || '8080'; 
    const geoserverUrl = `http://${host}:${port}/geoserver`;
    
    // 设置默认值
    const tileMatrixSet = 'EPSG:4326';
    const format = 'image/png';
    const style = '';
    
    // 获取投影
    const projection = getProjection(tileMatrixSet);
    if (!projection) {
      throw new Error(`无法获取投影系统: ${tileMatrixSet}`);
    }
    
    // 获取完整的WMTS服务URL (KVP格式)
    const wmtsEndpoint = `${geoserverUrl}/gwc/service/wmts`;
    
    // 创建WMTS源，使用优先级瓦片加载
    const source = new WMTS({
      url: wmtsEndpoint,
      layer: layerId,
      matrixSet: tileMatrixSet,
      format: format,
      projection: projection,
      style: style,
      requestEncoding: 'KVP',
      tileGrid: createWmtsTileGrid(projection, tileMatrixSet),
      wrapX: true,
      transition: 0,
      crossOrigin: 'anonymous',
      // 使用优先级瓦片加载函数
      tileLoadFunction: priorityTileLoadFunction
    });

    // 监听瓦片加载完成事件
    // 当有瓦片完成时，如果有待重试的瓦片 → 优先重试它们
    source.on('tileloadend', () => {
      if (retryQueue.length > 0) {
        const nextRetry = retryQueue.shift(); // 取一个高优先级瓦片
        console.log(`Retrying high-priority tile: ${nextRetry}`);

        // 刷新数据源 → 会重新加载当前视图可见的所有瓦片
        // 由于该瓦片是可见的，它会马上进入加载队列（优先级较高）
        source.refresh();
      }
    });
    
    // 创建图层
    const tileLayer = new TileLayer({
      preload: Infinity,
      source: source,
      visible: true,
      opacity: 1
    });
    
    return tileLayer;
  } catch (error) {
    console.error('创建WMTS图层失败:', error);
    return null;
  }
};

// 创建并添加WMTS图层（分屏模式使用）
const createAndAddWMTSLayer = (layerId: string, targetMap: any) => {
  if (!layerId || !targetMap) return;
  
  try {
    const tileLayer = createWMTSLayer(layerId);
    if (tileLayer) {
    targetMap.addLayer(tileLayer);
    
    // 尝试缩放到图层范围
      const parts = layerId.split(':');
      if (parts.length === 2) {
        zoomToLayerExtent(parts[0], parts[1]);
      }
    }
  } catch (error) {
    console.error('添加WMTS图层失败:', error);
    throw error;
  }
};

// 创建WMTS图层的瓦片网格
const createWmtsTileGrid = (projection: any, gridSetId: string): WMTSTileGrid => {
  const projectionExtent = projection.getExtent();
  
  // 根据不同的坐标系创建合适的参数
  let origin, resolutions, matrixIds;
  
  if (gridSetId === 'EPSG:4326') {
    // EPSG:4326 特殊处理
    origin = [-180, 90]; // 正确的原点
    
    // 标准的EPSG:4326分辨率
    resolutions = [
    0.703125,              // 层级 0
    0.3515625,             // 层级 1
    0.17578125,            // 层级 2
    0.087890625,           // 层级 3
    0.0439453125,          // 层级 4
    0.02197265625,         // 层级 5
    0.010986328125,        // 层级 6
    0.0054931640625,       // 层级 7
    0.00274658203125,      // 层级 8
    0.001373291015625,     // 层级 9
    0.0006866455078125,    // 层级 10
    0.0003433227539062,    // 层级 11
    0.0001716613769531,    // 层级 12
    0.0000858306884766,    // 层级 13
    0.0000429153442383,    // 层级 14
    0.0000214576721191,    // 层级 15
    0.0000107288360596,    // 层级 16
    0.0000053644180298,    // 层级 17
    0.0000026822090149,    // 层级 18
    0.0000013411045074,    // 层级 19
    0.0000006705522537,    // 层级 20
    0.0000003352761269,    // 层级 21
    0.00000016763806345,   // 层级 22
    0.00000008381903173,   // 层级 23
    0.00000004190951586,   // 层级 24
    0.00000002095475793    // 层级 25
];

    
    // 标准的EPSG:4326 GeoServer矩阵ID
    matrixIds = [];
    for (let i = 0; i < resolutions.length; i++) {
      matrixIds.push(`${gridSetId}:${i}`);
    }
  } else {
    // 默认情况下，使用自动计算的值
    origin = getTopLeft(projectionExtent);
    const size = Math.max(
      projectionExtent[2] - projectionExtent[0],
      projectionExtent[3] - projectionExtent[1]
    );
    const maxResolution = size / 256;
    
    resolutions = [];
    matrixIds = [];
    for (let i = 0; i < 20; i++) {
      resolutions.push(maxResolution / Math.pow(2, i));
      matrixIds.push(`${gridSetId}:${i}`);
    }
  }
  
  return new WMTSTileGrid({
    origin: origin,
    resolutions: resolutions,
    matrixIds: matrixIds
  });
};

// 缩放到图层范围
const zoomToLayerExtent = async (workspace: string, layerName: string) => {
  const mapInstance = compareMode.value === 'split' ? leftMap.value : map.value;
  if (!mapInstance) return;
  
  try {
    const bboxData = await getLayerBbox(workspace, layerName);
    
    if (bboxData && bboxData.status === 'success') {
      let extent;
      
      // 尝试使用latLon边界框
      if (bboxData.bbox?.latLon && 
          !(bboxData.bbox.latLon.minx === -180 && 
            bboxData.bbox.latLon.miny === -90 && 
            bboxData.bbox.latLon.maxx === 180 && 
            bboxData.bbox.latLon.maxy === 90)) {
              
        const { minx, miny, maxx, maxy } = bboxData.bbox.latLon;
        const bottomLeft = fromLonLat([minx, miny]);
        const topRight = fromLonLat([maxx, maxy]);
        
        extent = [bottomLeft[0], bottomLeft[1], topRight[0], topRight[1]];
      } 
      // 尝试使用native边界框
      else if (bboxData.bbox?.native && 
              !(bboxData.bbox.native.minx === -180 && 
                bboxData.bbox.native.miny === -90 && 
                bboxData.bbox.native.maxx === 180 && 
                bboxData.bbox.native.maxy === 90)) {
                  
        const { minx, miny, maxx, maxy } = bboxData.bbox.native;
        const bottomLeft = fromLonLat([minx, miny]);
        const topRight = fromLonLat([maxx, maxy]);
        
        extent = [bottomLeft[0], bottomLeft[1], topRight[0], topRight[1]];
      }
      
      // 如果找到有效的边界框，缩放到该范围
      if (extent && !extent.some(val => !isFinite(val))) {
        // 同步缩放地图
        mapInstance.getView().fit(extent, {
          padding: [50, 50, 50, 50],
          maxZoom: 18
        });
        
        console.log(`已缩放至图层范围: ${extent}`);
        return;
      }
    }
    
    // 如果没有找到有效边界框，使用默认范围
    const defaultExtent = fromLonLat([108.2, 22.7]).concat(fromLonLat([108.5, 23.0]));
    mapInstance.getView().fit(defaultExtent, {
      padding: [50, 50, 50, 50],
      maxZoom: 15
    });
    
  } catch (error) {
    console.error('缩放到图层范围失败:', error);
    
    // 使用默认范围
    if (mapInstance) {
      const defaultExtent = fromLonLat([108.2, 22.7]).concat(fromLonLat([108.5, 23.0]));
      mapInstance.getView().fit(defaultExtent, {
        padding: [50, 50, 50, 50],
        maxZoom: 15
      });
    }
  }
};

// 获取图层的显示标签
const getLayerLabel = (layerId: string) => {
  const layer = availableLayers.value.find(layer => layer.id === layerId);
  return layer ? `${layerId.split(':')[1]} (${layer.date})` : layerId;
};

// 模式切换处理
const handleModeChange = (value: string) => {
  compareMode.value = value;
  // 切换模式会触发watch(compareMode)事件
};

// 交换左右图层
const swapLayers = () => {
  if (!leftLayerId.value || !rightLayerId.value) return;
  
  // 保存当前值
  const tempLayerId = leftLayerId.value;
  const tempLayerSearch = leftLayerSearch.value;
  
  // 交换图层ID
  leftLayerId.value = rightLayerId.value;
  rightLayerId.value = tempLayerId;
  
  // 交换搜索框内容
  leftLayerSearch.value = rightLayerSearch.value;
  rightLayerSearch.value = tempLayerSearch;
  
  // 重新加载图层
  handleLayerChange();
  
  // 提示用户
  ElMessage.success('已交换左右图层');
};

// 清理地图
const cleanupMaps = () => {
  // 清理分屏模式地图
  if (leftMap.value) {
    leftMap.value.setTarget(undefined);
    leftMap.value = null;
  }
  if (rightMap.value) {
    rightMap.value.setTarget(undefined);
    rightMap.value = null;
  }
  
  // 清理卷帘模式地图
  if (map.value) {
    map.value.setTarget(undefined);
    map.value = null;
  }
  
  // 清理图层实例引用
  leftLayerInstance.value = null;
  rightLayerInstance.value = null;
};

// 对话框关闭前的处理
const handleClose = () => {
  // 移除文档事件监听
  document.removeEventListener('mousemove', onSwipeMouseMove);
  document.removeEventListener('mouseup', onSwipeMouseUp);
  
  // 清理地图
  cleanupMaps();
  dialogVisible.value = false;
};

// 清理优先级瓦片加载资源
const disposePriorityTileLoading = () => {
  // 清理所有正在加载的瓦片的超时定时器
  loadingTiles.forEach((timeoutId) => {
    clearTimeout(timeoutId);
  });
  loadingTiles.clear();

  // 清空重试队列
  retryQueue.length = 0;

  console.log('MapCompareDialog 优先级瓦片加载资源已清理');
};

// 组件销毁前清理
onBeforeUnmount(() => {
  // 清理优先级瓦片加载资源
  disposePriorityTileLoading();

  document.removeEventListener('mousemove', onSwipeMouseMove);
  document.removeEventListener('mouseup', onSwipeMouseUp);
  cleanupMaps();
});

// 添加resizeObserver监听地图容器大小变化
onMounted(() => {
  // 创建ResizeObserver监听地图容器大小变化
  const resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      if (compareMode.value === 'swipe' && map.value) {
        map.value.updateSize();
        updateSwipePosition();
        map.value.render();
      }
    }
  });
  
  // 当对话框可见时添加监听
  watch(() => dialogVisible.value, (visible) => {
    if (visible && mapContainer.value) {
      // 监听地图容器大小变化
      resizeObserver.observe(mapContainer.value);
    } else {
      // 取消监听
      resizeObserver.disconnect();
    }
  });
  
  // 组件卸载时清理
  onBeforeUnmount(() => {
    resizeObserver.disconnect();
  });
});

// 添加分割线拖动函数
const onMapDividerDrag = (e: MouseEvent) => {
  // 分屏模式下的分隔线拖动
  if (!leftMapContainer.value || !rightMapContainer.value) return;
  
  // 使用requestAnimationFrame优化性能
  let animationFrameId: number | null = null;
  
  const handleMouseMove = (moveEvent: MouseEvent) => {
    if (!leftMapContainer.value || !rightMapContainer.value) return;
    
    const parent = leftMapContainer.value.parentElement;
    if (!parent) return;
    
    // 计算鼠标位置并转换为百分比
    const rect = parent.getBoundingClientRect();
    const offsetX = moveEvent.clientX - rect.left;
    const percent = Math.max(10, Math.min(90, (offsetX / rect.width) * 100));
    
    // 取消上一帧的更新请求
    if (animationFrameId !== null) {
      cancelAnimationFrame(animationFrameId);
    }
    
    // 使用动画帧来批量更新UI，提高性能
    animationFrameId = requestAnimationFrame(() => {
      // 直接操作DOM，避免多次触发Vue响应式更新
      leftMapContainer.value!.style.width = `${percent}%`;
      rightMapContainer.value!.style.width = `${100 - percent}%`;
      
      // 更新分割线位置
      const mapDivider = document.querySelector('.map-divider') as HTMLElement;
      if (mapDivider) {
        mapDivider.style.left = `${percent}%`;
      }
      
      // 只在动画帧中更新一次Vue响应式数据
      sliderValue.value = Math.round(percent);
      
      // 更新地图尺寸
      if (leftMap.value) leftMap.value.updateSize();
      if (rightMap.value) rightMap.value.updateSize();
      
      animationFrameId = null;
    });
  };
  
  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };
  
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
  
  e.preventDefault();
};
</script>

<style lang="scss" scoped>
.map-compare-dialog {
  :deep(.el-dialog) {
    height: 90vh !important;
    max-height: 90vh !important;
    display: flex !important;
    flex-direction: column !important;
  }

  :deep(.el-dialog__header) {
    padding: 12px 16px;
    margin: 0;
    border-bottom: 1px solid #e4e7ed;
    flex-shrink: 0;
  }

  :deep(.el-dialog__body) {
    padding: 0;
    flex: 1;
    overflow: hidden !important;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__headerbtn) {
    top: 12px;
    right: 16px;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  
  .dialog-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    letter-spacing: 0.5px;
  }
  
  .mode-toggle {
    margin-right: 24px;
    
    :deep(.el-radio-button__inner) {
      padding: 8px 15px;
      font-size: 13px;
    }
  }
}

.map-compare-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
    
    .loading-icon {
      font-size: 24px;
      color: #409EFF;
      animation: rotate 2s linear infinite;
    }
    
    p {
      margin-top: 10px;
      font-size: 14px;
      color: #606266;
    }
  }
  
  .layer-selection {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-bottom: 8px;
    align-items: flex-end;
    
    .divider-controls {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-end;
      gap: 6px;
      padding: 0 6px;
      margin-top: 0; /* 移除顶部间距 */
      width: 70px; /* 调整整体宽度 */
      flex-shrink: 0;
      
      .ratio-input {
        width: 60px; /* 进一步减小输入框宽度 */
        
        :deep(.el-input-number__wrapper) {
          padding-right: 0;
        }
        
        :deep(.el-input__wrapper) {
          box-shadow: 0 0 0 1px #dcdfe6 inset;
        }
        
        :deep(.el-input-number__append) {
          background-color: #f5f7fa;
          color: #909399;
          font-weight: 600;
          padding: 0 8px;
        }
      }
      
      .swap-button {
        width: 28px;
        height: 28px;
        font-size: 14px;
        transition: all 0.2s;
        background-color: #409EFF;
        border-color: #409EFF;
        margin-bottom: 2px; /* 减少底部间距 */
        
        :deep(.el-icon) {
          animation: none;
          transform: rotate(90deg); /* 旋转图标使其看起来更像左右交换 */
        }
        
        &:hover {
          transform: scale(1.05);
          background-color: #66b1ff;
          border-color: #66b1ff;
          
          :deep(.el-icon) {
            animation: rotate-swap 0.6s ease;
          }
        }
        
        &:active {
          transform: scale(0.95);
        }
        
        &:disabled {
          transform: none;
          
          :deep(.el-icon) {
            animation: none;
          }
        }
      }
      
      @keyframes rotate-swap {
        0% { transform: rotate(90deg); }
        100% { transform: rotate(450deg); }
      }
    }
    
    .layer-select {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .layer-header {
      display: flex;
      align-items: center;
        margin-bottom: 2px;
        gap: 8px;

      span {
        white-space: nowrap;
          font-weight: 600;
          color: #303133;
          margin-right: 6px;
          min-width: 58px;
          font-size: 13px;
      }

        .search-input {
          flex: 1;

          .el-input {
        width: 100%;

            :deep(.el-input__wrapper) {
              padding: 0 8px;
              box-shadow: 0 0 0 1px #dcdfe6 inset;
            }
          }
        }

        .spatial-search-btn {
          flex-shrink: 0;

          .el-button {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.3s ease;

            &.el-button--primary {
              background-color: #409EFF;
              border-color: #409EFF;

              &:hover {
                background-color: #66b1ff;
                border-color: #66b1ff;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
              }
            }

            &.el-button--warning {
              background-color: #E6A23C;
              border-color: #E6A23C;

              &:hover {
                background-color: #ebb563;
                border-color: #ebb563;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
              }
            }

            &:active {
              transform: translateY(0);
            }
          }
        }
      }
      
      .layer-selector {
        width: 100%;
        
        :deep(.el-select__wrapper) {
          box-shadow: 0 0 0 1px #dcdfe6 inset;
        }
      }
    }
  }
  
  .map-container {
    width: 100%;
    flex: 1; /* 使用 flex: 1 自动占用剩余空间 */
    min-height: 400px; /* 设置最小高度确保可用性 */
    position: relative;
    overflow: hidden;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    margin-top: 2px; /* 添加非常小的顶部边距 */
    
    .maps-container {
      position: relative;
      display: flex;
      width: 100%;
      height: 100%;
    }
    
    .split-mode {
      .left-map {
        position: relative;
        width: 50%;
        height: 100%;
        transition: width 0.25s ease-out;
      }
      
      .map-divider {
        position: absolute;
        top: 0;
        bottom: 0;
        width: 6px;
        background-color: #409EFF;
        z-index: 1000;
        cursor: col-resize;
        box-shadow: 0 0 6px rgba(0, 0, 0, 0.3);
        transition: width 0.15s, opacity 0.15s;
        
        &:hover {
          opacity: 0.95;
          width: 8px;
        }
        
        &:active {
          width: 10px;
          opacity: 1;
        }
        
        &:before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background-color: #409EFF;
          box-shadow: 0 0 6px rgba(0, 0, 0, 0.5);
          transition: all 0.15s;
        }
        
        &:after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: white;
          transition: all 0.15s;
        }
      }
      
      .right-map {
        position: relative;
        width: 50%;
        height: 100%;
        transition: width 0.25s ease-out;
      }
    }

    .swipe-mode {
      .single-map {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;
      }
    }
  }
  
    /* 底部标签已移除 */
}

/* 卷帘样式 - 与对比模式模式保持一致 */
#swipeContainer {
        position: absolute;
        top: 0;
        bottom: 0;
  width: 6px;
  height: 100%;
  left: 50%;
        background-color: #409EFF;
  cursor: col-resize;
        z-index: 1000;
        box-shadow: 0 0 6px rgba(0, 0, 0, 0.3);
  transition: width 0.15s, opacity 0.15s;
  
  &:hover {
    opacity: 0.95;
    width: 8px;
  }
  
  &:active {
    width: 10px;
    opacity: 1;
  }
}

#swipeDiv {
  height: 100%;
  width: 100%;
}

/* 中间圆点指示器 */
#swipeDiv .handle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #409EFF;
  z-index: 30;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.5);
  transition: all 0.15s;
}

#swipeDiv .handle:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: white;
  transition: all 0.15s;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>

<!-- 添加全局样式覆盖 -->
<style lang="scss">
.map-compare-dialog .el-dialog__body {
  max-height: none !important;
  height: auto !important;
  overflow: visible !important;
}
</style> 