// node_modules/ol/size.js
function buffer(size, num, dest) {
  if (dest === void 0) {
    dest = [0, 0];
  }
  dest[0] = size[0] + 2 * num;
  dest[1] = size[1] + 2 * num;
  return dest;
}
function hasArea(size) {
  return size[0] > 0 && size[1] > 0;
}
function scale(size, ratio, dest) {
  if (dest === void 0) {
    dest = [0, 0];
  }
  dest[0] = size[0] * ratio + 0.5 | 0;
  dest[1] = size[1] * ratio + 0.5 | 0;
  return dest;
}
function toSize(size, dest) {
  if (Array.isArray(size)) {
    return size;
  }
  if (dest === void 0) {
    dest = [size, size];
  } else {
    dest[0] = size;
    dest[1] = size;
  }
  return dest;
}

export {
  buffer,
  hasArea,
  scale,
  toSize
};
//# sourceMappingURL=chunk-J4FNYJJI.js.map
