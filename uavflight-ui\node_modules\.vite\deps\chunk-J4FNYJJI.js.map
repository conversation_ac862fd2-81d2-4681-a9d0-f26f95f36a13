{"version": 3, "sources": ["../../ol/size.js"], "sourcesContent": ["/**\n * @module ol/size\n */\n\n/**\n * An array of numbers representing a size: `[width, height]`.\n * @typedef {Array<number>} Size\n * @api\n */\n\n/**\n * Returns a buffered size.\n * @param {Size} size Size.\n * @param {number} num The amount by which to buffer.\n * @param {Size} [dest] Optional reusable size array.\n * @return {Size} The buffered size.\n */\nexport function buffer(size, num, dest) {\n  if (dest === undefined) {\n    dest = [0, 0];\n  }\n  dest[0] = size[0] + 2 * num;\n  dest[1] = size[1] + 2 * num;\n  return dest;\n}\n\n/**\n * Determines if a size has a positive area.\n * @param {Size} size The size to test.\n * @return {boolean} The size has a positive area.\n */\nexport function hasArea(size) {\n  return size[0] > 0 && size[1] > 0;\n}\n\n/**\n * Returns a size scaled by a ratio. The result will be an array of integers.\n * @param {Size} size Size.\n * @param {number} ratio Ratio.\n * @param {Size} [dest] Optional reusable size array.\n * @return {Size} The scaled size.\n */\nexport function scale(size, ratio, dest) {\n  if (dest === undefined) {\n    dest = [0, 0];\n  }\n  dest[0] = (size[0] * ratio + 0.5) | 0;\n  dest[1] = (size[1] * ratio + 0.5) | 0;\n  return dest;\n}\n\n/**\n * Returns an `Size` array for the passed in number (meaning: square) or\n * `Size` array.\n * (meaning: non-square),\n * @param {number|Size} size Width and height.\n * @param {Size} [dest] Optional reusable size array.\n * @return {Size} Size.\n * @api\n */\nexport function toSize(size, dest) {\n  if (Array.isArray(size)) {\n    return size;\n  }\n  if (dest === undefined) {\n    dest = [size, size];\n  } else {\n    dest[0] = size;\n    dest[1] = size;\n  }\n  return dest;\n}\n"], "mappings": ";AAiBO,SAAS,OAAO,MAAM,KAAK,MAAM;AACtC,MAAI,SAAS,QAAW;AACtB,WAAO,CAAC,GAAG,CAAC;AAAA,EACd;AACA,OAAK,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI;AACxB,OAAK,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI;AACxB,SAAO;AACT;AAOO,SAAS,QAAQ,MAAM;AAC5B,SAAO,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;AAClC;AASO,SAAS,MAAM,MAAM,OAAO,MAAM;AACvC,MAAI,SAAS,QAAW;AACtB,WAAO,CAAC,GAAG,CAAC;AAAA,EACd;AACA,OAAK,CAAC,IAAK,KAAK,CAAC,IAAI,QAAQ,MAAO;AACpC,OAAK,CAAC,IAAK,KAAK,CAAC,IAAI,QAAQ,MAAO;AACpC,SAAO;AACT;AAWO,SAAS,OAAO,MAAM,MAAM;AACjC,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,QAAW;AACtB,WAAO,CAAC,MAAM,IAAI;AAAA,EACpB,OAAO;AACL,SAAK,CAAC,IAAI;AACV,SAAK,CAAC,IAAI;AAAA,EACZ;AACA,SAAO;AACT;", "names": []}