{"version": 3, "sources": ["../../ol/Disposable.js", "../../ol/events/Event.js", "../../ol/functions.js", "../../ol/events/Target.js", "../../ol/events/EventType.js", "../../ol/events.js", "../../ol/Observable.js"], "sourcesContent": ["/**\n * @module ol/Disposable\n */\n\n/**\n * @classdesc\n * Objects that need to clean up after themselves.\n */\nclass Disposable {\n  constructor() {\n    /**\n     * The object has already been disposed.\n     * @type {boolean}\n     * @protected\n     */\n    this.disposed = false;\n  }\n\n  /**\n   * Clean up.\n   */\n  dispose() {\n    if (!this.disposed) {\n      this.disposed = true;\n      this.disposeInternal();\n    }\n  }\n\n  /**\n   * Extension point for disposable objects.\n   * @protected\n   */\n  disposeInternal() {}\n}\n\nexport default Disposable;\n", "/**\n * @module ol/events/Event\n */\n\n/**\n * @classdesc\n * Stripped down implementation of the W3C DOM Level 2 Event interface.\n * See https://www.w3.org/TR/DOM-Level-2-Events/events.html#Events-interface.\n *\n * This implementation only provides `type` and `target` properties, and\n * `stopPropagation` and `preventDefault` methods. It is meant as base class\n * for higher level events defined in the library, and works with\n * {@link module:ol/events/Target~Target}.\n */\nclass BaseEvent {\n  /**\n   * @param {string} type Type.\n   */\n  constructor(type) {\n    /**\n     * @type {boolean}\n     */\n    this.propagationStopped;\n\n    /**\n     * @type {boolean}\n     */\n    this.defaultPrevented;\n\n    /**\n     * The event type.\n     * @type {string}\n     * @api\n     */\n    this.type = type;\n\n    /**\n     * The event target.\n     * @type {Object}\n     * @api\n     */\n    this.target = null;\n  }\n\n  /**\n   * Prevent default. This means that no emulated `click`, `singleclick` or `doubleclick` events\n   * will be fired.\n   * @api\n   */\n  preventDefault() {\n    this.defaultPrevented = true;\n  }\n\n  /**\n   * Stop event propagation.\n   * @api\n   */\n  stopPropagation() {\n    this.propagationStopped = true;\n  }\n}\n\n/**\n * @param {Event|import(\"./Event.js\").default} evt Event\n */\nexport function stopPropagation(evt) {\n  evt.stopPropagation();\n}\n\n/**\n * @param {Event|import(\"./Event.js\").default} evt Event\n */\nexport function preventDefault(evt) {\n  evt.preventDefault();\n}\n\nexport default BaseEvent;\n", "/**\n * @module ol/functions\n */\n\nimport {equals as arrayEquals} from './array.js';\n\n/**\n * Always returns true.\n * @return {boolean} true.\n */\nexport function TRUE() {\n  return true;\n}\n\n/**\n * Always returns false.\n * @return {boolean} false.\n */\nexport function FALSE() {\n  return false;\n}\n\n/**\n * A reusable function, used e.g. as a default for callbacks.\n *\n * @return {void} Nothing.\n */\nexport function VOID() {}\n\n/**\n * Wrap a function in another function that remembers the last return.  If the\n * returned function is called twice in a row with the same arguments and the same\n * this object, it will return the value from the first call in the second call.\n *\n * @param {function(...any): ReturnType} fn The function to memoize.\n * @return {function(...any): ReturnType} The memoized function.\n * @template ReturnType\n */\nexport function memoizeOne(fn) {\n  let called = false;\n\n  /** @type {ReturnType} */\n  let lastResult;\n\n  /** @type {Array<any>} */\n  let lastArgs;\n\n  let lastThis;\n\n  return function () {\n    const nextArgs = Array.prototype.slice.call(arguments);\n    if (!called || this !== lastThis || !arrayEquals(nextArgs, lastArgs)) {\n      called = true;\n      lastThis = this;\n      lastArgs = nextArgs;\n      lastResult = fn.apply(this, arguments);\n    }\n    return lastResult;\n  };\n}\n\n/**\n * @template T\n * @param {function(): (T | Promise<T>)} getter A function that returns a value or a promise for a value.\n * @return {Promise<T>} A promise for the value.\n */\nexport function toPromise(getter) {\n  function promiseGetter() {\n    let value;\n    try {\n      value = getter();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n    if (value instanceof Promise) {\n      return value;\n    }\n    return Promise.resolve(value);\n  }\n  return promiseGetter();\n}\n", "/**\n * @module ol/events/Target\n */\nimport Disposable from '../Disposable.js';\nimport Event from './Event.js';\nimport {VOID} from '../functions.js';\nimport {clear} from '../obj.js';\n\n/**\n * @typedef {EventTarget|Target} EventTargetLike\n */\n\n/**\n * @classdesc\n * A simplified implementation of the W3C DOM Level 2 EventTarget interface.\n * See https://www.w3.org/TR/2000/REC-DOM-Level-2-Events-20001113/events.html#Events-EventTarget.\n *\n * There are two important simplifications compared to the specification:\n *\n * 1. The handling of `useCapture` in `addEventListener` and\n *    `removeEventListener`. There is no real capture model.\n * 2. The handling of `stopPropagation` and `preventDefault` on `dispatchEvent`.\n *    There is no event target hierarchy. When a listener calls\n *    `stopPropagation` or `preventDefault` on an event object, it means that no\n *    more listeners after this one will be called. Same as when the listener\n *    returns false.\n */\nclass Target extends Disposable {\n  /**\n   * @param {*} [target] Default event target for dispatched events.\n   */\n  constructor(target) {\n    super();\n\n    /**\n     * @private\n     * @type {*}\n     */\n    this.eventTarget_ = target;\n\n    /**\n     * @private\n     * @type {Object<string, number>}\n     */\n    this.pendingRemovals_ = null;\n\n    /**\n     * @private\n     * @type {Object<string, number>}\n     */\n    this.dispatching_ = null;\n\n    /**\n     * @private\n     * @type {Object<string, Array<import(\"../events.js\").Listener>>}\n     */\n    this.listeners_ = null;\n  }\n\n  /**\n   * @param {string} type Type.\n   * @param {import(\"../events.js\").Listener} listener Listener.\n   */\n  addEventListener(type, listener) {\n    if (!type || !listener) {\n      return;\n    }\n    const listeners = this.listeners_ || (this.listeners_ = {});\n    const listenersForType = listeners[type] || (listeners[type] = []);\n    if (!listenersForType.includes(listener)) {\n      listenersForType.push(listener);\n    }\n  }\n\n  /**\n   * Dispatches an event and calls all listeners listening for events\n   * of this type. The event parameter can either be a string or an\n   * Object with a `type` property.\n   *\n   * @param {import(\"./Event.js\").default|string} event Event object.\n   * @return {boolean|undefined} `false` if anyone called preventDefault on the\n   *     event object or if any of the listeners returned false.\n   * @api\n   */\n  dispatchEvent(event) {\n    const isString = typeof event === 'string';\n    const type = isString ? event : event.type;\n    const listeners = this.listeners_ && this.listeners_[type];\n    if (!listeners) {\n      return;\n    }\n\n    const evt = isString ? new Event(event) : /** @type {Event} */ (event);\n    if (!evt.target) {\n      evt.target = this.eventTarget_ || this;\n    }\n    const dispatching = this.dispatching_ || (this.dispatching_ = {});\n    const pendingRemovals =\n      this.pendingRemovals_ || (this.pendingRemovals_ = {});\n    if (!(type in dispatching)) {\n      dispatching[type] = 0;\n      pendingRemovals[type] = 0;\n    }\n    ++dispatching[type];\n    let propagate;\n    for (let i = 0, ii = listeners.length; i < ii; ++i) {\n      if ('handleEvent' in listeners[i]) {\n        propagate = /** @type {import(\"../events.js\").ListenerObject} */ (\n          listeners[i]\n        ).handleEvent(evt);\n      } else {\n        propagate = /** @type {import(\"../events.js\").ListenerFunction} */ (\n          listeners[i]\n        ).call(this, evt);\n      }\n      if (propagate === false || evt.propagationStopped) {\n        propagate = false;\n        break;\n      }\n    }\n    if (--dispatching[type] === 0) {\n      let pr = pendingRemovals[type];\n      delete pendingRemovals[type];\n      while (pr--) {\n        this.removeEventListener(type, VOID);\n      }\n      delete dispatching[type];\n    }\n    return propagate;\n  }\n\n  /**\n   * Clean up.\n   */\n  disposeInternal() {\n    this.listeners_ && clear(this.listeners_);\n  }\n\n  /**\n   * Get the listeners for a specified event type. Listeners are returned in the\n   * order that they will be called in.\n   *\n   * @param {string} type Type.\n   * @return {Array<import(\"../events.js\").Listener>|undefined} Listeners.\n   */\n  getListeners(type) {\n    return (this.listeners_ && this.listeners_[type]) || undefined;\n  }\n\n  /**\n   * @param {string} [type] Type. If not provided,\n   *     `true` will be returned if this event target has any listeners.\n   * @return {boolean} Has listeners.\n   */\n  hasListener(type) {\n    if (!this.listeners_) {\n      return false;\n    }\n    return type\n      ? type in this.listeners_\n      : Object.keys(this.listeners_).length > 0;\n  }\n\n  /**\n   * @param {string} type Type.\n   * @param {import(\"../events.js\").Listener} listener Listener.\n   */\n  removeEventListener(type, listener) {\n    const listeners = this.listeners_ && this.listeners_[type];\n    if (listeners) {\n      const index = listeners.indexOf(listener);\n      if (index !== -1) {\n        if (this.pendingRemovals_ && type in this.pendingRemovals_) {\n          // make listener a no-op, and remove later in #dispatchEvent()\n          listeners[index] = VOID;\n          ++this.pendingRemovals_[type];\n        } else {\n          listeners.splice(index, 1);\n          if (listeners.length === 0) {\n            delete this.listeners_[type];\n          }\n        }\n      }\n    }\n  }\n}\n\nexport default Target;\n", "/**\n * @module ol/events/EventType\n */\n\n/**\n * @enum {string}\n * @const\n */\nexport default {\n  /**\n   * Generic change event. Triggered when the revision counter is increased.\n   * @event module:ol/events/Event~BaseEvent#change\n   * @api\n   */\n  CHANGE: 'change',\n\n  /**\n   * Generic error event. Triggered when an error occurs.\n   * @event module:ol/events/Event~BaseEvent#error\n   * @api\n   */\n  ERROR: 'error',\n\n  BLUR: 'blur',\n  CLEAR: 'clear',\n  CONTEXTMENU: 'contextmenu',\n  CLICK: 'click',\n  DBLCLICK: 'dblclick',\n  DRAGENTER: 'dragenter',\n  DRAGOVER: 'dragover',\n  DROP: 'drop',\n  FOCUS: 'focus',\n  KEYDOWN: 'keydown',\n  KEYPRESS: 'keypress',\n  LOAD: 'load',\n  RESIZE: 'resize',\n  TOUCHMOVE: 'touchmove',\n  WHEEL: 'wheel',\n};\n", "/**\n * @module ol/events\n */\nimport {clear} from './obj.js';\n\n/**\n * Key to use with {@link module:ol/Observable.unByKey}.\n * @typedef {Object} EventsKey\n * @property {ListenerFunction} listener Listener.\n * @property {import(\"./events/Target.js\").EventTargetLike} target Target.\n * @property {string} type Type.\n * @api\n */\n\n/**\n * Listener function. This function is called with an event object as argument.\n * When the function returns `false`, event propagation will stop.\n *\n * @typedef {function((Event|import(\"./events/Event.js\").default)): (void|boolean)} ListenerFunction\n * @api\n */\n\n/**\n * @typedef {Object} ListenerObject\n * @property {ListenerFunction} handleEvent HandleEvent listener function.\n */\n\n/**\n * @typedef {ListenerFunction|ListenerObject} Listener\n */\n\n/**\n * Registers an event listener on an event target. Inspired by\n * https://google.github.io/closure-library/api/source/closure/goog/events/events.js.src.html\n *\n * This function efficiently binds a `listener` to a `this` object, and returns\n * a key for use with {@link module:ol/events.unlistenByKey}.\n *\n * @param {import(\"./events/Target.js\").EventTargetLike} target Event target.\n * @param {string} type Event type.\n * @param {ListenerFunction} listener Listener.\n * @param {Object} [thisArg] Object referenced by the `this` keyword in the\n *     listener. Default is the `target`.\n * @param {boolean} [once] If true, add the listener as one-off listener.\n * @return {EventsKey} Unique key for the listener.\n */\nexport function listen(target, type, listener, thisArg, once) {\n  if (thisArg && thisArg !== target) {\n    listener = listener.bind(thisArg);\n  }\n  if (once) {\n    const originalListener = listener;\n    listener = function () {\n      target.removeEventListener(type, listener);\n      originalListener.apply(this, arguments);\n    };\n  }\n  const eventsKey = {\n    target: target,\n    type: type,\n    listener: listener,\n  };\n  target.addEventListener(type, listener);\n  return eventsKey;\n}\n\n/**\n * Registers a one-off event listener on an event target. Inspired by\n * https://google.github.io/closure-library/api/source/closure/goog/events/events.js.src.html\n *\n * This function efficiently binds a `listener` as self-unregistering listener\n * to a `this` object, and returns a key for use with\n * {@link module:ol/events.unlistenByKey} in case the listener needs to be\n * unregistered before it is called.\n *\n * When {@link module:ol/events.listen} is called with the same arguments after this\n * function, the self-unregistering listener will be turned into a permanent\n * listener.\n *\n * @param {import(\"./events/Target.js\").EventTargetLike} target Event target.\n * @param {string} type Event type.\n * @param {ListenerFunction} listener Listener.\n * @param {Object} [thisArg] Object referenced by the `this` keyword in the\n *     listener. Default is the `target`.\n * @return {EventsKey} Key for unlistenByKey.\n */\nexport function listenOnce(target, type, listener, thisArg) {\n  return listen(target, type, listener, thisArg, true);\n}\n\n/**\n * Unregisters event listeners on an event target. Inspired by\n * https://google.github.io/closure-library/api/source/closure/goog/events/events.js.src.html\n *\n * The argument passed to this function is the key returned from\n * {@link module:ol/events.listen} or {@link module:ol/events.listenOnce}.\n *\n * @param {EventsKey} key The key.\n */\nexport function unlistenByKey(key) {\n  if (key && key.target) {\n    key.target.removeEventListener(key.type, key.listener);\n    clear(key);\n  }\n}\n", "/**\n * @module ol/Observable\n */\nimport EventTarget from './events/Target.js';\nimport EventType from './events/EventType.js';\nimport {listen, listenOnce, unlistenByKey} from './events.js';\n\n/***\n * @template {string} Type\n * @template {Event|import(\"./events/Event.js\").default} EventClass\n * @template Return\n * @typedef {(type: Type, listener: (event: EventClass) => ?) => Return} OnSignature\n */\n\n/***\n * @template {string} Type\n * @template Return\n * @typedef {(type: Type[], listener: (event: Event|import(\"./events/Event\").default) => ?) => Return extends void ? void : Return[]} CombinedOnSignature\n */\n\n/**\n * @typedef {'change'|'error'} EventTypes\n */\n\n/***\n * @template Return\n * @typedef {OnSignature<EventTypes, import(\"./events/Event.js\").default, Return> & CombinedOnSignature<EventTypes, Return>} ObservableOnSignature\n */\n\n/**\n * @classdesc\n * Abstract base class; normally only used for creating subclasses and not\n * instantiated in apps.\n * An event target providing convenient methods for listener registration\n * and unregistration. A generic `change` event is always available through\n * {@link module:ol/Observable~Observable#changed}.\n *\n * @fires import(\"./events/Event.js\").default\n * @api\n */\nclass Observable extends EventTarget {\n  constructor() {\n    super();\n\n    this.on =\n      /** @type {ObservableOnSignature<import(\"./events\").EventsKey>} */ (\n        this.onInternal\n      );\n\n    this.once =\n      /** @type {ObservableOnSignature<import(\"./events\").EventsKey>} */ (\n        this.onceInternal\n      );\n\n    this.un = /** @type {ObservableOnSignature<void>} */ (this.unInternal);\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.revision_ = 0;\n  }\n\n  /**\n   * Increases the revision counter and dispatches a 'change' event.\n   * @api\n   */\n  changed() {\n    ++this.revision_;\n    this.dispatchEvent(EventType.CHANGE);\n  }\n\n  /**\n   * Get the version number for this object.  Each time the object is modified,\n   * its version number will be incremented.\n   * @return {number} Revision.\n   * @api\n   */\n  getRevision() {\n    return this.revision_;\n  }\n\n  /**\n   * @param {string|Array<string>} type Type.\n   * @param {function((Event|import(\"./events/Event\").default)): ?} listener Listener.\n   * @return {import(\"./events.js\").EventsKey|Array<import(\"./events.js\").EventsKey>} Event key.\n   * @protected\n   */\n  onInternal(type, listener) {\n    if (Array.isArray(type)) {\n      const len = type.length;\n      const keys = new Array(len);\n      for (let i = 0; i < len; ++i) {\n        keys[i] = listen(this, type[i], listener);\n      }\n      return keys;\n    }\n    return listen(this, /** @type {string} */ (type), listener);\n  }\n\n  /**\n   * @param {string|Array<string>} type Type.\n   * @param {function((Event|import(\"./events/Event\").default)): ?} listener Listener.\n   * @return {import(\"./events.js\").EventsKey|Array<import(\"./events.js\").EventsKey>} Event key.\n   * @protected\n   */\n  onceInternal(type, listener) {\n    let key;\n    if (Array.isArray(type)) {\n      const len = type.length;\n      key = new Array(len);\n      for (let i = 0; i < len; ++i) {\n        key[i] = listenOnce(this, type[i], listener);\n      }\n    } else {\n      key = listenOnce(this, /** @type {string} */ (type), listener);\n    }\n    /** @type {Object} */ (listener).ol_key = key;\n    return key;\n  }\n\n  /**\n   * Unlisten for a certain type of event.\n   * @param {string|Array<string>} type Type.\n   * @param {function((Event|import(\"./events/Event\").default)): ?} listener Listener.\n   * @protected\n   */\n  unInternal(type, listener) {\n    const key = /** @type {Object} */ (listener).ol_key;\n    if (key) {\n      unByKey(key);\n    } else if (Array.isArray(type)) {\n      for (let i = 0, ii = type.length; i < ii; ++i) {\n        this.removeEventListener(type[i], listener);\n      }\n    } else {\n      this.removeEventListener(type, listener);\n    }\n  }\n}\n\n/**\n * Listen for a certain type of event.\n * @function\n * @param {string|Array<string>} type The event type or array of event types.\n * @param {function((Event|import(\"./events/Event\").default)): ?} listener The listener function.\n * @return {import(\"./events.js\").EventsKey|Array<import(\"./events.js\").EventsKey>} Unique key for the listener. If\n *     called with an array of event types as the first argument, the return\n *     will be an array of keys.\n * @api\n */\nObservable.prototype.on;\n\n/**\n * Listen once for a certain type of event.\n * @function\n * @param {string|Array<string>} type The event type or array of event types.\n * @param {function((Event|import(\"./events/Event\").default)): ?} listener The listener function.\n * @return {import(\"./events.js\").EventsKey|Array<import(\"./events.js\").EventsKey>} Unique key for the listener. If\n *     called with an array of event types as the first argument, the return\n *     will be an array of keys.\n * @api\n */\nObservable.prototype.once;\n\n/**\n * Unlisten for a certain type of event.\n * @function\n * @param {string|Array<string>} type The event type or array of event types.\n * @param {function((Event|import(\"./events/Event\").default)): ?} listener The listener function.\n * @api\n */\nObservable.prototype.un;\n\n/**\n * Removes an event listener using the key returned by `on()` or `once()`.\n * @param {import(\"./events.js\").EventsKey|Array<import(\"./events.js\").EventsKey>} key The key returned by `on()`\n *     or `once()` (or an array of keys).\n * @api\n */\nexport function unByKey(key) {\n  if (Array.isArray(key)) {\n    for (let i = 0, ii = key.length; i < ii; ++i) {\n      unlistenByKey(key[i]);\n    }\n  } else {\n    unlistenByKey(/** @type {import(\"./events.js\").EventsKey} */ (key));\n  }\n}\n\nexport default Observable;\n"], "mappings": ";;;;;;;;AAQA,IAAM,aAAN,MAAiB;AAAA,EACf,cAAc;AAMZ,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW;AAChB,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAAA,EAAC;AACrB;AAEA,IAAO,qBAAQ;;;ACrBf,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA,EAId,YAAY,MAAM;AAIhB,SAAK;AAKL,SAAK;AAOL,SAAK,OAAO;AAOZ,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,SAAK,qBAAqB;AAAA,EAC5B;AACF;AAKO,SAAS,gBAAgB,KAAK;AACnC,MAAI,gBAAgB;AACtB;AASA,IAAO,gBAAQ;;;AClER,SAAS,OAAO;AACrB,SAAO;AACT;AAMO,SAAS,QAAQ;AACtB,SAAO;AACT;AAOO,SAAS,OAAO;AAAC;AAWjB,SAAS,WAAW,IAAI;AAC7B,MAAI,SAAS;AAGb,MAAI;AAGJ,MAAI;AAEJ,MAAI;AAEJ,SAAO,WAAY;AACjB,UAAM,WAAW,MAAM,UAAU,MAAM,KAAK,SAAS;AACrD,QAAI,CAAC,UAAU,SAAS,YAAY,CAAC,OAAY,UAAU,QAAQ,GAAG;AACpE,eAAS;AACT,iBAAW;AACX,iBAAW;AACX,mBAAa,GAAG,MAAM,MAAM,SAAS;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AACF;AAOO,SAAS,UAAU,QAAQ;AAChC,WAAS,gBAAgB;AACvB,QAAI;AACJ,QAAI;AACF,cAAQ,OAAO;AAAA,IACjB,SAAS,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;AAAA,IAC3B;AACA,QAAI,iBAAiB,SAAS;AAC5B,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,QAAQ,KAAK;AAAA,EAC9B;AACA,SAAO,cAAc;AACvB;;;ACrDA,IAAM,SAAN,cAAqB,mBAAW;AAAA;AAAA;AAAA;AAAA,EAI9B,YAAY,QAAQ;AAClB,UAAM;AAMN,SAAK,eAAe;AAMpB,SAAK,mBAAmB;AAMxB,SAAK,eAAe;AAMpB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,MAAM,UAAU;AAC/B,QAAI,CAAC,QAAQ,CAAC,UAAU;AACtB;AAAA,IACF;AACA,UAAM,YAAY,KAAK,eAAe,KAAK,aAAa,CAAC;AACzD,UAAM,mBAAmB,UAAU,IAAI,MAAM,UAAU,IAAI,IAAI,CAAC;AAChE,QAAI,CAAC,iBAAiB,SAAS,QAAQ,GAAG;AACxC,uBAAiB,KAAK,QAAQ;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,cAAc,OAAO;AACnB,UAAM,WAAW,OAAO,UAAU;AAClC,UAAM,OAAO,WAAW,QAAQ,MAAM;AACtC,UAAM,YAAY,KAAK,cAAc,KAAK,WAAW,IAAI;AACzD,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AAEA,UAAM,MAAM,WAAW,IAAI,cAAM,KAAK;AAAA;AAAA,MAA0B;AAAA;AAChE,QAAI,CAAC,IAAI,QAAQ;AACf,UAAI,SAAS,KAAK,gBAAgB;AAAA,IACpC;AACA,UAAM,cAAc,KAAK,iBAAiB,KAAK,eAAe,CAAC;AAC/D,UAAM,kBACJ,KAAK,qBAAqB,KAAK,mBAAmB,CAAC;AACrD,QAAI,EAAE,QAAQ,cAAc;AAC1B,kBAAY,IAAI,IAAI;AACpB,sBAAgB,IAAI,IAAI;AAAA,IAC1B;AACA,MAAE,YAAY,IAAI;AAClB,QAAI;AACJ,aAAS,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI,EAAE,GAAG;AAClD,UAAI,iBAAiB,UAAU,CAAC,GAAG;AACjC;AAAA,QACE,UAAU,CAAC,EACX,YAAY,GAAG;AAAA,MACnB,OAAO;AACL;AAAA,QACE,UAAU,CAAC,EACX,KAAK,MAAM,GAAG;AAAA,MAClB;AACA,UAAI,cAAc,SAAS,IAAI,oBAAoB;AACjD,oBAAY;AACZ;AAAA,MACF;AAAA,IACF;AACA,QAAI,EAAE,YAAY,IAAI,MAAM,GAAG;AAC7B,UAAI,KAAK,gBAAgB,IAAI;AAC7B,aAAO,gBAAgB,IAAI;AAC3B,aAAO,MAAM;AACX,aAAK,oBAAoB,MAAM,IAAI;AAAA,MACrC;AACA,aAAO,YAAY,IAAI;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,SAAK,cAAc,MAAM,KAAK,UAAU;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,MAAM;AACjB,WAAQ,KAAK,cAAc,KAAK,WAAW,IAAI,KAAM;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,MAAM;AAChB,QAAI,CAAC,KAAK,YAAY;AACpB,aAAO;AAAA,IACT;AACA,WAAO,OACH,QAAQ,KAAK,aACb,OAAO,KAAK,KAAK,UAAU,EAAE,SAAS;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,MAAM,UAAU;AAClC,UAAM,YAAY,KAAK,cAAc,KAAK,WAAW,IAAI;AACzD,QAAI,WAAW;AACb,YAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,UAAI,UAAU,IAAI;AAChB,YAAI,KAAK,oBAAoB,QAAQ,KAAK,kBAAkB;AAE1D,oBAAU,KAAK,IAAI;AACnB,YAAE,KAAK,iBAAiB,IAAI;AAAA,QAC9B,OAAO;AACL,oBAAU,OAAO,OAAO,CAAC;AACzB,cAAI,UAAU,WAAW,GAAG;AAC1B,mBAAO,KAAK,WAAW,IAAI;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,iBAAQ;;;ACnLf,IAAO,oBAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOR,OAAO;AAAA,EAEP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,MAAM;AAAA,EACN,OAAO;AAAA,EACP,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,OAAO;AACT;;;ACQO,SAAS,OAAO,QAAQ,MAAM,UAAU,SAAS,MAAM;AAC5D,MAAI,WAAW,YAAY,QAAQ;AACjC,eAAW,SAAS,KAAK,OAAO;AAAA,EAClC;AACA,MAAI,MAAM;AACR,UAAM,mBAAmB;AACzB,eAAW,WAAY;AACrB,aAAO,oBAAoB,MAAM,QAAQ;AACzC,uBAAiB,MAAM,MAAM,SAAS;AAAA,IACxC;AAAA,EACF;AACA,QAAM,YAAY;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO,iBAAiB,MAAM,QAAQ;AACtC,SAAO;AACT;AAsBO,SAAS,WAAW,QAAQ,MAAM,UAAU,SAAS;AAC1D,SAAO,OAAO,QAAQ,MAAM,UAAU,SAAS,IAAI;AACrD;AAWO,SAAS,cAAc,KAAK;AACjC,MAAI,OAAO,IAAI,QAAQ;AACrB,QAAI,OAAO,oBAAoB,IAAI,MAAM,IAAI,QAAQ;AACrD,UAAM,GAAG;AAAA,EACX;AACF;;;AChEA,IAAM,aAAN,cAAyB,eAAY;AAAA,EACnC,cAAc;AACZ,UAAM;AAEN,SAAK;AAAA,IAED,KAAK;AAGT,SAAK;AAAA,IAED,KAAK;AAGT,SAAK;AAAA,IAAiD,KAAK;AAM3D,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,MAAE,KAAK;AACP,SAAK,cAAc,kBAAU,MAAM;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,MAAM,UAAU;AACzB,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,YAAM,MAAM,KAAK;AACjB,YAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,eAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,aAAK,CAAC,IAAI,OAAO,MAAM,KAAK,CAAC,GAAG,QAAQ;AAAA,MAC1C;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,MAAO;AAAA;AAAA,MAA6B;AAAA,MAAO;AAAA,IAAQ;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,MAAM,UAAU;AAC3B,QAAI;AACJ,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,YAAM,MAAM,KAAK;AACjB,YAAM,IAAI,MAAM,GAAG;AACnB,eAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,YAAI,CAAC,IAAI,WAAW,MAAM,KAAK,CAAC,GAAG,QAAQ;AAAA,MAC7C;AAAA,IACF,OAAO;AACL,YAAM;AAAA,QAAW;AAAA;AAAA,QAA6B;AAAA,QAAO;AAAA,MAAQ;AAAA,IAC/D;AACsB,IAAC,SAAU,SAAS;AAC1C,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,MAAM,UAAU;AACzB,UAAM;AAAA;AAAA,MAA6B,SAAU;AAAA;AAC7C,QAAI,KAAK;AACP,cAAQ,GAAG;AAAA,IACb,WAAW,MAAM,QAAQ,IAAI,GAAG;AAC9B,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,aAAK,oBAAoB,KAAK,CAAC,GAAG,QAAQ;AAAA,MAC5C;AAAA,IACF,OAAO;AACL,WAAK,oBAAoB,MAAM,QAAQ;AAAA,IACzC;AAAA,EACF;AACF;AAYA,WAAW,UAAU;AAYrB,WAAW,UAAU;AASrB,WAAW,UAAU;AAQd,SAAS,QAAQ,KAAK;AAC3B,MAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,aAAS,IAAI,GAAG,KAAK,IAAI,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC5C,oBAAc,IAAI,CAAC,CAAC;AAAA,IACtB;AAAA,EACF,OAAO;AACL;AAAA;AAAA,MAA8D;AAAA,IAAI;AAAA,EACpE;AACF;AAEA,IAAO,qBAAQ;", "names": []}