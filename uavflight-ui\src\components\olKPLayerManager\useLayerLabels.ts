import { reactive } from 'vue';
import { useMapLayerManagerStore } from '/@/stores/olMapLayer/mapLayerManager';
import { ElMessage } from 'element-plus';
import { Style, Fill, Stroke, Text } from 'ol/style';

// 标注设置对话框状态
export const labelDialog = reactive({
  visible: false,
  currentLayer: null as any,
  availableFields: [] as string[],
  selectedField: '',
  labelColor: '#ffffff',
  fontSize: 12,
  showLabels: true,
  maxLabelResolution: 100  // 添加最大分辨率属性
});

// 显示标注设置对话框
export function showLabelDialog(layer: any) {
  if (!layer || layer.type !== 'vector') {
    return;
  }
  
  try {
    const mapLayerStore = useMapLayerManagerStore();
    const layerId = layer.id;
    const layerObj = mapLayerStore.getLayerById(layerId);
    
    if (!layerObj || !layerObj.layerInstance) {
      ElMessage.warning('无法获取图层实例');
      return;
    }
    
    // 设置当前图层
    labelDialog.currentLayer = layer;
    
    // 获取图层的第一个要素，用于提取属性字段
    const source = layerObj.layerInstance.getSource();
    const features = source.getFeatures();
    
    if (!features || features.length === 0) {
      ElMessage.warning('图层没有要素数据');
      return;
    }
    
    // 获取第一个要素的属性
    const properties = features[0].getProperties();
    
    // 过滤掉几何属性和内部属性
    const fields = Object.keys(properties).filter(key => 
      key !== 'geometry' && !key.startsWith('_')
    );
    
    if (fields.length === 0) {
      ElMessage.warning('图层要素没有可用的属性字段');
      return;
    }
    
    // 设置可用字段
    labelDialog.availableFields = fields;
    
    // 尝试获取当前的标注配置
    const layerWithLabels = layerObj as any;
    if (layerWithLabels.labelField) {
      labelDialog.selectedField = layerWithLabels.labelField;
      labelDialog.labelColor = layerWithLabels.labelStyle?.color || '#ffffff';
      labelDialog.fontSize = layerWithLabels.labelStyle?.fontSize || 24;
      labelDialog.showLabels = layerWithLabels.showLabels === true;
      labelDialog.maxLabelResolution = layerWithLabels.maxLabelResolution || 100; // 获取已保存的最大分辨率
    } else {
      // 默认选择第一个字段
      labelDialog.selectedField = fields[0];
      labelDialog.labelColor = '#ffffff';
      labelDialog.fontSize = 24;
      labelDialog.showLabels = false;
      labelDialog.maxLabelResolution = 100; // 默认值
    }
    
    // 显示对话框
    labelDialog.visible = true;
  } catch (error) {
    console.error('打开标注设置对话框失败:', error);
    ElMessage.error('无法获取图层属性');
  }
}

// 应用标注设置
export function applyLabels() {
  if (!labelDialog.currentLayer) return;
  
  try {
    const mapLayerStore = useMapLayerManagerStore();
    const layerId = labelDialog.currentLayer.id;
    const layer = mapLayerStore.getLayerById(layerId);
    
    if (!layer || !layer.layerInstance) {
      ElMessage.error('图层不存在或未加载');
      return;
    }
    
    // 保存标注配置到图层
    const layerWithLabels = layer as any;
    layerWithLabels.labelField = labelDialog.selectedField;
    layerWithLabels.labelStyle = {
      color: labelDialog.labelColor,
      fontSize: labelDialog.fontSize
    };
    layerWithLabels.showLabels = labelDialog.showLabels;
    layerWithLabels.maxLabelResolution = labelDialog.maxLabelResolution; // 保存最大分辨率设置
    
    // 获取图层的当前样式
    const currentStyle = layer.layerInstance.getStyle();
    
    // 创建新的样式函数，使用闭包保存当前图层的标注设置
    const labelSettings = {
      field: labelDialog.selectedField,
      color: labelDialog.labelColor,
      fontSize: labelDialog.fontSize,
      showLabels: labelDialog.showLabels,
      maxLabelResolution: labelDialog.maxLabelResolution // 包含最大分辨率设置
    };
    
    const newStyleFunction = (feature: any, resolution: number) => {
      // 基础样式（可能是函数）
      let baseStyle = typeof currentStyle === 'function' 
        ? currentStyle(feature, resolution) 
        : currentStyle;
      
      // 如果是数组，取第一个样式
      if (Array.isArray(baseStyle)) {
        baseStyle = baseStyle[0];
      }
      
      // 如果不显示标注，直接返回基础样式
      if (!labelSettings.showLabels) {
        // 确保返回的样式没有文本
        if (baseStyle) {
          return new Style({
            image: baseStyle.getImage(),
            fill: baseStyle.getFill(),
            stroke: baseStyle.getStroke(),
            // 不设置text属性，这样就不会显示标注
          });
        }
        return baseStyle;
      }
      
      // 如果当前分辨率大于最大标签分辨率，不显示标签
      if (resolution > labelSettings.maxLabelResolution) {
        if (baseStyle) {
          return new Style({
            image: baseStyle.getImage(),
            fill: baseStyle.getFill(),
            stroke: baseStyle.getStroke()
          });
        }
        return baseStyle;
      }
      
      // 获取标注文本
      const labelValue = feature.get(labelSettings.field);
      if (labelValue === undefined || labelValue === null) {
        return baseStyle;
      }
      
      // 创建文本样式
      const textStyle = new Text({
        text: String(labelValue),
        font: `${labelSettings.fontSize}px sans-serif`,
        fill: new Fill({
          color: labelSettings.color
        }),
        stroke: new Stroke({
          color: '#000000',
          width: 2
        }),
        offsetY: -15,
        overflow: true
      });
      
      // 创建包含文本的新样式
      const newStyle = new Style({
        image: baseStyle.getImage(),
        fill: baseStyle.getFill(),
        stroke: baseStyle.getStroke(),
        text: textStyle
      });
      
      return newStyle;
    };
    
    // 应用新样式
    layer.layerInstance.setStyle(newStyleFunction);
    
    ElMessage.success('图层标注已更新');
    labelDialog.visible = false;
  } catch (error) {
    console.error('应用标注设置失败:', error);
    ElMessage.error('更新图层标注失败');
  }
} 