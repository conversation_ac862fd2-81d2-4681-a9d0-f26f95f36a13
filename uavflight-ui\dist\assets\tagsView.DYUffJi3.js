const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/contextmenu.SCQO7rZH.js","assets/vue.CnN__PXn.js","assets/index.C0-0gsfl.js","assets/index.CB8Sa4Ps.css","assets/contextmenu.A9zZL5vq.css"])))=>i.map(i=>d[i]);
import{w as ae,u as be,b as qe,o as se,e as v,x as Ce,a as ke,S as L,E as Re,c as xe,aa as ie,q as De,__tla as Se}from"./index.C0-0gsfl.js";import{d as re,k as S,s as ne,l as Ae,z as Te,A as Ie,c as E,h as _e,i as Pe,X as Ue,o as Oe,O as $e,w as Ee,B as le,a as C,b as V,t as j,v as je,f as ue,g as M,u as g,F as oe,p as Me,Q as k,D as R,e as F,G as Fe,j as We,y as W}from"./vue.CnN__PXn.js";import{S as ze}from"./sortable.esm.BGML4dzN.js";let me,He=Promise.all([(()=>{try{return Se}catch{}})()]).then(async()=>{let z,H,X;z=["data-url","onContextmenu","onMousedown","onClick"],H={key:0,class:"iconfont icon-webicon318 layout-navbars-tagsview-ul-li-iconfont"},X=re({name:"layoutTagsView"}),me=De(re({...X,setup(Xe){const ce=We(()=>ke(()=>import("./contextmenu.SCQO7rZH.js").then(async t=>(await t.__tla,t)),__vite__mapDeps([0,1,2,3,4]))),p=S([]),x=S(),B=S(),he=S(),we=ae(),pe=be(),G=ae(),{themeConfig:K}=ne(pe),{tagsViewRoutes:ge,favoriteRoutes:fe}=ne(G),d=qe(),w=Ae(),c=Te(),s=Ie({routeActive:"",routePath:w.path,dropdown:{x:"",y:""},sortable:"",tagsRefsIndex:0,tagsViewList:[],tagsViewRoutesList:[]}),ye=E(()=>K.value.tagsStyle),m=E(()=>K.value),de=E(()=>t=>se.setMenuI18n(t)),D=t=>m.value.isShareTagsView?t.path===s.routePath:(t.query&&Object.keys(t.query).length||t.params&&Object.keys(t.params).length)&&t.url?t.url===s.routeActive:t.path===s.routeActive,f=t=>{L.set("tagsViewList",t)},Q=async()=>{s.routeActive=await y(w),s.routePath=await w.meta.isDynamic?w.meta.isDynamicPath:w.path,s.tagsViewList=[],s.tagsViewRoutesList=ge.value,Ve()},Ve=async()=>{L.get("tagsViewList")&&m.value.isCacheTagsView?s.tagsViewList=await L.get("tagsViewList"):(await s.tagsViewRoutesList.map(t=>{var e;(e=t.meta)!=null&&e.isAffix&&!t.meta.isHide&&(t.url=y(t),s.tagsViewList.push({...t}),d.addCachedView(t))}),await A(w.path,w)),ee(m.value.isShareTagsView?s.routePath:s.routeActive)},Y=async(t,e)=>{var r,i;let a=(r=e==null?void 0:e.meta)!=null&&r.isDynamic?e.meta.isDynamicPath:t;if(s.tagsViewList.filter(u=>{var l,n;return u.path===a&&ie((l=e==null?void 0:e.meta)!=null&&l.isDynamic?u.params?u.params:null:u.query?u.query:null,(n=e==null?void 0:e.meta)!=null&&n.isDynamic?e!=null&&e.params?e==null?void 0:e.params:null:e!=null&&e.query?e==null?void 0:e.query:null)}).length<=0){let u=s.tagsViewRoutesList.find(l=>l.path===a);if(!u||u.meta.isAffix||u.meta.isLink&&!u.meta.isIframe)return!1;(i=e==null?void 0:e.meta)!=null&&i.isDynamic?u.params=e.params:u.query=e==null?void 0:e.query,u.url=y(u),s.tagsViewList.push({...u}),await d.addCachedView(u),f(s.tagsViewList)}},J=(t,e)=>{var r;let a=(r=e==null?void 0:e.meta)!=null&&r.isDynamic?e.meta.isDynamicPath:t;s.tagsViewList.forEach(i=>{var u,l,n;i.path!==a||ie((u=e==null?void 0:e.meta)!=null&&u.isDynamic?i.params?i.params:null:i.query?i.query:null,(l=e==null?void 0:e.meta)!=null&&l.isDynamic?e!=null&&e.params?e==null?void 0:e.params:null:e!=null&&e.query?e==null?void 0:e.query:null)||((n=e==null?void 0:e.meta)!=null&&n.isDynamic?i.params=e.params:i.query=e==null?void 0:e.query,i.url=y(i),f(s.tagsViewList))})},A=(t,e)=>{W(async()=>{var r,i,u;let a;if((r=e==null?void 0:e.meta)!=null&&r.isDynamic){if(m.value.isShareTagsView?await J(t,e):await Y(t,e),s.tagsViewList.some(l=>{var n;return l.path===((n=e==null?void 0:e.meta)==null?void 0:n.isDynamicPath)}))return f(s.tagsViewList),!1;a=s.tagsViewRoutesList.find(l=>{var n;return l.path===((n=e==null?void 0:e.meta)==null?void 0:n.isDynamicPath)})}else{if(m.value.isShareTagsView?await J(t,e):await Y(t,e),s.tagsViewList.some(l=>l.path===t))return f(s.tagsViewList),!1;a=s.tagsViewRoutesList.find(l=>l.path===t)}return!!a&&!((i=a==null?void 0:a.meta)!=null&&i.isLink&&!a.meta.isIframe)&&((u=e==null?void 0:e.meta)!=null&&u.isDynamic?a.params=e!=null&&e.params?e==null?void 0:e.params:w.params:a.query=e!=null&&e.query?e==null?void 0:e.query:w.query,a.url=y(a),await d.addCachedView(a),await s.tagsViewList.push({...a}),void await f(s.tagsViewList))})},N=async t=>{var r;const e=decodeURI(t);let a={};if(s.tagsViewList.forEach(i=>{i.transUrl=q(i),i.transUrl?i.transUrl===q(i)&&(a=i):i.path===e&&(a=i)}),!a)return!1;await d.delCachedView(a),v.emit("onTagsViewRefreshRouterView",t),(r=a.meta)!=null&&r.isKeepAlive&&d.addCachedView(a)},T=t=>{s.tagsViewList.map((e,a,r)=>{var i;(i=e.meta)!=null&&i.isAffix||(m.value.isShareTagsView?e.path===t:e.url===t)&&(d.delCachedView(e),s.tagsViewList.splice(a,1),setTimeout(()=>{(s.tagsViewList.length===a&&m.value.isShareTagsView?s.routePath===t:s.routeActive===t)?r[r.length-1].meta.isDynamic?a!==r.length?c.push({name:r[a].name,params:r[a].params}):c.push({name:r[r.length-1].name,params:r[r.length-1].params}):a!==r.length?c.push({path:r[a].path,query:r[a].query}):c.push({path:r[r.length-1].path,query:r[r.length-1].query}):(s.tagsViewList.length!==a&&m.value.isShareTagsView?s.routePath===t:s.routeActive===t)&&(r[a].meta.isDynamic?c.push({name:r[a].name,params:r[a].params}):c.push({path:r[a].path,query:r[a].query}))},0))}),f(s.tagsViewList)},Z=t=>{let e={};return s.tagsViewList.forEach(a=>{a.transUrl=q(a),a.transUrl?a.transUrl===q(a)&&a.transUrl===t.commonUrl&&(e=a):a.path===decodeURI(t.path)&&(e=a)}),e||null},I=async t=>{if(t.commonUrl=q(t),!Z(t))return Re({type:"warning",message:"\u8BF7\u6B63\u786E\u8F93\u5165\u8DEF\u5F84\u53CA\u5B8C\u6574\u53C2\u6570\uFF08query\u3001params\uFF09"});const{path:e,name:a,params:r,query:i,meta:u,url:l}=Z(t);switch(t.contextMenuClickId){case 0:u.isDynamic?await c.push({name:a,params:r}):await c.push({path:e,query:i}),N(w.fullPath);break;case 1:T(m.value.isShareTagsView?e:l);break;case 2:u.isDynamic?await c.push({name:a,params:r}):await c.push({path:e,query:i}),(n=>{L.get("tagsViewList")&&(s.tagsViewList=[],L.get("tagsViewList").map(o=>{var h;(h=o.meta)!=null&&h.isAffix&&!o.meta.isHide&&(o.url=y(o),d.delOthersCachedViews(o),s.tagsViewList.push({...o}))}),A(n,w),f(s.tagsViewList))})(e);break;case 3:L.get("tagsViewList")&&(d.delAllCachedViews(),s.tagsViewList=[],L.get("tagsViewList").map(n=>{var o;(o=n.meta)!=null&&o.isAffix&&!n.meta.isHide&&(n.url=y(n),s.tagsViewList.push({...n}),c.push({path:s.tagsViewList[s.tagsViewList.length-1].path}))}),f(s.tagsViewList));break;case 4:(async n=>{const o=s.tagsViewList.find(h=>m.value.isShareTagsView?h.path===n:h.url===n);o.meta.isDynamic?await c.push({name:o.name,params:o.params}):await c.push({name:o.name,query:o.query}),we.setCurrenFullscreen(!0)})(m.value.isShareTagsView?e:l);break;case 5:ve(t)}},ve=t=>{fe.value.find(e=>e.path===t.path)?xe().error("\u5DF2\u7ECF\u5B58\u5728\u6536\u85CF"):G.setFavoriteRoutes(t)},q=t=>{var r,i;let e=t.query&&Object.keys(t.query).length>0?t.query:t.params;if(!e)return"";let a="";for(let[u,l]of Object.entries(e))(r=t.meta)!=null&&r.isDynamic?a+=`/${l}`:a+=`&${u}=${l}`;return(i=t.meta)!=null&&i.isDynamic?t.isFnClick?decodeURI(t.path):`${t.path.split(":")[0]}${a.replace(/^\//,"")}`:`${t.path}${a.replace(/^&/,"?")}`},y=t=>{var r;let e=t.query&&Object.keys(t.query).length>0?t.query:t.params;if(!e||Object.keys(e).length<=0)return t.path;let a="";for(let i in e)a+=e[i];return`${(r=t.meta)!=null&&r.isDynamic?t.meta.isDynamicPath:t.path}-${a}`},Le=t=>{x.value.$refs.wrapRef.scrollLeft+=t.wheelDelta/4},ee=t=>{W(async()=>{let e=await s.tagsViewList;s.tagsRefsIndex=e.findIndex(a=>m.value.isShareTagsView?a.path===t:a.url===t),W(()=>{if(p.value.length<=0)return!1;let a=p.value[s.tagsRefsIndex],r=s.tagsRefsIndex,i=p.value.length,u=p.value[0],l=p.value[p.value.length-1],n=x.value.$refs.wrapRef,o=n.scrollWidth,h=n.offsetWidth,b=n.scrollLeft,U=p.value[s.tagsRefsIndex-1],te=p.value[s.tagsRefsIndex+1],O=0,$=0;a===u?n.scrollLeft=0:a===l?n.scrollLeft=o-h:(O=r===0?u.offsetLeft-5:(U==null?void 0:U.offsetLeft)-5,$=r===i?l.offsetLeft+l.offsetWidth+5:te.offsetLeft+te.offsetWidth+5,$>b+h?n.scrollLeft=$-h:O<b&&(n.scrollLeft=O)),x.value.update()})})},_=async()=>{const t=document.querySelector(".layout-navbars-tagsview-ul");if(!t)return!1;s.sortable.el&&s.sortable.destroy(),s.sortable=ze.create(t,{animation:300,dataIdAttr:"data-url",disabled:!m.value.isSortableTagsView,onEnd:()=>{const e=[];s.sortable.toArray().map(a=>{s.tagsViewList.map(r=>{r.url===a&&e.push({...r})})}),f(e)}})},P=async()=>{await _(),se.isMobile()&&s.sortable.el&&s.sortable.destroy()};return _e(()=>{P(),window.addEventListener("resize",P),v.on("onCurrentContextmenuClick",t=>{t.isFnClick=!0,I(t)}),v.on("openOrCloseSortable",()=>{_()}),v.on("openShareTagsView",()=>{m.value.isShareTagsView&&(c.push("/home/<USER>"),s.tagsViewList=[],s.tagsViewRoutesList.map(t=>{var e;(e=t.meta)!=null&&e.isAffix&&!t.meta.isHide&&(t.url=y(t),s.tagsViewList.push({...t}))}))})}),Pe(()=>{v.off("onCurrentContextmenuClick",()=>{}),v.off("openOrCloseSortable",()=>{}),v.off("openShareTagsView",()=>{}),window.removeEventListener("resize",P)}),Ue(()=>{p.value=[]}),Oe(()=>{Q(),_()}),$e(async t=>{s.routeActive=y(t),s.routePath=t.meta.isDynamic?t.meta.isDynamicPath:t.path,await A(t.path,t),ee(m.value.isShareTagsView?s.routePath:s.routeActive)}),Ee(Ce.state,t=>{if(t.tagsViewRoutes.tagsViewRoutes.length===s.tagsViewRoutesList.length)return!1;Q()},{deep:!0}),(t,e)=>{const a=le("SvgIcon"),r=le("el-scrollbar");return V(),C("div",{class:M(["layout-navbars-tagsview",{"layout-navbars-tagsview-shadow":g(m).layout==="classic"}])},[j(r,{ref_key:"scrollbarRef",ref:x,onWheel:k(Le,["prevent"])},{default:je(()=>[ue("ul",{class:M(["layout-navbars-tagsview-ul",g(ye)]),ref_key:"tagsUlRef",ref:he},[(V(!0),C(oe,null,Me(g(s).tagsViewList,(i,u)=>(V(),C("li",{key:u,class:M(["layout-navbars-tagsview-ul-li",{"is-active":D(i)}]),"data-url":i.url,onContextmenu:k(l=>((n,o)=>{const{clientX:h,clientY:b}=o;s.dropdown.x=h,s.dropdown.y=b,B.value.openContextmenu(n)})(i,l),["prevent"]),onMousedown:l=>((n,o)=>{var h;if(!((h=n.meta)!=null&&h.isAffix)&&o.button===1){const b=Object.assign({},{contextMenuClickId:1,...n});I(b)}})(i,l),onClick:l=>((n,o)=>{s.tagsRefsIndex=o,c.push(n)})(i,u),ref_for:!0,ref:l=>{l&&(g(p)[u]=l)}},[D(i)?(V(),C("i",H)):R("",!0),!D(i)&&g(m).isTagsviewIcon?(V(),F(a,{key:1,name:i.meta.icon,class:"pr5"},null,8,["name"])):R("",!0),ue("span",null,Fe(g(de)(i)),1),D(i)?(V(),C(oe,{key:2},[j(a,{name:"ele-RefreshRight",class:"ml5 layout-navbars-tagsview-ul-li-refresh",onClick:e[0]||(e[0]=k(l=>N(t.$route.fullPath),["stop"]))}),i.meta.isAffix?R("",!0):(V(),F(a,{key:0,name:"ele-Close",class:"layout-navbars-tagsview-ul-li-icon layout-icon-active",onClick:k(l=>T(g(m).isShareTagsView?i.path:i.url),["stop"])},null,8,["onClick"]))],64)):R("",!0),i.meta.isAffix?R("",!0):(V(),F(a,{key:3,name:"ele-Close",class:"layout-navbars-tagsview-ul-li-icon layout-icon-three",onClick:k(l=>T(g(m).isShareTagsView?i.path:i.url),["stop"])},null,8,["onClick"]))],42,z))),128))],2)]),_:1},512),j(g(ce),{dropdown:g(s).dropdown,ref_key:"contextmenuRef",ref:B,onCurrentContextmenuClick:I},null,8,["dropdown"])],2)}}}),[["__scopeId","data-v-1af161e2"]])});export{He as __tla,me as default};
