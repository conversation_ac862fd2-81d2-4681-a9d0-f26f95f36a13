<!--
 * @Author: AI Assistant 
 * @Date: 2025-07-15 
 * @Description: 基础图层面板组件
 *  
 * Copyright (c) 2025 by XMFX团队, All Rights Reserved. 
-->
<template>
  <div class="base-map-layer-panel">
    <div class="panel-title">
      <span>基础图层</span>
    </div>
    <div class="panel-content">
      <div class="layer-list">
        <div class="layer-group" v-for="(group, groupIndex) in layerGroups" :key="groupIndex">
          <div class="group-header">
            <div class="group-title">{{ group.name }}</div>
            <el-switch v-model="group.visible" @change="toggleGroupVisibility(group)"></el-switch>
          </div>
          <div class="group-content" v-if="group.visible">
            <div class="layer-item" v-for="(layer, layerIndex) in group.layers" :key="layerIndex">
              <div class="item-header">
                <el-checkbox v-model="layer.visible" @change="toggleLayerVisibility(layer)">{{ layer.name }}</el-checkbox>
              </div>
              <div class="item-content" v-if="layer.visible">
                <div class="opacity-control">
                  <span>透明度：</span>
                  <el-slider v-model="layer.opacity" :min="0" :max="100" :step="10" 
                    @change="handleOpacityChange(layer)" show-tooltip></el-slider>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

// 图层组数据
const layerGroups = reactive([
  {
    id: '1',
    name: '地图底图',
    visible: true,
    layers: [
      { id: '101', name: '卫星影像', visible: true, opacity: 100, type: 'satellite' },
      { id: '102', name: '行政区划', visible: true, opacity: 80, type: 'boundary' },
      { id: '103', name: '地形图层', visible: false, opacity: 100, type: 'terrain' }
    ]
  },
  {
    id: '2',
    name: '辅助图层',
    visible: true,
    layers: [
      { id: '201', name: '道路网络', visible: true, opacity: 100, type: 'road' },
      { id: '202', name: '水系', visible: false, opacity: 80, type: 'water' }
    ]
  }
]);

// 切换图层组可见性
const toggleGroupVisibility = (group: any) => {
  console.log(`图层组 ${group.name} 可见性已更改为: ${group.visible}`);
  // 更新组内所有图层状态
  group.layers.forEach((layer: any) => {
    layer.visible = group.visible;
  });
};

// 切换单个图层可见性
const toggleLayerVisibility = (layer: any) => {
  console.log(`图层 ${layer.name} 可见性已更改为: ${layer.visible}`);
  // 这里添加更新图层可见性的逻辑
};

// 处理图层透明度变化
const handleOpacityChange = (layer: any) => {
  console.log(`图层 ${layer.name} 透明度已更改为: ${layer.opacity}%`);
  // 这里添加更新图层透明度的逻辑
};

onMounted(() => {
  console.log('基础图层面板已加载');
});
</script>

<style lang="scss" scoped>
.base-map-layer-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
  padding: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.layer-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.layer-group {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.group-header {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(64, 158, 255, 0.15);
  border-radius: 4px;
}

.group-title {
  font-size: 14px;
  font-weight: 600;
  color: #e6e6e6;
}

.group-content {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.layer-item {
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 4px;
  padding: 8px;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-content {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed rgba(255, 255, 255, 0.1);
}

.opacity-control {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

:deep(.el-checkbox__label) {
  color: #fff;
}

:deep(.el-switch__core) {
  border-color: rgba(64, 158, 255, 0.3);
  background-color: rgba(64, 158, 255, 0.3);
}

:deep(.el-switch.is-checked .el-switch__core) {
  border-color: #409eff;
  background-color: #409eff;
}

:deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.2);
}

:deep(.el-slider__bar) {
  background-color: #409eff;
}

:deep(.el-slider__button) {
  border: 2px solid #409eff;
}
</style> 