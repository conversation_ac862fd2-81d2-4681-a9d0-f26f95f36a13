{"version": 3, "sources": ["../../ol/tilecoord.js", "../../ol/tilegrid/TileGrid.js"], "sourcesContent": ["/**\n * @module ol/tilecoord\n */\n\n/**\n * An array of three numbers representing the location of a tile in a tile\n * grid. The order is `z` (zoom level), `x` (column), and `y` (row).\n * @typedef {Array<number>} TileCoord\n * @api\n */\n\n/**\n * @param {number} z Z.\n * @param {number} x X.\n * @param {number} y Y.\n * @param {TileCoord} [tileCoord] Tile coordinate.\n * @return {TileCoord} Tile coordinate.\n */\nexport function createOrUpdate(z, x, y, tileCoord) {\n  if (tileCoord !== undefined) {\n    tileCoord[0] = z;\n    tileCoord[1] = x;\n    tileCoord[2] = y;\n    return tileCoord;\n  }\n  return [z, x, y];\n}\n\n/**\n * @param {number} z Z.\n * @param {number} x X.\n * @param {number} y Y.\n * @return {string} Key.\n */\nexport function getKeyZXY(z, x, y) {\n  return z + '/' + x + '/' + y;\n}\n\n/**\n * Get the key for a tile coord.\n * @param {TileCoord} tileCoord The tile coord.\n * @return {string} Key.\n */\nexport function getKey(tileCoord) {\n  return getKeyZXY(tileCoord[0], tileCoord[1], tileCoord[2]);\n}\n\n/**\n * Get the tile cache key for a tile key obtained through `tile.getKey()`.\n * @param {string} tileKey The tile key.\n * @return {string} The cache key.\n */\nexport function getCacheKeyForTileKey(tileKey) {\n  const [z, x, y] = tileKey\n    .substring(tileKey.lastIndexOf('/') + 1, tileKey.length)\n    .split(',')\n    .map(Number);\n  return getKeyZXY(z, x, y);\n}\n\n/**\n * Get a tile coord given a key.\n * @param {string} key The tile coord key.\n * @return {TileCoord} The tile coord.\n */\nexport function fromKey(key) {\n  return key.split('/').map(Number);\n}\n\n/**\n * @param {TileCoord} tileCoord Tile coord.\n * @return {number} Hash.\n */\nexport function hash(tileCoord) {\n  return (tileCoord[1] << tileCoord[0]) + tileCoord[2];\n}\n\n/**\n * @param {TileCoord} tileCoord Tile coordinate.\n * @param {!import(\"./tilegrid/TileGrid.js\").default} tileGrid Tile grid.\n * @return {boolean} Tile coordinate is within extent and zoom level range.\n */\nexport function withinExtentAndZ(tileCoord, tileGrid) {\n  const z = tileCoord[0];\n  const x = tileCoord[1];\n  const y = tileCoord[2];\n\n  if (tileGrid.getMinZoom() > z || z > tileGrid.getMaxZoom()) {\n    return false;\n  }\n  const tileRange = tileGrid.getFullTileRange(z);\n  if (!tileRange) {\n    return true;\n  }\n  return tileRange.containsXY(x, y);\n}\n", "/**\n * @module ol/tilegrid/TileGrid\n */\nimport TileRange, {\n  createOrUpdate as createOrUpdateTileRange,\n} from '../TileRange.js';\nimport {DEFAULT_TILE_SIZE} from './common.js';\nimport {assert} from '../asserts.js';\nimport {ceil, clamp, floor} from '../math.js';\nimport {createOrUpdate, getTopLeft} from '../extent.js';\nimport {createOrUpdate as createOrUpdateTileCoord} from '../tilecoord.js';\nimport {intersectsLinearRing} from '../geom/flat/intersectsextent.js';\nimport {isSorted, linearFindNearest} from '../array.js';\nimport {toSize} from '../size.js';\n\n/**\n * @private\n * @type {import(\"../tilecoord.js\").TileCoord}\n */\nconst tmpTileCoord = [0, 0, 0];\n\n/**\n * Number of decimal digits to consider in integer values when rounding.\n * @type {number}\n */\nconst DECIMALS = 5;\n\n/**\n * @typedef {Object} Options\n * @property {import(\"../extent.js\").Extent} [extent] Extent for the tile grid. No tiles outside this\n * extent will be requested by {@link module:ol/source/Tile~TileSource} sources. When no `origin` or\n * `origins` are configured, the `origin` will be set to the top-left corner of the extent.\n * @property {number} [minZoom=0] Minimum zoom.\n * @property {import(\"../coordinate.js\").Coordinate} [origin] The tile grid origin, i.e. where the `x`\n * and `y` axes meet (`[z, 0, 0]`). Tile coordinates increase left to right and downwards. If not\n * specified, `extent` or `origins` must be provided.\n * @property {Array<import(\"../coordinate.js\").Coordinate>} [origins] Tile grid origins, i.e. where\n * the `x` and `y` axes meet (`[z, 0, 0]`), for each zoom level. If given, the array length\n * should match the length of the `resolutions` array, i.e. each resolution can have a different\n * origin. Tile coordinates increase left to right and downwards. If not specified, `extent` or\n * `origin` must be provided.\n * @property {!Array<number>} resolutions Resolutions. The array index of each resolution needs\n * to match the zoom level. This means that even if a `minZoom` is configured, the resolutions\n * array will have a length of `maxZoom + 1`.\n * @property {Array<import(\"../size.js\").Size>} [sizes] Number of tile rows and columns\n * of the grid for each zoom level. If specified the values\n * define each zoom level's extent together with the `origin` or `origins`.\n * A grid `extent` can be configured in addition, and will further limit the extent\n * for which tile requests are made by sources. If the bottom-left corner of\n * an extent is used as `origin` or `origins`, then the `y` value must be\n * negative because OpenLayers tile coordinates use the top left as the origin.\n * @property {number|import(\"../size.js\").Size} [tileSize] Tile size.\n * Default is `[256, 256]`.\n * @property {Array<number|import(\"../size.js\").Size>} [tileSizes] Tile sizes. If given, the array length\n * should match the length of the `resolutions` array, i.e. each resolution can have a different\n * tile size.\n */\n\n/**\n * @classdesc\n * Base class for setting the grid pattern for sources accessing tiled-image\n * servers.\n * @api\n */\nclass TileGrid {\n  /**\n   * @param {Options} options Tile grid options.\n   */\n  constructor(options) {\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.minZoom = options.minZoom !== undefined ? options.minZoom : 0;\n\n    /**\n     * @private\n     * @type {!Array<number>}\n     */\n    this.resolutions_ = options.resolutions;\n    assert(\n      isSorted(\n        this.resolutions_,\n        function (a, b) {\n          return b - a;\n        },\n        true\n      ),\n      17\n    ); // `resolutions` must be sorted in descending order\n\n    // check if we've got a consistent zoom factor and origin\n    let zoomFactor;\n    if (!options.origins) {\n      for (let i = 0, ii = this.resolutions_.length - 1; i < ii; ++i) {\n        if (!zoomFactor) {\n          zoomFactor = this.resolutions_[i] / this.resolutions_[i + 1];\n        } else {\n          if (this.resolutions_[i] / this.resolutions_[i + 1] !== zoomFactor) {\n            zoomFactor = undefined;\n            break;\n          }\n        }\n      }\n    }\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.zoomFactor_ = zoomFactor;\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.maxZoom = this.resolutions_.length - 1;\n\n    /**\n     * @private\n     * @type {import(\"../coordinate.js\").Coordinate|null}\n     */\n    this.origin_ = options.origin !== undefined ? options.origin : null;\n\n    /**\n     * @private\n     * @type {Array<import(\"../coordinate.js\").Coordinate>}\n     */\n    this.origins_ = null;\n    if (options.origins !== undefined) {\n      this.origins_ = options.origins;\n      assert(this.origins_.length == this.resolutions_.length, 20); // Number of `origins` and `resolutions` must be equal\n    }\n\n    const extent = options.extent;\n\n    if (extent !== undefined && !this.origin_ && !this.origins_) {\n      this.origin_ = getTopLeft(extent);\n    }\n\n    assert(\n      (!this.origin_ && this.origins_) || (this.origin_ && !this.origins_),\n      18\n    ); // Either `origin` or `origins` must be configured, never both\n\n    /**\n     * @private\n     * @type {Array<number|import(\"../size.js\").Size>}\n     */\n    this.tileSizes_ = null;\n    if (options.tileSizes !== undefined) {\n      this.tileSizes_ = options.tileSizes;\n      assert(this.tileSizes_.length == this.resolutions_.length, 19); // Number of `tileSizes` and `resolutions` must be equal\n    }\n\n    /**\n     * @private\n     * @type {number|import(\"../size.js\").Size}\n     */\n    this.tileSize_ =\n      options.tileSize !== undefined\n        ? options.tileSize\n        : !this.tileSizes_\n        ? DEFAULT_TILE_SIZE\n        : null;\n    assert(\n      (!this.tileSize_ && this.tileSizes_) ||\n        (this.tileSize_ && !this.tileSizes_),\n      22\n    ); // Either `tileSize` or `tileSizes` must be configured, never both\n\n    /**\n     * @private\n     * @type {import(\"../extent.js\").Extent}\n     */\n    this.extent_ = extent !== undefined ? extent : null;\n\n    /**\n     * @private\n     * @type {Array<import(\"../TileRange.js\").default>}\n     */\n    this.fullTileRanges_ = null;\n\n    /**\n     * @private\n     * @type {import(\"../size.js\").Size}\n     */\n    this.tmpSize_ = [0, 0];\n\n    /**\n     * @private\n     * @type {import(\"../extent.js\").Extent}\n     */\n    this.tmpExtent_ = [0, 0, 0, 0];\n\n    if (options.sizes !== undefined) {\n      this.fullTileRanges_ = options.sizes.map(function (size, z) {\n        const tileRange = new TileRange(\n          Math.min(0, size[0]),\n          Math.max(size[0] - 1, -1),\n          Math.min(0, size[1]),\n          Math.max(size[1] - 1, -1)\n        );\n        if (extent) {\n          const restrictedTileRange = this.getTileRangeForExtentAndZ(extent, z);\n          tileRange.minX = Math.max(restrictedTileRange.minX, tileRange.minX);\n          tileRange.maxX = Math.min(restrictedTileRange.maxX, tileRange.maxX);\n          tileRange.minY = Math.max(restrictedTileRange.minY, tileRange.minY);\n          tileRange.maxY = Math.min(restrictedTileRange.maxY, tileRange.maxY);\n        }\n        return tileRange;\n      }, this);\n    } else if (extent) {\n      this.calculateTileRanges_(extent);\n    }\n  }\n\n  /**\n   * Call a function with each tile coordinate for a given extent and zoom level.\n   *\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {number} zoom Integer zoom level.\n   * @param {function(import(\"../tilecoord.js\").TileCoord): void} callback Function called with each tile coordinate.\n   * @api\n   */\n  forEachTileCoord(extent, zoom, callback) {\n    const tileRange = this.getTileRangeForExtentAndZ(extent, zoom);\n    for (let i = tileRange.minX, ii = tileRange.maxX; i <= ii; ++i) {\n      for (let j = tileRange.minY, jj = tileRange.maxY; j <= jj; ++j) {\n        callback([zoom, i, j]);\n      }\n    }\n  }\n\n  /**\n   * @param {import(\"../tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n   * @param {function(number, import(\"../TileRange.js\").default): boolean} callback Callback.\n   * @param {import(\"../TileRange.js\").default} [tempTileRange] Temporary import(\"../TileRange.js\").default object.\n   * @param {import(\"../extent.js\").Extent} [tempExtent] Temporary import(\"../extent.js\").Extent object.\n   * @return {boolean} Callback succeeded.\n   */\n  forEachTileCoordParentTileRange(\n    tileCoord,\n    callback,\n    tempTileRange,\n    tempExtent\n  ) {\n    let tileRange, x, y;\n    let tileCoordExtent = null;\n    let z = tileCoord[0] - 1;\n    if (this.zoomFactor_ === 2) {\n      x = tileCoord[1];\n      y = tileCoord[2];\n    } else {\n      tileCoordExtent = this.getTileCoordExtent(tileCoord, tempExtent);\n    }\n    while (z >= this.minZoom) {\n      if (this.zoomFactor_ === 2) {\n        x = Math.floor(x / 2);\n        y = Math.floor(y / 2);\n        tileRange = createOrUpdateTileRange(x, x, y, y, tempTileRange);\n      } else {\n        tileRange = this.getTileRangeForExtentAndZ(\n          tileCoordExtent,\n          z,\n          tempTileRange\n        );\n      }\n      if (callback(z, tileRange)) {\n        return true;\n      }\n      --z;\n    }\n    return false;\n  }\n\n  /**\n   * Get the extent for this tile grid, if it was configured.\n   * @return {import(\"../extent.js\").Extent} Extent.\n   * @api\n   */\n  getExtent() {\n    return this.extent_;\n  }\n\n  /**\n   * Get the maximum zoom level for the grid.\n   * @return {number} Max zoom.\n   * @api\n   */\n  getMaxZoom() {\n    return this.maxZoom;\n  }\n\n  /**\n   * Get the minimum zoom level for the grid.\n   * @return {number} Min zoom.\n   * @api\n   */\n  getMinZoom() {\n    return this.minZoom;\n  }\n\n  /**\n   * Get the origin for the grid at the given zoom level.\n   * @param {number} z Integer zoom level.\n   * @return {import(\"../coordinate.js\").Coordinate} Origin.\n   * @api\n   */\n  getOrigin(z) {\n    if (this.origin_) {\n      return this.origin_;\n    }\n    return this.origins_[z];\n  }\n\n  /**\n   * Get the resolution for the given zoom level.\n   * @param {number} z Integer zoom level.\n   * @return {number} Resolution.\n   * @api\n   */\n  getResolution(z) {\n    return this.resolutions_[z];\n  }\n\n  /**\n   * Get the list of resolutions for the tile grid.\n   * @return {Array<number>} Resolutions.\n   * @api\n   */\n  getResolutions() {\n    return this.resolutions_;\n  }\n\n  /**\n   * @param {import(\"../tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n   * @param {import(\"../TileRange.js\").default} [tempTileRange] Temporary import(\"../TileRange.js\").default object.\n   * @param {import(\"../extent.js\").Extent} [tempExtent] Temporary import(\"../extent.js\").Extent object.\n   * @return {import(\"../TileRange.js\").default|null} Tile range.\n   */\n  getTileCoordChildTileRange(tileCoord, tempTileRange, tempExtent) {\n    if (tileCoord[0] < this.maxZoom) {\n      if (this.zoomFactor_ === 2) {\n        const minX = tileCoord[1] * 2;\n        const minY = tileCoord[2] * 2;\n        return createOrUpdateTileRange(\n          minX,\n          minX + 1,\n          minY,\n          minY + 1,\n          tempTileRange\n        );\n      }\n      const tileCoordExtent = this.getTileCoordExtent(\n        tileCoord,\n        tempExtent || this.tmpExtent_\n      );\n      return this.getTileRangeForExtentAndZ(\n        tileCoordExtent,\n        tileCoord[0] + 1,\n        tempTileRange\n      );\n    }\n    return null;\n  }\n\n  /**\n   * @param {import(\"../tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n   * @param {number} z Integer zoom level.\n   * @param {import(\"../TileRange.js\").default} [tempTileRange] Temporary import(\"../TileRange.js\").default object.\n   * @return {import(\"../TileRange.js\").default|null} Tile range.\n   */\n  getTileRangeForTileCoordAndZ(tileCoord, z, tempTileRange) {\n    if (z > this.maxZoom || z < this.minZoom) {\n      return null;\n    }\n\n    const tileCoordZ = tileCoord[0];\n    const tileCoordX = tileCoord[1];\n    const tileCoordY = tileCoord[2];\n\n    if (z === tileCoordZ) {\n      return createOrUpdateTileRange(\n        tileCoordX,\n        tileCoordY,\n        tileCoordX,\n        tileCoordY,\n        tempTileRange\n      );\n    }\n\n    if (this.zoomFactor_) {\n      const factor = Math.pow(this.zoomFactor_, z - tileCoordZ);\n      const minX = Math.floor(tileCoordX * factor);\n      const minY = Math.floor(tileCoordY * factor);\n      if (z < tileCoordZ) {\n        return createOrUpdateTileRange(minX, minX, minY, minY, tempTileRange);\n      }\n\n      const maxX = Math.floor(factor * (tileCoordX + 1)) - 1;\n      const maxY = Math.floor(factor * (tileCoordY + 1)) - 1;\n      return createOrUpdateTileRange(minX, maxX, minY, maxY, tempTileRange);\n    }\n\n    const tileCoordExtent = this.getTileCoordExtent(tileCoord, this.tmpExtent_);\n    return this.getTileRangeForExtentAndZ(tileCoordExtent, z, tempTileRange);\n  }\n\n  /**\n   * Get a tile range for the given extent and integer zoom level.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {number} z Integer zoom level.\n   * @param {import(\"../TileRange.js\").default} [tempTileRange] Temporary tile range object.\n   * @return {import(\"../TileRange.js\").default} Tile range.\n   */\n  getTileRangeForExtentAndZ(extent, z, tempTileRange) {\n    this.getTileCoordForXYAndZ_(extent[0], extent[3], z, false, tmpTileCoord);\n    const minX = tmpTileCoord[1];\n    const minY = tmpTileCoord[2];\n    this.getTileCoordForXYAndZ_(extent[2], extent[1], z, true, tmpTileCoord);\n    const maxX = tmpTileCoord[1];\n    const maxY = tmpTileCoord[2];\n    return createOrUpdateTileRange(minX, maxX, minY, maxY, tempTileRange);\n  }\n\n  /**\n   * @param {import(\"../tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n   * @return {import(\"../coordinate.js\").Coordinate} Tile center.\n   */\n  getTileCoordCenter(tileCoord) {\n    const origin = this.getOrigin(tileCoord[0]);\n    const resolution = this.getResolution(tileCoord[0]);\n    const tileSize = toSize(this.getTileSize(tileCoord[0]), this.tmpSize_);\n    return [\n      origin[0] + (tileCoord[1] + 0.5) * tileSize[0] * resolution,\n      origin[1] - (tileCoord[2] + 0.5) * tileSize[1] * resolution,\n    ];\n  }\n\n  /**\n   * Get the extent of a tile coordinate.\n   *\n   * @param {import(\"../tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n   * @param {import(\"../extent.js\").Extent} [tempExtent] Temporary extent object.\n   * @return {import(\"../extent.js\").Extent} Extent.\n   * @api\n   */\n  getTileCoordExtent(tileCoord, tempExtent) {\n    const origin = this.getOrigin(tileCoord[0]);\n    const resolution = this.getResolution(tileCoord[0]);\n    const tileSize = toSize(this.getTileSize(tileCoord[0]), this.tmpSize_);\n    const minX = origin[0] + tileCoord[1] * tileSize[0] * resolution;\n    const minY = origin[1] - (tileCoord[2] + 1) * tileSize[1] * resolution;\n    const maxX = minX + tileSize[0] * resolution;\n    const maxY = minY + tileSize[1] * resolution;\n    return createOrUpdate(minX, minY, maxX, maxY, tempExtent);\n  }\n\n  /**\n   * Get the tile coordinate for the given map coordinate and resolution.  This\n   * method considers that coordinates that intersect tile boundaries should be\n   * assigned the higher tile coordinate.\n   *\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @param {number} resolution Resolution.\n   * @param {import(\"../tilecoord.js\").TileCoord} [opt_tileCoord] Destination import(\"../tilecoord.js\").TileCoord object.\n   * @return {import(\"../tilecoord.js\").TileCoord} Tile coordinate.\n   * @api\n   */\n  getTileCoordForCoordAndResolution(coordinate, resolution, opt_tileCoord) {\n    return this.getTileCoordForXYAndResolution_(\n      coordinate[0],\n      coordinate[1],\n      resolution,\n      false,\n      opt_tileCoord\n    );\n  }\n\n  /**\n   * Note that this method should not be called for resolutions that correspond\n   * to an integer zoom level.  Instead call the `getTileCoordForXYAndZ_` method.\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {number} resolution Resolution (for a non-integer zoom level).\n   * @param {boolean} reverseIntersectionPolicy Instead of letting edge\n   *     intersections go to the higher tile coordinate, let edge intersections\n   *     go to the lower tile coordinate.\n   * @param {import(\"../tilecoord.js\").TileCoord} [opt_tileCoord] Temporary import(\"../tilecoord.js\").TileCoord object.\n   * @return {import(\"../tilecoord.js\").TileCoord} Tile coordinate.\n   * @private\n   */\n  getTileCoordForXYAndResolution_(\n    x,\n    y,\n    resolution,\n    reverseIntersectionPolicy,\n    opt_tileCoord\n  ) {\n    const z = this.getZForResolution(resolution);\n    const scale = resolution / this.getResolution(z);\n    const origin = this.getOrigin(z);\n    const tileSize = toSize(this.getTileSize(z), this.tmpSize_);\n\n    let tileCoordX = (scale * (x - origin[0])) / resolution / tileSize[0];\n    let tileCoordY = (scale * (origin[1] - y)) / resolution / tileSize[1];\n\n    if (reverseIntersectionPolicy) {\n      tileCoordX = ceil(tileCoordX, DECIMALS) - 1;\n      tileCoordY = ceil(tileCoordY, DECIMALS) - 1;\n    } else {\n      tileCoordX = floor(tileCoordX, DECIMALS);\n      tileCoordY = floor(tileCoordY, DECIMALS);\n    }\n\n    return createOrUpdateTileCoord(z, tileCoordX, tileCoordY, opt_tileCoord);\n  }\n\n  /**\n   * Although there is repetition between this method and `getTileCoordForXYAndResolution_`,\n   * they should have separate implementations.  This method is for integer zoom\n   * levels.  The other method should only be called for resolutions corresponding\n   * to non-integer zoom levels.\n   * @param {number} x Map x coordinate.\n   * @param {number} y Map y coordinate.\n   * @param {number} z Integer zoom level.\n   * @param {boolean} reverseIntersectionPolicy Instead of letting edge\n   *     intersections go to the higher tile coordinate, let edge intersections\n   *     go to the lower tile coordinate.\n   * @param {import(\"../tilecoord.js\").TileCoord} [opt_tileCoord] Temporary import(\"../tilecoord.js\").TileCoord object.\n   * @return {import(\"../tilecoord.js\").TileCoord} Tile coordinate.\n   * @private\n   */\n  getTileCoordForXYAndZ_(x, y, z, reverseIntersectionPolicy, opt_tileCoord) {\n    const origin = this.getOrigin(z);\n    const resolution = this.getResolution(z);\n    const tileSize = toSize(this.getTileSize(z), this.tmpSize_);\n\n    let tileCoordX = (x - origin[0]) / resolution / tileSize[0];\n    let tileCoordY = (origin[1] - y) / resolution / tileSize[1];\n\n    if (reverseIntersectionPolicy) {\n      tileCoordX = ceil(tileCoordX, DECIMALS) - 1;\n      tileCoordY = ceil(tileCoordY, DECIMALS) - 1;\n    } else {\n      tileCoordX = floor(tileCoordX, DECIMALS);\n      tileCoordY = floor(tileCoordY, DECIMALS);\n    }\n\n    return createOrUpdateTileCoord(z, tileCoordX, tileCoordY, opt_tileCoord);\n  }\n\n  /**\n   * Get a tile coordinate given a map coordinate and zoom level.\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @param {number} z Zoom level.\n   * @param {import(\"../tilecoord.js\").TileCoord} [opt_tileCoord] Destination import(\"../tilecoord.js\").TileCoord object.\n   * @return {import(\"../tilecoord.js\").TileCoord} Tile coordinate.\n   * @api\n   */\n  getTileCoordForCoordAndZ(coordinate, z, opt_tileCoord) {\n    return this.getTileCoordForXYAndZ_(\n      coordinate[0],\n      coordinate[1],\n      z,\n      false,\n      opt_tileCoord\n    );\n  }\n\n  /**\n   * @param {import(\"../tilecoord.js\").TileCoord} tileCoord Tile coordinate.\n   * @return {number} Tile resolution.\n   */\n  getTileCoordResolution(tileCoord) {\n    return this.resolutions_[tileCoord[0]];\n  }\n\n  /**\n   * Get the tile size for a zoom level. The type of the return value matches the\n   * `tileSize` or `tileSizes` that the tile grid was configured with. To always\n   * get an {@link import(\"../size.js\").Size}, run the result through {@link module:ol/size.toSize}.\n   * @param {number} z Z.\n   * @return {number|import(\"../size.js\").Size} Tile size.\n   * @api\n   */\n  getTileSize(z) {\n    if (this.tileSize_) {\n      return this.tileSize_;\n    }\n    return this.tileSizes_[z];\n  }\n\n  /**\n   * @param {number} z Zoom level.\n   * @return {import(\"../TileRange.js\").default} Extent tile range for the specified zoom level.\n   */\n  getFullTileRange(z) {\n    if (!this.fullTileRanges_) {\n      return this.extent_\n        ? this.getTileRangeForExtentAndZ(this.extent_, z)\n        : null;\n    }\n    return this.fullTileRanges_[z];\n  }\n\n  /**\n   * @param {number} resolution Resolution.\n   * @param {number|import(\"../array.js\").NearestDirectionFunction} [opt_direction]\n   *     If 0, the nearest resolution will be used.\n   *     If 1, the nearest higher resolution (lower Z) will be used. If -1, the\n   *     nearest lower resolution (higher Z) will be used. Default is 0.\n   *     Use a {@link module:ol/array~NearestDirectionFunction} for more precise control.\n   *\n   * For example to change tile Z at the midpoint of zoom levels\n   * ```js\n   * function(value, high, low) {\n   *   return value - low * Math.sqrt(high / low);\n   * }\n   * ```\n   * @return {number} Z.\n   * @api\n   */\n  getZForResolution(resolution, opt_direction) {\n    const z = linearFindNearest(\n      this.resolutions_,\n      resolution,\n      opt_direction || 0\n    );\n    return clamp(z, this.minZoom, this.maxZoom);\n  }\n\n  /**\n   * The tile with the provided tile coordinate intersects the given viewport.\n   * @param {import('../tilecoord.js').TileCoord} tileCoord Tile coordinate.\n   * @param {Array<number>} viewport Viewport as returned from {@link module:ol/extent.getRotatedViewport}.\n   * @return {boolean} The tile with the provided tile coordinate intersects the given viewport.\n   */\n  tileCoordIntersectsViewport(tileCoord, viewport) {\n    return intersectsLinearRing(\n      viewport,\n      0,\n      viewport.length,\n      2,\n      this.getTileCoordExtent(tileCoord)\n    );\n  }\n\n  /**\n   * @param {!import(\"../extent.js\").Extent} extent Extent for this tile grid.\n   * @private\n   */\n  calculateTileRanges_(extent) {\n    const length = this.resolutions_.length;\n    const fullTileRanges = new Array(length);\n    for (let z = this.minZoom; z < length; ++z) {\n      fullTileRanges[z] = this.getTileRangeForExtentAndZ(extent, z);\n    }\n    this.fullTileRanges_ = fullTileRanges;\n  }\n}\n\nexport default TileGrid;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBO,SAASA,gBAAe,GAAG,GAAG,GAAG,WAAW;AACjD,MAAI,cAAc,QAAW;AAC3B,cAAU,CAAC,IAAI;AACf,cAAU,CAAC,IAAI;AACf,cAAU,CAAC,IAAI;AACf,WAAO;AAAA,EACT;AACA,SAAO,CAAC,GAAG,GAAG,CAAC;AACjB;AAQO,SAAS,UAAU,GAAG,GAAG,GAAG;AACjC,SAAO,IAAI,MAAM,IAAI,MAAM;AAC7B;AAOO,SAAS,OAAO,WAAW;AAChC,SAAO,UAAU,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;AAC3D;AAOO,SAAS,sBAAsB,SAAS;AAC7C,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,QACf,UAAU,QAAQ,YAAY,GAAG,IAAI,GAAG,QAAQ,MAAM,EACtD,MAAM,GAAG,EACT,IAAI,MAAM;AACb,SAAO,UAAU,GAAG,GAAG,CAAC;AAC1B;AAOO,SAAS,QAAQ,KAAK;AAC3B,SAAO,IAAI,MAAM,GAAG,EAAE,IAAI,MAAM;AAClC;AAMO,SAAS,KAAK,WAAW;AAC9B,UAAQ,UAAU,CAAC,KAAK,UAAU,CAAC,KAAK,UAAU,CAAC;AACrD;AAOO,SAAS,iBAAiB,WAAW,UAAU;AACpD,QAAM,IAAI,UAAU,CAAC;AACrB,QAAM,IAAI,UAAU,CAAC;AACrB,QAAM,IAAI,UAAU,CAAC;AAErB,MAAI,SAAS,WAAW,IAAI,KAAK,IAAI,SAAS,WAAW,GAAG;AAC1D,WAAO;AAAA,EACT;AACA,QAAM,YAAY,SAAS,iBAAiB,CAAC;AAC7C,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AACA,SAAO,UAAU,WAAW,GAAG,CAAC;AAClC;;;AC5EA,IAAM,eAAe,CAAC,GAAG,GAAG,CAAC;AAM7B,IAAM,WAAW;AAuCjB,IAAM,WAAN,MAAe;AAAA;AAAA;AAAA;AAAA,EAIb,YAAY,SAAS;AAKnB,SAAK,UAAU,QAAQ,YAAY,SAAY,QAAQ,UAAU;AAMjE,SAAK,eAAe,QAAQ;AAC5B;AAAA,MACE;AAAA,QACE,KAAK;AAAA,QACL,SAAU,GAAG,GAAG;AACd,iBAAO,IAAI;AAAA,QACb;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAGA,QAAI;AACJ,QAAI,CAAC,QAAQ,SAAS;AACpB,eAAS,IAAI,GAAG,KAAK,KAAK,aAAa,SAAS,GAAG,IAAI,IAAI,EAAE,GAAG;AAC9D,YAAI,CAAC,YAAY;AACf,uBAAa,KAAK,aAAa,CAAC,IAAI,KAAK,aAAa,IAAI,CAAC;AAAA,QAC7D,OAAO;AACL,cAAI,KAAK,aAAa,CAAC,IAAI,KAAK,aAAa,IAAI,CAAC,MAAM,YAAY;AAClE,yBAAa;AACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAMA,SAAK,cAAc;AAMnB,SAAK,UAAU,KAAK,aAAa,SAAS;AAM1C,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAM/D,SAAK,WAAW;AAChB,QAAI,QAAQ,YAAY,QAAW;AACjC,WAAK,WAAW,QAAQ;AACxB,aAAO,KAAK,SAAS,UAAU,KAAK,aAAa,QAAQ,EAAE;AAAA,IAC7D;AAEA,UAAM,SAAS,QAAQ;AAEvB,QAAI,WAAW,UAAa,CAAC,KAAK,WAAW,CAAC,KAAK,UAAU;AAC3D,WAAK,UAAU,WAAW,MAAM;AAAA,IAClC;AAEA;AAAA,MACG,CAAC,KAAK,WAAW,KAAK,YAAc,KAAK,WAAW,CAAC,KAAK;AAAA,MAC3D;AAAA,IACF;AAMA,SAAK,aAAa;AAClB,QAAI,QAAQ,cAAc,QAAW;AACnC,WAAK,aAAa,QAAQ;AAC1B,aAAO,KAAK,WAAW,UAAU,KAAK,aAAa,QAAQ,EAAE;AAAA,IAC/D;AAMA,SAAK,YACH,QAAQ,aAAa,SACjB,QAAQ,WACR,CAAC,KAAK,aACN,oBACA;AACN;AAAA,MACG,CAAC,KAAK,aAAa,KAAK,cACtB,KAAK,aAAa,CAAC,KAAK;AAAA,MAC3B;AAAA,IACF;AAMA,SAAK,UAAU,WAAW,SAAY,SAAS;AAM/C,SAAK,kBAAkB;AAMvB,SAAK,WAAW,CAAC,GAAG,CAAC;AAMrB,SAAK,aAAa,CAAC,GAAG,GAAG,GAAG,CAAC;AAE7B,QAAI,QAAQ,UAAU,QAAW;AAC/B,WAAK,kBAAkB,QAAQ,MAAM,IAAI,SAAU,MAAM,GAAG;AAC1D,cAAM,YAAY,IAAI;AAAA,UACpB,KAAK,IAAI,GAAG,KAAK,CAAC,CAAC;AAAA,UACnB,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG,EAAE;AAAA,UACxB,KAAK,IAAI,GAAG,KAAK,CAAC,CAAC;AAAA,UACnB,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG,EAAE;AAAA,QAC1B;AACA,YAAI,QAAQ;AACV,gBAAM,sBAAsB,KAAK,0BAA0B,QAAQ,CAAC;AACpE,oBAAU,OAAO,KAAK,IAAI,oBAAoB,MAAM,UAAU,IAAI;AAClE,oBAAU,OAAO,KAAK,IAAI,oBAAoB,MAAM,UAAU,IAAI;AAClE,oBAAU,OAAO,KAAK,IAAI,oBAAoB,MAAM,UAAU,IAAI;AAClE,oBAAU,OAAO,KAAK,IAAI,oBAAoB,MAAM,UAAU,IAAI;AAAA,QACpE;AACA,eAAO;AAAA,MACT,GAAG,IAAI;AAAA,IACT,WAAW,QAAQ;AACjB,WAAK,qBAAqB,MAAM;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,iBAAiB,QAAQ,MAAM,UAAU;AACvC,UAAM,YAAY,KAAK,0BAA0B,QAAQ,IAAI;AAC7D,aAAS,IAAI,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,IAAI,EAAE,GAAG;AAC9D,eAAS,IAAI,UAAU,MAAM,KAAK,UAAU,MAAM,KAAK,IAAI,EAAE,GAAG;AAC9D,iBAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gCACE,WACA,UACA,eACA,YACA;AACA,QAAI,WAAW,GAAG;AAClB,QAAI,kBAAkB;AACtB,QAAI,IAAI,UAAU,CAAC,IAAI;AACvB,QAAI,KAAK,gBAAgB,GAAG;AAC1B,UAAI,UAAU,CAAC;AACf,UAAI,UAAU,CAAC;AAAA,IACjB,OAAO;AACL,wBAAkB,KAAK,mBAAmB,WAAW,UAAU;AAAA,IACjE;AACA,WAAO,KAAK,KAAK,SAAS;AACxB,UAAI,KAAK,gBAAgB,GAAG;AAC1B,YAAI,KAAK,MAAM,IAAI,CAAC;AACpB,YAAI,KAAK,MAAM,IAAI,CAAC;AACpB,oBAAYC,gBAAwB,GAAG,GAAG,GAAG,GAAG,aAAa;AAAA,MAC/D,OAAO;AACL,oBAAY,KAAK;AAAA,UACf;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,UAAI,SAAS,GAAG,SAAS,GAAG;AAC1B,eAAO;AAAA,MACT;AACA,QAAE;AAAA,IACJ;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,GAAG;AACX,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK,SAAS,CAAC;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,GAAG;AACf,WAAO,KAAK,aAAa,CAAC;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,2BAA2B,WAAW,eAAe,YAAY;AAC/D,QAAI,UAAU,CAAC,IAAI,KAAK,SAAS;AAC/B,UAAI,KAAK,gBAAgB,GAAG;AAC1B,cAAM,OAAO,UAAU,CAAC,IAAI;AAC5B,cAAM,OAAO,UAAU,CAAC,IAAI;AAC5B,eAAOA;AAAA,UACL;AAAA,UACA,OAAO;AAAA,UACP;AAAA,UACA,OAAO;AAAA,UACP;AAAA,QACF;AAAA,MACF;AACA,YAAM,kBAAkB,KAAK;AAAA,QAC3B;AAAA,QACA,cAAc,KAAK;AAAA,MACrB;AACA,aAAO,KAAK;AAAA,QACV;AAAA,QACA,UAAU,CAAC,IAAI;AAAA,QACf;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,6BAA6B,WAAW,GAAG,eAAe;AACxD,QAAI,IAAI,KAAK,WAAW,IAAI,KAAK,SAAS;AACxC,aAAO;AAAA,IACT;AAEA,UAAM,aAAa,UAAU,CAAC;AAC9B,UAAM,aAAa,UAAU,CAAC;AAC9B,UAAM,aAAa,UAAU,CAAC;AAE9B,QAAI,MAAM,YAAY;AACpB,aAAOA;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,aAAa;AACpB,YAAM,SAAS,KAAK,IAAI,KAAK,aAAa,IAAI,UAAU;AACxD,YAAM,OAAO,KAAK,MAAM,aAAa,MAAM;AAC3C,YAAM,OAAO,KAAK,MAAM,aAAa,MAAM;AAC3C,UAAI,IAAI,YAAY;AAClB,eAAOA,gBAAwB,MAAM,MAAM,MAAM,MAAM,aAAa;AAAA,MACtE;AAEA,YAAM,OAAO,KAAK,MAAM,UAAU,aAAa,EAAE,IAAI;AACrD,YAAM,OAAO,KAAK,MAAM,UAAU,aAAa,EAAE,IAAI;AACrD,aAAOA,gBAAwB,MAAM,MAAM,MAAM,MAAM,aAAa;AAAA,IACtE;AAEA,UAAM,kBAAkB,KAAK,mBAAmB,WAAW,KAAK,UAAU;AAC1E,WAAO,KAAK,0BAA0B,iBAAiB,GAAG,aAAa;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,0BAA0B,QAAQ,GAAG,eAAe;AAClD,SAAK,uBAAuB,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,GAAG,OAAO,YAAY;AACxE,UAAM,OAAO,aAAa,CAAC;AAC3B,UAAM,OAAO,aAAa,CAAC;AAC3B,SAAK,uBAAuB,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,GAAG,MAAM,YAAY;AACvE,UAAM,OAAO,aAAa,CAAC;AAC3B,UAAM,OAAO,aAAa,CAAC;AAC3B,WAAOA,gBAAwB,MAAM,MAAM,MAAM,MAAM,aAAa;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,WAAW;AAC5B,UAAM,SAAS,KAAK,UAAU,UAAU,CAAC,CAAC;AAC1C,UAAM,aAAa,KAAK,cAAc,UAAU,CAAC,CAAC;AAClD,UAAM,WAAW,OAAO,KAAK,YAAY,UAAU,CAAC,CAAC,GAAG,KAAK,QAAQ;AACrE,WAAO;AAAA,MACL,OAAO,CAAC,KAAK,UAAU,CAAC,IAAI,OAAO,SAAS,CAAC,IAAI;AAAA,MACjD,OAAO,CAAC,KAAK,UAAU,CAAC,IAAI,OAAO,SAAS,CAAC,IAAI;AAAA,IACnD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,mBAAmB,WAAW,YAAY;AACxC,UAAM,SAAS,KAAK,UAAU,UAAU,CAAC,CAAC;AAC1C,UAAM,aAAa,KAAK,cAAc,UAAU,CAAC,CAAC;AAClD,UAAM,WAAW,OAAO,KAAK,YAAY,UAAU,CAAC,CAAC,GAAG,KAAK,QAAQ;AACrE,UAAM,OAAO,OAAO,CAAC,IAAI,UAAU,CAAC,IAAI,SAAS,CAAC,IAAI;AACtD,UAAM,OAAO,OAAO,CAAC,KAAK,UAAU,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI;AAC5D,UAAM,OAAO,OAAO,SAAS,CAAC,IAAI;AAClC,UAAM,OAAO,OAAO,SAAS,CAAC,IAAI;AAClC,WAAO,eAAe,MAAM,MAAM,MAAM,MAAM,UAAU;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,kCAAkC,YAAY,YAAY,eAAe;AACvE,WAAO,KAAK;AAAA,MACV,WAAW,CAAC;AAAA,MACZ,WAAW,CAAC;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,gCACE,GACA,GACA,YACA,2BACA,eACA;AACA,UAAM,IAAI,KAAK,kBAAkB,UAAU;AAC3C,UAAM,QAAQ,aAAa,KAAK,cAAc,CAAC;AAC/C,UAAM,SAAS,KAAK,UAAU,CAAC;AAC/B,UAAM,WAAW,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,QAAQ;AAE1D,QAAI,aAAc,SAAS,IAAI,OAAO,CAAC,KAAM,aAAa,SAAS,CAAC;AACpE,QAAI,aAAc,SAAS,OAAO,CAAC,IAAI,KAAM,aAAa,SAAS,CAAC;AAEpE,QAAI,2BAA2B;AAC7B,mBAAa,KAAK,YAAY,QAAQ,IAAI;AAC1C,mBAAa,KAAK,YAAY,QAAQ,IAAI;AAAA,IAC5C,OAAO;AACL,mBAAa,MAAM,YAAY,QAAQ;AACvC,mBAAa,MAAM,YAAY,QAAQ;AAAA,IACzC;AAEA,WAAOA,gBAAwB,GAAG,YAAY,YAAY,aAAa;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,uBAAuB,GAAG,GAAG,GAAG,2BAA2B,eAAe;AACxE,UAAM,SAAS,KAAK,UAAU,CAAC;AAC/B,UAAM,aAAa,KAAK,cAAc,CAAC;AACvC,UAAM,WAAW,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,QAAQ;AAE1D,QAAI,cAAc,IAAI,OAAO,CAAC,KAAK,aAAa,SAAS,CAAC;AAC1D,QAAI,cAAc,OAAO,CAAC,IAAI,KAAK,aAAa,SAAS,CAAC;AAE1D,QAAI,2BAA2B;AAC7B,mBAAa,KAAK,YAAY,QAAQ,IAAI;AAC1C,mBAAa,KAAK,YAAY,QAAQ,IAAI;AAAA,IAC5C,OAAO;AACL,mBAAa,MAAM,YAAY,QAAQ;AACvC,mBAAa,MAAM,YAAY,QAAQ;AAAA,IACzC;AAEA,WAAOA,gBAAwB,GAAG,YAAY,YAAY,aAAa;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,yBAAyB,YAAY,GAAG,eAAe;AACrD,WAAO,KAAK;AAAA,MACV,WAAW,CAAC;AAAA,MACZ,WAAW,CAAC;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,WAAW;AAChC,WAAO,KAAK,aAAa,UAAU,CAAC,CAAC;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,GAAG;AACb,QAAI,KAAK,WAAW;AAClB,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK,WAAW,CAAC;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,GAAG;AAClB,QAAI,CAAC,KAAK,iBAAiB;AACzB,aAAO,KAAK,UACR,KAAK,0BAA0B,KAAK,SAAS,CAAC,IAC9C;AAAA,IACN;AACA,WAAO,KAAK,gBAAgB,CAAC;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,kBAAkB,YAAY,eAAe;AAC3C,UAAM,IAAI;AAAA,MACR,KAAK;AAAA,MACL;AAAA,MACA,iBAAiB;AAAA,IACnB;AACA,WAAO,MAAM,GAAG,KAAK,SAAS,KAAK,OAAO;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,4BAA4B,WAAW,UAAU;AAC/C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA,KAAK,mBAAmB,SAAS;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,QAAQ;AAC3B,UAAM,SAAS,KAAK,aAAa;AACjC,UAAM,iBAAiB,IAAI,MAAM,MAAM;AACvC,aAAS,IAAI,KAAK,SAAS,IAAI,QAAQ,EAAE,GAAG;AAC1C,qBAAe,CAAC,IAAI,KAAK,0BAA0B,QAAQ,CAAC;AAAA,IAC9D;AACA,SAAK,kBAAkB;AAAA,EACzB;AACF;AAEA,IAAO,mBAAQ;", "names": ["createOrUpdate", "createOrUpdate"]}