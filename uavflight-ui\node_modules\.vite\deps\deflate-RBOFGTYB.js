import {
  inflate_1
} from "./chunk-Z2XZFPA7.js";
import {
  BaseDecoder
} from "./chunk-C5KGH6RQ.js";
import "./chunk-PLDDJCW6.js";

// node_modules/geotiff/dist-module/compression/deflate.js
var DeflateDecoder = class extends BaseDecoder {
  decodeBlock(buffer) {
    return inflate_1(new Uint8Array(buffer)).buffer;
  }
};
export {
  DeflateDecoder as default
};
//# sourceMappingURL=deflate-RBOFGTYB.js.map
