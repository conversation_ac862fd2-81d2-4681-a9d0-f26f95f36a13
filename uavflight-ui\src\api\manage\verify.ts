import request from '/@/utils/request';

export const pageList = (params?: Object) => {
	return request({
		url: '/admin/businessResult/page',
		method: 'get',
		params,
	});
};

export const getAuditCount = (params?: Object) => {
	return request({
		url: '/admin/businessResult/getAuditCount',
		method: 'get',
		params,
	});
};


 export function audit(obj?: Object) {
  return request({
    url: '/admin/businessResult/audit',
    method: 'post',
    data: obj
  })
}