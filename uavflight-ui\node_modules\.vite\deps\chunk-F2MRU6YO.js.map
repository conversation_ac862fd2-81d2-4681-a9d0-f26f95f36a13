{"version": 3, "sources": ["../../ol/AssertionError.js", "../../ol/asserts.js"], "sourcesContent": ["/**\n * @module ol/AssertionError\n */\n\n/** @type {Object<number, string>} */\nconst messages = {\n  1: 'The view center is not defined',\n  2: 'The view resolution is not defined',\n  3: 'The view rotation is not defined',\n  4: '`image` and `src` cannot be provided at the same time',\n  5: '`imgSize` must be set when `image` is provided',\n  7: '`format` must be set when `url` is set',\n  8: 'Unknown `serverType` configured',\n  9: '`url` must be configured or set using `#setUrl()`',\n  10: 'The default `geometryFunction` can only handle `Point` geometries',\n  11: '`options.featureTypes` must be an Array',\n  12: '`options.geometryName` must also be provided when `options.bbox` is set',\n  13: 'Invalid corner',\n  14: 'Invalid color',\n  15: 'Tried to get a value for a key that does not exist in the cache',\n  16: 'Tried to set a value for a key that is used already',\n  17: '`resolutions` must be sorted in descending order',\n  18: 'Either `origin` or `origins` must be configured, never both',\n  19: 'Number of `tileSizes` and `resolutions` must be equal',\n  20: 'Number of `origins` and `resolutions` must be equal',\n  22: 'Either `tileSize` or `tileSizes` must be configured, never both',\n  24: 'Invalid extent or geometry provided as `geometry`',\n  25: 'Cannot fit empty extent provided as `geometry`',\n  26: 'Features must have an id set',\n  27: 'Features must have an id set',\n  28: '`renderMode` must be `\"hybrid\"` or `\"vector\"`',\n  30: 'The passed `feature` was already added to the source',\n  31: 'Tried to enqueue an `element` that was already added to the queue',\n  32: 'Transformation matrix cannot be inverted',\n  33: 'Invalid units',\n  34: 'Invalid geometry layout',\n  36: 'Unknown SRS type',\n  37: 'Unknown geometry type found',\n  38: '`styleMapValue` has an unknown type',\n  39: 'Unknown geometry type',\n  40: 'Expected `feature` to have a geometry',\n  41: 'Expected an `ol/style/Style` or an array of `ol/style/Style.js`',\n  42: 'Question unknown, the answer is 42',\n  43: 'Expected `layers` to be an array or a `Collection`',\n  47: 'Expected `controls` to be an array or an `ol/Collection`',\n  48: 'Expected `interactions` to be an array or an `ol/Collection`',\n  49: 'Expected `overlays` to be an array or an `ol/Collection`',\n  50: '`options.featureTypes` should be an Array',\n  51: 'Either `url` or `tileJSON` options must be provided',\n  52: 'Unknown `serverType` configured',\n  53: 'Unknown `tierSizeCalculation` configured',\n  55: 'The {-y} placeholder requires a tile grid with extent',\n  56: 'mapBrowserEvent must originate from a pointer event',\n  57: 'At least 2 conditions are required',\n  59: 'Invalid command found in the PBF',\n  60: 'Missing or invalid `size`',\n  61: 'Cannot determine IIIF Image API version from provided image information JSON',\n  62: 'A `WebGLArrayBuffer` must either be of type `ELEMENT_ARRAY_BUFFER` or `ARRAY_BUFFER`',\n  64: 'Layer opacity must be a number',\n  66: '`forEachFeatureAtCoordinate` cannot be used on a WebGL layer if the hit detection logic has not been enabled. This is done by providing adequate shaders using the `hitVertexShader` and `hitFragmentShader` properties of `WebGLPointsLayerRenderer`',\n  67: 'A layer can only be added to the map once. Use either `layer.setMap()` or `map.addLayer()`, not both',\n  68: 'A VectorTile source can only be rendered if it has a projection compatible with the view projection',\n  69: '`width` or `height` cannot be provided together with `scale`',\n};\n\n/**\n * Error object thrown when an assertion failed. This is an ECMA-262 Error,\n * extended with a `code` property.\n * See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error.\n */\nclass AssertionError extends Error {\n  /**\n   * @param {number} code Error code.\n   */\n  constructor(code) {\n    const message = messages[code];\n\n    super(message);\n\n    /**\n     * Error code. The meaning of the code can be found on\n     * https://openlayers.org/en/latest/doc/errors/ (replace `latest` with\n     * the version found in the OpenLayers script's header comment if a version\n     * other than the latest is used).\n     * @type {number}\n     * @deprecated ol/AssertionError and error codes will be removed in v8.0\n     * @api\n     */\n    this.code = code;\n\n    /**\n     * @type {string}\n     */\n    this.name = 'AssertionError';\n\n    // Re-assign message, see https://github.com/Rich-Harris/buble/issues/40\n    this.message = message;\n  }\n}\n\nexport default AssertionError;\n", "/**\n * @module ol/asserts\n */\nimport AssertionError from './AssertionError.js';\n\n/**\n * @param {*} assertion Assertion we expected to be truthy.\n * @param {number} errorCode Error code.\n */\nexport function assert(assertion, errorCode) {\n  if (!assertion) {\n    throw new AssertionError(errorCode);\n  }\n}\n"], "mappings": ";AAKA,IAAM,WAAW;AAAA,EACf,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AAOA,IAAM,iBAAN,cAA6B,MAAM;AAAA;AAAA;AAAA;AAAA,EAIjC,YAAY,MAAM;AAChB,UAAM,UAAU,SAAS,IAAI;AAE7B,UAAM,OAAO;AAWb,SAAK,OAAO;AAKZ,SAAK,OAAO;AAGZ,SAAK,UAAU;AAAA,EACjB;AACF;AAEA,IAAO,yBAAQ;;;AC3FR,SAAS,OAAO,WAAW,WAAW;AAC3C,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,uBAAe,SAAS;AAAA,EACpC;AACF;", "names": []}