import request from '/@/utils/request';

export function getRealTimeTaskList(obj: Object) {
	return request({
		url: '/admin/bigScreen/getRealTimeTaskList',
		method: 'get',
		params: obj,
	});
}

export const getVideoInfoByTaskId = (object: object) => {
	return request({
		url: '/admin/bigScreen/getVideoInfoByTaskId',
		method: 'post',
		data: object,
	});
};

export function getRealEventList(obj: Object) {
	return request({
		url: '/admin/bigScreen/getRealEventList',
		method: 'get',
		params: obj,
	});
}

export function getBusinessResultList(obj: Object) {
	return request({
		url: '/admin/bigScreen/getBusinessResultList',
		method: 'get',
		params: obj,
	});
}

export function getUavAndEventInfo() {
	return request({
		url: '/admin/bigScreen/getUavAndEventInfo',
		method: 'get',
		params: {},
	});
}

export function getEventDataHx(obj: Object) {
	return request({
		url: '/admin/bigScreen/getEventDataHx',
		method: 'get',
		params: obj,
	});
}

export function getEventTop10(obj: Object) {
	return request({
		url: '/admin/bigScreen/getEventTop10',
		method: 'get',
		params: obj,
	});
}

// 获取首页参数统计信息
export function getIndexParams() {
	return request({
		url: '/admin/bigScreen/getIndexParams',
		method: 'get',
		params: {},
	});
}

// 获取每日事件数折线图数据
export function getEventByDay() {
	return request({
		url: '/admin/bigScreen/getEventByDay',
		method: 'get',
		params: {},
	});
}

// 获取发布服务数统计
export function getServiceCount(obj: Object) {
	return request({
		url: '/admin/bigScreen/getServiceCount',
		method: 'get',
		params: obj,
	});
}

// 获取发布服务数折线图数据
export function getServiceLineChart() {
	return request({
		url: '/admin/bigScreen/getServiceLineChart',
		method: 'get',
		params: {},
	});
}
