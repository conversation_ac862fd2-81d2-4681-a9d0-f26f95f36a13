import request from '/@/utils/request';

export function getRealTimeTaskList(obj: Object) {
	return request({
		url: '/admin/bigScreen/getRealTimeTaskList',
		method: 'get',
		params: obj,
	});
}

export const getVideoInfoByTaskId = (object: object) => {
	return request({
		url: '/admin/bigScreen/getVideoInfoByTaskId',
		method: 'post',
		data: object,
	});
};

export function getRealEventList(obj: Object) {
	return request({
		url: '/admin/bigScreen/getRealEventList',
		method: 'get',
		params: obj,
	});
}

export function getBusinessResultList(obj: Object) {
	return request({
		url: '/admin/bigScreen/getBusinessResultList',
		method: 'get',
		params: obj,
	});
}

export function getUavAndEventInfo() {
	return request({
		url: '/admin/bigScreen/getUavAndEventInfo',
		method: 'get',
		params: {},
	});
}

export function getEventDataHx(obj: Object) {
	return request({
		url: '/admin/bigScreen/getEventDataHx',
		method: 'get',
		params: obj,
	});
}

export function getEventTop10(obj: Object) {
	return request({
		url: '/admin/bigScreen/getEventTop10',
		method: 'get',
		params: obj,
	});
}
