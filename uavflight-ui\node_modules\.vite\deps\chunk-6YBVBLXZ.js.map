{"version": 3, "sources": ["../../ol/has.js"], "sourcesContent": ["/**\n * @module ol/has\n */\n\nconst ua =\n  typeof navigator !== 'undefined' && typeof navigator.userAgent !== 'undefined'\n    ? navigator.userAgent.toLowerCase()\n    : '';\n\n/**\n * User agent string says we are dealing with Firefox as browser.\n * @type {boolean}\n */\nexport const FIREFOX = ua.includes('firefox');\n\n/**\n * User agent string says we are dealing with Safari as browser.\n * @type {boolean}\n */\nexport const SAFARI = ua.includes('safari') && !ua.includes('chrom');\n\n/**\n * https://bugs.webkit.org/show_bug.cgi?id=237906\n * @type {boolean}\n */\nexport const SAFARI_BUG_237906 =\n  SAFARI &&\n  (ua.includes('version/15.4') ||\n    /cpu (os|iphone os) 15_4 like mac os x/.test(ua));\n\n/**\n * User agent string says we are dealing with a WebKit engine.\n * @type {boolean}\n */\nexport const WEBKIT = ua.includes('webkit') && !ua.includes('edge');\n\n/**\n * User agent string says we are dealing with a Mac as platform.\n * @type {boolean}\n */\nexport const MAC = ua.includes('macintosh');\n\n/**\n * The ratio between physical pixels and device-independent pixels\n * (dips) on the device (`window.devicePixelRatio`).\n * @const\n * @type {number}\n * @api\n */\nexport const DEVICE_PIXEL_RATIO =\n  typeof devicePixelRatio !== 'undefined' ? devicePixelRatio : 1;\n\n/**\n * The execution context is a worker with OffscreenCanvas available.\n * @const\n * @type {boolean}\n */\nexport const WORKER_OFFSCREEN_CANVAS =\n  typeof WorkerGlobalScope !== 'undefined' &&\n  typeof OffscreenCanvas !== 'undefined' &&\n  self instanceof WorkerGlobalScope; //eslint-disable-line\n\n/**\n * Image.prototype.decode() is supported.\n * @type {boolean}\n */\nexport const IMAGE_DECODE =\n  typeof Image !== 'undefined' && Image.prototype.decode;\n\n/**\n * @type {boolean}\n */\nexport const PASSIVE_EVENT_LISTENERS = (function () {\n  let passive = false;\n  try {\n    const options = Object.defineProperty({}, 'passive', {\n      get: function () {\n        passive = true;\n      },\n    });\n\n    window.addEventListener('_', null, options);\n    window.removeEventListener('_', null, options);\n  } catch (error) {\n    // passive not supported\n  }\n  return passive;\n})();\n"], "mappings": ";AAIA,IAAM,KACJ,OAAO,cAAc,eAAe,OAAO,UAAU,cAAc,cAC/D,UAAU,UAAU,YAAY,IAChC;AAMC,IAAM,UAAU,GAAG,SAAS,SAAS;AAMrC,IAAM,SAAS,GAAG,SAAS,QAAQ,KAAK,CAAC,GAAG,SAAS,OAAO;AAM5D,IAAM,oBACX,WACC,GAAG,SAAS,cAAc,KACzB,wCAAwC,KAAK,EAAE;AAM5C,IAAM,SAAS,GAAG,SAAS,QAAQ,KAAK,CAAC,GAAG,SAAS,MAAM;AAM3D,IAAM,MAAM,GAAG,SAAS,WAAW;AASnC,IAAM,qBACX,OAAO,qBAAqB,cAAc,mBAAmB;AAOxD,IAAM,0BACX,OAAO,sBAAsB,eAC7B,OAAO,oBAAoB,eAC3B,gBAAgB;AAMX,IAAM,eACX,OAAO,UAAU,eAAe,MAAM,UAAU;AAK3C,IAAM,0BAA2B,WAAY;AAClD,MAAI,UAAU;AACd,MAAI;AACF,UAAM,UAAU,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,MACnD,KAAK,WAAY;AACf,kBAAU;AAAA,MACZ;AAAA,IACF,CAAC;AAED,WAAO,iBAAiB,KAAK,MAAM,OAAO;AAC1C,WAAO,oBAAoB,KAAK,MAAM,OAAO;AAAA,EAC/C,SAAS,OAAO;AAAA,EAEhB;AACA,SAAO;AACT,EAAG;", "names": []}