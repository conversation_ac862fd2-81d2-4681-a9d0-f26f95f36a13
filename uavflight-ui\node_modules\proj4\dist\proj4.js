!function(t,s){"object"==typeof exports&&"undefined"!=typeof module?module.exports=s():"function"==typeof define&&define.amd?define(s):(t="undefined"!=typeof globalThis?globalThis:t||self).proj4=s()}(this,(function(){"use strict";var t=6378137,s=.0066943799901413165,i=484813681109536e-20,a=Math.PI/2,h=1e-10,e=.017453292519943295,r=57.29577951308232,n=Math.PI/4,o=2*Math.PI,l=3.14159265359,c={greenwich:0,lisbon:-9.131906111111,paris:2.337229166667,bogota:-74.080916666667,madrid:-3.687938888889,rome:12.452333333333,bern:7.439583333333,jakarta:106.807719444444,ferro:-17.666666666667,brussels:4.367975,stockholm:18.058277777778,athens:23.7163375,oslo:10.722916666667},u={mm:{to_meter:.001},cm:{to_meter:.01},ft:{to_meter:.3048},"us-ft":{to_meter:1200/3937},fath:{to_meter:1.8288},kmi:{to_meter:1852},"us-ch":{to_meter:20.1168402336805},"us-mi":{to_meter:1609.34721869444},km:{to_meter:1e3},"ind-ft":{to_meter:.30479841},"ind-yd":{to_meter:.91439523},mi:{to_meter:1609.344},yd:{to_meter:.9144},ch:{to_meter:20.1168},link:{to_meter:.201168},dm:{to_meter:.1},in:{to_meter:.0254},"ind-ch":{to_meter:20.11669506},"us-in":{to_meter:.025400050800101},"us-yd":{to_meter:.914401828803658}},_=/[\s_\-\/\(\)]/g;function M(t,s){if(t[s])return t[s];for(var i,a=Object.keys(t),h=s.toLowerCase().replace(_,""),e=-1;++e<a.length;)if((i=a[e]).toLowerCase().replace(_,"")===h)return t[i]}function d(t){var s,i,a,h={},r=t.split("+").map((function(t){return t.trim()})).filter((function(t){return t})).reduce((function(t,s){var i=s.split("=");return i.push(!0),t[i[0].toLowerCase()]=i[1],t}),{}),n={proj:"projName",datum:"datumCode",rf:function(t){h.rf=parseFloat(t)},lat_0:function(t){h.lat0=t*e},lat_1:function(t){h.lat1=t*e},lat_2:function(t){h.lat2=t*e},lat_ts:function(t){h.lat_ts=t*e},lon_0:function(t){h.long0=t*e},lon_1:function(t){h.long1=t*e},lon_2:function(t){h.long2=t*e},alpha:function(t){h.alpha=parseFloat(t)*e},gamma:function(t){h.rectified_grid_angle=parseFloat(t)*e},lonc:function(t){h.longc=t*e},x_0:function(t){h.x0=parseFloat(t)},y_0:function(t){h.y0=parseFloat(t)},k_0:function(t){h.k0=parseFloat(t)},k:function(t){h.k0=parseFloat(t)},a:function(t){h.a=parseFloat(t)},b:function(t){h.b=parseFloat(t)},r:function(t){h.a=h.b=parseFloat(t)},r_a:function(){h.R_A=!0},zone:function(t){h.zone=parseInt(t,10)},south:function(){h.utmSouth=!0},towgs84:function(t){h.datum_params=t.split(",").map((function(t){return parseFloat(t)}))},to_meter:function(t){h.to_meter=parseFloat(t)},units:function(t){h.units=t;var s=M(u,t);s&&(h.to_meter=s.to_meter)},from_greenwich:function(t){h.from_greenwich=t*e},pm:function(t){var s=M(c,t);h.from_greenwich=(s||parseFloat(t))*e},nadgrids:function(t){"@null"===t?h.datumCode="none":h.nadgrids=t},axis:function(t){var s="ewnsud";3===t.length&&-1!==s.indexOf(t.substr(0,1))&&-1!==s.indexOf(t.substr(1,1))&&-1!==s.indexOf(t.substr(2,1))&&(h.axis=t)},approx:function(){h.approx=!0}};for(s in r)i=r[s],s in n?"function"==typeof(a=n[s])?a(i):h[a]=i:h[s]=i;return"string"==typeof h.datumCode&&"WGS84"!==h.datumCode&&(h.datumCode=h.datumCode.toLowerCase()),h}class f{static getId(t){const s=t.find((t=>Array.isArray(t)&&"ID"===t[0]));return s&&s.length>=3?{authority:s[1],code:parseInt(s[2],10)}:null}static convertUnit(t,s="unit"){if(!t||t.length<3)return{type:s,name:"unknown",conversion_factor:null};const i=t[1],a=parseFloat(t[2])||null,h=t.find((t=>Array.isArray(t)&&"ID"===t[0]));return{type:s,name:i,conversion_factor:a,id:h?{authority:h[1],code:parseInt(h[2],10)}:null}}static convertAxis(t){const s=t[1]||"Unknown";let i;const a=s.match(/^\((.)\)$/);if(a){const t=a[1].toUpperCase();if("E"===t)i="east";else if("N"===t)i="north";else{if("U"!==t)throw new Error(`Unknown axis abbreviation: ${t}`);i="up"}}else i=t[2]?.toLowerCase()||"unknown";const h=t.find((t=>Array.isArray(t)&&"ORDER"===t[0])),e=h?parseInt(h[1],10):null,r=t.find((t=>Array.isArray(t)&&("LENGTHUNIT"===t[0]||"ANGLEUNIT"===t[0]||"SCALEUNIT"===t[0])));return{name:s,direction:i,unit:this.convertUnit(r),order:e}}static extractAxes(t){return t.filter((t=>Array.isArray(t)&&"AXIS"===t[0])).map((t=>this.convertAxis(t))).sort(((t,s)=>(t.order||0)-(s.order||0)))}static convert(t,s={}){switch(t[0]){case"PROJCRS":s.type="ProjectedCRS",s.name=t[1],s.base_crs=t.find((t=>Array.isArray(t)&&"BASEGEOGCRS"===t[0]))?this.convert(t.find((t=>Array.isArray(t)&&"BASEGEOGCRS"===t[0]))):null,s.conversion=t.find((t=>Array.isArray(t)&&"CONVERSION"===t[0]))?this.convert(t.find((t=>Array.isArray(t)&&"CONVERSION"===t[0]))):null;const i=t.find((t=>Array.isArray(t)&&"CS"===t[0]));i&&(s.coordinate_system={type:i[1],axis:this.extractAxes(t)});const a=t.find((t=>Array.isArray(t)&&"LENGTHUNIT"===t[0]));if(a){const t=this.convertUnit(a);s.coordinate_system.unit=t}s.id=this.getId(t);break;case"BASEGEOGCRS":case"GEOGCRS":s.type="GeographicCRS",s.name=t[1];const h=t.find((t=>Array.isArray(t)&&("DATUM"===t[0]||"ENSEMBLE"===t[0])));if(h){const i=this.convert(h);"ENSEMBLE"===h[0]?s.datum_ensemble=i:s.datum=i;const a=t.find((t=>Array.isArray(t)&&"PRIMEM"===t[0]));a&&"Greenwich"!==a[1]&&(i.prime_meridian={name:a[1],longitude:parseFloat(a[2])})}s.coordinate_system={type:"ellipsoidal",axis:this.extractAxes(t)},s.id=this.getId(t);break;case"DATUM":s.type="GeodeticReferenceFrame",s.name=t[1],s.ellipsoid=t.find((t=>Array.isArray(t)&&"ELLIPSOID"===t[0]))?this.convert(t.find((t=>Array.isArray(t)&&"ELLIPSOID"===t[0]))):null;break;case"ENSEMBLE":s.type="DatumEnsemble",s.name=t[1],s.members=t.filter((t=>Array.isArray(t)&&"MEMBER"===t[0])).map((t=>({type:"DatumEnsembleMember",name:t[1],id:this.getId(t)})));const e=t.find((t=>Array.isArray(t)&&"ENSEMBLEACCURACY"===t[0]));e&&(s.accuracy=parseFloat(e[1]));const r=t.find((t=>Array.isArray(t)&&"ELLIPSOID"===t[0]));r&&(s.ellipsoid=this.convert(r)),s.id=this.getId(t);break;case"ELLIPSOID":s.type="Ellipsoid",s.name=t[1],s.semi_major_axis=parseFloat(t[2]),s.inverse_flattening=parseFloat(t[3]),t.find((t=>Array.isArray(t)&&"LENGTHUNIT"===t[0]))&&this.convert(t.find((t=>Array.isArray(t)&&"LENGTHUNIT"===t[0])),s);break;case"CONVERSION":s.type="Conversion",s.name=t[1],s.method=t.find((t=>Array.isArray(t)&&"METHOD"===t[0]))?this.convert(t.find((t=>Array.isArray(t)&&"METHOD"===t[0]))):null,s.parameters=t.filter((t=>Array.isArray(t)&&"PARAMETER"===t[0])).map((t=>this.convert(t)));break;case"METHOD":s.type="Method",s.name=t[1],s.id=this.getId(t);break;case"PARAMETER":s.type="Parameter",s.name=t[1],s.value=parseFloat(t[2]),s.unit=this.convertUnit(t.find((t=>Array.isArray(t)&&("LENGTHUNIT"===t[0]||"ANGLEUNIT"===t[0]||"SCALEUNIT"===t[0])))),s.id=this.getId(t);break;case"BOUNDCRS":s.type="BoundCRS";const n=t.find((t=>Array.isArray(t)&&"SOURCECRS"===t[0]));if(n){const t=n.find((t=>Array.isArray(t)));s.source_crs=t?this.convert(t):null}const o=t.find((t=>Array.isArray(t)&&"TARGETCRS"===t[0]));if(o){const t=o.find((t=>Array.isArray(t)));s.target_crs=t?this.convert(t):null}const l=t.find((t=>Array.isArray(t)&&"ABRIDGEDTRANSFORMATION"===t[0]));s.transformation=l?this.convert(l):null;break;case"ABRIDGEDTRANSFORMATION":if(s.type="Transformation",s.name=t[1],s.method=t.find((t=>Array.isArray(t)&&"METHOD"===t[0]))?this.convert(t.find((t=>Array.isArray(t)&&"METHOD"===t[0]))):null,s.parameters=t.filter((t=>Array.isArray(t)&&("PARAMETER"===t[0]||"PARAMETERFILE"===t[0]))).map((t=>"PARAMETER"===t[0]?this.convert(t):"PARAMETERFILE"===t[0]?{name:t[1],value:t[2],id:{authority:"EPSG",code:8656}}:void 0)),7===s.parameters.length){const t=s.parameters[6];"Scale difference"===t.name&&(t.value=Math.round(1e12*(t.value-1))/1e6)}s.id=this.getId(t);break;case"AXIS":s.coordinate_system||(s.coordinate_system={type:"unspecified",axis:[]}),s.coordinate_system.axis.push(this.convertAxis(t));break;case"LENGTHUNIT":const c=this.convertUnit(t,"LinearUnit");s.coordinate_system&&s.coordinate_system.axis&&s.coordinate_system.axis.forEach((t=>{t.unit||(t.unit=c)})),c.conversion_factor&&1!==c.conversion_factor&&s.semi_major_axis&&(s.semi_major_axis={value:s.semi_major_axis,unit:c});break;default:s.keyword=t[0]}return s}}class g extends f{static convert(t,s={}){return super.convert(t,s),"Cartesian"===s.coordinate_system?.subtype&&delete s.coordinate_system,s.usage&&delete s.usage,s}}class m extends f{static convert(t,s={}){super.convert(t,s);const i=t.find((t=>Array.isArray(t)&&"CS"===t[0]));i&&(s.coordinate_system={subtype:i[1],axis:this.extractAxes(t)});const a=t.find((t=>Array.isArray(t)&&"USAGE"===t[0]));return a&&(s.usage={scope:a.find((t=>Array.isArray(t)&&"SCOPE"===t[0]))?.[1],area:a.find((t=>Array.isArray(t)&&"AREA"===t[0]))?.[1],bbox:a.find((t=>Array.isArray(t)&&"BBOX"===t[0]))?.slice(1)}),s}}function p(t){const s=function(t){return t.find((t=>Array.isArray(t)&&"USAGE"===t[0]))?"2019":(t.find((t=>Array.isArray(t)&&"CS"===t[0]))||"BOUNDCRS"===t[0]||"PROJCRS"===t[0]||t[0],"2015")}(t);return("2019"===s?m:g).convert(t)}var y=/\s/,w=/[A-Za-z]/,E=/[A-Za-z84_]/,v=/[,\]]/,x=/[\d\.E\-\+]/;function S(t){if("string"!=typeof t)throw new Error("not a string");this.text=t.trim(),this.level=0,this.place=0,this.root=null,this.stack=[],this.currentObject=null,this.state=1}function G(t,s,i){Array.isArray(s)&&(i.unshift(s),s=null);var a=s?{}:t,h=i.reduce((function(t,s){return P(s,t),t}),a);s&&(t[s]=h)}function P(t,s){if(Array.isArray(t)){var i=t.shift();if("PARAMETER"===i&&(i=t.shift()),1===t.length)return Array.isArray(t[0])?(s[i]={},void P(t[0],s[i])):void(s[i]=t[0]);if(t.length)if("TOWGS84"!==i){if("AXIS"===i)return i in s||(s[i]=[]),void s[i].push(t);var a;switch(Array.isArray(i)||(s[i]={}),i){case"UNIT":case"PRIMEM":case"VERT_DATUM":return s[i]={name:t[0].toLowerCase(),convert:t[1]},void(3===t.length&&P(t[2],s[i]));case"SPHEROID":case"ELLIPSOID":return s[i]={name:t[0],a:t[1],rf:t[2]},void(4===t.length&&P(t[3],s[i]));case"EDATUM":case"ENGINEERINGDATUM":case"LOCAL_DATUM":case"DATUM":case"VERT_CS":case"VERTCRS":case"VERTICALCRS":return t[0]=["name",t[0]],void G(s,i,t);case"COMPD_CS":case"COMPOUNDCRS":case"FITTED_CS":case"PROJECTEDCRS":case"PROJCRS":case"GEOGCS":case"GEOCCS":case"PROJCS":case"LOCAL_CS":case"GEODCRS":case"GEODETICCRS":case"GEODETICDATUM":case"ENGCRS":case"ENGINEERINGCRS":return t[0]=["name",t[0]],G(s,i,t),void(s[i].type=i);default:for(a=-1;++a<t.length;)if(!Array.isArray(t[a]))return P(t,s[i]);return G(s,i,t)}}else s[i]=t;else s[i]=!0}else s[t]=!0}S.prototype.readCharicter=function(){var t=this.text[this.place++];if(4!==this.state)for(;y.test(t);){if(this.place>=this.text.length)return;t=this.text[this.place++]}switch(this.state){case 1:return this.neutral(t);case 2:return this.keyword(t);case 4:return this.quoted(t);case 5:return this.afterquote(t);case 3:return this.number(t);case-1:return}},S.prototype.afterquote=function(t){if('"'===t)return this.word+='"',void(this.state=4);if(v.test(t))return this.word=this.word.trim(),void this.afterItem(t);throw new Error("havn't handled \""+t+'" in afterquote yet, index '+this.place)},S.prototype.afterItem=function(t){return","===t?(null!==this.word&&this.currentObject.push(this.word),this.word=null,void(this.state=1)):"]"===t?(this.level--,null!==this.word&&(this.currentObject.push(this.word),this.word=null),this.state=1,this.currentObject=this.stack.pop(),void(this.currentObject||(this.state=-1))):void 0},S.prototype.number=function(t){if(!x.test(t)){if(v.test(t))return this.word=parseFloat(this.word),void this.afterItem(t);throw new Error("havn't handled \""+t+'" in number yet, index '+this.place)}this.word+=t},S.prototype.quoted=function(t){'"'!==t?this.word+=t:this.state=5},S.prototype.keyword=function(t){if(E.test(t))this.word+=t;else{if("["===t){var s=[];return s.push(this.word),this.level++,null===this.root?this.root=s:this.currentObject.push(s),this.stack.push(this.currentObject),this.currentObject=s,void(this.state=1)}if(!v.test(t))throw new Error("havn't handled \""+t+'" in keyword yet, index '+this.place);this.afterItem(t)}},S.prototype.neutral=function(t){if(w.test(t))return this.word=t,void(this.state=2);if('"'===t)return this.word="",void(this.state=4);if(x.test(t))return this.word=t,void(this.state=3);if(!v.test(t))throw new Error("havn't handled \""+t+'" in neutral yet, index '+this.place);this.afterItem(t)},S.prototype.output=function(){for(;this.place<this.text.length;)this.readCharicter();if(-1===this.state)return this.root;throw new Error('unable to parse string "'+this.text+'". State is '+this.state)};function b(t){return.017453292519943295*t}function N(t){const s=(t.projName||"").toLowerCase().replace(/_/g," ");t.long0||!t.longc||"albers conic equal area"!==s&&"lambert azimuthal equal area"!==s||(t.long0=t.longc),t.lat_ts||!t.lat1||"stereographic south pole"!==s&&"polar stereographic (variant b)"!==s?t.lat_ts||!t.lat0||"polar stereographic"!==s&&"polar stereographic (variant a)"!==s||(t.lat_ts=t.lat0,t.lat0=b(t.lat0>0?90:-90),delete t.lat1):(t.lat0=b(t.lat1>0?90:-90),t.lat_ts=t.lat1,delete t.lat1)}function A(t){let s={units:null,to_meter:void 0};return"string"==typeof t?(s.units=t.toLowerCase(),"metre"===s.units&&(s.units="meter"),"meter"===s.units&&(s.to_meter=1)):t?.name&&(s.units=t.name.toLowerCase(),"metre"===s.units&&(s.units="meter"),s.to_meter=t.conversion_factor),s}function I(t){return"object"==typeof t?t.value*t.unit.conversion_factor:t}function C(t,s){t.ellipsoid.radius?(s.a=t.ellipsoid.radius,s.rf=0):(s.a=I(t.ellipsoid.semi_major_axis),void 0!==t.ellipsoid.inverse_flattening?s.rf=t.ellipsoid.inverse_flattening:void 0!==t.ellipsoid.semi_major_axis&&void 0!==t.ellipsoid.semi_minor_axis&&(s.rf=s.a/(s.a-I(t.ellipsoid.semi_minor_axis))))}function k(t,s={}){return t&&"object"==typeof t?"BoundCRS"===t.type?(k(t.source_crs,s),t.transformation&&("NTv2"===t.transformation.method?.name?s.nadgrids=t.transformation.parameters[0].value:s.datum_params=t.transformation.parameters.map((t=>t.value))),s):(Object.keys(t).forEach((i=>{const a=t[i];if(null!==a)switch(i){case"name":if(s.srsCode)break;s.name=a,s.srsCode=a;break;case"type":"GeographicCRS"===a?s.projName="longlat":"ProjectedCRS"===a&&(s.projName=t.conversion?.method?.name);break;case"datum":case"datum_ensemble":a.ellipsoid&&(s.ellps=a.ellipsoid.name,C(a,s)),a.prime_meridian&&(s.from_greenwich=a.prime_meridian.longitude*Math.PI/180);break;case"ellipsoid":s.ellps=a.name,C(a,s);break;case"prime_meridian":s.long0=(a.longitude||0)*Math.PI/180;break;case"coordinate_system":if(a.axis)if(s.axis=a.axis.map((t=>{const s=t.direction;if("east"===s)return"e";if("north"===s)return"n";if("west"===s)return"w";if("south"===s)return"s";throw new Error(`Unknown axis direction: ${s}`)})).join("")+"u",a.unit){const{units:t,to_meter:i}=A(a.unit);s.units=t,s.to_meter=i}else if(a.axis[0]?.unit){const{units:t,to_meter:i}=A(a.axis[0].unit);s.units=t,s.to_meter=i}break;case"id":a.authority&&a.code&&(s.title=a.authority+":"+a.code);break;case"conversion":a.method&&a.method.name&&(s.projName=a.method.name),a.parameters&&a.parameters.forEach((t=>{const i=t.name.toLowerCase().replace(/\s+/g,"_"),a=t.value;t.unit&&t.unit.conversion_factor?s[i]=a*t.unit.conversion_factor:"degree"===t.unit?s[i]=a*Math.PI/180:s[i]=a}));break;case"unit":a.name&&(s.units=a.name.toLowerCase(),"metre"===s.units&&(s.units="meter")),a.conversion_factor&&(s.to_meter=a.conversion_factor);break;case"base_crs":k(a,s),s.datumCode=a.id?a.id.authority+"_"+a.id.code:a.name}})),void 0!==s.latitude_of_false_origin&&(s.lat0=s.latitude_of_false_origin),void 0!==s.longitude_of_false_origin&&(s.long0=s.longitude_of_false_origin),void 0!==s.latitude_of_standard_parallel&&(s.lat0=s.latitude_of_standard_parallel,s.lat1=s.latitude_of_standard_parallel),void 0!==s.latitude_of_1st_standard_parallel&&(s.lat1=s.latitude_of_1st_standard_parallel),void 0!==s.latitude_of_2nd_standard_parallel&&(s.lat2=s.latitude_of_2nd_standard_parallel),void 0!==s.latitude_of_projection_centre&&(s.lat0=s.latitude_of_projection_centre),void 0!==s.longitude_of_projection_centre&&(s.longc=s.longitude_of_projection_centre),void 0!==s.easting_at_false_origin&&(s.x0=s.easting_at_false_origin),void 0!==s.northing_at_false_origin&&(s.y0=s.northing_at_false_origin),void 0!==s.latitude_of_natural_origin&&(s.lat0=s.latitude_of_natural_origin),void 0!==s.longitude_of_natural_origin&&(s.long0=s.longitude_of_natural_origin),void 0!==s.longitude_of_origin&&(s.long0=s.longitude_of_origin),void 0!==s.false_easting&&(s.x0=s.false_easting),s.easting_at_projection_centre&&(s.x0=s.easting_at_projection_centre),void 0!==s.false_northing&&(s.y0=s.false_northing),s.northing_at_projection_centre&&(s.y0=s.northing_at_projection_centre),void 0!==s.standard_parallel_1&&(s.lat1=s.standard_parallel_1),void 0!==s.standard_parallel_2&&(s.lat2=s.standard_parallel_2),void 0!==s.scale_factor_at_natural_origin&&(s.k0=s.scale_factor_at_natural_origin),void 0!==s.scale_factor_at_projection_centre&&(s.k0=s.scale_factor_at_projection_centre),void 0!==s.scale_factor_on_pseudo_standard_parallel&&(s.k0=s.scale_factor_on_pseudo_standard_parallel),void 0!==s.azimuth&&(s.alpha=s.azimuth),void 0!==s.azimuth_at_projection_centre&&(s.alpha=s.azimuth_at_projection_centre),s.angle_from_rectified_to_skew_grid&&(s.rectified_grid_angle=s.angle_from_rectified_to_skew_grid),N(s),s):t}var R=["PROJECTEDCRS","PROJCRS","GEOGCS","GEOCCS","PROJCS","LOCAL_CS","GEODCRS","GEODETICCRS","GEODETICDATUM","ENGCRS","ENGINEERINGCRS"];function O(t){for(var s=Object.keys(t),i=0,a=s.length;i<a;++i){var h=s[i];-1!==R.indexOf(h)&&q(t[h]),"object"==typeof t[h]&&O(t[h])}}function q(t){if(t.AUTHORITY){var s=Object.keys(t.AUTHORITY)[0];s&&s in t.AUTHORITY&&(t.title=s+":"+t.AUTHORITY[s])}if("GEOGCS"===t.type?t.projName="longlat":"LOCAL_CS"===t.type?(t.projName="identity",t.local=!0):"object"==typeof t.PROJECTION?t.projName=Object.keys(t.PROJECTION)[0]:t.projName=t.PROJECTION,t.AXIS){for(var i="",a=0,h=t.AXIS.length;a<h;++a){var e=[t.AXIS[a][0].toLowerCase(),t.AXIS[a][1].toLowerCase()];-1!==e[0].indexOf("north")||("y"===e[0]||"lat"===e[0])&&"north"===e[1]?i+="n":-1!==e[0].indexOf("south")||("y"===e[0]||"lat"===e[0])&&"south"===e[1]?i+="s":-1!==e[0].indexOf("east")||("x"===e[0]||"lon"===e[0])&&"east"===e[1]?i+="e":-1===e[0].indexOf("west")&&("x"!==e[0]&&"lon"!==e[0]||"west"!==e[1])||(i+="w")}2===i.length&&(i+="u"),3===i.length&&(t.axis=i)}t.UNIT&&(t.units=t.UNIT.name.toLowerCase(),"metre"===t.units&&(t.units="meter"),t.UNIT.convert&&("GEOGCS"===t.type?t.DATUM&&t.DATUM.SPHEROID&&(t.to_meter=t.UNIT.convert*t.DATUM.SPHEROID.a):t.to_meter=t.UNIT.convert));var r=t.GEOGCS;function n(s){return s*(t.to_meter||1)}"GEOGCS"===t.type&&(r=t),r&&(r.DATUM?t.datumCode=r.DATUM.name.toLowerCase():t.datumCode=r.name.toLowerCase(),"d_"===t.datumCode.slice(0,2)&&(t.datumCode=t.datumCode.slice(2)),"new_zealand_1949"===t.datumCode&&(t.datumCode="nzgd49"),"wgs_1984"!==t.datumCode&&"world_geodetic_system_1984"!==t.datumCode||("Mercator_Auxiliary_Sphere"===t.PROJECTION&&(t.sphere=!0),t.datumCode="wgs84"),"belge_1972"===t.datumCode&&(t.datumCode="rnb72"),r.DATUM&&r.DATUM.SPHEROID&&(t.ellps=r.DATUM.SPHEROID.name.replace("_19","").replace(/[Cc]larke\_18/,"clrk"),"international"===t.ellps.toLowerCase().slice(0,13)&&(t.ellps="intl"),t.a=r.DATUM.SPHEROID.a,t.rf=parseFloat(r.DATUM.SPHEROID.rf,10)),r.DATUM&&r.DATUM.TOWGS84&&(t.datum_params=r.DATUM.TOWGS84),~t.datumCode.indexOf("osgb_1936")&&(t.datumCode="osgb36"),~t.datumCode.indexOf("osni_1952")&&(t.datumCode="osni52"),(~t.datumCode.indexOf("tm65")||~t.datumCode.indexOf("geodetic_datum_of_1965"))&&(t.datumCode="ire65"),"ch1903+"===t.datumCode&&(t.datumCode="ch1903"),~t.datumCode.indexOf("israel")&&(t.datumCode="isr93")),t.b&&!isFinite(t.b)&&(t.b=t.a),t.rectified_grid_angle&&(t.rectified_grid_angle=b(t.rectified_grid_angle));[["standard_parallel_1","Standard_Parallel_1"],["standard_parallel_1","Latitude of 1st standard parallel"],["standard_parallel_2","Standard_Parallel_2"],["standard_parallel_2","Latitude of 2nd standard parallel"],["false_easting","False_Easting"],["false_easting","False easting"],["false-easting","Easting at false origin"],["false_northing","False_Northing"],["false_northing","False northing"],["false_northing","Northing at false origin"],["central_meridian","Central_Meridian"],["central_meridian","Longitude of natural origin"],["central_meridian","Longitude of false origin"],["latitude_of_origin","Latitude_Of_Origin"],["latitude_of_origin","Central_Parallel"],["latitude_of_origin","Latitude of natural origin"],["latitude_of_origin","Latitude of false origin"],["scale_factor","Scale_Factor"],["k0","scale_factor"],["latitude_of_center","Latitude_Of_Center"],["latitude_of_center","Latitude_of_center"],["lat0","latitude_of_center",b],["longitude_of_center","Longitude_Of_Center"],["longitude_of_center","Longitude_of_center"],["longc","longitude_of_center",b],["x0","false_easting",n],["y0","false_northing",n],["long0","central_meridian",b],["lat0","latitude_of_origin",b],["lat0","standard_parallel_1",b],["lat1","standard_parallel_1",b],["lat2","standard_parallel_2",b],["azimuth","Azimuth"],["alpha","azimuth",b],["srsCode","name"]].forEach((function(s){return function(t,s){var i=s[0],a=s[1];!(i in t)&&a in t&&(t[i]=t[a],3===s.length&&(t[i]=s[2](t[i])))}(t,s)})),N(t)}function j(t){if("object"==typeof t)return k(t);const s=function(t){const s=t.toUpperCase();return s.includes("PROJCRS")||s.includes("GEOGCRS")||s.includes("BOUNDCRS")||s.includes("VERTCRS")||s.includes("LENGTHUNIT")||s.includes("ANGLEUNIT")||s.includes("SCALEUNIT")?"WKT2":(s.includes("PROJCS")||s.includes("GEOGCS")||s.includes("LOCAL_CS")||s.includes("VERT_CS")||s.includes("UNIT"),"WKT1")}(t);var i=new S(t).output();if("WKT2"===s){return k(p(i))}var a=i[0],h={};return P(i,h),O(h),h[a]}function T(t){var s=this;if(2===arguments.length){var i=arguments[1];"string"==typeof i?"+"===i.charAt(0)?T[t]=d(arguments[1]):T[t]=j(arguments[1]):T[t]=i}else if(1===arguments.length){if(Array.isArray(t))return t.map((function(t){return Array.isArray(t)?T.apply(s,t):T(t)}));if("string"==typeof t){if(t in T)return T[t]}else"EPSG"in t?T["EPSG:"+t.EPSG]=t:"ESRI"in t?T["ESRI:"+t.ESRI]=t:"IAU2000"in t?T["IAU2000:"+t.IAU2000]=t:console.log(t);return}}!function(t){t("EPSG:4326","+title=WGS 84 (long/lat) +proj=longlat +ellps=WGS84 +datum=WGS84 +units=degrees"),t("EPSG:4269","+title=NAD83 (long/lat) +proj=longlat +a=6378137.0 +b=6356752.31414036 +ellps=GRS80 +datum=NAD83 +units=degrees"),t("EPSG:3857","+title=WGS 84 / Pseudo-Mercator +proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +no_defs");for(var s=1;s<=60;++s)t("EPSG:"+(32600+s),"+proj=utm +zone="+s+" +datum=WGS84 +units=m"),t("EPSG:"+(32700+s),"+proj=utm +zone="+s+" +south +datum=WGS84 +units=m");t.WGS84=t["EPSG:4326"],t["EPSG:3785"]=t["EPSG:3857"],t.GOOGLE=t["EPSG:3857"],t["EPSG:900913"]=t["EPSG:3857"],t["EPSG:102113"]=t["EPSG:3857"]}(T);var L=["3857","900913","3785","102113"];function F(t){if(!function(t){return"string"==typeof t}(t))return"projName"in t?t:j(t);if(function(t){return t in T}(t))return T[t];if(function(t){return 0!==t.indexOf("+")&&-1!==t.indexOf("[")||"object"==typeof t&&!("srsCode"in t)}(t)){var s=j(t);if(function(t){var s=M(t,"authority");if(s){var i=M(s,"epsg");return i&&L.indexOf(i)>-1}}(s))return T["EPSG:3857"];var i=function(t){var s=M(t,"extension");if(s)return M(s,"proj4")}(s);return i?d(i):s}return function(t){return"+"===t[0]}(t)?d(t):void 0}function D(t,s){var i,a;if(t=t||{},!s)return t;for(a in s)void 0!==(i=s[a])&&(t[a]=i);return t}function B(t,s,i){var a=t*s;return i/Math.sqrt(1-a*a)}function z(t){return t<0?-1:1}function U(t){return Math.abs(t)<=l?t:t-z(t)*o}function H(t,s,i){var h=t*i,e=.5*t;return h=Math.pow((1-h)/(1+h),e),Math.tan(.5*(a-s))/h}function W(t,s){for(var i,h,e=.5*t,r=a-2*Math.atan(s),n=0;n<=15;n++)if(i=t*Math.sin(r),r+=h=a-2*Math.atan(s*Math.pow((1-i)/(1+i),e))-r,Math.abs(h)<=1e-10)return r;return-9999}function Q(t){return t}var X=[{init:function(){var t=this.b/this.a;this.es=1-t*t,"x0"in this||(this.x0=0),"y0"in this||(this.y0=0),this.e=Math.sqrt(this.es),this.lat_ts?this.sphere?this.k0=Math.cos(this.lat_ts):this.k0=B(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts)):this.k0||(this.k?this.k0=this.k:this.k0=1)},forward:function(t){var s,i,e=t.x,o=t.y;if(o*r>90&&o*r<-90&&e*r>180&&e*r<-180)return null;if(Math.abs(Math.abs(o)-a)<=h)return null;if(this.sphere)s=this.x0+this.a*this.k0*U(e-this.long0),i=this.y0+this.a*this.k0*Math.log(Math.tan(n+.5*o));else{var l=Math.sin(o),c=H(this.e,o,l);s=this.x0+this.a*this.k0*U(e-this.long0),i=this.y0-this.a*this.k0*Math.log(c)}return t.x=s,t.y=i,t},inverse:function(t){var s,i,h=t.x-this.x0,e=t.y-this.y0;if(this.sphere)i=a-2*Math.atan(Math.exp(-e/(this.a*this.k0)));else{var r=Math.exp(-e/(this.a*this.k0));if(-9999===(i=W(this.e,r)))return null}return s=U(this.long0+h/(this.a*this.k0)),t.x=s,t.y=i,t},names:["Mercator","Popular Visualisation Pseudo Mercator","Mercator_1SP","Mercator_Auxiliary_Sphere","Mercator_Variant_A","merc"]},{init:function(){},forward:Q,inverse:Q,names:["longlat","identity"]}],J={},V=[];function K(t,s){var i=V.length;return t.names?(V[i]=t,t.names.forEach((function(t){J[t.toLowerCase()]=i})),this):(console.log(s),!0)}function Z(t){return t.replace(/[-\(\)\s]+/g," ").trim().replace(/ /g,"_")}var Y={start:function(){X.forEach(K)},add:K,get:function(t){if(!t)return!1;var s=t.toLowerCase();return void 0!==J[s]&&V[J[s]]||(s=Z(s))in J&&V[J[s]]?V[J[s]]:void 0}},$={MERIT:{a:6378137,rf:298.257,ellipseName:"MERIT 1983"},SGS85:{a:6378136,rf:298.257,ellipseName:"Soviet Geodetic System 85"},GRS80:{a:6378137,rf:298.*********,ellipseName:"GRS 1980(IUGG, 1980)"},IAU76:{a:6378140,rf:298.257,ellipseName:"IAU 1976"},airy:{a:6377563.396,b:6356256.91,ellipseName:"Airy 1830"},APL4:{a:6378137,rf:298.25,ellipseName:"Appl. Physics. 1965"},NWL9D:{a:6378145,rf:298.25,ellipseName:"Naval Weapons Lab., 1965"},mod_airy:{a:6377340.189,b:6356034.446,ellipseName:"Modified Airy"},andrae:{a:6377104.43,rf:300,ellipseName:"Andrae 1876 (Den., Iclnd.)"},aust_SA:{a:6378160,rf:298.25,ellipseName:"Australian Natl & S. Amer. 1969"},GRS67:{a:6378160,rf:298.*********,ellipseName:"GRS 67(IUGG 1967)"},bessel:{a:6377397.155,rf:299.1528128,ellipseName:"Bessel 1841"},bess_nam:{a:6377483.865,rf:299.1528128,ellipseName:"Bessel 1841 (Namibia)"},clrk66:{a:6378206.4,b:6356583.8,ellipseName:"Clarke 1866"},clrk80:{a:6378249.145,rf:293.4663,ellipseName:"Clarke 1880 mod."},clrk80ign:{a:6378249.2,b:6356515,rf:293.4660213,ellipseName:"Clarke 1880 (IGN)"},clrk58:{a:6378293.*********,rf:294.2606763692654,ellipseName:"Clarke 1858"},CPM:{a:6375738.7,rf:334.29,ellipseName:"Comm. des Poids et Mesures 1799"},delmbr:{a:6376428,rf:311.5,ellipseName:"Delambre 1810 (Belgium)"},engelis:{a:6378136.05,rf:298.2566,ellipseName:"Engelis 1985"},evrst30:{a:6377276.345,rf:300.8017,ellipseName:"Everest 1830"},evrst48:{a:6377304.063,rf:300.8017,ellipseName:"Everest 1948"},evrst56:{a:6377301.243,rf:300.8017,ellipseName:"Everest 1956"},evrst69:{a:6377295.664,rf:300.8017,ellipseName:"Everest 1969"},evrstSS:{a:6377298.556,rf:300.8017,ellipseName:"Everest (Sabah & Sarawak)"},fschr60:{a:6378166,rf:298.3,ellipseName:"Fischer (Mercury Datum) 1960"},fschr60m:{a:6378155,rf:298.3,ellipseName:"Fischer 1960"},fschr68:{a:6378150,rf:298.3,ellipseName:"Fischer 1968"},helmert:{a:6378200,rf:298.3,ellipseName:"Helmert 1906"},hough:{a:6378270,rf:297,ellipseName:"Hough"},intl:{a:6378388,rf:297,ellipseName:"International 1909 (Hayford)"},kaula:{a:6378163,rf:298.24,ellipseName:"Kaula 1961"},lerch:{a:6378139,rf:298.257,ellipseName:"Lerch 1979"},mprts:{a:6397300,rf:191,ellipseName:"Maupertius 1738"},new_intl:{a:6378157.5,b:6356772.2,ellipseName:"New International 1967"},plessis:{a:6376523,rf:6355863,ellipseName:"Plessis 1817 (France)"},krass:{a:6378245,rf:298.3,ellipseName:"Krassovsky, 1942"},SEasia:{a:6378155,b:6356773.3205,ellipseName:"Southeast Asia"},walbeck:{a:6376896,b:6355834.8467,ellipseName:"Walbeck"},WGS60:{a:6378165,rf:298.3,ellipseName:"WGS 60"},WGS66:{a:6378145,rf:298.25,ellipseName:"WGS 66"},WGS7:{a:6378135,rf:298.26,ellipseName:"WGS 72"},WGS84:{a:6378137,rf:298.257223563,ellipseName:"WGS 84"},sphere:{a:6370997,b:6370997,ellipseName:"Normal Sphere (r=6370997)"}};const tt=$.WGS84;var st={wgs84:{towgs84:"0,0,0",ellipse:"WGS84",datumName:"WGS84"},ch1903:{towgs84:"674.374,15.056,405.346",ellipse:"bessel",datumName:"swiss"},ggrs87:{towgs84:"-199.87,74.79,246.62",ellipse:"GRS80",datumName:"Greek_Geodetic_Reference_System_1987"},nad83:{towgs84:"0,0,0",ellipse:"GRS80",datumName:"North_American_Datum_1983"},nad27:{nadgrids:"@conus,@alaska,@ntv2_0.gsb,@ntv1_can.dat",ellipse:"clrk66",datumName:"North_American_Datum_1927"},potsdam:{towgs84:"598.1,73.7,418.2,0.202,0.045,-2.455,6.7",ellipse:"bessel",datumName:"Potsdam Rauenberg 1950 DHDN"},carthage:{towgs84:"-263.0,6.0,431.0",ellipse:"clark80",datumName:"Carthage 1934 Tunisia"},hermannskogel:{towgs84:"577.326,90.129,463.919,5.137,1.474,5.297,2.4232",ellipse:"bessel",datumName:"Hermannskogel"},mgi:{towgs84:"577.326,90.129,463.919,5.137,1.474,5.297,2.4232",ellipse:"bessel",datumName:"Militar-Geographische Institut"},osni52:{towgs84:"482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15",ellipse:"airy",datumName:"Irish National"},ire65:{towgs84:"482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15",ellipse:"mod_airy",datumName:"Ireland 1965"},rassadiran:{towgs84:"-133.63,-157.5,-158.62",ellipse:"intl",datumName:"Rassadiran"},nzgd49:{towgs84:"59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993",ellipse:"intl",datumName:"New Zealand Geodetic Datum 1949"},osgb36:{towgs84:"446.448,-125.157,542.060,0.1502,0.2470,0.8421,-20.4894",ellipse:"airy",datumName:"Ordnance Survey of Great Britain 1936"},s_jtsk:{towgs84:"589,76,480",ellipse:"bessel",datumName:"S-JTSK (Ferro)"},beduaram:{towgs84:"-106,-87,188",ellipse:"clrk80",datumName:"Beduaram"},gunung_segara:{towgs84:"-403,684,41",ellipse:"bessel",datumName:"Gunung Segara Jakarta"},rnb72:{towgs84:"106.869,-52.2978,103.724,-0.33657,0.456955,-1.84218,1",ellipse:"intl",datumName:"Reseau National Belge 1972"},EPSG_5451:{towgs84:"6.41,-49.05,-11.28,1.5657,0.5242,6.9718,-5.7649"},IGNF_LURESG:{towgs84:"-192.986,13.673,-39.309,-0.4099,-2.9332,2.6881,0.43"},EPSG_4614:{towgs84:"-119.4248,-303.65872,-11.00061,1.164298,0.174458,1.096259,3.657065"},EPSG_4615:{towgs84:"-494.088,-312.129,279.877,-1.423,-1.013,1.59,-0.748"},ESRI_37241:{towgs84:"-76.822,257.457,-12.817,2.136,-0.033,-2.392,-0.031"},ESRI_37249:{towgs84:"-440.296,58.548,296.265,1.128,10.202,4.559,-0.438"},ESRI_37245:{towgs84:"-511.151,-181.269,139.609,1.05,2.703,1.798,3.071"},EPSG_4178:{towgs84:"24.9,-126.4,-93.2,-0.063,-0.247,-0.041,1.01"},EPSG_4622:{towgs84:"-472.29,-5.63,-304.12,0.4362,-0.8374,0.2563,1.8984"},EPSG_4625:{towgs84:"126.93,547.94,130.41,-2.7867,5.1612,-0.8584,13.8227"},EPSG_5252:{towgs84:"0.023,0.036,-0.068,0.00176,0.00912,-0.01136,0.00439"},EPSG_4314:{towgs84:"597.1,71.4,412.1,0.894,0.068,-1.563,7.58"},EPSG_4282:{towgs84:"-178.3,-316.7,-131.5,5.278,6.077,10.979,19.166"},EPSG_4231:{towgs84:"-83.11,-97.38,-117.22,0.0276,-0.2167,0.2147,0.1218"},EPSG_4274:{towgs84:"-230.994,102.591,25.199,0.633,-0.239,0.9,1.95"},EPSG_4134:{towgs84:"-180.624,-225.516,173.919,-0.81,-1.898,8.336,16.71006"},EPSG_4254:{towgs84:"18.38,192.45,96.82,0.056,-0.142,-0.2,-0.0013"},EPSG_4159:{towgs84:"-194.513,-63.978,-25.759,-3.4027,3.756,-3.352,-0.9175"},EPSG_4687:{towgs84:"0.072,-0.507,-0.245,0.0183,-0.0003,0.007,-0.0093"},EPSG_4227:{towgs84:"-83.58,-397.54,458.78,-17.595,-2.847,4.256,3.225"},EPSG_4746:{towgs84:"599.4,72.4,419.2,-0.062,-0.022,-2.723,6.46"},EPSG_4745:{towgs84:"612.4,77,440.2,-0.054,0.057,-2.797,2.55"},EPSG_6311:{towgs84:"8.846,-4.394,-1.122,-0.00237,-0.146528,0.130428,0.783926"},EPSG_4289:{towgs84:"565.7381,50.4018,465.2904,-1.91514,1.60363,-9.09546,4.07244"},EPSG_4230:{towgs84:"-68.863,-134.888,-111.49,-0.53,-0.14,0.57,-3.4"},EPSG_4154:{towgs84:"-123.02,-158.95,-168.47"},EPSG_4156:{towgs84:"570.8,85.7,462.8,4.998,1.587,5.261,3.56"},EPSG_4299:{towgs84:"482.5,-130.6,564.6,-1.042,-0.214,-0.631,8.15"},EPSG_4179:{towgs84:"33.4,-146.6,-76.3,-0.359,-0.053,0.844,-0.84"},EPSG_4313:{towgs84:"-106.8686,52.2978,-103.7239,0.3366,-0.457,1.8422,-1.2747"},EPSG_4194:{towgs84:"163.511,127.533,-159.789"},EPSG_4195:{towgs84:"105,326,-102.5"},EPSG_4196:{towgs84:"-45,417,-3.5"},EPSG_4611:{towgs84:"-162.619,-276.959,-161.764,0.067753,-2.243649,-1.158827,-1.094246"},EPSG_4633:{towgs84:"137.092,131.66,91.475,-1.9436,-11.5993,-4.3321,-7.4824"},EPSG_4641:{towgs84:"-408.809,366.856,-412.987,1.8842,-0.5308,2.1655,-121.0993"},EPSG_4643:{towgs84:"-480.26,-438.32,-643.429,16.3119,20.1721,-4.0349,-111.7002"},EPSG_4300:{towgs84:"482.5,-130.6,564.6,-1.042,-0.214,-0.631,8.15"},EPSG_4188:{towgs84:"482.5,-130.6,564.6,-1.042,-0.214,-0.631,8.15"},EPSG_4660:{towgs84:"982.6087,552.753,-540.873,32.39344,-153.25684,-96.2266,16.805"},EPSG_4662:{towgs84:"97.295,-263.247,310.882,-1.5999,0.8386,3.1409,13.3259"},EPSG_3906:{towgs84:"577.88891,165.22205,391.18289,4.9145,-0.94729,-13.05098,7.78664"},EPSG_4307:{towgs84:"-209.3622,-87.8162,404.6198,0.0046,3.4784,0.5805,-1.4547"},EPSG_6892:{towgs84:"-76.269,-16.683,68.562,-6.275,10.536,-4.286,-13.686"},EPSG_4690:{towgs84:"221.597,152.441,176.523,2.403,1.3893,0.884,11.4648"},EPSG_4691:{towgs84:"218.769,150.75,176.75,3.5231,2.0037,1.288,10.9817"},EPSG_4629:{towgs84:"72.51,345.411,79.241,-1.5862,-0.8826,-0.5495,1.3653"},EPSG_4630:{towgs84:"165.804,216.213,180.26,-0.6251,-0.4515,-0.0721,7.4111"},EPSG_4692:{towgs84:"217.109,86.452,23.711,0.0183,-0.0003,0.007,-0.0093"},EPSG_9333:{towgs84:"0,0,0,-8.393,0.749,-10.276,0"},EPSG_9059:{towgs84:"0,0,0"},EPSG_4312:{towgs84:"601.705,84.263,485.227,4.7354,1.3145,5.393,-2.3887"},EPSG_4123:{towgs84:"-96.062,-82.428,-121.753,4.801,0.345,-1.376,1.496"},EPSG_4309:{towgs84:"-124.45,183.74,44.64,-0.4384,0.5446,-0.9706,-2.1365"},ESRI_104106:{towgs84:"-283.088,-70.693,117.445,-1.157,0.059,-0.652,-4.058"},EPSG_4281:{towgs84:"-219.247,-73.802,269.529"},EPSG_4322:{towgs84:"0,0,4.5"},EPSG_4324:{towgs84:"0,0,1.9"},EPSG_4284:{towgs84:"43.822,-108.842,-119.585,1.455,-0.761,0.737,0.549"},EPSG_4277:{towgs84:"446.448,-125.157,542.06,0.15,0.247,0.842,-20.489"},EPSG_4207:{towgs84:"-282.1,-72.2,120,-1.529,0.145,-0.89,-4.46"},EPSG_4688:{towgs84:"347.175,1077.618,2623.677,33.9058,-70.6776,9.4013,186.0647"},EPSG_4689:{towgs84:"410.793,54.542,80.501,-2.5596,-2.3517,-0.6594,17.3218"},EPSG_4720:{towgs84:"0,0,4.5"},EPSG_4273:{towgs84:"278.3,93,474.5,7.889,0.05,-6.61,6.21"},EPSG_4240:{towgs84:"204.64,834.74,293.8"},EPSG_4817:{towgs84:"278.3,93,474.5,7.889,0.05,-6.61,6.21"},ESRI_104131:{towgs84:"426.62,142.62,460.09,4.98,4.49,-12.42,-17.1"},EPSG_4265:{towgs84:"-104.1,-49.1,-9.9,0.971,-2.917,0.714,-11.68"},EPSG_4263:{towgs84:"-111.92,-87.85,114.5,1.875,0.202,0.219,0.032"},EPSG_4298:{towgs84:"-689.5937,623.84046,-65.93566,-0.02331,1.17094,-0.80054,5.88536"},EPSG_4270:{towgs84:"-253.4392,-148.452,386.5267,0.15605,0.43,-0.1013,-0.0424"},EPSG_4229:{towgs84:"-121.8,98.1,-10.7"},EPSG_4220:{towgs84:"-55.5,-348,-229.2"},EPSG_4214:{towgs84:"12.646,-155.176,-80.863"},EPSG_4232:{towgs84:"-345,3,223"},EPSG_4238:{towgs84:"-1.977,-13.06,-9.993,0.364,0.254,0.689,-1.037"},EPSG_4168:{towgs84:"-170,33,326"},EPSG_4131:{towgs84:"199,931,318.9"},EPSG_4152:{towgs84:"-0.9102,2.0141,0.5602,0.029039,0.010065,0.010101,0"},EPSG_5228:{towgs84:"572.213,85.334,461.94,4.9732,1.529,5.2484,3.5378"},EPSG_8351:{towgs84:"485.021,169.465,483.839,7.786342,4.397554,4.102655,0"},EPSG_4683:{towgs84:"-127.62,-67.24,-47.04,-3.068,4.903,1.578,-1.06"},EPSG_4133:{towgs84:"0,0,0"},EPSG_7373:{towgs84:"0.819,-0.5762,-1.6446,-0.00378,-0.03317,0.00318,0.0693"},EPSG_9075:{towgs84:"-0.9102,2.0141,0.5602,0.029039,0.010065,0.010101,0"},EPSG_9072:{towgs84:"-0.9102,2.0141,0.5602,0.029039,0.010065,0.010101,0"},EPSG_9294:{towgs84:"1.16835,-1.42001,-2.24431,-0.00822,-0.05508,0.01818,0.23388"},EPSG_4212:{towgs84:"-267.434,173.496,181.814,-13.4704,8.7154,7.3926,14.7492"},EPSG_4191:{towgs84:"-44.183,-0.58,-38.489,2.3867,2.7072,-3.5196,-8.2703"},EPSG_4237:{towgs84:"52.684,-71.194,-13.975,-0.312,-0.1063,-0.3729,1.0191"},EPSG_4740:{towgs84:"-1.08,-0.27,-0.9"},EPSG_4124:{towgs84:"419.3836,99.3335,591.3451,0.850389,1.817277,-7.862238,-0.99496"},EPSG_5681:{towgs84:"584.9636,107.7175,413.8067,1.1155,0.2824,-3.1384,7.9922"},EPSG_4141:{towgs84:"23.772,17.49,17.859,-0.3132,-1.85274,1.67299,-5.4262"},EPSG_4204:{towgs84:"-85.645,-273.077,-79.708,2.289,-1.421,2.532,3.194"},EPSG_4319:{towgs84:"226.702,-193.337,-35.371,-2.229,-4.391,9.238,0.9798"},EPSG_4200:{towgs84:"24.82,-131.21,-82.66"},EPSG_4130:{towgs84:"0,0,0"},EPSG_4127:{towgs84:"-82.875,-57.097,-156.768,-2.158,1.524,-0.982,-0.359"},EPSG_4149:{towgs84:"674.374,15.056,405.346"},EPSG_4617:{towgs84:"-0.991,1.9072,0.5129,1.25033e-7,4.6785e-8,5.6529e-8,0"},EPSG_4663:{towgs84:"-210.502,-66.902,-48.476,2.094,-15.067,-5.817,0.485"},EPSG_4664:{towgs84:"-211.939,137.626,58.3,-0.089,0.251,0.079,0.384"},EPSG_4665:{towgs84:"-105.854,165.589,-38.312,-0.003,-0.026,0.024,-0.048"},EPSG_4666:{towgs84:"631.392,-66.551,481.442,1.09,-4.445,-4.487,-4.43"},EPSG_4756:{towgs84:"-192.873,-39.382,-111.202,-0.00205,-0.0005,0.00335,0.0188"},EPSG_4723:{towgs84:"-179.483,-69.379,-27.584,-7.862,8.163,6.042,-13.925"},EPSG_4726:{towgs84:"8.853,-52.644,180.304,-0.393,-2.323,2.96,-24.081"},EPSG_4267:{towgs84:"-8.0,160.0,176.0"},EPSG_5365:{towgs84:"-0.16959,0.35312,0.51846,0.03385,-0.16325,0.03446,0.03693"},EPSG_4218:{towgs84:"304.5,306.5,-318.1"},EPSG_4242:{towgs84:"-33.722,153.789,94.959,-8.581,-4.478,4.54,8.95"},EPSG_4216:{towgs84:"-292.295,248.758,429.447,4.9971,2.99,6.6906,1.0289"},ESRI_104105:{towgs84:"631.392,-66.551,481.442,1.09,-4.445,-4.487,-4.43"},ESRI_104129:{towgs84:"0,0,0"},EPSG_4673:{towgs84:"174.05,-25.49,112.57"},EPSG_4202:{towgs84:"-124,-60,154"},EPSG_4203:{towgs84:"-117.763,-51.51,139.061,0.292,0.443,0.277,-0.191"},EPSG_3819:{towgs84:"595.48,121.69,515.35,4.115,-2.9383,0.853,-3.408"},EPSG_8694:{towgs84:"-93.799,-132.737,-219.073,-1.844,0.648,-6.37,-0.169"},EPSG_4145:{towgs84:"275.57,676.78,229.6"},EPSG_4283:{towgs84:"61.55,-10.87,-40.19,39.4924,32.7221,32.8979,-9.994"},EPSG_4317:{towgs84:"2.3287,-147.0425,-92.0802,-0.3092483,0.32482185,0.49729934,5.68906266"},EPSG_4272:{towgs84:"59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993"},EPSG_4248:{towgs84:"-307.7,265.3,-363.5"},EPSG_5561:{towgs84:"24,-121,-76"},EPSG_5233:{towgs84:"-0.293,766.95,87.713,0.195704,1.695068,3.473016,-0.039338"},ESRI_104130:{towgs84:"-86,-98,-119"},ESRI_104102:{towgs84:"682,-203,480"},ESRI_37207:{towgs84:"7,-10,-26"},EPSG_4675:{towgs84:"59.935,118.4,-10.871"},ESRI_104109:{towgs84:"-89.121,-348.182,260.871"},ESRI_104112:{towgs84:"-185.583,-230.096,281.361"},ESRI_104113:{towgs84:"25.1,-275.6,222.6"},IGNF_WGS72G:{towgs84:"0,12,6"},IGNF_NTFG:{towgs84:"-168,-60,320"},IGNF_EFATE57G:{towgs84:"-127,-769,472"},IGNF_PGP50G:{towgs84:"324.8,153.6,172.1"},IGNF_REUN47G:{towgs84:"94,-948,-1262"},IGNF_CSG67G:{towgs84:"-186,230,110"},IGNF_GUAD48G:{towgs84:"-467,-16,-300"},IGNF_TAHI51G:{towgs84:"162,117,154"},IGNF_TAHAAG:{towgs84:"65,342,77"},IGNF_NUKU72G:{towgs84:"84,274,65"},IGNF_PETRELS72G:{towgs84:"365,194,166"},IGNF_WALL78G:{towgs84:"253,-133,-127"},IGNF_MAYO50G:{towgs84:"-382,-59,-262"},IGNF_TANNAG:{towgs84:"-139,-967,436"},IGNF_IGN72G:{towgs84:"-13,-348,292"},IGNF_ATIGG:{towgs84:"1118,23,66"},IGNF_FANGA84G:{towgs84:"150.57,158.33,118.32"},IGNF_RUSAT84G:{towgs84:"202.13,174.6,-15.74"},IGNF_KAUE70G:{towgs84:"126.74,300.1,-75.49"},IGNF_MOP90G:{towgs84:"-10.8,-1.8,12.77"},IGNF_MHPF67G:{towgs84:"338.08,212.58,-296.17"},IGNF_TAHI79G:{towgs84:"160.61,116.05,153.69"},IGNF_ANAA92G:{towgs84:"1.5,3.84,4.81"},IGNF_MARQUI72G:{towgs84:"330.91,-13.92,58.56"},IGNF_APAT86G:{towgs84:"143.6,197.82,74.05"},IGNF_TUBU69G:{towgs84:"237.17,171.61,-77.84"},IGNF_STPM50G:{towgs84:"11.363,424.148,373.13"},EPSG_4150:{towgs84:"674.374,15.056,405.346"},EPSG_4754:{towgs84:"-208.4058,-109.8777,-2.5764"},ESRI_104101:{towgs84:"374,150,588"},EPSG_4693:{towgs84:"0,-0.15,0.68"},EPSG_6207:{towgs84:"293.17,726.18,245.36"},EPSG_4153:{towgs84:"-133.63,-157.5,-158.62"},EPSG_4132:{towgs84:"-241.54,-163.64,396.06"},EPSG_4221:{towgs84:"-154.5,150.7,100.4"},EPSG_4266:{towgs84:"-80.7,-132.5,41.1"},EPSG_4193:{towgs84:"-70.9,-151.8,-41.4"},EPSG_5340:{towgs84:"-0.41,0.46,-0.35"},EPSG_4246:{towgs84:"-294.7,-200.1,525.5"},EPSG_4318:{towgs84:"-3.2,-5.7,2.8"},EPSG_4121:{towgs84:"-199.87,74.79,246.62"},EPSG_4223:{towgs84:"-260.1,5.5,432.2"},EPSG_4158:{towgs84:"-0.465,372.095,171.736"},EPSG_4285:{towgs84:"-128.16,-282.42,21.93"},EPSG_4613:{towgs84:"-404.78,685.68,45.47"},EPSG_4607:{towgs84:"195.671,332.517,274.607"},EPSG_4475:{towgs84:"-381.788,-57.501,-256.673"},EPSG_4208:{towgs84:"-157.84,308.54,-146.6"},EPSG_4743:{towgs84:"70.995,-335.916,262.898"},EPSG_4710:{towgs84:"-323.65,551.39,-491.22"},EPSG_7881:{towgs84:"-0.077,0.079,0.086"},EPSG_4682:{towgs84:"283.729,735.942,261.143"},EPSG_4739:{towgs84:"-156,-271,-189"},EPSG_4679:{towgs84:"-80.01,253.26,291.19"},EPSG_4750:{towgs84:"-56.263,16.136,-22.856"},EPSG_4644:{towgs84:"-10.18,-350.43,291.37"},EPSG_4695:{towgs84:"-103.746,-9.614,-255.95"},EPSG_4292:{towgs84:"-355,21,72"},EPSG_4302:{towgs84:"-61.702,284.488,472.052"},EPSG_4143:{towgs84:"-124.76,53,466.79"},EPSG_4606:{towgs84:"-153,153,307"},EPSG_4699:{towgs84:"-770.1,158.4,-498.2"},EPSG_4247:{towgs84:"-273.5,110.6,-357.9"},EPSG_4160:{towgs84:"8.88,184.86,106.69"},EPSG_4161:{towgs84:"-233.43,6.65,173.64"},EPSG_9251:{towgs84:"-9.5,122.9,138.2"},EPSG_9253:{towgs84:"-78.1,101.6,133.3"},EPSG_4297:{towgs84:"-198.383,-240.517,-107.909"},EPSG_4269:{towgs84:"0,0,0"},EPSG_4301:{towgs84:"-147,506,687"},EPSG_4618:{towgs84:"-59,-11,-52"},EPSG_4612:{towgs84:"0,0,0"},EPSG_4678:{towgs84:"44.585,-131.212,-39.544"},EPSG_4250:{towgs84:"-130,29,364"},EPSG_4144:{towgs84:"214,804,268"},EPSG_4147:{towgs84:"-17.51,-108.32,-62.39"},EPSG_4259:{towgs84:"-254.1,-5.36,-100.29"},EPSG_4164:{towgs84:"-76,-138,67"},EPSG_4211:{towgs84:"-378.873,676.002,-46.255"},EPSG_4182:{towgs84:"-422.651,-172.995,84.02"},EPSG_4224:{towgs84:"-143.87,243.37,-33.52"},EPSG_4225:{towgs84:"-205.57,168.77,-4.12"},EPSG_5527:{towgs84:"-67.35,3.88,-38.22"},EPSG_4752:{towgs84:"98,390,-22"},EPSG_4310:{towgs84:"-30,190,89"},EPSG_9248:{towgs84:"-192.26,65.72,132.08"},EPSG_4680:{towgs84:"124.5,-63.5,-281"},EPSG_4701:{towgs84:"-79.9,-158,-168.9"},EPSG_4706:{towgs84:"-146.21,112.63,4.05"},EPSG_4805:{towgs84:"682,-203,480"},EPSG_4201:{towgs84:"-165,-11,206"},EPSG_4210:{towgs84:"-157,-2,-299"},EPSG_4183:{towgs84:"-104,167,-38"},EPSG_4139:{towgs84:"11,72,-101"},EPSG_4668:{towgs84:"-86,-98,-119"},EPSG_4717:{towgs84:"-2,151,181"},EPSG_4732:{towgs84:"102,52,-38"},EPSG_4280:{towgs84:"-377,681,-50"},EPSG_4209:{towgs84:"-138,-105,-289"},EPSG_4261:{towgs84:"31,146,47"},EPSG_4658:{towgs84:"-73,46,-86"},EPSG_4721:{towgs84:"265.025,384.929,-194.046"},EPSG_4222:{towgs84:"-136,-108,-292"},EPSG_4601:{towgs84:"-255,-15,71"},EPSG_4602:{towgs84:"725,685,536"},EPSG_4603:{towgs84:"72,213.7,93"},EPSG_4605:{towgs84:"9,183,236"},EPSG_4621:{towgs84:"137,248,-430"},EPSG_4657:{towgs84:"-28,199,5"},EPSG_4316:{towgs84:"103.25,-100.4,-307.19"},EPSG_4642:{towgs84:"-13,-348,292"},EPSG_4698:{towgs84:"145,-187,103"},EPSG_4192:{towgs84:"-206.1,-174.7,-87.7"},EPSG_4311:{towgs84:"-265,120,-358"},EPSG_4135:{towgs84:"58,-283,-182"},ESRI_104138:{towgs84:"198,-226,-347"},EPSG_4245:{towgs84:"-11,851,5"},EPSG_4142:{towgs84:"-125,53,467"},EPSG_4213:{towgs84:"-106,-87,188"},EPSG_4253:{towgs84:"-133,-77,-51"},EPSG_4129:{towgs84:"-132,-110,-335"},EPSG_4713:{towgs84:"-77,-128,142"},EPSG_4239:{towgs84:"217,823,299"},EPSG_4146:{towgs84:"295,736,257"},EPSG_4155:{towgs84:"-83,37,124"},EPSG_4165:{towgs84:"-173,253,27"},EPSG_4672:{towgs84:"175,-38,113"},EPSG_4236:{towgs84:"-637,-549,-203"},EPSG_4251:{towgs84:"-90,40,88"},EPSG_4271:{towgs84:"-2,374,172"},EPSG_4175:{towgs84:"-88,4,101"},EPSG_4716:{towgs84:"298,-304,-375"},EPSG_4315:{towgs84:"-23,259,-9"},EPSG_4744:{towgs84:"-242.2,-144.9,370.3"},EPSG_4244:{towgs84:"-97,787,86"},EPSG_4293:{towgs84:"616,97,-251"},EPSG_4714:{towgs84:"-127,-769,472"},EPSG_4736:{towgs84:"260,12,-147"},EPSG_6883:{towgs84:"-235,-110,393"},EPSG_6894:{towgs84:"-63,176,185"},EPSG_4205:{towgs84:"-43,-163,45"},EPSG_4256:{towgs84:"41,-220,-134"},EPSG_4262:{towgs84:"639,405,60"},EPSG_4604:{towgs84:"174,359,365"},EPSG_4169:{towgs84:"-115,118,426"},EPSG_4620:{towgs84:"-106,-129,165"},EPSG_4184:{towgs84:"-203,141,53"},EPSG_4616:{towgs84:"-289,-124,60"},EPSG_9403:{towgs84:"-307,-92,127"},EPSG_4684:{towgs84:"-133,-321,50"},EPSG_4708:{towgs84:"-491,-22,435"},EPSG_4707:{towgs84:"114,-116,-333"},EPSG_4709:{towgs84:"145,75,-272"},EPSG_4712:{towgs84:"-205,107,53"},EPSG_4711:{towgs84:"124,-234,-25"},EPSG_4718:{towgs84:"230,-199,-752"},EPSG_4719:{towgs84:"211,147,111"},EPSG_4724:{towgs84:"208,-435,-229"},EPSG_4725:{towgs84:"189,-79,-202"},EPSG_4735:{towgs84:"647,1777,-1124"},EPSG_4722:{towgs84:"-794,119,-298"},EPSG_4728:{towgs84:"-307,-92,127"},EPSG_4734:{towgs84:"-632,438,-609"},EPSG_4727:{towgs84:"912,-58,1227"},EPSG_4729:{towgs84:"185,165,42"},EPSG_4730:{towgs84:"170,42,84"},EPSG_4733:{towgs84:"276,-57,149"},ESRI_37218:{towgs84:"230,-199,-752"},ESRI_37240:{towgs84:"-7,215,225"},ESRI_37221:{towgs84:"252,-209,-751"},ESRI_4305:{towgs84:"-123,-206,219"},ESRI_104139:{towgs84:"-73,-247,227"},EPSG_4748:{towgs84:"51,391,-36"},EPSG_4219:{towgs84:"-384,664,-48"},EPSG_4255:{towgs84:"-333,-222,114"},EPSG_4257:{towgs84:"-587.8,519.75,145.76"},EPSG_4646:{towgs84:"-963,510,-359"},EPSG_6881:{towgs84:"-24,-203,268"},EPSG_6882:{towgs84:"-183,-15,273"},EPSG_4715:{towgs84:"-104,-129,239"},IGNF_RGF93GDD:{towgs84:"0,0,0"},IGNF_RGM04GDD:{towgs84:"0,0,0"},IGNF_RGSPM06GDD:{towgs84:"0,0,0"},IGNF_RGTAAF07GDD:{towgs84:"0,0,0"},IGNF_RGFG95GDD:{towgs84:"0,0,0"},IGNF_RGNCG:{towgs84:"0,0,0"},IGNF_RGPFGDD:{towgs84:"0,0,0"},IGNF_ETRS89G:{towgs84:"0,0,0"},IGNF_RGR92GDD:{towgs84:"0,0,0"},EPSG_4173:{towgs84:"0,0,0"},EPSG_4180:{towgs84:"0,0,0"},EPSG_4619:{towgs84:"0,0,0"},EPSG_4667:{towgs84:"0,0,0"},EPSG_4075:{towgs84:"0,0,0"},EPSG_6706:{towgs84:"0,0,0"},EPSG_7798:{towgs84:"0,0,0"},EPSG_4661:{towgs84:"0,0,0"},EPSG_4669:{towgs84:"0,0,0"},EPSG_8685:{towgs84:"0,0,0"},EPSG_4151:{towgs84:"0,0,0"},EPSG_9702:{towgs84:"0,0,0"},EPSG_4758:{towgs84:"0,0,0"},EPSG_4761:{towgs84:"0,0,0"},EPSG_4765:{towgs84:"0,0,0"},EPSG_8997:{towgs84:"0,0,0"},EPSG_4023:{towgs84:"0,0,0"},EPSG_4670:{towgs84:"0,0,0"},EPSG_4694:{towgs84:"0,0,0"},EPSG_4148:{towgs84:"0,0,0"},EPSG_4163:{towgs84:"0,0,0"},EPSG_4167:{towgs84:"0,0,0"},EPSG_4189:{towgs84:"0,0,0"},EPSG_4190:{towgs84:"0,0,0"},EPSG_4176:{towgs84:"0,0,0"},EPSG_4659:{towgs84:"0,0,0"},EPSG_3824:{towgs84:"0,0,0"},EPSG_3889:{towgs84:"0,0,0"},EPSG_4046:{towgs84:"0,0,0"},EPSG_4081:{towgs84:"0,0,0"},EPSG_4558:{towgs84:"0,0,0"},EPSG_4483:{towgs84:"0,0,0"},EPSG_5013:{towgs84:"0,0,0"},EPSG_5264:{towgs84:"0,0,0"},EPSG_5324:{towgs84:"0,0,0"},EPSG_5354:{towgs84:"0,0,0"},EPSG_5371:{towgs84:"0,0,0"},EPSG_5373:{towgs84:"0,0,0"},EPSG_5381:{towgs84:"0,0,0"},EPSG_5393:{towgs84:"0,0,0"},EPSG_5489:{towgs84:"0,0,0"},EPSG_5593:{towgs84:"0,0,0"},EPSG_6135:{towgs84:"0,0,0"},EPSG_6365:{towgs84:"0,0,0"},EPSG_5246:{towgs84:"0,0,0"},EPSG_7886:{towgs84:"0,0,0"},EPSG_8431:{towgs84:"0,0,0"},EPSG_8427:{towgs84:"0,0,0"},EPSG_8699:{towgs84:"0,0,0"},EPSG_8818:{towgs84:"0,0,0"},EPSG_4757:{towgs84:"0,0,0"},EPSG_9140:{towgs84:"0,0,0"},EPSG_8086:{towgs84:"0,0,0"},EPSG_4686:{towgs84:"0,0,0"},EPSG_4737:{towgs84:"0,0,0"},EPSG_4702:{towgs84:"0,0,0"},EPSG_4747:{towgs84:"0,0,0"},EPSG_4749:{towgs84:"0,0,0"},EPSG_4674:{towgs84:"0,0,0"},EPSG_4755:{towgs84:"0,0,0"},EPSG_4759:{towgs84:"0,0,0"},EPSG_4762:{towgs84:"0,0,0"},EPSG_4763:{towgs84:"0,0,0"},EPSG_4764:{towgs84:"0,0,0"},EPSG_4166:{towgs84:"0,0,0"},EPSG_4170:{towgs84:"0,0,0"},EPSG_5546:{towgs84:"0,0,0"},EPSG_7844:{towgs84:"0,0,0"},EPSG_4818:{towgs84:"589,76,480"}};for(var it in st){var at=st[it];at.datumName&&(st[at.datumName]=at)}var ht={};async function et(t,s){for(var i=[],a=await s.getImageCount(),h=a-1;h>=0;h--){var e=await s.getImage(h),r=await e.readRasters(),n=[e.getWidth(),e.getHeight()],o=e.getBoundingBox().map(nt),l=[e.fileDirectory.ModelPixelScale[0],e.fileDirectory.ModelPixelScale[1]].map(nt),c=o[0]+(n[0]-1)*l[0],u=o[3]-(n[1]-1)*l[1],_=r[0],M=r[1],d=[];for(let t=n[1]-1;t>=0;t--)for(let s=n[0]-1;s>=0;s--){var f=t*n[0]+s;d.push([-ot(M[f]),ot(_[f])])}i.push({del:l,lim:n,ll:[-c,u],cvs:d})}var g={header:{nSubgrids:a},subgrids:i};return ht[t]=g,g}function rt(t){if(0===t.length)return null;var s="@"===t[0];return s&&(t=t.slice(1)),"null"===t?{name:"null",mandatory:!s,grid:null,isNull:!0}:{name:t,mandatory:!s,grid:ht[t]||null,isNull:!1}}function nt(t){return t*Math.PI/180}function ot(t){return t/3600*Math.PI/180}function lt(t,s,i){return String.fromCharCode.apply(null,new Uint8Array(t.buffer.slice(s,i)))}function ct(t){return t.map((function(t){return[ot(t.longitudeShift),ot(t.latitudeShift)]}))}function ut(t,s,i){return{name:lt(t,s+8,s+16).trim(),parent:lt(t,s+24,s+24+8).trim(),lowerLatitude:t.getFloat64(s+72,i),upperLatitude:t.getFloat64(s+88,i),lowerLongitude:t.getFloat64(s+104,i),upperLongitude:t.getFloat64(s+120,i),latitudeInterval:t.getFloat64(s+136,i),longitudeInterval:t.getFloat64(s+152,i),gridNodeCount:t.getInt32(s+168,i)}}function _t(t,s,i,a,h){var e=s+176,r=16;!1===h&&(r=8);for(var n=[],o=0;o<i.gridNodeCount;o++){var l={latitudeShift:t.getFloat32(e+o*r,a),longitudeShift:t.getFloat32(e+o*r+4,a)};!1!==h&&(l.latitudeAccuracy=t.getFloat32(e+o*r+8,a),l.longitudeAccuracy=t.getFloat32(e+o*r+12,a)),n.push(l)}return n}function Mt(t,s){if(!(this instanceof Mt))return new Mt(t);this.forward=null,this.inverse=null,this.init=null,this.name,this.names=null,this.title,s=s||function(t){if(t)throw t};var a=F(t);if("object"==typeof a){var e=Mt.projections.get(a.projName);if(e){if(a.datumCode&&"none"!==a.datumCode){var r=M(st,a.datumCode);r&&(a.datum_params=a.datum_params||(r.towgs84?r.towgs84.split(","):null),a.ellps=r.ellipse,a.datumName=r.datumName?r.datumName:a.datumCode)}a.k0=a.k0||1,a.axis=a.axis||"enu",a.ellps=a.ellps||"wgs84",a.lat1=a.lat1||a.lat0;var n,o,l,c,u,_,d,f=function(t,s,i,a,e){if(!t){var r=M($,a);r||(r=tt),t=r.a,s=r.b,i=r.rf}return i&&!s&&(s=(1-1/i)*t),(0===i||Math.abs(t-s)<h)&&(e=!0,s=t),{a:t,b:s,rf:i,sphere:e}}(a.a,a.b,a.rf,a.ellps,a.sphere),g=(n=f.a,o=f.b,f.rf,l=a.R_A,_=((c=n*n)-(u=o*o))/c,d=0,l?(c=(n*=1-_*(.16666666666666666+_*(.04722222222222222+.022156084656084655*_)))*n,_=0):d=Math.sqrt(_),{es:_,e:d,ep2:(c-u)/u}),m=function(t){return void 0===t?null:t.split(",").map(rt)}(a.nadgrids),p=a.datum||function(t,s,a,h,e,r,n){var o={};return o.datum_type=void 0===t||"none"===t?5:4,s&&(o.datum_params=s.map(parseFloat),0===o.datum_params[0]&&0===o.datum_params[1]&&0===o.datum_params[2]||(o.datum_type=1),o.datum_params.length>3&&(0===o.datum_params[3]&&0===o.datum_params[4]&&0===o.datum_params[5]&&0===o.datum_params[6]||(o.datum_type=2,o.datum_params[3]*=i,o.datum_params[4]*=i,o.datum_params[5]*=i,o.datum_params[6]=o.datum_params[6]/1e6+1))),n&&(o.datum_type=3,o.grids=n),o.a=a,o.b=h,o.es=e,o.ep2=r,o}(a.datumCode,a.datum_params,f.a,f.b,g.es,g.ep2,m);D(this,a),D(this,e),this.a=f.a,this.b=f.b,this.rf=f.rf,this.sphere=f.sphere,this.es=g.es,this.e=g.e,this.ep2=g.ep2,this.datum=p,"init"in this&&"function"==typeof this.init&&this.init(),s(null,this)}else s("Could not get projection name from: "+t)}else s("Could not parse to valid json: "+t)}function dt(t,s,i){var h,e,r,n,o=t.x,l=t.y,c=t.z?t.z:0;if(l<-a&&l>-1.001*a)l=-a;else if(l>a&&l<1.001*a)l=a;else{if(l<-a)return{x:-1/0,y:-1/0,z:t.z};if(l>a)return{x:1/0,y:1/0,z:t.z}}return o>Math.PI&&(o-=2*Math.PI),e=Math.sin(l),n=Math.cos(l),r=e*e,{x:((h=i/Math.sqrt(1-s*r))+c)*n*Math.cos(o),y:(h+c)*n*Math.sin(o),z:(h*(1-s)+c)*e}}function ft(t,s,i,a){var h,e,r,n,o,l,c,u,_,M,d,f,g,m,p,y=1e-12,w=t.x,E=t.y,v=t.z?t.z:0;if(h=Math.sqrt(w*w+E*E),e=Math.sqrt(w*w+E*E+v*v),h/i<y){if(m=0,e/i<y)return p=-a,{x:t.x,y:t.y,z:t.z}}else m=Math.atan2(E,w);r=v/e,u=(n=h/e)*(1-s)*(o=1/Math.sqrt(1-s*(2-s)*n*n)),_=r*o,g=0;do{g++,l=s*(c=i/Math.sqrt(1-s*_*_))/(c+(p=h*u+v*_-c*(1-s*_*_))),f=(d=r*(o=1/Math.sqrt(1-l*(2-l)*n*n)))*u-(M=n*(1-l)*o)*_,u=M,_=d}while(f*f>1e-24&&g<30);return{x:m,y:Math.atan(d/Math.abs(M)),z:p}}function gt(t){return 1===t||2===t}function mt(i,a,h){if(function(t,s){return t.datum_type===s.datum_type&&!(t.a!==s.a||Math.abs(t.es-s.es)>5e-11)&&(1===t.datum_type?t.datum_params[0]===s.datum_params[0]&&t.datum_params[1]===s.datum_params[1]&&t.datum_params[2]===s.datum_params[2]:2!==t.datum_type||t.datum_params[0]===s.datum_params[0]&&t.datum_params[1]===s.datum_params[1]&&t.datum_params[2]===s.datum_params[2]&&t.datum_params[3]===s.datum_params[3]&&t.datum_params[4]===s.datum_params[4]&&t.datum_params[5]===s.datum_params[5]&&t.datum_params[6]===s.datum_params[6])}(i,a))return h;if(5===i.datum_type||5===a.datum_type)return h;var e=i.a,r=i.es;if(3===i.datum_type){if(0!==pt(i,!1,h))return;e=t,r=s}var n=a.a,o=a.b,l=a.es;if(3===a.datum_type&&(n=t,o=6356752.314,l=s),r===l&&e===n&&!gt(i.datum_type)&&!gt(a.datum_type))return h;if((h=dt(h,r,e),gt(i.datum_type)&&(h=function(t,s,i){if(1===s)return{x:t.x+i[0],y:t.y+i[1],z:t.z+i[2]};if(2===s){var a=i[0],h=i[1],e=i[2],r=i[3],n=i[4],o=i[5],l=i[6];return{x:l*(t.x-o*t.y+n*t.z)+a,y:l*(o*t.x+t.y-r*t.z)+h,z:l*(-n*t.x+r*t.y+t.z)+e}}}(h,i.datum_type,i.datum_params)),gt(a.datum_type)&&(h=function(t,s,i){if(1===s)return{x:t.x-i[0],y:t.y-i[1],z:t.z-i[2]};if(2===s){var a=i[0],h=i[1],e=i[2],r=i[3],n=i[4],o=i[5],l=i[6],c=(t.x-a)/l,u=(t.y-h)/l,_=(t.z-e)/l;return{x:c+o*u-n*_,y:-o*c+u+r*_,z:n*c-r*u+_}}}(h,a.datum_type,a.datum_params)),h=ft(h,l,n,o),3===a.datum_type)&&0!==pt(a,!0,h))return;return h}function pt(t,s,i){if(null===t.grids||0===t.grids.length)return console.log("Grid shift grids not found"),-1;var a={x:-i.x,y:i.y},h={x:Number.NaN,y:Number.NaN},e=[];t:for(var n=0;n<t.grids.length;n++){var o=t.grids[n];if(e.push(o.name),o.isNull){h=a;break}if(null!==o.grid)for(var l=o.grid.subgrids,c=0,u=l.length;c<u;c++){var _=l[c],M=(Math.abs(_.del[1])+Math.abs(_.del[0]))/1e4,d=_.ll[0]-M,f=_.ll[1]-M,g=_.ll[0]+(_.lim[0]-1)*_.del[0]+M,m=_.ll[1]+(_.lim[1]-1)*_.del[1]+M;if(!(f>a.y||d>a.x||m<a.y||g<a.x)&&(h=yt(a,s,_),!isNaN(h.x)))break t}else if(o.mandatory)return console.log("Unable to find mandatory grid '"+o.name+"'"),-1}return isNaN(h.x)?(console.log("Failed to find a grid shift table for location '"+-a.x*r+" "+a.y*r+" tried: '"+e+"'"),-1):(i.x=-h.x,i.y=h.y,0)}function yt(t,s,i){var a={x:Number.NaN,y:Number.NaN};if(isNaN(t.x))return a;var h={x:t.x,y:t.y};h.x-=i.ll[0],h.y-=i.ll[1],h.x=U(h.x-Math.PI)+Math.PI;var e=wt(h,i);if(s){if(isNaN(e.x))return a;e.x=h.x-e.x,e.y=h.y-e.y;var r,n,o=9;do{if(n=wt(e,i),isNaN(n.x)){console.log("Inverse grid shift iteration failed, presumably at grid edge.  Using first approximation.");break}r={x:h.x-(n.x+e.x),y:h.y-(n.y+e.y)},e.x+=r.x,e.y+=r.y}while(o--&&Math.abs(r.x)>1e-12&&Math.abs(r.y)>1e-12);if(o<0)return console.log("Inverse grid shift iterator failed to converge."),a;a.x=U(e.x+i.ll[0]),a.y=e.y+i.ll[1]}else isNaN(e.x)||(a.x=t.x+e.x,a.y=t.y+e.y);return a}function wt(t,s){var i,a={x:t.x/s.del[0],y:t.y/s.del[1]},h=Math.floor(a.x),e=Math.floor(a.y),r=a.x-1*h,n=a.y-1*e,o={x:Number.NaN,y:Number.NaN};if(h<0||h>=s.lim[0])return o;if(e<0||e>=s.lim[1])return o;i=e*s.lim[0]+h;var l=s.cvs[i][0],c=s.cvs[i][1];i++;var u=s.cvs[i][0],_=s.cvs[i][1];i+=s.lim[0];var M=s.cvs[i][0],d=s.cvs[i][1];i--;var f=s.cvs[i][0],g=s.cvs[i][1],m=r*n,p=r*(1-n),y=(1-r)*(1-n),w=(1-r)*n;return o.x=y*l+p*u+w*f+m*M,o.y=y*c+p*_+w*g+m*d,o}function Et(t,s,i){var a,h,e,r=i.x,n=i.y,o=i.z||0,l={};for(e=0;e<3;e++)if(!s||2!==e||void 0!==i.z)switch(0===e?(a=r,h=-1!=="ew".indexOf(t.axis[e])?"x":"y"):1===e?(a=n,h=-1!=="ns".indexOf(t.axis[e])?"y":"x"):(a=o,h="z"),t.axis[e]){case"e":case"n":l[h]=a;break;case"w":case"s":l[h]=-a;break;case"u":void 0!==i[h]&&(l.z=a);break;case"d":void 0!==i[h]&&(l.z=-a);break;default:return null}return l}function vt(t){var s={x:t[0],y:t[1]};return t.length>2&&(s.z=t[2]),t.length>3&&(s.m=t[3]),s}function xt(t){if("function"==typeof Number.isFinite){if(Number.isFinite(t))return;throw new TypeError("coordinates must be finite numbers")}if("number"!=typeof t||t!=t||!isFinite(t))throw new TypeError("coordinates must be finite numbers")}function St(t,s,i,a){var h,n=void 0!==(i=Array.isArray(i)?vt(i):{x:i.x,y:i.y,z:i.z,m:i.m}).z;if(function(t){xt(t.x),xt(t.y)}(i),t.datum&&s.datum&&function(t,s){return(1===t.datum.datum_type||2===t.datum.datum_type||3===t.datum.datum_type)&&"WGS84"!==s.datumCode||(1===s.datum.datum_type||2===s.datum.datum_type||3===s.datum.datum_type)&&"WGS84"!==t.datumCode}(t,s)&&(i=St(t,h=new Mt("WGS84"),i,a),t=h),a&&"enu"!==t.axis&&(i=Et(t,!1,i)),"longlat"===t.projName)i={x:i.x*e,y:i.y*e,z:i.z||0};else if(t.to_meter&&(i={x:i.x*t.to_meter,y:i.y*t.to_meter,z:i.z||0}),!(i=t.inverse(i)))return;if(t.from_greenwich&&(i.x+=t.from_greenwich),i=mt(t.datum,s.datum,i))return s.from_greenwich&&(i={x:i.x-s.from_greenwich,y:i.y,z:i.z||0}),"longlat"===s.projName?i={x:i.x*r,y:i.y*r,z:i.z||0}:(i=s.forward(i),s.to_meter&&(i={x:i.x/s.to_meter,y:i.y/s.to_meter,z:i.z||0})),a&&"enu"!==s.axis?Et(s,!0,i):(i&&!n&&delete i.z,i)}Mt.projections=Y,Mt.projections.start();var Gt=Mt("WGS84");function Pt(t,s,i,a){var h,e,r;return Array.isArray(i)?(h=St(t,s,i,a)||{x:NaN,y:NaN},i.length>2?void 0!==t.name&&"geocent"===t.name||void 0!==s.name&&"geocent"===s.name?"number"==typeof h.z?[h.x,h.y,h.z].concat(i.slice(3)):[h.x,h.y,i[2]].concat(i.slice(3)):[h.x,h.y].concat(i.slice(2)):[h.x,h.y]):(e=St(t,s,i,a),2===(r=Object.keys(i)).length||r.forEach((function(a){if(void 0!==t.name&&"geocent"===t.name||void 0!==s.name&&"geocent"===s.name){if("x"===a||"y"===a||"z"===a)return}else if("x"===a||"y"===a)return;e[a]=i[a]})),e)}function bt(t){return t instanceof Mt?t:"object"==typeof t&&"oProj"in t?t.oProj:Mt(t)}var Nt="AJSAJS",At="AFAFAF",It=65,Ct=73,kt=79,Rt=86,Ot=90,qt={forward:jt,inverse:function(t){var s=Dt(Ut(t.toUpperCase()));if(s.lat&&s.lon)return[s.lon,s.lat,s.lon,s.lat];return[s.left,s.bottom,s.right,s.top]},toPoint:Tt};function jt(t,s){return s=s||5,function(t,s){var i="00000"+t.easting,a="00000"+t.northing;return t.zoneNumber+t.zoneLetter+(M=t.easting,d=t.northing,f=t.zoneNumber,g=zt(f),m=Math.floor(M/1e5),p=Math.floor(d/1e5)%20,h=m,e=p,r=g,n=r-1,o=Nt.charCodeAt(n),l=At.charCodeAt(n),c=o+h-1,u=l+e,_=!1,c>Ot&&(c=c-Ot+It-1,_=!0),(c===Ct||o<Ct&&c>Ct||(c>Ct||o<Ct)&&_)&&c++,(c===kt||o<kt&&c>kt||(c>kt||o<kt)&&_)&&++c===Ct&&c++,c>Ot&&(c=c-Ot+It-1),u>Rt?(u=u-Rt+It-1,_=!0):_=!1,(u===Ct||l<Ct&&u>Ct||(u>Ct||l<Ct)&&_)&&u++,(u===kt||l<kt&&u>kt||(u>kt||l<kt)&&_)&&++u===Ct&&u++,u>Rt&&(u=u-Rt+It-1),String.fromCharCode(c)+String.fromCharCode(u))+i.substr(i.length-5,s)+a.substr(a.length-5,s);var h,e,r,n,o,l,c,u,_;var M,d,f,g,m,p}(function(t){var s,i,a,h,e,r,n,o,l=t.lat,c=t.lon,u=6378137,_=.00669438,M=.9996,d=Lt(l),f=Lt(c);o=Math.floor((c+180)/6)+1,180===c&&(o=60);l>=56&&l<64&&c>=3&&c<12&&(o=32);l>=72&&l<84&&(c>=0&&c<9?o=31:c>=9&&c<21?o=33:c>=21&&c<33?o=35:c>=33&&c<42&&(o=37));n=Lt(6*(o-1)-180+3),s=_/(1-_),i=u/Math.sqrt(1-_*Math.sin(d)*Math.sin(d)),a=Math.tan(d)*Math.tan(d),h=s*Math.cos(d)*Math.cos(d),e=Math.cos(d)*(f-n),r=u*((1-_/4-3*_*_/64-5*_*_*_/256)*d-(3*_/8+3*_*_/32+45*_*_*_/1024)*Math.sin(2*d)+(15*_*_/256+45*_*_*_/1024)*Math.sin(4*d)-35*_*_*_/3072*Math.sin(6*d));var g=M*i*(e+(1-a+h)*e*e*e/6+(5-18*a+a*a+72*h-58*s)*e*e*e*e*e/120)+5e5,m=M*(r+i*Math.tan(d)*(e*e/2+(5-a+9*h+4*h*h)*e*e*e*e/24+(61-58*a+a*a+600*h-330*s)*e*e*e*e*e*e/720));l<0&&(m+=1e7);return{northing:Math.round(m),easting:Math.round(g),zoneNumber:o,zoneLetter:Bt(l)}}({lat:t[1],lon:t[0]}),s)}function Tt(t){var s=Dt(Ut(t.toUpperCase()));return s.lat&&s.lon?[s.lon,s.lat]:[(s.left+s.right)/2,(s.top+s.bottom)/2]}function Lt(t){return t*(Math.PI/180)}function Ft(t){return t/Math.PI*180}function Dt(t){var s=t.northing,i=t.easting,a=t.zoneLetter,h=t.zoneNumber;if(h<0||h>60)return null;var e,r,n,o,l,c,u,_,M,d=.9996,f=6378137,g=.00669438,m=(1-Math.sqrt(.99330562))/(1+Math.sqrt(.99330562)),p=i-5e5,y=s;a<"N"&&(y-=1e7),u=6*(h-1)-180+3,e=.006739496752268451,M=(_=y/d/6367449.145945056)+(3*m/2-27*m*m*m/32)*Math.sin(2*_)+(21*m*m/16-55*m*m*m*m/32)*Math.sin(4*_)+151*m*m*m/96*Math.sin(6*_),r=f/Math.sqrt(1-g*Math.sin(M)*Math.sin(M)),n=Math.tan(M)*Math.tan(M),o=e*Math.cos(M)*Math.cos(M),l=.99330562*f/Math.pow(1-g*Math.sin(M)*Math.sin(M),1.5),c=p/(r*d);var w=M-r*Math.tan(M)/l*(c*c/2-(5+3*n+10*o-4*o*o-9*e)*c*c*c*c/24+(61+90*n+298*o+45*n*n-1.6983531815716497-3*o*o)*c*c*c*c*c*c/720);w=Ft(w);var E,v=(c-(1+2*n+o)*c*c*c/6+(5-2*o+28*n-3*o*o+8*e+24*n*n)*c*c*c*c*c/120)/Math.cos(M);if(v=u+Ft(v),t.accuracy){var x=Dt({northing:t.northing+t.accuracy,easting:t.easting+t.accuracy,zoneLetter:t.zoneLetter,zoneNumber:t.zoneNumber});E={top:x.lat,right:x.lon,bottom:w,left:v}}else E={lat:w,lon:v};return E}function Bt(t){var s="Z";return 84>=t&&t>=72?s="X":72>t&&t>=64?s="W":64>t&&t>=56?s="V":56>t&&t>=48?s="U":48>t&&t>=40?s="T":40>t&&t>=32?s="S":32>t&&t>=24?s="R":24>t&&t>=16?s="Q":16>t&&t>=8?s="P":8>t&&t>=0?s="N":0>t&&t>=-8?s="M":-8>t&&t>=-16?s="L":-16>t&&t>=-24?s="K":-24>t&&t>=-32?s="J":-32>t&&t>=-40?s="H":-40>t&&t>=-48?s="G":-48>t&&t>=-56?s="F":-56>t&&t>=-64?s="E":-64>t&&t>=-72?s="D":-72>t&&t>=-80&&(s="C"),s}function zt(t){var s=t%6;return 0===s&&(s=6),s}function Ut(t){if(t&&0===t.length)throw"MGRSPoint coverting from nothing";for(var s,i=t.length,a=null,h="",e=0;!/[A-Z]/.test(s=t.charAt(e));){if(e>=2)throw"MGRSPoint bad conversion from: "+t;h+=s,e++}var r=parseInt(h,10);if(0===e||e+3>i)throw"MGRSPoint bad conversion from: "+t;var n=t.charAt(e++);if(n<="A"||"B"===n||"Y"===n||n>="Z"||"I"===n||"O"===n)throw"MGRSPoint zone letter "+n+" not handled: "+t;a=t.substring(e,e+=2);for(var o=zt(r),l=function(t,s){var i=Nt.charCodeAt(s-1),a=1e5,h=!1;for(;i!==t.charCodeAt(0);){if(++i===Ct&&i++,i===kt&&i++,i>Ot){if(h)throw"Bad character: "+t;i=It,h=!0}a+=1e5}return a}(a.charAt(0),o),c=function(t,s){if(t>"V")throw"MGRSPoint given invalid Northing "+t;var i=At.charCodeAt(s-1),a=0,h=!1;for(;i!==t.charCodeAt(0);){if(++i===Ct&&i++,i===kt&&i++,i>Rt){if(h)throw"Bad character: "+t;i=It,h=!0}a+=1e5}return a}(a.charAt(1),o);c<Ht(n);)c+=2e6;var u=i-e;if(u%2!=0)throw"MGRSPoint has to have an even number \nof digits after the zone letter and two 100km letters - front \nhalf for easting meters, second half for \nnorthing meters"+t;var _,M,d,f=u/2,g=0,m=0;return f>0&&(_=1e5/Math.pow(10,f),M=t.substring(e,e+f),g=parseFloat(M)*_,d=t.substring(e+f),m=parseFloat(d)*_),{easting:g+l,northing:m+c,zoneLetter:n,zoneNumber:r,accuracy:_}}function Ht(t){var s;switch(t){case"C":s=11e5;break;case"D":s=2e6;break;case"E":s=28e5;break;case"F":s=37e5;break;case"G":s=46e5;break;case"H":s=55e5;break;case"J":s=64e5;break;case"K":s=73e5;break;case"L":s=82e5;break;case"M":s=91e5;break;case"N":s=0;break;case"P":s=8e5;break;case"Q":s=17e5;break;case"R":s=26e5;break;case"S":s=35e5;break;case"T":s=44e5;break;case"U":s=53e5;break;case"V":s=62e5;break;case"W":s=7e6;break;case"X":s=79e5;break;default:s=-1}if(s>=0)return s;throw"Invalid zone letter: "+t}function Wt(t,s,i){if(!(this instanceof Wt))return new Wt(t,s,i);if(Array.isArray(t))this.x=t[0],this.y=t[1],this.z=t[2]||0;else if("object"==typeof t)this.x=t.x,this.y=t.y,this.z=t.z||0;else if("string"==typeof t&&void 0===s){var a=t.split(",");this.x=parseFloat(a[0]),this.y=parseFloat(a[1]),this.z=parseFloat(a[2])||0}else this.x=t,this.y=s,this.z=i||0;console.warn("proj4.Point will be removed in version 3, use proj4.toPoint")}Wt.fromMGRS=function(t){return new Wt(Tt(t))},Wt.prototype.toMGRS=function(t){return jt([this.x,this.y],t)};var Qt=.046875,Xt=.01953125,Jt=.01068115234375;function Vt(t){var s=[];s[0]=1-t*(.25+t*(Qt+t*(Xt+t*Jt))),s[1]=t*(.75-t*(Qt+t*(Xt+t*Jt)));var i=t*t;return s[2]=i*(.46875-t*(.013020833333333334+.007120768229166667*t)),i*=t,s[3]=i*(.3645833333333333-.005696614583333333*t),s[4]=i*t*.3076171875,s}function Kt(t,s,i,a){return i*=s,s*=s,a[0]*t-i*(a[1]+s*(a[2]+s*(a[3]+s*a[4])))}function Zt(t,s,i){for(var a=1/(1-s),e=t,r=20;r;--r){var n=Math.sin(e),o=1-s*n*n;if(e-=o=(Kt(e,n,Math.cos(e),i)-t)*(o*Math.sqrt(o))*a,Math.abs(o)<h)return e}return e}var Yt={init:function(){this.x0=void 0!==this.x0?this.x0:0,this.y0=void 0!==this.y0?this.y0:0,this.long0=void 0!==this.long0?this.long0:0,this.lat0=void 0!==this.lat0?this.lat0:0,this.es&&(this.en=Vt(this.es),this.ml0=Kt(this.lat0,Math.sin(this.lat0),Math.cos(this.lat0),this.en))},forward:function(t){var s,i,a,e=t.x,r=t.y,n=U(e-this.long0),o=Math.sin(r),l=Math.cos(r);if(this.es){var c=l*n,u=Math.pow(c,2),_=this.ep2*Math.pow(l,2),M=Math.pow(_,2),d=Math.abs(l)>h?Math.tan(r):0,f=Math.pow(d,2),g=Math.pow(f,2);s=1-this.es*Math.pow(o,2),c/=Math.sqrt(s);var m=Kt(r,o,l,this.en);i=this.a*(this.k0*c*(1+u/6*(1-f+_+u/20*(5-18*f+g+14*_-58*f*_+u/42*(61+179*g-g*f-479*f)))))+this.x0,a=this.a*(this.k0*(m-this.ml0+o*n*c/2*(1+u/12*(5-f+9*_+4*M+u/30*(61+g-58*f+270*_-330*f*_+u/56*(1385+543*g-g*f-3111*f))))))+this.y0}else{var p=l*Math.sin(n);if(Math.abs(Math.abs(p)-1)<h)return 93;if(i=.5*this.a*this.k0*Math.log((1+p)/(1-p))+this.x0,a=l*Math.cos(n)/Math.sqrt(1-Math.pow(p,2)),(p=Math.abs(a))>=1){if(p-1>h)return 93;a=0}else a=Math.acos(a);r<0&&(a=-a),a=this.a*this.k0*(a-this.lat0)+this.y0}return t.x=i,t.y=a,t},inverse:function(t){var s,i,e,r,n=(t.x-this.x0)*(1/this.a),o=(t.y-this.y0)*(1/this.a);if(this.es)if(i=Zt(s=this.ml0+o/this.k0,this.es,this.en),Math.abs(i)<a){var l=Math.sin(i),c=Math.cos(i),u=Math.abs(c)>h?Math.tan(i):0,_=this.ep2*Math.pow(c,2),M=Math.pow(_,2),d=Math.pow(u,2),f=Math.pow(d,2);s=1-this.es*Math.pow(l,2);var g=n*Math.sqrt(s)/this.k0,m=Math.pow(g,2);e=i-(s*=u)*m/(1-this.es)*.5*(1-m/12*(5+3*d-9*_*d+_-4*M-m/30*(61+90*d-252*_*d+45*f+46*_-m/56*(1385+3633*d+4095*f+1574*f*d)))),r=U(this.long0+g*(1-m/6*(1+2*d+_-m/20*(5+28*d+24*f+8*_*d+6*_-m/42*(61+662*d+1320*f+720*f*d))))/c)}else e=a*z(o),r=0;else{var p=Math.exp(n/this.k0),y=.5*(p-1/p),w=this.lat0+o/this.k0,E=Math.cos(w);s=Math.sqrt((1-Math.pow(E,2))/(1+Math.pow(y,2))),e=Math.asin(s),o<0&&(e=-e),r=0===y&&0===E?0:U(Math.atan2(y,E)+this.long0)}return t.x=r,t.y=e,t},names:["Fast_Transverse_Mercator","Fast Transverse Mercator"]};function $t(t){var s=Math.exp(t);return s=(s-1/s)/2}function ts(t,s){t=Math.abs(t),s=Math.abs(s);var i=Math.max(t,s),a=Math.min(t,s)/(i||1);return i*Math.sqrt(1+Math.pow(a,2))}function ss(t){var s=Math.abs(t);return s=function(t){var s=1+t,i=s-1;return 0===i?t:t*Math.log(s)/i}(s*(1+s/(ts(1,s)+1))),t<0?-s:s}function is(t,s){for(var i,a=2*Math.cos(2*s),h=t.length-1,e=t[h],r=0;--h>=0;)i=a*e-r+t[h],r=e,e=i;return s+i*Math.sin(2*s)}function as(t,s,i){for(var a,h,e=Math.sin(s),r=Math.cos(s),n=$t(i),o=function(t){var s=Math.exp(t);return(s+1/s)/2}(i),l=2*r*o,c=-2*e*n,u=t.length-1,_=t[u],M=0,d=0,f=0;--u>=0;)a=d,h=M,_=l*(d=_)-a-c*(M=f)+t[u],f=c*d-h+l*M;return[(l=e*o)*_-(c=r*n)*f,l*f+c*_]}var hs={init:function(){if(!this.approx&&(isNaN(this.es)||this.es<=0))throw new Error('Incorrect elliptical usage. Try using the +approx option in the proj string, or PROJECTION["Fast_Transverse_Mercator"] in the WKT.');this.approx&&(Yt.init.apply(this),this.forward=Yt.forward,this.inverse=Yt.inverse),this.x0=void 0!==this.x0?this.x0:0,this.y0=void 0!==this.y0?this.y0:0,this.long0=void 0!==this.long0?this.long0:0,this.lat0=void 0!==this.lat0?this.lat0:0,this.cgb=[],this.cbg=[],this.utg=[],this.gtu=[];var t=this.es/(1+Math.sqrt(1-this.es)),s=t/(2-t),i=s;this.cgb[0]=s*(2+s*(-2/3+s*(s*(116/45+s*(26/45+s*(-2854/675)))-2))),this.cbg[0]=s*(s*(2/3+s*(4/3+s*(-82/45+s*(32/45+s*(4642/4725)))))-2),i*=s,this.cgb[1]=i*(7/3+s*(s*(-227/45+s*(2704/315+s*(2323/945)))-1.6)),this.cbg[1]=i*(5/3+s*(-16/15+s*(-13/9+s*(904/315+s*(-1522/945))))),i*=s,this.cgb[2]=i*(56/15+s*(-136/35+s*(-1262/105+s*(73814/2835)))),this.cbg[2]=i*(-26/15+s*(34/21+s*(1.6+s*(-12686/2835)))),i*=s,this.cgb[3]=i*(4279/630+s*(-332/35+s*(-399572/14175))),this.cbg[3]=i*(1237/630+s*(s*(-24832/14175)-2.4)),i*=s,this.cgb[4]=i*(4174/315+s*(-144838/6237)),this.cbg[4]=i*(-734/315+s*(109598/31185)),i*=s,this.cgb[5]=i*(601676/22275),this.cbg[5]=i*(444337/155925),i=Math.pow(s,2),this.Qn=this.k0/(1+s)*(1+i*(1/4+i*(1/64+i/256))),this.utg[0]=s*(s*(2/3+s*(-37/96+s*(1/360+s*(81/512+s*(-96199/604800)))))-.5),this.gtu[0]=s*(.5+s*(-2/3+s*(5/16+s*(41/180+s*(-127/288+s*(7891/37800)))))),this.utg[1]=i*(-1/48+s*(-1/15+s*(437/1440+s*(-46/105+s*(1118711/3870720))))),this.gtu[1]=i*(13/48+s*(s*(557/1440+s*(281/630+s*(-1983433/1935360)))-.6)),i*=s,this.utg[2]=i*(-17/480+s*(37/840+s*(209/4480+s*(-5569/90720)))),this.gtu[2]=i*(61/240+s*(-103/140+s*(15061/26880+s*(167603/181440)))),i*=s,this.utg[3]=i*(-4397/161280+s*(11/504+s*(830251/7257600))),this.gtu[3]=i*(49561/161280+s*(-179/168+s*(6601661/7257600))),i*=s,this.utg[4]=i*(-4583/161280+s*(108847/3991680)),this.gtu[4]=i*(34729/80640+s*(-3418889/1995840)),i*=s,this.utg[5]=i*(-20648693/638668800),this.gtu[5]=.6650675310896665*i;var a=is(this.cbg,this.lat0);this.Zb=-this.Qn*(a+function(t,s){for(var i,a=2*Math.cos(s),h=t.length-1,e=t[h],r=0;--h>=0;)i=a*e-r+t[h],r=e,e=i;return Math.sin(s)*i}(this.gtu,2*a))},forward:function(t){var s=U(t.x-this.long0),i=t.y;i=is(this.cbg,i);var a=Math.sin(i),h=Math.cos(i),e=Math.sin(s),r=Math.cos(s);i=Math.atan2(a,r*h),s=Math.atan2(e*h,ts(a,h*r)),s=ss(Math.tan(s));var n,o,l=as(this.gtu,2*i,2*s);return i+=l[0],s+=l[1],Math.abs(s)<=2.623395162778?(n=this.a*(this.Qn*s)+this.x0,o=this.a*(this.Qn*i+this.Zb)+this.y0):(n=1/0,o=1/0),t.x=n,t.y=o,t},inverse:function(t){var s,i,a=(t.x-this.x0)*(1/this.a),h=(t.y-this.y0)*(1/this.a);if(h=(h-this.Zb)/this.Qn,a/=this.Qn,Math.abs(a)<=2.623395162778){var e=as(this.utg,2*h,2*a);h+=e[0],a+=e[1],a=Math.atan($t(a));var r=Math.sin(h),n=Math.cos(h),o=Math.sin(a),l=Math.cos(a);h=Math.atan2(r*l,ts(o,l*n)),s=U((a=Math.atan2(o,l*n))+this.long0),i=is(this.cgb,h)}else s=1/0,i=1/0;return t.x=s,t.y=i,t},names:["Extended_Transverse_Mercator","Extended Transverse Mercator","etmerc","Transverse_Mercator","Transverse Mercator","Gauss Kruger","Gauss_Kruger","tmerc"]};var es={init:function(){var t=function(t,s){if(void 0===t){if((t=Math.floor(30*(U(s)+Math.PI)/Math.PI)+1)<0)return 0;if(t>60)return 60}return t}(this.zone,this.long0);if(void 0===t)throw new Error("unknown utm zone");this.lat0=0,this.long0=(6*Math.abs(t)-183)*e,this.x0=5e5,this.y0=this.utmSouth?1e7:0,this.k0=.9996,hs.init.apply(this),this.forward=hs.forward,this.inverse=hs.inverse},names:["Universal Transverse Mercator System","utm"],dependsOn:"etmerc"};function rs(t,s){return Math.pow((1-t)/(1+t),s)}var ns={init:function(){var t=Math.sin(this.lat0),s=Math.cos(this.lat0);s*=s,this.rc=Math.sqrt(1-this.es)/(1-this.es*t*t),this.C=Math.sqrt(1+this.es*s*s/(1-this.es)),this.phic0=Math.asin(t/this.C),this.ratexp=.5*this.C*this.e,this.K=Math.tan(.5*this.phic0+n)/(Math.pow(Math.tan(.5*this.lat0+n),this.C)*rs(this.e*t,this.ratexp))},forward:function(t){var s=t.x,i=t.y;return t.y=2*Math.atan(this.K*Math.pow(Math.tan(.5*i+n),this.C)*rs(this.e*Math.sin(i),this.ratexp))-a,t.x=this.C*s,t},inverse:function(t){for(var s=t.x/this.C,i=t.y,h=Math.pow(Math.tan(.5*i+n)/this.K,1/this.C),e=20;e>0&&(i=2*Math.atan(h*rs(this.e*Math.sin(t.y),-.5*this.e))-a,!(Math.abs(i-t.y)<1e-14));--e)t.y=i;return e?(t.x=s,t.y=i,t):null}};var os={init:function(){ns.init.apply(this),this.rc&&(this.sinc0=Math.sin(this.phic0),this.cosc0=Math.cos(this.phic0),this.R2=2*this.rc,this.title||(this.title="Oblique Stereographic Alternative"))},forward:function(t){var s,i,a,h;return t.x=U(t.x-this.long0),ns.forward.apply(this,[t]),s=Math.sin(t.y),i=Math.cos(t.y),a=Math.cos(t.x),h=this.k0*this.R2/(1+this.sinc0*s+this.cosc0*i*a),t.x=h*i*Math.sin(t.x),t.y=h*(this.cosc0*s-this.sinc0*i*a),t.x=this.a*t.x+this.x0,t.y=this.a*t.y+this.y0,t},inverse:function(t){var s,i,a,h,e;if(t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,t.x/=this.k0,t.y/=this.k0,e=ts(t.x,t.y)){var r=2*Math.atan2(e,this.R2);s=Math.sin(r),i=Math.cos(r),h=Math.asin(i*this.sinc0+t.y*s*this.cosc0/e),a=Math.atan2(t.x*s,e*this.cosc0*i-t.y*this.sinc0*s)}else h=this.phic0,a=0;return t.x=a,t.y=h,ns.inverse.apply(this,[t]),t.x=U(t.x+this.long0),t},names:["Stereographic_North_Pole","Oblique_Stereographic","sterea","Oblique Stereographic Alternative","Double_Stereographic"]};function ls(t,s,i){return s*=i,Math.tan(.5*(a+t))*Math.pow((1-s)/(1+s),.5*i)}var cs={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.coslat0=Math.cos(this.lat0),this.sinlat0=Math.sin(this.lat0),this.sphere?1===this.k0&&!isNaN(this.lat_ts)&&Math.abs(this.coslat0)<=h&&(this.k0=.5*(1+z(this.lat0)*Math.sin(this.lat_ts))):(Math.abs(this.coslat0)<=h&&(this.lat0>0?this.con=1:this.con=-1),this.cons=Math.sqrt(Math.pow(1+this.e,1+this.e)*Math.pow(1-this.e,1-this.e)),1===this.k0&&!isNaN(this.lat_ts)&&Math.abs(this.coslat0)<=h&&Math.abs(Math.cos(this.lat_ts))>h&&(this.k0=.5*this.cons*B(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts))/H(this.e,this.con*this.lat_ts,this.con*Math.sin(this.lat_ts))),this.ms1=B(this.e,this.sinlat0,this.coslat0),this.X0=2*Math.atan(ls(this.lat0,this.sinlat0,this.e))-a,this.cosX0=Math.cos(this.X0),this.sinX0=Math.sin(this.X0))},forward:function(t){var s,i,e,r,n,o,l=t.x,c=t.y,u=Math.sin(c),_=Math.cos(c),M=U(l-this.long0);return Math.abs(Math.abs(l-this.long0)-Math.PI)<=h&&Math.abs(c+this.lat0)<=h?(t.x=NaN,t.y=NaN,t):this.sphere?(s=2*this.k0/(1+this.sinlat0*u+this.coslat0*_*Math.cos(M)),t.x=this.a*s*_*Math.sin(M)+this.x0,t.y=this.a*s*(this.coslat0*u-this.sinlat0*_*Math.cos(M))+this.y0,t):(i=2*Math.atan(ls(c,u,this.e))-a,r=Math.cos(i),e=Math.sin(i),Math.abs(this.coslat0)<=h?(n=H(this.e,c*this.con,this.con*u),o=2*this.a*this.k0*n/this.cons,t.x=this.x0+o*Math.sin(l-this.long0),t.y=this.y0-this.con*o*Math.cos(l-this.long0),t):(Math.abs(this.sinlat0)<h?(s=2*this.a*this.k0/(1+r*Math.cos(M)),t.y=s*e):(s=2*this.a*this.k0*this.ms1/(this.cosX0*(1+this.sinX0*e+this.cosX0*r*Math.cos(M))),t.y=s*(this.cosX0*e-this.sinX0*r*Math.cos(M))+this.y0),t.x=s*r*Math.sin(M)+this.x0,t))},inverse:function(t){var s,i,e,r,n;t.x-=this.x0,t.y-=this.y0;var o=Math.sqrt(t.x*t.x+t.y*t.y);if(this.sphere){var l=2*Math.atan(o/(2*this.a*this.k0));return s=this.long0,i=this.lat0,o<=h?(t.x=s,t.y=i,t):(i=Math.asin(Math.cos(l)*this.sinlat0+t.y*Math.sin(l)*this.coslat0/o),s=Math.abs(this.coslat0)<h?this.lat0>0?U(this.long0+Math.atan2(t.x,-1*t.y)):U(this.long0+Math.atan2(t.x,t.y)):U(this.long0+Math.atan2(t.x*Math.sin(l),o*this.coslat0*Math.cos(l)-t.y*this.sinlat0*Math.sin(l))),t.x=s,t.y=i,t)}if(Math.abs(this.coslat0)<=h){if(o<=h)return i=this.lat0,s=this.long0,t.x=s,t.y=i,t;t.x*=this.con,t.y*=this.con,e=o*this.cons/(2*this.a*this.k0),i=this.con*W(this.e,e),s=this.con*U(this.con*this.long0+Math.atan2(t.x,-1*t.y))}else r=2*Math.atan(o*this.cosX0/(2*this.a*this.k0*this.ms1)),s=this.long0,o<=h?n=this.X0:(n=Math.asin(Math.cos(r)*this.sinX0+t.y*Math.sin(r)*this.cosX0/o),s=U(this.long0+Math.atan2(t.x*Math.sin(r),o*this.cosX0*Math.cos(r)-t.y*this.sinX0*Math.sin(r)))),i=-1*W(this.e,Math.tan(.5*(a+n)));return t.x=s,t.y=i,t},names:["stere","Stereographic_South_Pole","Polar_Stereographic_variant_A","Polar_Stereographic_variant_B","Polar_Stereographic"],ssfn_:ls};var us={init:function(){var t=this.lat0;this.lambda0=this.long0;var s=Math.sin(t),i=this.a,a=1/this.rf,h=2*a-Math.pow(a,2),e=this.e=Math.sqrt(h);this.R=this.k0*i*Math.sqrt(1-h)/(1-h*Math.pow(s,2)),this.alpha=Math.sqrt(1+h/(1-h)*Math.pow(Math.cos(t),4)),this.b0=Math.asin(s/this.alpha);var r=Math.log(Math.tan(Math.PI/4+this.b0/2)),n=Math.log(Math.tan(Math.PI/4+t/2)),o=Math.log((1+e*s)/(1-e*s));this.K=r-this.alpha*n+this.alpha*e/2*o},forward:function(t){var s=Math.log(Math.tan(Math.PI/4-t.y/2)),i=this.e/2*Math.log((1+this.e*Math.sin(t.y))/(1-this.e*Math.sin(t.y))),a=-this.alpha*(s+i)+this.K,h=2*(Math.atan(Math.exp(a))-Math.PI/4),e=this.alpha*(t.x-this.lambda0),r=Math.atan(Math.sin(e)/(Math.sin(this.b0)*Math.tan(h)+Math.cos(this.b0)*Math.cos(e))),n=Math.asin(Math.cos(this.b0)*Math.sin(h)-Math.sin(this.b0)*Math.cos(h)*Math.cos(e));return t.y=this.R/2*Math.log((1+Math.sin(n))/(1-Math.sin(n)))+this.y0,t.x=this.R*r+this.x0,t},inverse:function(t){for(var s=t.x-this.x0,i=t.y-this.y0,a=s/this.R,h=2*(Math.atan(Math.exp(i/this.R))-Math.PI/4),e=Math.asin(Math.cos(this.b0)*Math.sin(h)+Math.sin(this.b0)*Math.cos(h)*Math.cos(a)),r=Math.atan(Math.sin(a)/(Math.cos(this.b0)*Math.cos(a)-Math.sin(this.b0)*Math.tan(h))),n=this.lambda0+r/this.alpha,o=0,l=e,c=-1e3,u=0;Math.abs(l-c)>1e-7;){if(++u>20)return;o=1/this.alpha*(Math.log(Math.tan(Math.PI/4+e/2))-this.K)+this.e*Math.log(Math.tan(Math.PI/4+Math.asin(this.e*Math.sin(l))/2)),c=l,l=2*Math.atan(Math.exp(o))-Math.PI/2}return t.x=n,t.y=l,t},names:["somerc"]},_s=1e-7;var Ms={init:function(){var t,s,i,e,r,l,c,u,_,M,d,f,g,m,p=0,y=0,w=0,E=0,v=0,x=0,S=0;this.no_off=(g=["Hotine_Oblique_Mercator","Hotine_Oblique_Mercator_variant_A","Hotine_Oblique_Mercator_Azimuth_Natural_Origin"],m="object"==typeof(f=this).projName?Object.keys(f.projName)[0]:f.projName,"no_uoff"in f||"no_off"in f||-1!==g.indexOf(m)||-1!==g.indexOf(Z(m))),this.no_rot="no_rot"in this;var G=!1;"alpha"in this&&(G=!0);var P=!1;if("rectified_grid_angle"in this&&(P=!0),G&&(S=this.alpha),P&&(p=this.rectified_grid_angle),G||P)y=this.longc;else if(w=this.long1,v=this.lat1,E=this.long2,x=this.lat2,Math.abs(v-x)<=_s||(t=Math.abs(v))<=_s||Math.abs(t-a)<=_s||Math.abs(Math.abs(this.lat0)-a)<=_s||Math.abs(Math.abs(x)-a)<=_s)throw new Error;var b=1-this.es;s=Math.sqrt(b),Math.abs(this.lat0)>h?(u=Math.sin(this.lat0),i=Math.cos(this.lat0),t=1-this.es*u*u,this.B=i*i,this.B=Math.sqrt(1+this.es*this.B*this.B/b),this.A=this.B*this.k0*s/t,(r=(e=this.B*s/(i*Math.sqrt(t)))*e-1)<=0?r=0:(r=Math.sqrt(r),this.lat0<0&&(r=-r)),this.E=r+=e,this.E*=Math.pow(H(this.e,this.lat0,u),this.B)):(this.B=1/s,this.A=this.k0,this.E=e=r=1),G||P?(G?(d=Math.asin(Math.sin(S)/e),P||(p=S)):(d=p,S=Math.asin(e*Math.sin(d))),this.lam0=y-Math.asin(.5*(r-1/r)*Math.tan(d))/this.B):(l=Math.pow(H(this.e,v,Math.sin(v)),this.B),c=Math.pow(H(this.e,x,Math.sin(x)),this.B),r=this.E/l,_=(c-l)/(c+l),M=((M=this.E*this.E)-c*l)/(M+c*l),(t=w-E)<-Math.PI?E-=o:t>Math.PI&&(E+=o),this.lam0=U(.5*(w+E)-Math.atan(M*Math.tan(.5*this.B*(w-E))/_)/this.B),d=Math.atan(2*Math.sin(this.B*U(w-this.lam0))/(r-1/r)),p=S=Math.asin(e*Math.sin(d))),this.singam=Math.sin(d),this.cosgam=Math.cos(d),this.sinrot=Math.sin(p),this.cosrot=Math.cos(p),this.rB=1/this.B,this.ArB=this.A*this.rB,this.BrA=1/this.ArB,this.no_off?this.u_0=0:(this.u_0=Math.abs(this.ArB*Math.atan(Math.sqrt(e*e-1)/Math.cos(S))),this.lat0<0&&(this.u_0=-this.u_0)),r=.5*d,this.v_pole_n=this.ArB*Math.log(Math.tan(n-r)),this.v_pole_s=this.ArB*Math.log(Math.tan(n+r))},forward:function(t){var s,i,e,r,n,o,l,c,u={};if(t.x=t.x-this.lam0,Math.abs(Math.abs(t.y)-a)>h){if(s=.5*((n=this.E/Math.pow(H(this.e,t.y,Math.sin(t.y)),this.B))-(o=1/n)),i=.5*(n+o),r=Math.sin(this.B*t.x),e=(s*this.singam-r*this.cosgam)/i,Math.abs(Math.abs(e)-1)<h)throw new Error;c=.5*this.ArB*Math.log((1-e)/(1+e)),o=Math.cos(this.B*t.x),l=Math.abs(o)<_s?this.A*t.x:this.ArB*Math.atan2(s*this.cosgam+r*this.singam,o)}else c=t.y>0?this.v_pole_n:this.v_pole_s,l=this.ArB*t.y;return this.no_rot?(u.x=l,u.y=c):(l-=this.u_0,u.x=c*this.cosrot+l*this.sinrot,u.y=l*this.cosrot-c*this.sinrot),u.x=this.a*u.x+this.x0,u.y=this.a*u.y+this.y0,u},inverse:function(t){var s,i,e,r,n,o,l,c={};if(t.x=(t.x-this.x0)*(1/this.a),t.y=(t.y-this.y0)*(1/this.a),this.no_rot?(i=t.y,s=t.x):(i=t.x*this.cosrot-t.y*this.sinrot,s=t.y*this.cosrot+t.x*this.sinrot+this.u_0),r=.5*((e=Math.exp(-this.BrA*i))-1/e),n=.5*(e+1/e),l=((o=Math.sin(this.BrA*s))*this.cosgam+r*this.singam)/n,Math.abs(Math.abs(l)-1)<h)c.x=0,c.y=l<0?-a:a;else{if(c.y=this.E/Math.sqrt((1+l)/(1-l)),c.y=W(this.e,Math.pow(c.y,1/this.B)),c.y===1/0)throw new Error;c.x=-this.rB*Math.atan2(r*this.cosgam-o*this.singam,Math.cos(this.BrA*s))}return c.x+=this.lam0,c},names:["Hotine_Oblique_Mercator","Hotine Oblique Mercator","Hotine_Oblique_Mercator_variant_A","Hotine_Oblique_Mercator_Variant_B","Hotine_Oblique_Mercator_Azimuth_Natural_Origin","Hotine_Oblique_Mercator_Two_Point_Natural_Origin","Hotine_Oblique_Mercator_Azimuth_Center","Oblique_Mercator","omerc"]};var ds={init:function(){if(this.lat2||(this.lat2=this.lat1),this.k0||(this.k0=1),this.x0=this.x0||0,this.y0=this.y0||0,!(Math.abs(this.lat1+this.lat2)<h)){var t=this.b/this.a;this.e=Math.sqrt(1-t*t);var s=Math.sin(this.lat1),i=Math.cos(this.lat1),e=B(this.e,s,i),r=H(this.e,this.lat1,s),n=Math.sin(this.lat2),o=Math.cos(this.lat2),l=B(this.e,n,o),c=H(this.e,this.lat2,n),u=Math.abs(Math.abs(this.lat0)-a)<h?0:H(this.e,this.lat0,Math.sin(this.lat0));Math.abs(this.lat1-this.lat2)>h?this.ns=Math.log(e/l)/Math.log(r/c):this.ns=s,isNaN(this.ns)&&(this.ns=s),this.f0=e/(this.ns*Math.pow(r,this.ns)),this.rh=this.a*this.f0*Math.pow(u,this.ns),this.title||(this.title="Lambert Conformal Conic")}},forward:function(t){var s=t.x,i=t.y;Math.abs(2*Math.abs(i)-Math.PI)<=h&&(i=z(i)*(a-2e-10));var e,r,n=Math.abs(Math.abs(i)-a);if(n>h)e=H(this.e,i,Math.sin(i)),r=this.a*this.f0*Math.pow(e,this.ns);else{if((n=i*this.ns)<=0)return null;r=0}var o=this.ns*U(s-this.long0);return t.x=this.k0*(r*Math.sin(o))+this.x0,t.y=this.k0*(this.rh-r*Math.cos(o))+this.y0,t},inverse:function(t){var s,i,h,e,r,n=(t.x-this.x0)/this.k0,o=this.rh-(t.y-this.y0)/this.k0;this.ns>0?(s=Math.sqrt(n*n+o*o),i=1):(s=-Math.sqrt(n*n+o*o),i=-1);var l=0;if(0!==s&&(l=Math.atan2(i*n,i*o)),0!==s||this.ns>0){if(i=1/this.ns,h=Math.pow(s/(this.a*this.f0),i),-9999===(e=W(this.e,h)))return null}else e=-a;return r=U(l/this.ns+this.long0),t.x=r,t.y=e,t},names:["Lambert Tangential Conformal Conic Projection","Lambert_Conformal_Conic","Lambert_Conformal_Conic_1SP","Lambert_Conformal_Conic_2SP","lcc","Lambert Conic Conformal (1SP)","Lambert Conic Conformal (2SP)"]};var fs={init:function(){this.a=6377397.155,this.es=.006674372230614,this.e=Math.sqrt(this.es),this.lat0||(this.lat0=.863937979737193),this.long0||(this.long0=.4334234309119251),this.k0||(this.k0=.9999),this.s45=.785398163397448,this.s90=2*this.s45,this.fi0=this.lat0,this.e2=this.es,this.e=Math.sqrt(this.e2),this.alfa=Math.sqrt(1+this.e2*Math.pow(Math.cos(this.fi0),4)/(1-this.e2)),this.uq=1.04216856380474,this.u0=Math.asin(Math.sin(this.fi0)/this.alfa),this.g=Math.pow((1+this.e*Math.sin(this.fi0))/(1-this.e*Math.sin(this.fi0)),this.alfa*this.e/2),this.k=Math.tan(this.u0/2+this.s45)/Math.pow(Math.tan(this.fi0/2+this.s45),this.alfa)*this.g,this.k1=this.k0,this.n0=this.a*Math.sqrt(1-this.e2)/(1-this.e2*Math.pow(Math.sin(this.fi0),2)),this.s0=1.37008346281555,this.n=Math.sin(this.s0),this.ro0=this.k1*this.n0/Math.tan(this.s0),this.ad=this.s90-this.uq},forward:function(t){var s,i,a,h,e,r,n,o=t.x,l=t.y,c=U(o-this.long0);return s=Math.pow((1+this.e*Math.sin(l))/(1-this.e*Math.sin(l)),this.alfa*this.e/2),i=2*(Math.atan(this.k*Math.pow(Math.tan(l/2+this.s45),this.alfa)/s)-this.s45),a=-c*this.alfa,h=Math.asin(Math.cos(this.ad)*Math.sin(i)+Math.sin(this.ad)*Math.cos(i)*Math.cos(a)),e=Math.asin(Math.cos(i)*Math.sin(a)/Math.cos(h)),r=this.n*e,n=this.ro0*Math.pow(Math.tan(this.s0/2+this.s45),this.n)/Math.pow(Math.tan(h/2+this.s45),this.n),t.y=n*Math.cos(r)/1,t.x=n*Math.sin(r)/1,this.czech||(t.y*=-1,t.x*=-1),t},inverse:function(t){var s,i,a,h,e,r,n,o=t.x;t.x=t.y,t.y=o,this.czech||(t.y*=-1,t.x*=-1),e=Math.sqrt(t.x*t.x+t.y*t.y),h=Math.atan2(t.y,t.x)/Math.sin(this.s0),a=2*(Math.atan(Math.pow(this.ro0/e,1/this.n)*Math.tan(this.s0/2+this.s45))-this.s45),s=Math.asin(Math.cos(this.ad)*Math.sin(a)-Math.sin(this.ad)*Math.cos(a)*Math.cos(h)),i=Math.asin(Math.cos(a)*Math.sin(h)/Math.cos(s)),t.x=this.long0-i/this.alfa,r=s,n=0;var l=0;do{t.y=2*(Math.atan(Math.pow(this.k,-1/this.alfa)*Math.pow(Math.tan(s/2+this.s45),1/this.alfa)*Math.pow((1+this.e*Math.sin(r))/(1-this.e*Math.sin(r)),this.e/2))-this.s45),Math.abs(r-t.y)<1e-10&&(n=1),r=t.y,l+=1}while(0===n&&l<15);return l>=15?null:t},names:["Krovak","krovak"]};function gs(t,s,i,a,h){return t*h-s*Math.sin(2*h)+i*Math.sin(4*h)-a*Math.sin(6*h)}function ms(t){return 1-.25*t*(1+t/16*(3+1.25*t))}function ps(t){return.375*t*(1+.25*t*(1+.46875*t))}function ys(t){return.05859375*t*t*(1+.75*t)}function ws(t){return t*t*t*(35/3072)}function Es(t,s,i){var a=s*i;return t/Math.sqrt(1-a*a)}function vs(t){return Math.abs(t)<a?t:t-z(t)*Math.PI}function xs(t,s,i,a,h){var e,r;e=t/s;for(var n=0;n<15;n++)if(e+=r=(t-(s*e-i*Math.sin(2*e)+a*Math.sin(4*e)-h*Math.sin(6*e)))/(s-2*i*Math.cos(2*e)+4*a*Math.cos(4*e)-6*h*Math.cos(6*e)),Math.abs(r)<=1e-10)return e;return NaN}var Ss={init:function(){this.sphere||(this.e0=ms(this.es),this.e1=ps(this.es),this.e2=ys(this.es),this.e3=ws(this.es),this.ml0=this.a*gs(this.e0,this.e1,this.e2,this.e3,this.lat0))},forward:function(t){var s,i,a=t.x,h=t.y;if(a=U(a-this.long0),this.sphere)s=this.a*Math.asin(Math.cos(h)*Math.sin(a)),i=this.a*(Math.atan2(Math.tan(h),Math.cos(a))-this.lat0);else{var e=Math.sin(h),r=Math.cos(h),n=Es(this.a,this.e,e),o=Math.tan(h)*Math.tan(h),l=a*Math.cos(h),c=l*l,u=this.es*r*r/(1-this.es);s=n*l*(1-c*o*(1/6-(8-o+8*u)*c/120)),i=this.a*gs(this.e0,this.e1,this.e2,this.e3,h)-this.ml0+n*e/r*c*(.5+(5-o+6*u)*c/24)}return t.x=s+this.x0,t.y=i+this.y0,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var s,i,e=t.x/this.a,r=t.y/this.a;if(this.sphere){var n=r+this.lat0;s=Math.asin(Math.sin(n)*Math.cos(e)),i=Math.atan2(Math.tan(e),Math.cos(n))}else{var o=xs(this.ml0/this.a+r,this.e0,this.e1,this.e2,this.e3);if(Math.abs(Math.abs(o)-a)<=h)return t.x=this.long0,t.y=a,r<0&&(t.y*=-1),t;var l=Es(this.a,this.e,Math.sin(o)),c=l*l*l/this.a/this.a*(1-this.es),u=Math.pow(Math.tan(o),2),_=e*this.a/l,M=_*_;s=o-l*Math.tan(o)/c*_*_*(.5-(1+3*u)*_*_/24),i=_*(1-M*(u/3+(1+3*u)*u*M/15))/Math.cos(o)}return t.x=U(i+this.long0),t.y=vs(s),t},names:["Cassini","Cassini_Soldner","cass"]};function Gs(t,s){var i;return t>1e-7?(1-t*t)*(s/(1-(i=t*s)*i)-.5/t*Math.log((1-i)/(1+i))):2*s}var Ps=.3333333333333333,bs=.17222222222222222,Ns=.10257936507936508,As=.06388888888888888,Is=.0664021164021164,Cs=.016415012942191543;var ks={init:function(){var t,s=Math.abs(this.lat0);if(Math.abs(s-a)<h?this.mode=this.lat0<0?1:2:Math.abs(s)<h?this.mode=3:this.mode=4,this.es>0)switch(this.qp=Gs(this.e,1),this.mmf=.5/(1-this.es),this.apa=function(t){var s,i=[];return i[0]=t*Ps,s=t*t,i[0]+=s*bs,i[1]=s*As,s*=t,i[0]+=s*Ns,i[1]+=s*Is,i[2]=s*Cs,i}(this.es),this.mode){case 2:case 1:this.dd=1;break;case 3:this.rq=Math.sqrt(.5*this.qp),this.dd=1/this.rq,this.xmf=1,this.ymf=.5*this.qp;break;case 4:this.rq=Math.sqrt(.5*this.qp),t=Math.sin(this.lat0),this.sinb1=Gs(this.e,t)/this.qp,this.cosb1=Math.sqrt(1-this.sinb1*this.sinb1),this.dd=Math.cos(this.lat0)/(Math.sqrt(1-this.es*t*t)*this.rq*this.cosb1),this.ymf=(this.xmf=this.rq)/this.dd,this.xmf*=this.dd}else 4===this.mode&&(this.sinph0=Math.sin(this.lat0),this.cosph0=Math.cos(this.lat0))},forward:function(t){var s,i,e,r,o,l,c,u,_,M,d=t.x,f=t.y;if(d=U(d-this.long0),this.sphere){if(o=Math.sin(f),M=Math.cos(f),e=Math.cos(d),this.mode===this.OBLIQ||this.mode===this.EQUIT){if((i=this.mode===this.EQUIT?1+M*e:1+this.sinph0*o+this.cosph0*M*e)<=h)return null;s=(i=Math.sqrt(2/i))*M*Math.sin(d),i*=this.mode===this.EQUIT?o:this.cosph0*o-this.sinph0*M*e}else if(this.mode===this.N_POLE||this.mode===this.S_POLE){if(this.mode===this.N_POLE&&(e=-e),Math.abs(f+this.lat0)<h)return null;i=n-.5*f,s=(i=2*(this.mode===this.S_POLE?Math.cos(i):Math.sin(i)))*Math.sin(d),i*=e}}else{switch(c=0,u=0,_=0,e=Math.cos(d),r=Math.sin(d),o=Math.sin(f),l=Gs(this.e,o),this.mode!==this.OBLIQ&&this.mode!==this.EQUIT||(c=l/this.qp,u=Math.sqrt(1-c*c)),this.mode){case this.OBLIQ:_=1+this.sinb1*c+this.cosb1*u*e;break;case this.EQUIT:_=1+u*e;break;case this.N_POLE:_=a+f,l=this.qp-l;break;case this.S_POLE:_=f-a,l=this.qp+l}if(Math.abs(_)<h)return null;switch(this.mode){case this.OBLIQ:case this.EQUIT:_=Math.sqrt(2/_),i=this.mode===this.OBLIQ?this.ymf*_*(this.cosb1*c-this.sinb1*u*e):(_=Math.sqrt(2/(1+u*e)))*c*this.ymf,s=this.xmf*_*u*r;break;case this.N_POLE:case this.S_POLE:l>=0?(s=(_=Math.sqrt(l))*r,i=e*(this.mode===this.S_POLE?_:-_)):s=i=0}}return t.x=this.a*s+this.x0,t.y=this.a*i+this.y0,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var s,i,e,r,n,o,l,c,u,_,M=t.x/this.a,d=t.y/this.a;if(this.sphere){var f,g=0,m=0;if((i=.5*(f=Math.sqrt(M*M+d*d)))>1)return null;switch(i=2*Math.asin(i),this.mode!==this.OBLIQ&&this.mode!==this.EQUIT||(m=Math.sin(i),g=Math.cos(i)),this.mode){case this.EQUIT:i=Math.abs(f)<=h?0:Math.asin(d*m/f),M*=m,d=g*f;break;case this.OBLIQ:i=Math.abs(f)<=h?this.lat0:Math.asin(g*this.sinph0+d*m*this.cosph0/f),M*=m*this.cosph0,d=(g-Math.sin(i)*this.sinph0)*f;break;case this.N_POLE:d=-d,i=a-i;break;case this.S_POLE:i-=a}s=0!==d||this.mode!==this.EQUIT&&this.mode!==this.OBLIQ?Math.atan2(M,d):0}else{if(l=0,this.mode===this.OBLIQ||this.mode===this.EQUIT){if(M/=this.dd,d*=this.dd,(o=Math.sqrt(M*M+d*d))<h)return t.x=this.long0,t.y=this.lat0,t;r=2*Math.asin(.5*o/this.rq),e=Math.cos(r),M*=r=Math.sin(r),this.mode===this.OBLIQ?(l=e*this.sinb1+d*r*this.cosb1/o,n=this.qp*l,d=o*this.cosb1*e-d*this.sinb1*r):(l=d*r/o,n=this.qp*l,d=o*e)}else if(this.mode===this.N_POLE||this.mode===this.S_POLE){if(this.mode===this.N_POLE&&(d=-d),!(n=M*M+d*d))return t.x=this.long0,t.y=this.lat0,t;l=1-n/this.qp,this.mode===this.S_POLE&&(l=-l)}s=Math.atan2(M,d),c=Math.asin(l),u=this.apa,_=c+c,i=c+u[0]*Math.sin(_)+u[1]*Math.sin(_+_)+u[2]*Math.sin(_+_+_)}return t.x=U(this.long0+s),t.y=i,t},names:["Lambert Azimuthal Equal Area","Lambert_Azimuthal_Equal_Area","laea"],S_POLE:1,N_POLE:2,EQUIT:3,OBLIQ:4};function Rs(t){return Math.abs(t)>1&&(t=t>1?1:-1),Math.asin(t)}var Os={init:function(){Math.abs(this.lat1+this.lat2)<h||(this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e3=Math.sqrt(this.es),this.sin_po=Math.sin(this.lat1),this.cos_po=Math.cos(this.lat1),this.t1=this.sin_po,this.con=this.sin_po,this.ms1=B(this.e3,this.sin_po,this.cos_po),this.qs1=Gs(this.e3,this.sin_po),this.sin_po=Math.sin(this.lat2),this.cos_po=Math.cos(this.lat2),this.t2=this.sin_po,this.ms2=B(this.e3,this.sin_po,this.cos_po),this.qs2=Gs(this.e3,this.sin_po),this.sin_po=Math.sin(this.lat0),this.cos_po=Math.cos(this.lat0),this.t3=this.sin_po,this.qs0=Gs(this.e3,this.sin_po),Math.abs(this.lat1-this.lat2)>h?this.ns0=(this.ms1*this.ms1-this.ms2*this.ms2)/(this.qs2-this.qs1):this.ns0=this.con,this.c=this.ms1*this.ms1+this.ns0*this.qs1,this.rh=this.a*Math.sqrt(this.c-this.ns0*this.qs0)/this.ns0)},forward:function(t){var s=t.x,i=t.y;this.sin_phi=Math.sin(i),this.cos_phi=Math.cos(i);var a=Gs(this.e3,this.sin_phi),h=this.a*Math.sqrt(this.c-this.ns0*a)/this.ns0,e=this.ns0*U(s-this.long0),r=h*Math.sin(e)+this.x0,n=this.rh-h*Math.cos(e)+this.y0;return t.x=r,t.y=n,t},inverse:function(t){var s,i,a,h,e,r;return t.x-=this.x0,t.y=this.rh-t.y+this.y0,this.ns0>=0?(s=Math.sqrt(t.x*t.x+t.y*t.y),a=1):(s=-Math.sqrt(t.x*t.x+t.y*t.y),a=-1),h=0,0!==s&&(h=Math.atan2(a*t.x,a*t.y)),a=s*this.ns0/this.a,this.sphere?r=Math.asin((this.c-a*a)/(2*this.ns0)):(i=(this.c-a*a)/this.ns0,r=this.phi1z(this.e3,i)),e=U(h/this.ns0+this.long0),t.x=e,t.y=r,t},names:["Albers_Conic_Equal_Area","Albers_Equal_Area","Albers","aea"],phi1z:function(t,s){var i,a,e,r,n=Rs(.5*s);if(t<h)return n;for(var o=t*t,l=1;l<=25;l++)if(n+=r=.5*(e=1-(a=t*(i=Math.sin(n)))*a)*e/Math.cos(n)*(s/(1-o)-i/e+.5/t*Math.log((1-a)/(1+a))),Math.abs(r)<=1e-7)return n;return null}};var qs={init:function(){this.sin_p14=Math.sin(this.lat0),this.cos_p14=Math.cos(this.lat0),this.infinity_dist=1e3*this.a,this.rc=1},forward:function(t){var s,i,a,e,r,n,o,l=t.x,c=t.y;return a=U(l-this.long0),s=Math.sin(c),i=Math.cos(c),e=Math.cos(a),(r=this.sin_p14*s+this.cos_p14*i*e)>0||Math.abs(r)<=h?(n=this.x0+1*this.a*i*Math.sin(a)/r,o=this.y0+1*this.a*(this.cos_p14*s-this.sin_p14*i*e)/r):(n=this.x0+this.infinity_dist*i*Math.sin(a),o=this.y0+this.infinity_dist*(this.cos_p14*s-this.sin_p14*i*e)),t.x=n,t.y=o,t},inverse:function(t){var s,i,a,h,e,r;return t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,t.x/=this.k0,t.y/=this.k0,(s=Math.sqrt(t.x*t.x+t.y*t.y))?(h=Math.atan2(s,this.rc),i=Math.sin(h),r=Rs((a=Math.cos(h))*this.sin_p14+t.y*i*this.cos_p14/s),e=Math.atan2(t.x*i,s*this.cos_p14*a-t.y*this.sin_p14*i),e=U(this.long0+e)):(r=this.phic0,e=0),t.x=e,t.y=r,t},names:["gnom"]};var js={init:function(){this.sphere||(this.k0=B(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts)))},forward:function(t){var s,i,a=t.x,h=t.y,e=U(a-this.long0);if(this.sphere)s=this.x0+this.a*e*Math.cos(this.lat_ts),i=this.y0+this.a*Math.sin(h)/Math.cos(this.lat_ts);else{var r=Gs(this.e,Math.sin(h));s=this.x0+this.a*this.k0*e,i=this.y0+this.a*r*.5/this.k0}return t.x=s,t.y=i,t},inverse:function(t){var s,i;return t.x-=this.x0,t.y-=this.y0,this.sphere?(s=U(this.long0+t.x/this.a/Math.cos(this.lat_ts)),i=Math.asin(t.y/this.a*Math.cos(this.lat_ts))):(i=function(t,s){var i=1-(1-t*t)/(2*t)*Math.log((1-t)/(1+t));if(Math.abs(Math.abs(s)-i)<1e-6)return s<0?-1*a:a;for(var h,e,r,n,o=Math.asin(.5*s),l=0;l<30;l++)if(e=Math.sin(o),r=Math.cos(o),n=t*e,o+=h=Math.pow(1-n*n,2)/(2*r)*(s/(1-t*t)-e/(1-n*n)+.5/t*Math.log((1-n)/(1+n))),Math.abs(h)<=1e-10)return o;return NaN}(this.e,2*t.y*this.k0/this.a),s=U(this.long0+t.x/(this.a*this.k0))),t.x=s,t.y=i,t},names:["cea"]};var Ts={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.lat_ts=this.lat_ts||0,this.title=this.title||"Equidistant Cylindrical (Plate Carre)",this.rc=Math.cos(this.lat_ts)},forward:function(t){var s=t.x,i=t.y,a=U(s-this.long0),h=vs(i-this.lat0);return t.x=this.x0+this.a*a*this.rc,t.y=this.y0+this.a*h,t},inverse:function(t){var s=t.x,i=t.y;return t.x=U(this.long0+(s-this.x0)/(this.a*this.rc)),t.y=vs(this.lat0+(i-this.y0)/this.a),t},names:["Equirectangular","Equidistant_Cylindrical","Equidistant_Cylindrical_Spherical","eqc"]};var Ls={init:function(){this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e=Math.sqrt(this.es),this.e0=ms(this.es),this.e1=ps(this.es),this.e2=ys(this.es),this.e3=ws(this.es),this.ml0=this.a*gs(this.e0,this.e1,this.e2,this.e3,this.lat0)},forward:function(t){var s,i,a,e=t.x,r=t.y,n=U(e-this.long0);if(a=n*Math.sin(r),this.sphere)Math.abs(r)<=h?(s=this.a*n,i=-1*this.a*this.lat0):(s=this.a*Math.sin(a)/Math.tan(r),i=this.a*(vs(r-this.lat0)+(1-Math.cos(a))/Math.tan(r)));else if(Math.abs(r)<=h)s=this.a*n,i=-1*this.ml0;else{var o=Es(this.a,this.e,Math.sin(r))/Math.tan(r);s=o*Math.sin(a),i=this.a*gs(this.e0,this.e1,this.e2,this.e3,r)-this.ml0+o*(1-Math.cos(a))}return t.x=s+this.x0,t.y=i+this.y0,t},inverse:function(t){var s,i,a,e,r,n,o,l,c;if(a=t.x-this.x0,e=t.y-this.y0,this.sphere)if(Math.abs(e+this.a*this.lat0)<=h)s=U(a/this.a+this.long0),i=0;else{var u;for(n=this.lat0+e/this.a,o=a*a/this.a/this.a+n*n,l=n,r=20;r;--r)if(l+=c=-1*(n*(l*(u=Math.tan(l))+1)-l-.5*(l*l+o)*u)/((l-n)/u-1),Math.abs(c)<=h){i=l;break}s=U(this.long0+Math.asin(a*Math.tan(l)/this.a)/Math.sin(i))}else if(Math.abs(e+this.ml0)<=h)i=0,s=U(this.long0+a/this.a);else{var _,M,d,f,g;for(n=(this.ml0+e)/this.a,o=a*a/this.a/this.a+n*n,l=n,r=20;r;--r)if(g=this.e*Math.sin(l),_=Math.sqrt(1-g*g)*Math.tan(l),M=this.a*gs(this.e0,this.e1,this.e2,this.e3,l),d=this.e0-2*this.e1*Math.cos(2*l)+4*this.e2*Math.cos(4*l)-6*this.e3*Math.cos(6*l),l-=c=(n*(_*(f=M/this.a)+1)-f-.5*_*(f*f+o))/(this.es*Math.sin(2*l)*(f*f+o-2*n*f)/(4*_)+(n-f)*(_*d-2/Math.sin(2*l))-d),Math.abs(c)<=h){i=l;break}_=Math.sqrt(1-this.es*Math.pow(Math.sin(i),2))*Math.tan(i),s=U(this.long0+Math.asin(a*_/this.a)/Math.sin(i))}return t.x=s,t.y=i,t},names:["Polyconic","American_Polyconic","poly"]};var Fs={init:function(){this.A=[],this.A[1]=.6399175073,this.A[2]=-.1358797613,this.A[3]=.063294409,this.A[4]=-.02526853,this.A[5]=.0117879,this.A[6]=-.0055161,this.A[7]=.0026906,this.A[8]=-.001333,this.A[9]=67e-5,this.A[10]=-34e-5,this.B_re=[],this.B_im=[],this.B_re[1]=.7557853228,this.B_im[1]=0,this.B_re[2]=.249204646,this.B_im[2]=.003371507,this.B_re[3]=-.001541739,this.B_im[3]=.04105856,this.B_re[4]=-.10162907,this.B_im[4]=.01727609,this.B_re[5]=-.26623489,this.B_im[5]=-.36249218,this.B_re[6]=-.6870983,this.B_im[6]=-1.1651967,this.C_re=[],this.C_im=[],this.C_re[1]=1.3231270439,this.C_im[1]=0,this.C_re[2]=-.577245789,this.C_im[2]=-.007809598,this.C_re[3]=.508307513,this.C_im[3]=-.112208952,this.C_re[4]=-.15094762,this.C_im[4]=.18200602,this.C_re[5]=1.01418179,this.C_im[5]=1.64497696,this.C_re[6]=1.9660549,this.C_im[6]=2.5127645,this.D=[],this.D[1]=1.5627014243,this.D[2]=.5185406398,this.D[3]=-.03333098,this.D[4]=-.1052906,this.D[5]=-.0368594,this.D[6]=.007317,this.D[7]=.0122,this.D[8]=.00394,this.D[9]=-.0013},forward:function(t){var s,a=t.x,h=t.y-this.lat0,e=a-this.long0,r=h/i*1e-5,n=e,o=1,l=0;for(s=1;s<=10;s++)o*=r,l+=this.A[s]*o;var c,u=l,_=n,M=1,d=0,f=0,g=0;for(s=1;s<=6;s++)c=d*u+M*_,M=M*u-d*_,d=c,f=f+this.B_re[s]*M-this.B_im[s]*d,g=g+this.B_im[s]*M+this.B_re[s]*d;return t.x=g*this.a+this.x0,t.y=f*this.a+this.y0,t},inverse:function(t){var s,a,h=t.x,e=t.y,r=h-this.x0,n=(e-this.y0)/this.a,o=r/this.a,l=1,c=0,u=0,_=0;for(s=1;s<=6;s++)a=c*n+l*o,l=l*n-c*o,c=a,u=u+this.C_re[s]*l-this.C_im[s]*c,_=_+this.C_im[s]*l+this.C_re[s]*c;for(var M=0;M<this.iterations;M++){var d,f=u,g=_,m=n,p=o;for(s=2;s<=6;s++)d=g*u+f*_,f=f*u-g*_,g=d,m+=(s-1)*(this.B_re[s]*f-this.B_im[s]*g),p+=(s-1)*(this.B_im[s]*f+this.B_re[s]*g);f=1,g=0;var y=this.B_re[1],w=this.B_im[1];for(s=2;s<=6;s++)d=g*u+f*_,f=f*u-g*_,g=d,y+=s*(this.B_re[s]*f-this.B_im[s]*g),w+=s*(this.B_im[s]*f+this.B_re[s]*g);var E=y*y+w*w;u=(m*y+p*w)/E,_=(p*y-m*w)/E}var v=u,x=_,S=1,G=0;for(s=1;s<=9;s++)S*=v,G+=this.D[s]*S;var P=this.lat0+G*i*1e5,b=this.long0+x;return t.x=b,t.y=P,t},names:["New_Zealand_Map_Grid","nzmg"]};var Ds={init:function(){},forward:function(t){var s=t.x,i=t.y,a=U(s-this.long0),h=this.x0+this.a*a,e=this.y0+this.a*Math.log(Math.tan(Math.PI/4+i/2.5))*1.25;return t.x=h,t.y=e,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var s=U(this.long0+t.x/this.a),i=2.5*(Math.atan(Math.exp(.8*t.y/this.a))-Math.PI/4);return t.x=s,t.y=i,t},names:["Miller_Cylindrical","mill"]};var Bs={init:function(){this.sphere?(this.n=1,this.m=0,this.es=0,this.C_y=Math.sqrt((this.m+1)/this.n),this.C_x=this.C_y/(this.m+1)):this.en=Vt(this.es)},forward:function(t){var s,i,a=t.x,e=t.y;if(a=U(a-this.long0),this.sphere){if(this.m)for(var r=this.n*Math.sin(e),n=20;n;--n){var o=(this.m*e+Math.sin(e)-r)/(this.m+Math.cos(e));if(e-=o,Math.abs(o)<h)break}else e=1!==this.n?Math.asin(this.n*Math.sin(e)):e;s=this.a*this.C_x*a*(this.m+Math.cos(e)),i=this.a*this.C_y*e}else{var l=Math.sin(e),c=Math.cos(e);i=this.a*Kt(e,l,c,this.en),s=this.a*a*c/Math.sqrt(1-this.es*l*l)}return t.x=s,t.y=i,t},inverse:function(t){var s,i,e;return t.x-=this.x0,i=t.x/this.a,t.y-=this.y0,s=t.y/this.a,this.sphere?(s/=this.C_y,i/=this.C_x*(this.m+Math.cos(s)),this.m?s=Rs((this.m*s+Math.sin(s))/this.n):1!==this.n&&(s=Rs(Math.sin(s)/this.n)),i=U(i+this.long0),s=vs(s)):(s=Zt(t.y/this.a,this.es,this.en),(e=Math.abs(s))<a?(e=Math.sin(s),i=U(this.long0+t.x*Math.sqrt(1-this.es*e*e)/(this.a*Math.cos(s)))):e-h<a&&(i=this.long0)),t.x=i,t.y=s,t},names:["Sinusoidal","sinu"]};var zs={init:function(){},forward:function(t){for(var s=t.x,i=t.y,a=U(s-this.long0),e=i,r=Math.PI*Math.sin(i);;){var n=-(e+Math.sin(e)-r)/(1+Math.cos(e));if(e+=n,Math.abs(n)<h)break}e/=2,Math.PI/2-Math.abs(i)<h&&(a=0);var o=.900316316158*this.a*a*Math.cos(e)+this.x0,l=1.4142135623731*this.a*Math.sin(e)+this.y0;return t.x=o,t.y=l,t},inverse:function(t){var s,i;t.x-=this.x0,t.y-=this.y0,i=t.y/(1.4142135623731*this.a),Math.abs(i)>.999999999999&&(i=.999999999999),s=Math.asin(i);var a=U(this.long0+t.x/(.900316316158*this.a*Math.cos(s)));a<-Math.PI&&(a=-Math.PI),a>Math.PI&&(a=Math.PI),i=(2*s+Math.sin(2*s))/Math.PI,Math.abs(i)>1&&(i=1);var h=Math.asin(i);return t.x=a,t.y=h,t},names:["Mollweide","moll"]};var Us={init:function(){Math.abs(this.lat1+this.lat2)<h||(this.lat2=this.lat2||this.lat1,this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e=Math.sqrt(this.es),this.e0=ms(this.es),this.e1=ps(this.es),this.e2=ys(this.es),this.e3=ws(this.es),this.sin_phi=Math.sin(this.lat1),this.cos_phi=Math.cos(this.lat1),this.ms1=B(this.e,this.sin_phi,this.cos_phi),this.ml1=gs(this.e0,this.e1,this.e2,this.e3,this.lat1),Math.abs(this.lat1-this.lat2)<h?this.ns=this.sin_phi:(this.sin_phi=Math.sin(this.lat2),this.cos_phi=Math.cos(this.lat2),this.ms2=B(this.e,this.sin_phi,this.cos_phi),this.ml2=gs(this.e0,this.e1,this.e2,this.e3,this.lat2),this.ns=(this.ms1-this.ms2)/(this.ml2-this.ml1)),this.g=this.ml1+this.ms1/this.ns,this.ml0=gs(this.e0,this.e1,this.e2,this.e3,this.lat0),this.rh=this.a*(this.g-this.ml0))},forward:function(t){var s,i=t.x,a=t.y;if(this.sphere)s=this.a*(this.g-a);else{var h=gs(this.e0,this.e1,this.e2,this.e3,a);s=this.a*(this.g-h)}var e=this.ns*U(i-this.long0),r=this.x0+s*Math.sin(e),n=this.y0+this.rh-s*Math.cos(e);return t.x=r,t.y=n,t},inverse:function(t){var s,i,a,h;t.x-=this.x0,t.y=this.rh-t.y+this.y0,this.ns>=0?(i=Math.sqrt(t.x*t.x+t.y*t.y),s=1):(i=-Math.sqrt(t.x*t.x+t.y*t.y),s=-1);var e=0;return 0!==i&&(e=Math.atan2(s*t.x,s*t.y)),this.sphere?(h=U(this.long0+e/this.ns),a=vs(this.g-i/this.a),t.x=h,t.y=a,t):(a=xs(this.g-i/this.a,this.e0,this.e1,this.e2,this.e3),h=U(this.long0+e/this.ns),t.x=h,t.y=a,t)},names:["Equidistant_Conic","eqdc"]};var Hs={init:function(){this.R=this.a},forward:function(t){var s,i,e=t.x,r=t.y,n=U(e-this.long0);Math.abs(r)<=h&&(s=this.x0+this.R*n,i=this.y0);var o=Rs(2*Math.abs(r/Math.PI));(Math.abs(n)<=h||Math.abs(Math.abs(r)-a)<=h)&&(s=this.x0,i=r>=0?this.y0+Math.PI*this.R*Math.tan(.5*o):this.y0+Math.PI*this.R*-Math.tan(.5*o));var l=.5*Math.abs(Math.PI/n-n/Math.PI),c=l*l,u=Math.sin(o),_=Math.cos(o),M=_/(u+_-1),d=M*M,f=M*(2/u-1),g=f*f,m=Math.PI*this.R*(l*(M-g)+Math.sqrt(c*(M-g)*(M-g)-(g+c)*(d-g)))/(g+c);n<0&&(m=-m),s=this.x0+m;var p=c+M;return m=Math.PI*this.R*(f*p-l*Math.sqrt((g+c)*(c+1)-p*p))/(g+c),i=r>=0?this.y0+m:this.y0-m,t.x=s,t.y=i,t},inverse:function(t){var s,i,a,e,r,n,o,l,c,u,_,M;return t.x-=this.x0,t.y-=this.y0,_=Math.PI*this.R,r=(a=t.x/_)*a+(e=t.y/_)*e,_=3*(e*e/(l=-2*(n=-Math.abs(e)*(1+r))+1+2*e*e+r*r)+(2*(o=n-2*e*e+a*a)*o*o/l/l/l-9*n*o/l/l)/27)/(c=(n-o*o/3/l)/l)/(u=2*Math.sqrt(-c/3)),Math.abs(_)>1&&(_=_>=0?1:-1),M=Math.acos(_)/3,i=t.y>=0?(-u*Math.cos(M+Math.PI/3)-o/3/l)*Math.PI:-(-u*Math.cos(M+Math.PI/3)-o/3/l)*Math.PI,s=Math.abs(a)<h?this.long0:U(this.long0+Math.PI*(r-1+Math.sqrt(1+2*(a*a-e*e)+r*r))/2/a),t.x=s,t.y=i,t},names:["Van_der_Grinten_I","VanDerGrinten","Van_der_Grinten","vandg"]};var Ws={init:function(){this.sin_p12=Math.sin(this.lat0),this.cos_p12=Math.cos(this.lat0),this.f=this.es/(1+Math.sqrt(1-this.es))},forward:function(t){var s,i,e,r,n,o,l,c,u,_,M,d=t.x,f=t.y,g=Math.sin(t.y),m=Math.cos(t.y),p=U(d-this.long0);return this.sphere?Math.abs(this.sin_p12-1)<=h?(t.x=this.x0+this.a*(a-f)*Math.sin(p),t.y=this.y0-this.a*(a-f)*Math.cos(p),t):Math.abs(this.sin_p12+1)<=h?(t.x=this.x0+this.a*(a+f)*Math.sin(p),t.y=this.y0+this.a*(a+f)*Math.cos(p),t):(u=this.sin_p12*g+this.cos_p12*m*Math.cos(p),c=(l=Math.acos(u))?l/Math.sin(l):1,t.x=this.x0+this.a*c*m*Math.sin(p),t.y=this.y0+this.a*c*(this.cos_p12*g-this.sin_p12*m*Math.cos(p)),t):(s=ms(this.es),i=ps(this.es),e=ys(this.es),r=ws(this.es),Math.abs(this.sin_p12-1)<=h?(n=this.a*gs(s,i,e,r,a),o=this.a*gs(s,i,e,r,f),t.x=this.x0+(n-o)*Math.sin(p),t.y=this.y0-(n-o)*Math.cos(p),t):Math.abs(this.sin_p12+1)<=h?(n=this.a*gs(s,i,e,r,a),o=this.a*gs(s,i,e,r,f),t.x=this.x0+(n+o)*Math.sin(p),t.y=this.y0+(n+o)*Math.cos(p),t):Math.abs(d)<h&&Math.abs(f-this.lat0)<h?(t.x=t.y=0,t):(_=function(t,s,i,a,h,e){const r=a-s,n=Math.atan((1-e)*Math.tan(t)),o=Math.atan((1-e)*Math.tan(i)),l=Math.sin(n),c=Math.cos(n),u=Math.sin(o),_=Math.cos(o);let M,d,f,g,m,p,y,w,E,v,x,S,G,P,b,N=r,A=100;do{if(d=Math.sin(N),f=Math.cos(N),g=Math.sqrt(_*d*(_*d)+(c*u-l*_*f)*(c*u-l*_*f)),0===g)return{azi1:0,s12:0};m=l*u+c*_*f,p=Math.atan2(g,m),y=c*_*d/g,w=1-y*y,E=0!==w?m-2*l*u/w:0,v=e/16*w*(4+e*(4-3*w)),M=N,N=r+(1-v)*e*y*(p+v*g*(E+v*m*(2*E*E-1)))}while(Math.abs(N-M)>1e-12&&--A>0);return 0===A?{azi1:NaN,s12:NaN}:(x=w*(h*h-h*(1-e)*(h*(1-e)))/(h*(1-e)*(h*(1-e))),S=1+x/16384*(4096+x*(x*(320-175*x)-768)),G=x/1024*(256+x*(x*(74-47*x)-128)),P=G*g*(E+G/4*(m*(2*E*E-1)-G/6*E*(4*g*g-3)*(4*E*E-3))),b=h*(1-e)*S*(p-P),{azi1:Math.atan2(_*d,c*u-l*_*f),s12:b})}(this.lat0,this.long0,f,d,this.a,this.f),M=_.azi1,t.x=_.s12*Math.sin(M),t.y=_.s12*Math.cos(M),t))},inverse:function(t){var s,i,e,r,n,o,l,c,u,_,M,d,f,g,m;if(t.x-=this.x0,t.y-=this.y0,this.sphere){if((s=Math.sqrt(t.x*t.x+t.y*t.y))>2*a*this.a)return;return i=s/this.a,e=Math.sin(i),r=Math.cos(i),n=this.long0,Math.abs(s)<=h?o=this.lat0:(o=Rs(r*this.sin_p12+t.y*e*this.cos_p12/s),l=Math.abs(this.lat0)-a,n=Math.abs(l)<=h?this.lat0>=0?U(this.long0+Math.atan2(t.x,-t.y)):U(this.long0-Math.atan2(-t.x,t.y)):U(this.long0+Math.atan2(t.x*e,s*this.cos_p12*r-t.y*this.sin_p12*e))),t.x=n,t.y=o,t}return c=ms(this.es),u=ps(this.es),_=ys(this.es),M=ws(this.es),Math.abs(this.sin_p12-1)<=h?(o=xs(((d=this.a*gs(c,u,_,M,a))-(s=Math.sqrt(t.x*t.x+t.y*t.y)))/this.a,c,u,_,M),n=U(this.long0+Math.atan2(t.x,-1*t.y)),t.x=n,t.y=o,t):Math.abs(this.sin_p12+1)<=h?(d=this.a*gs(c,u,_,M,a),o=xs(((s=Math.sqrt(t.x*t.x+t.y*t.y))-d)/this.a,c,u,_,M),n=U(this.long0+Math.atan2(t.x,t.y)),t.x=n,t.y=o,t):(f=Math.atan2(t.x,t.y),g=Math.sqrt(t.x*t.x+t.y*t.y),m=function(t,s,i,a,h,e){const r=Math.atan((1-e)*Math.tan(t)),n=Math.sin(r),o=Math.cos(r),l=Math.sin(i),c=Math.cos(i),u=Math.atan2(n,o*c),_=o*l,M=1-_*_,d=M*(h*h-h*(1-e)*(h*(1-e)))/(h*(1-e)*(h*(1-e))),f=1+d/16384*(4096+d*(d*(320-175*d)-768)),g=d/1024*(256+d*(d*(74-47*d)-128));let m,p,y,w,E,v=a/(h*(1-e)*f),x=100;do{p=Math.cos(2*u+v),y=Math.sin(v),w=Math.cos(v),E=g*y*(p+g/4*(w*(2*p*p-1)-g/6*p*(4*y*y-3)*(4*p*p-3))),m=v,v=a/(h*(1-e)*f)+E}while(Math.abs(v-m)>1e-12&&--x>0);if(0===x)return{lat2:NaN,lon2:NaN};const S=n*y-o*w*c,G=e/16*M*(4+e*(4-3*M));return{lat2:Math.atan2(n*w+o*y*c,(1-e)*Math.sqrt(_*_+S*S)),lon2:s+(Math.atan2(y*l,o*w-n*y*c)-(1-G)*e*_*(v+G*y*(p+G*w*(2*p*p-1))))}}(this.lat0,this.long0,f,g,this.a,this.f),t.x=m.lon2,t.y=m.lat2,t)},names:["Azimuthal_Equidistant","aeqd"]};var Qs={init:function(){this.sin_p14=Math.sin(this.lat0),this.cos_p14=Math.cos(this.lat0)},forward:function(t){var s,i,a,e,r,n,o,l=t.x,c=t.y;return a=U(l-this.long0),s=Math.sin(c),i=Math.cos(c),e=Math.cos(a),((r=this.sin_p14*s+this.cos_p14*i*e)>0||Math.abs(r)<=h)&&(n=1*this.a*i*Math.sin(a),o=this.y0+1*this.a*(this.cos_p14*s-this.sin_p14*i*e)),t.x=n,t.y=o,t},inverse:function(t){var s,i,e,r,n,o,l;return t.x-=this.x0,t.y-=this.y0,i=Rs((s=Math.sqrt(t.x*t.x+t.y*t.y))/this.a),e=Math.sin(i),r=Math.cos(i),o=this.long0,Math.abs(s)<=h?(l=this.lat0,t.x=o,t.y=l,t):(l=Rs(r*this.sin_p14+t.y*e*this.cos_p14/s),n=Math.abs(this.lat0)-a,Math.abs(n)<=h?(o=this.lat0>=0?U(this.long0+Math.atan2(t.x,-t.y)):U(this.long0-Math.atan2(-t.x,t.y)),t.x=o,t.y=l,t):(o=U(this.long0+Math.atan2(t.x*e,s*this.cos_p14*r-t.y*this.sin_p14*e)),t.x=o,t.y=l,t))},names:["ortho"]},Xs=1,Js=2,Vs=3,Ks=4,Zs=5,Ys=6,$s=1,ti=2,si=3,ii=4;function ai(t,s,i,e){var r;return t<h?(e.value=$s,r=0):(r=Math.atan2(s,i),Math.abs(r)<=n?e.value=$s:r>n&&r<=a+n?(e.value=ti,r-=a):r>a+n||r<=-(a+n)?(e.value=si,r=r>=0?r-l:r+l):(e.value=ii,r+=a)),r}function hi(t,s){var i=t+s;return i<-3.14159265359?i+=o:i>3.14159265359&&(i-=o),i}var ei={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.lat_ts=this.lat_ts||0,this.title=this.title||"Quadrilateralized Spherical Cube",this.lat0>=a-n/2?this.face=Zs:this.lat0<=-(a-n/2)?this.face=Ys:Math.abs(this.long0)<=n?this.face=Xs:Math.abs(this.long0)<=a+n?this.face=this.long0>0?Js:Ks:this.face=Vs,0!==this.es&&(this.one_minus_f=1-(this.a-this.b)/this.a,this.one_minus_f_squared=this.one_minus_f*this.one_minus_f)},forward:function(t){var s,i,h,e,r,o,c={x:0,y:0},u={value:0};if(t.x-=this.long0,s=0!==this.es?Math.atan(this.one_minus_f_squared*Math.tan(t.y)):t.y,i=t.x,this.face===Zs)e=a-s,i>=n&&i<=a+n?(u.value=$s,h=i-a):i>a+n||i<=-(a+n)?(u.value=ti,h=i>0?i-l:i+l):i>-(a+n)&&i<=-n?(u.value=si,h=i+a):(u.value=ii,h=i);else if(this.face===Ys)e=a+s,i>=n&&i<=a+n?(u.value=$s,h=-i+a):i<n&&i>=-n?(u.value=ti,h=-i):i<-n&&i>=-(a+n)?(u.value=si,h=-i-a):(u.value=ii,h=i>0?-i+l:-i-l);else{var _,M,d,f,g,m;this.face===Js?i=hi(i,+a):this.face===Vs?i=hi(i,3.14159265359):this.face===Ks&&(i=hi(i,-a)),f=Math.sin(s),g=Math.cos(s),m=Math.sin(i),_=g*Math.cos(i),M=g*m,d=f,this.face===Xs?h=ai(e=Math.acos(_),d,M,u):this.face===Js?h=ai(e=Math.acos(M),d,-_,u):this.face===Vs?h=ai(e=Math.acos(-_),d,-M,u):this.face===Ks?h=ai(e=Math.acos(-M),d,_,u):(e=h=0,u.value=$s)}return o=Math.atan(12/l*(h+Math.acos(Math.sin(h)*Math.cos(n))-a)),r=Math.sqrt((1-Math.cos(e))/(Math.cos(o)*Math.cos(o))/(1-Math.cos(Math.atan(1/Math.cos(h))))),u.value===ti?o+=a:u.value===si?o+=l:u.value===ii&&(o+=1.5*l),c.x=r*Math.cos(o),c.y=r*Math.sin(o),c.x=c.x*this.a+this.x0,c.y=c.y*this.a+this.y0,t.x=c.x,t.y=c.y,t},inverse:function(t){var s,i,h,e,r,n,o,c,u,_,M,d,f={lam:0,phi:0},g={value:0};if(t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,i=Math.atan(Math.sqrt(t.x*t.x+t.y*t.y)),s=Math.atan2(t.y,t.x),t.x>=0&&t.x>=Math.abs(t.y)?g.value=$s:t.y>=0&&t.y>=Math.abs(t.x)?(g.value=ti,s-=a):t.x<0&&-t.x>=Math.abs(t.y)?(g.value=si,s=s<0?s+l:s-l):(g.value=ii,s+=a),u=l/12*Math.tan(s),r=Math.sin(u)/(Math.cos(u)-1/Math.sqrt(2)),n=Math.atan(r),(o=1-(h=Math.cos(s))*h*(e=Math.tan(i))*e*(1-Math.cos(Math.atan(1/Math.cos(n)))))<-1?o=-1:o>1&&(o=1),this.face===Zs)c=Math.acos(o),f.phi=a-c,g.value===$s?f.lam=n+a:g.value===ti?f.lam=n<0?n+l:n-l:g.value===si?f.lam=n-a:f.lam=n;else if(this.face===Ys)c=Math.acos(o),f.phi=c-a,g.value===$s?f.lam=-n+a:g.value===ti?f.lam=-n:g.value===si?f.lam=-n-a:f.lam=n<0?-n-l:-n+l;else{var m,p,y;u=(m=o)*m,p=(u+=(y=u>=1?0:Math.sqrt(1-u)*Math.sin(n))*y)>=1?0:Math.sqrt(1-u),g.value===ti?(u=p,p=-y,y=u):g.value===si?(p=-p,y=-y):g.value===ii&&(u=p,p=y,y=-u),this.face===Js?(u=m,m=-p,p=u):this.face===Vs?(m=-m,p=-p):this.face===Ks&&(u=m,m=p,p=-u),f.phi=Math.acos(-y)-a,f.lam=Math.atan2(p,m),this.face===Js?f.lam=hi(f.lam,-a):this.face===Vs?f.lam=hi(f.lam,-3.14159265359):this.face===Ks&&(f.lam=hi(f.lam,+a))}return 0!==this.es&&(_=f.phi<0?1:0,M=Math.tan(f.phi),d=this.b/Math.sqrt(M*M+this.one_minus_f_squared),f.phi=Math.atan(Math.sqrt(this.a*this.a-d*d)/(this.one_minus_f*d)),_&&(f.phi=-f.phi)),f.lam+=this.long0,t.x=f.lam,t.y=f.phi,t},names:["Quadrilateralized Spherical Cube","Quadrilateralized_Spherical_Cube","qsc"]},ri=[[1,22199e-21,-715515e-10,31103e-10],[.9986,-482243e-9,-24897e-9,-13309e-10],[.9954,-83103e-8,-448605e-10,-9.86701e-7],[.99,-.00135364,-59661e-9,36777e-10],[.9822,-.00167442,-449547e-11,-572411e-11],[.973,-.00214868,-903571e-10,1.8736e-8],[.96,-.00305085,-900761e-10,164917e-11],[.9427,-.00382792,-653386e-10,-26154e-10],[.9216,-.00467746,-10457e-8,481243e-11],[.8962,-.00536223,-323831e-10,-543432e-11],[.8679,-.00609363,-113898e-9,332484e-11],[.835,-.00698325,-640253e-10,9.34959e-7],[.7986,-.00755338,-500009e-10,9.35324e-7],[.7597,-.00798324,-35971e-9,-227626e-11],[.7186,-.00851367,-701149e-10,-86303e-10],[.6732,-.00986209,-199569e-9,191974e-10],[.6213,-.010418,883923e-10,624051e-11],[.5722,-.00906601,182e-6,624051e-11],[.5322,-.00677797,275608e-9,624051e-11]],ni=[[-520417e-23,.0124,121431e-23,-845284e-16],[.062,.0124,-1.26793e-9,4.22642e-10],[.124,.0124,5.07171e-9,-1.60604e-9],[.186,.0123999,-1.90189e-8,6.00152e-9],[.248,.0124002,7.10039e-8,-2.24e-8],[.31,.0123992,-2.64997e-7,8.35986e-8],[.372,.0124029,9.88983e-7,-3.11994e-7],[.434,.0123893,-369093e-11,-4.35621e-7],[.4958,.0123198,-102252e-10,-3.45523e-7],[.5571,.0121916,-154081e-10,-5.82288e-7],[.6176,.0119938,-241424e-10,-5.25327e-7],[.6769,.011713,-320223e-10,-5.16405e-7],[.7346,.0113541,-397684e-10,-6.09052e-7],[.7903,.0109107,-489042e-10,-104739e-11],[.8435,.0103431,-64615e-9,-1.40374e-9],[.8936,.00969686,-64636e-9,-8547e-9],[.9394,.00840947,-192841e-9,-42106e-10],[.9761,.00616527,-256e-6,-42106e-10],[1,.00328947,-319159e-9,-42106e-10]],oi=.8487,li=1.3523,ci=r/5,ui=1/ci,_i=18,Mi=function(t,s){return t[0]+s*(t[1]+s*(t[2]+s*t[3]))};var di={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.long0=this.long0||0,this.es=0,this.title=this.title||"Robinson"},forward:function(t){var s=U(t.x-this.long0),i=Math.abs(t.y),a=Math.floor(i*ci);a<0?a=0:a>=_i&&(a=17);var h={x:Mi(ri[a],i=r*(i-ui*a))*s,y:Mi(ni[a],i)};return t.y<0&&(h.y=-h.y),h.x=h.x*this.a*oi+this.x0,h.y=h.y*this.a*li+this.y0,h},inverse:function(t){var s={x:(t.x-this.x0)/(this.a*oi),y:Math.abs(t.y-this.y0)/(this.a*li)};if(s.y>=1)s.x/=ri[18][0],s.y=t.y<0?-a:a;else{var i=Math.floor(s.y*_i);for(i<0?i=0:i>=_i&&(i=17);;)if(ni[i][0]>s.y)--i;else{if(!(ni[i+1][0]<=s.y))break;++i}var r=ni[i],n=5*(s.y-r[0])/(ni[i+1][0]-r[0]);n=function(t,s,i,a){for(var h=s;a;--a){var e=t(h);if(h-=e,Math.abs(e)<i)break}return h}((function(t){return(Mi(r,t)-s.y)/function(t,s){return t[1]+s*(2*t[2]+3*s*t[3])}(r,t)}),n,h,100),s.x/=Mi(ri[i],n),s.y=(5*i+n)*e,t.y<0&&(s.y=-s.y)}return s.x=U(s.x+this.long0),s},names:["Robinson","robin"]};var fi={init:function(){this.name="geocent"},forward:function(t){return dt(t,this.es,this.a)},inverse:function(t){return ft(t,this.es,this.a,this.b)},names:["Geocentric","geocentric","geocent","Geocent"]},gi=0,mi=1,pi=2,yi=3,wi={h:{def:1e5,num:!0},azi:{def:0,num:!0,degrees:!0},tilt:{def:0,num:!0,degrees:!0},long0:{def:0,num:!0},lat0:{def:0,num:!0}};var Ei={init:function(){if(Object.keys(wi).forEach(function(t){if(void 0===this[t])this[t]=wi[t].def;else{if(wi[t].num&&isNaN(this[t]))throw new Error("Invalid parameter value, must be numeric "+t+" = "+this[t]);wi[t].num&&(this[t]=parseFloat(this[t]))}wi[t].degrees&&(this[t]=this[t]*e)}.bind(this)),Math.abs(Math.abs(this.lat0)-a)<h?this.mode=this.lat0<0?mi:gi:Math.abs(this.lat0)<h?this.mode=pi:(this.mode=yi,this.sinph0=Math.sin(this.lat0),this.cosph0=Math.cos(this.lat0)),this.pn1=this.h/this.a,this.pn1<=0||this.pn1>1e10)throw new Error("Invalid height");this.p=1+this.pn1,this.rp=1/this.p,this.h1=1/this.pn1,this.pfact=(this.p+1)*this.h1,this.es=0;var t=this.tilt,s=this.azi;this.cg=Math.cos(s),this.sg=Math.sin(s),this.cw=Math.cos(t),this.sw=Math.sin(t)},forward:function(t){t.x-=this.long0;var s,i,a,h,e=Math.sin(t.y),r=Math.cos(t.y),n=Math.cos(t.x);switch(this.mode){case yi:i=this.sinph0*e+this.cosph0*r*n;break;case pi:i=r*n;break;case mi:i=-e;break;case gi:i=e}switch(s=(i=this.pn1/(this.p-i))*r*Math.sin(t.x),this.mode){case yi:i*=this.cosph0*e-this.sinph0*r*n;break;case pi:i*=e;break;case gi:i*=-r*n;break;case mi:i*=r*n}return h=1/((a=i*this.cg+s*this.sg)*this.sw*this.h1+this.cw),s=(s*this.cg-i*this.sg)*this.cw*h,i=a*h,t.x=s*this.a,t.y=i*this.a,t},inverse:function(t){t.x/=this.a,t.y/=this.a;var s,i,a,e={x:t.x,y:t.y};a=1/(this.pn1-t.y*this.sw),s=this.pn1*t.x*a,i=this.pn1*t.y*this.cw*a,t.x=s*this.cg+i*this.sg,t.y=i*this.cg-s*this.sg;var r=ts(t.x,t.y);if(Math.abs(r)<h)e.x=0,e.y=t.y;else{var n,o;switch(o=1-r*r*this.pfact,o=(this.p-Math.sqrt(o))/(this.pn1/r+r/this.pn1),n=Math.sqrt(1-o*o),this.mode){case yi:e.y=Math.asin(n*this.sinph0+t.y*o*this.cosph0/r),t.y=(n-this.sinph0*Math.sin(e.y))*r,t.x*=o*this.cosph0;break;case pi:e.y=Math.asin(t.y*o/r),t.y=n*r,t.x*=o;break;case gi:e.y=Math.asin(n),t.y=-t.y;break;case mi:e.y=-Math.asin(n)}e.x=Math.atan2(t.x,t.y)}return t.x=e.x+this.long0,t.y=e.y,t},names:["Tilted_Perspective","tpers"]};var vi={init:function(){if(this.flip_axis="x"===this.sweep?1:0,this.h=Number(this.h),this.radius_g_1=this.h/this.a,this.radius_g_1<=0||this.radius_g_1>1e10)throw new Error;if(this.radius_g=1+this.radius_g_1,this.C=this.radius_g*this.radius_g-1,0!==this.es){var t=1-this.es,s=1/t;this.radius_p=Math.sqrt(t),this.radius_p2=t,this.radius_p_inv2=s,this.shape="ellipse"}else this.radius_p=1,this.radius_p2=1,this.radius_p_inv2=1,this.shape="sphere";this.title||(this.title="Geostationary Satellite View")},forward:function(t){var s,i,a,h,e=t.x,r=t.y;if(e-=this.long0,"ellipse"===this.shape){r=Math.atan(this.radius_p2*Math.tan(r));var n=this.radius_p/ts(this.radius_p*Math.cos(r),Math.sin(r));if(i=n*Math.cos(e)*Math.cos(r),a=n*Math.sin(e)*Math.cos(r),h=n*Math.sin(r),(this.radius_g-i)*i-a*a-h*h*this.radius_p_inv2<0)return t.x=Number.NaN,t.y=Number.NaN,t;s=this.radius_g-i,this.flip_axis?(t.x=this.radius_g_1*Math.atan(a/ts(h,s)),t.y=this.radius_g_1*Math.atan(h/s)):(t.x=this.radius_g_1*Math.atan(a/s),t.y=this.radius_g_1*Math.atan(h/ts(a,s)))}else"sphere"===this.shape&&(s=Math.cos(r),i=Math.cos(e)*s,a=Math.sin(e)*s,h=Math.sin(r),s=this.radius_g-i,this.flip_axis?(t.x=this.radius_g_1*Math.atan(a/ts(h,s)),t.y=this.radius_g_1*Math.atan(h/s)):(t.x=this.radius_g_1*Math.atan(a/s),t.y=this.radius_g_1*Math.atan(h/ts(a,s))));return t.x=t.x*this.a,t.y=t.y*this.a,t},inverse:function(t){var s,i,a,h,e=-1,r=0,n=0;if(t.x=t.x/this.a,t.y=t.y/this.a,"ellipse"===this.shape){this.flip_axis?(n=Math.tan(t.y/this.radius_g_1),r=Math.tan(t.x/this.radius_g_1)*ts(1,n)):(r=Math.tan(t.x/this.radius_g_1),n=Math.tan(t.y/this.radius_g_1)*ts(1,r));var o=n/this.radius_p;if(s=r*r+o*o+e*e,(a=(i=2*this.radius_g*e)*i-4*s*this.C)<0)return t.x=Number.NaN,t.y=Number.NaN,t;h=(-i-Math.sqrt(a))/(2*s),e=this.radius_g+h*e,r*=h,n*=h,t.x=Math.atan2(r,e),t.y=Math.atan(n*Math.cos(t.x)/e),t.y=Math.atan(this.radius_p_inv2*Math.tan(t.y))}else if("sphere"===this.shape){if(this.flip_axis?(n=Math.tan(t.y/this.radius_g_1),r=Math.tan(t.x/this.radius_g_1)*Math.sqrt(1+n*n)):(r=Math.tan(t.x/this.radius_g_1),n=Math.tan(t.y/this.radius_g_1)*Math.sqrt(1+r*r)),s=r*r+n*n+e*e,(a=(i=2*this.radius_g*e)*i-4*s*this.C)<0)return t.x=Number.NaN,t.y=Number.NaN,t;h=(-i-Math.sqrt(a))/(2*s),e=this.radius_g+h*e,r*=h,n*=h,t.x=Math.atan2(r,e),t.y=Math.atan(n*Math.cos(t.x)/e)}return t.x=t.x+this.long0,t},names:["Geostationary Satellite View","Geostationary_Satellite","geos"]},xi=1.340264,Si=-.081106,Gi=893e-6,Pi=.003796,bi=Math.sqrt(3)/2;var Ni={init:function(){this.es=0,this.long0=void 0!==this.long0?this.long0:0},forward:function(t){var s=U(t.x-this.long0),i=t.y,a=Math.asin(bi*Math.sin(i)),h=a*a,e=h*h*h;return t.x=s*Math.cos(a)/(bi*(xi+3*Si*h+e*(7*Gi+9*Pi*h))),t.y=a*(xi+Si*h+e*(Gi+Pi*h)),t.x=this.a*t.x+this.x0,t.y=this.a*t.y+this.y0,t},inverse:function(t){t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a;var s,i,a,h,e=t.y;for(h=0;h<12&&(e-=a=(e*(xi+Si*(s=e*e)+(i=s*s*s)*(Gi+Pi*s))-t.y)/(xi+3*Si*s+i*(7*Gi+9*Pi*s)),!(Math.abs(a)<1e-9));++h);return i=(s=e*e)*s*s,t.x=bi*t.x*(xi+3*Si*s+i*(7*Gi+9*Pi*s))/Math.cos(e),t.y=Math.asin(Math.sin(e)/bi),t.x=U(t.x+this.long0),t},names:["eqearth","Equal Earth","Equal_Earth"]},Ai=1e-10;function Ii(t){var s,i,a,h=U(t.x-(this.long0||0)),e=t.y;return s=this.am1+this.m1-Kt(e,i=Math.sin(e),a=Math.cos(e),this.en),i=a*h/(s*Math.sqrt(1-this.es*i*i)),t.x=s*Math.sin(i),t.y=this.am1-s*Math.cos(i),t.x=this.a*t.x+(this.x0||0),t.y=this.a*t.y+(this.y0||0),t}function Ci(t){var s,i,h,e;if(t.x=(t.x-(this.x0||0))/this.a,t.y=(t.y-(this.y0||0))/this.a,i=ts(t.x,t.y=this.am1-t.y),e=Zt(this.am1+this.m1-i,this.es,this.en),(s=Math.abs(e))<a)s=Math.sin(e),h=i*Math.atan2(t.x,t.y)*Math.sqrt(1-this.es*s*s)/Math.cos(e);else{if(!(Math.abs(s-a)<=Ai))throw new Error;h=0}return t.x=U(h+(this.long0||0)),t.y=vs(e),t}function ki(t){var s,i,a=U(t.x-(this.long0||0)),h=t.y;return i=this.cphi1+this.phi1-h,Math.abs(i)>Ai?(t.x=i*Math.sin(s=a*Math.cos(h)/i),t.y=this.cphi1-i*Math.cos(s)):t.x=t.y=0,t.x=this.a*t.x+(this.x0||0),t.y=this.a*t.y+(this.y0||0),t}function Ri(t){var s,i;t.x=(t.x-(this.x0||0))/this.a,t.y=(t.y-(this.y0||0))/this.a;var h=ts(t.x,t.y=this.cphi1-t.y);if(i=this.cphi1+this.phi1-h,Math.abs(i)>a)throw new Error;return s=Math.abs(Math.abs(i)-a)<=Ai?0:h*Math.atan2(t.x,t.y)/Math.cos(i),t.x=U(s+(this.long0||0)),t.y=vs(i),t}var Oi={init:function(){var t;if(this.phi1=this.lat1,Math.abs(this.phi1)<Ai)throw new Error;this.es?(this.en=Vt(this.es),this.m1=Kt(this.phi1,this.am1=Math.sin(this.phi1),t=Math.cos(this.phi1),this.en),this.am1=t/(Math.sqrt(1-this.es*this.am1*this.am1)*this.am1),this.inverse=Ci,this.forward=Ii):(Math.abs(this.phi1)+Ai>=a?this.cphi1=0:this.cphi1=1/Math.tan(this.phi1),this.inverse=Ri,this.forward=ki)},names:["bonne","Bonne (Werner lat_1=90)"]};const qi=Object.assign((function(t,s,i){var a,h,e,r=!1;return void 0===s?(h=bt(t),a=Gt,r=!0):(void 0!==s.x||Array.isArray(s))&&(i=s,h=bt(t),a=Gt,r=!0),a||(a=bt(t)),h||(h=bt(s)),i?Pt(a,h,i):(e={forward:function(t,s){return Pt(a,h,t,s)},inverse:function(t,s){return Pt(h,a,t,s)}},r&&(e.oProj=h),e)}),{defaultDatum:"WGS84",Proj:Mt,WGS84:new Mt("WGS84"),Point:Wt,toPoint:vt,defs:T,nadgrid:function(t,s,i){return s instanceof ArrayBuffer?function(t,s,i){var a=!0;void 0!==i&&!1===i.includeErrorFields&&(a=!1);var h=new DataView(s),e=function(t){var s=t.getInt32(8,!1);if(11===s)return!1;s=t.getInt32(8,!0),11!==s&&console.warn("Failed to detect nadgrid endian-ness, defaulting to little-endian");return!0}(h),r=function(t,s){return{nFields:t.getInt32(8,s),nSubgridFields:t.getInt32(24,s),nSubgrids:t.getInt32(40,s),shiftType:lt(t,56,64).trim(),fromSemiMajorAxis:t.getFloat64(120,s),fromSemiMinorAxis:t.getFloat64(136,s),toSemiMajorAxis:t.getFloat64(152,s),toSemiMinorAxis:t.getFloat64(168,s)}}(h,e),n=function(t,s,i,a){for(var h=176,e=[],r=0;r<s.nSubgrids;r++){var n=ut(t,h,i),o=_t(t,h,n,i,a),l=Math.round(1+(n.upperLongitude-n.lowerLongitude)/n.longitudeInterval),c=Math.round(1+(n.upperLatitude-n.lowerLatitude)/n.latitudeInterval);e.push({ll:[ot(n.lowerLongitude),ot(n.lowerLatitude)],del:[ot(n.longitudeInterval),ot(n.latitudeInterval)],lim:[l,c],count:n.gridNodeCount,cvs:ct(o)});var u=16;!1===a&&(u=8),h+=176+n.gridNodeCount*u}return e}(h,r,e,a),o={header:r,subgrids:n};return ht[t]=o,o}(t,s,i):{ready:et(t,s)}},transform:St,mgrs:qt,version:"2.19.10"});return function(t){t.Proj.projections.add(Yt),t.Proj.projections.add(hs),t.Proj.projections.add(es),t.Proj.projections.add(os),t.Proj.projections.add(cs),t.Proj.projections.add(us),t.Proj.projections.add(Ms),t.Proj.projections.add(ds),t.Proj.projections.add(fs),t.Proj.projections.add(Ss),t.Proj.projections.add(ks),t.Proj.projections.add(Os),t.Proj.projections.add(qs),t.Proj.projections.add(js),t.Proj.projections.add(Ts),t.Proj.projections.add(Ls),t.Proj.projections.add(Fs),t.Proj.projections.add(Ds),t.Proj.projections.add(Bs),t.Proj.projections.add(zs),t.Proj.projections.add(Us),t.Proj.projections.add(Hs),t.Proj.projections.add(Ws),t.Proj.projections.add(Qs),t.Proj.projections.add(ei),t.Proj.projections.add(di),t.Proj.projections.add(fi),t.Proj.projections.add(Ei),t.Proj.projections.add(vi),t.Proj.projections.add(Ni),t.Proj.projections.add(Oi)}(qi),qi}));
