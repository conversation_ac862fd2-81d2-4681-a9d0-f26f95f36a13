/*
 * @Description: 地图URL解析工具
 */
import * as Cesium from 'cesium';

/**
 * WFS参数接口定义
 */
export interface WfsParams {
  service: string;
  version: string;
  request: string;
  typeName: string;
  outputFormat: string;
  CQL_FILTER?: string;
  [key: string]: any;
}

/**
 * URL解析工具类
 */
export class UrlParser {
  /**
   * 解析OpenLayers3格式的WMS URL，提取出WMS服务地址和参数
   * @param url OpenLayers3格式的WMS URL
   * @returns 解析后的WMS信息 {baseUrl, parameters}
   */
  static parseOLWmsUrl(url: string): {
    baseUrl: string;
    parameters: {
      layers: string;
      bbox?: string;
      width?: number;
      height?: number;
      srs?: string;
      format?: string;
      service: string;
      version: string;
      request: string;
      [key: string]: any;
    };
  } {
    try {
      const urlObj = new URL(url);
      const baseUrl = `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;
      const parameters: any = {};
      
      // 解析URL参数
      for (const [key, value] of urlObj.searchParams.entries()) {
        // 处理数字类型的参数
        if (['width', 'height'].includes(key)) {
          parameters[key] = parseInt(value, 10);
        } else {
          parameters[key] = value;
        }
      }
      
      // 确保必要的WMS参数存在
      if (!parameters.service) parameters.service = 'WMS';
      if (!parameters.version) parameters.version = '1.1.0';
      if (!parameters.request) parameters.request = 'GetMap';
      
      return {
        baseUrl,
        parameters,
      };
    } catch (error) {
      console.error('解析WMS URL失败:', error);
      // 返回默认值，以防解析失败
      return {
        baseUrl: url,
        parameters: {
          layers: '',
          service: 'WMS',
          version: '1.1.0',
          request: 'GetMap',
        },
      };
    }
  }

  /**
   * 解析WMTS URL，提取GeoServer WMTS的相关参数
   * @param url GeoServer WMTS demo URL
   * @returns WMTS参数和URL模板
   */
  static parseWMTSUrl(url: string): {
    serviceUrl: string;
    layer: string;
    style: string;
    tileMatrixSetID: string;
    format: string;
  } {
    try {
      console.log('解析WMTS URL:', url);
      
      // 解析URL以获取基本信息
      const urlObj = new URL(url);
      const baseUrl = `${urlObj.protocol}//${urlObj.host}`;
      
      // 从路径中提取图层名
      const pathParts = urlObj.pathname.split('/');
      let layer = '';
      
      // 查找demo和图层名称的位置
      const demoIndex = pathParts.indexOf('demo');
      if (demoIndex !== -1 && demoIndex + 1 < pathParts.length) {
        // 图层名在demo后面
        layer = pathParts[demoIndex + 1].split('?')[0];
      } else {
        // 尝试从URL中提取图层名
        const lastPart = pathParts[pathParts.length - 1].split('?')[0];
        if (lastPart.includes(':')) {
          layer = lastPart;
        }
      }
      
      // 处理图层名格式 (如果包含命名空间)
      if (!layer) {
        console.error('无法从URL中提取图层名');
        layer = 'unknown';
      }
      
      // 获取tileMatrixSetID和format
      const gridSet = urlObj.searchParams.get('gridSet') || 'EPSG:4326';
      const format = urlObj.searchParams.get('format') || 'image/png';
      
      // 构建WMTS REST URL模板
      const serviceUrl = `${baseUrl}/geoserver/gwc/service/wmts/rest/${layer}/{Style}/${gridSet}/${gridSet}:{TileMatrix}/{TileRow}/{TileCol}?format=${format}`;
      
      return {
        serviceUrl,
        layer,
        style: '',  // GeoServer默认样式通常为空字符串
        tileMatrixSetID: gridSet,
        format,
      };
    } catch (error) {
      console.error('解析WMTS URL失败:', error);
      // 返回默认值，以防解析失败
      return {
        serviceUrl: url,
        layer: 'unknown',
        style: '',
        tileMatrixSetID: 'EPSG:4326',
        format: 'image/png',
      };
    }
  }

  /**
   * 构建WFS URL
   * @param baseUrl 基础URL
   * @param params WFS参数
   * @returns 完整的WFS URL
   */
  static buildWfsUrl(baseUrl: string, params: WfsParams): string {
    const url = new URL(baseUrl);
    
    // 添加WFS参数
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        url.searchParams.set(key, value.toString());
      }
    });
    
    return url.toString();
  }
} 