import {
  IIIFInfo_default
} from "./chunk-37SEWD33.js";
import {
  GeoJSON_default,
  JSONFeature_default
} from "./chunk-MCUHCJHG.js";
import {
  MVT_default
} from "./chunk-T3HD2SPM.js";
import {
  Feature_default as Feature_default2,
  transformExtentWithOptions,
  transformGeometryWithOptions
} from "./chunk-MQEFFQYJ.js";
import "./chunk-B6KHFG6K.js";
import "./chunk-KDZELOKE.js";
import {
  GeometryCollection_default,
  LineString_default,
  MultiLineString_default,
  MultiPoint_default,
  MultiPolygon_default
} from "./chunk-WDCIHQAY.js";
import "./chunk-JFXZSSOM.js";
import {
  Feature_default
} from "./chunk-NM3FJR57.js";
import {
  Geometry_default,
  LinearRing_default,
  Point_default,
  Polygon_default,
  SimpleGeometry_default,
  deflateCoordinates,
  getStrideForLayout,
  inflateCoordinates,
  linearRingIsClockwise
} from "./chunk-3N43N7BS.js";
import "./chunk-YJNARELB.js";
import {
  get,
  padNumber
} from "./chunk-VO4OC6Q7.js";
import {
  Fill_default,
  Icon_default,
  Stroke_default,
  Style_default,
  Text_default
} from "./chunk-N7A7KCCM.js";
import "./chunk-JNGMLQYZ.js";
import "./chunk-QD3OARAC.js";
import {
  asArray
} from "./chunk-VZFLE47V.js";
import {
  ImageState_default
} from "./chunk-NFZ3N3GI.js";
import "./chunk-HH6RWFQF.js";
import "./chunk-6YBVBLXZ.js";
import {
  abstract
} from "./chunk-OT3HC6IH.js";
import "./chunk-N5K5EBVX.js";
import "./chunk-J4FNYJJI.js";
import {
  extend
} from "./chunk-NCSZTHHM.js";
import {
  isEmpty
} from "./chunk-X7AUGEB2.js";
import {
  assert,
  boundingExtent,
  containsExtent,
  createOrUpdate
} from "./chunk-IGSFLGV4.js";
import "./chunk-Q6I4HU3X.js";
import {
  toRadians
} from "./chunk-KPFVJIQJ.js";
import "./chunk-PLDDJCW6.js";

// node_modules/ol/format/EsriJSON.js
var GEOMETRY_READERS = {
  Point: readPointGeometry,
  LineString: readLineStringGeometry,
  Polygon: readPolygonGeometry,
  MultiPoint: readMultiPointGeometry,
  MultiLineString: readMultiLineStringGeometry,
  MultiPolygon: readMultiPolygonGeometry
};
var GEOMETRY_WRITERS = {
  Point: writePointGeometry,
  LineString: writeLineStringGeometry,
  Polygon: writePolygonGeometry,
  MultiPoint: writeMultiPointGeometry,
  MultiLineString: writeMultiLineStringGeometry,
  MultiPolygon: writeMultiPolygonGeometry
};
var EsriJSON = class extends JSONFeature_default {
  /**
   * @param {Options} [options] Options.
   */
  constructor(options) {
    options = options ? options : {};
    super();
    this.geometryName_ = options.geometryName;
  }
  /**
   * @param {Object} object Object.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @param {string} [idField] Name of the field where to get the id from.
   * @protected
   * @return {import("../Feature.js").default} Feature.
   */
  readFeatureFromObject(object, options, idField) {
    const esriJSONFeature = (
      /** @type {EsriJSONFeature} */
      object
    );
    const geometry = readGeometry(esriJSONFeature.geometry, options);
    const feature = new Feature_default();
    if (this.geometryName_) {
      feature.setGeometryName(this.geometryName_);
    }
    feature.setGeometry(geometry);
    if (esriJSONFeature.attributes) {
      feature.setProperties(esriJSONFeature.attributes, true);
      const id = esriJSONFeature.attributes[idField];
      if (id !== void 0) {
        feature.setId(
          /** @type {number} */
          id
        );
      }
    }
    return feature;
  }
  /**
   * @param {Object} object Object.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {Array<Feature>} Features.
   */
  readFeaturesFromObject(object, options) {
    options = options ? options : {};
    if (object["features"]) {
      const esriJSONFeatureSet = (
        /** @type {EsriJSONFeatureSet} */
        object
      );
      const features = [];
      const esriJSONFeatures = esriJSONFeatureSet.features;
      for (let i = 0, ii = esriJSONFeatures.length; i < ii; ++i) {
        features.push(
          this.readFeatureFromObject(
            esriJSONFeatures[i],
            options,
            object.objectIdFieldName
          )
        );
      }
      return features;
    }
    return [this.readFeatureFromObject(object, options)];
  }
  /**
   * @param {EsriJSONGeometry} object Object.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {import("../geom/Geometry.js").default} Geometry.
   */
  readGeometryFromObject(object, options) {
    return readGeometry(object, options);
  }
  /**
   * @param {Object} object Object.
   * @protected
   * @return {import("../proj/Projection.js").default} Projection.
   */
  readProjectionFromObject(object) {
    if (object["spatialReference"] && object["spatialReference"]["wkid"] !== void 0) {
      const spatialReference = (
        /** @type {EsriJSONSpatialReferenceWkid} */
        object["spatialReference"]
      );
      const crs = spatialReference.wkid;
      return get("EPSG:" + crs);
    }
    return null;
  }
  /**
   * Encode a geometry as a EsriJSON object.
   *
   * @param {import("../geom/Geometry.js").default} geometry Geometry.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {EsriJSONGeometry} Object.
   * @api
   */
  writeGeometryObject(geometry, options) {
    return writeGeometry(geometry, this.adaptOptions(options));
  }
  /**
   * Encode a feature as a esriJSON Feature object.
   *
   * @param {import("../Feature.js").default} feature Feature.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {Object} Object.
   * @api
   */
  writeFeatureObject(feature, options) {
    options = this.adaptOptions(options);
    const object = {};
    if (!feature.hasProperties()) {
      object["attributes"] = {};
      return object;
    }
    const properties = feature.getProperties();
    const geometry = feature.getGeometry();
    if (geometry) {
      object["geometry"] = writeGeometry(geometry, options);
      const projection = options && (options.dataProjection || options.featureProjection);
      if (projection) {
        object["geometry"]["spatialReference"] = /** @type {EsriJSONSpatialReferenceWkid} */
        {
          wkid: Number(get(projection).getCode().split(":").pop())
        };
      }
      delete properties[feature.getGeometryName()];
    }
    if (!isEmpty(properties)) {
      object["attributes"] = properties;
    } else {
      object["attributes"] = {};
    }
    return object;
  }
  /**
   * Encode an array of features as a EsriJSON object.
   *
   * @param {Array<import("../Feature.js").default>} features Features.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {EsriJSONFeatureSet} EsriJSON Object.
   * @api
   */
  writeFeaturesObject(features, options) {
    options = this.adaptOptions(options);
    const objects = [];
    for (let i = 0, ii = features.length; i < ii; ++i) {
      objects.push(this.writeFeatureObject(features[i], options));
    }
    return {
      "features": objects
    };
  }
};
function readGeometry(object, options) {
  if (!object) {
    return null;
  }
  let type;
  if (typeof object["x"] === "number" && typeof object["y"] === "number") {
    type = "Point";
  } else if (object["points"]) {
    type = "MultiPoint";
  } else if (object["paths"]) {
    const esriJSONPolyline = (
      /** @type {EsriJSONPolyline} */
      object
    );
    if (esriJSONPolyline.paths.length === 1) {
      type = "LineString";
    } else {
      type = "MultiLineString";
    }
  } else if (object["rings"]) {
    const esriJSONPolygon = (
      /** @type {EsriJSONPolygon} */
      object
    );
    const layout = getGeometryLayout(esriJSONPolygon);
    const rings = convertRings(esriJSONPolygon.rings, layout);
    if (rings.length === 1) {
      type = "Polygon";
      object = Object.assign({}, object, { ["rings"]: rings[0] });
    } else {
      type = "MultiPolygon";
      object = Object.assign({}, object, { ["rings"]: rings });
    }
  }
  const geometryReader = GEOMETRY_READERS[type];
  return transformGeometryWithOptions(geometryReader(object), false, options);
}
function convertRings(rings, layout) {
  const flatRing = [];
  const outerRings = [];
  const holes = [];
  let i, ii;
  for (i = 0, ii = rings.length; i < ii; ++i) {
    flatRing.length = 0;
    deflateCoordinates(flatRing, 0, rings[i], layout.length);
    const clockwise = linearRingIsClockwise(
      flatRing,
      0,
      flatRing.length,
      layout.length
    );
    if (clockwise) {
      outerRings.push([rings[i]]);
    } else {
      holes.push(rings[i]);
    }
  }
  while (holes.length) {
    const hole = holes.shift();
    let matched = false;
    for (i = outerRings.length - 1; i >= 0; i--) {
      const outerRing = outerRings[i][0];
      const containsHole = containsExtent(
        new LinearRing_default(outerRing).getExtent(),
        new LinearRing_default(hole).getExtent()
      );
      if (containsHole) {
        outerRings[i].push(hole);
        matched = true;
        break;
      }
    }
    if (!matched) {
      outerRings.push([hole.reverse()]);
    }
  }
  return outerRings;
}
function readPointGeometry(object) {
  let point;
  if (object.m !== void 0 && object.z !== void 0) {
    point = new Point_default([object.x, object.y, object.z, object.m], "XYZM");
  } else if (object.z !== void 0) {
    point = new Point_default([object.x, object.y, object.z], "XYZ");
  } else if (object.m !== void 0) {
    point = new Point_default([object.x, object.y, object.m], "XYM");
  } else {
    point = new Point_default([object.x, object.y]);
  }
  return point;
}
function readLineStringGeometry(object) {
  const layout = getGeometryLayout(object);
  return new LineString_default(object.paths[0], layout);
}
function readMultiLineStringGeometry(object) {
  const layout = getGeometryLayout(object);
  return new MultiLineString_default(object.paths, layout);
}
function getGeometryLayout(object) {
  let layout = "XY";
  if (object.hasZ === true && object.hasM === true) {
    layout = "XYZM";
  } else if (object.hasZ === true) {
    layout = "XYZ";
  } else if (object.hasM === true) {
    layout = "XYM";
  }
  return layout;
}
function readMultiPointGeometry(object) {
  const layout = getGeometryLayout(object);
  return new MultiPoint_default(object.points, layout);
}
function readMultiPolygonGeometry(object) {
  const layout = getGeometryLayout(object);
  return new MultiPolygon_default(object.rings, layout);
}
function readPolygonGeometry(object) {
  const layout = getGeometryLayout(object);
  return new Polygon_default(object.rings, layout);
}
function writePointGeometry(geometry, options) {
  const coordinates = geometry.getCoordinates();
  let esriJSON;
  const layout = geometry.getLayout();
  if (layout === "XYZ") {
    esriJSON = {
      x: coordinates[0],
      y: coordinates[1],
      z: coordinates[2]
    };
  } else if (layout === "XYM") {
    esriJSON = {
      x: coordinates[0],
      y: coordinates[1],
      m: coordinates[2]
    };
  } else if (layout === "XYZM") {
    esriJSON = {
      x: coordinates[0],
      y: coordinates[1],
      z: coordinates[2],
      m: coordinates[3]
    };
  } else if (layout === "XY") {
    esriJSON = {
      x: coordinates[0],
      y: coordinates[1]
    };
  } else {
    assert(false, 34);
  }
  return esriJSON;
}
function getHasZM(geometry) {
  const layout = geometry.getLayout();
  return {
    hasZ: layout === "XYZ" || layout === "XYZM",
    hasM: layout === "XYM" || layout === "XYZM"
  };
}
function writeLineStringGeometry(lineString, options) {
  const hasZM = getHasZM(lineString);
  return {
    hasZ: hasZM.hasZ,
    hasM: hasZM.hasM,
    paths: [
      /** @type {Array<EsriJSONPosition>} */
      lineString.getCoordinates()
    ]
  };
}
function writePolygonGeometry(polygon, options) {
  const hasZM = getHasZM(polygon);
  return {
    hasZ: hasZM.hasZ,
    hasM: hasZM.hasM,
    rings: (
      /** @type {Array<Array<EsriJSONPosition>>} */
      polygon.getCoordinates(false)
    )
  };
}
function writeMultiLineStringGeometry(multiLineString, options) {
  const hasZM = getHasZM(multiLineString);
  return {
    hasZ: hasZM.hasZ,
    hasM: hasZM.hasM,
    paths: (
      /** @type {Array<Array<EsriJSONPosition>>} */
      multiLineString.getCoordinates()
    )
  };
}
function writeMultiPointGeometry(multiPoint, options) {
  const hasZM = getHasZM(multiPoint);
  return {
    hasZ: hasZM.hasZ,
    hasM: hasZM.hasM,
    points: (
      /** @type {Array<EsriJSONPosition>} */
      multiPoint.getCoordinates()
    )
  };
}
function writeMultiPolygonGeometry(geometry, options) {
  const hasZM = getHasZM(geometry);
  const coordinates = geometry.getCoordinates(false);
  const output = [];
  for (let i = 0; i < coordinates.length; i++) {
    for (let x = coordinates[i].length - 1; x >= 0; x--) {
      output.push(coordinates[i][x]);
    }
  }
  return {
    hasZ: hasZM.hasZ,
    hasM: hasZM.hasM,
    rings: (
      /** @type {Array<Array<EsriJSONPosition>>} */
      output
    )
  };
}
function writeGeometry(geometry, options) {
  const geometryWriter = GEOMETRY_WRITERS[geometry.getType()];
  return geometryWriter(
    transformGeometryWithOptions(geometry, true, options),
    options
  );
}
var EsriJSON_default = EsriJSON;

// node_modules/ol/xml.js
var XML_SCHEMA_INSTANCE_URI = "http://www.w3.org/2001/XMLSchema-instance";
function createElementNS(namespaceURI, qualifiedName) {
  return getDocument().createElementNS(namespaceURI, qualifiedName);
}
function getAllTextContent(node, normalizeWhitespace) {
  return getAllTextContent_(node, normalizeWhitespace, []).join("");
}
function getAllTextContent_(node, normalizeWhitespace, accumulator) {
  if (node.nodeType == Node.CDATA_SECTION_NODE || node.nodeType == Node.TEXT_NODE) {
    if (normalizeWhitespace) {
      accumulator.push(String(node.nodeValue).replace(/(\r\n|\r|\n)/g, ""));
    } else {
      accumulator.push(node.nodeValue);
    }
  } else {
    let n;
    for (n = node.firstChild; n; n = n.nextSibling) {
      getAllTextContent_(n, normalizeWhitespace, accumulator);
    }
  }
  return accumulator;
}
function isDocument(object) {
  return "documentElement" in object;
}
function getAttributeNS(node, namespaceURI, name) {
  return node.getAttributeNS(namespaceURI, name) || "";
}
function parse(xml) {
  return new DOMParser().parseFromString(xml, "application/xml");
}
function makeArrayExtender(valueReader, thisArg) {
  return (
    /**
     * @param {Node} node Node.
     * @param {Array<*>} objectStack Object stack.
     */
    function(node, objectStack) {
      const value = valueReader.call(
        thisArg !== void 0 ? thisArg : this,
        node,
        objectStack
      );
      if (value !== void 0) {
        const array = (
          /** @type {Array<*>} */
          objectStack[objectStack.length - 1]
        );
        extend(array, value);
      }
    }
  );
}
function makeArrayPusher(valueReader, thisArg) {
  return (
    /**
     * @param {Element} node Node.
     * @param {Array<*>} objectStack Object stack.
     */
    function(node, objectStack) {
      const value = valueReader.call(
        thisArg !== void 0 ? thisArg : this,
        node,
        objectStack
      );
      if (value !== void 0) {
        const array = (
          /** @type {Array<*>} */
          objectStack[objectStack.length - 1]
        );
        array.push(value);
      }
    }
  );
}
function makeReplacer(valueReader, thisArg) {
  return (
    /**
     * @param {Node} node Node.
     * @param {Array<*>} objectStack Object stack.
     */
    function(node, objectStack) {
      const value = valueReader.call(
        thisArg !== void 0 ? thisArg : this,
        node,
        objectStack
      );
      if (value !== void 0) {
        objectStack[objectStack.length - 1] = value;
      }
    }
  );
}
function makeObjectPropertyPusher(valueReader, property, thisArg) {
  return (
    /**
     * @param {Element} node Node.
     * @param {Array<*>} objectStack Object stack.
     */
    function(node, objectStack) {
      const value = valueReader.call(
        thisArg !== void 0 ? thisArg : this,
        node,
        objectStack
      );
      if (value !== void 0) {
        const object = (
          /** @type {!Object} */
          objectStack[objectStack.length - 1]
        );
        const name = property !== void 0 ? property : node.localName;
        let array;
        if (name in object) {
          array = object[name];
        } else {
          array = [];
          object[name] = array;
        }
        array.push(value);
      }
    }
  );
}
function makeObjectPropertySetter(valueReader, property, thisArg) {
  return (
    /**
     * @param {Element} node Node.
     * @param {Array<*>} objectStack Object stack.
     */
    function(node, objectStack) {
      const value = valueReader.call(
        thisArg !== void 0 ? thisArg : this,
        node,
        objectStack
      );
      if (value !== void 0) {
        const object = (
          /** @type {!Object} */
          objectStack[objectStack.length - 1]
        );
        const name = property !== void 0 ? property : node.localName;
        object[name] = value;
      }
    }
  );
}
function makeChildAppender(nodeWriter, thisArg) {
  return function(node, value, objectStack) {
    nodeWriter.call(
      thisArg !== void 0 ? thisArg : this,
      node,
      value,
      objectStack
    );
    const parent = (
      /** @type {NodeStackItem} */
      objectStack[objectStack.length - 1]
    );
    const parentNode = parent.node;
    parentNode.appendChild(node);
  };
}
function makeArraySerializer(nodeWriter, thisArg) {
  let serializersNS, nodeFactory;
  return function(node, value, objectStack) {
    if (serializersNS === void 0) {
      serializersNS = {};
      const serializers = {};
      serializers[node.localName] = nodeWriter;
      serializersNS[node.namespaceURI] = serializers;
      nodeFactory = makeSimpleNodeFactory(node.localName);
    }
    serialize(serializersNS, nodeFactory, value, objectStack);
  };
}
function makeSimpleNodeFactory(fixedNodeName, fixedNamespaceURI) {
  return (
    /**
     * @param {*} value Value.
     * @param {Array<*>} objectStack Object stack.
     * @param {string} [newNodeName] Node name.
     * @return {Node} Node.
     */
    function(value, objectStack, newNodeName) {
      const context = (
        /** @type {NodeStackItem} */
        objectStack[objectStack.length - 1]
      );
      const node = context.node;
      let nodeName = fixedNodeName;
      if (nodeName === void 0) {
        nodeName = newNodeName;
      }
      const namespaceURI = fixedNamespaceURI !== void 0 ? fixedNamespaceURI : node.namespaceURI;
      return createElementNS(
        namespaceURI,
        /** @type {string} */
        nodeName
      );
    }
  );
}
var OBJECT_PROPERTY_NODE_FACTORY = makeSimpleNodeFactory();
function makeSequence(object, orderedKeys) {
  const length = orderedKeys.length;
  const sequence = new Array(length);
  for (let i = 0; i < length; ++i) {
    sequence[i] = object[orderedKeys[i]];
  }
  return sequence;
}
function makeStructureNS(namespaceURIs, structure, structureNS) {
  structureNS = structureNS !== void 0 ? structureNS : {};
  let i, ii;
  for (i = 0, ii = namespaceURIs.length; i < ii; ++i) {
    structureNS[namespaceURIs[i]] = structure;
  }
  return structureNS;
}
function parseNode(parsersNS, node, objectStack, thisArg) {
  let n;
  for (n = node.firstElementChild; n; n = n.nextElementSibling) {
    const parsers = parsersNS[n.namespaceURI];
    if (parsers !== void 0) {
      const parser = parsers[n.localName];
      if (parser !== void 0) {
        parser.call(thisArg, n, objectStack);
      }
    }
  }
}
function pushParseAndPop(object, parsersNS, node, objectStack, thisArg) {
  objectStack.push(object);
  parseNode(parsersNS, node, objectStack, thisArg);
  return (
    /** @type {T} */
    objectStack.pop()
  );
}
function serialize(serializersNS, nodeFactory, values, objectStack, keys, thisArg) {
  const length = (keys !== void 0 ? keys : values).length;
  let value, node;
  for (let i = 0; i < length; ++i) {
    value = values[i];
    if (value !== void 0) {
      node = nodeFactory.call(
        thisArg !== void 0 ? thisArg : this,
        value,
        objectStack,
        keys !== void 0 ? keys[i] : void 0
      );
      if (node !== void 0) {
        serializersNS[node.namespaceURI][node.localName].call(
          thisArg,
          node,
          value,
          objectStack
        );
      }
    }
  }
}
function pushSerializeAndPop(object, serializersNS, nodeFactory, values, objectStack, keys, thisArg) {
  objectStack.push(object);
  serialize(serializersNS, nodeFactory, values, objectStack, keys, thisArg);
  return (
    /** @type {O|undefined} */
    objectStack.pop()
  );
}
var xmlSerializer_ = void 0;
function getXMLSerializer() {
  if (xmlSerializer_ === void 0 && typeof XMLSerializer !== "undefined") {
    xmlSerializer_ = new XMLSerializer();
  }
  return xmlSerializer_;
}
var document_ = void 0;
function getDocument() {
  if (document_ === void 0 && typeof document !== "undefined") {
    document_ = document.implementation.createDocument("", "", null);
  }
  return document_;
}

// node_modules/ol/format/XMLFeature.js
var XMLFeature = class extends Feature_default2 {
  constructor() {
    super();
    this.xmlSerializer_ = getXMLSerializer();
  }
  /**
   * @return {import("./Feature.js").Type} Format.
   */
  getType() {
    return "xml";
  }
  /**
   * Read a single feature.
   *
   * @param {Document|Element|Object|string} source Source.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @return {import("../Feature.js").default} Feature.
   * @api
   */
  readFeature(source, options) {
    if (!source) {
      return null;
    }
    if (typeof source === "string") {
      const doc = parse(source);
      return this.readFeatureFromDocument(doc, options);
    }
    if (isDocument(source)) {
      return this.readFeatureFromDocument(
        /** @type {Document} */
        source,
        options
      );
    }
    return this.readFeatureFromNode(
      /** @type {Element} */
      source,
      options
    );
  }
  /**
   * @param {Document} doc Document.
   * @param {import("./Feature.js").ReadOptions} [options] Options.
   * @return {import("../Feature.js").default} Feature.
   */
  readFeatureFromDocument(doc, options) {
    const features = this.readFeaturesFromDocument(doc, options);
    if (features.length > 0) {
      return features[0];
    }
    return null;
  }
  /**
   * @param {Element} node Node.
   * @param {import("./Feature.js").ReadOptions} [options] Options.
   * @return {import("../Feature.js").default} Feature.
   */
  readFeatureFromNode(node, options) {
    return null;
  }
  /**
   * Read all features from a feature collection.
   *
   * @param {Document|Element|Object|string} source Source.
   * @param {import("./Feature.js").ReadOptions} [options] Options.
   * @return {Array<import("../Feature.js").default>} Features.
   * @api
   */
  readFeatures(source, options) {
    if (!source) {
      return [];
    }
    if (typeof source === "string") {
      const doc = parse(source);
      return this.readFeaturesFromDocument(doc, options);
    }
    if (isDocument(source)) {
      return this.readFeaturesFromDocument(
        /** @type {Document} */
        source,
        options
      );
    }
    return this.readFeaturesFromNode(
      /** @type {Element} */
      source,
      options
    );
  }
  /**
   * @param {Document} doc Document.
   * @param {import("./Feature.js").ReadOptions} [options] Options.
   * @protected
   * @return {Array<import("../Feature.js").default>} Features.
   */
  readFeaturesFromDocument(doc, options) {
    const features = [];
    for (let n = doc.firstChild; n; n = n.nextSibling) {
      if (n.nodeType == Node.ELEMENT_NODE) {
        extend(
          features,
          this.readFeaturesFromNode(
            /** @type {Element} */
            n,
            options
          )
        );
      }
    }
    return features;
  }
  /**
   * @abstract
   * @param {Element} node Node.
   * @param {import("./Feature.js").ReadOptions} [options] Options.
   * @protected
   * @return {Array<import("../Feature.js").default>} Features.
   */
  readFeaturesFromNode(node, options) {
    return abstract();
  }
  /**
   * Read a single geometry from a source.
   *
   * @param {Document|Element|Object|string} source Source.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @return {import("../geom/Geometry.js").default} Geometry.
   */
  readGeometry(source, options) {
    if (!source) {
      return null;
    }
    if (typeof source === "string") {
      const doc = parse(source);
      return this.readGeometryFromDocument(doc, options);
    }
    if (isDocument(source)) {
      return this.readGeometryFromDocument(
        /** @type {Document} */
        source,
        options
      );
    }
    return this.readGeometryFromNode(
      /** @type {Element} */
      source,
      options
    );
  }
  /**
   * @param {Document} doc Document.
   * @param {import("./Feature.js").ReadOptions} [options] Options.
   * @protected
   * @return {import("../geom/Geometry.js").default} Geometry.
   */
  readGeometryFromDocument(doc, options) {
    return null;
  }
  /**
   * @param {Element} node Node.
   * @param {import("./Feature.js").ReadOptions} [options] Options.
   * @protected
   * @return {import("../geom/Geometry.js").default} Geometry.
   */
  readGeometryFromNode(node, options) {
    return null;
  }
  /**
   * Read the projection from the source.
   *
   * @param {Document|Element|Object|string} source Source.
   * @return {import("../proj/Projection.js").default} Projection.
   * @api
   */
  readProjection(source) {
    if (!source) {
      return null;
    }
    if (typeof source === "string") {
      const doc = parse(source);
      return this.readProjectionFromDocument(doc);
    }
    if (isDocument(source)) {
      return this.readProjectionFromDocument(
        /** @type {Document} */
        source
      );
    }
    return this.readProjectionFromNode(
      /** @type {Element} */
      source
    );
  }
  /**
   * @param {Document} doc Document.
   * @protected
   * @return {import("../proj/Projection.js").default} Projection.
   */
  readProjectionFromDocument(doc) {
    return this.dataProjection;
  }
  /**
   * @param {Element} node Node.
   * @protected
   * @return {import("../proj/Projection.js").default} Projection.
   */
  readProjectionFromNode(node) {
    return this.dataProjection;
  }
  /**
   * Encode a feature as string.
   *
   * @param {import("../Feature.js").default} feature Feature.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {string} Encoded feature.
   */
  writeFeature(feature, options) {
    const node = this.writeFeatureNode(feature, options);
    return this.xmlSerializer_.serializeToString(node);
  }
  /**
   * @param {import("../Feature.js").default} feature Feature.
   * @param {import("./Feature.js").WriteOptions} [options] Options.
   * @protected
   * @return {Node} Node.
   */
  writeFeatureNode(feature, options) {
    return null;
  }
  /**
   * Encode an array of features as string.
   *
   * @param {Array<import("../Feature.js").default>} features Features.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {string} Result.
   * @api
   */
  writeFeatures(features, options) {
    const node = this.writeFeaturesNode(features, options);
    return this.xmlSerializer_.serializeToString(node);
  }
  /**
   * @param {Array<import("../Feature.js").default>} features Features.
   * @param {import("./Feature.js").WriteOptions} [options] Options.
   * @return {Node} Node.
   */
  writeFeaturesNode(features, options) {
    return null;
  }
  /**
   * Encode a geometry as string.
   *
   * @param {import("../geom/Geometry.js").default} geometry Geometry.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {string} Encoded geometry.
   */
  writeGeometry(geometry, options) {
    const node = this.writeGeometryNode(geometry, options);
    return this.xmlSerializer_.serializeToString(node);
  }
  /**
   * @param {import("../geom/Geometry.js").default} geometry Geometry.
   * @param {import("./Feature.js").WriteOptions} [options] Options.
   * @return {Node} Node.
   */
  writeGeometryNode(geometry, options) {
    return null;
  }
};
var XMLFeature_default = XMLFeature;

// node_modules/ol/format/GMLBase.js
var GMLNS = "http://www.opengis.net/gml";
var ONLY_WHITESPACE_RE = /^\s*$/;
var GMLBase = class extends XMLFeature_default {
  /**
   * @param {Options} [options] Optional configuration object.
   */
  constructor(options) {
    super();
    options = options ? options : {};
    this.featureType = options.featureType;
    this.featureNS = options.featureNS;
    this.srsName = options.srsName;
    this.schemaLocation = "";
    this.FEATURE_COLLECTION_PARSERS = {};
    this.FEATURE_COLLECTION_PARSERS[this.namespace] = {
      "featureMember": makeArrayPusher(this.readFeaturesInternal),
      "featureMembers": makeReplacer(this.readFeaturesInternal)
    };
    this.supportedMediaTypes = ["application/gml+xml"];
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Array<Feature> | undefined} Features.
   */
  readFeaturesInternal(node, objectStack) {
    const localName = node.localName;
    let features = null;
    if (localName == "FeatureCollection") {
      features = pushParseAndPop(
        [],
        this.FEATURE_COLLECTION_PARSERS,
        node,
        objectStack,
        this
      );
    } else if (localName == "featureMembers" || localName == "featureMember" || localName == "member") {
      const context = objectStack[0];
      let featureType = context["featureType"];
      let featureNS = context["featureNS"];
      const prefix = "p";
      const defaultPrefix = "p0";
      if (!featureType && node.childNodes) {
        featureType = [], featureNS = {};
        for (let i = 0, ii = node.childNodes.length; i < ii; ++i) {
          const child = (
            /** @type {Element} */
            node.childNodes[i]
          );
          if (child.nodeType === 1) {
            const ft = child.nodeName.split(":").pop();
            if (!featureType.includes(ft)) {
              let key = "";
              let count = 0;
              const uri = child.namespaceURI;
              for (const candidate in featureNS) {
                if (featureNS[candidate] === uri) {
                  key = candidate;
                  break;
                }
                ++count;
              }
              if (!key) {
                key = prefix + count;
                featureNS[key] = uri;
              }
              featureType.push(key + ":" + ft);
            }
          }
        }
        if (localName != "featureMember") {
          context["featureType"] = featureType;
          context["featureNS"] = featureNS;
        }
      }
      if (typeof featureNS === "string") {
        const ns = featureNS;
        featureNS = {};
        featureNS[defaultPrefix] = ns;
      }
      const parsersNS = {};
      const featureTypes = Array.isArray(featureType) ? featureType : [featureType];
      for (const p in featureNS) {
        const parsers = {};
        for (let i = 0, ii = featureTypes.length; i < ii; ++i) {
          const featurePrefix = featureTypes[i].includes(":") ? featureTypes[i].split(":")[0] : defaultPrefix;
          if (featurePrefix === p) {
            parsers[featureTypes[i].split(":").pop()] = localName == "featureMembers" ? makeArrayPusher(this.readFeatureElement, this) : makeReplacer(this.readFeatureElement, this);
          }
        }
        parsersNS[featureNS[p]] = parsers;
      }
      if (localName == "featureMember" || localName == "member") {
        features = pushParseAndPop(void 0, parsersNS, node, objectStack);
      } else {
        features = pushParseAndPop([], parsersNS, node, objectStack);
      }
    }
    if (features === null) {
      features = [];
    }
    return features;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {import("../geom/Geometry.js").default|import("../extent.js").Extent|undefined} Geometry.
   */
  readGeometryOrExtent(node, objectStack) {
    const context = (
      /** @type {Object} */
      objectStack[0]
    );
    context["srsName"] = node.firstElementChild.getAttribute("srsName");
    context["srsDimension"] = node.firstElementChild.getAttribute("srsDimension");
    return pushParseAndPop(
      null,
      this.GEOMETRY_PARSERS,
      node,
      objectStack,
      this
    );
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {import("../extent.js").Extent|undefined} Geometry.
   */
  readExtentElement(node, objectStack) {
    const context = (
      /** @type {Object} */
      objectStack[0]
    );
    const extent = (
      /** @type {import("../extent.js").Extent} */
      this.readGeometryOrExtent(node, objectStack)
    );
    return extent ? transformExtentWithOptions(extent, context) : void 0;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {import("../geom/Geometry.js").default|undefined} Geometry.
   */
  readGeometryElement(node, objectStack) {
    const context = (
      /** @type {Object} */
      objectStack[0]
    );
    const geometry = (
      /** @type {import("../geom/Geometry.js").default} */
      this.readGeometryOrExtent(node, objectStack)
    );
    return geometry ? transformGeometryWithOptions(geometry, false, context) : void 0;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @param {boolean} asFeature whether result should be wrapped as a feature.
   * @return {Feature|Object} Feature
   */
  readFeatureElementInternal(node, objectStack, asFeature) {
    let geometryName;
    const values = {};
    for (let n = node.firstElementChild; n; n = n.nextElementSibling) {
      let value;
      const localName = n.localName;
      if (n.childNodes.length === 0 || n.childNodes.length === 1 && (n.firstChild.nodeType === 3 || n.firstChild.nodeType === 4)) {
        value = getAllTextContent(n, false);
        if (ONLY_WHITESPACE_RE.test(value)) {
          value = void 0;
        }
      } else {
        if (asFeature) {
          value = localName === "boundedBy" ? this.readExtentElement(n, objectStack) : this.readGeometryElement(n, objectStack);
        }
        if (!value) {
          value = this.readFeatureElementInternal(n, objectStack, false);
        } else if (localName !== "boundedBy") {
          geometryName = localName;
        }
      }
      const len = n.attributes.length;
      if (len > 0 && !(value instanceof Geometry_default)) {
        value = { _content_: value };
        for (let i = 0; i < len; i++) {
          const attName = n.attributes[i].name;
          value[attName] = n.attributes[i].value;
        }
      }
      if (values[localName]) {
        if (!(values[localName] instanceof Array)) {
          values[localName] = [values[localName]];
        }
        values[localName].push(value);
      } else {
        values[localName] = value;
      }
    }
    if (!asFeature) {
      return values;
    }
    const feature = new Feature_default(values);
    if (geometryName) {
      feature.setGeometryName(geometryName);
    }
    const fid = node.getAttribute("fid") || getAttributeNS(node, this.namespace, "id");
    if (fid) {
      feature.setId(fid);
    }
    return feature;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Feature} Feature.
   */
  readFeatureElement(node, objectStack) {
    return this.readFeatureElementInternal(node, objectStack, true);
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Point|undefined} Point.
   */
  readPoint(node, objectStack) {
    const flatCoordinates = this.readFlatCoordinatesFromNode(node, objectStack);
    if (flatCoordinates) {
      return new Point_default(flatCoordinates, "XYZ");
    }
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {MultiPoint|undefined} MultiPoint.
   */
  readMultiPoint(node, objectStack) {
    const coordinates = pushParseAndPop(
      [],
      this.MULTIPOINT_PARSERS,
      node,
      objectStack,
      this
    );
    if (coordinates) {
      return new MultiPoint_default(coordinates);
    }
    return void 0;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {MultiLineString|undefined} MultiLineString.
   */
  readMultiLineString(node, objectStack) {
    const lineStrings = pushParseAndPop(
      [],
      this.MULTILINESTRING_PARSERS,
      node,
      objectStack,
      this
    );
    if (lineStrings) {
      return new MultiLineString_default(lineStrings);
    }
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {MultiPolygon|undefined} MultiPolygon.
   */
  readMultiPolygon(node, objectStack) {
    const polygons = pushParseAndPop(
      [],
      this.MULTIPOLYGON_PARSERS,
      node,
      objectStack,
      this
    );
    if (polygons) {
      return new MultiPolygon_default(polygons);
    }
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   */
  pointMemberParser(node, objectStack) {
    parseNode(this.POINTMEMBER_PARSERS, node, objectStack, this);
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   */
  lineStringMemberParser(node, objectStack) {
    parseNode(this.LINESTRINGMEMBER_PARSERS, node, objectStack, this);
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   */
  polygonMemberParser(node, objectStack) {
    parseNode(this.POLYGONMEMBER_PARSERS, node, objectStack, this);
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {LineString|undefined} LineString.
   */
  readLineString(node, objectStack) {
    const flatCoordinates = this.readFlatCoordinatesFromNode(node, objectStack);
    if (flatCoordinates) {
      const lineString = new LineString_default(flatCoordinates, "XYZ");
      return lineString;
    }
    return void 0;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Array<number>|undefined} LinearRing flat coordinates.
   */
  readFlatLinearRing(node, objectStack) {
    const ring = pushParseAndPop(
      null,
      this.GEOMETRY_FLAT_COORDINATES_PARSERS,
      node,
      objectStack,
      this
    );
    if (ring) {
      return ring;
    }
    return void 0;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {LinearRing|undefined} LinearRing.
   */
  readLinearRing(node, objectStack) {
    const flatCoordinates = this.readFlatCoordinatesFromNode(node, objectStack);
    if (flatCoordinates) {
      return new LinearRing_default(flatCoordinates, "XYZ");
    }
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Polygon|undefined} Polygon.
   */
  readPolygon(node, objectStack) {
    const flatLinearRings = pushParseAndPop(
      [null],
      this.FLAT_LINEAR_RINGS_PARSERS,
      node,
      objectStack,
      this
    );
    if (flatLinearRings && flatLinearRings[0]) {
      const flatCoordinates = flatLinearRings[0];
      const ends = [flatCoordinates.length];
      let i, ii;
      for (i = 1, ii = flatLinearRings.length; i < ii; ++i) {
        extend(flatCoordinates, flatLinearRings[i]);
        ends.push(flatCoordinates.length);
      }
      return new Polygon_default(flatCoordinates, "XYZ", ends);
    }
    return void 0;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Array<number>} Flat coordinates.
   */
  readFlatCoordinatesFromNode(node, objectStack) {
    return pushParseAndPop(
      null,
      this.GEOMETRY_FLAT_COORDINATES_PARSERS,
      node,
      objectStack,
      this
    );
  }
  /**
   * @param {Element} node Node.
   * @param {import("./Feature.js").ReadOptions} [options] Options.
   * @protected
   * @return {import("../geom/Geometry.js").default} Geometry.
   */
  readGeometryFromNode(node, options) {
    const geometry = this.readGeometryElement(node, [
      this.getReadOptions(node, options ? options : {})
    ]);
    return geometry ? geometry : null;
  }
  /**
   * @param {Element} node Node.
   * @param {import("./Feature.js").ReadOptions} [options] Options.
   * @return {Array<import("../Feature.js").default>} Features.
   */
  readFeaturesFromNode(node, options) {
    const internalOptions = {
      featureType: this.featureType,
      featureNS: this.featureNS
    };
    if (internalOptions) {
      Object.assign(internalOptions, this.getReadOptions(node, options));
    }
    const features = this.readFeaturesInternal(node, [internalOptions]);
    return features || [];
  }
  /**
   * @param {Element} node Node.
   * @return {import("../proj/Projection.js").default} Projection.
   */
  readProjectionFromNode(node) {
    return get(
      this.srsName ? this.srsName : node.firstElementChild.getAttribute("srsName")
    );
  }
};
GMLBase.prototype.namespace = GMLNS;
GMLBase.prototype.FLAT_LINEAR_RINGS_PARSERS = {
  "http://www.opengis.net/gml": {}
};
GMLBase.prototype.GEOMETRY_FLAT_COORDINATES_PARSERS = {
  "http://www.opengis.net/gml": {}
};
GMLBase.prototype.GEOMETRY_PARSERS = {
  "http://www.opengis.net/gml": {}
};
GMLBase.prototype.MULTIPOINT_PARSERS = {
  "http://www.opengis.net/gml": {
    "pointMember": makeArrayPusher(GMLBase.prototype.pointMemberParser),
    "pointMembers": makeArrayPusher(GMLBase.prototype.pointMemberParser)
  }
};
GMLBase.prototype.MULTILINESTRING_PARSERS = {
  "http://www.opengis.net/gml": {
    "lineStringMember": makeArrayPusher(
      GMLBase.prototype.lineStringMemberParser
    ),
    "lineStringMembers": makeArrayPusher(
      GMLBase.prototype.lineStringMemberParser
    )
  }
};
GMLBase.prototype.MULTIPOLYGON_PARSERS = {
  "http://www.opengis.net/gml": {
    "polygonMember": makeArrayPusher(GMLBase.prototype.polygonMemberParser),
    "polygonMembers": makeArrayPusher(GMLBase.prototype.polygonMemberParser)
  }
};
GMLBase.prototype.POINTMEMBER_PARSERS = {
  "http://www.opengis.net/gml": {
    "Point": makeArrayPusher(GMLBase.prototype.readFlatCoordinatesFromNode)
  }
};
GMLBase.prototype.LINESTRINGMEMBER_PARSERS = {
  "http://www.opengis.net/gml": {
    "LineString": makeArrayPusher(GMLBase.prototype.readLineString)
  }
};
GMLBase.prototype.POLYGONMEMBER_PARSERS = {
  "http://www.opengis.net/gml": {
    "Polygon": makeArrayPusher(GMLBase.prototype.readPolygon)
  }
};
GMLBase.prototype.RING_PARSERS = {
  "http://www.opengis.net/gml": {
    "LinearRing": makeReplacer(GMLBase.prototype.readFlatLinearRing)
  }
};
var GMLBase_default = GMLBase;

// node_modules/ol/format/xsd.js
function readBoolean(node) {
  const s = getAllTextContent(node, false);
  return readBooleanString(s);
}
function readBooleanString(string) {
  const m = /^\s*(true|1)|(false|0)\s*$/.exec(string);
  if (m) {
    return m[1] !== void 0 || false;
  }
  return void 0;
}
function readDateTime(node) {
  const s = getAllTextContent(node, false);
  const dateTime = Date.parse(s);
  return isNaN(dateTime) ? void 0 : dateTime / 1e3;
}
function readDecimal(node) {
  const s = getAllTextContent(node, false);
  return readDecimalString(s);
}
function readDecimalString(string) {
  const m = /^\s*([+\-]?\d*\.?\d+(?:e[+\-]?\d+)?)\s*$/i.exec(string);
  if (m) {
    return parseFloat(m[1]);
  }
  return void 0;
}
function readPositiveInteger(node) {
  const s = getAllTextContent(node, false);
  return readNonNegativeIntegerString(s);
}
function readNonNegativeIntegerString(string) {
  const m = /^\s*(\d+)\s*$/.exec(string);
  if (m) {
    return parseInt(m[1], 10);
  }
  return void 0;
}
function readString(node) {
  return getAllTextContent(node, false).trim();
}
function writeBooleanTextNode(node, bool) {
  writeStringTextNode(node, bool ? "1" : "0");
}
function writeCDATASection(node, string) {
  node.appendChild(getDocument().createCDATASection(string));
}
function writeDateTimeTextNode(node, dateTime) {
  const date = new Date(dateTime * 1e3);
  const string = date.getUTCFullYear() + "-" + padNumber(date.getUTCMonth() + 1, 2) + "-" + padNumber(date.getUTCDate(), 2) + "T" + padNumber(date.getUTCHours(), 2) + ":" + padNumber(date.getUTCMinutes(), 2) + ":" + padNumber(date.getUTCSeconds(), 2) + "Z";
  node.appendChild(getDocument().createTextNode(string));
}
function writeDecimalTextNode(node, decimal) {
  const string = decimal.toPrecision();
  node.appendChild(getDocument().createTextNode(string));
}
function writeNonNegativeIntegerTextNode(node, nonNegativeInteger) {
  const string = nonNegativeInteger.toString();
  node.appendChild(getDocument().createTextNode(string));
}
function writeStringTextNode(node, string) {
  node.appendChild(getDocument().createTextNode(string));
}

// node_modules/ol/format/GML2.js
var schemaLocation = GMLNS + " http://schemas.opengis.net/gml/2.1.2/feature.xsd";
var MULTIGEOMETRY_TO_MEMBER_NODENAME = {
  "MultiLineString": "lineStringMember",
  "MultiCurve": "curveMember",
  "MultiPolygon": "polygonMember",
  "MultiSurface": "surfaceMember"
};
var GML2 = class extends GMLBase_default {
  /**
   * @param {import("./GMLBase.js").Options} [options] Optional configuration object.
   */
  constructor(options) {
    options = options ? options : {};
    super(options);
    this.FEATURE_COLLECTION_PARSERS[GMLNS]["featureMember"] = makeArrayPusher(
      this.readFeaturesInternal
    );
    this.schemaLocation = options.schemaLocation ? options.schemaLocation : schemaLocation;
  }
  /**
   * @param {Node} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Array<number>|undefined} Flat coordinates.
   */
  readFlatCoordinates(node, objectStack) {
    const s = getAllTextContent(node, false).replace(/^\s*|\s*$/g, "");
    const context = (
      /** @type {import("../xml.js").NodeStackItem} */
      objectStack[0]
    );
    const containerSrs = context["srsName"];
    let axisOrientation = "enu";
    if (containerSrs) {
      const proj = get(containerSrs);
      if (proj) {
        axisOrientation = proj.getAxisOrientation();
      }
    }
    const coordsGroups = s.trim().split(/\s+/);
    const flatCoordinates = [];
    for (let i = 0, ii = coordsGroups.length; i < ii; i++) {
      const coords = coordsGroups[i].split(/,+/);
      const x = parseFloat(coords[0]);
      const y = parseFloat(coords[1]);
      const z = coords.length === 3 ? parseFloat(coords[2]) : 0;
      if (axisOrientation.substr(0, 2) === "en") {
        flatCoordinates.push(x, y, z);
      } else {
        flatCoordinates.push(y, x, z);
      }
    }
    return flatCoordinates;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {import("../extent.js").Extent|undefined} Envelope.
   */
  readBox(node, objectStack) {
    const flatCoordinates = pushParseAndPop(
      [null],
      this.BOX_PARSERS_,
      node,
      objectStack,
      this
    );
    return createOrUpdate(
      flatCoordinates[1][0],
      flatCoordinates[1][1],
      flatCoordinates[1][3],
      flatCoordinates[1][4]
    );
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   */
  innerBoundaryIsParser(node, objectStack) {
    const flatLinearRing = pushParseAndPop(
      void 0,
      this.RING_PARSERS,
      node,
      objectStack,
      this
    );
    if (flatLinearRing) {
      const flatLinearRings = (
        /** @type {Array<Array<number>>} */
        objectStack[objectStack.length - 1]
      );
      flatLinearRings.push(flatLinearRing);
    }
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   */
  outerBoundaryIsParser(node, objectStack) {
    const flatLinearRing = pushParseAndPop(
      void 0,
      this.RING_PARSERS,
      node,
      objectStack,
      this
    );
    if (flatLinearRing) {
      const flatLinearRings = (
        /** @type {Array<Array<number>>} */
        objectStack[objectStack.length - 1]
      );
      flatLinearRings[0] = flatLinearRing;
    }
  }
  /**
   * @const
   * @param {*} value Value.
   * @param {Array<*>} objectStack Object stack.
   * @param {string} [nodeName] Node name.
   * @return {Element|undefined} Node.
   * @private
   */
  GEOMETRY_NODE_FACTORY_(value, objectStack, nodeName) {
    const context = objectStack[objectStack.length - 1];
    const multiSurface = context["multiSurface"];
    const surface = context["surface"];
    const multiCurve = context["multiCurve"];
    if (!Array.isArray(value)) {
      nodeName = /** @type {import("../geom/Geometry.js").default} */
      value.getType();
      if (nodeName === "MultiPolygon" && multiSurface === true) {
        nodeName = "MultiSurface";
      } else if (nodeName === "Polygon" && surface === true) {
        nodeName = "Surface";
      } else if (nodeName === "MultiLineString" && multiCurve === true) {
        nodeName = "MultiCurve";
      }
    } else {
      nodeName = "Envelope";
    }
    return createElementNS("http://www.opengis.net/gml", nodeName);
  }
  /**
   * @param {Element} node Node.
   * @param {import("../Feature.js").default} feature Feature.
   * @param {Array<*>} objectStack Node stack.
   */
  writeFeatureElement(node, feature, objectStack) {
    const fid = feature.getId();
    if (fid) {
      node.setAttribute(
        "fid",
        /** @type {string} */
        fid
      );
    }
    const context = (
      /** @type {Object} */
      objectStack[objectStack.length - 1]
    );
    const featureNS = context["featureNS"];
    const geometryName = feature.getGeometryName();
    if (!context.serializers) {
      context.serializers = {};
      context.serializers[featureNS] = {};
    }
    const keys = [];
    const values = [];
    if (feature.hasProperties()) {
      const properties = feature.getProperties();
      for (const key in properties) {
        const value = properties[key];
        if (value !== null) {
          keys.push(key);
          values.push(value);
          if (key == geometryName || typeof /** @type {?} */
          value.getSimplifiedGeometry === "function") {
            if (!(key in context.serializers[featureNS])) {
              context.serializers[featureNS][key] = makeChildAppender(
                this.writeGeometryElement,
                this
              );
            }
          } else {
            if (!(key in context.serializers[featureNS])) {
              context.serializers[featureNS][key] = makeChildAppender(writeStringTextNode);
            }
          }
        }
      }
    }
    const item = Object.assign({}, context);
    item.node = node;
    pushSerializeAndPop(
      /** @type {import("../xml.js").NodeStackItem} */
      item,
      context.serializers,
      makeSimpleNodeFactory(void 0, featureNS),
      values,
      objectStack,
      keys
    );
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/LineString.js").default} geometry LineString geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeCurveOrLineString(node, geometry, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const srsName = context["srsName"];
    if (node.nodeName !== "LineStringSegment" && srsName) {
      node.setAttribute("srsName", srsName);
    }
    if (node.nodeName === "LineString" || node.nodeName === "LineStringSegment") {
      const coordinates = this.createCoordinatesNode_(node.namespaceURI);
      node.appendChild(coordinates);
      this.writeCoordinates_(coordinates, geometry, objectStack);
    } else if (node.nodeName === "Curve") {
      const segments = createElementNS(node.namespaceURI, "segments");
      node.appendChild(segments);
      this.writeCurveSegments_(segments, geometry, objectStack);
    }
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/LineString.js").default} line LineString geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeLineStringOrCurveMember(node, line, objectStack) {
    const child = this.GEOMETRY_NODE_FACTORY_(line, objectStack);
    if (child) {
      node.appendChild(child);
      this.writeCurveOrLineString(child, line, objectStack);
    }
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/MultiLineString.js").default} geometry MultiLineString geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeMultiCurveOrLineString(node, geometry, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const hasZ = context["hasZ"];
    const srsName = context["srsName"];
    const curve = context["curve"];
    if (srsName) {
      node.setAttribute("srsName", srsName);
    }
    const lines = geometry.getLineStrings();
    pushSerializeAndPop(
      { node, hasZ, srsName, curve },
      this.LINESTRINGORCURVEMEMBER_SERIALIZERS,
      this.MULTIGEOMETRY_MEMBER_NODE_FACTORY_,
      lines,
      objectStack,
      void 0,
      this
    );
  }
  /**
   * @param {Node} node Node.
   * @param {import("../geom/Geometry.js").default|import("../extent.js").Extent} geometry Geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeGeometryElement(node, geometry, objectStack) {
    const context = (
      /** @type {import("./Feature.js").WriteOptions} */
      objectStack[objectStack.length - 1]
    );
    const item = Object.assign({}, context);
    item["node"] = node;
    let value;
    if (Array.isArray(geometry)) {
      value = transformExtentWithOptions(
        /** @type {import("../extent.js").Extent} */
        geometry,
        context
      );
    } else {
      value = transformGeometryWithOptions(
        /** @type {import("../geom/Geometry.js").default} */
        geometry,
        true,
        context
      );
    }
    pushSerializeAndPop(
      /** @type {import("../xml.js").NodeStackItem} */
      item,
      this.GEOMETRY_SERIALIZERS,
      this.GEOMETRY_NODE_FACTORY_,
      [value],
      objectStack,
      void 0,
      this
    );
  }
  /**
   * @param {string} namespaceURI XML namespace.
   * @return {Element} coordinates node.
   * @private
   */
  createCoordinatesNode_(namespaceURI) {
    const coordinates = createElementNS(namespaceURI, "coordinates");
    coordinates.setAttribute("decimal", ".");
    coordinates.setAttribute("cs", ",");
    coordinates.setAttribute("ts", " ");
    return coordinates;
  }
  /**
   * @param {Node} node Node.
   * @param {import("../geom/LineString.js").default|import("../geom/LinearRing.js").default} value Geometry.
   * @param {Array<*>} objectStack Node stack.
   * @private
   */
  writeCoordinates_(node, value, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const hasZ = context["hasZ"];
    const srsName = context["srsName"];
    const points = value.getCoordinates();
    const len = points.length;
    const parts = new Array(len);
    for (let i = 0; i < len; ++i) {
      const point = points[i];
      parts[i] = this.getCoords_(point, srsName, hasZ);
    }
    writeStringTextNode(node, parts.join(" "));
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/LineString.js").default} line LineString geometry.
   * @param {Array<*>} objectStack Node stack.
   * @private
   */
  writeCurveSegments_(node, line, objectStack) {
    const child = createElementNS(node.namespaceURI, "LineStringSegment");
    node.appendChild(child);
    this.writeCurveOrLineString(child, line, objectStack);
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/Polygon.js").default} geometry Polygon geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeSurfaceOrPolygon(node, geometry, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const hasZ = context["hasZ"];
    const srsName = context["srsName"];
    if (node.nodeName !== "PolygonPatch" && srsName) {
      node.setAttribute("srsName", srsName);
    }
    if (node.nodeName === "Polygon" || node.nodeName === "PolygonPatch") {
      const rings = geometry.getLinearRings();
      pushSerializeAndPop(
        { node, hasZ, srsName },
        this.RING_SERIALIZERS,
        this.RING_NODE_FACTORY_,
        rings,
        objectStack,
        void 0,
        this
      );
    } else if (node.nodeName === "Surface") {
      const patches = createElementNS(node.namespaceURI, "patches");
      node.appendChild(patches);
      this.writeSurfacePatches_(patches, geometry, objectStack);
    }
  }
  /**
   * @param {*} value Value.
   * @param {Array<*>} objectStack Object stack.
   * @param {string} [nodeName] Node name.
   * @return {Node} Node.
   * @private
   */
  RING_NODE_FACTORY_(value, objectStack, nodeName) {
    const context = objectStack[objectStack.length - 1];
    const parentNode = context.node;
    const exteriorWritten = context["exteriorWritten"];
    if (exteriorWritten === void 0) {
      context["exteriorWritten"] = true;
    }
    return createElementNS(
      parentNode.namespaceURI,
      exteriorWritten !== void 0 ? "innerBoundaryIs" : "outerBoundaryIs"
    );
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/Polygon.js").default} polygon Polygon geometry.
   * @param {Array<*>} objectStack Node stack.
   * @private
   */
  writeSurfacePatches_(node, polygon, objectStack) {
    const child = createElementNS(node.namespaceURI, "PolygonPatch");
    node.appendChild(child);
    this.writeSurfaceOrPolygon(child, polygon, objectStack);
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/LinearRing.js").default} ring LinearRing geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeRing(node, ring, objectStack) {
    const linearRing = createElementNS(node.namespaceURI, "LinearRing");
    node.appendChild(linearRing);
    this.writeLinearRing(linearRing, ring, objectStack);
  }
  /**
   * @param {Array<number>} point Point geometry.
   * @param {string} [srsName] Optional srsName
   * @param {boolean} [hasZ] whether the geometry has a Z coordinate (is 3D) or not.
   * @return {string} The coords string.
   * @private
   */
  getCoords_(point, srsName, hasZ) {
    let axisOrientation = "enu";
    if (srsName) {
      axisOrientation = get(srsName).getAxisOrientation();
    }
    let coords = axisOrientation.substr(0, 2) === "en" ? point[0] + "," + point[1] : point[1] + "," + point[0];
    if (hasZ) {
      const z = point[2] || 0;
      coords += "," + z;
    }
    return coords;
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/Point.js").default} geometry Point geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writePoint(node, geometry, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const hasZ = context["hasZ"];
    const srsName = context["srsName"];
    if (srsName) {
      node.setAttribute("srsName", srsName);
    }
    const coordinates = this.createCoordinatesNode_(node.namespaceURI);
    node.appendChild(coordinates);
    const point = geometry.getCoordinates();
    const coord = this.getCoords_(point, srsName, hasZ);
    writeStringTextNode(coordinates, coord);
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/MultiPoint.js").default} geometry MultiPoint geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeMultiPoint(node, geometry, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const hasZ = context["hasZ"];
    const srsName = context["srsName"];
    if (srsName) {
      node.setAttribute("srsName", srsName);
    }
    const points = geometry.getPoints();
    pushSerializeAndPop(
      { node, hasZ, srsName },
      this.POINTMEMBER_SERIALIZERS,
      makeSimpleNodeFactory("pointMember"),
      points,
      objectStack,
      void 0,
      this
    );
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/Point.js").default} point Point geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writePointMember(node, point, objectStack) {
    const child = createElementNS(node.namespaceURI, "Point");
    node.appendChild(child);
    this.writePoint(child, point, objectStack);
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/LinearRing.js").default} geometry LinearRing geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeLinearRing(node, geometry, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const srsName = context["srsName"];
    if (srsName) {
      node.setAttribute("srsName", srsName);
    }
    const coordinates = this.createCoordinatesNode_(node.namespaceURI);
    node.appendChild(coordinates);
    this.writeCoordinates_(coordinates, geometry, objectStack);
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/MultiPolygon.js").default} geometry MultiPolygon geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeMultiSurfaceOrPolygon(node, geometry, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const hasZ = context["hasZ"];
    const srsName = context["srsName"];
    const surface = context["surface"];
    if (srsName) {
      node.setAttribute("srsName", srsName);
    }
    const polygons = geometry.getPolygons();
    pushSerializeAndPop(
      { node, hasZ, srsName, surface },
      this.SURFACEORPOLYGONMEMBER_SERIALIZERS,
      this.MULTIGEOMETRY_MEMBER_NODE_FACTORY_,
      polygons,
      objectStack,
      void 0,
      this
    );
  }
  /**
   * @param {Node} node Node.
   * @param {import("../geom/Polygon.js").default} polygon Polygon geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeSurfaceOrPolygonMember(node, polygon, objectStack) {
    const child = this.GEOMETRY_NODE_FACTORY_(polygon, objectStack);
    if (child) {
      node.appendChild(child);
      this.writeSurfaceOrPolygon(child, polygon, objectStack);
    }
  }
  /**
   * @param {Element} node Node.
   * @param {import("../extent.js").Extent} extent Extent.
   * @param {Array<*>} objectStack Node stack.
   */
  writeEnvelope(node, extent, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const srsName = context["srsName"];
    if (srsName) {
      node.setAttribute("srsName", srsName);
    }
    const keys = ["lowerCorner", "upperCorner"];
    const values = [extent[0] + " " + extent[1], extent[2] + " " + extent[3]];
    pushSerializeAndPop(
      /** @type {import("../xml.js").NodeStackItem} */
      { node },
      this.ENVELOPE_SERIALIZERS,
      OBJECT_PROPERTY_NODE_FACTORY,
      values,
      objectStack,
      keys,
      this
    );
  }
  /**
   * @const
   * @param {*} value Value.
   * @param {Array<*>} objectStack Object stack.
   * @param {string} [nodeName] Node name.
   * @return {Node|undefined} Node.
   * @private
   */
  MULTIGEOMETRY_MEMBER_NODE_FACTORY_(value, objectStack, nodeName) {
    const parentNode = objectStack[objectStack.length - 1].node;
    return createElementNS(
      "http://www.opengis.net/gml",
      MULTIGEOMETRY_TO_MEMBER_NODENAME[parentNode.nodeName]
    );
  }
};
GML2.prototype.GEOMETRY_FLAT_COORDINATES_PARSERS = {
  "http://www.opengis.net/gml": {
    "coordinates": makeReplacer(GML2.prototype.readFlatCoordinates)
  }
};
GML2.prototype.FLAT_LINEAR_RINGS_PARSERS = {
  "http://www.opengis.net/gml": {
    "innerBoundaryIs": GML2.prototype.innerBoundaryIsParser,
    "outerBoundaryIs": GML2.prototype.outerBoundaryIsParser
  }
};
GML2.prototype.BOX_PARSERS_ = {
  "http://www.opengis.net/gml": {
    "coordinates": makeArrayPusher(GML2.prototype.readFlatCoordinates)
  }
};
GML2.prototype.GEOMETRY_PARSERS = {
  "http://www.opengis.net/gml": {
    "Point": makeReplacer(GMLBase_default.prototype.readPoint),
    "MultiPoint": makeReplacer(GMLBase_default.prototype.readMultiPoint),
    "LineString": makeReplacer(GMLBase_default.prototype.readLineString),
    "MultiLineString": makeReplacer(GMLBase_default.prototype.readMultiLineString),
    "LinearRing": makeReplacer(GMLBase_default.prototype.readLinearRing),
    "Polygon": makeReplacer(GMLBase_default.prototype.readPolygon),
    "MultiPolygon": makeReplacer(GMLBase_default.prototype.readMultiPolygon),
    "Box": makeReplacer(GML2.prototype.readBox)
  }
};
GML2.prototype.GEOMETRY_SERIALIZERS = {
  "http://www.opengis.net/gml": {
    "Curve": makeChildAppender(GML2.prototype.writeCurveOrLineString),
    "MultiCurve": makeChildAppender(GML2.prototype.writeMultiCurveOrLineString),
    "Point": makeChildAppender(GML2.prototype.writePoint),
    "MultiPoint": makeChildAppender(GML2.prototype.writeMultiPoint),
    "LineString": makeChildAppender(GML2.prototype.writeCurveOrLineString),
    "MultiLineString": makeChildAppender(
      GML2.prototype.writeMultiCurveOrLineString
    ),
    "LinearRing": makeChildAppender(GML2.prototype.writeLinearRing),
    "Polygon": makeChildAppender(GML2.prototype.writeSurfaceOrPolygon),
    "MultiPolygon": makeChildAppender(
      GML2.prototype.writeMultiSurfaceOrPolygon
    ),
    "Surface": makeChildAppender(GML2.prototype.writeSurfaceOrPolygon),
    "MultiSurface": makeChildAppender(
      GML2.prototype.writeMultiSurfaceOrPolygon
    ),
    "Envelope": makeChildAppender(GML2.prototype.writeEnvelope)
  }
};
GML2.prototype.LINESTRINGORCURVEMEMBER_SERIALIZERS = {
  "http://www.opengis.net/gml": {
    "lineStringMember": makeChildAppender(
      GML2.prototype.writeLineStringOrCurveMember
    ),
    "curveMember": makeChildAppender(
      GML2.prototype.writeLineStringOrCurveMember
    )
  }
};
GML2.prototype.RING_SERIALIZERS = {
  "http://www.opengis.net/gml": {
    "outerBoundaryIs": makeChildAppender(GML2.prototype.writeRing),
    "innerBoundaryIs": makeChildAppender(GML2.prototype.writeRing)
  }
};
GML2.prototype.POINTMEMBER_SERIALIZERS = {
  "http://www.opengis.net/gml": {
    "pointMember": makeChildAppender(GML2.prototype.writePointMember)
  }
};
GML2.prototype.SURFACEORPOLYGONMEMBER_SERIALIZERS = {
  "http://www.opengis.net/gml": {
    "surfaceMember": makeChildAppender(
      GML2.prototype.writeSurfaceOrPolygonMember
    ),
    "polygonMember": makeChildAppender(
      GML2.prototype.writeSurfaceOrPolygonMember
    )
  }
};
GML2.prototype.ENVELOPE_SERIALIZERS = {
  "http://www.opengis.net/gml": {
    "lowerCorner": makeChildAppender(writeStringTextNode),
    "upperCorner": makeChildAppender(writeStringTextNode)
  }
};
var GML2_default = GML2;

// node_modules/ol/format/GML3.js
var schemaLocation2 = GMLNS + " http://schemas.opengis.net/gml/3.1.1/profiles/gmlsfProfile/1.0.0/gmlsf.xsd";
var MULTIGEOMETRY_TO_MEMBER_NODENAME2 = {
  "MultiLineString": "lineStringMember",
  "MultiCurve": "curveMember",
  "MultiPolygon": "polygonMember",
  "MultiSurface": "surfaceMember"
};
var GML3 = class extends GMLBase_default {
  /**
   * @param {import("./GMLBase.js").Options} [options] Optional configuration object.
   */
  constructor(options) {
    options = options ? options : {};
    super(options);
    this.surface_ = options.surface !== void 0 ? options.surface : false;
    this.curve_ = options.curve !== void 0 ? options.curve : false;
    this.multiCurve_ = options.multiCurve !== void 0 ? options.multiCurve : true;
    this.multiSurface_ = options.multiSurface !== void 0 ? options.multiSurface : true;
    this.schemaLocation = options.schemaLocation ? options.schemaLocation : schemaLocation2;
    this.hasZ = options.hasZ !== void 0 ? options.hasZ : false;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {MultiLineString|undefined} MultiLineString.
   */
  readMultiCurve(node, objectStack) {
    const lineStrings = pushParseAndPop(
      [],
      this.MULTICURVE_PARSERS,
      node,
      objectStack,
      this
    );
    if (lineStrings) {
      const multiLineString = new MultiLineString_default(lineStrings);
      return multiLineString;
    }
    return void 0;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Array<number>|undefined} Polygon.
   */
  readFlatCurveRing(node, objectStack) {
    const lineStrings = pushParseAndPop(
      [],
      this.MULTICURVE_PARSERS,
      node,
      objectStack,
      this
    );
    const flatCoordinates = [];
    for (let i = 0, ii = lineStrings.length; i < ii; ++i) {
      extend(flatCoordinates, lineStrings[i].getFlatCoordinates());
    }
    return flatCoordinates;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {MultiPolygon|undefined} MultiPolygon.
   */
  readMultiSurface(node, objectStack) {
    const polygons = pushParseAndPop(
      [],
      this.MULTISURFACE_PARSERS,
      node,
      objectStack,
      this
    );
    if (polygons) {
      return new MultiPolygon_default(polygons);
    }
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   */
  curveMemberParser(node, objectStack) {
    parseNode(this.CURVEMEMBER_PARSERS, node, objectStack, this);
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   */
  surfaceMemberParser(node, objectStack) {
    parseNode(this.SURFACEMEMBER_PARSERS, node, objectStack, this);
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Array<(Array<number>)>|undefined} flat coordinates.
   */
  readPatch(node, objectStack) {
    return pushParseAndPop(
      [null],
      this.PATCHES_PARSERS,
      node,
      objectStack,
      this
    );
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Array<number>|undefined} flat coordinates.
   */
  readSegment(node, objectStack) {
    return pushParseAndPop([], this.SEGMENTS_PARSERS, node, objectStack, this);
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Array<(Array<number>)>|undefined} flat coordinates.
   */
  readPolygonPatch(node, objectStack) {
    return pushParseAndPop(
      [null],
      this.FLAT_LINEAR_RINGS_PARSERS,
      node,
      objectStack,
      this
    );
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Array<number>|undefined} flat coordinates.
   */
  readLineStringSegment(node, objectStack) {
    return pushParseAndPop(
      [null],
      this.GEOMETRY_FLAT_COORDINATES_PARSERS,
      node,
      objectStack,
      this
    );
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   */
  interiorParser(node, objectStack) {
    const flatLinearRing = pushParseAndPop(
      void 0,
      this.RING_PARSERS,
      node,
      objectStack,
      this
    );
    if (flatLinearRing) {
      const flatLinearRings = (
        /** @type {Array<Array<number>>} */
        objectStack[objectStack.length - 1]
      );
      flatLinearRings.push(flatLinearRing);
    }
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   */
  exteriorParser(node, objectStack) {
    const flatLinearRing = pushParseAndPop(
      void 0,
      this.RING_PARSERS,
      node,
      objectStack,
      this
    );
    if (flatLinearRing) {
      const flatLinearRings = (
        /** @type {Array<Array<number>>} */
        objectStack[objectStack.length - 1]
      );
      flatLinearRings[0] = flatLinearRing;
    }
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Polygon|undefined} Polygon.
   */
  readSurface(node, objectStack) {
    const flatLinearRings = pushParseAndPop(
      [null],
      this.SURFACE_PARSERS,
      node,
      objectStack,
      this
    );
    if (flatLinearRings && flatLinearRings[0]) {
      const flatCoordinates = flatLinearRings[0];
      const ends = [flatCoordinates.length];
      let i, ii;
      for (i = 1, ii = flatLinearRings.length; i < ii; ++i) {
        extend(flatCoordinates, flatLinearRings[i]);
        ends.push(flatCoordinates.length);
      }
      return new Polygon_default(flatCoordinates, "XYZ", ends);
    }
    return void 0;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {LineString|undefined} LineString.
   */
  readCurve(node, objectStack) {
    const flatCoordinates = pushParseAndPop(
      [null],
      this.CURVE_PARSERS,
      node,
      objectStack,
      this
    );
    if (flatCoordinates) {
      const lineString = new LineString_default(flatCoordinates, "XYZ");
      return lineString;
    }
    return void 0;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {import("../extent.js").Extent|undefined} Envelope.
   */
  readEnvelope(node, objectStack) {
    const flatCoordinates = pushParseAndPop(
      [null],
      this.ENVELOPE_PARSERS,
      node,
      objectStack,
      this
    );
    return createOrUpdate(
      flatCoordinates[1][0],
      flatCoordinates[1][1],
      flatCoordinates[2][0],
      flatCoordinates[2][1]
    );
  }
  /**
   * @param {Node} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Array<number>|undefined} Flat coordinates.
   */
  readFlatPos(node, objectStack) {
    let s = getAllTextContent(node, false);
    const re = /^\s*([+\-]?\d*\.?\d+(?:[eE][+\-]?\d+)?)\s*/;
    const flatCoordinates = [];
    let m;
    while (m = re.exec(s)) {
      flatCoordinates.push(parseFloat(m[1]));
      s = s.substr(m[0].length);
    }
    if (s !== "") {
      return void 0;
    }
    const context = objectStack[0];
    const containerSrs = context["srsName"];
    let axisOrientation = "enu";
    if (containerSrs) {
      const proj = get(containerSrs);
      axisOrientation = proj.getAxisOrientation();
    }
    if (axisOrientation === "neu") {
      let i, ii;
      for (i = 0, ii = flatCoordinates.length; i < ii; i += 3) {
        const y = flatCoordinates[i];
        const x = flatCoordinates[i + 1];
        flatCoordinates[i] = x;
        flatCoordinates[i + 1] = y;
      }
    }
    const len = flatCoordinates.length;
    if (len == 2) {
      flatCoordinates.push(0);
    }
    if (len === 0) {
      return void 0;
    }
    return flatCoordinates;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Array<number>|undefined} Flat coordinates.
   */
  readFlatPosList(node, objectStack) {
    const s = getAllTextContent(node, false).replace(/^\s*|\s*$/g, "");
    const context = objectStack[0];
    const containerSrs = context["srsName"];
    const contextDimension = context["srsDimension"];
    let axisOrientation = "enu";
    if (containerSrs) {
      const proj = get(containerSrs);
      axisOrientation = proj.getAxisOrientation();
    }
    const coords = s.split(/\s+/);
    let dim = 2;
    if (node.getAttribute("srsDimension")) {
      dim = readNonNegativeIntegerString(node.getAttribute("srsDimension"));
    } else if (node.getAttribute("dimension")) {
      dim = readNonNegativeIntegerString(node.getAttribute("dimension"));
    } else if (
      /** @type {Element} */
      node.parentNode.getAttribute("srsDimension")
    ) {
      dim = readNonNegativeIntegerString(
        /** @type {Element} */
        node.parentNode.getAttribute("srsDimension")
      );
    } else if (contextDimension) {
      dim = readNonNegativeIntegerString(contextDimension);
    }
    let x, y, z;
    const flatCoordinates = [];
    for (let i = 0, ii = coords.length; i < ii; i += dim) {
      x = parseFloat(coords[i]);
      y = parseFloat(coords[i + 1]);
      z = dim === 3 ? parseFloat(coords[i + 2]) : 0;
      if (axisOrientation.substr(0, 2) === "en") {
        flatCoordinates.push(x, y, z);
      } else {
        flatCoordinates.push(y, x, z);
      }
    }
    return flatCoordinates;
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/Point.js").default} value Point geometry.
   * @param {Array<*>} objectStack Node stack.
   * @private
   */
  writePos_(node, value, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const hasZ = context["hasZ"];
    const srsDimension = hasZ ? "3" : "2";
    node.setAttribute("srsDimension", srsDimension);
    const srsName = context["srsName"];
    let axisOrientation = "enu";
    if (srsName) {
      axisOrientation = get(srsName).getAxisOrientation();
    }
    const point = value.getCoordinates();
    let coords;
    if (axisOrientation.substr(0, 2) === "en") {
      coords = point[0] + " " + point[1];
    } else {
      coords = point[1] + " " + point[0];
    }
    if (hasZ) {
      const z = point[2] || 0;
      coords += " " + z;
    }
    writeStringTextNode(node, coords);
  }
  /**
   * @param {Array<number>} point Point geometry.
   * @param {string} [srsName] Optional srsName
   * @param {boolean} [hasZ] whether the geometry has a Z coordinate (is 3D) or not.
   * @return {string} The coords string.
   * @private
   */
  getCoords_(point, srsName, hasZ) {
    let axisOrientation = "enu";
    if (srsName) {
      axisOrientation = get(srsName).getAxisOrientation();
    }
    let coords = axisOrientation.substr(0, 2) === "en" ? point[0] + " " + point[1] : point[1] + " " + point[0];
    if (hasZ) {
      const z = point[2] || 0;
      coords += " " + z;
    }
    return coords;
  }
  /**
   * @param {Element} node Node.
   * @param {LineString|import("../geom/LinearRing.js").default} value Geometry.
   * @param {Array<*>} objectStack Node stack.
   * @private
   */
  writePosList_(node, value, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const hasZ = context["hasZ"];
    const srsDimension = hasZ ? "3" : "2";
    node.setAttribute("srsDimension", srsDimension);
    const srsName = context["srsName"];
    const points = value.getCoordinates();
    const len = points.length;
    const parts = new Array(len);
    let point;
    for (let i = 0; i < len; ++i) {
      point = points[i];
      parts[i] = this.getCoords_(point, srsName, hasZ);
    }
    writeStringTextNode(node, parts.join(" "));
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/Point.js").default} geometry Point geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writePoint(node, geometry, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const srsName = context["srsName"];
    if (srsName) {
      node.setAttribute("srsName", srsName);
    }
    const pos = createElementNS(node.namespaceURI, "pos");
    node.appendChild(pos);
    this.writePos_(pos, geometry, objectStack);
  }
  /**
   * @param {Element} node Node.
   * @param {import("../extent.js").Extent} extent Extent.
   * @param {Array<*>} objectStack Node stack.
   */
  writeEnvelope(node, extent, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const srsName = context["srsName"];
    if (srsName) {
      node.setAttribute("srsName", srsName);
    }
    const keys = ["lowerCorner", "upperCorner"];
    const values = [extent[0] + " " + extent[1], extent[2] + " " + extent[3]];
    pushSerializeAndPop(
      /** @type {import("../xml.js").NodeStackItem} */
      { node },
      this.ENVELOPE_SERIALIZERS,
      OBJECT_PROPERTY_NODE_FACTORY,
      values,
      objectStack,
      keys,
      this
    );
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/LinearRing.js").default} geometry LinearRing geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeLinearRing(node, geometry, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const srsName = context["srsName"];
    if (srsName) {
      node.setAttribute("srsName", srsName);
    }
    const posList = createElementNS(node.namespaceURI, "posList");
    node.appendChild(posList);
    this.writePosList_(posList, geometry, objectStack);
  }
  /**
   * @param {*} value Value.
   * @param {Array<*>} objectStack Object stack.
   * @param {string} [nodeName] Node name.
   * @return {Node} Node.
   * @private
   */
  RING_NODE_FACTORY_(value, objectStack, nodeName) {
    const context = objectStack[objectStack.length - 1];
    const parentNode = context.node;
    const exteriorWritten = context["exteriorWritten"];
    if (exteriorWritten === void 0) {
      context["exteriorWritten"] = true;
    }
    return createElementNS(
      parentNode.namespaceURI,
      exteriorWritten !== void 0 ? "interior" : "exterior"
    );
  }
  /**
   * @param {Element} node Node.
   * @param {Polygon} geometry Polygon geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeSurfaceOrPolygon(node, geometry, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const hasZ = context["hasZ"];
    const srsName = context["srsName"];
    if (node.nodeName !== "PolygonPatch" && srsName) {
      node.setAttribute("srsName", srsName);
    }
    if (node.nodeName === "Polygon" || node.nodeName === "PolygonPatch") {
      const rings = geometry.getLinearRings();
      pushSerializeAndPop(
        { node, hasZ, srsName },
        this.RING_SERIALIZERS,
        this.RING_NODE_FACTORY_,
        rings,
        objectStack,
        void 0,
        this
      );
    } else if (node.nodeName === "Surface") {
      const patches = createElementNS(node.namespaceURI, "patches");
      node.appendChild(patches);
      this.writeSurfacePatches_(patches, geometry, objectStack);
    }
  }
  /**
   * @param {Element} node Node.
   * @param {LineString} geometry LineString geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeCurveOrLineString(node, geometry, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const srsName = context["srsName"];
    if (node.nodeName !== "LineStringSegment" && srsName) {
      node.setAttribute("srsName", srsName);
    }
    if (node.nodeName === "LineString" || node.nodeName === "LineStringSegment") {
      const posList = createElementNS(node.namespaceURI, "posList");
      node.appendChild(posList);
      this.writePosList_(posList, geometry, objectStack);
    } else if (node.nodeName === "Curve") {
      const segments = createElementNS(node.namespaceURI, "segments");
      node.appendChild(segments);
      this.writeCurveSegments_(segments, geometry, objectStack);
    }
  }
  /**
   * @param {Element} node Node.
   * @param {MultiPolygon} geometry MultiPolygon geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeMultiSurfaceOrPolygon(node, geometry, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const hasZ = context["hasZ"];
    const srsName = context["srsName"];
    const surface = context["surface"];
    if (srsName) {
      node.setAttribute("srsName", srsName);
    }
    const polygons = geometry.getPolygons();
    pushSerializeAndPop(
      { node, hasZ, srsName, surface },
      this.SURFACEORPOLYGONMEMBER_SERIALIZERS,
      this.MULTIGEOMETRY_MEMBER_NODE_FACTORY_,
      polygons,
      objectStack,
      void 0,
      this
    );
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/MultiPoint.js").default} geometry MultiPoint geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeMultiPoint(node, geometry, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const srsName = context["srsName"];
    const hasZ = context["hasZ"];
    if (srsName) {
      node.setAttribute("srsName", srsName);
    }
    const points = geometry.getPoints();
    pushSerializeAndPop(
      { node, hasZ, srsName },
      this.POINTMEMBER_SERIALIZERS,
      makeSimpleNodeFactory("pointMember"),
      points,
      objectStack,
      void 0,
      this
    );
  }
  /**
   * @param {Element} node Node.
   * @param {MultiLineString} geometry MultiLineString geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeMultiCurveOrLineString(node, geometry, objectStack) {
    const context = objectStack[objectStack.length - 1];
    const hasZ = context["hasZ"];
    const srsName = context["srsName"];
    const curve = context["curve"];
    if (srsName) {
      node.setAttribute("srsName", srsName);
    }
    const lines = geometry.getLineStrings();
    pushSerializeAndPop(
      { node, hasZ, srsName, curve },
      this.LINESTRINGORCURVEMEMBER_SERIALIZERS,
      this.MULTIGEOMETRY_MEMBER_NODE_FACTORY_,
      lines,
      objectStack,
      void 0,
      this
    );
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/LinearRing.js").default} ring LinearRing geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeRing(node, ring, objectStack) {
    const linearRing = createElementNS(node.namespaceURI, "LinearRing");
    node.appendChild(linearRing);
    this.writeLinearRing(linearRing, ring, objectStack);
  }
  /**
   * @param {Node} node Node.
   * @param {Polygon} polygon Polygon geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeSurfaceOrPolygonMember(node, polygon, objectStack) {
    const child = this.GEOMETRY_NODE_FACTORY_(polygon, objectStack);
    if (child) {
      node.appendChild(child);
      this.writeSurfaceOrPolygon(child, polygon, objectStack);
    }
  }
  /**
   * @param {Element} node Node.
   * @param {import("../geom/Point.js").default} point Point geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writePointMember(node, point, objectStack) {
    const child = createElementNS(node.namespaceURI, "Point");
    node.appendChild(child);
    this.writePoint(child, point, objectStack);
  }
  /**
   * @param {Node} node Node.
   * @param {LineString} line LineString geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeLineStringOrCurveMember(node, line, objectStack) {
    const child = this.GEOMETRY_NODE_FACTORY_(line, objectStack);
    if (child) {
      node.appendChild(child);
      this.writeCurveOrLineString(child, line, objectStack);
    }
  }
  /**
   * @param {Element} node Node.
   * @param {Polygon} polygon Polygon geometry.
   * @param {Array<*>} objectStack Node stack.
   * @private
   */
  writeSurfacePatches_(node, polygon, objectStack) {
    const child = createElementNS(node.namespaceURI, "PolygonPatch");
    node.appendChild(child);
    this.writeSurfaceOrPolygon(child, polygon, objectStack);
  }
  /**
   * @param {Element} node Node.
   * @param {LineString} line LineString geometry.
   * @param {Array<*>} objectStack Node stack.
   * @private
   */
  writeCurveSegments_(node, line, objectStack) {
    const child = createElementNS(node.namespaceURI, "LineStringSegment");
    node.appendChild(child);
    this.writeCurveOrLineString(child, line, objectStack);
  }
  /**
   * @param {Node} node Node.
   * @param {import("../geom/Geometry.js").default|import("../extent.js").Extent} geometry Geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeGeometryElement(node, geometry, objectStack) {
    const context = (
      /** @type {import("./Feature.js").WriteOptions} */
      objectStack[objectStack.length - 1]
    );
    const item = Object.assign({}, context);
    item["node"] = node;
    let value;
    if (Array.isArray(geometry)) {
      value = transformExtentWithOptions(
        /** @type {import("../extent.js").Extent} */
        geometry,
        context
      );
    } else {
      value = transformGeometryWithOptions(
        /** @type {import("../geom/Geometry.js").default} */
        geometry,
        true,
        context
      );
    }
    pushSerializeAndPop(
      /** @type {import("../xml.js").NodeStackItem} */
      item,
      this.GEOMETRY_SERIALIZERS,
      this.GEOMETRY_NODE_FACTORY_,
      [value],
      objectStack,
      void 0,
      this
    );
  }
  /**
   * @param {Element} node Node.
   * @param {import("../Feature.js").default} feature Feature.
   * @param {Array<*>} objectStack Node stack.
   */
  writeFeatureElement(node, feature, objectStack) {
    const fid = feature.getId();
    if (fid) {
      node.setAttribute(
        "fid",
        /** @type {string} */
        fid
      );
    }
    const context = (
      /** @type {Object} */
      objectStack[objectStack.length - 1]
    );
    const featureNS = context["featureNS"];
    const geometryName = feature.getGeometryName();
    if (!context.serializers) {
      context.serializers = {};
      context.serializers[featureNS] = {};
    }
    const keys = [];
    const values = [];
    if (feature.hasProperties()) {
      const properties = feature.getProperties();
      for (const key in properties) {
        const value = properties[key];
        if (value !== null) {
          keys.push(key);
          values.push(value);
          if (key == geometryName || typeof /** @type {?} */
          value.getSimplifiedGeometry === "function") {
            if (!(key in context.serializers[featureNS])) {
              context.serializers[featureNS][key] = makeChildAppender(
                this.writeGeometryElement,
                this
              );
            }
          } else {
            if (!(key in context.serializers[featureNS])) {
              context.serializers[featureNS][key] = makeChildAppender(writeStringTextNode);
            }
          }
        }
      }
    }
    const item = Object.assign({}, context);
    item.node = node;
    pushSerializeAndPop(
      /** @type {import("../xml.js").NodeStackItem} */
      item,
      context.serializers,
      makeSimpleNodeFactory(void 0, featureNS),
      values,
      objectStack,
      keys
    );
  }
  /**
   * @param {Node} node Node.
   * @param {Array<import("../Feature.js").default>} features Features.
   * @param {Array<*>} objectStack Node stack.
   * @private
   */
  writeFeatureMembers_(node, features, objectStack) {
    const context = (
      /** @type {Object} */
      objectStack[objectStack.length - 1]
    );
    const featureType = context["featureType"];
    const featureNS = context["featureNS"];
    const serializers = {};
    serializers[featureNS] = {};
    serializers[featureNS][featureType] = makeChildAppender(
      this.writeFeatureElement,
      this
    );
    const item = Object.assign({}, context);
    item.node = node;
    pushSerializeAndPop(
      /** @type {import("../xml.js").NodeStackItem} */
      item,
      serializers,
      makeSimpleNodeFactory(featureType, featureNS),
      features,
      objectStack
    );
  }
  /**
   * @const
   * @param {*} value Value.
   * @param {Array<*>} objectStack Object stack.
   * @param {string} [nodeName] Node name.
   * @return {Node|undefined} Node.
   * @private
   */
  MULTIGEOMETRY_MEMBER_NODE_FACTORY_(value, objectStack, nodeName) {
    const parentNode = objectStack[objectStack.length - 1].node;
    return createElementNS(
      this.namespace,
      MULTIGEOMETRY_TO_MEMBER_NODENAME2[parentNode.nodeName]
    );
  }
  /**
   * @const
   * @param {*} value Value.
   * @param {Array<*>} objectStack Object stack.
   * @param {string} [nodeName] Node name.
   * @return {Element|undefined} Node.
   * @private
   */
  GEOMETRY_NODE_FACTORY_(value, objectStack, nodeName) {
    const context = objectStack[objectStack.length - 1];
    const multiSurface = context["multiSurface"];
    const surface = context["surface"];
    const curve = context["curve"];
    const multiCurve = context["multiCurve"];
    if (!Array.isArray(value)) {
      nodeName = /** @type {import("../geom/Geometry.js").default} */
      value.getType();
      if (nodeName === "MultiPolygon" && multiSurface === true) {
        nodeName = "MultiSurface";
      } else if (nodeName === "Polygon" && surface === true) {
        nodeName = "Surface";
      } else if (nodeName === "LineString" && curve === true) {
        nodeName = "Curve";
      } else if (nodeName === "MultiLineString" && multiCurve === true) {
        nodeName = "MultiCurve";
      }
    } else {
      nodeName = "Envelope";
    }
    return createElementNS(this.namespace, nodeName);
  }
  /**
   * Encode a geometry in GML 3.1.1 Simple Features.
   *
   * @param {import("../geom/Geometry.js").default} geometry Geometry.
   * @param {import("./Feature.js").WriteOptions} [options] Options.
   * @return {Node} Node.
   * @api
   */
  writeGeometryNode(geometry, options) {
    options = this.adaptOptions(options);
    const geom = createElementNS(this.namespace, "geom");
    const context = {
      node: geom,
      hasZ: this.hasZ,
      srsName: this.srsName,
      curve: this.curve_,
      surface: this.surface_,
      multiSurface: this.multiSurface_,
      multiCurve: this.multiCurve_
    };
    if (options) {
      Object.assign(context, options);
    }
    this.writeGeometryElement(geom, geometry, [context]);
    return geom;
  }
  /**
   * Encode an array of features in the GML 3.1.1 format as an XML node.
   *
   * @param {Array<import("../Feature.js").default>} features Features.
   * @param {import("./Feature.js").WriteOptions} [options] Options.
   * @return {Element} Node.
   * @api
   */
  writeFeaturesNode(features, options) {
    options = this.adaptOptions(options);
    const node = createElementNS(this.namespace, "featureMembers");
    node.setAttributeNS(
      XML_SCHEMA_INSTANCE_URI,
      "xsi:schemaLocation",
      this.schemaLocation
    );
    const context = {
      srsName: this.srsName,
      hasZ: this.hasZ,
      curve: this.curve_,
      surface: this.surface_,
      multiSurface: this.multiSurface_,
      multiCurve: this.multiCurve_,
      featureNS: this.featureNS,
      featureType: this.featureType
    };
    if (options) {
      Object.assign(context, options);
    }
    this.writeFeatureMembers_(node, features, [context]);
    return node;
  }
};
GML3.prototype.GEOMETRY_FLAT_COORDINATES_PARSERS = {
  "http://www.opengis.net/gml": {
    "pos": makeReplacer(GML3.prototype.readFlatPos),
    "posList": makeReplacer(GML3.prototype.readFlatPosList),
    "coordinates": makeReplacer(GML2_default.prototype.readFlatCoordinates)
  }
};
GML3.prototype.FLAT_LINEAR_RINGS_PARSERS = {
  "http://www.opengis.net/gml": {
    "interior": GML3.prototype.interiorParser,
    "exterior": GML3.prototype.exteriorParser
  }
};
GML3.prototype.GEOMETRY_PARSERS = {
  "http://www.opengis.net/gml": {
    "Point": makeReplacer(GMLBase_default.prototype.readPoint),
    "MultiPoint": makeReplacer(GMLBase_default.prototype.readMultiPoint),
    "LineString": makeReplacer(GMLBase_default.prototype.readLineString),
    "MultiLineString": makeReplacer(GMLBase_default.prototype.readMultiLineString),
    "LinearRing": makeReplacer(GMLBase_default.prototype.readLinearRing),
    "Polygon": makeReplacer(GMLBase_default.prototype.readPolygon),
    "MultiPolygon": makeReplacer(GMLBase_default.prototype.readMultiPolygon),
    "Surface": makeReplacer(GML3.prototype.readSurface),
    "MultiSurface": makeReplacer(GML3.prototype.readMultiSurface),
    "Curve": makeReplacer(GML3.prototype.readCurve),
    "MultiCurve": makeReplacer(GML3.prototype.readMultiCurve),
    "Envelope": makeReplacer(GML3.prototype.readEnvelope)
  }
};
GML3.prototype.MULTICURVE_PARSERS = {
  "http://www.opengis.net/gml": {
    "curveMember": makeArrayPusher(GML3.prototype.curveMemberParser),
    "curveMembers": makeArrayPusher(GML3.prototype.curveMemberParser)
  }
};
GML3.prototype.MULTISURFACE_PARSERS = {
  "http://www.opengis.net/gml": {
    "surfaceMember": makeArrayPusher(GML3.prototype.surfaceMemberParser),
    "surfaceMembers": makeArrayPusher(GML3.prototype.surfaceMemberParser)
  }
};
GML3.prototype.CURVEMEMBER_PARSERS = {
  "http://www.opengis.net/gml": {
    "LineString": makeArrayPusher(GMLBase_default.prototype.readLineString),
    "Curve": makeArrayPusher(GML3.prototype.readCurve)
  }
};
GML3.prototype.SURFACEMEMBER_PARSERS = {
  "http://www.opengis.net/gml": {
    "Polygon": makeArrayPusher(GMLBase_default.prototype.readPolygon),
    "Surface": makeArrayPusher(GML3.prototype.readSurface)
  }
};
GML3.prototype.SURFACE_PARSERS = {
  "http://www.opengis.net/gml": {
    "patches": makeReplacer(GML3.prototype.readPatch)
  }
};
GML3.prototype.CURVE_PARSERS = {
  "http://www.opengis.net/gml": {
    "segments": makeReplacer(GML3.prototype.readSegment)
  }
};
GML3.prototype.ENVELOPE_PARSERS = {
  "http://www.opengis.net/gml": {
    "lowerCorner": makeArrayPusher(GML3.prototype.readFlatPosList),
    "upperCorner": makeArrayPusher(GML3.prototype.readFlatPosList)
  }
};
GML3.prototype.PATCHES_PARSERS = {
  "http://www.opengis.net/gml": {
    "PolygonPatch": makeReplacer(GML3.prototype.readPolygonPatch)
  }
};
GML3.prototype.SEGMENTS_PARSERS = {
  "http://www.opengis.net/gml": {
    "LineStringSegment": makeArrayExtender(
      GML3.prototype.readLineStringSegment
    )
  }
};
GMLBase_default.prototype.RING_PARSERS = {
  "http://www.opengis.net/gml": {
    "LinearRing": makeReplacer(GMLBase_default.prototype.readFlatLinearRing),
    "Ring": makeReplacer(GML3.prototype.readFlatCurveRing)
  }
};
GML3.prototype.writeFeatures;
GML3.prototype.RING_SERIALIZERS = {
  "http://www.opengis.net/gml": {
    "exterior": makeChildAppender(GML3.prototype.writeRing),
    "interior": makeChildAppender(GML3.prototype.writeRing)
  }
};
GML3.prototype.ENVELOPE_SERIALIZERS = {
  "http://www.opengis.net/gml": {
    "lowerCorner": makeChildAppender(writeStringTextNode),
    "upperCorner": makeChildAppender(writeStringTextNode)
  }
};
GML3.prototype.SURFACEORPOLYGONMEMBER_SERIALIZERS = {
  "http://www.opengis.net/gml": {
    "surfaceMember": makeChildAppender(
      GML3.prototype.writeSurfaceOrPolygonMember
    ),
    "polygonMember": makeChildAppender(
      GML3.prototype.writeSurfaceOrPolygonMember
    )
  }
};
GML3.prototype.POINTMEMBER_SERIALIZERS = {
  "http://www.opengis.net/gml": {
    "pointMember": makeChildAppender(GML3.prototype.writePointMember)
  }
};
GML3.prototype.LINESTRINGORCURVEMEMBER_SERIALIZERS = {
  "http://www.opengis.net/gml": {
    "lineStringMember": makeChildAppender(
      GML3.prototype.writeLineStringOrCurveMember
    ),
    "curveMember": makeChildAppender(
      GML3.prototype.writeLineStringOrCurveMember
    )
  }
};
GML3.prototype.GEOMETRY_SERIALIZERS = {
  "http://www.opengis.net/gml": {
    "Curve": makeChildAppender(GML3.prototype.writeCurveOrLineString),
    "MultiCurve": makeChildAppender(GML3.prototype.writeMultiCurveOrLineString),
    "Point": makeChildAppender(GML3.prototype.writePoint),
    "MultiPoint": makeChildAppender(GML3.prototype.writeMultiPoint),
    "LineString": makeChildAppender(GML3.prototype.writeCurveOrLineString),
    "MultiLineString": makeChildAppender(
      GML3.prototype.writeMultiCurveOrLineString
    ),
    "LinearRing": makeChildAppender(GML3.prototype.writeLinearRing),
    "Polygon": makeChildAppender(GML3.prototype.writeSurfaceOrPolygon),
    "MultiPolygon": makeChildAppender(
      GML3.prototype.writeMultiSurfaceOrPolygon
    ),
    "Surface": makeChildAppender(GML3.prototype.writeSurfaceOrPolygon),
    "MultiSurface": makeChildAppender(
      GML3.prototype.writeMultiSurfaceOrPolygon
    ),
    "Envelope": makeChildAppender(GML3.prototype.writeEnvelope)
  }
};
var GML3_default = GML3;

// node_modules/ol/format/GML.js
var GML = GML3_default;
GML.prototype.writeFeatures;
GML.prototype.writeFeaturesNode;
var GML_default = GML;

// node_modules/ol/format/GPX.js
var NAMESPACE_URIS = [
  null,
  "http://www.topografix.com/GPX/1/0",
  "http://www.topografix.com/GPX/1/1"
];
var SCHEMA_LOCATION = "http://www.topografix.com/GPX/1/1 http://www.topografix.com/GPX/1/1/gpx.xsd";
var FEATURE_READER = {
  "rte": readRte,
  "trk": readTrk,
  "wpt": readWpt
};
var GPX_PARSERS = makeStructureNS(NAMESPACE_URIS, {
  "rte": makeArrayPusher(readRte),
  "trk": makeArrayPusher(readTrk),
  "wpt": makeArrayPusher(readWpt)
});
var LINK_PARSERS = makeStructureNS(NAMESPACE_URIS, {
  "text": makeObjectPropertySetter(readString, "linkText"),
  "type": makeObjectPropertySetter(readString, "linkType")
});
var GPX_SERIALIZERS = makeStructureNS(NAMESPACE_URIS, {
  "rte": makeChildAppender(writeRte),
  "trk": makeChildAppender(writeTrk),
  "wpt": makeChildAppender(writeWpt)
});
var GPX = class extends XMLFeature_default {
  /**
   * @param {Options} [options] Options.
   */
  constructor(options) {
    super();
    options = options ? options : {};
    this.dataProjection = get("EPSG:4326");
    this.readExtensions_ = options.readExtensions;
  }
  /**
   * @param {Array<Feature>} features List of features.
   * @private
   */
  handleReadExtensions_(features) {
    if (!features) {
      features = [];
    }
    for (let i = 0, ii = features.length; i < ii; ++i) {
      const feature = features[i];
      if (this.readExtensions_) {
        const extensionsNode = feature.get("extensionsNode_") || null;
        this.readExtensions_(feature, extensionsNode);
      }
      feature.set("extensionsNode_", void 0);
    }
  }
  /**
   * @param {Element} node Node.
   * @param {import("./Feature.js").ReadOptions} [options] Options.
   * @return {import("../Feature.js").default} Feature.
   */
  readFeatureFromNode(node, options) {
    if (!NAMESPACE_URIS.includes(node.namespaceURI)) {
      return null;
    }
    const featureReader = FEATURE_READER[node.localName];
    if (!featureReader) {
      return null;
    }
    const feature = featureReader(node, [this.getReadOptions(node, options)]);
    if (!feature) {
      return null;
    }
    this.handleReadExtensions_([feature]);
    return feature;
  }
  /**
   * @param {Element} node Node.
   * @param {import("./Feature.js").ReadOptions} [options] Options.
   * @return {Array<import("../Feature.js").default>} Features.
   */
  readFeaturesFromNode(node, options) {
    if (!NAMESPACE_URIS.includes(node.namespaceURI)) {
      return [];
    }
    if (node.localName == "gpx") {
      const features = pushParseAndPop([], GPX_PARSERS, node, [
        this.getReadOptions(node, options)
      ]);
      if (features) {
        this.handleReadExtensions_(features);
        return features;
      }
      return [];
    }
    return [];
  }
  /**
   * Encode an array of features in the GPX format as an XML node.
   * LineString geometries are output as routes (`<rte>`), and MultiLineString
   * as tracks (`<trk>`).
   *
   * @param {Array<Feature>} features Features.
   * @param {import("./Feature.js").WriteOptions} [options] Options.
   * @return {Node} Node.
   * @api
   */
  writeFeaturesNode(features, options) {
    options = this.adaptOptions(options);
    const gpx = createElementNS("http://www.topografix.com/GPX/1/1", "gpx");
    const xmlnsUri = "http://www.w3.org/2000/xmlns/";
    gpx.setAttributeNS(xmlnsUri, "xmlns:xsi", XML_SCHEMA_INSTANCE_URI);
    gpx.setAttributeNS(
      XML_SCHEMA_INSTANCE_URI,
      "xsi:schemaLocation",
      SCHEMA_LOCATION
    );
    gpx.setAttribute("version", "1.1");
    gpx.setAttribute("creator", "OpenLayers");
    pushSerializeAndPop(
      /** @type {import("../xml.js").NodeStackItem} */
      { node: gpx },
      GPX_SERIALIZERS,
      GPX_NODE_FACTORY,
      features,
      [options]
    );
    return gpx;
  }
};
var RTE_PARSERS = makeStructureNS(NAMESPACE_URIS, {
  "name": makeObjectPropertySetter(readString),
  "cmt": makeObjectPropertySetter(readString),
  "desc": makeObjectPropertySetter(readString),
  "src": makeObjectPropertySetter(readString),
  "link": parseLink,
  "number": makeObjectPropertySetter(readPositiveInteger),
  "extensions": parseExtensions,
  "type": makeObjectPropertySetter(readString),
  "rtept": parseRtePt
});
var RTEPT_PARSERS = makeStructureNS(NAMESPACE_URIS, {
  "ele": makeObjectPropertySetter(readDecimal),
  "time": makeObjectPropertySetter(readDateTime)
});
var TRK_PARSERS = makeStructureNS(NAMESPACE_URIS, {
  "name": makeObjectPropertySetter(readString),
  "cmt": makeObjectPropertySetter(readString),
  "desc": makeObjectPropertySetter(readString),
  "src": makeObjectPropertySetter(readString),
  "link": parseLink,
  "number": makeObjectPropertySetter(readPositiveInteger),
  "type": makeObjectPropertySetter(readString),
  "extensions": parseExtensions,
  "trkseg": parseTrkSeg
});
var TRKSEG_PARSERS = makeStructureNS(NAMESPACE_URIS, {
  "trkpt": parseTrkPt
});
var TRKPT_PARSERS = makeStructureNS(NAMESPACE_URIS, {
  "ele": makeObjectPropertySetter(readDecimal),
  "time": makeObjectPropertySetter(readDateTime)
});
var WPT_PARSERS = makeStructureNS(NAMESPACE_URIS, {
  "ele": makeObjectPropertySetter(readDecimal),
  "time": makeObjectPropertySetter(readDateTime),
  "magvar": makeObjectPropertySetter(readDecimal),
  "geoidheight": makeObjectPropertySetter(readDecimal),
  "name": makeObjectPropertySetter(readString),
  "cmt": makeObjectPropertySetter(readString),
  "desc": makeObjectPropertySetter(readString),
  "src": makeObjectPropertySetter(readString),
  "link": parseLink,
  "sym": makeObjectPropertySetter(readString),
  "type": makeObjectPropertySetter(readString),
  "fix": makeObjectPropertySetter(readString),
  "sat": makeObjectPropertySetter(readPositiveInteger),
  "hdop": makeObjectPropertySetter(readDecimal),
  "vdop": makeObjectPropertySetter(readDecimal),
  "pdop": makeObjectPropertySetter(readDecimal),
  "ageofdgpsdata": makeObjectPropertySetter(readDecimal),
  "dgpsid": makeObjectPropertySetter(readPositiveInteger),
  "extensions": parseExtensions
});
var LINK_SEQUENCE = ["text", "type"];
var LINK_SERIALIZERS = makeStructureNS(NAMESPACE_URIS, {
  "text": makeChildAppender(writeStringTextNode),
  "type": makeChildAppender(writeStringTextNode)
});
var RTE_SEQUENCE = makeStructureNS(NAMESPACE_URIS, [
  "name",
  "cmt",
  "desc",
  "src",
  "link",
  "number",
  "type",
  "rtept"
]);
var RTE_SERIALIZERS = makeStructureNS(NAMESPACE_URIS, {
  "name": makeChildAppender(writeStringTextNode),
  "cmt": makeChildAppender(writeStringTextNode),
  "desc": makeChildAppender(writeStringTextNode),
  "src": makeChildAppender(writeStringTextNode),
  "link": makeChildAppender(writeLink),
  "number": makeChildAppender(writeNonNegativeIntegerTextNode),
  "type": makeChildAppender(writeStringTextNode),
  "rtept": makeArraySerializer(makeChildAppender(writeWptType))
});
var RTEPT_TYPE_SEQUENCE = makeStructureNS(NAMESPACE_URIS, ["ele", "time"]);
var TRK_SEQUENCE = makeStructureNS(NAMESPACE_URIS, [
  "name",
  "cmt",
  "desc",
  "src",
  "link",
  "number",
  "type",
  "trkseg"
]);
var TRK_SERIALIZERS = makeStructureNS(NAMESPACE_URIS, {
  "name": makeChildAppender(writeStringTextNode),
  "cmt": makeChildAppender(writeStringTextNode),
  "desc": makeChildAppender(writeStringTextNode),
  "src": makeChildAppender(writeStringTextNode),
  "link": makeChildAppender(writeLink),
  "number": makeChildAppender(writeNonNegativeIntegerTextNode),
  "type": makeChildAppender(writeStringTextNode),
  "trkseg": makeArraySerializer(makeChildAppender(writeTrkSeg))
});
var TRKSEG_NODE_FACTORY = makeSimpleNodeFactory("trkpt");
var TRKSEG_SERIALIZERS = makeStructureNS(NAMESPACE_URIS, {
  "trkpt": makeChildAppender(writeWptType)
});
var WPT_TYPE_SEQUENCE = makeStructureNS(NAMESPACE_URIS, [
  "ele",
  "time",
  "magvar",
  "geoidheight",
  "name",
  "cmt",
  "desc",
  "src",
  "link",
  "sym",
  "type",
  "fix",
  "sat",
  "hdop",
  "vdop",
  "pdop",
  "ageofdgpsdata",
  "dgpsid"
]);
var WPT_TYPE_SERIALIZERS = makeStructureNS(NAMESPACE_URIS, {
  "ele": makeChildAppender(writeDecimalTextNode),
  "time": makeChildAppender(writeDateTimeTextNode),
  "magvar": makeChildAppender(writeDecimalTextNode),
  "geoidheight": makeChildAppender(writeDecimalTextNode),
  "name": makeChildAppender(writeStringTextNode),
  "cmt": makeChildAppender(writeStringTextNode),
  "desc": makeChildAppender(writeStringTextNode),
  "src": makeChildAppender(writeStringTextNode),
  "link": makeChildAppender(writeLink),
  "sym": makeChildAppender(writeStringTextNode),
  "type": makeChildAppender(writeStringTextNode),
  "fix": makeChildAppender(writeStringTextNode),
  "sat": makeChildAppender(writeNonNegativeIntegerTextNode),
  "hdop": makeChildAppender(writeDecimalTextNode),
  "vdop": makeChildAppender(writeDecimalTextNode),
  "pdop": makeChildAppender(writeDecimalTextNode),
  "ageofdgpsdata": makeChildAppender(writeDecimalTextNode),
  "dgpsid": makeChildAppender(writeNonNegativeIntegerTextNode)
});
var GEOMETRY_TYPE_TO_NODENAME = {
  "Point": "wpt",
  "LineString": "rte",
  "MultiLineString": "trk"
};
function GPX_NODE_FACTORY(value, objectStack, nodeName) {
  const geometry = (
    /** @type {Feature} */
    value.getGeometry()
  );
  if (geometry) {
    const nodeName2 = GEOMETRY_TYPE_TO_NODENAME[geometry.getType()];
    if (nodeName2) {
      const parentNode = objectStack[objectStack.length - 1].node;
      return createElementNS(parentNode.namespaceURI, nodeName2);
    }
  }
}
function appendCoordinate(flatCoordinates, layoutOptions, node, values) {
  flatCoordinates.push(
    parseFloat(node.getAttribute("lon")),
    parseFloat(node.getAttribute("lat"))
  );
  if ("ele" in values) {
    flatCoordinates.push(
      /** @type {number} */
      values["ele"]
    );
    delete values["ele"];
    layoutOptions.hasZ = true;
  } else {
    flatCoordinates.push(0);
  }
  if ("time" in values) {
    flatCoordinates.push(
      /** @type {number} */
      values["time"]
    );
    delete values["time"];
    layoutOptions.hasM = true;
  } else {
    flatCoordinates.push(0);
  }
  return flatCoordinates;
}
function applyLayoutOptions(layoutOptions, flatCoordinates, ends) {
  let layout = "XY";
  let stride = 2;
  if (layoutOptions.hasZ && layoutOptions.hasM) {
    layout = "XYZM";
    stride = 4;
  } else if (layoutOptions.hasZ) {
    layout = "XYZ";
    stride = 3;
  } else if (layoutOptions.hasM) {
    layout = "XYM";
    stride = 3;
  }
  if (stride !== 4) {
    for (let i = 0, ii = flatCoordinates.length / 4; i < ii; i++) {
      flatCoordinates[i * stride] = flatCoordinates[i * 4];
      flatCoordinates[i * stride + 1] = flatCoordinates[i * 4 + 1];
      if (layoutOptions.hasZ) {
        flatCoordinates[i * stride + 2] = flatCoordinates[i * 4 + 2];
      }
      if (layoutOptions.hasM) {
        flatCoordinates[i * stride + 2] = flatCoordinates[i * 4 + 3];
      }
    }
    flatCoordinates.length = flatCoordinates.length / 4 * stride;
    if (ends) {
      for (let i = 0, ii = ends.length; i < ii; i++) {
        ends[i] = ends[i] / 4 * stride;
      }
    }
  }
  return layout;
}
function parseLink(node, objectStack) {
  const values = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const href = node.getAttribute("href");
  if (href !== null) {
    values["link"] = href;
  }
  parseNode(LINK_PARSERS, node, objectStack);
}
function parseExtensions(node, objectStack) {
  const values = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  values["extensionsNode_"] = node;
}
function parseRtePt(node, objectStack) {
  const values = pushParseAndPop({}, RTEPT_PARSERS, node, objectStack);
  if (values) {
    const rteValues = (
      /** @type {!Object} */
      objectStack[objectStack.length - 1]
    );
    const flatCoordinates = (
      /** @type {Array<number>} */
      rteValues["flatCoordinates"]
    );
    const layoutOptions = (
      /** @type {LayoutOptions} */
      rteValues["layoutOptions"]
    );
    appendCoordinate(flatCoordinates, layoutOptions, node, values);
  }
}
function parseTrkPt(node, objectStack) {
  const values = pushParseAndPop({}, TRKPT_PARSERS, node, objectStack);
  if (values) {
    const trkValues = (
      /** @type {!Object} */
      objectStack[objectStack.length - 1]
    );
    const flatCoordinates = (
      /** @type {Array<number>} */
      trkValues["flatCoordinates"]
    );
    const layoutOptions = (
      /** @type {LayoutOptions} */
      trkValues["layoutOptions"]
    );
    appendCoordinate(flatCoordinates, layoutOptions, node, values);
  }
}
function parseTrkSeg(node, objectStack) {
  const values = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  parseNode(TRKSEG_PARSERS, node, objectStack);
  const flatCoordinates = (
    /** @type {Array<number>} */
    values["flatCoordinates"]
  );
  const ends = (
    /** @type {Array<number>} */
    values["ends"]
  );
  ends.push(flatCoordinates.length);
}
function readRte(node, objectStack) {
  const options = (
    /** @type {import("./Feature.js").ReadOptions} */
    objectStack[0]
  );
  const values = pushParseAndPop(
    {
      "flatCoordinates": [],
      "layoutOptions": {}
    },
    RTE_PARSERS,
    node,
    objectStack
  );
  if (!values) {
    return void 0;
  }
  const flatCoordinates = (
    /** @type {Array<number>} */
    values["flatCoordinates"]
  );
  delete values["flatCoordinates"];
  const layoutOptions = (
    /** @type {LayoutOptions} */
    values["layoutOptions"]
  );
  delete values["layoutOptions"];
  const layout = applyLayoutOptions(layoutOptions, flatCoordinates);
  const geometry = new LineString_default(flatCoordinates, layout);
  transformGeometryWithOptions(geometry, false, options);
  const feature = new Feature_default(geometry);
  feature.setProperties(values, true);
  return feature;
}
function readTrk(node, objectStack) {
  const options = (
    /** @type {import("./Feature.js").ReadOptions} */
    objectStack[0]
  );
  const values = pushParseAndPop(
    {
      "flatCoordinates": [],
      "ends": [],
      "layoutOptions": {}
    },
    TRK_PARSERS,
    node,
    objectStack
  );
  if (!values) {
    return void 0;
  }
  const flatCoordinates = (
    /** @type {Array<number>} */
    values["flatCoordinates"]
  );
  delete values["flatCoordinates"];
  const ends = (
    /** @type {Array<number>} */
    values["ends"]
  );
  delete values["ends"];
  const layoutOptions = (
    /** @type {LayoutOptions} */
    values["layoutOptions"]
  );
  delete values["layoutOptions"];
  const layout = applyLayoutOptions(layoutOptions, flatCoordinates, ends);
  const geometry = new MultiLineString_default(flatCoordinates, layout, ends);
  transformGeometryWithOptions(geometry, false, options);
  const feature = new Feature_default(geometry);
  feature.setProperties(values, true);
  return feature;
}
function readWpt(node, objectStack) {
  const options = (
    /** @type {import("./Feature.js").ReadOptions} */
    objectStack[0]
  );
  const values = pushParseAndPop({}, WPT_PARSERS, node, objectStack);
  if (!values) {
    return void 0;
  }
  const layoutOptions = (
    /** @type {LayoutOptions} */
    {}
  );
  const coordinates = appendCoordinate([], layoutOptions, node, values);
  const layout = applyLayoutOptions(layoutOptions, coordinates);
  const geometry = new Point_default(coordinates, layout);
  transformGeometryWithOptions(geometry, false, options);
  const feature = new Feature_default(geometry);
  feature.setProperties(values, true);
  return feature;
}
function writeLink(node, value, objectStack) {
  node.setAttribute("href", value);
  const context = objectStack[objectStack.length - 1];
  const properties = context["properties"];
  const link = [properties["linkText"], properties["linkType"]];
  pushSerializeAndPop(
    /** @type {import("../xml.js").NodeStackItem} */
    { node },
    LINK_SERIALIZERS,
    OBJECT_PROPERTY_NODE_FACTORY,
    link,
    objectStack,
    LINK_SEQUENCE
  );
}
function writeWptType(node, coordinate, objectStack) {
  const context = objectStack[objectStack.length - 1];
  const parentNode = context.node;
  const namespaceURI = parentNode.namespaceURI;
  const properties = context["properties"];
  node.setAttributeNS(null, "lat", String(coordinate[1]));
  node.setAttributeNS(null, "lon", String(coordinate[0]));
  const geometryLayout = context["geometryLayout"];
  switch (geometryLayout) {
    case "XYZM":
      if (coordinate[3] !== 0) {
        properties["time"] = coordinate[3];
      }
    case "XYZ":
      if (coordinate[2] !== 0) {
        properties["ele"] = coordinate[2];
      }
      break;
    case "XYM":
      if (coordinate[2] !== 0) {
        properties["time"] = coordinate[2];
      }
      break;
    default:
  }
  const orderedKeys = node.nodeName == "rtept" ? RTEPT_TYPE_SEQUENCE[namespaceURI] : WPT_TYPE_SEQUENCE[namespaceURI];
  const values = makeSequence(properties, orderedKeys);
  pushSerializeAndPop(
    /** @type {import("../xml.js").NodeStackItem} */
    { node, "properties": properties },
    WPT_TYPE_SERIALIZERS,
    OBJECT_PROPERTY_NODE_FACTORY,
    values,
    objectStack,
    orderedKeys
  );
}
function writeRte(node, feature, objectStack) {
  const options = (
    /** @type {import("./Feature.js").WriteOptions} */
    objectStack[0]
  );
  const properties = feature.getProperties();
  const context = { node };
  context["properties"] = properties;
  const geometry = feature.getGeometry();
  if (geometry.getType() == "LineString") {
    const lineString = (
      /** @type {LineString} */
      transformGeometryWithOptions(geometry, true, options)
    );
    context["geometryLayout"] = lineString.getLayout();
    properties["rtept"] = lineString.getCoordinates();
  }
  const parentNode = objectStack[objectStack.length - 1].node;
  const orderedKeys = RTE_SEQUENCE[parentNode.namespaceURI];
  const values = makeSequence(properties, orderedKeys);
  pushSerializeAndPop(
    context,
    RTE_SERIALIZERS,
    OBJECT_PROPERTY_NODE_FACTORY,
    values,
    objectStack,
    orderedKeys
  );
}
function writeTrk(node, feature, objectStack) {
  const options = (
    /** @type {import("./Feature.js").WriteOptions} */
    objectStack[0]
  );
  const properties = feature.getProperties();
  const context = { node };
  context["properties"] = properties;
  const geometry = feature.getGeometry();
  if (geometry.getType() == "MultiLineString") {
    const multiLineString = (
      /** @type {MultiLineString} */
      transformGeometryWithOptions(geometry, true, options)
    );
    properties["trkseg"] = multiLineString.getLineStrings();
  }
  const parentNode = objectStack[objectStack.length - 1].node;
  const orderedKeys = TRK_SEQUENCE[parentNode.namespaceURI];
  const values = makeSequence(properties, orderedKeys);
  pushSerializeAndPop(
    context,
    TRK_SERIALIZERS,
    OBJECT_PROPERTY_NODE_FACTORY,
    values,
    objectStack,
    orderedKeys
  );
}
function writeTrkSeg(node, lineString, objectStack) {
  const context = { node };
  context["geometryLayout"] = lineString.getLayout();
  context["properties"] = {};
  pushSerializeAndPop(
    context,
    TRKSEG_SERIALIZERS,
    TRKSEG_NODE_FACTORY,
    lineString.getCoordinates(),
    objectStack
  );
}
function writeWpt(node, feature, objectStack) {
  const options = (
    /** @type {import("./Feature.js").WriteOptions} */
    objectStack[0]
  );
  const context = objectStack[objectStack.length - 1];
  context["properties"] = feature.getProperties();
  const geometry = feature.getGeometry();
  if (geometry.getType() == "Point") {
    const point = (
      /** @type {Point} */
      transformGeometryWithOptions(geometry, true, options)
    );
    context["geometryLayout"] = point.getLayout();
    writeWptType(node, point.getCoordinates(), objectStack);
  }
}
var GPX_default = GPX;

// node_modules/ol/format/TextFeature.js
var TextFeature = class extends Feature_default2 {
  constructor() {
    super();
  }
  /**
   * @return {import("./Feature.js").Type} Format.
   */
  getType() {
    return "text";
  }
  /**
   * Read the feature from the source.
   *
   * @param {Document|Element|Object|string} source Source.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @return {import("../Feature.js").default} Feature.
   * @api
   */
  readFeature(source, options) {
    return this.readFeatureFromText(
      getText(source),
      this.adaptOptions(options)
    );
  }
  /**
   * @abstract
   * @param {string} text Text.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {import("../Feature.js").default} Feature.
   */
  readFeatureFromText(text, options) {
    return abstract();
  }
  /**
   * Read the features from the source.
   *
   * @param {Document|Element|Object|string} source Source.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @return {Array<import("../Feature.js").default>} Features.
   * @api
   */
  readFeatures(source, options) {
    return this.readFeaturesFromText(
      getText(source),
      this.adaptOptions(options)
    );
  }
  /**
   * @abstract
   * @param {string} text Text.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {Array<import("../Feature.js").default>} Features.
   */
  readFeaturesFromText(text, options) {
    return abstract();
  }
  /**
   * Read the geometry from the source.
   *
   * @param {Document|Element|Object|string} source Source.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @return {import("../geom/Geometry.js").default} Geometry.
   * @api
   */
  readGeometry(source, options) {
    return this.readGeometryFromText(
      getText(source),
      this.adaptOptions(options)
    );
  }
  /**
   * @abstract
   * @param {string} text Text.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {import("../geom/Geometry.js").default} Geometry.
   */
  readGeometryFromText(text, options) {
    return abstract();
  }
  /**
   * Read the projection from the source.
   *
   * @param {Document|Element|Object|string} source Source.
   * @return {import("../proj/Projection.js").default|undefined} Projection.
   * @api
   */
  readProjection(source) {
    return this.readProjectionFromText(getText(source));
  }
  /**
   * @param {string} text Text.
   * @protected
   * @return {import("../proj/Projection.js").default|undefined} Projection.
   */
  readProjectionFromText(text) {
    return this.dataProjection;
  }
  /**
   * Encode a feature as a string.
   *
   * @param {import("../Feature.js").default} feature Feature.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {string} Encoded feature.
   * @api
   */
  writeFeature(feature, options) {
    return this.writeFeatureText(feature, this.adaptOptions(options));
  }
  /**
   * @abstract
   * @param {import("../Feature.js").default} feature Features.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @protected
   * @return {string} Text.
   */
  writeFeatureText(feature, options) {
    return abstract();
  }
  /**
   * Encode an array of features as string.
   *
   * @param {Array<import("../Feature.js").default>} features Features.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {string} Encoded features.
   * @api
   */
  writeFeatures(features, options) {
    return this.writeFeaturesText(features, this.adaptOptions(options));
  }
  /**
   * @abstract
   * @param {Array<import("../Feature.js").default>} features Features.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @protected
   * @return {string} Text.
   */
  writeFeaturesText(features, options) {
    return abstract();
  }
  /**
   * Write a single geometry.
   *
   * @param {import("../geom/Geometry.js").default} geometry Geometry.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {string} Geometry.
   * @api
   */
  writeGeometry(geometry, options) {
    return this.writeGeometryText(geometry, this.adaptOptions(options));
  }
  /**
   * @abstract
   * @param {import("../geom/Geometry.js").default} geometry Geometry.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @protected
   * @return {string} Text.
   */
  writeGeometryText(geometry, options) {
    return abstract();
  }
};
function getText(source) {
  if (typeof source === "string") {
    return source;
  }
  return "";
}
var TextFeature_default = TextFeature;

// node_modules/ol/format/IGC.js
var B_RECORD_RE = /^B(\d{2})(\d{2})(\d{2})(\d{2})(\d{5})([NS])(\d{3})(\d{5})([EW])([AV])(\d{5})(\d{5})/;
var H_RECORD_RE = /^H.([A-Z]{3}).*?:(.*)/;
var HFDTE_RECORD_RE = /^HFDTE(\d{2})(\d{2})(\d{2})/;
var NEWLINE_RE = /\r\n|\r|\n/;
var IGC = class extends TextFeature_default {
  /**
   * @param {Options} [options] Options.
   */
  constructor(options) {
    super();
    options = options ? options : {};
    this.dataProjection = get("EPSG:4326");
    this.altitudeMode_ = options.altitudeMode ? options.altitudeMode : "none";
  }
  /**
   * @protected
   * @param {string} text Text.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @return {import("../Feature.js").default} Feature.
   */
  readFeatureFromText(text, options) {
    const altitudeMode = this.altitudeMode_;
    const lines = text.split(NEWLINE_RE);
    const properties = {};
    const flatCoordinates = [];
    let year = 2e3;
    let month = 0;
    let day = 1;
    let lastDateTime = -1;
    let i, ii;
    for (i = 0, ii = lines.length; i < ii; ++i) {
      const line = lines[i];
      let m;
      if (line.charAt(0) == "B") {
        m = B_RECORD_RE.exec(line);
        if (m) {
          const hour = parseInt(m[1], 10);
          const minute = parseInt(m[2], 10);
          const second = parseInt(m[3], 10);
          let y = parseInt(m[4], 10) + parseInt(m[5], 10) / 6e4;
          if (m[6] == "S") {
            y = -y;
          }
          let x = parseInt(m[7], 10) + parseInt(m[8], 10) / 6e4;
          if (m[9] == "W") {
            x = -x;
          }
          flatCoordinates.push(x, y);
          if (altitudeMode != "none") {
            let z;
            if (altitudeMode == "gps") {
              z = parseInt(m[11], 10);
            } else if (altitudeMode == "barometric") {
              z = parseInt(m[12], 10);
            } else {
              z = 0;
            }
            flatCoordinates.push(z);
          }
          let dateTime = Date.UTC(year, month, day, hour, minute, second);
          if (dateTime < lastDateTime) {
            dateTime = Date.UTC(year, month, day + 1, hour, minute, second);
          }
          flatCoordinates.push(dateTime / 1e3);
          lastDateTime = dateTime;
        }
      } else if (line.charAt(0) == "H") {
        m = HFDTE_RECORD_RE.exec(line);
        if (m) {
          day = parseInt(m[1], 10);
          month = parseInt(m[2], 10) - 1;
          year = 2e3 + parseInt(m[3], 10);
        } else {
          m = H_RECORD_RE.exec(line);
          if (m) {
            properties[m[1]] = m[2].trim();
          }
        }
      }
    }
    if (flatCoordinates.length === 0) {
      return null;
    }
    const layout = altitudeMode == "none" ? "XYM" : "XYZM";
    const lineString = new LineString_default(flatCoordinates, layout);
    const feature = new Feature_default(
      transformGeometryWithOptions(lineString, false, options)
    );
    feature.setProperties(properties, true);
    return feature;
  }
  /**
   * @param {string} text Text.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {Array<Feature>} Features.
   */
  readFeaturesFromText(text, options) {
    const feature = this.readFeatureFromText(text, options);
    if (feature) {
      return [feature];
    }
    return [];
  }
};
var IGC_default = IGC;

// node_modules/ol/format/KML.js
var GX_NAMESPACE_URIS = ["http://www.google.com/kml/ext/2.2"];
var NAMESPACE_URIS2 = [
  null,
  "http://earth.google.com/kml/2.0",
  "http://earth.google.com/kml/2.1",
  "http://earth.google.com/kml/2.2",
  "http://www.opengis.net/kml/2.2"
];
var SCHEMA_LOCATION2 = "http://www.opengis.net/kml/2.2 https://developers.google.com/kml/schema/kml22gx.xsd";
var ICON_ANCHOR_UNITS_MAP = {
  "fraction": "fraction",
  "pixels": "pixels",
  "insetPixels": "pixels"
};
var PLACEMARK_PARSERS = makeStructureNS(
  NAMESPACE_URIS2,
  {
    "ExtendedData": extendedDataParser,
    "Region": regionParser,
    "MultiGeometry": makeObjectPropertySetter(readMultiGeometry, "geometry"),
    "LineString": makeObjectPropertySetter(readLineString, "geometry"),
    "LinearRing": makeObjectPropertySetter(readLinearRing, "geometry"),
    "Point": makeObjectPropertySetter(readPoint, "geometry"),
    "Polygon": makeObjectPropertySetter(readPolygon, "geometry"),
    "Style": makeObjectPropertySetter(readStyle),
    "StyleMap": placemarkStyleMapParser,
    "address": makeObjectPropertySetter(readString),
    "description": makeObjectPropertySetter(readString),
    "name": makeObjectPropertySetter(readString),
    "open": makeObjectPropertySetter(readBoolean),
    "phoneNumber": makeObjectPropertySetter(readString),
    "styleUrl": makeObjectPropertySetter(readStyleURL),
    "visibility": makeObjectPropertySetter(readBoolean)
  },
  makeStructureNS(GX_NAMESPACE_URIS, {
    "MultiTrack": makeObjectPropertySetter(readGxMultiTrack, "geometry"),
    "Track": makeObjectPropertySetter(readGxTrack, "geometry")
  })
);
var NETWORK_LINK_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "ExtendedData": extendedDataParser,
  "Region": regionParser,
  "Link": linkParser,
  "address": makeObjectPropertySetter(readString),
  "description": makeObjectPropertySetter(readString),
  "name": makeObjectPropertySetter(readString),
  "open": makeObjectPropertySetter(readBoolean),
  "phoneNumber": makeObjectPropertySetter(readString),
  "visibility": makeObjectPropertySetter(readBoolean)
});
var LINK_PARSERS2 = makeStructureNS(NAMESPACE_URIS2, {
  "href": makeObjectPropertySetter(readURI)
});
var CAMERA_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  Altitude: makeObjectPropertySetter(readDecimal),
  Longitude: makeObjectPropertySetter(readDecimal),
  Latitude: makeObjectPropertySetter(readDecimal),
  Tilt: makeObjectPropertySetter(readDecimal),
  AltitudeMode: makeObjectPropertySetter(readString),
  Heading: makeObjectPropertySetter(readDecimal),
  Roll: makeObjectPropertySetter(readDecimal)
});
var REGION_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "LatLonAltBox": latLonAltBoxParser,
  "Lod": lodParser
});
var KML_SEQUENCE = makeStructureNS(NAMESPACE_URIS2, ["Document", "Placemark"]);
var KML_SERIALIZERS = makeStructureNS(NAMESPACE_URIS2, {
  "Document": makeChildAppender(writeDocument),
  "Placemark": makeChildAppender(writePlacemark)
});
var DEFAULT_COLOR;
var DEFAULT_FILL_STYLE = null;
var DEFAULT_IMAGE_STYLE_ANCHOR;
var DEFAULT_IMAGE_STYLE_ANCHOR_X_UNITS;
var DEFAULT_IMAGE_STYLE_ANCHOR_Y_UNITS;
var DEFAULT_IMAGE_STYLE_SIZE;
var DEFAULT_IMAGE_STYLE_SRC;
var DEFAULT_IMAGE_STYLE = null;
var DEFAULT_NO_IMAGE_STYLE;
var DEFAULT_STROKE_STYLE = null;
var DEFAULT_TEXT_STROKE_STYLE;
var DEFAULT_TEXT_STYLE = null;
var DEFAULT_STYLE = null;
var DEFAULT_STYLE_ARRAY = null;
function scaleForSize(size) {
  return 32 / Math.min(size[0], size[1]);
}
function createStyleDefaults() {
  DEFAULT_COLOR = [255, 255, 255, 1];
  DEFAULT_FILL_STYLE = new Fill_default({
    color: DEFAULT_COLOR
  });
  DEFAULT_IMAGE_STYLE_ANCHOR = [20, 2];
  DEFAULT_IMAGE_STYLE_ANCHOR_X_UNITS = "pixels";
  DEFAULT_IMAGE_STYLE_ANCHOR_Y_UNITS = "pixels";
  DEFAULT_IMAGE_STYLE_SIZE = [64, 64];
  DEFAULT_IMAGE_STYLE_SRC = "https://maps.google.com/mapfiles/kml/pushpin/ylw-pushpin.png";
  DEFAULT_IMAGE_STYLE = new Icon_default({
    anchor: DEFAULT_IMAGE_STYLE_ANCHOR,
    anchorOrigin: "bottom-left",
    anchorXUnits: DEFAULT_IMAGE_STYLE_ANCHOR_X_UNITS,
    anchorYUnits: DEFAULT_IMAGE_STYLE_ANCHOR_Y_UNITS,
    crossOrigin: "anonymous",
    rotation: 0,
    scale: scaleForSize(DEFAULT_IMAGE_STYLE_SIZE),
    size: DEFAULT_IMAGE_STYLE_SIZE,
    src: DEFAULT_IMAGE_STYLE_SRC
  });
  DEFAULT_NO_IMAGE_STYLE = "NO_IMAGE";
  DEFAULT_STROKE_STYLE = new Stroke_default({
    color: DEFAULT_COLOR,
    width: 1
  });
  DEFAULT_TEXT_STROKE_STYLE = new Stroke_default({
    color: [51, 51, 51, 1],
    width: 2
  });
  DEFAULT_TEXT_STYLE = new Text_default({
    font: "bold 16px Helvetica",
    fill: DEFAULT_FILL_STYLE,
    stroke: DEFAULT_TEXT_STROKE_STYLE,
    scale: 0.8
  });
  DEFAULT_STYLE = new Style_default({
    fill: DEFAULT_FILL_STYLE,
    image: DEFAULT_IMAGE_STYLE,
    text: DEFAULT_TEXT_STYLE,
    stroke: DEFAULT_STROKE_STYLE,
    zIndex: 0
  });
  DEFAULT_STYLE_ARRAY = [DEFAULT_STYLE];
}
var TEXTAREA;
function defaultIconUrlFunction(href) {
  return href;
}
var KML = class extends XMLFeature_default {
  /**
   * @param {Options} [options] Options.
   */
  constructor(options) {
    super();
    options = options ? options : {};
    if (!DEFAULT_STYLE_ARRAY) {
      createStyleDefaults();
    }
    this.dataProjection = get("EPSG:4326");
    this.defaultStyle_ = options.defaultStyle ? options.defaultStyle : DEFAULT_STYLE_ARRAY;
    this.extractStyles_ = options.extractStyles !== void 0 ? options.extractStyles : true;
    this.writeStyles_ = options.writeStyles !== void 0 ? options.writeStyles : true;
    this.sharedStyles_ = {};
    this.showPointNames_ = options.showPointNames !== void 0 ? options.showPointNames : true;
    this.crossOrigin_ = options.crossOrigin !== void 0 ? options.crossOrigin : "anonymous";
    this.iconUrlFunction_ = options.iconUrlFunction ? options.iconUrlFunction : defaultIconUrlFunction;
    this.supportedMediaTypes = ["application/vnd.google-earth.kml+xml"];
  }
  /**
   * @param {Node} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @private
   * @return {Array<Feature>|undefined} Features.
   */
  readDocumentOrFolder_(node, objectStack) {
    const parsersNS = makeStructureNS(NAMESPACE_URIS2, {
      "Document": makeArrayExtender(this.readDocumentOrFolder_, this),
      "Folder": makeArrayExtender(this.readDocumentOrFolder_, this),
      "Placemark": makeArrayPusher(this.readPlacemark_, this),
      "Style": this.readSharedStyle_.bind(this),
      "StyleMap": this.readSharedStyleMap_.bind(this)
    });
    const features = pushParseAndPop([], parsersNS, node, objectStack, this);
    if (features) {
      return features;
    }
    return void 0;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @private
   * @return {Feature|undefined} Feature.
   */
  readPlacemark_(node, objectStack) {
    const object = pushParseAndPop(
      { "geometry": null },
      PLACEMARK_PARSERS,
      node,
      objectStack,
      this
    );
    if (!object) {
      return void 0;
    }
    const feature = new Feature_default();
    const id = node.getAttribute("id");
    if (id !== null) {
      feature.setId(id);
    }
    const options = (
      /** @type {import("./Feature.js").ReadOptions} */
      objectStack[0]
    );
    const geometry = object["geometry"];
    if (geometry) {
      transformGeometryWithOptions(geometry, false, options);
    }
    feature.setGeometry(geometry);
    delete object["geometry"];
    if (this.extractStyles_) {
      const style = object["Style"];
      const styleUrl = object["styleUrl"];
      const styleFunction = createFeatureStyleFunction(
        style,
        styleUrl,
        this.defaultStyle_,
        this.sharedStyles_,
        this.showPointNames_
      );
      feature.setStyle(styleFunction);
    }
    delete object["Style"];
    feature.setProperties(object, true);
    return feature;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @private
   */
  readSharedStyle_(node, objectStack) {
    const id = node.getAttribute("id");
    if (id !== null) {
      const style = readStyle.call(this, node, objectStack);
      if (style) {
        let styleUri;
        let baseURI = node.baseURI;
        if (!baseURI || baseURI == "about:blank") {
          baseURI = window.location.href;
        }
        if (baseURI) {
          const url = new URL("#" + id, baseURI);
          styleUri = url.href;
        } else {
          styleUri = "#" + id;
        }
        this.sharedStyles_[styleUri] = style;
      }
    }
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @private
   */
  readSharedStyleMap_(node, objectStack) {
    const id = node.getAttribute("id");
    if (id === null) {
      return;
    }
    const styleMapValue = readStyleMapValue.call(this, node, objectStack);
    if (!styleMapValue) {
      return;
    }
    let styleUri;
    let baseURI = node.baseURI;
    if (!baseURI || baseURI == "about:blank") {
      baseURI = window.location.href;
    }
    if (baseURI) {
      const url = new URL("#" + id, baseURI);
      styleUri = url.href;
    } else {
      styleUri = "#" + id;
    }
    this.sharedStyles_[styleUri] = styleMapValue;
  }
  /**
   * @param {Element} node Node.
   * @param {import("./Feature.js").ReadOptions} [options] Options.
   * @return {import("../Feature.js").default} Feature.
   */
  readFeatureFromNode(node, options) {
    if (!NAMESPACE_URIS2.includes(node.namespaceURI)) {
      return null;
    }
    const feature = this.readPlacemark_(node, [
      this.getReadOptions(node, options)
    ]);
    if (feature) {
      return feature;
    }
    return null;
  }
  /**
   * @protected
   * @param {Element} node Node.
   * @param {import("./Feature.js").ReadOptions} [options] Options.
   * @return {Array<import("../Feature.js").default>} Features.
   */
  readFeaturesFromNode(node, options) {
    if (!NAMESPACE_URIS2.includes(node.namespaceURI)) {
      return [];
    }
    let features;
    const localName = node.localName;
    if (localName == "Document" || localName == "Folder") {
      features = this.readDocumentOrFolder_(node, [
        this.getReadOptions(node, options)
      ]);
      if (features) {
        return features;
      }
      return [];
    }
    if (localName == "Placemark") {
      const feature = this.readPlacemark_(node, [
        this.getReadOptions(node, options)
      ]);
      if (feature) {
        return [feature];
      }
      return [];
    }
    if (localName == "kml") {
      features = [];
      for (let n = node.firstElementChild; n; n = n.nextElementSibling) {
        const fs = this.readFeaturesFromNode(n, options);
        if (fs) {
          extend(features, fs);
        }
      }
      return features;
    }
    return [];
  }
  /**
   * Read the name of the KML.
   *
   * @param {Document|Element|string} source Source.
   * @return {string|undefined} Name.
   * @api
   */
  readName(source) {
    if (!source) {
      return void 0;
    }
    if (typeof source === "string") {
      const doc = parse(source);
      return this.readNameFromDocument(doc);
    }
    if (isDocument(source)) {
      return this.readNameFromDocument(
        /** @type {Document} */
        source
      );
    }
    return this.readNameFromNode(
      /** @type {Element} */
      source
    );
  }
  /**
   * @param {Document} doc Document.
   * @return {string|undefined} Name.
   */
  readNameFromDocument(doc) {
    for (let n = (
      /** @type {Node} */
      doc.firstChild
    ); n; n = n.nextSibling) {
      if (n.nodeType == Node.ELEMENT_NODE) {
        const name = this.readNameFromNode(
          /** @type {Element} */
          n
        );
        if (name) {
          return name;
        }
      }
    }
    return void 0;
  }
  /**
   * @param {Element} node Node.
   * @return {string|undefined} Name.
   */
  readNameFromNode(node) {
    for (let n = node.firstElementChild; n; n = n.nextElementSibling) {
      if (NAMESPACE_URIS2.includes(n.namespaceURI) && n.localName == "name") {
        return readString(n);
      }
    }
    for (let n = node.firstElementChild; n; n = n.nextElementSibling) {
      const localName = n.localName;
      if (NAMESPACE_URIS2.includes(n.namespaceURI) && (localName == "Document" || localName == "Folder" || localName == "Placemark" || localName == "kml")) {
        const name = this.readNameFromNode(n);
        if (name) {
          return name;
        }
      }
    }
    return void 0;
  }
  /**
   * Read the network links of the KML.
   *
   * @param {Document|Element|string} source Source.
   * @return {Array<Object>} Network links.
   * @api
   */
  readNetworkLinks(source) {
    const networkLinks = [];
    if (typeof source === "string") {
      const doc = parse(source);
      extend(networkLinks, this.readNetworkLinksFromDocument(doc));
    } else if (isDocument(source)) {
      extend(
        networkLinks,
        this.readNetworkLinksFromDocument(
          /** @type {Document} */
          source
        )
      );
    } else {
      extend(
        networkLinks,
        this.readNetworkLinksFromNode(
          /** @type {Element} */
          source
        )
      );
    }
    return networkLinks;
  }
  /**
   * @param {Document} doc Document.
   * @return {Array<Object>} Network links.
   */
  readNetworkLinksFromDocument(doc) {
    const networkLinks = [];
    for (let n = (
      /** @type {Node} */
      doc.firstChild
    ); n; n = n.nextSibling) {
      if (n.nodeType == Node.ELEMENT_NODE) {
        extend(
          networkLinks,
          this.readNetworkLinksFromNode(
            /** @type {Element} */
            n
          )
        );
      }
    }
    return networkLinks;
  }
  /**
   * @param {Element} node Node.
   * @return {Array<Object>} Network links.
   */
  readNetworkLinksFromNode(node) {
    const networkLinks = [];
    for (let n = node.firstElementChild; n; n = n.nextElementSibling) {
      if (NAMESPACE_URIS2.includes(n.namespaceURI) && n.localName == "NetworkLink") {
        const obj = pushParseAndPop({}, NETWORK_LINK_PARSERS, n, []);
        networkLinks.push(obj);
      }
    }
    for (let n = node.firstElementChild; n; n = n.nextElementSibling) {
      const localName = n.localName;
      if (NAMESPACE_URIS2.includes(n.namespaceURI) && (localName == "Document" || localName == "Folder" || localName == "kml")) {
        extend(networkLinks, this.readNetworkLinksFromNode(n));
      }
    }
    return networkLinks;
  }
  /**
   * Read the regions of the KML.
   *
   * @param {Document|Element|string} source Source.
   * @return {Array<Object>} Regions.
   * @api
   */
  readRegion(source) {
    const regions = [];
    if (typeof source === "string") {
      const doc = parse(source);
      extend(regions, this.readRegionFromDocument(doc));
    } else if (isDocument(source)) {
      extend(
        regions,
        this.readRegionFromDocument(
          /** @type {Document} */
          source
        )
      );
    } else {
      extend(regions, this.readRegionFromNode(
        /** @type {Element} */
        source
      ));
    }
    return regions;
  }
  /**
   * @param {Document} doc Document.
   * @return {Array<Object>} Region.
   */
  readRegionFromDocument(doc) {
    const regions = [];
    for (let n = (
      /** @type {Node} */
      doc.firstChild
    ); n; n = n.nextSibling) {
      if (n.nodeType == Node.ELEMENT_NODE) {
        extend(regions, this.readRegionFromNode(
          /** @type {Element} */
          n
        ));
      }
    }
    return regions;
  }
  /**
   * @param {Element} node Node.
   * @return {Array<Object>} Region.
   * @api
   */
  readRegionFromNode(node) {
    const regions = [];
    for (let n = node.firstElementChild; n; n = n.nextElementSibling) {
      if (NAMESPACE_URIS2.includes(n.namespaceURI) && n.localName == "Region") {
        const obj = pushParseAndPop({}, REGION_PARSERS, n, []);
        regions.push(obj);
      }
    }
    for (let n = node.firstElementChild; n; n = n.nextElementSibling) {
      const localName = n.localName;
      if (NAMESPACE_URIS2.includes(n.namespaceURI) && (localName == "Document" || localName == "Folder" || localName == "kml")) {
        extend(regions, this.readRegionFromNode(n));
      }
    }
    return regions;
  }
  /**
   * @typedef {Object} KMLCamera Specifies the observer's viewpoint and associated view parameters.
   * @property {number} [Latitude] Latitude of the camera.
   * @property {number} [Longitude] Longitude of the camera.
   * @property {number} [Altitude] Altitude of the camera.
   * @property {string} [AltitudeMode] Floor-related altitude mode.
   * @property {number} [Heading] Horizontal camera rotation.
   * @property {number} [Tilt] Lateral camera rotation.
   * @property {number} [Roll] Vertical camera rotation.
   */
  /**
   * Read the cameras of the KML.
   *
   * @param {Document|Element|string} source Source.
   * @return {Array<KMLCamera>} Cameras.
   * @api
   */
  readCamera(source) {
    const cameras = [];
    if (typeof source === "string") {
      const doc = parse(source);
      extend(cameras, this.readCameraFromDocument(doc));
    } else if (isDocument(source)) {
      extend(
        cameras,
        this.readCameraFromDocument(
          /** @type {Document} */
          source
        )
      );
    } else {
      extend(cameras, this.readCameraFromNode(
        /** @type {Element} */
        source
      ));
    }
    return cameras;
  }
  /**
   * @param {Document} doc Document.
   * @return {Array<KMLCamera>} Cameras.
   */
  readCameraFromDocument(doc) {
    const cameras = [];
    for (let n = (
      /** @type {Node} */
      doc.firstChild
    ); n; n = n.nextSibling) {
      if (n.nodeType === Node.ELEMENT_NODE) {
        extend(cameras, this.readCameraFromNode(
          /** @type {Element} */
          n
        ));
      }
    }
    return cameras;
  }
  /**
   * @param {Element} node Node.
   * @return {Array<KMLCamera>} Cameras.
   * @api
   */
  readCameraFromNode(node) {
    const cameras = [];
    for (let n = node.firstElementChild; n; n = n.nextElementSibling) {
      if (NAMESPACE_URIS2.includes(n.namespaceURI) && n.localName === "Camera") {
        const obj = pushParseAndPop({}, CAMERA_PARSERS, n, []);
        cameras.push(obj);
      }
    }
    for (let n = node.firstElementChild; n; n = n.nextElementSibling) {
      const localName = n.localName;
      if (NAMESPACE_URIS2.includes(n.namespaceURI) && (localName === "Document" || localName === "Folder" || localName === "Placemark" || localName === "kml")) {
        extend(cameras, this.readCameraFromNode(n));
      }
    }
    return cameras;
  }
  /**
   * Encode an array of features in the KML format as an XML node. GeometryCollections,
   * MultiPoints, MultiLineStrings, and MultiPolygons are output as MultiGeometries.
   *
   * @param {Array<Feature>} features Features.
   * @param {import("./Feature.js").WriteOptions} [options] Options.
   * @return {Node} Node.
   * @api
   */
  writeFeaturesNode(features, options) {
    options = this.adaptOptions(options);
    const kml = createElementNS(NAMESPACE_URIS2[4], "kml");
    const xmlnsUri = "http://www.w3.org/2000/xmlns/";
    kml.setAttributeNS(xmlnsUri, "xmlns:gx", GX_NAMESPACE_URIS[0]);
    kml.setAttributeNS(xmlnsUri, "xmlns:xsi", XML_SCHEMA_INSTANCE_URI);
    kml.setAttributeNS(
      XML_SCHEMA_INSTANCE_URI,
      "xsi:schemaLocation",
      SCHEMA_LOCATION2
    );
    const context = {
      node: kml
    };
    const properties = {};
    if (features.length > 1) {
      properties["Document"] = features;
    } else if (features.length == 1) {
      properties["Placemark"] = features[0];
    }
    const orderedKeys = KML_SEQUENCE[kml.namespaceURI];
    const values = makeSequence(properties, orderedKeys);
    pushSerializeAndPop(
      context,
      KML_SERIALIZERS,
      OBJECT_PROPERTY_NODE_FACTORY,
      values,
      [options],
      orderedKeys,
      this
    );
    return kml;
  }
};
function createNameStyleFunction(foundStyle, name) {
  const textOffset = [0, 0];
  let textAlign = "start";
  const imageStyle = foundStyle.getImage();
  if (imageStyle) {
    const imageSize = imageStyle.getSize();
    if (imageSize && imageSize.length == 2) {
      const imageScale = imageStyle.getScaleArray();
      const anchor = imageStyle.getAnchor();
      textOffset[0] = imageScale[0] * (imageSize[0] - anchor[0]);
      textOffset[1] = imageScale[1] * (imageSize[1] / 2 - anchor[1]);
      textAlign = "left";
    }
  }
  let textStyle = foundStyle.getText();
  if (textStyle) {
    textStyle = textStyle.clone();
    textStyle.setFont(textStyle.getFont() || DEFAULT_TEXT_STYLE.getFont());
    textStyle.setScale(textStyle.getScale() || DEFAULT_TEXT_STYLE.getScale());
    textStyle.setFill(textStyle.getFill() || DEFAULT_TEXT_STYLE.getFill());
    textStyle.setStroke(textStyle.getStroke() || DEFAULT_TEXT_STROKE_STYLE);
  } else {
    textStyle = DEFAULT_TEXT_STYLE.clone();
  }
  textStyle.setText(name);
  textStyle.setOffsetX(textOffset[0]);
  textStyle.setOffsetY(textOffset[1]);
  textStyle.setTextAlign(textAlign);
  const nameStyle = new Style_default({
    image: imageStyle,
    text: textStyle
  });
  return nameStyle;
}
function createFeatureStyleFunction(style, styleUrl, defaultStyle, sharedStyles, showPointNames) {
  return (
    /**
     * @param {Feature} feature feature.
     * @param {number} resolution Resolution.
     * @return {Array<Style>|Style} Style.
     */
    function(feature, resolution) {
      let drawName = showPointNames;
      let name = "";
      let multiGeometryPoints = [];
      if (drawName) {
        const geometry = feature.getGeometry();
        if (geometry) {
          if (geometry instanceof GeometryCollection_default) {
            multiGeometryPoints = geometry.getGeometriesArrayRecursive().filter(function(geometry2) {
              const type = geometry2.getType();
              return type === "Point" || type === "MultiPoint";
            });
            drawName = multiGeometryPoints.length > 0;
          } else {
            const type = geometry.getType();
            drawName = type === "Point" || type === "MultiPoint";
          }
        }
      }
      if (drawName) {
        name = /** @type {string} */
        feature.get("name");
        drawName = drawName && !!name;
        if (drawName && /&[^&]+;/.test(name)) {
          if (!TEXTAREA) {
            TEXTAREA = document.createElement("textarea");
          }
          TEXTAREA.innerHTML = name;
          name = TEXTAREA.value;
        }
      }
      let featureStyle = defaultStyle;
      if (style) {
        featureStyle = style;
      } else if (styleUrl) {
        featureStyle = findStyle(styleUrl, defaultStyle, sharedStyles);
      }
      if (drawName) {
        const nameStyle = createNameStyleFunction(featureStyle[0], name);
        if (multiGeometryPoints.length > 0) {
          nameStyle.setGeometry(new GeometryCollection_default(multiGeometryPoints));
          const baseStyle = new Style_default({
            geometry: featureStyle[0].getGeometry(),
            image: null,
            fill: featureStyle[0].getFill(),
            stroke: featureStyle[0].getStroke(),
            text: null
          });
          return [nameStyle, baseStyle].concat(featureStyle.slice(1));
        }
        return nameStyle;
      }
      return featureStyle;
    }
  );
}
function findStyle(styleValue, defaultStyle, sharedStyles) {
  if (Array.isArray(styleValue)) {
    return styleValue;
  }
  if (typeof styleValue === "string") {
    return findStyle(sharedStyles[styleValue], defaultStyle, sharedStyles);
  }
  return defaultStyle;
}
function readColor(node) {
  const s = getAllTextContent(node, false);
  const m = /^\s*#?\s*([0-9A-Fa-f]{8})\s*$/.exec(s);
  if (m) {
    const hexColor = m[1];
    return [
      parseInt(hexColor.substr(6, 2), 16),
      parseInt(hexColor.substr(4, 2), 16),
      parseInt(hexColor.substr(2, 2), 16),
      parseInt(hexColor.substr(0, 2), 16) / 255
    ];
  }
  return void 0;
}
function readFlatCoordinates(node) {
  let s = getAllTextContent(node, false);
  const flatCoordinates = [];
  s = s.replace(/\s*,\s*/g, ",");
  const re = /^\s*([+\-]?\d*\.?\d+(?:e[+\-]?\d+)?),([+\-]?\d*\.?\d+(?:e[+\-]?\d+)?)(?:\s+|,|$)(?:([+\-]?\d*\.?\d+(?:e[+\-]?\d+)?)(?:\s+|$))?\s*/i;
  let m;
  while (m = re.exec(s)) {
    const x = parseFloat(m[1]);
    const y = parseFloat(m[2]);
    const z = m[3] ? parseFloat(m[3]) : 0;
    flatCoordinates.push(x, y, z);
    s = s.substr(m[0].length);
  }
  if (s !== "") {
    return void 0;
  }
  return flatCoordinates;
}
function readURI(node) {
  const s = getAllTextContent(node, false).trim();
  let baseURI = node.baseURI;
  if (!baseURI || baseURI == "about:blank") {
    baseURI = window.location.href;
  }
  if (baseURI) {
    const url = new URL(s, baseURI);
    return url.href;
  }
  return s;
}
function readStyleURL(node) {
  const s = getAllTextContent(node, false).trim().replace(/^(?!.*#)/, "#");
  let baseURI = node.baseURI;
  if (!baseURI || baseURI == "about:blank") {
    baseURI = window.location.href;
  }
  if (baseURI) {
    const url = new URL(s, baseURI);
    return url.href;
  }
  return s;
}
function readVec2(node) {
  const xunits = node.getAttribute("xunits");
  const yunits = node.getAttribute("yunits");
  let origin;
  if (xunits !== "insetPixels") {
    if (yunits !== "insetPixels") {
      origin = "bottom-left";
    } else {
      origin = "top-left";
    }
  } else {
    if (yunits !== "insetPixels") {
      origin = "bottom-right";
    } else {
      origin = "top-right";
    }
  }
  return {
    x: parseFloat(node.getAttribute("x")),
    xunits: ICON_ANCHOR_UNITS_MAP[xunits],
    y: parseFloat(node.getAttribute("y")),
    yunits: ICON_ANCHOR_UNITS_MAP[yunits],
    origin
  };
}
function readScale(node) {
  return readDecimal(node);
}
var STYLE_MAP_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "Pair": pairDataParser
});
function readStyleMapValue(node, objectStack) {
  return pushParseAndPop(void 0, STYLE_MAP_PARSERS, node, objectStack, this);
}
var ICON_STYLE_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "Icon": makeObjectPropertySetter(readIcon),
  "color": makeObjectPropertySetter(readColor),
  "heading": makeObjectPropertySetter(readDecimal),
  "hotSpot": makeObjectPropertySetter(readVec2),
  "scale": makeObjectPropertySetter(readScale)
});
function iconStyleParser(node, objectStack) {
  const object = pushParseAndPop({}, ICON_STYLE_PARSERS, node, objectStack);
  if (!object) {
    return;
  }
  const styleObject = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const IconObject = "Icon" in object ? object["Icon"] : {};
  const drawIcon = !("Icon" in object) || Object.keys(IconObject).length > 0;
  let src;
  const href = (
    /** @type {string|undefined} */
    IconObject["href"]
  );
  if (href) {
    src = href;
  } else if (drawIcon) {
    src = DEFAULT_IMAGE_STYLE_SRC;
  }
  let anchor, anchorXUnits, anchorYUnits;
  let anchorOrigin = "bottom-left";
  const hotSpot = (
    /** @type {Vec2|undefined} */
    object["hotSpot"]
  );
  if (hotSpot) {
    anchor = [hotSpot.x, hotSpot.y];
    anchorXUnits = hotSpot.xunits;
    anchorYUnits = hotSpot.yunits;
    anchorOrigin = hotSpot.origin;
  } else if (/^https?:\/\/maps\.(?:google|gstatic)\.com\//.test(src)) {
    if (src.includes("pushpin")) {
      anchor = DEFAULT_IMAGE_STYLE_ANCHOR;
      anchorXUnits = DEFAULT_IMAGE_STYLE_ANCHOR_X_UNITS;
      anchorYUnits = DEFAULT_IMAGE_STYLE_ANCHOR_Y_UNITS;
    } else if (src.includes("arrow-reverse")) {
      anchor = [54, 42];
      anchorXUnits = DEFAULT_IMAGE_STYLE_ANCHOR_X_UNITS;
      anchorYUnits = DEFAULT_IMAGE_STYLE_ANCHOR_Y_UNITS;
    } else if (src.includes("paddle")) {
      anchor = [32, 1];
      anchorXUnits = DEFAULT_IMAGE_STYLE_ANCHOR_X_UNITS;
      anchorYUnits = DEFAULT_IMAGE_STYLE_ANCHOR_Y_UNITS;
    }
  }
  let offset;
  const x = (
    /** @type {number|undefined} */
    IconObject["x"]
  );
  const y = (
    /** @type {number|undefined} */
    IconObject["y"]
  );
  if (x !== void 0 && y !== void 0) {
    offset = [x, y];
  }
  let size;
  const w = (
    /** @type {number|undefined} */
    IconObject["w"]
  );
  const h = (
    /** @type {number|undefined} */
    IconObject["h"]
  );
  if (w !== void 0 && h !== void 0) {
    size = [w, h];
  }
  let rotation;
  const heading = (
    /** @type {number} */
    object["heading"]
  );
  if (heading !== void 0) {
    rotation = toRadians(heading);
  }
  const scale = (
    /** @type {number|undefined} */
    object["scale"]
  );
  const color = (
    /** @type {Array<number>|undefined} */
    object["color"]
  );
  if (drawIcon) {
    if (src == DEFAULT_IMAGE_STYLE_SRC) {
      size = DEFAULT_IMAGE_STYLE_SIZE;
    }
    const imageStyle = new Icon_default({
      anchor,
      anchorOrigin,
      anchorXUnits,
      anchorYUnits,
      crossOrigin: this.crossOrigin_,
      offset,
      offsetOrigin: "bottom-left",
      rotation,
      scale,
      size,
      src: this.iconUrlFunction_(src),
      color
    });
    const imageScale = imageStyle.getScaleArray()[0];
    const imageSize = imageStyle.getSize();
    if (imageSize === null) {
      const imageState = imageStyle.getImageState();
      if (imageState === ImageState_default.IDLE || imageState === ImageState_default.LOADING) {
        const listener = function() {
          const imageState2 = imageStyle.getImageState();
          if (!(imageState2 === ImageState_default.IDLE || imageState2 === ImageState_default.LOADING)) {
            const imageSize2 = imageStyle.getSize();
            if (imageSize2 && imageSize2.length == 2) {
              const resizeScale = scaleForSize(imageSize2);
              imageStyle.setScale(imageScale * resizeScale);
            }
            imageStyle.unlistenImageChange(listener);
          }
        };
        imageStyle.listenImageChange(listener);
        if (imageState === ImageState_default.IDLE) {
          imageStyle.load();
        }
      }
    } else if (imageSize.length == 2) {
      const resizeScale = scaleForSize(imageSize);
      imageStyle.setScale(imageScale * resizeScale);
    }
    styleObject["imageStyle"] = imageStyle;
  } else {
    styleObject["imageStyle"] = DEFAULT_NO_IMAGE_STYLE;
  }
}
var LABEL_STYLE_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "color": makeObjectPropertySetter(readColor),
  "scale": makeObjectPropertySetter(readScale)
});
function labelStyleParser(node, objectStack) {
  const object = pushParseAndPop({}, LABEL_STYLE_PARSERS, node, objectStack);
  if (!object) {
    return;
  }
  const styleObject = objectStack[objectStack.length - 1];
  const textStyle = new Text_default({
    fill: new Fill_default({
      color: (
        /** @type {import("../color.js").Color} */
        "color" in object ? object["color"] : DEFAULT_COLOR
      )
    }),
    scale: (
      /** @type {number|undefined} */
      object["scale"]
    )
  });
  styleObject["textStyle"] = textStyle;
}
var LINE_STYLE_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "color": makeObjectPropertySetter(readColor),
  "width": makeObjectPropertySetter(readDecimal)
});
function lineStyleParser(node, objectStack) {
  const object = pushParseAndPop({}, LINE_STYLE_PARSERS, node, objectStack);
  if (!object) {
    return;
  }
  const styleObject = objectStack[objectStack.length - 1];
  const strokeStyle = new Stroke_default({
    color: (
      /** @type {import("../color.js").Color} */
      "color" in object ? object["color"] : DEFAULT_COLOR
    ),
    width: (
      /** @type {number} */
      "width" in object ? object["width"] : 1
    )
  });
  styleObject["strokeStyle"] = strokeStyle;
}
var POLY_STYLE_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "color": makeObjectPropertySetter(readColor),
  "fill": makeObjectPropertySetter(readBoolean),
  "outline": makeObjectPropertySetter(readBoolean)
});
function polyStyleParser(node, objectStack) {
  const object = pushParseAndPop({}, POLY_STYLE_PARSERS, node, objectStack);
  if (!object) {
    return;
  }
  const styleObject = objectStack[objectStack.length - 1];
  const fillStyle = new Fill_default({
    color: (
      /** @type {import("../color.js").Color} */
      "color" in object ? object["color"] : DEFAULT_COLOR
    )
  });
  styleObject["fillStyle"] = fillStyle;
  const fill = (
    /** @type {boolean|undefined} */
    object["fill"]
  );
  if (fill !== void 0) {
    styleObject["fill"] = fill;
  }
  const outline = (
    /** @type {boolean|undefined} */
    object["outline"]
  );
  if (outline !== void 0) {
    styleObject["outline"] = outline;
  }
}
var FLAT_LINEAR_RING_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "coordinates": makeReplacer(readFlatCoordinates)
});
function readFlatLinearRing(node, objectStack) {
  return pushParseAndPop(null, FLAT_LINEAR_RING_PARSERS, node, objectStack);
}
function gxCoordParser(node, objectStack) {
  const gxTrackObject = (
    /** @type {GxTrackObject} */
    objectStack[objectStack.length - 1]
  );
  const coordinates = gxTrackObject.coordinates;
  const s = getAllTextContent(node, false);
  const re = /^\s*([+\-]?\d+(?:\.\d*)?(?:e[+\-]?\d*)?)\s+([+\-]?\d+(?:\.\d*)?(?:e[+\-]?\d*)?)\s+([+\-]?\d+(?:\.\d*)?(?:e[+\-]?\d*)?)\s*$/i;
  const m = re.exec(s);
  if (m) {
    const x = parseFloat(m[1]);
    const y = parseFloat(m[2]);
    const z = parseFloat(m[3]);
    coordinates.push([x, y, z]);
  } else {
    coordinates.push([]);
  }
}
var GX_MULTITRACK_GEOMETRY_PARSERS = makeStructureNS(GX_NAMESPACE_URIS, {
  "Track": makeArrayPusher(readGxTrack)
});
function readGxMultiTrack(node, objectStack) {
  const lineStrings = pushParseAndPop(
    [],
    GX_MULTITRACK_GEOMETRY_PARSERS,
    node,
    objectStack
  );
  if (!lineStrings) {
    return void 0;
  }
  return new MultiLineString_default(lineStrings);
}
var GX_TRACK_PARSERS = makeStructureNS(
  NAMESPACE_URIS2,
  {
    "when": whenParser
  },
  makeStructureNS(GX_NAMESPACE_URIS, {
    "coord": gxCoordParser
  })
);
function readGxTrack(node, objectStack) {
  const gxTrackObject = pushParseAndPop(
    /** @type {GxTrackObject} */
    {
      coordinates: [],
      whens: []
    },
    GX_TRACK_PARSERS,
    node,
    objectStack
  );
  if (!gxTrackObject) {
    return void 0;
  }
  const flatCoordinates = [];
  const coordinates = gxTrackObject.coordinates;
  const whens = gxTrackObject.whens;
  for (let i = 0, ii = Math.min(coordinates.length, whens.length); i < ii; ++i) {
    if (coordinates[i].length == 3) {
      flatCoordinates.push(
        coordinates[i][0],
        coordinates[i][1],
        coordinates[i][2],
        whens[i]
      );
    }
  }
  return new LineString_default(flatCoordinates, "XYZM");
}
var ICON_PARSERS = makeStructureNS(
  NAMESPACE_URIS2,
  {
    "href": makeObjectPropertySetter(readURI)
  },
  makeStructureNS(GX_NAMESPACE_URIS, {
    "x": makeObjectPropertySetter(readDecimal),
    "y": makeObjectPropertySetter(readDecimal),
    "w": makeObjectPropertySetter(readDecimal),
    "h": makeObjectPropertySetter(readDecimal)
  })
);
function readIcon(node, objectStack) {
  const iconObject = pushParseAndPop({}, ICON_PARSERS, node, objectStack);
  if (iconObject) {
    return iconObject;
  }
  return null;
}
var GEOMETRY_FLAT_COORDINATES_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "coordinates": makeReplacer(readFlatCoordinates)
});
function readFlatCoordinatesFromNode(node, objectStack) {
  return pushParseAndPop(
    null,
    GEOMETRY_FLAT_COORDINATES_PARSERS,
    node,
    objectStack
  );
}
var EXTRUDE_AND_ALTITUDE_MODE_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "extrude": makeObjectPropertySetter(readBoolean),
  "tessellate": makeObjectPropertySetter(readBoolean),
  "altitudeMode": makeObjectPropertySetter(readString)
});
function readLineString(node, objectStack) {
  const properties = pushParseAndPop(
    {},
    EXTRUDE_AND_ALTITUDE_MODE_PARSERS,
    node,
    objectStack
  );
  const flatCoordinates = readFlatCoordinatesFromNode(node, objectStack);
  if (flatCoordinates) {
    const lineString = new LineString_default(flatCoordinates, "XYZ");
    lineString.setProperties(properties, true);
    return lineString;
  }
  return void 0;
}
function readLinearRing(node, objectStack) {
  const properties = pushParseAndPop(
    {},
    EXTRUDE_AND_ALTITUDE_MODE_PARSERS,
    node,
    objectStack
  );
  const flatCoordinates = readFlatCoordinatesFromNode(node, objectStack);
  if (flatCoordinates) {
    const polygon = new Polygon_default(flatCoordinates, "XYZ", [
      flatCoordinates.length
    ]);
    polygon.setProperties(properties, true);
    return polygon;
  }
  return void 0;
}
var MULTI_GEOMETRY_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "LineString": makeArrayPusher(readLineString),
  "LinearRing": makeArrayPusher(readLinearRing),
  "MultiGeometry": makeArrayPusher(readMultiGeometry),
  "Point": makeArrayPusher(readPoint),
  "Polygon": makeArrayPusher(readPolygon)
});
function readMultiGeometry(node, objectStack) {
  const geometries = pushParseAndPop(
    [],
    MULTI_GEOMETRY_PARSERS,
    node,
    objectStack
  );
  if (!geometries) {
    return null;
  }
  if (geometries.length === 0) {
    return new GeometryCollection_default(geometries);
  }
  let multiGeometry;
  let homogeneous = true;
  const type = geometries[0].getType();
  let geometry;
  for (let i = 1, ii = geometries.length; i < ii; ++i) {
    geometry = geometries[i];
    if (geometry.getType() != type) {
      homogeneous = false;
      break;
    }
  }
  if (homogeneous) {
    let layout;
    let flatCoordinates;
    if (type == "Point") {
      const point = geometries[0];
      layout = point.getLayout();
      flatCoordinates = point.getFlatCoordinates();
      for (let i = 1, ii = geometries.length; i < ii; ++i) {
        geometry = geometries[i];
        extend(flatCoordinates, geometry.getFlatCoordinates());
      }
      multiGeometry = new MultiPoint_default(flatCoordinates, layout);
      setCommonGeometryProperties(multiGeometry, geometries);
    } else if (type == "LineString") {
      multiGeometry = new MultiLineString_default(geometries);
      setCommonGeometryProperties(multiGeometry, geometries);
    } else if (type == "Polygon") {
      multiGeometry = new MultiPolygon_default(geometries);
      setCommonGeometryProperties(multiGeometry, geometries);
    } else if (type == "GeometryCollection") {
      multiGeometry = new GeometryCollection_default(geometries);
    } else {
      assert(false, 37);
    }
  } else {
    multiGeometry = new GeometryCollection_default(geometries);
  }
  return (
    /** @type {import("../geom/Geometry.js").default} */
    multiGeometry
  );
}
function readPoint(node, objectStack) {
  const properties = pushParseAndPop(
    {},
    EXTRUDE_AND_ALTITUDE_MODE_PARSERS,
    node,
    objectStack
  );
  const flatCoordinates = readFlatCoordinatesFromNode(node, objectStack);
  if (flatCoordinates) {
    const point = new Point_default(flatCoordinates, "XYZ");
    point.setProperties(properties, true);
    return point;
  }
  return void 0;
}
var FLAT_LINEAR_RINGS_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "innerBoundaryIs": innerBoundaryIsParser,
  "outerBoundaryIs": outerBoundaryIsParser
});
function readPolygon(node, objectStack) {
  const properties = pushParseAndPop(
    /** @type {Object<string,*>} */
    {},
    EXTRUDE_AND_ALTITUDE_MODE_PARSERS,
    node,
    objectStack
  );
  const flatLinearRings = pushParseAndPop(
    [null],
    FLAT_LINEAR_RINGS_PARSERS,
    node,
    objectStack
  );
  if (flatLinearRings && flatLinearRings[0]) {
    const flatCoordinates = flatLinearRings[0];
    const ends = [flatCoordinates.length];
    for (let i = 1, ii = flatLinearRings.length; i < ii; ++i) {
      extend(flatCoordinates, flatLinearRings[i]);
      ends.push(flatCoordinates.length);
    }
    const polygon = new Polygon_default(flatCoordinates, "XYZ", ends);
    polygon.setProperties(properties, true);
    return polygon;
  }
  return void 0;
}
var STYLE_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "IconStyle": iconStyleParser,
  "LabelStyle": labelStyleParser,
  "LineStyle": lineStyleParser,
  "PolyStyle": polyStyleParser
});
function readStyle(node, objectStack) {
  const styleObject = pushParseAndPop(
    {},
    STYLE_PARSERS,
    node,
    objectStack,
    this
  );
  if (!styleObject) {
    return null;
  }
  let fillStyle = (
    /** @type {Fill} */
    "fillStyle" in styleObject ? styleObject["fillStyle"] : DEFAULT_FILL_STYLE
  );
  const fill = (
    /** @type {boolean|undefined} */
    styleObject["fill"]
  );
  if (fill !== void 0 && !fill) {
    fillStyle = null;
  }
  let imageStyle;
  if ("imageStyle" in styleObject) {
    if (styleObject["imageStyle"] != DEFAULT_NO_IMAGE_STYLE) {
      imageStyle = /** @type {import("../style/Image.js").default} */
      styleObject["imageStyle"];
    }
  } else {
    imageStyle = DEFAULT_IMAGE_STYLE;
  }
  const textStyle = (
    /** @type {Text} */
    "textStyle" in styleObject ? styleObject["textStyle"] : DEFAULT_TEXT_STYLE
  );
  const strokeStyle = (
    /** @type {Stroke} */
    "strokeStyle" in styleObject ? styleObject["strokeStyle"] : DEFAULT_STROKE_STYLE
  );
  const outline = (
    /** @type {boolean|undefined} */
    styleObject["outline"]
  );
  if (outline !== void 0 && !outline) {
    return [
      new Style_default({
        geometry: function(feature) {
          const geometry = feature.getGeometry();
          const type = geometry.getType();
          if (type === "GeometryCollection") {
            const collection = (
              /** @type {import("../geom/GeometryCollection").default} */
              geometry
            );
            return new GeometryCollection_default(
              collection.getGeometriesArrayRecursive().filter(function(geometry2) {
                const type2 = geometry2.getType();
                return type2 !== "Polygon" && type2 !== "MultiPolygon";
              })
            );
          }
          if (type !== "Polygon" && type !== "MultiPolygon") {
            return geometry;
          }
        },
        fill: fillStyle,
        image: imageStyle,
        stroke: strokeStyle,
        text: textStyle,
        zIndex: void 0
        // FIXME
      }),
      new Style_default({
        geometry: function(feature) {
          const geometry = feature.getGeometry();
          const type = geometry.getType();
          if (type === "GeometryCollection") {
            const collection = (
              /** @type {import("../geom/GeometryCollection").default} */
              geometry
            );
            return new GeometryCollection_default(
              collection.getGeometriesArrayRecursive().filter(function(geometry2) {
                const type2 = geometry2.getType();
                return type2 === "Polygon" || type2 === "MultiPolygon";
              })
            );
          }
          if (type === "Polygon" || type === "MultiPolygon") {
            return geometry;
          }
        },
        fill: fillStyle,
        stroke: null,
        zIndex: void 0
        // FIXME
      })
    ];
  }
  return [
    new Style_default({
      fill: fillStyle,
      image: imageStyle,
      stroke: strokeStyle,
      text: textStyle,
      zIndex: void 0
      // FIXME
    })
  ];
}
function setCommonGeometryProperties(multiGeometry, geometries) {
  const ii = geometries.length;
  const extrudes = new Array(geometries.length);
  const tessellates = new Array(geometries.length);
  const altitudeModes = new Array(geometries.length);
  let hasExtrude, hasTessellate, hasAltitudeMode;
  hasExtrude = false;
  hasTessellate = false;
  hasAltitudeMode = false;
  for (let i = 0; i < ii; ++i) {
    const geometry = geometries[i];
    extrudes[i] = geometry.get("extrude");
    tessellates[i] = geometry.get("tessellate");
    altitudeModes[i] = geometry.get("altitudeMode");
    hasExtrude = hasExtrude || extrudes[i] !== void 0;
    hasTessellate = hasTessellate || tessellates[i] !== void 0;
    hasAltitudeMode = hasAltitudeMode || altitudeModes[i];
  }
  if (hasExtrude) {
    multiGeometry.set("extrude", extrudes);
  }
  if (hasTessellate) {
    multiGeometry.set("tessellate", tessellates);
  }
  if (hasAltitudeMode) {
    multiGeometry.set("altitudeMode", altitudeModes);
  }
}
var DATA_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "displayName": makeObjectPropertySetter(readString),
  "value": makeObjectPropertySetter(readString)
});
function dataParser(node, objectStack) {
  const name = node.getAttribute("name");
  parseNode(DATA_PARSERS, node, objectStack);
  const featureObject = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  if (name && featureObject.displayName) {
    featureObject[name] = {
      value: featureObject.value,
      displayName: featureObject.displayName,
      toString: function() {
        return featureObject.value;
      }
    };
  } else if (name !== null) {
    featureObject[name] = featureObject.value;
  } else if (featureObject.displayName !== null) {
    featureObject[featureObject.displayName] = featureObject.value;
  }
  delete featureObject["value"];
}
var EXTENDED_DATA_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "Data": dataParser,
  "SchemaData": schemaDataParser
});
function extendedDataParser(node, objectStack) {
  parseNode(EXTENDED_DATA_PARSERS, node, objectStack);
}
function regionParser(node, objectStack) {
  parseNode(REGION_PARSERS, node, objectStack);
}
var PAIR_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "Style": makeObjectPropertySetter(readStyle),
  "key": makeObjectPropertySetter(readString),
  "styleUrl": makeObjectPropertySetter(readStyleURL)
});
function pairDataParser(node, objectStack) {
  const pairObject = pushParseAndPop({}, PAIR_PARSERS, node, objectStack, this);
  if (!pairObject) {
    return;
  }
  const key = (
    /** @type {string|undefined} */
    pairObject["key"]
  );
  if (key && key == "normal") {
    const styleUrl = (
      /** @type {string|undefined} */
      pairObject["styleUrl"]
    );
    if (styleUrl) {
      objectStack[objectStack.length - 1] = styleUrl;
    }
    const style = (
      /** @type {Style} */
      pairObject["Style"]
    );
    if (style) {
      objectStack[objectStack.length - 1] = style;
    }
  }
}
function placemarkStyleMapParser(node, objectStack) {
  const styleMapValue = readStyleMapValue.call(this, node, objectStack);
  if (!styleMapValue) {
    return;
  }
  const placemarkObject = objectStack[objectStack.length - 1];
  if (Array.isArray(styleMapValue)) {
    placemarkObject["Style"] = styleMapValue;
  } else if (typeof styleMapValue === "string") {
    placemarkObject["styleUrl"] = styleMapValue;
  } else {
    assert(false, 38);
  }
}
var SCHEMA_DATA_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "SimpleData": simpleDataParser
});
function schemaDataParser(node, objectStack) {
  parseNode(SCHEMA_DATA_PARSERS, node, objectStack);
}
function simpleDataParser(node, objectStack) {
  const name = node.getAttribute("name");
  if (name !== null) {
    const data = readString(node);
    const featureObject = (
      /** @type {Object} */
      objectStack[objectStack.length - 1]
    );
    featureObject[name] = data;
  }
}
var LAT_LON_ALT_BOX_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "altitudeMode": makeObjectPropertySetter(readString),
  "minAltitude": makeObjectPropertySetter(readDecimal),
  "maxAltitude": makeObjectPropertySetter(readDecimal),
  "north": makeObjectPropertySetter(readDecimal),
  "south": makeObjectPropertySetter(readDecimal),
  "east": makeObjectPropertySetter(readDecimal),
  "west": makeObjectPropertySetter(readDecimal)
});
function latLonAltBoxParser(node, objectStack) {
  const object = pushParseAndPop(
    {},
    LAT_LON_ALT_BOX_PARSERS,
    node,
    objectStack
  );
  if (!object) {
    return;
  }
  const regionObject = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const extent = [
    parseFloat(object["west"]),
    parseFloat(object["south"]),
    parseFloat(object["east"]),
    parseFloat(object["north"])
  ];
  regionObject["extent"] = extent;
  regionObject["altitudeMode"] = object["altitudeMode"];
  regionObject["minAltitude"] = parseFloat(object["minAltitude"]);
  regionObject["maxAltitude"] = parseFloat(object["maxAltitude"]);
}
var LOD_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "minLodPixels": makeObjectPropertySetter(readDecimal),
  "maxLodPixels": makeObjectPropertySetter(readDecimal),
  "minFadeExtent": makeObjectPropertySetter(readDecimal),
  "maxFadeExtent": makeObjectPropertySetter(readDecimal)
});
function lodParser(node, objectStack) {
  const object = pushParseAndPop({}, LOD_PARSERS, node, objectStack);
  if (!object) {
    return;
  }
  const lodObject = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  lodObject["minLodPixels"] = parseFloat(object["minLodPixels"]);
  lodObject["maxLodPixels"] = parseFloat(object["maxLodPixels"]);
  lodObject["minFadeExtent"] = parseFloat(object["minFadeExtent"]);
  lodObject["maxFadeExtent"] = parseFloat(object["maxFadeExtent"]);
}
var INNER_BOUNDARY_IS_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  // KML spec only allows one LinearRing  per innerBoundaryIs, but Google Earth
  // allows multiple, so we parse multiple here too.
  "LinearRing": makeArrayPusher(readFlatLinearRing)
});
function innerBoundaryIsParser(node, objectStack) {
  const innerBoundaryFlatLinearRings = pushParseAndPop(
    /** @type {Array<Array<number>>} */
    [],
    INNER_BOUNDARY_IS_PARSERS,
    node,
    objectStack
  );
  if (innerBoundaryFlatLinearRings.length > 0) {
    const flatLinearRings = (
      /** @type {Array<Array<number>>} */
      objectStack[objectStack.length - 1]
    );
    flatLinearRings.push(...innerBoundaryFlatLinearRings);
  }
}
var OUTER_BOUNDARY_IS_PARSERS = makeStructureNS(NAMESPACE_URIS2, {
  "LinearRing": makeReplacer(readFlatLinearRing)
});
function outerBoundaryIsParser(node, objectStack) {
  const flatLinearRing = pushParseAndPop(
    void 0,
    OUTER_BOUNDARY_IS_PARSERS,
    node,
    objectStack
  );
  if (flatLinearRing) {
    const flatLinearRings = (
      /** @type {Array<Array<number>>} */
      objectStack[objectStack.length - 1]
    );
    flatLinearRings[0] = flatLinearRing;
  }
}
function linkParser(node, objectStack) {
  parseNode(LINK_PARSERS2, node, objectStack);
}
function whenParser(node, objectStack) {
  const gxTrackObject = (
    /** @type {GxTrackObject} */
    objectStack[objectStack.length - 1]
  );
  const whens = gxTrackObject.whens;
  const s = getAllTextContent(node, false);
  const when = Date.parse(s);
  whens.push(isNaN(when) ? 0 : when);
}
function writeColorTextNode(node, color) {
  const rgba = asArray(color);
  const opacity = rgba.length == 4 ? rgba[3] : 1;
  const abgr = [opacity * 255, rgba[2], rgba[1], rgba[0]];
  for (let i = 0; i < 4; ++i) {
    const hex = Math.floor(
      /** @type {number} */
      abgr[i]
    ).toString(16);
    abgr[i] = hex.length == 1 ? "0" + hex : hex;
  }
  writeStringTextNode(node, abgr.join(""));
}
function writeCoordinatesTextNode(node, coordinates, objectStack) {
  const context = objectStack[objectStack.length - 1];
  const layout = context["layout"];
  const stride = context["stride"];
  let dimension;
  if (layout == "XY" || layout == "XYM") {
    dimension = 2;
  } else if (layout == "XYZ" || layout == "XYZM") {
    dimension = 3;
  } else {
    assert(false, 34);
  }
  const ii = coordinates.length;
  let text = "";
  if (ii > 0) {
    text += coordinates[0];
    for (let d = 1; d < dimension; ++d) {
      text += "," + coordinates[d];
    }
    for (let i = stride; i < ii; i += stride) {
      text += " " + coordinates[i];
      for (let d = 1; d < dimension; ++d) {
        text += "," + coordinates[i + d];
      }
    }
  }
  writeStringTextNode(node, text);
}
var EXTENDEDDATA_NODE_SERIALIZERS = makeStructureNS(NAMESPACE_URIS2, {
  "Data": makeChildAppender(writeDataNode),
  "value": makeChildAppender(writeDataNodeValue),
  "displayName": makeChildAppender(writeDataNodeName)
});
function writeDataNode(node, pair, objectStack) {
  node.setAttribute("name", pair.name);
  const context = { node };
  const value = pair.value;
  if (typeof value == "object") {
    if (value !== null && value.displayName) {
      pushSerializeAndPop(
        context,
        EXTENDEDDATA_NODE_SERIALIZERS,
        OBJECT_PROPERTY_NODE_FACTORY,
        [value.displayName],
        objectStack,
        ["displayName"]
      );
    }
    if (value !== null && value.value) {
      pushSerializeAndPop(
        context,
        EXTENDEDDATA_NODE_SERIALIZERS,
        OBJECT_PROPERTY_NODE_FACTORY,
        [value.value],
        objectStack,
        ["value"]
      );
    }
  } else {
    pushSerializeAndPop(
      context,
      EXTENDEDDATA_NODE_SERIALIZERS,
      OBJECT_PROPERTY_NODE_FACTORY,
      [value],
      objectStack,
      ["value"]
    );
  }
}
function writeDataNodeName(node, name) {
  writeCDATASection(node, name);
}
function writeDataNodeValue(node, value) {
  writeStringTextNode(node, value);
}
var DOCUMENT_SERIALIZERS = makeStructureNS(NAMESPACE_URIS2, {
  "Placemark": makeChildAppender(writePlacemark)
});
var DOCUMENT_NODE_FACTORY = function(value, objectStack, nodeName) {
  const parentNode = objectStack[objectStack.length - 1].node;
  return createElementNS(parentNode.namespaceURI, "Placemark");
};
function writeDocument(node, features, objectStack) {
  const context = { node };
  pushSerializeAndPop(
    context,
    DOCUMENT_SERIALIZERS,
    DOCUMENT_NODE_FACTORY,
    features,
    objectStack,
    void 0,
    this
  );
}
var DATA_NODE_FACTORY = makeSimpleNodeFactory("Data");
function writeExtendedData(node, namesAndValues, objectStack) {
  const context = { node };
  const names = namesAndValues.names;
  const values = namesAndValues.values;
  const length = names.length;
  for (let i = 0; i < length; i++) {
    pushSerializeAndPop(
      context,
      EXTENDEDDATA_NODE_SERIALIZERS,
      DATA_NODE_FACTORY,
      [{ name: names[i], value: values[i] }],
      objectStack
    );
  }
}
var ICON_SEQUENCE = makeStructureNS(
  NAMESPACE_URIS2,
  ["href"],
  makeStructureNS(GX_NAMESPACE_URIS, ["x", "y", "w", "h"])
);
var ICON_SERIALIZERS = makeStructureNS(
  NAMESPACE_URIS2,
  {
    "href": makeChildAppender(writeStringTextNode)
  },
  makeStructureNS(GX_NAMESPACE_URIS, {
    "x": makeChildAppender(writeDecimalTextNode),
    "y": makeChildAppender(writeDecimalTextNode),
    "w": makeChildAppender(writeDecimalTextNode),
    "h": makeChildAppender(writeDecimalTextNode)
  })
);
var GX_NODE_FACTORY = function(value, objectStack, nodeName) {
  return createElementNS(GX_NAMESPACE_URIS[0], "gx:" + nodeName);
};
function writeIcon(node, icon, objectStack) {
  const context = { node };
  const parentNode = objectStack[objectStack.length - 1].node;
  let orderedKeys = ICON_SEQUENCE[parentNode.namespaceURI];
  let values = makeSequence(icon, orderedKeys);
  pushSerializeAndPop(
    context,
    ICON_SERIALIZERS,
    OBJECT_PROPERTY_NODE_FACTORY,
    values,
    objectStack,
    orderedKeys
  );
  orderedKeys = ICON_SEQUENCE[GX_NAMESPACE_URIS[0]];
  values = makeSequence(icon, orderedKeys);
  pushSerializeAndPop(
    context,
    ICON_SERIALIZERS,
    GX_NODE_FACTORY,
    values,
    objectStack,
    orderedKeys
  );
}
var ICON_STYLE_SEQUENCE = makeStructureNS(NAMESPACE_URIS2, [
  "scale",
  "heading",
  "Icon",
  "color",
  "hotSpot"
]);
var ICON_STYLE_SERIALIZERS = makeStructureNS(NAMESPACE_URIS2, {
  "Icon": makeChildAppender(writeIcon),
  "color": makeChildAppender(writeColorTextNode),
  "heading": makeChildAppender(writeDecimalTextNode),
  "hotSpot": makeChildAppender(writeVec2),
  "scale": makeChildAppender(writeScaleTextNode)
});
function writeIconStyle(node, style, objectStack) {
  const context = { node };
  const properties = {};
  const src = style.getSrc();
  const size = style.getSize();
  const iconImageSize = style.getImageSize();
  const iconProperties = {
    "href": src
  };
  if (size) {
    iconProperties["w"] = size[0];
    iconProperties["h"] = size[1];
    const anchor = style.getAnchor();
    const origin = style.getOrigin();
    if (origin && iconImageSize && origin[0] !== 0 && origin[1] !== size[1]) {
      iconProperties["x"] = origin[0];
      iconProperties["y"] = iconImageSize[1] - (origin[1] + size[1]);
    }
    if (anchor && (anchor[0] !== size[0] / 2 || anchor[1] !== size[1] / 2)) {
      const hotSpot = {
        x: anchor[0],
        xunits: "pixels",
        y: size[1] - anchor[1],
        yunits: "pixels"
      };
      properties["hotSpot"] = hotSpot;
    }
  }
  properties["Icon"] = iconProperties;
  let scale = style.getScaleArray()[0];
  let imageSize = size;
  if (imageSize === null) {
    imageSize = DEFAULT_IMAGE_STYLE_SIZE;
  }
  if (imageSize.length == 2) {
    const resizeScale = scaleForSize(imageSize);
    scale = scale / resizeScale;
  }
  if (scale !== 1) {
    properties["scale"] = scale;
  }
  const rotation = style.getRotation();
  if (rotation !== 0) {
    properties["heading"] = rotation;
  }
  const color = style.getColor();
  if (color) {
    properties["color"] = color;
  }
  const parentNode = objectStack[objectStack.length - 1].node;
  const orderedKeys = ICON_STYLE_SEQUENCE[parentNode.namespaceURI];
  const values = makeSequence(properties, orderedKeys);
  pushSerializeAndPop(
    context,
    ICON_STYLE_SERIALIZERS,
    OBJECT_PROPERTY_NODE_FACTORY,
    values,
    objectStack,
    orderedKeys
  );
}
var LABEL_STYLE_SEQUENCE = makeStructureNS(NAMESPACE_URIS2, [
  "color",
  "scale"
]);
var LABEL_STYLE_SERIALIZERS = makeStructureNS(NAMESPACE_URIS2, {
  "color": makeChildAppender(writeColorTextNode),
  "scale": makeChildAppender(writeScaleTextNode)
});
function writeLabelStyle(node, style, objectStack) {
  const context = { node };
  const properties = {};
  const fill = style.getFill();
  if (fill) {
    properties["color"] = fill.getColor();
  }
  const scale = style.getScale();
  if (scale && scale !== 1) {
    properties["scale"] = scale;
  }
  const parentNode = objectStack[objectStack.length - 1].node;
  const orderedKeys = LABEL_STYLE_SEQUENCE[parentNode.namespaceURI];
  const values = makeSequence(properties, orderedKeys);
  pushSerializeAndPop(
    context,
    LABEL_STYLE_SERIALIZERS,
    OBJECT_PROPERTY_NODE_FACTORY,
    values,
    objectStack,
    orderedKeys
  );
}
var LINE_STYLE_SEQUENCE = makeStructureNS(NAMESPACE_URIS2, ["color", "width"]);
var LINE_STYLE_SERIALIZERS = makeStructureNS(NAMESPACE_URIS2, {
  "color": makeChildAppender(writeColorTextNode),
  "width": makeChildAppender(writeDecimalTextNode)
});
function writeLineStyle(node, style, objectStack) {
  const context = { node };
  const properties = {
    "color": style.getColor(),
    "width": Number(style.getWidth()) || 1
  };
  const parentNode = objectStack[objectStack.length - 1].node;
  const orderedKeys = LINE_STYLE_SEQUENCE[parentNode.namespaceURI];
  const values = makeSequence(properties, orderedKeys);
  pushSerializeAndPop(
    context,
    LINE_STYLE_SERIALIZERS,
    OBJECT_PROPERTY_NODE_FACTORY,
    values,
    objectStack,
    orderedKeys
  );
}
var GEOMETRY_TYPE_TO_NODENAME2 = {
  "Point": "Point",
  "LineString": "LineString",
  "LinearRing": "LinearRing",
  "Polygon": "Polygon",
  "MultiPoint": "MultiGeometry",
  "MultiLineString": "MultiGeometry",
  "MultiPolygon": "MultiGeometry",
  "GeometryCollection": "MultiGeometry"
};
var GEOMETRY_NODE_FACTORY = function(value, objectStack, nodeName) {
  if (value) {
    const parentNode = objectStack[objectStack.length - 1].node;
    return createElementNS(
      parentNode.namespaceURI,
      GEOMETRY_TYPE_TO_NODENAME2[
        /** @type {import("../geom/Geometry.js").default} */
        value.getType()
      ]
    );
  }
};
var POINT_NODE_FACTORY = makeSimpleNodeFactory("Point");
var LINE_STRING_NODE_FACTORY = makeSimpleNodeFactory("LineString");
var LINEAR_RING_NODE_FACTORY = makeSimpleNodeFactory("LinearRing");
var POLYGON_NODE_FACTORY = makeSimpleNodeFactory("Polygon");
var MULTI_GEOMETRY_SERIALIZERS = makeStructureNS(NAMESPACE_URIS2, {
  "LineString": makeChildAppender(writePrimitiveGeometry),
  "Point": makeChildAppender(writePrimitiveGeometry),
  "Polygon": makeChildAppender(writePolygon),
  "GeometryCollection": makeChildAppender(writeMultiGeometry)
});
function writeMultiGeometry(node, geometry, objectStack) {
  const context = { node };
  const type = geometry.getType();
  let geometries = [];
  let factory;
  if (type === "GeometryCollection") {
    geometry.getGeometriesArrayRecursive().forEach(function(geometry2) {
      const type2 = geometry2.getType();
      if (type2 === "MultiPoint") {
        geometries = geometries.concat(
          /** @type {MultiPoint} */
          geometry2.getPoints()
        );
      } else if (type2 === "MultiLineString") {
        geometries = geometries.concat(
          /** @type {MultiLineString} */
          geometry2.getLineStrings()
        );
      } else if (type2 === "MultiPolygon") {
        geometries = geometries.concat(
          /** @type {MultiPolygon} */
          geometry2.getPolygons()
        );
      } else if (type2 === "Point" || type2 === "LineString" || type2 === "Polygon") {
        geometries.push(geometry2);
      } else {
        assert(false, 39);
      }
    });
    factory = GEOMETRY_NODE_FACTORY;
  } else if (type === "MultiPoint") {
    geometries = /** @type {MultiPoint} */
    geometry.getPoints();
    factory = POINT_NODE_FACTORY;
  } else if (type === "MultiLineString") {
    geometries = /** @type {MultiLineString} */
    geometry.getLineStrings();
    factory = LINE_STRING_NODE_FACTORY;
  } else if (type === "MultiPolygon") {
    geometries = /** @type {MultiPolygon} */
    geometry.getPolygons();
    factory = POLYGON_NODE_FACTORY;
  } else {
    assert(false, 39);
  }
  pushSerializeAndPop(
    context,
    MULTI_GEOMETRY_SERIALIZERS,
    factory,
    geometries,
    objectStack
  );
}
var BOUNDARY_IS_SERIALIZERS = makeStructureNS(NAMESPACE_URIS2, {
  "LinearRing": makeChildAppender(writePrimitiveGeometry)
});
function writeBoundaryIs(node, linearRing, objectStack) {
  const context = { node };
  pushSerializeAndPop(
    context,
    BOUNDARY_IS_SERIALIZERS,
    LINEAR_RING_NODE_FACTORY,
    [linearRing],
    objectStack
  );
}
var PLACEMARK_SERIALIZERS = makeStructureNS(NAMESPACE_URIS2, {
  "ExtendedData": makeChildAppender(writeExtendedData),
  "MultiGeometry": makeChildAppender(writeMultiGeometry),
  "LineString": makeChildAppender(writePrimitiveGeometry),
  "LinearRing": makeChildAppender(writePrimitiveGeometry),
  "Point": makeChildAppender(writePrimitiveGeometry),
  "Polygon": makeChildAppender(writePolygon),
  "Style": makeChildAppender(writeStyle),
  "address": makeChildAppender(writeStringTextNode),
  "description": makeChildAppender(writeStringTextNode),
  "name": makeChildAppender(writeStringTextNode),
  "open": makeChildAppender(writeBooleanTextNode),
  "phoneNumber": makeChildAppender(writeStringTextNode),
  "styleUrl": makeChildAppender(writeStringTextNode),
  "visibility": makeChildAppender(writeBooleanTextNode)
});
var PLACEMARK_SEQUENCE = makeStructureNS(NAMESPACE_URIS2, [
  "name",
  "open",
  "visibility",
  "address",
  "phoneNumber",
  "description",
  "styleUrl",
  "Style"
]);
var EXTENDEDDATA_NODE_FACTORY = makeSimpleNodeFactory("ExtendedData");
function writePlacemark(node, feature, objectStack) {
  const context = { node };
  if (feature.getId()) {
    node.setAttribute(
      "id",
      /** @type {string} */
      feature.getId()
    );
  }
  const properties = feature.getProperties();
  const filter = {
    "address": 1,
    "description": 1,
    "name": 1,
    "open": 1,
    "phoneNumber": 1,
    "styleUrl": 1,
    "visibility": 1
  };
  filter[feature.getGeometryName()] = 1;
  const keys = Object.keys(properties || {}).sort().filter(function(v) {
    return !filter[v];
  });
  const styleFunction = feature.getStyleFunction();
  if (styleFunction) {
    const styles = styleFunction(feature, 0);
    if (styles) {
      const styleArray = Array.isArray(styles) ? styles : [styles];
      let pointStyles = styleArray;
      if (feature.getGeometry()) {
        pointStyles = styleArray.filter(function(style) {
          const geometry2 = style.getGeometryFunction()(feature);
          if (geometry2) {
            const type = geometry2.getType();
            if (type === "GeometryCollection") {
              return (
                /** @type {GeometryCollection} */
                geometry2.getGeometriesArrayRecursive().filter(function(geometry3) {
                  const type2 = geometry3.getType();
                  return type2 === "Point" || type2 === "MultiPoint";
                }).length
              );
            }
            return type === "Point" || type === "MultiPoint";
          }
        });
        "Point";
      }
      if (this.writeStyles_) {
        let lineStyles = styleArray;
        let polyStyles = styleArray;
        if (feature.getGeometry()) {
          lineStyles = styleArray.filter(function(style) {
            const geometry2 = style.getGeometryFunction()(feature);
            if (geometry2) {
              const type = geometry2.getType();
              if (type === "GeometryCollection") {
                return (
                  /** @type {GeometryCollection} */
                  geometry2.getGeometriesArrayRecursive().filter(function(geometry3) {
                    const type2 = geometry3.getType();
                    return type2 === "LineString" || type2 === "MultiLineString";
                  }).length
                );
              }
              return type === "LineString" || type === "MultiLineString";
            }
          });
          polyStyles = styleArray.filter(function(style) {
            const geometry2 = style.getGeometryFunction()(feature);
            if (geometry2) {
              const type = geometry2.getType();
              if (type === "GeometryCollection") {
                return (
                  /** @type {GeometryCollection} */
                  geometry2.getGeometriesArrayRecursive().filter(function(geometry3) {
                    const type2 = geometry3.getType();
                    return type2 === "Polygon" || type2 === "MultiPolygon";
                  }).length
                );
              }
              return type === "Polygon" || type === "MultiPolygon";
            }
          });
        }
        properties["Style"] = {
          pointStyles,
          lineStyles,
          polyStyles
        };
      }
      if (pointStyles.length && properties["name"] === void 0) {
        const textStyle = pointStyles[0].getText();
        if (textStyle) {
          properties["name"] = textStyle.getText();
        }
      }
    }
  }
  const parentNode = objectStack[objectStack.length - 1].node;
  const orderedKeys = PLACEMARK_SEQUENCE[parentNode.namespaceURI];
  const values = makeSequence(properties, orderedKeys);
  pushSerializeAndPop(
    context,
    PLACEMARK_SERIALIZERS,
    OBJECT_PROPERTY_NODE_FACTORY,
    values,
    objectStack,
    orderedKeys
  );
  if (keys.length > 0) {
    const sequence = makeSequence(properties, keys);
    const namesAndValues = { names: keys, values: sequence };
    pushSerializeAndPop(
      context,
      PLACEMARK_SERIALIZERS,
      EXTENDEDDATA_NODE_FACTORY,
      [namesAndValues],
      objectStack
    );
  }
  const options = (
    /** @type {import("./Feature.js").WriteOptions} */
    objectStack[0]
  );
  let geometry = feature.getGeometry();
  if (geometry) {
    geometry = transformGeometryWithOptions(geometry, true, options);
  }
  pushSerializeAndPop(
    context,
    PLACEMARK_SERIALIZERS,
    GEOMETRY_NODE_FACTORY,
    [geometry],
    objectStack
  );
}
var PRIMITIVE_GEOMETRY_SEQUENCE = makeStructureNS(NAMESPACE_URIS2, [
  "extrude",
  "tessellate",
  "altitudeMode",
  "coordinates"
]);
var PRIMITIVE_GEOMETRY_SERIALIZERS = makeStructureNS(NAMESPACE_URIS2, {
  "extrude": makeChildAppender(writeBooleanTextNode),
  "tessellate": makeChildAppender(writeBooleanTextNode),
  "altitudeMode": makeChildAppender(writeStringTextNode),
  "coordinates": makeChildAppender(writeCoordinatesTextNode)
});
function writePrimitiveGeometry(node, geometry, objectStack) {
  const flatCoordinates = geometry.getFlatCoordinates();
  const context = { node };
  context["layout"] = geometry.getLayout();
  context["stride"] = geometry.getStride();
  const properties = geometry.getProperties();
  properties.coordinates = flatCoordinates;
  const parentNode = objectStack[objectStack.length - 1].node;
  const orderedKeys = PRIMITIVE_GEOMETRY_SEQUENCE[parentNode.namespaceURI];
  const values = makeSequence(properties, orderedKeys);
  pushSerializeAndPop(
    context,
    PRIMITIVE_GEOMETRY_SERIALIZERS,
    OBJECT_PROPERTY_NODE_FACTORY,
    values,
    objectStack,
    orderedKeys
  );
}
var POLY_STYLE_SEQUENCE = makeStructureNS(NAMESPACE_URIS2, [
  "color",
  "fill",
  "outline"
]);
var POLYGON_SERIALIZERS = makeStructureNS(NAMESPACE_URIS2, {
  "outerBoundaryIs": makeChildAppender(writeBoundaryIs),
  "innerBoundaryIs": makeChildAppender(writeBoundaryIs)
});
var INNER_BOUNDARY_NODE_FACTORY = makeSimpleNodeFactory("innerBoundaryIs");
var OUTER_BOUNDARY_NODE_FACTORY = makeSimpleNodeFactory("outerBoundaryIs");
function writePolygon(node, polygon, objectStack) {
  const linearRings = polygon.getLinearRings();
  const outerRing = linearRings.shift();
  const context = { node };
  pushSerializeAndPop(
    context,
    POLYGON_SERIALIZERS,
    INNER_BOUNDARY_NODE_FACTORY,
    linearRings,
    objectStack
  );
  pushSerializeAndPop(
    context,
    POLYGON_SERIALIZERS,
    OUTER_BOUNDARY_NODE_FACTORY,
    [outerRing],
    objectStack
  );
}
var POLY_STYLE_SERIALIZERS = makeStructureNS(NAMESPACE_URIS2, {
  "color": makeChildAppender(writeColorTextNode),
  "fill": makeChildAppender(writeBooleanTextNode),
  "outline": makeChildAppender(writeBooleanTextNode)
});
function writePolyStyle(node, style, objectStack) {
  const context = { node };
  const fill = style.getFill();
  const stroke = style.getStroke();
  const properties = {
    "color": fill ? fill.getColor() : void 0,
    "fill": fill ? void 0 : false,
    "outline": stroke ? void 0 : false
  };
  const parentNode = objectStack[objectStack.length - 1].node;
  const orderedKeys = POLY_STYLE_SEQUENCE[parentNode.namespaceURI];
  const values = makeSequence(properties, orderedKeys);
  pushSerializeAndPop(
    context,
    POLY_STYLE_SERIALIZERS,
    OBJECT_PROPERTY_NODE_FACTORY,
    values,
    objectStack,
    orderedKeys
  );
}
function writeScaleTextNode(node, scale) {
  writeDecimalTextNode(node, Math.round(scale * 1e6) / 1e6);
}
var STYLE_SEQUENCE = makeStructureNS(NAMESPACE_URIS2, [
  "IconStyle",
  "LabelStyle",
  "LineStyle",
  "PolyStyle"
]);
var STYLE_SERIALIZERS = makeStructureNS(NAMESPACE_URIS2, {
  "IconStyle": makeChildAppender(writeIconStyle),
  "LabelStyle": makeChildAppender(writeLabelStyle),
  "LineStyle": makeChildAppender(writeLineStyle),
  "PolyStyle": makeChildAppender(writePolyStyle)
});
function writeStyle(node, styles, objectStack) {
  const context = { node };
  const properties = {};
  if (styles.pointStyles.length) {
    const textStyle = styles.pointStyles[0].getText();
    if (textStyle) {
      properties["LabelStyle"] = textStyle;
    }
    const imageStyle = styles.pointStyles[0].getImage();
    if (imageStyle && typeof /** @type {?} */
    imageStyle.getSrc === "function") {
      properties["IconStyle"] = imageStyle;
    }
  }
  if (styles.lineStyles.length) {
    const strokeStyle = styles.lineStyles[0].getStroke();
    if (strokeStyle) {
      properties["LineStyle"] = strokeStyle;
    }
  }
  if (styles.polyStyles.length) {
    const strokeStyle = styles.polyStyles[0].getStroke();
    if (strokeStyle && !properties["LineStyle"]) {
      properties["LineStyle"] = strokeStyle;
    }
    properties["PolyStyle"] = styles.polyStyles[0];
  }
  const parentNode = objectStack[objectStack.length - 1].node;
  const orderedKeys = STYLE_SEQUENCE[parentNode.namespaceURI];
  const values = makeSequence(properties, orderedKeys);
  pushSerializeAndPop(
    context,
    STYLE_SERIALIZERS,
    OBJECT_PROPERTY_NODE_FACTORY,
    values,
    objectStack,
    orderedKeys
  );
}
function writeVec2(node, vec2) {
  node.setAttribute("x", String(vec2.x));
  node.setAttribute("y", String(vec2.y));
  node.setAttribute("xunits", vec2.xunits);
  node.setAttribute("yunits", vec2.yunits);
}
var KML_default = KML;

// node_modules/ol/format/XML.js
var XML = class {
  /**
   * Read the source document.
   *
   * @param {Document|Element|string} source The XML source.
   * @return {Object} An object representing the source.
   * @api
   */
  read(source) {
    if (!source) {
      return null;
    }
    if (typeof source === "string") {
      const doc = parse(source);
      return this.readFromDocument(doc);
    }
    if (isDocument(source)) {
      return this.readFromDocument(
        /** @type {Document} */
        source
      );
    }
    return this.readFromNode(
      /** @type {Element} */
      source
    );
  }
  /**
   * @param {Document} doc Document.
   * @return {Object} Object
   */
  readFromDocument(doc) {
    for (let n = doc.firstChild; n; n = n.nextSibling) {
      if (n.nodeType == Node.ELEMENT_NODE) {
        return this.readFromNode(
          /** @type {Element} */
          n
        );
      }
    }
    return null;
  }
  /**
   * @abstract
   * @param {Element} node Node.
   * @return {Object} Object
   */
  readFromNode(node) {
  }
};
var XML_default = XML;

// node_modules/ol/format/xlink.js
var NAMESPACE_URI = "http://www.w3.org/1999/xlink";
function readHref(node) {
  return node.getAttributeNS(NAMESPACE_URI, "href");
}

// node_modules/ol/format/OWS.js
var NAMESPACE_URIS3 = [null, "http://www.opengis.net/ows/1.1"];
var PARSERS = makeStructureNS(NAMESPACE_URIS3, {
  "ServiceIdentification": makeObjectPropertySetter(readServiceIdentification),
  "ServiceProvider": makeObjectPropertySetter(readServiceProvider),
  "OperationsMetadata": makeObjectPropertySetter(readOperationsMetadata)
});
var OWS = class extends XML_default {
  constructor() {
    super();
  }
  /**
   * @param {Element} node Node.
   * @return {Object} Object
   */
  readFromNode(node) {
    const owsObject = pushParseAndPop({}, PARSERS, node, []);
    return owsObject ? owsObject : null;
  }
};
var ADDRESS_PARSERS = makeStructureNS(NAMESPACE_URIS3, {
  "DeliveryPoint": makeObjectPropertySetter(readString),
  "City": makeObjectPropertySetter(readString),
  "AdministrativeArea": makeObjectPropertySetter(readString),
  "PostalCode": makeObjectPropertySetter(readString),
  "Country": makeObjectPropertySetter(readString),
  "ElectronicMailAddress": makeObjectPropertySetter(readString)
});
var ALLOWED_VALUES_PARSERS = makeStructureNS(NAMESPACE_URIS3, {
  "Value": makeObjectPropertyPusher(readValue)
});
var CONSTRAINT_PARSERS = makeStructureNS(NAMESPACE_URIS3, {
  "AllowedValues": makeObjectPropertySetter(readAllowedValues)
});
var CONTACT_INFO_PARSERS = makeStructureNS(NAMESPACE_URIS3, {
  "Phone": makeObjectPropertySetter(readPhone),
  "Address": makeObjectPropertySetter(readAddress)
});
var DCP_PARSERS = makeStructureNS(NAMESPACE_URIS3, {
  "HTTP": makeObjectPropertySetter(readHttp)
});
var HTTP_PARSERS = makeStructureNS(NAMESPACE_URIS3, {
  "Get": makeObjectPropertyPusher(readGet),
  "Post": void 0
  // TODO
});
var OPERATION_PARSERS = makeStructureNS(NAMESPACE_URIS3, {
  "DCP": makeObjectPropertySetter(readDcp)
});
var OPERATIONS_METADATA_PARSERS = makeStructureNS(NAMESPACE_URIS3, {
  "Operation": readOperation
});
var PHONE_PARSERS = makeStructureNS(NAMESPACE_URIS3, {
  "Voice": makeObjectPropertySetter(readString),
  "Facsimile": makeObjectPropertySetter(readString)
});
var REQUEST_METHOD_PARSERS = makeStructureNS(NAMESPACE_URIS3, {
  "Constraint": makeObjectPropertyPusher(readConstraint)
});
var SERVICE_CONTACT_PARSERS = makeStructureNS(NAMESPACE_URIS3, {
  "IndividualName": makeObjectPropertySetter(readString),
  "PositionName": makeObjectPropertySetter(readString),
  "ContactInfo": makeObjectPropertySetter(readContactInfo)
});
var SERVICE_IDENTIFICATION_PARSERS = makeStructureNS(NAMESPACE_URIS3, {
  "Abstract": makeObjectPropertySetter(readString),
  "AccessConstraints": makeObjectPropertySetter(readString),
  "Fees": makeObjectPropertySetter(readString),
  "Title": makeObjectPropertySetter(readString),
  "ServiceTypeVersion": makeObjectPropertySetter(readString),
  "ServiceType": makeObjectPropertySetter(readString)
});
var SERVICE_PROVIDER_PARSERS = makeStructureNS(NAMESPACE_URIS3, {
  "ProviderName": makeObjectPropertySetter(readString),
  "ProviderSite": makeObjectPropertySetter(readHref),
  "ServiceContact": makeObjectPropertySetter(readServiceContact)
});
function readAddress(node, objectStack) {
  return pushParseAndPop({}, ADDRESS_PARSERS, node, objectStack);
}
function readAllowedValues(node, objectStack) {
  return pushParseAndPop({}, ALLOWED_VALUES_PARSERS, node, objectStack);
}
function readConstraint(node, objectStack) {
  const name = node.getAttribute("name");
  if (!name) {
    return void 0;
  }
  return pushParseAndPop({ "name": name }, CONSTRAINT_PARSERS, node, objectStack);
}
function readContactInfo(node, objectStack) {
  return pushParseAndPop({}, CONTACT_INFO_PARSERS, node, objectStack);
}
function readDcp(node, objectStack) {
  return pushParseAndPop({}, DCP_PARSERS, node, objectStack);
}
function readGet(node, objectStack) {
  const href = readHref(node);
  if (!href) {
    return void 0;
  }
  return pushParseAndPop(
    { "href": href },
    REQUEST_METHOD_PARSERS,
    node,
    objectStack
  );
}
function readHttp(node, objectStack) {
  return pushParseAndPop({}, HTTP_PARSERS, node, objectStack);
}
function readOperation(node, objectStack) {
  const name = node.getAttribute("name");
  const value = pushParseAndPop({}, OPERATION_PARSERS, node, objectStack);
  if (!value) {
    return void 0;
  }
  const object = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  object[name] = value;
}
function readOperationsMetadata(node, objectStack) {
  return pushParseAndPop({}, OPERATIONS_METADATA_PARSERS, node, objectStack);
}
function readPhone(node, objectStack) {
  return pushParseAndPop({}, PHONE_PARSERS, node, objectStack);
}
function readServiceIdentification(node, objectStack) {
  return pushParseAndPop({}, SERVICE_IDENTIFICATION_PARSERS, node, objectStack);
}
function readServiceContact(node, objectStack) {
  return pushParseAndPop({}, SERVICE_CONTACT_PARSERS, node, objectStack);
}
function readServiceProvider(node, objectStack) {
  return pushParseAndPop({}, SERVICE_PROVIDER_PARSERS, node, objectStack);
}
function readValue(node, objectStack) {
  return readString(node);
}
var OWS_default = OWS;

// node_modules/ol/geom/flat/flip.js
function flipXY(flatCoordinates, offset, end, stride, dest, destOffset) {
  if (dest !== void 0) {
    dest = dest;
    destOffset = destOffset !== void 0 ? destOffset : 0;
  } else {
    dest = [];
    destOffset = 0;
  }
  let j = offset;
  while (j < end) {
    const x = flatCoordinates[j++];
    dest[destOffset++] = flatCoordinates[j++];
    dest[destOffset++] = x;
    for (let k = 2; k < stride; ++k) {
      dest[destOffset++] = flatCoordinates[j++];
    }
  }
  dest.length = destOffset;
  return dest;
}

// node_modules/ol/format/Polyline.js
var Polyline = class extends TextFeature_default {
  /**
   * @param {Options} [options] Optional configuration object.
   */
  constructor(options) {
    super();
    options = options ? options : {};
    this.dataProjection = get("EPSG:4326");
    this.factor_ = options.factor ? options.factor : 1e5;
    this.geometryLayout_ = options.geometryLayout ? options.geometryLayout : "XY";
  }
  /**
   * @protected
   * @param {string} text Text.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @return {import("../Feature.js").default} Feature.
   */
  readFeatureFromText(text, options) {
    const geometry = this.readGeometryFromText(text, options);
    return new Feature_default(geometry);
  }
  /**
   * @param {string} text Text.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {Array<Feature>} Features.
   */
  readFeaturesFromText(text, options) {
    const feature = this.readFeatureFromText(text, options);
    return [feature];
  }
  /**
   * @param {string} text Text.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {import("../geom/Geometry.js").default} Geometry.
   */
  readGeometryFromText(text, options) {
    const stride = getStrideForLayout(this.geometryLayout_);
    const flatCoordinates = decodeDeltas(text, stride, this.factor_);
    flipXY(flatCoordinates, 0, flatCoordinates.length, stride, flatCoordinates);
    const coordinates = inflateCoordinates(
      flatCoordinates,
      0,
      flatCoordinates.length,
      stride
    );
    const lineString = new LineString_default(coordinates, this.geometryLayout_);
    return transformGeometryWithOptions(
      lineString,
      false,
      this.adaptOptions(options)
    );
  }
  /**
   * @param {import("../Feature.js").default<LineString>} feature Features.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @protected
   * @return {string} Text.
   */
  writeFeatureText(feature, options) {
    const geometry = feature.getGeometry();
    if (geometry) {
      return this.writeGeometryText(geometry, options);
    }
    assert(false, 40);
    return "";
  }
  /**
   * @param {Array<import("../Feature.js").default<LineString>>} features Features.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @protected
   * @return {string} Text.
   */
  writeFeaturesText(features, options) {
    return this.writeFeatureText(features[0], options);
  }
  /**
   * @param {LineString} geometry Geometry.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @protected
   * @return {string} Text.
   */
  writeGeometryText(geometry, options) {
    geometry = /** @type {LineString} */
    transformGeometryWithOptions(geometry, true, this.adaptOptions(options));
    const flatCoordinates = geometry.getFlatCoordinates();
    const stride = geometry.getStride();
    flipXY(flatCoordinates, 0, flatCoordinates.length, stride, flatCoordinates);
    return encodeDeltas(flatCoordinates, stride, this.factor_);
  }
};
function encodeDeltas(numbers, stride, factor) {
  factor = factor ? factor : 1e5;
  let d;
  const lastNumbers = new Array(stride);
  for (d = 0; d < stride; ++d) {
    lastNumbers[d] = 0;
  }
  for (let i = 0, ii = numbers.length; i < ii; ) {
    for (d = 0; d < stride; ++d, ++i) {
      const num = numbers[i];
      const delta = num - lastNumbers[d];
      lastNumbers[d] = num;
      numbers[i] = delta;
    }
  }
  return encodeFloats(numbers, factor);
}
function decodeDeltas(encoded, stride, factor) {
  factor = factor ? factor : 1e5;
  let d;
  const lastNumbers = new Array(stride);
  for (d = 0; d < stride; ++d) {
    lastNumbers[d] = 0;
  }
  const numbers = decodeFloats(encoded, factor);
  for (let i = 0, ii = numbers.length; i < ii; ) {
    for (d = 0; d < stride; ++d, ++i) {
      lastNumbers[d] += numbers[i];
      numbers[i] = lastNumbers[d];
    }
  }
  return numbers;
}
function encodeFloats(numbers, factor) {
  factor = factor ? factor : 1e5;
  for (let i = 0, ii = numbers.length; i < ii; ++i) {
    numbers[i] = Math.round(numbers[i] * factor);
  }
  return encodeSignedIntegers(numbers);
}
function decodeFloats(encoded, factor) {
  factor = factor ? factor : 1e5;
  const numbers = decodeSignedIntegers(encoded);
  for (let i = 0, ii = numbers.length; i < ii; ++i) {
    numbers[i] /= factor;
  }
  return numbers;
}
function encodeSignedIntegers(numbers) {
  for (let i = 0, ii = numbers.length; i < ii; ++i) {
    const num = numbers[i];
    numbers[i] = num < 0 ? ~(num << 1) : num << 1;
  }
  return encodeUnsignedIntegers(numbers);
}
function decodeSignedIntegers(encoded) {
  const numbers = decodeUnsignedIntegers(encoded);
  for (let i = 0, ii = numbers.length; i < ii; ++i) {
    const num = numbers[i];
    numbers[i] = num & 1 ? ~(num >> 1) : num >> 1;
  }
  return numbers;
}
function encodeUnsignedIntegers(numbers) {
  let encoded = "";
  for (let i = 0, ii = numbers.length; i < ii; ++i) {
    encoded += encodeUnsignedInteger(numbers[i]);
  }
  return encoded;
}
function decodeUnsignedIntegers(encoded) {
  const numbers = [];
  let current = 0;
  let shift = 0;
  for (let i = 0, ii = encoded.length; i < ii; ++i) {
    const b = encoded.charCodeAt(i) - 63;
    current |= (b & 31) << shift;
    if (b < 32) {
      numbers.push(current);
      current = 0;
      shift = 0;
    } else {
      shift += 5;
    }
  }
  return numbers;
}
function encodeUnsignedInteger(num) {
  let value, encoded = "";
  while (num >= 32) {
    value = (32 | num & 31) + 63;
    encoded += String.fromCharCode(value);
    num >>= 5;
  }
  value = num + 63;
  encoded += String.fromCharCode(value);
  return encoded;
}
var Polyline_default = Polyline;

// node_modules/ol/format/TopoJSON.js
var TopoJSON = class extends JSONFeature_default {
  /**
   * @param {Options} [options] Options.
   */
  constructor(options) {
    super();
    options = options ? options : {};
    this.layerName_ = options.layerName;
    this.layers_ = options.layers ? options.layers : null;
    this.dataProjection = get(
      options.dataProjection ? options.dataProjection : "EPSG:4326"
    );
  }
  /**
   * @param {Object} object Object.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {Array<Feature>} Features.
   */
  readFeaturesFromObject(object, options) {
    if (object.type == "Topology") {
      const topoJSONTopology = (
        /** @type {TopoJSONTopology} */
        object
      );
      let transform, scale = null, translate = null;
      if (topoJSONTopology["transform"]) {
        transform = topoJSONTopology["transform"];
        scale = transform["scale"];
        translate = transform["translate"];
      }
      const arcs = topoJSONTopology["arcs"];
      if (transform) {
        transformArcs(arcs, scale, translate);
      }
      const features = [];
      const topoJSONFeatures = topoJSONTopology["objects"];
      const property = this.layerName_;
      let feature;
      for (const objectName in topoJSONFeatures) {
        if (this.layers_ && !this.layers_.includes(objectName)) {
          continue;
        }
        if (topoJSONFeatures[objectName].type === "GeometryCollection") {
          feature = /** @type {TopoJSONGeometryCollection} */
          topoJSONFeatures[objectName];
          features.push.apply(
            features,
            readFeaturesFromGeometryCollection(
              feature,
              arcs,
              scale,
              translate,
              property,
              objectName,
              options
            )
          );
        } else {
          feature = /** @type {TopoJSONGeometry} */
          topoJSONFeatures[objectName];
          features.push(
            readFeatureFromGeometry(
              feature,
              arcs,
              scale,
              translate,
              property,
              objectName,
              options
            )
          );
        }
      }
      return features;
    }
    return [];
  }
  /**
   * @param {Object} object Object.
   * @protected
   * @return {import("../proj/Projection.js").default} Projection.
   */
  readProjectionFromObject(object) {
    return this.dataProjection;
  }
};
var GEOMETRY_READERS2 = {
  "Point": readPointGeometry2,
  "LineString": readLineStringGeometry2,
  "Polygon": readPolygonGeometry2,
  "MultiPoint": readMultiPointGeometry2,
  "MultiLineString": readMultiLineStringGeometry2,
  "MultiPolygon": readMultiPolygonGeometry2
};
function concatenateArcs(indices, arcs) {
  const coordinates = [];
  let index;
  for (let i = 0, ii = indices.length; i < ii; ++i) {
    index = indices[i];
    if (i > 0) {
      coordinates.pop();
    }
    if (index >= 0) {
      const arc = arcs[index];
      for (let j = 0, jj = arc.length; j < jj; ++j) {
        coordinates.push(arc[j].slice(0));
      }
    } else {
      const arc = arcs[~index];
      for (let j = arc.length - 1; j >= 0; --j) {
        coordinates.push(arc[j].slice(0));
      }
    }
  }
  return coordinates;
}
function readPointGeometry2(object, scale, translate) {
  const coordinates = object["coordinates"];
  if (scale && translate) {
    transformVertex(coordinates, scale, translate);
  }
  return new Point_default(coordinates);
}
function readMultiPointGeometry2(object, scale, translate) {
  const coordinates = object["coordinates"];
  if (scale && translate) {
    for (let i = 0, ii = coordinates.length; i < ii; ++i) {
      transformVertex(coordinates[i], scale, translate);
    }
  }
  return new MultiPoint_default(coordinates);
}
function readLineStringGeometry2(object, arcs) {
  const coordinates = concatenateArcs(object["arcs"], arcs);
  return new LineString_default(coordinates);
}
function readMultiLineStringGeometry2(object, arcs) {
  const coordinates = [];
  for (let i = 0, ii = object["arcs"].length; i < ii; ++i) {
    coordinates[i] = concatenateArcs(object["arcs"][i], arcs);
  }
  return new MultiLineString_default(coordinates);
}
function readPolygonGeometry2(object, arcs) {
  const coordinates = [];
  for (let i = 0, ii = object["arcs"].length; i < ii; ++i) {
    coordinates[i] = concatenateArcs(object["arcs"][i], arcs);
  }
  return new Polygon_default(coordinates);
}
function readMultiPolygonGeometry2(object, arcs) {
  const coordinates = [];
  for (let i = 0, ii = object["arcs"].length; i < ii; ++i) {
    const polyArray = object["arcs"][i];
    const ringCoords = [];
    for (let j = 0, jj = polyArray.length; j < jj; ++j) {
      ringCoords[j] = concatenateArcs(polyArray[j], arcs);
    }
    coordinates[i] = ringCoords;
  }
  return new MultiPolygon_default(coordinates);
}
function readFeaturesFromGeometryCollection(collection, arcs, scale, translate, property, name, options) {
  const geometries = collection["geometries"];
  const features = [];
  for (let i = 0, ii = geometries.length; i < ii; ++i) {
    features[i] = readFeatureFromGeometry(
      geometries[i],
      arcs,
      scale,
      translate,
      property,
      name,
      options
    );
  }
  return features;
}
function readFeatureFromGeometry(object, arcs, scale, translate, property, name, options) {
  let geometry = null;
  const type = object.type;
  if (type) {
    const geometryReader = GEOMETRY_READERS2[type];
    if (type === "Point" || type === "MultiPoint") {
      geometry = geometryReader(object, scale, translate);
    } else {
      geometry = geometryReader(object, arcs);
    }
    geometry = transformGeometryWithOptions(geometry, false, options);
  }
  const feature = new Feature_default({ geometry });
  if (object.id !== void 0) {
    feature.setId(object.id);
  }
  let properties = object.properties;
  if (property) {
    if (!properties) {
      properties = {};
    }
    properties[property] = name;
  }
  if (properties) {
    feature.setProperties(properties, true);
  }
  return feature;
}
function transformArcs(arcs, scale, translate) {
  for (let i = 0, ii = arcs.length; i < ii; ++i) {
    transformArc(arcs[i], scale, translate);
  }
}
function transformArc(arc, scale, translate) {
  let x = 0;
  let y = 0;
  for (let i = 0, ii = arc.length; i < ii; ++i) {
    const vertex = arc[i];
    x += vertex[0];
    y += vertex[1];
    vertex[0] = x;
    vertex[1] = y;
    transformVertex(vertex, scale, translate);
  }
}
function transformVertex(vertex, scale, translate) {
  vertex[0] = vertex[0] * scale[0] + translate[0];
  vertex[1] = vertex[1] * scale[1] + translate[1];
}
var TopoJSON_default = TopoJSON;

// node_modules/ol/format/GML32.js
var GML32 = class extends GML3_default {
  /**
   * @param {import("./GMLBase.js").Options} [options] Optional configuration object.
   */
  constructor(options) {
    options = options ? options : {};
    super(options);
    this.schemaLocation = options.schemaLocation ? options.schemaLocation : this.namespace + " http://schemas.opengis.net/gml/3.2.1/gml.xsd";
  }
  /**
   * @param {Node} node Node.
   * @param {import("../geom/Geometry.js").default|import("../extent.js").Extent} geometry Geometry.
   * @param {Array<*>} objectStack Node stack.
   */
  writeGeometryElement(node, geometry, objectStack) {
    const context = objectStack[objectStack.length - 1];
    objectStack[objectStack.length - 1] = Object.assign(
      { multiCurve: true, multiSurface: true },
      context
    );
    super.writeGeometryElement(node, geometry, objectStack);
  }
};
GML32.prototype.namespace = "http://www.opengis.net/gml/3.2";
GML32.prototype.GEOMETRY_FLAT_COORDINATES_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "pos": makeReplacer(GML3_default.prototype.readFlatPos),
    "posList": makeReplacer(GML3_default.prototype.readFlatPosList),
    "coordinates": makeReplacer(GML2_default.prototype.readFlatCoordinates)
  }
};
GML32.prototype.FLAT_LINEAR_RINGS_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "interior": GML3_default.prototype.interiorParser,
    "exterior": GML3_default.prototype.exteriorParser
  }
};
GML32.prototype.GEOMETRY_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "Point": makeReplacer(GMLBase_default.prototype.readPoint),
    "MultiPoint": makeReplacer(GMLBase_default.prototype.readMultiPoint),
    "LineString": makeReplacer(GMLBase_default.prototype.readLineString),
    "MultiLineString": makeReplacer(GMLBase_default.prototype.readMultiLineString),
    "LinearRing": makeReplacer(GMLBase_default.prototype.readLinearRing),
    "Polygon": makeReplacer(GMLBase_default.prototype.readPolygon),
    "MultiPolygon": makeReplacer(GMLBase_default.prototype.readMultiPolygon),
    "Surface": makeReplacer(GML32.prototype.readSurface),
    "MultiSurface": makeReplacer(GML3_default.prototype.readMultiSurface),
    "Curve": makeReplacer(GML32.prototype.readCurve),
    "MultiCurve": makeReplacer(GML3_default.prototype.readMultiCurve),
    "Envelope": makeReplacer(GML32.prototype.readEnvelope)
  }
};
GML32.prototype.MULTICURVE_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "curveMember": makeArrayPusher(GML3_default.prototype.curveMemberParser),
    "curveMembers": makeArrayPusher(GML3_default.prototype.curveMemberParser)
  }
};
GML32.prototype.MULTISURFACE_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "surfaceMember": makeArrayPusher(GML3_default.prototype.surfaceMemberParser),
    "surfaceMembers": makeArrayPusher(GML3_default.prototype.surfaceMemberParser)
  }
};
GML32.prototype.CURVEMEMBER_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "LineString": makeArrayPusher(GMLBase_default.prototype.readLineString),
    "Curve": makeArrayPusher(GML3_default.prototype.readCurve)
  }
};
GML32.prototype.SURFACEMEMBER_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "Polygon": makeArrayPusher(GMLBase_default.prototype.readPolygon),
    "Surface": makeArrayPusher(GML3_default.prototype.readSurface)
  }
};
GML32.prototype.SURFACE_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "patches": makeReplacer(GML3_default.prototype.readPatch)
  }
};
GML32.prototype.CURVE_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "segments": makeReplacer(GML3_default.prototype.readSegment)
  }
};
GML32.prototype.ENVELOPE_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "lowerCorner": makeArrayPusher(GML3_default.prototype.readFlatPosList),
    "upperCorner": makeArrayPusher(GML3_default.prototype.readFlatPosList)
  }
};
GML32.prototype.PATCHES_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "PolygonPatch": makeReplacer(GML3_default.prototype.readPolygonPatch)
  }
};
GML32.prototype.SEGMENTS_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "LineStringSegment": makeArrayExtender(
      GML3_default.prototype.readLineStringSegment
    )
  }
};
GML32.prototype.MULTIPOINT_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "pointMember": makeArrayPusher(GMLBase_default.prototype.pointMemberParser),
    "pointMembers": makeArrayPusher(GMLBase_default.prototype.pointMemberParser)
  }
};
GML32.prototype.MULTILINESTRING_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "lineStringMember": makeArrayPusher(
      GMLBase_default.prototype.lineStringMemberParser
    ),
    "lineStringMembers": makeArrayPusher(
      GMLBase_default.prototype.lineStringMemberParser
    )
  }
};
GML32.prototype.MULTIPOLYGON_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "polygonMember": makeArrayPusher(GMLBase_default.prototype.polygonMemberParser),
    "polygonMembers": makeArrayPusher(GMLBase_default.prototype.polygonMemberParser)
  }
};
GML32.prototype.POINTMEMBER_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "Point": makeArrayPusher(GMLBase_default.prototype.readFlatCoordinatesFromNode)
  }
};
GML32.prototype.LINESTRINGMEMBER_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "LineString": makeArrayPusher(GMLBase_default.prototype.readLineString)
  }
};
GML32.prototype.POLYGONMEMBER_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "Polygon": makeArrayPusher(GMLBase_default.prototype.readPolygon)
  }
};
GML32.prototype.RING_PARSERS = {
  "http://www.opengis.net/gml/3.2": {
    "LinearRing": makeReplacer(GMLBase_default.prototype.readFlatLinearRing),
    "Ring": makeReplacer(GML32.prototype.readFlatCurveRing)
  }
};
GML32.prototype.RING_SERIALIZERS = {
  "http://www.opengis.net/gml/3.2": {
    "exterior": makeChildAppender(GML3_default.prototype.writeRing),
    "interior": makeChildAppender(GML3_default.prototype.writeRing)
  }
};
GML32.prototype.ENVELOPE_SERIALIZERS = {
  "http://www.opengis.net/gml/3.2": {
    "lowerCorner": makeChildAppender(writeStringTextNode),
    "upperCorner": makeChildAppender(writeStringTextNode)
  }
};
GML32.prototype.SURFACEORPOLYGONMEMBER_SERIALIZERS = {
  "http://www.opengis.net/gml/3.2": {
    "surfaceMember": makeChildAppender(
      GML3_default.prototype.writeSurfaceOrPolygonMember
    ),
    "polygonMember": makeChildAppender(
      GML3_default.prototype.writeSurfaceOrPolygonMember
    )
  }
};
GML32.prototype.POINTMEMBER_SERIALIZERS = {
  "http://www.opengis.net/gml/3.2": {
    "pointMember": makeChildAppender(GML3_default.prototype.writePointMember)
  }
};
GML32.prototype.LINESTRINGORCURVEMEMBER_SERIALIZERS = {
  "http://www.opengis.net/gml/3.2": {
    "lineStringMember": makeChildAppender(
      GML3_default.prototype.writeLineStringOrCurveMember
    ),
    "curveMember": makeChildAppender(
      GML3_default.prototype.writeLineStringOrCurveMember
    )
  }
};
GML32.prototype.GEOMETRY_SERIALIZERS = {
  "http://www.opengis.net/gml/3.2": {
    "Curve": makeChildAppender(GML3_default.prototype.writeCurveOrLineString),
    "MultiCurve": makeChildAppender(GML3_default.prototype.writeMultiCurveOrLineString),
    "Point": makeChildAppender(GML32.prototype.writePoint),
    "MultiPoint": makeChildAppender(GML3_default.prototype.writeMultiPoint),
    "LineString": makeChildAppender(GML3_default.prototype.writeCurveOrLineString),
    "MultiLineString": makeChildAppender(
      GML3_default.prototype.writeMultiCurveOrLineString
    ),
    "LinearRing": makeChildAppender(GML3_default.prototype.writeLinearRing),
    "Polygon": makeChildAppender(GML3_default.prototype.writeSurfaceOrPolygon),
    "MultiPolygon": makeChildAppender(
      GML3_default.prototype.writeMultiSurfaceOrPolygon
    ),
    "Surface": makeChildAppender(GML3_default.prototype.writeSurfaceOrPolygon),
    "MultiSurface": makeChildAppender(
      GML3_default.prototype.writeMultiSurfaceOrPolygon
    ),
    "Envelope": makeChildAppender(GML3_default.prototype.writeEnvelope)
  }
};
var GML32_default = GML32;

// node_modules/ol/format/filter/Filter.js
var Filter = class {
  /**
   * @param {!string} tagName The XML tag name for this filter.
   */
  constructor(tagName) {
    this.tagName_ = tagName;
  }
  /**
   * The XML tag name for a filter.
   * @return {!string} Name.
   */
  getTagName() {
    return this.tagName_;
  }
};
var Filter_default = Filter;

// node_modules/ol/format/filter/LogicalNary.js
var LogicalNary = class extends Filter_default {
  /**
   * @param {!string} tagName The XML tag name for this filter.
   * @param {Array<import("./Filter.js").default>} conditions Conditions.
   */
  constructor(tagName, conditions) {
    super(tagName);
    this.conditions = conditions;
    assert(this.conditions.length >= 2, 57);
  }
};
var LogicalNary_default = LogicalNary;

// node_modules/ol/format/filter/And.js
var And = class extends LogicalNary_default {
  /**
   * @param {...import("./Filter.js").default} conditions Conditions.
   */
  constructor(conditions) {
    super("And", Array.prototype.slice.call(arguments));
  }
};
var And_default = And;

// node_modules/ol/format/filter/Bbox.js
var Bbox = class extends Filter_default {
  /**
   * @param {!string} geometryName Geometry name to use.
   * @param {!import("../../extent.js").Extent} extent Extent.
   * @param {string} [srsName] SRS name. No srsName attribute will be set
   * on geometries when this is not provided.
   */
  constructor(geometryName, extent, srsName) {
    super("BBOX");
    this.geometryName = geometryName;
    this.extent = extent;
    if (extent.length !== 4) {
      throw new Error(
        "Expected an extent with four values ([minX, minY, maxX, maxY])"
      );
    }
    this.srsName = srsName;
  }
};
var Bbox_default = Bbox;

// node_modules/ol/format/filter.js
function and(conditions) {
  const params = [null].concat(Array.prototype.slice.call(arguments));
  return new (Function.prototype.bind.apply(And_default, params))();
}
function bbox(geometryName, extent, srsName) {
  return new Bbox_default(geometryName, extent, srsName);
}

// node_modules/ol/format/WFS.js
var FEATURE_COLLECTION_PARSERS = {
  "http://www.opengis.net/gml": {
    "boundedBy": makeObjectPropertySetter(
      GMLBase_default.prototype.readExtentElement,
      "bounds"
    )
  },
  "http://www.opengis.net/wfs/2.0": {
    "member": makeArrayPusher(GMLBase_default.prototype.readFeaturesInternal)
  }
};
var TRANSACTION_SUMMARY_PARSERS = {
  "http://www.opengis.net/wfs": {
    "totalInserted": makeObjectPropertySetter(readPositiveInteger),
    "totalUpdated": makeObjectPropertySetter(readPositiveInteger),
    "totalDeleted": makeObjectPropertySetter(readPositiveInteger)
  },
  "http://www.opengis.net/wfs/2.0": {
    "totalInserted": makeObjectPropertySetter(readPositiveInteger),
    "totalUpdated": makeObjectPropertySetter(readPositiveInteger),
    "totalDeleted": makeObjectPropertySetter(readPositiveInteger)
  }
};
var TRANSACTION_RESPONSE_PARSERS = {
  "http://www.opengis.net/wfs": {
    "TransactionSummary": makeObjectPropertySetter(
      readTransactionSummary,
      "transactionSummary"
    ),
    "InsertResults": makeObjectPropertySetter(readInsertResults, "insertIds")
  },
  "http://www.opengis.net/wfs/2.0": {
    "TransactionSummary": makeObjectPropertySetter(
      readTransactionSummary,
      "transactionSummary"
    ),
    "InsertResults": makeObjectPropertySetter(readInsertResults, "insertIds")
  }
};
var QUERY_SERIALIZERS = {
  "http://www.opengis.net/wfs": {
    "PropertyName": makeChildAppender(writeStringTextNode)
  },
  "http://www.opengis.net/wfs/2.0": {
    "PropertyName": makeChildAppender(writeStringTextNode)
  }
};
var TRANSACTION_SERIALIZERS = {
  "http://www.opengis.net/wfs": {
    "Insert": makeChildAppender(writeFeature),
    "Update": makeChildAppender(writeUpdate),
    "Delete": makeChildAppender(writeDelete),
    "Property": makeChildAppender(writeProperty),
    "Native": makeChildAppender(writeNative)
  },
  "http://www.opengis.net/wfs/2.0": {
    "Insert": makeChildAppender(writeFeature),
    "Update": makeChildAppender(writeUpdate),
    "Delete": makeChildAppender(writeDelete),
    "Property": makeChildAppender(writeProperty),
    "Native": makeChildAppender(writeNative)
  }
};
var FEATURE_PREFIX = "feature";
var XMLNS = "http://www.w3.org/2000/xmlns/";
var OGCNS = {
  "2.0.0": "http://www.opengis.net/ogc/1.1",
  "1.1.0": "http://www.opengis.net/ogc",
  "1.0.0": "http://www.opengis.net/ogc"
};
var WFSNS = {
  "2.0.0": "http://www.opengis.net/wfs/2.0",
  "1.1.0": "http://www.opengis.net/wfs",
  "1.0.0": "http://www.opengis.net/wfs"
};
var FESNS = {
  "2.0.0": "http://www.opengis.net/fes/2.0",
  "1.1.0": "http://www.opengis.net/fes",
  "1.0.0": "http://www.opengis.net/fes"
};
var SCHEMA_LOCATIONS = {
  "2.0.0": "http://www.opengis.net/wfs/2.0 http://schemas.opengis.net/wfs/2.0/wfs.xsd",
  "1.1.0": "http://www.opengis.net/wfs http://schemas.opengis.net/wfs/1.1.0/wfs.xsd",
  "1.0.0": "http://www.opengis.net/wfs http://schemas.opengis.net/wfs/1.0.0/wfs.xsd"
};
var GML_FORMATS = {
  "2.0.0": GML32_default,
  "1.1.0": GML3_default,
  "1.0.0": GML2_default
};
var DEFAULT_VERSION = "1.1.0";
var WFS = class extends XMLFeature_default {
  /**
   * @param {Options} [options] Optional configuration object.
   */
  constructor(options) {
    super();
    options = options ? options : {};
    this.version_ = options.version ? options.version : DEFAULT_VERSION;
    this.featureType_ = options.featureType;
    this.featureNS_ = options.featureNS;
    this.gmlFormat_ = options.gmlFormat ? options.gmlFormat : new GML_FORMATS[this.version_]();
    this.schemaLocation_ = options.schemaLocation ? options.schemaLocation : SCHEMA_LOCATIONS[this.version_];
  }
  /**
   * @return {Array<string>|string|undefined} featureType
   */
  getFeatureType() {
    return this.featureType_;
  }
  /**
   * @param {Array<string>|string|undefined} featureType Feature type(s) to parse.
   */
  setFeatureType(featureType) {
    this.featureType_ = featureType;
  }
  /**
   * @protected
   * @param {Element} node Node.
   * @param {import("./Feature.js").ReadOptions} [options] Options.
   * @return {Array<import("../Feature.js").default>} Features.
   */
  readFeaturesFromNode(node, options) {
    const context = {
      node
    };
    Object.assign(context, {
      "featureType": this.featureType_,
      "featureNS": this.featureNS_
    });
    Object.assign(context, this.getReadOptions(node, options ? options : {}));
    const objectStack = [context];
    let featuresNS;
    if (this.version_ === "2.0.0") {
      featuresNS = FEATURE_COLLECTION_PARSERS;
    } else {
      featuresNS = this.gmlFormat_.FEATURE_COLLECTION_PARSERS;
    }
    let features = pushParseAndPop(
      [],
      featuresNS,
      node,
      objectStack,
      this.gmlFormat_
    );
    if (!features) {
      features = [];
    }
    return features;
  }
  /**
   * Read transaction response of the source.
   *
   * @param {Document|Element|Object|string} source Source.
   * @return {TransactionResponse|undefined} Transaction response.
   * @api
   */
  readTransactionResponse(source) {
    if (!source) {
      return void 0;
    }
    if (typeof source === "string") {
      const doc = parse(source);
      return this.readTransactionResponseFromDocument(doc);
    }
    if (isDocument(source)) {
      return this.readTransactionResponseFromDocument(
        /** @type {Document} */
        source
      );
    }
    return this.readTransactionResponseFromNode(
      /** @type {Element} */
      source
    );
  }
  /**
   * Read feature collection metadata of the source.
   *
   * @param {Document|Element|Object|string} source Source.
   * @return {FeatureCollectionMetadata|undefined}
   *     FeatureCollection metadata.
   * @api
   */
  readFeatureCollectionMetadata(source) {
    if (!source) {
      return void 0;
    }
    if (typeof source === "string") {
      const doc = parse(source);
      return this.readFeatureCollectionMetadataFromDocument(doc);
    }
    if (isDocument(source)) {
      return this.readFeatureCollectionMetadataFromDocument(
        /** @type {Document} */
        source
      );
    }
    return this.readFeatureCollectionMetadataFromNode(
      /** @type {Element} */
      source
    );
  }
  /**
   * @param {Document} doc Document.
   * @return {FeatureCollectionMetadata|undefined}
   *     FeatureCollection metadata.
   */
  readFeatureCollectionMetadataFromDocument(doc) {
    for (let n = (
      /** @type {Node} */
      doc.firstChild
    ); n; n = n.nextSibling) {
      if (n.nodeType == Node.ELEMENT_NODE) {
        return this.readFeatureCollectionMetadataFromNode(
          /** @type {Element} */
          n
        );
      }
    }
    return void 0;
  }
  /**
   * @param {Element} node Node.
   * @return {FeatureCollectionMetadata|undefined}
   *     FeatureCollection metadata.
   */
  readFeatureCollectionMetadataFromNode(node) {
    const result = {};
    const value = readNonNegativeIntegerString(
      node.getAttribute("numberOfFeatures")
    );
    result["numberOfFeatures"] = value;
    return pushParseAndPop(
      /** @type {FeatureCollectionMetadata} */
      result,
      FEATURE_COLLECTION_PARSERS,
      node,
      [],
      this.gmlFormat_
    );
  }
  /**
   * @param {Document} doc Document.
   * @return {TransactionResponse|undefined} Transaction response.
   */
  readTransactionResponseFromDocument(doc) {
    for (let n = (
      /** @type {Node} */
      doc.firstChild
    ); n; n = n.nextSibling) {
      if (n.nodeType == Node.ELEMENT_NODE) {
        return this.readTransactionResponseFromNode(
          /** @type {Element} */
          n
        );
      }
    }
    return void 0;
  }
  /**
   * @param {Element} node Node.
   * @return {TransactionResponse|undefined} Transaction response.
   */
  readTransactionResponseFromNode(node) {
    return pushParseAndPop(
      /** @type {TransactionResponse} */
      {},
      TRANSACTION_RESPONSE_PARSERS,
      node,
      []
    );
  }
  /**
   * Encode format as WFS `GetFeature` and return the Node.
   *
   * @param {WriteGetFeatureOptions} options Options.
   * @return {Node} Result.
   * @api
   */
  writeGetFeature(options) {
    const node = createElementNS(WFSNS[this.version_], "GetFeature");
    node.setAttribute("service", "WFS");
    node.setAttribute("version", this.version_);
    if (options.handle) {
      node.setAttribute("handle", options.handle);
    }
    if (options.outputFormat) {
      node.setAttribute("outputFormat", options.outputFormat);
    }
    if (options.maxFeatures !== void 0) {
      node.setAttribute("maxFeatures", String(options.maxFeatures));
    }
    if (options.resultType) {
      node.setAttribute("resultType", options.resultType);
    }
    if (options.startIndex !== void 0) {
      node.setAttribute("startIndex", String(options.startIndex));
    }
    if (options.count !== void 0) {
      node.setAttribute("count", String(options.count));
    }
    if (options.viewParams !== void 0) {
      node.setAttribute("viewParams", options.viewParams);
    }
    node.setAttributeNS(
      XML_SCHEMA_INSTANCE_URI,
      "xsi:schemaLocation",
      this.schemaLocation_
    );
    const context = {
      node
    };
    Object.assign(context, {
      "version": this.version_,
      "srsName": options.srsName,
      "featureNS": options.featureNS ? options.featureNS : this.featureNS_,
      "featurePrefix": options.featurePrefix,
      "propertyNames": options.propertyNames ? options.propertyNames : []
    });
    assert(Array.isArray(options.featureTypes), 11);
    if (typeof options.featureTypes[0] === "string") {
      let filter = options.filter;
      if (options.bbox) {
        assert(options.geometryName, 12);
        filter = this.combineBboxAndFilter(
          options.geometryName,
          options.bbox,
          options.srsName,
          filter
        );
      }
      Object.assign(context, {
        "geometryName": options.geometryName,
        "filter": filter
      });
      writeGetFeature(
        node,
        /** @type {!Array<string>} */
        options.featureTypes,
        [context]
      );
    } else {
      options.featureTypes.forEach((featureType) => {
        const completeFilter = this.combineBboxAndFilter(
          featureType.geometryName,
          featureType.bbox,
          options.srsName,
          options.filter
        );
        Object.assign(context, {
          "geometryName": featureType.geometryName,
          "filter": completeFilter
        });
        writeGetFeature(node, [featureType.name], [context]);
      });
    }
    return node;
  }
  /**
   * Create a bbox filter and combine it with another optional filter.
   *
   * @param {!string} geometryName Geometry name to use.
   * @param {!import("../extent.js").Extent} extent Extent.
   * @param {string} [srsName] SRS name. No srsName attribute will be
   *    set on geometries when this is not provided.
   * @param {import("./filter/Filter.js").default} [filter] Filter condition.
   * @return {import("./filter/Filter.js").default} The filter.
   */
  combineBboxAndFilter(geometryName, extent, srsName, filter) {
    const bboxFilter = bbox(geometryName, extent, srsName);
    if (filter) {
      return and(filter, bboxFilter);
    }
    return bboxFilter;
  }
  /**
   * Encode format as WFS `Transaction` and return the Node.
   *
   * @param {Array<import("../Feature.js").default>} inserts The features to insert.
   * @param {Array<import("../Feature.js").default>} updates The features to update.
   * @param {Array<import("../Feature.js").default>} deletes The features to delete.
   * @param {WriteTransactionOptions} options Write options.
   * @return {Node} Result.
   * @api
   */
  writeTransaction(inserts, updates, deletes, options) {
    const objectStack = [];
    const version = options.version ? options.version : this.version_;
    const node = createElementNS(WFSNS[version], "Transaction");
    node.setAttribute("service", "WFS");
    node.setAttribute("version", version);
    let baseObj;
    if (options) {
      baseObj = options.gmlOptions ? options.gmlOptions : {};
      if (options.handle) {
        node.setAttribute("handle", options.handle);
      }
    }
    node.setAttributeNS(
      XML_SCHEMA_INSTANCE_URI,
      "xsi:schemaLocation",
      SCHEMA_LOCATIONS[version]
    );
    const request = createTransactionRequest(node, baseObj, version, options);
    if (inserts) {
      serializeTransactionRequest("Insert", inserts, objectStack, request);
    }
    if (updates) {
      serializeTransactionRequest("Update", updates, objectStack, request);
    }
    if (deletes) {
      serializeTransactionRequest("Delete", deletes, objectStack, request);
    }
    if (options.nativeElements) {
      serializeTransactionRequest(
        "Native",
        options.nativeElements,
        objectStack,
        request
      );
    }
    return node;
  }
  /**
   * @param {Document} doc Document.
   * @return {import("../proj/Projection.js").default} Projection.
   */
  readProjectionFromDocument(doc) {
    for (let n = doc.firstChild; n; n = n.nextSibling) {
      if (n.nodeType == Node.ELEMENT_NODE) {
        return this.readProjectionFromNode(
          /** @type {Element} */
          n
        );
      }
    }
    return null;
  }
  /**
   * @param {Element} node Node.
   * @return {import("../proj/Projection.js").default} Projection.
   */
  readProjectionFromNode(node) {
    if (node.firstElementChild && node.firstElementChild.firstElementChild) {
      node = node.firstElementChild.firstElementChild;
      for (let n = node.firstElementChild; n; n = n.nextElementSibling) {
        if (!(n.childNodes.length === 0 || n.childNodes.length === 1 && n.firstChild.nodeType === 3)) {
          const objectStack = [{}];
          this.gmlFormat_.readGeometryElement(n, objectStack);
          return get(objectStack.pop().srsName);
        }
      }
    }
    return null;
  }
};
function createTransactionRequest(node, baseObj, version, options) {
  const featurePrefix = options.featurePrefix ? options.featurePrefix : FEATURE_PREFIX;
  let gmlVersion;
  if (version === "1.0.0") {
    gmlVersion = 2;
  } else if (version === "1.1.0") {
    gmlVersion = 3;
  } else if (version === "2.0.0") {
    gmlVersion = 3.2;
  }
  const obj = Object.assign(
    { node },
    {
      version,
      "featureNS": options.featureNS,
      "featureType": options.featureType,
      "featurePrefix": featurePrefix,
      "gmlVersion": gmlVersion,
      "hasZ": options.hasZ,
      "srsName": options.srsName
    },
    baseObj
  );
  return obj;
}
function serializeTransactionRequest(type, features, objectStack, request) {
  pushSerializeAndPop(
    request,
    TRANSACTION_SERIALIZERS,
    makeSimpleNodeFactory(type),
    features,
    objectStack
  );
}
function readTransactionSummary(node, objectStack) {
  return pushParseAndPop({}, TRANSACTION_SUMMARY_PARSERS, node, objectStack);
}
var OGC_FID_PARSERS = {
  "http://www.opengis.net/ogc": {
    "FeatureId": makeArrayPusher(function(node, objectStack) {
      return node.getAttribute("fid");
    })
  },
  "http://www.opengis.net/ogc/1.1": {
    "FeatureId": makeArrayPusher(function(node, objectStack) {
      return node.getAttribute("fid");
    })
  }
};
function fidParser(node, objectStack) {
  parseNode(OGC_FID_PARSERS, node, objectStack);
}
var INSERT_RESULTS_PARSERS = {
  "http://www.opengis.net/wfs": {
    "Feature": fidParser
  },
  "http://www.opengis.net/wfs/2.0": {
    "Feature": fidParser
  }
};
function readInsertResults(node, objectStack) {
  return pushParseAndPop([], INSERT_RESULTS_PARSERS, node, objectStack);
}
function writeFeature(node, feature, objectStack) {
  const context = objectStack[objectStack.length - 1];
  const featureType = context["featureType"];
  const featureNS = context["featureNS"];
  const gmlVersion = context["gmlVersion"];
  const child = createElementNS(featureNS, featureType);
  node.appendChild(child);
  if (gmlVersion === 2) {
    GML2_default.prototype.writeFeatureElement(child, feature, objectStack);
  } else if (gmlVersion === 3) {
    GML3_default.prototype.writeFeatureElement(child, feature, objectStack);
  } else {
    GML32_default.prototype.writeFeatureElement(child, feature, objectStack);
  }
}
function writeOgcFidFilter(node, fid, objectStack) {
  const context = objectStack[objectStack.length - 1];
  const version = context["version"];
  const ns = OGCNS[version];
  const filter = createElementNS(ns, "Filter");
  const child = createElementNS(ns, "FeatureId");
  filter.appendChild(child);
  child.setAttribute(
    "fid",
    /** @type {string} */
    fid
  );
  node.appendChild(filter);
}
function getTypeName(featurePrefix, featureType) {
  featurePrefix = featurePrefix ? featurePrefix : FEATURE_PREFIX;
  const prefix = featurePrefix + ":";
  if (featureType.startsWith(prefix)) {
    return featureType;
  }
  return prefix + featureType;
}
function writeDelete(node, feature, objectStack) {
  const context = objectStack[objectStack.length - 1];
  assert(feature.getId() !== void 0, 26);
  const featureType = context["featureType"];
  const featurePrefix = context["featurePrefix"];
  const featureNS = context["featureNS"];
  const typeName = getTypeName(featurePrefix, featureType);
  node.setAttribute("typeName", typeName);
  node.setAttributeNS(XMLNS, "xmlns:" + featurePrefix, featureNS);
  const fid = feature.getId();
  if (fid !== void 0) {
    writeOgcFidFilter(node, fid, objectStack);
  }
}
function writeUpdate(node, feature, objectStack) {
  const context = objectStack[objectStack.length - 1];
  assert(feature.getId() !== void 0, 27);
  const version = context["version"];
  const featureType = context["featureType"];
  const featurePrefix = context["featurePrefix"];
  const featureNS = context["featureNS"];
  const typeName = getTypeName(featurePrefix, featureType);
  const geometryName = feature.getGeometryName();
  node.setAttribute("typeName", typeName);
  node.setAttributeNS(XMLNS, "xmlns:" + featurePrefix, featureNS);
  const fid = feature.getId();
  if (fid !== void 0) {
    const keys = feature.getKeys();
    const values = [];
    for (let i = 0, ii = keys.length; i < ii; i++) {
      const value = feature.get(keys[i]);
      if (value !== void 0) {
        let name = keys[i];
        if (value && typeof /** @type {?} */
        value.getSimplifiedGeometry === "function") {
          name = geometryName;
        }
        values.push({ name, value });
      }
    }
    pushSerializeAndPop(
      /** @type {import("../xml.js").NodeStackItem} */
      {
        version,
        "gmlVersion": context["gmlVersion"],
        node,
        "hasZ": context["hasZ"],
        "srsName": context["srsName"]
      },
      TRANSACTION_SERIALIZERS,
      makeSimpleNodeFactory("Property"),
      values,
      objectStack
    );
    writeOgcFidFilter(node, fid, objectStack);
  }
}
function writeProperty(node, pair, objectStack) {
  const context = objectStack[objectStack.length - 1];
  const version = context["version"];
  const ns = WFSNS[version];
  const name = createElementNS(ns, "Name");
  const gmlVersion = context["gmlVersion"];
  node.appendChild(name);
  writeStringTextNode(name, pair.name);
  if (pair.value !== void 0 && pair.value !== null) {
    const value = createElementNS(ns, "Value");
    node.appendChild(value);
    if (pair.value && typeof /** @type {?} */
    pair.value.getSimplifiedGeometry === "function") {
      if (gmlVersion === 2) {
        GML2_default.prototype.writeGeometryElement(value, pair.value, objectStack);
      } else if (gmlVersion === 3) {
        GML3_default.prototype.writeGeometryElement(value, pair.value, objectStack);
      } else {
        GML32_default.prototype.writeGeometryElement(value, pair.value, objectStack);
      }
    } else {
      writeStringTextNode(value, pair.value);
    }
  }
}
function writeNative(node, nativeElement, objectStack) {
  if (nativeElement.vendorId) {
    node.setAttribute("vendorId", nativeElement.vendorId);
  }
  if (nativeElement.safeToIgnore !== void 0) {
    node.setAttribute("safeToIgnore", String(nativeElement.safeToIgnore));
  }
  if (nativeElement.value !== void 0) {
    writeStringTextNode(node, nativeElement.value);
  }
}
var GETFEATURE_SERIALIZERS = {
  "http://www.opengis.net/wfs": {
    "Query": makeChildAppender(writeQuery)
  },
  "http://www.opengis.net/wfs/2.0": {
    "Query": makeChildAppender(writeQuery)
  },
  "http://www.opengis.net/ogc": {
    "During": makeChildAppender(writeDuringFilter),
    "And": makeChildAppender(writeLogicalFilter),
    "Or": makeChildAppender(writeLogicalFilter),
    "Not": makeChildAppender(writeNotFilter),
    "BBOX": makeChildAppender(writeBboxFilter),
    "Contains": makeChildAppender(writeSpatialFilter),
    "Intersects": makeChildAppender(writeSpatialFilter),
    "Within": makeChildAppender(writeSpatialFilter),
    "DWithin": makeChildAppender(writeDWithinFilter),
    "PropertyIsEqualTo": makeChildAppender(writeComparisonFilter),
    "PropertyIsNotEqualTo": makeChildAppender(writeComparisonFilter),
    "PropertyIsLessThan": makeChildAppender(writeComparisonFilter),
    "PropertyIsLessThanOrEqualTo": makeChildAppender(writeComparisonFilter),
    "PropertyIsGreaterThan": makeChildAppender(writeComparisonFilter),
    "PropertyIsGreaterThanOrEqualTo": makeChildAppender(writeComparisonFilter),
    "PropertyIsNull": makeChildAppender(writeIsNullFilter),
    "PropertyIsBetween": makeChildAppender(writeIsBetweenFilter),
    "PropertyIsLike": makeChildAppender(writeIsLikeFilter)
  },
  "http://www.opengis.net/fes/2.0": {
    "During": makeChildAppender(writeDuringFilter),
    "And": makeChildAppender(writeLogicalFilter),
    "Or": makeChildAppender(writeLogicalFilter),
    "Not": makeChildAppender(writeNotFilter),
    "BBOX": makeChildAppender(writeBboxFilter),
    "Contains": makeChildAppender(writeSpatialFilter),
    "Disjoint": makeChildAppender(writeSpatialFilter),
    "Intersects": makeChildAppender(writeSpatialFilter),
    "ResourceId": makeChildAppender(writeResourceIdFilter),
    "Within": makeChildAppender(writeSpatialFilter),
    "DWithin": makeChildAppender(writeDWithinFilter),
    "PropertyIsEqualTo": makeChildAppender(writeComparisonFilter),
    "PropertyIsNotEqualTo": makeChildAppender(writeComparisonFilter),
    "PropertyIsLessThan": makeChildAppender(writeComparisonFilter),
    "PropertyIsLessThanOrEqualTo": makeChildAppender(writeComparisonFilter),
    "PropertyIsGreaterThan": makeChildAppender(writeComparisonFilter),
    "PropertyIsGreaterThanOrEqualTo": makeChildAppender(writeComparisonFilter),
    "PropertyIsNull": makeChildAppender(writeIsNullFilter),
    "PropertyIsBetween": makeChildAppender(writeIsBetweenFilter),
    "PropertyIsLike": makeChildAppender(writeIsLikeFilter)
  }
};
function writeQuery(node, featureType, objectStack) {
  const context = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const version = context["version"];
  const featurePrefix = context["featurePrefix"];
  const featureNS = context["featureNS"];
  const propertyNames = context["propertyNames"];
  const srsName = context["srsName"];
  let typeName;
  if (featurePrefix) {
    typeName = getTypeName(featurePrefix, featureType);
  } else {
    typeName = featureType;
  }
  let typeNameAttr;
  if (version === "2.0.0") {
    typeNameAttr = "typeNames";
  } else {
    typeNameAttr = "typeName";
  }
  node.setAttribute(typeNameAttr, typeName);
  if (srsName) {
    node.setAttribute("srsName", srsName);
  }
  if (featureNS) {
    node.setAttributeNS(XMLNS, "xmlns:" + featurePrefix, featureNS);
  }
  const item = (
    /** @type {import("../xml.js").NodeStackItem} */
    Object.assign({}, context)
  );
  item.node = node;
  pushSerializeAndPop(
    item,
    QUERY_SERIALIZERS,
    makeSimpleNodeFactory("PropertyName"),
    propertyNames,
    objectStack
  );
  const filter = context["filter"];
  if (filter) {
    const child = createElementNS(getFilterNS(version), "Filter");
    node.appendChild(child);
    writeFilterCondition(child, filter, objectStack);
  }
}
function writeFilterCondition(node, filter, objectStack) {
  const context = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const item = { node };
  Object.assign(item, { context });
  pushSerializeAndPop(
    item,
    GETFEATURE_SERIALIZERS,
    makeSimpleNodeFactory(filter.getTagName()),
    [filter],
    objectStack
  );
}
function writeBboxFilter(node, filter, objectStack) {
  const parent = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const context = parent["context"];
  const version = context["version"];
  parent["srsName"] = filter.srsName;
  const format = GML_FORMATS[version];
  writePropertyName(version, node, filter.geometryName);
  format.prototype.writeGeometryElement(node, filter.extent, objectStack);
}
function writeResourceIdFilter(node, filter, objectStack) {
  node.setAttribute(
    "rid",
    /** @type {string} */
    filter.rid
  );
}
function writeSpatialFilter(node, filter, objectStack) {
  const parent = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const context = parent["context"];
  const version = context["version"];
  parent["srsName"] = filter.srsName;
  const format = GML_FORMATS[version];
  writePropertyName(version, node, filter.geometryName);
  format.prototype.writeGeometryElement(node, filter.geometry, objectStack);
}
function writeDWithinFilter(node, filter, objectStack) {
  const parent = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const context = parent["context"];
  const version = context["version"];
  writeSpatialFilter(node, filter, objectStack);
  const distance = createElementNS(getFilterNS(version), "Distance");
  writeStringTextNode(distance, filter.distance.toString());
  if (version === "2.0.0") {
    distance.setAttribute("uom", filter.unit);
  } else {
    distance.setAttribute("units", filter.unit);
  }
  node.appendChild(distance);
}
function writeDuringFilter(node, filter, objectStack) {
  const parent = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const context = parent["context"];
  const version = context["version"];
  writeExpression(FESNS[version], "ValueReference", node, filter.propertyName);
  const timePeriod = createElementNS(GMLNS, "TimePeriod");
  node.appendChild(timePeriod);
  const begin = createElementNS(GMLNS, "begin");
  timePeriod.appendChild(begin);
  writeTimeInstant(begin, filter.begin);
  const end = createElementNS(GMLNS, "end");
  timePeriod.appendChild(end);
  writeTimeInstant(end, filter.end);
}
function writeLogicalFilter(node, filter, objectStack) {
  const parent = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const context = parent["context"];
  const item = { node };
  Object.assign(item, { context });
  const conditions = filter.conditions;
  for (let i = 0, ii = conditions.length; i < ii; ++i) {
    const condition = conditions[i];
    pushSerializeAndPop(
      item,
      GETFEATURE_SERIALIZERS,
      makeSimpleNodeFactory(condition.getTagName()),
      [condition],
      objectStack
    );
  }
}
function writeNotFilter(node, filter, objectStack) {
  const parent = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const context = parent["context"];
  const item = { node };
  Object.assign(item, { context });
  const condition = filter.condition;
  pushSerializeAndPop(
    item,
    GETFEATURE_SERIALIZERS,
    makeSimpleNodeFactory(condition.getTagName()),
    [condition],
    objectStack
  );
}
function writeComparisonFilter(node, filter, objectStack) {
  const parent = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const context = parent["context"];
  const version = context["version"];
  if (filter.matchCase !== void 0) {
    node.setAttribute("matchCase", filter.matchCase.toString());
  }
  writePropertyName(version, node, filter.propertyName);
  writeLiteral(version, node, "" + filter.expression);
}
function writeIsNullFilter(node, filter, objectStack) {
  const parent = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const context = parent["context"];
  const version = context["version"];
  writePropertyName(version, node, filter.propertyName);
}
function writeIsBetweenFilter(node, filter, objectStack) {
  const parent = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const context = parent["context"];
  const version = context["version"];
  const ns = getFilterNS(version);
  writePropertyName(version, node, filter.propertyName);
  const lowerBoundary = createElementNS(ns, "LowerBoundary");
  node.appendChild(lowerBoundary);
  writeLiteral(version, lowerBoundary, "" + filter.lowerBoundary);
  const upperBoundary = createElementNS(ns, "UpperBoundary");
  node.appendChild(upperBoundary);
  writeLiteral(version, upperBoundary, "" + filter.upperBoundary);
}
function writeIsLikeFilter(node, filter, objectStack) {
  const parent = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const context = parent["context"];
  const version = context["version"];
  node.setAttribute("wildCard", filter.wildCard);
  node.setAttribute("singleChar", filter.singleChar);
  node.setAttribute("escapeChar", filter.escapeChar);
  if (filter.matchCase !== void 0) {
    node.setAttribute("matchCase", filter.matchCase.toString());
  }
  writePropertyName(version, node, filter.propertyName);
  writeLiteral(version, node, "" + filter.pattern);
}
function writeExpression(ns, tagName, node, value) {
  const property = createElementNS(ns, tagName);
  writeStringTextNode(property, value);
  node.appendChild(property);
}
function writeLiteral(version, node, value) {
  writeExpression(getFilterNS(version), "Literal", node, value);
}
function writePropertyName(version, node, value) {
  if (version === "2.0.0") {
    writeExpression(FESNS[version], "ValueReference", node, value);
  } else {
    writeExpression(OGCNS[version], "PropertyName", node, value);
  }
}
function writeTimeInstant(node, time) {
  const timeInstant = createElementNS(GMLNS, "TimeInstant");
  node.appendChild(timeInstant);
  const timePosition = createElementNS(GMLNS, "timePosition");
  timeInstant.appendChild(timePosition);
  writeStringTextNode(timePosition, time);
}
function writeGetFeature(node, featureTypes, objectStack) {
  const context = (
    /** @type {Object} */
    objectStack[objectStack.length - 1]
  );
  const item = (
    /** @type {import("../xml.js").NodeStackItem} */
    Object.assign({}, context)
  );
  item.node = node;
  pushSerializeAndPop(
    item,
    GETFEATURE_SERIALIZERS,
    makeSimpleNodeFactory("Query"),
    featureTypes,
    objectStack
  );
}
function getFilterNS(version) {
  let ns;
  if (version === "2.0.0") {
    ns = FESNS[version];
  } else {
    ns = OGCNS[version];
  }
  return ns;
}
var WFS_default = WFS;

// node_modules/ol/format/WKB.js
var WKBGeometryType = {
  POINT: 1,
  LINE_STRING: 2,
  POLYGON: 3,
  MULTI_POINT: 4,
  MULTI_LINE_STRING: 5,
  MULTI_POLYGON: 6,
  GEOMETRY_COLLECTION: 7,
  /*
    CIRCULAR_STRING: 8,
    COMPOUND_CURVE: 9,
    CURVE_POLYGON: 10,
  
    MULTI_CURVE: 11,
    MULTI_SURFACE: 12,
    CURVE: 13,
    SURFACE: 14,
    */
  POLYHEDRAL_SURFACE: 15,
  TIN: 16,
  TRIANGLE: 17
};
var WkbReader = class {
  /**
   * @param {DataView} view source to read
   */
  constructor(view) {
    this.view_ = view;
    this.pos_ = 0;
    this.initialized_ = false;
    this.isLittleEndian_ = false;
    this.hasZ_ = false;
    this.hasM_ = false;
    this.srid_ = null;
    this.layout_ = "XY";
  }
  /**
   * @return {number} value
   */
  readUint8() {
    return this.view_.getUint8(this.pos_++);
  }
  /**
   * @param {boolean} [isLittleEndian] Whether read value as little endian
   * @return {number} value
   */
  readUint32(isLittleEndian) {
    return this.view_.getUint32(
      (this.pos_ += 4) - 4,
      isLittleEndian !== void 0 ? isLittleEndian : this.isLittleEndian_
    );
  }
  /**
   * @param {boolean} [isLittleEndian] Whether read value as little endian
   * @return {number} value
   */
  readDouble(isLittleEndian) {
    return this.view_.getFloat64(
      (this.pos_ += 8) - 8,
      isLittleEndian !== void 0 ? isLittleEndian : this.isLittleEndian_
    );
  }
  /**
   * @return {import('../coordinate.js').Coordinate} coords for Point
   */
  readPoint() {
    const coords = [];
    coords.push(this.readDouble());
    coords.push(this.readDouble());
    if (this.hasZ_) {
      coords.push(this.readDouble());
    }
    if (this.hasM_) {
      coords.push(this.readDouble());
    }
    return coords;
  }
  /**
   * @return {Array<import('../coordinate.js').Coordinate>} coords for LineString / LinearRing
   */
  readLineString() {
    const numPoints = this.readUint32();
    const coords = [];
    for (let i = 0; i < numPoints; i++) {
      coords.push(this.readPoint());
    }
    return coords;
  }
  /**
   * @return {Array<Array<import('../coordinate.js').Coordinate>>} coords for Polygon like
   */
  readPolygon() {
    const numRings = this.readUint32();
    const rings = [];
    for (let i = 0; i < numRings; i++) {
      rings.push(this.readLineString());
    }
    return rings;
  }
  /**
   * @param {number} [expectedTypeId] Expected WKB Type ID
   * @return {number} WKB Type ID
   */
  readWkbHeader(expectedTypeId) {
    const byteOrder = this.readUint8();
    const isLittleEndian = byteOrder > 0;
    const wkbType = this.readUint32(isLittleEndian);
    const wkbTypeThousandth = Math.floor((wkbType & 268435455) / 1e3);
    const hasZ = Boolean(wkbType & 2147483648) || wkbTypeThousandth === 1 || wkbTypeThousandth === 3;
    const hasM = Boolean(wkbType & 1073741824) || wkbTypeThousandth === 2 || wkbTypeThousandth === 3;
    const hasSRID = Boolean(wkbType & 536870912);
    const typeId = (wkbType & 268435455) % 1e3;
    const layout = (
      /** @type {import("../geom/Geometry.js").GeometryLayout} */
      ["XY", hasZ ? "Z" : "", hasM ? "M" : ""].join("")
    );
    const srid = hasSRID ? this.readUint32(isLittleEndian) : null;
    if (expectedTypeId !== void 0 && expectedTypeId !== typeId) {
      throw new Error("Unexpected WKB geometry type " + typeId);
    }
    if (this.initialized_) {
      if (this.isLittleEndian_ !== isLittleEndian) {
        throw new Error("Inconsistent endian");
      }
      if (this.layout_ !== layout) {
        throw new Error("Inconsistent geometry layout");
      }
      if (srid && this.srid_ !== srid) {
        throw new Error("Inconsistent coordinate system (SRID)");
      }
    } else {
      this.isLittleEndian_ = isLittleEndian;
      this.hasZ_ = hasZ;
      this.hasM_ = hasM;
      this.layout_ = layout;
      this.srid_ = srid;
      this.initialized_ = true;
    }
    return typeId;
  }
  /**
   * @param {number} typeId WKB Type ID
   * @return {any} values read
   */
  readWkbPayload(typeId) {
    switch (typeId) {
      case WKBGeometryType.POINT:
        return this.readPoint();
      case WKBGeometryType.LINE_STRING:
        return this.readLineString();
      case WKBGeometryType.POLYGON:
      case WKBGeometryType.TRIANGLE:
        return this.readPolygon();
      case WKBGeometryType.MULTI_POINT:
        return this.readMultiPoint();
      case WKBGeometryType.MULTI_LINE_STRING:
        return this.readMultiLineString();
      case WKBGeometryType.MULTI_POLYGON:
      case WKBGeometryType.POLYHEDRAL_SURFACE:
      case WKBGeometryType.TIN:
        return this.readMultiPolygon();
      case WKBGeometryType.GEOMETRY_COLLECTION:
        return this.readGeometryCollection();
      default:
        throw new Error(
          "Unsupported WKB geometry type " + typeId + " is found"
        );
    }
  }
  /**
   * @param {number} expectedTypeId Expected WKB Type ID
   * @return {any} values read
   */
  readWkbBlock(expectedTypeId) {
    return this.readWkbPayload(this.readWkbHeader(expectedTypeId));
  }
  /**
   * @param {Function} reader reader function for each item
   * @param {number} [expectedTypeId] Expected WKB Type ID
   * @return {any} values read
   */
  readWkbCollection(reader, expectedTypeId) {
    const num = this.readUint32();
    const items = [];
    for (let i = 0; i < num; i++) {
      const result = reader.call(this, expectedTypeId);
      if (result) {
        items.push(result);
      }
    }
    return items;
  }
  /**
   * @return {Array<import('../coordinate.js').Coordinate>} coords for MultiPoint
   */
  readMultiPoint() {
    return this.readWkbCollection(this.readWkbBlock, WKBGeometryType.POINT);
  }
  /**
   * @return {Array<Array<import('../coordinate.js').Coordinate>>} coords for MultiLineString like
   */
  readMultiLineString() {
    return this.readWkbCollection(
      this.readWkbBlock,
      WKBGeometryType.LINE_STRING
    );
  }
  /**
   * @return {Array<Array<Array<import('../coordinate.js').Coordinate>>>} coords for MultiPolygon like
   */
  readMultiPolygon() {
    return this.readWkbCollection(this.readWkbBlock, WKBGeometryType.POLYGON);
  }
  /**
   * @return {Array<import('../geom/Geometry.js').default>} array of geometries
   */
  readGeometryCollection() {
    return this.readWkbCollection(this.readGeometry);
  }
  /**
   * @return {import('../geom/Geometry.js').default} geometry
   */
  readGeometry() {
    const typeId = this.readWkbHeader();
    const result = this.readWkbPayload(typeId);
    switch (typeId) {
      case WKBGeometryType.POINT:
        return new Point_default(
          /** @type {import('../coordinate.js').Coordinate} */
          result,
          this.layout_
        );
      case WKBGeometryType.LINE_STRING:
        return new LineString_default(
          /** @type {Array<import('../coordinate.js').Coordinate>} */
          result,
          this.layout_
        );
      case WKBGeometryType.POLYGON:
      case WKBGeometryType.TRIANGLE:
        return new Polygon_default(
          /** @type {Array<Array<import('../coordinate.js').Coordinate>>} */
          result,
          this.layout_
        );
      case WKBGeometryType.MULTI_POINT:
        return new MultiPoint_default(
          /** @type {Array<import('../coordinate.js').Coordinate>} */
          result,
          this.layout_
        );
      case WKBGeometryType.MULTI_LINE_STRING:
        return new MultiLineString_default(
          /** @type {Array<Array<import('../coordinate.js').Coordinate>>} */
          result,
          this.layout_
        );
      case WKBGeometryType.MULTI_POLYGON:
      case WKBGeometryType.POLYHEDRAL_SURFACE:
      case WKBGeometryType.TIN:
        return new MultiPolygon_default(
          /** @type {Array<Array<Array<import('../coordinate.js').Coordinate>>>} */
          result,
          this.layout_
        );
      case WKBGeometryType.GEOMETRY_COLLECTION:
        return new GeometryCollection_default(
          /** @type {Array<import('../geom/Geometry.js').default>} */
          result
        );
      default:
        return null;
    }
  }
  /**
   * @return {number|null} SRID in the EWKB. `null` if not defined.
   */
  getSrid() {
    return this.srid_;
  }
};
var WkbWriter = class {
  /**
   * @type {Object}
   * @property {string} [layout] geometryLayout
   * @property {boolean} [littleEndian=true] littleEndian
   * @property {boolean} [ewkb=true] Whether writes in EWKB format
   * @property {Object} [nodata] NoData value for each axes
   * @param {Object} opts options
   */
  constructor(opts) {
    opts = opts || {};
    this.layout_ = opts.layout;
    this.isLittleEndian_ = opts.littleEndian !== false;
    this.isEWKB_ = opts.ewkb !== false;
    this.writeQueue_ = [];
    this.nodata_ = Object.assign({ X: 0, Y: 0, Z: 0, M: 0 }, opts.nodata);
  }
  /**
   * @param {number} value value
   */
  writeUint8(value) {
    this.writeQueue_.push([1, value]);
  }
  /**
   * @param {number} value value
   */
  writeUint32(value) {
    this.writeQueue_.push([4, value]);
  }
  /**
   * @param {number} value value
   */
  writeDouble(value) {
    this.writeQueue_.push([8, value]);
  }
  /**
   * @param {import('../coordinate.js').Coordinate} coords coords
   * @param {import("../geom/Geometry.js").GeometryLayout} layout layout
   */
  writePoint(coords, layout) {
    const coordsObj = Object.assign.apply(
      null,
      layout.split("").map((axis, idx) => ({ [axis]: coords[idx] }))
    );
    for (const axis of this.layout_) {
      this.writeDouble(
        axis in coordsObj ? coordsObj[axis] : this.nodata_[axis]
      );
    }
  }
  /**
   * @param {Array<import('../coordinate.js').Coordinate>} coords coords
   * @param {import("../geom/Geometry.js").GeometryLayout} layout layout
   */
  writeLineString(coords, layout) {
    this.writeUint32(coords.length);
    for (let i = 0; i < coords.length; i++) {
      this.writePoint(coords[i], layout);
    }
  }
  /**
   * @param {Array<Array<import('../coordinate.js').Coordinate>>} rings rings
   * @param {import("../geom/Geometry.js").GeometryLayout} layout layout
   */
  writePolygon(rings, layout) {
    this.writeUint32(rings.length);
    for (let i = 0; i < rings.length; i++) {
      this.writeLineString(rings[i], layout);
    }
  }
  /**
   * @param {number} wkbType WKB Type ID
   * @param {number} [srid] SRID
   */
  writeWkbHeader(wkbType, srid) {
    wkbType %= 1e3;
    if (this.layout_.includes("Z")) {
      wkbType += this.isEWKB_ ? 2147483648 : 1e3;
    }
    if (this.layout_.includes("M")) {
      wkbType += this.isEWKB_ ? 1073741824 : 2e3;
    }
    if (this.isEWKB_ && Number.isInteger(srid)) {
      wkbType |= 536870912;
    }
    this.writeUint8(this.isLittleEndian_ ? 1 : 0);
    this.writeUint32(wkbType);
    if (this.isEWKB_ && Number.isInteger(srid)) {
      this.writeUint32(srid);
    }
  }
  /**
   * @param {Array<import('../coordinate.js').Coordinate>} coords coords
   * @param {import("../geom/Geometry.js").GeometryLayout} layout layout
   */
  writeMultiPoint(coords, layout) {
    this.writeUint32(coords.length);
    for (let i = 0; i < coords.length; i++) {
      this.writeWkbHeader(1);
      this.writePoint(coords[i], layout);
    }
  }
  /**
   * @param {Array<Array<import('../coordinate.js').Coordinate>>} coords coords
   * @param {import("../geom/Geometry.js").GeometryLayout} layout layout
   */
  writeMultiLineString(coords, layout) {
    this.writeUint32(coords.length);
    for (let i = 0; i < coords.length; i++) {
      this.writeWkbHeader(2);
      this.writeLineString(coords[i], layout);
    }
  }
  /**
   * @param {Array<Array<Array<import('../coordinate.js').Coordinate>>>} coords coords
   * @param {import("../geom/Geometry.js").GeometryLayout} layout layout
   */
  writeMultiPolygon(coords, layout) {
    this.writeUint32(coords.length);
    for (let i = 0; i < coords.length; i++) {
      this.writeWkbHeader(3);
      this.writePolygon(coords[i], layout);
    }
  }
  /**
   * @param {Array<import('../geom/Geometry.js').default>} geometries geometries
   */
  writeGeometryCollection(geometries) {
    this.writeUint32(geometries.length);
    for (let i = 0; i < geometries.length; i++) {
      this.writeGeometry(geometries[i]);
    }
  }
  /**
   * @param {import("../geom/Geometry.js").default} geom geometry
   * @param {import("../geom/Geometry.js").GeometryLayout} [layout] layout
   * @return {import("../geom/Geometry.js").GeometryLayout} minimum layout made by common axes
   */
  findMinimumLayout(geom, layout = "XYZM") {
    const GeometryLayout_min = (a, b) => {
      if (a === b) {
        return a;
      }
      if (a === "XYZM") {
        return b;
      }
      if (b === "XYZM") {
        return a;
      }
      return "XY";
    };
    if (geom instanceof SimpleGeometry_default) {
      return GeometryLayout_min(geom.getLayout(), layout);
    }
    if (geom instanceof GeometryCollection_default) {
      const geoms = geom.getGeometriesArray();
      for (let i = 0; i < geoms.length && layout !== "XY"; i++) {
        layout = this.findMinimumLayout(geoms[i], layout);
      }
    }
    return layout;
  }
  /**
   * @param {import("../geom/Geometry.js").default} geom geometry
   * @param {number} [srid] SRID
   */
  writeGeometry(geom, srid) {
    const wkblut = {
      Point: WKBGeometryType.POINT,
      LineString: WKBGeometryType.LINE_STRING,
      Polygon: WKBGeometryType.POLYGON,
      MultiPoint: WKBGeometryType.MULTI_POINT,
      MultiLineString: WKBGeometryType.MULTI_LINE_STRING,
      MultiPolygon: WKBGeometryType.MULTI_POLYGON,
      GeometryCollection: WKBGeometryType.GEOMETRY_COLLECTION
    };
    const geomType = geom.getType();
    const typeId = wkblut[geomType];
    if (!typeId) {
      throw new Error("GeometryType " + geomType + " is not supported");
    }
    if (!this.layout_) {
      this.layout_ = this.findMinimumLayout(geom);
    }
    this.writeWkbHeader(typeId, srid);
    if (geom instanceof SimpleGeometry_default) {
      const writerLUT = {
        Point: this.writePoint,
        LineString: this.writeLineString,
        Polygon: this.writePolygon,
        MultiPoint: this.writeMultiPoint,
        MultiLineString: this.writeMultiLineString,
        MultiPolygon: this.writeMultiPolygon
      };
      writerLUT[geomType].call(this, geom.getCoordinates(), geom.getLayout());
    } else if (geom instanceof GeometryCollection_default) {
      this.writeGeometryCollection(geom.getGeometriesArray());
    }
  }
  getBuffer() {
    const byteLength = this.writeQueue_.reduce((acc, item) => acc + item[0], 0);
    const buffer = new ArrayBuffer(byteLength);
    const view = new DataView(buffer);
    let pos = 0;
    this.writeQueue_.forEach((item) => {
      switch (item[0]) {
        case 1:
          view.setUint8(pos, item[1]);
          break;
        case 4:
          view.setUint32(pos, item[1], this.isLittleEndian_);
          break;
        case 8:
          view.setFloat64(pos, item[1], this.isLittleEndian_);
          break;
        default:
          break;
      }
      pos += item[0];
    });
    return buffer;
  }
};
var WKB = class extends Feature_default2 {
  /**
   * @param {Options} [options] Optional configuration object.
   */
  constructor(options) {
    super();
    options = options ? options : {};
    this.splitCollection = Boolean(options.splitCollection);
    this.viewCache_ = null;
    this.hex_ = options.hex !== false;
    this.littleEndian_ = options.littleEndian !== false;
    this.ewkb_ = options.ewkb !== false;
    this.layout_ = options.geometryLayout;
    this.nodataZ_ = options.nodataZ || 0;
    this.nodataM_ = options.nodataM || 0;
    this.srid_ = options.srid;
  }
  /**
   * @return {import("./Feature.js").Type} Format.
   */
  getType() {
    return this.hex_ ? "text" : "arraybuffer";
  }
  /**
   * Read a single feature from a source.
   *
   * @param {string|ArrayBuffer|ArrayBufferView} source Source.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @return {import("../Feature.js").default} Feature.
   * @api
   */
  readFeature(source, options) {
    return new Feature_default({
      geometry: this.readGeometry(source, options)
    });
  }
  /**
   * Read all features from a source.
   *
   * @param {string|ArrayBuffer|ArrayBufferView} source Source.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @return {Array<import("../Feature.js").default>} Features.
   * @api
   */
  readFeatures(source, options) {
    let geometries = [];
    const geometry = this.readGeometry(source, options);
    if (this.splitCollection && geometry instanceof GeometryCollection_default) {
      geometries = geometry.getGeometriesArray();
    } else {
      geometries = [geometry];
    }
    return geometries.map((geometry2) => new Feature_default({ geometry: geometry2 }));
  }
  /**
   * Read a single geometry from a source.
   *
   * @param {string|ArrayBuffer|ArrayBufferView} source Source.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @return {import("../geom/Geometry.js").default} Geometry.
   * @api
   */
  readGeometry(source, options) {
    const view = getDataView(source);
    if (!view) {
      return null;
    }
    const reader = new WkbReader(view);
    const geometry = reader.readGeometry();
    this.viewCache_ = view;
    options = this.getReadOptions(source, options);
    this.viewCache_ = null;
    return transformGeometryWithOptions(geometry, false, options);
  }
  /**
   * Read the projection from a source.
   *
   * @param {string|ArrayBuffer|ArrayBufferView} source Source.
   * @return {import("../proj/Projection.js").default|undefined} Projection.
   * @api
   */
  readProjection(source) {
    const view = this.viewCache_ || getDataView(source);
    if (!view) {
      return void 0;
    }
    const reader = new WkbReader(view);
    reader.readWkbHeader();
    return reader.getSrid() && get("EPSG:" + reader.getSrid()) || void 0;
  }
  /**
   * Encode a feature in this format.
   *
   * @param {import("../Feature.js").default} feature Feature.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {string|ArrayBuffer} Result.
   * @api
   */
  writeFeature(feature, options) {
    return this.writeGeometry(feature.getGeometry(), options);
  }
  /**
   * Encode an array of features in this format.
   *
   * @param {Array<import("../Feature.js").default>} features Features.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {string|ArrayBuffer} Result.
   * @api
   */
  writeFeatures(features, options) {
    return this.writeGeometry(
      new GeometryCollection_default(features.map((f) => f.getGeometry())),
      options
    );
  }
  /**
   * Write a single geometry in this format.
   *
   * @param {import("../geom/Geometry.js").default} geometry Geometry.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @return {string|ArrayBuffer} Result.
   * @api
   */
  writeGeometry(geometry, options) {
    options = this.adaptOptions(options);
    const writer = new WkbWriter({
      layout: this.layout_,
      littleEndian: this.littleEndian_,
      ewkb: this.ewkb_,
      nodata: {
        Z: this.nodataZ_,
        M: this.nodataM_
      }
    });
    let srid = Number.isInteger(this.srid_) ? Number(this.srid_) : null;
    if (this.srid_ !== false && !Number.isInteger(this.srid_)) {
      const dataProjection = options.dataProjection && get(options.dataProjection);
      if (dataProjection) {
        const code = dataProjection.getCode();
        if (code.startsWith("EPSG:")) {
          srid = Number(code.substring(5));
        }
      }
    }
    writer.writeGeometry(
      transformGeometryWithOptions(geometry, true, options),
      srid
    );
    const buffer = writer.getBuffer();
    return this.hex_ ? encodeHexString(buffer) : buffer;
  }
};
function encodeHexString(buffer) {
  const view = new Uint8Array(buffer);
  return Array.from(view.values()).map((x) => (x < 16 ? "0" : "") + Number(x).toString(16).toUpperCase()).join("");
}
function decodeHexString(text) {
  const buffer = new Uint8Array(text.length / 2);
  for (let i = 0; i < text.length / 2; i++) {
    buffer[i] = parseInt(text.substr(i * 2, 2), 16);
  }
  return new DataView(buffer.buffer);
}
function getDataView(source) {
  if (typeof source === "string") {
    return decodeHexString(source);
  }
  if (ArrayBuffer.isView(source)) {
    if (source instanceof DataView) {
      return source;
    }
    return new DataView(source.buffer, source.byteOffset, source.byteLength);
  }
  if (source instanceof ArrayBuffer) {
    return new DataView(source);
  }
  return null;
}
var WKB_default = WKB;

// node_modules/ol/format/WKT.js
var GeometryConstructor = {
  "POINT": Point_default,
  "LINESTRING": LineString_default,
  "POLYGON": Polygon_default,
  "MULTIPOINT": MultiPoint_default,
  "MULTILINESTRING": MultiLineString_default,
  "MULTIPOLYGON": MultiPolygon_default
};
var EMPTY = "EMPTY";
var Z = "Z";
var M = "M";
var ZM = "ZM";
var TokenType = {
  START: 0,
  TEXT: 1,
  LEFT_PAREN: 2,
  RIGHT_PAREN: 3,
  NUMBER: 4,
  COMMA: 5,
  EOF: 6
};
var wktTypeLookup = {
  Point: "POINT",
  LineString: "LINESTRING",
  Polygon: "POLYGON",
  MultiPoint: "MULTIPOINT",
  MultiLineString: "MULTILINESTRING",
  MultiPolygon: "MULTIPOLYGON",
  GeometryCollection: "GEOMETRYCOLLECTION",
  Circle: "CIRCLE"
};
var Lexer = class {
  /**
   * @param {string} wkt WKT string.
   */
  constructor(wkt) {
    this.wkt = wkt;
    this.index_ = -1;
  }
  /**
   * @param {string} c Character.
   * @return {boolean} Whether the character is alphabetic.
   * @private
   */
  isAlpha_(c) {
    return c >= "a" && c <= "z" || c >= "A" && c <= "Z";
  }
  /**
   * @param {string} c Character.
   * @param {boolean} [decimal] Whether the string number
   *     contains a dot, i.e. is a decimal number.
   * @return {boolean} Whether the character is numeric.
   * @private
   */
  isNumeric_(c, decimal) {
    decimal = decimal !== void 0 ? decimal : false;
    return c >= "0" && c <= "9" || c == "." && !decimal;
  }
  /**
   * @param {string} c Character.
   * @return {boolean} Whether the character is whitespace.
   * @private
   */
  isWhiteSpace_(c) {
    return c == " " || c == "	" || c == "\r" || c == "\n";
  }
  /**
   * @return {string} Next string character.
   * @private
   */
  nextChar_() {
    return this.wkt.charAt(++this.index_);
  }
  /**
   * Fetch and return the next token.
   * @return {Token} Next string token.
   */
  nextToken() {
    const c = this.nextChar_();
    const position = this.index_;
    let value = c;
    let type;
    if (c == "(") {
      type = TokenType.LEFT_PAREN;
    } else if (c == ",") {
      type = TokenType.COMMA;
    } else if (c == ")") {
      type = TokenType.RIGHT_PAREN;
    } else if (this.isNumeric_(c) || c == "-") {
      type = TokenType.NUMBER;
      value = this.readNumber_();
    } else if (this.isAlpha_(c)) {
      type = TokenType.TEXT;
      value = this.readText_();
    } else if (this.isWhiteSpace_(c)) {
      return this.nextToken();
    } else if (c === "") {
      type = TokenType.EOF;
    } else {
      throw new Error("Unexpected character: " + c);
    }
    return { position, value, type };
  }
  /**
   * @return {number} Numeric token value.
   * @private
   */
  readNumber_() {
    let c;
    const index = this.index_;
    let decimal = false;
    let scientificNotation = false;
    do {
      if (c == ".") {
        decimal = true;
      } else if (c == "e" || c == "E") {
        scientificNotation = true;
      }
      c = this.nextChar_();
    } while (this.isNumeric_(c, decimal) || // if we haven't detected a scientific number before, 'e' or 'E'
    // hint that we should continue to read
    !scientificNotation && (c == "e" || c == "E") || // once we know that we have a scientific number, both '-' and '+'
    // are allowed
    scientificNotation && (c == "-" || c == "+"));
    return parseFloat(this.wkt.substring(index, this.index_--));
  }
  /**
   * @return {string} String token value.
   * @private
   */
  readText_() {
    let c;
    const index = this.index_;
    do {
      c = this.nextChar_();
    } while (this.isAlpha_(c));
    return this.wkt.substring(index, this.index_--).toUpperCase();
  }
};
var Parser = class {
  /**
   * @param {Lexer} lexer The lexer.
   */
  constructor(lexer) {
    this.lexer_ = lexer;
    this.token_ = {
      position: 0,
      type: TokenType.START
    };
    this.layout_ = "XY";
  }
  /**
   * Fetch the next token form the lexer and replace the active token.
   * @private
   */
  consume_() {
    this.token_ = this.lexer_.nextToken();
  }
  /**
   * Tests if the given type matches the type of the current token.
   * @param {TokenType} type Token type.
   * @return {boolean} Whether the token matches the given type.
   */
  isTokenType(type) {
    return this.token_.type == type;
  }
  /**
   * If the given type matches the current token, consume it.
   * @param {TokenType} type Token type.
   * @return {boolean} Whether the token matches the given type.
   */
  match(type) {
    const isMatch = this.isTokenType(type);
    if (isMatch) {
      this.consume_();
    }
    return isMatch;
  }
  /**
   * Try to parse the tokens provided by the lexer.
   * @return {import("../geom/Geometry.js").default} The geometry.
   */
  parse() {
    this.consume_();
    return this.parseGeometry_();
  }
  /**
   * Try to parse the dimensional info.
   * @return {import("../geom/Geometry.js").GeometryLayout} The layout.
   * @private
   */
  parseGeometryLayout_() {
    let layout = "XY";
    const dimToken = this.token_;
    if (this.isTokenType(TokenType.TEXT)) {
      const dimInfo = dimToken.value;
      if (dimInfo === Z) {
        layout = "XYZ";
      } else if (dimInfo === M) {
        layout = "XYM";
      } else if (dimInfo === ZM) {
        layout = "XYZM";
      }
      if (layout !== "XY") {
        this.consume_();
      }
    }
    return layout;
  }
  /**
   * @return {Array<import("../geom/Geometry.js").default>} A collection of geometries.
   * @private
   */
  parseGeometryCollectionText_() {
    if (this.match(TokenType.LEFT_PAREN)) {
      const geometries = [];
      do {
        geometries.push(this.parseGeometry_());
      } while (this.match(TokenType.COMMA));
      if (this.match(TokenType.RIGHT_PAREN)) {
        return geometries;
      }
    }
    throw new Error(this.formatErrorMessage_());
  }
  /**
   * @return {Array<number>} All values in a point.
   * @private
   */
  parsePointText_() {
    if (this.match(TokenType.LEFT_PAREN)) {
      const coordinates = this.parsePoint_();
      if (this.match(TokenType.RIGHT_PAREN)) {
        return coordinates;
      }
    }
    throw new Error(this.formatErrorMessage_());
  }
  /**
   * @return {Array<Array<number>>} All points in a linestring.
   * @private
   */
  parseLineStringText_() {
    if (this.match(TokenType.LEFT_PAREN)) {
      const coordinates = this.parsePointList_();
      if (this.match(TokenType.RIGHT_PAREN)) {
        return coordinates;
      }
    }
    throw new Error(this.formatErrorMessage_());
  }
  /**
   * @return {Array<Array<Array<number>>>} All points in a polygon.
   * @private
   */
  parsePolygonText_() {
    if (this.match(TokenType.LEFT_PAREN)) {
      const coordinates = this.parseLineStringTextList_();
      if (this.match(TokenType.RIGHT_PAREN)) {
        return coordinates;
      }
    }
    throw new Error(this.formatErrorMessage_());
  }
  /**
   * @return {Array<Array<number>>} All points in a multipoint.
   * @private
   */
  parseMultiPointText_() {
    if (this.match(TokenType.LEFT_PAREN)) {
      let coordinates;
      if (this.token_.type == TokenType.LEFT_PAREN) {
        coordinates = this.parsePointTextList_();
      } else {
        coordinates = this.parsePointList_();
      }
      if (this.match(TokenType.RIGHT_PAREN)) {
        return coordinates;
      }
    }
    throw new Error(this.formatErrorMessage_());
  }
  /**
   * @return {Array<Array<Array<number>>>} All linestring points
   *                                          in a multilinestring.
   * @private
   */
  parseMultiLineStringText_() {
    if (this.match(TokenType.LEFT_PAREN)) {
      const coordinates = this.parseLineStringTextList_();
      if (this.match(TokenType.RIGHT_PAREN)) {
        return coordinates;
      }
    }
    throw new Error(this.formatErrorMessage_());
  }
  /**
   * @return {Array<Array<Array<Array<number>>>>} All polygon points in a multipolygon.
   * @private
   */
  parseMultiPolygonText_() {
    if (this.match(TokenType.LEFT_PAREN)) {
      const coordinates = this.parsePolygonTextList_();
      if (this.match(TokenType.RIGHT_PAREN)) {
        return coordinates;
      }
    }
    throw new Error(this.formatErrorMessage_());
  }
  /**
   * @return {Array<number>} A point.
   * @private
   */
  parsePoint_() {
    const coordinates = [];
    const dimensions = this.layout_.length;
    for (let i = 0; i < dimensions; ++i) {
      const token = this.token_;
      if (this.match(TokenType.NUMBER)) {
        coordinates.push(
          /** @type {number} */
          token.value
        );
      } else {
        break;
      }
    }
    if (coordinates.length == dimensions) {
      return coordinates;
    }
    throw new Error(this.formatErrorMessage_());
  }
  /**
   * @return {Array<Array<number>>} An array of points.
   * @private
   */
  parsePointList_() {
    const coordinates = [this.parsePoint_()];
    while (this.match(TokenType.COMMA)) {
      coordinates.push(this.parsePoint_());
    }
    return coordinates;
  }
  /**
   * @return {Array<Array<number>>} An array of points.
   * @private
   */
  parsePointTextList_() {
    const coordinates = [this.parsePointText_()];
    while (this.match(TokenType.COMMA)) {
      coordinates.push(this.parsePointText_());
    }
    return coordinates;
  }
  /**
   * @return {Array<Array<Array<number>>>} An array of points.
   * @private
   */
  parseLineStringTextList_() {
    const coordinates = [this.parseLineStringText_()];
    while (this.match(TokenType.COMMA)) {
      coordinates.push(this.parseLineStringText_());
    }
    return coordinates;
  }
  /**
   * @return {Array<Array<Array<Array<number>>>>} An array of points.
   * @private
   */
  parsePolygonTextList_() {
    const coordinates = [this.parsePolygonText_()];
    while (this.match(TokenType.COMMA)) {
      coordinates.push(this.parsePolygonText_());
    }
    return coordinates;
  }
  /**
   * @return {boolean} Whether the token implies an empty geometry.
   * @private
   */
  isEmptyGeometry_() {
    const isEmpty2 = this.isTokenType(TokenType.TEXT) && this.token_.value == EMPTY;
    if (isEmpty2) {
      this.consume_();
    }
    return isEmpty2;
  }
  /**
   * Create an error message for an unexpected token error.
   * @return {string} Error message.
   * @private
   */
  formatErrorMessage_() {
    return "Unexpected `" + this.token_.value + "` at position " + this.token_.position + " in `" + this.lexer_.wkt + "`";
  }
  /**
   * @return {import("../geom/Geometry.js").default} The geometry.
   * @private
   */
  parseGeometry_() {
    const token = this.token_;
    if (this.match(TokenType.TEXT)) {
      const geomType = (
        /** @type {string} */
        token.value
      );
      this.layout_ = this.parseGeometryLayout_();
      const isEmpty2 = this.isEmptyGeometry_();
      if (geomType == "GEOMETRYCOLLECTION") {
        if (isEmpty2) {
          return new GeometryCollection_default([]);
        }
        const geometries = this.parseGeometryCollectionText_();
        return new GeometryCollection_default(geometries);
      }
      const ctor = GeometryConstructor[geomType];
      if (!ctor) {
        throw new Error("Invalid geometry type: " + geomType);
      }
      let coordinates;
      if (isEmpty2) {
        if (geomType == "POINT") {
          coordinates = [NaN, NaN];
        } else {
          coordinates = [];
        }
      } else {
        switch (geomType) {
          case "POINT": {
            coordinates = this.parsePointText_();
            break;
          }
          case "LINESTRING": {
            coordinates = this.parseLineStringText_();
            break;
          }
          case "POLYGON": {
            coordinates = this.parsePolygonText_();
            break;
          }
          case "MULTIPOINT": {
            coordinates = this.parseMultiPointText_();
            break;
          }
          case "MULTILINESTRING": {
            coordinates = this.parseMultiLineStringText_();
            break;
          }
          case "MULTIPOLYGON": {
            coordinates = this.parseMultiPolygonText_();
            break;
          }
          default:
            break;
        }
      }
      return new ctor(coordinates, this.layout_);
    }
    throw new Error(this.formatErrorMessage_());
  }
};
var WKT = class extends TextFeature_default {
  /**
   * @param {Options} [options] Options.
   */
  constructor(options) {
    super();
    options = options ? options : {};
    this.splitCollection_ = options.splitCollection !== void 0 ? options.splitCollection : false;
  }
  /**
   * Parse a WKT string.
   * @param {string} wkt WKT string.
   * @return {import("../geom/Geometry.js").default}
   *     The geometry created.
   * @private
   */
  parse_(wkt) {
    const lexer = new Lexer(wkt);
    const parser = new Parser(lexer);
    return parser.parse();
  }
  /**
   * @protected
   * @param {string} text Text.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @return {import("../Feature.js").default} Feature.
   */
  readFeatureFromText(text, options) {
    const geom = this.readGeometryFromText(text, options);
    const feature = new Feature_default();
    feature.setGeometry(geom);
    return feature;
  }
  /**
   * @param {string} text Text.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {Array<Feature>} Features.
   */
  readFeaturesFromText(text, options) {
    let geometries = [];
    const geometry = this.readGeometryFromText(text, options);
    if (this.splitCollection_ && geometry.getType() == "GeometryCollection") {
      geometries = /** @type {GeometryCollection} */
      geometry.getGeometriesArray();
    } else {
      geometries = [geometry];
    }
    const features = [];
    for (let i = 0, ii = geometries.length; i < ii; ++i) {
      const feature = new Feature_default();
      feature.setGeometry(geometries[i]);
      features.push(feature);
    }
    return features;
  }
  /**
   * @param {string} text Text.
   * @param {import("./Feature.js").ReadOptions} [options] Read options.
   * @protected
   * @return {import("../geom/Geometry.js").default} Geometry.
   */
  readGeometryFromText(text, options) {
    const geometry = this.parse_(text);
    return transformGeometryWithOptions(geometry, false, options);
  }
  /**
   * @param {import("../Feature.js").default} feature Features.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @protected
   * @return {string} Text.
   */
  writeFeatureText(feature, options) {
    const geometry = feature.getGeometry();
    if (geometry) {
      return this.writeGeometryText(geometry, options);
    }
    return "";
  }
  /**
   * @param {Array<import("../Feature.js").default>} features Features.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @protected
   * @return {string} Text.
   */
  writeFeaturesText(features, options) {
    if (features.length == 1) {
      return this.writeFeatureText(features[0], options);
    }
    const geometries = [];
    for (let i = 0, ii = features.length; i < ii; ++i) {
      geometries.push(features[i].getGeometry());
    }
    const collection = new GeometryCollection_default(geometries);
    return this.writeGeometryText(collection, options);
  }
  /**
   * @param {import("../geom/Geometry.js").default} geometry Geometry.
   * @param {import("./Feature.js").WriteOptions} [options] Write options.
   * @protected
   * @return {string} Text.
   */
  writeGeometryText(geometry, options) {
    return encode(transformGeometryWithOptions(geometry, true, options));
  }
};
function encodePointGeometry(geom) {
  const coordinates = geom.getCoordinates();
  if (coordinates.length === 0) {
    return "";
  }
  return coordinates.join(" ");
}
function encodeMultiPointGeometry(geom) {
  const array = [];
  const components = geom.getPoints();
  for (let i = 0, ii = components.length; i < ii; ++i) {
    array.push("(" + encodePointGeometry(components[i]) + ")");
  }
  return array.join(",");
}
function encodeGeometryCollectionGeometry(geom) {
  const array = [];
  const geoms = geom.getGeometries();
  for (let i = 0, ii = geoms.length; i < ii; ++i) {
    array.push(encode(geoms[i]));
  }
  return array.join(",");
}
function encodeLineStringGeometry(geom) {
  const coordinates = geom.getCoordinates();
  const array = [];
  for (let i = 0, ii = coordinates.length; i < ii; ++i) {
    array.push(coordinates[i].join(" "));
  }
  return array.join(",");
}
function encodeMultiLineStringGeometry(geom) {
  const array = [];
  const components = geom.getLineStrings();
  for (let i = 0, ii = components.length; i < ii; ++i) {
    array.push("(" + encodeLineStringGeometry(components[i]) + ")");
  }
  return array.join(",");
}
function encodePolygonGeometry(geom) {
  const array = [];
  const rings = geom.getLinearRings();
  for (let i = 0, ii = rings.length; i < ii; ++i) {
    array.push("(" + encodeLineStringGeometry(rings[i]) + ")");
  }
  return array.join(",");
}
function encodeMultiPolygonGeometry(geom) {
  const array = [];
  const components = geom.getPolygons();
  for (let i = 0, ii = components.length; i < ii; ++i) {
    array.push("(" + encodePolygonGeometry(components[i]) + ")");
  }
  return array.join(",");
}
function encodeGeometryLayout(geom) {
  const layout = geom.getLayout();
  let dimInfo = "";
  if (layout === "XYZ" || layout === "XYZM") {
    dimInfo += Z;
  }
  if (layout === "XYM" || layout === "XYZM") {
    dimInfo += M;
  }
  return dimInfo;
}
var GeometryEncoder = {
  "Point": encodePointGeometry,
  "LineString": encodeLineStringGeometry,
  "Polygon": encodePolygonGeometry,
  "MultiPoint": encodeMultiPointGeometry,
  "MultiLineString": encodeMultiLineStringGeometry,
  "MultiPolygon": encodeMultiPolygonGeometry,
  "GeometryCollection": encodeGeometryCollectionGeometry
};
function encode(geom) {
  const type = geom.getType();
  const geometryEncoder = GeometryEncoder[type];
  const enc = geometryEncoder(geom);
  let wktType = wktTypeLookup[type];
  if (typeof /** @type {?} */
  geom.getFlatCoordinates === "function") {
    const dimInfo = encodeGeometryLayout(
      /** @type {import("../geom/SimpleGeometry.js").default} */
      geom
    );
    if (dimInfo.length > 0) {
      wktType += " " + dimInfo;
    }
  }
  if (enc.length === 0) {
    return wktType + " " + EMPTY;
  }
  return wktType + "(" + enc + ")";
}
var WKT_default = WKT;

// node_modules/ol/format/WMSCapabilities.js
var NAMESPACE_URIS4 = [null, "http://www.opengis.net/wms"];
var PARSERS2 = makeStructureNS(NAMESPACE_URIS4, {
  "Service": makeObjectPropertySetter(readService),
  "Capability": makeObjectPropertySetter(readCapability)
});
var CAPABILITY_PARSERS = makeStructureNS(NAMESPACE_URIS4, {
  "Request": makeObjectPropertySetter(readRequest),
  "Exception": makeObjectPropertySetter(readException),
  "Layer": makeObjectPropertySetter(readCapabilityLayer)
});
var WMSCapabilities = class extends XML_default {
  constructor() {
    super();
    this.version = void 0;
  }
  /**
   * @param {Element} node Node.
   * @return {Object} Object
   */
  readFromNode(node) {
    this.version = node.getAttribute("version").trim();
    const wmsCapabilityObject = pushParseAndPop(
      {
        "version": this.version
      },
      PARSERS2,
      node,
      []
    );
    return wmsCapabilityObject ? wmsCapabilityObject : null;
  }
};
var SERVICE_PARSERS = makeStructureNS(NAMESPACE_URIS4, {
  "Name": makeObjectPropertySetter(readString),
  "Title": makeObjectPropertySetter(readString),
  "Abstract": makeObjectPropertySetter(readString),
  "KeywordList": makeObjectPropertySetter(readKeywordList),
  "OnlineResource": makeObjectPropertySetter(readHref),
  "ContactInformation": makeObjectPropertySetter(readContactInformation),
  "Fees": makeObjectPropertySetter(readString),
  "AccessConstraints": makeObjectPropertySetter(readString),
  "LayerLimit": makeObjectPropertySetter(readPositiveInteger),
  "MaxWidth": makeObjectPropertySetter(readPositiveInteger),
  "MaxHeight": makeObjectPropertySetter(readPositiveInteger)
});
var CONTACT_INFORMATION_PARSERS = makeStructureNS(NAMESPACE_URIS4, {
  "ContactPersonPrimary": makeObjectPropertySetter(readContactPersonPrimary),
  "ContactPosition": makeObjectPropertySetter(readString),
  "ContactAddress": makeObjectPropertySetter(readContactAddress),
  "ContactVoiceTelephone": makeObjectPropertySetter(readString),
  "ContactFacsimileTelephone": makeObjectPropertySetter(readString),
  "ContactElectronicMailAddress": makeObjectPropertySetter(readString)
});
var CONTACT_PERSON_PARSERS = makeStructureNS(NAMESPACE_URIS4, {
  "ContactPerson": makeObjectPropertySetter(readString),
  "ContactOrganization": makeObjectPropertySetter(readString)
});
var CONTACT_ADDRESS_PARSERS = makeStructureNS(NAMESPACE_URIS4, {
  "AddressType": makeObjectPropertySetter(readString),
  "Address": makeObjectPropertySetter(readString),
  "City": makeObjectPropertySetter(readString),
  "StateOrProvince": makeObjectPropertySetter(readString),
  "PostCode": makeObjectPropertySetter(readString),
  "Country": makeObjectPropertySetter(readString)
});
var EXCEPTION_PARSERS = makeStructureNS(NAMESPACE_URIS4, {
  "Format": makeArrayPusher(readString)
});
var LAYER_PARSERS = makeStructureNS(NAMESPACE_URIS4, {
  "Name": makeObjectPropertySetter(readString),
  "Title": makeObjectPropertySetter(readString),
  "Abstract": makeObjectPropertySetter(readString),
  "KeywordList": makeObjectPropertySetter(readKeywordList),
  "CRS": makeObjectPropertyPusher(readString),
  "EX_GeographicBoundingBox": makeObjectPropertySetter(
    readEXGeographicBoundingBox
  ),
  "BoundingBox": makeObjectPropertyPusher(readBoundingBox),
  "Dimension": makeObjectPropertyPusher(readDimension),
  "Attribution": makeObjectPropertySetter(readAttribution),
  "AuthorityURL": makeObjectPropertyPusher(readAuthorityURL),
  "Identifier": makeObjectPropertyPusher(readString),
  "MetadataURL": makeObjectPropertyPusher(readMetadataURL),
  "DataURL": makeObjectPropertyPusher(readFormatOnlineresource),
  "FeatureListURL": makeObjectPropertyPusher(readFormatOnlineresource),
  "Style": makeObjectPropertyPusher(readStyle2),
  "MinScaleDenominator": makeObjectPropertySetter(readDecimal),
  "MaxScaleDenominator": makeObjectPropertySetter(readDecimal),
  "Layer": makeObjectPropertyPusher(readLayer)
});
var ATTRIBUTION_PARSERS = makeStructureNS(NAMESPACE_URIS4, {
  "Title": makeObjectPropertySetter(readString),
  "OnlineResource": makeObjectPropertySetter(readHref),
  "LogoURL": makeObjectPropertySetter(readSizedFormatOnlineresource)
});
var EX_GEOGRAPHIC_BOUNDING_BOX_PARSERS = makeStructureNS(NAMESPACE_URIS4, {
  "westBoundLongitude": makeObjectPropertySetter(readDecimal),
  "eastBoundLongitude": makeObjectPropertySetter(readDecimal),
  "southBoundLatitude": makeObjectPropertySetter(readDecimal),
  "northBoundLatitude": makeObjectPropertySetter(readDecimal)
});
var REQUEST_PARSERS = makeStructureNS(NAMESPACE_URIS4, {
  "GetCapabilities": makeObjectPropertySetter(readOperationType),
  "GetMap": makeObjectPropertySetter(readOperationType),
  "GetFeatureInfo": makeObjectPropertySetter(readOperationType)
});
var OPERATIONTYPE_PARSERS = makeStructureNS(NAMESPACE_URIS4, {
  "Format": makeObjectPropertyPusher(readString),
  "DCPType": makeObjectPropertyPusher(readDCPType)
});
var DCPTYPE_PARSERS = makeStructureNS(NAMESPACE_URIS4, {
  "HTTP": makeObjectPropertySetter(readHTTP)
});
var HTTP_PARSERS2 = makeStructureNS(NAMESPACE_URIS4, {
  "Get": makeObjectPropertySetter(readFormatOnlineresource),
  "Post": makeObjectPropertySetter(readFormatOnlineresource)
});
var STYLE_PARSERS2 = makeStructureNS(NAMESPACE_URIS4, {
  "Name": makeObjectPropertySetter(readString),
  "Title": makeObjectPropertySetter(readString),
  "Abstract": makeObjectPropertySetter(readString),
  "LegendURL": makeObjectPropertyPusher(readSizedFormatOnlineresource),
  "StyleSheetURL": makeObjectPropertySetter(readFormatOnlineresource),
  "StyleURL": makeObjectPropertySetter(readFormatOnlineresource)
});
var FORMAT_ONLINERESOURCE_PARSERS = makeStructureNS(NAMESPACE_URIS4, {
  "Format": makeObjectPropertySetter(readString),
  "OnlineResource": makeObjectPropertySetter(readHref)
});
var KEYWORDLIST_PARSERS = makeStructureNS(NAMESPACE_URIS4, {
  "Keyword": makeArrayPusher(readString)
});
function readAttribution(node, objectStack) {
  return pushParseAndPop({}, ATTRIBUTION_PARSERS, node, objectStack);
}
function readBoundingBox(node, objectStack) {
  const extent = [
    readDecimalString(node.getAttribute("minx")),
    readDecimalString(node.getAttribute("miny")),
    readDecimalString(node.getAttribute("maxx")),
    readDecimalString(node.getAttribute("maxy"))
  ];
  const resolutions = [
    readDecimalString(node.getAttribute("resx")),
    readDecimalString(node.getAttribute("resy"))
  ];
  return {
    "crs": node.getAttribute("CRS"),
    "extent": extent,
    "res": resolutions
  };
}
function readEXGeographicBoundingBox(node, objectStack) {
  const geographicBoundingBox = pushParseAndPop(
    {},
    EX_GEOGRAPHIC_BOUNDING_BOX_PARSERS,
    node,
    objectStack
  );
  if (!geographicBoundingBox) {
    return void 0;
  }
  const westBoundLongitude = (
    /** @type {number|undefined} */
    geographicBoundingBox["westBoundLongitude"]
  );
  const southBoundLatitude = (
    /** @type {number|undefined} */
    geographicBoundingBox["southBoundLatitude"]
  );
  const eastBoundLongitude = (
    /** @type {number|undefined} */
    geographicBoundingBox["eastBoundLongitude"]
  );
  const northBoundLatitude = (
    /** @type {number|undefined} */
    geographicBoundingBox["northBoundLatitude"]
  );
  if (westBoundLongitude === void 0 || southBoundLatitude === void 0 || eastBoundLongitude === void 0 || northBoundLatitude === void 0) {
    return void 0;
  }
  return [
    westBoundLongitude,
    southBoundLatitude,
    eastBoundLongitude,
    northBoundLatitude
  ];
}
function readCapability(node, objectStack) {
  return pushParseAndPop({}, CAPABILITY_PARSERS, node, objectStack);
}
function readService(node, objectStack) {
  return pushParseAndPop({}, SERVICE_PARSERS, node, objectStack);
}
function readContactInformation(node, objectStack) {
  return pushParseAndPop({}, CONTACT_INFORMATION_PARSERS, node, objectStack);
}
function readContactPersonPrimary(node, objectStack) {
  return pushParseAndPop({}, CONTACT_PERSON_PARSERS, node, objectStack);
}
function readContactAddress(node, objectStack) {
  return pushParseAndPop({}, CONTACT_ADDRESS_PARSERS, node, objectStack);
}
function readException(node, objectStack) {
  return pushParseAndPop([], EXCEPTION_PARSERS, node, objectStack);
}
function readCapabilityLayer(node, objectStack) {
  const layerObject = pushParseAndPop({}, LAYER_PARSERS, node, objectStack);
  if (layerObject["Layer"] === void 0) {
    return Object.assign(layerObject, readLayer(node, objectStack));
  }
  return layerObject;
}
function readLayer(node, objectStack) {
  const parentLayerObject = (
    /**  @type {!Object<string,*>} */
    objectStack[objectStack.length - 1]
  );
  const layerObject = pushParseAndPop({}, LAYER_PARSERS, node, objectStack);
  if (!layerObject) {
    return void 0;
  }
  let queryable = readBooleanString(node.getAttribute("queryable"));
  if (queryable === void 0) {
    queryable = parentLayerObject["queryable"];
  }
  layerObject["queryable"] = queryable !== void 0 ? queryable : false;
  let cascaded = readNonNegativeIntegerString(node.getAttribute("cascaded"));
  if (cascaded === void 0) {
    cascaded = parentLayerObject["cascaded"];
  }
  layerObject["cascaded"] = cascaded;
  let opaque = readBooleanString(node.getAttribute("opaque"));
  if (opaque === void 0) {
    opaque = parentLayerObject["opaque"];
  }
  layerObject["opaque"] = opaque !== void 0 ? opaque : false;
  let noSubsets = readBooleanString(node.getAttribute("noSubsets"));
  if (noSubsets === void 0) {
    noSubsets = parentLayerObject["noSubsets"];
  }
  layerObject["noSubsets"] = noSubsets !== void 0 ? noSubsets : false;
  let fixedWidth = readDecimalString(node.getAttribute("fixedWidth"));
  if (!fixedWidth) {
    fixedWidth = parentLayerObject["fixedWidth"];
  }
  layerObject["fixedWidth"] = fixedWidth;
  let fixedHeight = readDecimalString(node.getAttribute("fixedHeight"));
  if (!fixedHeight) {
    fixedHeight = parentLayerObject["fixedHeight"];
  }
  layerObject["fixedHeight"] = fixedHeight;
  const addKeys = ["Style", "CRS", "AuthorityURL"];
  addKeys.forEach(function(key) {
    if (key in parentLayerObject) {
      const childValue = layerObject[key] || [];
      layerObject[key] = childValue.concat(parentLayerObject[key]);
    }
  });
  const replaceKeys = [
    "EX_GeographicBoundingBox",
    "BoundingBox",
    "Dimension",
    "Attribution",
    "MinScaleDenominator",
    "MaxScaleDenominator"
  ];
  replaceKeys.forEach(function(key) {
    if (!(key in layerObject)) {
      const parentValue = parentLayerObject[key];
      layerObject[key] = parentValue;
    }
  });
  return layerObject;
}
function readDimension(node, objectStack) {
  const dimensionObject = {
    "name": node.getAttribute("name"),
    "units": node.getAttribute("units"),
    "unitSymbol": node.getAttribute("unitSymbol"),
    "default": node.getAttribute("default"),
    "multipleValues": readBooleanString(node.getAttribute("multipleValues")),
    "nearestValue": readBooleanString(node.getAttribute("nearestValue")),
    "current": readBooleanString(node.getAttribute("current")),
    "values": readString(node)
  };
  return dimensionObject;
}
function readFormatOnlineresource(node, objectStack) {
  return pushParseAndPop({}, FORMAT_ONLINERESOURCE_PARSERS, node, objectStack);
}
function readRequest(node, objectStack) {
  return pushParseAndPop({}, REQUEST_PARSERS, node, objectStack);
}
function readDCPType(node, objectStack) {
  return pushParseAndPop({}, DCPTYPE_PARSERS, node, objectStack);
}
function readHTTP(node, objectStack) {
  return pushParseAndPop({}, HTTP_PARSERS2, node, objectStack);
}
function readOperationType(node, objectStack) {
  return pushParseAndPop({}, OPERATIONTYPE_PARSERS, node, objectStack);
}
function readSizedFormatOnlineresource(node, objectStack) {
  const formatOnlineresource = readFormatOnlineresource(node, objectStack);
  if (formatOnlineresource) {
    const size = [
      readNonNegativeIntegerString(node.getAttribute("width")),
      readNonNegativeIntegerString(node.getAttribute("height"))
    ];
    formatOnlineresource["size"] = size;
    return formatOnlineresource;
  }
  return void 0;
}
function readAuthorityURL(node, objectStack) {
  const authorityObject = readFormatOnlineresource(node, objectStack);
  if (authorityObject) {
    authorityObject["name"] = node.getAttribute("name");
    return authorityObject;
  }
  return void 0;
}
function readMetadataURL(node, objectStack) {
  const metadataObject = readFormatOnlineresource(node, objectStack);
  if (metadataObject) {
    metadataObject["type"] = node.getAttribute("type");
    return metadataObject;
  }
  return void 0;
}
function readStyle2(node, objectStack) {
  return pushParseAndPop({}, STYLE_PARSERS2, node, objectStack);
}
function readKeywordList(node, objectStack) {
  return pushParseAndPop([], KEYWORDLIST_PARSERS, node, objectStack);
}
var WMSCapabilities_default = WMSCapabilities;

// node_modules/ol/format/WMSGetFeatureInfo.js
var featureIdentifier = "_feature";
var layerIdentifier = "_layer";
var WMSGetFeatureInfo = class extends XMLFeature_default {
  /**
   * @param {Options} [options] Options.
   */
  constructor(options) {
    super();
    options = options ? options : {};
    this.featureNS_ = "http://mapserver.gis.umn.edu/mapserver";
    this.gmlFormat_ = new GML2_default();
    this.layers_ = options.layers ? options.layers : null;
  }
  /**
   * @return {Array<string>|null} layers
   */
  getLayers() {
    return this.layers_;
  }
  /**
   * @param {Array<string>|null} layers Layers to parse.
   */
  setLayers(layers) {
    this.layers_ = layers;
  }
  /**
   * @param {Element} node Node.
   * @param {Array<*>} objectStack Object stack.
   * @return {Array<import("../Feature.js").default>} Features.
   * @private
   */
  readFeatures_(node, objectStack) {
    node.setAttribute("namespaceURI", this.featureNS_);
    const localName = node.localName;
    let features = [];
    if (node.childNodes.length === 0) {
      return features;
    }
    if (localName == "msGMLOutput") {
      for (let i = 0, ii = node.childNodes.length; i < ii; i++) {
        const layer = node.childNodes[i];
        if (layer.nodeType !== Node.ELEMENT_NODE) {
          continue;
        }
        const layerElement = (
          /** @type {Element} */
          layer
        );
        const context = objectStack[0];
        const toRemove = layerIdentifier;
        const layerName = layerElement.localName.replace(toRemove, "");
        if (this.layers_ && !this.layers_.includes(layerName)) {
          continue;
        }
        const featureType = layerName + featureIdentifier;
        context["featureType"] = featureType;
        context["featureNS"] = this.featureNS_;
        const parsers = {};
        parsers[featureType] = makeArrayPusher(
          this.gmlFormat_.readFeatureElement,
          this.gmlFormat_
        );
        const parsersNS = makeStructureNS(
          [context["featureNS"], null],
          parsers
        );
        layerElement.setAttribute("namespaceURI", this.featureNS_);
        const layerFeatures = pushParseAndPop(
          [],
          // @ts-ignore
          parsersNS,
          layerElement,
          objectStack,
          this.gmlFormat_
        );
        if (layerFeatures) {
          extend(features, layerFeatures);
        }
      }
    }
    if (localName == "FeatureCollection") {
      const gmlFeatures = pushParseAndPop(
        [],
        this.gmlFormat_.FEATURE_COLLECTION_PARSERS,
        node,
        [{}],
        this.gmlFormat_
      );
      if (gmlFeatures) {
        features = gmlFeatures;
      }
    }
    return features;
  }
  /**
   * @protected
   * @param {Element} node Node.
   * @param {import("./Feature.js").ReadOptions} [options] Options.
   * @return {Array<import("../Feature.js").default>} Features.
   */
  readFeaturesFromNode(node, options) {
    const internalOptions = {};
    if (options) {
      Object.assign(internalOptions, this.getReadOptions(node, options));
    }
    return this.readFeatures_(node, [internalOptions]);
  }
};
var WMSGetFeatureInfo_default = WMSGetFeatureInfo;

// node_modules/ol/format/WMTSCapabilities.js
var NAMESPACE_URIS5 = [null, "http://www.opengis.net/wmts/1.0"];
var OWS_NAMESPACE_URIS = [null, "http://www.opengis.net/ows/1.1"];
var PARSERS3 = makeStructureNS(NAMESPACE_URIS5, {
  "Contents": makeObjectPropertySetter(readContents)
});
var WMTSCapabilities = class extends XML_default {
  constructor() {
    super();
    this.owsParser_ = new OWS_default();
  }
  /**
   * @param {Element} node Node.
   * @return {Object} Object
   */
  readFromNode(node) {
    let version = node.getAttribute("version");
    if (version) {
      version = version.trim();
    }
    let WMTSCapabilityObject = this.owsParser_.readFromNode(node);
    if (!WMTSCapabilityObject) {
      return null;
    }
    WMTSCapabilityObject["version"] = version;
    WMTSCapabilityObject = pushParseAndPop(
      WMTSCapabilityObject,
      PARSERS3,
      node,
      []
    );
    return WMTSCapabilityObject ? WMTSCapabilityObject : null;
  }
};
var CONTENTS_PARSERS = makeStructureNS(NAMESPACE_URIS5, {
  "Layer": makeObjectPropertyPusher(readLayer2),
  "TileMatrixSet": makeObjectPropertyPusher(readTileMatrixSet)
});
var LAYER_PARSERS2 = makeStructureNS(
  NAMESPACE_URIS5,
  {
    "Style": makeObjectPropertyPusher(readStyle3),
    "Format": makeObjectPropertyPusher(readString),
    "TileMatrixSetLink": makeObjectPropertyPusher(readTileMatrixSetLink),
    "Dimension": makeObjectPropertyPusher(readDimensions),
    "ResourceURL": makeObjectPropertyPusher(readResourceUrl)
  },
  makeStructureNS(OWS_NAMESPACE_URIS, {
    "Title": makeObjectPropertySetter(readString),
    "Abstract": makeObjectPropertySetter(readString),
    "WGS84BoundingBox": makeObjectPropertySetter(readBoundingBox2),
    "Identifier": makeObjectPropertySetter(readString)
  })
);
var STYLE_PARSERS3 = makeStructureNS(
  NAMESPACE_URIS5,
  {
    "LegendURL": makeObjectPropertyPusher(readLegendUrl)
  },
  makeStructureNS(OWS_NAMESPACE_URIS, {
    "Title": makeObjectPropertySetter(readString),
    "Identifier": makeObjectPropertySetter(readString)
  })
);
var TMS_LINKS_PARSERS = makeStructureNS(NAMESPACE_URIS5, {
  "TileMatrixSet": makeObjectPropertySetter(readString),
  "TileMatrixSetLimits": makeObjectPropertySetter(readTileMatrixLimitsList)
});
var TMS_LIMITS_LIST_PARSERS = makeStructureNS(NAMESPACE_URIS5, {
  "TileMatrixLimits": makeArrayPusher(readTileMatrixLimits)
});
var TMS_LIMITS_PARSERS = makeStructureNS(NAMESPACE_URIS5, {
  "TileMatrix": makeObjectPropertySetter(readString),
  "MinTileRow": makeObjectPropertySetter(readPositiveInteger),
  "MaxTileRow": makeObjectPropertySetter(readPositiveInteger),
  "MinTileCol": makeObjectPropertySetter(readPositiveInteger),
  "MaxTileCol": makeObjectPropertySetter(readPositiveInteger)
});
var DIMENSION_PARSERS = makeStructureNS(
  NAMESPACE_URIS5,
  {
    "Default": makeObjectPropertySetter(readString),
    "Value": makeObjectPropertyPusher(readString)
  },
  makeStructureNS(OWS_NAMESPACE_URIS, {
    "Identifier": makeObjectPropertySetter(readString)
  })
);
var WGS84_BBOX_READERS = makeStructureNS(OWS_NAMESPACE_URIS, {
  "LowerCorner": makeArrayPusher(readCoordinates),
  "UpperCorner": makeArrayPusher(readCoordinates)
});
var TMS_PARSERS = makeStructureNS(
  NAMESPACE_URIS5,
  {
    "WellKnownScaleSet": makeObjectPropertySetter(readString),
    "TileMatrix": makeObjectPropertyPusher(readTileMatrix)
  },
  makeStructureNS(OWS_NAMESPACE_URIS, {
    "SupportedCRS": makeObjectPropertySetter(readString),
    "Identifier": makeObjectPropertySetter(readString),
    "BoundingBox": makeObjectPropertySetter(readBoundingBox2)
  })
);
var TM_PARSERS = makeStructureNS(
  NAMESPACE_URIS5,
  {
    "TopLeftCorner": makeObjectPropertySetter(readCoordinates),
    "ScaleDenominator": makeObjectPropertySetter(readDecimal),
    "TileWidth": makeObjectPropertySetter(readPositiveInteger),
    "TileHeight": makeObjectPropertySetter(readPositiveInteger),
    "MatrixWidth": makeObjectPropertySetter(readPositiveInteger),
    "MatrixHeight": makeObjectPropertySetter(readPositiveInteger)
  },
  makeStructureNS(OWS_NAMESPACE_URIS, {
    "Identifier": makeObjectPropertySetter(readString)
  })
);
function readContents(node, objectStack) {
  return pushParseAndPop({}, CONTENTS_PARSERS, node, objectStack);
}
function readLayer2(node, objectStack) {
  return pushParseAndPop({}, LAYER_PARSERS2, node, objectStack);
}
function readTileMatrixSet(node, objectStack) {
  return pushParseAndPop({}, TMS_PARSERS, node, objectStack);
}
function readStyle3(node, objectStack) {
  const style = pushParseAndPop({}, STYLE_PARSERS3, node, objectStack);
  if (!style) {
    return void 0;
  }
  const isDefault = node.getAttribute("isDefault") === "true";
  style["isDefault"] = isDefault;
  return style;
}
function readTileMatrixSetLink(node, objectStack) {
  return pushParseAndPop({}, TMS_LINKS_PARSERS, node, objectStack);
}
function readDimensions(node, objectStack) {
  return pushParseAndPop({}, DIMENSION_PARSERS, node, objectStack);
}
function readResourceUrl(node, objectStack) {
  const format = node.getAttribute("format");
  const template = node.getAttribute("template");
  const resourceType = node.getAttribute("resourceType");
  const resource = {};
  if (format) {
    resource["format"] = format;
  }
  if (template) {
    resource["template"] = template;
  }
  if (resourceType) {
    resource["resourceType"] = resourceType;
  }
  return resource;
}
function readBoundingBox2(node, objectStack) {
  const coordinates = pushParseAndPop(
    [],
    WGS84_BBOX_READERS,
    node,
    objectStack
  );
  if (coordinates.length != 2) {
    return void 0;
  }
  return boundingExtent(coordinates);
}
function readLegendUrl(node, objectStack) {
  const legend = {};
  legend["format"] = node.getAttribute("format");
  legend["href"] = readHref(node);
  return legend;
}
function readCoordinates(node, objectStack) {
  const coordinates = readString(node).split(/\s+/);
  if (!coordinates || coordinates.length != 2) {
    return void 0;
  }
  const x = +coordinates[0];
  const y = +coordinates[1];
  if (isNaN(x) || isNaN(y)) {
    return void 0;
  }
  return [x, y];
}
function readTileMatrix(node, objectStack) {
  return pushParseAndPop({}, TM_PARSERS, node, objectStack);
}
function readTileMatrixLimitsList(node, objectStack) {
  return pushParseAndPop([], TMS_LIMITS_LIST_PARSERS, node, objectStack);
}
function readTileMatrixLimits(node, objectStack) {
  return pushParseAndPop({}, TMS_LIMITS_PARSERS, node, objectStack);
}
var WMTSCapabilities_default = WMTSCapabilities;
export {
  EsriJSON_default as EsriJSON,
  GML_default as GML,
  GPX_default as GPX,
  GeoJSON_default as GeoJSON,
  IGC_default as IGC,
  IIIFInfo_default as IIIFInfo,
  KML_default as KML,
  MVT_default as MVT,
  OWS_default as OWS,
  Polyline_default as Polyline,
  TopoJSON_default as TopoJSON,
  WFS_default as WFS,
  WKB_default as WKB,
  WKT_default as WKT,
  WMSCapabilities_default as WMSCapabilities,
  WMSGetFeatureInfo_default as WMSGetFeatureInfo,
  WMTSCapabilities_default as WMTSCapabilities
};
//# sourceMappingURL=ol_format.js.map
